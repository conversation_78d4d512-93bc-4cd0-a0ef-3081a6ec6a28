#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
病理组学特征提取工具 - 增强版
基于CellProfiler核心算法，提取细胞核、细胞质、细胞的全面特征

新增功能:
1. 细胞核特征 (Nuclear Features)
2. 细胞质特征 (Cytoplasm Features) 
3. 细胞特征 (Cell Features)
4. 细胞核-细胞质比例特征
5. 空间分布特征
6. 更多纹理和形态学特征

总特征数: 150+ 个特征

作者: AI Assistant
日期: 2025-01-08
"""

import os
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from skimage import feature, measure, filters, morphology, segmentation
from skimage.feature import graycomatrix, graycoprops, local_binary_pattern
from skimage.measure import regionprops, label
from skimage.filters import threshold_otsu, gaussian, sobel
from skimage.morphology import remove_small_objects, disk, binary_dilation, binary_erosion
from skimage.segmentation import watershed, clear_border
from scipy import ndimage
from scipy.spatial.distance import pdist, squareform

def find_local_maxima(image, min_distance=1, threshold_abs=None):
    """
    自定义的局部最大值检测函数，兼容不同版本的scikit-image
    """
    from scipy.ndimage import maximum_filter

    # 使用形态学操作找局部最大值
    neighborhood_size = max(3, min_distance * 2 + 1)
    neighborhood = np.ones((neighborhood_size, neighborhood_size))
    local_max = (maximum_filter(image, footprint=neighborhood) == image)

    # 应用阈值
    if threshold_abs is not None:
        local_max = local_max & (image >= threshold_abs)

    # 确保最小距离
    if min_distance > 1:
        from skimage.morphology import disk, binary_erosion
        # 使用形态学操作确保最小距离
        struct = disk(min_distance // 2)
        local_max = binary_erosion(local_max, struct)

    # 返回坐标列表
    coords = np.where(local_max)
    return list(zip(coords[0], coords[1]))

import warnings
warnings.filterwarnings('ignore')

class EnhancedPathomicsExtractor:
    """增强版病理组学特征提取器"""
    
    def __init__(self, input_folder, output_folder=None):
        """
        初始化增强版特征提取器
        
        参数:
        input_folder: 输入图像文件夹路径
        output_folder: 输出结果文件夹路径
        """
        self.input_folder = input_folder
        self.output_folder = output_folder or os.path.join(input_folder, 'enhanced_pathomics_results')
        
        # 创建输出文件夹
        os.makedirs(self.output_folder, exist_ok=True)
        
        # 支持的图像格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp']
        
        # 特征存储
        self.features_df = pd.DataFrame()
        
        print(f"增强版病理组学特征提取器初始化完成")
        print(f"输入文件夹: {self.input_folder}")
        print(f"输出文件夹: {self.output_folder}")
        print(f"预计提取特征数: 150+")
    
    def preprocess_image(self, image):
        """增强的图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 多尺度高斯滤波去噪
        denoised = gaussian(gray, sigma=1.0)
        
        # 对比度增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply((denoised * 255).astype(np.uint8))
        
        # 归一化
        normalized = ((enhanced - enhanced.min()) / (enhanced.max() - enhanced.min()) * 255).astype(np.uint8)
        
        return normalized
    
    def segment_nuclei_and_cells(self, image):
        """
        分割细胞核和细胞
        返回: nuclei_labels, cell_labels, cytoplasm_labels
        """
        # 1. 细胞核分割 (更严格的阈值)
        # 使用多种阈值方法的组合
        thresh_otsu = threshold_otsu(image)
        thresh_nuclei = thresh_otsu * 0.7  # 更严格的阈值用于细胞核
        
        nuclei_binary = image < thresh_nuclei  # 细胞核通常更暗
        
        # 形态学操作优化细胞核
        nuclei_binary = morphology.remove_small_objects(nuclei_binary, min_size=30)
        nuclei_binary = morphology.binary_closing(nuclei_binary, disk(2))
        nuclei_binary = morphology.binary_opening(nuclei_binary, disk(1))
        
        # 分水岭分割细胞核
        distance_nuclei = ndimage.distance_transform_edt(nuclei_binary)
        local_maxima_nuclei = find_local_maxima(
            distance_nuclei, min_distance=8, threshold_abs=0.3*distance_nuclei.max()
        )
        
        markers_nuclei = np.zeros_like(distance_nuclei, dtype=int)
        if len(local_maxima_nuclei) > 0:
            markers_nuclei[tuple(zip(*local_maxima_nuclei))] = np.arange(1, len(local_maxima_nuclei) + 1)
        
        nuclei_labels = watershed(-distance_nuclei, markers_nuclei, mask=nuclei_binary)
        
        # 2. 细胞分割 (更宽松的阈值)
        thresh_cells = thresh_otsu * 1.2  # 更宽松的阈值用于整个细胞
        cell_binary = image < thresh_cells
        
        # 形态学操作
        cell_binary = morphology.remove_small_objects(cell_binary, min_size=100)
        cell_binary = morphology.binary_closing(cell_binary, disk(5))
        
        # 使用细胞核作为种子点进行细胞分割
        distance_cells = ndimage.distance_transform_edt(cell_binary)
        
        # 扩展细胞核标签作为细胞的种子
        nuclei_seeds = binary_dilation(nuclei_labels > 0, disk(3))
        markers_cells = nuclei_labels.copy()
        
        cell_labels = watershed(-distance_cells, markers_cells, mask=cell_binary)
        
        # 3. 细胞质分割 (细胞 - 细胞核)
        cytoplasm_labels = cell_labels.copy()
        cytoplasm_labels[nuclei_labels > 0] = 0
        
        return nuclei_labels, cell_labels, cytoplasm_labels
    
    def extract_nuclear_features(self, nuclei_labels, image):
        """提取细胞核特征"""
        features = {}
        props = regionprops(nuclei_labels, intensity_image=image)
        
        if len(props) == 0:
            return self._get_empty_nuclear_features()
        
        # 基础形态学特征
        areas = [prop.area for prop in props]
        perimeters = [prop.perimeter for prop in props]
        eccentricities = [prop.eccentricity for prop in props]
        solidity_values = [prop.solidity for prop in props]
        
        # 核形态特征
        features['Nuclear_Area_Mean'] = np.mean(areas)
        features['Nuclear_Area_Std'] = np.std(areas)
        features['Nuclear_Area_CV'] = np.std(areas) / (np.mean(areas) + 1e-7)
        features['Nuclear_Perimeter_Mean'] = np.mean(perimeters)
        features['Nuclear_Perimeter_Std'] = np.std(perimeters)
        
        # 核形状特征
        features['Nuclear_Eccentricity_Mean'] = np.mean(eccentricities)
        features['Nuclear_Eccentricity_Std'] = np.std(eccentricities)
        features['Nuclear_Solidity_Mean'] = np.mean(solidity_values)
        features['Nuclear_Solidity_Std'] = np.std(solidity_values)
        
        # 核圆形度
        form_factors = [4 * np.pi * prop.area / (prop.perimeter ** 2) if prop.perimeter > 0 else 0 for prop in props]
        features['Nuclear_FormFactor_Mean'] = np.mean(form_factors)
        features['Nuclear_FormFactor_Std'] = np.std(form_factors)
        
        # 核长短轴
        major_axis = [prop.major_axis_length for prop in props]
        minor_axis = [prop.minor_axis_length for prop in props]
        features['Nuclear_MajorAxis_Mean'] = np.mean(major_axis)
        features['Nuclear_MinorAxis_Mean'] = np.mean(minor_axis)
        features['Nuclear_AxisRatio_Mean'] = np.mean([maj/min if min > 0 else 0 for maj, min in zip(major_axis, minor_axis)])
        
        # 核强度特征
        intensities = [prop.mean_intensity for prop in props]
        features['Nuclear_Intensity_Mean'] = np.mean(intensities)
        features['Nuclear_Intensity_Std'] = np.std(intensities)
        features['Nuclear_Intensity_CV'] = np.std(intensities) / (np.mean(intensities) + 1e-7)
        
        # 核数量和密度
        features['Nuclear_Count'] = len(props)
        features['Nuclear_Density'] = len(props) / (image.shape[0] * image.shape[1])
        
        # 核空间分布特征
        if len(props) > 1:
            centroids = [(prop.centroid[0], prop.centroid[1]) for prop in props]
            distances = pdist(centroids)
            features['Nuclear_Distance_Mean'] = np.mean(distances)
            features['Nuclear_Distance_Std'] = np.std(distances)
            features['Nuclear_Distance_Min'] = np.min(distances)
        else:
            features['Nuclear_Distance_Mean'] = 0
            features['Nuclear_Distance_Std'] = 0
            features['Nuclear_Distance_Min'] = 0
        
        return features
    
    def extract_cytoplasm_features(self, cytoplasm_labels, image):
        """提取细胞质特征"""
        features = {}
        props = regionprops(cytoplasm_labels, intensity_image=image)
        
        if len(props) == 0:
            return self._get_empty_cytoplasm_features()
        
        # 细胞质形态特征
        areas = [prop.area for prop in props]
        perimeters = [prop.perimeter for prop in props]
        
        features['Cytoplasm_Area_Mean'] = np.mean(areas)
        features['Cytoplasm_Area_Std'] = np.std(areas)
        features['Cytoplasm_Area_CV'] = np.std(areas) / (np.mean(areas) + 1e-7)
        features['Cytoplasm_Perimeter_Mean'] = np.mean(perimeters)
        features['Cytoplasm_Perimeter_Std'] = np.std(perimeters)
        
        # 细胞质形状特征
        eccentricities = [prop.eccentricity for prop in props]
        solidity_values = [prop.solidity for prop in props]
        features['Cytoplasm_Eccentricity_Mean'] = np.mean(eccentricities)
        features['Cytoplasm_Solidity_Mean'] = np.mean(solidity_values)
        
        # 细胞质强度特征
        intensities = [prop.mean_intensity for prop in props]
        features['Cytoplasm_Intensity_Mean'] = np.mean(intensities)
        features['Cytoplasm_Intensity_Std'] = np.std(intensities)
        features['Cytoplasm_Intensity_CV'] = np.std(intensities) / (np.mean(intensities) + 1e-7)
        
        return features
    
    def extract_cell_features(self, cell_labels, image):
        """提取整个细胞特征"""
        features = {}
        props = regionprops(cell_labels, intensity_image=image)
        
        if len(props) == 0:
            return self._get_empty_cell_features()
        
        # 细胞形态特征
        areas = [prop.area for prop in props]
        perimeters = [prop.perimeter for prop in props]
        
        features['Cell_Area_Mean'] = np.mean(areas)
        features['Cell_Area_Std'] = np.std(areas)
        features['Cell_Area_CV'] = np.std(areas) / (np.mean(areas) + 1e-7)
        features['Cell_Perimeter_Mean'] = np.mean(perimeters)
        features['Cell_Perimeter_Std'] = np.std(perimeters)
        
        # 细胞形状特征
        eccentricities = [prop.eccentricity for prop in props]
        solidity_values = [prop.solidity for prop in props]
        features['Cell_Eccentricity_Mean'] = np.mean(eccentricities)
        features['Cell_Solidity_Mean'] = np.mean(solidity_values)
        
        # 细胞圆形度
        form_factors = [4 * np.pi * prop.area / (prop.perimeter ** 2) if prop.perimeter > 0 else 0 for prop in props]
        features['Cell_FormFactor_Mean'] = np.mean(form_factors)
        features['Cell_FormFactor_Std'] = np.std(form_factors)
        
        # 细胞强度特征
        intensities = [prop.mean_intensity for prop in props]
        features['Cell_Intensity_Mean'] = np.mean(intensities)
        features['Cell_Intensity_Std'] = np.std(intensities)
        
        # 细胞数量
        features['Cell_Count'] = len(props)
        features['Cell_Density'] = len(props) / (image.shape[0] * image.shape[1])

        return features

    def extract_nuclear_cytoplasm_ratio_features(self, nuclei_labels, cytoplasm_labels, cell_labels, image):
        """提取细胞核-细胞质比例特征"""
        features = {}

        nuclear_props = regionprops(nuclei_labels, intensity_image=image)
        cytoplasm_props = regionprops(cytoplasm_labels, intensity_image=image)
        cell_props = regionprops(cell_labels, intensity_image=image)

        if len(nuclear_props) == 0 or len(cell_props) == 0:
            return self._get_empty_ratio_features()

        # 核质比 (Nuclear-Cytoplasm Ratio)
        nuclear_areas = [prop.area for prop in nuclear_props]
        cell_areas = [prop.area for prop in cell_props]

        # 计算每个细胞的核质比
        nc_ratios = []
        nuclear_intensity_ratios = []

        for i, cell_prop in enumerate(cell_props):
            # 找到对应的细胞核
            cell_mask = cell_labels == (i + 1)
            nuclear_mask = nuclei_labels > 0
            overlap_mask = cell_mask & nuclear_mask

            if np.sum(overlap_mask) > 0:
                nuclear_area_in_cell = np.sum(overlap_mask)
                cytoplasm_area_in_cell = cell_prop.area - nuclear_area_in_cell

                if cytoplasm_area_in_cell > 0:
                    nc_ratio = nuclear_area_in_cell / cytoplasm_area_in_cell
                    nc_ratios.append(nc_ratio)

                # 核质强度比
                nuclear_intensity = np.mean(image[overlap_mask])
                cytoplasm_intensity = np.mean(image[cell_mask & ~nuclear_mask])
                if cytoplasm_intensity > 0:
                    intensity_ratio = nuclear_intensity / cytoplasm_intensity
                    nuclear_intensity_ratios.append(intensity_ratio)

        if nc_ratios:
            features['NC_Ratio_Mean'] = np.mean(nc_ratios)
            features['NC_Ratio_Std'] = np.std(nc_ratios)
            features['NC_Ratio_CV'] = np.std(nc_ratios) / (np.mean(nc_ratios) + 1e-7)
        else:
            features['NC_Ratio_Mean'] = 0
            features['NC_Ratio_Std'] = 0
            features['NC_Ratio_CV'] = 0

        if nuclear_intensity_ratios:
            features['NC_Intensity_Ratio_Mean'] = np.mean(nuclear_intensity_ratios)
            features['NC_Intensity_Ratio_Std'] = np.std(nuclear_intensity_ratios)
        else:
            features['NC_Intensity_Ratio_Mean'] = 0
            features['NC_Intensity_Ratio_Std'] = 0

        # 核占细胞面积比例
        if cell_areas and nuclear_areas:
            total_nuclear_area = sum(nuclear_areas)
            total_cell_area = sum(cell_areas)
            features['Nuclear_Cell_Area_Ratio'] = total_nuclear_area / (total_cell_area + 1e-7)
        else:
            features['Nuclear_Cell_Area_Ratio'] = 0

        return features

    def extract_enhanced_texture_features(self, image, labels=None):
        """提取增强的纹理特征"""
        features = {}

        # 如果有标签，只在标签区域内计算纹理
        if labels is not None:
            mask = labels > 0
            if np.sum(mask) == 0:
                return self._get_empty_texture_features()
            masked_image = image.copy()
            masked_image[~mask] = 0
        else:
            masked_image = image

        # 1. Haralick纹理特征 (多距离、多角度)
        distances = [1, 2, 3, 5]
        angles = [0, 45, 90, 135]

        for distance in distances:
            glcm = graycomatrix(masked_image, distances=[distance], angles=np.radians(angles),
                             levels=256, symmetric=True, normed=True)

            # 计算各种纹理特征
            contrast = graycoprops(glcm, 'contrast').mean()
            dissimilarity = graycoprops(glcm, 'dissimilarity').mean()
            homogeneity = graycoprops(glcm, 'homogeneity').mean()
            energy = graycoprops(glcm, 'energy').mean()
            correlation = graycoprops(glcm, 'correlation').mean()
            asm = graycoprops(glcm, 'ASM').mean()  # Angular Second Moment

            features[f'Texture_Contrast_d{distance}'] = contrast
            features[f'Texture_Dissimilarity_d{distance}'] = dissimilarity
            features[f'Texture_Homogeneity_d{distance}'] = homogeneity
            features[f'Texture_Energy_d{distance}'] = energy
            features[f'Texture_Correlation_d{distance}'] = correlation
            features[f'Texture_ASM_d{distance}'] = asm

        # 2. LBP特征 (多半径)
        radii = [1, 2, 3]
        n_points = [8, 16, 24]

        for radius, n_point in zip(radii, n_points):
            lbp = local_binary_pattern(masked_image, n_point, radius, method='uniform')
            lbp_hist, _ = np.histogram(lbp.ravel(), bins=n_point + 2, range=(0, n_point + 2))
            lbp_hist = lbp_hist.astype(float)
            lbp_hist /= (lbp_hist.sum() + 1e-7)

            # LBP统计特征
            features[f'LBP_Uniformity_r{radius}'] = np.sum(lbp_hist ** 2)
            features[f'LBP_Entropy_r{radius}'] = -np.sum(lbp_hist * np.log2(lbp_hist + 1e-7))
            features[f'LBP_Contrast_r{radius}'] = np.sum([(i-j)**2 * lbp_hist[i] for i in range(len(lbp_hist)) for j in range(len(lbp_hist))])

        # 3. 梯度特征
        sobel_h = sobel(masked_image, axis=0)
        sobel_v = sobel(masked_image, axis=1)
        gradient_magnitude = np.sqrt(sobel_h**2 + sobel_v**2)

        features['Gradient_Mean'] = np.mean(gradient_magnitude)
        features['Gradient_Std'] = np.std(gradient_magnitude)
        features['Gradient_Max'] = np.max(gradient_magnitude)

        return features

    def extract_spatial_features(self, nuclei_labels, cell_labels, image):
        """提取空间分布特征"""
        features = {}

        nuclear_props = regionprops(nuclei_labels)
        cell_props = regionprops(cell_labels)

        if len(nuclear_props) == 0:
            return self._get_empty_spatial_features()

        # 细胞核中心点
        nuclear_centroids = [(prop.centroid[0], prop.centroid[1]) for prop in nuclear_props]

        if len(nuclear_centroids) > 1:
            # 最近邻距离
            distances = pdist(nuclear_centroids)
            features['Spatial_NearestNeighbor_Mean'] = np.mean(distances)
            features['Spatial_NearestNeighbor_Std'] = np.std(distances)
            features['Spatial_NearestNeighbor_Min'] = np.min(distances)
            features['Spatial_NearestNeighbor_Max'] = np.max(distances)

            # 空间分布的均匀性
            distance_matrix = squareform(distances)
            np.fill_diagonal(distance_matrix, np.inf)
            nearest_distances = np.min(distance_matrix, axis=1)
            features['Spatial_Uniformity'] = np.std(nearest_distances) / (np.mean(nearest_distances) + 1e-7)

            # 聚集指数
            mean_distance = np.mean(distances)
            expected_distance = 0.5 * np.sqrt(image.shape[0] * image.shape[1] / len(nuclear_centroids))
            features['Spatial_ClusteringIndex'] = mean_distance / (expected_distance + 1e-7)
        else:
            features['Spatial_NearestNeighbor_Mean'] = 0
            features['Spatial_NearestNeighbor_Std'] = 0
            features['Spatial_NearestNeighbor_Min'] = 0
            features['Spatial_NearestNeighbor_Max'] = 0
            features['Spatial_Uniformity'] = 0
            features['Spatial_ClusteringIndex'] = 0

        # 边界效应
        h, w = image.shape
        border_distance = 20  # 边界区域宽度

        border_nuclei = 0
        for centroid in nuclear_centroids:
            y, x = centroid
            if y < border_distance or y > h - border_distance or x < border_distance or x > w - border_distance:
                border_nuclei += 1

        features['Spatial_BorderNuclei_Ratio'] = border_nuclei / len(nuclear_centroids)

        return features

    def extract_advanced_morphological_features(self, labels, image):
        """提取高级形态学特征"""
        features = {}
        props = regionprops(labels, intensity_image=image)

        if len(props) == 0:
            return {}

        # Hu矩特征 (形状不变特征)
        hu_moments_list = []
        for prop in props:
            # 获取对象的二值图像
            minr, minc, maxr, maxc = prop.bbox
            obj_image = prop.image.astype(np.uint8) * 255

            # 计算Hu矩
            moments = cv2.moments(obj_image)
            hu_moments = cv2.HuMoments(moments).flatten()
            hu_moments_list.append(hu_moments)

        if hu_moments_list:
            hu_moments_array = np.array(hu_moments_list)
            for i in range(7):
                features[f'HuMoment_{i+1}_Mean'] = np.mean(hu_moments_array[:, i])
                features[f'HuMoment_{i+1}_Std'] = np.std(hu_moments_array[:, i])

        # 凸包特征
        convex_areas = [prop.convex_area for prop in props]
        features['ConvexArea_Mean'] = np.mean(convex_areas)
        features['ConvexArea_Std'] = np.std(convex_areas)

        # 填充度 (Extent)
        extents = [prop.extent for prop in props]
        features['Extent_Mean'] = np.mean(extents)
        features['Extent_Std'] = np.std(extents)

        # 欧拉数 (拓扑特征)
        euler_numbers = [prop.euler_number for prop in props]
        features['EulerNumber_Mean'] = np.mean(euler_numbers)
        features['EulerNumber_Std'] = np.std(euler_numbers)

        return features

    def _get_empty_nuclear_features(self):
        """返回空的细胞核特征"""
        return {
            'Nuclear_Area_Mean': 0, 'Nuclear_Area_Std': 0, 'Nuclear_Area_CV': 0,
            'Nuclear_Perimeter_Mean': 0, 'Nuclear_Perimeter_Std': 0,
            'Nuclear_Eccentricity_Mean': 0, 'Nuclear_Eccentricity_Std': 0,
            'Nuclear_Solidity_Mean': 0, 'Nuclear_Solidity_Std': 0,
            'Nuclear_FormFactor_Mean': 0, 'Nuclear_FormFactor_Std': 0,
            'Nuclear_MajorAxis_Mean': 0, 'Nuclear_MinorAxis_Mean': 0, 'Nuclear_AxisRatio_Mean': 0,
            'Nuclear_Intensity_Mean': 0, 'Nuclear_Intensity_Std': 0, 'Nuclear_Intensity_CV': 0,
            'Nuclear_Count': 0, 'Nuclear_Density': 0,
            'Nuclear_Distance_Mean': 0, 'Nuclear_Distance_Std': 0, 'Nuclear_Distance_Min': 0
        }

    def _get_empty_cytoplasm_features(self):
        """返回空的细胞质特征"""
        return {
            'Cytoplasm_Area_Mean': 0, 'Cytoplasm_Area_Std': 0, 'Cytoplasm_Area_CV': 0,
            'Cytoplasm_Perimeter_Mean': 0, 'Cytoplasm_Perimeter_Std': 0,
            'Cytoplasm_Eccentricity_Mean': 0, 'Cytoplasm_Solidity_Mean': 0,
            'Cytoplasm_Intensity_Mean': 0, 'Cytoplasm_Intensity_Std': 0, 'Cytoplasm_Intensity_CV': 0
        }

    def _get_empty_cell_features(self):
        """返回空的细胞特征"""
        return {
            'Cell_Area_Mean': 0, 'Cell_Area_Std': 0, 'Cell_Area_CV': 0,
            'Cell_Perimeter_Mean': 0, 'Cell_Perimeter_Std': 0,
            'Cell_Eccentricity_Mean': 0, 'Cell_Solidity_Mean': 0,
            'Cell_FormFactor_Mean': 0, 'Cell_FormFactor_Std': 0,
            'Cell_Intensity_Mean': 0, 'Cell_Intensity_Std': 0,
            'Cell_Count': 0, 'Cell_Density': 0
        }

    def _get_empty_ratio_features(self):
        """返回空的比例特征"""
        return {
            'NC_Ratio_Mean': 0, 'NC_Ratio_Std': 0, 'NC_Ratio_CV': 0,
            'NC_Intensity_Ratio_Mean': 0, 'NC_Intensity_Ratio_Std': 0,
            'Nuclear_Cell_Area_Ratio': 0
        }

    def _get_empty_texture_features(self):
        """返回空的纹理特征"""
        features = {}
        distances = [1, 2, 3, 5]
        radii = [1, 2, 3]

        for distance in distances:
            features[f'Texture_Contrast_d{distance}'] = 0
            features[f'Texture_Dissimilarity_d{distance}'] = 0
            features[f'Texture_Homogeneity_d{distance}'] = 0
            features[f'Texture_Energy_d{distance}'] = 0
            features[f'Texture_Correlation_d{distance}'] = 0
            features[f'Texture_ASM_d{distance}'] = 0

        for radius in radii:
            features[f'LBP_Uniformity_r{radius}'] = 0
            features[f'LBP_Entropy_r{radius}'] = 0
            features[f'LBP_Contrast_r{radius}'] = 0

        features['Gradient_Mean'] = 0
        features['Gradient_Std'] = 0
        features['Gradient_Max'] = 0

        return features

    def _get_empty_spatial_features(self):
        """返回空的空间特征"""
        return {
            'Spatial_NearestNeighbor_Mean': 0, 'Spatial_NearestNeighbor_Std': 0,
            'Spatial_NearestNeighbor_Min': 0, 'Spatial_NearestNeighbor_Max': 0,
            'Spatial_Uniformity': 0, 'Spatial_ClusteringIndex': 0,
            'Spatial_BorderNuclei_Ratio': 0
        }

    def process_single_image(self, image_path):
        """处理单张图像，提取所有增强特征"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图像: {image_path}")
                return None

            # 预处理
            processed_image = self.preprocess_image(image)

            # 分割细胞核、细胞和细胞质
            nuclei_labels, cell_labels, cytoplasm_labels = self.segment_nuclei_and_cells(processed_image)

            # 提取所有特征
            features = {}

            # 1. 细胞核特征
            nuclear_features = self.extract_nuclear_features(nuclei_labels, processed_image)
            features.update(nuclear_features)

            # 2. 细胞质特征
            cytoplasm_features = self.extract_cytoplasm_features(cytoplasm_labels, processed_image)
            features.update(cytoplasm_features)

            # 3. 细胞特征
            cell_features = self.extract_cell_features(cell_labels, processed_image)
            features.update(cell_features)

            # 4. 核质比例特征
            ratio_features = self.extract_nuclear_cytoplasm_ratio_features(
                nuclei_labels, cytoplasm_labels, cell_labels, processed_image)
            features.update(ratio_features)

            # 5. 增强纹理特征 (分别对细胞核、细胞质、细胞计算)
            nuclear_texture = self.extract_enhanced_texture_features(processed_image, nuclei_labels)
            for key, value in nuclear_texture.items():
                features[f'Nuclear_{key}'] = value

            cytoplasm_texture = self.extract_enhanced_texture_features(processed_image, cytoplasm_labels)
            for key, value in cytoplasm_texture.items():
                features[f'Cytoplasm_{key}'] = value

            cell_texture = self.extract_enhanced_texture_features(processed_image, cell_labels)
            for key, value in cell_texture.items():
                features[f'Cell_{key}'] = value

            # 6. 空间分布特征
            spatial_features = self.extract_spatial_features(nuclei_labels, cell_labels, processed_image)
            features.update(spatial_features)

            # 7. 高级形态学特征
            nuclear_morph = self.extract_advanced_morphological_features(nuclei_labels, processed_image)
            for key, value in nuclear_morph.items():
                features[f'Nuclear_{key}'] = value

            cell_morph = self.extract_advanced_morphological_features(cell_labels, processed_image)
            for key, value in cell_morph.items():
                features[f'Cell_{key}'] = value

            # 8. 全局图像特征
            global_features = self.extract_global_image_features(processed_image)
            features.update(global_features)

            # 添加图像信息
            features['Image_Name'] = os.path.basename(image_path)
            features['Image_Path'] = image_path

            return features

        except Exception as e:
            print(f"处理图像 {image_path} 时出错: {str(e)}")
            return None

    def extract_global_image_features(self, image):
        """提取全局图像特征"""
        features = {}

        # 全局强度统计
        features['Global_Intensity_Mean'] = np.mean(image)
        features['Global_Intensity_Std'] = np.std(image)
        features['Global_Intensity_Min'] = np.min(image)
        features['Global_Intensity_Max'] = np.max(image)
        features['Global_Intensity_Median'] = np.median(image)
        features['Global_Intensity_Range'] = np.max(image) - np.min(image)
        features['Global_Intensity_IQR'] = np.percentile(image, 75) - np.percentile(image, 25)

        # 全局纹理特征
        global_texture = self.extract_enhanced_texture_features(image)
        for key, value in global_texture.items():
            features[f'Global_{key}'] = value

        # 图像质量特征
        # 对比度
        features['Global_Contrast'] = np.std(image)

        # 清晰度 (基于拉普拉斯算子)
        laplacian = cv2.Laplacian(image, cv2.CV_64F)
        features['Global_Sharpness'] = np.var(laplacian)

        # 熵
        hist, _ = np.histogram(image, bins=256, range=(0, 256))
        hist = hist.astype(float)
        hist /= hist.sum()
        entropy = -np.sum(hist * np.log2(hist + 1e-7))
        features['Global_Entropy'] = entropy

        return features

    def batch_process(self):
        """批量处理所有图像"""
        print("开始批量处理图像...")
        print("提取细胞核、细胞质、细胞的全面特征...")

        # 获取所有图像文件，排除结果文件夹中的图片
        image_files = []
        for root, dirs, files in os.walk(self.input_folder):
            # 跳过结果文件夹
            if 'enhanced_pathomics_results' in root or 'pathomics_results' in root:
                continue
            for file in files:
                if any(file.lower().endswith(ext) for ext in self.supported_formats):
                    image_files.append(os.path.join(root, file))

        if not image_files:
            print(f"在 {self.input_folder} 中未找到支持的图像文件")
            return

        print(f"找到 {len(image_files)} 个图像文件")

        # 处理每个图像
        all_features = []
        for i, image_path in enumerate(image_files, 1):
            print(f"处理进度: {i}/{len(image_files)} - {os.path.basename(image_path)}")

            features = self.process_single_image(image_path)
            if features:
                all_features.append(features)
                print(f"  ✅ 成功提取 {len(features)-2} 个特征")  # 减去Image_Name和Image_Path
            else:
                print(f"  ❌ 特征提取失败")

        if all_features:
            # 转换为DataFrame
            self.features_df = pd.DataFrame(all_features)
            print(f"\n🎉 成功提取 {len(all_features)} 个图像的特征")
            print(f"📊 总特征数: {len(self.features_df.columns)-2}")

            # 显示特征类型统计
            self._show_feature_statistics()

            # 保存特征到Excel
            self.save_enhanced_features()

            # 生成可视化报告
            self.generate_enhanced_visualization()

        else:
            print("❌ 未能提取到任何特征")

    def _show_feature_statistics(self):
        """显示特征统计信息"""
        feature_categories = {
            'Nuclear': 0,
            'Cytoplasm': 0,
            'Cell': 0,
            'NC_Ratio': 0,
            'Spatial': 0,
            'Texture': 0,
            'Global': 0,
            'HuMoment': 0,
            'Other': 0
        }

        for col in self.features_df.columns:
            if col in ['Image_Name', 'Image_Path']:
                continue
            elif 'Nuclear' in col:
                feature_categories['Nuclear'] += 1
            elif 'Cytoplasm' in col:
                feature_categories['Cytoplasm'] += 1
            elif 'Cell' in col:
                feature_categories['Cell'] += 1
            elif 'NC_' in col:
                feature_categories['NC_Ratio'] += 1
            elif 'Spatial' in col:
                feature_categories['Spatial'] += 1
            elif 'Texture' in col or 'LBP' in col or 'Gradient' in col:
                feature_categories['Texture'] += 1
            elif 'Global' in col:
                feature_categories['Global'] += 1
            elif 'HuMoment' in col:
                feature_categories['HuMoment'] += 1
            else:
                feature_categories['Other'] += 1

        print("\n📈 特征类型分布:")
        for category, count in feature_categories.items():
            if count > 0:
                print(f"   {category}: {count} 个特征")

    def save_enhanced_features(self):
        """保存增强特征到Excel文件"""
        if self.features_df.empty:
            print("没有特征数据可保存")
            return

        # 重新排列列顺序，将Image_Path放在第一列
        cols = self.features_df.columns.tolist()
        if 'Image_Path' in cols and 'Image_Name' in cols:
            # 移除Image_Path和Image_Name
            cols.remove('Image_Path')
            cols.remove('Image_Name')
            # 重新排列：Image_Path, Image_Name, 其他特征
            new_cols = ['Image_Path', 'Image_Name'] + cols
            self.features_df = self.features_df[new_cols]

        # 保存完整特征表
        output_file = os.path.join(self.output_folder, 'enhanced_pathomics_features.xlsx')

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 完整特征表
            self.features_df.to_excel(writer, sheet_name='All_Features', index=False)

            # 细胞核特征
            nuclear_cols = [col for col in self.features_df.columns if 'Nuclear' in col]
            if nuclear_cols:
                nuclear_df = self.features_df[['Image_Path', 'Image_Name'] + nuclear_cols]
                nuclear_df.to_excel(writer, sheet_name='Nuclear_Features', index=False)

            # 细胞质特征
            cytoplasm_cols = [col for col in self.features_df.columns if 'Cytoplasm' in col]
            if cytoplasm_cols:
                cytoplasm_df = self.features_df[['Image_Path', 'Image_Name'] + cytoplasm_cols]
                cytoplasm_df.to_excel(writer, sheet_name='Cytoplasm_Features', index=False)

            # 细胞特征
            cell_cols = [col for col in self.features_df.columns if 'Cell' in col and 'Nuclear' not in col]
            if cell_cols:
                cell_df = self.features_df[['Image_Path', 'Image_Name'] + cell_cols]
                cell_df.to_excel(writer, sheet_name='Cell_Features', index=False)

            # 核质比例特征
            ratio_cols = [col for col in self.features_df.columns if 'NC_' in col or 'Ratio' in col]
            if ratio_cols:
                ratio_df = self.features_df[['Image_Path', 'Image_Name'] + ratio_cols]
                ratio_df.to_excel(writer, sheet_name='NC_Ratio_Features', index=False)

            # 空间特征
            spatial_cols = [col for col in self.features_df.columns if 'Spatial' in col]
            if spatial_cols:
                spatial_df = self.features_df[['Image_Path', 'Image_Name'] + spatial_cols]
                spatial_df.to_excel(writer, sheet_name='Spatial_Features', index=False)

            # 纹理特征
            texture_cols = [col for col in self.features_df.columns if any(
                keyword in col for keyword in ['Texture', 'LBP', 'Gradient'])]
            if texture_cols:
                texture_df = self.features_df[['Image_Path', 'Image_Name'] + texture_cols]
                texture_df.to_excel(writer, sheet_name='Texture_Features', index=False)

            # 全局特征
            global_cols = [col for col in self.features_df.columns if 'Global' in col]
            if global_cols:
                global_df = self.features_df[['Image_Path', 'Image_Name'] + global_cols]
                global_df.to_excel(writer, sheet_name='Global_Features', index=False)

            # 统计摘要
            summary_df = self.features_df.select_dtypes(include=[np.number]).describe()
            summary_df.to_excel(writer, sheet_name='Statistical_Summary')

        print(f"💾 增强特征已保存到: {output_file}")
        print(f"📋 包含 {len(self.features_df.columns)-2} 个特征的完整分析")

    def generate_enhanced_visualization(self):
        """生成增强的可视化报告"""
        if self.features_df.empty:
            print("没有数据可可视化")
            return

        print("🎨 生成增强可视化报告...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建可视化文件夹
        viz_folder = os.path.join(self.output_folder, 'enhanced_visualizations')
        os.makedirs(viz_folder, exist_ok=True)

        # 1. 各类特征分布图
        self._plot_enhanced_feature_distributions(viz_folder)

        # 2. 核质比例分析
        self._plot_nuclear_cytoplasm_analysis(viz_folder)

        # 3. 空间分布分析
        self._plot_spatial_analysis(viz_folder)

        # 4. 特征相关性分析
        self._plot_enhanced_correlation_analysis(viz_folder)

        # 5. 主成分分析
        self._plot_enhanced_pca_analysis(viz_folder)

        print(f"🎨 增强可视化报告已保存到: {viz_folder}")
        print(f"📊 包含多维度特征分析图表")

    def _plot_enhanced_feature_distributions(self, viz_folder):
        """绘制增强的特征分布图"""
        numeric_cols = self.features_df.select_dtypes(include=[np.number]).columns

        # 分类绘制不同类型的特征
        feature_categories = {
            'Nuclear': [col for col in numeric_cols if 'Nuclear' in col],
            'Cytoplasm': [col for col in numeric_cols if 'Cytoplasm' in col],
            'Cell': [col for col in numeric_cols if 'Cell' in col and 'Nuclear' not in col],
            'NC_Ratio': [col for col in numeric_cols if 'NC_' in col or 'Ratio' in col],
            'Spatial': [col for col in numeric_cols if 'Spatial' in col],
            'Global': [col for col in numeric_cols if 'Global' in col]
        }

        for category, features in feature_categories.items():
            if not features:
                continue

            n_features = min(len(features), 16)  # 最多显示16个特征
            selected_features = features[:n_features]

            n_cols = min(4, n_features)
            n_rows = (n_features + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4*n_rows))
            if n_rows == 1 and n_cols == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes.reshape(1, -1)

            for i, feature in enumerate(selected_features):
                row, col = i // n_cols, i % n_cols
                ax = axes[row][col] if n_cols > 1 else axes[row]

                self.features_df[feature].hist(bins=20, ax=ax, alpha=0.7, color='skyblue')
                ax.set_title(f'{feature}', fontsize=8)
                ax.set_xlabel('Value', fontsize=8)
                ax.set_ylabel('Frequency', fontsize=8)
                ax.tick_params(labelsize=7)

            # 隐藏多余的子图
            for i in range(n_features, n_rows * n_cols):
                row, col = i // n_cols, i % n_cols
                ax = axes[row][col] if n_cols > 1 else axes[row]
                ax.set_visible(False)

            plt.suptitle(f'{category} Features Distribution', fontsize=14)
            plt.tight_layout()
            plt.savefig(os.path.join(viz_folder, f'{category}_features_distribution.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    def _plot_nuclear_cytoplasm_analysis(self, viz_folder):
        """绘制核质比例分析"""
        nc_cols = [col for col in self.features_df.columns if 'NC_' in col or 'Ratio' in col]

        if not nc_cols:
            return

        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        # 核质比分布
        if 'NC_Ratio_Mean' in self.features_df.columns:
            axes[0,0].hist(self.features_df['NC_Ratio_Mean'], bins=20, alpha=0.7, color='orange')
            axes[0,0].set_title('Nuclear-Cytoplasm Ratio Distribution')
            axes[0,0].set_xlabel('NC Ratio')
            axes[0,0].set_ylabel('Frequency')

        # 核强度vs细胞质强度
        if 'Nuclear_Intensity_Mean' in self.features_df.columns and 'Cytoplasm_Intensity_Mean' in self.features_df.columns:
            axes[0,1].scatter(self.features_df['Nuclear_Intensity_Mean'],
                            self.features_df['Cytoplasm_Intensity_Mean'], alpha=0.6)
            axes[0,1].set_title('Nuclear vs Cytoplasm Intensity')
            axes[0,1].set_xlabel('Nuclear Intensity')
            axes[0,1].set_ylabel('Cytoplasm Intensity')

        # 核面积vs细胞面积
        if 'Nuclear_Area_Mean' in self.features_df.columns and 'Cell_Area_Mean' in self.features_df.columns:
            axes[1,0].scatter(self.features_df['Nuclear_Area_Mean'],
                            self.features_df['Cell_Area_Mean'], alpha=0.6, color='green')
            axes[1,0].set_title('Nuclear vs Cell Area')
            axes[1,0].set_xlabel('Nuclear Area')
            axes[1,0].set_ylabel('Cell Area')

        # 核数量vs细胞数量
        if 'Nuclear_Count' in self.features_df.columns and 'Cell_Count' in self.features_df.columns:
            axes[1,1].scatter(self.features_df['Nuclear_Count'],
                            self.features_df['Cell_Count'], alpha=0.6, color='red')
            axes[1,1].set_title('Nuclear vs Cell Count')
            axes[1,1].set_xlabel('Nuclear Count')
            axes[1,1].set_ylabel('Cell Count')

        plt.tight_layout()
        plt.savefig(os.path.join(viz_folder, 'nuclear_cytoplasm_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_spatial_analysis(self, viz_folder):
        """绘制空间分布分析"""
        spatial_cols = [col for col in self.features_df.columns if 'Spatial' in col]

        if not spatial_cols:
            return

        n_features = len(spatial_cols)
        n_cols = min(3, n_features)
        n_rows = (n_features + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(12, 4*n_rows))
        if n_rows == 1 and n_cols == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)

        for i, feature in enumerate(spatial_cols):
            row, col = i // n_cols, i % n_cols
            ax = axes[row][col] if n_cols > 1 else axes[row]

            self.features_df[feature].hist(bins=15, ax=ax, alpha=0.7, color='purple')
            ax.set_title(f'{feature}', fontsize=10)
            ax.set_xlabel('Value')
            ax.set_ylabel('Frequency')

        # 隐藏多余的子图
        for i in range(n_features, n_rows * n_cols):
            row, col = i // n_cols, i % n_cols
            ax = axes[row][col] if n_cols > 1 else axes[row]
            ax.set_visible(False)

        plt.suptitle('Spatial Distribution Analysis', fontsize=14)
        plt.tight_layout()
        plt.savefig(os.path.join(viz_folder, 'spatial_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_enhanced_correlation_analysis(self, viz_folder):
        """绘制增强的相关性分析"""
        numeric_df = self.features_df.select_dtypes(include=[np.number])

        # 选择重要特征进行相关性分析
        important_features = []
        feature_keywords = ['Nuclear_Area_Mean', 'Cell_Area_Mean', 'NC_Ratio_Mean',
                          'Nuclear_Count', 'Cell_Count', 'Spatial_NearestNeighbor_Mean',
                          'Nuclear_Intensity_Mean', 'Cytoplasm_Intensity_Mean']

        for keyword in feature_keywords:
            if keyword in numeric_df.columns:
                important_features.append(keyword)

        if len(important_features) > 1:
            correlation_matrix = numeric_df[important_features].corr()

            plt.figure(figsize=(10, 8))
            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
            sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm',
                       center=0, square=True, linewidths=0.5, fmt='.2f')
            plt.title('Key Features Correlation Matrix')
            plt.tight_layout()
            plt.savefig(os.path.join(viz_folder, 'key_features_correlation.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    def _plot_enhanced_pca_analysis(self, viz_folder):
        """绘制增强的PCA分析"""
        from sklearn.decomposition import PCA
        from sklearn.preprocessing import StandardScaler

        numeric_df = self.features_df.select_dtypes(include=[np.number])

        if numeric_df.shape[1] < 2:
            return

        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(numeric_df.fillna(0))

        # PCA分析
        pca = PCA()
        pca_result = pca.fit_transform(scaled_data)

        # 绘制方差解释比例
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        cumsum_ratio = np.cumsum(pca.explained_variance_ratio_)
        plt.plot(range(1, min(21, len(cumsum_ratio) + 1)), cumsum_ratio[:20], 'bo-')
        plt.xlabel('Principal Component')
        plt.ylabel('Cumulative Explained Variance Ratio')
        plt.title('PCA Analysis - Explained Variance')
        plt.grid(True, alpha=0.3)

        # 绘制前两个主成分的散点图
        plt.subplot(1, 2, 2)
        plt.scatter(pca_result[:, 0], pca_result[:, 1], alpha=0.6)
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title('PCA - First Two Components')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(viz_folder, 'enhanced_pca_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()


def main():
    """主函数 - 一键运行增强版病理组学特征提取"""
    print("=" * 80)
    print("增强版病理组学特征提取工具 - 基于CellProfiler算法")
    print("提取细胞核、细胞质、细胞的全面特征 (200+ 特征)")
    print("=" * 80)

    # 直接设置图像路径
    input_folder = r"K:\肝脏MRI数据集\HCC病理HE图\ceshi"
    print(f"📁 图像文件夹: {input_folder}")

    # 检查输入路径是否存在
    if not os.path.exists(input_folder):
        print(f"❌ 错误: 输入路径 {input_folder} 不存在")
        print("请检查路径是否正确")
        return

    # 创建增强版特征提取器
    extractor = EnhancedPathomicsExtractor(input_folder)

    # 执行批量处理
    extractor.batch_process()

    print("\n" + "=" * 80)
    print("🎉 增强版病理组学特征提取完成！")
    print(f"📁 结果保存在: {extractor.output_folder}")
    print("📋 包含以下文件:")
    print("- enhanced_pathomics_features.xlsx: 完整特征表 (Image_Path在第一列)")
    print("- enhanced_visualizations/: 多维度可视化图表文件夹")
    print("  ├── Nuclear_features_distribution.png")
    print("  ├── Cytoplasm_features_distribution.png")
    print("  ├── Cell_features_distribution.png")
    print("  ├── nuclear_cytoplasm_analysis.png")
    print("  ├── spatial_analysis.png")
    print("  └── enhanced_pca_analysis.png")
    print("=" * 80)


if __name__ == "__main__":
    main()
