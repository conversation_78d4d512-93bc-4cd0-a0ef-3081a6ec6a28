name: premerge-notebooks

on:
  # quick tests of modified notebooks for every pull request
  push:
    branches:
      - main
  pull_request:

concurrency:
  # automatically cancel the previously triggered workflows when there's a newer version
  group: build-gpu-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    if: github.repository == 'Project-MONAI/tutorials'
    container:
      image: nvcr.io/nvidia/pytorch:24.02-py3
      options: --gpus all --ipc host
    runs-on: [self-hosted, linux, x64]
    steps:
    - name: Install MONAI
      id: monai-install
      run: |
        which python
        nvidia-smi
        rm -rf ../../MONAI/MONAI
        python -m pip install --upgrade pip wheel
        pip uninstall -y monai
        pip uninstall -y monai
        pip uninstall -y monai-weekly
        pip uninstall -y monai-weekly # make sure there's no existing installation
        BUILD_MONAI=0 python -m pip install git+https://github.com/Project-MONAI/MONAI#egg=MONAI
        python -m pip install -r https://raw.githubusercontent.com/Project-MONAI/MONAI/dev/requirements-dev.txt
        python -m pip install -U torch torchvision torchaudio
    - uses: actions/checkout@v3
    - name: Notebook quick check
      shell: bash
      run: |
        git config --global --add safe.directory /__w/tutorials/tutorials
        git fetch origin main
        python -m pip install -r requirements.txt; python -m pip list
        python -c "import monai; monai.config.print_debug_info()"
        export CUDA_VISIBLE_DEVICES=0
        git diff --name-only origin/main | while read line; do if [[ $line == *.ipynb ]]; then ./runner.sh -p " -and -wholename './${line}'"; fi; done;
          # [[ $line == *.ipynb ]] && ./runner.sh --file "$line"
