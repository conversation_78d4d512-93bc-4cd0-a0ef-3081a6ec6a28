name: pycaret

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  schedule:
    - cron: "0 5 * * *"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  code_quality:
    runs-on: ubuntu-latest
    name: Code quality checks
    steps:
      - name: Check out source repository
        uses: actions/checkout@v3
      - name: Run black
        uses: psf/black@stable
        with:
          src: pycaret tests
          version: 22.12.0
      - name: Check imports
        uses: jamescurtin/isort-action@master
        with:
          sort-paths: pycaret tests
          isort-version: 5.11.4
      - name: Run flake8
        uses: py-actions/flake8@v2
        with:
          path: pycaret tests
          flake8-version: 6.0.0

  # JOBS MUST START WITH test !!!!
  test_notebooks:
    runs-on: ubuntu-latest
    name: Test notebooks
    steps:
      - name: Check out source repository
        uses: actions/checkout@v3
      - name: Set up Python environment
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: |
          pip install -U pytest pytest-xdist nbmake

          # https://github.com/yaml/pyyaml/issues/736
          echo 'Cython < 3.0' > /tmp/constraint.txt
          PIP_CONSTRAINT=/tmp/constraint.txt pip wheel PyYAML==5.4.1

          pip install ".[full]"
          python -m ipykernel install --user --name cikernel
      - name: Python version and dependency list
        run: |
          echo "Python version expected: 3.11"
          python --version
          which python
          pip list
      - name: Run notebooks
        run: pytest --nbmake --nbmake-kernel=cikernel -n=auto --nbmake-timeout=3000 --ignore=./tutorials/translations ./tutorials/

  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ 'ubuntu-latest', 'windows-latest' ]
        python-version: [ '3.9', '3.10', '3.11' ]

    defaults:
      run:
        shell: bash

    name: Python ${{ matrix.python-version }} on OS ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        # SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL required due to
        # pyLDAvis 3.3.1 having an "sklearn" dependency
        run: |
          export SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL=True
          python -m pip install -U pip
          python -m pip install -U pytest numpy

          # https://github.com/yaml/pyyaml/issues/736
          echo 'Cython < 3.0' > /tmp/constraint.txt
          PIP_CONSTRAINT=/tmp/constraint.txt pip wheel PyYAML==5.4.1

          pip install ".[full, test]"
          if [ -f requirements-prophet.txt ]; then pip install -r requirements-prophet.txt; fi
      - name: Python version and dependency list
        run: |
          echo "Python version expected: ${{ matrix.python-version }}"
          python --version
          which python
          pip list
      - name: Test with pytest
        run: pytest --durations=0 -m "not (benchmark or plotting or tuning_random or tuning_grid)"

  test_plots:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]

    name: Test plotting
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        # SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL required due to
        # pyLDAvis 3.3.1 having an "sklearn" dependency
        run: |
          export SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL=True
          python -m pip install --upgrade pip
          python -m pip install -U pytest numpy

          # https://github.com/yaml/pyyaml/issues/736
          echo 'Cython < 3.0' > /tmp/constraint.txt
          PIP_CONSTRAINT=/tmp/constraint.txt pip wheel PyYAML==5.4.1

          pip install ".[full, test]"
      - name: Python version and dependency list
        run: |
          echo "Python version expected: ${{ matrix.python-version }}"
          python --version
          which python
          pip list
      - name: Test with pytest
        run: pytest --durations=0 -m "plotting"

  test_ts_tune_random:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]

    name: Test random tuning
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        # SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL required due to
        # pyLDAvis 3.3.1 having an "sklearn" dependency
        run: |
          export SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL=True
          python -m pip install -U pip
          python -m pip install -U pytest numpy

          # https://github.com/yaml/pyyaml/issues/736
          echo 'Cython < 3.0' > /tmp/constraint.txt
          PIP_CONSTRAINT=/tmp/constraint.txt pip wheel PyYAML==5.4.1

          pip install ".[full, test]"
          if [ -f requirements-prophet.txt ]; then pip install -r requirements-prophet.txt; fi
      - name: Python version and dependency list
        run: |
          echo "Python version expected: ${{ matrix.python-version }}"
          python --version
          which python
          pip list
      - name: Test with pytest
        run: pytest --durations=0 -m "tuning_random"

  test_ts_tune_grid:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]

    name: Test grid tuning
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        # SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL required due to
        # pyLDAvis 3.3.1 having an "sklearn" dependency
        run: |
          export SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL=True
          python -m pip install -U pip
          python -m pip install -U pytest numpy

          # https://github.com/yaml/pyyaml/issues/736
          echo 'Cython < 3.0' > /tmp/constraint.txt
          PIP_CONSTRAINT=/tmp/constraint.txt pip wheel PyYAML==5.4.1

          pip install ".[full, test]"
          if [ -f requirements-prophet.txt ]; then pip install -r requirements-prophet.txt; fi
      - name: Python version and dependency list
        run: |
          echo "Python version expected: ${{ matrix.python-version }}"
          python --version
          which python
          pip list
      - name: Test with pytest
        run: pytest --durations=0 -m "tuning_grid"

  test_benchmarks:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11"]

    name: Benchmark tests
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        # SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL required due to
        # pyLDAvis 3.3.1 having an "sklearn" dependency
        run: |
          export SKLEARN_ALLOW_DEPRECATED_SKLEARN_PACKAGE_INSTALL=True
          python -m pip install -U pip
          python -m pip install -U pytest numpy

          # https://github.com/yaml/pyyaml/issues/736
          echo 'Cython < 3.0' > /tmp/constraint.txt
          PIP_CONSTRAINT=/tmp/constraint.txt pip wheel PyYAML==5.4.1

          pip install ".[full, test]"
          if [ -f requirements-prophet.txt ]; then pip install -r requirements-prophet.txt; fi
      - name: Python version and dependency list
        run: |
          echo "Python version expected: ${{ matrix.python-version }}"
          python --version
          which python
          pip list
      - name: Run benchmarks
        run: pytest --durations=0 -m "benchmark"
