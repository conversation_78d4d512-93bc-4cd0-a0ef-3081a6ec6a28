{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 3DMedical-Imaging-Preprocessing-All-you-need"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This Repo contains the Preprocessing Code for 3D Medical Imaging\n", "\n", "From the last year of my undergrad studies I was very queries about Biomedical Imaging. But until the starting my master I don't have the chance to go deep into medical imaging. Like most people at the beginning, I also suffered and was a bit confused about a few things. In this notebook, I will try to easily explain/show commonly used Preprocessing in Medical Imaging especially with 3D Nifti.\n", "\n", "In this tutorial we will be using Public Abdomen Dataset From: Multi-Atlas Labeling Beyond the Cranial Vault - Workshop and Challenge Link: https://www.synapse.org/#!Synapse:syn3193805/wiki/217789\n", "\n", "Reference: https://github.com/DLTK/DLTK\n", "\n", "In this Notebook we will cover\n", "1. **Reading Nifti Data and ploting**\n", "2. **Different Intensity Normalization Approaches**\n", "3. **Resampling 3D CT data**\n", "4. **Cropping and Padding CT data**\n", "5. **Histogram equalization**\n", "6. **Maximum Intensity Projection (MIP)**\n", "\n", "\n", "`if You want know about MRI Histogram Matching, Histogram Equalization and Registration, You could have a look to my repo`\n", "\n", "* https://github.com/fitushar/Brain-Tissue-Segmentation-Using-Deep-Learning-Pipeline-NeuroNet\n", "* https://github.com/fitushar/Registration-as-Data-Augumentation-for-CT--DATA\n", "\n", "\n", "`To Learn about Segmentation`\n", "\n", "* **Brain Tissue Segmentation**, 3D : https://github.com/fitushar/Brain-Tissue-Segmentation-Using-Deep-Learning-Pipeline-NeuroNet \n", "* **Chest-Abdomen-Pelvis (Segmentation)** 3D DenseVnet :https://github.com/fitushar/DenseVNet3D_Chest_Abdomen_Pelvis_Segmentation_tf2\n", "\n", "* **3D-Unet** : https://github.com/fitushar/3DUnet_tensorflow2.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lets Start"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import libararies "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:06:51.158920Z", "start_time": "2020-07-21T09:06:48.035719Z"}}, "outputs": [{"ename": "AttributeError", "evalue": "module 'numpy' has no attribute 'int'.\n`np.int` was a deprecated alias for the builtin `int`. To avoid this error in existing code, use `int` by itself. Doing this will not modify any behavior and is safe. When replacing `np.int`, you may wish to use e.g. `np.int64` or `np.int32` to specify the precision. If you wish to review your current use, check the release note link for additional information.\nThe aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:\n    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_15328\\841337770.py\u001b[0m in \u001b[0;36m<cell line: 2>\u001b[1;34m()\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0m__future__\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mabsolute_import\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mdivision\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mprint_function\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0municode_literals\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtf\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mnumpy\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mnp\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mpandas\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mSimpleITK\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0msitk\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     39\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0msys\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0m_sys\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     40\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 41\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtools\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mmodule_util\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0m_module_util\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     42\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mutil\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mlazy_loader\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mLazyLoader\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0m_LazyLoader\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     43\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     46\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdata\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     47\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdistribute\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 48\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mkeras\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     49\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mfeature_column\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mfeature_column_lib\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mfeature_column\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     50\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mlayers\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mlayers\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\keras\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     20\u001b[0m \u001b[1;31m# pylint: disable=unused-import\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtf2\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 22\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mkeras\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdistribute\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     23\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[1;31m# See b/110718070#comment18 for more details about this import.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\keras\\distribute\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[1;31m# pylint: disable=unused-import\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 18\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mkeras\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0msidecar_evaluator\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\keras\\distribute\\sidecar_evaluator.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mplatform\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtf_logging\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mlogging\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mcheckpoint_utils\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 26\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtracking\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mutil\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtracking_util\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     27\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     28\u001b[0m \u001b[0m_PRINT_EVAL_STEP_EVERY_SEC\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;36m60.0\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\training\\tracking\\util.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     53\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtracking\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mbase\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     54\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtracking\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdata_structures\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 55\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtracking\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mgraph_view\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mgraph_view_lib\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     56\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtracking\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtracking\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     57\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mutil\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mcompat\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\training\\tracking\\graph_view.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mframework\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdtypes\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     26\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mframework\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mops\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 27\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0moptimizer\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0moptimizer_v1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     28\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msaving\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0msaveable_object\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0msaveable_object_lib\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     29\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtraining\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msaving\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0msaveable_object_util\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\training\\optimizer.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     26\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdistribute_lib\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 28\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdistribute_utils\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     29\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdistribution_strategy_context\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mdistribute_ctx\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mreduce_util\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mds_reduce_util\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\distribute\\distribute_utils.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mcollections\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mabc\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     22\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 23\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtpu_values\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtpu_values_lib\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     24\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mvalues\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mvalues_lib\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0meager\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mcontext\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\distribute\\tpu_values.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     26\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mpacked_distributed_variable\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mpacked\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 28\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtpu_util\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     29\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mvalues\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdistribute\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mvalues_util\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\distribute\\tpu_util.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     17\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mframework\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mops\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 18\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtpu\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtpu\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     19\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     20\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\tpu\\tpu.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     29\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0msix\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmoves\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mxrange\u001b[0m  \u001b[1;31m# pylint: disable=redefined-builtin\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 31\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcompiler\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtf2xla\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mxla\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtf2xla\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     32\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcore\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mframework\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mattr_value_pb2\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     33\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcore\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mprotobuf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtpu\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mdynamic_padding_pb2\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mdynamic_padding\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\compiler\\tf2xla\\python\\xla.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     41\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mrandom_ops\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     42\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mspecial_math_ops\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 43\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnumpy_ops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mnp_utils\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     44\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     45\u001b[0m \u001b[1;31m# TODO(phawkins): provide wrappers for all XLA operators. Currently the missing\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\ops\\numpy_ops\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m    171\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    172\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0marray_ops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mnewaxis\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 173\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnumpy_ops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mnp_random\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mrandom\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    174\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnumpy_ops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mnp_utils\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    175\u001b[0m \u001b[1;31m# pylint: disable=wildcard-import\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\anaconda3\\lib\\site-packages\\tensorflow\\python\\ops\\numpy_ops\\np_random.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m    108\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    109\u001b[0m \u001b[1;33m@\u001b[0m\u001b[0mnp_utils\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnp_doc\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'random.randint'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 110\u001b[1;33m \u001b[1;32mdef\u001b[0m \u001b[0mrandint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mlow\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mhigh\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0msize\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mdtype\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0monp\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mint\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m  \u001b[1;31m# pylint: disable=missing-function-docstring\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    111\u001b[0m   \u001b[0mlow\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mlow\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    112\u001b[0m   \u001b[1;32mif\u001b[0m \u001b[0mhigh\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__init__.py\u001b[0m in \u001b[0;36m__getattr__\u001b[1;34m(attr)\u001b[0m\n\u001b[0;32m    303\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    304\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mattr\u001b[0m \u001b[1;32min\u001b[0m \u001b[0m__former_attrs__\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 305\u001b[1;33m             \u001b[1;32mraise\u001b[0m \u001b[0mAttributeError\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0m__former_attrs__\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mattr\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    306\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    307\u001b[0m         \u001b[1;31m# Importing Tester requires importing all of UnitTest which is not a\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mAttributeError\u001b[0m: module 'numpy' has no attribute 'int'.\n`np.int` was a deprecated alias for the builtin `int`. To avoid this error in existing code, use `int` by itself. Doing this will not modify any behavior and is safe. When replacing `np.int`, you may wish to use e.g. `np.int64` or `np.int32` to specify the precision. If you wish to review your current use, check the release note link for additional information.\nThe aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:\n    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations"]}], "source": ["from __future__ import absolute_import, division, print_function, unicode_literals\n", "import tensorflow as tf\n", "import numpy as np\n", "import pandas as pd\n", "import SimpleITK as sitk\n", "from matplotlib import pyplot as plt\n", "from scipy.ndimage.interpolation import map_coordinates\n", "from scipy.ndimage.filters import gaussian_filter\n", "from scipy import ndimage\n", "import math\n", "from skimage.filters import threshold_mean\n", "from scipy.ndimage.interpolation import map_coordinates\n", "from scipy.ndimage.filters import gaussian_filter\n", "from scipy import ndimage\n", "import cv2\n", "import math"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reading Nifti Image "]}, {"cell_type": "markdown", "metadata": {}, "source": ["We are going to use *SimpleITK* python Libarary for most of our preprocessing task in this Notebook.Hopefully you will be find it very easy to use"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:06:52.450169Z", "start_time": "2020-07-21T09:06:51.159876Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'sitk' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_15328\\3064927775.py\u001b[0m in \u001b[0;36m<cell line: 7>\u001b[1;34m()\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;31m# CT\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 7\u001b[1;33m \u001b[0mimg_sitk\u001b[0m  \u001b[1;33m=\u001b[0m \u001b[0msitk\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mReadImage\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mct_path\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0msitk\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msitkFloat32\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;31m# Reading CT\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      8\u001b[0m \u001b[0mimage\u001b[0m     \u001b[1;33m=\u001b[0m \u001b[0msitk\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mGetArrayFromImage\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mimg_sitk\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;31m#Converting sitk_metadata to image Array\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[1;31m# Mask\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mNameError\u001b[0m: name 'sitk' is not defined"]}], "source": ["# Reading- Image\n", "#-|path of the data\n", "ct_path=r'K:\\2020-2023HCC\\579hcc\\模型代码总结2\\Githubcode\\3D-Medical-Imaging-Preprocessing-All-you-need-master\\Data\\img0001.nii.gz'\n", "ct_label_path=r'K:\\2020-2023HCC\\579hcc\\模型代码总结2\\Githubcode\\3D-Medical-Imaging-Preprocessing-All-you-need-master\\Data\\label0001.nii.gz'\n", "\n", "# CT\n", "img_sitk  = sitk.ReadImage(ct_path, sitk.sitkFloat32) # Reading CT\n", "image     = sitk.GetArrayFromImage(img_sitk) #Converting sitk_metadata to image Array\n", "# Mask\n", "mask_sitk = sitk.ReadImage(ct_label_path,sitk.sitkInt32) # Reading CT\n", "mask      = sitk.GetArrayFromImage(mask_sitk)#Converting sitk_metadata to image Array\n", "\n", "print('CT Shape={}'.format(image.shape))\n", "print('CT Mask Shape={}'.format(mask.shape))\n", "\n", "#----Lets plot \n", "f, axarr = plt.subplots(1,3,figsize=(15,15))\n", "axarr[0].imshow(np.squeeze(image[100, :, :]), cmap='gray',origin='lower');\n", "axarr[0].set_ylabel('Axial View',fontsize=14)\n", "axarr[0].set_xticks([])\n", "axarr[0].set_yticks([])\n", "axarr[0].set_title('CT',fontsize=14)\n", "\n", "axarr[1].imshow(np.squeeze(mask[100, :, :]), cmap='jet',origin='lower');\n", "axarr[1].axis('off')\n", "axarr[1].set_title('Mask',fontsize=14)\n", "\n", "axarr[2].imshow(np.squeeze(image[100, :, :]), cmap='gray',alpha=1,origin='lower');\n", "axarr[2].imshow(np.squeeze(mask[100, :, :]),cmap='jet',alpha=0.5,origin='lower');\n", "axarr[2].axis('off')\n", "axarr[2].set_title('Overlay',fontsize=14)\n", "plt.tight_layout()\n", "plt.subplots_adjust(wspace=0, hspace=0)"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-07-21T08:17:00.953657Z", "start_time": "2020-07-21T08:17:00.837462Z"}}, "source": ["# Intensity Normalization "]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-07-21T08:32:38.158826Z", "start_time": "2020-07-21T08:32:38.154804Z"}}, "source": ["Now we know how to load the nifti data. Next thing is to learn how to normalize the intensities"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:06:53.226243Z", "start_time": "2020-07-21T09:06:52.452165Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'image' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_15328\\1290365090.py\u001b[0m in \u001b[0;36m<cell line: 46>\u001b[1;34m()\u001b[0m\n\u001b[0;32m     44\u001b[0m     \u001b[1;32mreturn\u001b[0m \u001b[0mret\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     45\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 46\u001b[1;33m \u001b[0mNormalize_minun100_to_800hu\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mnormalise\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mimage\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     47\u001b[0m \u001b[0mNormalize_0mean_UnitVr\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mwhitening\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mimage\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     48\u001b[0m \u001b[0mNormalize_0to1\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mnormalise_zero_one\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mimage\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mNameError\u001b[0m: name 'image' is not defined"]}], "source": ["def normalise(image):\n", "    # normalise and clip images -1000 to 800\n", "    np_img = image\n", "    np_img = np.clip(np_img, -1000., 800.).astype(np.float32)\n", "    return np_img\n", "\n", "\n", "def whitening(image):\n", "    \"\"\"Whitening. Normalises image to zero mean and unit variance.\"\"\"\n", "\n", "    image = image.astype(np.float32)\n", "\n", "    mean = np.mean(image)\n", "    std = np.std(image)\n", "\n", "    if std > 0:\n", "        ret = (image - mean) / std\n", "    else:\n", "        ret = image * 0.\n", "    return ret\n", "\n", "\n", "def normalise_zero_one(image):\n", "    \"\"\"Image normalisation. Normalises image to fit [0, 1] range.\"\"\"\n", "\n", "    image = image.astype(np.float32)\n", "\n", "    minimum = np.min(image)\n", "    maximum = np.max(image)\n", "\n", "    if maximum > minimum:\n", "        ret = (image - minimum) / (maximum - minimum)\n", "    else:\n", "        ret = image * 0.\n", "    return ret\n", "\n", "\n", "def normalise_one_one(image):\n", "    \"\"\"Image normalisation. Normalises image to fit [-1, 1] range.\"\"\"\n", "\n", "    ret = normalise_zero_one(image)\n", "    ret *= 2.\n", "    ret -= 1.\n", "    return ret\n", "\n", "Normalize_minun100_to_800hu=normalise(image)\n", "Normalize_0mean_UnitVr=whitening(image)\n", "Normalize_0to1=normalise_zero_one(image)\n", "normalise_minus1_to1=normalise_one_one(image)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:06:53.897719Z", "start_time": "2020-07-21T09:06:53.228163Z"}}, "outputs": [], "source": ["f, axarr = plt.subplots(1, 5, figsize=(20,5));\n", "f.suptitle('Different intensity normalisation methods on a Abdomen CT')\n", "\n", "img = axarr[0].imshow(np.squeeze(image[100, :, :]), cmap='gray',origin='lower');\n", "axarr[0].axis('off')\n", "axarr[0].set_title('Original image')\n", "f.colorbar(img, ax=axarr[0])\n", "\n", "img = axarr[1].imshow(np.squeeze(Normalize_minun100_to_800hu[100, :, :]), cmap='gray',origin='lower');\n", "axarr[1].axis('off')\n", "axarr[1].set_title('-1000 to 800')\n", "f.colorbar(img, ax=axarr[1])\n", "\n", "img = axarr[2].imshow(np.squeeze(Normalize_0mean_UnitVr[100, :, :]), cmap='gray',origin='lower');\n", "axarr[2].axis('off')\n", "axarr[2].set_title('Zero mean/unit stdev')\n", "f.colorbar(img, ax=axarr[2])\n", "\n", "img = axarr[3].imshow(np.squeeze(Normalize_0to1[100, :, :]), cmap='gray',origin='lower');\n", "axarr[3].axis('off')\n", "axarr[3].set_title('[0,1] rescaling')\n", "f.colorbar(img, ax=axarr[3])\n", "\n", "img = axarr[4].imshow(np.squeeze(normalise_minus1_to1[100, :, :]), cmap='gray',origin='lower');\n", "axarr[4].axis('off')\n", "axarr[4].set_title('[-1,1] rescaling')\n", "f.colorbar(img, ax=axarr[4])\n", "    \n", "f.subplots_adjust(wspace=0.05, hspace=0, top=0.8)\n", "plt.show();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Resampling "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:07:03.110048Z", "start_time": "2020-07-21T09:06:53.899705Z"}}, "outputs": [], "source": ["def resample_img(itk_image, out_spacing=[2.0, 2.0, 2.0], is_label=False):\n", "    # resample images to 2mm spacing with simple itk\n", "\n", "    original_spacing = itk_image.GetSpacing()\n", "    original_size = itk_image.GetSize()\n", "\n", "    out_size = [\n", "        int(np.round(original_size[0] * (original_spacing[0] / out_spacing[0]))),\n", "        int(np.round(original_size[1] * (original_spacing[1] / out_spacing[1]))),\n", "        int(np.round(original_size[2] * (original_spacing[2] / out_spacing[2])))]\n", "\n", "    resample = sitk.ResampleImageFilter()\n", "    resample.SetOutputSpacing(out_spacing)\n", "    resample.SetSize(out_size)\n", "    resample.SetOutputDirection(itk_image.GetDirection())\n", "    resample.SetOutputOrigin(itk_image.GetOrigin())\n", "    resample.SetTransform(sitk.Transform())\n", "    resample.SetDefaultPixelValue(itk_image.GetPixelIDValue())\n", "\n", "    if is_label:\n", "        resample.SetInterpolator(sitk.sitkNearestNeighbor)\n", "    else:\n", "        resample.SetInterpolator(sitk.sitkBSpline)\n", "\n", "    return resample.Execute(itk_image)\n", "\n", "\n", "ct_resampled_to1m=resample_img(img_sitk, out_spacing=[1, 1, 1], is_label=False)\n", "ct_resampled_to1m=sitk.GetArrayFromImage(ct_resampled_to1m)\n", "mask_resampled_to1m=resample_img(mask_sitk, out_spacing=[1, 1, 1], is_label=True)\n", "mask_resampled_to1m=sitk.GetArrayFromImage(mask_resampled_to1m)\n", "print('CT Shape Original={}, Resampled to 1mm={}'.format(image.shape,ct_resampled_to1m.shape))\n", "print('CT Mask Shape={}, Resampled to 1mm={}'.format(mask.shape,mask_resampled_to1m.shape))\n", "\n", "#----Lets plot \n", "f, axarr = plt.subplots(1,3,figsize=(15,15))\n", "axarr[0].imshow(np.squeeze(ct_resampled_to1m[250, :, :]), cmap='gray',origin='lower');\n", "axarr[0].set_ylabel('Axial View',fontsize=14)\n", "axarr[0].set_xticks([])\n", "axarr[0].set_yticks([])\n", "axarr[0].set_title('CT',fontsize=14)\n", "\n", "axarr[1].imshow(np.squeeze(mask_resampled_to1m[250, :, :]), cmap='jet',origin='lower');\n", "axarr[1].axis('off')\n", "axarr[1].set_title('Mask',fontsize=14)\n", "\n", "axarr[2].imshow(np.squeeze(ct_resampled_to1m[250, :, :]), cmap='gray',alpha=1,origin='lower');\n", "axarr[2].imshow(np.squeeze(mask_resampled_to1m[250, :, :]),cmap='jet',alpha=0.5,origin='lower');\n", "axarr[2].axis('off')\n", "axarr[2].set_title('Overlay',fontsize=14)\n", "plt.tight_layout()\n", "plt.subplots_adjust(wspace=0, hspace=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Crop or Padding "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:07:03.962714Z", "start_time": "2020-07-21T09:07:03.112012Z"}}, "outputs": [], "source": ["def resize_image_with_crop_or_pad(image, img_size=(64, 64, 64), **kwargs):\n", "    \"\"\"Image resizing. Resizes image by cropping or padding dimension\n", "     to fit specified size.\n", "    Args:\n", "        image (np.ndarray): image to be resized\n", "        img_size (list or tuple): new image size\n", "        kwargs (): additional arguments to be passed to np.pad\n", "    Returns:\n", "        np.ndarray: resized image\n", "    \"\"\"\n", "\n", "    assert isinstance(image, (np.ndarray, np.generic))\n", "    assert (image.ndim - 1 == len(img_size) or image.ndim == len(img_size)), \\\n", "        'Example size doesnt fit image size'\n", "\n", "    # Get the image dimensionality\n", "    rank = len(img_size)\n", "\n", "    # Create placeholders for the new shape\n", "    from_indices = [[0, image.shape[dim]] for dim in range(rank)]\n", "    to_padding = [[0, 0] for dim in range(rank)]\n", "\n", "    slicer = [slice(None)] * rank\n", "\n", "    # For each dimensions find whether it is supposed to be cropped or padded\n", "    for i in range(rank):\n", "        if image.shape[i] < img_size[i]:\n", "            to_padding[i][0] = (img_size[i] - image.shape[i]) // 2\n", "            to_padding[i][1] = img_size[i] - image.shape[i] - to_padding[i][0]\n", "        else:\n", "            from_indices[i][0] = int(np.floor((image.shape[i] - img_size[i]) / 2.))\n", "            from_indices[i][1] = from_indices[i][0] + img_size[i]\n", "\n", "        # Create slicer object to crop or leave each dimension\n", "        slicer[i] = slice(from_indices[i][0], from_indices[i][1])\n", "\n", "    # Pad the cropped image to extend the missing dimension\n", "    return np.pad(image[slicer], to_padding, **kwargs)\n", "\n", "\n", "# Crop to [64, 64, 64]\n", "img_cropped = resize_image_with_crop_or_pad(image, [96, 96, 96], mode='symmetric')\n", "\n", "# Resizing image to [128, 256, 256] required padding\n", "img_padded = resize_image_with_crop_or_pad(image, [256, 544, 544], mode='symmetric')\n", "\n", "# Visualise using matplotlib.\n", "f, axarr = plt.subplots(1, 3, figsize=(15,15));\n", "axarr[0].imshow(np.squeeze(image[100, :, :]), cmap='gray',origin='lower');\n", "axarr[0].axis('off')\n", "axarr[0].set_title('Original image {}'.format(image.shape))\n", "\n", "axarr[1].imshow(np.squeeze(img_cropped[32, :, :]), cmap='gray',origin='lower');\n", "axarr[1].axis('off')\n", "axarr[1].set_title('Cropped to {}'.format(img_cropped.shape))\n", "\n", "axarr[2].imshow(np.squeeze(img_padded[140, :, :]), cmap='gray',origin='lower');\n", "axarr[2].axis('off')\n", "axarr[2].set_title('Padded to {}'.format(img_padded.shape))\n", "plt.tight_layout()\n", "plt.subplots_adjust(wspace=0, hspace=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Histogram Matching "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-07-21T09:09:03.656201Z", "start_time": "2020-07-21T09:09:03.651222Z"}}, "outputs": [], "source": ["def histogram_matching(mov_scan, ref_scan,\n", "                       histogram_levels=2048,\n", "                       match_points=100,\n", "                       set_th_mean=True):\n", "    \"\"\"\n", "    Histogram matching following the method developed on\n", "    <PERSON><PERSON><PERSON> et al 2001 (ITK implementation)\n", "    inputs:\n", "    - mov_scan: np.array containing the image to normalize\n", "    - ref_scan np.array containing the reference image\n", "    - histogram levels\n", "    - number of matched points\n", "    - Threshold Mean setting\n", "    outputs:\n", "    - histogram matched image\n", "    \"\"\"\n", "\n", "    # convert np arrays into itk image objects\n", "    ref = sitk.GetImageFromArray(ref_scan.astype('float32'))\n", "    mov = sitk.GetImageFromArray(mov_scan.astype('float32'))\n", "\n", "    # perform histogram matching\n", "    caster = sitk.CastImageFilter()\n", "    caster.SetOutputPixelType(ref.GetPixelID())\n", "\n", "    matcher = sitk.HistogramMatchingImageFilter()\n", "    matcher.SetNumberOfHistogramLevels(histogram_levels)\n", "    matcher.SetNumberOfMatchPoints(match_points)\n", "    matcher.SetThresholdAtMeanIntensity(set_th_mean)\n", "    matched_vol = matcher.Execute(mov, ref)\n", "\n", "    return matched_vol"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Maximum Intensity Projection "]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2020-07-22T06:09:05.248271Z", "start_time": "2020-07-22T06:09:05.239292Z"}}, "outputs": [], "source": ["import SimpleITK as sitk\n", "import os\n", "from os import listdir\n", "from os.path import isfile, join\n", "import glob\n", "import numpy as np\n", "\n", "'''\n", "This function was deigned to Give MIP for two label Left and Right Lung.\n", "if you have your own labels change or introduce for label indexes based on your need. \n", "Otherwise if you don't have labels set give_onlysegmented_data=False ony want to MIP between slices indicate the within how many slices it should count.\n", "'''\n", "\n", "def MIP_onrun(img,lbl,slices_num,give_onlysegmented_data=True):\n", "\n", "    \"\"\" Performed maximum instensity projection.\n", "\n", "    Args:\n", "        image (np.ndarray): image to perform mip\n", "        label (np.ndarray): labels to use for extraxting classes the classes\n", "        slices_num(int):\n", "\n", "    Returns:\n", "        Returned maximum intensity projected ndarray.\n", "        give_onlysegmented_data=True, returns only the segmented Regions.\n", "\n", "    \"\"\"\n", "    img_flatten=img.flatten()\n", "    lbl_flatten=lbl.flatten()\n", "    img_new=img.flatten()\n", "\n", "    ###Getting the Index for Right and Left Lung\n", "\n", "    idx_rl = np.argwhere(lbl_flatten == 2.0)\n", "    idx_ll = np.argwhere(lbl_flatten == 3.0)\n", "\n", "    #print(len(idx_rl))\n", "    #print(len(idx_ll))\n", "\n", "    idx_Index_1=np.argwhere(lbl_flatten<2)\n", "    #print(len(idx_Index_1))\n", "    idx_3up=np.argwhere(lbl_flatten>3)\n", "    #print(len(idx_3up))\n", "\n", "    ###########Removing all other labels\n", "    img_new[idx_Index_1]=0.0\n", "    img_new[idx_3up]=0.0\n", "\n", "    img_new_reshape=np.reshape(img_new,img.shape)\n", "\n", "\n", "    def createMIP(np_img, slices_num):\n", "        ''' create the mip image from original image, slice_num is the number of\n", "        slices for maximum intensity projection'''\n", "        img_shape = np_img.shape\n", "        np_mip = np.zeros(img_shape)\n", "        for i in range(img_shape[0]):\n", "            start = max(0, i-slices_num)\n", "            np_mip[i,:,:] = np.amax(np_img[start:i+1],0)\n", "        return np_mip\n", "\n", "    np_mip = createMIP(img_new_reshape,slices_num=slices_num)\n", "\n", "    if give_onlysegmented_data is True:\n", "\n", "       final_img=np_mip\n", "    else:\n", "\n", "        np_mip_flatten=np_mip.flatten()\n", "        np_mip_flatten[idx_Index_1]=img_flatten[idx_Index_1]\n", "        np_mip_flatten[idx_3up]=img_flatten[idx_3up]\n", "\n", "        final_img=np.reshape(np_mip_flatten,np_mip.shape)\n", "\n", "    return final_img\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}