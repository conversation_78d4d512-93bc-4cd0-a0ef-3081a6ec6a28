name: Auto Publish
on:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
        - uses: actions/checkout@v2
        - name: Set up Python
          uses: actions/setup-python@v2.2.2
          with:
            python-version: 3.9

        - name: Install poetry
          run: make setup
          
        - name: Install poeblix for version freezing
          run: poetry self add poeblix@latest
          
        - name: Bump version
          run: |
            poetry version prerelease

        - name: Commit changes
          run: |
            git config --local user.email "<EMAIL>"
            git config --local user.name "GitHub Action"
            git add .
            git commit -m "Bump version" || echo "No changes to commit"

        - name: Push changes
          run: git push

        - name: Build with lockfile versions
          run: poetry blixbuild --only-lock
          
        - name: Publish distribution 📦 to PyPI
          uses: pypa/gh-action-pypi-publish@release/v1
          with:
            password: ${{ secrets.PYPI_TOKEN }}
