#!/usr/bin/env python
# coding: utf-8
# ## 3. 3D Image Registration with Masks
# Often, some content in the images may not correspond. For example, there may be background content or noisy areas.
# A mask defined on the fixed and/or moving image can be used to exclude these regions at a pixel level from the similarity metric computations.
# This improves the robustness of registration.
# A mask is a binary image with 1 meaning that a pixel is include in elastix' computation and a 0 meaning it's not.
# For more information, see Section 5.4, "Masks" of the [Elastix Manual](https://elastix.lumc.nl/download/elastix-5.0.1-manual.pdf).

## 多个nii图像批量elastix配准
#根据ITKElastix包https://github.com/InsightSoftwareConsortium/ITKElastix里面的examples的ITK_Example03_Masked_3D_Registration.ipynb修改的
#安装itk-elastix程序包 pip install itk-elastix
import os
import glob
import itk
fixed_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\hbp_data"
moving_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data"
output_image_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\elastix-registered"

 # Get a list of all NIfTI files in fixed_path and moving_path
fixed_files = glob.glob(os.path.join(fixed_path, "*.nii"))
moving_files = glob.glob(os.path.join(moving_path, "*.nii"))
fixed_files[:5]
moving_files[:5]

 # Import Custom Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile(r'D:\anaconda3\envs\ITKElastix-main\examples\data\parameters.3D.NC.affine.ASGD.001.txt')

 # "WriteResultImage" needs to be set to "true" so that the image is resampled at the end of the registration
# and the result_image is populated properly
parameter_object.SetParameter(0, "WriteResultImage", "true")

 # Iterate over each pair of fixed and moving images
for fixed_file, moving_file in zip(fixed_files, moving_files):
    # Import Images
    fixed_image = itk.imread(fixed_file, itk.F)
    moving_image = itk.imread(moving_file, itk.F)
    print(fixed_file)
     # Call registration function
    result_image, result_transform_parameters = itk.elastix_registration_method(
        fixed_image, moving_image,
        parameter_object=parameter_object,
        log_to_console=False)
     # Save the result_image with the same pixel dimensions as fixed_image
    output_file = os.path.join(output_image_path, os.path.basename(moving_file))
    itk.imwrite(result_image, output_file)



## 未成功 根据fixed image，fixed mask对move image进行配准
import os
import glob
import itk
from itkwidgets import compare, checkerboard, view
fixed_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\hbp_data"
moving_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data"
fixed_mask_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\mask\hbp_mask"  # Update with the correct path
output_image_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\register"
 # Get a list of all NIfTI files in fixed_path, moving_path, and fixed_mask_path
fixed_files = glob.glob(os.path.join(fixed_path, "*.nii"))
moving_files = glob.glob(os.path.join(moving_path, "*.nii"))
fixed_mask_files = glob.glob(os.path.join(fixed_mask_path, "*.nii"))

 # Import Custom Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile(r'D:\anaconda3\envs\ITKElastix-main\examples\data\parameters.3D.NC.affine.ASGD.001.txt')
 # "WriteResultImage" needs to be set to "true" so that the image is resampled at the end of the registration
# and the result_image is populated properly
parameter_object.SetParameter(0, "WriteResultImage", "true")
parameter_object.SetParameter(0, "LogToConsoleOn", "true")

 # Iterate over each pair of fixed and moving images
for fixed_file, moving_file, fixed_mask_file in zip(fixed_files, moving_files, fixed_mask_files):
    # Import Images
    fixed_image = itk.imread(fixed_file, itk.F)
    moving_image = itk.imread(moving_file, itk.F)
    print(fixed_file)
     # Import Mask Images
    fixed_mask = itk.imread(fixed_mask_file, itk.UC)
    moving_mask = itk.imread(fixed_mask_file, itk.UC)
     # Call registration function
    result_image, result_transform_parameters = itk.elastix_registration_method(
        fixed_image, moving_image,
        parameter_object=parameter_object,
        fixed_mask=fixed_mask,
        moving_mask=moving_mask,
        log_to_console=True)
     # Save the result_image with the same pixel dimensions as fixed_image
    output_file = os.path.join(output_image_path, os.path.basename(moving_file))
    itk.imwrite(result_image, output_file)

##In[1]:单个图像elastix配准(可带mask)

import itk
from itkwidgets import compare, checkerboard, view

fixed_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\hbp_data\bianzhenhua.nii"
moving_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data\bianzhenhua.nii"
output_image_path = r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\bianzhenhua.nii"

# Import Images
fixed_image = itk.imread(fixed_path, itk.F)
moving_image = itk.imread(moving_path, itk.F)

# Import Custom Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile(r'D:\anaconda3\envs\ITKElastix-main\examples\data\parameters.3D.NC.affine.ASGD.001.txt')

# "WriteResultImage" needs to be set to "true" so that the image is resampled at the end of the registration
# and the result_image is populated properly
parameter_object.SetParameter(0, "WriteResultImage", "true")

# Import Mask Images
# fixed_mask = itk.imread(r'D:\anaconda3\envs\ITKElastix-main\examples\data\CT_3D_lung_fixed_mask.mha', itk.UC)
# moving_mask = itk.imread(r'D:\anaconda3\envs\ITKElastix-main\examples\data\CT_3D_lung_moving_mask.mha', itk.UC)

# ### Input Visualization
# The images and their masks can be visualized with the itkwidget's view function. This can be useful to visually inspect the quality of the masks.

# view(fixed_image, label_image=fixed_mask)
# view(moving_image, label_image = moving_mask)

# Registration can either be done in one line with the registration function...

# 方法1 Call registration function
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image,
    parameter_object=parameter_object,
    # fixed_mask=fixed_mask, moving_mask=moving_mask,
    log_to_console=False)

itk.imwrite(result_image, output_image_path)

# 方法2 or by initializing the ElastixRegistrationMethod and then running the registration method.
# Load Elastix Image Filter Object
# # Fixed and moving image should be given to the Elastix method to ensure that
# # the correct 3D class is initialized.
# elastix_object = itk.ElastixRegistrationMethod.New(fixed_image, moving_image)
# # elastix_object.SetFixedMask(fixed_mask)
# # elastix_object.SetMovingMask(moving_mask)
# elastix_object.SetParameterObject(parameter_object)
#
# # Set additional options
# elastix_object.SetLogToConsole(False)
#
# # Update filter object (required)
# elastix_object.UpdateLargestPossibleRegion()
#
# # Results of Registration
# result_image = elastix_object.GetOutput()
# result_transform_parameters = elastix_object.GetTransformParameterObject()
#
# itk.imwrite(result_image, output_image_path)

# ### Output Visualization
# The results of the 3D image registration can also be visualized with widgets from the itkwidget library such as the checkerboard and compare widgets.

# checkerboard(fixed_image, result_image,pattern=5)

# compare(fixed_image, result_image, label_image= [fixed_image, result_image],link_cmap=True)


