# 病理图像多实例学习 (Pathology Multiple Instance Learning)

## 概述
该项目实现了基于深度学习的病理图像多实例学习方法，用于从病理patch级别的图像特征聚合得到患者级别的诊断特征。

## 主要功能

### 1. 特征提取
- 使用预训练的ResNet模型(ResNet18/ResNet50)提取patch级别特征
- 支持自定义特征维度
- 包含特征投影层进行降维

### 2. 多实例学习聚合方法
- **注意力机制MIL**: 使用注意力权重对patch特征进行加权聚合
- **平均池化MIL**: 简单的平均池化聚合
- **最大池化MIL**: 最大值池化聚合

### 3. 完整的训练和推理流程
- 支持数据增强
- 包含验证和测试评估
- 模型保存和加载
- 训练过程可视化

## 代码结构

### 核心类
1. `PatchDataset`: 处理患者级别的patch数据
2. `FeatureExtractor`: 提取patch特征的CNN backbone
3. `AttentionMIL`: 基于注意力机制的MIL聚合
4. `MeanPoolingMIL`: 平均池化MIL聚合
5. `MaxPoolingMIL`: 最大池化MIL聚合
6. `PathologyMIL`: 主要的MIL训练和推理类

### 主要方法
- `train_model()`: 训练模型
- `evaluate()`: 评估模型性能
- `predict()`: 进行预测
- `save_model()` / `load_model()`: 模型保存和加载

## 使用方法

### 1. 数据准备
数据目录结构应为:
```
data_dir/
├── class_0/
│   ├── patient_001/
│   │   ├── patch_001.png
│   │   ├── patch_002.png
│   │   └── ...
│   └── patient_002/
└── class_1/
    ├── patient_003/
    └── ...
```

### 2. 训练模型
```python
# 初始化模型
mil_model = PathologyMIL(
    feature_extractor_name='resnet18',
    mil_method='attention',  # 或 'mean', 'max'
    feature_dim=512,
    num_classes=2
)

# 训练
history = mil_model.train_model(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=50,
    learning_rate=1e-4
)
```

### 3. 评估和预测
```python
# 评估
test_loss, test_acc, test_auc = mil_model.evaluate(test_loader)

# 预测
results = mil_model.predict(test_loader)
```

## 依赖库
- torch
- torchvision
- numpy
- pandas
- scikit-learn
- PIL
- matplotlib
- seaborn

## 特点
1. **灵活的架构**: 支持多种特征提取器和MIL聚合方法
2. **注意力可视化**: 可以获取注意力权重进行解释性分析
3. **完整的训练流程**: 包含数据加载、训练、验证、测试
4. **性能监控**: 实时监控训练损失、准确率和AUC
5. **模型保存**: 支持模型检查点保存和加载

## 适用场景
- 病理图像分类
- 医学图像诊断
- 任何需要从局部特征聚合到全局特征的场景