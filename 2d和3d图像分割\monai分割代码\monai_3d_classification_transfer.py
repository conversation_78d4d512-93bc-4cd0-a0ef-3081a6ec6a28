import os
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import optuna
from optuna.trial import Trial

from monai.networks.nets import DenseNet121, ResNet
from monai.transforms import (
    Compose,
    LoadImaged,
    AddChanneld,
    ScaleIntensityd,
    RandRotate90d,
    Resized,
    ToTensord,
)
from monai.data import Dataset, partition_dataset
from monai.utils import set_determinism

# 设置随机种子以确保可重复性
set_determinism(seed=0)

class TransferLearning3DClassifier(nn.Module):
    def __init__(self, model_name="densenet", num_classes=2, pretrained=True):
        super().__init__()
        
        if model_name == "densenet":
            self.model = DenseNet121(
                spatial_dims=3,
                in_channels=1,
                out_channels=num_classes,
                pretrained=pretrained,
            )
            
            if pretrained:
                for param in self.model.features.parameters():
                    param.requires_grad = False
                
                num_features = self.model.class_layers.out.in_features
                self.model.class_layers.out = nn.Linear(
                    in_features=num_features,
                    out_features=num_classes
                )
                
        elif model_name == "resnet":
            self.model = ResNet(
                block="basic",
                layers=[3, 4, 6, 3],
                block_inplanes=[64, 128, 256, 512],
                spatial_dims=3,
                n_input_channels=1,
                num_classes=num_classes,
            )
            
            if pretrained:
                for name, param in self.model.named_parameters():
                    if "fc" not in name:
                        param.requires_grad = False

    def forward(self, x):
        return self.model(x)

def create_data_loaders(train_files, val_files, batch_size, spatial_size=(96, 96, 96)):
    train_transforms = Compose([
        LoadImaged(keys=["image"]),
        AddChanneld(keys=["image"]),
        ScaleIntensityd(keys=["image"]),
        Resized(keys=["image"], spatial_size=spatial_size),
        RandRotate90d(keys=["image"], prob=0.5, spatial_axes=[0, 1]),
        ToTensord(keys=["image"])
    ])

    val_transforms = Compose([
        LoadImaged(keys=["image"]),
        AddChanneld(keys=["image"]),
        ScaleIntensityd(keys=["image"]),
        Resized(keys=["image"], spatial_size=spatial_size),
        ToTensord(keys=["image"])
    ])

    train_ds = Dataset(data=train_files, transform=train_transforms)
    val_ds = Dataset(data=val_files, transform=val_transforms)

    train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_ds, batch_size=batch_size, num_workers=4)

    return train_loader, val_loader

def objective(trial: Trial, train_files, val_files):
    # 超参数搜索空间
    config = {
        "batch_size": trial.suggest_int("batch_size", 2, 8),
        "lr": trial.suggest_loguniform("lr", 1e-5, 1e-3),
        "spatial_size": trial.suggest_categorical("spatial_size", [(64, 64, 64), (96, 96, 96), (128, 128, 128)]),
        "unfreeze_epoch": trial.suggest_int("unfreeze_epoch", 5, 30),
        "fine_tune_lr": trial.suggest_loguniform("fine_tune_lr", 1e-6, 1e-4)
    }
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建数据加载器
    train_loader, val_loader = create_data_loaders(
        train_files, 
        val_files, 
        config["batch_size"],
        config["spatial_size"]
    )
    
    # 初始化模型
    model = TransferLearning3DClassifier(
        model_name="densenet",
        num_classes=2,
        pretrained=True
    ).to(device)
    
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(
        filter(lambda p: p.requires_grad, model.parameters()),
        lr=config["lr"]
    )
    
    best_val_acc = 0.0
    patience = 5
    patience_counter = 0
    
    # 训练循环
    for epoch in range(50):
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        for batch_data in train_loader:
            inputs = batch_data["image"].to(device)
            labels = batch_data["label"].to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = outputs.max(1)
            train_total += labels.size(0)
            train_correct += predicted.eq(labels).sum().item()
        
        # 验证
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch_data in val_loader:
                inputs = batch_data["image"].to(device)
                labels = batch_data["label"].to(device)
                
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = outputs.max(1)
                val_total += labels.size(0)
                val_correct += predicted.eq(labels).sum().item()
        
        val_acc = val_correct / val_total
        
        # 早停
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            break
        
        # 解冻特征提取器
        if epoch == config["unfreeze_epoch"]:
            print("Unfreezing feature extractor...")
            for param in model.model.features.parameters():
                param.requires_grad = True
            optimizer = torch.optim.Adam(
                filter(lambda p: p.requires_grad, model.parameters()),
                lr=config["fine_tune_lr"]
            )
        
        # 报告中间值给Optuna
        trial.report(val_acc, epoch)
        
        # 处理提前停止
        if trial.should_prune():
            raise optuna.TrialPruned()
    
    return best_val_acc

def main():
    # 准备数据
    all_files = [
        {"image": "path/to/image1.nii.gz", "label": 0},
        {"image": "path/to/image2.nii.gz", "label": 1},
        # 添加更多训练数据
    ]
    
    # 划分训练集和验证集
    train_files, val_files = partition_dataset(
        data=all_files,
        ratios=[0.8, 0.2],
        shuffle=True
    )
    
    # 创建Optuna study对象
    study = optuna.create_study(
        direction="maximize",
        pruner=optuna.pruners.MedianPruner()
    )
    
    # 开始超参数优化
    study.optimize(
        lambda trial: objective(trial, train_files, val_files),
        n_trials=20,  # 优化尝试次数
        timeout=3600  # 最大运行时间（秒）
    )
    
    # 打印最佳超参数
    print("Best trial:")
    trial = study.best_trial
    print("  Value: ", trial.value)
    print("  Params: ")
    for key, value in trial.params.items():
        print(f"    {key}: {value}")
    
    # 使用最佳超参数训练最终模型
    best_config = trial.params
    train_loader, val_loader = create_data_loaders(
        train_files,
        val_files,
        best_config["batch_size"],
        best_config["spatial_size"]
    )
    
    # 训练最终模型...
    # [此处可以添加使用最佳参数训练最终模型的代码]

if __name__ == "__main__":
    main()