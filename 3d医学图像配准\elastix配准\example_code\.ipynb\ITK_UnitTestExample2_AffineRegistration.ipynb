{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def image_generator(x1, x2, y1, y2):\n", "    image = np.zeros([100, 100], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    image = itk.image_view_from_array(image)\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affine Test"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create test images\n", "fixed_image_affine = image_generator(25,75,25,75)\n", "moving_image_affine = image_generator(1,71,1,91)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "default_affine_parameter_map = parameter_object.GetDefaultParameterMap('affine',4)\n", "default_affine_parameter_map['FinalBSplineInterpolationOrder'] = ['0']\n", "parameter_object.AddParameterMap(default_affine_parameter_map)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image_affine, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image_affine, moving_image_affine,\n", "    parameter_object=parameter_object,\n", "    log_to_console=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization Affine Test"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 2160x2160 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(1,3, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0].imshow(result_image_affine)\n", "axs[0].set_title('Result', fontsize=30)\n", "axs[1].imshow(fixed_image_affine)\n", "axs[1].set_title('Fixed', fontsize=30)\n", "axs[2].imshow(moving_image_affine)\n", "axs[2].set_title('Moving', fontsize=30)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}