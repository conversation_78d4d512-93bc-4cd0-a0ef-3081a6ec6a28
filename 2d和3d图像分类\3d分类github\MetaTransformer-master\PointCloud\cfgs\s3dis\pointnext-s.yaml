# Model Information
# GFLOPs  GMACs   Params.(M)
#  3.64    1.79    0.792
# Throughput (ins./s): 226.73038739659074 

model:
  NAME: BaseSeg
  encoder_args:
    NAME: PointNextEncoder
    blocks: [1, 1, 1, 1, 1]
    strides: [1, 4, 4, 4, 4]
    sa_layers: 2 
    sa_use_res: True 
    width: 32
    in_channels: 4
    expansion: 4
    radius: 0.1
    nsample: 32
    aggr_args:
      feature_type: 'dp_fj'
      reduction: 'max'
    group_args:
      NAME: 'ballquery'
      normalize_dp: True
    conv_args:
      order: conv-norm-act
    act_args:
      act: 'relu'
    norm_args:
      norm: 'bn'
  decoder_args:
    NAME: PointNextDecoder
  cls_args:
    NAME: SegHead
    num_classes: 13
    in_channels: null
    norm_args:
      norm: 'bn'


# do not conduct rotation in small model.
datatransforms:
  train: [ChromaticAutoContrast, PointsToTensor, PointCloudScaling, PointCloudXYZ<PERSON>lign, PointCloudJitter, ChromaticDropGPU, ChromaticNormalize]
  val: [<PERSON>ToTensor, PointCloudXYZAlign, ChromaticNormalize]
  vote: [ChromaticDropGPU]
  kwargs:
    color_drop: 0.2
    gravity_dim: 2
    scale: [0.9, 1.1]
    jitter_sigma: 0.005
    jitter_clip: 0.02