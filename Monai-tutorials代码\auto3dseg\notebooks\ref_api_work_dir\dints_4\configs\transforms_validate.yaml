_meta_: {}
image_key: image
label_key: label
transforms_validate:
  _target_: Compo<PERSON>
  transforms:
  - _target_: Compose
    transforms:
    - {_target_: LoadImaged, dtype: $np.float32, image_only: false, keys: '@image_key'}
    - {_target_: LoadImaged, dtype: $np.uint8, image_only: false, keys: '@label_key'}
  - _target_: EnsureChannelFirstd
    keys: ['@image_key', '@label_key']
  - {_target_: NormalizeIntensityd, channel_wise: true, keys: '@image_key', nonzero: true}
  - _target_: Orientationd
    axcodes: RAS
    keys: ['@image_key', '@label_key']
  - _target_: Spacingd
    align_corners: [true, true]
    keys: ['@image_key', '@label_key']
    mode: [bilinear, nearest]
    pixdim: '@training#transforms#resample_resolution'
  - _target_: CastToTyped
    dtype: [$torch.float32, $torch.uint8]
    keys: ['@image_key', '@label_key']
  - _target_: EnsureTyped
    keys: ['@image_key', '@label_key']
    track_meta: false
