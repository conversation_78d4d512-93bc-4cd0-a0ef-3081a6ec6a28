#!/usr/bin/env python
# coding: utf-8

# # Spleen 3D segmentation with MONAI
# This tutorial shows how to integrate MONAI into an existing PyTorch medical DL program.

# And easily use below features:
# 1. Transforms for dictionary format data.
# 1. Load Nifti image with metadata.
# 1. Add channel dim to the data if no channel dimension.
# 1. Scale medical image intensity with expected range.
# 1. Crop out a batch of balanced images based on positive / negative label ratio.
# 1. Cache IO and transforms to accelerate training and validation.
# 1. 3D UNet model, Dice loss function, Mean Dice metric for 3D segmentation task.
# 1. Sliding window inference method.
# 1. Deterministic training for reproducibility.

# The Spleen dataset can be downloaded from http://medicaldecathlon.com/.
# [![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/spleen_segmentation_3d.ipynb)
#%% Autodl云服务器和本地图像快速传输方法
# 复制您的ssh登录指令，指令格式为：ssh -p 12603 <EMAIL>

# （注意35394为端口号，region-1.autodl.com为远程地址，请更换为您的实例端口和地址）

# 那么scp远程拷贝文件的指令为：
# scp -rP 12603 "H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\503HCC\image\ap.zip" <EMAIL>:/root/autodl-tmp/HCC503ap 
# # （注意需要在您本地的机器的终端上执行）

# # 如果是将实例中的数据拷贝到本地，那么scp远程拷贝指令为：
# # scp -rP 35394 root@************:<实例中的文件/文件夹> <本地文件/文件夹>
# scp -rP 47477 <EMAIL>:/root/autodl-tmp/nnUNet/nnUNet_results/Dataset503 'j:\nnUNet'

# In[1]: 3d unet,swinunetr模型图像分割，成功了
# 自己数据集运行成功，在autodl；适用于标签为2，即病灶和背景

# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[gdown, nibabel, tqdm, ignite]"')
# get_ipython().system('python -c "import matplotlib" || pip install -q matplotlib')
# get_ipython().run_line_magic('matplotlib', 'inline')

# pip install monai nibabel matplotlib pandas einops openpyxl

import monai
print(monai.__version__)

# In[2]:
from monai.utils import first, set_determinism
from monai.transforms import (
    AsDiscrete,
    AsDiscreted,
    EnsureChannelFirstd,
    Compose,
    CropForegroundd,
    LoadImaged,
    Orientationd,
    RandCropByPosNegLabeld,#注意一定要用这个，不能用RandSpatialCropd
    SaveImaged,
    ScaleIntensityRanged,
    Spacingd,
    Invertd,
    RandRotate90d,
    RandFlipd,
    RandScaleIntensityd,
    RandAffined,
    SpatialPadd,
    ResizeWithPadOrCropd
)
from monai.handlers.utils import from_engine
from monai.networks.nets import UNet,UNETR,VNet,SwinUNETR,AttentionUnet,SegResNet
from monai.networks.layers import Norm
from monai.metrics import DiceMetric
from monai.losses import DiceCELoss, FocalLoss
from monai.inferers import sliding_window_inference
from monai.data import CacheDataset, DataLoader, Dataset, decollate_batch
from monai.config import print_config
from monai.apps import download_and_extract
import torch
import matplotlib.pyplot as plt
import tempfile
import shutil
import os
import glob
import nibabel as nib
import pandas as pd
print_config()

#%%图像报错处理：使用重抽样，使得image和mask的Direction、origin和spacing一致
# pip install SimpleITK==2.3.1
# import SimpleITK as sitk
# import os

# # 指定图像和标签路径
# image_dir = '/root/autodl-tmp/HCC320/image/ap'
# label_dir = '/root/autodl-tmp/HCC320/mask/ap'

# # 获取所有 .nii.gz 文件并排序
# nii_images = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])
# nii_labels = sorted([filename for filename in os.listdir(label_dir) if filename.endswith('.nii.gz')])

# # 遍历图像和标签
# for img_filename, label_filename in zip(nii_images, nii_labels):
#     # 构建完整路径
#     image_path = os.path.join(image_dir, img_filename)
#     label_path = os.path.join(label_dir, label_filename)
    
#     try:
#         # 检查文件大小是否为0
#         if os.path.getsize(image_path) == 0 or os.path.getsize(label_path) == 0:
#             print(f"警告: 文件 {image_path} 或 {label_path} 大小为0，跳过")
#             continue
            
#         # 读取图像和标签
#         image = sitk.ReadImage(image_path)
#         label = sitk.ReadImage(label_path)
        
#         # 检查方向、原点和间距是否一致
#         if (image.GetDirection() != label.GetDirection() or
#             image.GetOrigin() != label.GetOrigin() or
#             image.GetSpacing() != label.GetSpacing()):
            
#             # 创建一个默认的 Transform（Identity Transform）
#             transform = sitk.Transform()
            
#             # 使用图像作为参考对标签进行重采样
#             resampled_label = sitk.Resample(
#                 label,  # 要重采样的图像
#                 image,  # 参考图像
#                 transform,  # 使用单位变换
#                 sitk.sitkNearestNeighbor,  # 插值方式
#                 0,  # 默认像素值
#                 label.GetPixelID()  # 输出像素类型
#             )
            
#             # 保存重采样后的标签
#             resampled_label_path = os.path.join(label_dir, label_filename)
#             sitk.WriteImage(resampled_label, resampled_label_path)
#             print(f"Resampled label saved for {label_filename}")

#         # 打印图像和标签的形状
#         print(f"Image shape for {img_filename}: {image.GetSize()}, Label shape for {label_filename}: {label.GetSize()}")
    
#     except Exception as e:
#         print(f"错误处理文件 {image_path} 和 {label_path}: {e}")
#         continue

#%% 要检查image和mask目录下的文件形状shape是否匹配
#解决报错ValueError: operands could not be broadcast together with shapes (4,) (3,)
# import os
# import SimpleITK as sitk

# # 目标目录
# # image_dir = r"H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp"
# # mask_dir = r"H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\mask\hbp"

# image_dir =  '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr'
# mask_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/labelsTr'


# # 获取目录下的所有文件
# image_files = sorted(os.listdir(image_dir))
# mask_files = sorted(os.listdir(mask_dir))

# # 检查文件形状是否匹配
# for image_file, mask_file in zip(image_files, mask_files):
#     image_path = os.path.join(image_dir, image_file)
#     mask_path = os.path.join(mask_dir, mask_file)
    
#     # 读取图像和掩码文件
#     image = sitk.ReadImage(image_path)
#     mask = sitk.ReadImage(mask_path)
    
#     # 获取形状
#     image_shape = image.GetSize()
#     mask_shape = mask.GetSize()
    
#     # 检查形状是否匹配
#     if image_shape != mask_shape:
#         print(f"Shape mismatch: {image_file} (image) vs {mask_file} (mask)")
#         print(f"Image shape: {image_shape}, Mask shape: {mask_shape}")

# #%%检查image和label的shape,保留前3个维度,并保存图像
# import nibabel as nib
# import os

# # 指定图像路径
# image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr'
# # image_dir = r"J:\nnUNet\nnUNet_raw\Dataset506\imagesTr"
# output_dir = image_dir   # 输出路径

# # 确保输出目录存在
# os.makedirs(output_dir, exist_ok=True)

# # 获取所有 .nii.gz 文件并排序
# nii_files = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])

# # 遍历排序后的文件
# for filename in nii_files:
#     # 加载图像
#     img_path = os.path.join(image_dir, filename)
#     img = nib.load(img_path)
#     image_data = img.get_fdata()    
    
#     # 只保留前三个维度
#     # if image_data.ndim > 3:       
#     #     image_data = image_data[:, :, :, 0, 0]  # 维度=5时，直接去掉最后2个维度
#         # image_data = image_data[:, :, :, 0]  # 维度=4时，直接去掉最后1个维度

#     # 只保留前三个维度
#     if image_data.ndim >= 4:
#         image_data = image_data[:, :, :, 0]  # 维度=4或5时，保留前三个维度

#     # 创建新的 NIfTI 图像对象
#     new_img = nib.Nifti1Image(image_data, img.affine, img.header)
    
#     # 保存新的图像
#     new_filename = os.path.join(output_dir, filename)
#     nib.save(new_img, new_filename)
    
#     # 打印新的形状
#     print(f"Image shape for {filename}: {image_data.shape}, Number of dimensions: {image_data.ndim}")

#%%自己的数据集
# root_dir = '/root/autodl-tmp/Task09_Spleen'
# train_images = '/root/autodl-tmp/Task09_Spleen/imagesTr'  
# train_labels = '/root/autodl-tmp/Task09_Spleen/labelsTr'

root_dir = '/root/autodl-tmp/120HCC'
train_images = '/root/autodl-tmp/120HCC/image/ap'
train_labels = '/root/autodl-tmp/120HCC/mask/ap'

train_images_list = sorted(glob.glob(os.path.join(train_images, "*.nii.gz")))
train_labels_list = sorted(glob.glob(os.path.join(train_labels, "*.nii.gz")))

print(f"Number of training images: {len(train_images_list)}")
print(f"Number of training labels: {len(train_labels_list)}")

data_dicts = [{"image": image_name, "label": label_name} for image_name, label_name in zip(train_images_list, train_labels_list)]

split_index = int(len(data_dicts) * 0.7)# 按照 8:2 划分
train_files, val_files = data_dicts[:split_index], data_dicts[split_index:]
print(f"Total number of samples: {len(data_dicts)}")
print(f"Number of training samples: {len(train_files)}")
print(f"Number of validation samples: {len(val_files)}")
train_files[:5]

# In[6]:# Set deterministic training for reproducibility

set_determinism(seed=0)

# In[7]:数据增强与预处理
import numpy as np
from monai.transforms import LoadImaged

# 优化 spatial_size，增大尺寸以获得更多上下文信息
spatial_size = (128, 128, 64)  # 增大尺寸，提供更多上下文信息

# 优化数据预处理和增强策略
train_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        # 优化强度归一化范围，基于HCC图像特点
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-100,  # 扩大范围以保留更多细节
            a_max=400,   # 适应HCC增强特点
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        # 优化重采样参数
        Spacingd(keys=["image", "label"], pixdim=(1.0, 1.0, 1.5), mode=("bilinear", "nearest")),
        # 确保最小尺寸
        SpatialPadd(
            keys=["image", "label"],
            spatial_size=spatial_size,
            mode="constant"
        ),
        # 优化正负样本比例，增加正样本比例
        RandCropByPosNegLabeld(
            keys=["image", "label"],
            label_key="label",
            spatial_size=spatial_size,
            pos=3,  # 增加正样本数量
            neg=1,  # 减少负样本比例
            num_samples=4,  # 增加样本数量
            image_key="image",
            image_threshold=0,
            allow_smaller=True,  # 允许较小的图像
        ),
        # 减少数据增强强度，避免过度变形
        RandFlipd(keys=["image", "label"], prob=0.3, spatial_axis=0),
        RandFlipd(keys=["image", "label"], prob=0.3, spatial_axis=1),
        RandFlipd(keys=["image", "label"], prob=0.3, spatial_axis=2),
        RandScaleIntensityd(keys="image", factors=0.05, prob=0.3),  # 减少强度变化
        RandRotate90d(keys=["image", "label"], prob=0.2),  # 减少旋转概率
        # 减少仿射变换强度
        RandAffined(
            keys=["image", "label"],
            prob=0.3,  # 降低概率
            rotate_range=(0.02, 0.02, 0.02),  # 减少旋转范围
            scale_range=(0.05, 0.05, 0.05),   # 减少缩放范围
            mode=("bilinear", "nearest"),
        ),
    ]
)
# 优化验证集预处理，与训练集保持一致
val_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        # 与训练集使用相同的强度归一化
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-100,
            a_max=400,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        # 与训练集使用相同的重采样参数
        Spacingd(keys=["image", "label"], pixdim=(1.0, 1.0, 1.5), mode=("bilinear", "nearest")),
        # 先裁剪前景，保留病灶区域
        CropForegroundd(keys=["image", "label"], source_key="image", margin=10),
        # 确保尺寸一致
        ResizeWithPadOrCropd(keys=["image", "label"], spatial_size=spatial_size),
    ]
)


# In[8]:

check_ds = Dataset(data=val_files, transform=val_transforms)
check_loader = DataLoader(check_ds, batch_size=2)
check_data = first(check_loader)
image, label = (check_data["image"][0][0], check_data["label"][0][0])
print(f"image shape: {image.shape}, label shape: {label.shape}")
# plot the slice [:, :, 80]
plt.figure("check", (12, 6))
plt.subplot(1, 2, 1)
plt.title("image")
plt.imshow(image[:, :, 16], cmap="gray")
plt.subplot(1, 2, 2)
plt.title("label")
plt.imshow(label[:, :, 16])
plt.show()

## Define CacheDataset and DataLoader for training and validation
# Here we use CacheDataset to accelerate training and validation process, it's 10x faster than the regular Dataset.  
# To achieve best performance, set `cache_rate=1.0` to cache all the data, if memory is not enough, set lower value.  
# Users can also set `cache_num` instead of `cache_rate`, will use the minimum value of the 2 settings.  
# And set `num_workers` to enable multi-threads during caching.  
# If want to to try the regular Dataset, just change to use the commented code below.

# In[9]:
train_ds = CacheDataset(data=train_files, transform=train_transforms, cache_rate=1, num_workers=2)
# train_ds = Dataset(data=train_files, transform=train_transforms)
train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=2)  # 减小batch_size因为每个样本会产生多个patches

val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_rate=1, num_workers=2)
# val_ds = Dataset(data=val_files, transform=val_transforms)
val_loader = DataLoader(val_ds, batch_size=2, num_workers=2)

#%%定义模型、损失函数与优化器
from monai.networks.nets import UNet,UNETR,VNet,SwinUNETR,AttentionUnet,SegResNet
from monai.losses import DiceCELoss, FocalLoss
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


#1.优化的UNet模型 - 增加模型容量和正则化
model = UNet(
    spatial_dims=3,
    in_channels=1,
    out_channels=2,  # 病灶和背景，所以label为2
    channels=(32, 64, 128, 256, 512),  # 增加通道数，提升模型容量
    strides=(2, 2, 2, 2),
    num_res_units=3,  # 增加残差单元数量
    norm=Norm.BATCH,
    dropout=0.2,  # 适度降低dropout，避免欠拟合
).to(device)

#2.UNETR模型(TransformerUnet)成功了!
# model = UNETR(
#     in_channels=1,
#     out_channels=2,#病灶和背景，所以label为2
#     img_size=(96, 96, 32),
#     feature_size=16,
#     hidden_size=768,
#     mlp_dim=3072,
#     num_heads=12,
#     pos_embed="perceptron",
#     norm_name="instance",
#     res_block=True,
#     dropout_rate=0.0,
# ).to(device)
# print(model)

#3.Swinunetr模型(SwinTransformerUnet)成功了!最新的！
# model = SwinUNETR(
#     img_size=(96, 96, 32),  # 使用更小的z维度
#     in_channels=1,
#     out_channels=2,
#     feature_size=48,
#     use_checkpoint=True,
# ).to(device)
# print(model)

# 4.SegResNet模型成功了!
# model = SegResNet(
#     spatial_dims=3,          # 输入的空间维度（3D）
#     init_filters=8,          # 初始卷积层的过滤器数量
#     in_channels=1,           # 输入通道数（例如，单通道灰度图像）
#     out_channels=2,          # 输出通道数（例如，背景和目标）
#     dropout_prob=0.1,       # Dropout 概率，用于正则化    
#     num_groups=8,           # 组归一化的组数
#     use_conv_final=True,     # 是否使用最终卷积层   
# ).to(device)
# print(model)

# 5.VNet模型成功了!
# model = VNet(
#     spatial_dims=3,          # 输入的空间维度（3D）
#     in_channels=1,           # 输入通道数（例如，单通道灰度图像）
#     out_channels=2,          # 输出通道数（例如，分割的类别数）
#     dropout_prob_down=0.2,   # 下采样阶段的 Dropout 概率
#     dropout_prob_up=(0.2, 0.2),   # 上采样阶段的 Dropout 概率
#     dropout_dim=3,                # Dropout 的维度
#     bias=False,                   # 是否在卷积层中使用偏置
# ).to(device)
# print(model)


# 6.AttentionUnet模型成功了!慢！
# model = AttentionUnet(
#     spatial_dims=3, # 3D 输入
#     in_channels=1,
#     out_channels=2,
#     channels = [32, 64, 128],  # 各层的通道数
#     strides=[1, 2] ,
#     kernel_size=3,
#     up_kernel_size=3,
#     dropout=0
# ).to(device)
# print(model)

#%% 优化损失函数 - 先使用简单版本确保运行
# 使用标准DiceCELoss，后续可以逐步优化
loss_function = DiceCELoss(to_onehot_y=True, softmax=True, smooth_dr=1e-6, smooth_nr=1e-6)

# 如果需要加权损失，可以取消下面的注释
# class_weights = torch.tensor([0.2, 0.8], dtype=torch.float32, device=device)
# dice_ce_loss = DiceCELoss(
#     to_onehot_y=True,
#     softmax=True,
#     smooth_dr=1e-5,
#     smooth_nr=1e-5,
#     ce_weight=class_weights
# ).to(device)
#
# focal_loss_fn = FocalLoss(
#     to_onehot_y=True,
#     gamma=2.0,
#     alpha=0.75
# ).to(device)
#
# def loss_function(pred, target):
#     dice_loss = dice_ce_loss(pred, target)
#     focal_loss_value = focal_loss_fn(pred, target)
#     return dice_loss + 0.3 * focal_loss_value

# 优化器配置 - 使用AdamW和更合适的学习率
optimizer = torch.optim.AdamW(
    model.parameters(),
    lr=1e-4,  # 降低初始学习率
    weight_decay=1e-4,  # 增加正则化
    betas=(0.9, 0.999),
    eps=1e-8
)


#%% 添加学习率调度器
# scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
#     optimizer, 
#     T_0=10,              # 第一次重启的周期长度（epoch数）
#     T_mult=2,            # 每次重启后周期长度的倍增因子
#     eta_min=1e-6,        # 最小学习率
#     verbose=True         # 打印学习率变化
# )

#%% 定义最大epochs数量
max_epochs = 400

# 实现类似nnUNet的学习率调度器: 多项式衰减 + 预热期
# def poly_lr_scheduler_with_warmup(epoch, initial_lr, max_epochs, warmup_epochs=10, power=0.9):
#     if epoch < warmup_epochs:
#         # 预热期: 从initial_lr * 0.1到initial_lr线性增加
#         return initial_lr * (0.1 + 0.9 * epoch / warmup_epochs)
#     else:
#         # 多项式衰减
#         return initial_lr * (1 - (epoch - warmup_epochs) / (max_epochs - warmup_epochs)) ** power

# # 使用LambdaLR自定义学习率调度器
# initial_lr = optimizer.param_groups[0]['lr']  # 获取初始学习率
# scheduler = torch.optim.lr_scheduler.LambdaLR(
#     optimizer,
#     lr_lambda=lambda epoch: poly_lr_scheduler_with_warmup(
#         epoch, 1.0, max_epochs, warmup_epochs=10, power=0.9
#     ),
#     verbose=True
# )

# from torch.optim.lr_scheduler import CyclicLR
# scheduler = CyclicLR(
#     optimizer,
#     base_lr=1e-5,
#     max_lr= 1e-3,
#     step_size_up=50,  # 50个batch上升到最大值
#     step_size_down=150,  # 150个batch下降到最小值
#     cycle_momentum=False,
#     verbose=True
# )

# 优化学习率调度器 - 使用CosineAnnealingWarmRestarts
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts, ReduceLROnPlateau

# 主调度器：余弦退火重启
scheduler_cosine = CosineAnnealingWarmRestarts(
    optimizer,
    T_0=20,              # 第一次重启的周期长度（epoch数）
    T_mult=2,            # 每次重启后周期长度的倍增因子
    eta_min=1e-6,        # 最小学习率
    verbose=True
)

# 备用调度器：基于验证指标的自适应调度
scheduler_plateau = ReduceLROnPlateau(
    optimizer,
    mode='max',          # 监控指标越大越好（Dice分数）
    factor=0.5,          # 学习率衰减因子
    patience=15,         # 等待epoch数
    verbose=True,
    min_lr=1e-7
)

# 使用主调度器
scheduler = scheduler_cosine


#%% 设置标签
dice_metric_train = DiceMetric(include_background=False, reduction="mean")  # 新增
dice_metric_val = DiceMetric(include_background=False, reduction="mean")    # 新增

post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])
post_label = Compose([AsDiscrete(to_onehot=2)])

#%% 加载预训练权重和训练状态
model_weights_path = os.path.join(root_dir, "final_model.pth")  # 仅权重文件

# 只加载预训练模型权重，忽略完整检查点
if os.path.exists(model_weights_path):
    print(f"Loading model weights from {model_weights_path}")
    model.load_state_dict(torch.load(model_weights_path))
    print("Starting training from epoch 0 with pre-trained weights")
else:
    print("No weights found, starting training from scratch")


#%%模型训练
val_interval = 1

# 初始化训练状态和早停机制
start_epoch = 0
train_loss_values = []      # 训练 loss
val_loss_values = []        # 验证 loss
train_dice_values = []      # 训练 dice
val_dice_values = []        # 验证 dice
lr_values = []              # 学习率值
val_epochs = []
best_metric = -1
best_metric_epoch = -1

# 早停机制参数
early_stop_patience = 50    # 早停耐心值
early_stop_counter = 0      # 早停计数器
min_improvement = 0.001     # 最小改进阈值

# 创建日志文件，用于实时保存训练和验证指标
metrics_log_path = os.path.join(root_dir, "training_metrics.txt")
with open(metrics_log_path, "w") as f:
    f.write("Epoch,Train_Loss,Train_Dice,Val_Loss,Val_Dice,Learning_Rate\n")
    
print(f"创建指标记录文件: {metrics_log_path}")

for epoch in range(start_epoch, max_epochs):
    print("-" * 10)
    print(f"epoch {epoch + 1}/{max_epochs}")
    model.train()
    train_epoch_loss = 0
    step = 0
    dice_metric_train.reset()   

    # 打印当前学习率
    current_lr = optimizer.param_groups[0]['lr']
    print(f"Current learning rate: {current_lr:.6f}")
    # 记录学习率
    lr_values.append(current_lr)

    for batch_data in train_loader:
        step += 1
        inputs, labels = (
            batch_data["image"].to(device),
            batch_data["label"].to(device),
        )
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = loss_function(outputs, labels)
        loss.backward()
        optimizer.step()
        train_epoch_loss += loss.item()

        # 计算训练 Dice - 修改后的版本
        train_outputs = [post_pred(i) for i in decollate_batch(outputs)]
        train_labels = [post_label(i) for i in decollate_batch(labels)]
        dice_metric_train(y_pred=train_outputs, y=train_labels)
     
    torch.cuda.empty_cache()

    train_epoch_loss /= step
    train_loss_values.append(train_epoch_loss)
    
    # 计算并记录训练 Dice
    train_dice = dice_metric_train.aggregate().item()
    train_dice_values.append(train_dice)
    dice_metric_train.reset()
    
    print(f"epoch {epoch + 1} average loss: {train_epoch_loss:.4f}, train Dice: {train_dice:.4f}")
    
    # 在每个epoch结束时更新学习率
    scheduler.step()

    # 临时保存当前epoch的验证指标（初始化为"N/A"值）
    current_val_loss = "N/A"
    current_val_dice = "N/A"

    if (epoch + 1) % val_interval == 0:
        model.eval()
        val_epoch_loss = 0
        val_steps = 0
        with torch.no_grad():
            for val_data in val_loader:
                val_steps += 1
                val_inputs, val_labels = (
                    val_data["image"].to(device),
                    val_data["label"].to(device),
                )
                roi_size = spatial_size  # 使用与训练一致的尺寸
                sw_batch_size = 1  # 减小批次大小以减少内存使用
                val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
                
                # 计算验证 loss
                val_loss = loss_function(val_outputs, val_labels)
                val_epoch_loss += val_loss.item()
                
                val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]
                val_labels = [post_label(i) for i in decollate_batch(val_labels)]
                dice_metric_val(y_pred=val_outputs, y=val_labels)

            # 计算平均验证 loss
            val_epoch_loss /= val_steps
            val_loss_values.append(val_epoch_loss)

            # 计算验证 Dice
            val_dice = dice_metric_val.aggregate().item()
            val_dice_values.append(val_dice)
            dice_metric_val.reset()
            
            # 更新当前epoch的验证指标
            current_val_loss = val_epoch_loss
            current_val_dice = val_dice

            val_epochs.append(epoch + 1)

            # 早停和最佳模型保存逻辑
            if val_dice > best_metric + min_improvement:
                best_metric = val_dice
                best_metric_epoch = epoch + 1
                early_stop_counter = 0  # 重置早停计数器
                # 保存最佳模型权重
                torch.save(model.state_dict(), os.path.join(root_dir, "best_metric_model.pth"))
                print("saved new best metric model")
            else:
                early_stop_counter += 1
                print(f"No improvement for {early_stop_counter} epochs")

            # 使用ReduceLROnPlateau调度器
            scheduler_plateau.step(val_dice)

            print(
                f"current epoch: {epoch + 1} validation loss: {val_epoch_loss:.4f}, validation dice: {val_dice:.4f}"
                f"\nbest validation dice: {best_metric:.4f} at epoch: {best_metric_epoch}"
                f"\nearly stop counter: {early_stop_counter}/{early_stop_patience}"
            )

            # 早停检查
            if early_stop_counter >= early_stop_patience:
                print(f"Early stopping triggered after {early_stop_patience} epochs without improvement")
                break
        
    # 实时更新日志文件
    with open(metrics_log_path, "a") as f:
        if current_val_loss == "N/A":
            f.write(f"{epoch + 1},{train_epoch_loss:.4f},{train_dice:.4f},{current_val_loss},{current_val_dice},{current_lr:.6f}\n")
        else:
            f.write(f"{epoch + 1},{train_epoch_loss:.4f},{train_dice:.4f},{current_val_loss:.4f},{current_val_dice:.4f},{current_lr:.6f}\n")
            
    # 每10个epoch打印日志保存信息
    if (epoch + 1) % 10 == 0:
        print(f"训练指标已实时保存到 {metrics_log_path}")

# 保存最后一个模型的权重
torch.save(model.state_dict(), os.path.join(root_dir, "final_model.pth"))
print(f"已保存最终模型权重到 {os.path.join(root_dir, 'final_model.pth')}")

print(f"Training completed, best validation dice: {best_metric:.4f} at epoch: {best_metric_epoch}")

#%% 绘制训练曲线
plt.figure("Training Curves", (15, 10))

# 1. Loss 曲线
plt.subplot(2, 2, 1)
plt.title("Loss during Training and Validation")
epochs = list(range(1, len(train_loss_values) + 1))  # 使用实际训练的epoch数
plt.plot(epochs, train_loss_values, label="Training Loss")
plt.plot(val_epochs, val_loss_values, label="Validation Loss")
plt.xlabel("Epoch")
plt.ylabel("Loss")
plt.legend()
plt.grid(True)

# 2. Dice 曲线
plt.subplot(2, 2, 2)
plt.title("Dice Scores during Training and Validation")
plt.plot(epochs, train_dice_values, label="Training Dice")
plt.plot(val_epochs, val_dice_values, label="Validation Dice")
plt.xlabel("Epoch")
plt.ylabel("Dice Score")
plt.legend()
plt.grid(True)

# 保存图像到指定路径
figure_path = os.path.join(root_dir, "training_curves.png")
plt.savefig(figure_path, dpi=300, bbox_inches='tight')
print(f"训练曲线已保存到: {figure_path}")

#%%导出训练指标到 Excel
import pandas as pd
df_train = pd.DataFrame({
    'Epoch': epochs,  # 使用实际训练的epoch数
    'Train Loss': train_loss_values,
    'Train Dice': train_dice_values,
    'Learning Rate': lr_values,  # 添加学习率列
})

df_val = pd.DataFrame({
    'Epoch': val_epochs,
    'Validation Loss': val_loss_values,
    'Validation Dice': val_dice_values
})

# 合并数据框
df_combined = pd.merge(df_train, df_val, on='Epoch', how='left')

# 导出到 Excel
output_path = os.path.join(root_dir, "training_metrics.xlsx")
df_combined.to_excel(output_path, index=False)

print(f"Training metrics and curves have been saved to {root_dir}")

# In[11]: Check best model output with the input image and label
model.load_state_dict(torch.load(os.path.join(root_dir, "final_model.pth")))
model.eval()
with torch.no_grad():
    for i, val_data in enumerate(val_loader):
        roi_size = spatial_size  # 使用与训练一致的尺寸
        sw_batch_size = 1  # 减小批次大小以减少内存使用
        val_outputs = sliding_window_inference(val_data["image"].to(device), roi_size, sw_batch_size, model)

        # 选择一个合适的切片进行可视化
        middle_slice = spatial_size[2] // 2  # 选择中间切片
        
        # plot the slice
        plt.figure("check", (18, 6))
        plt.subplot(1, 3, 1)
        plt.title(f"image {i}")
        plt.imshow(val_data["image"][0, 0, :, :, middle_slice], cmap="gray")
        plt.subplot(1, 3, 2)
        plt.title(f"label {i}")
        plt.imshow(val_data["label"][0, 0, :, :, middle_slice])
        plt.subplot(1, 3, 3)
        plt.title(f"output {i}")
        plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, middle_slice])
        plt.show()
        if i == 2:
            break

#Evaluation on original image spacings

# In[12]:
val_org_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.5, 1.5, 2.0), mode="bilinear"),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-100,
            a_max=400,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image"], source_key="image"),
        SpatialPadd(keys=["image"], spatial_size=spatial_size, mode="constant"),
    ]
)

val_org_ds = Dataset(data=val_files, transform=val_org_transforms)
val_org_loader = DataLoader(val_org_ds, batch_size=1, num_workers=4)

post_transforms = Compose(
    [
        Invertd(
            keys="pred",
            transform=val_org_transforms,
            orig_keys="image",
            meta_keys="pred_meta_dict",
            orig_meta_keys="image_meta_dict",
            meta_key_postfix="meta_dict",
            nearest_interp=False,
            to_tensor=True,
            device="cpu",
        ),
        AsDiscreted(keys="pred", argmax=True),
        AsDiscreted(keys="label", to_onehot=2),
    ]
)

# In[16]:计算验证集的平均dice

model.load_state_dict(torch.load(os.path.join(root_dir, "final_model.pth")))
model.eval()

with torch.no_grad():
    for val_data in val_org_loader:
        val_inputs = val_data["image"].to(device)
        roi_size = spatial_size  # 使用与训练一致的尺寸
        sw_batch_size = 1  # 减小批次大小
        val_data["pred"] = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
        val_data = [post_transforms(i) for i in decollate_batch(val_data)]
        val_outputs, val_labels = from_engine(["pred", "label"])(val_data)
        # compute metric for current iteration
        dice_metric_val(y_pred=val_outputs, y=val_labels)

    # aggregate the final mean dice result
    metric_org = dice_metric_val.aggregate().item()
    # reset the status for next validation round
    dice_metric_val.reset()

print("Metric on original image spacing: ", metric_org)


#%%模型对test数据集进行批量预测，得到预测的mask
# import glob

# test_dir = '/root/autodl-tmp/HCC320/image/aptest' # 修改为imageTs目录，而不是labelsTs
# test_images = sorted(glob.glob(os.path.join(test_dir, "*.nii.gz")))


# # 检查test集目录是否存在，如果不存在则创建
# test_mask = '/root/autodl-tmp/HCC320/mask/aptest_predmask'

# if not os.path.exists(test_mask):
#     os.makedirs(test_mask)

# # 将路径单独列出来
# for image_path in test_images:
#     print(image_path)

# test_data = [{"image": image} for image in test_images]

# test_org_transforms = Compose(
#     [
#         LoadImaged(keys="image"),
#         EnsureChannelFirstd(keys="image"),
#         Orientationd(keys=["image"], axcodes="RAS"),
#         Spacingd(keys=["image"], pixdim=(1.5, 1.5, 2.0), mode="bilinear"),
#         ScaleIntensityRanged(
#             keys=["image"],
#             a_min=-57,
#             a_max=164,
#             b_min=0.0,
#             b_max=1.0,
#             clip=True,
#         ),
#         CropForegroundd(keys=["image"], source_key="image"),
#         SpatialPadd(keys=["image"], spatial_size=(96, 96, 32), mode="constant"),
#     ]
# )

# # test_org_ds = CacheDataset(data=test_data, transform=val_transforms, cache_rate=1.0, num_workers=4)
# test_org_ds = Dataset(data=test_data, transform=test_org_transforms)

# test_org_loader = DataLoader(test_org_ds, batch_size=1, num_workers=4)


# post_transforms = Compose(
#     [
#         Invertd(
#             keys="pred",
#             transform=test_org_transforms,
#             orig_keys="image",
#             meta_keys="pred_meta_dict",
#             orig_meta_keys="image_meta_dict",
#             meta_key_postfix="meta_dict",
#             nearest_interp=False,
#             to_tensor=True,
#         ),
#         AsDiscreted(keys="pred", argmax=True),
#         SaveImaged(
#             keys="pred", 
#             meta_keys="pred_meta_dict", 
#             output_dir=test_mask, 
#             output_postfix="seg", 
#             resample=False,
#             separate_folder=False,
#             output_ext=".nii.gz"
#         ),
#     ]
# )

# # 调用模型进行预测
# model.load_state_dict(torch.load(os.path.join(root_dir, "final_model.pth")))
# model.eval()

# with torch.no_grad():
#     for idx, test_data in enumerate(test_org_loader):
#         test_inputs = test_data["image"].to(device)
        
#         # 保存原始图像便于可视化
#         input_image = test_data["image"][0, 0].detach().cpu().numpy()
        
#         # 推理过程
#         roi_size = (96, 96, 32)  # 使用更小的z维度
#         sw_batch_size = 1  # 减小批次大小
#         test_data["pred"] = sliding_window_inference(test_inputs, roi_size, sw_batch_size, model)
        
#         # 获取预测结果用于可视化
#         pred_before_post = torch.argmax(test_data["pred"], dim=1)[0].detach().cpu().numpy()
        
#         # 应用后处理变换
#         test_data = [post_transforms(i) for i in decollate_batch(test_data)]
        
#         # 可视化预测结果
#         # 选择中间的切片以确保能看到内容
#         middle_slice = input_image.shape[2] // 2
        
#         plt.figure("Prediction Results", (12, 6))
#         plt.subplot(1, 2, 1)
#         plt.title(f"Original Image (slice {middle_slice})")
#         plt.imshow(input_image[:, :, middle_slice], cmap="gray")
        
#         plt.subplot(1, 2, 2)
#         plt.title(f"Prediction Mask (slice {middle_slice})")
#         plt.imshow(pred_before_post[:, :, middle_slice])
        
#         plt.tight_layout()
#         plt.savefig(os.path.join(test_mask, f"visualization_{idx}.png"))
#         plt.show()
        
#         print(f"处理完成 {idx+1}/{len(test_org_loader)} - 已保存预测结果和可视化")

