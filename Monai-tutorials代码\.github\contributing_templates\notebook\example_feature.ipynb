{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# Your Tutorial Title"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[pillow]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.0+2.g97918e46\n", "Numpy version: 1.22.2\n", "Pytorch version: 1.13.0a0+d0d6b1f\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 97918e46e0d2700c050e678d72e3edb35afbd737\n", "MONAI __file__: /workspace/monai/monai-in-dev/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.0.1\n", "Tensorboard version: 2.10.1\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.0a0\n", "tqdm version: 4.64.1\n", "lmdb version: 1.3.0\n", "psutil version: 5.9.2\n", "pandas version: 1.4.4\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.0.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "from monai.config import print_config\n", "from monai.transforms import LoadImage\n", "\n", "from example_class import ExampleImageGenerator\n", "\n", "print_config()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/workspace/data\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Your tutorial code and result\n", "\n", "### Example code\n", "Here we use the `ExampleImageGenerator` in the `example_class.py` to synethesize 2D images. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["generator = ExampleImageGenerator()\n", "images, _ = generator.generate(root_dir)\n", "im_data = LoadImage(image_only=True)(images)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Check data shape and visualize\n", "\n", "Printing the shape of the data and plotting would help readers better understand the inputs/outputs"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([40, 128, 128])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(im_data.shape)\n", "plt.imshow(im_data[0])\n", "plt.show()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10 (default, Nov 14 2022, 12:59:47) \n[GCC 9.4.0]"}, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}