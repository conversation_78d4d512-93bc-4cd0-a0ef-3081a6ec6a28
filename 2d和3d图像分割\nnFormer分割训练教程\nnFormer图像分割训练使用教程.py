#%% Autodl的screen后台运行，守护进程，也可以复杂功能多的tmux后台运行

#linux终端安装screen：          
sudo apt-get update && sudo apt-get install -y screen
# 启动screen会话，终端输入    
screen 
# 再按
enter
        
#运行长时间任务（例如训练模型）：    
python train_model.py     
    
# 断开screen会话（按下 Ctrl + A 然后按 D）：     
Ctrl + A, 再按D

# 列出所有会话    
screen -ls 
# 重新连接到会话
screen -r 
# 重新连接到特定会话，如果同时有2个以上运行
# screen -r session_name 如44401.pts-6.autodl-container-b49841aad4-7594a09d

# 强制终止所有 screen 会话
pkill screen
screen -ls

# nvidia-smi 是 NVIDIA 提供的一个命令行工具，可以显示 GPU 的状态和正在运行的 CUDA 程序
nvidia-smi

#%% nnFormer训练成功了，需要使用 RTX 2080Ti服务器，安装python3.7版本，成功了
# 不能按照太高的版本，其他服务器cuda版本太高，也不成功
# 在服务器上创建路径 nnFormerFrame

cd /root/autodl-tmp/nnFormerFrame

git clone https://github.com/282857341/nnFormer.git

cd nnFormer

# 使用 environment.yml 创建作者所提供的虚拟环境
conda env create -f environment.yml

# 注意要用虚拟环境下使用
source activate nnFormer
pip install -e .

# 2.1.1. 整理数据路径
# 进入创建的 nnFormerFrame 文件路径
cd /root/nnFormerFrame

# 创建一个名为 DATASET 的文件夹
# 进入创建好的 DATASET 文件夹
# 依次在DATASET文件夹下创建3个文件夹nnFormer_preprocessed/nnFormer_raw/nnFormer_trained_models
mkdir /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_preprocessed
mkdir /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw
mkdir /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_trained_models

nnFormer_preprocessed # 存放原始数据预处理之后的数据
nnFormer_raw # 存放原始的训练的数据
nnFormer_trained_models # 存放训练的结果

# 进入 nnFormer_raw
# 依次创建
nnFormer_cropped_data # crop 以后的数据
nnFormer_raw_data # 原始数据

mkdir /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data
mkdir /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_cropped_data

# 导入路径
export nnFormer_raw_data_base="/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw"
export nnFormer_preprocessed="/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_preprocessed"
export RESULTS_FOLDER="/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_trained_models"


# ./Pretrained_weight/
# ./nnFormer/
# ./DATASET/
#   ├── nnFormer_raw/
#       ├── nnFormer_raw_data/
#           ├── Task01_ACDC/
#               ├── imagesTr/
#               ├── imagesTs/
#               ├── labelsTr/
#               ├── labelsTs/
#               ├── dataset.json
#           ├── Task02_Synapse/
#               ├── imagesTr/
#               ├── imagesTs/
#               ├── labelsTr/
#               ├── labelsTs/
#               ├── dataset.json
#           ├── Task03_tumor/
#               ├── imagesTr/
#               ├── imagesTs/
#               ├── labelsTr/
#               ├── labelsTs/
#               ├── dataset.json
#       ├── nnFormer_cropped_data/
#   ├── nnFormer_trained_models/
#   ├── nnFormer_preprocessed/


#%% 生成json文件代码
import os
import json

# 定义路径
images_tr_path = '/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task01_hcc/imagesTr'
labels_tr_path = '/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task01_hcc/labelsTr'
images_ts_path = '/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task01_hcc/imagesTs'
dataset_json = '/root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task01_hcc/dataset.json'


# 获取标签文件名
label_files = {label_file for label_file in os.listdir(labels_tr_path) if label_file.endswith('.nii.gz')}

# 获取训练集图像和标签
training_data = []
for image_file in os.listdir(images_tr_path):
    print(image_file)
    if image_file.endswith('.nii.gz') and '_0000' in image_file:
        # 去掉 '_0000_0000' 以生成标签文件名
        label_name = image_file.replace('_0000', '')
        if label_name in label_files:
            training_data.append({
                "image": os.path.join(images_tr_path, image_file),
                "label": os.path.join(labels_tr_path, label_name)
            })

# 获取测试集图像
test_data = []
for image_file in os.listdir(images_ts_path):
    if image_file.endswith('.nii.gz'):
        test_data.append(os.path.join(images_ts_path, image_file))

# 打印结果
print("训练集数据：", len(training_data))
print("测试集数据：", len(test_data))

# 可选：将结果保存为 JSON 文件
dataset_info = {     
    "description": "Segmentation",
    "labels": {
        "0": "background",
        "1": "hcc",       
    },
    "licence": "see challenge website",
    "modality": {
        "0": "MRI"
    },    
    "reference": "see challenge website",
    "release": "0.0",   
    # "numTest": 10,
    # "numTraining": 108,
    "training": training_data,
    "test": test_data
}

with open(dataset_json, 'w') as json_file:
    json.dump(dataset_info, json_file, indent=4)

print("数据已保存到 dataset.json")

#%%注意要用虚拟环境下使用
source activate nnFormer

# 
# 转换数据集，让它可以被 nnFormer 识别
nnFormer_convert_decathlon_task -i /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task01_hcc

# nnFormer_convert_decathlon_task -i ../DATASET/nnFormer_raw/nnFormer_raw_data/Task03_tumor

# 进行插值等操作
nnFormer_plan_and_preprocess -t 1
# nnFormer_plan_and_preprocess -t 3

# 训练
# cd /root/autodl-tmp/model/nnFormerFrame/nnFormer
# 最终训练和推测代码：
bash train_inference.sh -c 0 -t 1   #训练时，关闭推测代码
bash train_inference2.sh -c 0 -t 1  #推测时，关闭训练代码
# bash train_inference.sh -c 0 -n nnformer_acdc -t 1


#-c stands for the index of your cuda device
#-n denotes the suffix of the trainer located at nnFormer/nnformer/training/network_training/
#-t denotes the task index


#使用预训练模型
../DATASET/nnFormer_trained_models/nnFormer/3d_fullres/Task001_ACDC/nnFormerTrainerV2_nnformer_acdc__nnFormerPlansv2.1/fold_0/model_best.model
../DATASET/nnFormer_trained_models/nnFormer/3d_fullres/Task001_ACDC/nnFormerTrainerV2_nnformer_acdc__nnFormerPlansv2.1/fold_0/model_best.model.pkl


#%% train_inference.sh 内容修改如下,注意路径需要修改：

#!/bin/bash


while getopts 'c:n:t:r:p' OPT; do
    case $OPT in
        c) cuda=$OPTARG;;
        n) name=$OPTARG;;
		t) task=$OPTARG;;
        r) train="true";;
        p) predict="true";;
        
    esac
done
echo $name	


if ${train}
then
    # # 设置环境变量
    # export CUDA_VISIBLE_DEVICES=0
    # export CUDA_LAUNCH_BLOCKING=1
    # export CUDA_VISIBLE_DEVICES=${cuda}      
    
    # # 清理GPU内存
    # nvidia-smi -r
    
    # # 设置多进程参数
    # export OMP_NUM_THREADS=1
    # export OPENBLAS_NUM_THREADS=1
    # export MKL_NUM_THREADS=1
    # export VECLIB_MAXIMUM_THREADS=1
    # export NUMEXPR_NUM_THREADS=1	
	
	cd /root/autodl-tmp/nnFormerFrame/nnFormer/nnformer/ #修改路径  
	CUDA_VISIBLE_DEVICES=${cuda} nnFormer_train 3d_fullres nnFormerTrainerV2 ${task} 0  #修改训练代码名nnFormerTrainerV2 ${task} 0 
fi

if ${predict}
then


	cd /root/autodl-tmp/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task01_hcc/
	# CUDA_VISIBLE_DEVICES=${cuda} nnFormer_predict -i imagesTs -o inferTs/ -m 3d_fullres -t ${task} -f 0 -chk model_best -tr nnFormerTrainerV2
	# python predict_simple.py 
    python /root/autodl-tmp/nnFormerFrame/nnFormer/nnformer/inference/predict_simple.py \
        -i imagesTs \
        -o inferTs/ \
        -t Task001_hcc \
        -m 3d_fullres \
        -f 0 \
        -chk model_best \
        -tr nnFormerTrainerV2 \
        --all_in_gpu True \
        --disable_tta
fi

#%% 报错 RuntimeError: CUDA error: no kernel image is available for execution on the device

# 需要使用 RTX 2080Ti服务器，安装python3.7版本，成功了
# 不能按照太高的版本，其他服务器cuda版本太高，也不成功

# cuda版本与torch版本不兼容
nvcc -V
nvidia-smi
import torch
print(torch.__version__)


# 本项目使用python3.6.1 所以要用下面低版本torch
pip uninstall torch torchvision torchaudio
# conda install pytorch==2.0.0 torchvision==0.15.0 torchaudio==2.0.0 pytorch-cuda=11.8 -c pytorch -c nvidia
# conda install pytorch==1.8.1 torchvision==0.9.1 torchaudio==0.8.1 cudatoolkit=10.2 -c pytorch


# 我刚开始使用 RTX3090 服务器进行训练，在训练的最后一步会报如下错：
# NVIDIA GeForce RTX 3090 with CUDA capability sm_86 is not compatible with the current PyTorch installation.
# The current PyTorch install supports CUDA capabilities sm_37 sm_50 sm_60 sm_70.
# If you want to use the NVIDIA GeForce RTX 3090 GPU with PyTorch, please check the instructions at https://pytorch.org/get-started/locally/

# 可以确定的是，这是因为 torch 版本不匹配所导致的问题
# 解决方案一：删除当前 nnFormer 虚拟环境下的 torch 并重装合适的 torch 版本（我尝试过这个方法，但并没有成功）
# 解决方案二：换一台服务器，再更换服务器为 RTX 2080Ti 之后就没有这个问题了