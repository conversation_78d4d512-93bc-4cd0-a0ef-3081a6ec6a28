#%%totalsegmentator自动分割脏器，多个病例批量分割1，分割后文件位于同一个文件夹 
# pip install TotalSegmentator 安装
# TotalSegmentator是在大型数据集上使用nnUNet V2训练的一个AI模型，
# 可以在CT数据上自动分割全身117个器官！同时还可以分割部分血管，脑出血，胸腔积液等
#可设置快速模式

import torch
import os
import glob
from turtle import color
from totalsegmentator.python_api import totalsegmentator
from totalsegmentator.map_to_binary import class_map   # 新增
# help(totalsegmentator)

device = 'gpu' if torch.cuda.is_available() else 'cpu'
print(device)

# 打印 total_mr 支持的所有解剖结构
# print("TotalSegmentator 在 total_mr 任务下可分割的 MRI 脏器/结构：")
# print(", ".join(sorted(class_map["total_mr"].values())))
# print("=" * 80)

if __name__ == "__main__":  

    input_dir = r"K:\肝脏MRI数据集\HCC新增待整理\HCC2024-2025\pp"
    output_dir = r"K:\肝脏MRI数据集\HCC新增待整理\HCC2024-2025\pp_organs_segment"

    organs_to_segment = ['liver','spleen','portal_vein_and_splenic_vein'] #liver_vessels

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 获取输入目录下的所有 .nii.gz 文件并按文件名排序
    nii_files = sorted(glob.glob(os.path.join(input_dir, '*.nii.gz')))

    # 对每个 .nii.gz 文件调用 totalsegmentator 函数
    for nii_file in nii_files:
        filename = os.path.basename(nii_file)
        output_path = os.path.join(output_dir, filename)

        # 检查哪些器官已经分割完成
        existing_organs = []
        missing_organs = []

        for organ in organs_to_segment:
            new_segmented_file_path = os.path.join(output_dir, f'{filename[:-7]}-{organ}.nii.gz')
            if os.path.exists(new_segmented_file_path):
                existing_organs.append(organ)
                print(f"Found existing segmentation: {filename[:-7]}-{organ}.nii.gz")
            else:
                missing_organs.append(organ)

        # 如果所有器官都已经分割完成，跳过这个患者
        if len(missing_organs) == 0:
            print(f"Skipping {filename} - all organs already segmented: {existing_organs}")
            continue

        # 如果有部分器官已经分割，只处理缺失的器官
        if len(existing_organs) > 0:
            print(f"Patient {filename[:-7]} - Found existing: {existing_organs}, Missing: {missing_organs}")
            print(f"Will segment missing organs: {missing_organs}")
        else:
            print(f"Processing new patient {filename[:-7]} - segmenting all organs: {missing_organs}")
        
        try:
            print(f"Processing {nii_file}...")
            # 只分割缺失的器官
            totalsegmentator(
                nii_file,
                output_dir,
                roi_subset=missing_organs,  # 使用missing_organs而不是organs_to_segment
                task='total_mr',          # 仍然是 MRI 任务
                device=device,             # <── 指定 GPU
                radiomics=False,
                fast=False                # 显存不够可改 True
            )

            # 移动并重命名分割后的文件（只处理新分割的器官）
            for organ in missing_organs:  # 只处理缺失的器官
                segmented_file_path = os.path.join(output_dir, f'{organ}.nii.gz')
                new_segmented_file_path = os.path.join(output_dir, f'{filename[:-7]}-{organ}.nii.gz')
                if os.path.exists(segmented_file_path):  # 确保文件存在再重命名
                    os.rename(segmented_file_path, new_segmented_file_path)
                    print(f"Saved segmented output: {filename[:-7]}-{organ}.nii.gz")
                else:
                    print(f"Warning: Expected segmentation file not found: {segmented_file_path}")

            print(f"Completed processing {filename[:-7]} - segmented {len(missing_organs)} organs")
        except Exception as e:
            print(f"An error occurred while processing {nii_file}: {e}")
            import traceback
            print(traceback.format_exc())

    print("Finish")

#total_mr可分割脏器结构
# adrenal_gland_left, adrenal_gland_right, aorta, autochthon_left, autochthon_right, brain,
# clavicula_left, clavicula_right, color, duodenum, esophagus, femur_left, femur_right, 
# gallbladder, gluteus_maximus_left, gluteus_maximus_right, gluteus_medius_left, 
# gluteus_medius_right, gluteus_minimus_left, gluteus_minimus_right, heart, hip_left, 
# hip_right, humerus_left, humerus_right, iliac_artery_left, iliac_artery_right, 
# iliac_vena_left, iliac_vena_right, iliopsoas_left, iliopsoas_right, inferior_vena_cava, 
# intervertebral_discs, kidney_left, kidney_right, liver, lung_left, lung_right, pancreas, 
# portal_vein_and_splenic_vein, prostate, sacrum, scapula_left, scapula_right, small_bowel, 
# spinal_cord, spleen, stomach, urinary_bladder, vertebrae

# 左肾上腺, 右肾上腺, 主动脉, 左腰大肌, 右腰大肌, 大脑,
# 左锁骨, 右锁骨, 结肠, 十二指肠, 食道, 左股骨, 右股骨,
# 胆囊, 左臀大肌, 右臀大肌, 左臀中肌, 右臀中肌,
# 左臀小肌, 右臀小肌, 心脏, 左髋, 右髋, 左肱骨, 右肱骨,
# 左髂动脉, 右髂动脉, 左髂静脉, 右髂静脉, 左髂腰肌, 右髂腰肌, 下腔静脉,
# 椎间盘, 左肾, 右肾, 肝脏, 左肺, 右肺, 胰腺,
# 门静脉和脾静脉, 前列腺, 骶骨, 左肩胛骨, 右肩胛骨, 小肠,
# 脊髓, 脾脏, 胃, 膀胱, 椎骨
