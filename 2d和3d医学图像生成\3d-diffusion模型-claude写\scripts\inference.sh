#!/bin/bash

# 医学图像扩散模型推理脚本

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0

# 模型和配置路径
MODEL_PATH="outputs/medical_diffusion_experiment/checkpoints/best_checkpoint.pth"
CONFIG_PATH="configs/config.json"

# 推理参数
NUM_STEPS=50
DEVICE="cuda"
SAMPLE_METHOD="ddpm"  # 或者 "ddim"

# 检查模型文件是否存在
if [ ! -f "$MODEL_PATH" ]; then
    echo "模型文件不存在: $MODEL_PATH"
    echo "请先训练模型或检查路径"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "$CONFIG_PATH" ]; then
    echo "配置文件不存在: $CONFIG_PATH"
    exit 1
fi

echo "使用的模型: $MODEL_PATH"
echo "使用的配置: $CONFIG_PATH"
echo "采样步数: $NUM_STEPS"
echo "采样方法: $SAMPLE_METHOD"

# 如果有命令行参数，使用参数
if [ $# -eq 0 ]; then
    echo "用法示例:"
    echo "1. 单张图像推理:"
    echo "   $0 single path/to/ap_image.nii.gz path/to/output_dir"
    echo ""
    echo "2. 批量推理:"
    echo "   $0 batch path/to/ap_images_dir path/to/output_dir"
    echo ""
    echo "3. 与真实图像比较:"
    echo "   $0 compare path/to/ap_image.nii.gz path/to/hbp_image.nii.gz path/to/output_dir"
    exit 1
fi

MODE=$1

case $MODE in
    "single")
        if [ $# -ne 3 ]; then
            echo "单张图像推理用法: $0 single <ap_image_path> <output_dir>"
            exit 1
        fi
        
        AP_IMAGE=$2
        OUTPUT_DIR=$3
        
        echo "开始单张图像推理..."
        echo "输入AP图像: $AP_IMAGE"
        echo "输出目录: $OUTPUT_DIR"
        
        python inference.py \
            --model_path $MODEL_PATH \
            --config_path $CONFIG_PATH \
            --ap_image $AP_IMAGE \
            --output_dir $OUTPUT_DIR \
            --num_steps $NUM_STEPS \
            --sample_method $SAMPLE_METHOD \
            --device $DEVICE
        ;;
        
    "batch")
        if [ $# -ne 3 ]; then
            echo "批量推理用法: $0 batch <ap_images_dir> <output_dir>"
            exit 1
        fi
        
        AP_DIR=$2
        OUTPUT_DIR=$3
        
        echo "开始批量推理..."
        echo "输入AP图像目录: $AP_DIR"
        echo "输出目录: $OUTPUT_DIR"
        
        python inference.py \
            --model_path $MODEL_PATH \
            --config_path $CONFIG_PATH \
            --ap_dir $AP_DIR \
            --output_dir $OUTPUT_DIR \
            --num_steps $NUM_STEPS \
            --sample_method $SAMPLE_METHOD \
            --device $DEVICE
        ;;
        
    "compare")
        if [ $# -ne 4 ]; then
            echo "比较推理用法: $0 compare <ap_image_path> <hbp_image_path> <output_dir>"
            exit 1
        fi
        
        AP_IMAGE=$2
        HBP_IMAGE=$3
        OUTPUT_DIR=$4
        
        echo "开始比较推理..."
        echo "输入AP图像: $AP_IMAGE"
        echo "真实HBP图像: $HBP_IMAGE"
        echo "输出目录: $OUTPUT_DIR"
        
        python inference.py \
            --model_path $MODEL_PATH \
            --config_path $CONFIG_PATH \
            --ap_image $AP_IMAGE \
            --hbp_image $HBP_IMAGE \
            --output_dir $OUTPUT_DIR \
            --num_steps $NUM_STEPS \
            --sample_method $SAMPLE_METHOD \
            --device $DEVICE
        ;;
        
    *)
        echo "未知模式: $MODE"
        echo "支持的模式: single, batch, compare"
        exit 1
        ;;
esac

echo "推理完成!"