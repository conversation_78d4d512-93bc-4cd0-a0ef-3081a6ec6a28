#!/usr/bin/env python
# coding: utf-8

# In[8]:


import itk
from itkwidgets import view, compare, checkerboard
from itkwidgets.widget_viewer import Viewer
import ipywidgets
from ipywidgets import Button, Label, HBox, VBox
import os
import base64


# for use in viewers, from
# https://itk.org/ITKExamples/src/Core/Common/CreateAnImage/Documentation.html
Dimension = 3
PixelType = itk.ctype('float')
ImageType = itk.Image[PixelType, Dimension]

placeholderImage = ImageType.New()

ind = itk.Index[Dimension]()
ind[0] = 0  # first index on X
ind[1] = 0  # first index on Y
ind[2] = 0  # first index on Z

size1 = 4
size = itk.Size[Dimension]()
size[0] = size1  # size along X
size[1] = size1  # size along Y
size[2] = size1  # size along Z

region = itk.ImageRegion[Dimension]()
region.SetSize(size)
region.SetIndex(ind)

placeholderImage.SetRegions(region)
placeholderImage.Allocate()

# set voxel values
for k in range(size1):
    ind[2]=k
    for j in range(size1):
        ind[1]=j
        for i in range(size1):
            ind[0]=i
            placeholderImage.SetPixel(ind, i+2*j+k*k)


# from https://github.com/InsightSoftwareConsortium/itkwidgets/blob/cffd4583775af2d3fa2aaac67e34fc21b3a790b5/itkwidgets/widget_compare.py#L26-L34
def linkViewers(viewer1, viewer2):
    ipywidgets.jslink((viewer1, 'mode'), (viewer2, 'mode'))
    ipywidgets.jslink((viewer1, 'camera'), (viewer2, 'camera'))
    ipywidgets.jslink((viewer1, 'roi'), (viewer2, 'roi'))
    ipywidgets.jslink((viewer1, 'rotate'), (viewer2, 'rotate'))
    ipywidgets.jslink((viewer1, 'annotations'), (viewer2, 'annotations'))
    ipywidgets.jslink((viewer1, 'x_slice'), (viewer2, 'x_slice'))
    ipywidgets.jslink((viewer1, 'y_slice'), (viewer2, 'y_slice'))
    ipywidgets.jslink((viewer1, 'z_slice'), (viewer2, 'z_slice'))
    ipywidgets.jslink((viewer1, 'slicing_planes'), (viewer2, 'slicing_planes'))
    

def readUploadedImage(uploader):   
    # using FileUpload's syntax for ipywidgets <= 7
    tempFilename="./uploaded_"+ next(iter(uploader.value))
    with open(tempFilename, "wb") as fp:
        fp.write(uploader.data[0]) # write to temporary image file
        
    # I don't know how to read image from memory in Python
    image = itk.imread(tempFilename, itk.F)
    os.remove(tempFilename)
    return image


# these FileUpload widgets have maximum file size
# controlled via jupyter_notebook_config.py
# https://github.com/jupyter-widgets/ipywidgets/issues/2522
# Start with:
#  python -m jupyter notebook --config ./.jupyter/jupyter_notebook_config.py ./examples/ITK_Registration_App.ipynb
# for Jupyter, or
#  python -m voila --Voila.tornado_settings '{"websocket_max_message_size":*********}' --theme=dark ./examples/ITK_Registration_App.ipynb
# for Voilà on Linux. Command is slightly different on Windows:
#  python -m voila "--Voila.tornado_settings={\"websocket_max_message_size\":*********}" --theme=dark ./examples/ITK_Registration_App.ipynb

fixedUploader = ipywidgets.FileUpload(
    accept='.nrrd,.mha,.nii,image/*',
    multiple=False)
movingUploader = ipywidgets.FileUpload(
    accept='.nrrd,.mha,.nii,image/*',
    multiple=False)

# create viewers which we will link to have the same view etc
# to change images as needed, they need to have an initial image
fixedViewer = view(image=placeholderImage, ui_collapsed=True)
movingViewer = view(image=placeholderImage, ui_collapsed=True)
resultViewer = view(image=placeholderImage, ui_collapsed=True)
linkViewers(fixedViewer, movingViewer)
linkViewers(fixedViewer, resultViewer)

vboxFixed = VBox([Label('Fixed image'), fixedUploader, fixedViewer])
vboxMoving = VBox([Label('Moving image'), movingUploader, movingViewer])
hboxFixedMoving = HBox([vboxFixed, vboxMoving])
vboxResult = VBox([Label('Result image'), Label(' '), resultViewer])
imageViews = VBox([hboxFixedMoving, vboxResult])
display(imageViews)

out = ipywidgets.Output()
display(out) # for status messages

regType=ipywidgets.RadioButtons(
    options=['rigid', 'affine', 'bspline'],
    value='rigid',
    description='Type:',
    disabled=False
)
registerButton = ipywidgets.Button(description="Register")
buttons = HBox([regType, registerButton])
display(buttons)

appState={'fixed':placeholderImage, 'moving':placeholderImage, 'result':None}


def uploadedImage(b):
    uploader=b['owner']
    if (uploader is fixedUploader):
        appState['fixed'] = readUploadedImage(fixedUploader)
        fixedViewer.image=appState['fixed']
        if (appState['moving'] is placeholderImage):
            # make them close in physical space
            appState['moving'].SetOrigin(appState['fixed'].GetOrigin())
            movingViewer.image=appState['moving']
    else:
        appState['moving'] = readUploadedImage(movingUploader)
        movingViewer.image=appState['moving']
        if (appState['fixed'] is placeholderImage):
            # make them close in physical space
            appState['fixed'].SetOrigin(appState['moving'].GetOrigin())
            fixedViewer.image=appState['fixed']
    
    resultViewer=checkerboard(appState['fixed'], appState['moving'])
    linkViewers(fixedViewer, resultViewer.children[0])
    
    vboxResult=imageViews.children[1]
    vboxResult.children=(Label('Fixed/moving'),
                        Label('checkerboard'),
                        resultViewer)
    imageViews.children=(hboxFixedMoving,
                        vboxResult)
    out.clear_output(wait=True)

fixedUploader.observe(uploadedImage, names='value')
movingUploader.observe(uploadedImage, names='value')


def registerImages(b):
    out.clear_output(wait=True)
    parameters = itk.ParameterObject.New()

    resolutions = 3
    default_rigid = parameters.GetDefaultParameterMap("rigid", resolutions)
    parameters.AddParameterMap(default_rigid)

    if (regType.value!='rigid'):
        resolutions = 2
        default_affine = parameters.GetDefaultParameterMap("affine", resolutions)
        parameters.AddParameterMap(default_affine)

    if (regType.value=='bspline'):
        resolutions = 1
        default_bspline = parameters.GetDefaultParameterMap("bspline", resolutions)
        parameters.AddParameterMap(default_bspline)

    parameters.RemoveParameter("ResultImageFormat")
    with out:
        print('Executing '+regType.value+' registration. Please wait...')
    
    appState['result'], params = itk.elastix_registration_method(appState['fixed'],
                                                                 appState['moving'],
                                                                 parameter_object=parameters,
                                                                 log_to_file=True,
                                                                 log_file_name="elx_HASI.log",
                                                                 output_directory='.')
    
    with out:
        print('Compressing result image...')
    itk.imwrite(appState['result'], "./result.nrrd", compression=True)
    
    resultViewer=checkerboard(appState['fixed'], appState['result'])
    linkViewers(fixedViewer, resultViewer.children[0])
    
    vboxResult=imageViews.children[1]
    vboxResult.children=(Label('Fixed/registered'),
                        vboxResult.children[1],
                        resultViewer)
    
    imageViews.children=(imageViews.children[0],
                        vboxResult)
    
    # create a button which will contain base64-encoded file content
    # that way server permission don't matter for downloading
    with open("./result.nrrd", mode='rb') as file:
        fileContent = file.read()    
    fileContent64 = base64.b64encode(fileContent)
    html = '''<a download="MovingRegistered.nrrd"
        href="data:image/nrrd;base64,{payload}"
        target="_blank">Download moving registered image</a>'''
    html = html.format(payload=fileContent64.decode())
    downloadLink=ipywidgets.HTML(html)
    buttons.children=(buttons.children[0],
                     buttons.children[1],
                     downloadLink)

    out.clear_output()

registerButton.on_click(registerImages)


# In[ ]:




