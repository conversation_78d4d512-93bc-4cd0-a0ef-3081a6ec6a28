{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from fmcib.utils import build_image_seed_dict"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Point to the directory where the LUNG1 download is located\n", "download_dir = \"../../download/hi/\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-02-14 15:16:59.878 | INFO     | fmcib.utils.idc_helper:build_image_seed_dict:220 - Converting DICOM files to NIFTI ...\n", "100%|██████████| 5/5 [00:11<00:00,  2.23s/it]\n"]}], "source": ["preprocessed_df = build_image_seed_dict(\n", "    download_dir, samples=5\n", ")  # samples=5 is for testing purposes. Remove it to pre-process all images"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>image_path</th>\n", "      <th>PatientID</th>\n", "      <th>coordX</th>\n", "      <th>coordY</th>\n", "      <th>coordZ</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>/home/<USER>/Repositories/foundation-cancer-ima...</td>\n", "      <td>hi</td>\n", "      <td>-40.461844</td>\n", "      <td>8.983224</td>\n", "      <td>-457.380631</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>/home/<USER>/Repositories/foundation-cancer-ima...</td>\n", "      <td>hi</td>\n", "      <td>77.674134</td>\n", "      <td>54.468827</td>\n", "      <td>-586.252077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>/home/<USER>/Repositories/foundation-cancer-ima...</td>\n", "      <td>hi</td>\n", "      <td>71.665946</td>\n", "      <td>-123.702465</td>\n", "      <td>-548.448292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>/home/<USER>/Repositories/foundation-cancer-ima...</td>\n", "      <td>hi</td>\n", "      <td>-72.438383</td>\n", "      <td>-16.486298</td>\n", "      <td>-418.794178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>/home/<USER>/Repositories/foundation-cancer-ima...</td>\n", "      <td>hi</td>\n", "      <td>-45.095535</td>\n", "      <td>-0.349988</td>\n", "      <td>-510.816668</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          image_path PatientID     coordX  \\\n", "0  /home/<USER>/Repositories/foundation-cancer-ima...        hi -40.461844   \n", "1  /home/<USER>/Repositories/foundation-cancer-ima...        hi  77.674134   \n", "2  /home/<USER>/Repositories/foundation-cancer-ima...        hi  71.665946   \n", "3  /home/<USER>/Repositories/foundation-cancer-ima...        hi -72.438383   \n", "4  /home/<USER>/Repositories/foundation-cancer-ima...        hi -45.095535   \n", "\n", "       coordY      coordZ  \n", "0    8.983224 -457.380631  \n", "1   54.468827 -586.252077  \n", "2 -123.702465 -548.448292  \n", "3  -16.486298 -418.794178  \n", "4   -0.349988 -510.816668  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["preprocessed_df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["preprocessed_df.to_csv(\"annotations/annotations.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "fmcib_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}