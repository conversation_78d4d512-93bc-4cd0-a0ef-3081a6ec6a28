{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Note: \n", "- This code is to perform image processing including registration, and N4 bias correction via Nipype (example images were skull-stripped for de-idenficiation), automated tumor segmentation, extraction of tumor loci and shape features, and IDH status prediction which is the final step of hybrid model. \n", "\n", "<img src=\"workflow.png\">\n", "  \n", "- Python module requirements  : Nipype /  FSL /  ANTs / PyRadiomics / PyTorch\n", "- The process resquires GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["from img_processing import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Image processing - resampling, registration and bias correction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for de-identification, example files were skull-striped. \n", "    \n", "    # file path of skull-stripped images\n", "T1C_bet_file = 'example_t1c_bet.nii.gz'            \n", "T2_bet_file = 'example_t2_bet.nii.gz'\n", "FLAIR_bet_file = 'example_flair_bet.nii.gz'\n", "    \n", "    # file path of the mask for T1C skull stripping\n", "brainmask_T1C_file = 'example_t1c_bet_mask.nii.gz'  \n", "\n", "# filenames to save isovoxel images / brain mask\n", "T1C_iso_file = 't1c_isovoxel.nii.gz'\n", "T2_iso_file = 't2_isovoxel.nii.gz'\n", "FLAIR_iso_file = 'flair_isovoxel.nii.gz'\n", "brainmask_iso_file = 'mask_brain_isovoxel.nii.gz'    \n", "\n", "# filenames to save bias-corrected images   \n", "T1C_corrected_file = 't1c_corrected.nii.gz'\n", "T2_corrected_file = 't2_corrected.nii.gz'\n", "FLAIR_corrected_file = 'flair_corrected.nii.gz'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["func_img_proc(T1C_bet_file, T2_bet_file, FLAIR_bet_file, brainmask_T1C_file,\n", "              T1C_iso_file, T2_iso_file, FLAIR_iso_file, brainmask_iso_file,\n", "              T1C_corrected_file, T2_corrected_file, FLAIR_corrected_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model 1 : Automatic tumor segmentation  \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Preprocessing for Model 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(t1c_unet_arr, flair_unet_arr, cropdown_info) = func_norm_model1(T1C_corrected_file, FLAIR_corrected_file, brainmask_iso_file)\n", "\n", "# cropdown_info will be used for resmampling the predicted tumor mask to original isovoxel space, and preprcessing for Model2.\n", "cropdown_info"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get tumor mask from Model 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["\n", "predmask_arr = func_get_predmask(t1c_unet_arr, flair_unet_arr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Resample the predicted mask back to original isovoxel space"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "predmask_isovoxel_arr = func_mask_back2iso(predmask_arr, cropdown_info)\n", "predmask_isovoxel_arr_sitk = np.transpose(predmask_isovoxel_arr, (2,1,0))\n", "predmask_isovoxel_img = sitk.GetImageFromArray(predmask_isovoxel_arr_sitk)\n", "\n", "predmask_isovoxel_file = 'predmask_isovoxel.nii.gz' #filename for predicted mask of isovoxel resolution\n", "sitk.WriteImage(predmask_isovoxel_img, predmask_isovoxel_file)   # save the automatic segmentation of isovoxel resolution"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model 2 : CNN classifier for IDH status prediction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Preprocessing for Model 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t1c_corrected_img = nb.load(T1C_corrected_file)\n", "t1c_corrected_arr = t1c_corrected_img.get_data()\n", "t2_corrected_img = nb.load(T2_corrected_file)\n", "t2_corrected_arr = t2_corrected_img.get_data()\n", "brain_mask = nb.load(brainmask_iso_file)\n", "brain_mask_arr = brain_mask.get_data()\n", "\n", "t1c_resnet_arr = func_norm_resnet(t1c_corrected_arr, predmask_isovoxel_arr, brain_mask_arr, cropdown_info)\n", "t2_resnet_arr = func_norm_resnet(t2_corrected_arr, predmask_isovoxel_arr, brain_mask_arr, cropdown_info)\n", "\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get shape and loci features from tumor mask of 1mm isovoxel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sla_features = func_shapeloci(T1C_iso_file, predmask_isovoxel_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Add patient's age"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sla_features['age'] = pd.Series(50)  ## change 50 to the patient's age."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sla_features = np.array(sla_features)\n", "sla_arr = np.repeat(sla_features, 5, axis=0)\n", "sla_arr.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get probability of IDH mutation from Model 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#download Model 2\n", "from torch.hub import download_url_to_file\n", "\n", "url='https://github.com/yoonchoi-neuro/automated_hybrid_IDH/releases/download/final/MODEL2_CNNclassifier.pth'\n", "download_url_to_file(url, 'MODEL2_CNNclassifier.pth')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_mean = get_IDH_pred(t1c_resnet_arr, t2_resnet_arr, predmask_arr, sla_arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 2}