{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def image_generator(x1, x2, y1, y2, bspline=False):\n", "    image = np.zeros([100, 100], np.float32)\n", "    for x in range(x1, x2):\n", "        for y in range(y1, y2):\n", "            if bspline:\n", "                y += x\n", "                if x > 99 or y > 99:\n", "                    pass\n", "                else:\n", "                    image[y, x] = 1\n", "            else:\n", "                image[y, x] = 1\n", "    image = itk.image_view_from_array(image)\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bspline Test"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Create test images\n", "fixed_image_bspline = image_generator(25,65,25,65)\n", "moving_image_bspline = image_generator(5,55,5,40, bspline=True)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "default_affine_parameter_map = parameter_object.GetDefaultParameterMap('affine',4)\n", "default_affine_parameter_map['FinalBSplineInterpolationOrder'] = ['1']\n", "parameter_object.AddParameterMap(default_affine_parameter_map)\n", "default_bspline_parameter_map = parameter_object.GetDefaultParameterMap('bspline',4)\n", "default_bspline_parameter_map['FinalBSplineInterpolationOrder'] = ['1']\n", "parameter_object.AddParameterMap(default_bspline_parameter_map)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image_bspline, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image_bspline, moving_image_bspline,\n", "    parameter_object=parameter_object,\n", "    log_to_console=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bspline Test Transformix"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Load Transformix Object\n", "transformix_object = itk.TransformixFilter.New(moving_image_bspline)\n", "transformix_object.SetTransformParameterObject(result_transform_parameters)\n", "\n", "# Update object (required)\n", "transformix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Transformation\n", "result_image_transformix = transformix_object.GetOutput()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bspline Test Visualization"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 2160x2160 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(1,4, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0].imshow(result_image_bspline)\n", "axs[0].set_title('Result', fontsize=30)\n", "axs[1].imshow(fixed_image_bspline)\n", "axs[1].set_title('Fixed', fontsize=30)\n", "axs[2].imshow(moving_image_bspline)\n", "axs[2].set_title('Moving', fontsize=30)\n", "axs[3].imshow(result_image_transformix)\n", "axs[3].set_title('Transformix', fontsize=30)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}