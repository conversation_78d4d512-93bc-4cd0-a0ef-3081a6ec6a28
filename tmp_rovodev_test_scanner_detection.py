#!/usr/bin/env python3
"""
测试MRI扫描仪检测功能
Test MRI Scanner Detection Functionality
"""

import os
import sys
from pathlib import Path

# 导入我们的检测器
try:
    from tmp_rovodev_advanced_mri_scanner_detector import MRIScannerDetector
    from tmp_rovodev_extract_mri_scanner_info import analyze_single_nii_file, batch_extract_scanner_info
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保检测器脚本在同一目录下")
    sys.exit(1)

def find_sample_nii_files(workspace_dir="."):
    """在工作空间中查找示例NII文件"""
    workspace_path = Path(workspace_dir)
    
    # 查找NII文件
    nii_files = list(workspace_path.rglob("*.nii")) + list(workspace_path.rglob("*.nii.gz"))
    
    print(f"在工作空间中找到 {len(nii_files)} 个NII文件:")
    for i, file_path in enumerate(nii_files[:10], 1):  # 只显示前10个
        print(f"  {i}. {file_path}")
    
    if len(nii_files) > 10:
        print(f"  ... 还有 {len(nii_files) - 10} 个文件")
    
    return nii_files

def test_basic_detection():
    """测试基础检测功能"""
    print("🔍 测试基础扫描仪检测功能")
    print("="*50)
    
    # 查找示例文件
    nii_files = find_sample_nii_files()
    
    if not nii_files:
        print("❌ 未找到NII文件进行测试")
        return
    
    # 测试第一个文件
    test_file = nii_files[0]
    print(f"\n📁 测试文件: {test_file}")
    
    try:
        # 使用基础检测器
        print("\n--- 基础检测结果 ---")
        analyze_single_nii_file(test_file)
        
    except Exception as e:
        print(f"❌ 基础检测失败: {e}")

def test_advanced_detection():
    """测试高级检测功能"""
    print("\n🚀 测试高级扫描仪检测功能")
    print("="*50)
    
    # 查找示例文件
    nii_files = find_sample_nii_files()
    
    if not nii_files:
        print("❌ 未找到NII文件进行测试")
        return
    
    # 初始化高级检测器
    detector = MRIScannerDetector()
    
    # 测试第一个文件
    test_file = nii_files[0]
    print(f"\n📁 测试文件: {test_file}")
    
    try:
        print("\n--- 高级检测结果 ---")
        result = detector.comprehensive_analysis(test_file)
        
        # 格式化输出结果
        print(f"📄 文件: {result['file_name']}")
        
        if 'final_assessment' in result:
            assessment = result['final_assessment']
            print(f"🎯 置信度: {assessment['confidence_level']}")
            print(f"🏭 厂商: {assessment['manufacturer'] or '未检测到'}")
            print(f"📱 型号: {assessment['model'] or '未检测到'}")
            print(f"🧲 磁场强度: {assessment['field_strength'] or '未检测到'}")
            
            if assessment['evidence_sources']:
                print(f"\n📋 证据来源:")
                for evidence in assessment['evidence_sources']:
                    print(f"  - {evidence['field']}: {evidence['value']} (来源: {evidence['source']}, 置信度: {evidence['confidence']})")
        
        # 显示其他检测到的信息
        if 'json_metadata' in result:
            print(f"\n📄 JSON元数据: 发现 {len(result['json_metadata'])} 个字段")
        
        if 'nii_header' in result and 'extensions' in result['nii_header']:
            print(f"🔧 NII扩展: 发现 {len(result['nii_header']['extensions'])} 个扩展")
        
        if 'filename_analysis' in result:
            print(f"📝 文件名分析: 发现 {len(result['filename_analysis'])} 个线索")
        
        if 'directory_analysis' in result:
            print(f"📁 目录分析: 发现 {len(result['directory_analysis'])} 个线索")
            
    except Exception as e:
        print(f"❌ 高级检测失败: {e}")
        import traceback
        traceback.print_exc()

def test_batch_detection():
    """测试批量检测功能"""
    print("\n📦 测试批量检测功能")
    print("="*50)
    
    # 查找包含NII文件的目录
    nii_files = find_sample_nii_files()
    
    if not nii_files:
        print("❌ 未找到NII文件进行测试")
        return
    
    # 选择一个包含多个文件的目录
    test_dirs = set()
    for file_path in nii_files:
        test_dirs.add(file_path.parent)
    
    if not test_dirs:
        print("❌ 未找到包含NII文件的目录")
        return
    
    # 选择文件最多的目录
    best_dir = max(test_dirs, key=lambda d: len(list(d.glob("*.nii*"))))
    files_in_dir = list(best_dir.glob("*.nii*"))
    
    print(f"📁 测试目录: {best_dir}")
    print(f"📊 文件数量: {len(files_in_dir)}")
    
    if len(files_in_dir) > 5:
        print("⚠️  文件较多，只分析前5个文件")
        # 创建临时目录进行测试
        import tempfile
        import shutil
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 复制前5个文件到临时目录
            for i, file_path in enumerate(files_in_dir[:5]):
                shutil.copy2(file_path, temp_path / file_path.name)
            
            try:
                detector = MRIScannerDetector()
                results = detector.batch_analysis(temp_path, None)  # 不保存CSV
                
                print(f"✅ 批量分析完成，处理了 {len(results)} 个文件")
                
            except Exception as e:
                print(f"❌ 批量检测失败: {e}")
    else:
        try:
            detector = MRIScannerDetector()
            results = detector.batch_analysis(best_dir, None)  # 不保存CSV
            
            print(f"✅ 批量分析完成，处理了 {len(results)} 个文件")
            
        except Exception as e:
            print(f"❌ 批量检测失败: {e}")

def create_demo_summary():
    """创建演示总结"""
    print("\n" + "="*60)
    print("📋 MRI扫描仪检测功能总结")
    print("="*60)
    
    print("""
🎯 主要功能:
1. 从NII.gz文件中提取MRI扫描仪信息
2. 支持多种信息来源:
   - NII文件头扩展信息
   - BIDS格式JSON元数据文件
   - 文件名模式识别
   - 目录结构分析

🏭 支持的厂商:
- Siemens (西门子)
- GE (通用电气)
- Philips (飞利浦)
- Toshiba/Canon (东芝/佳能)
- Hitachi (日立)

🧲 检测的信息:
- 厂商名称
- 设备型号
- 磁场强度 (0.2T - 9.4T)
- 软件版本
- 序列类型
- 扫描参数

📊 置信度评估:
- High: 来自JSON元数据
- Medium: 来自NII扩展信息
- Low: 来自文件名分析
- Very Low: 来自目录结构

💡 使用建议:
1. 如果有BIDS格式数据，检测准确率最高
2. 从DICOM转换的NII文件可能保留部分元数据
3. 文件名和目录命名规范有助于提高检测率
4. 批量分析可以发现数据集的整体特征
""")

if __name__ == "__main__":
    print("🔬 MRI扫描仪检测功能测试")
    print("="*60)
    
    # 检查依赖
    try:
        import nibabel
        import pandas
        print("✅ 依赖库检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("请安装: pip install nibabel pandas")
        sys.exit(1)
    
    # 运行测试
    test_basic_detection()
    test_advanced_detection()
    test_batch_detection()
    create_demo_summary()
    
    print("\n🎉 测试完成！")