#!/usr/bin/env python
# coding: utf-8

## In[184]:
# get_ipython().system('pip -q install timm')

from __future__ import print_function

import glob
from itertools import chain
import os
import random
import zipfile
import copy
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from PIL import Image
from sklearn.model_selection import train_test_split
from torch.optim.lr_scheduler import StepLR
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
from tqdm.notebook import tqdm

# get_ipython().run_line_magic('config', "InlineBackend.figure_format = 'retina'")
import ssl #更新ssl证书
ssl._create_default_https_context = ssl._create_unverified_context

print(f"Torch: {torch.__version__}")


# Training settings
batch_size = 32
seed = 42


# In[188]:


def seed_everything(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True

seed_everything(seed)


# In[189]:

import torchvision
from torchvision.transforms import ToTensor

#不做数据增强
# train_transform = transforms.Compose([
#         transforms.Resize((224, 224)),
#         transforms.ToTensor(),
#         transforms.Normalize(mean=[0.485, 0.456, 0.406],
#                              std=[0.229, 0.224, 0.225])
#    ])

# test_transform = transforms.Compose([
#     transforms.Resize((224, 224)),
#     transforms.ToTensor(),
#     transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
# ])

# #做数据增强
train_transform = transforms.Compose([
    transforms.Resize(224),
    transforms.RandomResizedCrop(224, scale=(0.6,1.0), ratio=(0.8,1.0)),
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(0.2),
    torchvision.transforms.ColorJitter(brightness=0.5, contrast=0, saturation=0, hue=0),
    torchvision.transforms.ColorJitter(brightness=0, contrast=0.5, saturation=0, hue=0),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  
])

test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

train_data = torchvision.datasets.ImageFolder('/root/autodl-tmp/ap/train', transform=train_transform )
valid_data = torchvision.datasets.ImageFolder('/root/autodl-tmp/ap/validation', transform=test_transform)
test_data = torchvision.datasets.ImageFolder('/root/autodl-tmp/ap/test', transform=test_transform)


# In[190]:
import torch.utils.data as data
from torch.autograd import Variable
import numpy as np

train_loader = data.DataLoader(train_data, batch_size=batch_size, shuffle=True)
valid_loader = data.DataLoader(valid_data, batch_size=batch_size, shuffle=True)
test_loader  = data.DataLoader(test_data, batch_size=batch_size, shuffle=True) 

print(len(train_data), len(train_loader))
print(len(valid_data), len(valid_loader))
print(len(test_data), len(test_loader))

## In[192]:
import timm
from pprint import pprint
model_names = timm.list_models(pretrained=True)
pprint(model_names)

#%%
# ### Visual Transformer
#用于学术资源加速
import subprocess
import os
result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
output = result.stdout
for line in output.splitlines():
    if '=' in line:
        var, value = line.split('=', 1)
        os.environ[var] = value

# unset http_proxy && unset https_proxy #取消学术加速


model = timm.create_model('convit_base', pretrained=True)
print(model)

#convit预训练权重下载地址
# https://dl.fbaipublicfiles.com/convit/convit_small.pth
# https://dl.fbaipublicfiles.com/convit/convit_base.pth
# https://dl.fbaipublicfiles.com/convit/convit_small.pth

model = timm.create_model('convit_base', pretrained=False)
checkpoint = torch.hub.load_state_dict_from_url(
    url="https://dl.fbaipublicfiles.com/convit/convit_base.pth",
    map_location="cpu", check_hash=True
)
model.load_state_dict(checkpoint)
print(model)

num_ftrs = model.head.in_features
num_classes = 2
# 根据分类任务修改最后一层
model.head = nn.Linear(num_ftrs, out_features=num_classes)
device = 'cuda'
model = model.to(device)

# 打印模型摘要
print(model)


#%% Training
#调整类别不平衡， 假设有两个类别，类别0和类别1
# class_weights = torch.tensor([10.0, 1.0])  # 根据实际情况设置类别权重
# class_weights = class_weights.to('cuda')
# # 定义损失函数，并指定类别权重
# criterion = nn.CrossEntropyLoss(weight=class_weights)

lr = 0.0001 # 原始lr = 0.00003   0.001   学习率小点如0.0001，有利于提高train的表现

# # loss function
criterion = nn.CrossEntropyLoss()
# optimizer
optimizer = optim.Adam(model.parameters(), lr=lr)
# scheduler
scheduler = StepLR(optimizer, step_size=5, gamma=0.1)
# scheduler = StepLR(optimizer, step_size=1, gamma=0.7) 


epochs = 30
n_epochs_stop = 20
min_val_loss = 10

epoch_l = []
loss_l = []
acc_l = []
v_loss_l = []
v_acc_l = []

for epoch in range(epochs):
    epoch_loss = 0
    epoch_accuracy = 0

    for data, label in tqdm(train_loader):
        data = data.to(device)
        label = label.to(device)
        output = model(data)
        loss = criterion(output, label)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        acc = (output.argmax(dim=1) == label).float().mean()
        epoch_accuracy += acc / len(train_loader)
        epoch_loss += loss / len(train_loader)

    with torch.no_grad():
        epoch_val_accuracy = 0
        epoch_val_loss = 0
        for data, label in valid_loader:
            data = data.to(device)
            label = label.to(device)

            val_output = model(data)
            val_loss = criterion(val_output, label)

            acc = (val_output.argmax(dim=1) == label).float().mean()
            epoch_val_accuracy += acc / len(valid_loader)
            epoch_val_loss += val_loss / len(valid_loader)
        
        epoch_l.append(epoch+1)
        loss_l.append(epoch_loss)
        acc_l.append(epoch_accuracy)
        v_loss_l.append(epoch_val_loss)
        v_acc_l.append(epoch_val_accuracy)
        
        print(f"Epoch : {epoch+1} - loss : {epoch_loss:.4f} - acc: {epoch_accuracy:.4f} - val_loss : {epoch_val_loss:.4f} - val_acc: {epoch_val_accuracy:.4f}\n")
        
        if epoch_val_loss < min_val_loss:
            #Saving the model
            best_model = copy.deepcopy(model.state_dict())
            epochs_no_improve = 0
            min_val_loss = epoch_val_loss
            early_stoped = False

        else:
            epochs_no_improve += 1
            # Check early stopping condition
            if epochs_no_improve == n_epochs_stop:
                print('Early stopping!' )
                model.load_state_dict(best_model)
                early_stoped = True
                break
    if early_stoped:
        break

## 保存预测概率、预测label、模型预测值（即深度学习特征）

import torch
import pandas as pd

def get_predictions(model, dataloader):
    model.eval()
    predict_label = []
    probability = []
    max_probability = []
    true_labels = []
    with torch.no_grad():
        for x, y in dataloader:
            if torch.cuda.is_available():
                x = x.to('cuda')
                y = y.to('cuda')
            y_pred = model(x)
            y_pred_prob = torch.softmax(y_pred, dim=1)
            max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)
            predict_label.extend(y_pred_class.tolist())
            probability.extend(y_pred_prob.tolist())
            max_probability.extend(max_prob.tolist())
            true_labels.extend(y.tolist())
    return  true_labels,predict_label,probability, max_probability

# 获取训练集和测试集的预测结果、概率和最大概率
train_true_label,train_predict_label, train_probability, train_max_probability = get_predictions(model, train_loader)
test_true_label,test_predict_label, test_probability, test_max_probability = get_predictions(model, valid_loader) 

# 创建DataFrame保存预测结果、概率和最大概率
train_results = pd.DataFrame({'True Labels': train_true_label,'Predict_label': train_predict_label,
                              'Probability': train_probability, 'Max Probability': train_max_probability})
test_results = pd.DataFrame({'True Labels': test_true_label,'Predict_label': test_predict_label,
                             'Probability': test_probability, 'Max Probability': test_max_probability})

# 将结果保存到Excel文件
train_results.to_excel('/root/autodl-tmp/train_results.xlsx', index=False)    #r'D:\data\train_results.xlsx'
test_results.to_excel('/root/autodl-tmp/test_results.xlsx', index=False)      #r'D:\data\test_results.xlsx'


#%%深度学习网格搜索ParameterGrid调参
from sklearn.model_selection import ParameterGrid
import torch.optim as optim

# 定义模型网格参数
param_grid = {    
    'batch_size': [32, 64, 128],
    'lr': [0.01,0.001, 0.0001],
    'loss_fn': ['CrossEntropyLoss'],
    'step_size': [5,10, 20],
    'gamma': [0.1, 0.5, 0.9]
}

# 创建模型网格
grid = ParameterGrid(param_grid)

num_epochs = 1  # 假设训练的总epoch数为10

# 遍历模型网格
for params in grid:    
    # 设置其他超参数
    batch_size = params['batch_size']
    lr = params['lr']
    loss_fn = params['loss_fn']
    step_size = params['step_size']
    gamma = params['gamma']
    
    # 定义损失函数
    if loss_fn == 'CrossEntropyLoss':
        criterion = nn.CrossEntropyLoss()
    
    
    # 定义优化器
    optimizer = optim.Adam(model.parameters(), lr=lr)
    
    # 训练循环
    for epoch in range(num_epochs):
        model.train()
        epoch_loss = 0.0
        epoch_accuracy = 0.0
        
        for images, labels in train_loader:
            optimizer.zero_grad()
            images = images.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(images)
            
            # 计算损失函数
            loss = criterion(outputs, labels)
            
            # 反向传播和参数更新
            loss.backward()
            optimizer.step()
            
            # 计算训练集上的准确率
            _, predicted = torch.max(outputs, 1)
            accuracy = (predicted == labels).sum().item() / labels.size(0)
            
            epoch_loss += loss.item()
            epoch_accuracy += accuracy
        
        # 打印每个epoch的训练指标
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"Train Loss: {epoch_loss/len(train_loader):.4f}, Train Accuracy: {epoch_accuracy/len(train_loader):.4f}")
    
        # 在此处进行模型评估
        model.eval()
        test_loss = 0.0
        test_accuracy = 0.0
        best_accuracy = 0.0
        with torch.no_grad():
            for images, labels in valid_loader:
                images = images.to(device)
                labels = labels.to(device)
                # 前向传播
                outputs = model(images)            
                # 计算损失函数
                loss = criterion(outputs, labels)            
                # 计算测试集上的准确率
                _, predicted = torch.max(outputs, 1)
                accuracy = (predicted == labels).sum().item() / labels.size(0)
            
                test_loss += loss.item()
                test_accuracy += accuracy
    
    # 打印当前参数组合的性能
        print(f"Parameters: {params}")
        print(f"Test Loss: {test_loss/len(test_loader):.4f}, Test Accuracy: {test_accuracy/len(test_loader):.4f}")
 
    #         # 更新最佳模型参数
    #     if test_accuracy > best_accuracy:
    #         best_accuracy = test_accuracy
    #         best_params = params    

    # # 打印最佳模型参数
    # print("Best Model Parameters:")
    # print(best_params)
    # print(f"Best Test Accuracy: {best_accuracy:.4f}")   
            


# In[ ]:


torch.save(model, './vit_model_pretrained.pt')


#%%随机搜索超参数
import random
from sklearn.model_selection import ParameterSampler
import torch
import torchvision.models as models

# 定义超参数搜索空间
param_space = {    
    'batch_size': [32, 64, 128],
    'lr': [0.001, 0.01, 0.1],
    'loss_fn': ['CrossEntropyLoss'],
    'step_size': [5,10, 20],
    'gamma': [0.1, 0.5, 0.9]
}

#设置随机搜索的参数
n_iter = 10  # 迭代次数
random.seed(42)  # 设置随机种子

# 创建参数采样器
param_sampler = ParameterSampler(param_space, n_iter=n_iter)

# 遍历参数采样器
for params in param_sampler:    
    # 设置其他超参数
    batch_size = params['batch_size']
    lr = params['lr']
    loss_fn = params['loss_fn']
    step_size = params['step_size']
    gamma = params['gamma']

    # 在此处进行模型训练和评估
    # 以下是一个简单的示例，您可以根据您的任务需求进行修改

    # 定义优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)

    # 定义损失函数
    if loss_fn == 'CrossEntropyLoss':
        criterion = torch.nn.CrossEntropyLoss()

    # 进行模型训练和评估
    for epoch in range(num_epochs):
        # 训练模型
        model.train()
        for batch_data, batch_labels in train_loader:
            # 前向传播
            batch_data = batch_data.to(device)
            model = model.to(device)
            outputs = model(batch_data)
            # 计算损失
            loss = criterion(outputs, batch_labels)
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        # 在验证集上评估模型
        model.eval()
        with torch.no_grad():
            total_correct = 0
            total_samples = 0
            for val_data, val_labels in valid_loader:
                val_outputs = model(val_data)
                _, predicted = torch.max(val_outputs, 1)
                total_correct += (predicted == val_labels).sum().item()
                total_samples += val_labels.size(0)
            
            accuracy = total_correct / total_samples

        # 打印当前参数组合的性能
        print(f"Parameters: {params}, Performance: {accuracy}")


# In[214]:


y_pred_list = []
y_true_list = []
with torch.no_grad():
    for x_batch, y_batch in tqdm(test_loader):
        x_batch, y_batch = x_batch.to(device), y_batch.to(device)
        y_test_pred = model(x_batch)
        _, y_pred_tag = torch.max(y_test_pred, dim = 1)
        y_pred_list.append(y_pred_tag.cpu().numpy())
        y_true_list.append(y_batch.cpu().numpy())


# In[132]:


def flatten(new:list, target:list):
    for li in target:
        for value in list(li):
            new.append(value)

y_pred = []
y_true = []
flatten(y_pred, y_pred_list)
flatten(y_true, y_true_list)


# In[133]:


from sklearn.metrics import accuracy_score, f1_score
print("Overall accuracy:", accuracy_score(y_true, y_pred))
print("Overall F1:", f1_score(y_true, y_pred, average='weighted'))


# In[134]:


from sklearn.metrics import precision_recall_fscore_support as score

precision, recall, fscore, support = score(y_true, y_pred)

print('precision: {}'.format(precision))
print('recall: {}'.format(recall))
print('fscore: {}'.format(fscore))
print('support: {}'.format(support))


# In[135]:


from sklearn.metrics import confusion_matrix
import numpy as np
import pandas as pd
import seaborn as sns

def plot_cm(y_true, y_pred, figsize=(10,9)):
    cm = confusion_matrix(y_true, y_pred, labels=np.unique(y_true))
    cm_sum = np.sum(cm, axis=1, keepdims=True)
    cm_perc = cm / cm_sum.astype(float) * 100
    annot = np.empty_like(cm).astype(str)
    nrows, ncols = cm.shape
    for i in range(nrows):
        for j in range(ncols):
            c = cm[i, j]
            p = cm_perc[i, j]
            if i == j:
                s = cm_sum[i]
                annot[i, j] = '%.1f%%\n%d/%d' % (p, c, s)
            elif c == 0:
                annot[i, j] = ''
            else:
                annot[i, j] = '%.1f%%\n%d' % (p, c)
    cm = pd.DataFrame(cm, index=np.unique(y_true), columns=np.unique(y_true))
    cm.index.name = 'Actual'
    cm.columns.name = 'Predicted'
    fig, ax = plt.subplots(figsize=figsize)
    sns.heatmap(cm, cmap= "YlGnBu", annot=annot, fmt='', ax=ax)

plot_cm(y_true, y_pred)

display()


# In[ ]:


loss_l_c = []
acc_l_c = []
v_loss_l_c = []
v_acc_l_c = []
for x in loss_l:
    x = x.cpu().detach().numpy()
    loss_l_c.append(x)
for x in acc_l:
    x = x.cpu().detach().numpy()
    acc_l_c.append(x)
for x in v_loss_l:
    x = x.cpu().detach().numpy()
    v_loss_l_c.append(x)
for x in v_acc_l:
    x = x.cpu().detach().numpy()
    v_acc_l_c.append(x)


# In[ ]:


import plotly.graph_objects as go

# Create traces
fig = go.Figure()
fig.add_trace(go.Scatter(x=epoch_l, y=loss_l_c,
                    mode='lines+markers',
                    name='Train loss'))
fig.add_trace(go.Scatter(x=epoch_l, y=acc_l_c,
                    mode='lines+markers',
                    name='Train accuracy'))
fig.add_trace(go.Scatter(x=epoch_l, y=v_loss_l_c,
                    mode='lines+markers',
                    name='Validation loss'))
fig.add_trace(go.Scatter(x=epoch_l, y=v_acc_l_c,
                    mode='lines+markers',
                    name='Validation accuracy'))
fig.update_layout(
    title='ViT(pre-trained)',
    autosize=False,
    width=1000,
    height=600,
)

fig.show()

