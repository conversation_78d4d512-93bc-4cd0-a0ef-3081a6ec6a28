import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import torchvision.transforms as transforms
from torchvision.models import resnet18, resnet50
from PIL import Image
import os
from typing import List, Tuple, Dict
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict


class PatchDataset(Dataset):
    def __init__(self, patient_patches: Dict[str, List[str]], labels: Dict[str, int], transform=None):
        self.patient_patches = patient_patches
        self.labels = labels
        self.patients = list(patient_patches.keys())
        self.transform = transform
        
    def __len__(self):
        return len(self.patients)
    
    def __getitem__(self, idx):
        patient_id = self.patients[idx]
        patch_paths = self.patient_patches[patient_id]
        label = self.labels[patient_id]
        
        patches = []
        for patch_path in patch_paths:
            try:
                patch = Image.open(patch_path).convert('RGB')
                if self.transform:
                    patch = self.transform(patch)
                patches.append(patch)
            except Exception as e:
                print(f"Error loading patch {patch_path}: {e}")
                continue
        
        if len(patches) == 0:
            patches = [torch.zeros(3, 224, 224)]
        
        patches = torch.stack(patches)
        return patches, torch.tensor(label, dtype=torch.long), patient_id


class FeatureExtractor(nn.Module):
    def __init__(self, model_name='resnet18', pretrained=True, feature_dim=512):
        super(FeatureExtractor, self).__init__()
        
        if model_name == 'resnet18':
            self.backbone = resnet18(pretrained=pretrained)
            self.backbone.fc = nn.Identity()
            backbone_dim = 512
        elif model_name == 'resnet50':
            self.backbone = resnet50(pretrained=pretrained)
            self.backbone.fc = nn.Identity()
            backbone_dim = 2048
        else:
            raise ValueError(f"Unsupported model: {model_name}")
        
        self.feature_projection = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.5)
        )
        
    def forward(self, x):
        batch_size, num_patches, c, h, w = x.shape
        x = x.view(-1, c, h, w)
        features = self.backbone(x)
        features = self.feature_projection(features)
        features = features.view(batch_size, num_patches, -1)
        return features


class AttentionMIL(nn.Module):
    def __init__(self, feature_dim=512, num_classes=2, dropout=0.5):
        super(AttentionMIL, self).__init__()
        
        self.attention = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.Tanh(),
            nn.Dropout(dropout),
            nn.Linear(256, 1)
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        batch_size, num_patches, feature_dim = x.shape
        
        attention_weights = self.attention(x)
        attention_weights = torch.softmax(attention_weights, dim=1)
        
        weighted_features = torch.sum(attention_weights * x, dim=1)
        
        logits = self.classifier(weighted_features)
        
        return logits, attention_weights


class MeanPoolingMIL(nn.Module):
    def __init__(self, feature_dim=512, num_classes=2, dropout=0.5):
        super(MeanPoolingMIL, self).__init__()
        
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        pooled_features = torch.mean(x, dim=1)
        logits = self.classifier(pooled_features)
        return logits, None


class MaxPoolingMIL(nn.Module):
    def __init__(self, feature_dim=512, num_classes=2, dropout=0.5):
        super(MaxPoolingMIL, self).__init__()
        
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
        
    def forward(self, x):
        pooled_features = torch.max(x, dim=1)[0]
        logits = self.classifier(pooled_features)
        return logits, None


class PathologyMIL:
    def __init__(self, 
                 feature_extractor_name='resnet18',
                 mil_method='attention',
                 feature_dim=512,
                 num_classes=2,
                 device='cuda' if torch.cuda.is_available() else 'cpu'):
        
        self.device = device
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        
        self.feature_extractor = FeatureExtractor(
            model_name=feature_extractor_name,
            feature_dim=feature_dim
        ).to(device)
        
        if mil_method == 'attention':
            self.mil_model = AttentionMIL(feature_dim, num_classes).to(device)
        elif mil_method == 'mean':
            self.mil_model = MeanPoolingMIL(feature_dim, num_classes).to(device)
        elif mil_method == 'max':
            self.mil_model = MaxPoolingMIL(feature_dim, num_classes).to(device)
        else:
            raise ValueError(f"Unsupported MIL method: {mil_method}")
        
        self.mil_method = mil_method
        
    def train_model(self, 
                   train_loader, 
                   val_loader,
                   num_epochs=50,
                   learning_rate=1e-4,
                   weight_decay=1e-4,
                   save_path=None):
        
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(
            list(self.feature_extractor.parameters()) + list(self.mil_model.parameters()),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.1)
        
        train_losses = []
        val_losses = []
        val_accuracies = []
        val_aucs = []
        
        best_val_auc = 0.0
        
        for epoch in range(num_epochs):
            self.feature_extractor.train()
            self.mil_model.train()
            
            train_loss = 0.0
            for batch_idx, (patches, labels, patient_ids) in enumerate(train_loader):
                patches = patches.to(self.device)
                labels = labels.to(self.device)
                
                optimizer.zero_grad()
                
                features = self.feature_extractor(patches)
                logits, attention_weights = self.mil_model(features)
                
                loss = criterion(logits, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
                if batch_idx % 10 == 0:
                    print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {loss.item():.4f}')
            
            scheduler.step()
            
            val_loss, val_acc, val_auc = self.evaluate(val_loader)
            
            train_losses.append(train_loss / len(train_loader))
            val_losses.append(val_loss)
            val_accuracies.append(val_acc)
            val_aucs.append(val_auc)
            
            print(f'Epoch {epoch}: Train Loss: {train_loss/len(train_loader):.4f}, '
                  f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}, Val AUC: {val_auc:.4f}')
            
            if val_auc > best_val_auc:
                best_val_auc = val_auc
                if save_path:
                    self.save_model(save_path)
        
        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_accuracies': val_accuracies,
            'val_aucs': val_aucs
        }
    
    def evaluate(self, data_loader):
        self.feature_extractor.eval()
        self.mil_model.eval()
        
        criterion = nn.CrossEntropyLoss()
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for patches, labels, patient_ids in data_loader:
                patches = patches.to(self.device)
                labels = labels.to(self.device)
                
                features = self.feature_extractor(patches)
                logits, attention_weights = self.mil_model(features)
                
                loss = criterion(logits, labels)
                total_loss += loss.item()
                
                probabilities = torch.softmax(logits, dim=1)
                predictions = torch.argmax(logits, dim=1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        avg_loss = total_loss / len(data_loader)
        accuracy = accuracy_score(all_labels, all_predictions)
        
        if self.num_classes == 2:
            auc = roc_auc_score(all_labels, [prob[1] for prob in all_probabilities])
        else:
            auc = roc_auc_score(all_labels, all_probabilities, multi_class='ovr')
        
        return avg_loss, accuracy, auc
    
    def predict(self, data_loader):
        self.feature_extractor.eval()
        self.mil_model.eval()
        
        predictions = []
        probabilities = []
        patient_ids = []
        attention_weights_list = []
        
        with torch.no_grad():
            for patches, labels, batch_patient_ids in data_loader:
                patches = patches.to(self.device)
                
                features = self.feature_extractor(patches)
                logits, attention_weights = self.mil_model(features)
                
                probs = torch.softmax(logits, dim=1)
                preds = torch.argmax(logits, dim=1)
                
                predictions.extend(preds.cpu().numpy())
                probabilities.extend(probs.cpu().numpy())
                patient_ids.extend(batch_patient_ids)
                
                if attention_weights is not None:
                    attention_weights_list.extend(attention_weights.cpu().numpy())
        
        return {
            'predictions': predictions,
            'probabilities': probabilities,
            'patient_ids': patient_ids,
            'attention_weights': attention_weights_list if attention_weights_list else None
        }
    
    def save_model(self, save_path):
        torch.save({
            'feature_extractor_state_dict': self.feature_extractor.state_dict(),
            'mil_model_state_dict': self.mil_model.state_dict(),
            'feature_dim': self.feature_dim,
            'num_classes': self.num_classes,
            'mil_method': self.mil_method
        }, save_path)
    
    def load_model(self, load_path):
        checkpoint = torch.load(load_path, map_location=self.device)
        self.feature_extractor.load_state_dict(checkpoint['feature_extractor_state_dict'])
        self.mil_model.load_state_dict(checkpoint['mil_model_state_dict'])


def create_transforms():
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomVerticalFlip(),
        transforms.RandomRotation(90),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    return train_transform, val_transform


def load_data_from_directory(data_dir, max_patches_per_patient=100):
    patient_patches = defaultdict(list)
    labels = {}
    
    for class_name in os.listdir(data_dir):
        class_path = os.path.join(data_dir, class_name)
        if not os.path.isdir(class_path):
            continue
        
        class_label = 0 if class_name.lower() in ['normal', 'benign', '0'] else 1
        
        for patient_folder in os.listdir(class_path):
            patient_path = os.path.join(class_path, patient_folder)
            if not os.path.isdir(patient_path):
                continue
            
            patient_id = f"{class_name}_{patient_folder}"
            labels[patient_id] = class_label
            
            patch_files = [f for f in os.listdir(patient_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.tif'))]
            
            if len(patch_files) > max_patches_per_patient:
                patch_files = np.random.choice(patch_files, max_patches_per_patient, replace=False)
            
            for patch_file in patch_files:
                patch_path = os.path.join(patient_path, patch_file)
                patient_patches[patient_id].append(patch_path)
    
    return dict(patient_patches), labels


def plot_training_history(history, save_path=None):
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    axes[0, 0].plot(history['train_losses'], label='Train Loss')
    axes[0, 0].plot(history['val_losses'], label='Val Loss')
    axes[0, 0].set_title('Training and Validation Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    axes[0, 1].plot(history['val_accuracies'])
    axes[0, 1].set_title('Validation Accuracy')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].grid(True)
    
    axes[1, 0].plot(history['val_aucs'])
    axes[1, 0].set_title('Validation AUC')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('AUC')
    axes[1, 0].grid(True)
    
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


def main():
    data_dir = "path/to/your/patch/data"
    
    print("Loading data...")
    patient_patches, labels = load_data_from_directory(data_dir)
    
    print(f"Loaded {len(patient_patches)} patients")
    print(f"Class distribution: {pd.Series(labels).value_counts()}")
    
    patients = list(patient_patches.keys())
    train_patients, temp_patients = train_test_split(patients, test_size=0.3, random_state=42, stratify=[labels[p] for p in patients])
    val_patients, test_patients = train_test_split(temp_patients, test_size=0.5, random_state=42, stratify=[labels[p] for p in temp_patients])
    
    train_patches = {p: patient_patches[p] for p in train_patients}
    val_patches = {p: patient_patches[p] for p in val_patients}
    test_patches = {p: patient_patches[p] for p in test_patients}
    
    train_labels = {p: labels[p] for p in train_patients}
    val_labels = {p: labels[p] for p in val_patients}
    test_labels = {p: labels[p] for p in test_patients}
    
    train_transform, val_transform = create_transforms()
    
    train_dataset = PatchDataset(train_patches, train_labels, train_transform)
    val_dataset = PatchDataset(val_patches, val_labels, val_transform)
    test_dataset = PatchDataset(test_patches, test_labels, val_transform)
    
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False, num_workers=4)
    
    print("Training model...")
    mil_model = PathologyMIL(
        feature_extractor_name='resnet18',
        mil_method='attention',
        feature_dim=512,
        num_classes=2
    )
    
    history = mil_model.train_model(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=50,
        learning_rate=1e-4,
        save_path='best_mil_model.pth'
    )
    
    print("Evaluating on test set...")
    test_loss, test_acc, test_auc = mil_model.evaluate(test_loader)
    print(f"Test Results - Loss: {test_loss:.4f}, Accuracy: {test_acc:.4f}, AUC: {test_auc:.4f}")
    
    test_results = mil_model.predict(test_loader)
    print("\nTest Predictions:")
    print(classification_report([test_labels[pid] for pid in test_results['patient_ids']], 
                               test_results['predictions']))
    
    plot_training_history(history, 'training_history.png')
    
    print("Training completed!")


if __name__ == "__main__":
    main()