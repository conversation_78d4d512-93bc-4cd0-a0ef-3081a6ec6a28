import logging
import os
from typing import Any, BinaryIO, Callable, Dict, List, Optional, Union

import pandas as pd
from joblib.memory import Memory

from pycaret.anomaly.oop import AnomalyExperiment
from pycaret.loggers.base_logger import BaseLogger
from pycaret.utils.constants import DATAFRAME_LIKE, SEQUENCE_LIKE
from pycaret.utils.generic import check_if_global_is_not_none

_EXPERIMENT_CLASS = AnomalyExperiment
_CURRENT_EXPERIMENT: Optional[AnomalyExperiment] = None
_CURRENT_EXPERIMENT_EXCEPTION = (
    "_CURRENT_EXPERIMENT global variable is not set. Please run setup() first."
)
_CURRENT_EXPERIMENT_DECORATOR_DICT = {
    "_CURRENT_EXPERIMENT": _CURRENT_EXPERIMENT_EXCEPTION
}


def setup(
    data: Optional[DATAFRAME_LIKE] = None,
    data_func: Optional[Callable[[], DATAFRAME_LIKE]] = None,
    index: Union[bool, int, str, SEQUENCE_LIKE] = True,
    ordinal_features: Optional[Dict[str, list]] = None,
    numeric_features: Optional[List[str]] = None,
    categorical_features: Optional[List[str]] = None,
    date_features: Optional[List[str]] = None,
    text_features: Optional[List[str]] = None,
    ignore_features: Optional[List[str]] = None,
    keep_features: Optional[List[str]] = None,
    preprocess: bool = True,
    create_date_columns: List[str] = ["day", "month", "year"],
    imputation_type: Optional[str] = "simple",
    numeric_imputation: str = "mean",
    categorical_imputation: str = "mode",
    text_features_method: str = "tf-idf",
    max_encoding_ohe: int = -1,
    encoding_method: Optional[Any] = None,
    rare_to_value: Optional[float] = None,
    rare_value: str = "rare",
    polynomial_features: bool = False,
    polynomial_degree: int = 2,
    low_variance_threshold: Optional[float] = None,
    group_features: Optional[dict] = None,
    drop_groups: bool = False,
    remove_multicollinearity: bool = False,
    multicollinearity_threshold: float = 0.9,
    bin_numeric_features: Optional[List[str]] = None,
    remove_outliers: bool = False,
    outliers_method: str = "iforest",
    outliers_threshold: float = 0.05,
    transformation: bool = False,
    transformation_method: str = "yeo-johnson",
    normalize: bool = False,
    normalize_method: str = "zscore",
    pca: bool = False,
    pca_method: str = "linear",
    pca_components: Optional[Union[int, float, str]] = None,
    custom_pipeline: Optional[Any] = None,
    custom_pipeline_position: int = -1,
    n_jobs: Optional[int] = -1,
    use_gpu: bool = False,
    html: bool = True,
    session_id: Optional[int] = None,
    system_log: Union[bool, str, logging.Logger] = True,
    log_experiment: Union[bool, str, BaseLogger, List[Union[str, BaseLogger]]] = False,
    experiment_name: Optional[str] = None,
    experiment_custom_tags: Optional[Dict[str, Any]] = None,
    log_plots: Union[bool, list] = False,
    log_profile: bool = False,
    log_data: bool = False,
    verbose: bool = True,
    memory: Union[bool, str, Memory] = True,
    profile: bool = False,
    profile_kwargs: Optional[Dict[str, Any]] = None,
):
    """
    This function initializes the training environment and creates the transformation
    pipeline. Setup function must be called before executing any other function. It
    takes one mandatory parameter: ``data``. All the other parameters are optional.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)


    data: dataframe-like
        Data set with shape (n_samples, n_features), where n_samples is the
        number of samples and n_features is the number of features. If data
        is not a pandas dataframe, it's converted to one using default column
        names.


    data_func: Callable[[], DATAFRAME_LIKE] = None
        The function that generate ``data`` (the dataframe-like input). This
        is useful when the dataset is large, and you need parallel operations
        such as ``compare_models``. It can avoid broadcasting large dataset
        from driver to workers. Notice one and only one of ``data`` and
        ``data_func`` must be set.


    index: bool, int, str or sequence, default = True
        Handle indices in the `data` dataframe.
            - If False: Reset to RangeIndex.
            - If True: Keep the provided index.
            - If int: Position of the column to use as index.
            - If str: Name of the column to use as index.
            - If sequence: Array with shape=(n_samples,) to use as index.


    ordinal_features: dict, default = None
        Categorical features to be encoded ordinally. For example, a categorical
        feature with 'low', 'medium', 'high' values where low < medium < high can
        be passed as ordinal_features = {'column_name' : ['low', 'medium', 'high']}.


    numeric_features: list of str, default = None
        If the inferred data types are not correct, the numeric_features param can
        be used to define the data types. It takes a list of strings with column
        names that are numeric.


    categorical_features: list of str, default = None
        If the inferred data types are not correct, the categorical_features param
        can be used to define the data types. It takes a list of strings with column
        names that are categorical.


    date_features: list of str, default = None
        If the inferred data types are not correct, the date_features param can be
        used to overwrite the data types. It takes a list of strings with column
        names that are DateTime.


    text_features: list of str, default = None
        Column names that contain a text corpus. If None, no text features are
        selected.


    ignore_features: list of str, default = None
        ignore_features param can be used to ignore features during preprocessing
        and model training. It takes a list of strings with column names that are
        to be ignored.


    keep_features: list of str, default = None
        keep_features param can be used to always keep specific features during
        preprocessing, i.e. these features are never dropped by any kind of
        feature selection. It takes a list of strings with column names that are
        to be kept.


    preprocess: bool, default = True
        When set to False, no transformations are applied except for train_test_split
        and custom transformations passed in ``custom_pipeline`` param. Data must be
        ready for modeling (no missing values, no dates, categorical data encoding),
        when preprocess is set to False.


    create_date_columns: list of str, default = ["day", "month", "year"]
        Columns to create from the date features. Note that created features
        with zero variance (e.g. the feature hour in a column that only contains
        dates) are ignored. Allowed values are datetime attributes from
        `pandas.Series.dt`. The datetime format of the feature is inferred
        automatically from the first non NaN value.


    imputation_type: str or None, default = 'simple'
        The type of imputation to use. Unsupervised learning only supports
        'imputation_type=simple'. If None, no imputation of missing values
        is performed.


    numeric_imputation: str, default = 'mean'
        Missing values in numeric features are imputed with 'mean' value of the feature
        in the training dataset. The other available option is 'median' or 'zero'.


    categorical_imputation: str, default = 'constant'
        Missing values in categorical features are imputed with a constant 'not_available'
        value. The other available option is 'mode'.


    text_features_method: str, default = "tf-idf"
        Method with which to embed the text features in the dataset. Choose
        between "bow" (Bag of Words - CountVectorizer) or "tf-idf" (TfidfVectorizer).
        Be aware that the sparse matrix output of the transformer is converted
        internally to its full array. This can cause memory issues for large
        text embeddings.


    max_encoding_ohe: int, default = -1
        Categorical columns with `max_encoding_ohe` or less unique values are
        encoded using OneHotEncoding. If more, the `encoding_method` estimator
        is used. Note that columns with exactly two classes are always encoded
        ordinally. Set to below 0 to always use OneHotEncoding.


    encoding_method: category-encoders estimator, default = None
        A `category-encoders` estimator to encode the categorical columns
        with more than `max_encoding_ohe` unique values. If None,
        `category_encoders.basen.BaseN` is used.


    rare_to_value: float or None, default=None
        Minimum fraction of category occurrences in a categorical column.
        If a category is less frequent than `rare_to_value * len(X)`, it is
        replaced with the string `other`. Use this parameter to group rare
        categories before encoding the column. If None, ignores this step.


    polynomial_features: bool, default = False
        When set to True, new features are derived using existing numeric features.


    polynomial_degree: int, default = 2
        Degree of polynomial features. For example, if an input sample is two dimensional
        and of the form [a, b], the polynomial features with degree = 2 are:
        [1, a, b, a^2, ab, b^2]. Ignored when ``polynomial_features`` is not True.


    low_variance_threshold: float or None, default = None
        Remove features with a training-set variance lower than the provided
        threshold. If 0, keep all features with non-zero variance, i.e. remove
        the features that have the same value in all samples. If None, skip
        this transformation step.


    group_features: dict or None, default = None
        When the dataset contains features with related characteristics,
        add new fetaures with the following statistical properties of that
        group: min, max, mean, std, median and mode. The parameter takes a
        dict with the group name as key and a list of feature names
        belonging to that group as value.


    drop_groups: bool, default=False
        Whether to drop the original features in the group. Ignored when
        ``group_features`` is None.


    remove_multicollinearity: bool, default = False
        When set to True, features with the inter-correlations higher than
        the defined threshold are removed. For each group, it removes all
        except the first feature.


    multicollinearity_threshold: float, default = 0.9
        Minimum absolute Pearson correlation to identify correlated
        features. The default value removes equal columns. Ignored when
        ``remove_multicollinearity`` is not True.


    bin_numeric_features: list of str, default = None
        To convert numeric features into categorical, bin_numeric_features parameter can
        be used. It takes a list of strings with column names to be discretized. It does
        so by using 'sturges' rule to determine the number of clusters and then apply
        KMeans algorithm. Original values of the feature are then replaced by the
        cluster label.


    remove_outliers: bool, default = False
        When set to True, outliers from the training data are removed using an
        Isolation Forest.


    outliers_method: str, default = "iforest"
        Method with which to remove outliers. Possible values are:
            - 'iforest': Uses sklearn's IsolationForest.
            - 'ee': Uses sklearn's EllipticEnvelope.
            - 'lof': Uses sklearn's LocalOutlierFactor.


    outliers_threshold: float, default = 0.05
        The percentage outliers to be removed from the dataset. Ignored
        when ``remove_outliers=False``.


    transformation: bool, default = False
        When set to True, it applies the power transform to make data more Gaussian-like.
        Type of transformation is defined by the ``transformation_method`` parameter.


    transformation_method: str, default = 'yeo-johnson'
        Defines the method for transformation. By default, the transformation method is
        set to 'yeo-johnson'. The other available option for transformation is 'quantile'.
        Ignored when ``transformation`` is not True.


    normalize: bool, default = False
        When set to True, it transforms the features by scaling them to a given
        range. Type of scaling is defined by the ``normalize_method`` parameter.


    normalize_method: str, default = 'zscore'
        Defines the method for scaling. By default, normalize method is set to 'zscore'
        The standard zscore is calculated as z = (x - u) / s. Ignored when ``normalize``
        is not True. The other options are:

        - minmax: scales and translates each feature individually such that it is in
          the range of 0 - 1.
        - maxabs: scales and translates each feature individually such that the
          maximal absolute value of each feature will be 1.0. It does not
          shift/center the data, and thus does not destroy any sparsity.
        - robust: scales and translates each feature according to the Interquartile
          range. When the dataset contains outliers, robust scaler often gives
          better results.


    pca: bool, default = False
        When set to True, dimensionality reduction is applied to project the data into
        a lower dimensional space using the method defined in ``pca_method`` parameter.


    pca_method: str, default = 'linear'
        Method with which to apply PCA. Possible values are:
            - 'linear': Uses Singular Value  Decomposition.
            - 'kernel': Dimensionality reduction through the use of RBF kernel.
            - 'incremental': Similar to 'linear', but more efficient for large datasets.


    pca_components: int, float, str or None, default = None
        Number of components to keep. This parameter is ignored when `pca=False`.
            - If None: All components are kept.
            - If int: Absolute number of components.
            - If float: Such an amount that the variance that needs to be explained
                        is greater than the percentage specified by `n_components`.
                        Value should lie between 0 and 1 (ony for pca_method='linear').
            - If "mle": Minka’s MLE is used to guess the dimension (ony for pca_method='linear').


    custom_pipeline: list of (str, transformer), dict or Pipeline, default = None
        Addidiotnal custom transformers. If passed, they are applied to the
        pipeline last, after all the build-in transformers.


    custom_pipeline_position: int, default = -1
        Position of the custom pipeline in the overal preprocessing pipeline.
        The default value adds the custom pipeline last.


    n_jobs: int, default = -1
        The number of jobs to run in parallel (for functions that supports parallel
        processing) -1 means using all processors. To run all functions on single
        processor set n_jobs to None.


    use_gpu: bool or str, default = False
        When set to True, it will use GPU for training with algorithms that support it,
        and fall back to CPU if they are unavailable. When set to 'force', it will only
        use GPU-enabled algorithms and raise exceptions when they are unavailable. When
        False, all algorithms are trained using CPU only.

        GPU enabled algorithms:

        - None at this moment.


    html: bool, default = True
        When set to False, prevents runtime display of monitor. This must be set to False
        when the environment does not support IPython. For example, command line terminal,
        Databricks Notebook, Spyder and other similar IDEs.


    session_id: int, default = None
        Controls the randomness of experiment. It is equivalent to 'random_state' in
        scikit-learn. When None, a pseudo random number is generated. This can be used
        for later reproducibility of the entire experiment.


    system_log: bool or str or logging.Logger, default = True
        Whether to save the system logging file (as logs.log). If the input
        is a string, use that as the path to the logging file. If the input
        already is a logger object, use that one instead.


    log_experiment: bool, default = False
        A (list of) PyCaret ``BaseLogger`` or str (one of 'mlflow', 'wandb', 'comet_ml')
        corresponding to a logger to determine which experiment loggers to use.
        Setting to True will use just MLFlow.


    experiment_name: str, default = None
        Name of the experiment for logging. Ignored when ``log_experiment`` is False.


    experiment_custom_tags: dict or None, default = None
        Dictionary of tag_name: String -> value: (String, but will be string-ified
        if not) passed to the mlflow.set_tags to add new custom tags for the experiment.


    log_plots: bool or list, default = False
        When set to True, certain plots are logged automatically in the ``MLFlow`` server.
        To change the type of plots to be logged, pass a list containing plot IDs. Refer
        to documentation of ``plot_model``. Ignored when ``log_experiment`` is False.


    log_profile: bool, default = False
        When set to True, data profile is logged on the ``MLflow`` server as a html file.
        Ignored when ``log_experiment`` is False.


    log_data: bool, default = False
        When set to True, dataset is logged on the ``MLflow`` server as a csv file.
        Ignored when ``log_experiment`` is False.


    verbose: bool, default = True
        When set to False, Information grid is not printed.


    memory: str, bool or Memory, default=True
        Used to cache the fitted transformers of the pipeline.
            If False: No caching is performed.
            If True: A default temp directory is used.
            If str: Path to the caching directory.


    profile: bool, default = False
        When set to True, an interactive EDA report is displayed.


    profile_kwargs: dict, default = {} (empty dict)
        Dictionary of arguments passed to the ProfileReport method used
        to create the EDA report. Ignored if ``profile`` is False.


    Returns:
        Global variables that can be changed using the ``set_config`` function.

    """

    exp = _EXPERIMENT_CLASS()
    set_current_experiment(exp)
    return exp.setup(
        data=data,
        data_func=data_func,
        index=index,
        ordinal_features=ordinal_features,
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        date_features=date_features,
        text_features=text_features,
        ignore_features=ignore_features,
        keep_features=keep_features,
        preprocess=preprocess,
        create_date_columns=create_date_columns,
        imputation_type=imputation_type,
        numeric_imputation=numeric_imputation,
        categorical_imputation=categorical_imputation,
        text_features_method=text_features_method,
        max_encoding_ohe=max_encoding_ohe,
        encoding_method=encoding_method,
        rare_to_value=rare_to_value,
        rare_value=rare_value,
        polynomial_features=polynomial_features,
        polynomial_degree=polynomial_degree,
        low_variance_threshold=low_variance_threshold,
        group_features=group_features,
        drop_groups=drop_groups,
        remove_multicollinearity=remove_multicollinearity,
        multicollinearity_threshold=multicollinearity_threshold,
        bin_numeric_features=bin_numeric_features,
        remove_outliers=remove_outliers,
        outliers_method=outliers_method,
        outliers_threshold=outliers_threshold,
        transformation=transformation,
        transformation_method=transformation_method,
        normalize=normalize,
        normalize_method=normalize_method,
        pca=pca,
        pca_method=pca_method,
        pca_components=pca_components,
        custom_pipeline=custom_pipeline,
        custom_pipeline_position=custom_pipeline_position,
        n_jobs=n_jobs,
        use_gpu=use_gpu,
        html=html,
        session_id=session_id,
        system_log=system_log,
        log_experiment=log_experiment,
        experiment_name=experiment_name,
        experiment_custom_tags=experiment_custom_tags,
        log_plots=log_plots,
        log_profile=log_profile,
        log_data=log_data,
        verbose=verbose,
        memory=memory,
        profile=profile,
        profile_kwargs=profile_kwargs,
    )


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def create_model(
    model: Union[str, Any],
    fraction: float = 0.05,
    verbose: bool = True,
    fit_kwargs: Optional[dict] = None,
    experiment_custom_tags: Optional[Dict[str, Any]] = None,
    **kwargs,
):
    """
    This function trains a given model from the model library. All available
    models can be accessed using the ``models`` function.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')


    model: str or scikit-learn compatible object
        ID of an model available in the model library or pass an untrained
        model object consistent with scikit-learn API. Estimators available
        in the model library (ID - Name):

        * 'abod' - Angle-base Outlier Detection
        * 'cluster' - Clustering-Based Local Outlier
        * 'cof' - Connectivity-Based Outlier Factor
        * 'histogram' - Histogram-based Outlier Detection
        * 'iforest' - Isolation Forest
        * 'knn' - k-Nearest Neighbors Detector
        * 'lof' - Local Outlier Factor
        * 'svm' - One-class SVM detector
        * 'pca' - Principal Component Analysis
        * 'mcd' - Minimum Covariance Determinant
        * 'sod' - Subspace Outlier Detection
        * 'sos' - Stochastic Outlier Selection


    fraction: float, default = 0.05
        The amount of contamination of the data set, i.e. the proportion of
        outliers in the data set. Used when fitting to define the threshold on
        the decision function.


    verbose: bool, default = True
        Status update is not printed when verbose is set to False.


    experiment_custom_tags: dict, default = None
        Dictionary of tag_name: String -> value: (String, but will be string-ified
        if not) passed to the mlflow.set_tags to add new custom tags for the experiment.


    fit_kwargs: dict, default = {} (empty dict)
        Dictionary of arguments passed to the fit method of the model.


    **kwargs:
        Additional keyword arguments to pass to the estimator.


    Returns:
        Trained Model

    """

    return _CURRENT_EXPERIMENT.create_model(
        estimator=model,
        fraction=fraction,
        fit_kwargs=fit_kwargs,
        verbose=verbose,
        experiment_custom_tags=experiment_custom_tags,
        **kwargs,
    )


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def assign_model(
    model, transformation: bool = False, score: bool = True, verbose: bool = True
) -> pd.DataFrame:
    """
    This function assigns anomaly labels to the dataset for a given model.
    (1 = outlier, 0 = inlier).


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')
    >>> knn_df = assign_model(knn)


    model: scikit-learn compatible object
        Trained model object


    transformation: bool, default = False
        Whether to apply anomaly labels on the transformed dataset.


    score: bool, default = True
        Whether to show outlier score or not.


    verbose: bool, default = True
        Status update is not printed when verbose is set to False.


    Returns:
        pandas.DataFrame

    """
    return _CURRENT_EXPERIMENT.assign_model(
        model, transformation=transformation, score=score, verbose=verbose
    )


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def plot_model(
    model,
    plot: str = "tsne",
    feature: Optional[str] = None,
    label: bool = False,
    scale: float = 1,
    save: bool = False,
    display_format: Optional[str] = None,
) -> Optional[str]:
    """
    This function analyzes the performance of a trained model.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')
    >>> plot_model(knn, plot = 'tsne')


    model: scikit-learn compatible object
        Trained Model Object


    plot: str, default = 'tsne'
        List of available plots (ID - Name):

        * 'tsne' - t-SNE (3d) Dimension Plot
        * 'umap' - UMAP Dimensionality Plot


    feature: str, default = None
        Feature to be used as a hoverover tooltip and/or label when the ``label``
        param is set to True. When feature is None, first column of the dataset
        is used.


    label: bool, default = False
        Name of column to be used as data labels.


    scale: float, default = 1
        The resolution scale of the figure.


    save: bool, default = False
        When set to True, plot is saved in the current working directory.


    display_format: str, default = None
        To display plots in Streamlit (https://www.streamlit.io/), set this to 'streamlit'.
        Currently, not all plots are supported.


    Returns:
        Path to saved file, if any.

    """
    return _CURRENT_EXPERIMENT.plot_model(
        model,
        plot=plot,
        feature_name=feature,
        label=label,
        scale=scale,
        save=save,
        display_format=display_format,
    )


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def evaluate_model(
    model,
    feature: Optional[str] = None,
    fit_kwargs: Optional[dict] = None,
):
    """
    This function displays a user interface for analyzing performance of a trained
    model. It calls the ``plot_model`` function internally.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')
    >>> evaluate_model(knn)


    model: scikit-learn compatible object
        Trained model object


    feature: str, default = None
        Feature to be used as a hoverover tooltip and/or label when the ``label``
        param is set to True. When feature is None, first column of the dataset
        is used by default.


    fit_kwargs: dict, default = {} (empty dict)
        Dictionary of arguments passed to the fit method of the model.

    Returns:
        None


    Warnings
    --------
    -   This function only works in IPython enabled Notebook.

    """

    return _CURRENT_EXPERIMENT.evaluate_model(
        estimator=model, feature_name=feature, fit_kwargs=fit_kwargs
    )


# not using check_if_global_is_not_none on purpose
def predict_model(model, data: pd.DataFrame) -> pd.DataFrame:
    """
    This function generates anomaly labels on using a trained model.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')
    >>> knn_predictions = predict_model(model = knn, data = unseen_data)


    model: scikit-learn compatible object
        Trained Model Object.


    data : pandas.DataFrame
        Shape (n_samples, n_features) where n_samples is the number of samples and
        n_features is the number of features.


    Returns:
        pandas.DataFrame


    Warnings
    --------
    - The behavior of the predict_model is changed in version 2.1 without backward compatibility.
      As such, the pipelines trained using the version (<= 2.0), may not work for inference
      with version >= 2.1. You can either retrain your models with a newer version or downgrade
      the version for inference.


    """

    experiment = _CURRENT_EXPERIMENT
    if experiment is None:
        experiment = _EXPERIMENT_CLASS()

    return experiment.predict_model(estimator=model, data=data)


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def deploy_model(
    model,
    model_name: str,
    authentication: dict,
    platform: str = "aws",
):
    """
    This function deploys the transformation pipeline and trained model on cloud.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')
    >>> # sets appropriate credentials for the platform as environment variables
    >>> import os
    >>> os.environ["AWS_ACCESS_KEY_ID"] = str("foo")
    >>> os.environ["AWS_SECRET_ACCESS_KEY"] = str("bar")
    >>> deploy_model(model = knn, model_name = 'knn-for-deployment', platform = 'aws', authentication = {'bucket' : 'S3-bucket-name'})


    Amazon Web Service (AWS) users:
        To deploy a model on AWS S3 ('aws'), the credentials have to be passed. The easiest way is to use environment
        variables in your local environment. Following information from the IAM portal of amazon console account
        are required:

        - AWS Access Key ID
        - AWS Secret Key Access


        More info: https://boto3.amazonaws.com/v1/documentation/api/latest/guide/credentials.html#environment-variables


    Google Cloud Platform (GCP) users:
        To deploy a model on Google Cloud Platform ('gcp'), project must be created
        using command line or GCP console. Once project is created, you must create
        a service account and download the service account key as a JSON file to set
        environment variables in your local environment.

        More info: https://cloud.google.com/docs/authentication/production


    Microsoft Azure (Azure) users:
        To deploy a model on Microsoft Azure ('azure'), environment variables for connection
        string must be set in your local environment. Go to settings of storage account on
        Azure portal to access the connection string required.

        - AZURE_STORAGE_CONNECTION_STRING (required as environment variable)

        More info: https://docs.microsoft.com/en-us/azure/storage/blobs/storage-quickstart-blobs-python?toc=%2Fpython%2Fazure%2FTOC.json


    model: scikit-learn compatible object
        Trained model object


    model_name: str
        Name of model.


    authentication: dict
        Dictionary of applicable authentication tokens.

        When platform = 'aws':
        {'bucket' : 'S3-bucket-name', 'path': (optional) folder name under the bucket}

        When platform = 'gcp':
        {'project': 'gcp-project-name', 'bucket' : 'gcp-bucket-name'}

        When platform = 'azure':
        {'container': 'azure-container-name'}


    platform: str, default = 'aws'
        Name of the platform. Currently supported platforms: 'aws', 'gcp' and 'azure'.


    Returns:
        None

    """

    return _CURRENT_EXPERIMENT.deploy_model(
        model=model,
        model_name=model_name,
        authentication=authentication,
        platform=platform,
    )


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def save_model(
    model, model_name: str, model_only: bool = False, verbose: bool = True, **kwargs
):
    """
    This function saves the transformation pipeline and trained model object
    into the current working directory as a pickle file for later use.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> knn = create_model('knn')
    >>> save_model(knn, 'saved_knn_model')


    model: scikit-learn compatible object
        Trained model object


    model_name: str
        Name of the model.


    model_only: bool, default = False
        When set to True, only trained model object is saved instead of the
        entire pipeline.


    verbose: bool, default = True
        Success message is not printed when verbose is set to False.


    **kwargs:
        Additional keyword arguments to pass to joblib.dump().


    Returns:
        Tuple of the model object and the filename.

    """

    return _CURRENT_EXPERIMENT.save_model(
        model=model,
        model_name=model_name,
        model_only=model_only,
        verbose=verbose,
        **kwargs,
    )


# not using check_if_global_is_not_none on purpose
def load_model(
    model_name: str,
    platform: Optional[str] = None,
    authentication: Optional[Dict[str, str]] = None,
    verbose: bool = True,
):
    """
    This function loads a previously saved pipeline.


    Example
    -------
    >>> from pycaret.anomaly import load_model
    >>> saved_knn = load_model('saved_knn_model')


    model_name: str
        Name of the model.


    platform: str, default = None
        Name of the cloud platform. Currently supported platforms:
        'aws', 'gcp' and 'azure'.


    authentication: dict, default = None
        dictionary of applicable authentication tokens.

        when platform = 'aws':
        {'bucket' : 'Name of Bucket on S3', 'path': (optional) folder name under the bucket}

        when platform = 'gcp':
        {'project': 'gcp-project-name', 'bucket' : 'gcp-bucket-name'}

        when platform = 'azure':
        {'container': 'azure-container-name'}


    verbose: bool, default = True
        Success message is not printed when verbose is set to False.


    Returns:
        Trained Model

    """

    experiment = _CURRENT_EXPERIMENT
    if experiment is None:
        experiment = _EXPERIMENT_CLASS()

    return experiment.load_model(
        model_name=model_name,
        platform=platform,
        authentication=authentication,
        verbose=verbose,
    )


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def pull(pop: bool = False) -> pd.DataFrame:
    """
    Returns the latest displayed table.

    Parameters
    ----------
    pop : bool, default = False
        If true, will pop (remove) the returned dataframe from the
        display container.

    Returns
    -------
    pandas.DataFrame

    """
    return _CURRENT_EXPERIMENT.pull(pop=pop)


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def models(
    internal: bool = False,
    raise_errors: bool = True,
) -> pd.DataFrame:
    """
    Returns table of models available in the model library.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly)
    >>> all_models = models()


    internal: bool, default = False
        If True, will return extra columns and rows used internally.


    raise_errors: bool, default = True
        If False, will suppress all exceptions, ignoring models
        that couldn't be created.


    Returns:
        pandas.DataFrame

    """
    return _CURRENT_EXPERIMENT.models(internal=internal, raise_errors=raise_errors)


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def get_logs(experiment_name: Optional[str] = None, save: bool = False) -> pd.DataFrame:
    """
    Returns a table of experiment logs. Only works when ``log_experiment``
    is True when initializing the ``setup`` function.


    Example
    -------
    >>> from pycaret.datasets import get_data
    >>> anomaly = get_data('anomaly')
    >>> from pycaret.anomaly import *
    >>> exp_name = setup(data = anomaly,  log_experiment = True)
    >>> knn = create_model('knn')
    >>> exp_logs = get_logs()


    experiment_name: str, default = None
        When None current active run is used.


    save: bool, default = False
        When set to True, csv file is saved in current working directory.


    Returns:
        pandas.DataFrame

    """

    return _CURRENT_EXPERIMENT.get_logs(experiment_name=experiment_name, save=save)


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def get_config(variable: Optional[str] = None):
    """
    This function is used to access global environment variables.

    Example
    -------
    >>> X_train = get_config('X_train')

    This will return training features.


    variable : str, default = None
        Name of the variable to return the value of. If None,
        will return a list of possible names.


    Returns
    -------
    variable

    """

    return _CURRENT_EXPERIMENT.get_config(variable=variable)


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def set_config(variable: str, value):
    """
    This function is used to reset global environment variables.

    Example
    -------
    >>> set_config('seed', 123)

    This will set the global seed to '123'.

    """
    return _CURRENT_EXPERIMENT.set_config(variable=variable, value=value)


@check_if_global_is_not_none(globals(), _CURRENT_EXPERIMENT_DECORATOR_DICT)
def save_experiment(
    path_or_file: Union[str, os.PathLike, BinaryIO], **cloudpickle_kwargs
) -> None:
    """
    Saves the experiment to a pickle file.

    The experiment is saved using cloudpickle to deal with lambda
    functions. The data or test data is NOT saved with the experiment
    and will need to be specified again when loading using
    ``load_experiment``.


    path_or_file: str or BinaryIO (file pointer)
        The path/file pointer to save the experiment to.


    **cloudpickle_kwargs:
        Kwargs to pass to the ``cloudpickle.dump`` call.


    Returns:
        None

    """

    return _CURRENT_EXPERIMENT.save_experiment(
        path_or_file=path_or_file, **cloudpickle_kwargs
    )


def load_experiment(
    path_or_file: Union[str, os.PathLike, BinaryIO],
    data: Optional[DATAFRAME_LIKE] = None,
    data_func: Optional[Callable[[], DATAFRAME_LIKE]] = None,
    preprocess_data: bool = True,
    **cloudpickle_kwargs,
) -> AnomalyExperiment:
    """
    Load an experiment saved with ``save_experiment`` from path
    or file.

    The data (and test data) is NOT saved with the experiment
    and will need to be specified again.


    path_or_file: str or BinaryIO (file pointer)
        The path/file pointer to load the experiment from.
        The pickle file must be created through ``save_experiment``.


    data: dataframe-like
        Data set with shape (n_samples, n_features), where n_samples is the
        number of samples and n_features is the number of features. If data
        is not a pandas dataframe, it's converted to one using default column
        names.


    data_func: Callable[[], DATAFRAME_LIKE] = None
        The function that generate ``data`` (the dataframe-like input). This
        is useful when the dataset is large, and you need parallel operations
        such as ``compare_models``. It can avoid broadcasting large dataset
        from driver to workers. Notice one and only one of ``data`` and
        ``data_func`` must be set.


    preprocess_data: bool, default = True
        If True, the data will be preprocessed again (through running ``setup``
        internally). If False, the data will not be preprocessed. This means
        you can save the value of the ``data`` attribute of an experiment
        separately, and then load it separately and pass it here with
        ``preprocess_data`` set to False. This is an advanced feature.
        We recommend leaving it set to True and passing the same data
        as passed to the initial ``setup`` call.


    **cloudpickle_kwargs:
        Kwargs to pass to the ``cloudpickle.load`` call.


    Returns:
        loaded experiment

    """
    exp = _EXPERIMENT_CLASS.load_experiment(
        path_or_file=path_or_file,
        data=data,
        data_func=data_func,
        preprocess_data=preprocess_data,
        **cloudpickle_kwargs,
    )
    set_current_experiment(exp)
    return exp


def set_current_experiment(experiment: AnomalyExperiment):
    """
    Set the current experiment to be used with the functional API.

    experiment: AnomalyExperiment
        Experiment object to use.

    Returns:
        None
    """
    global _CURRENT_EXPERIMENT

    if not isinstance(experiment, AnomalyExperiment):
        raise TypeError(
            f"experiment must be a PyCaret AnomalyExperiment object, got {type(experiment)}."
        )
    _CURRENT_EXPERIMENT = experiment


def get_current_experiment() -> AnomalyExperiment:
    """
    Obtain the current experiment object.

    Returns:
        Current AnomalyExperiment
    """
    return _CURRENT_EXPERIMENT
