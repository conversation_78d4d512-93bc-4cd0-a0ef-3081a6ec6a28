"""
2D YOLO逐层检测生成3D检测框系统
基于现有的2D YOLO检测代码，对医学图像的每个层面进行检测，然后生成3D检测框
"""

import os
import glob
import numpy as np
import pandas as pd
import nibabel as nib
import cv2
import matplotlib.pyplot as plt
from ultralytics import YOLO
from pathlib import Path
from tqdm import tqdm
import json
from scipy import ndimage
from skimage import measure
import warnings
warnings.filterwarnings('ignore')

class YOLO2D_to_3D_Detector:
    def __init__(self, model_path, output_dir="yolo_3d_results", conf_threshold=0.5):
        """
        初始化2D YOLO到3D检测转换器
        
        参数:
            model_path (str): 训练好的YOLO模型路径
            output_dir (str): 输出目录
            conf_threshold (float): 置信度阈值
        """
        self.model = YOLO(model_path)
        self.output_dir = output_dir
        self.conf_threshold = conf_threshold
        self.class_names = {0: 'hcc'}  # 根据实际类别调整
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "slice_predictions"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "3d_masks"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "visualizations"), exist_ok=True)
        
        print(f"YOLO 2D到3D检测器初始化完成")
        print(f"模型: {model_path}")
        print(f"输出目录: {output_dir}")
        print(f"置信度阈值: {conf_threshold}")
    
    def extract_slices_from_nii(self, nii_path, slice_output_dir=None, normalize=True):
        """
        从NII文件提取所有切片
        
        参数:
            nii_path (str): NII文件路径
            slice_output_dir (str): 切片保存目录，None表示不保存
            normalize (bool): 是否归一化像素值
            
        返回:
            tuple: (切片数组列表, NII图像对象, 切片信息字典)
        """
        print(f"正在处理NII文件: {os.path.basename(nii_path)}")
        
        # 加载NII文件
        nii_img = nib.load(nii_path)
        img_data = nii_img.get_fdata()
        
        print(f"图像形状: {img_data.shape}")
        print(f"像素范围: [{img_data.min():.2f}, {img_data.max():.2f}]")
        
        # 提取切片信息
        slice_info = {
            'original_shape': img_data.shape,
            'pixel_spacing': nii_img.header.get_zooms()[:2],  # x, y方向像素间距
            'slice_thickness': nii_img.header.get_zooms()[2],  # z方向层厚
            'total_slices': img_data.shape[2]
        }
        
        # 提取所有Z轴切片
        slices = []
        base_name = Path(nii_path).stem.split('.')[0]
        
        if slice_output_dir:
            os.makedirs(slice_output_dir, exist_ok=True)
        
        for z in range(img_data.shape[2]):
            # 提取切片 (假设轴向切片，即沿Z轴)
            slice_img = img_data[:, :, z]
            
            # 归一化到0-255范围
            if normalize:
                slice_min, slice_max = slice_img.min(), slice_img.max()
                if slice_max > slice_min:
                    slice_img = ((slice_img - slice_min) / (slice_max - slice_min) * 255).astype(np.uint8)
                else:
                    slice_img = np.zeros_like(slice_img, dtype=np.uint8)
            
            # 转换为3通道图像（YOLO需要RGB格式）
            slice_rgb = cv2.cvtColor(slice_img, cv2.COLOR_GRAY2RGB)
            slices.append(slice_rgb)
            
            # 可选：保存切片图像
            if slice_output_dir:
                slice_path = os.path.join(slice_output_dir, f"{base_name}_slice_{z:03d}.png")
                cv2.imwrite(slice_path, slice_rgb)
        
        print(f"成功提取 {len(slices)} 个切片")
        return slices, nii_img, slice_info
    
    def detect_on_slices(self, slices, slice_info, base_name):
        """
        对所有切片进行2D YOLO检测
        
        参数:
            slices (list): 切片图像列表
            slice_info (dict): 切片信息
            base_name (str): 基础文件名
            
        返回:
            list: 每个切片的检测结果
        """
        print(f"开始对 {len(slices)} 个切片进行2D检测...")
        
        all_detections = []
        detection_summary = []
        
        for z, slice_img in enumerate(tqdm(slices, desc="检测切片")):
            # 使用YOLO进行检测
            results = self.model(slice_img, conf=self.conf_threshold)
            
            slice_detections = []
            for r in results:
                if hasattr(r.boxes, 'xyxy'):
                    boxes = r.boxes.xyxy.cpu().numpy()
                    scores = r.boxes.conf.cpu().numpy()
                    labels = r.boxes.cls.cpu().numpy()
                    
                    for box, score, label_idx in zip(boxes, scores, labels):
                        x1, y1, x2, y2 = box
                        width = x2 - x1
                        height = y2 - y1
                        
                        detection = {
                            'slice_idx': z,
                            'box_x1': float(x1),
                            'box_y1': float(y1),
                            'box_x2': float(x2),
                            'box_y2': float(y2),
                            'width': float(width),
                            'height': float(height),
                            'area': float(width * height),
                            'center_x': float((x1 + x2) / 2),
                            'center_y': float((y1 + y2) / 2),
                            'label': self.class_names.get(int(label_idx), 'unknown'),
                            'score': float(score),
                            'label_idx': int(label_idx)
                        }
                        slice_detections.append(detection)
            
            all_detections.append(slice_detections)
            
            # 记录检测摘要
            detection_summary.append({
                'slice_idx': z,
                'num_detections': len(slice_detections),
                'max_score': max([d['score'] for d in slice_detections]) if slice_detections else 0,
                'avg_score': np.mean([d['score'] for d in slice_detections]) if slice_detections else 0
            })
        
        # 保存检测结果到Excel
        all_det_flat = []
        for det_list in all_detections:
            all_det_flat.extend(det_list)
        
        if all_det_flat:
            df = pd.DataFrame(all_det_flat)
            excel_path = os.path.join(self.output_dir, f"{base_name}_slice_detections.xlsx")
            df.to_excel(excel_path, index=False)
            print(f"切片检测结果已保存到: {excel_path}")
            
            # 保存检测摘要
            summary_df = pd.DataFrame(detection_summary)
            summary_path = os.path.join(self.output_dir, f"{base_name}_detection_summary.xlsx")
            summary_df.to_excel(summary_path, index=False)
            print(f"检测摘要已保存到: {summary_path}")
        
        print(f"检测完成，共在 {sum([len(d) for d in all_detections])} 个位置检测到目标")
        return all_detections
    
    def cluster_detections_to_3d(self, all_detections, slice_info, clustering_method='connected_components'):
        """
        将2D检测结果聚类形成3D检测框
        
        参数:
            all_detections (list): 所有切片的检测结果
            slice_info (dict): 切片信息
            clustering_method (str): 聚类方法 ('connected_components', 'distance_based', 'overlap_based')
            
        返回:
            list: 3D检测框列表
        """
        print(f"开始将2D检测结果聚类为3D检测框，使用方法: {clustering_method}")
        
        # 提取所有有效检测
        all_detections_flat = []
        for det_list in all_detections:
            all_detections_flat.extend(det_list)
        
        if not all_detections_flat:
            print("没有检测到任何目标，无法生成3D检测框")
            return []
        
        print(f"总共有 {len(all_detections_flat)} 个2D检测结果")
        
        if clustering_method == 'connected_components':
            return self._cluster_by_connected_components(all_detections_flat, slice_info)
        elif clustering_method == 'distance_based':
            return self._cluster_by_distance(all_detections_flat, slice_info)
        elif clustering_method == 'overlap_based':
            return self._cluster_by_overlap(all_detections_flat, slice_info)
        else:
            raise ValueError(f"未知的聚类方法: {clustering_method}")
    
    def _cluster_by_connected_components(self, detections, slice_info):
        """使用连通组件聚类"""
        # 创建二进制mask来找连通组件
        shape = slice_info['original_shape']
        binary_mask = np.zeros(shape, dtype=np.uint8)
        
        # 将所有检测框填充到mask中
        for det in detections:
            z = det['slice_idx']
            x1, y1, x2, y2 = int(det['box_x1']), int(det['box_y1']), int(det['box_x2']), int(det['box_y2'])
            
            # 确保坐标在有效范围内
            x1 = max(0, min(x1, shape[0]-1))
            y1 = max(0, min(y1, shape[1]-1))
            x2 = max(0, min(x2, shape[0]))
            y2 = max(0, min(y2, shape[1]))
            z = max(0, min(z, shape[2]-1))
            
            binary_mask[x1:x2, y1:y2, z] = 1
        
        # 找连通组件
        labeled_mask, num_components = measure.label(binary_mask, return_num=True, connectivity=3)
        
        print(f"找到 {num_components} 个3D连通组件")
        
        # 为每个连通组件创建3D边界框
        boxes_3d = []
        for i in range(1, num_components + 1):
            component_mask = (labeled_mask == i)
            
            # 找到组件的边界
            coords = np.where(component_mask)
            if len(coords[0]) == 0:
                continue
                
            min_x, max_x = coords[0].min(), coords[0].max()
            min_y, max_y = coords[1].min(), coords[1].max()
            min_z, max_z = coords[2].min(), coords[2].max()
            
            # 计算该组件内所有检测的平均置信度
            component_scores = []
            component_detections = []
            for det in detections:
                z = det['slice_idx']
                if min_z <= z <= max_z:
                    x_center = det['center_x']
                    y_center = det['center_y']
                    if (min_x <= x_center <= max_x and min_y <= y_center <= max_y):
                        component_scores.append(det['score'])
                        component_detections.append(det)
            
            avg_score = np.mean(component_scores) if component_scores else 0.5
            
            box_3d = {
                'id': i,
                'x_min': min_x, 'x_max': max_x,
                'y_min': min_y, 'y_max': max_y,
                'z_min': min_z, 'z_max': max_z,
                'width': max_x - min_x,
                'height': max_y - min_y,
                'depth': max_z - min_z + 1,
                'volume': (max_x - min_x) * (max_y - min_y) * (max_z - min_z + 1),
                'center_x': (min_x + max_x) / 2,
                'center_y': (min_y + max_y) / 2,
                'center_z': (min_z + max_z) / 2,
                'confidence': avg_score,
                'num_slices': max_z - min_z + 1,
                'num_detections': len(component_detections),
                'slice_range': [min_z, max_z],
                'component_detections': component_detections
            }
            boxes_3d.append(box_3d)
        
        return boxes_3d
    
    def _cluster_by_distance(self, detections, slice_info, max_distance=50):
        """基于距离的聚类"""
        # 简化的距离聚类实现
        clusters = []
        used = set()
        
        for i, det1 in enumerate(detections):
            if i in used:
                continue
                
            cluster = [det1]
            used.add(i)
            
            for j, det2 in enumerate(detections):
                if j in used or i == j:
                    continue
                
                # 计算3D距离
                dx = det1['center_x'] - det2['center_x']
                dy = det1['center_y'] - det2['center_y']
                dz = (det1['slice_idx'] - det2['slice_idx']) * slice_info['slice_thickness']
                
                distance = np.sqrt(dx*dx + dy*dy + dz*dz)
                
                if distance <= max_distance:
                    cluster.append(det2)
                    used.add(j)
            
            clusters.append(cluster)
        
        # 转换为3D边界框
        boxes_3d = []
        for i, cluster in enumerate(clusters):
            if len(cluster) < 2:  # 至少需要2个检测才形成3D框
                continue
                
            x_coords = [d['center_x'] for d in cluster]
            y_coords = [d['center_y'] for d in cluster]
            z_coords = [d['slice_idx'] for d in cluster]
            scores = [d['score'] for d in cluster]
            
            box_3d = {
                'id': i + 1,
                'x_min': min(x_coords) - 10, 'x_max': max(x_coords) + 10,
                'y_min': min(y_coords) - 10, 'y_max': max(y_coords) + 10,
                'z_min': min(z_coords), 'z_max': max(z_coords),
                'center_x': np.mean(x_coords),
                'center_y': np.mean(y_coords),
                'center_z': np.mean(z_coords),
                'confidence': np.mean(scores),
                'num_slices': max(z_coords) - min(z_coords) + 1,
                'num_detections': len(cluster),
                'component_detections': cluster
            }
            boxes_3d.append(box_3d)
        
        return boxes_3d
    
    def _cluster_by_overlap(self, detections, slice_info, min_overlap=0.3):
        """基于重叠的聚类"""
        # 按切片分组
        slices_dict = {}
        for det in detections:
            z = det['slice_idx']
            if z not in slices_dict:
                slices_dict[z] = []
            slices_dict[z].append(det)
        
        # 连接相邻切片中重叠的检测
        clusters = []
        used_global = set()
        
        for z in sorted(slices_dict.keys()):
            slice_dets = slices_dict[z]
            
            for det in slice_dets:
                det_id = id(det)
                if det_id in used_global:
                    continue
                
                # 开始一个新的聚类
                cluster = [det]
                used_global.add(det_id)
                
                # 向前向后查找重叠的检测
                self._extend_cluster_by_overlap(det, slices_dict, cluster, used_global, min_overlap)
                
                clusters.append(cluster)
        
        # 转换为3D边界框
        boxes_3d = []
        for i, cluster in enumerate(clusters):
            if len(cluster) < 1:
                continue
            
            # 计算边界框
            x1_coords = [d['box_x1'] for d in cluster]
            y1_coords = [d['box_y1'] for d in cluster]
            x2_coords = [d['box_x2'] for d in cluster]
            y2_coords = [d['box_y2'] for d in cluster]
            z_coords = [d['slice_idx'] for d in cluster]
            scores = [d['score'] for d in cluster]
            
            box_3d = {
                'id': i + 1,
                'x_min': min(x1_coords), 'x_max': max(x2_coords),
                'y_min': min(y1_coords), 'y_max': max(y2_coords),
                'z_min': min(z_coords), 'z_max': max(z_coords),
                'width': max(x2_coords) - min(x1_coords),
                'height': max(y2_coords) - min(y1_coords),
                'depth': max(z_coords) - min(z_coords) + 1,
                'center_x': (min(x1_coords) + max(x2_coords)) / 2,
                'center_y': (min(y1_coords) + max(y2_coords)) / 2,
                'center_z': (min(z_coords) + max(z_coords)) / 2,
                'confidence': np.mean(scores),
                'num_slices': max(z_coords) - min(z_coords) + 1,
                'num_detections': len(cluster),
                'component_detections': cluster
            }
            boxes_3d.append(box_3d)
        
        return boxes_3d
    
    def _extend_cluster_by_overlap(self, seed_det, slices_dict, cluster, used_global, min_overlap):
        """扩展聚类基于重叠"""
        current_z = seed_det['slice_idx']
        
        # 检查相邻切片
        for next_z in [current_z - 1, current_z + 1]:
            if next_z not in slices_dict:
                continue
                
            for candidate in slices_dict[next_z]:
                candidate_id = id(candidate)
                if candidate_id in used_global:
                    continue
                
                # 计算重叠
                overlap = self._calculate_box_overlap(seed_det, candidate)
                if overlap >= min_overlap:
                    cluster.append(candidate)
                    used_global.add(candidate_id)
                    # 递归扩展
                    self._extend_cluster_by_overlap(candidate, slices_dict, cluster, used_global, min_overlap)
    
    def _calculate_box_overlap(self, box1, box2):
        """计算两个边界框的重叠率"""
        x1 = max(box1['box_x1'], box2['box_x1'])
        y1 = max(box1['box_y1'], box2['box_y1'])
        x2 = min(box1['box_x2'], box2['box_x2'])
        y2 = min(box1['box_y2'], box2['box_y2'])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        area1 = box1['area']
        area2 = box2['area']
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def create_3d_masks(self, boxes_3d, slice_info, base_name):
        """
        为3D检测框创建NIFTI mask文件
        
        参数:
            boxes_3d (list): 3D检测框列表
            slice_info (dict): 切片信息
            base_name (str): 基础文件名
            
        返回:
            list: 创建的mask文件路径列表
        """
        print(f"为 {len(boxes_3d)} 个3D检测框创建mask...")
        
        mask_paths = []
        shape = slice_info['original_shape']
        
        for i, box in enumerate(boxes_3d):
            # 创建mask
            mask = np.zeros(shape, dtype=np.uint8)
            
            # 填充3D框区域
            x1, x2 = int(max(0, box['x_min'])), int(min(shape[0], box['x_max']))
            y1, y2 = int(max(0, box['y_min'])), int(min(shape[1], box['y_max']))
            z1, z2 = int(max(0, box['z_min'])), int(min(shape[2], box['z_max'] + 1))
            
            mask[x1:x2, y1:y2, z1:z2] = 1
            
            # 创建NIFTI图像
            mask_img = nib.Nifti1Image(mask, np.eye(4))
            
            # 保存
            mask_filename = f"{base_name}_3d_detection_{i+1}_conf_{box['confidence']:.3f}.nii.gz"
            mask_path = os.path.join(self.output_dir, "3d_masks", mask_filename)
            nib.save(mask_img, mask_path)
            mask_paths.append(mask_path)
            
            print(f"已保存3D mask {i+1}: {mask_filename}")
        
        return mask_paths
    
    def visualize_results(self, boxes_3d, slice_info, base_name):
        """
        可视化3D检测结果
        
        参数:
            boxes_3d (list): 3D检测框列表
            slice_info (dict): 切片信息
            base_name (str): 基础文件名
        """
        if not boxes_3d:
            print("没有3D检测框可供可视化")
            return
        
        print(f"正在生成可视化图表...")
        
        # 1. 检测框统计图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 置信度分布
        confidences = [box['confidence'] for box in boxes_3d]
        axes[0, 0].hist(confidences, bins=10, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('3D检测框置信度分布')
        axes[0, 0].set_xlabel('置信度')
        axes[0, 0].set_ylabel('数量')
        
        # 体积分布
        volumes = [box.get('volume', 0) for box in boxes_3d]
        axes[0, 1].hist(volumes, bins=10, alpha=0.7, color='orange', edgecolor='black')
        axes[0, 1].set_title('3D检测框体积分布')
        axes[0, 1].set_xlabel('体积')
        axes[0, 1].set_ylabel('数量')
        
        # Z轴分布
        z_centers = [box['center_z'] for box in boxes_3d]
        axes[1, 0].scatter(range(len(z_centers)), z_centers, alpha=0.7, color='green')
        axes[1, 0].set_title('3D检测框Z轴中心分布')
        axes[1, 0].set_xlabel('检测框ID')
        axes[1, 0].set_ylabel('Z轴中心位置')
        
        # 每个检测框的切片数
        slice_counts = [box['num_slices'] for box in boxes_3d]
        axes[1, 1].bar(range(len(slice_counts)), slice_counts, alpha=0.7, color='red')
        axes[1, 1].set_title('每个3D检测框包含的切片数')
        axes[1, 1].set_xlabel('检测框ID')
        axes[1, 1].set_ylabel('切片数')
        
        plt.tight_layout()
        plot_path = os.path.join(self.output_dir, "visualizations", f"{base_name}_3d_detection_stats.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"统计图表已保存: {plot_path}")
        
        # 2. 创建检测结果摘要表
        summary_data = []
        for i, box in enumerate(boxes_3d):
            summary_data.append({
                'detection_id': i + 1,
                'confidence': box['confidence'],
                'center_x': box['center_x'],
                'center_y': box['center_y'],
                'center_z': box['center_z'],
                'width': box['width'],
                'height': box['height'],
                'depth': box['depth'],
                'volume': box.get('volume', 0),
                'num_slices': box['num_slices'],
                'num_detections': box['num_detections'],
                'z_range': f"{box['z_min']}-{box['z_max']}"
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(self.output_dir, f"{base_name}_3d_detection_summary.xlsx")
        summary_df.to_excel(summary_path, index=False)
        print(f"3D检测摘要已保存: {summary_path}")
    
    def process_nii_file(self, nii_path, clustering_method='connected_components', save_slices=False):
        """
        处理单个NII文件的完整流程
        
        参数:
            nii_path (str): NII文件路径
            clustering_method (str): 聚类方法
            save_slices (bool): 是否保存切片图像
            
        返回:
            dict: 处理结果
        """
        base_name = Path(nii_path).stem.split('.')[0]
        print(f"\n{'='*60}")
        print(f"开始处理: {base_name}")
        print(f"{'='*60}")
        
        try:
            # 1. 提取切片
            slice_output_dir = os.path.join(self.output_dir, "slice_predictions", base_name) if save_slices else None
            slices, nii_img, slice_info = self.extract_slices_from_nii(nii_path, slice_output_dir)
            
            # 2. 对切片进行2D检测
            all_detections = self.detect_on_slices(slices, slice_info, base_name)
            
            # 3. 聚类生成3D检测框
            boxes_3d = self.cluster_detections_to_3d(all_detections, slice_info, clustering_method)
            
            # 4. 创建3D masks
            mask_paths = self.create_3d_masks(boxes_3d, slice_info, base_name) if boxes_3d else []
            
            # 5. 生成可视化
            self.visualize_results(boxes_3d, slice_info, base_name)
            
            result = {
                'base_name': base_name,
                'success': True,
                'total_slices': len(slices),
                'total_2d_detections': sum([len(d) for d in all_detections]),
                'total_3d_detections': len(boxes_3d),
                'boxes_3d': boxes_3d,
                'mask_paths': mask_paths,
                'slice_info': slice_info
            }
            
            print(f"\n{base_name} 处理完成:")
            print(f"  - 总切片数: {result['total_slices']}")
            print(f"  - 2D检测数: {result['total_2d_detections']}")
            print(f"  - 3D检测框数: {result['total_3d_detections']}")
            print(f"  - 生成mask数: {len(mask_paths)}")
            
            return result
            
        except Exception as e:
            print(f"处理 {base_name} 时出错: {str(e)}")
            return {
                'base_name': base_name,
                'success': False,
                'error': str(e)
            }


def batch_process_nii_files(
    model_path, 
    nii_folder, 
    output_dir="yolo_3d_batch_results", 
    conf_threshold=0.5,
    clustering_method='connected_components',
    save_slices=False
):
    """
    批量处理NII文件
    
    参数:
        model_path (str): YOLO模型路径
        nii_folder (str): NII文件夹路径
        output_dir (str): 输出目录
        conf_threshold (float): 置信度阈值
        clustering_method (str): 聚类方法
        save_slices (bool): 是否保存切片
        
    返回:
        dict: 批处理结果
    """
    print(f"开始批量处理NII文件...")
    print(f"输入目录: {nii_folder}")
    print(f"输出目录: {output_dir}")
    print(f"聚类方法: {clustering_method}")
    
    # 创建检测器
    detector = YOLO2D_to_3D_Detector(model_path, output_dir, conf_threshold)
    
    # 获取所有NII文件
    nii_files = glob.glob(os.path.join(nii_folder, "*.nii.gz"))
    nii_files.extend(glob.glob(os.path.join(nii_folder, "*.nii")))
    
    if not nii_files:
        print(f"在 {nii_folder} 中未找到NII文件")
        return {'success': False, 'message': 'No NII files found'}
    
    print(f"找到 {len(nii_files)} 个NII文件")
    
    # 批量处理
    results = []
    successful = 0
    failed = 0
    
    for nii_file in nii_files:
        result = detector.process_nii_file(nii_file, clustering_method, save_slices)
        results.append(result)
        
        if result['success']:
            successful += 1
        else:
            failed += 1
    
    # 汇总结果
    total_slices = sum([r.get('total_slices', 0) for r in results if r['success']])
    total_2d_detections = sum([r.get('total_2d_detections', 0) for r in results if r['success']])
    total_3d_detections = sum([r.get('total_3d_detections', 0) for r in results if r['success']])
    
    batch_result = {
        'success': True,
        'total_files': len(nii_files),
        'successful': successful,
        'failed': failed,
        'total_slices': total_slices,
        'total_2d_detections': total_2d_detections,
        'total_3d_detections': total_3d_detections,
        'individual_results': results
    }
    
    # 保存批处理摘要
    summary_path = os.path.join(output_dir, "batch_processing_summary.json")
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(batch_result, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n{'='*60}")
    print(f"批处理完成!")
    print(f"{'='*60}")
    print(f"总文件数: {batch_result['total_files']}")
    print(f"成功处理: {batch_result['successful']}")
    print(f"处理失败: {batch_result['failed']}")
    print(f"总切片数: {batch_result['total_slices']}")
    print(f"总2D检测: {batch_result['total_2d_detections']}")
    print(f"总3D检测框: {batch_result['total_3d_detections']}")
    print(f"摘要已保存: {summary_path}")
    
    return batch_result


# 使用示例
if __name__ == "__main__":
    # 配置参数
    MODEL_PATH = "/root/autodl-tmp/yolov12-main/result/weights/best.pt"  # 您训练的YOLO模型路径
    NII_FOLDER = "H:/1.HCC-dataset/850HCC/all-HCC/598HCC-suzhou/598HCC/image/ap"  # NII文件目录
    OUTPUT_DIR = "yolo_3d_detection_results"  # 输出目录
    
    # 方法1: 处理单个NII文件
    # detector = YOLO2D_to_3D_Detector(MODEL_PATH, OUTPUT_DIR, conf_threshold=0.5)
    # result = detector.process_nii_file("path/to/your/file.nii.gz", 
    #                                   clustering_method='connected_components',
    #                                   save_slices=True)
    
    # 方法2: 批量处理
    batch_result = batch_process_nii_files(
        model_path=MODEL_PATH,
        nii_folder=NII_FOLDER,
        output_dir=OUTPUT_DIR,
        conf_threshold=0.4,  # 可以调整置信度阈值
        clustering_method='connected_components',  # 可选: 'connected_components', 'distance_based', 'overlap_based'
        save_slices=False  # 设为True如果想保存所有切片图像
    )