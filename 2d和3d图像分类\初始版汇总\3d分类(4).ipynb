{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "import torch\n", "import matplotlib.pyplot as plt\n", "import monai\n", "from torch.utils.tensorboard import SummaryWriter\n", "from monai.config import print_config\n", "from monai.data import DataLoader,Dataset,decollate_batch\n", "from monai.networks import eval_mode\n", "from monai.networks.nets import densenet121,resnet50,resnet152\n", "from monai.metrics import ROCAUCMetric\n", "from monai.transforms import Activations, AsDiscrete,LoadImageD, EnsureChannelFirstD, ScaleIntensityRangeD,RandRotate90D,SpacingD,OrientationD, ResizeD,ScaleIntensityD ,Compose\n", "import random\n", "from monai.networks.nets import efficientnet"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["data_dir = r\"G:\\data\\monai_brats176\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total image count: 176\n", "Label names: ['HGG', 'LGG']\n", "Label counts: [100, 76]\n"]}], "source": ["class_names = sorted(x for x in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, x)))\n", "\n", "num_class = len(class_names)\n", "image_files = [\n", "    [os.path.join(data_dir, class_names[i], x ) for x in os.listdir(os.path.join(data_dir, class_names[i]))]\n", "    for i in range(num_class)\n", "]\n", "num_each = [len(image_files[i]) for i in range(num_class)]\n", "image_files_list = []\n", "image_class = []\n", "for i in range(num_class):\n", "    image_files_list.extend(image_files[i])\n", "    image_class.extend([i] * num_each[i])\n", "image_class = np.array(image_class, dtype=np.int64)\n", "#打算顺序\n", "random.seed(2023)\n", "templist = [i for i in zip(image_files_list, image_class)]\n", "random.shuffle(templist)\n", "image_files_list[:], image_class[:] = zip(*templist)\n", "print(f\"Total image count: {len(image_files_list)}\")\n", "print(f\"Label names: {class_names}\")\n", "print(f\"Label counts: {num_each}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["num_class = 2\n", "train_transform = Compose(\n", "    [\n", "        LoadImageD(keys=\"image\", image_only=True),\n", "        EnsureChannelFirstD(keys=\"image\"),\n", "        ScaleIntensityRangeD(\n", "            keys=\"image\",\n", "            a_min=-17.3,\n", "            a_max=1280.5,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        OrientationD(keys=\"image\", axcodes=\"RAS\"),\n", "        SpacingD(keys=\"image\", pixdim=(1, 1, 1), mode=\"bilinear\"),\n", "        ResizeD(keys=\"image\",spatial_size=(96,96,96)),\n", "        RandRotate90D(keys=\"image\", prob=0.8, spatial_axes=[0, 2]),\n", "    ]\n", ")\n", "\n", "val_transforms = Compose(\n", "    [\n", "        LoadImageD(keys=\"image\", image_only=True),\n", "        EnsureChannelFirstD(keys=\"image\"),\n", "        ScaleIntensityRangeD(\n", "            keys=\"image\",\n", "            a_min=-17.3,\n", "            a_max=1280.5,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        OrientationD(keys=\"image\", axcodes=\"RAS\"),\n", "        SpacingD(keys=\"image\", pixdim=(1, 1, 1), mode=\"bilinear\"),\n", "        ResizeD(keys=\"image\",spatial_size=(96,96,96)),\n", "\n", "    ]\n", ")\n", "post_pred = Compose([Activations(softmax=True)])\n", "post_label = Compose([AsDiscrete(to_onehot=num_class)])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["val_frac = 0.2\n", "slicer = int(len(image_files_list)*val_frac)\n", "train_files = [{\"image\": img, \"label\": label} for img, label in zip(image_files_list[:slicer], image_class[:slicer])]\n", "val_files = [{\"image\": img, \"label\": label} for img, label in zip(image_files_list[-slicer:], image_class[-slicer:])]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["#构建Dataset\n", "tra_ds = Dataset(data=train_files, transform=train_transform)\n", "tra_loader = DataLoader(tra_ds, batch_size=2, num_workers=4, pin_memory=torch.cuda.is_available())\n", "val_ds = monai.data.Dataset(data=val_files, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=2, num_workers=4, pin_memory=torch.cuda.is_available())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_data = monai.utils.misc.first(tra_loader)\n", "print(check_data[\"image\"].shape, check_data[\"label\"])\n", "image = check_data[\"image\"][0][0]\n", "plt.figure(\"check\", (12, 6))\n", "plt.imshow(image[40,:, :], cmap=\"gray\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "#model = densenet121(spatial_dims=3, in_channels=1, out_channels=num_class).to(device)\n", "model = resnet50(spatial_dims=3,n_input_channels=1,num_classes=num_class).to(device)\n", "loss_function = torch.nn.CrossEntropyLoss()\n", "optimizer = torch.optim.Adam(model.parameters(), 1e-5)\n", "auc_metric = ROCAUCMetric()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#开始训练\n", "max_epochs = 5\n", "val_interval = 2\n", "best_metric = -1\n", "best_metric_epoch = -1\n", "writer = SummaryWriter()\n", "for epoch in range(max_epochs):\n", "    print(\"-\" * 10)\n", "    print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    for batch_data in tra_loader:\n", "        step += 1\n", "        inputs, labels = batch_data[\"image\"].to(device), batch_data[\"label\"].to(device)\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = loss_function(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "        epoch_len = len(tra_ds) // tra_loader.batch_size\n", "        print(f\"{step}/{epoch_len}, train_loss: {loss.item():.4f}\")\n", "        writer.add_scalar(\"train_loss\", loss.item(), epoch_len * epoch + step)\n", "    epoch_loss /= step\n", "    print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        with torch.no_grad():\n", "            y_pred = torch.tensor([], dtype=torch.float32, device=device)\n", "            y = torch.tensor([], dtype=torch.long, device=device)\n", "            for val_data in val_loader:\n", "                val_images, val_labels = val_data[\"image\"].to(device), val_data[\"label\"].to(device)\n", "                y_pred = torch.cat([y_pred, model(val_images)], dim=0)\n", "                y = torch.cat([y, val_labels], dim=0)\n", "\n", "            acc_value = torch.eq(y_pred.argmax(dim=1), y)\n", "            acc_metric = acc_value.sum().item() / len(acc_value)\n", "            y_onehot = [post_label(i) for i in decollate_batch(y, detach=False)]\n", "            y_pred_act = [post_pred(i) for i in decollate_batch(y_pred)]\n", "            auc_metric(y_pred_act, y_onehot)\n", "            auc_result = auc_metric.aggregate()\n", "            auc_metric.reset()\n", "            del y_pred_act, y_onehot\n", "            if acc_metric > best_metric:\n", "                best_metric = acc_metric\n", "                best_metric_epoch = epoch + 1\n", "                torch.save(model.state_dict(), \"best_metric_model_classification3d_dict.pth\")\n", "                print(\"saved new best metric model\")\n", "            print(\n", "                \"current epoch: {} current accuracy: {:.4f} current AUC: {:.4f} best accuracy: {:.4f} at epoch {}\".format(\n", "                    epoch + 1, acc_metric, auc_result, best_metric, best_metric_epoch\n", "                )\n", "            )\n", "            writer.add_scalar(\"val_accuracy\", acc_metric, epoch + 1)\n", "print(f\"train completed, best_metric: {best_metric:.4f} at epoch: {best_metric_epoch}\")\n", "writer.close()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["evaluation metric: 0.6571428571428571\n"]}], "source": ["#验证\n", "import pandas as pd\n", "dataframe_list = []\n", "\n", "model.load_state_dict(torch.load(\"best_metric_model_classification3d_dict.pth\"))\n", "model.eval()\n", "with torch.no_grad():\n", "    num_correct = 0.0\n", "    metric_count = 0\n", "    for val_data in val_loader:\n", "        val_images, val_labels = val_data[\"image\"].to(device), val_data[\"label\"].to(device)\n", "        val_outputs = model(val_images).argmax(dim=1)\n", "        val_pred_score = torch.nn.Softmax(dim=1)(model(val_images)).cpu().numpy()\n", "\n", "        value = torch.eq(val_outputs, val_labels)\n", "        metric_count += len(value)\n", "        num_correct += value.sum().item()\n", "        for i in range(len(val_data[\"label\"].numpy())):\n", "            pd_data_featrues = pd.DataFrame({\n", "                'index': [val_data[\"image\"].meta['filename_or_obj'][i]],\n", "                'label': [val_labels.cpu().numpy()[i]],\n", "                'predict':[val_outputs.cpu().numpy()[i]],\n", "                'pre_score' :[list(val_pred_score)[i]]\n", "                    })\n", "            dataframe_list.append(pd_data_featrues)\n", "    dataframe_total = pd.concat(dataframe_list,axis=0)\n", "    dataframe_total= dataframe_total.reset_index(drop=True)     \n", "    dataframe_total.to_csv('predict_result3d.csv',index=False)\n", "    metric = num_correct / metric_count\n", "    print(\"evaluation metric:\", metric)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#绘制ROC曲线\n", "from sklearn.metrics import roc_curve,auc,accuracy_score,confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "import seaborn as sns\n", "warnings.filterwarnings(\"ignore\")\n", "def fun(x,index):\n", "    #返回列表中第几个元素\n", "    return x[index]\n", "#绘制roc曲线\n", "plt.figure(figsize=(8,8))\n", "\n", "y_true = dataframe_total['label']\n", "y_pred = dataframe_total['predict']\n", "y_score = dataframe_total['pre_score'].apply(lambda x:fun(x,1))\n", "#计算roc和 auc\n", "fpr, tpr, thresholds=roc_curve(y_true,y_score,pos_label=1)\n", "ACC = accuracy_score(y_true,y_pred)\n", "AUC = auc(fpr, tpr) \n", "plt.plot(fpr,tpr,label=' ACC=%0.2f AUC = %0.2f'% (ACC ,AUC))\n", "plt.legend(loc='lower right',fontsize = 12)\n", "plt.plot([0,1],[0,1],color='black',linestyle='dashed')\n", "plt.ylabel('True Positive Rate',fontsize = 14)\n", "plt.xlabel('Flase Positive Rate',fontsize = 14)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#绘制混淆矩阵\n", "plt.figure(figsize=(5,5))\n", "\n", "y_true = dataframe_total['label']\n", "y_pred = dataframe_total['predict']\n", "\n", "sns.heatmap(confusion_matrix(y_true, y_pred),annot=True, cmap='Blues')\n", "plt.xlabel('Predicted Label')\n", "plt.ylabel('True Label')\n", "plt.title(\"confusion_matrix\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#绘制DCA曲线\n", "def calculate_net_benefit_model(thresh_group, y_pred_score, y_label):\n", "    net_benefit_model = np.array([])\n", "    for thresh in thresh_group:\n", "        y_pred_label = y_pred_score > thresh\n", "        tn, fp, fn, tp = confusion_matrix(y_label, y_pred_label).ravel()\n", "        n = len(y_label)\n", "        net_benefit = (tp / n) - (fp / n) * (thresh / (1 - thresh))\n", "        net_benefit_model = np.append(net_benefit_model, net_benefit)\n", "    return net_benefit_model\n", "\n", "def calculate_net_benefit_all(thresh_group, y_label):\n", "    net_benefit_all = np.array([])\n", "    tn, fp, fn, tp = confusion_matrix(y_label, y_label).ravel()\n", "    total = tp + tn\n", "    for thresh in thresh_group:\n", "        net_benefit = (tp / total) - (tn / total) * (thresh / (1 - thresh))\n", "        net_benefit_all = np.append(net_benefit_all, net_benefit)\n", "    return net_benefit_all\n", "\n", "def plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all,color='crimson',index=18):\n", "\n", "    ax.plot(thresh_group, net_benefit_model, color = color, label = 'Resnet3D-'+str(index))\n", "\n", "    #Figure Configuration， 美化一下细节\n", "    ax.set_xlim(0,1)\n", "    ax.set_ylim(net_benefit_model.min() - 0.15, net_benefit_model.max() + 0.15)#adjustify the y axis limitation\n", "    ax.set_xlabel(\n", "        xlabel = 'High Risk Threshold', \n", "        fontdict= {'family': 'Times New Roman', 'fontsize': 13}\n", "        )\n", "    ax.set_ylabel(\n", "        ylabel = 'Net Benefit', \n", "        fontdict= {'family': 'Times New Roman', 'fontsize': 13}\n", "        )\n", "    ax.grid('off')\n", "    ax.spines['right'].set_color((0.8, 0.8, 0.8))\n", "    ax.spines['top'].set_color((0.8, 0.8, 0.8))\n", "    ax.legend(loc = 'upper right')\n", "    return ax\n", "\n", "fig, ax = plt.subplots()\n", "fig.set_size_inches(10, 8)\n", "\n", "y_label = dataframe_total['label']\n", "y_pred_score = dataframe_total['pre_score'].apply(lambda x:fun(x,1))\n", "\n", "thresh_group = np.arange(0,1,0.01)\n", "net_benefit_model = calculate_net_benefit_model(thresh_group, y_pred_score, y_label)\n", "net_benefit_all = calculate_net_benefit_all(thresh_group, y_label)\n", "ax = plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all)\n", "ax.plot(thresh_group, net_benefit_all, color = 'black',label = 'ALL')\n", "ax.plot((0, 1), (0, 0), color = 'black', linestyle = ':', label = 'None')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Actual probability')"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#校准曲线\n", "from sklearn.calibration import calibration_curve\n", "\n", "y_true = dataframe_total['label']\n", "y_score = dataframe_total['pre_score'].apply(lambda x:fun(x,1))\n", "prob_true, prob_pred = calibration_curve(list(y_true), list(y_score), n_bins=10)\n", "plt.plot(prob_pred, prob_true, linewidth=1, marker=\"o\",label=\"Mean value\")\n", "plt.plot([0,1],[0,1],color='black',linestyle='dashed')\n", "plt.legend([\"model\"],loc=\"upper left\")\n", "plt.xlabel('Predicted Probability of hypertension')\n", "plt.ylabel('Actual probability')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}