# Please install OpenAI SDK first: `pip3 install openai`
#%%
from openai import OpenAI

client = OpenAI(api_key="sk-8ede46f7884c41519c552217a59a21fe", base_url="https://api.deepseek.com")

response = client.chat.completions.create(
    model="deepseek-chat",
    messages=[
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "Hello"},
    ],
    stream=False
)

print(response.choices[0].message.content)
