#!/usr/bin/env python
# coding: utf-8
#%% 2dCNN-Vit 多任务学习代码,成功
import torch
print(torch.__version__)
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import torchvision
import glob
from torchvision import transforms
from torch.utils import data
from PIL import Image
import torchvision.models as models
import timm
import os
import pandas as pd
import math  # Added math module import

#%%
def get_file_list(directory):
    file_list = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.jpg'):
                file_list.append(os.path.join(root, file))
    file_list.sort()  # 按照文件名排序
    return file_list

def get_label_list(file_path):   
    label_file = pd.read_excel(file_path)    
    name_values = label_file['name'].tolist()
    label_values1 = label_file['MVI'].tolist()   
    label_values2 = label_file['MTM'].tolist()
    label_values3 = label_file['VETC'].tolist()  # 添加第三个任务的标签
    sorted_label_values1 = [label for _, label in sorted(zip(name_values, label_values1))]
    sorted_label_values2 = [label for _, label in sorted(zip(name_values, label_values2))]
    sorted_label_values3 = [label for _, label in sorted(zip(name_values, label_values3))]  # 排序第三个任务的标签
    label_list1 = sorted_label_values1
    label_list2 = sorted_label_values2
    label_list3 = sorted_label_values3  # 添加第三个标签列表     
    return label_list1, label_list2, label_list3

train_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\train"
test_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\test"
train_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\train30.xlsx"
test_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\test.xlsx"

train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5])
print(len(train_image_list))
print(len(test_image_list))

# 调用函数获取标签列表
train_label_list1 = get_label_list(train_file_path)[0]
train_label_list2 = get_label_list(train_file_path)[1]
train_label_list3 = get_label_list(train_file_path)[2]  # 添加第三个训练标签
test_label_list1 = get_label_list(test_file_path)[0]
test_label_list2 = get_label_list(test_file_path)[1]
test_label_list3 = get_label_list(test_file_path)[2]  # 添加第三个测试标签
print(train_label_list1[:5])
print(train_label_list2[:5])
print(train_label_list3[:5])
print(len(train_label_list1))
print(len(test_label_list1))

# 打印类别分布情况
print("Task 1 class distribution:", np.unique(train_label_list1, return_counts=True))
print("Task 2 class distribution:", np.unique(train_label_list2, return_counts=True))
print("Task 3 class distribution:", np.unique(train_label_list3, return_counts=True))

#%% 数据预处理和增强
transforms = transforms.Compose([
    transforms.Resize((224, 224)),  # 调整大小以适应模型输入
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.RandomRotation(10),
    transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),
    transforms.ColorJitter(brightness=0.2, contrast=0.2),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

#%% 数据集类定义
class Multi_output_dataset(data.Dataset):
    def __init__(self, imgs_path, label1, label2, label3):
        self.imgs_path = imgs_path
        self.label1 = label1
        self.label2 = label2
        self.label3 = label3

    def __getitem__(self, index):
        img_path = self.imgs_path[index]
        pil_img = Image.open(img_path)
        pil_img = pil_img.convert("RGB")
        pil_img = transforms(pil_img)
        label_1 = self.label1[index]
        label_2 = self.label2[index]
        label_3 = self.label3[index]
        return pil_img, (label_1, label_2, label_3)
    
    def __len__(self):
        return len(self.imgs_path)

#%% 创建数据集和数据加载器
train_dataset = Multi_output_dataset(train_image_list, train_label_list1, train_label_list2, train_label_list3)
test_dataset = Multi_output_dataset(test_image_list, test_label_list1, test_label_list2, test_label_list3)
print(len(train_dataset), len(test_dataset))

BATCH_SIZE = 16  # 减小batch size以适应更复杂的模型

train_dl = torch.utils.data.DataLoader(train_dataset, 
                                     batch_size=BATCH_SIZE,
                                     shuffle=True,
                                     num_workers=0,
                                     pin_memory=True)

test_dl = torch.utils.data.DataLoader(test_dataset,
                                    batch_size=BATCH_SIZE,
                                    shuffle=False,
                                    num_workers=0,
                                    pin_memory=True)

#%% ResNet50-Transformer级联融合模型
class CustomModel(nn.Module):
    def __init__(self, num_classes1, num_classes2, num_classes3):
        super(CustomModel, self).__init__()
        # 加载预训练的ResNet50作为特征提取器
        self.resnet = models.resnet50(pretrained=True)
        # 冻结ResNet的前几层
        for param in list(self.resnet.parameters())[:-30]:  # 只训练最后几层
            param.requires_grad = False
        self.resnet = nn.Sequential(*list(self.resnet.children())[:-2])  # 保留最后的卷积层输出
        
        # 创建新的特征处理层
        self.feature_embed = nn.Sequential(
            nn.Conv2d(2048, 1024, kernel_size=1),  # 1x1 卷积降维
            nn.LayerNorm([1024, 7, 7]),
            nn.GELU()
        )
        
        # 自定义Transformer块
        self.transformer_blocks = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=1024,
                nhead=8,
                dim_feedforward=2048,
                dropout=0.1,
                activation='gelu',
                batch_first=True,
                norm_first=True
            ) for _ in range(4)  # 使用4个transformer块
        ])
        
        # 特征整合层（添加注意力机制）
        self.attention = nn.MultiheadAttention(embed_dim=1024, num_heads=8, dropout=0.1)
        self.norm1 = nn.LayerNorm(1024)
        self.norm2 = nn.LayerNorm(1024)
        
        # 添加位置编码
        self.pos_embed = nn.Parameter(torch.zeros(1, 49, 1024))
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        
        # 特征融合层
        self.integration = nn.Sequential(
            nn.Linear(1024, 512),
            nn.LayerNorm(512),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 为每个任务添加独立的分类层
        self.task_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(512, 256),
                nn.LayerNorm(256),
                nn.GELU(),
                nn.Dropout(0.1),
                nn.Linear(256, num_classes)
            ) for num_classes in [num_classes1, num_classes2, num_classes3]
        ])
        
        # 添加任务权重参数
        self.task1_weight = nn.Parameter(torch.zeros(1))
        self.task2_weight = nn.Parameter(torch.zeros(1))
        self.task3_weight = nn.Parameter(torch.zeros(1))

    def forward(self, x):
        # 通过ResNet提取底层特征
        resnet_features = self.resnet(x)  # [B, 2048, 7, 7]
        
        # 将ResNet特征转换为适当的格式
        processed_features = self.feature_embed(resnet_features)  # [B, 1024, 7, 7]
        
        # 重塑特征以适应Transformer处理
        B, C, H, W = processed_features.shape
        processed_features = processed_features.flatten(2).transpose(1, 2)  # [B, 49, 1024]
        
        # 添加位置编码
        processed_features = processed_features + self.pos_embed
        
        # 应用自注意力机制
        attended_features, _ = self.attention(
            processed_features, processed_features, processed_features
        )
        
        # 残差连接和层归一化
        processed_features = self.norm1(processed_features + attended_features)
        
        # 通过Transformer blocks
        for block in self.transformer_blocks:
            processed_features = block(processed_features)
        
        processed_features = self.norm2(processed_features)
        
        # 全局平均池化得到特征向量
        features = processed_features.mean(dim=1)  # [B, 1024]
        
        # 特征整合
        integrated_features = self.integration(features)  # [B, 512]
        
        # 任务特定的预测
        outputs = [layer(integrated_features) for layer in self.task_layers]
        
        return outputs

#%% 模型实例化
device = "cuda" if torch.cuda.is_available() else "cpu"
print("Using {} device".format(device))

model = CustomModel(num_classes1=2, num_classes2=2, num_classes3=2).to(device)
print(model)

#%% 训练设置
# 损失函数和优化器设置
loss_fn = nn.CrossEntropyLoss(reduction='none')
optimizer = torch.optim.AdamW(filter(lambda p: p.requires_grad, model.parameters()), 
                            lr=0.00001,
                            weight_decay=0.01)

# 学习率调度器，包含预热期
def get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps, num_cycles=0.5):
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        progress = float(current_step - num_warmup_steps) / float(max(1, num_training_steps - num_warmup_steps))
        return max(0.0, 0.5 * (1.0 + math.cos(math.pi * float(num_cycles) * 2.0 * progress)))

    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

# 计算总训练步数
epochs = 300
total_steps = epochs * len(train_dl)
warmup_steps = total_steps // 10  # 10%的步数用于预热

scheduler = get_cosine_schedule_with_warmup(optimizer, 
                                          num_warmup_steps=warmup_steps,
                                          num_training_steps=total_steps)

#%% 训练循环

def train(dataloader, model, loss_fn, optimizer, scheduler):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    train_loss, correct1, correct2, correct3 = 0, 0, 0, 0
    model.train()
    
    for X, y in dataloader:
        y1, y2, y3 = y[0], y[1], y[2]  # 获取三个任务的标签
        X, y1, y2, y3 = X.to(device), y1.to(device), y2.to(device), y3.to(device)
        
        # 计算预测和损失
        pred1, pred2, pred3 = model(X)  # 获取三个任务的预测
        
        # 计算每个样本的损失
        loss1 = loss_fn(pred1, y1)
        loss2 = loss_fn(pred2, y2)
        loss3 = loss_fn(pred3, y3)  # 计算第三个任务的损失
        
        # 使用动态任务权重
        task1_weight = torch.exp(model.task1_weight)
        task2_weight = torch.exp(model.task2_weight)
        task3_weight = torch.exp(model.task3_weight)  # 添加第三个任务的权重
        total_weight = task1_weight + task2_weight + task3_weight
        
        # 归一化权重
        task1_weight = task1_weight / total_weight
        task2_weight = task2_weight / total_weight
        task3_weight = task3_weight / total_weight
        
        # 组合损失
        loss = task1_weight * loss1.mean() + task2_weight * loss2.mean() + task3_weight * loss3.mean()
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 统计
        with torch.no_grad():
            correct1 += (pred1.argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred2.argmax(1) == y2).type(torch.float).sum().item()
            correct3 += (pred3.argmax(1) == y3).type(torch.float).sum().item()
            train_loss += loss.item()
    
    # 更新学习率
    scheduler.step(train_loss)
    
    return train_loss/num_batches, correct1/size, correct2/size, correct3/size

def test(dataloader, model, loss_fn):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss, correct1, correct2, correct3 = 0, 0, 0, 0
    
    with torch.no_grad():
        for X, y in dataloader:
            y1, y2, y3 = y[0], y[1], y[2]  # 获取三个任务的标签
            X, y1, y2, y3 = X.to(device), y1.to(device), y2.to(device), y3.to(device)
            
            # 计算预测
            pred1, pred2, pred3 = model(X)  # 获取三个任务的预测
            
            # 计算每个样本的损失
            loss1 = loss_fn(pred1, y1)
            loss2 = loss_fn(pred2, y2)
            loss3 = loss_fn(pred3, y3)  # 计算第三个任务的损失
            
            # 使用动态任务权重
            task1_weight = torch.exp(model.task1_weight)
            task2_weight = torch.exp(model.task2_weight)
            task3_weight = torch.exp(model.task3_weight)
            total_weight = task1_weight + task2_weight + task3_weight
            
            # 归一化权重
            task1_weight = task1_weight / total_weight
            task2_weight = task2_weight / total_weight
            task3_weight = task3_weight / total_weight
            
            # 组合损失
            loss = task1_weight * loss1.mean() + task2_weight * loss2.mean() + task3_weight * loss3.mean()
            
            # 统计
            test_loss += loss.item()
            correct1 += (pred1.argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred2.argmax(1) == y2).type(torch.float).sum().item()
            correct3 += (pred3.argmax(1) == y3).type(torch.float).sum().item()
    
    test_loss /= num_batches
    correct1 /= size
    correct2 /= size
    correct3 /= size
    return test_loss, correct1, correct2, correct3


# In[36]:
epochs = 300
patience = 100  # 早停的耐心值
best_val_loss = float('inf')
best_epoch = 0
no_improve_count = 0

# 保存训练和测试指标
train_loss = []
train_acc1 = []
train_acc2 = []
train_acc3 = []  # 添加第三个任务的准确率列表
test_loss = []
test_acc1 = []
test_acc2 = []
test_acc3 = []  # 添加第三个任务的准确率列表

# 创建保存目录
model_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\models-resswin-cascade-3任务"
os.makedirs(model_dir, exist_ok=True)

# 最佳模型路径
best_model_path = os.path.join(model_dir, "best_model.pth")
final_model_path = os.path.join(model_dir, "final_model.pth")

# 创建训练日志文件
training_log_path = os.path.join(model_dir, "training_log.txt")
with open(training_log_path, 'w', encoding='utf-8') as f:
    f.write("Training Log\n")
    f.write("============\n\n")
    f.write("Training Configuration:\n")
    f.write(f"Total Epochs: {epochs}\n")
    f.write(f"Early Stopping Patience: {patience}\n")
    f.write(f"Batch Size: {BATCH_SIZE}\n")
    f.write(f"Initial Learning Rate: {optimizer.param_groups[0]['lr']}\n")
    f.write(f"Weight Decay: {optimizer.param_groups[0]['weight_decay']}\n\n")
    
    f.write("Dataset Information:\n")
    f.write(f"Training Set Size: {len(train_dataset)}\n")
    f.write(f"Test Set Size: {len(test_dataset)}\n\n")
    
    f.write("Class Distribution:\n")
    f.write("Task 1:\n")
    unique_labels1, counts1 = np.unique(train_label_list1, return_counts=True)
    for label, count in zip(unique_labels1, counts1):
        f.write(f"  Class {label}: {count} samples\n")
    f.write("\nTask 2:\n")
    unique_labels2, counts2 = np.unique(train_label_list2, return_counts=True)
    for label, count in zip(unique_labels2, counts2):
        f.write(f"  Class {label}: {count} samples\n")
    
    f.write("\nTraining Progress:\n")
    f.write("Epoch  Train Loss  Train Acc1  Train Acc2  Train Acc3  Test Loss  Test Acc1  Test Acc2  Test Acc3\n")
    f.write("=============================================================================================\n")

for epoch in range(epochs):
    epoch_loss, epoch_acc1, epoch_acc2, epoch_acc3 = train(train_dl, model, loss_fn, optimizer, scheduler)
    epoch_test_loss, epoch_test_acc1, epoch_test_acc2, epoch_test_acc3 = test(test_dl, model, loss_fn)
    
    # 记录指标
    train_loss.append(epoch_loss)
    train_acc1.append(epoch_acc1)
    train_acc2.append(epoch_acc2)
    train_acc3.append(epoch_acc3)  # 记录第三个任务的训练准确率
    test_loss.append(epoch_test_loss)
    test_acc1.append(epoch_test_acc1)
    test_acc2.append(epoch_test_acc2)
    test_acc3.append(epoch_test_acc3)  # 记录第三个任务的测试准确率
    
    # 打印当前训练状态
    template = ("epoch:{:2d}, train_loss: {:.5f}, train_acc1: {:.1f}%, train_acc2: {:.1f}%, train_acc3: {:.1f}%, "
                "test_loss: {:.5f}, test_acc1: {:.1f}%, test_acc2: {:.1f}%, test_acc3: {:.1f}%")
    print(template.format(
          epoch, epoch_loss, epoch_acc1*100, epoch_acc2*100, epoch_acc3*100,
          epoch_test_loss, epoch_test_acc1*100, epoch_test_acc2*100, epoch_test_acc3*100))
    
    # 将每个epoch的结果写入日志文件
    with open(training_log_path, 'a', encoding='utf-8') as f:
        f.write(f"{epoch:5d}  {epoch_loss:.6f}  {epoch_acc1*100:9.2f}  {epoch_acc2*100:9.2f}  {epoch_acc3*100:9.2f}  "
                f"{epoch_test_loss:.6f}  {epoch_test_acc1*100:8.2f}  {epoch_test_acc2*100:8.2f}  {epoch_test_acc3*100:8.2f}\n")
    
    # 检查是否是最佳模型
    if epoch_test_loss < best_val_loss:
        best_val_loss = epoch_test_loss
        best_epoch = epoch
        no_improve_count = 0
        
        # 保存最佳模型
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'train_loss': train_loss,
            'train_acc1': train_acc1,
            'train_acc2': train_acc2,
            'train_acc3': train_acc3,
            'test_loss': test_loss,
            'test_acc1': test_acc1,
            'test_acc2': test_acc2,
            'test_acc3': test_acc3,
            'best_val_loss': best_val_loss,
        }, best_model_path)
        
        print(f"Saved best model at epoch {epoch} with validation loss: {best_val_loss:.5f}")
        
        # 记录最佳模型信息到日志文件
        with open(training_log_path, 'a', encoding='utf-8') as f:
            f.write(f"\nNew best model saved at epoch {epoch} with validation loss: {best_val_loss:.5f}\n\n")
    else:
        no_improve_count += 1
        print(f"No improvement for {no_improve_count} epochs. Best epoch: {best_epoch}")
    
    # 早停检查
    if no_improve_count >= patience:
        print(f"Early stopping triggered after {epoch+1} epochs. Best epoch: {best_epoch}")
        # 记录早停信息到日志文件
        with open(training_log_path, 'a', encoding='utf-8') as f:
            f.write(f"\nEarly stopping triggered after {epoch+1} epochs. Best epoch: {best_epoch}\n")
        break

# 保存最终模型
torch.save({
    'epoch': epoch,
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'scheduler_state_dict': scheduler.state_dict(),
    'train_loss': train_loss,
    'train_acc1': train_acc1,
    'train_acc2': train_acc2,
    'train_acc3': train_acc3,
    'test_loss': test_loss,
    'test_acc1': test_acc1,
    'test_acc2': test_acc2,
    'test_acc3': test_acc3,
    'best_val_loss': best_val_loss,
    'best_epoch': best_epoch,
}, final_model_path)

print(f"Training completed. Final model saved at epoch {epoch}.")
print(f"Best model was at epoch {best_epoch} with validation loss: {best_val_loss:.5f}")

# 绘制损失曲线和准确率曲线
plt.figure(figsize=(15, 5))
plt.subplot(1, 2, 1)
plt.plot(range(1, len(train_loss)+1), train_loss, label='train_loss')
plt.plot(range(1, len(test_loss)+1), test_loss, label='test_loss')
plt.axvline(x=best_epoch+1, color='r', linestyle='--', label=f'Best epoch ({best_epoch+1})')
plt.legend()
plt.title("Loss Curves")

# 绘制准确率曲线
plt.subplot(1, 2, 2)
plt.plot(range(1, len(train_acc1)+1), train_acc1, label='train_task1_acc')
plt.plot(range(1, len(test_acc1)+1), test_acc1, label='test_task1_acc')
plt.plot(range(1, len(train_acc2)+1), train_acc2, label='train_task2_acc')
plt.plot(range(1, len(test_acc2)+1), test_acc2, label='test_task2_acc')
plt.plot(range(1, len(train_acc3)+1), train_acc3, label='train_task3_acc')  # 添加第三个任务的训练准确率
plt.plot(range(1, len(test_acc3)+1), test_acc3, label='test_task3_acc')  # 添加第三个任务的测试准确率
plt.axvline(x=best_epoch+1, color='r', linestyle='--', label=f'Best epoch ({best_epoch+1})')
plt.legend(loc='lower right', fontsize='small')
plt.title("Accuracy Curves")

plt.tight_layout()
plt.savefig(os.path.join(model_dir, "training_curves.png"), dpi=300)
plt.show()

#%% 定义获取预测结果的函数
def get_predictions(model, dataloader, image_paths):
    model.eval()
    true_label1 = []
    true_label2 = []
    true_label3 = []  # 添加第三个任务的标签列表
    predict_label1 = []
    predict_label2 = []
    predict_label3 = []  # 添加第三个任务的预测列表
    predict_prob1 = []
    predict_prob2 = []
    predict_prob3 = []  # 添加第三个任务的概率列表
    
    with torch.no_grad():
        for x, (y1, y2, y3) in dataloader:  # 修改为接收三个标签
            if torch.cuda.is_available():
                x = x.to('cuda')
                y1 = y1.to('cuda')
                y2 = y2.to('cuda')
                y3 = y3.to('cuda')  # 添加第三个标签的设备转换
            
            y_pred1, y_pred2, y_pred3 = model(x)  # 获取三个任务的预测
            _, y_pred_class1 = torch.max(y_pred1, dim=1)
            _, y_pred_class2 = torch.max(y_pred2, dim=1)
            _, y_pred_class3 = torch.max(y_pred3, dim=1)  # 获取第三个任务的预测类别
            
            # 获取预测概率
            y_pred_prob1 = torch.softmax(y_pred1, dim=1)
            y_pred_prob2 = torch.softmax(y_pred2, dim=1)
            y_pred_prob3 = torch.softmax(y_pred3, dim=1)  # 获取第三个任务的预测概率
            
            true_label1.extend(y1.cpu().tolist())
            true_label2.extend(y2.cpu().tolist())
            true_label3.extend(y3.cpu().tolist())  # 添加第三个任务的真实标签
            predict_label1.extend(y_pred_class1.cpu().tolist())
            predict_label2.extend(y_pred_class2.cpu().tolist())
            predict_label3.extend(y_pred_class3.cpu().tolist())  # 添加第三个任务的预测标签
            predict_prob1.extend(y_pred_prob1.cpu().tolist())
            predict_prob2.extend(y_pred_prob2.cpu().tolist())
            predict_prob3.extend(y_pred_prob3.cpu().tolist())  # 添加第三个任务的预测概率
    
    return true_label1, true_label2, true_label3, predict_label1, predict_label2, predict_label3, predict_prob1, predict_prob2, predict_prob3

# 保存预测结果的函数
def save_predictions(model, train_dataset, test_dataset, prefix=""):
    # 创建数据加载器
    new_train_dl = torch.utils.data.DataLoader(train_dataset, batch_size=16, shuffle=False)
    test_dl = torch.utils.data.DataLoader(test_dataset, batch_size=16, shuffle=False)

    # 直接使用dataset的imgs_path属性
    train_image_paths = train_dataset.imgs_path
    test_image_paths = test_dataset.imgs_path
    
    # 获取预测结果
    train_true_label1, train_true_label2, train_true_label3, train_predict_label1, train_predict_label2, train_predict_label3, train_predict_prob1, train_predict_prob2, train_predict_prob3 = get_predictions(model, new_train_dl, train_image_paths)
    test_true_label1, test_true_label2, test_true_label3, test_predict_label1, test_predict_label2, test_predict_label3, test_predict_prob1, test_predict_prob2, test_predict_prob3 = get_predictions(model, test_dl, test_image_paths)

    # 创建结果DataFrame
    train_data = {
        'Image Path': train_image_paths,
        'True Label1': train_true_label1,
        'Predict Label1': train_predict_label1,
        'True Label2': train_true_label2,
        'Predict Label2': train_predict_label2,
        'True Label3': train_true_label3,  # 添加第三个任务的标签
        'Predict Label3': train_predict_label3,  # 添加第三个任务的预测
        'Predict Prob1': train_predict_prob1,
        'Predict Prob2': train_predict_prob2,
        'Predict Prob3': train_predict_prob3  # 添加第三个任务的概率
    }

    test_data = {
        'Image Path': test_image_paths,
        'True Label1': test_true_label1,
        'Predict Label1': test_predict_label1,
        'True Label2': test_true_label2,
        'Predict Label2': test_predict_label2,
        'True Label3': test_true_label3,  # 添加第三个任务的标签
        'Predict Label3': test_predict_label3,  # 添加第三个任务的预测
        'Predict Prob1': test_predict_prob1,
        'Predict Prob2': test_predict_prob2,
        'Predict Prob3': test_predict_prob3  # 添加第三个任务的概率
    }

    # 保存结果
    train_results = pd.DataFrame(train_data)
    test_results = pd.DataFrame(test_data)
   
    train_results.to_excel(os.path.join(model_dir, f"{prefix}train_results.xlsx"), index=False)
    test_results.to_excel(os.path.join(model_dir, f"{prefix}test_results.xlsx"), index=False)
    
    # 计算并返回评估指标
    train_acc1 = sum(1 for true, pred in zip(train_true_label1, train_predict_label1) if true == pred) / len(train_true_label1)
    train_acc2 = sum(1 for true, pred in zip(train_true_label2, train_predict_label2) if true == pred) / len(train_true_label2)
    train_acc3 = sum(1 for true, pred in zip(train_true_label3, train_predict_label3) if true == pred) / len(train_true_label3)
    test_acc1 = sum(1 for true, pred in zip(test_true_label1, test_predict_label1) if true == pred) / len(test_true_label1)
    test_acc2 = sum(1 for true, pred in zip(test_true_label2, test_predict_label2) if true == pred) / len(test_true_label2)
    test_acc3 = sum(1 for true, pred in zip(test_true_label3, test_predict_label3) if true == pred) / len(test_true_label3)
    
    return train_acc1, train_acc2, train_acc3, test_acc1, test_acc2, test_acc3


#%%加载最佳模型并保存其预测结果
print("Generating predictions for best model...")
best_checkpoint = torch.load(best_model_path)
model.load_state_dict(best_checkpoint['model_state_dict'])
best_train_acc1, best_train_acc2, best_train_acc3, best_test_acc1, best_test_acc2, best_test_acc3 = save_predictions(model, train_dataset, test_dataset, prefix="best_")

# 加载最终模型并保存其预测结果
print("Generating predictions for final model...")
final_checkpoint = torch.load(final_model_path)
model.load_state_dict(final_checkpoint['model_state_dict'])
final_train_acc1, final_train_acc2, final_train_acc3, final_test_acc1, final_test_acc2, final_test_acc3 = save_predictions(model, train_dataset, test_dataset, prefix="final_")

# 打印最佳模型和最终模型的性能比较
print("\nModel Performance Comparison:")
print(f"Best Model (Epoch {best_epoch}):")
print(f"  Train Acc Task1: {best_train_acc1*100:.2f}%, Train Acc Task2: {best_train_acc2*100:.2f}%, Train Acc Task3: {best_train_acc3*100:.2f}%")
print(f"  Test Acc Task1: {best_test_acc1*100:.2f}%, Test Acc Task2: {best_test_acc2*100:.2f}%, Test Acc Task3: {best_test_acc3*100:.2f}%")
print(f"Final Model (Epoch {epoch}):")
print(f"  Train Acc Task1: {final_train_acc1*100:.2f}%, Train Acc Task2: {final_train_acc2*100:.2f}%, Train Acc Task3: {final_train_acc3*100:.2f}%")
print(f"  Test Acc Task1: {final_test_acc1*100:.2f}%, Test Acc Task2: {final_test_acc2*100:.2f}%, Test Acc Task3: {final_test_acc3*100:.2f}%")

# 保存最终性能报告
final_report_path = os.path.join(model_dir, "final_performance_report.txt")
with open(final_report_path, 'w', encoding='utf-8') as f:
    f.write("Final Performance Report\n")
    f.write("=======================\n\n")
    
    f.write("Training Summary\n")
    f.write("---------------\n")
    f.write(f"Total Epochs Run: {epoch + 1}\n")
    f.write(f"Best Epoch: {best_epoch}\n")
    f.write(f"Best Validation Loss: {best_val_loss:.6f}\n")
    f.write(f"Early Stopping: {'Yes' if no_improve_count >= patience else 'No'}\n\n")
    
    f.write("Best Model Performance\n")
    f.write("---------------------\n")
    f.write(f"Train Accuracy Task1: {best_train_acc1*100:.2f}%\n")
    f.write(f"Train Accuracy Task2: {best_train_acc2*100:.2f}%\n")
    f.write(f"Train Accuracy Task3: {best_train_acc3*100:.2f}%\n")
    f.write(f"Test Accuracy Task1: {best_test_acc1*100:.2f}%\n")
    f.write(f"Test Accuracy Task2: {best_test_acc2*100:.2f}%\n")
    f.write(f"Test Accuracy Task3: {best_test_acc3*100:.2f}%\n\n")
    
    f.write("Final Model Performance\n")
    f.write("----------------------\n")
    f.write(f"Train Accuracy Task1: {final_train_acc1*100:.2f}%\n")
    f.write(f"Train Accuracy Task2: {final_train_acc2*100:.2f}%\n")
    f.write(f"Train Accuracy Task3: {final_train_acc3*100:.2f}%\n")
    f.write(f"Test Accuracy Task1: {final_test_acc1*100:.2f}%\n")
    f.write(f"Test Accuracy Task2: {final_test_acc2*100:.2f}%\n")
    f.write(f"Test Accuracy Task3: {final_test_acc3*100:.2f}%\n")

print(f"\nResults have been saved to:")
print(f"1. Training log: {training_log_path}")
print(f"2. Final performance report: {final_report_path}")
print("Done!")
