{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import glob\n", "import os\n", "#################################################################\n", "path_originalData = r\"F:\\sth\\23Fall\\fcpro\\brain_image2\"\n", "\n", "train_image = list(os.listdir(path_originalData + \"/imagesTr\"))\n", "train_label = list(os.listdir(path_originalData + \"/labelsTr\"))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["196 46\n"]}], "source": ["validation_image = train_image[-46:]\n", "validation_label = train_label[-46:]\n", "\n", "train_image = train_image[:-46]\n", "train_label = train_label[:-46]\n", "print(len(train_image), len(validation_image))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import OrderedDict\n", "\n", "json_dict = OrderedDict()\n", "json_dict['name'] = \"<PERSON>\"\n", "json_dict['tensorImageSize'] = \"3D\"\n", "json_dict['release'] = \"0.0\"\n", "json_dict['modality'] = {\n", "    \"0\": \"CT\"\n", "}\n", "\n", "json_dict['labels'] = {\n", "    \"0\": \"Background\",\n", "    \"1\": \"Right_ventricular_cistern\",\n", "    \"2\": \"Right_cerebral_sulcus\",\n", "    \"3\": \"Left_ventricular_cistern\",\n", "    \"4\": \"Left_cerebral_sulcus\"\n", "}\n", "\n", "json_dict['numTraining'] = len(train_image)\n", "\n", "json_dict['training'] = []\n", "for idx in range(len(train_image)):\n", "    json_dict['training'].append({'image': \"./imagesTr/{}\".format(train_image[idx]) , \"label\": \"./labelsTr/%s\" % train_label[idx]})\n", "json_dict['validation'] = []\n", "for idx in range(len(validation_image)):\n", "    json_dict['validation'].append({'image': \"./imagesTr/{}\".format(validation_image[idx]) , \"label\": \"./labelsTr/%s\" % validation_label[idx]})\n", "\n", "with open(os.path.join(path_originalData, \"dataset.json\"), 'w') as f:\n", "    json.dump(json_dict, f, indent=4, sort_keys=True)"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}