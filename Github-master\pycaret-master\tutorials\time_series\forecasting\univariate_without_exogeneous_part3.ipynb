{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Overview \n", "\n", "In the 10x series of notebooks, we will look at Time Series modeling in pycaret using univariate data and no exogenous variables. We will use the famous airline dataset for illustration. Our plan of action is as follows:\n", "\n", "1. Perform EDA on the dataset to extract valuable insight about the process generating the time series. **(COMPLETED)**\n", "2. Model the dataset based on exploratory analysis (univariable model without exogenous variables). **(COMPLETED)**\n", "3. Use an automated approach (AutoML) to improve the performance. **(Covered in this notebook)**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Only enable critical logging (Optional)\n", "import os\n", "os.environ[\"PYCARET_CUSTOM_LOGGING_LEVEL\"] = \"CRITICAL\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "System:\n", "    python: 3.9.16 (main, Jan 11 2023, 16:16:36) [MSC v.1916 64 bit (AMD64)]\n", "executable: C:\\Users\\<USER>\\.conda\\envs\\pycaret_dev_sktime_16p1\\python.exe\n", "   machine: Windows-10-10.0.19044-SP0\n", "\n", "PyCaret required dependencies:\n", "                 pip: 22.3.1\n", "          setuptools: 65.6.3\n", "             pycaret: 3.0.0\n", "             IPython: 8.10.0\n", "          ipywidgets: 8.0.4\n", "                tqdm: 4.64.1\n", "               numpy: 1.23.5\n", "              pandas: 1.5.3\n", "              jinja2: 3.1.2\n", "               scipy: 1.10.0\n", "              joblib: 1.2.0\n", "             sklearn: 1.2.1\n", "                pyod: 1.0.8\n", "            imblearn: 0.10.1\n", "   category_encoders: 2.6.0\n", "            lightgbm: 3.3.5\n", "               numba: 0.56.4\n", "            requests: 2.28.2\n", "          matplotlib: 3.7.0\n", "          scikitplot: 0.3.7\n", "         yellowbrick: 1.5\n", "              plotly: 5.13.0\n", "             kaleido: 0.2.1\n", "         statsmodels: 0.13.5\n", "              sktime: 0.16.1\n", "               tbats: 1.1.2\n", "            pmdarima: 2.0.2\n", "              psutil: 5.9.4\n", "\n", "PyCaret optional dependencies:\n", "                shap: 0.41.0\n", "           interpret: Not installed\n", "                umap: Not installed\n", "    pandas_profiling: Not installed\n", "  explainerdashboard: Not installed\n", "             autoviz: Not installed\n", "           fairlearn: Not installed\n", "             xgboost: Not installed\n", "            catboost: Not installed\n", "              kmodes: Not installed\n", "             mlxtend: Not installed\n", "       statsforecast: Not installed\n", "        tune_sklearn: Not installed\n", "                 ray: Not installed\n", "            hyperopt: Not installed\n", "              optuna: Not installed\n", "               skopt: Not installed\n", "              mlflow: 2.1.1\n", "              gradio: Not installed\n", "             fastapi: Not installed\n", "             uvicorn: Not installed\n", "              m2cgen: Not installed\n", "           evidently: Not installed\n", "               fugue: 0.8.0\n", "           streamlit: Not installed\n", "             prophet: 1.1.2\n"]}], "source": ["def what_is_installed():\n", "    from pycaret import show_versions\n", "    show_versions()\n", "\n", "try:\n", "    what_is_installed()\n", "except ModuleNotFoundError:\n", "    !pip install pycaret\n", "    what_is_installed()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import time\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from pycaret.datasets import get_data\n", "from pycaret.time_series import TSForecastingExperiment"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["y = get_data('airline', verbose=False)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# We want to forecast the next 12 months of data and we will use 3 fold cross-validation to test the models.\n", "fh = 12 # or alternately fh = np.arange(1,13)\n", "fold = 3"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Global Figure Settings for notebook ----\n", "# Depending on whether you are using jupyter notebook, jupyter lab, Google Colab, you may have to set the renderer appropriately\n", "# NOTE: Setting to a static renderer here so that the notebook saved size is reduced.\n", "fig_kwargs = {\n", "    # \"renderer\": \"notebook\",\n", "    \"renderer\": \"png\",\n", "    \"width\": 1000,\n", "    \"height\": 600,\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Auto Create\n", "\n", "We have so many models to choose from. How do we know which ones perform the best. Let's see how we can do with `pycaret`."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_ec923_row25_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_ec923\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_ec923_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_ec923_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_ec923_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_ec923_row0_col1\" class=\"data row0 col1\" >42</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_ec923_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_ec923_row1_col1\" class=\"data row1 col1\" >Number of airline passengers</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_ec923_row2_col0\" class=\"data row2 col0\" >Approach</td>\n", "      <td id=\"T_ec923_row2_col1\" class=\"data row2 col1\" >Univariate</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_ec923_row3_col0\" class=\"data row3 col0\" >Exogenous Variables</td>\n", "      <td id=\"T_ec923_row3_col1\" class=\"data row3 col1\" >Not Present</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_ec923_row4_col0\" class=\"data row4 col0\" >Original data shape</td>\n", "      <td id=\"T_ec923_row4_col1\" class=\"data row4 col1\" >(144, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_ec923_row5_col0\" class=\"data row5 col0\" >Transformed data shape</td>\n", "      <td id=\"T_ec923_row5_col1\" class=\"data row5 col1\" >(144, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_ec923_row6_col0\" class=\"data row6 col0\" >Transformed train set shape</td>\n", "      <td id=\"T_ec923_row6_col1\" class=\"data row6 col1\" >(132, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_ec923_row7_col0\" class=\"data row7 col0\" >Transformed test set shape</td>\n", "      <td id=\"T_ec923_row7_col1\" class=\"data row7 col1\" >(12, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_ec923_row8_col0\" class=\"data row8 col0\" >Rows with missing values</td>\n", "      <td id=\"T_ec923_row8_col1\" class=\"data row8 col1\" >0.0%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_ec923_row9_col0\" class=\"data row9 col0\" >Fold Generator</td>\n", "      <td id=\"T_ec923_row9_col1\" class=\"data row9 col1\" >ExpandingWindowSplitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_ec923_row10_col0\" class=\"data row10 col0\" >Fold Number</td>\n", "      <td id=\"T_ec923_row10_col1\" class=\"data row10 col1\" >3</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_ec923_row11_col0\" class=\"data row11 col0\" >Enforce Prediction Interval</td>\n", "      <td id=\"T_ec923_row11_col1\" class=\"data row11 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_ec923_row12_col0\" class=\"data row12 col0\" >Splits used for hyperparameters</td>\n", "      <td id=\"T_ec923_row12_col1\" class=\"data row12 col1\" >all</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_ec923_row13_col0\" class=\"data row13 col0\" >User Defined Seasonal Period(s)</td>\n", "      <td id=\"T_ec923_row13_col1\" class=\"data row13 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_ec923_row14_col0\" class=\"data row14 col0\" >Ignore Seasonality Test</td>\n", "      <td id=\"T_ec923_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_ec923_row15_col0\" class=\"data row15 col0\" >Seasonality Detection Algo</td>\n", "      <td id=\"T_ec923_row15_col1\" class=\"data row15 col1\" >auto</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_ec923_row16_col0\" class=\"data row16 col0\" >Max Period to Consider</td>\n", "      <td id=\"T_ec923_row16_col1\" class=\"data row16 col1\" >60</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_ec923_row17_col0\" class=\"data row17 col0\" >Seasonal Period(s) Tested</td>\n", "      <td id=\"T_ec923_row17_col1\" class=\"data row17 col1\" >[12, 24, 36, 11, 48]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_ec923_row18_col0\" class=\"data row18 col0\" >Significant Seasonal Period(s)</td>\n", "      <td id=\"T_ec923_row18_col1\" class=\"data row18 col1\" >[12, 24, 36, 11, 48]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_ec923_row19_col0\" class=\"data row19 col0\" >Significant Seasonal Period(s) without Harmonics</td>\n", "      <td id=\"T_ec923_row19_col1\" class=\"data row19 col1\" >[48, 36, 11]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_ec923_row20_col0\" class=\"data row20 col0\" >Remove Harmonics</td>\n", "      <td id=\"T_ec923_row20_col1\" class=\"data row20 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_ec923_row21_col0\" class=\"data row21 col0\" >Harmonics Order Method</td>\n", "      <td id=\"T_ec923_row21_col1\" class=\"data row21 col1\" >harmonic_max</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_ec923_row22_col0\" class=\"data row22 col0\" >Num Seasonalities to Use</td>\n", "      <td id=\"T_ec923_row22_col1\" class=\"data row22 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_ec923_row23_col0\" class=\"data row23 col0\" >All Seasonalities to Use</td>\n", "      <td id=\"T_ec923_row23_col1\" class=\"data row23 col1\" >[12]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_ec923_row24_col0\" class=\"data row24 col0\" >Primary Seasonality</td>\n", "      <td id=\"T_ec923_row24_col1\" class=\"data row24 col1\" >12</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_ec923_row25_col0\" class=\"data row25 col0\" >Seasonality Present</td>\n", "      <td id=\"T_ec923_row25_col1\" class=\"data row25 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_ec923_row26_col0\" class=\"data row26 col0\" >Seasonality Type</td>\n", "      <td id=\"T_ec923_row26_col1\" class=\"data row26 col1\" >mul</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_ec923_row27_col0\" class=\"data row27 col0\" >Target Strictly Positive</td>\n", "      <td id=\"T_ec923_row27_col1\" class=\"data row27 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_ec923_row28_col0\" class=\"data row28 col0\" >Target White Noise</td>\n", "      <td id=\"T_ec923_row28_col1\" class=\"data row28 col1\" >No</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_ec923_row29_col0\" class=\"data row29 col0\" >Recommended d</td>\n", "      <td id=\"T_ec923_row29_col1\" class=\"data row29 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_ec923_row30_col0\" class=\"data row30 col0\" >Recommended Seasonal D</td>\n", "      <td id=\"T_ec923_row30_col1\" class=\"data row30 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_ec923_row31_col0\" class=\"data row31 col0\" >Preprocess</td>\n", "      <td id=\"T_ec923_row31_col1\" class=\"data row31 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_ec923_row32_col0\" class=\"data row32 col0\" >CPU Jobs</td>\n", "      <td id=\"T_ec923_row32_col1\" class=\"data row32 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_ec923_row33_col0\" class=\"data row33 col0\" >Use GPU</td>\n", "      <td id=\"T_ec923_row33_col1\" class=\"data row33 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_ec923_row34_col0\" class=\"data row34 col0\" >Log Experiment</td>\n", "      <td id=\"T_ec923_row34_col1\" class=\"data row34 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_ec923_row35_col0\" class=\"data row35 col0\" >Experiment Name</td>\n", "      <td id=\"T_ec923_row35_col1\" class=\"data row35 col1\" >ts-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ec923_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_ec923_row36_col0\" class=\"data row36 col0\" >USI</td>\n", "      <td id=\"T_ec923_row36_col1\" class=\"data row36 col1\" >8a65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f7546e9d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<pycaret.time_series.forecasting.oop.TSForecastingExperiment at 0x27f75438310>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["exp = TSForecastingExperiment()\n", "exp.setup(data=y, fh=fh, fold=fold, fig_kwargs=fig_kwargs, session_id=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compare Models"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_ca553 th {\n", "  text-align: left;\n", "}\n", "#T_ca553_row0_col0, #T_ca553_row1_col0, #T_ca553_row1_col1, #T_ca553_row1_col2, #T_ca553_row1_col3, #T_ca553_row1_col4, #T_ca553_row1_col5, #T_ca553_row1_col6, #T_ca553_row1_col7, #T_ca553_row2_col0, #T_ca553_row2_col1, #T_ca553_row2_col2, #T_ca553_row2_col3, #T_ca553_row2_col4, #T_ca553_row2_col5, #T_ca553_row2_col6, #T_ca553_row2_col7, #T_ca553_row3_col0, #T_ca553_row3_col1, #T_ca553_row3_col2, #T_ca553_row3_col3, #T_ca553_row3_col4, #T_ca553_row3_col5, #T_ca553_row3_col6, #T_ca553_row3_col7, #T_ca553_row4_col0, #T_ca553_row4_col1, #T_ca553_row4_col2, #T_ca553_row4_col3, #T_ca553_row4_col4, #T_ca553_row4_col5, #T_ca553_row4_col6, #T_ca553_row4_col7, #T_ca553_row5_col0, #T_ca553_row5_col1, #T_ca553_row5_col2, #T_ca553_row5_col3, #T_ca553_row5_col4, #T_ca553_row5_col5, #T_ca553_row5_col6, #T_ca553_row5_col7, #T_ca553_row6_col0, #T_ca553_row6_col1, #T_ca553_row6_col2, #T_ca553_row6_col3, #T_ca553_row6_col4, #T_ca553_row6_col5, #T_ca553_row6_col6, #T_ca553_row6_col7, #T_ca553_row7_col0, #T_ca553_row7_col1, #T_ca553_row7_col2, #T_ca553_row7_col3, #T_ca553_row7_col4, #T_ca553_row7_col5, #T_ca553_row7_col6, #T_ca553_row7_col7, #T_ca553_row8_col0, #T_ca553_row8_col1, #T_ca553_row8_col2, #T_ca553_row8_col3, #T_ca553_row8_col4, #T_ca553_row8_col5, #T_ca553_row8_col6, #T_ca553_row8_col7, #T_ca553_row9_col0, #T_ca553_row9_col1, #T_ca553_row9_col2, #T_ca553_row9_col3, #T_ca553_row9_col4, #T_ca553_row9_col5, #T_ca553_row9_col6, #T_ca553_row9_col7, #T_ca553_row10_col0, #T_ca553_row10_col1, #T_ca553_row10_col2, #T_ca553_row10_col3, #T_ca553_row10_col4, #T_ca553_row10_col5, #T_ca553_row10_col6, #T_ca553_row10_col7, #T_ca553_row11_col0, #T_ca553_row11_col1, #T_ca553_row11_col2, #T_ca553_row11_col3, #T_ca553_row11_col4, #T_ca553_row11_col5, #T_ca553_row11_col6, #T_ca553_row11_col7, #T_ca553_row12_col0, #T_ca553_row12_col1, #T_ca553_row12_col2, #T_ca553_row12_col3, #T_ca553_row12_col4, #T_ca553_row12_col5, #T_ca553_row12_col6, #T_ca553_row12_col7, #T_ca553_row13_col0, #T_ca553_row13_col1, #T_ca553_row13_col2, #T_ca553_row13_col3, #T_ca553_row13_col4, #T_ca553_row13_col5, #T_ca553_row13_col6, #T_ca553_row13_col7, #T_ca553_row14_col0, #T_ca553_row14_col1, #T_ca553_row14_col2, #T_ca553_row14_col3, #T_ca553_row14_col4, #T_ca553_row14_col5, #T_ca553_row14_col6, #T_ca553_row14_col7, #T_ca553_row15_col0, #T_ca553_row15_col1, #T_ca553_row15_col2, #T_ca553_row15_col3, #T_ca553_row15_col4, #T_ca553_row15_col5, #T_ca553_row15_col6, #T_ca553_row15_col7, #T_ca553_row16_col0, #T_ca553_row16_col1, #T_ca553_row16_col2, #T_ca553_row16_col3, #T_ca553_row16_col4, #T_ca553_row16_col5, #T_ca553_row16_col6, #T_ca553_row16_col7, #T_ca553_row17_col0, #T_ca553_row17_col1, #T_ca553_row17_col2, #T_ca553_row17_col3, #T_ca553_row17_col4, #T_ca553_row17_col5, #T_ca553_row17_col6, #T_ca553_row17_col7, #T_ca553_row18_col0, #T_ca553_row18_col1, #T_ca553_row18_col2, #T_ca553_row18_col3, #T_ca553_row18_col4, #T_ca553_row18_col5, #T_ca553_row18_col6, #T_ca553_row18_col7, #T_ca553_row19_col0, #T_ca553_row19_col1, #T_ca553_row19_col2, #T_ca553_row19_col3, #T_ca553_row19_col4, #T_ca553_row19_col5, #T_ca553_row19_col6, #T_ca553_row19_col7, #T_ca553_row20_col0, #T_ca553_row20_col1, #T_ca553_row20_col2, #T_ca553_row20_col3, #T_ca553_row20_col4, #T_ca553_row20_col5, #T_ca553_row20_col6, #T_ca553_row20_col7, #T_ca553_row21_col0, #T_ca553_row21_col1, #T_ca553_row21_col2, #T_ca553_row21_col3, #T_ca553_row21_col4, #T_ca553_row21_col5, #T_ca553_row21_col6, #T_ca553_row21_col7, #T_ca553_row22_col0, #T_ca553_row22_col1, #T_ca553_row22_col2, #T_ca553_row22_col3, #T_ca553_row22_col4, #T_ca553_row22_col5, #T_ca553_row22_col6, #T_ca553_row22_col7, #T_ca553_row23_col0, #T_ca553_row23_col1, #T_ca553_row23_col2, #T_ca553_row23_col3, #T_ca553_row23_col4, #T_ca553_row23_col5, #T_ca553_row23_col6, #T_ca553_row23_col7, #T_ca553_row24_col0, #T_ca553_row24_col1, #T_ca553_row24_col2, #T_ca553_row24_col3, #T_ca553_row24_col4, #T_ca553_row24_col5, #T_ca553_row24_col6, #T_ca553_row24_col7, #T_ca553_row25_col0, #T_ca553_row25_col1, #T_ca553_row25_col2, #T_ca553_row25_col3, #T_ca553_row25_col4, #T_ca553_row25_col5, #T_ca553_row25_col6, #T_ca553_row25_col7 {\n", "  text-align: left;\n", "}\n", "#T_ca553_row0_col1, #T_ca553_row0_col2, #T_ca553_row0_col3, #T_ca553_row0_col4, #T_ca553_row0_col5, #T_ca553_row0_col6, #T_ca553_row0_col7 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_ca553_row0_col8, #T_ca553_row1_col8, #T_ca553_row2_col8, #T_ca553_row3_col8, #T_ca553_row4_col8, #T_ca553_row5_col8, #T_ca553_row6_col8, #T_ca553_row7_col8, #T_ca553_row8_col8, #T_ca553_row9_col8, #T_ca553_row10_col8, #T_ca553_row11_col8, #T_ca553_row12_col8, #T_ca553_row13_col8, #T_ca553_row14_col8, #T_ca553_row15_col8, #T_ca553_row16_col8, #T_ca553_row17_col8, #T_ca553_row18_col8, #T_ca553_row19_col8, #T_ca553_row20_col8, #T_ca553_row21_col8, #T_ca553_row23_col8, #T_ca553_row24_col8, #T_ca553_row25_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_ca553_row22_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_ca553\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_ca553_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_ca553_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_ca553_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_ca553_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_ca553_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_ca553_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_ca553_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_ca553_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "      <th id=\"T_ca553_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row0\" class=\"row_heading level0 row0\" >exp_smooth</th>\n", "      <td id=\"T_ca553_row0_col0\" class=\"data row0 col0\" >Exponential Smoothing</td>\n", "      <td id=\"T_ca553_row0_col1\" class=\"data row0 col1\" >0.5852</td>\n", "      <td id=\"T_ca553_row0_col2\" class=\"data row0 col2\" >0.6105</td>\n", "      <td id=\"T_ca553_row0_col3\" class=\"data row0 col3\" >17.1926</td>\n", "      <td id=\"T_ca553_row0_col4\" class=\"data row0 col4\" >20.1633</td>\n", "      <td id=\"T_ca553_row0_col5\" class=\"data row0 col5\" >0.0435</td>\n", "      <td id=\"T_ca553_row0_col6\" class=\"data row0 col6\" >0.0439</td>\n", "      <td id=\"T_ca553_row0_col7\" class=\"data row0 col7\" >0.8918</td>\n", "      <td id=\"T_ca553_row0_col8\" class=\"data row0 col8\" >0.1100</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row1\" class=\"row_heading level0 row1\" >ets</th>\n", "      <td id=\"T_ca553_row1_col0\" class=\"data row1 col0\" >ETS</td>\n", "      <td id=\"T_ca553_row1_col1\" class=\"data row1 col1\" >0.5931</td>\n", "      <td id=\"T_ca553_row1_col2\" class=\"data row1 col2\" >0.6212</td>\n", "      <td id=\"T_ca553_row1_col3\" class=\"data row1 col3\" >17.4165</td>\n", "      <td id=\"T_ca553_row1_col4\" class=\"data row1 col4\" >20.5103</td>\n", "      <td id=\"T_ca553_row1_col5\" class=\"data row1 col5\" >0.0440</td>\n", "      <td id=\"T_ca553_row1_col6\" class=\"data row1 col6\" >0.0445</td>\n", "      <td id=\"T_ca553_row1_col7\" class=\"data row1 col7\" >0.8882</td>\n", "      <td id=\"T_ca553_row1_col8\" class=\"data row1 col8\" >0.1533</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row2\" class=\"row_heading level0 row2\" >et_cds_dt</th>\n", "      <td id=\"T_ca553_row2_col0\" class=\"data row2 col0\" >Extra Trees w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row2_col1\" class=\"data row2 col1\" >0.6666</td>\n", "      <td id=\"T_ca553_row2_col2\" class=\"data row2 col2\" >0.7255</td>\n", "      <td id=\"T_ca553_row2_col3\" class=\"data row2 col3\" >19.6620</td>\n", "      <td id=\"T_ca553_row2_col4\" class=\"data row2 col4\" >24.0121</td>\n", "      <td id=\"T_ca553_row2_col5\" class=\"data row2 col5\" >0.0490</td>\n", "      <td id=\"T_ca553_row2_col6\" class=\"data row2 col6\" >0.0489</td>\n", "      <td id=\"T_ca553_row2_col7\" class=\"data row2 col7\" >0.8465</td>\n", "      <td id=\"T_ca553_row2_col8\" class=\"data row2 col8\" >0.5033</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row3\" class=\"row_heading level0 row3\" >huber_cds_dt</th>\n", "      <td id=\"T_ca553_row3_col0\" class=\"data row3 col0\" >Huber w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row3_col1\" class=\"data row3 col1\" >0.6813</td>\n", "      <td id=\"T_ca553_row3_col2\" class=\"data row3 col2\" >0.7866</td>\n", "      <td id=\"T_ca553_row3_col3\" class=\"data row3 col3\" >20.0334</td>\n", "      <td id=\"T_ca553_row3_col4\" class=\"data row3 col4\" >25.9670</td>\n", "      <td id=\"T_ca553_row3_col5\" class=\"data row3 col5\" >0.0491</td>\n", "      <td id=\"T_ca553_row3_col6\" class=\"data row3 col6\" >0.0499</td>\n", "      <td id=\"T_ca553_row3_col7\" class=\"data row3 col7\" >0.8113</td>\n", "      <td id=\"T_ca553_row3_col8\" class=\"data row3 col8\" >0.4100</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row4\" class=\"row_heading level0 row4\" >arima</th>\n", "      <td id=\"T_ca553_row4_col0\" class=\"data row4 col0\" >ARIMA</td>\n", "      <td id=\"T_ca553_row4_col1\" class=\"data row4 col1\" >0.6830</td>\n", "      <td id=\"T_ca553_row4_col2\" class=\"data row4 col2\" >0.6735</td>\n", "      <td id=\"T_ca553_row4_col3\" class=\"data row4 col3\" >20.0069</td>\n", "      <td id=\"T_ca553_row4_col4\" class=\"data row4 col4\" >22.2199</td>\n", "      <td id=\"T_ca553_row4_col5\" class=\"data row4 col5\" >0.0501</td>\n", "      <td id=\"T_ca553_row4_col6\" class=\"data row4 col6\" >0.0507</td>\n", "      <td id=\"T_ca553_row4_col7\" class=\"data row4 col7\" >0.8677</td>\n", "      <td id=\"T_ca553_row4_col8\" class=\"data row4 col8\" >0.0767</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row5\" class=\"row_heading level0 row5\" >lr_cds_dt</th>\n", "      <td id=\"T_ca553_row5_col0\" class=\"data row5 col0\" >Linear w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row5_col1\" class=\"data row5 col1\" >0.7004</td>\n", "      <td id=\"T_ca553_row5_col2\" class=\"data row5 col2\" >0.7702</td>\n", "      <td id=\"T_ca553_row5_col3\" class=\"data row5 col3\" >20.6084</td>\n", "      <td id=\"T_ca553_row5_col4\" class=\"data row5 col4\" >25.4401</td>\n", "      <td id=\"T_ca553_row5_col5\" class=\"data row5 col5\" >0.0509</td>\n", "      <td id=\"T_ca553_row5_col6\" class=\"data row5 col6\" >0.0514</td>\n", "      <td id=\"T_ca553_row5_col7\" class=\"data row5 col7\" >0.8215</td>\n", "      <td id=\"T_ca553_row5_col8\" class=\"data row5 col8\" >0.6733</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row6\" class=\"row_heading level0 row6\" >ridge_cds_dt</th>\n", "      <td id=\"T_ca553_row6_col0\" class=\"data row6 col0\" >Ridge w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row6_col1\" class=\"data row6 col1\" >0.7004</td>\n", "      <td id=\"T_ca553_row6_col2\" class=\"data row6 col2\" >0.7703</td>\n", "      <td id=\"T_ca553_row6_col3\" class=\"data row6 col3\" >20.6086</td>\n", "      <td id=\"T_ca553_row6_col4\" class=\"data row6 col4\" >25.4405</td>\n", "      <td id=\"T_ca553_row6_col5\" class=\"data row6 col5\" >0.0509</td>\n", "      <td id=\"T_ca553_row6_col6\" class=\"data row6 col6\" >0.0514</td>\n", "      <td id=\"T_ca553_row6_col7\" class=\"data row6 col7\" >0.8215</td>\n", "      <td id=\"T_ca553_row6_col8\" class=\"data row6 col8\" >0.6367</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row7\" class=\"row_heading level0 row7\" >en_cds_dt</th>\n", "      <td id=\"T_ca553_row7_col0\" class=\"data row7 col0\" >Elastic Net w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row7_col1\" class=\"data row7 col1\" >0.7029</td>\n", "      <td id=\"T_ca553_row7_col2\" class=\"data row7 col2\" >0.7732</td>\n", "      <td id=\"T_ca553_row7_col3\" class=\"data row7 col3\" >20.6816</td>\n", "      <td id=\"T_ca553_row7_col4\" class=\"data row7 col4\" >25.5362</td>\n", "      <td id=\"T_ca553_row7_col5\" class=\"data row7 col5\" >0.0511</td>\n", "      <td id=\"T_ca553_row7_col6\" class=\"data row7 col6\" >0.0516</td>\n", "      <td id=\"T_ca553_row7_col7\" class=\"data row7 col7\" >0.8201</td>\n", "      <td id=\"T_ca553_row7_col8\" class=\"data row7 col8\" >0.6833</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row8\" class=\"row_heading level0 row8\" >lasso_cds_dt</th>\n", "      <td id=\"T_ca553_row8_col0\" class=\"data row8 col0\" >Lasso w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row8_col1\" class=\"data row8 col1\" >0.7048</td>\n", "      <td id=\"T_ca553_row8_col2\" class=\"data row8 col2\" >0.7751</td>\n", "      <td id=\"T_ca553_row8_col3\" class=\"data row8 col3\" >20.7373</td>\n", "      <td id=\"T_ca553_row8_col4\" class=\"data row8 col4\" >25.6005</td>\n", "      <td id=\"T_ca553_row8_col5\" class=\"data row8 col5\" >0.0512</td>\n", "      <td id=\"T_ca553_row8_col6\" class=\"data row8 col6\" >0.0517</td>\n", "      <td id=\"T_ca553_row8_col7\" class=\"data row8 col7\" >0.8193</td>\n", "      <td id=\"T_ca553_row8_col8\" class=\"data row8 col8\" >0.4000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row9\" class=\"row_heading level0 row9\" >llar_cds_dt</th>\n", "      <td id=\"T_ca553_row9_col0\" class=\"data row9 col0\" >Lasso Least Angular Regressor w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row9_col1\" class=\"data row9 col1\" >0.7048</td>\n", "      <td id=\"T_ca553_row9_col2\" class=\"data row9 col2\" >0.7751</td>\n", "      <td id=\"T_ca553_row9_col3\" class=\"data row9 col3\" >20.7366</td>\n", "      <td id=\"T_ca553_row9_col4\" class=\"data row9 col4\" >25.6009</td>\n", "      <td id=\"T_ca553_row9_col5\" class=\"data row9 col5\" >0.0512</td>\n", "      <td id=\"T_ca553_row9_col6\" class=\"data row9 col6\" >0.0517</td>\n", "      <td id=\"T_ca553_row9_col7\" class=\"data row9 col7\" >0.8192</td>\n", "      <td id=\"T_ca553_row9_col8\" class=\"data row9 col8\" >0.3867</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row10\" class=\"row_heading level0 row10\" >br_cds_dt</th>\n", "      <td id=\"T_ca553_row10_col0\" class=\"data row10 col0\" >Bayesian Ridge w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row10_col1\" class=\"data row10 col1\" >0.7112</td>\n", "      <td id=\"T_ca553_row10_col2\" class=\"data row10 col2\" >0.7837</td>\n", "      <td id=\"T_ca553_row10_col3\" class=\"data row10 col3\" >20.9213</td>\n", "      <td id=\"T_ca553_row10_col4\" class=\"data row10 col4\" >25.8795</td>\n", "      <td id=\"T_ca553_row10_col5\" class=\"data row10 col5\" >0.0515</td>\n", "      <td id=\"T_ca553_row10_col6\" class=\"data row10 col6\" >0.0521</td>\n", "      <td id=\"T_ca553_row10_col7\" class=\"data row10 col7\" >0.8144</td>\n", "      <td id=\"T_ca553_row10_col8\" class=\"data row10 col8\" >0.3367</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row11\" class=\"row_heading level0 row11\" >stlf</th>\n", "      <td id=\"T_ca553_row11_col0\" class=\"data row11 col0\" >STLF</td>\n", "      <td id=\"T_ca553_row11_col1\" class=\"data row11 col1\" >0.7133</td>\n", "      <td id=\"T_ca553_row11_col2\" class=\"data row11 col2\" >0.7059</td>\n", "      <td id=\"T_ca553_row11_col3\" class=\"data row11 col3\" >21.0007</td>\n", "      <td id=\"T_ca553_row11_col4\" class=\"data row11 col4\" >23.3522</td>\n", "      <td id=\"T_ca553_row11_col5\" class=\"data row11 col5\" >0.0530</td>\n", "      <td id=\"T_ca553_row11_col6\" class=\"data row11 col6\" >0.0531</td>\n", "      <td id=\"T_ca553_row11_col7\" class=\"data row11 col7\" >0.8527</td>\n", "      <td id=\"T_ca553_row11_col8\" class=\"data row11 col8\" >0.0367</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row12\" class=\"row_heading level0 row12\" >knn_cds_dt</th>\n", "      <td id=\"T_ca553_row12_col0\" class=\"data row12 col0\" >K Neighbors w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row12_col1\" class=\"data row12 col1\" >0.7162</td>\n", "      <td id=\"T_ca553_row12_col2\" class=\"data row12 col2\" >0.8157</td>\n", "      <td id=\"T_ca553_row12_col3\" class=\"data row12 col3\" >21.1613</td>\n", "      <td id=\"T_ca553_row12_col4\" class=\"data row12 col4\" >26.9700</td>\n", "      <td id=\"T_ca553_row12_col5\" class=\"data row12 col5\" >0.0521</td>\n", "      <td id=\"T_ca553_row12_col6\" class=\"data row12 col6\" >0.0529</td>\n", "      <td id=\"T_ca553_row12_col7\" class=\"data row12 col7\" >0.7811</td>\n", "      <td id=\"T_ca553_row12_col8\" class=\"data row12 col8\" >0.2867</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row13\" class=\"row_heading level0 row13\" >auto_arima</th>\n", "      <td id=\"T_ca553_row13_col0\" class=\"data row13 col0\" >Auto ARIMA</td>\n", "      <td id=\"T_ca553_row13_col1\" class=\"data row13 col1\" >0.7181</td>\n", "      <td id=\"T_ca553_row13_col2\" class=\"data row13 col2\" >0.7114</td>\n", "      <td id=\"T_ca553_row13_col3\" class=\"data row13 col3\" >21.0297</td>\n", "      <td id=\"T_ca553_row13_col4\" class=\"data row13 col4\" >23.4661</td>\n", "      <td id=\"T_ca553_row13_col5\" class=\"data row13 col5\" >0.0525</td>\n", "      <td id=\"T_ca553_row13_col6\" class=\"data row13 col6\" >0.0531</td>\n", "      <td id=\"T_ca553_row13_col7\" class=\"data row13 col7\" >0.8509</td>\n", "      <td id=\"T_ca553_row13_col8\" class=\"data row13 col8\" >2.6633</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row14\" class=\"row_heading level0 row14\" >rf_cds_dt</th>\n", "      <td id=\"T_ca553_row14_col0\" class=\"data row14 col0\" >Random Forest w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row14_col1\" class=\"data row14 col1\" >0.7848</td>\n", "      <td id=\"T_ca553_row14_col2\" class=\"data row14 col2\" >0.8887</td>\n", "      <td id=\"T_ca553_row14_col3\" class=\"data row14 col3\" >23.0593</td>\n", "      <td id=\"T_ca553_row14_col4\" class=\"data row14 col4\" >29.3081</td>\n", "      <td id=\"T_ca553_row14_col5\" class=\"data row14 col5\" >0.0553</td>\n", "      <td id=\"T_ca553_row14_col6\" class=\"data row14 col6\" >0.0565</td>\n", "      <td id=\"T_ca553_row14_col7\" class=\"data row14 col7\" >0.7655</td>\n", "      <td id=\"T_ca553_row14_col8\" class=\"data row14 col8\" >0.4600</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row15\" class=\"row_heading level0 row15\" >gbr_cds_dt</th>\n", "      <td id=\"T_ca553_row15_col0\" class=\"data row15 col0\" >Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row15_col1\" class=\"data row15 col1\" >0.7892</td>\n", "      <td id=\"T_ca553_row15_col2\" class=\"data row15 col2\" >0.9185</td>\n", "      <td id=\"T_ca553_row15_col3\" class=\"data row15 col3\" >23.2026</td>\n", "      <td id=\"T_ca553_row15_col4\" class=\"data row15 col4\" >30.3044</td>\n", "      <td id=\"T_ca553_row15_col5\" class=\"data row15 col5\" >0.0563</td>\n", "      <td id=\"T_ca553_row15_col6\" class=\"data row15 col6\" >0.0571</td>\n", "      <td id=\"T_ca553_row15_col7\" class=\"data row15 col7\" >0.7515</td>\n", "      <td id=\"T_ca553_row15_col8\" class=\"data row15 col8\" >0.3000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row16\" class=\"row_heading level0 row16\" >dt_cds_dt</th>\n", "      <td id=\"T_ca553_row16_col0\" class=\"data row16 col0\" >Decision Tree w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row16_col1\" class=\"data row16 col1\" >0.7930</td>\n", "      <td id=\"T_ca553_row16_col2\" class=\"data row16 col2\" >0.8759</td>\n", "      <td id=\"T_ca553_row16_col3\" class=\"data row16 col3\" >23.3852</td>\n", "      <td id=\"T_ca553_row16_col4\" class=\"data row16 col4\" >29.0221</td>\n", "      <td id=\"T_ca553_row16_col5\" class=\"data row16 col5\" >0.0566</td>\n", "      <td id=\"T_ca553_row16_col6\" class=\"data row16 col6\" >0.0571</td>\n", "      <td id=\"T_ca553_row16_col7\" class=\"data row16 col7\" >0.7698</td>\n", "      <td id=\"T_ca553_row16_col8\" class=\"data row16 col8\" >0.2467</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row17\" class=\"row_heading level0 row17\" >ada_cds_dt</th>\n", "      <td id=\"T_ca553_row17_col0\" class=\"data row17 col0\" >AdaBoost w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row17_col1\" class=\"data row17 col1\" >0.8058</td>\n", "      <td id=\"T_ca553_row17_col2\" class=\"data row17 col2\" >0.9723</td>\n", "      <td id=\"T_ca553_row17_col3\" class=\"data row17 col3\" >23.7210</td>\n", "      <td id=\"T_ca553_row17_col4\" class=\"data row17 col4\" >32.0863</td>\n", "      <td id=\"T_ca553_row17_col5\" class=\"data row17 col5\" >0.0565</td>\n", "      <td id=\"T_ca553_row17_col6\" class=\"data row17 col6\" >0.0582</td>\n", "      <td id=\"T_ca553_row17_col7\" class=\"data row17 col7\" >0.7012</td>\n", "      <td id=\"T_ca553_row17_col8\" class=\"data row17 col8\" >0.3300</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row18\" class=\"row_heading level0 row18\" >lightgbm_cds_dt</th>\n", "      <td id=\"T_ca553_row18_col0\" class=\"data row18 col0\" >Light Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row18_col1\" class=\"data row18 col1\" >0.8156</td>\n", "      <td id=\"T_ca553_row18_col2\" class=\"data row18 col2\" >0.9117</td>\n", "      <td id=\"T_ca553_row18_col3\" class=\"data row18 col3\" >24.0002</td>\n", "      <td id=\"T_ca553_row18_col4\" class=\"data row18 col4\" >30.0956</td>\n", "      <td id=\"T_ca553_row18_col5\" class=\"data row18 col5\" >0.0575</td>\n", "      <td id=\"T_ca553_row18_col6\" class=\"data row18 col6\" >0.0587</td>\n", "      <td id=\"T_ca553_row18_col7\" class=\"data row18 col7\" >0.7561</td>\n", "      <td id=\"T_ca553_row18_col8\" class=\"data row18 col8\" >0.4233</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row19\" class=\"row_heading level0 row19\" >theta</th>\n", "      <td id=\"T_ca553_row19_col0\" class=\"data row19 col0\" >Theta Forecaster</td>\n", "      <td id=\"T_ca553_row19_col1\" class=\"data row19 col1\" >0.9729</td>\n", "      <td id=\"T_ca553_row19_col2\" class=\"data row19 col2\" >1.0306</td>\n", "      <td id=\"T_ca553_row19_col3\" class=\"data row19 col3\" >28.3192</td>\n", "      <td id=\"T_ca553_row19_col4\" class=\"data row19 col4\" >33.8639</td>\n", "      <td id=\"T_ca553_row19_col5\" class=\"data row19 col5\" >0.0670</td>\n", "      <td id=\"T_ca553_row19_col6\" class=\"data row19 col6\" >0.0700</td>\n", "      <td id=\"T_ca553_row19_col7\" class=\"data row19 col7\" >0.6710</td>\n", "      <td id=\"T_ca553_row19_col8\" class=\"data row19 col8\" >0.0333</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row20\" class=\"row_heading level0 row20\" >omp_cds_dt</th>\n", "      <td id=\"T_ca553_row20_col0\" class=\"data row20 col0\" >Orthogonal Matching Pursuit w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_ca553_row20_col1\" class=\"data row20 col1\" >1.0090</td>\n", "      <td id=\"T_ca553_row20_col2\" class=\"data row20 col2\" >1.2370</td>\n", "      <td id=\"T_ca553_row20_col3\" class=\"data row20 col3\" >29.6294</td>\n", "      <td id=\"T_ca553_row20_col4\" class=\"data row20 col4\" >40.8121</td>\n", "      <td id=\"T_ca553_row20_col5\" class=\"data row20 col5\" >0.0685</td>\n", "      <td id=\"T_ca553_row20_col6\" class=\"data row20 col6\" >0.0718</td>\n", "      <td id=\"T_ca553_row20_col7\" class=\"data row20 col7\" >0.5462</td>\n", "      <td id=\"T_ca553_row20_col8\" class=\"data row20 col8\" >0.3500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row21\" class=\"row_heading level0 row21\" >snaive</th>\n", "      <td id=\"T_ca553_row21_col0\" class=\"data row21 col0\" >Seasonal Naive Forecaster</td>\n", "      <td id=\"T_ca553_row21_col1\" class=\"data row21 col1\" >1.1479</td>\n", "      <td id=\"T_ca553_row21_col2\" class=\"data row21 col2\" >1.0945</td>\n", "      <td id=\"T_ca553_row21_col3\" class=\"data row21 col3\" >33.3611</td>\n", "      <td id=\"T_ca553_row21_col4\" class=\"data row21 col4\" >35.9139</td>\n", "      <td id=\"T_ca553_row21_col5\" class=\"data row21 col5\" >0.0832</td>\n", "      <td id=\"T_ca553_row21_col6\" class=\"data row21 col6\" >0.0879</td>\n", "      <td id=\"T_ca553_row21_col7\" class=\"data row21 col7\" >0.6072</td>\n", "      <td id=\"T_ca553_row21_col8\" class=\"data row21 col8\" >1.3700</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row22\" class=\"row_heading level0 row22\" >polytrend</th>\n", "      <td id=\"T_ca553_row22_col0\" class=\"data row22 col0\" >Polynomial Trend Forecaster</td>\n", "      <td id=\"T_ca553_row22_col1\" class=\"data row22 col1\" >1.6523</td>\n", "      <td id=\"T_ca553_row22_col2\" class=\"data row22 col2\" >1.9202</td>\n", "      <td id=\"T_ca553_row22_col3\" class=\"data row22 col3\" >48.6301</td>\n", "      <td id=\"T_ca553_row22_col4\" class=\"data row22 col4\" >63.4299</td>\n", "      <td id=\"T_ca553_row22_col5\" class=\"data row22 col5\" >0.1170</td>\n", "      <td id=\"T_ca553_row22_col6\" class=\"data row22 col6\" >0.1216</td>\n", "      <td id=\"T_ca553_row22_col7\" class=\"data row22 col7\" >-0.0784</td>\n", "      <td id=\"T_ca553_row22_col8\" class=\"data row22 col8\" >0.0167</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row23\" class=\"row_heading level0 row23\" >croston</th>\n", "      <td id=\"T_ca553_row23_col0\" class=\"data row23 col0\" ><PERSON><PERSON><PERSON></td>\n", "      <td id=\"T_ca553_row23_col1\" class=\"data row23 col1\" >1.9311</td>\n", "      <td id=\"T_ca553_row23_col2\" class=\"data row23 col2\" >2.3517</td>\n", "      <td id=\"T_ca553_row23_col3\" class=\"data row23 col3\" >56.6180</td>\n", "      <td id=\"T_ca553_row23_col4\" class=\"data row23 col4\" >77.5856</td>\n", "      <td id=\"T_ca553_row23_col5\" class=\"data row23 col5\" >0.1295</td>\n", "      <td id=\"T_ca553_row23_col6\" class=\"data row23 col6\" >0.1439</td>\n", "      <td id=\"T_ca553_row23_col7\" class=\"data row23 col7\" >-0.6281</td>\n", "      <td id=\"T_ca553_row23_col8\" class=\"data row23 col8\" >0.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row24\" class=\"row_heading level0 row24\" >naive</th>\n", "      <td id=\"T_ca553_row24_col0\" class=\"data row24 col0\" >Naive Forecaster</td>\n", "      <td id=\"T_ca553_row24_col1\" class=\"data row24 col1\" >2.3599</td>\n", "      <td id=\"T_ca553_row24_col2\" class=\"data row24 col2\" >2.7612</td>\n", "      <td id=\"T_ca553_row24_col3\" class=\"data row24 col3\" >69.0278</td>\n", "      <td id=\"T_ca553_row24_col4\" class=\"data row24 col4\" >91.0322</td>\n", "      <td id=\"T_ca553_row24_col5\" class=\"data row24 col5\" >0.1569</td>\n", "      <td id=\"T_ca553_row24_col6\" class=\"data row24 col6\" >0.1792</td>\n", "      <td id=\"T_ca553_row24_col7\" class=\"data row24 col7\" >-1.2216</td>\n", "      <td id=\"T_ca553_row24_col8\" class=\"data row24 col8\" >2.4633</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ca553_level0_row25\" class=\"row_heading level0 row25\" >grand_means</th>\n", "      <td id=\"T_ca553_row25_col0\" class=\"data row25 col0\" >Grand Means Forecaster</td>\n", "      <td id=\"T_ca553_row25_col1\" class=\"data row25 col1\" >5.5306</td>\n", "      <td id=\"T_ca553_row25_col2\" class=\"data row25 col2\" >5.2596</td>\n", "      <td id=\"T_ca553_row25_col3\" class=\"data row25 col3\" >162.4117</td>\n", "      <td id=\"T_ca553_row25_col4\" class=\"data row25 col4\" >173.6492</td>\n", "      <td id=\"T_ca553_row25_col5\" class=\"data row25 col5\" >0.4000</td>\n", "      <td id=\"T_ca553_row25_col6\" class=\"data row25 col6\" >0.5075</td>\n", "      <td id=\"T_ca553_row25_col7\" class=\"data row25 col7\" >-7.0462</td>\n", "      <td id=\"T_ca553_row25_col8\" class=\"data row25 col8\" >1.5933</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f77c192e0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[ExponentialSmoothing(seasonal='mul', sp=12, trend='add'),\n", " AutoETS(seasonal='mul', sp=12, trend='add'),\n", " BaseCdsDtForecaster(fe_target_rr=[WindowSummarizer(lag_feature={'lag': [12, 11,\n", "                                                                         10, 9,\n", "                                                                         8, 7, 6,\n", "                                                                         5, 4, 3,\n", "                                                                         2, 1]},\n", "                                                    n_jobs=1)],\n", "                     regressor=ExtraTreesRegressor(n_jobs=-1, random_state=42),\n", "                     sp=12, window_length=12)]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the 3 best baseline models \n", "best_baseline_models = exp.compare_models(n_select=3)\n", "best_baseline_models"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# We will save the metrics to be used in a later step.\n", "compare_metrics = exp.pull()\n", "# compare_metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Note that some models like BATS and TBATS are disabled by default. \n", "* You can enable them by setting `turbo = False`\n", "\n", "`best_baseline_models = exp.compare_models(n_select=3, turbo=False)`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON>ne Best Models"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_e806d_row3_col0, #T_e806d_row3_col1, #T_e806d_row3_col2, #T_e806d_row3_col3, #T_e806d_row3_col4, #T_e806d_row3_col5, #T_e806d_row3_col6, #T_e806d_row3_col7 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_e806d\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_e806d_level0_col0\" class=\"col_heading level0 col0\" >cutoff</th>\n", "      <th id=\"T_e806d_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_e806d_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_e806d_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_e806d_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_e806d_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_e806d_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_e806d_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_e806d_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_e806d_row0_col0\" class=\"data row0 col0\" >1956-12</td>\n", "      <td id=\"T_e806d_row0_col1\" class=\"data row0 col1\" >0.3617</td>\n", "      <td id=\"T_e806d_row0_col2\" class=\"data row0 col2\" >0.4124</td>\n", "      <td id=\"T_e806d_row0_col3\" class=\"data row0 col3\" >10.5620</td>\n", "      <td id=\"T_e806d_row0_col4\" class=\"data row0 col4\" >13.4978</td>\n", "      <td id=\"T_e806d_row0_col5\" class=\"data row0 col5\" >0.0272</td>\n", "      <td id=\"T_e806d_row0_col6\" class=\"data row0 col6\" >0.0273</td>\n", "      <td id=\"T_e806d_row0_col7\" class=\"data row0 col7\" >0.9407</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e806d_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_e806d_row1_col0\" class=\"data row1 col0\" >1957-12</td>\n", "      <td id=\"T_e806d_row1_col1\" class=\"data row1 col1\" >0.8588</td>\n", "      <td id=\"T_e806d_row1_col2\" class=\"data row1 col2\" >0.8856</td>\n", "      <td id=\"T_e806d_row1_col3\" class=\"data row1 col3\" >26.2572</td>\n", "      <td id=\"T_e806d_row1_col4\" class=\"data row1 col4\" >30.0651</td>\n", "      <td id=\"T_e806d_row1_col5\" class=\"data row1 col5\" >0.0738</td>\n", "      <td id=\"T_e806d_row1_col6\" class=\"data row1 col6\" >0.0703</td>\n", "      <td id=\"T_e806d_row1_col7\" class=\"data row1 col7\" >0.7632</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e806d_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_e806d_row2_col0\" class=\"data row2 col0\" >1958-12</td>\n", "      <td id=\"T_e806d_row2_col1\" class=\"data row2 col1\" >0.3942</td>\n", "      <td id=\"T_e806d_row2_col2\" class=\"data row2 col2\" >0.4126</td>\n", "      <td id=\"T_e806d_row2_col3\" class=\"data row2 col3\" >11.2644</td>\n", "      <td id=\"T_e806d_row2_col4\" class=\"data row2 col4\" >13.4112</td>\n", "      <td id=\"T_e806d_row2_col5\" class=\"data row2 col5\" >0.0261</td>\n", "      <td id=\"T_e806d_row2_col6\" class=\"data row2 col6\" >0.0265</td>\n", "      <td id=\"T_e806d_row2_col7\" class=\"data row2 col7\" >0.9598</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e806d_level0_row3\" class=\"row_heading level0 row3\" >Mean</th>\n", "      <td id=\"T_e806d_row3_col0\" class=\"data row3 col0\" >NaT</td>\n", "      <td id=\"T_e806d_row3_col1\" class=\"data row3 col1\" >0.5382</td>\n", "      <td id=\"T_e806d_row3_col2\" class=\"data row3 col2\" >0.5702</td>\n", "      <td id=\"T_e806d_row3_col3\" class=\"data row3 col3\" >16.0279</td>\n", "      <td id=\"T_e806d_row3_col4\" class=\"data row3 col4\" >18.9914</td>\n", "      <td id=\"T_e806d_row3_col5\" class=\"data row3 col5\" >0.0424</td>\n", "      <td id=\"T_e806d_row3_col6\" class=\"data row3 col6\" >0.0414</td>\n", "      <td id=\"T_e806d_row3_col7\" class=\"data row3 col7\" >0.8879</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e806d_level0_row4\" class=\"row_heading level0 row4\" >SD</th>\n", "      <td id=\"T_e806d_row4_col0\" class=\"data row4 col0\" >NaT</td>\n", "      <td id=\"T_e806d_row4_col1\" class=\"data row4 col1\" >0.2271</td>\n", "      <td id=\"T_e806d_row4_col2\" class=\"data row4 col2\" >0.2230</td>\n", "      <td id=\"T_e806d_row4_col3\" class=\"data row4 col3\" >7.2389</td>\n", "      <td id=\"T_e806d_row4_col4\" class=\"data row4 col4\" >7.8304</td>\n", "      <td id=\"T_e806d_row4_col5\" class=\"data row4 col5\" >0.0222</td>\n", "      <td id=\"T_e806d_row4_col6\" class=\"data row4 col6\" >0.0205</td>\n", "      <td id=\"T_e806d_row4_col7\" class=\"data row4 col7\" >0.0885</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f775030d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Fitting 3 folds for each of 10 candidates, totalling 30 fits\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  30 out of  30 | elapsed:    1.7s finished\n"]}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_11738_row3_col0, #T_11738_row3_col1, #T_11738_row3_col2, #T_11738_row3_col3, #T_11738_row3_col4, #T_11738_row3_col5, #T_11738_row3_col6, #T_11738_row3_col7 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_11738\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_11738_level0_col0\" class=\"col_heading level0 col0\" >cutoff</th>\n", "      <th id=\"T_11738_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_11738_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_11738_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_11738_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_11738_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_11738_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_11738_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_11738_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_11738_row0_col0\" class=\"data row0 col0\" >1956-12</td>\n", "      <td id=\"T_11738_row0_col1\" class=\"data row0 col1\" >0.3641</td>\n", "      <td id=\"T_11738_row0_col2\" class=\"data row0 col2\" >0.3969</td>\n", "      <td id=\"T_11738_row0_col3\" class=\"data row0 col3\" >10.6335</td>\n", "      <td id=\"T_11738_row0_col4\" class=\"data row0 col4\" >12.9923</td>\n", "      <td id=\"T_11738_row0_col5\" class=\"data row0 col5\" >0.0280</td>\n", "      <td id=\"T_11738_row0_col6\" class=\"data row0 col6\" >0.0281</td>\n", "      <td id=\"T_11738_row0_col7\" class=\"data row0 col7\" >0.9451</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_11738_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_11738_row1_col0\" class=\"data row1 col0\" >1957-12</td>\n", "      <td id=\"T_11738_row1_col1\" class=\"data row1 col1\" >1.0557</td>\n", "      <td id=\"T_11738_row1_col2\" class=\"data row1 col2\" >1.0278</td>\n", "      <td id=\"T_11738_row1_col3\" class=\"data row1 col3\" >32.2760</td>\n", "      <td id=\"T_11738_row1_col4\" class=\"data row1 col4\" >34.8945</td>\n", "      <td id=\"T_11738_row1_col5\" class=\"data row1 col5\" >0.0900</td>\n", "      <td id=\"T_11738_row1_col6\" class=\"data row1 col6\" >0.0854</td>\n", "      <td id=\"T_11738_row1_col7\" class=\"data row1 col7\" >0.6810</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_11738_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_11738_row2_col0\" class=\"data row2 col0\" >1958-12</td>\n", "      <td id=\"T_11738_row2_col1\" class=\"data row2 col1\" >0.4579</td>\n", "      <td id=\"T_11738_row2_col2\" class=\"data row2 col2\" >0.4697</td>\n", "      <td id=\"T_11738_row2_col3\" class=\"data row2 col3\" >13.0839</td>\n", "      <td id=\"T_11738_row2_col4\" class=\"data row2 col4\" >15.2673</td>\n", "      <td id=\"T_11738_row2_col5\" class=\"data row2 col5\" >0.0296</td>\n", "      <td id=\"T_11738_row2_col6\" class=\"data row2 col6\" >0.0302</td>\n", "      <td id=\"T_11738_row2_col7\" class=\"data row2 col7\" >0.9479</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_11738_level0_row3\" class=\"row_heading level0 row3\" >Mean</th>\n", "      <td id=\"T_11738_row3_col0\" class=\"data row3 col0\" >NaT</td>\n", "      <td id=\"T_11738_row3_col1\" class=\"data row3 col1\" >0.6259</td>\n", "      <td id=\"T_11738_row3_col2\" class=\"data row3 col2\" >0.6315</td>\n", "      <td id=\"T_11738_row3_col3\" class=\"data row3 col3\" >18.6645</td>\n", "      <td id=\"T_11738_row3_col4\" class=\"data row3 col4\" >21.0513</td>\n", "      <td id=\"T_11738_row3_col5\" class=\"data row3 col5\" >0.0492</td>\n", "      <td id=\"T_11738_row3_col6\" class=\"data row3 col6\" >0.0479</td>\n", "      <td id=\"T_11738_row3_col7\" class=\"data row3 col7\" >0.8580</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_11738_level0_row4\" class=\"row_heading level0 row4\" >SD</th>\n", "      <td id=\"T_11738_row4_col0\" class=\"data row4 col0\" >NaT</td>\n", "      <td id=\"T_11738_row4_col1\" class=\"data row4 col1\" >0.3063</td>\n", "      <td id=\"T_11738_row4_col2\" class=\"data row4 col2\" >0.2818</td>\n", "      <td id=\"T_11738_row4_col3\" class=\"data row4 col3\" >9.6767</td>\n", "      <td id=\"T_11738_row4_col4\" class=\"data row4 col4\" >9.8325</td>\n", "      <td id=\"T_11738_row4_col5\" class=\"data row4 col5\" >0.0288</td>\n", "      <td id=\"T_11738_row4_col6\" class=\"data row4 col6\" >0.0265</td>\n", "      <td id=\"T_11738_row4_col7\" class=\"data row4 col7\" >0.1251</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f77bd35e0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Fitting 3 folds for each of 10 candidates, totalling 30 fits\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  30 out of  30 | elapsed:    2.3s finished\n"]}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_55efe_row3_col0, #T_55efe_row3_col1, #T_55efe_row3_col2, #T_55efe_row3_col3, #T_55efe_row3_col4, #T_55efe_row3_col5, #T_55efe_row3_col6, #T_55efe_row3_col7 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_55efe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_55efe_level0_col0\" class=\"col_heading level0 col0\" >cutoff</th>\n", "      <th id=\"T_55efe_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_55efe_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_55efe_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_55efe_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_55efe_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_55efe_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_55efe_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_55efe_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_55efe_row0_col0\" class=\"data row0 col0\" >1956-12</td>\n", "      <td id=\"T_55efe_row0_col1\" class=\"data row0 col1\" >1.0549</td>\n", "      <td id=\"T_55efe_row0_col2\" class=\"data row0 col2\" >1.2622</td>\n", "      <td id=\"T_55efe_row0_col3\" class=\"data row0 col3\" >30.8068</td>\n", "      <td id=\"T_55efe_row0_col4\" class=\"data row0 col4\" >41.3148</td>\n", "      <td id=\"T_55efe_row0_col5\" class=\"data row0 col5\" >0.0755</td>\n", "      <td id=\"T_55efe_row0_col6\" class=\"data row0 col6\" >0.0801</td>\n", "      <td id=\"T_55efe_row0_col7\" class=\"data row0 col7\" >0.4444</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_55efe_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_55efe_row1_col0\" class=\"data row1 col0\" >1957-12</td>\n", "      <td id=\"T_55efe_row1_col1\" class=\"data row1 col1\" >0.7417</td>\n", "      <td id=\"T_55efe_row1_col2\" class=\"data row1 col2\" >0.8045</td>\n", "      <td id=\"T_55efe_row1_col3\" class=\"data row1 col3\" >22.6769</td>\n", "      <td id=\"T_55efe_row1_col4\" class=\"data row1 col4\" >27.3108</td>\n", "      <td id=\"T_55efe_row1_col5\" class=\"data row1 col5\" >0.0589</td>\n", "      <td id=\"T_55efe_row1_col6\" class=\"data row1 col6\" >0.0582</td>\n", "      <td id=\"T_55efe_row1_col7\" class=\"data row1 col7\" >0.8046</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_55efe_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_55efe_row2_col0\" class=\"data row2 col0\" >1958-12</td>\n", "      <td id=\"T_55efe_row2_col1\" class=\"data row2 col1\" >0.7252</td>\n", "      <td id=\"T_55efe_row2_col2\" class=\"data row2 col2\" >0.8562</td>\n", "      <td id=\"T_55efe_row2_col3\" class=\"data row2 col3\" >20.7230</td>\n", "      <td id=\"T_55efe_row2_col4\" class=\"data row2 col4\" >27.8322</td>\n", "      <td id=\"T_55efe_row2_col5\" class=\"data row2 col5\" >0.0448</td>\n", "      <td id=\"T_55efe_row2_col6\" class=\"data row2 col6\" >0.0460</td>\n", "      <td id=\"T_55efe_row2_col7\" class=\"data row2 col7\" >0.8267</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_55efe_level0_row3\" class=\"row_heading level0 row3\" >Mean</th>\n", "      <td id=\"T_55efe_row3_col0\" class=\"data row3 col0\" >NaT</td>\n", "      <td id=\"T_55efe_row3_col1\" class=\"data row3 col1\" >0.8406</td>\n", "      <td id=\"T_55efe_row3_col2\" class=\"data row3 col2\" >0.9743</td>\n", "      <td id=\"T_55efe_row3_col3\" class=\"data row3 col3\" >24.7356</td>\n", "      <td id=\"T_55efe_row3_col4\" class=\"data row3 col4\" >32.1526</td>\n", "      <td id=\"T_55efe_row3_col5\" class=\"data row3 col5\" >0.0597</td>\n", "      <td id=\"T_55efe_row3_col6\" class=\"data row3 col6\" >0.0614</td>\n", "      <td id=\"T_55efe_row3_col7\" class=\"data row3 col7\" >0.6919</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_55efe_level0_row4\" class=\"row_heading level0 row4\" >SD</th>\n", "      <td id=\"T_55efe_row4_col0\" class=\"data row4 col0\" >NaT</td>\n", "      <td id=\"T_55efe_row4_col1\" class=\"data row4 col1\" >0.1517</td>\n", "      <td id=\"T_55efe_row4_col2\" class=\"data row4 col2\" >0.2047</td>\n", "      <td id=\"T_55efe_row4_col3\" class=\"data row4 col3\" >4.3665</td>\n", "      <td id=\"T_55efe_row4_col4\" class=\"data row4 col4\" >6.4822</td>\n", "      <td id=\"T_55efe_row4_col5\" class=\"data row4 col5\" >0.0125</td>\n", "      <td id=\"T_55efe_row4_col6\" class=\"data row4 col6\" >0.0141</td>\n", "      <td id=\"T_55efe_row4_col7\" class=\"data row4 col7\" >0.1753</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f77b6de50>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Fitting 3 folds for each of 10 candidates, totalling 30 fits\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  30 out of  30 | elapsed:   10.7s finished\n"]}, {"data": {"text/plain": ["[ExponentialSmoothing(seasonal='add', sp=12, trend='add', use_boxcox=True),\n", " AutoETS(seasonal='mul', sp=12, trend='add'),\n", " BaseCdsDtForecaster(fe_target_rr=[WindowSummarizer(lag_feature={'lag': [12, 11,\n", "                                                                         10, 9,\n", "                                                                         8, 7, 6,\n", "                                                                         5, 4, 3,\n", "                                                                         2, 1]},\n", "                                                    n_jobs=1)],\n", "                     regressor=ExtraTreesRegressor(n_jobs=-1, random_state=42),\n", "                     sp=12, window_length=12)]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["best_tuned_models = [exp.tune_model(model) for model in best_baseline_models]\n", "best_tuned_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Blend Best Models\n", "\n", "We can achieve even better results sometimes if we combine results of several good models. This can be achieved using the `blend_model` functionality. There are several options available to blend such as the `mean`, `gmean` `median`, `min`, `max`. In addition, weights can be applied to the forecasts from the base learners. This is useful when we want to give more importance (weight) to models with a lower error for example. Please refer to the `blend_models` docstring for more information about the blending functionality."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# help(exp.blend_models)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see the voting blender in action."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["exp_smooth    17.1926\n", "ets           17.4165\n", "et_cds_dt      19.662\n", "Name: MAE, dtype: object"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["exp_smooth    0.683209\n", "ets           0.679083\n", "et_cds_dt     0.637708\n", "Name: MAE, dtype: object"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get model weights to use\n", "top_model_metrics = compare_metrics.iloc[0:3]['MAE']\n", "display(top_model_metrics)\n", "\n", "top_model_weights = 1 - top_model_metrics/top_model_metrics.sum()\n", "display(top_model_weights)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_87dbd_row3_col0, #T_87dbd_row3_col1, #T_87dbd_row3_col2, #T_87dbd_row3_col3, #T_87dbd_row3_col4, #T_87dbd_row3_col5, #T_87dbd_row3_col6, #T_87dbd_row3_col7 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_87dbd\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_87dbd_level0_col0\" class=\"col_heading level0 col0\" >cutoff</th>\n", "      <th id=\"T_87dbd_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_87dbd_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_87dbd_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_87dbd_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_87dbd_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_87dbd_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_87dbd_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_87dbd_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_87dbd_row0_col0\" class=\"data row0 col0\" >1956-12</td>\n", "      <td id=\"T_87dbd_row0_col1\" class=\"data row0 col1\" >0.3703</td>\n", "      <td id=\"T_87dbd_row0_col2\" class=\"data row0 col2\" >0.4756</td>\n", "      <td id=\"T_87dbd_row0_col3\" class=\"data row0 col3\" >10.8141</td>\n", "      <td id=\"T_87dbd_row0_col4\" class=\"data row0 col4\" >15.5674</td>\n", "      <td id=\"T_87dbd_row0_col5\" class=\"data row0 col5\" >0.0264</td>\n", "      <td id=\"T_87dbd_row0_col6\" class=\"data row0 col6\" >0.0269</td>\n", "      <td id=\"T_87dbd_row0_col7\" class=\"data row0 col7\" >0.9211</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_87dbd_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_87dbd_row1_col0\" class=\"data row1 col0\" >1957-12</td>\n", "      <td id=\"T_87dbd_row1_col1\" class=\"data row1 col1\" >0.7174</td>\n", "      <td id=\"T_87dbd_row1_col2\" class=\"data row1 col2\" >0.7209</td>\n", "      <td id=\"T_87dbd_row1_col3\" class=\"data row1 col3\" >21.9328</td>\n", "      <td id=\"T_87dbd_row1_col4\" class=\"data row1 col4\" >24.4724</td>\n", "      <td id=\"T_87dbd_row1_col5\" class=\"data row1 col5\" >0.0609</td>\n", "      <td id=\"T_87dbd_row1_col6\" class=\"data row1 col6\" >0.0587</td>\n", "      <td id=\"T_87dbd_row1_col7\" class=\"data row1 col7\" >0.8431</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_87dbd_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_87dbd_row2_col0\" class=\"data row2 col0\" >1958-12</td>\n", "      <td id=\"T_87dbd_row2_col1\" class=\"data row2 col1\" >0.5437</td>\n", "      <td id=\"T_87dbd_row2_col2\" class=\"data row2 col2\" >0.5535</td>\n", "      <td id=\"T_87dbd_row2_col3\" class=\"data row2 col3\" >15.5368</td>\n", "      <td id=\"T_87dbd_row2_col4\" class=\"data row2 col4\" >17.9920</td>\n", "      <td id=\"T_87dbd_row2_col5\" class=\"data row2 col5\" >0.0353</td>\n", "      <td id=\"T_87dbd_row2_col6\" class=\"data row2 col6\" >0.0362</td>\n", "      <td id=\"T_87dbd_row2_col7\" class=\"data row2 col7\" >0.9276</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_87dbd_level0_row3\" class=\"row_heading level0 row3\" >Mean</th>\n", "      <td id=\"T_87dbd_row3_col0\" class=\"data row3 col0\" >NaT</td>\n", "      <td id=\"T_87dbd_row3_col1\" class=\"data row3 col1\" >0.5438</td>\n", "      <td id=\"T_87dbd_row3_col2\" class=\"data row3 col2\" >0.5833</td>\n", "      <td id=\"T_87dbd_row3_col3\" class=\"data row3 col3\" >16.0946</td>\n", "      <td id=\"T_87dbd_row3_col4\" class=\"data row3 col4\" >19.3439</td>\n", "      <td id=\"T_87dbd_row3_col5\" class=\"data row3 col5\" >0.0409</td>\n", "      <td id=\"T_87dbd_row3_col6\" class=\"data row3 col6\" >0.0406</td>\n", "      <td id=\"T_87dbd_row3_col7\" class=\"data row3 col7\" >0.8973</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_87dbd_level0_row4\" class=\"row_heading level0 row4\" >SD</th>\n", "      <td id=\"T_87dbd_row4_col0\" class=\"data row4 col0\" >NaT</td>\n", "      <td id=\"T_87dbd_row4_col1\" class=\"data row4 col1\" >0.1417</td>\n", "      <td id=\"T_87dbd_row4_col2\" class=\"data row4 col2\" >0.1023</td>\n", "      <td id=\"T_87dbd_row4_col3\" class=\"data row4 col3\" >4.5563</td>\n", "      <td id=\"T_87dbd_row4_col4\" class=\"data row4 col4\" >3.7591</td>\n", "      <td id=\"T_87dbd_row4_col5\" class=\"data row4 col5\" >0.0146</td>\n", "      <td id=\"T_87dbd_row4_col6\" class=\"data row4 col6\" >0.0133</td>\n", "      <td id=\"T_87dbd_row4_col7\" class=\"data row4 col7\" >0.0384</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f77b7ad00>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["blender = exp.blend_models(best_tuned_models, method='mean', weights=top_model_weights.values.tolist())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_f960d\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_f960d_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_f960d_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_f960d_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_f960d_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_f960d_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_f960d_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_f960d_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_f960d_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_f960d_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_f960d_row0_col0\" class=\"data row0 col0\" >EnsembleForecaster</td>\n", "      <td id=\"T_f960d_row0_col1\" class=\"data row0 col1\" >0.3310</td>\n", "      <td id=\"T_f960d_row0_col2\" class=\"data row0 col2\" >0.3904</td>\n", "      <td id=\"T_f960d_row0_col3\" class=\"data row0 col3\" >10.0804</td>\n", "      <td id=\"T_f960d_row0_col4\" class=\"data row0 col4\" >13.4888</td>\n", "      <td id=\"T_f960d_row0_col5\" class=\"data row0 col5\" >0.0219</td>\n", "      <td id=\"T_f960d_row0_col6\" class=\"data row0 col6\" >0.0217</td>\n", "      <td id=\"T_f960d_row0_col7\" class=\"data row0 col7\" >0.9672</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27f7ae174f0>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["           y_pred\n", "1960-01  411.8049\n", "1960-02  392.7194\n", "1960-03  453.3590\n", "1960-04  443.1362\n", "1960-05  464.3702\n", "1960-06  531.1468\n", "1960-07  606.6252\n", "1960-08  614.9484\n", "1960-09  508.5177\n", "1960-10  451.2852\n", "1960-11  402.6351\n", "1960-12  435.1540\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA+gAAAJYCAYAAADxHswlAAAgAElEQVR4XuydCZQVxfm33xm2YZRNRcWMCy5RFg2yCAqCa0TFIIgKJIpxQxNFNGjiFwkSTPKPogFNIrhgcAFjUDCg4r4iIOCKKAiiiCwS2ZcBZvlOtenJnctdurveurcHn3uOJzBT9euqp3rIPF3VVQWVlZWVwgcCEIAABCAAAQhAAAIQgAAEIACBvBIoQNDzyp+LQwACEIAABCAAAQhAAAIQgAAEPAIIOjcCBCAAAQhAAAIQgAAEIAABCEAgBgQQ9BgMAk2AAAQgAAEIQAACEIAABCAAAQgg6NwDEIAABCAAAQhAAAIQgAAEIACBGBBA0GMwCDQBAhCAAAQgAAEIQAACEIAABCCAoHMPQAACEIAABCAAAQhAAAIQgAAEYkAAQY/BINAECEAAAhCAAAQgAAEIQAACEIAAgs49AAEIQAACEIAABCAAAQhAAAIQiAEBBD0Gg0ATIAABCEAAAhCAAAQgAAEIQAACCDr3AAQgAAEIQAACEIAABCAAAQhAIAYEEPQYDAJNgAAEIAABCEAAAhCAAAQgAAEIIOjcAxCAAAQgAAEIQAACEIAABCAAgRgQQNBjMAg0AQIQgAAEIAABCEAAAhCAAAQggKBzD0AAAhCAAAQgAAEIQAACEIAABGJAAEGPwSDQBAhAAAIQgAAEIAABCEAAAhCAAILOPQABCEAAAhCAAAQgAAEIQAACEIgBAQQ9BoNAEyAAAQhAAAIQgAAEIAABCEAAAgg69wAEIAABCEAAAhCAAAQgAAEIQCAGBBD0GAwCTYAABCAAAQhAAAIQgAAEIAABCCDo3AMQgAAEIAABCEAAAhCAAAQgAIEYEEDQYzAINAECEIAABCAAAQhAAAIQgAAEIICgcw9AAAIQgAAEIAABCEAAAhCAAARiQABBj8Eg0AQIQAACEIAABCAAAQhAAAIQgACCzj0AAQhAAAIQgAAEIAABCEAAAhCIAQEEPQaDQBMgAAEIQAACEIAABCAAAQhAAAIIOvcABCAAAQhAAAIQgAAEIAABCEAgBgQQ9BgMAk2AAAQgAAEIQAACEIAABCAAAQgg6NwDEIAABCAAAQhAAAIQgAAEIACBGBBA0GMwCDQBAhCAAAQgAAEIQAACEIAABCCAoHMPQAACEIAABCAAAQhAAAIQgAAEYkAAQY/BINAECEAAAhCAAAQgAAEIQAACEIAAgs49AAEIQAACEIAABCAAAQhAAAIQiAEBBD0Gg0ATIAABCEAAAhCAAAQgAAEIQAACCDr3AAQgAAEIQAACEIAABCAAAQhAIAYEEPQYDAJNgAAEIAABCEAAAhCAAAQgAAEIIOjcAxCAAAQgAAEIQAACEIAABCAAgRgQQNBjMAg0AQIQgEDcCTz99NNy//33yz333CPNmzePe3NpX0QCZpxHjBghAwYMkGuvvTZiSjyr+X0zrWvdurWMGjVKGjduHM/GKrXK/LzOmzcvdF+XLl3qjf8+++wTuq5S04mBAAQg8L0lgKB/b4eejkMAAvkiYH5pHj9+vJUk+L9AX3HFFdKzZ0/nXakpgp4oYamgjB07Vtq1a+ecVz4vYCtl6e6p9evXy+DBg2X+/PnVumfLNBf3ctT7N12fu3fvLrfccosUFRXlc6izXjvqvWCC/b6XlJTUiL5mhUEBCEAAAjWEAIJeQwaKZkIAArsHAf+X3gYNGsjMmTMlqtzkQmoSiUcVnFyPWk1pp0suUaSstLRUbrvtNq9ZqcQz3cy6mZ0dOHCg2Air63vZ79t+++0XalWA37fk1QQ1aXY5yr2QeG/6DKL+O+XyPicbAhCAwO5KAEHfXUeWfkEAArEkYH7hHTZsmAwfPtxbLm5mc6MsJXYtNcnwaor41pR2urw5o0hZJhHz77Uzzjgj5b2a7fvZ+ur6Xo4i6NkeWJg+Pf74496DiTgvk49yLySOVxAO2caX70MAAhCAQDgCCHo4XpSGAAQgEJlA8i+75p3uTO+H+tKUeMGhQ4fKMccc44nSqlWrqrXFn+kzkjp58uRd3h31Hw4kvkeeakn4/vvvv8u75kHEN9Mv86m+539t+vTpVf2wmYk1IUHaacqlWrqcPEuYKI5mma+ZKTafxBnV5DFK925zquv5fTWZZvY6kYP5WioW2Zj5r08k3hipxjP5Js4kckEkL7mM399evXpVewUjWZZ9xunu5SA/bMl9ThyfdEvUzc9RpldD/HpBH6Clu07y7HsiF9M3876///HblNyf5Lb69/jtt98uEyZMqLpvUt176cYu+ec+089dqn83gowLZSAAAQhAIBoBBD0aN2pBAAIQCE0geaYw06yl+cX6+eefrybK5pf7Rx55RMw7witXrvQkPdX7wmEF3XQkUVZSSW5Q8U3Xp+S+pxL2sFKUagCCtDPVjK3f7kQZSpTHVJumpRqjVNdPlW3aPm7cODn55JOlWbNm3sOUCy+8sGoDvkwPNExdfxl6KmZBhDqRXSbuQd9D9oXPf8gRVNBNO6LOoGe6hxLfm7aZQTcPTYIs7zb9HTlypAwZMqRqRj0V10SRT7zXEoU5+evJmzP6ZZMfvKQa9+Sv+SyWL19e7QFepnsm6viE/geSChCAAAQg4BFA0LkRIAABCOSIQLK8pZtxDvLeZ6ZfmsMIeqqup5KrIOJrstLJXnKb0rXfMHnrrbfktNNOizQqmTaJM+Jjlmmne9c6uY+ZGPvfM68qJG46l9x/m4cOyTOXQZmFFfRM/Qza/uSHELkQ9HT3ZLqHQWHfQQ86K57pRk2+79NxCfP1oP027Uq+F9LNhme6B6I84Ij0w0slCEAAAhBA0LkHIAABCOSKQDoZTzdbnWqJemJbNQU93TLjbDN56dgl9ynbTGKQGcqg45TtQUI6ETL5yQ9GojD2pWj16tXeLLe/0iFZ5FP1J9vrBonCmIlZVEFP1cZ8CnqqVzwSl3GbfvqcE3dTT25zOsFMxTvdUu9Urw6kW1VhTmhI/CS2OYyIJz7wSnxVIN09HmQ1RTpmmSQcQQ/6rw/lIAABCOgQYAZdhyMpEIAABDISSCfBfqVEGQ4iWFHkMdXsmS8eide3mUE3/Um1lN9sjJd8hnqqGUrb86mzCXqQWXH/tYFMZVMJW+IN4Ivexx9/7L27nkmofQ7/+c9/qjFKNV5BmAW5f1I97Mkk6NmO2sr1Evcw+x2YvppVE2Fn0DP9QPvj749rup3dczmDnkqkE+8F8xAj1V4Hif1M9dABQef/3CAAAQjklgCCnlveXA0CEPieEkgnTalEI90S9VRSZfMOejoBtRV0005/ps68k2vezTWfbOdGaxzZlU3QczGDHlR+/XLp2hxkc65UzKIKerrzz4PkRd0kLtUDnaD/RNjOoAe9TqpyyT87mTZjS1wNE9cZ9EwsEHSbO4W6EIAABMITQNDDM6MGBCAAgVAEsi0TTp59zPQO+ksvvSRdunQR80vz4MGDJXmXbNOwoMKX7joagu5nG0F/9NFHvWPlEt/VNoKzbds2admyZTWWQWQwE/xsgp5p5jXMO+hB9gkw7cw09gsWLJD69evLtGnTUu7mn+od9CDMgjzgSWSY7f7Mdoxaqgc96TJTyV6mhyZRxlrjHXTTJrNBXN++fVM2IXH8W7VqlXFfg1wJeqp9EZJ/nrL9fAR5GBHqHz8KQwACEIBAaAIIemhkVIAABCAQjkA2mUv37mjyLu6JM4amBek2O8u0S3nizs/Z3g2P+g66aVvicWCplq0HlTqfXaqlt6lGIYiApBLOTLu4Z5pZNu8bJy9fN1lvv/121ZnhqbIT5Xvt2rW7LIP36ySOV1hmYd7tz/ZgxH+IlDwOmVY9pNtB3IhvYk7Us7ZT7Uju39O2u7hnet8/1T2Zbi8Jc4xaLt5BT9Vv8/ORbgzef//9lEcpmjrJx88FWckR7l9ESkMAAhCAQCYCCDr3BwQgAAGHBNIda5R8yVSClLyJVbLoJr+PnOl8bvO9E044QZLfBU/OMNe49dZbvf+CbEyVCZ3f/nRnTqfaBCy5rAtBN21O9S53pnPQ052ZnaoPqc4dT7UHQeL1knPSjVcQZqZ/ifdOkHPQsz1ESsfMfD3dg4DkM9tNO/yzu5PfB890L2f78UzeDyD5IULUJdqpzpw3bUnHM/nn1dzL5uNiBj3x/HSfT7pN68zYmmP8GjduXIUy1QZ5qR6kRX14km3M+D4EIAABCKQngKBzd0AAAhCAAAS+5wQQsZpzAwRZJaLVmyAPbrSuRQ4EIAABCHxHAEHnToAABCAAAQhAYJfd90ESTwK5EvRsexPEkw6tggAEIFDzCSDoNX8M6QEEIAABCEBAhUC6d81VwglRIZALQU93bJxKBwiBAAQgAIGMBBB0bhAIQAACEIAABCAAAQhAAAIQgEAMCCDoMRgEmgABCEAAAhCAAAQgAAEIQAACEEDQuQcgAAEIQAACEIAABCAAAQhAAAIxIICgx2AQaAIEIAABCEAAAhCAAAQgAAEIQABB5x6AAAQgAAEIQAACEIAABCAAAQjEgACCHoNBoAkQgAAEIAABCEAAAhCAAAQgAAEEnXsAAhCAAAQgAAEIQAACEIAABCAQAwIIegwGgSZAAAIQgAAEIAABCEAAAhCAAAQQdO4BCEAAAhCAAAQgAAEIQAACEIBADAgg6DEYBJoAAQhAAAIQgAAEIAABCEAAAhBA0LkHIAABCEAAAhCAAAQgAAEIQAACMSCAoMdgEGgCBCAAAQhAAAIQgAAEIAABCEAAQecegAAEIAABCEAAAhCAAAQgAAEIxIAAgh6DQaAJEIAABCAAAQhAAAIQgAAEIAABBJ17AAIQgAAEIAABCEAAAhCAAAQgEAMCCHoMBoEmQAACEIAABCAAAQhAAAIQgAAEEHTuAQhAAAIQgAAEIAABCEAAAhCAQAwIIOgxGASaAAEIQAACEIAABCAAAQhAAAIQQNC5ByAAAQhAAAIQgAAEIAABCEAAAjEggKDHYBBoAgQgAAEIQAACEIAABCAAAQhAAEHnHoAABCAAAQhAAAIQgAAEIAABCMSAAIIeg0GgCRCAAAQgAAEIQAACEIAABCAAAQSdewACEIAABCAAAQhAAAIQgAAEIBADAgh6DAaBJkAAAhCAAAQgAAEIQAACEIAABBB07gEIQAACEIAABCAAAQhAAAIQgEAMCCDoMRgEmgABCEAAAhCAAAQgAAEIQAACEEDQuQcgAAEIQAACEIAABCAAAQhAAAIxIICgx2AQaAIEIAABCEAAAhCAAAQgAAEIQABB5x6AAAQgAAEIQAACEIAABCAAAQjEgACCHoNBoAkQgAAEIAABCEAAAhCAAAQgAAEEnXsAAhCAAAQgAAEIQAACEIAABCAQAwIIegwGgSZAAAIQgAAEIAABCEAAAhCAAAQQdO4BCEAAAhCAAAQgAAEIQAACEIBADAgg6DEYBJoAAQhAAAIQgAAEIAABCEAAAhBA0LkHIAABCEAAAhCAAAQgAAEIQAACMSCAoMdgEGgCBCAAAQhAAAIQgAAEIAABCEAAQecegAAEIAABCEAAAhCAAAQgAAEIxIAAgh6DQaAJEIAABCAAAQhAAAIQgAAEIAABBJ17AAIQgAAEIAABCEAAAhCAAAQgEAMCCHoMBoEmQAACEIAABCAAAQhAAAIQgAAEEHTuAQhAAAIQgAAEIAABCEAAAhCAQAwIIOgxGASaAAEIQAACEIAABCAAAQhAAAIQQNC5ByAAAQhAAAIQgAAEIAABCEAAAjEggKDHYBBoAgQgAAEIQAACEIAABCAAAQhAAEHnHoAABCAAAQhAAAIQgAAEIAABCMSAAIIeg0GgCRCAAAQgAAEIQAACEIAABCAAAQSdewACEIAABCAAAQhAAAIQgAAEIBADAgi65SCsXbvWMoHqqQgUFxdLRUWFlJaWAigGBGrVqiV77rmnbNiwIQatoQmGgBmPHTt2eP/xyT+B2rVri/l3a+PGjflvDC3wCDRo0MD7/5CdO3dCJAYE6tSpI0VFRbJp06YYtKbmN2Gvvfaq+Z2gBxCAQEoCCLrljYGgWwJMUx1Bd8M1aiqCHpWcu3oIuju2UZIR9CjU3NZB0N3yDZuOoIcllrk8gq7LkzQIxIkAgm45Ggi6JUAE3Q1A5VQEXRmoQhyCrgBRMQJBV4SpFIWgK4FUikHQlUD+NwZB1+VJGgTiRABBtxwNBN0SIILuBqByKoKuDFQhDkFXgKgYgaArwlSKQtCVQCrFIOhKIBF0XZCkQSCGBBB0y0FB0C0BIuhuACqnIujKQBXiEHQFiIoRCLoiTKUoBF0JpFIMgq4EEkHXBUkaBGJIAEG3HBQE3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0JVAKsUg6EogEXRdkKRBIIYEEHTLQUHQLQEi6G4AKqci6MpAFeIQdAWIihEIuiJMpSgEXQmkUgyCrgQSQdcFSRoEYkgAQbccFATdEiCC7gagciqCrgxUIQ5BV4CoGIGgK8JUikLQlUAqxSDoSiARdF2QpEEghgQQdMtBQdAtASLobgAqpyLoykAV4hB0BYiKEQi6IkylKARdCaRSDIKuBBJB1wVJGgRiSABBtxwUBN0SIILuBqByKoKuDFQhDkFXgKgYgaArwlSKQtCVQCrFIOhKIBF0VZBLly6VW2+91fuvefPmqtlBwsz1r732Wlm1apUMHTpUevbsGaSaV+aee+7x/tfUT/VJ7FuzZs3ktttukw4dOoS6RuDGUFCVAIJuiRNBtwSIoLsBqJyKoCsDVYhD0BUgKkYg6IowlaIQdCWQSjEIuhLI3VzQ582bJwMHDpTu3bvLLbfcIkVFRVJaWurJZa9evaRdu3aqIPMp6H6/okozgq56K8QqDEG3HA4E3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0JVAKsUg6EogvweC/vDDD3u9vPjiiz0h310Fff369TJ48GBvBlz7wYPhl8+HD7p3+/cvDUG3HHME3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0JVAKsUg6EogYyDoMz//Vm781weyfN02admsodxx/o+k1QENVTpoZtAnT57szaBPnz7dm0U3n8QZ9KefflrmzJlTNcNuRHTUqFEyfPhwr6yRXiO848eP9/5usvr37y833XSTt5Q8cXbel9gWLVrIpEmTvPIDBgyotmzcXG/EiBFVWf7MvpnB3rJli2zevNlra7ol6onL2Pfff39vaXqTJk28ds6fP9/L9b+evMw+sW5y2xJn0P1+nHnmmTJy5Ehp3bq1XH/99XLXXXd5y/eTl7j7DM3vEn6/x44dW+1BQbp+Jw90tqzEHFM38Trp+uc/vPD5JI5Z8vf8vGQG2cbSfN9wMvdO48aNqx4EmbE0H388/euZNjz66KPe9xLZm3sq1bVsfiAQdBt6IoKgWwJMU724uFgqKiq8HxY++SeAoOd/DJJbgKDHa0wQ9HiNh2kNgh6vMUHQdcdjr732Ugl8beEaeW/ZulBZY15fItvLKqrqHLrPHnLOjw4IldHtyH2l7UGNd6njC/qQIUM80TTL2lu1ahVJ0M3MtC9XJSUlntCb3ysTZ619Qbziiiu8d7OTZ7WNXJoHBr7EGTE76KCDvLLmz88//7z3v+neX0/OM/0bNmxYNUnPNIP+0ksvyWGHHebl+201DyLMA4hkQTc5Z5xxRtXDhUzvoPvS7MutaZfJ8/uZqd+pBN08wEiVZV5RmDJlipx77rne6wqJuebviQ9ezNj4Ze+///4qzuZ6jz/+uPdgxXzM+Jn7woxB4sOZdevWeX33GWRib3gm9tlvy3777edlmLpmnMy1/Icp/j2U6rWLxLab79t+EHRLggi6JUAE3Q1A5VQEXRmoQhyCrgBRMQJBV4SpFIWgK4FUikHQlUD+N0ZL0H8/dYGMm7FUt3EB0ob2aCmXddl1UzZf0I1Mf/zxx54cJ8q6EdMgM+i+9Ca/553891TLwH3xNdKevLFaYvuMRJpPuk3azPeSxTfx+t26dQu1xD257alm0BM3u8sm6OlWIfiymvhufGK/kwU0eTwyLd1PbJMvvr5s+7eN309flhNvp3Q8TYb5mUjc8C/51Yjkd/YTs4zcJ28W6D+MSTVOfh+T2x7g1g9UBEEPhCl9IQTdEiCC7gagciqCrgxUIQ5BV4CoGIGgK8JUikLQlUAqxSDoSiCVBT3KDPq9ry+RHY5n0BOXtvvL3f1N4lwLuslftmyZ+ILuL3n2R9Bfbh1U0M1DBn9ZvMnIJH7Jd4kvmYlt8Jdeuxb0dP3OJujJYpy8jD1xOX/y9/xZ+ORl7H6f/U0EkzmZekEE3V/9kPzwxJ9995er+/nmuukepKRru8ZPOoJuSRFBtwSIoLsBqJyKoCsDVYhD0BUgKkYg6IowlaIQdCWQSjEIuhJIZUGP0irzDvqQJz6Qr9dvkxbNGsjI89uov4PuC60RsuRN41wLevIMerrd47Ptop4sgYnvOZvZ6Wwz6MkzybmeQQ+6a36mGXQjzWaFgb8sP9PGdcmz4/69mVjHeFfyA49U5cwS9rAz6P4+BmacEj9BNvNL1/YoP1+mDoIeldx/6yHolgARdDcAlVMRdGWgCnEIugJExQgEXRGmUhSCrgRSKQZBVwIZA0HX7Un1tOSl1IkzyOnelzaibOoZwTKfxHfMwy5xTxbI5Hexk9+TNtfLtMQ9eSl0mHfQk9uenOVqBt0IaqZ+Z5tBTxT2lStXVls6ntx/M0vft29fb9x8yf2///s/ee2116reW8+2LN7UM59sM+jJEp143yS/g27yzHWXLFki7du33+VVBDMWqdruv8Nv+zOCoFsSRNAtASLobgAqpyLoykAV4hB0BYiKEQi6IkylKARdCaRSDIKuBPJ7Jui+uJmz0RN3/zZy5e/Sbt5RnzlzZrVd3MO8g27KJi5tzrSbuWlPqiXmmUY31S7uZoY36Mys6bv5mKXhe++9t/Tu3btqkzr/AUGqmemo76D7M8jJu6+n26U+uVzizuimfYljZTb8M5/EneX9pfSJS98T65jyiWOSvPzdv17ye+SpjudLzDW79a9evbrq9YPk1wmSd9xP3MwvXdl0mwWG/elH0MMSSyqPoFsCRNDdAFRORdCVgSrEIegKEBUjEHRFmEpRCLoSSKUYBF0J5G4u6LqUSMsFgeQl7rm4psY1/L0GMq2A0LhOlAwEPQq1hDoIuiVABN0NQOVUBF0ZqEIcgq4AUTECQVeEqRSFoCuBVIpB0JVAIui6IEmzJlBTBD3T2erWEJQDEHRLoAi6JUAE3Q1A5VQEXRmoQhyCrgBRMQJBV4SpFIWgK4FUismFoM9dtkGWfrtNzm7VVIrr1lJqeTxjtI5Zi2fvaBUEvt8EEHTL8UfQLQEi6G4AKqci6MpAFeIQdAWIihEIuiJMpSgEXQmkUoxrQf/V5IXy6mdrvdbuWa+WjP/Z0dJ87/pKrY9fDIIevzGhRRDQIoCgW5JE0C0BIuhuACqnIujKQBXiEHQFiIoRCLoiTKUoBF0JpFKMS0FfvWmHnHnvdzs5+58BHQ+Q67odrNT6+MUg6PEbE1oEAS0CCLolSQTdEiCC7gagciqCrgxUIQ5BV4CoGIGgK8JUikLQlUAqxbgU9M//s1X6jPugWkt/2r6Z/OqUQ5RaH78YBD1+Y0KLIKBFAEG3JImgWwJE0N0AVE5F0JWBKsQh6AoQFSMQdEWYSlEIuhJIpRiXgm6aeOFDH8hna7ZWtfaePkdJ50ObKLU+fjEIevzGhBZBQIsAgm5JEkG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVohB0JZBKMa4Ffcv2Mjlx9Jyq1t7Xt6V02L9Qan3zsZTv20oq6zZQ6kk8YhD0eIwDrYCACwIIuiVVBN0SIILuBqByKoKuDFQhDkFXgKgYgaArwlSKQtCVQCrFuBZ008y2t8+sau2gksVyw39+V/X3TT8ZJzsPOVmpN/mPQdDzPwa0AAKuCCDolmQRdEuACLobgMqpCLoyUIU4BF0BomIEgq4IUykKQVcCqRSTa0F/r96V0qRgc1XrK4uayLor31XqTf5jEPT8jMHSpUvl1ltv9f5r3rx5tUaUlpbKbbfdJh06dJCePXvmp4FcdbcggKBbDiOCbgkQQXcDUDkVQVcGqhCHoCtAVIxA0BVhKkUh6EoglWJcC/qm0jLpdvccOaBRPa/Fb28/b5eWrx20VKk3+Y/ZXQV93rx5MnDgwF0ADx06NBbSqyXoqfq5//77yz333LOL+Of/bvtfC55++mmZM2eO3HLLLVJUVBSnpu1WbUHQLYcTQbcEiKC7AaiciqArA1WIQ9AVICpGIOiKMJWiEHQlkEoxrgV94erN0m/8R9L+oEZy0hFNpMObl0nHwk+qWr+9ZR/ZctodSr3Jf8zuLOhGUkeNGiWNGzfOP+ikFmgKepz7mQ48gp6bWxJBt+SMoFsCRNDdAFRORdCVgSrEIegKEBUjEHRFmEpRCLoSSKUY14I+d9kGufLxBZ6gP3zUbCl+4/dVLS/bv61sPH+SSEGBUm/yH5NXQf/iTZEpvxBZv0xk/6NFzv27yP7HqEAxM8vpxHX9+vUyePBg6d69uzz66KOyatUq78/+bK7//fnz53ttyfS9sWPHSrt27cSvY/48fvz4qnr9+/eXm266aZdr+ILeokULmTRpkld+wIABcu2110qqJe6JM+WtW7euevCQqZ8m0zDw2+Pnm6/7gmx+BzDX97+X7jqmTjIXfzWCyRoxYkTVuPlMzBdMP02fDGO/jz169Kj2tcT+JGYlcjf92LJli2zevFmmT58ucVkJoXKzOgxB0C3hIuiWABF0NwCVUxF0ZaAKcQi6AkTFCARdEaZSFIKuBFIpxrWgv7roW/nb069Jj/3Xyw3rbvNaPauihXQq/ER2HHq6bO5xn1JP4hGjJuiLXxT5am64Ts34i0jZ9v/V2ftwkdZ9wmUccZpISYdd6gQR9JKSEt6FOsgAACAASURBVE/KzSfxnW8jgwcddFDVUvjHH3/ck3TzMWLfq1cv73tGPs0M/fDhw6u+ZwTdCKkvs/41jHSbuuZ7powvrldccYWX5Zc332/VqlW19pi+DBs2rGrZupHYZcuWeVmZ+pk4S+33cb/99vPq+SKcKNOZruO3z++76c9bb70lXbp0kSlTpsi5557rLVU3uZMnT/a4mL8brqaO6bOp45d9/vnnd1ninljXrHpIHAfzZ1Mn7kv3w9287ksj6JaMEXRLgAi6G4DKqQi6MlCFOARdAaJiBIKuCFMpCkFXAqkU41rQ60y7Vhp8Pq2qtc/UOlke2dZZHq97m3yxZxsp7/uENCmuo9Sb/MeoCfr034jMujf3Her+J5FOv0gp6OneQe/WrVs1WTaVfRk844wzPKn0RTYxOFmG/ZluI6CHHXZYtczkWfDkv6da4m7aYD5G2pMfGJivG7E2n8QHA0uWLNnlXXszu+z3I3GjucT2v/7667sIsn/9dNcJspQ+sV9NmjSp9kAjkWXyEvd0qwaM7JuHKPfff79X3W9b7m+0mnlFBN1y3BB0S4AIuhuAyqkIujJQhTgEXQGiYgSCrghTKQpBVwKpFONS0As2r5Im446v1tJ7y86RqeUnyLP1bpYFFQfLrc3+LmMubKnUm/zHqAl6lBn0t/4iUp6/GXR/NjtR0BNns/0l7v5y6nQbz5lZaA1B92fGUwm6v0zdv2P8ZeFG0FOJc+LDAzN7nSz26QQ903V8WU7e1C15GXviJnXJ3/Nn7NMJulm+nvjxl7kj6NH+rditBD3T+xfp3o0w2PwfBv/mCvN+BIIe7cbLVqu4uFgqKiq8seGTfwIIev7HILkFCHq8xgRBj9d4mNYg6PEaE5eCXuvbRdLosTOqdXhc2Zkyrry7vFXvOlle2VS6bB8t795UXeLjRShca9QEPdxlvytt3kGffLXIhq9E9mst0uvenL6Dnk7QE7uSOCNsfldPJ6mJS9T95dyJs+C2M+iJS+4T25duiXu6GWlf5tMJetjr+AJulvn7S/fTHR+XaQY/1QOFxH4mz+5Hud2+j3V2G0HP9C5H8veSb5bEvyf/oGa7KRD0bISifR9Bj8bNVS0E3RXZ6LkIenR2Lmoi6C6o2mUi6Hb8tGu7FHTT1kaPni611i6uavbvim+RKWsPlg+LrpCNlcVyboMJMunSH2l3K295eRV0h70O8g56KkE3S8MT36nOtmTbXMd8bGfQE6/TrFmzjO+gm+v578Wnm0E3ZVK9D574DnryMWfJ76AnXsf8OfH9e+M5ZkKyY8eO1c5zT8wwS9xNmb59+3qMEsckVbuT30FPfGedGfRoPyy7haCbm81swmBuwObNm+9CInnTiMQbzRROrhvmaQ+CHu3Gy1YLQc9GKLffR9BzyzvI1RD0IJRyVwZBzx3roFdC0IOSyk0514J+13MfyG2fnet1ZtNPHpIPitrLr//9mcwo7e197a0LPpKW+++Zm87m4Cq7s6BHeQfdLHFP3PncDEHiRmrJO5n7S819gfWlP8g76Im7mydeJ9su7qZs4q7rmd4Nz7aLe/I55MnL+BN3fk9eru6vFE68htngznzMLLr/oMFfWZy49D1x1XG6XdxNTuI1zN95Bz3cPwq7haAn33iJPwCpflgSn3b5N6O5IX25D3PGH4Ie7oYLWhpBD0oqN+UQ9NxwDnMVBD0MLfdlEXT3jMNeAUEPS8xtedeCbo5Ym/TN2V4n1g5a6v3v1PnfyIBXOlb7mtte5i59dxX03BHkShCIL4HdQtDNU6PEd0sSjxTwd0P0jwowQ5Es6P5RC+ZoAPNJJeh/+tOfdhnFm2++mXekHd3b5pdd8ykrK3N0BWLDECgsLBQzJjt27AhTjbIOCZhfds0+DeXl5Q6vQnRQAvyMBCWVu3J169b1/j/E/JzwyT8B1z8j14x/Ux5Y2UekqJGUDl7kdfidL9ZJ18eP8v7sfa2oUf5BKLUgecMvpVhiIACBGBDYLQU9UbKHDBkiI0eOlMTjCqLMoE+aNGmX4erTp49s3bo1BsO4+zXB/GJlfqlC0OMxtuYXq3r16sm2bdvi0SBa4Y2H+flA0ONxM5ifEfPvFhtbxmM8TCv4GYnPWJiWmJVY5kHv9u0Ju38rNvG6vz4u92+9Xir2bSWll7zkJX+6apPsO76zlBSskW0D35HKRgcqXjG/UWalIR8IQGD3JLBbCHriuYKJs+DLli3z3nngHfSad/OyxD1eY8YS93iNh2kNS9zjNSYscY/XeJjWsMQ9XmPieon7r+8ZL/dX3iplJZ1kY++JXudXbCiVxv/o6gn6hn7TpLzpd+/Z7g4flrjvDqNIHyCQmsBuIej+e+b+DofJO7Gzi3vNu/0R9HiNGYIer/FA0OM3Hgh6/MYEQY/XmLgW9N+MvEfuq3uX7Dj0dNnc4z6v85tKy2TlmJ9Ip8JPZGPvCVJWwjFr8boraA0EIJCKwG4h6KZjybszJp9lzjnoNesHAEGP13gh6PEaDwQ9fuOBoMdvTBD0eI2Ja0H/652/k9/VeURK21wqW7sOrer83L+cLz+uNVc2nT1Gdh5W/az0eBEK1xpm0MPxojQEahKB3UbQ8wWdXdzdkEfQ3XCNmoqgRyXnrh5L3N2xjZKMoEeh5rYOgu6Wb9h0l4JuZson//03Mrj2k7Kt42DZ1vG6qua98pcB0qfWG7Ll9Dtke4s+YZsd2/IIemyHhoZBwJoAgm6JEEG3BJimOoLuhmvUVAQ9Kjl39RB0d2yjJCPoUai5rYOgu+UbNt2loC9cvVlmP3ZrSkF/+e/Xyvll02Rr199JaZufh212bMsj6LEdGhoGAWsCCLolQgTdEiCC7gagciqCrgxUIQ5BV4CoGIGgK8JUikLQlUAqxbgU9LnLNsjGJwelnCl/6cHfygVbJuwys67UrbzFIOh5Q8+FIeCcAIJuiRhBtwSIoLsBqJyKoCsDVYhD0BUgKkYg6IowlaIQdCWQSjEuBf3VRd/KD569KOVmcAsmjZAuK8Yh6ErjSAwEIOCeAIJuyRhBtwSIoLsBqJyKoCsDVYhD0BUgKkYg6IowlaIQdCWQSjEuBX3q/G/kyJcuySjoyZvHKXUrbzHMoOcNPReGgHMCCLolYgTdEiCC7gagciqCrgxUIQ5BV4CoGIGgK8JUikLQlUAqxbgU9LEzlstFc3t7552vv+RNqWhYUtXqj54fJ90WjvA2iDMbxe0uHwR9dxlJ+gGBXQkg6JZ3BYJuCRBBdwNQORVBVwaqEIegK0BUjEDQFWEqRSHoSiCVYlwL+s3zTvRaunbQ0mot/vC1SXLShzdWOx9dqUt5jUHQ84qfi0PAKQEE3RIvgm4JEEF3A1A5FUFXBqoQh6ArQFSMQNAVYSpFIehKIJVi8iXoH8x8QU6eM1DKSjrJxt4TlXqT/xgEPf9jQAsg4IoAgm5JFkG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVohB0JZBKMS4F/c7nPpQ/fNZTKus1lHUDP6jW4jdnzpCec34m5U1byoZ+zyj1Jv8xCHr+x4AWQMAVAQTdkiyCbgkQQXcDUDkVQVcGqhCHoCtAVIxA0BVhKkUh6EoglWJcCvqoJ56T36/6RUoJf+Pdj+Tct37ivZdu3k/fXT4I+u4ykvQDArsSQNAt7woE3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0JVAKsW4FPRxT0ySIatuTLmM/fX5X0ivV05OObuu1LW8xCDoecHORSGQEwIIuiVmBN0SIILuBqByKoKuDFQhDkFXgKgYgaArwlSKQtCVQCrFuBT0ux+4T27d+qeUG8GZM9LPm97e60XyBnJKXctLDIKeF+xcFAI5IYCgW2JG0C0BIuhuACqnIujKQBXiEHQFiIoRCLoiTKUoBF0JpFKMS0Ef/7ffy/XlD0mqs87nLtsgP57SBkFXGkdiIAAB9wQQdEvGCLolQATdDUDlVARdGahCHIKuAFExAkFXhKkUhaArgVSKcSnoD981RAbXflK2dRws2zpeV63FKzaUSuvxLbyvmQ3kzEZyu8OHGfTdYRTpAwRSE0DQLe8MBN0SIILuBqByKoKuDFQhDkFXgKgYgaArwlSKQtCVQCrF5FPQG/+jq5QUrPE2iTObxe0OHwR9dxhF+gABBN3JPYCgO8EqxcXFUlFRIaWlpW4uQGooAgh6KFw5KYyg5wRz4Isg6IFR5awggp4z1IEu5ErQN5WWyZx7L5M+td6QLaffIdtb9NmlPatGnSQtC7+UDf2mSXnTVoHaG/dCCHrcR4j2QSA6AWbQo7PzaiLolgDTVEfQ3XCNmoqgRyXnrh6C7o5tlGQEPQo1t3UQdLd8w6a7EvSFqzdLwcQLpFPhJ7Kx9wQpKzl+l6YtGnVWxu+H7UscyiPocRgF2gABNwQQdEuuCLolQATdDUDlVARdGahCHIKuAFExAkFXhKkUhaArgVSKcSXoZhO4hk/1yyjgc/9yvvy41lzZdPYY2XnYGUo9ym8Mgp5f/lwdAi4JIOiWdBF0S4AIuhuAyqkIujJQhTgEXQGiYgSCrghTKQpBVwKpFONK0M0xah2fOzPjO+afjhsoJ2x+Ie0SeKUu5jQGQc8pbi4GgZwSQNAtcSPolgARdDcAlVMRdGWgCnEIugJExQgEXRGmUhSCrgRSKcaVoE+d/40MeKWj18p055x/8tiN0vnbSbK16++ktM3PlXqU3xgEPb/8uToEXBJA0C3pIuiWABF0NwCVUxF0ZaAKcQi6AkTFCARdEaZSFIKuBFIpxpWgj52xXG6ed2JGQV8waYR0WTEu5TFsSt3LeQyCnnPkXBACOSOAoFuiRtAtASLobgAqpyLoykAV4hB0BYiKEQi6IkylKARdCaRSjCtBn/j2Qvnl3O7e+ebmnPNUn2VT/yBtlj6AoCuNJTEQgIBbAgi6JV8E3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0KODLK+oFPNf3dqF0UOSaroS9DdnzpCec34m5U1byoZ+z6Rs77oX/iyHfTpGSttcKlu7DlXrUz6DmEHPJ32uDQG3BBB0S74IuiVABN0NQOVUBF0ZqEIcgq4AUTECQVeEqRSFoEcD+eDMr+Vvby7zKrc9sKHce0FLqVOrIFpYQi1Xgv7c9Kny00WDpKykk2zsPTFlO794/WFp+8Ew74x0c1b67vBB0HeHUaQPEEhNAEG3vDMQdEuACLobgMqpCLoyUIU4BF0BomIEgq4IUykKQQ8Pcum32+S8B9+vVvHqLgfKFSeUhA9LquFK0Cf962G5cuUw2XHo6bK5x30p27l01hRp9871GctYdzDHAQh6joFzOQjkkACCbgkbQbcEiKC7AaiciqArA1WIQ9AVICpGIOiKMJWiEPTwIP89f43c+uziahUv6fgDGdTtoPBhORL0WZPulLNW/DXj8vUPZr4gJ88ZmHGW3bqDOQ5A0HMMnMtBIIcEEHRL2Ai6JUAE3Q1A5VQEXRmoQhyCrgBRMQJBV4SpFIWghwe5eXu5nHTPHKmoqKyq/MdzjpDuLfYJH5YjQX/pwd/KBVsmZNwAbsnHc6TDyxdkfE/duoM5DkDQcwycy0EghwQQdEvYCLolQATdDUDlVARdGahCHIKuAFExAkFXhKkUhaCHB7liQ6n0GPteVcUL2+4vvz6tefigFDVcLXGf/Ldfy2XlT2QU9Pc+XiCnvny2VDQskfWXvKnSn3yHIOj5HgGuDwF3BBB0S7YIuiVABN0NQOVUBF0ZqEIcgq4AUTECQVeEqRSFoIcHOezZxTJ1/pqqisPPOkzOab1v+KAcCvorfxkgfWq94W3+ZjaBS/VZtGyFdJrSOeNRbCqdzGEIgp5D2FwKAjkmgKBbAkfQLQEi6G4AKqci6MpAFeIQdAWIihEIuiJMpSgEPRzIxNnzk45oIq99tk6GnHqI9G/XLFxQmtKuZtAXjTpLOhV+Iht7T5CykuNTXn3usg3y4yltvO+tHbRUpT/5DkHQ8z0CXB8C7ggg6JZsEXRLgAi6G4DKqQi6MlCFOARdAaJiBIKuCFMpCkEPDvKbTTvk0gnzZcWG7dK/fTNpUK+2jJ3xlQzsfKAM7Gy/g7tpiQtB31RaJivH/CSroJuHD63Ht0DQg98SlIQABPJIAEG3hI+gWwJE0N0AVE5F0JWBKsQh6AoQFSMQdEWYSlEIenaQ28sq5NLHPpZPVm/2ChcWFMir17aXCfNW1QhBX7h6sxz4+ClSUrDGe7fcvGOe6mNE/uD7jvC+tW7gB95S95r+YQa9po8g7YdAegIIuuXdgaBbAkTQ3QBUTkXQlYEqxCHoChAVIxB0RZhKUQh6dpAPzFwuf3/zq2oFR513lCxfXyojX/7Cm00fcsoh2YMClHAxgx5m6frW0cdlFfkA3YhNEQQ9NkNBQyCgTgBBt0SKoFsCRNDdAFRORdCVgSrEIegKEBUjEHRFmEpRCHp2kGNmLJf7ZlQX9OtPOlgaF9eWYc8ukXNaN5XhZx2ePShACReC/uqib+W86e29q2d7t3zVqJOkZeGXsqHfNClv2ipAi+NdBEGP9/jQOgjYEEDQbeiZ/0NYu9YygeqpCBQXF0tFRYWUlpYCKAYEEPQYDEJSExD0eI0Jgh6v8TCtQdCzj8l7yzfKZRM+ripYVKeWPH91W1n4zRa58vEF0v6gRnJf35bZgwKUcCHor8//Qnq9cnIgQV9570+k1c6PMm4mF6AbsSmCoMdmKGgIBNQJIOiWSBF0S4BpqiPobrhGTUXQo5JzVw9Bd8c2SjKCHoWa2zoIejC+T76/Wv7wwufSrGE9+UvvI+WH++4hZul4TRD0N2fOkJ5zfhbofPMVD/1MWm+aIZvOHiM7DzsjGJwYl0LQYzw4NA0ClgQQdEuACLolQATdDUDlVARdGahCHIKuAFExAkFXhKkUhaAHAzl1/jfecnZztNpdvY7yKvlHrh3QqJ5MG9g2WFCWUi5m0D+Y+YKcPGeglJV0ko29J2ZswdcTfiFH/+e5jOelq3Q0RyEIeo5AcxkI5IEAgm4JHUG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVohD0YCDHzlju7dieuCGc2fW8291zpEG9WvL6dccFC8qDoC+dNUXavXN9IEFf/uRv5Jiv/ylbu/5OStv8XKVP+QxB0PNJn2tDwC0BBN2SL4JuCRBBdwNQORVBVwaqEIegK0BUjEDQFWEqRSHowUD6gp585nnb22d6Ae/edHywoDwI+pcv/FWO/fRO2d6ijzcznumz7oU/y2GfjpFtHQfLto7XqfQpnyEIej7pc20IuCWAoFvyRdAtASLobgAqpyLoykAV4hB0BYiKEQi6IkylKAQ9GMiaLOgLJo2QLivGBZJuBD3Y/UApCEAg/wQQdMsxQNAtASLobgAqpyLoykAV4hB0BYiKEQi6IkylKAQ9GMhhzy6WqfPXyPCzDpNzWu9bVanb6Hdk0/ZyeX1QB2lQVDtYWIZS2u+gv/bZWvnP83+WKyuekAWHXyn7n3VzxjbWnz1a6s8eJaVtLpWtXYda9yffAcyg53sEuD4E3BFA0C3ZIuiWABF0NwCVUxF0ZaAKcQi6AkTFCARdEaZSFIIeDKTZrd3s2m6OUzPHqvmfHmPflRUbtsu0gcfKAY2KgoXlSNCXrd0m5z7wvoysM0b61HpDfr/zIjni7OvkjKP2TtuCb2dNkCPe+W2g5fDWnc1BAIKeA8hcAgJ5IoCgW4JH0C0BIuhuACqnIujKQBXiEHQFiIoRCLoiTKUoBD0YyHSCnu7rwVJ3LaU5g/7gzOXytze/ksfrjpBOhZ/IlTtukEM6nStXdTkwbfP8DeV2HHq6bO5xX9RuxKYegh6boaAhEFAngKBbIkXQLQEi6G4AKqci6MpAFeIQdAWIihEIuiJMpSgEPRjIdDPlcRb0T1Ztli8nXCf9a7/idfKdiqPkw24PSu9jD0jb6TULXpcjX7ok0I7vwcjltxSCnl/+XB0CLgkg6JZ0EXRLgAi6G4DKqQi6MlCFOARdAaJiBIKuCFMpCkEPBjLdbu3p3k0PlrprKc0Z9NpfvS0NJ/+02kW2dP6NbG83MG3zVi9+V1o8e56UN20pG/o9E7UbsamHoMdmKGgIBNQJIOiWSBF0S4AIuhuAyqkIujJQhTgEXQGiYgSCrghTKQpBDwYynaCPfOULmTB3pQw59RDp365ZsLAMpTQFvWjuvVL89u3VrrbtuEGyrdP1aVuw6qvF0nLy6VLRsETWX/KmdX/yHYCg53sEuD4E3BFA0C3ZIuiWABF0NwCVUxF0ZaAKcQi6AkTFCARdEaZSFIKeHeSm0jLpdvccaVCvlrx+3XHVKqQ7fi17auoSmoJesOUbafJgJxGprLrYpp7/kJ0Hd0vbvJXffCOtHu8olfUayrqBH0TtRmzqIeixGQoaAgF1Agi6JVIE3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0LODXLh6s/Qb/5Ecud8eMnHAMTVG0E1DF7/7shz31uUiderL5tPvlB2Hn5mxwys2lErr8S28MmsHLc0OJ+YlEPSYDxDNg4AFAQTdAp73j/zatZYJVE9FoLi4WCoqKqS0tBRAMSCAoMdgEJKagKDHa0wQ9HiNh2kNgp59TMzxamYzOHO8mjlmLfEzYd5KGfnyF9K/fTMZcsoh2cOylNCcQTeXenPmDOk552eh3inf6+7mCLr1SBIAAQi4JoCgWxJG0C0BpqmOoLvhGjUVQY9Kzl09BN0d2yjJCHoUam7rIOjZ+b666Fv51ZRFctIRTeSuXkdVqzB1/jcy7Nklck7rpjL8rMOzh+VY0D+Y+YKcPGdgqF3ZfUE3S9zNUvea/GEGvSaPHm2HQGYCCLrlHYKgWwJE0N0AVE5F0JWBKsQh6AoQFSMQdEWYSlEIenaQmWbJ/dn1VPKePXnXEtoz6DOef1zOWXizbG/RR7acfkegJm0dfZyUFKzxNokzm8XV5A+CXpNHj7ZDAEF3eg8g6G7wMoPuhmvUVAQ9Kjl39RB0d2yjJCPoUai5rYOgZ+ebaSO4TMvfsye7F/QFk0ZIlxXjZFvHwbKt43WBmlT4jzOk8cZFsqHfNClv2ipQnbgWQtDjOjK0CwL2BJhBt2SIoFsCTFMdQXfDNWoqgh6VnLt6CLo7tlGSEfQo1NzWQdCz880k6GZTtR5j35MDGtWTaQPbZg/LUkJ7Bn32Y8PkzG8fDiXomx/qLQdtek829p4gZSXHW/cpnwEIej7pc20IuCWAoFvyRdAtASLobgAqpyLoykAV4hB0BYiKEQi6IkylKAQ9O8hhzy6WqfPXyPCzDpNzWu9brUKmI9iyJ+9aQlvQX/77tXJ+2TRvebtZ5h7o89Tlstfyl2XT2WNk52FnBKoS10IIelxHhnZBwJ4Agm7JEEG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVohD07CDNDu5mKbvZwd3s5J78aXv7TO9L795kP9usLeiLRp0lnQo/CTUbXjHtetnn8ynhpD47xryUQNDzgp2LQiAnBBB0S8wIuiVABN0NQOVUBF0ZqEIcgq4AUTECQVeEqRSFoGcHWVMF3czurxzzk9CCXv7iMGn6ycOytevvpLTNz7MDinEJBD3Gg0PTIGBJAEG3BIigWwJE0N0AVE5F0JWBKsQh6AoQFSMQdEWYSlEIenaQPca+Kys2bJdpA4+VAxoV7VKh2+h3ZNP2cnl9UAdpUFQ7e2CGEpoz6GbWv9PkE6RhwVYJc2Ra2Rt3yr7v/zXUe+tWnXZYGUF3CJdoCOSZAIJuOQAIuiVABN0NQOVUBF0ZqEIcgq4AUTECQVeEqRSFoGcHmW0JezaBz36F/5XQFvQfT2njha8dtDRwM+rPHi31Z49C0AMToyAEIJAPAgi6JXUE3RIggu4GoHIqgq4MVCEOQVeAqBiBoCvCVIpC0LODzCbo/hL4iQOOliP32zN7YIYSmoJuzm+/ZsYJkQW9tM2lsrXrUKv+5LsyM+j5HgGuDwF3BBB0S7YIuiVABN0NQOVUBF0ZqEIcgq4AUTECQVeEqRSFoGcG6e/Sbkql2wQu2zvqYYZKU9Cfefs9uWhubylv2lI29HsmWDMqK6XO5MukwfJXpWLPZrL5jL9I2Q86Bqsbw1IIegwHhSZBQIkAgm4JEkG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVohD0zCAXrt4s/cZ/lPGc80zHsIUdJk1Bf+u1Z+UnH/5Syko6ycbeEwM1pWjO36R45siqspW1i2TdVfNFCmsFqh+3Qgh63EaE9kBAjwCCbskSQbcEiKC7AaiciqArA1WIQ9AVICpGIOiKMJWici3oZRWVMuXD1VKnVqGc07qpFBYUKPXETYzZaM3MkJvj1cwxa6k+I1/5QibMXZnynPSwrdIU9OlPPyb9v7xFdhx6umzucV+gphQ+/2tpvPCJamVXXjRD6jU5IFD9uBVC0OM2IrQHAnoEEHRLlgi6JUAE3Q1A5VQEXRmoQhyCrgBRMQJBV4SpFJVLQd+2s1x+Ov4j+WLtNq/1R+67hzxy8dFSuzC+kv7qom/lV1MWZRT0sTOWy9gZX8nAzgfKwM4lViOjKeizJt0pZ634q4R5l7zitT/LPh+OqepDpYh8feWnUlxUz6pf+aqMoOeLPNeFgHsCu42g33PPPTJ+/PhqxIYOHSo9e/b0vvb000/LiBEjvD93795dbrnlFikq+u5IkdLSUrnttttk+vTp3t8T62UbAgQ9G6Fo3y8uLpaKigpvbPjknwCCnv8xSG4Bgh6vMUHQ4zUepjW5FPQHZ30tf3tjWTUIN556iPRr1yx+YP7bIrPR2siXv/Bm+4efdXjKdsZV0F968LdywZYJoXZj/3TFWqn416XSpeADr68PlvxRevXuF9vxydYwBD0bIb4PgZpLYLcSdDMM11577S6jMW/ePDECP2rU8dTwtgAAIABJREFUKGncuLH358SyiX9fv369DB482Mtp165d1pFF0LMiilQAQY+EzVklBN0Z2sjBCHpkdE4qIuhOsFqF5lLQfZFNbPAvux4kl3X6gVUfXFYOIt++xPdv30yGnHKIVXM0Z9Cn3HW1XFp7umzt+jspbfPzwO1as2m7HPnQUV75L6/8zPps98AXdlAQQXcAlUgIxITA90LQjYAfdNBBVbPpicJuxmHYsGGelDdv3twblmSBzzRWCLqbOxlBd8M1aiqCHpWcu3oIuju2UZIR9CjU3NbJpaCv2rhdzrnvPSmvMAunRRrVry1PX3GsNCyq7baTFulBBH3q/G9k2LNLMs6yB22CpqDP/cv58uNac2XT2WNk52FnBG2CV67xP06Uwo3L5au+r8oe+9o9dAh1YeXCCLoyUOIgECMCu5WgJy5x95ep+8vXO3ToUCXoS5culVtvvdX7z3z8P/uCbpbDz5kzp9oy+HRjhqC7uZsRdDdco6Yi6FHJuauHoLtjGyUZQY9CzW2dXAr6ig2l0mPse1UdeuSio6VVM7tzw93SEfE3gBty6iHSP81SfP899ZOOaCJ39fpu5jnqR0vQze7zBRMvkE6Fn8jG3hOkrOT4UE1q+FQ/qb18lnx91sNS//ATQ9WNU2EEPU6jQVsgoEtgtxH0RCxGwM0S9eHDh0urVq2898t79epVtWQ9WdDN0ndT1ix/N59Ugn7hhRfuQv6f//ynlJWV6Y4IaR6BwsJC738rK7+bjeCTfwJmTMy+AHziQcCMh/n54GckHuPh/7vFz8j3czxuevIjefLdr6s6//qQrlLSpDg+MFK05KrH3pUXF3wjY356rJzecr+UbZ29dK30f+Ad6dh8L5lw+XHW/Un8/5Go/3aZNh386AlSUrBGyq55T6TxQaHaVWvqtVLwwQRZduJIOaBb8OXxoS6Sg8LmoSAfCEBg9ySwWwq6GSp/WfsZZ5zhCbrtDPrixYt3uQMOP/xw2bBhw+55Z+S5V2YDP/OL7vbt2/PcEi5vCJgZdLOqYdOmTQCJCQEzHjt37vT+45N/AuaXZfPv1ubNm/PfGFrgEdhjjz1kx44dOfkZafN/M7xrHtConqzYsF0e6N/a2x09zp/LJ8wXc9RapraalQFn3TvP69ezV7e36o6ZQa9bt65s2bLFyymIeAydmdU/95ljvYwN11ffmC9IA4teGy713ntQ1hx7rdQ96cYgVWJZplGjeN9fsYRGoyBQQwjs9oJudnHnHfQacjcmNJMl7vEaM5a4x2s8TGtY4h6vMWGJe7zGw7QmV0vcp85fI8OeXVwl5EZ67zz3h3LyD/eOH5SEFvUb/6EsXL1FJg44Wo7cL/Vy/E2lZdLt7jmeoE8b2NaqP1pL3M278zfP+25p+tpBS0O3qf7s0VJ/9ih5v/nlctA5vw1dPy4VWOIel5GgHRDQJ7BbCLrZed0ckda3b1+PUOISdvNeObu46984rhMRdNeEw+Uj6OF45aI0gp4LysGvgaAHZ5WrkrkS9B5j3/Vmzc1RZUbOjbAPP+swOaf1vrnqaqTrdBv9jmzaXi6vD+qQcTfztrfP9PLfvSncu97JjdIS9Gfefk8umttbKhqWyPpL3gzd93qfTJI9XrxRPtrnTPlB/7+Hrh+XCgh6XEaCdkBAn8BuIejJ55gbTGPHjq12TBrnoOvfPC4TEXSXdMNnI+jhmbmugaC7JhwuH0EPxysXpV0L+ubtZfLUB9/IqNe+rJphDrLxWi76HuQaQcU7aLls19QS9OemT5WfLhokZSWdZGPvidkuu8v3ay+fKQ2f6i8f1zlaml3979D141IBQY/LSNAOCOgT2C0EXR9L8ER2cQ/OKkxJBD0MLfdlEXT3jMNeAUEPS8xteQTdLd8o6S4F/f3lG+WKxxdUHat2dqumMuLswyXI0WVR+uKiTlDx9mfa4zKDPulfD8uVK4fJjkNPl8097guNxhyxZo5aW17ZVMoHvl1jz0JH0EMPPRUgUGMIIOiWQ4WgWwJMUx1Bd8M1aiqCHpWcu3oIuju2UZIR9CjU3NZxKei3PLNYnv14TbUOzPpVR3lo1goZO+MrGdj5QBnYucRtBy3S/WPhgrxb7i/hnzbwWDmgUVHkq2rNoL/46J/lwrVjpLTNpbK169BI7dnr7uZevZkXfpT2/ftIwTmshKDnEDaXgkCOCSDolsARdEuACLobgMqpCLoyUIU4BF0BomIEgq4IUynKpaBf9+Sn8uaSddVaOuP64+Thd1Z6gt6/fTMZcsohSj3RjzHvyl/5+AJvY7v7+rbMeIEgm8kFaaGWoE/+26/lsvInZFvHwbKt43VBLr1LGV/Qn+w+N/ab+aXrIIIeaeipBIEaQQBBtxwmBN0SIILuBqByKoKuDFQhDkFXgKgYgaArwlSKcino0z/5j/y/qZ9VtbRNSUMZ17+VTJ3/jQx7domc07qpt2lcXD9hBN2IvClvRN7m6DgtQZ9y19Vyae3pVoJulribpe5TuvxburY9Oq7DlLFdCHqNHDYaDYFABBD0QJjSF0LQLQEi6G4AKqci6MpAFeIQdAWIihEIuiJMpSiXgm6aeOcrX8hjc1dKp0May+jzjpQ6tQrFnNH9qymL5KQjmshdvY5S6ol+TJgHCeYIObMzve3RcRqCbo59W3hvP/lxrbmy6ewxsvOwMyLBafhUP6m9fJa82mGs/Oj4H0fKyHclBD3fI8D1IeCOAIJuyRZBtwSIoLsBqJyKoCsDVYhD0BUgKkYg6IowlaJcC/oNkz+V1z5bV01cw8xMK3UzUkyYzex8Qbc9Ok5D0Beu3iwFEy+QToWfyMbeE6SsJNrRb+aYNXPc2utHDpWjz7g0EsN8V0LQ8z0CXB8C7ggg6JZsEXRLgAi6G4DKqQi6MlCFOARdAaJiBIKuCFMpyrWgp1r6bQSy33iz8dgeMnHAMUo90Y8JI+hhymZqqYagmwcgJU/1lJaFX8qGftOkvGmrSHDqzx4t9WePkuf2vlg6/nR4pIx8V0LQ8z0CXB8C7ggg6JZsEXRLgAi6G4DKqQi6MlCFOARdAaJiBIKuCFMpyrWg+7ubTxxwdNVO4GF2R1fqZqSYMOe1x0nQJ8xbKdfMOMHr89pBSyP13VTyBf3BWhdIr1/+OXJOPisi6Pmkz7Uh4JYAgm7JF0G3BIiguwGonIqgKwNViEPQFSAqRiDoijCVolwLerpzxIOeL67UzUgxqZbnpwsyUjzy5S+sj47TmEE3DwtunneitaCb5e1mmfuk8q5yyvXjIzHMdyUEPd8jwPUh4I4Agm7JFkG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVolwKutmsrNvdc7yWvntT9fega4Kgh9mZPcyGcpmGTkPQJ769UH45t7tU1mso6wZ+EPlOqb18pjR8qr/Mqmghe/78Kavz3SM3wrIigm4JkOoQiDEBBN1ycBB0S4AIuhuAyqkIujJQhTgEXQGiYgSCrghTKcqloGd617wmCHqYs821dqbXEPQnnp4sV315g5Q3bSkb+j0T+U4xR6yZo9aWVzaV2Wc+VyPPQkfQIw8/FSEQewIIuuUQIeiWABF0NwCVUxF0ZaAKcQi6AkTFCARdEaZSlEtBz7Rbe7fR78im7eUybeCxsZ2Z9dv4+qAO0qCodkbiWjvTawj6uCcmyZBVN0pZSSfZ2Hui1Z2y193Nvfp/7fy29G/XzCorH5UR9HxQ55oQyA0BBN2SM4JuCRBBdwNQORVBVwaqEIegK0BUjEDQFWEqRbkUdP+97HNaN5XhZx1ercX+5nFxFvQws/z+xne2O9NrCPpTD/5RLt9yv2xv0Ue2nH6H1Z3iC/rf2k+XficcaZWVj8oIej6oc00I5IYAgm7JGUG3BIiguwGonIqgKwNViEPQFSAqRiDoijCVolwKeqadzcO8363U1VAxLy9aKzdOWejVee7qtrJfg3oZ62vtTG8r6G8sXitf/PuPMrj2k7Kt42DZ1vG6UP1OLmyWuJul7n8+YqJccWYnq6x8VEbQ80Gda0IgNwQQdEvOCLolQATdDUDlVARdGahCHIKuAFExAkFXhKkU5VLQ/WPKBnY+UAZ2LqnW4jgLeqKcm0b/oFGR/PvKNlJQUJCRepgZ93RBNoI+4/P1cu2kTzw5N/+Nr9tXul/xB6lTqzDy3dLwqX5Se/ksGdbkT3LdRX0j5+SrIoKeL/JcFwLuCSDolowRdEuACLobgMqpCLoyUIU4BF0BomIEgq4IUynKpaBnOqZs2LOLZer8NTL8rMPknNb7KvVGJ+aOl7+QifNWVgt75qq20qxh5ln0fAv6PW8sk4dmfS0j64yRPrXekCE7r5JT+1wlHQ9uFBmMOWbNHLc2vPCXcu01QyLn5Ksigp4v8lwXAu4JIOiWjBF0S4AIuhuAyqkIujJQhTgEXQGiYgSCbgezorJSCrPM4oa9gktBzzRL7s+uDzn1kNhtPuaLbiLLN647TvasVysjXn9TueQj5cKMic0M+r1vfiX3z1wuj9cdIZ0KP5G+O26RG3/eX5rvXT9ME6qVrT97tNSfPUpGlZ0nF98wMnJOvioi6Pkiz3Uh4J4Agm7JGEG3BIiguwGonIqgKwNViEPQFSAqRiDo0WBu3l4m1/zrU/lwxSYxfv6nc34oPz5q72hhSbVcCnqmXdAzvZ+u0jGLkNWbtsvlExbI1xtKvZR+7ZrJjacekjVRY+M7G0H/dPVm6T/+I3m27s3SsvBL+Uvz+2TAOadnbXemAr6gjyvrLsdd9pfY7rifrg8IutXwUxkCsSaAoFsOD4JuCRBBdwNQORVBVwaqEIegK0BUjEDQg8GsrKyUR+aslP9s2Sn77FFHNpSWeUuXEz8zb+go9WpHf7fYz3Ip6JmWfMdZ0A0bv31XnFAiV3c5MNDA+eem2+xMbyPo/msDH9a7XBoWbJV1Az+QynoNA7U9XSGzvN0sc59U3lUanne3tD8o+nJ5q4ZErIygRwRHNQjUAAIIuuUgIeiWABF0NwCVUxF0ZaAKcQi6AkTFCAQ9GMxbn1si//7om6rCZta8srJ63elXt5V9s+wsHuRqrgR9U2mZdLt7jjSoV0tev+64XZriC3D/9s1kyCnZZ6eD9EWzTKb359Ndx1/SP+bCFnLcwY0jNSeqoJtd5PuOfVueqDtcWhYu8669vdWFsuXU/4vUDr9S7eUzpeFT/WVWRQtZeNo/YrdfQLbOIejZCPF9CNRcAgi65dgh6JYAEXQ3AJVTEXRloApxCLoCRMUIBD0YTH/mOV1pI72vDuqg8j66K0Gfu2yDGGFNdy741PnfyLBnl0iqM9KDUXJbKuwu8y98+q385t+LvEbVrVUofzv/KGkXYbY5qqCbBwptl94ng2pPrgZm/UWvSEWT5pFhmSPWzFFryyubyiPtn9plN/7IwTmqiKDnCDSXgUAeCCDoltARdEuACLobgMqpCLoyUIU4BF0BomIEgh4M5sn3zJUN23ZWFW5Uv46cf+x+8sDby72vPXzR0dK62Z7BwrKUciXory76Vn41ZZG3JPq+vi13aYX//ZOOaCJ39TpKpS+aIZnen091nQ4jZ0l5xf+WObTYf0957OKjQzcprKA/MHO5vPrZOvlk1Wb5de3H5era/652zU3nPiI7D+oSuh2JFfa6+zvBP6l4svy/Hx8q7Q+yWzZv1ZiQlRH0kMAoDoEaRABBtxwsBN0SIILuBqByKoKuDFQhDkFXgKgYgaAHg/nWknUy6MlPqwrffd5R0uWwJqJxjFdyC1wJ+oR5K2Xky19IuiXs/gx7OoEPRspdqTCszesH7e6YWa0xxXVryVuDd13an63FYQR9/DsrZPRrX1ZFnlY4Tx6oe2fV38375+sunyNSq262y6b9/iuL1kqf6e287x9Ter9sKthTXvplO2lSXCdyZi4rIui5pM21IJBbAgi6JW8E3RIggu4GoHIqgq4MVCEOQVeAqBiBoAeH2W30HNm0vUweuehoafXf2XKNXcJzJejZNoFbuHqz9Bv/Udol8MFJ6ZfM9v58qite9c8F8s6XG6q+9fOOB8i13Q4O3bgwgn795IXy+mdrq11j/t43y55bvpQdPzxHtnW4Rsr3/mHoNiRWMEe3DXj3PCkpWCNdto/2lrrfdFpz6dt2f6vcXFVG0HNFmutAIPcEEHRL5gi6JUAE3Q1A5VQEXRmoQhyCrgBRMQJBDw4z1Qxu2Peig1zN1Qx6tnPOzaZmPca+Jwc0qifTBrYN0tSclYkyu7+9rFz+39TF8upna6XtgQ3lgX6tIrU3jKDf8fJSmThvVdV1GsoW+bDoCu/vawctjXT95EoPzf5aOs+8oupc9VkVLb2l+2YJf034IOg1YZRoIwSiEUDQo3GrqoWgWwJE0N0AVE5F0JWBKsQh6AoQFSMQ9GAw/dnl5B3Q/WO0hp91mNpu2q4EPdvDBH+W2hB596bjg4HJUamo78dHEfvkLoUR9G+37JBf/utTWfTNFikQkXuOmi89vvijlJV0ko29J6rQMu/Vfz3hF9Jm7XQZsvMqaXxcf/nFicGOnVNpgGUIgm4JkOoQiDEBBN1ycBB0S4AIuhuAyqkIujJQhTgEXQGiYgSCHgxmOtHLNisdLL16KVeC7p8JbjaIS3d2dpj3vKP0LWqdbMvz0+VqrArIJugFW9ZI7TXzpaxZe6ms10CqjoPrdaQn5/U+eVK2dRws2zpeF7X7u9SrP3u01J89SkaVnSenXXabHNCoSC3bdRCC7pow+RDIHwEE3ZI9gm4JEEF3A1A5FUFXBqoQh6ArQFSMQNCDwUy3wVpUccx0VVeC7sv364M6SIOi2imbEFdBt3kQYtunTIJe99PJsucLN1Sx3HTueOnzamNZuHqLTBxwtHR87kwxx6Jt6DdNyptGW2KfaqB8QR9X1l1a/PQOOXK/mrG83fQFQQ/2bw6lIFATCSDolqOGoFsCRNDdAFRORdCVgSrEIegKEBUjEPRgMNOJuC/ummeHuxb0TMvX/aPMpg08NlazslWz0uf+UE7+4d7BBu2/pfw+RV22n0nQ9/rrESIVZVXtqWhYIod+c7v39/evKpHG/+gq5mvrL3kzVJuzFS5+8zYpeu9BWVJxgOw8/Y+yb6tu2arE5vsIemyGgoZAQJ0Agm6JFEG3BIiguwGonIqgKwNViEPQFSAqRiDowWCmE8So70ZnuqoLQfeXeie/Q5/cDhe70gcjnLlUkOX56RJs+5RR0P97Hrl/7Ypa9eTQLQ+J4Ty728dS/MbvZXuL82TL6SM1MHgZdZdMlz2fufp/eQWF3tFtlfX3UruGyyAE3SVdsiGQXwIIuiV/BN0SIILuBqByKoKuDFQhDkFXgKgYgaAHg5lOEF0cTeZC0INulpZtI7lgtPRL+bPgmZbnp7uq/3Al07v3mVqcSdD3fPYXUnfxc1XV5+57vvRZ1st7x3/CnqOk7ucvenJuJF3rU3/mXVJ/zj3V4rZ2u1VKfzRA6xJOcxB0p3gJh0BeCSDolvgRdEuACLobgMqpCLoyUIU4BF0BomIEgh4MZrr3t6Ocz53tii4EPehMf1wF3eY9ctud9jNuEldZKY0fPU0K133uDevGymLvbPIe7Q+XP84/2fva+kvekIqGerusF829V4rf/m4Zvf/Z0PffUr7v0dlurVh8H0GPxTDQCAg4IYCgW2JF0C0BIuhuACqnIujKQBXiEHQFiIoRCHp2mNmOH7ORx1RXdyHo/jv0/ds3kyGnHJK207Yym51m+BK2qxRsN/LLtot7o4lnS601C7x3zc2GcGZndXOs2pBVNzp5/1wqyqXh0xdL7a/e9mB+UNJfDuz9h/Bg81QDQc8TeC4LgRwQQNAtISPolgARdDcAlVMRdGWgCnEIugJExQgEPTtMf3n4kfvtIRMHHLNLBZvl17kW9IGdD5SBnUuyCvqQUw+R/u2aZYeTgxJBl+ena0q6HfiDNj2boO/13/fQzU7tjSb28GLnN+gsrTfNkNI2l8rWrkODXipUuYZP9pXaX8+WMQffJRf07BWqbj4LI+j5pM+1IeCWAIJuyRdBtwSIoLsBqJyKoCsDVYhD0BUgKkYg6NlhZlse7m9CZo7V0jjuysUMuj8znk28bWebs9MMX8J2p3x//KLutB9U0NcOWiqfjrtSTtj8YlUnt554i5Qee1n4TgeosceLN0q9TybJ/Xv9Ss772TUBasSjCIIej3GgFRBwQQBBt6SKoFsCRNDdAFRORdCVgSrEIegKEBUjEPTsMLNJq//e9p0RjgBLdXUXgh703fJsfc1OS7+EbZtsl8hnEnSzpL3xP06sWsr+xF2D5KraU/8HoXY9WXv1ApGCQnUw/lnoT+zRX067jCXu6oAJhAAEQhNA0EMjq14BQbcEiKC7AaiciqArA1WIQ9AVICpGIOjZYY585QuZMHelpJt91n5v24Wg+7vQZ5vlD/quenZqeiWy8c92JX8PgQMa1ZNpA9tmK77L9zMJeu3lM6XhU/29d86/PusRWXzvhXJqrXerZay7Yp6TI9CK3n/IO8ZtXFl3OfeGe0P3K18VmEHPF3muCwH3BBB0S8YIuiVABN0NQOVUBF0ZqEIcgq4AUTECQc8OM9vss+0Mb3ILXAh60I3sps7/RoY9u0SiLgfPTjN8iWz8gyQG7X+qrEyCXmfJ89Lgmatkx6Gny2vH3CGfTxqasxl0/9ovlLeX9tf/KwiGWJRB0GMxDDQCAk4IIOiWWBF0S4AIuhuAyqkIujJQhTgEXQGiYgSCnh2mvwlcutln7VnnfAp6tvfts9PSLxF09j/TlW028ssk6P4sttkM7oE9LpdRLy+WxxrfJx1L35KKPfeXzaeNlLKDOutDEfF2jDfL65dXNvWOcjugUZGT62iHIujaRMmDQHwIIOiWY4GgWwJE0N0AVE5F0JWBKsQh6AoQFSMQ9Owws82+as86awv6wtVbxEhukCXetjumZ6cZvkQ2/kESbSQ/k6D774Fv6zjYO15t7IyvJNtO+UHaG7SMv4P8zAs/2mWDwoJt66Rw8wop3/sokcJaQSOdl0PQnSPmAhDIGwEE3RI9gm4JEEF3A1A5FUFXBqoQh6ArQFSMQNAzw/Q3GGtQr5a8ft1xKQtrS62moH+yarMMfmqhrNm8Q+rUKpR7+hwpxx3cOG2nbTdUU7w1q6I0BP2GyZ/Ka5+tkygb+WUS9OI3RkjR++Nka9ffyS+WHh/5GlG5NRn7IynYvlFePPMNaXfEgVUx9ef8VerPvNP7e+UeTWVDrwlSsdfhUS+jWg9BV8VJGARiRQBBtxwOBN0SIILuBqByKoKuDFQhDkFXgKgYgaBnhhlEvldsKJUeY98LNEMdZOg0Bf3iRz6S+Ss3V122SXEdefma9mmbod2XIP3NVEbrgYHNRnOZBH3PaVdK3c9flE1nj5E+bzUTs1rhvr4tpf1BjWy7Hqh+o4lnS601C2TOqU/IYa06fFenbLvs9fejqtU3R72ZI9/i8EHQ4zAKtAECbggg6JZcEXRLgAi6G4DKqQi6MlCFOARdAaJiBIKeGaZ/Bnf/9s1kyCmHpCzs7xJuvvnuTcdbj46moLe7Y6ZUVlZvUqY2avfFFkaQByRBrmGzkV8mQW/4VD+pvXyWbOw9QY6Z8F1LNO6BIH0yZfyz0D877g+yd6f+XrWC0g3S5L421SK2t+wjW067I2is03IIulO8hEMgrwQQdEv8CLolQATdDUDlVARdGahCHIKuAFExAkHPDDOo2Gksw/ZboinoQ59ZLM98vKaqk21+0EDG/bR1xk5r9sX2Vg3ygCTINWxyMgm62aTNbNb2Vd9X5cR/rJRMr0IEaWfYMonvwG/reJ1XvbyiUtbe3VWOLFxeFfdx299Lsy4XhY13Uh5Bd4KVUAjEggCCbjkMCLolQATdDUDlVARdGahCHIKuAFExAkHPDDPou8s9xr4rKzZsl2kDj7XeTVtT0HeWV4pZ5r7wmy1yfPPG8oceh0vj+nVqjKAHfUCS7UfCZnf6TILub9L2wrnvizkOzixtN0vcc/Xxd5Ff0+JiqXX6cO+y67eVyc1/e1j+WXeErKzcS24v6ys7juolI87mHfRcjQvXgcD3lQCCbjnyCLolQATdDUDlVARdGahCHIKuAFExAkHPDNPf/Tvbe8VBywUZOk1BN9fzzxEPukGazZFkQfoXpoz/gGTIqYdI/3bNwlStVtbmXfYggv7Xzm/LyJe/yPn58YnnsG/ucZ/X54rKShlxzxgZLbeLOSP9yp03yOXH/0B+ceJBkflpVmQGXZMmWRCIFwEE3XI8EHRLgAi6G4DKqQi6MlCFOARdAaJiBIKeGaa/3Pv1QR2kQVHttIV9kRx+1mFyTut9rUbIlaBne8jgN1pzNYAViISHC0Hbnu56/rv1UZagpxN0/xzyioYl8ucjJub8iDXT11prPpZGE3uIacP6S96s6n7R7FFSPHu0d/TboiOvkuFnHia1Cgtsh0OlPoKugpEQCMSSAIJuOSwIuiVABN0NQOVUBF0ZqEIcgq4AUTECQU8PM8yGaTa7hCe3QFvQwwq3P+NuK8Uat6k/mz9xwNG7nPMdNj/qu/XpBL328pnS8Kn+UlbSSS4vGJbzI9ZM/zdvWCsHjW/noVg7aGkVEn93+V8V3iS/vebqsKiclkfQneIlHAJ5JYCgW+JH0C0BIuhuACqnIujKQBXiEHQFiIoRCHp6mP4O4kfut4dMHHBMRupa70qbi2gLelgxjZOgh217pkEK+6DCz0on6InLy8/59pqcH7Hmt6/26FbSsGCrrBv4gVTWa+h92d+87nf7/10GX3Cm4r8Y9lEIuj1DEiAQVwIIuuXIIOiWABF0NwCVUxF0ZaAKcQi6AkTFCAQ9NczVm3bIXa98IS8u/FZKGhfJlCvaSGFB+iXC/i7h57RuKsPPstuMK9+CPuzZxTJ1/hrRWK5vc6vaLEtPdV1/n4Cws/HpBN3foK20zaVy1KwBq7f8AAAgAElEQVTTvEtmexXChke6uqtGnSQtC7+UDf2mSXnTVl4xf/O6/9f61bTHA7poS5BMBD0IJcpAoGYSQNAtxw1BtwSIoLsBqJyKoCsDVYhD0BUgKkYg6KlhXvDQB7J4zdaqb/6sfTO5Ic056KaQzS7hyS3QFPQokusLuu3GbLa36fOf/EdunvqZHNG0WP758x/Zxonfr6Cb5fkXTCfo/hFn37S5Ro6bdULOj1jz27fioZ9J600zZMvpd8j2Fn3EX3q/oOJgebrDozKwc4k1O80ABF2TJlkQiBcBBN1yPBB0S4AIuhuAyqkIujJQhTgEXQGiYgSCvivMneUV0vHO2dW+cfg+xfLEpekl0WaXcJeCHmaZvt8OzeX6UW/V5xb8R3477bOq6n/ocYSc2XKfqHFevaj7BKQT9OI3RkjR++PkwT2ukBHfniyH7l1fJl3WxqqNUSp/8tiN0vnbSbKt42AxZ6H7M/uTyrtKwdl3yck/3DtKrLM6CLoztARDIO8EEHTLIUDQLQEi6G4AKqci6MpAFeIQdAWIihEIemqYJ/zlHSndWV71TSOHRhLTfVZsKJUeY99TmUXVnEH3BT3M+dxxEPTOo96RbTv+x79+3VoyY/BxVnd+1H7trCyUisI6Uq9ye7Xr+xuxXbnjBnmhor33vYuPO0AGn3SwVTvDVl429Q/SZukDYpbab+06VPwHB2YH944/vdV6c72w7clWHkHPRojvQ6DmEkDQLccOQbcEiKC7AaiciqArA1WIQ9AVICpGIOipYb69dL0MmvSpd6b0gU3qy6jeP5TmexdnJK+1oZmmoE+d/40Me3ZJqPO5o4qs4m0p7e6YJZWVlVWRBQUFMu/GTlaXiMLijpe/kInzVnrXPXSf+vJgv9bSqP53x+01fKqf1F4+S/ruuEVmVbT0vla3dqHMuqGjVTvDVv7o+XHSbeEI2XHo6WLOQk9s19+HXBY2znl5BN05Yi4AgbwRQNAt0SPolgARdDcAlVMRdGWgCnEIugJExQgEPT1M/4ivaQOPlQMaFWWl7pe33ShMU9CjyHYUkc0KJ2SBUa99KQ+/s6KqlsbMdNjVBGajwDPvnVet5ZcfXyK/OPFA72v+Tuldto+W5ZVNva+ZhzlPX5HbZe5vzpwhPef8TMqbtpQN/Z6RJmN/JAXbN0pneUimDjopJHn3xRF094y5AgTyRQBBtySPoFsCRNDdAFRORdCVgSrEIegKEBUjciHoH3y9ST5dvVnObrWv7FmvlmLr3UaFnRH3j/EKu0t4ci/yLeiaG95FHaHEVwaGnNrcWwFg+/EzD2hUT6YNbJs1zty3P39sfrVyiQ8K/J3SDymd4JUxs/x/P/8o6XhI46zZmgUmvr1Qfjm3u3fE2vpL3vQEfWNlsVy637/EnGUftw+CHrcRoT0Q0COAoFuyRNAtASLobgAqpyLoykAV4hB0BYiKEa4FfcTzn8vkD1Z7LTbLfx/o10paN9tTsQfuosIKun9+eNhdwl0KepSN0cLONLsYAXPMm9l1/aQjmshdvY5Su4QZ0wb1asnr12V/n72solJOvXuW9C2fJnsUbJep5Z3k6p6nyCk/3MtrT6Kgm8znrm4rxXW/W/6ey49Z8TDgle+W1W86e4w0eOYqmVXRQsYcfJcqO60+IehaJMmBQPwIIOiWY4KgWwJE0N0AVE5F0JWBKsQh6AoQFSNcCrrZZM1stpb46XXMfjK0+6GKPXAT5e/KHnS21bRC6/xwzRn0GyZ/Kq99tk7CPDTQ3JE+6uj4DxYGdj5Q9ZiwMA9dzIx7+UNny48KP/e6Yd6I33TBU1K2/7FSuHG5t8TdLG03S9zDbMIXlUm6embFQ8fnzpSSgjXee+h1P39RxpV1l9Udf6vKTqvdCLoWSXIgED8CCLrlmCDolgARdDcAlVMRdGWgCnEIugJExQiXgr55e5l0HT2nWmtPP2pv+fNPfqjYAzdRUWaRo7zvnar1moLuz+qbpc5GIoN8wi4FD5IZtky/8R/KwtVbvCXaQdsd5Br+awhB9hX45/SX5epFl1eLLW13lWzt/Ouqs8bNTHXfHUNF+0FCkL74ZcwDlU0Tfi4/rjVXKhqWeA8Pfr/zIjn4jGtVXg0I05YgZRH0IJQoA4GaSQBBtxw3BN0SIILuBqByKoKuDFQhDkFXgKgY4VLQTTMvfuQjmb9yc1WLe7RqKr8/+3DFHriJivIedhwFPcp78ZtKy6Tb3XMCLwXXHgH/+ib33ZuOV40P+sDCPKS44b6p8kK9m6oLevurZesJN0mdJc97S8lfKG8vV+68IdQKBdUOiYhp6zsPXi+X1p5eFW12lr/0gj6qDze02o2ga5EkBwLxI4CgW44Jgm4JEEF3A1A5FUFXBqoQh6ArQFSMcC3o28sq5Pi7Zldr8Z29jpSTj/juPd64fibMWykjX/5C+rdvJkNOOSRQM7V2P9ecQQ+zpDuxk1HrBQL130IfrdgsG7btlE7NG0vtwoKqqv7DEe1l41+tK5Vf/usTWb6+1NuV/4/nHC7HHNAgZZP91xUm1b1V2hcu8sqYJe7PdnxMju94ghS9/5AUv/F7byn578suliAz8mHYhC378F1DZHDtJ6uqmY3rbE8TCNuGoOUR9KCkKAeBmkcAQbccMwTdEmCa6sXFxVJRUSGlpaVuLkBqKAIIeihcOSmMoOcEc+CLuBZ0f6n4kfvt4S23NdJrNtS64Nj9pUFRbbmowwFS8D83C9xu1wWjzIZHWRafqh/fB0G/bMLH8t7yjV73929YTx65qLXsvUdd7+9R2Ae5H656/GN5Z9l31zQfI+lGrBM/81dukjeXrJf7317ufXnalW3k8JVTpejFX3t//+0RT8uvzjxG6s8eLfVnj5JRZed5/2nP9AfpT2KZP975ZxlZZ4z3peWV+0iX7XfnvU3p+oCghx1dykOg5hBA0C3HCkG3BJimOoLuhmvUVAQ9Kjl39RB0d2yjJLsW9OSl4ldM/FjmffU/STr1yL3ljp7xeyc9yu7nk95fLX984XOpVVggN5/eXHr/aL8oQyJagh51qfqcLzfIwH8u8Nrer10zufHUYCsIgnb2zSXr5LonP61W/MrOJXJV5+/OFw+6DD3o9fxy7W6f6c2CJ34SxXrxmq1y4T8+lMrK70qZUwde/EU72atBfdlz0oVS8OUM+VXhTfLba66W4jdGSNH747x3vT/8Qd+8H2d20/2T5YFtN3jtNsvu79xrqEwccExYRDkpj6DnBDMXgUBeCOx2gm5mXG+77TYP5i233CJFRUXen59++mkZMWKE9+fu3btX+55fZ/r07947Gjp0qPTs2TPQgCDogTCFLoSgh0bmtAKC7hRvpHAEPRI2Z5VcC3rybKhZ7m6WvaeTJGcdDRkcdvdz8569ed8+8XN3n6Oky6FNQl5Z1AQ9ym7sO8oqpMvoOVJW/r8xuv6kg+Wi/8/eeYBXVaR/+EshgUDovUd6UyBUQURcG4qCuipYsMeGsIpld3WR1f27a2FRbGBFWLDs2sBeAEWlhd57DYSehBYgyf+ZE+fm3Jt77inzzb0n8Xeex9Xkznwz885l9T0z802Phq7HYVXh0xV7aeyXm4I+vvPsxnRX32JB17W9/qFP1tH36w8G2m1dtzK9d3OJxL42dwdN+m3lXBYacW5TurNvc0pZPpkSvv2rsaX9/HsmUIPv7jGypd958gFK6z0k5tnS3313Io06/E+j298VdKWpzf/hyyvWRP8g6Gx/lBAIBHxHoFwJulm0zRKemZlJEyZMoPHjx1P16tWNfxbPiBEjjL+bfz58+DCNGjXK+Cw9Pd12wiDotog8FYCge8KmrRIEXRtaz4Eh6J7RaamoW9Cl6I4d2NLY4i6TlsnBJCbE04IHi+9w9tPjdhVXvogwj8FrZm+uFXQvW+5X7zlCN7wb/KLh2i716ZEL0timR7wEGPzGUtqTmx+I+a8rWtMFbWqR+UgE9wrwkfwCeuzzDfTjxkNUOSmBpg0/k5rUKF4MEc+bv+6il3/aHjRO8b29sktDqkj5VOH5FpRblEIfnz+Lrl8/khJ3ziORjO2Siy+Pabb0uOMHqcbrwf/dt7XnWKra8ya2OeMMBEHnpIlYIOAvAuVK0IVoN23a1CC8cOHCwCq5/L1cFTcLuyg7ZswYQ8rT0or/xRkq8JGmDIKu5wsNQdfD1WtUCLpXcvrqQdD1sfUSWbegh4rur1sP070frAl09clLW9KlHep46brWOm6u4xId+WXLYbrvw5Jxid/932Wt6OL2tV33k0vQZdK6/q1qOF5NPZp/ms4JuRrPvLrtejAWFbYePE5XvrE08Knso3zR4SY5n9s+Wa3QnzhVQDdNXUliq7t4zmqUSm8N60BJSUnGrsb4Sf0oYd9q+uzMV2jQukcpLj/XuAP9tTsvNM6zx+pJXv0BVf6u+Iy8fI53u5eOnz06Vl2K2C4E3ZfTgk6BAAuBciPoZqkW29mloAtKYst79+7dA9vWt2zZQk888YTxl3jkP0tBN9eXW+SPHCm53kaSF/+BfOjQIZaJQJBgAhB0f30jhKBXrlyZcnNLzrz6q4e/v96I+Th16hSdPHny9zd4H45YCHqlSpUoLy9PS++6/OsXI+6SR84OxF+/9yhd+/YySk1OpB9H9dDSrmrQcP22iznx5x0ktkmL57KOdUm8fPDyiH9H5+fnG39OVB7RF9Gn67s3dJyJXrQ3a8NB+suMDSSEtWuTqjTxug5BWdZV+iTriu33172z3EgYKJ68/AJ6fWgHI0GcWEUfJzL9t67F0VSpGP3Gzzfa+3FkDyNRofmRzC7vVJfE6rl4KlSoQMnJyVT4xSOUvORN+rnW1dTnwH+Nz0S2dPN3W0uHbYLG5+2iam/1CSqV2/tRKuhxVyy6Y9tmjRruj33YBkUBEAABXxAoF4IuhHr79u2BLevhBH3IkCGBLeuhgi62vo8dO9bY/i6ecILev3//UhM2e/bsQBIUX8wmOgECGgnExcXh+66Rr9vQmA+3xPSX1zUnucdP0Vl//5aqVkykZWMuDBpI2p+/MH7e8vRA/QP00ILX/mVMzaRvVmXTs1d3oqvTi89Uu3245mP8dxvohe830MjzW9GoP7Ry1Y23ft5CT85cQ7f2TaPHL23nqq6TwvM2H6Chr8+nXmfUop5pNY1+Nq5RiXYeOm5UX/a3C6hqpQpOQrkuc+mEubQ6K5c+H9GH2jesFlTfipkxJ1t+pLjJg4xt7lXjilfZr2v4NU2/I/ZHNJZ/MYk6LHiUEuMK6YuCHtTnzzOpaqXirPh+ewRLPCAAAuWTQLkQdLF6Pnny5FIzJM6hjx49mp577jnlFXSr6ccWdz1/MLCCroer16jY4u6VnL562OKuj62XyDq3uEc6A33uCwuMVcxY3x8djllWzgm6bOISY3V3zkh3K/xesr+H9oFri7tKX0Kz73v5bkWqExr/j28to037i6VXPB/ccha1rJPC3awRT95xPnZgCxrUsW5QG/JIxvODWwdW8MUKutiVKHaZ1Jh4lrG1XTw7i+rQlG4fxTxBnOiLPM5QfFt7nG+vWBN9xRZ3LV9rBAUBXxAoF4IeSjJ0BRxn0H3xXXPVCQi6K1zaC0PQtSN23QAE3TUyrRV0Cvq0zN3GvefhzhMPnbyc1mUfNa6n6tY0eBVT64AdBPeSXE2G5bjDm0vQ3WaiN6ORLykaVkummRldHVBzVyT0u9Hz+fl0ypQ5XmS/F1nwdTyR5ijc99Is6PJ6NdGveYXtaN0fJsc0QZzkI194iJ/b1Kvs2yvWIOg6vtGICQL+IfC7EHRkcffPF85pTyDoTklFpxwEPTqc3bQCQXdDS39ZnYIeSYQirWLqH3XkFqTsiBcH4gWCm0euZIqM9fIMs5v6oiyXoLvNRB/aT13XnYl2zN+NO85uTN2e/TWo+WqVEmnWiO5u0TkqL18OhJujcGM2C/rp9d9R3a/uMNrZWVSbDt/wHTWsFfsXTPJKPdEvL99bR+CYCmEFnQkkwoCADwn8LgRdcMc96D789kXoEgTdX/MFQffXfIjeQND9NSc6BT3SCi7HSrMukpFW/u3aVJF7GZtL0OVq8PThnahNvSp2XS/1uc5jCHL7vbyKbtjk5bQ2+2igDzd0b0gPnNfMdZ+dVLCao7wTp+ncFxcaIRY/3DsQyizoqR9dTxV2Fic+FM/JVgPpyCUvO2lWaxm540E0ojMDPscgIOgcFBEDBPxJoFwKejRR4wy6HtoQdD1cvUaFoHslp68eBF0fWy+RdQp6JEFUkWAv43RTR+XlgVzJVNkaziXocjV4zv3dS2Urd8JD5zGE0Jc3+4+cpDd+3UlbD+bTmQ2r0N19G5OuZGJWOQas7mA3C3rNF0vfB3/w/i1OcGots3B7DmW8t9po46J2tejpQa21tqcSHIKuQg91QcDfBCDoivMDQVcEaFEdgq6Hq9eoEHSv5PTVg6DrY+slsk5Bj7RFWncSMi8sZJ3Q1V03saxWYd3E4BZ082qwm37oPIaguv3ezTjClQ333ZQvjULvjTcLev4L6dQg7mAg5PqiJlR75I+q3VGqL75z5720iAoLRYK44kdc8XdphzpKcXVVhqDrIou4IBB7AhB0xTmAoCsChKDrAcgcFYLODJQhHASdASJjCF2CLkXVKhM6x0ozI4agUHJ1N1yWbydtqp7d5hB0jhcFKjsJ7Dipbr+3i2/3+WUTF1NWTn7QLQJW4zUL+sh/v0Mvxz9DVeOO0/6iqvRgwqP07/uG2jWn9fNPV+ylsV9uCmrj9t6N6Z5zvF3zp7WzyOKuGy/ig0BMCUDQFfFD0BUBQtD1AGSOCkFnBsoQDoLOAJExhC5Bt8uEziGQjBiCQqmu7oaTPzd95RB0+QJEJaN3pGRqbsYTrqw83+51+71q++HyI8jfjT6/OQ1LbxBowizonyzfS3//aiMl0ynKpwr07OC2dH7rmqrdUaq//dAJGvz6kqAY9/VrSrf2aqQUV1dlrKDrIou4IBB7AhB0xTmAoCsChKDrAcgcFYLODJQhHASdASJjCF2C7uSMeawlzQqj6uqu6tltDkG3e0Hi5CvEkfDOqh3VXQZO+h+pTLg74q1ezJgFXcRcv/co/bTpEA1oXYvSalVS7QpL/WmLdpMYk3h6Nq9GE65qS4kJ8SyxuYNA0LmJIh4I+IcABF1xLiDoigAh6HoAMkeFoDMDZQgHQWeAyBhCl6A72R6tKsKMGIJCqSZXUz27zSHoHGf8rZKpqXK3O/6gGt9J/XDfT6sXRqGC7iR+LMoUFREVFBVSYrw/xVwygaDH4tuBNkEgOgQg6IqcIeiKACHoegAyR4WgMwNlCAdBZ4DIGEKXoDvZJh7pGjbGIboOpbq6q5JkTnSWQ9Cd7GBwAkaVRbg2OLbfO+l7pDLyvnpzQjirsZYVQVdlEq36EPRokUY7IBB9AhB0ReYQdEWAEHQ9AJmjQtCZgTKEg6AzQGQMoUvQnayOh9tmzDg0T6E4Vned7B6I1DkOQVftg+yfjrvQObbfe5pcU6XQlwSRkhZC0FVpB9eHoPPyRDQQ8BMBCLribEDQFQFC0PUAZI4KQWcGyhAOgs4AkTGELkF3svLKJZGMOMjqLmw3bcjV2UEd69DYgS3dVDXKcgi66iq+7LR80fL84NZ0XutarscSrgLH9nvVjoS+iIl03h6Crkobgs5LENFAwL8EIOiKcwNBVwQIQdcDkDkqBJ0ZKEM4CDoDRMYQOgTd6RZmVZFlxBAIxbG6q5pcjUPQVa+Kk0DkefrQzOYq7Lm236v0QdQ1nzmflrmHJv68g4Z1a0CjBzQPCg1BVyUNQecliGgg4F8CEHTFuYGgKwKEoOsByBwVgs4MlCEcBJ0BImMIHYLuVFCdijzjcG1DcVwtJpOrNayWTDMzutq2GVqAQ9Cd5ABw0jEduxx0xHQyltAy5mMYM1btJ5EJPaNPE8ro0xiC7gWowzrY4u4QFIqBQBkkAEFXnDQIuiJACLoegMxRIejMQBnCQdAZIDKG0CHoT369iT5etjes7Ji7znHemxGFEYpDHlXveOcQdCc5AJyw07HLwS+5B8y7DGas3G8cbwi3lR8r6E6+Kc7LQNCds0JJEChrBCDoijMGQVcECEHXA5A5KgSdGShDOAg6A0TGENyCftu0VbRkZ67Rw2qVKtB/bupEYiXZ6nFyVp1xuLahOARdNKIyLg5B57pjnuNMfih0v2TvN8/17I0HaV32UZo+vBO1qVclqMsQdNs/Nq4KQNBd4UJhEChTBCDoitMFQVcECEHXA5A5KgSdGShDOAg6A0TGEJyCvjb7CA2bvCKod7f3bkz3nNPEsseXTVxMWTn5NDOjCzWsVpFxZN5Cqd5hLltVGReHoKu8IDCT03EXOtf2e28zXFLLfBZebG8Xz+KHe5cKC0FXJR1cH4LOyxPRQMBPBCDoirMBQVcECEHXA5A5KgSdGShDOAg6A0TGEJyCnrkjh+6Yvjqod7f2akT39Wtq2WO/yJrsIFd/VOL4SdAFFy7Zl4y5tt+r/jEw50oQOwVSkxNozsgeEHRVsDb1IeiaASM8CMSQAARdET4EXREgBF0PQOaoEHRmoAzhIOgMEBlDcAp6YVERXf3mUtp68ESgh+/c0JHObJhq2WOuFWsuJFzyqDIuVUGPdKe3F04quwHCtSe334dbrfbSP6915O4AWb9b02o06br2EHSvQB3Wg6A7BIViIFAGCUDQFScNgq4IEIKuByBzVAg6M1CGcBB0BoiMITgFXXRrT95JGvhqprEa+eq17al9/eDzvKFd5zrzzYWE6+y2yj3kqoLOcVWcmafcDcB1Fzr3irzK3Mu+iBj9W9WgcUPaQtBVgDqoC0F3AAlFQKCMEoCgK04cBF0RIARdD0DmqBB0ZqAM4SDoDBAZQ3ALutur0/xyJ7ZEyiWPKi8eVAVdbt22Ek63Xx/OrOt+y9wvX8gIJuGuWBO/xxl0t9+YyOUh6Lw8EQ0E/EQAgq44GxB0RYAQdD0AmaNC0JmBMoSDoDuHeCT/NMXHxVFKUoLzSi5Lcgu629Vbbpl0Ofyg4qrXo5mDqVxPpiro3C89VF42hM6H2xc4KvPppK480iDKjj6/OQ1Lb1CqGgTdCUnnZSDozlmhJAiUNQIQdMUZg6ArAoSg6wHIHBWCzgyUIRwE3R5iUVER3fX+Glq4Pcco3K9lTRp/ZRv7ih5KcAu6WzmU54DFVWwzM7p6GAFfFU55dPuiwjwKVUH/2+cbaeaqfXR770Z0zznWCfqcklN52RDahgoXp/11U07mChB1xPlzcQ499IGguyFqXxaCbs8IJUCgrBKAoCvOHARdESAEXQ9A5qgQdGagDOEg6PYQP1i8h/753Zaggk9d2pIGdqhjX9llCW5B97LayrWt3OXQSxXnlEeVFw+RBH32hoP01Zr9VKlCAg0+sw6d1ahq0DhGfbSWftx4KPC7CVe3oz5nVFdCw/niwk87JgSUJ7/aRB8v32vweWNoB+raJJin+D0EXenrU6oyBJ2XJ6KBgJ8IQNAVZwOCrggQgq4HIHNUCDozUIZwEHR7iFJyzSUz+jQ2zshyP9yC7uW8MldiNlU2cqWY4+y2ynZ5K0Gfvy2H7n6/5Bo7cfzhm3vTqWZKBWPo+acLqfe4+UEYzm9Ti569orUSGq/nxpdn5RntmrP4yx0WVue9lTrqsvI3aw/Qo5+tD9SqWjGRvr03nSokxAdFgqC7BGtTHILOyxPRQMBPBCDoirMBQVcECEHXA5A5KgSdGShDOAi6PcRVu4/QjVNWBBX89I4u1KRGRfvKLktwC7qX+7+5rjZzOfRSxb2s/kdq0+vOACtBf23uDpr0y86gJkf2b0bDezTUKugFhUXU/bl5RhvPDW5DA1rXjIhalB8+dSWt3nPEKCcy+U++oSMlxMcRN2OVOf/3rG00ZWFWUIi3hnWkzo2DrwWEoKtQLl0Xgs7LE9FAwE8EIOiKswFBVwQIQdcDkDkqBJ0ZKEM4CLoziG/P30UT5mw3CndtXJXeGNbBWUWXpbgF3Ytsq9wZ7nK4EYtzy6PX+8OtBN38nZADeeWa9tSrecm56ZEfraWfmLe43/X+alqwrTgfgnjGDmxJgzpaH7eYlrmHnvs++IjG6PPTaFh6ffKyw4Jzjs2xXvlpO73x666g8F/d3ZXqpiYH/Q6CzjsDEHRenogGAn4iAEFXnA0IuiJACLoegMxRIejMQBnCQdCdQZRbgWXpOfd3p9SKic4quyjFLeheVo2lGFtl0XYxHMuiOw6doC9W76Mm1StanuWXLwq4+uFlN4EYgJWgny4sojumr6Jlu4q3jteqnGRsyTY/D3y8lmZvOGQI9LBuDahN3cpK+E4VFFLP54O3zYtEaiKhmtUz8ecdxkq5+ZFHNGT/uO5UVxnckZMFdMe0VbRu71EjzK29GtF9/Uon1YOgq1AuXReCzssT0UDATwQg6IqzAUFXBAhB1wOQOSoEnRkoQzgIujOIoefQdZ3Z9YOg//PbzfTBkmxKb1qN/nJBc0qrleIMksNSC7flUIbp7Ha4rPirdufRw59uoN25+fTnC9Loj13qO4xuXczrzoBISeJCX9yYM4/LxHSiRzMzulDDaupHIszb2+VIz2tVk54fYn2rwJ7cfLr89aV0uqDQqJKYEE+f3dGZ6ldNJq8vLZQnI0IAccY+KSGOkiuEv84Qgs5LH4LOyxPRQMBPBCDoirMBQVcECEHXA5A5KgSdGShDOAi6M4hypVFsCxZbhtvUq0zTh5/prLKLUpyC7iVz+Q/rD9LoT9YFelyzcgX66u50SoyPczGKyEUf/3wjfb5qX1ChuaN6BO6Xzzl+mi58JZPEarF8Jl7XnrqHuXLLTae8bpmPJOihL27EKvnoAc2NbsnPxOq52IbO9bzy0w5649fiFfG4uDiaeK24jqx0tnNze+bry8wr7vIIBNcLBK4xRooDQeelDEHn5YloIK7fxDgAACAASURBVOAnAhB0xdmAoCsChKDrAcgcFYLODJQhHATdGUTzSuODH6+jvPwCy3uanUUMX4pT0L1cUzZ+9jZ6d0Fwoq63r+9IZzUKTtSlMsbHPt9IX4QI+o8ju1OV5OIjA6J90Q/zc2efJnRXn8YqzQaE2SzRTgJGEnT54mbswBY05otNlJqcYNwfL44/yDPvOraPT12YReNmbSOnGe5lX0T/xHdXHtHwcgTCCTOdZSDovHQh6Lw8EQ0E/EQAgq44GxB0RYAQdD0AmaNC0JmBMoSDoDuDaL56bOIvO2naot3GmWK5Wuosin0pTkH3ck2ZeXVW9vbLu9OpXmqSfecdlvhly2G678M1gdJibX6GaQv43M2H6P7/rg2Kdm+/pnRbr0YOWwhfTPJwu6IdSdDNL24m/bKLxEsRcWa+dZ0UY/t4w2rJhrBzP27uQjfvpOjfqqbx3ZXn+iHo3DNT9uJB0MvenKHHIOCUAATdKSmLchB0RYAQdD0AmaNC0JmBMoSDoDuDaBYZuTItViPnjOzhLIDDUpyC7mVLt9heftv0lbR5/3Gjx0PTG9BD5xdv2eZ8Xp27g143XVEmkqdNv7n4yIDMLC7b69CgCr05rAMlhdyH7bY/XnYUiDYiCbr5xY1IbiakXHwvhAjPWLmPdOUqcHMXuuiH2OIuXkyIv0QfxTZ3sbJ/7osLjf5yf4/dzo2b8lhBd0PLviwE3Z4RSoBAWSUAQVecOQi6IkAIuh6AzFEh6MxAGcJB0O0hhpMhKWbmpGD2kexLxFrQZQ+9XklmP8LiEvJMtBBYsbKdlZNPPZpVo8pJCTRrw0FDGt8Y1pFqV65ANVIqOA0bsZyXM/l2gh66Ai2/F7IjOs92O139LkmOV3wdm/m7K2RdVz4FlkkLEwSCzksWgs7LE9FAwE8EIOiKswFBVwQIQdcDkDkqBJ0ZKEM4CLo9xHArr//3zWb679Jso/If2tSipwe1ogSGRGqcgq5yhZZKXXuiFJQ9XGRqF+e3zc9fLjyDru5cz0kox2W+XXuAHvlsvVH+0o516EmHidusVtBDt5mLa9cufjWTDh49FejTc4Pb0IDWNR330U1Bpy9RpJDLlwVyh4I4vy6ugLO7ps1Nn6JRFoLOSxmCzssT0UDATwQg6IqzAUFXBAhB1wOQOSoEnRkoQzgIuj3E0LPLx08VUJ9/LwiqKBKYiURmqg+noKtcoSUljusO8lAuMnv49OGdaPbGwyTu6jY/d/VtQneerZYUzhwvOy+fLnl1cVAbD57XjK7v3tB2yqwEPfTFjbgP/Zb/rAyKd1OPhjSqfzPbNrwUkPMbKQmdfIlgPgtvPqIhEsa5PZPvpa+cdSDonDSJIOi8PBENBPxEAIKuOBsQdEWAEHQ9AJmjQtCZgTKEg6DbQww9y716zxG64d0VQRWv7VKfHrkgzT6YTQlOQTefjxZZxd08Xs6vu4lv7tvMVfvo2e+3BlUX15IJceR6Plm+l/7+VfAqvUg6J5LP2T1Wgi7vQJfJArPzTtIlr2YGhbu9d2O65xz1Fzfh+ii3rkd6iSL7GCrh5q34us7J23H1+jkE3Su58PUg6Lw8EQ0E/EQAgq44GxB0RYAQdD0AmaNC0JmBMoSDoNtDDF1Nzj9dSGf/ez4VFZXUHdGvKd2imGVcROMUdKdnlMMR8Jrx3J5mcQlz3wqLiuj26ato6c484zNxFv21a9s7DeWo3P4jJ4271c3Poxek0TVd6tvWtxL0cC8xxIuG6Zm7jZhn1K5Ebw7tSNUquXs5Ytuh3wo4eYlScg1c8AsPcyI+CLpT4uWzHAS9fM4rRgUCggAEXfF7AEFXBAhB1wOQOSoEnRkoQzgIuj3EcFvF52/NoSe+3Ehi1bRdvco09aZOFBcnLgxTe7gE3U2W73A99prx3MnorfomtqEnxMVR7Sp8V7qZ+zN30yEa+9VmOnD0JJ2dVp1e+mM7J921zOJuvgN9UMe6gVi5J06T+Ktx9YqO4nst5OQlinwREpqsTlxzJ667E098XBy9fX0H6tSQ7657r2NyUg8r6E4oOS8DQXfOCiVBoKwRgKArzhgEXREgBF0PQOaoEHRmoAzhIOj2EM3npdvUqxKoMGv9AXrwk/WsWbC5BF1VsL1mPLenScZd4bHKHh66LT1Sf4uKigyh/2zFXqpWqQI9ObAF9W1RI1BF5Yy/E052Zew4ys9D72I/kl9A/V4IzqEgt+nbtemHzyHovLMAQefliWgg4CcCEHTF2YCgKwKEoOsByBwVgs4MlCEcBN0eotVWcdVV6nAtcwm6fHkgMnWPG9LWfpBhSqhskY/UoOxbLLKHu+Hy7oIsGj97W9BQfhzZg6okJxi/Uznj72lCQirJlyhW95jLLfCh588PHz9FAyYsCop2bZd69MgFZ3B0S3sMCDovYgg6L09EAwE/EYCgK84GBF0RIARdD0DmqBB0ZqAM4SDo9hAjiSq3pHEJupuVYisC3GOT7XD0zX7WwpdwsjOgsEhs+yYaN2sbTV2YFRRo0tAO1K1JVeN3ul5guBmbVR/emb+Lpi7abVz5NuLcpnRLz0ZBYW+eupKWZxWf+ReP2O4vtv2XhQeCzjtLEHRenogGAn4iAEFXnA0IuiJACLoegMxRIejMQBnCQdAjQwy96zq0tNz+HumqKzfTxCXoThKI2fXLamu/XT27zzn6ZtdGpM+tpFZk5x/9yXrak5tPYlVaXEEW+sgVdLvVa5X+uakbese5qPve4j30zHdbAmFqpFSg7+7tRuYUCQWFRfSfRbvpmLgyMK16mTl/LgYFQXfzDbEvC0G3Z4QSIFBWCUDQFWcOgq4IEIKuByBzVAg6M1CGcBD0yBDlOV6rreLc94VzCbqTK7jsvj4yBtfLB9keNzO7cYR+ftnExZSVk0+hidOGvrOc1u09GlQ8vUlVytyRa/xOZOkX2frFo3rG322frcrLlyiTrmtP4siAeP4yYwN9tWZ/UJWv70mnOpqS73GNxWkcCLpTUs7KQdCdcUIpECiLBCDoirMGQVcECEHXA5A5KgSdGShDOAh6ZIh227HtPnc7RVyCzpHATK50R7pn2+34RHnZN27xd9oXmX09tH25sm6Os/jh3vS/lQfpH1+sM+5lF/ezi8fqfnGnfeAqJ1+ijB3YgmQmeXFuXpyfNz/zHuhJSYnxXM3GNA4EnRc/BJ2XJ6KBgJ8IQNAVZwOCrggQgq4HIHNUCDozUIZwEPTIEO22Y9tl0nY7RVyCzrE9nfvlg2TB8fLALVdzeasV/Ac/XkezNhwMFBWr568P7UA5pxPovHFzjW3vc0b2MD63+16o9M9N3XD9EFe8/fGtZbTvyEnj6r8nLjkjIO9uYvu1LASdd2Yg6Lw8EQ0E/EQAgq44GxB0RYAQdD0AmaNC0JmBMoSDoEeGaLXaKmtxZ3LnEnSOBGYy43loFnDVr52u5HNO+yWlNvRqsWMnC6jv+OLrx/7Qphb95cIzqHqlROMe9PR//GDcbT59eCcSV+3Fepu+HKvVSxTJeMadXaiR5vvYnXLnKgdB5yJZHAeCzssT0UDATwQg6IqzAUFXBAhB1wOQOSoEnRkoQzgIemSITlZ7OYXTT4JulyDP69eP4+WB17ZFvUhXrYXrmxD0sTNW0dQFu0hu93fyvVDpo9O64cYiE9iJGGKLfnl7IOi8MwpB5+WJaCDgJwIQdMXZgKArAoSg6wHIHBWCzgyUIRwEPTJEJ/LNeaaaQ9C5xJp7d4AgLWPGUh6trlqzOq4gBP39BVvpsRnrjURsIiGb/F7IFXWGP4qeQoQbSyzvmfc0CJeVIOgugdkUh6Dz8kQ0EPATAQi64mxA0BUBQtD1AGSOCkFnBsoQDoIeGaKT1V7O7c4cgs6ZYdzJ+N18DbleHrhpM1zZcOOySvwmBH3voTzqM+5XI5RYlebmojKe0L745Xy8ypgi1YWg85KFoPPyRDQQ8BMBCLribEDQFQFC0PUAZI4KQWcGyhAOgm4N0ekKMmcyNQ5B58wwbnUlmdevHufLA699EPXC3R9u9aJFCPqJEyfo6jcyaV32URLZ3x/8ZH1Q0jiVvqjWDd3lwbmjQ7VvOupD0HmpQtB5eSIaCPiJAARdcTYg6IoAIeh6ADJHhaAzA2UIB0G3huhUJjlXhTkEnXMFVSbJM9+zrfK143x5oNKPcBJrda5cCvrTX2+gaYt2U5t6lQ1Rl9vdVfrBUTc0Y79cUZ9zf3dKrZjI0YSvYkDQeacDgs7LE9FAwE8EIOiKswFBVwQIQdcDkDkqBJ0ZKEM4CLo1xEjJxMy1nK60O5kuDkHn3HIf7p5tJ+OwKsP58kClH+EYWYmtFPRfN+037nCXT/9WNWjckLYq3WCpa56j1nVSaOjkFdSwWjLNzOjKEt9vQSDovDMCQefliWgg4CcCEHTF2YCgKwKEoOsByBwVgs4MlCEcBN0aohuZDLdl2sv0cAg65xZnNwycjFeKcUafJpTRp7GTKlrKhF61JndBhBNbKeinTp0KnD0XnYr1GCQY8xyJ/gth98vLAx2TB0HnpQpB5+WJaCDgJwIQdMXZgKArAoSg6wHIHBWCzgyUIRwE3Rqim5Vou/vSnU4Vh6Bf+/Yy2rDvmJFtXGzDVnlmrNxLY77YRKF3hnuNycXJa/uyXujuiBkr91mKrVnQ5csPEUdeuabaF9X65hwIYjeHGItfXh6oji1cfQg6L1UIOi9PRAMBPxGAoCvOBgRdESAEXQ9A5qgQdGagDOEg6NYQ3dx1zbUybCfov2w5TCuyjlCnhlXo7LTqQZ0/XVhEd0xfRct25Rm/v7Btbfrn5a2UviVOt/k7bcQNU6cxvZQLXTGPtFPALOh3f7Ca5m/NMZrs0qQqvX5de4qPi/PSBbY65jnanXvSOB/P8XKGrYPMgSDovEAh6Lw8EQ0E/EQAgq44GxB0RYAQdD0AmaNC0JmBMoSDoFtDDE2+FQk3V/KzSII+ddFuGvfD1kA3HhjQnG7o1iDw89vzd9GEOduDuvnKNe2pV3Pvq+hWd4Z7/erJrPCxvj9c9N98PVmkYwFS0L9elU2jP1kXNPTQOfDKRaWeOUmhkHPxiKvgyusDQeedWQg6L09EAwE/EYCgK84GBF0RIARdD0DmqBB0ZqAM4SDopSEWFRE9/Nl6+n7dAePDJy9tSZd2qBORNlcm90iC3uO5eSRWyeWTGB9HC0b3Cvz82twdNOmXnUH9HNm/GQ3v0VDpm+L2zu89eSfpwY/X0Zo9R6huahI9PagVdWlc1eiD21hKHbepbM4bMPSd5ZSXX0AzM7pQw2oVg2pKQX/1xy30yk87gj67s09juqtPE53dtI0tkxTKgiLL/PThZ9rWK6sFIOi8MwdB5+WJaCDgJwIQdMXZgKArAoSg6wHIHBWCzgyUIRwEvTTEyfN30QshK9Hf3ptOtSonWRI3S5LK6qUbQRedGTuwJe06fMLo18xV+ygrJz/QR7H1+pt706lmSgWlb0roPdt2wf700Tqas/FgoFidKkn09T3pvhN0uUNi7MAWxjl78YSbOynoOw8epYGvZlLJKxKiN4d1CLx8sOOi83M5R6KNQR3rGN+L8vpA0HlnFoLOyxPRQMBPBCDoirMBQVcECEHXA5A5KgSdGShDOAh6aYgvztlO78zfFfTBi1e1pb4talgS33/kJF34Sqbx+YDWNemJS1pSleQE1zPkZot7uOBi9bRZjYpUqUICDT6zDp3VqHjlWuVxs9VftNNr3Hw6ebowqMnMh3rT+r1HfHUFmMwbIDKez95wyPJec/MZ9MU7cunTFXvpVGERDWxXO+J3QoW527pyjkQ9IedC0svrA0HnnVkIOi9PRAMBPxGAoCvOBgRdESAEXQ9A5qgQdGagDOEg6KUhfrQsm576enPQBz+O7BFRuG/5z8pAcjZRsV/LGjT+Svd3ZNslibvqzaW05cBxeurSlsaK+bzfEpbJzn5021nUvFYKwzejJITbzOuPf76RPl+1LxCgec1K9NHtnWnR9hzjHnGRWV4kMYv1IxPDyX5YZao3C3qs+2zV/p8+XkdzNhTvWvDD+X6dnCDovHQh6Lw8EQ0E/EQAgq44GxB0RYAQdD0AmaNC0JmBMoSDoIeHOOqjtfTjxkNUv2oSPX5xC+rdPDhjemgtebba/HsvW93tBN18hvuFOdto8vysoK7Mur87VauYyPDNKAnh5ro5sdVfrOaKrfYit7nYDi7FV17Z5pc7umV/5Eitrk3zu6CL76n4vsrnmi716dEL0li/A34KBkHnnQ0IOi9PRAMBPxGAoCvOBgRdESAEXQ9A5qgQdGagDOEg6OEhupFSEeHy15fQzkPFZ8HF075+FZp6UyfXMxRJ0OU599TkBJozsoexkj586go6kl9gtHNeq5r0/JA2rtu0qxDpCrLQumO+2Gjcw92mbmV6cEAzY8Vc9HdmRlealrmHJv68wzd3dMvEfnIMVleT+V3Qr35rGW3efyxoKsSRghjf/mb3tfL8OQTdM7qwFSHovDwRDQT8RACCrjgbEHRFgBB0PQCZo0LQmYEyhIOgh4fo9r7u5Vl59MinGyg7L58qVoind67vSK3rVnY9Q5EEPdwW8WMnC4zt5Gm1Khlbx3U8HyzeTf/8bqsh3ROubku1qwQnyyssKjLke3dOPk3P3G10QWZDlxzF6nTeiQJfCXpo9nOrHQ9+F/TBry+l7YeOBwv6w72NHQzl8YGg884qBJ2XJ6KBgJ8IQNAVZwOCrggQgq4HIHNUCDozUIZwEPTwEN0mRhNRQle4vUxPJEGXd61bnZX20p5dne0Hj9NVby2jgt+ud6tVuQJ9cntnqpxcso3+ng9WB52Fvy69Pj18fvEW61kbDhpXrgm5b103xVhdF1nTB3Wsa9e09s/35ObTwNcWG+2kJieSyNSflBhfql2/C/o3a/fTo59tCPT74na16f8GtdLOL1YNQNB5yUPQeXkiGgj4iUC5EfQJEybQ5MmTA2wnTpxI6enF18OI59NPP6Unn3zS+OeLL76YHnvsMapYsfjO1BMnTtBTTz1FX331lfHz448/TldccYWjeYKgO8LkulBKSgoVFhYac4Mn9gQg6LGfg9AeQNDDz4nX+7rdXkkW2nokQXez1ZzrmxbubvVR/ZvRTb/drX7qdCH1HDc/qLkezarRa9eWJIGTTMRWd3HXuNVWcq4+O41z3TvLaP3ekq3hV3SqS2MuaVGqut8FXXR44fYcytyeSw2rV6TLy3EGdzFWCLrTb7izchB0Z5xQCgTKIoFyIeiHDx+mKVOm0B133GFI95YtW+iJJ54w/kpLS6PMzEwSAj9+/HiqXr268c/iGTFihPF3888i1qhRo4zPzIJvNbkQdD1fewi6Hq5eo0LQvZLTV0+3oH+99gAt25lLlSrEG8nCIt0lrm+U7iKrrIR7WXk39y6SoLvNpu5u1OFLT/plJwlJNz9/vfAMuqpzPeNXpwuLqMdz84I+79+qJo0znYUPzZbuhyzjRUVE6c/+GtTvpIR4mvdgzzIp6BxzXVZiQNB5ZwqCzssT0UDATwTKhaCHAg2VbCHgTZs2DayKm4Vd1B0zZowh5ULmQ4XdbrIg6HaEvH0OQffGTVctCLoust7j6hT0L1fvp7/OLNl627xWJfrots7eOxulmirXgckkaVYZwe2GEEnQpfxHcwU6L/80XT95Be08XLwLSSS/e/fGjhRvykA2btY2mrqwJJu8WD0Xq+jyCT3r/beLW9DgM2O/xf28CQsp5/jpQD97Na9Gr1xT+vq3srCCbve9Kk+fQ9B5ZxOCzssT0UDATwTKpaALARfSLcS8QYMGxvb17t27BwTdvMIuJsO82i5+FtvhFy5cGLQN3mrSIOh6vs4QdD1cvUaFoHslp6+eTkH/v28203+XZgd1/pt706l25eAkY/pG5y2yPOvt5Tow1W3okQRdbrufc393SmW+Si0SqZOnC+nRGRtotjhPPqA5Xd+tQVBx846DD2/tTHVTg+f3f8uy6R8h98p/fU861QlJNudttrzXWrgthx78ZD0dyT9NzWpWonGDW1Na7dJ3yEPQvTPWUROCzksVgs7LE9FAwE8EypWgC/EWW9P37NlD8gy6PF8+ZMiQwJb1UEEXW9/Hjh1rbH+3EvS5c+eWmre+fftSXl6en+az3PQlOTmZioqK6OTJk+VmTGV5IPHx8cbxkWPHgq8EKstjKut9F/Nx+vRp4y/uZ9z3m+nteTuDws5/6GxKSeK9p5u736/8uI1e/Wkb3X1OM7qnXzNX4T9dvocem7GeBrSpRS9c3cFVXVFYvMQS/78V+mck9/gp6jPuV6paMZF+fvBs13FVK0xduIv+9c0muqFHI3rkguBz2gu3HaZbpy6n7s2q01s3nFmqqRdnb6XXf94e9Pvnr2xHF7aro9otlvqnCgqpQkLp5HAyeKVKlYx/hxQUFF9nhye2BMSfkaSkJDp+PDhzfWx7VXZbFy+g8IAACJRPAuVK0OUUmbe4d+jQgWUFXWyBD32E2EMg9fzBEP8iF4IuEsXhiT2BuLg4EiuEp06din1n0AODgJgP8edDx5+R7QeP0Q1vZ9KenOLt0cmJCfTliF7UuEbpVUo/Tcc905fSd2v20V8HtqHhvZq66tqa3bl0xavzqXGNSvTDn/q6qisKiz8j4v+3Ql+YzN9ykG58O5N6ptWkKbeUJC513YDHCt+t2Uv3TF9Gf2hXh14ZGnxMYfK87fSPL9bR8N5N6a+XlL6H/b1FO+lvn60JannuQ/2obmqyx95Et5rOPyPRHUn5aE286BV/6XipWD4IuRuFeNmBBwRAoHwSKJeCLqbKfO4cZ9DL3pcXW9z9NWfY4u6v+RC90bnFXcSXic0aVkumrJx8465ucYbaz4/bO9BDx+I1A7x8YSL+fys3NzcorLieTJxv97LtnoN1pMR5z/2wlaYt2k2Rzt0/8/0Wej9zD1VKiqcnLmlJf2hTi6NbUYmBLe5Rwey4EWxxd4zKUUFscXeECYVAoEwSKBeCLrasz5o1i2699VZjEuRWd7FtXWRiRxb3svfdhKD7a84g6P6aj2gIukxs9vzg1vTEl5uMa7buP7cpDTmzHlWr5M+t7qpXpV02cbHxMmJmRhdqWK34Gk6nj9UZdNWz7U7bj1TOiovqCw2OvumMAUHXSdd9bAi6e2aRakDQeXkiGgj4iUC5EPTQe8wFYNyD7qevmfu+QNDdM9NZA4Kuk6632LpX0M2ryT+sP0CjP1kf6Gjnxqn0xtAOQRnBvY2Ct5bKCrjoicp1aFaCLmN6zQ7PQcgqi7zqCw2OvumMAUHXSdd9bAi6e2YQdF5miAYCZYVAuRD0WMJGFnc99CHoerh6jQpB90pOXz2dgh66LVpu0zaP5qHzm9PQ9OCs4PpGax95XfYRGjp5BbWpV5mmDy+d8Mw+ApGTLd9WcawE3Q+r1OHGpXJnvBOWfigDQffDLJT0AYLOOx9YQefliWgg4CcCEHTF2YCgKwK0qA5B18PVa1QIuldy+urpFPTQ+8Qn/bKTXpu7I2gwGX2aUEafxvoG6DLyrPUHjKu3VM7Ky2vahnVrQKMHNHfVAytB98MqdbhxqdwZ7wpMDAtD0GMIP0zTEHTe+YCg8/JENBDwEwEIuuJsQNAVAULQ9QBkjgpBZwbKEE6noIcKXVbOCRo0cQkVmfo9bfiZ1LZeZYaR8ISQZ729yLXsgZR8LwndrARddds9Bx05LvPuApWXERx9ikYMCHo0KDtvA4LunJWTkhB0J5RQBgTKJgEIuuK8QdAVAULQ9QBkjgpBZwbKEE6noIdLbLZkZy699NMOWrIjl27r3YjuPcfdNWYMQ44YgiMZm8q273CCLlepVbbdc3ALNy6V7fwcfYpGDAh6NCg7bwOC7pyVk5IQdCeUUAYEyiYBCLrivEHQFQFC0PUAZI4KQWcGyhBOp6DLc9Mig/t5rUuu1fLzqqtKgjfzdMgV7zn3d6fUis6z1YcTdJUVeYavSFCI0K32fjgbzz3G0HgQdN2E3cWHoLvjZVcagm5HCJ+DQNklAEFXnDsIuiJACLoegMxRIejMQBnC6RR0ed3Y9OGdqE29KoHe+mVFOBw+mak8tM9uUXuJs3n/MZq2OJuy807R2c1TA8nzOFb13fbfqrz52jzx0sUPZ+O5xmYVB4Kum7C7+BB0d7zsSkPQ7QjhcxAouwQg6IpzB0FXBAhB1wOQOSoEnRkoQzidgm51blplCzjDkCOG4DrrPeaLjSSy1o8d2IIGdaxr2+2iIqKLX82kfUdOBsr++YI0+mOX+kpZ4W0bdlnAvKV9UIc6dO6LC40Iix/u7TJS2SkOQffXXEHQeecDgs7LE9FAwE8EIOiKswFBVwQIQdcDkDkqBJ0ZKEM4XYJud12ZXHmdmdGFGlaryDAS9RCcLw7crnoLMb/olcygQVzVuT799cI08tM2cvPxhP4taxh9U8l4rz5r+iNA0PUzdtMCBN0NLfuyEHR7RigBAmWVAARdceYg6IoAIeh6ADJHhaAzA2UIp0vQ7c5Nc531ZkAQCMF5ZdiMlXtpzBebaFDHOjR2YEvbbm7Zf4yuemtZULlbejWmEf2aBLaR++Flhvl4ghjbc99vJZWM97ZgfFAAgu6DSTB1AYLOOx8QdF6eiAYCfiIAQVecDQi6IkAIuh6AzFEh6MxAGcLpEnS7FWS5VdpP96DbvVRwg9tuB4GIdST/NH2wJJsKCovo0xV7KSsnP6iJ1OQEmn7zmXTZxCXG7/2wjdy8y2BQp7o0bdFu8tMcupkjp2Uh6E5JRaccBJ2XMwSdlyeigYCfCEDQFWcDgq4IEIKuByBzVAg6M1CGcLoE3e76LblV2ukKM8NQbUPYvVSwDWAqIEXWSqyFnF/91jLam1dy5rxhtWSafksXqp5amUa+t5hmbzhEQtLz8guMv397bzdKSox30w0tZeXxBHHt27rsozTpuvbGNvfy+kDQ/TWzdsRDSwAAIABJREFUEHTe+YCg8/JENBDwEwEIuuJsQNAVAULQ9QBkjgpBZwbKEE6XoNudm5YrzEJKZ2Z0ZRiJegi7lwpuW4iU4fytebvopR+3B4W8u28Turtfc0pJSaFdew/SkDeX0cGjJQLfr2VNGn9lG7fdYC8vjyfIwH7Yes8+SFNACLpOuu5jQ9DdM4tUA4LOyxPRQMBPBCDoirMBQVcECEHXA5A5KgSdGShDOF2C7uT6La6M6QwYjBB2LxXctjP4jaW0/eBxuqZLfXr4D80pPi4uECKcoN/Xrynd2beZIei5ubmU/syvVBTSqB+2ucsXGbJrfuiT27lxUx6C7oaW/rIQdF7GEHRenogGAn4iAEFXnA0IuiJACLoegMxRIehqQI+dLKAKCXFUIYFvm7MOQbfb3i0pWN2TrkbJe235UkH1DnTRgw+X7KGnv90S6EyfM6rThKvbBX4O3eJeNzWJ/nvrWVS9csWAoEvBl5VqpFSg7+/r5n2ATDXl8QQRrrxncBdjhKAzfXGYwkDQmUD+FgaCzssT0UDATwQg6IqzAUFXBAhB1wOQOSoE3RtQIeZidXf1niNGALHSemuvRt6ChdTSIehOs6HLrdJO7wpnGXCEIJwr+pe8mknZpvPlotlFD/UKWkX/37Js+sfXm6l9/Sr02rXtqEpyIiUmJgYEXXAc+dE6On6ygBLj4wzB79k89me9xbnzoZOXGyT9lENA1/cDgq6LrLe4EHRv3KxqQdB5eSIaCPiJAARdcTYg6IoAIeh6ADJHhaB7A/rq3B30+i87gyp/c0861a6S5C2gqZYOQXeaAI4zKZsKiKKiInpxznaavCDLCDPvgZ7KydhueHdF4IWK7FvmQ70ozrTNPdz4zYIu6hUWFVHuieIkcQnxJVvkVcarWveO91ZT5vYcI4wYzuQbOlHHBlVUw/q2PgTdX1MDQeedDwg6L09EAwE/EYCgK84GBF0RIARdD0DmqBB0b0D/PWsbTVlYLI/yeXNYB+rSuKq3gJoF3al4c15r5gaE2Inw2OebaOuBY4ZYikR136w9EAhxdlp1eumPJdvR3cSWZedvPUz3fLAmcIY83K4HuYNg9PnNaVh6A6NqqKB7aVtnnV2H82nQpMVBTdzUoyGN6t9MZ7MxjQ1Bjyn+Uo1D0HnnA4LOyxPRQMBPBCDoirMBQVcECEHXA5A5KgTdG1ApvLJ2SlIC/TCiGyUxnEXXsYIuxfP5wa3pvNa1LAedlXPCuONbrA7PGdnDGxwHtY6dPE3/WbSHCoqIxFnwv87YQDsPn4hYkyPx2YGjJ+mClzONdsLFC5eUzu+Cvu3gcRryxtIgdjd2b0h/Og+C7uCriCIMBCDoDBBNISDovDwRDQT8RACCrjgbEHRFgBB0PQCZo0LQ3QMVEjv0neXGXdjy+eflrenCttbi66YVHYIuzieLc8pOkq3Jc99z7u9OqRUT3XTdUVmxff26yStow96jjsqLQilJiTR3VHfH5SMVjMQiXKZ7vwu6GOvwqStoRVZxPgTxvD60A6U3Ud/NwQJcQxCsoGuAqhASgq4AL0xVCDovT0QDAT8RgKArzgYEXREgBF0PQOaoEHTnQDfsO0qzNhyiWesP0rq9R6l/qxqUmpxIM1buI/OWaOcRw5fkFnSxbfzKN5cZjTlZhXYj817GKrazi/PgkZ7OjVJp7d5jdOJUAYlT3uOvakvntKjhpblSdcZ8sdGYs9BEeFaZ7suCoBcUFtF7i/fQkfwCOr91TWpZJ4WFlV+DQND9NTMQdN75gKDz8kQ0EPATAQi64mxA0BUBQtD1AGSOCkF3BvSH9Qdp9CfrAoVrVU6iyTd0oBkr99PEn3fQsG4NaPSA5s6C2ZTiFPS5mw/R/f9dG2jRydZneac250sH85DNLwzk7/u3qkkHj56i5Vl5xpb3vw9sSVUrJtKBo6eoRkoi6zV2VufxZab7NvUq0/ThZwa6XBYEneWLV4aCQND9NVkQdN75gKDz8kQ0EPATAQi64mxA0BUBQtD1AGSOCkF3BlScWxbnl+UjVnUzH+5NOpKqcQr6tW8vow37jgUNMvOh3kamb6tHZnznfOkQ2tZd76+mBduKs45XTkqgd2/sSGm1orPqO2PlXhrzxSZjB8S4IW0DXbOaSwi6sz8j0SwFQY8mbfu2IOj2jNyUgKC7oYWyIFC2CEDQFecLgq4IEIKuByBzVAi6M6B9xy8gcfe5+RHbxXUkVeMU9MsnLSmVfE28WIh0OZgU1dCVZGeknJWSq9XNalaiKTd2NO4bj9azLvsIDZ28wsgUPzOja6BZq5V1CHq0ZsZ5OxB056yiURKCzksZgs7LE9FAwE8EIOiKswFBVwQIQdcDkDkqBN0Z0Pcyd9Mz328NFL6qcz3664VnGD9zJ1XjFPQvVu2jxz7fGOj3gNa16LnBrSMOeuG2HMp4f7Vxx/fNPRvSvec0dQbJRalorNJH6o6cM/OZfHk2PXRrPwTdxcRGqSgEPUqgHTYDQXcIymExCLpDUCgGAmWQAARdcdIg6IoAIeh6ADJHhaA7A2peKX9wQBpd3qlOoCJ3UjVOQRedvOiVRbTvyCka0a8p3dKrUcQBiwzrF76yOGg7f7j7wp1Rsy7l9F521Xas6l82cTFl5eTTzIwu1LBaRaNYuCvWxO8h6LpmwXtcCLp3djpqQtB5qULQeXkiGgj4iQAEXXE2IOiKACHoegAyR4WgOwMqV3wHdaxDYwe2DKpktfLqLHLpUpyCbpWZ3Kpvh4+fogETFgV9fG7LGvTvK0vOansdl7melGG7e9k52goXQ94Lb87kHu6KNQi6rhlQiwtBV+PHXRuCzksUgs7LE9FAwE8EIOiKswFBVwQIQdcDkDkqBN0Z0BKha0lC0s2PXA3mSqrGKejyrHe3ptVo0nXtbQd7urCIej4/n8RKunyu79aAHmTKUC9jShk2r2Dbdo6xQLgV/HDb3iHojNAZQ0HQGWEyhIKgM0A0hYCg8/JENBDwEwEIuuJsQNAVAULQ9QBkjgpBdwZUyls4oeTO5M4p6F5eHvyw/gA98tkGEndr16mSRB/eepZx5RnnYyXDnG1EihW6I0ImjguXGA9b3KM1K87bgaA7ZxWNkhB0XsoQdF6eiAYCfiIAQVecDQi6IkAIuh6AzFEh6PZA5Sp0aNZvWVNuI09NTqA5I3vYB7QpwSnokVb+I3VjXfZREmfrrcasMshIMqwS103d0D7IlyzhdhpA0N2QjU5ZCHp0ODttBYLulJSzchB0Z5xQCgTKIgEIuuKsQdAVAULQ9QBkjgpBtwfqZBXa6vyyffTSJTgFXSZDmz68E7WpV8VVd7iz08vGuXccuBrUb4VDz+ZHSloHQfdCWG8dCLpevm6jQ9DdEotcHoLOyxPRQMBPBCDoirMBQVcECEHXA5A5KgTdHqjM0h4poZksI855i1VYlYdL0N0miAvtM+eYzLFjncFd9sV8Dn5a5h6atmg3hV6xJspC0FW+zXrqQtD1cPUaFYLulVz4ehB0Xp6IBgJ+IgBBV5wNCLoiQAi6HoDMUSHokYE6ldznfthqKXhup4xL0N0miAvtJ+eYzLHltvtYZXCXfTH3Y/ribBK8wr1ggaC7/QbrLw9B18/YTQsQdDe07MtC0O0ZoQQIlFUCEHTFmYOgKwKEoOsByBwVgh4Z6IyV+0hco2aXBV0mHePI5M4l6E625kcafaSr5VS+htz3xnvti3wBkdGnCU1blEV5+QUU7igABN0rYX31IOj62HqJDEH3Qs26DgSdlyeigYCfCEDQFWcDgq4IEIKuByBzVAh6ZKDyjnMhcRl9GlsWjpRkzO2UcQm61wRxsr+6krnFOoO7HJ/5pYrY3i6exQ/3LjVdEHS332D95SHo+hm7aQGC7oaWfVkIuj0jlACBskoAgq44cxB0RYAQdD0AmaNC0K2Bbtl/jEb8by1l5eSHXVk11+TM5M4l6CoJ4uTYuGU6K+cEXTZxCXFlvFf54yBfqoi+iNVzq4z1EHQVynrqQtD1cPUaFYLulVz4ehB0Xp6IBgJ+IgBBV5wNCLoiQAi6HoDMUWMh6J+v2keb9h+nykkJNLxHA0pMiGcelXq4j5dl05Nfbw4EmnB1O+pzRvWIgbkyuXMIutOz83akvEr+toPH6fNV+6mwqIj6t6pBHRukGk2pnou366+bz82MRD2rYwwQdDdUo1MWgh4dzk5bgaA7JeWsHATdGSeUAoGySACCrjhrEHRFgBB0PQCZo0Zb0MVWYnH2Vz49m1ejV69pzzwq9XDdnp1nyKV86qUm0Zd3p0cM7CTbu5OecQg6lwjLLf7hsptbjUWI7yWvZdKxk4VGkbi4OHr/5jOpZZ0U8ksGd9l3+VJF/GyVPwCC7uRbG90yEPTo8rZrDYJuR8jd5xB0d7xQGgTKEgEIuuJsQdAVAULQ9QBkjhptQb/olUzad+Rk0CgWPdSL4uPimEemFk5u7ZZRKlVIoJ//1CNiUK6s5xyCrpogTg7US/K7t+fvoglztgexuvPsxnRX3ybGyxmr68zUZsxbbflSRdS2yjMAQffGVmctCLpOuu5jQ9DdM4tUA4LOyxPRQMBPBCDoirMBQVcECEHXA5A5arQFffiUFbRi95GgUWQ+1MtYZfXT8+cZ6+nrNQcCXbrnnCZ0e2/rJHGioBeZDTdmDkH3svIdri9yJb5Nvco0ffiZjqZIZr43F773nKZ0W+9GdOd7qy2vM3MUnLmQfGEgwlpd+wZBZ4bOEA6CzgCRMQQEnREmEUHQeXkiGgj4iQAEXXE2IOiKACHoegAyR422oE/6ZSe9NndHYBSjBzQ3thbrfk4VFNLinXlUt0oSpdWqZNucFNz0JtXo8k61aVDHurZ1nvl+C72XuccoJ8qPHdjCto4uQfd6djy0P17OshcVFVH6s/OCQj09qBVd1K42cZ3T9wQ2TKWM91bTwu05xid39WlCd4bJ1A9B56LNFweCzseSIxIEnYNiSQwIOi9PRAMBPxGAoCvOBgRdESAEXQ9A5qjRFnQpjnIYc+7vTqkVE5lHFRxOJCy7ddoqOnTslPHBJe3r0N19G5NY6RWPEMczTNIuM42Lz2ZmdKGG1Sra9m/NniN0/bsrgsr95cIz6OrO9WzrhhaItIK++cBx+nrNfqPKoI51qHH18H3jzL4updopC7nqLrKiX9S2Fr09P8vI2j795jN9k8Fd8JueuZue/b4kH4L43f9uPYvSaqcETQkE3fVXWHsFCLp2xK4agKC7wmVbGIJuiwgFQKDMEoCgK04dBF0RIARdD0DmqNEUdLn1WYibkF4hclbbijmH+eKc7fTO/F1BIVOSEujYyQLjdwnxcfTpHULEk42f5eq5EOCxA1s66sqbv+6kl38q2RkgKslz144CmApZCbq47u2K15dQQWFx8joxhpkZXal6pZIXHOJquH98u4UW78il1ORE+vbedEpKVMuSX3KfegtHOwkkP8FOMJT1BV8xBqts6W45qZZ/+aft9Oavwd+LcMnwIOiqpPnrQ9D5mapEhKCr0CtdF4LOyxPRQMBPBCDoirMBQVcECEHXA5A5ajQFXa6eC3Fbt/eokSzMKjEX5zDN54yt4pr7Ifs56br2hkw6ebYfPE6D31gaVPTpy1sbK8huHytBl4nfzPHk2W75uyFvLCWxY0A+13atT4/8Ic1tF4LKu0k4F273gfjdNW8vD7wQqV0liV6/rj01q2l/1ECp4zaVZ6zcS2O+2BRU6su7u1K91OIXNfKBoOucBW+xIejeuOmqBUHnJQtB5+WJaCDgJwIQdMXZgKArArSonpKSQoWFhXTixAk9DSCqKwLREnTz6rlY9ZUJ1dysUrsamKnwjxsP0qiP1gV+k5IUH7gCTP7ynr5N6Pazi7e9ixVgsdor+unmmb3hID397RYjS33vtOr08h/buakeKGsl6G/N20Uv/RicHV3It5Bw8Yhz9j2fnx/UZovaKfThrWd56oesNGv9AXrwk/WOVr7D7T4QK/7dnws+k37lWXXpsYu8ndFXGkxI5Td+3Ulvz8uiqhUTjP6Eu+segs5JnCcWBJ2HI1cUCDoXyeI4EHRenogGAn4iAEFXnA0IuiJAi+oQdD1cvUbVLegLth2mP8/YGDj//egFaXRNl/q0LvsIDZ28wpMIexnr+Nnb6N0FWYZk/uvyVjTiv2tp9Z6SbPJCyB+76Ax66uvNxjZsuT3bbVtSZt1kPQ9tw0rQ808V0A1TVtKm/ccCVR44rxnd0L1h4GchwnILvPjlZR3q0N8vdbZN32qsMlGcOEf++V1dqUpy6ZwBpwuLaMG2HLrvwzVGGPN59aMnC+ic8QuCwvdsVo1evba9W7wxKQ9Bjwn2iI1C0P01JxB03vmAoPPyRDQQ8BMBCLribEDQFQFC0PUAZI6qU9BFNu9zXlgY2Nosuv7HLvXpzxcUb7nmTGRmh0Weg5Zn3kXfvlt30Ojbe4v3GFvuzc8396ST2Irt9vGS9dypoItyszYcpAc/Xme82BAvEsQzfXgnalOvCoVugW9dN4XGX9mW6lcN3rLtdkwfLcs2XlyIR1yG98o17aln85Kt/7tz8+nmqSsD99uLLPn/u61zUDNXv7mURII7+fypfzO6sUfJiwW3fYpmeQh6NGk7awuC7oxTtEpB0HlJQ9B5eSIaCPiJAARdcTYg6IoALapjBV0PV69RdQp6/ulC6j0ueMt1hwZVaMqNnYzucl0F5mTsQycvp3XZRyncufIVWUdo+NTgDOy3925E95zT1EnoUmW8nGE3B4mUxV1KuDgzn5d/2jjHnxgfR4kJ8XTiVHHSu4ni7HyTasR1tbx8kSL72KJOCn14S8m2ebHtXmy/Nz+f3tGZmtQoOWO+Ny+fJi/Iot25Jym9SVW6PgpX63mavDCVIOhcJPniQND5WHJEgqBzUCyJAUHn5YloIOAnAhB0xdmAoCsChKDrAcgcVaegi65e9EpmYGVV/DwsvQGJTNnikeeVw2XOZh5mYLU+3LVuC7flUMb7q4OavKVnIxpxrjdBl0npvI4rkqDL2ELQxUuE0LPdHEnhzCCKiojSn/01iI3IDj9nZPfA716Yvc2Qb/Pz7o2dqGODKtzTGJN4EPSYYI/YKATdX3MCQeedDwg6L09EAwE/EYCgK84GBF0RIARdD0DmqLoFXUq4WM0d2L4O/e3iFlQhQWyUpsCW7GHdGtDoAcXSruOx23YuzmxfPmkJia3a8pl4bXvq3sxZBvfQPssEeP1b1aBxQ9q6HlIkQTdv1U+rlUJXvhmcOX7IWXXpcebkayP/t5Z+2nQoMI77+jWlW3s1Cvz8y5bDgbPn4pdNa1aij247i+K5lvBdE+StAEHn5ckRDYLOQZEvBgSdj6WIBEHn5YloIOAnAhB0xdmAoCsChKDrAcgcVaegyyu3xHnp6cPPpNSKwcnFZEI1ryLrFIW4b/3O91ZTpMRt+4+cpA+W7KGCQqJezat5lnPRJ5kATyRVmzOyh9NuBspFEnQxDjEesVVfrFCf/e/g5GtCnIVAcz6FRUXGPfIv/biDkhPj6dcHepYKf9u0lbRkZx5d2qE2PXBec6qRUoGzCzGNBUGPKf6wjUPQ/TUnEHTe+YCg8/JENBDwEwEIuuJsQNAVAULQ9QBkjqpT0MNduWXuvjk7uBeRdYoiWi8CzP0594UFlJdfQOG21Nv1O5Kgh8b9Yf0BI0u+uGJNZKh/4co2VCkpwa4JT59Hyhkgz6l7Ga+nzkSxEgQ9irAdNgVBdwgqSsUg6LygIei8PBENBPxEAIKuOBsQdEWAEHQ9AJmj6hJ0uXouumu+ciu0+yoi6xSFObFaRp/GTqsplZMr3WMHtqBBHeu6ihVJ0K0y34uz4rp3lFudrY/2lXmuYDIUhqAzQGQOAUFnBqoYDoKuCDCkOgSdlyeigYCfCEDQFWcDgq4IEIKuByBzVB2C/tWa/fTRsr3GVuxBHesYd4pbPVJk5fVnzMMzwqkmbfPSJ/lSwMv5eitBV90672Uc5jrybH3onFr9XrU9v9SHoPtlJkr6AUH315xA0HnnA4LOyxPRQMBPBCDoirMBQVcECEHXA5A5Kreg3/X+alqwLSfQy+cGt6YBrWtZ9joa8mw+ty22gUfjkdvqI517t+qHlaDLs/RiDOIMerQfqxcE0czGH+0xi/Yg6LGgHrlNCLq/5gSCzjsfEHRenogGAn4iAEFXnA0IuiJACLoegMxROQXdvK1ddnN4j4Y0sn8zy15HY/U1GtvoQwdolzk+0jRaCbpqdniOr044ltG8z55jDG5jQNDdEtNfHoKun7GbFiDobmjZl4Wg2zNCCRAoqwQg6IozB0FXBAhB1wOQOSqnoG85cIyuenNZUA9v6N6QHjjPWtDlqqyXlWanKKzObTut77WcV3G1EvRYnKUPHbu85k2erVd5EeGVa7TrQdCjTdy+PQi6PaNoloCg89KGoPPyRDQQ8BMBCLribEDQFQFC0PUAZI7KKeiiaze8u5xW7zka6OVr17SjHs2rR+y1ToGOxgsAq8F53b5vJegyXkafJhStZHehYws9Wy+38sdq2z3zH4ew4SDo0aDsrg0IujteuktD0HkJQ9B5eSIaCPiJAARdcTYg6IoAIeh6ADJH5Rb0Vbvz6MYpK0ncff6vy1tRhwaptj32utJsG5jISFQnzqDHQiDllvRODavQS1e3K3UPvFX/rQQ9Ggn17JiG3invh1V9uz6rfg5BVyXIXx+Czs9UJSIEXYVe6boQdF6eiAYCfiIAQVecDQi6IkAIuh6AzFG5BX3Gyn0kkob1b1WDxg1p66i3MsmYlyvJ7BqQkuwlm7pdbLvP7/1gNf26tThhXsUKCfTO9R2odd3KdtXIStCHTl5O67KPGgniopXsLrSz5i3t4s7zBz9Zb7wE0ZmF3xaY5gIQdM2APYSHoHuAprEKBJ0XLgSdlyeigYCfCEDQFWcDgq4IEIKuByBzVG5B97KiOuqjdfTjxoNUvVIF+tvFZ1D/VjXZRumlPxyN787Np0tfWxwU6uaejej+c5vahrcSdHkUQIhxasVE2zi6CphfFIhVffFEuuteVz+iFReCHi3SztuBoDtnFY2SEHReyhB0Xp6IBgJ+IgBBV5wNCLoiQAi6HoDMUbkF3e027E+X76WxX20KGhWn7MmkZtFe4Q2XMO/6bg3owQHNbWfQTtAXP9zbNobOAvIsvNglMXvDIeM4w8yMrjqbjGlsCHpM8YdtHILurzmBoPPOBwSdlyeigYCfCEDQFWcDgq4IEIKuByBzVG5Bd3ue/NW5O+j1X3YGjWr0+Wk0LL0+y0jlau/04Z2oTb0qLDGdBrl+8gpak30kUPyVa9pTr+b297CHE/RYJrsLHe+MlXtpzBebKDU5gfLyC2hQxzo0dmBLp1jKXDkIuv+mDILurzmBoPPOBwSdlyeigYCfCEDQFWcDgq4IEIKuByBzVG5Bd5uR/YPFe+if320JGtUnd3ShpjUqsozUbX9YGv0tSP7pQnr884303boD1K9FDRp/lf2Z/BOnCuiLdTlUrWICnd+yROZjmewulIn5HLr4bPT5zWlYegNOdL6KBUH31XQYnYGg+2tOIOi88wFB5+WJaCDgJwLlQtBPnDhBTz31FH311VcBthMnTqT09PTAz59++ik9+eSTxs8XX3wxPfbYY1SxYvF/3IfWf/zxx+mKK65wNE8QdEeYXBdKSUmhwsJCY27wxJ4Ap6CHZvh2Orq/f7mJPlmx1yh+aYc69OSlPKuxUiTFSu+ckT2cdoe1XFbOCbps4hJjtdmuD3vz8umGKStp/5GTRh/OapRKb1/f0fhnmezOL6vVff69gI6fKjD65nRnACvYKAaDoEcRtsOmIOgOQUWpGASdFzQEnZcnooGAnwiUC0E/fPgwTZkyhe644w5DujMzM2nMmDE0YcIESktLM34W/zx+/HiqXr268c/iGTFihPF3888i1qhRo4zPzIJvNWkQdD1fZwi6Hq5eo3IKuopESrl3IrJOx+qXVedIyd0Ki4oMIRcJ8t6ct6vUdv+3hnWgzo2rUqyS3YVj/emKvTT2y5K8Ae3rV6GpN3VyOi1lrhwE3X9TBkH315xA0HnnA4LOyxPRQMBPBMqFoIcCDZVsIeBNmzYNrIqbhV3UFTIvpFzIfKiw200WBN2OkLfPIejeuOmqxSnoqhJ57gsLjDPNXNeIzVp/wLgGzM2Vbzo4W12PtmxXHt374Ro6drJ4Jbp5rUq09cDxoC6I+9PPPqM6ycRsfthOft+Ha+iXLYeD+jl3VA9KSUrQgS/mMSHoMZ+CUh2AoPtrTiDovPMBQefliWgg4CcC5VLQt2zZQk888YTxV4MGDYzt7927dw8IuvlzMRmyrBR0sR1+4cKFQdvgrSYNgq7n6wxBd8Z13tbD9L9l2VRQUERDzqpnnMmesjCLdh3Op7PTqtONPRo6C2RTilPQZQZ3r4ItJZTrznLVFwYsgIks5fqmKSto5e6SJHKh7TWuUZE+vq0zJcTHkSpbrrGIOH/7YiPNXLkvKOT8B3tShYR4zmZ8EwuC7pupCHQEgu6vOYGg884HBJ2XJ6KBgJ8IlDtBl+fJpZDLn4cMGRLYsh4q6GLr+9ixY43t7+IJJ+gvvfRSqXm777776Pjx4JUsP01uWe6L+Bd5UVERnT59uiwPQ2vft+w/SgNfnh/URv2qybQnNz/wu0cvakXDezVR7kd8fDyJOcnPL4ntNWjPf/1IuSdO03cje1Oj6pVch1mw9RANn7yEqlZMpPmP9HNdP7TC019voHfn7aA/X9yKbuqpzsprh96dv4Oe/moDDencgP7vinaBMF3/b07gHLf85UcZ3emzFfvonV+2Uo/mNWjy8C7GR1dOWkhrdufRR3d2o3YNqnrtCku9lbty6eYpS+lofvGf4VEDzqCMc+yvj2NpPAZBOP+MxKD75bLJpKQk498hIp8JntgTEH9GxIuskydlOKrOAAAgAElEQVSL82fgUSNQqZL7f3+qtYjaIAAC0SJQrgRdyni9evUC58tDhV2A9bKC/s4775Sak5tvvhmCrumbCkG3B/vS7M308pytEQsOaFObXr7uTPtgNiXEf1iJ/9hVTdqXe/wU9XzmJ6O1NWMGeO6XlHy3Ivr5ymzauPcoJSXG0+19mtHa7Dz604cradfhE/TiNZ3ognZ1PPdJteKa3bl05aRF1Kh6Rfpu5NmBcM98s5He/nV74Gcp5GI+ujz5nfGyQwi6+H27sT8os1Udh7n+yYJCWrErl5rWqER1UpM5Q/suFgTdd1NCycnJhqAXFBQfD8ETWwJiJ5YQdI4XvbEdiT9ah6D7Yx7QCxDQQaDcCHo4OZfAcAZdx1dHb0xscbfn+/Wa/fTnGRsiFhyaXp8eOr84t4LKw7XFnSshm9Oz1gWFRZSXf5qqVaxgZIB/8quSpGUi+/nyrCPGTg3xpCTF05d3pVNqxUQVVEp1w133Zr6u7OaejeiO3o2oUlICiXvQn/x8DU2Zv5PEdv+MsxvTuS8uNNpf/HBvpX6gsnsC2OLunpnuGtjirpuwu/jY4u6Ol11pbHG3I4TPQaDsEigXgh5uldw8JcjiXva+oBB0Z3N21/uracG2HKOwOHN+UbvaNOaLjYHK/721M51RW30bnJ2gL9yeQ4t35FHrOil0Xuualp2XGdxVz487Ef2fNh2iUf9bS0K/xXb4GikVaNvByEdSRpzblG7p2cgZfA2lZKK46cM7UZt6VYwWZqzcZ8xpt6bVjMR48hGC/vOGvXTLlGXG9Wzis6GTV1CbepVp+nD1XRMahleuQ0LQ/Te9EHR/zQkEnXc+IOi8PBENBPxEoFwIutiyLq5F27NnTxDb4cOHB7a64x50P33t7PsCQbdnJErIpGCvXNOOejUvzqEgVo3v/mANCYnlyuYdSdClQMoe39KrEY3o1zTsAOTKd0afJpTRp7GzQVqUktnc59zfPeyq99n/XkAnfruD22lDYwe2JHGHeKyecDsDhJwLxqHMhKCLs5w3v7vUmOth6fVpWuaeUiIfq7H83tqFoPtvxiHo/poTCDrvfEDQeXkiGgj4iUC5EPRYAkUWdz30Iej2XM1bn0O3NHPfFx5J0M97cSHlnAhO5me1xZozy/ht01bRkp25BqjQlwJi13r6s78GQUxOjDdeXpwuLN7SLoT3+/UHaOO+Y8bPYsu7uE88Li7OHr6mEuF2GFw2cTFl5eSTeVVdNC8F/Z1ft9Fz35fkIlDdnaBpaOU+LATdf1MMQffXnEDQeecDgs7LE9FAwE8EIOiKswFBVwRoUR2Cbs/VauuzrMl5XziXoNutetuPurjErPUH6cFP1gUVf35wm6Dt9bf8ZyWJO8Tlc116A7qnbxP6YcNB6tSgCqXVqkRC5GdvPEgpFRKoZ/NqTpvXVk6+WJHb1LNyTtBlE5cYW9jnjOwR1K4U9AO5xwJnz+WLB9XdCdoGWI4DQ9D9N7kQdH/NCQSddz4g6Lw8EQ0E/EQAgq44GxB0RYAQdM8A5dZnq23s8n7v/q1q0LghbT23IypGEvSpC7No3KxtgfhXda5Hf73wjFLtyRX/cLLptnNybOZ6oVvAhZwLSa9dJYmuOqsu3Xl245iujjsZY+iuCLmiLrbdi+335kcKutjmfut/VtLS315GiDwETw9q5aQ5lGEkAEFnhMkUCoLOBJIpDASdCeRvYSDovDwRDQT8RACCrjgbEHRFgBB0zwCttj7LgGbZszqn7bTxSIIuXxS0qVuZ1u09Gna1V7TjJLGb0/6IpHQZ760OKj7xuvbUvWnJKriU+HBy67SdWJQzz+vEX3bS7A2HDDkPPRsvBX3XgTy66NXFQV39x2Ut6ZL2sTtLHwtusW4Tgh7rGSjdPgTdX3MCQeedDwg6L09EAwE/EYCgK84GBF0RIATdE0C59blhtWSamdHVMoY8862aLC6SoMurwWZmdKEnvtxsmZyOK4O7HKzY4v/8D1uNe8Bb1kmhD245K4iDHHusE7+5nWDzzgh5tlywbVitYlCowBn0X7aRSC5nfu7o3ZjuPqeJ26ZRXoEABF0BnqaqEHRNYD2GhaB7BGdRDYLOyxPRQMBPBCDoirMBQVcECEH3BFCeP7dbHf5y9X7668ziu9Jb1E6hf1zWilrXTXHdppWgh56Dj5Sc7qmvN9NHy7LZMsuLQUTaJSBfHKjuHnANS7GCXPkX59DXZR8lq5cwUtDXZB2mP761LKjVBwY0pxu6NVDsCaq7IQBBd0MrOmUh6NHh7LQVCLpTUs7KQdCdcUIpECiLBCDoirMGQVcECEH3BPCBj9dabn02BzRnOpeS/uGtwSvNTjpgJehyO7Z5lXroO8uNre5y1V5kTb956gpaveeo0RR3tnS5Uv784NZ0XutaRhuhydacjNEvZWatP0APfrI+0B2rrOzmM+jTM3cbmdxFfvrzWtWk5wa3oRgmo/cLyqj2A4IeVdyOGoOgO8IUtUIQdF7UEHRenogGAn4iAEFXnA0IuiJACLongOZt5aFbn80BZTnz76yuQIvUkXCC/snyvfT3rzaVWuGdteEgPfjxOoqPi6MO9StTzSpJNGfDwaDwj110Bl15Vj1PYw+tFG7rvFyFLotXjpl3BYixTrquvXG3eehjFnT5mchKDzFn+Vq5DgJBd41MewUIunbErhqAoLvCZVsYgm6LCAVAoMwSgKArTh0EXREgBN01QDerw3d/sJrmb80JtNG+fhWaelMn122aBX3DvmM06n9raXduvhHnjrMb0919S847f75qHz3++caIbYjyoh7HEy47fLhVdY62ohFDzq9sa/KNnYxr4ZwIejT6hzbCE4Cg+++bAUH315xA0HnnA4LOyxPRQMBPBCDoirMBQVcE+DsW9ANHT9K0Rbvp+KlCOqtxVbqobfH27EjPvK05NOmXHbR0Zx6FXisWrt6+vHz625ebApIu5FxIutvHLOh3vb+aFmwrkf7KSQn006iSO7pfnLOd3pm/y7KJxPg4+vyurlSnSpLbbliWHzp5uXFeW642y50DXnYLsHXKY6CLX82kvXknA7VF7oBwxxLCraB7bBLVGAhA0BkgMoeAoDMDVQwHQVcEGFIdgs7LE9FAwE8EIOiKswFBVwT4Oxb0K99cSlsPHA8QEAncLmlf2xLokp25JM6Uy6dihQT6+u6ulFox0XYS5Jl1r9nczYLee9x8yj9dGNRm5kO9A1ur3/x1F7380/agz58d3IZW7T5ilLmgTU1qW8/9S4JIgxRZzMXLDrGlPb1xqnGGW2wLF8Je1h6nxxIg6P6aWQi6v+ZD9AaC7q85gaDzzgcEnZcnooGAnwhA0BVnA4KuCPB3Kug7Dp2gK15fEjT6qzvXo79ceIYl0Nd+3kmTft4R9Pmf+jejG3s0tJ0E1SvOzIL+5Neb6eNl2YE229WvQv8xbZs/XVBII/63NrBqP3pAc0OcdT7m7PGDOtU1ZN3JDgOdffIaO+O9VbRwe26g+h/a1KJnrmhdKhwE3SthPfUg6Hq4qkSFoKvQ468LQedlCkHn5YloIOAnAhB0xdmAoCsC/J0KemgiMIFheI+GNLJ/M2tBn7uDJv2yM+jz8Ve1pX4tathOgrw3PTU5geaMLNmOblvxtwJS0NdszyaRpT0vv4Ba1a1snI2+u29jqlW59Hb1wqIiiiORtEz8r/7n3BcWGP0SYxR/t0qupr8nai3k5Z+ml3/cQSt359GZDVON70RyYjwEXQ2r9toQdO2IXTcAQXeNTGsFCDovXgg6L09EAwE/EYCgK84GBF0R4O9U0MWw//ntZvpgSclKtFiFFqvR5mfrgWP0zdqDlJQYR+Jst/npc0Z1mnB1O8cTIAXWy73gUtBHTc8kcfd5/1Y1aNyQto7bjkZBuc1dtlUWz5+74YQVdDe09JeFoOtn7LYFCLpbYnrLQ9B5+ULQeXkiGgj4iQAEXXE2IOiKAH/Hgh5633Xo+fC5mw/R/f9dG0RoaHp9ElvhxfnzBlWTXcGXAuv2HLpY7Z+yaDdtP3yKvlmdbaxQT7/5TIp0vZurjjEVfuDjdTT7t+vcxKL969d1oK5NqjJF918YCLq/5gSC7q/5EL2BoPtrTiDovPMBQefliWgg4CcCEHTF2YCgKwKMgaAXFRXR2K8202cr9lK1ShXoyYEtjC3RT32zmU6cKqQbuzegURG2mnONWN7VLbdkhyY1u/btZSSuNDM/5mRsbvshz6G7Xf0eNnk5rc0+Gmgu9Fo1t/3QUf7YydPUd/zCoNDXdKlPj16QpqM5X8SEoPtiGgKdgKD7az4g6P6bDwg675xA0Hl5IhoI+IkABF1xNiDoigBjIOjvLsii8bO3Rez42IEtaVDHOnoG91tUmVldtPXc91tKnZu+fNIS2nn4RLCgP9zbONft5Ql3X7hdnGMnC6jv+AVBxXqnVaeX/+h8a71dGxyfH8kvoH4vBPfzyrPq0WMXWSfd42g3ljEg6LGkX7ptCLq/5gOC7r/5gKDzzgkEnZcnooGAnwhA0BVnA4KuCDAGgj5u1jaaujArYsfvPLsx3dW3iZ7B/Rb1somLKSsnn6YP70QzVu0PXBMmsp6L5/HPN9Lnq/YF+jCgdS16bnDpbN5uOmlus02Eq86KikR2N6LVu4/QjVNWBDVxecc69MTAlm6ajUrZ26evosU7SrKfv3hVW+rrIIFeVDqnoREIugaoCiEh6ArwNFXFFndNYD2GhaB7BGdRDYLOyxPRQMBPBCDoirMBQVcEGANB98MKujmLu0hmZr4mbGZGVxKZvC+bWHwNmzh3nlYrxTh7rvo4OYf+71nbaMpvLzASE+JJXJsmH3EkYOK17al13RTVrrDXF1njp2XuoSP5p6lns2rUpXH5PX8u4EHQ2b9CSgEh6Er4tFSGoGvB6jkoBN0zurAVIei8PBENBPxEAIKuOBsQdEWAMRB0cQb98teX0q7DJ0qdQT9+ssDokVjVjrTCrDpqKeTmc+d3vrfaEHWRxG32hkPGP4tt9mILPNcjE9OFnneX8edvy6G7318d1Jw4s/73S1vT4VPx1KBSESXEe91kzzUKxIGg++87AEH335xA0P01JxB03vmAoPPyRDQQ8BMBCLribEDQFQHGQNBDV6/NXZCJ29wmUnNLQbYzrFsDklva3563iyb8WHKVWsNqycZd3pzZ0s3n0L+5t1up+7Un/byTXvt5R9Bw7uzTmO7t19xYsc3JyXE7VJTXRAAr6JrAegwLQfcITmM1CLpGuB5CQ9A9QItQBYLOyxPRQMBPBCDoirMBQVcEGANBj7SKLARWnNMWWd11rqKP+WKjcZ+4+cqzW6etoqU7S85Qd25cld4a1oEV8Ob9x2jo5OV0qkAcMif6y4VpdHXn+oE2xL3rV765LKjNCVe3pX6takPQWWdCPRgEXZ0hZwQIOidNnlgQdB6OXFEg6Fwki+NA0Hl5IhoI+IkABF1xNiDoigBjIOhSjjP6NKGMPo1L9UCe09a5ii4keV320aCXAF2f+bVUX8T5dM7n719tok+W7w0KuWB0L0o0bVt/9LP19M3aA1Q3NYnu79eUBnaoQwkJCRB0zolgiAVBZ4DIGAKCzgiTKRQEnQkkUxgIOhPI38JA0Hl5IhoI+IkABF1xNiDoigBjIOgyk7nYPi7OYoc+5i3wD5zXjC7tUIdqpFRgHaiUcbOAX//uClqz50ignXb1q9B/burE2q44Xy7OmZufn0Z2p8rJiYFfhcv0DkFnnQaWYBB0FoxsQSDobCjZAkHQ2VCyBIKgs2AMBIGg8/JENBDwEwEIuuJsQNAVAUZZ0LNyThjZ0VOTE2jOyB6WnX/ok3X0/fqDxudxcXFG5vJuTXmygssEcW3qVabpw88M9GHLgeP05xkbaP3eo9S6bmV6elArSqtViRXw9Mzd9Oz3WwMxW9WpTO/fUtIHyUecfxfZ5OUDQWedBpZgEHQWjGxBIOhsKNkCQdDZULIEgqCzYISg82JENBDwJQEIuuK0QNAVAUZZ0MW5b7HFPVJ29ILCIur+3Lygnp3XqiY9P6QNy2CnZe6m577fyp6h3WnnPlyaTR8tzaZ1e4+SOUmdqG/VNwi6U7rRKwdBjx5rJy1B0J1Qim4ZCHp0edu1BkG3I+Tuc6ygu+OF0iBQlghA0BVnC4KuCDDKgv7Ax2uNK8zE1WVC0sM9pwoKqefz84M+srqWzMvondxF7iWumzrme9fNOwnkVW+hfCDobuhGpywEPTqcnbYCQXdKKnrlIOjRY+2kJQi6E0rOy0DQnbNCSRAoawQg6IozBkFXBBhlQZdnv2dmdIl4fdld76+mBaaz2pGE3i0BKcFWZ+DdxvNaPlw/JJ8593en1Iol59Ih6F4p66sHQdfH1ktkCLoXanrrQND18nUbHYLulljk8hB0Xp6IBgJ+IgBBV5wNCLoiwCgKulw1Dj1fHa4LYpv7uwuzaMKc7ZSSlEhzR3VnG2i4BHFswV0EktvZ5e4Auf0/3G4BCLoLsFEqCkGPEmiHzUDQHYKKYjEIehRhO2gKgu4AkosiEHQXsFAUBMoYAQi64oRB0BUBRlHQJ/68kyb+vKPUuetIIzj3hQXGneh2K+5OKBQWFdErP22nt+ZlGVnhv7+vm5Nq2sqYs9WLFXOx9V5Ierjr5yDo2qbBc2AIumd0WipC0LVgVQoKQVfCx14Zgs6LFILOyxPRQMBPBCDoirPxexT0I/mnKSUpgeLj4gx6x08VUEJ8HCUlxCvSLKmekpJChYWFdOLECeWYQoz/9d1W+mhZNomV8T9fkEZ/7FLfUVx5Zn30+c1pWHoDR3WsCo347xr6efPhwMdu+qHUcITKcpu7GN+0RbspKyc/6G52WRWCrmsGvMeFoHtnp6MmBF0HVbWYEHQ1fty1Iei8RCHovDwRDQT8RACCrjgbvydB37jvGAnJzM47ScLNnxvchqYs3E1Ld+YaFIXACtHjeDgFfcKcbfT2/KxAt+pUSaKv70l31E25DTw027mjyqZC4iVBt2eDM8PXS02iL+921g+37TktH7jyrW5lI6u71fZ/CLpTotErB0GPHmsnLUHQnVCKbhkIenR527UGQbcj5O5zCLo7XigNAmWJAARdcbZ+T4J+/3/X0tzNhwLExP3gRUVFQQTfvbETdWxQRZEqEaegZ7y3mhZuzwnq048ju1OV5JIkaFYdXpd9hIZOXkGhd5a7HeCuwydo0KQlQdXa169CU2/q5DYUe/lzXlhAR/MLjLiXdqhDT17aslQbEHR27MoBIejKCFkDQNBZcbIEg6CzYGQLAkFnQ2kEgqDz8kQ0EPATAQi64mz8ngT9vAkLKef46YjEHvlDGl3b1dn28UiBOAX9me+20HuL9wSaE9vx5/9/e/cCbkVZ73H8z3UDaqJCICKCKBmCqYiIHq+ZD1EGaAVWxjFTshNHjpoeTR/gkTonb2GctK2ZkR5NuwDhMSwfJU0xbt5AZQOCIOJWwI3c7+d5B2ftWbPXZdZ6/7P2O7O+63l6EvbMf975/New9m/NzDvXDspcol/sLZBvZvNi6017tV5+/NcVYs6em1sCtu7cH4LNy9wccM/XPyuDenYsVibWn7/87sdy+SOLs8b15FUDxJzdD74I6LG2oaziBPSy2GJbiYAeG23ZhQnoZdPFsiIBXZeVgK7rSTUEXBIgoFt2o5oC+l3PviMPz2u8VLzHoe1l1YZtGUFzT/ozY0+RTwUez1Uur2ZAN18qfKl2gWzduVfat20ltw/rI6f3ih6M/fu07xzeR87tc1ikXVqxbqtc/OtXs5Y1Z+F/eF5PWfLBVrnguEPlsAOyQ3CkwsoL/fKF1XLfC+9mVf3+mUfKdwd3J6ArW2uXI6Bri9rVI6Db+cWxNgE9DtXyaxLQy7fLtSYBXdeTagi4JEBAt+xGNQX0Py/6UCY8uUzMPdznHnuIF+Lmr/5Y/vLGOnl++f5L38PPzy6Xt1hA37l7r5iL62taF5+YLjhb+cLrB5c8JH/291yzm+cr5t+7Hvz5FYO7y1VnHlny9uNc4eH5a+WuZ1ZmbcLMLXBen0MJ6HHCK9QmoCsgKpYgoCtiKpUioCtBKpUhoCtBflKGgK7rSTUEXBIgoFt2o5oC+pdrF3qzfE8ceoxc2K9zltz4J5d5j+iynUzNL1oooF8/o06eXrLeW9ScCf+fr322YBefrVsv106vk1zP947S/qjrN2zbLX98pV4OrGklr6zZJE+9uS6r/MShveXCfp+OssmKLWMuvzdXCCxcvX+iPxPMTUAPv7jEvWItibwhAnpkqoosSECvCHNJGyGgl8QV+8IEdF1iArquJ9UQcEmAgG7ZjWoJ6CZ8mxCeb5ZvfzI1w6lxFj1fQDeh98aZS7O6dtMFR8tXT+ySt5Pm+d7mEWKlnAEPFotyBv7dhu0y6jevZd1nflBNa+nQtqV8vH2PXHZatyaXjVu+9VRX37xjj7RsYe6Vzz1xHgFdlVulGAFdhVGtCAFdjVKtEAFdjVKlEAFdhTFThICu60k1BFwSIKBbdqNaArp/H3aus+c+of/McI2z6PkCeu0Lq8Vcch58FQvel0x9TZbUb5H7RvX1zqKX8/JrPDq6v3ymS9NZ6u95frX8ak72uApZlTOG5lyHgN6c+rm3TUB3qycEdLf6YUZDQHerJwR03X4Q0HU9qYaASwIEdMtuVENAf2/jdvly7ct5z577hMGz6FeecaSc1bujmEeJlfPKF9BXrt8qFz2QPfna5/scKrfnuCzbbDfK2e8o4/PPwpvnvJvnvYdf9/5jtdz/YnZAv/frfWVQz/K+EIgypkouQ0CvpHa0bRHQozlVaikCeqWko2+HgB7dqhJLEtB1lQnoup5UQ8AlAQK6ZTfSHNDNZc+TZ78js5dukA1bd3n3nZuzwvle5n7mMyfPk227Gh8n9ptv9ZMTuh1UsnKhe9C/eO8Cqd+0U07r2VEWr90sm3bslpO6f0o279gtpx99iPz7WUeKeUa7eUW9f7zYAP1J38459hC5a8RxTRZ/e/02Gfngq7Jn7/7nwvfu1EEeu+yEyI9yK7b95v45Ab25O9B0+wR0t3pCQHerH2Y0BHS3ekJA1+0HAV3Xk2oIuCRAQLfsRpoD+g9+/6a8uKIhI1RsQrZn6jbIddOXZIlecXp3uepfSp+5PF9AD5/Nn7dqo4z53RtZ2zSzy5tHhZlXOTOw53pL+Ns9qKaV1I7sK8fluDLAvw3g4hO7yHXnHiU1bVpZvrvcWZ2A7k4v/JEQ0N3qCQHdrX4Q0N3rBwFdtycEdF1PqiHgkgAB3bIbSQrozy3bIHNXfSxtW7WUYf07y1GHti+49yffNqfJzws9pmzeOxtlzGPZYVk7oPuB2z+bb86an3X3vKxxnnpUR/nlyP0zu/uh2eb+c1PnsYVr5adPNz6O7CcXHitDPtspa7tn3z1XNu3YI0+MOUm6HdzO8p3l1uoEdLf6YUZDQHerJwR0t/pBQHevHwR03Z4Q0HU9qYaASwIEdMtuJCWgv/B2g4z9w5uZvT2kQxuZddXJ0qZV/ueIX/TAK7Jy/bbMOj0Pay9/uvzEgmKXP7JYXn53/yO7WrdqIdO/a8JqTcnK+c6g+4968wO3uax+4B0vyb79V5Z7r6+f1EX+8wtHe//tf8lQzvPPg4M22/AvX/fCUU0ree7qUzOLBM+w/z3w9yXvuKMrENDdawwB3a2eENDd6gcB3b1+ENB1e0JA1/WkGgIuCRDQLbuRlIA+5blV8uBLa7L29idfPkZWNeyQvXv3ydC+naRH6Iz6A3PWyC+eX+Wt0/nAtnL78D6R7if/25L1csOMOm+9ch+5liugz1+10TsjHn7U2/PLP5LrptfJrj17vW1+57Qj5Adn9RB/+XKffx7EGnD7nKwvAcxVCC9dOyiziP8Yunz3qFu+zZp9dQJ6s7egyQAI6G71hIDuVj8I6O71g4Cu2xMCuq4n1RBwSYCAbtmNpAT0e59fLfeHHgMW3PVWLVvI45d9Tnod1njZu3+2evwXe8uw/p8uScq/tDzfrOfFiuUK6OY57CYI53qM2759++TN+i3yrd++7pU2l5nPXLROzGPZij2GrdhYzM/venalPDxvbWbRsWf1kMtOOyLzZ39sGtuKMp5KL0NAr7R48e0R0IsbVXIJAnoltaNti0niojlVaikCuq40AV3Xk2oIuCRAQLfsRlIC+sfbd8s5P2+8V7tD21aydWfjbOuG4cozusv3ztg/uVq+s9VRufxZz8s9ex0O6M/WbZA7n10p723cUfAebz8omzPZm3fs9fbD9v5zf5//b/GH8vD8tTmfqa7xrPWots2xHAG9OdQLb5OA7lZPCOhu9cOMhoDuVk8I6Lr9IKDrelINAZcECOiW3UhKQA8G7l9/o5/8850GGf/k8qy9v/L07vK9T2Zctz0jHHz+eDmXuQcD+vcee0PmvrMxM9Y/Xm7O9HfI2TmzXXPm30zW5r9s7z8Pbsj/4iF8Fl/rXnfLt2NsqxPQY6MtuzABvWy6WFYkoMfCalWUgG7Fp74yAV2XlICu60k1BFwSIKBbdiMpAT0cuM2kapc+9Lq88f7mjMC/ndlDLh98hPgTnpkf2MxIbnOZux/Q31qzQS564NWsLl02qJuMPfuovJ278neLZf6q/RPVmde3T+0m487Jv3wpb4El9Zvlkqmvi3ncmj8ZnP/lx2e6HCCPjj6hlHKJWZaA7l6rCOhu9YSA7lY/zGgI6G71hICu2w8Cuq4n1RBwSYCAbtmNJAT0fIHbzIA+7dUPZMWGbfLI/P33Vz86ur/MXtbg3bvtP8qsXCKby9z9gL5w5brMfeX+OEYP6iZXFwjo4cfDtW3dUl66pnFCt3L3x1/Pf5yaf+m8/+i3XPfG227LlfUJ6K50onEcBHS3ekJAd6sfBHT3+kFA1+0JAV3Xk2oIuCRAQLfsRhICuj/DeKH7we94ZqUX0jsd2NZ7nAfgt9wAABowSURBVNhHW3dZ37ttc5m7H9C3bdsmF9yzUNZv2ZnpVO2ovjKwx8F5O3faXf+Unbv3z+huXkce0l5mXFH48XClvA18Kz+Q+1cKTBx6jPelRhpfBHT3ukpAd6snBHS3+kFAd68fBHTdnhDQdT2phoBLAgR0y24kIaD7s7EXCpAmlJtnfQdfGoHTD69fOr6T3HD+0d7zw6O8/ID+j7p679Fq5pLyS07pJmcc3VH6H35gwRLBx7y1aNFC7vnacTKoZ8com420jH9Ju3+Zu39G3eZ2gEgbbsaFCOjNiJ9n0wR0t3pCQHerHwR09/pBQNftCQFd15NqCLgkQEC37IbLAX3x2k3yzNIGefCld6Xbwe28+8nzvd6q3yzfmLr/EWX+a9TJXeX683tZCf3oiaXylzfWeTXMpea/uuR46RcK2DMXfSDLPtwm7du09B5dVtO6pfgB/fy7X/Rmbi/1cW1m1vq6D7ZI364HSIe2ra32IdfKfii/c3gfuXZ6XdY96eobc6AgAd2BJoSGQEB3qycEdLf6QUB3rx8EdN2eENB1PamGgEsCBHTLbrga0E2ovfC+hWImgzOvFiIyc8zJ0u3gmpx7vHHbLjl3yvysn33ntG7yg7PKn1xt+649cvrP5hYM/Y+//L78999WZJb53BEHyYPf7OcF9D+9/J7cNONNb8xPjDnZslO6q/uXuZuJ4ZbUbxHzWLe7RhynuxGHqhHQHWrGJ0MhoLvVEwK6W/0goLvXDwK6bk8I6LqeVEPAJQECumU3XA3o/sRlwd0bc8aRMuaM7nn3+H/nr5U7n1np/fy4LgdI7ci+clC78s8+b96xW866u/HZ66buiBM+LbcM6Z0Zw+WPLJaX322ccd38YOaVJ8lTdQ0y7ZW1sqZhu2hcam/Z5iar+5e5+z8oZqu9/UrXI6BXWrz49gjoxY0quQQBvZLa0bbFLO7RnCq1FAFdV5qArutJNQRcEiCgW3bD1YD+m3+ukZ//fVXW3l3/+V4yakDXgnu8e+8+b4K1Dm2j3StejO/bD70ui9Y2PsrtjuF95Lw+h2VWu+HPdfK3t9YXLGNmYDeXx7v0MhPgnf+LBbJrz/7J6H76lT7yheMa98ulsWqMhYCuoahbg4Cu62lbjYBuK6i/PgFd39SmIgHdRq/pugR0XU+qIeCSAAHdshsuBfStO/fISys3euF60lPLvXu3/Vf/bgd6l463bGEudq/ca8fuvfLQvLXy0Nw1smnHniYzw896c53cNHNpZkCfatdazP3jwde15/WUb55yeOUGHWFLv3+lXv7rr29nlux8YFuZddUAqTBvhJHqLEJA13HUrEJA19S0r0VAtzfUrkBA1xa1q0dAt/MLr01A1/WkGgIuCRDQLbvhSkA3Z6m/++jirMeLmceq/fDzPb09PLZzB8s9tVs93zPR/Uvxv/CZw7wJ6f7wSr33DPbg64bze8nIkwuf+bcbXelr/+iJZfKXNz7MWvGp7w8QE9TT+CKgu9dVArpbPSGgu9UPMxoCuls9IaDr9oOArutJNQRcEiCgW3bDlYB+66y3Zdpr9Vl78+J/nCrt2uhcqm7JJPmeie4/Au6+UX3FfKGwYesuGfnga5nnnvfu1EEe/dcTpHXLyp75L7a/P3v2HXlo3ntZi825ZpA3A30aXwR097pKQHerJwR0t/pBQHevHwR03Z4Q0HU9qYaASwIEdMtuuBLQc93L/dzVA+XAmvInebOkabK6/0x0f0K1mYs+lPFPLmsyS/vmHXtk+uIN0qalyEX9O0mbVm6Fc7NjG7bslKsef0uWfrjF28+bLuglXz3RrbP8mv0joGtq6tQioOs4alUhoGtJ6tXhDLqepUYlArqGYmMNArquJ9UQcEmAgG7ZDVcC+jN1G+S66Usye2OeNf7bS/tb7p3u6v7M5wfVtJK/X32q+IE91yzt/nPQt2/frjsI5WpbduyWmjatnDvDr7ybQkDXFrWvR0C3N9SsQEDX1NSpRUDXcdSqQkDXktxfh4Cu60k1BFwSIKBbdsOVgG524/nlH8lrazZ5M55/+9RuTl5uffbdc73J4iYO7S3jn1zu6S+8fnCTLiQloFu+fRKzOgHdvVYR0N3qCQHdrX6Y0RDQ3eoJAV23HwR0XU+qIeCSAAHdshsuBXTLXanI6uHns1/Yr7P3nPPwi4BekXZE3ggBPTJVxRYkoFeMOtKGCOiRmCq6EAG9otxFN0ZAL0pU0gIE9JK4WBiBRAmkLqBPmTJFevToIcOGDctqxIwZM+TWW2/1/m7IkCFy8803S7t27bw/m8uoJ02aJLNmzfL+fMsttzRZP19XCeilvd/vnv2OTJ3bOLnaV/p1lgkE9NIQm2FpAnozoBfZJAHdrZ4Q0N3qhxkNAd2tnhDQdftBQNf1pBoCLgmkJqAHA3g4YC9YsEBMcJ88ebJ07NjR+2/zGjt2rPf/wT83NDTIuHHjvJ8NGDCgaK8I6EWJshYYeMdLsmfvvqy/4xL30gybY2kCenOoF94mAd2tnhDQ3eoHAd29fhDQdXtCQNf1pBoCLgmkJqD7qLnOoIf/LhjYzXrjx4/3QnmvXr2aBPZizSKgFxPK/vl5U+ZLw7Zdmb+sad1K5lxzapMiXOJemmvcSxPQ4xYuvT4BvXSzONcgoMepW15tzqCX5xbXWgR0XVkCuq4n1RBwSSD1Ad2/fH3gwIGZy9ZXrFghEyZM8P5nXv5/+wHdnI2fN29e1mXw+ZpGQC/t7Tx76Qa5ZlrjbPOTvnSMDD2+MwG9NMaKL01Arzh50Q0S0IsSVXQBAnpFuSNtjIAeialiCxHQdakJ6LqeVEPAJYGqCegjRozIXLIeDujm0veJEyd6l7+bV66APmbMmCZ9q62tlV27Gs8Gu9RYl8fy7kdb5em31sk5fTpJz8M65ByqCYT79u2TvXv3urwrVTO2Fi1aeI9a2717d9Xss+s7yjHiVoc4RtzqhxmN+dLEfIbwOeJGb1q2bCnmf3yO6PTDfOHBCwEE0ilQNQHd9gz6/Pnzm7wDTjnlFNm0aVM63xnNvFc1NTVeQN+5c2czj4TNGwHzS1X79u1ly5YtgDgiYPphviDkl103GmK+MDH/bm3dutWNATEKMbdKmc8QjhE33gzmC5O2bdtyjCi1w1whwgsBBNIpkPqAbtrGPejJe/NyD7pbPeMSd7f6YUbDJe5u9YRL3N3qhxkNl7i71RMucdftB5e463pSDQGXBKoioDOLu0tvuWhjIaBHc6rUUgT0SklH3w4BPbpVJZYkoFdCubRtENBL84p7aQK6rjABXdeTagi4JJCagB58zJoB7tq1q3fmPDjxG89Bd+mtV3gsBHS3ekVAd6sfnEF3rx8EdPd6QkB3qycEdN1+ENB1PamGgEsCqQnozYXKLO7xyBPQ43EttyoBvVy5+NbjDHp8tuVUJqCXoxbvOgT0eH1LrU5AL1Ws8PIEdF1PqiHgkgAB3bIbBHRLwDyrE9DjcS23KgG9XLn41iOgx2dbTmUCejlq8a5DQI/Xt9TqBPRSxQjoumJUQyA5AgR0y14R0C0BCejxACpXJaArgyqUI6ArICqWIKArYiqVIqArQSqVIaArQX5ShjPoup5UQ8AlAQK6ZTcI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6ZVMI6JaABPR4AJWrEtCVQRXKEdAVEBVLENAVMZVKEdCVIJXKENCVIAnoupBUQ8BBAQK6g01hSAgggAACCCCAAAIIIIAAAtUnQECvvp4nYo9vu+02Oeqoo2TkyJGJGG/aB7l8+XK58cYb5fHHH0/7riZm/2655RYZPHiwDB06NDFjTvNAFyxYILW1tXLfffeleTcTtW/jxo2Tiy++WM4888xEjTutg509e7bMnDlT7rzzzrTuIvuFAAIIqAgQ0FUYKaItQEDXFrWrR0C384tjbQJ6HKrl1ySgl28X15oE9Lhky6tLQC/PjbUQQKD6BAjo1dfzROwxAd2tNhHQ3eqHGQ0B3a2eENDd6ocZDQHdrZ4Q0N3qB6NBAAF3BQjo7vamqkdGQHer/QR0t/pBQHevHwR093pCQHerJwR0t/rBaBBAwF0BArq7vanqkRHQ3Wo/Ad2tfhDQ3esHAd29nhDQ3eoJAd2tfjAaBBBwV4CA7m5vGBkCCCCAAAIIIIAAAggggEAVCRDQq6jZ7CoCCCCAAAIIIIAAAggggIC7AgR0d3vDyBBAAAEEEEAAAQQQQAABBKpIgIBeRc1mVxFAAAEEEEAAAQQQQAABBNwVIKC72xtGhgACCCCAAAIIIIAAAgggUEUCBPQqanZz7+qUKVOkR48eMmzYsKyhmL+fOnWq93dDhgyRm2++Wdq1a+f9ecaMGXLrrbdmLT969GgZO3as93dm5uQxY8Z4/92vXz+ZPHmydOzYsbl3NRHbN7arVq3KWPqDXrFihfd377//vvdXtbW1MmDAAO+/wz8Luwd7aX5mntUd7ncicJphkA0NDTJ+/Hjv2c29evWKfIyEzfO5+7274oor6EnE/sZxjISPo65du4rpYbjnEYdYVYvFdYwEP2foR2lvqXzHSNA012fz9u3bZdKkSTJr1ixvg8HPCtNn8+/gokWLmnwGlTY6lkYAAQSSKUBAT2bfEjXq4Ad1OLCZn82bN88L5eZlPrC7dOmSCY3Bn/uhPRgkJ0yYIOZ/5pfbQssmCizmwQa/1Ah+2WE26/9iZAK6CeVmWRMa/QBhQl7QPDhU8wvX/fffL5deeqn3JYkfCCdOnJgJ+DHvWiLLB39RzRUOih0jpjfm5X9plQsh+MUKX5oUf5vEdYz44TzfMVR8ZNW5RJzHiOm1OYb8L3fDf65O8eJ7XegYCRsa3/r6+syX734/Bw4c2OTLwvDPCn3mFB8lSyCAAALJFCCgJ7NviRx1+Ax6OAyanQp/sBcK3eFv7vkgL+1tkevMR9jb5pelQr+ElTbS6lg619nBKMdIsYDu1zVXmjzyyCOS65fi6hAufS+1jxH/mBgxYgRfWpXeDu8LxPBVJrbHSPjfPD5HSmtM+BjJ9e9+2DTfWXf/CyzzZYn5Ytd80cvnSGn9YGkEEEiHAAE9HX1MxF5ECei5PsiDl7gHz/iGg0muX9QSAdNMg4wSPszQgs7hS9wL3VZAP0prbNTwET5GCt1WEOzB8ccf712hQkCP3hftYyR86a4ZSfi2nuijq74l4zxGunfv7p3hfeqpp3Le+lN92tH2OEpAD38WhP/NCl45lOsKhmJfQkYbKUshgAACyREgoCenV4kfaa570MMfvIXOXvgf8ubsk7mvOUrgTzxajDuQK3zk8i/0y1H40sXgcPmlqrTm5bu/tpRjJHhbgR/I/bO1nIkqrR9mae1jZO3atd6l1OGzg8HbekofZfWsoX2M+HNrmGOsrq5O5syZI9yDXtr7KcqXWLm+KAxeRWJqTJs2zTs2li9f7v13cC4aPktK6wlLI4BA8gUI6MnvYWL2IFdAz3VGqdBZ2eAvA5xBt2t9lMl9/C3ku2/ZBMJg4PCXLxTc7Uad3rXzhY9SjxH/ODv77LOzJloKynEferT3kfYx8tFHHzU5XrjnOVovzFLax4j5ojfc4/C8G9FHV51L5jpGwhPAGRn/i4/DDz/cu5InGNCDAd4sG5wTwP+z+f9C82xUpz57jQACaRUgoKe1sw7uV75Z3INDNb8cvfjii3k/iIO/DHAPul2TC90H6Fc2v2iZAD5y5Mics0znCuiE8/L6UmiG6lKOkXzHGWfQS++L9jFiRhC+h9r8mxc+Y1j6SKtjjTiOEa7EsnvvRDlGzOfEY4895n1haCZ7zWXuHxdmNNyDbtcT1kYAgeQLENCT38PE7EGxgB6+vNoEiunTp8vw4cO9D/XwfWy57lf3Z4QPz/ieGKQKDjTKL1bhqxSefvpp6d27dyash3/OpYjlNzBK+Ai/58065jFFo0aN8jZcbJZ97kEvrT9xHSP+jNZmNPQkek/iOEaCl1ebSck4gx69H2bJYsdIvkn8gk8HyfWkCn+uDCbtK60fLI0AAukQIKCno49O74X58A1O9Ba8xy846ViuS9sLTYBldprnoJfe+qCZv3bwWedB8/Cl0OF1gxNc5boU29RnEqzCPcp1OWjQrNAxkmvdYC+DW+YMevRjJa5jxIwg3LPwow6jj7J6loz7GAn+m8c96NHeV4WOkeBnQT7P4O8F4c/+8GdJvn/Too2UpRBAAIHkCRDQk9czRowAAggggAACCCCAAAIIIJBCAQJ6CpvKLiGAAAIIIIAAAggggAACCCRPgICevJ4xYgQQQAABBBBAAAEEEEAAgRQKENBT2FR2CQEEEEAAAQQQQAABBBBAIHkCBPTk9YwRI4AAAggggAACCCCAAAIIpFCAgJ7CprJLCCCAAAIIIIAAAggggAACyRMgoCevZ4wYAQQQQAABBBBAAAEEEEAghQIE9BQ2lV1CAAEEEEAAAQQQQAABBBBIngABPXk9Y8QIIIAAAggggAACCCCAAAIpFCCgp7Cp7BICCCCAAAIIIIAAAggggEDyBAjoyesZI0YAAQQQQAABBBBAAAEEEEihAAE9hU1llxBAAAEEEEAAAQQQQAABBJInQEBPXs8YMQIIIIAAAggggAACCCCAQAoFCOgpbCq7hAACCCCAAAIIIIAAAgggkDwBAnryesaIEUAAAQQQQAABBBBAAAEEUihAQE9hU9klBBBAAAEEEEAAAQQQQACB5AkQ0JPXM0aMAAIIIIAAAggggAACCCCQQgECegqbyi4hgAACCCCAAAIIIIAAAggkT4CAnryeMWIEEEAAAQQQQAABBBBAAIEUChDQU9hUdgkBBBBAAAEEEEAAAQQQQCB5AgT05PWMESOAAAIIIIAAAggggAACCKRQgICewqaySwgggAACCCCAAAIIIIAAAskTIKAnr2eMGAEEEEAAAQQQQAABBBBAIIUCBPQUNpVdQgABBBBAAAEEEEAAAQQQSJ4AAT15PWPECCCAAAIIIIAAAggggAACKRQgoKewqewSAggggAACCCCAAAIIIIBA8gQI6MnrGSNGAAEEEEAAAQQQQAABBBBIoQABPYVNZZcQQAABBBBAAAEEEEAAAQSSJ0BAT17PGDECCCCAAAIIIIAAAggggEAKBQjoKWwqu4QAAggggAACCCCAAAIIIJA8AQJ68nrGiBFAAAEEEEAAAQQQQAABBFIoQEBPYVPZJQQQQAABBBBAAAEEEEAAgeQJENCT1zNGjAACCCCAAAIIIIAAAgggkEIBAnoKm8ouIYAAAggggAACCCCAAAIIJE+AgJ68njFiBBBAAAEEEEAAAQQQQACBFAoQ0FPYVHYJAQQQQAABBBBAAAEEEEAgeQIE9OT1jBEjgAACCCCAAAIIIIAAAgikUICAnsKmsksIIIAAAggggAACCCCAAALJEyCgJ69njBgBBBBAAAEEEEAAAQQQQCCFAgT0FDaVXUIAAQQQQAABBBBAAAEEEEieAAE9eT1jxAgggAACCCCAAAIIIIAAAikUIKCnsKnsEgIIIIAAAggggAACCCCAQPIECOjJ6xkjRgABBBBAAAEEEEAAAQQQSKEAAT2FTWWXEEAAAQQQQAABBBBAAAEEkidAQE9ezxgxAggggAACCCCAAAIIIIBACgUI6ClsKruEAAIIIIAAAggggAACCCCQPAECevJ6xogRQAABBBBAAAEEEEAAAQRSKEBAT2FT2SUEEEAAAQQQQAABBBBAAIHkCRDQk9czRowAAggggAACCCCAAAIIIJBCAQJ6CpvKLiGAAAIIIIAAAggggAACCCRPgICevJ4xYgQQQAABBBBAAAEEEEAAgRQKENBT2FR2CQEEEEAAAQQQQAABBBBAIHkCBPTk9YwRI4AAAggggAACCCCAAAIIpFCAgJ7CprJLCCCAAAIIIIAAAggggAACyRMgoCevZ4wYAQQQQAABBBBAAAEEEEAghQIE9BQ2lV1CAAEEEEAAAQQQQAABBBBIngABPXk9Y8QIIIAAAggggAACCCCAAAIpFCCgp7Cp7BICCCCAAAIIIIAAAggggEDyBAjoyesZI0YAAQQQQAABBBBAAAEEEEihAAE9hU1llxBAAAEEEEAAAQQQQAABBJIn8P81tx3wzJlg4wAAAABJRU5ErkJggg=="}, "metadata": {}, "output_type": "display_data"}], "source": ["y_predict = exp.predict_model(blender)\n", "print(y_predict)\n", "exp.plot_model(estimator=blender)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Finalize Model"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           y_pred\n", "1961-01  443.5568\n", "1961-02  420.8623\n", "1961-03  468.9008\n", "1961-04  491.6614\n", "1961-05  507.2296\n", "1961-06  573.9883\n", "1961-07  658.7005\n", "1961-08  653.2689\n", "1961-09  549.4467\n", "1961-10  496.5341\n", "1961-11  427.8801\n", "1961-12  467.9969\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA+gAAAJYCAYAAADxHswlAAAgAElEQVR4Xuy9CZhVxZ2w/2PtZusGDCKkJSAuYVFRRDQqbiEYxShoEiELmsR0MvOpJCHO/L8RkeBMMm6D8k0UTYwkDpiMAR3BwWgScQm4i4JbRIwSEVGWbpZutv4/deA0594+555z6lf39KX7vc8zz0h3/epUvVVdqffWcto0NDQ0CB8IQAACEIAABCAAAQhAAAIQgAAEmpVAGwS9WfnzcAhAAAIQgAAEIAABCEAAAhCAgEcAQacjQAACEIAABCAAAQhAAAIQgAAESoAAgl4CjUARIAABCEAAAhCAAAQgAAEIQAACCDp9AAIQgAAEIAABCEAAAhCAAAQgUAIEEPQSaASKAAEIQAACEIAABCAAAQhAAAIQQNDpAxCAAAQgAAEIQAACEIAABCAAgRIggKCXQCNQBAhAAAIQgAAEIAABCEAAAhCAAIJOH4AABCAAAQhAAAIQgAAEIAABCJQAAQS9BBqBIkAAAhCAAAQgAAEIQAACEIAABBB0+gAEIAABCEAAAhCAAAQgAAEIQKAECCDoJdAIFAECEIAABCAAAQhAAAIQgAAEIICg0wcgAAEIQAACEIAABCAAAQhAAAIlQABBL4FGoAgQgAAEIAABCEAAAhCAAAQgAAEEnT4AAQhAAAIQgAAEIAABCEAAAhAoAQIIegk0AkWAAAQgAAEIQAACEIAABCAAAQgg6PQBCEAAAhCAAAQgAAEIQAACEIBACRBA0EugESgCBCAAAQhAAAIQgAAEIAABCEAAQacPQAACEIAABCAAAQhAAAIQgAAESoAAgl4CjUARIAABCEAAAhCAAAQgAAEIQAACCDp9AAIQgAAEIAABCEAAAhCAAAQgUAIEEPQSaASKAAEIQAACEIAABCAAAQhAAAIQQNDpAxCAAAQgAAEIQAACEIAABCAAgRIggKCXQCNQBAhAAAIQgAAEIAABCEAAAhCAAIJOH4AABCAAAQhAAAIQgAAEIAABCJQAAQS9BBqBIkAAAhCAAAQgAAEIQAACEIAABBB0+gAEIAABCEAAAhCAAAQgAAEIQKAECCDoJdAIFAECEIAABCAAAQhAAAIQgAAEIICg0wcgAAEIQAACEIAABCAAAQhAAAIlQABBL4FGoAgQgAAEIAABCEAAAhCAAAQgAAEEnT4AAQhAAAIQgAAEIAABCEAAAhAoAQIIegk0AkWAAAQgAAEIQAACEIAABCAAAQgg6PQBCEAAAhCAAAQgAAEIQAACEIBACRBA0EugESgCBCAAAQhAAAIQgAAEIAABCEAAQacPQAACEIAABCAAAQhAAAIQgAAESoAAgl4CjUARIAABCEAAAhCAAAQgAAEIQAACCDp9AAIQgAAEIAABCEAAAhCAAAQgUAIEEPQSaASKAAEIQKDUCTz44INy1113yaxZs2TAgAGlXlzKZ0nAtPOMGTNk0qRJcsUVV1jmUpphft1M6YYOHSozZ86U7t27l2ZhHZXK/L2+8MILqeu6evVqr/0/9alPpY51VHSygQAEINBqCSDorbbpqTgEINBcBMykec6cOSpJ8CfQl19+uVxwwQVFr8qBIuhBCQuDMnv2bBk+fHjReTXnA7RSFtWnNm3aJJMnT5YVK1bkVE/LNIu+bNt/o+p8zjnnyDXXXCPl5eXN2dSxz7btCyZjv+5VVVUHRF1jYZAAAhCAwAFCAEE/QBqKYkIAAi2DgD/p7datmyxdulRs5SYLqQkStxWcrFvtQClnMbnYSFldXZ1cf/31XrHCxDNqZd2szlZXV4tGWIvdl/269e7dO9WuAL9u+bsJDqTVZZu+EOybPgPbcaqY/Zy8IQABCLRUAgh6S21Z6gUBCJQkATPhnTZtmkyfPt3bLm5Wc222EhdbavLhHSjie6CUs5id00bKComY39fGjBkT2lfjfh9X12L3ZRtBj/vCwtTpvvvu876YKOVt8jZ9IdheSTjEtS+/hwAEIACBdAQQ9HS8SA0BCEDAmkD+ZNec6S50PtSXpuADp06dKsccc4wnSh9++GFOWfyVPiOpCxYsaHJ21P9yIHiOPGxL+CGHHNLkrHkS8S00mQ/7nf+zxYsXN9ZDsxJrMklSTpMubOty/iphUBzNNl+zUmw+wRXV/DaKOtsc9jy/riZPs3od5GB+FsYijpl/fCLYMcLaM78TFxK5JJKXn8av77hx43KOYOTLss84qi8n+WPLr3OwfaK2qJu/o0JHQ/y4pF+gRT0nf/U9yMXUzZz39z9+mfLrk19Wv4/fcMMNMnfu3MZ+E9b3otou/+++0N9d2LiRpF1IAwEIQAACdgQQdDtuREEAAhBITSB/pbDQqqWZWD/yyCM5omwm97/5zW/EnBFeu3atJ+lh54XTCrqpSFBWwiQ3qfhG1Sm/7mHCnlaKwhogSTnDVmz9cgdlKCiPYZemhbVR2PPD8jZlv/vuu+XMM8+UPn36eF+mfPWrX228gK/QFxom1t+GHsYsiVAH2RXinvQcsi98/pccSQXdlMN2Bb1QHwqem9asoJsvTZJs7zb1vemmm2TKlCmNK+phXIMiH+xrQWHO/3n+5Yx+2vwvXsLaPf9nPos1a9bkfIFXqM/Ytk/qAZIACEAAAhDwCCDodAQIQAACGRHIl7eoFeck5z4LTZrTCHpY1cPkKon4mryiZC+/TFHlN0yeeuop+fznP2/VKoUuiTPiY7ZpR521zq9jIcb+78xRheClc/n113zpkL9ymZRZWkEvVM+k5c//EiILQY/qk1FfBqU9g550VbxQR83v91Fc0vw8ab1NufL7QtRqeKE+YPMFh9UfL0EQgAAEIICg0wcgAAEIZEUgSsajVqvDtqgHy+pS0KO2Gcet5EWxy69T3EpikhXKpO0U90VClAiZ/PO/GLFh7EvRunXrvFVuf6dDvsiH1SfuuEFQGAsxsxX0sDI2p6CHHfEIbuM29fQ5B29Tzy9zlGCG8Y7a6h12dCBqV4V5Q0PwEyxzGhEPfuEVPCoQ1ceT7KaIYlZIwhH0pKMP6SAAAQi4IcAKuhuO5AIBCECgIIEoCfaDgjKcRLBs5DFs9cwXj+DzNSvopj5hW/nNxXj571APW6HUvp86TtCTrIr7xwYKpQ0TtmAH8EVv5cqV3tn1QkLtc/j4449zGIW1VxJmSfpP2Jc9hQQ97lVbWW9xT3Pfgamr2TWRdgW90B+03/5+u0bd7J7lCnqYSAf7gvkSI+yug2A9w750QND5HzcIQAAC2RJA0LPlzdMgAIFWSiBKmsJEI2qLephUac6gRwmoVtBNOf2VOnMm15zNNZ+490a7eGVXnKBnsYKeVH79dFFlTnI5VxgzW0GPev95kvxsL4kL+0In6RChXUFP+pywdPl/O4UuYwvuhinVFfRCLBB0TU8hFgIQgEB6Agh6emZEQAACEEhFIG6bcP7qY6Ez6I899piceuqpYibNkydPlvxbsk3Bkgpf1HNcCLqftxH0e++913utXPCsthGc7du3y+DBg3NYJpHBQvDjBL3QymuaM+hJ7gkw5SzU9q+99pp06tRJFi5cGHqbf9gZ9CTMknzBE2QY1z/jXqMW9kVPVJ5hslfoSxObtnZxBt2UyVwQd8kll4QWIdj+Q4YMKXivQVaCHnYvQv7fU9zfR5IvI1INfiSGAAQgAIHUBBD01MgIgAAEIJCOQJzMRZ0dzb/FPbhiaEoQddlZoVvKgzc/x50Ntz2DbsoWfB1Y2Lb1pFLnswvbehvWCkkEJEw4C93iXmhl2Zw3zt++bvL6y1/+0vjO8LC8g/K9YcOGJtvg/Zhge6VlluZsf9wXI/6XSPntUGjXQ9QN4kZ8g/nYvms77EZyv09rb3EvdN4/rE9G3SVhXqOWxRn0sHqbv4+oNnj55ZdDX6VoYvJfP5dkJ0e6EZHUEIAABCBQiACCTv+AAAQgUEQCUa81yn9kmCDlX2KVL7r555ELvZ/b/O5zn/uc5J8Fz8/DPOO6667z/i/JxVSF0Pnlj3rndNglYPlpiyHopsxhZ7kLvQc96p3ZYXUIe+942B0Ewefl5xPVXkmYmfoF+06S96DHfYkUxcz8POqLgPx3tpty+O/uzj8PXqgvx/155t8HkP8lgu0W7bB3zpuyRPHM/3s1fdl8irGCHnx/us8n6tI607bmNX7du3dvRBl2QV7YF2m2X57EtRm/hwAEIACBaAIIOr0DAhCAAAQg0MoJIGIHTgdIskvEVW2SfHHj6lnkAwEIQAACewkg6PQECEAAAhCAAASa3L4PktIkkJWgx91NUJp0KBUEIACBA58Agn7gtyE1gAAEIAABCDghEHXW3EnmZOKEQBaCHvXaOCcVIBMIQAACEChIAEGng0AAAhCAAAQgAAEIQAACEIAABEqAAIJeAo1AESAAAQhAAAIQgAAEIAABCEAAAgg6fQACEIAABCAAAQhAAAIQgAAEIFACBBD0EmgEigABCEAAAhCAAAQgAAEIQAACEEDQ6QMQgAAEIAABCEAAAhCAAAQgAIESIICgl0AjUAQIQAACEIAABCAAAQhAAAIQgACCTh+AAAQgAAEIQAACEIAABCAAAQiUAAEEvQQagSJAAAIQgAAEIAABCEAAAhCAAAQQdPoABCAAAQhAAAIQgAAEIAABCECgBAgg6CXQCBQBAhCAAAQgAAEIQAACEIAABCCAoNMHIAABCEAAAhCAAAQgAAEIQAACJUAAQS+BRqAIEIAABCAAAQhAAAIQgAAEIAABBJ0+AAEIQAACEIAABCAAAQhAAAIQKAECCHoJNAJFgAAEIAABCEAAAhCAAAQgAAEIIOj0AQhAAAIQgAAEIAABCEAAAhCAQAkQQNBLoBEoAgQgAAEIQAACEIAABCAAAQhAAEGnD0AAAhCAAAQgAAEIQAACEIAABEqAAIJeAo1AESAAAQhAAAIQgAAEIAABCEAAAgg6fQACEIAABCAAAQhAAAIQgAAEIFACBBD0EmgEigABCEAAAhCAAAQgAAEIQAACEEDQ6QMQgAAEIAABCEAAAhCAAAQgAIESIICgl0AjUAQIQAACEIAABCAAAQhAAAIQgACCTh+AAAQgAAEIQAACEIAABCAAAQiUAAEEvQQagSJAAAIQgAAEIAABCEAAAhCAAAQQdPoABCAAAQhAAAIQgAAEIAABCECgBAgg6CXQCBQBAhCAAAQgAAEIQAACEIAABCCAoNMHIAABCEAAAhCAAAQgAAEIQAACJUAAQS+BRqAIEIAABCAAAQhAAAIQgAAEIAABBJ0+AAEIQAACEIAABCAAAQhAAAIQKAECCHoJNAJFgAAEIAABCEAAAhCAAAQgAAEIIOj0AQhAAAIQgAAEIAABCEAAAhCAQAkQQNBLoBEoAgQgAAEIQAACEIAABCAAAQhAAEGnD0AAAhCAAAQgAAEIQAACEIAABEqAAIJeAo1AESAAAQhAAAIQgAAEIAABCEAAAgg6fQACEIAABCAAAQhAAAIQgAAEIFACBBD0EmgEigABCEAAAhCAAAQgAAEIQAACEEDQ6QMQgAAEIAABCEAAAhCAAAQgAIESIICgl0AjUAQIQAACEIAABCAAAQhAAAIQgACCTh+AAAQgAAEIQAACEIAABCAAAQiUAAEEvQQagSJAAAIQgAAEIAABCEAAAhCAAAQQdPoABCAAAQhAAAIQgAAEIAABCECgBAgg6CXQCBQBAhCAAAQgAAEIQAACEIAABCCAoNMHIAABCEAAAhCAAAQgAAEIQAACJUAAQS+BRqAIEIAABCAAAQhAAAIQgAAEIAABBJ0+AAEIQAACEIAABCAAAQhAAAIQKAECCHoJNAJFgAAEIAABCEAAAhCAAAQgAAEIIOj0AQhAAAIQgAAEIAABCEAAAhCAQAkQQNCVjbBhwwZlDoQbAuXl5dK2bVvZtm0bQDIiUFlZKbW1tbJnz56Mnti6H9OmTRvp3r27bNy4sXWDyLD2jCsZwt73qK5du0p9fb3s3Lkz+4e3wicyrmTf6B06dPDmLOZ/P5vz07Nnz+Z8PM+GAASKSABBV8JF0JUA94UzkXbDMU0uCHoaWvq0TKT1DNPmwLiSlpg+PYKuZ5gmB8aVNLTcpEXQ3XAkFwhAIJoAgq7sHQi6EiCC7gagRS4IugU0RQgTaQU8y1AE3RKcIgxBV8CzCGVcsYCmDEHQlQAJhwAEYgkg6LGICidA0JUAEXQ3AC1yQdAtoClCmEgr4FmGIuiW4BRhCLoCnkUo44oFNGUIgq4ESDgEIBBLAEGPRYSgKxElCmcinQiT00QIulOcsZkxkY5F5DwB44pzpLEZIuixiJwmYFxxijNRZgh6IkwkggAEFAQQdAU8E8oKuhLgvnAm0m44pskFQU9DS5+WibSeYdocGFfSEtOnR9D1DNPkwLiShpabtAi6G47kAgEIRBNA0JW9A0FXAkTQ3QC0yAVBt4CmCGEirYBnGYqgW4JThCHoCngWoYwrFtCUIQi6EiDhEIBALAEEPRZR4QQIuhIggu4GoEUuCLoFNEUIE2kFPMtQBN0SnCIMQVfAswhlXLGApgxB0JUACYcABGIJIOixiBB0JaJE4UykE2FymghBd4ozNjMm0rGInCdgXHGONDZDBD0WkdMEjCtOcSbKDEFPhIlEEICAggCCroBnQllBVwLcF85E2g3HNLkg6Glo6dMykdYzTJsD40paYvr0CLqeYZocGFfS0HKTFkF3w7FUclm9erVcd9113v8NGDAg82KZ519xxRXy4YcfytSpU+WCCy5IXIZZs2Z5aU182CdYtz59+sj1118vI0aMSPWMxIUhoVMCCLoSJ4KuBIiguwFokQuCbgFNEcJEWgHPMhRBtwSnCEPQFfAsQhlXLKApQxB0JcCY8BdeeEGqq6vlnHPOkWuuuUbMOF5XV+fJ5bhx42T48OFOC9Ccgu7Xy1aaEXSnXaGkMkPQlc2BoCsBIuhuAFrkgqBbQFOEMJFWwLMMRdAtwSnCEHQFPItQxhULaMoQBF0JMIGg//rXv/ZSffOb3/SEvKUK+qZNm2Ty5MneCrjrLx4Mv+b88qG4vaTl546gK9sYQVcCRNDdALTIBUG3gKYIYSKtgGcZiqBbglOEIegKeBahjCsW0JQhCLrI0nc+kR//93JZs3G7DO5TITd++VgZ0rdCSXZvuFlBX7BggbeCvnjxYm8V3XyCK+gPPvigPPfcc40r7EZEZ86cKdOnT/fSGuk1wjtnzhzv3yaviRMnytVXX+1tJQ+uzvsSO2jQILn//vu99JMmTcrZNm6eN2PGjMa8/JV9s4K9detW2bJli1fWqC3qwW3shxxyiJi4Hj16eOVcsWKFl6//8/xt9sHY/LIFV9D9enzxi1+Um266SYYOHSo/+MEP5JZbbvG27+dvcfcZmjHbr/fs2bNzviiIqnd+Q8flFczHxAafE1U//8sLn0+wzfJ/5+eXzyCuLc3vDSfTd7p37974RZBpS/Px29N/ninDvffe6/0uyN70qbBnaf4gEHQNPc6gK+ntD2ci7Qxl4owQ9MSonCRkIu0EY6pMGFdS4XKSGEF3gjFxJowriVE5S9jSBP3xN9fLS+9tTMXnjiWrpH7XnsaYwz7VRc4/tm+qPE4/6mA5vl/3JjG+oE+ZMsUTTbOtfciQIVaCblamfbmqqqryhN6sxgdXrX1BvPzyy72z2fmr2kYuzRcGvsQZMevXr5+X1vz3I4884v3/qPPr+fmZ+k2bNi1H0gutoD/22GMycOBAL3+/rOaLCPMFRL6gm3zGjBnT+OVCoTPovjT7cmvKZfLz61mo3mGCbr7ACMvL/O/wAw88IBdeeKF3XCGYr/l38IsX0zZ+2rvuuquRs3nefffd532xYj6m/Uy/MG0Q/HJm48aNXt19BoXYG57BOvtl6d27t5eHiTXtZJ7lf5ni96GwYxfBspvfaz8IupIgK+hKgPvCmUi74ZgmFwQ9DS19WibSeoZpc2BcSUtMnx5B1zNMkwPjShpabtK2NEH/yUOvyd1Pr3YDJ0UuU8cOlm+f2vRSNl/QjUyvXLnSk+OgrBsxTbKC7ktv/jnv/H+HbQP3xddIe/7FasHyGYk0n6hL2szv8sU3+PzTTz891Rb3/LKHraAHL7uLE/SoXQi+rAbPxgfrnS+g+e1RaOt+sEy++Pqy7Xcfv56+LAe7VRRPk0fPnj1zLvzLPxqRf2Y/mJeR+/zLAv0vY8Laya9jftlT/AkUTIqgK0ki6EqACLobgBa5IOgW0BQhTKQV8CxDEXRLcIowBF0BzyKUccUCmjKkpQm6zQr67UtWyY4ir6AHt7b72939S+KKLegm//fee098Qfe3PPtdx99unVTQzZcM/rZ4k0ch8cvvnr5kBsvgb70utqBH1TtO0PPFOH8be3A7f/7v/FX4/G3sfp39SwTzOZm4JILu737I//LEX333t6v7+ZvnRn2RElV25RDjhSPoSooIuhIggu4GoEUuCLoFNEUIE2kFPMtQBN0SnCIMQVfAswhlXLGApgxpaYJug8OcQZ/yu+Xy903bZVCfbnLTl4c5P4PuC60RsvxL44ot6Pkr6FG3x8fdop4vgcFzzmZ1Om4FPX8lOesV9KS35hdaQTfSbHYY+NvyC11cl7867vfNYIzxrvwvPMLSmS3saVfQ/XsMTDsFP0ku84squ83fF4JuSy0Qh6A7gCjinUtp27atbNu2zU2G5BJLAEGPReQ0ARNppzgTZca4kgiT00QIulOcsZkxrsQicp4AQXeONCfD/K3UwRXkqPPSRpRNnBEs8wmeMU+7xT1fIPPPYuefkzbPK7TFPX8rdJoz6Pllz8+rWCvoRlAL1TtuBT0o7GvXrs3ZOp5ff7NKf8kll3jt5kvuz372M3n88ccbz63HbYs3ceYTt4KeL9HBfpN/Bt3kZ567atUqOeGEE5ocRTBtEVZ2/wy/9q+EFXQlQQRdCXBfOBNpNxzT5IKgp6GlT8tEWs8wbQ6MK2mJ6dMj6HqGaXJgXElDy01aBN0Nx6hcws46+9uag7d/G7nyb2k3Z9SXLl2ac4t7mjPoJm1wa3Oh28xNucO2mBeiEnaLu1nhTboya94Lbz5ma/hBBx0k48ePb7ykzv+CIGxl2vYMur+CnH/7etQt9fnpgjejm/IF28pc+Gc+wZvl/a30wa3vwRiTPtgm+dvf/eflnyMPez1fMF9zW/+6desajx/kHyfIv3E/eJlfVNqoywLT/tUg6GmJ5aVH0JUAEXQ3AC1yQdAtoClCmEgr4FmGIuiW4BRhCLoCnkUo44oFNGUIgq4ESHiLI5C/xf1AqaB/10ChHRDNVRcEXUkeQVcCRNDdALTIBUG3gKYIYSKtgGcZiqBbglOEIegKeBahjCsW0JQhCLoSIOEtjsCBIuiF3q1eao2CoCtbBEFXAkTQ3QC0yAVBt4CmCGEirYBnGYqgW4JThCHoCngWoYwrIdB275CyNx+QPZ0+JTsHnGVBtXAIgu4cKRlCAAJ5BBB0ZZdA0JUAEXQ3AC1yQdAtoClCmEgr4FmGIuiW4BRhCLoCnkUo40outDbbPpbK314gbWs/8H6xq+8Iqbn4dxZko0MQdKc4yQwCEAghgKAruwWCrgSIoLsBaJELgm4BTRHCRFoBzzIUQbcEpwhD0BXwLEIZV3KhdVr2H9Lp2dtyfljzlfmy65DjLOiGhyDozlCSEQQgEEEAQVd2DQRdCRBBdwPQIhcE3QKaIoSJtAKeZSiCbglOEYagK+BZhDKu5An60pul03P/L+eHtV+6W3b2P9OCLoLuDBoZQQACqQgg6KlwNU2MoCsBIuhuAFrkgqBbQFOEMJFWwLMMRdAtwSnCEHQFPItQxpVcaO0/fFkqfjcu54e7ew32VtAbOnaRuqFfkz2V/SxI7w9hBV2Fj2AIQCABAQQ9AaRCSRB0JUAE3Q1Ai1wQdAtoihAm0gp4lqEIuiU4RRiCroBnEcq40hRaxzcWSNc//FCMmLepr5G2NWsaE+3peohsuuxpkTZtLWjvDUHQrdERCAEIJCSAoCcEFZUMQVcCRNDdALTIBUG3gKYIYSKtgGcZiqBbglOEIegKeBahjCtNoZW9fr90efTHUj/oYmno2FXKl9+Tk2jzhEWevNt+EHRbcsRBAAJJCSDoSUlFpEPQlQARdDcALXJB0C2gKUKYSCvgWYYi6JbgFGEIugKeRSjjSlNonZ65VTo9M1O2j5wssrteOj1/e06ijd95Xho6H2RBe28Igm6NrkUErl69Wq677jrv/wYMGJBTp7q6Orn++utlxIgRcsEFF7SI+lKJ5iGAoCu5I+hKgAi6G4AWuSDoFtAUIUykFfAsQxF0S3CKMARdAc8ilHGlsKDXDZ0olfMnSNuNq7yE24d/X7afcrUF6f0hCLoKX2zwCy+8INXV1U3STZ06tSSk15Wgh9XzkEMOkVmzZjUR/1hoGSZ48MEH5bnnnpNrrrlGzP/G8ikOAQRdyRVBVwJE0N0AtMgFQbeApghhIq2AZxmKoFuCU4Qh6Ap4FqGMK4UFffvIq7wEPW/bu9K54crVFpRzQxB0NcKCGRhxNZI6c+ZM6d69e3EfZpG7S0Ev5XpGoUHQLTqNRQiCbgEtGIKgKwEi6G4AWuSCoFtAU4QwkVbAswxF0C3BKcIQdAU8i1DGlabQzPlzcw596+gbvXPoCLpFx4oLefdJkQf+QWTTeyKHHC1y4c9FDjkmLirR7wsJ+qZNm2Ty5MlyzjnnyL333isffvih99/+aq7/+xUrVnjPKvS72bNny/Dhw8WPMf89Z86cxriJEyfK1Vdf3eQZvqAPGjRI7r//fi/9pEmT5IorrpCwLe7BlfKhQ4c2fvEQ90WEkXe/PH7+5lm+IJux1jzf/13Uc0xMPhd/N4LJa8aMGY3t4jMxPzD1NHUyjP06jh07NudnwfoE8wpyN/XYunWrbNmyRRYvXiylshMiUWdsxkQIuhI+gq4EuC+cibQbjmlyQdDT0NKnZSKtZ5g2B8aVtMT06RF0PcM0OTCuNKXVdYoqGyAAACAASURBVOF3peM7j0rN+Lmyq+pkLwEr6AV61duPirz/fJpuJ/L0f4jsqt8fc9DhIkP3fhmS+HPE50WqRjRJnkTQq6qqPCk3n+CZbyOD/fr1a9wKf99993mSbj5G7MeNG+f9zsinWaGfPn164++MoBsh9WXWf4aRbhNrfmfS+OJ6+eWXe3n56c3vhwwZklMeU5dp06Y1bls3Evvee+95eRWqZ3CV2q9j7969vThfhIMyXeg5fvn8upv6PPXUU3LqqafKAw88IBdeeKG3Vd3ku2DBAo+L+bfhamJMnU2Mn/aRRx5pssU9GGt2PQTbwfy3iSn1rfuJ+21GCRF0JWgEXQlwXzgTaTcc0+SCoKehpU/LRFrPMG0OjCtpienTI+h6hmlyYFxpSqti/gRpv2ZZjqD3mH2s98q1TZc+KXsqqtIgbpK2xW1xX/zPIstyL9JTAUoafM5PRU76h1BBjzqDfvrpp+fIsgn2ZXDMmDGeVPoiG8w4X4b9lW4joAMHDszJM38VPP/fYVvcTRnMx0h7/hcG5udGrM0n+MXAqlWrmpy1N6vLfj2CF80Fy79kyZImguw/P+o5SbbSB+vVo0ePnC80gizzt7hH7Rowsm++RLnrrru8cL9sSbtHa0+HoCt7AIKuBIiguwFokQuCbgFNEcJEWgHPMhRBtwSnCEPQFfAsQhlXmkKrnHeetFv/Wo6Md7/nNO996Ah6SCezWUF/6j+8G/IbPxmvoPur2UFBD65m+1vc/e3UURfPmVVoF4Lur4yHCbq/Td1n5W8LN4IeJs7BLw/M6nW+2EcJeqHn+LKcf6lb/jb24CV1+b/zV+yjBN1sXw9+/G3uCLrFwC4iCLodt8YoBF0JEEF3A9AiFwTdApoihIm0Ap5lKIJuCU4RhqAr4FmEMq40heavlm+sXi4NZRVegrBVdQvcXkiLW0G3AWHOoC/4vsjm90V6DxUZd3umZ9CjBD1YleCKsJmrR0lqcIu6v507uAquXUEPbrkPli9qi3vUirQv81GCnvY5voCbbf7+1v2o18cVWsEP+0IhWM/81X2b7tYaYxB0Zasj6EqACLobgBa5IOgW0BQhTKQV8CxDEXRLcIowBF0BzyKUcaUptLDz5gi6RedqppAkZ9DDBN1sDQ+eqY7bsm2eYz7aFfTgc/r06VPwDLp5nn8uPmoF3aQJOw8ePIOe/5qz/DPoweeY/w6evzdfSJjV7pEjR+a8zz2Yh9nibtJccsklHqNgm4SVO/8MevDMOivodn9ICLodt8YoBF0JEEF3A9AiFwTdApoihIm0Ap5lKIJuCU4RhqAr4FmEMq4kE/Swm90tcHshrKDbkksWV+g96IXOoJst7sGbz83Tghep5d9k7m819wXWl/4kZ9CDt5sHnxN3i7tJG7x1vdDZ8Lhb3PPfQ57PLXjze/52dX/rf/AZ5oI78zGr6P4XDf629eDWd7+O5ndRt7ibfILPMP/mDHqy/u+nQtDT8WqSGkFXAkTQ3QC0yAVBt4CmCGEirYBnGYqgW4JThCHoCngWoYwrudDMRXBmi7vZ2m62uPsfBN2icxECAQg0GwEEXYkeQVcCRNDdALTIBUG3gKYIYSKtgGcZiqBbglOEIegKeBahjCu50NqtXymV88bK7l6DZfOERY2/7PTMrdLpmZmyfeRk2T7yKgvS+0NYQVfhIxgCEEhAAEFPAKlQEgRdCRBBdwPQIhcE3QKaIoSJtAKeZSiCbglOEYagK+BZhDKu5EJrv2apVMyfKLuqTpKa8fMQdIs+RQgEIND8BBB0ZRsg6EqACLobgBa5IOgW0BQhTKQV8CxDEXRLcIowBF0BzyKUcSVc0HccNlq2jL0TQbfoU4RAAALNTwBBV7YBgq4EiKC7AWiRC4JuAU0RwkRaAc8yFEG3BKcIQ9AV8CxCGVdyoZW9fr+Y8+b1gy6WraNvbPxl1M8tkHNJnA00YiAAgVQEEPRUuJomRtCVABF0NwAtckHQLaApQphIK+BZhiLoluAUYQi6Ap5FKONKLrSos+YIukXnIgQCEGg2Agi6Ej2CrgSIoLsBaJELgm4BTRHCRFoBzzIUQbcEpwhD0BXwLEIZV5IJetTZdAvkrKDbQCMGAhBIRQBBT4WraWIEXQkQQXcD0CIXBN0CmiKEibQCnmUogm4JThGGoCvgWYQyriDoFt2GEAhAoMQJIOjKBkLQlQARdDcALXJB0C2gKUKYSCvgWYYi6JbgFGEIugKeRWixxpXdexpk9tPvy9qaHXJwt45S/bkq6di+rUUJsw2Jet9525o10v2e02RPRZVsuvRJVaF4zZoKH8EQgEACAgh6AkiFkiDoSoAIuhuAFrkg6BbQFCHFmkgritTiQxH07JsYQc+WebHGlWsWvS0Pr1zfWJlRh/eUmeOPyrZyFk+rmD9B2q9ZJjXj58quqpMbc/AFvaGsQjZWL7fIeX8Igq7CRzAEIJCAAIKeABKCroSUIJyJdAJIjpMg6I6BxmRXrIl0trU4sJ7GuJJ9eyHo2TIv1rgy/Ial0pBXlRev3i+82dYy+dOiBN3k0PO2AV5GG65cnTzDkJQIugofwRCAQAICCHoCSAi6ElKCcCbSCSA5ToKgOwaKoGcLNMHTGFcSQHKcBEF3DLSZxpULf/GyvLdhe+PTe3TuIH/8PydkWzmLp1XOO0/arX/N28ZutrMHPwi6BVBCIACBZiGAoCuxs8VdCXBfOBNpNxzT5IKgp6GlT1uslS59yVpuDowr2bctgp4t82KNK8+/t1mumv+mbN+x26vQj87qL187oU+2lbN4Wo/Zx0qb+hpvG7vZzo6gW0AkBAIQaHYCCLqyCRB0JUAE3Q1Ai1wQdAtoipBiTaQVRWrxoQh69k2MoGfLvJjjyrSH35aHVuw9hz7xhD4y5az+2VbO4mmFVsnNJXHmLHrY6nqaR7HFPQ0t0kIAAjYEEHQbaoEYBF0JEEF3A9AiFwTdApoipJgTaUWxWnQogp598yLo2TIv5rjywwVvyON/3ehVqFtZO1lYfbx0K2+fbQVTPi2JoG+esFB29xqSMuf9yRF0a3QEQgACCQkg6AlBRSVD0JUAEXQ3AC1yQdAtoClCijmRVhSrRYci6Nk3L4KeLfNijivfve81MVvdjZzX1u+WKWf3l4nDS3ebu9nabra4R93UXugCuTSthqCnoUVaCEDAhgCCbkMtEIOgKwEi6G4AWuSCoFtAU4QUcyKtKFaLDkXQs29eBD1b5sUcVybMeUXeXLfV294+9/m1ckK/SrnzksHZVjDF09qtXymV88bK7l6DZfOERU0iuy78rnR851GpPe8O2TlwTIqcc5Mi6NboCIQABBISQNATgopKhqArASLobgBa5IKgW0BThBRzIq0oVosORdCzb14EPVvmxRxXxs5+UT7YXC8Lq4+TsbNf8iq25MoRJbvNvf2apVIxf6LsqjpJasbPa9IQXR79sZS9fr9sHX2j1A+62LqhEHRrdARCAAIJCSDoCUEh6EpQMeFMpIvLNyx3BD1b5sWcSGdbkwPnaYwr2bcVgp4t82KOK8ffsNSrjHn/ub/dvZS3ufuCvuOw0bJl7J1NGqLzEzOk/OW7Zduoa6Vu2GXWDYWgW6MjEAIQSEgAQU8ICkFXgkLQiwvQIncE3QKaIqSYE2lFsVp0KIKeffMi6NkyL+a4EhT03y9fJ//6yDte5Q77VGe58YIjZcBBnbKtbMzTzOq4WSU3q+NmlTz/0+mZW6XTMzNl+8jJsn3kVdZlR9Ct0REIAQgkJICgJwSFoCtBIejFBWiRO4JuAU0RUsyJtKJYLToUQc++eRH0bJkXc1wxgm4uiFty1Yky/pfL5d1PtjVW7pi+3eSerw/NtrIxT4sT8LjfJ60Mgp6UFOkgAAFbAi1K0F944QWprq72WAwdOlRmzpwp3bt39/794IMPyowZM7z/Puecc+Saa64RM3kzn7q6Orn++utl8eLF3r+nTp0qF1xwQSKmnEFPhCk2ERPpWETOEyDozpEWzLCYE+lsa3LgPI1xJfu2QtCzZV6sceWDzXXeufOjeneReZOOEX81PVg7s/W9lD5xAu7/vm7Yt2TbqKnWRUfQrdERCAEIJCTQYgTdyPmsWbNypNxnkP87k858rrjiCu//B/+9adMmmTx5sve74cOHx2JE0GMRJUrARDoRJqeJEHSnOGMzK9ZEOvbBrTgB40r2jY+gZ8u8WOOKeb2aOXfu39z+zd+8KivWbmms3CmHdZdZFw/KtrIxT4sT9Lgt8Ekrg6AnJUU6CEDAlkCLEHQj1dOmTfPEesCAAU1YGAHv169f46p4UNhN4vzYfIEvBBdBt+16uXFMpN1wTJMLgp6Glj5tsSbS+pK13BwYV7JvWwQ9W+bFGlfyBX1tTb1cs/BteWlNjRzeq7PM/upg6dG5Q7aVjXla3C3tHVY9It0WfU+iLpFLWhkEPSkp0kEAArYEWoSgr1692lvx/vDDDxs5TJo0yfuZv319xIgRjYJu0l933XXe/5mP/9++3Jvt8M8991zONvjt27c3YdypUydB0G27HoLuhpx9Lgi6PTs/0kyO03zMkRvzhSKfbAiUlZVJ27ZtJWz8zqYE2T+loaEh+4cGnoigZ4u/WIL+0IqPZNrDq+T8ob1k+rmHe5UK+1m2tS38tIr5E6T9mmVSM36u7Kpquv0+7jVsSeuCoCclRToIQMCWQIsQdLMivmDBgkah9repjxs3TsaMGeOdLzf/7W9Zzxd0c1Z9+vTpOefV8wX91FNPbcL4qaeekuaeDNk2PHEQgICeQJq/f1/m08ToS9i6c2iNzNN+adS6ewi1jyJw99OrZcbC1+Wqs4+QyZ8/wkv2h5UfSvW9L8pJhx0k8y4fWXLw2swZK/LuU9Iw6SGR/qc1Ld+Hr0ib2aNEDjlaGqqfLLnypy0Qf+tpiZEeAgcOgRYp6Aa/vwo+ZcoUuemmm0S7gh7VpKygu+nsbEV1wzFNLqygp6GlT1uslS59yVpuDowr2bctK+jZMi/WuDL76TUy++n3pfqUQ6X6lCqvUvnb3rOtafzTut9zmrStWSObLn1S9lTsLXPwY35n0pjfmTS2H1bQbckRBwEIJCXQIgTdrIiHrYK/99573jZ3zqAn7Q7Nl46JdPbsEfRsmRdrIp1tLQ6spzGuZN9eCHq2zIs1roQJun+ze9/KMllYfXy2FU3wtB6zj5U29TWysXq5NJRVNIkwvzNpzO9MGtsPgm5LjjgIQCApgRYh6P458969e3tCnn8TO7e4J+0OzZeOiXT27BH0bJkXayKdbS0OrKcxrmTfXgh6tsyLNa7c9Kd3Ze7za2X6uQPl/KEHe5XyBd1/N3q2NY1/Ws/b9l4SvOHK1ZGJk6SJexKCHkeI30MAAloCLULQDQRfylesWOExyX+XOe9B13aV4sYzkS4u37DcEfRsmRdrIp1tLQ6spzGuZN9eCHq2zIs1rvxwwRvy+F83ys0XHilnHnlQY6X896GX2jvQTQGTyHeSNHEtiKDHEeL3EICAlkCLEXQtCNt4zqDbksuNYyLthmOaXBD0NLT0aYs1kdaXrOXmwLiSfdsi6NkyL9a4Yt6Bbs6c33nJYO9d6P6nVAU96fb1uG3wSVoPQU9CiTQQgICGAIKuoWe2Um3YoMyBcEOAiXT2/QBBz5Z5sSbS2dbiwHoa40r27YWgZ8u8WOOKL+jzJh0tR/Xu2kTQl1w5QrqVt8+2sgWe1m79SqmcN1Z29xosmycsikwZd5Fckgoh6EkokQYCENAQQNA19BB0Jb394UyknaFMnBGCnhiVk4TFmkg7KVwLzYRxJfuGRdCzZV6scWXs7Bflg831srD6OOlbWd5YqaifZ1vrpk9L+o7zynnnSbv1r8nmCQtld68hVsVG0K2wEQQBCKQggKCngBWWlBV0JcB94Uyk3XBMkwuCnoaWPm2xJtL6krXcHBhXsm9bBD1b5sUaV6K2sk+Y84q8uW6r5K+sZ1vrpk/rsOoR6bboe7LjsNGyZeydkcWpmD9B2q9ZJjXj58quqpOtio2gW2EjCAIQSEEAQU8BC0FXwioQzkS6eGyjckbQs2VerIl0trU4sJ7GuJJ9eyHo2TIv1rgSJehRZ9OzrXXTp5W9fr90efTHUj/oYtk6+sbI4nRd+F3p+M6jUnveHbJz4BirYiPoVtgIggAEUhBA0FPAQtCVsBD04gG0yBlBt4CmCCnWRFpRpBYfiqBn38QIerbMizWuRAl61O3u2da66dM6PXOrdHpmpmwfOVm2j7wqsjhG4o3MG4k3Mm/zQdBtqBEDAQikIYCgp6EVkpYt7kqA+8KZSLvhmCYXBD0NLX3aYk2k9SVruTkwrmTftgh6tsyLMa747zvvW1kmC6uPz6nQtIfflodWrM95P3q2NQ5/WlJB7/zEDCl/+e5YkS9UJwS9FFqcMkCgZRNA0JXti6ArASLobgBa5IKgW0BThBRjIq0oTqsIRdCzb2YEPVvmxRhX3ly3RSbMedV7vZp5zVrwM/vpNTL76fel+pRDpfqUqmwrW+BpSQU9aToEvWSaloJAoFUSQNCVzY6gKwEi6G4AWuSCoFtAU4QUYyKtKE6rCEXQs29mBD1b5sUYV8z7z81Z8wNJ0JNuXUfQs+2fPA0CELAjgKDbcWuMQtCVABF0NwAtckHQLaApQooxkVYUp1WEIujZNzOCni3zYowrf37rE/nRA2/JGUf0kFvGfTanQqW6gp70dvbyl38lnZ/4idQN+5ZsGzXVqrHY4m6FjSAIQCAFAQQ9BaywpAi6EiCC7gagRS4IugU0RUgxJtKK4rSKUAQ9+2ZG0LNlXoxxZe4La+WmP74rE0/oI1PO6p9ToYdWfCTTHl4l5w/tJdPPPTzbykY8rc3Wj6Tyvy+StjVrZMu5P5cdh38xslxJb3svVDEEvSSanUJAoEUTQNCVzYugKwEi6G4AWuSCoFtAU4QUYyKtKE6rCEXQs29mBD1b5sUYVwqtkpecoO/YIj1+faa02fbxXvBt28mmrz8me7rnfrHgt4r/vvRdVSdJzfh5Vo2FoFthIwgCEEhBAEFPASssKYKuBIiguwFokQuCbgFNEVKMibSiOK0iFEHPvpkR9GyZF2NcKSTohc6nZ1vzvU8rf/FO6fzUT3MeXehVa+3XLJWK+RMFQW+O1uKZEIBAUgIIelJSEekQdCVABN0NQItcEHQLaIqQYkykFcVpFaEIevbNjKBny7wY48pNf3pX5j6/Vqac3V8mDu+TUyFf0I/q3UXmTTom28qGPK3s1bnS5c//kifoP5DtI68MLZvZBt/9ntNkT0WVbLr0Savys4JuhY0gCEAgBQEEPQWssKQIuhIggu4GoEUuCLoFNEVIMSbSiuK0ilAEPftmRtCzZV6McaXQu84LvSM925rve1rDHqn83Thpt+4V7wd7KvvJ5ksekoayCgS9WRqEh0IAAi4IIOhKigi6EiCC7gagRS4IugU0RUgxJtKK4rSKUAQ9+2ZG0LNlXoxxxbxizayUm3egm1etBT+1dbvk9Nuek25l7WTJVSdmW9mIp7X7aIVU3ne+7OkxQDZNXCzSrmPBcvW8bYD3+w1XrrYqPyvoVtgIggAEUhBA0FPACkuKoCsBIuhuAFrkgqBbQFOEFGMirShOqwhF0LNvZgQ9W+bFGFcKCbqp3fE3LPUq+eLVJ2db2YinpT1XjqCXRLNRCAhAoAABBF3ZPRB0JUAE3Q1Ai1wQdAtoipBiTKQVxWkVoQh69s2MoGfLvBjjytjZL8oHm+tlYfVx0reyvEmFSk3Q/Ven7ThstGwZe2dsAyDosYhIAAEINDMBBF3ZAAi6EiCC7gagRS4IugU0RUgxJtKK4rSKUAQ9+2ZG0LNlXoxx5fRbn5Xa+t2y5MoR0q28fZMKxf0+WwIinZ65VTo9M1Pqhn1Lto2aGvv4HrOPlTb1Nd4lceayuLQftrinJUZ6CEAgLQEEPS2xvPQIuhIggu4GoEUuCLoFNEVIMSbSiuK0ilAEPftmRtCzZV6McSVuhTxuhT1bAvsFvdDr1YJlMre4m9vcEfSsW4rnQQACSQkg6ElJRaRD0JUAEXQ3AC1yQdAtoClCijGRVhSnVYQi6Nk3M4JemPmGbTvlv1/6UETayIXHHCy9uxW+0CyuBYsxrsQJ+oQ5r8ib67aGXiIXV95i/L7zEzOk/OW7Zduoa6Vu2GWxj6iYP0Har1kmNePnyq6q9OfoWUGPRUwCCEBASQBBVwJE0JUAEXQ3AC1yQdAtoClCijGRVhSnVYQi6Nk3M4IezXxL/W4Z94uX5ZOtO7xEnTu2kwXfGSa9utpLuutxJckt7XGXyGXd63zhrj3vDtk5cEzs4xH0WEQkgAAEmpkAgq5sAARdCRBBdwPQIhcE3QKaIsT1RFpRlFYTiqBn39QIejTzu5f9Xf7fE+/lJKg+pUqqTznUuqFcjytvrtsiE+a8Kkf17iLzJh0TWq5C70m3rogiMK1wd3n0x2Iults6+kapH3Rx6iezgp4aGQEQgEBKAgh6SmD5yRF0JUAE3Q1Ai1wQdAtoihDXE2lFUVpNKIKefVMj6NHM/+v5tXLzn97NSfCPp/WTb5/8aeuGcj2umPefmxVy8/5z8x70sE+pCXraM+UIunV3IxACEMiIAIKuBI2gKwEi6G4AWuSCoFtAU4S4nkgritJqQhH07JsaQY9mvnN3g4y8eVljgqru5TJ30tHStazpTelJW871uJJE0Gc/vUZmP/2+t/JvdgA09yfta9P8W9+TXiqXXz9W0Ju7xXk+BFo+AQRd2cYIuhIggu4GoEUuCLoFNEWI64m0oiitJhRBz76pEfRo5g+tWC9m9dn//PobR8vQPl1VjeR6XHloxUcy7eFVcv7QXjL93MNDy4agdxAzttTW1qraThvcs2dPbRbEQwACJUoAQVc2DIKuBIiguwFokQuCbgFNEeJ6Iq0oSqsJRdCzb2oEPZz5B5vrvK3jH2yub0xgtpCbreSaj+txJYl8J0mjqVOaWPO6NLPF3bzP3Lw2LcmHFfQklEgDAQg0JwEEXUkfQVcCRNDdALTIBUG3gKYIcT2RVhSl1YQi6Nk3NYLelPnO3XvkJ4vfkUUr18sZR/TwEjz+141y84VHyplHHqRqJNfjShL5TrLKrqpUiuD2a5ZKxfyJsqvqJKkZPy82ss22j6XigW9Ku49f9151t+WLt8mOI8bGxgUTsMU9FS4SQwACFgQQdAtowRAEXQkQQXcD0CIXBN0CmiLE9URaUZRWE4qgZ9/UCHou8+sfeUfmL1/X+MMHLx8mv1j6dzHb3aefO1DOH3qwqpFcjytJBP3Pb30iP3rgLe/LhlvGfVZVfm1wh1WPSLdF30ss6J2X/ETKl/8q57Eb/vEtkXYdEhcFQU+MioQQgIAlAQTdEpwfhqArASLobgBa5IKgW0BThLieSCuK0mpCEfTsmxpB38/8ub9tlurfvpbTCJPP+Ixs37nH2SVrrseVJDe0J7lILqueV/7yr6TzEz+RumHfkm2jpsY+ttvCy6XDO4/lpNt4+YvS0GnvzoYkHwQ9CSXSQAACGgIIuoaeiCDoSoAIuhuAFrkg6BbQFCGuJ9KKorSaUAQ9+6ZG0Pcz/9WyNTLrifdzGuG7n6sSMxa4ugXd9bhizskbAS+0/T7Ju9Kz6nlpz5OXvzBbOj/9s8bi7enSWzZ9e//N+knKjaAnoUQaCEBAQwBB19BD0JX09oczkXaGMnFGCHpiVE4Sup5IOylUC8+EcSX7BkbQ9zP/eMsO+cLPX8hphFkXD5IVa7eUvKAXusDOXHg3dvZL0reyTBZWH599Jws8sfMTM6T85bslzSvTOj17q3RaNtPLZfPXHpHdBx2Zqg4IeipcJIYABCwIIOgW0IIhrKArAe4LZyLthmOaXBD0NLT0aRF0PcO0OTCupCWmT4+g5zL0t4Obn9544VFy9pE9xeUla67HlQlzXpE3122VeZOOlqN6R78C7vgblnoVffHqk/WdRpFD14XflY7vPCq1590hOweOSZyT/+70jdXLpaGsInGcSYigp8JFYghAwIIAgm4BDUFXQgsJZyLtnmlcjgh6HCG3v3c9kXZbupaZG+NK9u2KoOcyD1ttLmVBP/***************************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"}, "metadata": {}, "output_type": "display_data"}], "source": ["final_model = exp.finalize_model(blender)\n", "print(exp.predict_model(final_model))\n", "exp.plot_model(final_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Save and Load Model"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Saved\n"]}], "source": ["_ = exp.save_model(final_model, \"my_blender\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Loaded\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>y_pred</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1961-01</th>\n", "      <td>443.5568</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-02</th>\n", "      <td>420.8623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-03</th>\n", "      <td>468.9008</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-04</th>\n", "      <td>491.6614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-05</th>\n", "      <td>507.2296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-06</th>\n", "      <td>573.9883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-07</th>\n", "      <td>658.7005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-08</th>\n", "      <td>653.2689</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-09</th>\n", "      <td>549.4467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-10</th>\n", "      <td>496.5341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-11</th>\n", "      <td>427.8801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1961-12</th>\n", "      <td>467.9969</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           y_pred\n", "1961-01  443.5568\n", "1961-02  420.8623\n", "1961-03  468.9008\n", "1961-04  491.6614\n", "1961-05  507.2296\n", "1961-06  573.9883\n", "1961-07  658.7005\n", "1961-08  653.2689\n", "1961-09  549.4467\n", "1961-10  496.5341\n", "1961-11  427.8801\n", "1961-12  467.9969"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["loaded_exp = TSForecastingExperiment()\n", "m = loaded_exp.load_model(\"my_blender\")\n", "# Predictions should be same as before the model was saved and loaded\n", "loaded_exp.predict_model(m)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**That's it for this notebook. Users can hopefully see how easy it was to use the automated approach to model this data and we were able to achieve reasonale results on par with (or even better than) the manual approach.**"]}], "metadata": {"interpreter": {"hash": "83be8a105015beb0be3130957f981d91e0431cfb610106a7fbaabcd7fd8062ab"}, "kernelspec": {"display_name": "pycaret_dev_sktime_16p1", "language": "python", "name": "pycaret_dev_sktime_16p1"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}