{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# 3D Multi-organ Segmentation with <PERSON>win UNETR  (BTCV Challenge)\n", "\n", "\n", "This tutorial uses a Swin UNETR [1] model for the task of multi-organ segmentation task using the BTCV challenge dataset. The architecture of Swin UNETR is demonstrated as below\n", "\n", "![image](../figures/swin_unetr_btcv.png)\n", "\n", "The following features are included in this tutorial:\n", "1. Transforms for dictionary format data.\n", "1. Define a new transform according to MONAI transform API.\n", "1. Load Nifti image with metadata, load a list of images and stack them.\n", "1. Randomly adjust intensity for data augmentation.\n", "1. <PERSON><PERSON> IO and transforms, ThreadDataLoader, and AMP to accelerate training and validation.\n", "1. Swin <PERSON>ETR model, DiceCE loss function, Mean Dice metric for multi-organ segmentation task.\n", "\n", "For this tutorial, the dataset needs to be downloaded from: https://www.synapse.org/#!Synapse:syn3193805/wiki/217752. More details are provided in the \"Download dataset\" section below.\n", "\n", "In addition, the json file for data splits needs to be downloaded from this [link](https://drive.google.com/file/d/1qcGh41p-rI3H_sQ0JwOAhNiQSXriQqGi/view?usp=sharing). Once downloaded, place the json file in the same folder as the dataset. \n", "\n", "For BTCV dataset, under Institutional Review Board (IRB) supervision, 50 abdomen CT scans of were randomly selected from a combination of an ongoing colorectal cancer chemotherapy trial, and a retrospective ventral hernia study. The 50 scans were captured during portal venous contrast phase with variable volume sizes (512 x 512 x 85 - 512 x 512 x 198) and field of views (approx. 280 x 280 x 280 mm3 - 500 x 500 x 650 mm3). The in-plane resolution varies from 0.54 x 0.54 mm2 to 0.98 x 0.98 mm2, while the slice thickness ranges from 2.5 mm to 5.0 mm. \n", "\n", "- Target: 13 abdominal organs including \n", "    1. <PERSON><PERSON><PERSON> \n", "    2. <PERSON> \n", "    3. <PERSON> \n", "    4. <PERSON><PERSON><PERSON><PERSON> \n", "    5. <PERSON><PERSON><PERSON><PERSON> \n", "    6. <PERSON><PERSON> \n", "    7. <PERSON><PERSON><PERSON> \n", "    8. <PERSON><PERSON><PERSON> \n", "    9. IVC \n", "    10. <PERSON> and <PERSON><PERSON><PERSON> Veins \n", "    11. <PERSON><PERSON><PERSON> \n", "    12. Right adrenal gland \n", "    13. Left adrenal gland.\n", "- Modality: CT\n", "- Size: 30 3D volumes (24 Training + 6 Testing)\n", "- Challenge: BTCV MICCAI Challenge\n", "\n", "The following figure shows image patches with the organ sub-regions that are annotated in the CT (top left) and the final labels for the whole dataset (right).\n", "\n", "Data, figures and resources are taken from: \n", "\n", "\n", "1. [Self-Supervised Pre-Training of Swin Transformers\n", "for 3D Medical Image Analysis](https://arxiv.org/abs/2111.14791)\n", "\n", "2. [Swin UNETR: Swin Transformers for Semantic Segmentation of Brain Tumors in MRI Images](https://arxiv.org/abs/2201.01266)\n", "\n", "3. [High-resolution 3D abdominal segmentation with random patch network fusion (MIA)](https://www.sciencedirect.com/science/article/abs/pii/S1361841520302589)\n", "\n", "4. [Efficient multi-atlas abdominal segmentation on clinically acquired CT with SIMPLE context learning (MIA)](https://www.sciencedirect.com/science/article/abs/pii/S1361841515000766?via%3Dihub)\n", "\n", "\n", "![image](../figures/BTCV_organs.png)\n", "\n", "\n", "\n", "The image patches show anatomies of a subject, including: \n", "1. large organs: spleen, liver, stomach. \n", "2. Smaller organs: gallbladder, esophagus, kidneys, pancreas. \n", "3. Vascular tissues: aorta, IVC, P&S Veins. \n", "4. G<PERSON>: left and right adrenal gland\n", "\n", "If you find this tutorial helpful, please consider citing [1] and [2]:\n", "\n", "[1]: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON>, A., 2022. Self-supervised pre-training of swin transformers for 3d medical image analysis. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (pp. 20730-20740).\n", "\n", "[2]: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, D., 2022. Swin UNETR: Swin Transformers for Semantic Segmentation of Brain Tumors in MRI Images. arXiv preprint arXiv:2201.01266.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/swin_unetr_btcv_segmentation_3d.ipynb)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Pre-trained Swin UNETR Encoder\n", "\n", "We use weights from self-supervised pre-training of Swin UNETR encoder (3D Swin Tranformer) on a cohort of 5050 CT scans from publicly available datasets. The encoder is pre-trained using reconstructin, rotation prediction and contrastive learning pre-text tasks as shown below. For more details, please refer to [1] (CVPR paper) and see this [repository](https://github.com/Project-MONAI/research-contributions/tree/main/SwinUNETR/Pretrain). \n", "\n", "![image](https://lh3.googleusercontent.com/pw/AM-JKLVLgduGZ9naCSasWg09U665NBdd3UD4eLTy15wJiwbmKLS_p5WSZ2MBcRePEJO2tv9X3TkC52MsbnomuPy5JT3vSVeCji1MOEuAzcsxily88TdbHuAt6PzccefwKupbXyOCumK5hzz5Ul38kZnlEQ84=w397-h410-no?authuser=2)\n", "\n", "Please download the pre-trained weights from this [link](https://github.com/Project-MONAI/MONAI-extra-test-data/releases/download/0.8.1/model_swinvit.pt) and place it in the root directory of this tutorial. \n", "\n", "If training from scratch is desired, please skip the step for initializing from pre-trained weights. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai; import nibabel; import tqdm\" || pip install -q \"monai-weekly[nibabel, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "\n", "from monai.losses import DiceCELoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    RandCropByPosNegLabeld,\n", "    RandShiftIntensityd,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", "    RandRotate90d,\n", "    EnsureTyped,\n", ")\n", "\n", "from monai.config import print_config\n", "from monai.metrics import DiceMetric\n", "from monai.networks.nets import SwinUNETR\n", "\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    load_decathlon_datalist,\n", "    decollate_batch,\n", "    set_track_meta,\n", ")\n", "\n", "\n", "import torch\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms for training and validation\n", "To save on GPU memory utilization, the num_samples can be reduced to 2. \n", "\n", "A note on design related to MetaTensors:\n", "\n", "- Summary: using `EnsureTyped(..., track_meta=False)` (caching) and `set_track_meta(False)` (during training) speeds up training significantly.\n", "\n", "- We are moving towards the use of MONAI's MetaTensor in place of numpy arrays or PyTorch tensors. MetaTensors have the benefit of carrying the metadata directly with the tensor, but in some use cases (like here with training, where training data are only used for computing loss and metadata is not useful), we can safely disregard the metadata to improve speed.\n", "\n", "- Hence, you will see `EnsureTyped` being used before the first random transform in the training transform chain, which caches the result of deterministic transforms on GPU as Tensors (rather than MetaTensors), with `track_meta = False`. \n", "\n", "- On the other hand, in the following demos we will display example validation images, which uses metadata, so we use `EnsureTyped` with `track_meta = True`. Since there are no random transforms during validation, tracking metadata for validation images causes virtually no slowdown (~0.5%).\n", "\n", "- In the next section, you will see `set_track_meta(False)`. This is a global API introduced in MONAI 0.9.1, and it makes sure that random transforms will also be performed using Tensors rather than MetaTensors. Used together with `track_meta=False` in `EnsureTyped`, it results in all transforms being performed on Tensors, which we have found to speed up training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_samples = 4\n", "\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "train_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"], ensure_channel_first=True),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-175,\n", "            a_max=250,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.5, 1.5, 2.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        EnsureTyped(keys=[\"image\", \"label\"], device=device, track_meta=False),\n", "        RandCropByPosNegLabeld(\n", "            keys=[\"image\", \"label\"],\n", "            label_key=\"label\",\n", "            spatial_size=(96, 96, 96),\n", "            pos=1,\n", "            neg=1,\n", "            num_samples=num_samples,\n", "            image_key=\"image\",\n", "            image_threshold=0,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[0],\n", "            prob=0.10,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[1],\n", "            prob=0.10,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[2],\n", "            prob=0.10,\n", "        ),\n", "        RandRotate90d(\n", "            keys=[\"image\", \"label\"],\n", "            prob=0.10,\n", "            max_k=3,\n", "        ),\n", "        RandShiftIntensityd(\n", "            keys=[\"image\"],\n", "            offsets=0.10,\n", "            prob=0.50,\n", "        ),\n", "    ]\n", ")\n", "val_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"], ensure_channel_first=True),\n", "        ScaleIntensityRanged(keys=[\"image\"], a_min=-175, a_max=250, b_min=0.0, b_max=1.0, clip=True),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.5, 1.5, 2.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        EnsureTyped(keys=[\"image\", \"label\"], device=device, track_meta=True),\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download dataset and format in the folder\n", "1. Download dataset from here: https://www.synapse.org/#!Synapse:syn3193805/wiki/89480. After you open the link, navigate to the \"Files\" tab, then download Abdomen/RawData.zip.\n", "\n", "    Note that you may need to register for an account on Synapse and consent to use agreements before being able to view/download this file. There are options to download directly from the browser or from the command line; please refer to Synapse API documentation for more info.\n", "\n", "\n", "2. After downloading the zip file, unzip. Then put images from `RawData/Training/img` in `./data/imagesTr`, and put labels from `RawData/Training/label` in `./data/labelsTr`.\n", "\n", "\n", "3. Make a JSON file to define train/val split and other relevant parameters. Place the JSON file at `./data/dataset_0.json`.\n", "\n", "    You can download an example of the JSON file [here](https://drive.google.com/file/d/1qcGh41p-rI3H_sQ0JwOAhNiQSXriQqGi/view?usp=sharing), or, equivalently, use the following `wget` command. If you would like to use this directly, please move it into the `./data` folder."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# uncomment this command to download the JSON file directly\n", "# wget -O data/dataset_0.json 'https://drive.google.com/uc?export=download&id=1qcGh41p-rI3H_sQ0JwOAhNiQSXriQqGi'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_dir = \"data/\"\n", "split_json = \"dataset_0.json\"\n", "\n", "datasets = data_dir + split_json\n", "datalist = load_decathlon_datalist(datasets, True, \"training\")\n", "val_files = load_decathlon_datalist(datasets, True, \"validation\")\n", "train_ds = CacheDataset(\n", "    data=datalist,\n", "    transform=train_transforms,\n", "    cache_num=24,\n", "    cache_rate=1.0,\n", "    num_workers=8,\n", ")\n", "train_loader = ThreadDataLoader(train_ds, num_workers=0, batch_size=1, shuffle=True)\n", "val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_num=6, cache_rate=1.0, num_workers=4)\n", "val_loader = ThreadDataLoader(val_ds, num_workers=0, batch_size=1)\n", "\n", "# as explained in the \"Setup transforms\" section above, we want cached training images to not have metadata, and validations to have metadata\n", "# the EnsureTyped transforms allow us to make this distinction\n", "# on the other hand, set_track_meta is a global API; doing so here makes sure subsequent transforms (i.e., random transforms for training)\n", "# will be carried out as Tensors, not MetaTensors\n", "set_track_meta(False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check data shape and visualize"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image shape: torch.<PERSON><PERSON>([1, 255, 223, 276]), label shape: torch.<PERSON><PERSON>([1, 255, 223, 276])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["slice_map = {\n", "    \"img0035.nii.gz\": 170,\n", "    \"img0036.nii.gz\": 230,\n", "    \"img0037.nii.gz\": 204,\n", "    \"img0038.nii.gz\": 204,\n", "    \"img0039.nii.gz\": 204,\n", "    \"img0040.nii.gz\": 180,\n", "}\n", "case_num = 1\n", "img_name = os.path.split(val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n", "img = val_ds[case_num][\"image\"]\n", "label = val_ds[case_num][\"label\"]\n", "img_shape = img.shape\n", "label_shape = label.shape\n", "print(f\"image shape: {img_shape}, label shape: {label_shape}\")\n", "plt.figure(\"image\", (18, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"image\")\n", "plt.imshow(img[0, :, :, slice_map[img_name]].detach().cpu(), cmap=\"gray\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"label\")\n", "plt.imshow(label[0, :, :, slice_map[img_name]].detach().cpu())\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create Swin UNETR model\n", "\n", "In this section, we create a Swin UNETR model for the 14-class multi-organ segmentation. We use a feature size of 48, which is compatible with the self-supervised pre-trained weights. We also use gradient checkpointing (use_checkpoint) for more memory-efficient training. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "model = SwinUNETR(\n", "    img_size=(96, 96, 96),\n", "    in_channels=1,\n", "    out_channels=14,\n", "    feature_size=48,\n", "    use_checkpoint=True,\n", ").to(device)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize Swin UNETR encoder from self-supervised pre-trained weights\n", "\n", "In this section, we intialize the Swin UNETR encoder from pre-trained weights. The weights can be downloaded using the wget command below, or by following [this link](https://github.com/Project-MONAI/MONAI-extra-test-data/releases/download/0.8.1/model_swinvit.pt) to GitHub. If training from scratch is desired, please skip this section."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# uncomment to download the pre-trained weights\n", "# !wget https://github.com/Project-MONAI/MONAI-extra-test-data/releases/download/0.8.1/model_swinvit.pt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weight = torch.load(\"./model_swinvit.pt\")\n", "model.load_from(weights=weight)\n", "print(\"Using pretrained self-supervied Swin UNETR backbone weights !\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimizer and loss function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.backends.cudnn.benchmark = True\n", "loss_function = DiceCELoss(to_onehot_y=True, softmax=True)\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)\n", "scaler = torch.cuda.amp.GradScaler()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Execute a typical PyTorch training process"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["def validation(epoch_iterator_val):\n", "    model.eval()\n", "    with torch.no_grad():\n", "        for batch in epoch_iterator_val:\n", "            val_inputs, val_labels = (batch[\"image\"].cuda(), batch[\"label\"].cuda())\n", "            with torch.cuda.amp.autocast():\n", "                val_outputs = sliding_window_inference(val_inputs, (96, 96, 96), 4, model)\n", "            val_labels_list = decollate_batch(val_labels)\n", "            val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]\n", "            val_outputs_list = decollate_batch(val_outputs)\n", "            val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]\n", "            dice_metric(y_pred=val_output_convert, y=val_labels_convert)\n", "            epoch_iterator_val.set_description(\"Validate (%d / %d Steps)\" % (global_step, 10.0))  # noqa: B038\n", "        mean_dice_val = dice_metric.aggregate().item()\n", "        dice_metric.reset()\n", "    return mean_dice_val\n", "\n", "\n", "def train(global_step, train_loader, dice_val_best, global_step_best):\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    epoch_iterator = tqdm(train_loader, desc=\"Training (X / X Steps) (loss=X.X)\", dynamic_ncols=True)\n", "    for step, batch in enumerate(epoch_iterator):\n", "        step += 1\n", "        x, y = (batch[\"image\"].cuda(), batch[\"label\"].cuda())\n", "        with torch.cuda.amp.autocast():\n", "            logit_map = model(x)\n", "            loss = loss_function(logit_map, y)\n", "        scaler.scale(loss).backward()\n", "        epoch_loss += loss.item()\n", "        scaler.unscale_(optimizer)\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "        optimizer.zero_grad()\n", "        epoch_iterator.set_description(  # noqa: B038\n", "            f\"Training ({global_step} / {max_iterations} Steps) (loss={loss:2.5f})\"\n", "        )\n", "        if (global_step % eval_num == 0 and global_step != 0) or global_step == max_iterations:\n", "            epoch_iterator_val = tqdm(val_loader, desc=\"Validate (X / X Steps) (dice=X.X)\", dynamic_ncols=True)\n", "            dice_val = validation(epoch_iterator_val)\n", "            epoch_loss /= step\n", "            epoch_loss_values.append(epoch_loss)\n", "            metric_values.append(dice_val)\n", "            if dice_val > dice_val_best:\n", "                dice_val_best = dice_val\n", "                global_step_best = global_step\n", "                torch.save(model.state_dict(), os.path.join(root_dir, \"best_metric_model.pth\"))\n", "                print(\n", "                    \"Model Was Saved ! Current Best Avg. Dice: {} Current Avg. Dice: {}\".format(dice_val_best, dice_val)\n", "                )\n", "            else:\n", "                print(\n", "                    \"Model Was Not Saved ! Current Best Avg. Dice: {} Current Avg. Dice: {}\".format(\n", "                        dice_val_best, dice_val\n", "                    )\n", "                )\n", "        global_step += 1\n", "    return global_step, dice_val_best, global_step_best"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_iterations = 30000\n", "eval_num = 500\n", "post_label = AsDiscrete(to_onehot=14)\n", "post_pred = AsDiscrete(argmax=True, to_onehot=14)\n", "dice_metric = DiceMetric(include_background=True, reduction=\"mean\", get_not_nans=False)\n", "global_step = 0\n", "dice_val_best = 0.0\n", "global_step_best = 0\n", "epoch_loss_values = []\n", "metric_values = []\n", "while global_step < max_iterations:\n", "    global_step, dice_val_best, global_step_best = train(global_step, train_loader, dice_val_best, global_step_best)\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train completed, best_metric: 0.8430 at iteration: 29500\n"]}], "source": ["print(f\"train completed, best_metric: {dice_val_best:.4f} \" f\"at iteration: {global_step_best}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot the loss and metric"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAskAAAGDCAYAAADZHo16AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/MnkTPAAAACXBIWXMAAAsTAAALEwEAmpwYAABsKUlEQVR4nO3dd3xc5ZX/8c/RqHeruEnuvWIbYzA9tNCCSYcUQhqbQjbJJrsh2fRssimb/NJIIY0UCAGSECAkkBA6NrgA7sa23JuaJY3aSKN5fn/MHXksq1plir7v10svZu69M/fMyFydOXOe5zHnHCIiIiIickJKrAMQEREREYk3SpJFRERERLpQkiwiIiIi0oWSZBERERGRLpQki4iIiIh0oSRZRERERKQLJcky5Mys0cymxzoOEREZHDNzZjYz1nEMJTPbYmYXxzoOiX9KkpOMme01s8u82zeb2bPDfL4nzex90ducc7nOuYphPudxM8sYrnOMFDP7opn9LtZxiEhyMrO/m9mXu9m+ysyOmlnqIJ77SS+JPqPL9j972y8+3ec+zXimeudt9H6OmdnDZnZ59HHOuQXOuSdHMjZJTEqSpUeDuXgOFzObClwAOOC6YXj+uHvNIiKD8GvgHWZmXba/E7jLORcc5PO/CtwUuWNmxcBKoGqQzzsYhc65XOAM4B/An83s5hjGIwlKSXKSMrN5wE+Ald4n6jpve4aZ/Z+Z7fc+Zf/EzLK8fReb2UEz+5SZHQV+ZWZjvE/iVV719mEzK/eO/yrhhPWH3jl+6G3v/HrOzArM7Dfe4/eZ2WfNLMXbd7OZPevFc9zM9pjZVX28tJuANcCdwLuiXlOdmS2Mev2lZtZiZmO9+9ea2cvecc+b2eKoY/d6r3kj0GRmqWZ2m5ntNjO/mW01s9dHHe8zs2+bWbUX863ea06Nes2/MLMjZnbIzP7HzHyn8Tu8zvtasM6r2MyL2vcp77n9ZrbDzC71tq8ws3Vm1uD9fr8z0POKSFJ5ACgmfK0GwMzGANcCv/GuGau968wRM/uhmaUP4PnvAt4adY27Efgz0BZ1vpSoa2qNmd1rZkVR++/zqtr1Zva0mS2I2nenmd1uZn/1rncvmNmM/gTmnDvqnPse8EXgG1F/e6K/cfWZ2WeirvfrzWySt2+umf3DzGq96+xbBvC+SBJQkpyknHPbgA8Aq732h0Jv19eB2cASYCZQBnw+6qHjgSJgCnAL4X8jv/LuTwZagB965/hv4BngVu8ct3YTyg+AAmA6cBHhJPfdUfvPBnYAJcA3gV+YnVLxiHYT4YvyXcBrzWyccy4A/InwxTniLcBTzrlKM1sK/BL4N8J/LH4KPGgnt2vcCFxDuAIRBHYT/qNSAHwJ+J2ZTfCOfT9wFeH3cBlwfZcY7wSChN/fpcAVwPsYADObDfwe+BhQCjwCPGRm6WY2B7gVOMs5lwe8FtjrPfR7wPecc/nADODegZxXRJKLc66F8HXgpqjNbwG2O+deATqAjxO+Bq8ELgU+NIBTHAa2Er7O4Z3nN12O+Qjh6+RFwETgOHB71P6/AbOAscAGwtf3aDcQvg6PAXYBXx1AfBD++zAWmNPNvv8gfP2/GsgH3gM0m1kO4Sr03d5jbwB+ZGbzB3huSWTOOf0k0Q/hZOky7/bNwLNR+wxoAmZEbVsJ7PFuX0z4039mL8+/BDgedf9J4H1djnGEE0Sf93zzo/b9G/BkVHy7ovZle48d38O5zwfagRLv/nbg497ty4DdUcc+B9zk3f4x8JUuz7UDuCjqPXtPH+/ry8Aq7/a/gH+L2neZF3cqMA4IAFlR+28Enujheb8I/K6b7Z8D7o26nwIc8n5HM4FK77xpXR73NOE/JiWx/reoH/3oJz5+vGtnXeTa7l0fP97DsR8D/hx13wEzezj2ScIFgHcQ/lA/F3jV23cQuNi7vQ24NOpxE7xreWo3z1nonbPAu38n8POo/VcTTvC7i2dq5FrcZXumt/087/5eTvyd3BG5tnd5zFuBZ7ps+ynwhVj/PvUzcj+qJI8upYQT0fXeV2t1wN+97RFVzrnWyB0zyzazn3qtEg2Ek7DCfrYPlABpwL6obfsIV68jjkZuOOeavZu5PTzfu4DHnHPV3v27vW0ATwDZZna2hfuWlxD+yg/CVfBPRF6z97onEa5oRByIPpGZ3RTVnlEHLPReD97jDvTw2Cneaz4S9difEq5EDMREot4351zIO0+Zc24X4T9kXwQqzeweM4u8lvcS/qZgu5mtNbNrB3heEUkyzrlngWrgeq9VYQXh6ydmNtvCbXRHvWv81zhxreuvPwGXEP6G67fd7J9CuC84ck3cRriCPc5rd/i61+7QwIlvxaJjOBp1u5me/0b0JPI3p7abfZMIf3PYXcxnd/m78XbC37bKKKFBSsnNdblfTbhdYoFz7lA/H/MJwl9Rne2cO2pmS4CXCFeluzu+6/naCV9stnrbJhOuiA6Ihfum3wL4LNwvDZBBOGE/wzn3ipndS7hqewx42Dnn9447AHzVOdfbV3Sdr8PMpgA/I/y142rnXIeZvcyJ13wEKI967KSo2wcIV5JL3OAGxBwGFkXFZN55DgE45+4G7jazfMJJ+DeAdzrndgI3er13bwDuN7Ni51zTIGIRkcT3G8KtEHOAR51zx7ztPyZ8Tb/ROec3s48BbxrIEzvnms3sb8AHCbd5dXWA8Ld1z3XdYWbvBFYR/mZsL+EWt+OcuN4OhdcT/vZtRw+xzQA2d7P9Kefc5ac+REYLVZKT2zGgPDIIw6tG/gz4f3ZiQFuZmb22l+fII5xY13kDLb7QzTm6nRPZOddBuBfuq2aW5yWf/wGczpRn1xOuPMwnXCVeAswj3BMd6bW7m/BXZG/3bkf8DPiAV2U2M8sxs2vMLK+Hc+UQTpqrAMzs3YQryRH3Ah/13rtC4FNRr/kI8BjwbTPL9waszDCzi3p5bSlmlhn1k+Gd4xozu9TM0gh/WAkAz5vZHDO7xDuulfDvJ+TF+g4zK/V+13Xe84d6ObeIjA6/IZyIvp/wjBcReUAD0GhmcwknuqfjM4Rb2PZ2s+8nhP8OTIHOgdWros4fAGoIf9P5tdM8/ynMbJyZ3Ur479anvetiVz8HvmJms7y/D4stPEPHw8BsM3unmaV5P2dZ1ABqSX5KkpPbv4AtwFEzi7QofIrwwIc13ldb/6T7wQwR3wWyCFeF1xBuz4j2PeBNFp6d4vvdPP4jhPugK4BnCSevvzyN1/Iu4FfOuf0uPGL5qHPuKOFBhG83s1Tn3AveuSYSHggCgHNuHeE/DD8kXKHYRbgfulvOua3At4HVhD8ELCLcwxfxM8KJ8EbCFZhHCA/U6/D23wSkE66eHwfuJ9yD15MbCSe6kZ/dzrkdhPv8fkD4vX8d8DrnXBvhCvrXve1HCbdyfNp7riuBLWbWSPh3c4MLD9wRkVHMS16fJ1wEeDBq1yeBtwF+wte2P5zm8x/22jq68z3vnI+ZmZ/w35KzvX2/IdxadojwNXPN6Zy/izozawI2Ee5hfrNzrqe/O98hXJR4jPCHhV8QHlPiJzwY8QbC3+wdJfyNXcLPzy/9Z8719m25iPTFwtPW/cQ5NyXWsYiIiMjQUCVZZIDMLMvMrrbwfMplhL/K+3NfjxMREZHEoUqyyACZWTbwFOHpjlqAvwIfdc41xDQwERERGTJKkkVEREREulC7hYiIiIhIF0qSRURERES6iNliIiUlJW7q1KmxOr2IyKCsX7++2jlX2veRyUHXbBFJZKdzzY5Zkjx16lTWrVsXq9OLiAyKme3r+6jkoWu2iCSy07lmq91CRERERKQLJckiIiIiIl0oSRYRERER6UJJsoiIiIhIF0qSRURERES6UJIsIiIiItKFkmQRERERkS6UJIuIiIiIdKEkWURERESkCyXJIiIiIiJdKEkWEREREemizyTZzH5pZpVmtrmP484ys6CZvWnowjvZpoP1rNtbO1xPLyIiIiJxaldlIy8fqBux8/WnknwncGVvB5iZD/gG8NgQxNSjbz22g6/8ddtwnkJEREREeuGc44ntlXztkW280kPSGuwIcf/6g1z67Sf56D0vEQh2nPb56lva+crDW7nyu0/zpYe2nPbzDFRqXwc45542s6l9HPYR4I/AWUMRVE8KstLYX9M0nKcQERERSTjOOcxs2M+zbm8t3/z7Dl70vtm/4+kKlk4u5OZzp3LVwgmkGDzw8mF+8K+d7KtpZnppDn95+TBV/gB33LSc3Iw+U89OHSHHvesO8H+P7qC2uY0bzprEJ66YM1wv7RT9j7QHZlYGvB54DcOcJBdmpVHf0j6cpxAREZEYa24L8kJFLRfNLiUlZfgTv/4KdoT444aDvHbBeAqz0/v9uPX7jvPlh7awqLyAL123EN8QvKaOkGPjwTqe3VnNM7uqeXl/HWdPL+K2q+ayYGLBKce3tndw3/qDrN1TS/mYLKaV5DCtJIepJTnkZ6bR0tZBU1uQ5rYOWto6MIM0XwppPiPNl0JtUxs/+NdO/rmtktK8DP7n+oVcs2gCf3n5EL9evY+P3vMyX83bRla6j301zSyYmM/PblrOZfPG8ueXDvGf92/kxjvWcOe7z6I4N+OU+ALBDiobAhxtaOVoffjngZcPseVwA2dNHcOvX7eChWWnvq7hNOgkGfgu8CnnXKivTzBmdgtwC8DkyZMHfKICL0kOhVxc/U8jIiIiQ6OlrYObf7WWF/fU8po5pXz3rUspyE6LdVgA/H3LUT71x038ds0+7nrfORRk9R5XQ2s73/r7Dn73wj4KstJ45WA99S1BvvOWM0jznd7cCfXN7Xzj0e08/MphGlqDACyYmM8bzyznb5uPcO0PnuUNS8v55GtnM6EgC39rO3e9sJ+fP7OH6sYA4/IzeGRTG8GQG/C58zJT+c/XzuHd500lOz2cQt583jRuWjmVp3ZW8evn99IUCPLZa+Zz2byxnZXtNywrpzA7jQ/dtYE3/2Q1v3nvCsoKs9h2xM8TOyp5fNsxXj5QR9eQygqz+P6NS3nd4gkjUiXvypzr+03y2i0eds4t7GbfHiASeQnQDNzinHugt+dcvny5W7du3YCC/fkzFfzPX7ex8YtXkJ8ZH//DiMjoZGbrnXPLYx3HSDmda7Ykt2MNrYzNyxhw8vL0q1UU5aR3WxVsbe/g/b9Zx3O7qnnrWZO5f/0BJhRk8dN3nsm8CflDFfpp+8Bv1/Pc7mpa2ztYVFbAb997Njk9tA/8ffNRvvDgZqr8AW4+dxqfuGI2v12zj6//bTuXzx/HD9+2lIxU34DO//fNR/jcX7ZQ29TGqiUTuXjOWM6bUdxZma1vbudHT+7iV8/txQyuXDieJ7ZX0tAa5IJZJXz4NTM5e1oRHSHHweMt7KlpYm91E02BINnpqWSn+8hK93UmwO0dIdo7QrQFQwBcNm8cY3L6X0Hvat3eWt5z51rSU32k+Ywj9a0ALC4v4IJZJUwpymFcQSbj88M/+VmpQ5Ycn841e9CVZOfctKgA7iScTD8w2OftTr73ia2+uV1JsoiISAys31fLtx97led31/DW5ZP48vUL+p3svXKgjpt/9SIA7z5vGv9x+ezOJLMtGOLDd23gmZ3VfOtNi3nz8km8eXk5H/zdel7/o+f4xhsXs2pJ2YBi7Qg5jtS30BYMMb00d2AvtIvGQJAndlRyw1mTWDmjmA/f/RLv/fVafnXzCrLST7z+9fuO891/vsozO6uZNyGfO965nDMmFQLwgYtmkJXm4wsPbuH9v1nPT99x5kmP7UmVP8AXHtzMI5uOMn9CPr+6+axuP2QUZKfx6avn8Y5zpvB/j+3goVcOc/n8cXz4NTNZXF7YeVyqz5jqtVowci2+LJ9axH0fOJf//vMmSnIz+PhlY7l4Tilj8zNHLogB6DNJNrPfAxcDJWZ2EPgCkAbgnPvJsEbXRWEkSW5pZ9JInlhERCSB7K5q5OZfvUi1v43MtBQy03xkpKaQn5XGyunFXDJ3LGdOGUPqAL7y33Swnu/8YwdP7KiiJDedVUsm8od1B9hV1chP3nEmpXmn9plGawuG+NQfNzI2L5PXzB3LL57dw983H+V/rl/IBbNK+NgfXuLx7ZV85fqFvHl5+K/8ssljeOgj53Pr3S/x0Xte5rEtx5gzPi9caSwI/4Sc41hDgGMNrRyrb+VoQysHj7ewv7aZg8ebae8If2P++Wvn857zp/UWYq8e33aMQDDEtWdM5KypRXznLSE+9oeX+bffrednN53JxoP1fO+fO3l2VzVFOel89pp5vOvcqae0Vbzr3Klkpfn41J828q5fvchXVi1k9rjcbiumuyob+fvmI/z82T00Bzr4z9fO4ZYLp/fZqjGpKJvv3bCU7751SUzaFHozZ3we93/w3FiH0S/9md3ixv4+mXPu5kFF04eCqCRZREQkUXR4zZa9DdhyzvHMzmq2H23gcF040Ttc10IwFOJDF89k1ZKJ/Up4ahoDvPtXa2kOdPCOcybT2h6itb2D1mCIKn8rv3xuDz99uoL8zFQunjOWC2eXsri8gOklOSclzc45KqqbeH5XNf/aXskTO6ooyErjU1fO5V3nTiE7PZUr5o/nk/e9wnU/fJafvvPMk6qVXf3oyV1sP+rnF+9azqXzxvGGZWV8+k+bePeda5lekkNFdROfvWYe7zxnykmPG5uXyV3vO5tvPbqD+9cf5K+bjvT6+guz05g0Jpv5E/J57YLxTCnO5p9bj/E/f93KrHG5XDCrtNvH+VvbCTl67DN+6JUjjM/P5MzJYwBYtaSMQHuI//rjRi785hMcawhQkpvOZ66eyzvOmdLZstCdt5w1iYy0FD5x7yu89rtPU5KbwbkzijlvZjHTS3N5+tUq/rb5KLsqGwFYOb2Yr1y/gJlj83p97V3FW4KcaPrVkzwcTqe/bfvRBq787jPc/rZlXLN4wjBFJiLSN/Uky0B8+K4N7Kps5P4PriSvh3bB37+4n0//aRMAuRmplBVmUTYmi6P1rWw90sCKaUV8ZdVC5ozvOVFqbe/gxp+tYevhBu655RyWegldtMZAkGd3VvH4tkqe2FFJdWMbAJlpKcybkM/CiQU0BYI8v7uGow3hntGywizevLyc95w/7ZR2xy2H67nlN+upbgzwjTcu5vqlp7ZE7Djq59ofPMPViybwvRuWdm4PBDv4yZMV/PipXXzkkll8+DUz+3gnT8yCcMSrGqcYjM/PZFx+JqV5GWSmndq+0BQI8sYfP8/huhb+cuv5TCvJOWn/6t013Hr3Bopz03nk3y84pcJe39LOWf/zT965cgqfu3b+SfvuemEfv3hmD28/ZwpvWzG5X+0TEYfrWnh2ZzXP7a7m+d01VPkDAKQYnD2tmCsXjueKBeOYUJDV7+eU7p3ONTuhkuSj9a2c87+P87XXL+JtZw98dgwRkaGiJFn6a+vhBq7+/jMAXLN4Aj+8cekpFb4dR/1c98NnWTGtiB++bdlJ1cyOkOMPaw/wzUe3428NcvO5U/nYZbNOSbZDIceH797A37cc5cdvX8aVC/suJoVCjl1VjWw5XM/mQw1sPlTP1sMNpKWmsHJGMefNKOG8mcVMLsrutSpZ0xjgg3dt4MU9tbzujIl88XXzOweTBTtCvPHHz3PgeAv/+PiF3U7/1RFyQzItWm8O1DZz3Q+fpSgnnT9/+DzyM9NwzvHL5/bytUe2MSY7nerGAP/7hkXcuOLkHOP+9Qf55H2v8OcPndvtB4+h4JxjV2Uju6saWTGtmKJBDJCTU8Vk4N5Iilw06lraYhyJiIhI//zkqd3kpPu46dyp/PjJ3Zw9rYibVk7t3N/S1sGtd28gLzON77xlySlf9/tSjLedPZmrFo7nW4/t4JfP7eHetQe4dN5Yrl40gQtnl5KZ5uPrf9/O3zYf5bPXzOtXggyQkmLMHpfH7HF5vN4r8EaKZwP5qr44N4O73nc2P3lyN9//106e31XNl1Yt4JpFE/jlc3t45WA9379xabcJcuQ1DrdJRdn86O1n8s5fvMBHf/8SP3jbMv77z5v4y8uHuWL+OP7vLWfw7l+t5Tv/eJVVSyae1C7x8MbDlBVmscQbgDcczIxZ4/KYNW5gLRUyfBIqSc5MSyE9NUU9ySIikhD21zTz8MbDvP+C6fznFXN49aifrzy8lTPKCztnPPjyw1vYWdnIb9+7otfBb2Ny0vna6xdx41mT+e2avTy29RgPvHyYnHQfSyYX8tyuGt61cgrvHcTgNDj9PtY0XwofuXQWVywYz3/d/wq33v0Sf557iGd3VXPZvHG8Lg7aJFfOKOaL1y3gsw9s5vxv/Iv6lnY+ecVsPnTxTFJSjM9cPZc3/ng1v3hmDx+5dBYAx5vaeHZnNe89f5p6fEeZ05vJOkbMjIKsNBqUJIuISAK445ndpKak8J7zp5GSYnz7LWcwNi+TD921gbrmNh565TC/f/EAH7x4Ro8DyrpaVF7AN990Bmv/+zJ++94VXLekjO1H/Lx2wTg+/7oFMU/k5ozP448fPJfbrprLM7uqSU9N4auvXxjzuCLecc4Ubj53KgC/vPksbr1kVucCZWdOKeKK+eP46dMVVDeG+4Mf3XKUYMhx7eKJsQpZYiShKskQngaurllJsoiIxLcqf4B71x3kjWeWMc6bB7YwO53b376MN//keT74uw1sPlTPssmF/Mflswf8/Gm+FC6YVcoFs0r53zcsGurwByXVl8IHLprBNYsmEAiGOl9/vPjidQv43LXzu23z+K8r5/La7z7NDx7fyZdWLeThjUeYUpzNwrLYL2YiIyuhKslwYmlqERGR4eScI3QaS/dG/Oq5PbR3hLjlwhknbV8yqZDPXjOf1RU1mMH3blh62ksUx7tJRdnMHDu4RTyGS0990DPH5vLWsyZx1wv7Wb/vOM/vrubaGC2LLLGVcJXkgqy0zmUMRUREhsu//XY9jYEgd757BempA0tiG1rb+e3qfVy9cMIp040B3LRyCq3tHZwxqZBJRdlDFbIMkY9dNosHXjrE+369lpBDrRajVMJ9dC3IViVZRESGl7+1nX9tr+T53TV88aEtA378XWv24w8E+cBFM7rdb2b820UzOGd68WBDlWEwNi+T910wnePN7cwozWFuL3NTS/JKvCRZ7RYiIr0ysyvNbIeZ7TKz27rZP9nMnjCzl8xso5ldHYs449lzu2oIhhwXzCrh7hf287s1+/r92Nb2Dn7x7B4umFXCovKCYYxShtMtF05nSnE2N66YrFaLUSrhkuTCrHQaA0GCHaFYhyIiEnfMzAfcDlwFzAduNLP5XQ77LHCvc24pcAPwo5GNMv499WoVuRmp/Pxdy3nNnFK++OAWXqio6fNx9S3tfObPm6huDPDBHqrIkhhyM1J58pMX874Lpsc6FImRhEuSC7LCbdQNrcEYRyIiEpdWALuccxXOuTbgHmBVl2McEBmqXwAcHsH44p5zjqd2VHLezGIyUn1878alTC7O5kN3beDg8eYeH/O3TUe4/DtP8cBLh/jARTNYOUOtFIlOFeTRLeGS5MLs8DKNdc1adU9EpBtlwIGo+we9bdG+CLzDzA4CjwAfGZnQEsOuykYO17dy0eyxAORnpvGzm5bTFgxxy2/Ws7uqkSp/gNb2DpxzHKlv4ZbfrueDd22gJDeDBz58HrddNVcJlkiCS8jZLQD1JYuInL4bgTudc982s5XAb81soXPupD42M7sFuAVg8uTJMQgzNp7cUQXARXNOLO4xozSX79+4lPf8ei2Xfvupzu2p3jRiqT7j01fN5T3nT0va6dxERpuES5LzvSS5TkmyiEh3DgGTou6Xe9uivRe4EsA5t9rMMoESoDL6IOfcHcAdAMuXLz/9CYMTzFOvVjFrbC5lhVknbX/N3LE8dOv57Kz009gaxB8I0hQIEgw53r5iCpOLNZWbSDJJuCS5MDucJGtpahGRbq0FZpnZNMLJ8Q3A27ocsx+4FLjTzOYBmUDViEYZp5rbgry4p5Z3nTul2/0LywpYWKYZK0RGg4T7TijSbqGlqUVETuWcCwK3Ao8C2wjPYrHFzL5sZtd5h30CeL+ZvQL8HrjZOTdqKsW9Wb27hraOUGc/soiMXglXSVZPsohI75xzjxAekBe97fNRt7cC5410XIngqVeryErzcda0MbEORURiLOEqyWm+FHLSfUqSRURkSDnneHJHFefOCE/9JiKjW8IlyRCeBk7tFiIiMpT21jSzv7b5pFktRGT0SsgkOV9LU4uIyBB7akd4co+LZitJFpEETZILslKpb9FiIiIiMnSefLWKaSU5TCnOiXUoIhIHEjJJLsxKVyVZRESGTGt7B2sqalRFFpFOCZkkF2SlqSdZRESGzAt7amltDylJFpFOCZkkF2arJ1lERIbOH9cfJCM1hXOmF8c6FBGJEwmZJOdnpREIhmht74h1KCIikuCe313Ng68c5v0XTCcrXVO/iUhYQibJkaWpVU0WEZHBaAuG+NwDm5lUlMWtl8yMdTgiEkcSMknWqnsiIjIUfvZMBburmvjSdQvITFMVWUROSOgkWYP3RETkdB2obeYH/9rJaxeM45K542IdjojEmYRMkguz0gFVkkVE5PR96aEtpJjxhdctiHUoIhKHEjJJPlFJ1oIiIiIycP/Yeox/bqvko5fOYmJhVqzDEZE4lJhJsgbuiYjIaWpuC/LFB7cwZ1we7zl/WqzDEZE4lZBJcl5GKmbQoCRZREQG6PFtlRyqa+Fz184nzZeQfwZFZAQk5NUhJcXCq+4pSRYRkQHaVdmIGSyfOibWoYhIHEvIJBnCfclqtxARkYGqqG6irDBLU76JSK8SOknWFHAiIjJQe6obmV6aG+swRCTOJXSSrEqyiIgMhHOOPVVNTC/JiXUoIhLnlCSLiMiocawhQFNbBzNKlSSLSO8SNkkuzFaSLCIiA1NR1QigdgsR6VPCJsmRSrJzLtahiIhIgthd3QTAdFWSRaQPCZskF2al0xFyNAaCsQ5FREQSREVVI9npPsbnZ8Y6FBGJcwmbJEeWplbLhYiI9FdFVRPTSnIws1iHIiJxLmGT5HwvSdY0cCIi0l8Vmv5NRPopYZPkwuxwkqylqUVEpD9a2zs4eLxF07+JSL8kbJIcabfQ0tQiItIf+2qacU6D9kSkfxI2SY5UktWTLCIi/dE5/VuJ2i1EpG8JmyRr4J6IiAxEhTf92zRVkkWkHxI2Sc5K85HuS9HAPRER6ZfdVY2My88gNyM11qGISALoM0k2s1+aWaWZbe5h/9vNbKOZbTKz583sjKEPs9vzkq+lqUVEpJ8qqprUaiEi/dafSvKdwJW97N8DXOScWwR8BbhjCOLql/DS1G0jdToREUlQzjkqqho1aE9E+q3P75ycc0+b2dRe9j8fdXcNUD4EcfVLgSrJIiLSD7VNbTS0BjVHsoj021D3JL8X+NsQP2ePCrLS1JMsIiJ9igzaUyVZRPpryJJkM3sN4ST5U70cc4uZrTOzdVVVVYM+Z6EqySIi0g+R6d9mqCdZRPppSJJkM1sM/BxY5Zyr6ek459wdzrnlzrnlpaWlgz6vBu6JiEh/VFQ1kZ6aQtmYrFiHIiIJYtBJsplNBv4EvNM59+rgQ+q/wuw0/K1BOkJuJE8rIiIJZndVE1OLs/GlWKxDEZEE0efAPTP7PXAxUGJmB4EvAGkAzrmfAJ8HioEfmRlA0Dm3fLgCjhZZUKShpZ0xOekjcUoREUlAFdWNzB6bF+swRCSB9Gd2ixv72P8+4H1DFtEARJamrlOSLCLSycyuBL4H+ICfO+e+3mX//wNe493NBsY65wpHNMgR1N4RYn9NM1cuGB/rUEQkgST0skNamlpE5GRm5gNuBy4HDgJrzexB59zWyDHOuY9HHf8RYOmIBzqCDtQ2Eww5Tf8mIgOSsMtSw4kkua5ZC4qIiHhWALuccxXOuTbgHmBVL8ffCPx+RCKLkYoqTf8mIgOX4ElyuMVClWQRkU5lwIGo+we9bacwsynANOBfPewf0mk7Y6WiWtO/icjAJXiSfGLgnoiIDNgNwP3OuY7udg71tJ2xUlHVRHFOOgXeOBYRkf5IiiRZq+6JiHQ6BEyKul/ubevODSR5qwWEk+RpJWq1EJGBSegkOT01hZx0H7XqSRYRiVgLzDKzaWaWTjgRfrDrQWY2FxgDrB7h+EZcRXWj+pFFZMASOkkGmFiYxaHjLbEOQ0QkLjjngsCtwKPANuBe59wWM/uymV0XdegNwD3OuaRejamuuY3qxjbNbCEiA5bQU8ABTCrK5oCSZBGRTs65R4BHumz7fJf7XxzJmGJl/b7jAJxRXhjbQEQk4SR8JXnSmCwO1jaT5MUQERE5DS/uqSXdl8LSyYWxDkVEEkziJ8lF2fgDQU0DJyIip1izp5YzJhWQmeaLdSgikmASPkkuH5MNwIFatVyIiMgJTYEgmw/Vs2JaUaxDEZEElPBJ8qSiLAAOHG+OcSQiIhJP1u87TkfIcfa04liHIiIJKAmS5EglWUmyiIic8OKeWnwpxrIpY2IdiogkoIRPkvMz0yjISlMlWURETvLCnhoWlhWQm5HwEzmJSAwkfJIM4ZYL9SSLiEhEa3sHrxyo52z1I4vIaUqOJHlMtirJIiLS6aX9dbR1hJQki8hpS44kuSibg8dbCIU0V7KIiIT7kc1g+VQlySJyepIjSR6TRVswRFVjINahiIhIHHhhTw3zxudTkJUW61BEJEElRZJcrhkuRETE0xYMsWH/cc6eriqyiJy+pEiSJ0UWFFFfsojIqLfpUB2t7epHFpHBSYokuXyMt6CIZrgQERn1XthTC8BZ6kcWkUFIiiQ5M83H2LwMtVuIiAgvVNQya2wuxbkZsQ5FRBJYUiTJEJ7hQu0WIiKjW7AjxPp96kcWkcFLniR5jBYUEREZ7bYeaaAxEGTFtOJYhyIiCS55kuSibI7Ut9DeEYp1KCIiEiMvev3IGrQnIoOVPEnymGxCDo7UtcY6FBERiZE1FbVMLc5mXH5mrEMRkQSXNElyeZE3w4X6kkVERqUth+t5ckclF88ZG+tQRCQJJE2S3DlXsma4EBEZddo7Qnzyvo2MyUnnY5fNinU4IpIEUmMdwFCZUJCJL8VUSRYRGYVuf2IX24408LObllOYnR7rcEQkCSRNJTnVl8LEwkzNcCEiMspsOVzPD/+1i+uXTOTy+eNiHY6IJImkSZIh3HKhSrKIyOgRabMozE7ni9ctiHU4IpJEki9JViVZRGTUiLRZfO31C9VmISJDKml6kgEmFWVR3Rigpa2DrHRfrMMREZEhcqC2mduf2EVmmo/8rDQKstJITbHONosrFoyPdYgikmSSLEkOz3Bx8Hgzs8blxTgaEREZKr9ZvZc/rDtAbnoq/kCwc/uEgky+8Dq1WYjI0EuqJLk8Mg2ckmQRkaTy+PZKzp9Zwm/fezYdIYe/tZ2GliAleelkpyfVnzIRiRPJ1ZPsLSiyv0aD90REksWe6iYqqpq4bF545gpfilGYnc7k4mwlyCIybJIqSS7NzSAzLYUDxzV4T0QkWTy+7RgAl8zVSnoiMnKSKkk2M8rHZGvVPRGRJPL4tkrmjMvrHHciIjISkipJBpg0JkuVZBGRJFHf0s7avbVcMk9VZBEZWcmXJBdlc7C2GedcrEMREZFBevrVKoIhx2VKkkVkhCVfkjwmG38gSH1Le6xDERGRQXp82zGKctJZMmlMrEMRkVEm+ZJkb4YLrbwnIpLYgh0hnny1iovnlOJLsViHIyKjTNIlydFzJYuISOLasL+OuuZ2Lp07LtahiMgolHRJ8pTicJK8T3Mli4gktMe3HyM1xbhwdkmsQxGRUSjpkuS8zDSKctLZX9sU61BERGQQHt9WydnTi8jLTIt1KCIyCiVdkgwwuShblWQRkQS2r6aJXZWNarUQkZhJyiR5SrGSZBEZvczsSjPbYWa7zOy2Ho55i5ltNbMtZnb3SMfYl8e3VQJwqaZ+E5EYSc4kuSibI/UttAVDsQ5FRGREmZkPuB24CpgP3Ghm87scMwv4NHCec24B8LGRjrMvj28/xsyxuUwpzol1KCIySvWZJJvZL82s0sw297DfzOz7XsVio5ktG/owB2ZycQ4hBwc1w4WIjD4rgF3OuQrnXBtwD7CqyzHvB253zh0HcM5VjnCMvfK3tvNCRa2qyCISU/2pJN8JXNnL/quAWd7PLcCPBx/W4HTOcFGrJFlERp0y4EDU/YPetmizgdlm9pyZrTGzbq/xZnaLma0zs3VVVVXDFO6pdlU2Egw5zppSNGLnFBHpqs8k2Tn3NFDbyyGrgN+4sDVAoZlNGKoAT0ckSd6vvmQRke6kEi5sXAzcCPzMzAq7HuScu8M5t9w5t7y0tHTEgqv0BwAYl585YucUEelqKHqS+1O1AEauKlGam0F2uk+D90RkNDoETIq6X+5ti3YQeNA51+6c2wO8SjhpjgtVXpJcmpcR40hEZDQb0YF7I1WVMDMmF2VrrmQRGY3WArPMbJqZpQM3AA92OeYBwlVkzKyEcPtFxQjG2KtKfwAzKM5Nj3UoIjKKDUWS3J+qxYibXJTNXlWSRWSUcc4FgVuBR4FtwL3OuS1m9mUzu8477FGgxsy2Ak8A/+mcq4lNxKeq8gcoyk4nzZeUEzCJSIJIHYLneBC41czuAc4G6p1zR4bgeQdlSnE2T75aRSjkSEmxWIcjIjJinHOPAI902fb5qNsO+A/vJ+5U+VvVaiEiMddnkmxmvyf8tVyJmR0EvgCkATjnfkL4Qnw1sAtoBt49XMEOxOTiHNqCIY75W5lQkBXrcEREpJ+q/AElySISc30myc65G/vY74APD1lEQ2RKkTcNXE2zkmQRkQRS6Q8wc2xerMMQkVEuaRu+NA2ciEjiCYUc1Y0BxuarkiwisZW0SfLEwix8KcY+zXAhIpIw6lraae9wlOYqSRaR2EraJDnNl0JZYZbmShYRSSCROZJVSRaRWEvaJBnCLRf7tTS1iEjCqPS3AqiSLCIxl9RJ8uSibFWSRUQSyIlKspakFpHYSuokeWpxDvUt7dQ3t8c6FBER6YdKLUktInEiqZPkyd4MFxq8JyKSGKr8AbLTfeRmDMVaVyIipy+pk+TINHBquRARSQyVWkhEROJEUifJk70FRTR4T0QkMVT5WxmrJFlE4kBSJ8nZ6amU5mWwt1rtFiIiiUCVZBGJF0mdJEN4eep9qiSLiCSEKn+AsXma2UJEYi/pk+TJxdlamlpEJAG0tnfgbw2qkiwicSHpk+QpRTkcbWiltb0j1qGIiEgvKhs0/ZuIxI/kT5K9GS4OqOVCRCSuVTV6q+0pSRaROJD0SfJkTQMnIpIQIpVkzW4hIvEg6ZPkKUWRBUWUJIuIxLOqxkiSrIF7IhJ7SZ8kF+Wkk5uRyv4aTQMnIhLPKhsCpFj4ui0iEmtJnySbGVOKNQ2ciEi8q/IHKMnNwJdisQ5FRCT5k2QID97TNHAiIvGt0t+qQXsiEjdGRZI8uSiHA8eb6Qi5WIciIiI9qGoMaNCeiMSNUZEkTy/Nob3Dsae6MdahiIhIDyobtCS1iMSPUZEknzOtGIDVu2tiHImIiHSnI+SoaWrTzBYiEjdGRZI8qSiLssIsnleSLCISl2qb2ugIOVWSRSRujIok2cw4d0YxqytqCKkvWUQk7lT5tZCIiMSXUZEkA5w7s5i65na2HW2IdSgiItJFpV9LUotIfBk1SfLK6SVA733JT+6oZNsRJdEiIiPtRCVZPckiEh9GTZI8viCT6SU5PfYl+1vb+cDv1vPJ+17BObVkiIiMpEovSVYlWUTixahJkgFWzijmhYoa2jtCp+z768YjtLaH2HK4gVcO1scgOhGR0avKHyAvI5WsdF+sQxERAUZZknzujBKa2jrYdOjUJPi+9QeZWpxNdrqP363ZF4PoRERGryp/gNJ8VZFFJH6MqiT5nOlFwKl9yRVVjazfd5wbV0xm1ZIyHnrlMPXN7bEIUURkVKryByjNVZIsIvFjVCXJxbkZzB2fx/O7q0/afv/6g/hSjNcvLeMd50wmEAxx/4aDMYpSRGT0qfS3MjZfg/ZEJH6MqiQZwi0X6/YeJxDsAMKrPP1pwyEuml3K2PxMFkwsYOnkQu56YZ8G8ImIjJBKVZJFJM6MwiS5mEAwxEv76wB4dlc1RxtaefOZ5Z3HvP3sKVRUNbG6Qiv0iYgMt6ZAkOa2DsaqJ1lE4sioS5JXTC8ixeicCu6+dQcozE7jknljO4+5dvEECrLSuOuF/bEKU0Rk1Oic/k2VZBGJI6MuSc7PTGNReSGrd1dT39zOY1uPcf2SMjJST0w7lJnm481nlvPo5qOdq0CJiMjw6FxIRJVkEYkjoy5JBlg5vZiX9tfxh3X7aQuGeFNUq0XE286eTDDkuHftgRhEKCIyemhJahGJR6MyST53RjHBkOO7/9zJ3PF5LJiYf8ox00tzOW9mMb9/8QAdIQ3gE5HEYWZXmtkOM9tlZrd1s/9mM6sys5e9n/fFIs4ILUktIvFoVCbJy6eOIc1nNLd18KYzyzGzbo97+9lTOFTXwpM7Kkc4QhGR02NmPuB24CpgPnCjmc3v5tA/OOeWeD8/H9Egu6j0B0hNMQqz0mIZhojISUZlkpydnsrSSWNI9eZG7snl88dRkpvOvevUciEiCWMFsMs5V+GcawPuAVbFOKZeVfkDlOZlkJLSfcFCRCQWRmWSDPAfV8zma69fRHEvo6nTfCm8fmkZj2+rpKYxMILRiYictjIg+pP9QW9bV280s41mdr+ZTeruiczsFjNbZ2brqqqqhiNWwJsjWf3IIhJnRm2SfM70Yt5yVrd/F07y5uWTCIYcD7x8eASiEhEZEQ8BU51zi4F/AL/u7iDn3B3OueXOueWlpaXDFkyVP8BYJckiEmdGbZLcX7PH5XFGeQH3rTugFfhEJBEcAqIrAOXetk7OuRrnXOTrsZ8DZ45QbN2q8reqkiwicUdJcj+8afkkth/1s/lQQ6xDERHpy1pglplNM7N04AbgwegDzGxC1N3rgG0jGN9JnHPUNbdTmJ0eqxBERLqlJLkfrls8kfTUFO5brwF8IhLfnHNB4FbgUcLJ773OuS1m9mUzu8477N/NbIuZvQL8O3BzbKKFQDBEMOTIy0yNVQgiIt3SVakfCrLTeO2C8fzl5cN85up5ZKb5+n6QiEiMOOceAR7psu3zUbc/DXx6pOPqTkNrOwB5mZr+TUTiiyrJ/fTmM8upb2nnn9uOxToUEZGk4W8NApCXoZqNiMQXJcn9dN7MEiYWZHLfuoOxDkVEJGk0RpJktVuISJxRktxPvhTjjWeW8/TOKo7Ut3Ru31PdxC2/Wcdbf7pas1+IiAxQZyVZ7RYiEmf6lSSb2ZVmtsPMdpnZbd3sn2xmT5jZS97k9FcPfaix96Yzy3EO/rThEPXN7Xzl4a1c8f+e4h/bjvHCnlpePdYY6xBFRBJKYyDck5yrdgsRiTN9Jslm5gNuB64C5gM3mtn8Lod9lvAI6qWEpxv60VAHGg+mFOewYloRv3puLxf/3xP88rk9vOnMcv7y4fMAeGbn8K1IJSKSjBrUbiEicao/leQVwC7nXIVzrg24B1jV5RgH5Hu3C4CkXZ7ubSsmU90YYO74fB7+yPn87xsWs7i8kBmlOTyzszrW4YmIJBS/kmQRiVP9uSqVAdETBB8Ezu5yzBeBx8zsI0AOcFl3T2RmtwC3AEyePHmgscaFVUsmsmRSIVOKszGzzu0XzCrlnrX7aW3v0BRxIiL9FBm4p3YLEYk3QzVw70bgTudcOXA18FszO+W5nXN3OOeWO+eWl5aWDtGpR5aZMbUk56QEGeCCWSW0tofYsO94jCITEUk8/tZ2stN9pPo0jlxE4kt/rkqHgElR98u9bdHeC9wL4JxbDWQCJUMRYKI4Z3oxaT7jabVciIj0m781qCqyiMSl/iTJa4FZZjbNzNIJD8x7sMsx+4FLAcxsHuEkeVSNYsvJSGXZ5DEavCciMgCNgaD6kUUkLvWZJDvngsCtwKPANsKzWGwxsy+b2XXeYZ8A3m9mrwC/B252o3DS4AtmlbDlcAM1jYFYhyIikhAaWtvJ1RzJIhKH+vXx3Tn3CPBIl22fj7q9FThvaENLPBfMKuX/HnuVZ3dVs2pJWazDERGJe42BIPmqJItIHNJIiSG0sKyAwuw0TQUnItJP/la1W4hIfFKSPIR8KcZ5M0p4dme1lqgWEekHf2u7Bu6JSFxSkjzELphVwtGGVnZVaolqEZG+NLYGyVNPsojEISXJQ+z8WeGZ7zQVnIhI7zpCjqa2DrVbiEhcUpI8xMrHZDO9JIdnNRWciEivGgNabU9E4peS5GFwwawS1lTUEgh2xDoUEZG45W9tByBf7RYiEoeUJA+D82eV0tLewXotUS0i0iN/q1dJVruFiMQhJcnD4JzpRaSmGM+qL1lEpEeRdgv1JItIPFKSPAzyMtNYPnUMf99yVFPBiYj0INJuodktRCQeKUkeJm9YVk5FVZNaLkREetDZbqGBeyISh5QkD5NrFk0gO93HvesOxDoUEZG4FEmStSy1iMQjJcnDJCcjlWsXT+DhjUc6++5EROSESJKsdgsRiUdKkofRW8+aRHNbB49sPBLrUERE4k5joB1fipGZpj9FIhJ/dGUaRssmj2F6aQ5/GGTLxcMbD/PIJiXaIpJc/K1B8jJTMbNYhyIicgolycPIzHjr8kms33ecXZWNA368c47vP76TW+9+iS89tGUYIhQRiR1/a1CD9kQkbilJHmavX1aGL8W4b/3AqsmhkOPLD2/lO/94lYkFmRxrCFDpbx2mKEVERl64kqx+ZBGJT0qSh9nYvEwumTuWP64/RHtHqF+Pae8I8Yn7XuFXz+3lPedN49tvWQLAlsMNwxjp0NhyuJ7tR+M/ThGJPX9ruxYSEZG4pSR5BLxl+SSqGwM8uaOqz2Nb2zv4wG/X8+eXDvHJK2bzuWvnsbAsH4DNB+uHO9RB++R9G/nyQ1tjHYaIJAB/a5A8tVuISJxSkjwCLp5TSkluBn9Y23fLxZ3P7+Xx7ZX8z/ULufWSWZgZeZlpTCvJYfPh+E6SW9s7ePWYn7rm9liHIiIJoDEQVCVZROKWkuQRkOZL4Y1nlvHEjso++4rX7zvO9NIc3nHOlJO2L5iYz+ZD8d3GsPVIAx0hhz+gJFlE+uZvbSdXSbKIxCklySPkzWdOoiPkePDlw70et/lQPYvLCk7ZvqisgEN1LRxvahuuEAdtk9cOElkgQESkJ845r5KsgXsiEp+UJI+QmWNzmVKczYt7ans8ptLfypH6VhZ2kyRHtsVzy8WmQ+HYGluDOOdiHI2IxLNAMER7h1O7hYjELSXJI2jZ5DFs2F/XYwK52UsyF5cXnrJvwURv8F4ct1xE4g+GHK3t/ZvJQ0RGp4bWcFuWBu6JSLxSkjyClk0ZQ3VjgIPHW7rdv/FgPWYnEuJohdnpTCrK6rGS3BQI8ol7X+FAbfOQxtxfre0d7KxspCgnHQj3GoqI9KTRa8tSu4WIxCslySNo2eRCADbsP97t/k0H65lZmktOD5WVhRMLOqu1XT265Sh/3HCQf247NiSxDlRk0N7K6cUA+APqSxaRnvk7k2RVkkUkPilJHkFzxuWRne5j/b4ekuRD9SwqP7UfOWJhWQH7apqpbzm1SvvIpiMAVFQ1DU2wAxRJ3lfO8JJkDd4TiRkzu9LMdpjZLjO7rZfj3mhmzsyWj2R8EJ7+DdCy1CISt5Qkj6BUXwpnlBd2W0k+1tBKpT/Q7cwWEZHBe1u7rLzX0NrO069WA7C7qnEII+6/jQfrKc5JZ/a4PEDtFkPtLT9Zzfcf3xnrMCQBmJkPuB24CpgP3Ghm87s5Lg/4KPDCyEYYFrlGqN1CROKVkuQRtmxKIduO+GluO7nSutGbPq23SvKJwXsnt1w8vu0YbR0hppfmxLSSvLCsoPOr00ZVkodMa3sHa/fV8szOvlds7Oq+dQf44O/Wa7aR0WUFsMs5V+GcawPuAVZ1c9xXgG8AvU/ePkwa1G4hInFOSfIIWzZ5DB0h15kUR2w6WEeKwfwJPSfJJbkZTCjIPGXw3l83HmFCQSavX1LG0YbWzq8xR0pk0N7i8oLOr07VbjF09lQ34RxsP+ofcLL7j63H+Nvmo+yO0YcniYkyIHp5z4Petk5mtgyY5Jz760gGFq1RSbKIxDklySNs6eQxwKmD9zYeqmf2uDyy0n29Pn5Bl8F7kVaLqxZOYObYXAD2jHBCFBm0t7CsgHzvq9OGBGi3+P2L+3l0y9FYh9GnyLcD/tYgR+oHVvTbVxOe7eQfW2MzoFPij5mlAN8BPtGPY28xs3Vmtq6qauDfZPQm8kFaPckiEq+UJI+wopx0ppfksCFq8J5zjs2H6lnUSz9yxKKyAiqqmzqrxZFWi2sWj2eGlySPdF9yJGlfVFbQucTsSFezB6q9I8T/PLyV/7p/Y9wn9BVRv8/tR/s/T3Yo5NhXG06wYzXricTEIWBS1P1yb1tEHrAQeNLM9gLnAA92N3jPOXeHc265c255aWnpkAbpb20nK81Hqk9/hkQkPunqFANLuywqcqS+lerGNhb30o8csbAsH+dg25FwsvTXjUcZn5/J0kljmFKcTYqdnFSNhE3eoL0JBZn4UozsdF/ct1tsOlRPU1sH9S3t/PyZPbEOp1e7qxopzA5X6Lcf9ff7ccf8rbS2h5hQkMmG/cep8geGK0SJL2uBWWY2zczSgRuAByM7nXP1zrkS59xU59xUYA1wnXNu3UgGGV6SWlVkEYlfSpJjYNmUQmqb2jq/Co/0J3e3HHVXnctTH6rH39rO0zuruGrReFJSjIxUH5OKstldPbLtFpu8QXtmBoR7DON9dovVu2sAWDm9mF8+u4faprYYR9SziuomFpUVMLEgkx0DSJL3Vof/fb37vKk4B//armryaOCcCwK3Ao8C24B7nXNbzOzLZnZdbKM7wd+qJFlE4puS5BhY1qUvedOhOlJTjHkTTl1pr6tx+ZmU5mWw+VADj2+rpC0Y4ppFEzr3Ty/JYXflyFWSI4P2oltF8jLT4r7dYk1FDXPG5fHlVQtoagvy06d2xzqkbjnnqKhqYnpJDnPG57H9SP+T5H014Q9LVy2cQFlhFv/YWjlcYUqccc494pyb7Zyb4Zz7qrft8865B7s59uKRriJDeMGhXE3/JiJxTElyDMwel0duRmpnkrzxYHjQXmZa74P2IhZOzGfzoXr+uukI4/MzO5NugBmlueypbiIUOv0pv3ZV+jn/G//i5l+9yN83H6G9I9TjsdGD9iJyM1Ljut2iLRhi7d5aVs4oZta4PF6/pIxfr95LZUNMZsLqVZU/QGMgyPTSXOZOyGd3VSNtwZ5/H9H21jST5jMmFmZx+fxxPLuripa2jmGOWKR//K3t5KuSLCJxTElyDPhSjCWTClm/r65z0F5/+pEjFpYVsLPSz1OvVnHlwnCrRcT00lwCwRCH6lpOK7ZKfyvv+uVamts62HakgQ/8bgMr//dx/vdv29jTTRtH56C98uhKcmrnHKgjwTnHkfoWnt1ZzZ3P7eFzD2zmP+97hdb27hPClw/U0doe4hxvCe2PXjaLYIfj9id2DWucbcEQP3py14BaUSJTt80ozWXu+DyCIUdFdf++KdhX08Skomx8KcZl88bR2h46rbmWRYaDvzWomS1EJK7pChUjyyYX8sMndrHjmJ/jze29LiLS1cKyAkIunHRds3jCSfuml+YA4T7WSUXZA4qpKRDkPXeupbapjT/82znMn5DPU69Wcc/aA/z8mT387OkKvvb6RdywYnLnYzYdrKcoJ52JBZmd2/Iz0047SY/20CuHeWJHJcunFLFiWhEzSnM6+56r/AGe2FHJkzsqeWZn9UmV6+x0H81tHZw/q4RVS8pOed7Vu2swg3OmFwEwpTiHNy+fxN0v7uf9F06nfMzA3rf++tf2Y3zz7ztIS0nh/RdO79djIjOVTC/NoSQvHYAdR/3MHd93a87emmamFof/PZw9vYi8zFT+ue0YVywYf5qvQGToNKonWUTinK5QMbJ0yhhCDn63Zh9Av6Z/i4i0NozLz+DMqFYLCFccAXZXNnLR7P5P2RTsCHHr3RvYeriBn79rOYvLCwG4dN44Lp03jmMNrfzX/Ru57U+baGrr4L3nTwNOHbQH4XaLoVhx73bvQ8SfNoRnryrJTefMKWM4Ut/aOdhxXH4G1yyawMKyAmaU5jJjbA4lORlc+K0nuH/9we6T5Ipq5k/IpzA7vXPbRy6ZyR/XH+QHj+/iG29aPOjYu/PcrvBgwYc2Hu53klxR1URWmo/x+ZmU5GaQ5jO2HfGzaknvj3POsa+mqfODQJovhdfMGcvj2yrpCDl8Ud8+iMSCv7VdS1KLSFxTkhwjyyaFk9s/bThEms+YMz6v34+dWJDJlOJsrlk04aRWCwgnknmZqf3+Sh7CCdXn/rKFJ3ZU8dXXL+SSueNOOWZcfiZ33HQmH/39y3zl4a00B4K8/8Lp7Kxs5LJ5Jx8fnt1icElyfXM7O475+fhls3ndGRN5cU8NL+ypZf2+4xTnpPPJK2bzmrljmT8h/6QEPeKNy8r5/r92criuhYmFWZ3bW9s72LC/jpvOmXLS8RMLs3j7OZP5zep9fODiGUwryRlU/N15bnc1vhRj48F69lY3MbUf56iobmRaSQ4pKUZ6ijGjNJcd/ZgruaoxQHNbR2clGeCy+eN48JXDvHzgOGdOKRrUaxEZjI6Qo6mtQ+0WIhLX1JMcIwXZacwozaG5rYO54/PJSO3foD0AM+Oxj1/IJ66Y0+2+GaW5nau09cdPn67g9y/u50MXz+DtZ0/p8biMVB8/fNtS3rC0jG//41VuvXvDKYP2IDy7RUt7R68D/vqybl8tzsFZU4uYVpLDW8+azHfesoSn/vM1/OlD53HrJbNYMLGg2wQZwkmyc/Dnlw6dtH3DvuO0BUOsnFF8ymM+dPFMnHM80OUxQ+FofSsVVU28a+VUAB7eeLhfj6uoaupsoQGYMz6vX9PARaYXnFJ8onXk4jmlpPmMx7T6nsRYZPYbtVuISDxTkhxDkVkpBtKPHJGR6uvxK/PppTn9XnUv2BHi9id2ccncsXyym6S7q1RfCv/35jN4xzmT+ee28JRiXeOP/OFrGsQ0cC/uqSXNZyydXHhaj59cnM2KaUX8cf3BzkVbAFZX1OBLMVZMO7WSWpqXwcyxuWw8WHeaUffsuV3VALzxzDJWTC3ioVeO9PmY1vYODhxv7myhAZg7Pp/D9a3UN/c++C8yyDK6Ip6fmcY504uHdInqUMgRHMSHIRmdIoNX89VuISJxTElyDJ05JZwkLx5AP3J/zCjN5VhDoF9zFW853IC/Ncj1S8tOad3oSUqK8ZVVC/n3S2Zy6dyxJw3aAzqXph5My8WLe2tZXF7Y72nxuvOmM8upqG5iw/66zm2rd9ewsKygx17IxeWFbDpUf1JiPRSe211NUU4688bn87ozJrDjmL/PivC+mmac46RK8lyvLWfHsb4e20RqilEW1WoCcNm8cVRUNQ3Z0uWfvP8V3vzT1QmRKH/k9y/xxQe3xDoM4UQlOVeVZBGJY0qSY+iSeWO5cHYpr5k7dkifd0Zkhot+JELPR608NxBmxn9cMYdf3HzWKS0PkblPG05z1b2Wtg42Hazvtto7EFcvmkBWmo/71x8EoLktyMsH6np9rYvLC6hubONI/dDNmeyc4/ldNaycXkxKinHVogmkWHj2jt5Efn/RleRI73pffcl7a5opH5NFqu/k/8Uvmx/uH//nEFWTd1c28tL+On7+bHwv7e1vbedvm47wl5cPDWoOcRkakQ/QarcQkXimJDmGxuZl8pv3rGBcfmbfBw9AJKnqT1/y87urmTMuj9K8jCE7f6RKe7ozXLy0/zjBkGPF1MElybkZqVy1cDwPbzxMa3sHa/eGn7e7fuSIyCwjkdkzhkJFdRNHG1o5d2b4vCW5GZw3s4SHNh7utWJd0U3LxISCTPIzU9nWZxW6iSnFpw4MLCvMYp43td9QqG8JfxD6f/94tdt5tOPFmopagiHHcW9AqMRWpN1CA/dEJJ4pSU5Ck4uzSTH6/Eo9EOzoXHluKEX+8J1uu8WLe2sxg2VTxvR9cB/edGY5/tYgj209xurdNaSmGGdN7fl5503IJzXF2HSobtDnjoj0I583o6Rz2+sWT2RfTTObDvWcjO+ubGRCQSY5UYmEmTF3fH6vrRrOOfZVNzO1uPv5nldMHcMrB+qGpEWirqWdy+ePIz01hU//aeOQt6kMlWd2VpHmC3/jsdr79kRi50QlWT3JIhK/lCQnoYxUH5OKsvusJL+0P7zy3HkzS3o9bqAiX6H6A6fXbvHinlrmjc+nIGvwf0DPmV5MWWEW968/yOqKGpZMKiQ7vefqVWaaj9nj8oa0kvzcrmrKCrNOmmnitQvGk+azXlsudlefPLNFxJzxebx61N9jQlrb1IY/EOy2kgzhDx9NbR2DrqiGQo6GlnZmj8vlM1fPY01FLfesPTCo5xwuz+ys5vyZJUwpzmZ1hZLkWIskyVqWWkTimZLkJDWjNLfPSvLzu2tIMQbd+9vVYNot2jtCvLS/bshiSkkx3rCsjGd3VrHpYF2/quZnTCoYssF7HSHH6t01nDez+KTe7YLsNC6aXcrDG4902yPrnKOiqpHpJbmn7Js7IQ9/INjjqoZ7venfppZ0X0mODBjdsO/4gF9PtMa2ICEHBVlp3HDWJM6ZXsTXHtnGsYah6+ceCgdqm9lT3cQFs0pZOb2YFypq6FBfckxFkmQN3BOReKYkOUlNL8lhT3VTr4OUVu+uZlFZwZBUbKPldQ7c6zlJPtxDgrf5UD0t7R1Dmri/cVk5IQchR7+S5EVlhdQ1t3OgdvBLa285XE9Da7Dbav3rzpjIkfpW1u8/NVmtbmzD3xrstpIcmeFi+5HuK8H7asLfIPRUSS4rzGJcfgbrB5kkR6ahK8xKx8z4+hsW0xYM8bkHNsdV28UzO8PtLhfOLmHljGIaWoNsPdz3giwyfBoD7fhSjKxBzF4jIjLc+pUkm9mVZrbDzHaZ2W09HPMWM9tqZlvM7O6hDVMGasbYXALBUI/VxqZAkJf213HuELdaAGSkppDmsx57krcfbeDcr/+LP204eMq+F/fUAuFFRIbK1JIczpo6hvTUlM65qXuz2Jv3eeMQ9CVHlqLuLjm/bN44MtNSum25iHwLML301Ery7HG9TwO3t6aZFINJY7qvJJsZZ04Z021yPhCRQXv53oesqSU5fPzy2Ty29RiPbomfBUue2VnFhIJMZpTmds5ssrqiOsZRjW7+1iC5Gak9LgYkIhIP+kySzcwH3A5cBcwHbjSz+V2OmQV8GjjPObcA+NjQhyoDMd2bEaGnlou1e8Oj/c8d4kF7EE7C8jLTaOyhJ3lvdbgd4Ot/237KgiNr99YyrSRnSGfbAPjyqoV8/4Yl/Zp3efa4PNJ9KWwagr7k53dXM3tcLmPzTp3BJCcjlUvnjeOvG48QCHactC/STz6jm0pyXmYa5WOy2N7D4L291U2UjckiPbXn/72XTR7DgdoWKgfRGtHgJcnR30S87/xpTCnO5ndr9p328w6lYEeI53ZVc8GsEsyMsfmZTC/N0eC9GGtsDWr6NxGJe/2pJK8AdjnnKpxzbcA9wKoux7wfuN05dxzAOVc5tGHKQM0Y2/s0cM/vriHdl8LyKUPbjxyRm5HaYyW5qjEAQKU/wE+f2t25PRRyrN17fNBTv3Vn3oR8rlw4oV/HpqemMG/C4AfvRWYPOXdGz9X6t62YTE1TGz97uuKk7RVVjWSmpTCxIKvbx80dn8f2I923DOyraWJqD60WEZGZQzYMoppc302SnOpL4drFE1hdUUNtU9uAnu+eF/fz6T9tpNI/dD3NGw+F210umFXaue3cGcW8uKd2UMumy+A0tAY1s4WIxL3+JMllQPSQ9YPetmizgdlm9pyZrTGzK7t7IjO7xczWmdm6qqqhmadVuleck05+ZmqPleTnd1ezdHIhWenD0xOYl9lzklztD2AG1yyawE+fruhsCXm10k99SztnDfFAwtOxuLyQzYfqB7XwxIZ9fc8ect7MEq5aOJ4f/GsXB2qbO7dXVIcT3Z5WQZwzPo+K6qZTKtAQbreY0sP0bxELJuaTnpoyqL7kziQ5++Rk5+pFE+gIOR7dcrTfz/X87mo+8+dN/P7FA1z27af4w9r9Q9LX/Myr1Zhx0u9g5fQSmto6ep1+T4aXv7WdPM2RLCJxbqgG7qUCs4CLgRuBn5lZYdeDnHN3OOeWO+eWl5aWdt0tQ8jMmDE2t9tKcl1zG1sON/Ra4RysvMzUHme3qGoMMCY7nc9cMw+Ab/59OwBrvX7ks+MgSV5UXoA/EGRPzekvkPH87mp8KcbZ03t/PZ9/3XxSU4wvPLilMzHcXdV40kp7Xc0dn09HyLG78uT46prbqG9p77OSnJHqY3FZwdAkyV0Gfs6fkM/U4mwe2XSkX89zrKGVf//9S0wvzeWhW89n7vh8PvXHTdxwx5p+rRrZm2d2VrGorICinPTObed4vw+1XMROY0DtFiIS//qTJB8CJkXdL/e2RTsIPOica3fO7QFeJZw0SwxNL8mlovrUJGNNRQ3OwXkzh74fOSI3I63HZamr/QFKczMoK8zilgun85eXD7Nh/3Fe2FPL+PxMysd032IwkiKD9wbTl/zsrmoWlxeQ38fXyhMKsvj45bP51/ZKHtt6jECwgwO1zd32I0fMmxAevNd1zt/I9G89zWwR7cwpY9h8qKHbanR/1LWEZyjI6fJthJlx9aIJPL+775aL9o4QH7n7JZoCHfz47ctYVF7APbecw9ffsIhtRxq48nvP8LkHNrPmNKZta2ht56UDdVww6+QPg8W5GcwZl8cazZccM371JItIAuhPkrwWmGVm08wsHbgBeLDLMQ8QriJjZiWE2y8qkJiaXprDsYbAKauzPb+7hux0H4vLC4ft3Pm9tFtUNQYoyQtX9j5w0QzG5mXw5Ye2snZvLWdNK4qLEe8zS3PJTEs57b7kUMix6WB9v2fpeNe5U5k7Po8vPbiF7Uf8hFz3M1tEzCjN5ZzpRfzgXzs5HpWIRqZ/62m1vWjLpoyhrSPE5kOnNx1afUs7hVlp3f6++tty8X+P7uDFvbV8/Y2LmOXN2pGSYtywYjL//MRFXLtoAveuO8ANd6zh7K89zmcf2MTzu6r71Yqxenc4sY7uR45YOaOYtXtraQuqLzkWGgNBzZEsInGvzyTZORcEbgUeBbYB9zrntpjZl83sOu+wR4EaM9sKPAH8p3NOZZoYu2rheEpy03n9j57jLy+fKP4/t6uaFdOKep39YLDyMlNpDPTQk9wYriRDeIaH/3ztHF4+UMexhsCQL2xyulJ9KSyYWHDay1P7W4MEQ46x/ZylI82Xwv9cv5DD9a186o8bAbqdIznCzPjSdQvxtwb55qM7OrfvrW7GDCYV9SNJnjy4RUXqW9p7nGN7wcR8pvTRcvHolqP89OkK3nHOZFYt6TrMAcbmZfKdty5hw+cu54dvW8rZ04r44/pDvO3nL/DhuzfQ2t57BfyZnVVkp/u6nfZv5YxiWttDvHygrvcXKUPOORfuSdbAPRGJc/3KkpxzjzjnZjvnZjjnvupt+7xz7kHvtnPO/Ydzbr5zbpFz7p7hDFr6Z3ppLn/99wtYMDGfj97zMp99YBMHapvZXdU0LFO/RQtPARc8peLnnKPKH6Ak90Ty+MZl5SwsywcYlpktTteisgI2H2o4rdXZjjeHq7tjstP7OPKE5VOLeOvySZ1Tu00r6b1lYs74PG4+dyr3rN3PxoN1QLiSPLEgq19T3ZXmZTClOLvbvuQdR/285Sere51poqGlvXOO5K7MjGt6abnYV9PEJ+99hcXlBXzu2vndPMMJORmpXLt4Ire/fRkbPnc5n7pyLn/bfJQbf7aGam+mlO48s7OaldOLu/0weM60YszUlxwLgWCI9g5HrgbuiUic04p7SW5cfiZ3v/8c/u3C6fxuzX5W3f4cwLAO2oPwcrMdIUdz28nVvsZAkNb20EnzIKekGN960xl84KIZzBrbc4vBSDtjUgEt7R3sqhz44LHOJDlnYNWyT101l8LsNMbmZfSr0vaxy2ZRkpvB5/6yhVDIsbemqc+ZLaItmxxeVCT6w0xHyPFff9zIi3tr2dLLynS9VZLhRMvFY11aLlrbO/jA7zaQkmLc/rZlZKT2f4aVrHQfH7x4Bj9++zK2HWng9T96rtvfz76aJvbVNJ/SjxxRkJ3G/An5WlQkBiJtWPlqtxCROKckeRRI86Xw6avnccc7z6S9I0RxTjrzJ+QP6zkjg3K69iVXN4aTx66LhcybkM9tV83tccqzWFhUVgjQWaUdiLrIks0DqCQDFOWk8+O3n8mXrlvQr+PzMtP4zNVzeeVAHfeuO+BN/9b3oL2IZVPGUOUPcPD4iZUZf7dmH694bQg1jT0PvOsrSY60XPy1S8vF5/+ymW1HGvh/bz2jX20h3bly4QTuuWUlLW0dvPHHz/PUq1VsP9rAkzsquefF/fzfY68CcMHsnmfRWTm9mA376/ps25Ch5fcG9KrdQkTinT7KjyJXLBjPPycV4m8NDnsyGvkDGF5178Rqc1X+8Nfj0e0W8Wp6SQ456T42Harnzcsn9f2AKJEWg4G0W0R0t4R1b65fUsbdL+znf/+23Zv+rf+J55lev+76fceZVJTN0fpWvvXoDs6aOoa1e49T00s7Q19JcmSWizuerqC2qY2inHTuXXuAe9cd5NbXzOSSueP6/yK7sWRSIX/+0Hm8+861vOuXL56yf8W0os6VJ7tz7sxifv7sHjbsOz4sy7NL9yIfnNVuISLxTlepUWZcfibjhreIDNC5UEDDKZXkcNI11MtOD4eUFGNhWcFpzXARabcoOo0keaAig/iu/cEzQP+mf4uYMz6PnHQf6/cd5/qlZXzhwc0EQyG+/eYlXP7/nqKmhyncQiFHQx9JMoQXjPnxk7t5bMtRFpUX8Lm/bOa8mcV8/PLZ/X+BvZhUlM0fP3guj2w6Ql5mKuPzMxlfkMnYvMw+B6aeNbUIX4qxuqJGSfIIigzo1RRwIhLvdJWSYdFTu0UiVZIhPF/yr1fvo70jRJqv/91Jdc3tpNjIJQLzJ+Zz08qp3Pn83l7nV+7Kl2IsnTyG9fuO89iWozy65RifunIuk4uzKcnN6LHdorEtSMidupBIVwsm5jO5KJv71x/kx0/tZkx2Ot+7YSm+IfwmoyArjRtXTB7w4/Iy07j7fWczb+IIfGqUTmq3EJFEoZ5kGRad7RbdVJJTjJNWQItnZ0wqpC0Y4q41+wb0uOPNbRRmp49oj/VtV83lV+8+q3O+4f5aNmUM24828Lm/bGbu+Dzed8E0AIpz06lp6r7dor65+yWpu4q0XKzbd5xDx1u4/e1L4+oD0tnTi/tc7EWGVuSDsyrJIhLvlCTLsMjtrCSfvOpelT9AUU7GkFYSh9Pl88fxmjmlfPGhrXzz79sJ9XM6uLrmdgr7SCCHWmaaj9fMGTvgx505ZQwhB5X+AP/7hkWdFfOinPQeK8k9LUndnVVLJpJi8Jmr53HmlPiZ4k9iQ0myiCQKXaVkWPQ8u0UgIfqRIzJSffzspuV8/sEt/OjJ3Rw83sK33ry4z2nLjje3ndagvVhYMqmQzLQU3rp8EkujFt4ozsng1S6rNUYMJEmeNyGfDZ+7fMAzfUhyilwTcjRwT0TinK5SMixy070kOXBqT3JJbmIlS6m+FL56/ULKx2Txzb/v4FhDK3e8c3mvrQa1TW2Ujzm96c1GWkFWGk988mLG5mWetL0kN53qpjacc6csPT2QJBkGPhWeJK/GQDtZab4B9fiLiMSCrlIyLFJSjNyM1G7bLRKpkhxhZnzo4pl874YlvLS/jnf84oVej69rbmfMCLdbDMaEgqxTWmCKc9NpC4Zoajt1HuGBJskiEf7WoFotRCQhKEmWYZOXmXpSu4VzjurGtoRMkiNWLSnj1ktmsulQPU1dquTRjje3MSZBBif2pCgn/Hvqbq5kJclyuvytwc4xCyIi8UxJsgybvMzUk2a3aGgJ0tYRojSOZjc4HWWFWcCJOZ+7amnrIBAMjfjAvaFW7LXFVHczeK++pZ3UFCM7vf9LSosANLS2a0YREUkISpJl2ORlpuEPnGi3qEqghUR6E4k/MudzV5GFRBJl4F5PSvqoJBdkpZ3SqyzSl0RtuRKR0UdJsgybcE/yiUpyoi0k0pO+kuTBLEkdTyKV5NpuVt2rb2nvc45kke5U+gOMVZIsIglASbIMm67tFom0JHVvIkl+VQ/tFnXeQhuJNHCvO5EFX7pbmrq+ue8lqUW6aguGqG1K7HEJIjJ6KEmWYZOXmUZDElaSi3LSSbF+tFsk+MC9zDQfuRmp3fZeR9otJD6Z2ZVmtsPMdpnZbd3s/4CZbTKzl83sWTObPxJxRVZw7DrdoIhIPFKSLMMmPLvFiZ7k6sYAqSlGYYInV74Uozg3o8ckuc5LkhN94B54S1P3MHBPSXJ8MjMfcDtwFTAfuLGbJPhu59wi59wS4JvAd0YitsqGSJKc2B+URWR0UJIswyYvI5VAMERbMASEK6/FuemkJMiS1L0p7SVJPu61WxRmJXYlGaA4J73nnmQlyfFqBbDLOVfhnGsD7gFWRR/gnGuIupsD9G+99UGq9CdHy5WIjA6arFKGTWTBgMZAkKLUdKoSbEnq3pTmZfTYk1zb1EZeRirpqYn/GbQ4N4MDtc0nbQuFHA2tSpLjWBlwIOr+QeDsrgeZ2YeB/wDSgUtGIrDIB8ux+clxHRCR5Jb4f8UlbuV6c6FGWi6qGwMJP0dyRGle7+0WhTnJkUAW56SfMnDPHwjinBYSSXTOududczOATwGf7e4YM7vFzNaZ2bqqqqpBn7PS3wpAcU5yXAdEJLkpSZZhE6kkR6aBq/IHEn7QXkRpXgbVjQGcO/Vb6uPN7Qk//VtEcW643SIUOvE6G7TaXrw7BEyKul/ubevJPcD13e1wzt3hnFvunFteWlo66MCq/AGKctKT4lsWEUl+ulLJsIlOkkMhR02CL0kdrTQ3g/YO17k8c7S65jYKkyVJzsmgI3Ty69SS1HFvLTDLzKaZWTpwA/Bg9AFmNivq7jXAzpEITHMki0giUZIswyYv40S7RV1LO8GQS6pKMnQ/DVy4kpwcCWRkQZHolovIPNBKkuOTcy4I3Ao8CmwD7nXObTGzL5vZdd5ht5rZFjN7mXBf8rtGIrZKrbYnIglEA/dk2ERXkpNlIZGIzgVF/AFmjcs7ad/x5rbkabeIWpp65thcIKqSnCQfBJKRc+4R4JEu2z4fdfujIx4UUO0PMKM0JxanFhEZMFWSZdhEz26RLAuJRHRWkrvMcNHeEcLfGkyeJLmbSrLaLeR0OOeo8ge0kIiIJAwlyTJscjsrye1JV0nuqd2ic0nqZJndIpIkR30YUJIsp6OuuZ22jlDSXANEJPkpSZZhk5HqIz01BX/riUpysvyBzM8Mz4N8apIcWW0vOSrJRdndV5LTfEZWmi9WYUkCinzrooF7IpIo1JMswyo/MxV/IAgG6b4U8jOT45+cmXW76l5ktb1kGbiX6kuhMDvtpKWpI6vtmSX+yokyciJLUifLB2URSX7JkbFI3MrLTMPfGqS1vYPSvIykSqy6W3XvuFdJTpaeZIgsKHLidTZoSWo5DVWN4YVEVEkWkUShJFmGVW5GKv7WdjpCjpLc5EkcIZwkd12y+bjXljAmJ3lea3FuBtXdVJJFBiJSSR6br4F7IpIY1JMswyovM5XG1iDVSbSQSER3S1MnW7sFQElu+ikD95Qky0BV+gNkpfnISVcvu4gkBiXJMqzyMlM7B+4ly/RvEaW5GdQ2t9HeEercVtfcRnpqSlINaivKCS9NHVHX0qYkWQasyh9gbH5ytVyJSHJTkizDKjcjjbqWNmqbkm+lrdK8DJzjpAQyvJBIcg1qK87J4HhzO0Hvw0B9syrJMnCV/lb1I4tIQlGSLMMqLzOVYw0BQi55FhKJiF51LyK8JHXy9CMDnb3ktc1thEIOfyCoJFkGTEtSi0iiUZIswyp6yrdk+wPZ3ap7x5uSZ0nqiOLcyNLUbfhbgzgH+UqSZYC02p6IJBolyTKscpM4SR7bzap7x5vbkma1vYgib6aO2qY2rbYnp6W1vQN/azDprgEiktw0BZwMq7zME8nUaGi3qGtuT5rV9iIi7RbVjQHyvd9nsr1GGV7JtuKmiIwOqiTLsMpL4kpyVrqPvIzUzgTAOUddS3tSTf8G4YF7EG63UCVZTkelXwuJiEjiUZIswypSSc5MS0nK+VGjV91raA3SEXJJ15NckJWGL8WoaQooSZbToiWpRSQRKUmWYZWbEa4kJ9uS1BElUQuK1CXhktQAKSnGmOx09STLaYt8kNTAPRFJJEqSZVhFZrdItn7kiNK8DKq9JLm2c0nq5EsgS3LTqW5so64l/BqVJMtAVDYE8KVY5yBQEZFEoCRZhlWk3aI0WZPk3OhKcrjKmoyD2oq9panrW9pJ96WQmaZLh/Rfpb+V4px0fCnJ922SiCQv/aWTYRWZAq4kSXsRS/My8AeCtLR1cDxJ2y0gPHivpqmNhpZ28rOSa0VBGX6RJalFRBKJkmQZVjnpPkrzMpgzLi/WoQyLSIW8ujHAca+SXJSMSXJueufsFgVZmjlSBqZSC4mISALSXzsZVmbGM//1GtJ9yfl5LHrVvbrmNlLs5GnvkkVxTjqNgSBV/kBStpPI8KryB1g4sSDWYYiIDEhyZi4SVzLTfKQkaS9iadSqe7VNbRRmpyfla40sTV1R1aRBezIgHSFHdaPaLUQk8ShJFhmE6CQ5vNpeciaQxd6sBDVNbUqSZUBqmgKEnOZIFpHE068k2cyuNLMdZrbLzG7r5bg3mpkzs+VDF6JI/CrKSccsnCQfb25LykF7cKKSDJr+TQYmMvuLVtsTkUTTZ5JsZj7gduAqYD5wo5nN7+a4POCjwAtDHaRIvErzpVCUnU6VN3AvaZPkqPlt85UkywBU+iOr7Wngnogklv5UklcAu5xzFc65NuAeYFU3x30F+AbQOoTxicS9Um/VvbrmNsYka7tF7okkWZVkGYiqBlWSRSQx9SdJLgMORN0/6G3rZGbLgEnOub8OYWwiCSGSJNc2tTEmSVcUy81IJT01fLlQkiwDEVmSWj3JIpJoBj1wz8xSgO8An+jHsbeY2TozW1dVVTXYU4vEhZLcDA7UNhMIhpJ24J6ZUeJ9AChUkiwDUNnQSn5mKplpvliHIiIyIP1Jkg8Bk6Lul3vbIvKAhcCTZrYXOAd4sLvBe865O5xzy51zy0tLS08/apE4UpoXXo0OknO1vYgir+WiIEk/CMjwqPQHVEUWkYTUnyR5LTDLzKaZWTpwA/BgZKdzrt45V+Kcm+qcmwqsAa5zzq0blohF4kxp1MwPyZwkF+eEX6faLWQgqrTanogkqD6TZOdcELgVeBTYBtzrnNtiZl82s+uGO0CReBddJUvWgXtwYvCekmQZiEq/FhIRkcTUr/VznXOPAI902fb5Ho69ePBhiSSOk5LkJB24B+Hea1CSLP3nnKPKHzjp2xYRkUTRryRZRHoWnSQn68A9gNctnkiazzQAS/qtMRCkpb1DlWQRSUhKkkUGKbpKVpiVvJXkReUFLCoviHUYkkBOLCSiJFlEEo+SZJFBKshKC1dYU32dcwmLSPSS1Bq4JyKJR0myyCClpBgluRmk+izWoYjElUq/VtsTkcSlJFlkCJTkZmDKkUVOUtnQCqjdQkQSk5JkkSHw3vOnEXIu1mGIxJXSvAzOm1msGVFEJCEpSRYZAtcvLYt1CCKdzOxK4HuAD/i5c+7rXfb/B/A+IAhUAe9xzu0b6jhWLSlj1RL9vyEiiUmjjEREkoiZ+YDbgauA+cCNZja/y2EvAcudc4uB+4FvjmyUIiLxT0myiEhyWQHscs5VOOfagHuAVdEHOOeecM41e3fXAOUjHKOISNxTkiwiklzKgANR9w9623ryXuBvwxqRiEgCUk+yiMgoZWbvAJYDF/Ww/xbgFoDJkyePYGQiIrGnSrKISHI5BEyKul/ubTuJmV0G/DdwnXMu0N0TOefucM4td84tLy0tHZZgRUTilZJkEZHkshaYZWbTzCwduAF4MPoAM1sK/JRwglwZgxhFROKekmQRkSTinAsCtwKPAtuAe51zW8zsy2Z2nXfYt4Bc4D4ze9nMHuzh6URERi31JIuIJBnn3CPAI122fT7q9mUjHpSISIJRJVlEREREpAslySIiIiIiXShJFhERERHpQkmyiIiIiEgXSpJFRERERLow51xsTmxWBezr47ASoHoEwhkuij92Ejl2UPyx1N/YpzjnRs0KG/28ZsPo+N3HK8UfO4kcO4yO+Ad8zY5ZktwfZrbOObc81nGcLsUfO4kcOyj+WErk2ONBIr9/iRw7KP5YSuTYQfH3RO0WIiIiIiJdKEkWEREREeki3pPkO2IdwCAp/thJ5NhB8cdSIsceDxL5/Uvk2EHxx1Iixw6Kv1tx3ZMsIiIiIhIL8V5JFhEREREZcXGbJJvZlWa2w8x2mdltsY4nwsz2mtkmM3vZzNZ524rM7B9mttP77xhvu5nZ973XsNHMlkU9z7u843ea2buGMd5fmlmlmW2O2jZk8ZrZmd77sct7rI1A/F80s0Pe7+BlM7s6at+nvVh2mNlro7Z3++/JzKaZ2Qve9j+YWfoQxj7JzJ4ws61mtsXMPuptT4j3v5f4E+X9zzSzF83sFS/+L/V2TjPL8O7v8vZPPd3XNRrF63thumbrmt3/2HXNPrFd12wA51zc/QA+YDcwHUgHXgHmxzouL7a9QEmXbd8EbvNu3wZ8w7t9NfA3wIBzgBe87UVAhfffMd7tMcMU74XAMmDzcMQLvOgda95jrxqB+L8IfLKbY+d7/1YygGnevyFfb/+egHuBG7zbPwE+OISxTwCWebfzgFe9GBPi/e8l/kR5/w3I9W6nAS9471W35wQ+BPzEu30D8IfTfV2j7See3wt0zdY1u/+x65qta/ZJP/FaSV4B7HLOVTjn2oB7gFUxjqk3q4Bfe7d/DVwftf03LmwNUGhmE4DXAv9wztU6544D/wCuHI7AnHNPA7XDEa+3L985t8aF/2X+Juq5hjP+nqwC7nHOBZxze4BdhP8tdfvvyfsEfwlwv/f46PdiKGI/4pzb4N32A9uAMhLk/e8l/p7E2/vvnHON3t0078f1cs7o38v9wKVejAN6XUMVf4JJtPdC12xds7uLXddsXbNPEq9JchlwIOr+QXr/RY8kBzxmZuvN7BZv2zjn3BHv9lFgnHe7p9cR69c3VPGWebe7bh8Jt3pfb/0y8tUXA4+/GKhzzgW7bB9y3tdASwl/Mk64979L/JAg77+Z+czsZaCS8B+q3b2cszNOb3+9F2O8/n8cT+L5vdA1W9fsAdM1W9dsiN8kOZ6d75xbBlwFfNjMLoze6X06TJgpQxItXs+PgRnAEuAI8O2YRtMHM8sF/gh8zDnXEL0vEd7/buJPmPffOdfhnFsClBOuIsyNbUQSA7pmx17CXDNA1+xYirdrdrwmyYeASVH3y71tMeecO+T9txL4M+Ff4jHvaxS8/1Z6h/f0OmL9+oYq3kPe7a7bh5Vz7pj3P1II+Bnh3wF9xNnd9hrCX4+ldtk+ZMwsjfDF6i7n3J+8zQnz/ncXfyK9/xHOuTrgCWBlL+fsjNPbX+DFGK//H8eTuH0vdM0+Zbuu2b3QNVvX7K6BxN0PkEq40X0aJ5qrF8RBXDlAXtTt5wn3pX2Lk5v6v+ndvoaTm/pf9LYXAXsIN/SP8W4XDWPcUzl5EMWQxcupgxCuHoH4J0Td/jjh3iOABZzcrF9BuFG/x39PwH2cPCDgQ0MYtxHuOftul+0J8f73En+ivP+lQKF3Owt4Bri2p3MCH+bkQSD3nu7rGm0/8fpeoGu2rtkDi1vXbF2zT45pqP/nGMI362rCIzN3A/8d63i8mKZ7b+orwJZIXIR7YB4HdgL/jPqfwYDbvdewCVge9VzvIdxMvgt49zDG/HvCX6+0E+6/ee9QxgssBzZ7j/kh3gI1wxz/b734NgIPdrkA/LcXyw6iRg339O/J+52+6L2u+4CMIYz9fMJfy20EXvZ+rk6U97+X+BPl/V8MvOTFuRn4fG/nBDK9+7u8/dNP93WNxp94fC/QNVvX7IHFrmt2bN//uLtma8U9EREREZEu4rUnWUREREQkZpQki4iIiIh0oSRZRERERKQLJckiIiIiIl0oSRYRERER6UJJssQFM2v0/jvVzN42xM/9mS73nx/K5xcRGW10zZbRQEmyxJupwIAuuFEr8fTkpAuuc+7cAcYkIiLdm4qu2ZKklCRLvPk6cIGZvWxmHzczn5l9y8zWmtlGM/s3ADO72MyeMbMHga3etgfMbL2ZbTGzW7xtXweyvOe7y9sWqYCY99ybzWyTmb016rmfNLP7zWy7md1lZhaD90JEJN7pmi1Jq69PcyIj7Tbgk865awG8C2e9c+4sM8sAnjOzx7xjlwELnXN7vPvvcc7VmlkWsNbM/uicu83MbnXOLenmXG8AlgBnACXeY5729i0lvLTlYeA54Dzg2aF+sSIiCU7XbElaqiRLvLsCuMnMXgZeILw86Cxv34tRF1uAfzezV4A1wKSo43pyPvB751yHc+4Y8BRwVtRzH3TOhQgv7Tl1CF6LiEiy0zVbkoYqyRLvDPiIc+7RkzaaXQw0dbl/GbDSOddsZk8SXtf9dAWibneg/1dERPpD12xJGqokS7zxA3lR9x8FPmhmaQBmNtvMcrp5XAFw3LvYzgXOidrXHnl8F88Ab/V66EqBC4EXh+RViIiMDrpmS9LSJy2JNxuBDu8ruDuB7xH+2myDNxCjCri+m8f9HfiAmW0DdhD++i7iDmCjmW1wzr09avufgZXAK4AD/ss5d9S7YIuISN90zZakZc65WMcgIiIiIhJX1G4hIiIiItKFkmQRERERkS6UJIuIiIiIdKEkWURERESkCyXJIiIiIiJdKEkWEREREelCSbKIiIiISBdKkkVEREREuvj/chiVBsTZn+8AAAAASUVORK5CYII=", "text/plain": ["<Figure size 864x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Iteration Average Loss\")\n", "x = [eval_num * (i + 1) for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val Mean Dice\")\n", "x = [eval_num * (i + 1) for i in range(len(metric_values))]\n", "y = metric_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check best model output with the input image and label"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["case_num = 4\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "with torch.no_grad():\n", "    img_name = os.path.split(val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n", "    img = val_ds[case_num][\"image\"]\n", "    label = val_ds[case_num][\"label\"]\n", "    val_inputs = torch.unsqueeze(img, 1).cuda()\n", "    val_labels = torch.unsqueeze(label, 1).cuda()\n", "    val_outputs = sliding_window_inference(val_inputs, (96, 96, 96), 4, model, overlap=0.8)\n", "    plt.figure(\"check\", (18, 6))\n", "    plt.subplot(1, 3, 1)\n", "    plt.title(\"image\")\n", "    plt.imshow(val_inputs.cpu().numpy()[0, 0, :, :, slice_map[img_name]], cmap=\"gray\")\n", "    plt.subplot(1, 3, 2)\n", "    plt.title(\"label\")\n", "    plt.imshow(val_labels.cpu().numpy()[0, 0, :, :, slice_map[img_name]])\n", "    plt.subplot(1, 3, 3)\n", "    plt.title(\"output\")\n", "    plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, slice_map[img_name]])\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "monai-0", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13 (default, Mar 28 2022, 06:59:08) [MSC v.1916 64 bit (AMD64)]"}, "vscode": {"interpreter": {"hash": "85754776c861f3ba5898fde994bfc400ed208cd401a6036e85fdc16c4f506eaf"}}}, "nbformat": 4, "nbformat_minor": 4}