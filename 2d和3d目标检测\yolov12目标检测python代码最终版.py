#%%2d目标检测YOLOv12代码最终版. 2个类别
# pip install ultralytics
import os
import glob
import numpy as np
import pandas as pd
from PIL import Image
from tqdm import tqdm
from ultralytics import YOLO
import torch
import cv2
from pathlib import Path
import matplotlib.pyplot as plt
import shutil
import random

# 数据集路径，YOLO格式标签
DATA_IMG_DIR = '/root/autodl-tmp/yolov12-main/datasets/hcc/images/'
DATA_LABEL_DIR = '/root/autodl-tmp/yolov12-main/datasets/hcc/labels/'  # 这里存放的是YOLO格式的txt文件，只能用labels文件夹名

# 类别映射
label_dict = {'hcc': 0}  # YOLO类别从0开始
class_names = {0: 'hcc'}

# 添加以下函数，用于动态读取和设置类别信息
def set_class_names(classes_list=None, label_file=None):
    """
    动态设置类别名称
    
    参数:
        classes_list (list): 类别名称列表，例如 ['zhong', 'cai', 'third_class']
        label_file (str): 类别名称文件路径，文件中每行一个类别名称
        
    返回:
        tuple: (label_dict, class_names) 类别映射字典
    """
    global label_dict, class_names
    
    if classes_list:
        # 使用提供的类别列表
        names = classes_list
    elif label_file and os.path.exists(label_file):
        # 从文件读取类别列表
        with open(label_file, 'r') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
    else:
        # 默认使用现有类别
        return label_dict, class_names
    
    # 重建类别映射
    label_dict = {name: i for i, name in enumerate(names)}
    class_names = {i: name for i, name in enumerate(names)}
    
    print(f"已设置 {len(names)} 个类别: {', '.join(names)}")
    return label_dict, class_names

# 生成颜色函数
def get_color_for_class(class_idx, total_classes):
    """
    为类别生成颜色，支持任意数量的类别
    
    参数:
        class_idx (int): 类别索引
        total_classes (int): 类别总数
        
    返回:
        tuple: BGR颜色值 (B, G, R)
    """
    # 为每个类别生成不同的HSV颜色，然后转换为BGR
    import colorsys
    
    # 使用HSV颜色空间均匀分布颜色
    h = class_idx / total_classes
    s = 0.8  # 饱和度
    v = 0.9  # 亮度
    
    # 转换为RGB，然后反转为BGR (OpenCV使用BGR)
    r, g, b = colorsys.hsv_to_rgb(h, s, v)
    return (int(b * 255), int(g * 255), int(r * 255))

# 添加数据集分割函数
def split_dataset(img_dir=None, label_dir=None, train_ratio=0.9, random_seed=42, copy_mode=True):
    """
    将数据集分割为训练集和验证集，并组织到train和val子文件夹中
    
    参数:
        img_dir (str): 图像目录路径，默认使用全局DATA_IMG_DIR
        label_dir (str): 标签目录路径，默认使用全局DATA_LABEL_DIR
        train_ratio (float): 训练集比例，默认0.9
        random_seed (int): 随机种子，确保可重复性
        copy_mode (bool): True表示复制文件到目标目录，False表示移动文件
    
    返回:
        tuple: (train_count, val_count) 训练集和验证集的样本数
    """
    # 使用提供的路径或默认路径
    img_dir =  DATA_IMG_DIR
    label_dir =  DATA_LABEL_DIR
    
    # 创建train和val目录
    train_img_dir = os.path.join(img_dir, 'train')
    val_img_dir = os.path.join(img_dir, 'val')
    train_label_dir = os.path.join(label_dir, 'train')
    val_label_dir = os.path.join(label_dir, 'val')
    
    # 创建目录（如果不存在）
    for dir_path in [train_img_dir, val_img_dir, train_label_dir, val_label_dir]:
        os.makedirs(dir_path, exist_ok=True)
        print(f"创建目录: {dir_path}")
    
    # 获取所有图像和标签
    all_images = glob.glob(os.path.join(img_dir, '*.jpg')) + glob.glob(os.path.join(img_dir, '*.png'))
    all_labels = glob.glob(os.path.join(label_dir, '*.txt'))
    
    print(f"找到 {len(all_images)} 张图像和 {len(all_labels)} 个标签文件")
    
    # 匹配图像和标签
    valid_pairs = []
    for img_path in all_images:
        img_name = os.path.basename(img_path)
        base_name = os.path.splitext(img_name)[0]
        label_path = os.path.join(label_dir, f"{base_name}.txt")
        if os.path.exists(label_path):
            valid_pairs.append((img_path, label_path))
    
    print(f"找到 {len(valid_pairs)} 对有效的图像-标签对")
    
    # 随机划分训练集和验证集
    random.seed(random_seed)
    random.shuffle(valid_pairs)
    train_count = int(len(valid_pairs) * train_ratio)
    train_pairs = valid_pairs[:train_count]
    val_pairs = valid_pairs[train_count:]
    
    print(f"训练集: {len(train_pairs)} 对, 验证集: {len(val_pairs)} 对")
    
    # 文件操作函数
    file_op = shutil.copy if copy_mode else shutil.move
    op_name = "复制" if copy_mode else "移动"
    
    # 处理训练文件
    print(f"正在{op_name}训练文件...")
    for img_path, label_path in tqdm(train_pairs):
        img_name = os.path.basename(img_path)
        label_name = os.path.basename(label_path)
        
        # 复制/移动图像到训练图像目录
        file_op(img_path, os.path.join(train_img_dir, img_name))
        
        # 复制/移动标签到训练标签目录
        file_op(label_path, os.path.join(train_label_dir, label_name))
    
    # 处理验证文件
    print(f"正在{op_name}验证文件...")
    for img_path, label_path in tqdm(val_pairs):
        img_name = os.path.basename(img_path)
        label_name = os.path.basename(label_path)
        
        # 复制/移动图像到验证图像目录
        file_op(img_path, os.path.join(val_img_dir, img_name))
        
        # 复制/移动标签到验证标签目录
        file_op(label_path, os.path.join(val_label_dir, label_name))
    
    print("数据集分割完成!")
    print(f"训练集: {len(train_pairs)} 对图像/标签")
    print(f"验证集: {len(val_pairs)} 对图像/标签")
    
    return len(train_pairs), len(val_pairs)

#%% 3. 训练YOLOv12模型
def train_yolov12(
    model_type='yolov12n.pt', 
    epochs=200, 
    imgsz=352, 
    batch=8, 
    project='yolov12-main',
    name='result',
    patience=50,
    lr0=0.01,
    lrf=0.01,
    momentum=0.937,
    weight_decay=0.0005,
    warmup_epochs=3.0,
    warmup_momentum=0.8,
    augment=True,
    device='cuda:0',
    classes_list=None,
    label_file=None,
    use_existing_data=True  # 默认使用现有数据结构
):
    """
    训练YOLOv12模型
    
    参数:
        model_type (str): 预训练模型路径或名称，例如'yolov12n.pt', 'yolov12s.pt'等
        epochs (int): 训练轮数
        imgsz (int): 图像大小
        batch (int): 批次大小
        project (str): 项目名称
        name (str): 实验名称
        patience (int): 早停轮数
        lr0 (float): 初始学习率
        lrf (float): 最终学习率因子
        momentum (float): SGD动量
        weight_decay (float): 权重衰减
        warmup_epochs (float): 预热轮数
        warmup_momentum (float): 预热动量
        augment (bool): 是否使用数据增强
        device (str): 训练设备，为空表示自动选择
        classes_list (list): 可选，类别名称列表
        label_file (str): 可选，类别名称文件路径
        use_existing_data (bool): 是否使用现有数据结构，不创建新的train/val目录
    """
    # 如果提供了类别信息，设置类别
    if classes_list or label_file:
        set_class_names(classes_list, label_file)
        
    # 验证数据集路径
    for path in [DATA_IMG_DIR, DATA_LABEL_DIR]:
        if not os.path.exists(path):
            raise FileNotFoundError(f"路径不存在: {path}")
    
    # 检查是否已经存在train和val子目录
    train_img_dir = os.path.join(DATA_IMG_DIR, 'train')
    val_img_dir = os.path.join(DATA_IMG_DIR, 'val')
    has_split_dirs = os.path.exists(train_img_dir) and os.path.exists(val_img_dir)
    
    # 如果已经有train/val子目录，则直接使用
    if has_split_dirs:
        print("检测到数据已经分割到train和val目录，将直接使用")
        train_images = glob.glob(os.path.join(train_img_dir, '*.jpg')) + glob.glob(os.path.join(train_img_dir, '*.png'))
        val_images = glob.glob(os.path.join(val_img_dir, '*.jpg')) + glob.glob(os.path.join(val_img_dir, '*.png'))
        
        if not train_images:
            raise ValueError(f"训练图像目录中没有找到图片: {train_img_dir}")
        if not val_images:
            raise ValueError(f"验证图像目录中没有找到图片: {val_img_dir}")
            
        print(f"训练集: {len(train_images)} 张图像")
        print(f"验证集: {len(val_images)} 张图像")
        
        # 数据集目录
        dataset_dir = os.path.abspath('datasets/hcc')
        
        # 创建YOLOv12数据集配置文件 - 使用子目录结构
        data_yaml = f'''
# YOLOv12 数据集配置
path: {os.path.dirname(os.path.dirname(DATA_IMG_DIR))}
train: images/train
val: images/val
test:  # 测试图像路径（可选）

# 类别
nc: {len(class_names)}
names: {list(class_names.values())}
'''
        yaml_path = os.path.join(dataset_dir, 'data.yaml')
        with open(yaml_path, 'w') as f:
            f.write(data_yaml)
        
        print(f"数据配置已保存到 {yaml_path}")
    else:
        # 获取所有图像和标签文件
        all_images = glob.glob(os.path.join(DATA_IMG_DIR, '*.jpg')) + glob.glob(os.path.join(DATA_IMG_DIR, '*.png'))
        all_labels = glob.glob(os.path.join(DATA_LABEL_DIR, '*.txt'))
        
        if not all_images:
            raise ValueError(f"图像目录中没有找到图片: {DATA_IMG_DIR}")
        if not all_labels:
            raise ValueError(f"标签目录中没有找到YOLO标签文件: {DATA_LABEL_DIR}")
            
        print(f"找到 {len(all_images)} 张图像和 {len(all_labels)} 个YOLO标签文件")
        
        # 匹配图像和标签
        valid_pairs = []
        for img_path in all_images:
            img_name = os.path.basename(img_path)
            base_name = os.path.splitext(img_name)[0]
            label_path = os.path.join(DATA_LABEL_DIR, f"{base_name}.txt")
            if os.path.exists(label_path):
                valid_pairs.append((img_path, label_path))
        
        print(f"找到 {len(valid_pairs)} 对有效的图像-标签对")
        
        if not valid_pairs:
            raise ValueError("没有找到匹配的图像-标签对")
        
        # 按9:1比例划分训练集和验证集
        import random
        random.seed(42)  # 设置随机种子以确保可重复性
        random.shuffle(valid_pairs)
        
        train_count = int(len(valid_pairs) * 0.9)
        train_pairs = valid_pairs[:train_count]
        val_pairs = valid_pairs[train_count:]
        
        print(f"训练集: {len(train_pairs)} 对, 验证集: {len(val_pairs)} 对")
        
        # 数据集目录
        dataset_dir = os.path.abspath('datasets/hcc')
        
        if use_existing_data:
            # 创建文本文件列出训练和验证集中的图像文件名
            os.makedirs(dataset_dir, exist_ok=True)
            
            train_txt = os.path.join(dataset_dir, 'train.txt')
            val_txt = os.path.join(dataset_dir, 'val.txt')
            
            # 写入训练集文件名
            with open(train_txt, 'w') as f:
                for img_path, _ in train_pairs:
                    f.write(os.path.abspath(img_path) + '\n')
            
            # 写入验证集文件名
            with open(val_txt, 'w') as f:
                for img_path, _ in val_pairs:
                    f.write(os.path.abspath(img_path) + '\n')
            
            print(f"训练集列表已保存到 {train_txt}")
            print(f"验证集列表已保存到 {val_txt}")
            
            # YOLOv12数据集配置文件 - 使用现有数据结构
            data_yaml = f'''
# YOLOv12 数据集配置
path: {os.path.dirname(DATA_IMG_DIR)}
train: {train_txt}  # 训练图像列表
val: {val_txt}  # 验证图像列表
test:  # 测试图像路径（可选）

# 类别
nc: {len(class_names)}
names: {list(class_names.values())}
'''
        else:
            # 为YOLOv12创建标准数据集结构
            train_img_dir = os.path.join(dataset_dir, 'images/train')
            val_img_dir = os.path.join(dataset_dir, 'images/val')
            train_label_dir = os.path.join(dataset_dir, 'labels/train')
            val_label_dir = os.path.join(dataset_dir, 'labels/val')
            
            # 创建目录结构
            os.makedirs(train_img_dir, exist_ok=True)
            os.makedirs(val_img_dir, exist_ok=True)
            os.makedirs(train_label_dir, exist_ok=True)
            os.makedirs(val_label_dir, exist_ok=True)
            
            # 清空目标目录
            for dir_path in [train_img_dir, val_img_dir, train_label_dir, val_label_dir]:
                for file in glob.glob(os.path.join(dir_path, '*')):
                    if os.path.isfile(file):
                        os.remove(file)
            
            print("正在移动训练集文件...")
            for img_path, label_path in tqdm(train_pairs, desc="处理训练集"):
                img_name = os.path.basename(img_path)
                label_name = os.path.basename(label_path)
                
                # 移动图像到训练图像目录
                dst_img_path = os.path.join(train_img_dir, img_name)
                shutil.move(img_path, dst_img_path)
                
                # 移动YOLO标签到训练标签目录
                dst_label_path = os.path.join(train_label_dir, label_name)
                shutil.move(label_path, dst_label_path)
            
            print("正在移动验证集文件...")
            for img_path, label_path in tqdm(val_pairs, desc="处理验证集"):
                img_name = os.path.basename(img_path)
                label_name = os.path.basename(label_path)
                
                # 移动图像到验证图像目录
                dst_img_path = os.path.join(val_img_dir, img_name)
                shutil.move(img_path, dst_img_path)
                
                # 移动YOLO标签到验证标签目录
                dst_label_path = os.path.join(val_label_dir, label_name)
                shutil.move(label_path, dst_label_path)
            
            print("数据集划分完成并已移动文件")
            
            # 检查移动后的文件数量
            train_images = glob.glob(os.path.join(train_img_dir, '*'))
            val_images = glob.glob(os.path.join(val_img_dir, '*'))
            train_labels = glob.glob(os.path.join(train_label_dir, '*'))
            val_labels = glob.glob(os.path.join(val_label_dir, '*'))
            
            print(f"训练集: {len(train_images)} 张图像, {len(train_labels)} 个标签")
            print(f"验证集: {len(val_images)} 张图像, {len(val_labels)} 个标签")
            
            # YOLOv12数据集配置文件 - 使用标准YOLO格式
            data_yaml = f'''
# YOLOv12 数据集配置
path: {dataset_dir}
train: images/train  # 相对于path的训练图像路径
val: images/val  # 相对于path的验证图像路径
test:  # 相对于path的测试图像路径（可选）

# 类别
nc: {len(class_names)}
names: {list(class_names.values())}
'''
        
    yaml_path = os.path.join(dataset_dir, 'data.yaml')
    with open(yaml_path, 'w') as f:
        f.write(data_yaml)
    
    print(f"数据配置已保存到 {yaml_path}")
    
    # 训练
    model = YOLO(model_type)
    
    # 确保使用包含完整路径的yaml文件
    train_args = {
        'data': yaml_path,
        'epochs': epochs,
        'imgsz': imgsz,
        'batch': batch,
        'project': project,
        'name': name,
        'patience': patience,
        'lr0': lr0,
        'lrf': lrf,
        'momentum': momentum,
        'weight_decay': weight_decay,
        'warmup_epochs': warmup_epochs,
        'warmup_momentum': warmup_momentum,
        'augment': augment,
        'verbose': True,  # 输出详细信息以便调试
        'exist_ok': True  # 覆盖现有实验目录
    }
    
    # 添加设备参数
    if device:
        train_args['device'] = device
    
    # 开始训练
    print(f"开始训练YOLOv12模型，使用{model_type}，共{epochs}轮...")
    try:
        results = model.train(**train_args)
        print(f"训练完成！模型已保存到 {os.path.join(project, name)}")
        return model, results
    except Exception as e:
        print(f"训练过程中出现错误: {str(e)}")
        if "CUDA" in str(e):
            print("CUDA错误提示: 可能是由于内存不足或标签格式问题。尝试减小batch大小或检查标签。")
        raise

#%% 4. 推理与结果保存
def yolo_predict_and_save(
    model_path, 
    img_paths, 
    output_excel='prediction_results.xlsx', 
    conf=0.5,
    save_images=False,
    output_dir='prediction_results',
    iou=0.45,
    max_det=300,
    device=''
):
    """
    使用YOLOv12模型进行预测并保存结果
    
    参数:
        model_path (str): 模型路径
        img_paths (list): 图像路径列表
        output_excel (str): 结果保存的Excel文件路径
        conf (float): 置信度阈值
        save_images (bool): 是否保存带预测框的图像
        output_dir (str): 保存结果图像的目录
        iou (float): 非极大值抑制的IoU阈值
        max_det (int): 每张图最大检测目标数
        device (str): 推理设备，为空表示自动选择
    
    返回:
        pandas.DataFrame: 预测结果数据框
    """
    if not isinstance(img_paths, list):
        if os.path.isdir(img_paths):
            img_paths = glob.glob(os.path.join(img_paths, '*.jpg')) + glob.glob(os.path.join(img_paths, '*.png'))
        elif os.path.isfile(img_paths):
            img_paths = [img_paths]
        else:
            raise ValueError(f"无效的输入路径: {img_paths}")
    
    model = YOLO(model_path)
    results_list = []
    class_counts = {cls_name: 0 for cls_name in class_names.values()}
    
    # 创建输出目录（如果需要保存图像）
    if save_images:
        os.makedirs(output_dir, exist_ok=True)
    
    # 配置预测参数
    predict_args = {
        'conf': conf,
        'iou': iou,
        'max_det': max_det,
    }
    
    if device:
        predict_args['device'] = device
    
    # 计算类别总数，用于颜色生成
    total_classes = len(class_names)
    
    for img_path in tqdm(img_paths, desc=f'使用YOLOv12预测中'):
        # 预测
        results = model(img_path, **predict_args)
        
        # 保存带检测框的图像
        if save_images:
            img_filename = os.path.basename(img_path)
            output_img_path = os.path.join(output_dir, f"pred_{img_filename}")
            
            # 读取原始图像并绘制检测结果
            img = cv2.imread(img_path)
            for r in results:
                boxes = r.boxes.xyxy.cpu().numpy() if hasattr(r.boxes, 'xyxy') else []
                scores = r.boxes.conf.cpu().numpy() if hasattr(r.boxes, 'conf') else []
                labels = r.boxes.cls.cpu().numpy() if hasattr(r.boxes, 'cls') else []
                
                for box, score, label_idx in zip(boxes, scores, labels):
                    x1, y1, x2, y2 = box.astype(int)
                    label_idx_int = int(label_idx)
                    label = class_names.get(label_idx_int, 'Unknown')
                    
                    # 使用动态颜色生成函数
                    color = get_color_for_class(label_idx_int, total_classes)
                    
                    # 画框
                    cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
                    
                    # 添加标签和置信度
                    text = f"{label}: {score:.2f}"
                    cv2.putText(img, text, (x1, y1 - 10), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            cv2.imwrite(output_img_path, img)
        
        # 收集结果数据
        for r in results:
            boxes = r.boxes.xyxy.cpu().numpy() if hasattr(r.boxes, 'xyxy') else []
            scores = r.boxes.conf.cpu().numpy() if hasattr(r.boxes, 'conf') else []
            labels = r.boxes.cls.cpu().numpy() if hasattr(r.boxes, 'cls') else []
            
            for box, score, label_idx in zip(boxes, scores, labels):
                label = class_names.get(int(label_idx), 'Unknown')
                class_counts[label] += 1
                
                # 计算框的宽高
                width = box[2] - box[0]
                height = box[3] - box[1]
                
                results_list.append({
                    'image': os.path.basename(img_path),
                    'box_x1': box[0],
                    'box_y1': box[1],
                    'box_x2': box[2],
                    'box_y2': box[3],
                    'width': width,
                    'height': height,
                    'area': width * height,
                    'label': label,
                    'score': score
                })
    
    # 创建结果数据框
    df = pd.DataFrame(results_list)
    
    # 统计类别信息
    print("\n检测结果统计:")
    for cls_name, count in class_counts.items():
        print(f"- {cls_name}: {count}个")
    
    # 如果有结果，计算额外统计
    if not df.empty:
        # 按类别计算平均置信度
        avg_conf_by_class = df.groupby('label')['score'].mean()
        print("\n平均置信度:")
        for cls_name, avg_conf in avg_conf_by_class.items():
            print(f"- {cls_name}: {avg_conf:.4f}")
        
        # 按图像统计检测数量
        detections_per_image = df.groupby('image').size()
        avg_detections = detections_per_image.mean()
        print(f"\n平均每张图像检测到 {avg_detections:.2f} 个目标")
    
    # 保存Excel结果
    try:
        print(f"尝试保存Excel文件到 {output_excel}")
        
        # 确保即使没有检测到目标也能创建Excel
        if df.empty:
            print("警告: 未检测到任何目标! 将创建空的Excel文件。")
            # 创建一个空的DataFrame但带有所有预期的列
            df = pd.DataFrame(columns=[
                'image', 'box_x1', 'box_y1', 'box_x2', 'box_y2', 
                'width', 'height', 'area', 'label', 'score'
            ])
        
        # 保存到Excel
        df.to_excel(output_excel, index=False)
        print(f"预测结果已成功保存到 {output_excel}")
        
        # 额外保存一份CSV备份，以防Excel保存失败
        csv_path = output_excel.replace('.xlsx', '.csv')
        df.to_csv(csv_path, index=False)
        print(f"备份结果已保存到 {csv_path}")
    except Exception as e:
        print(f"保存Excel时出错: {str(e)}")
        # 尝试保存为CSV
        try:
            csv_path = output_excel.replace('.xlsx', '.csv')
            df.to_csv(csv_path, index=False)
            print(f"由于Excel保存失败，结果已保存到CSV: {csv_path}")
        except:
            print("警告: 无法保存结果到任何格式!")
    
    # 如果结果为空，返回空DataFrame
    if df.empty:
        print("警告: 未检测到任何目标!")
    
    return df

# 添加批量预测函数
def batch_predict(
    model_path, 
    input_dir, 
    output_dir,
    conf=0.5, 
    save_images=True,
    device='',
    classes_list=None,
    label_file=None,
    excel_filename='prediction_results.xlsx'  # 默认使用"prediction_results.xlsx"作为Excel文件名
):
    """
    批量处理目录下的所有图像并保存预测结果
    
    参数:
        model_path (str): 模型路径
        input_dir (str): 输入图像目录
        output_dir (str): 输出结果目录
        conf (float): 置信度阈值
        save_images (bool): 是否保存带预测框的图像
        device (str): 推理设备，为空表示自动选择
        classes_list (list): 可选，类别名称列表
        label_file (str): 可选，类别名称文件路径
        excel_filename (str): Excel文件名，默认为prediction_results.xlsx
    """
    # 如果提供了类别信息，设置类别
    if classes_list or label_file:
        set_class_names(classes_list, label_file)
    
    # 确保输入目录存在
    if not os.path.exists(input_dir):
        raise ValueError(f"输入目录不存在: {input_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有图像文件
    img_paths = glob.glob(os.path.join(input_dir, '*.jpg')) + \
                glob.glob(os.path.join(input_dir, '*.png')) + \
                glob.glob(os.path.join(input_dir, '*.jpeg'))
    
    if not img_paths:
        print(f"警告: 在 {input_dir} 中未找到图像文件")
        return
    
    # 使用完整的绝对路径
    output_excel = os.path.abspath(os.path.join(output_dir, excel_filename))
    print(f"Excel结果将保存到: {output_excel}")
    
    # 调用预测函数
    print(f"开始批量预测 {len(img_paths)} 张图像...")
    results_df = yolo_predict_and_save(
        model_path=model_path, 
        img_paths=img_paths, 
        output_excel=output_excel,
        conf=conf,
        save_images=save_images,
        output_dir=output_dir,
        device=device
    )
    
    # 绘制类别分布饼图
    if not results_df.empty:
        plt.figure(figsize=(10, 6))
        class_dist = results_df['label'].value_counts()
        
        # 为多个类别生成颜色
        colors = [get_color_for_class(label_dict.get(label, 0), len(class_names)) for label in class_dist.index]
        # 将BGR转换为RGB用于matplotlib
        colors = [(r/255, g/255, b/255) for (b, g, r) in colors]
        
        plt.pie(class_dist, labels=class_dist.index, autopct='%1.1f%%', 
                startangle=90, colors=colors)
        plt.axis('equal')
        plt.title('检测类别分布')
        plt.savefig(os.path.join(output_dir, 'class_distribution.png'))
        print(f"类别分布图已保存到 {os.path.join(output_dir, 'class_distribution.png')}")
    
    # 确认文件是否已成功创建
    if os.path.exists(output_excel):
        print(f"批量预测完成! Excel结果已成功保存到 {output_excel}")
    else:
        print(f"警告: Excel文件 {output_excel} 可能未成功创建")
        # 尝试检查CSV文件
        csv_path = output_excel.replace('.xlsx', '.csv')
        if os.path.exists(csv_path):
            print(f"但备份CSV文件已保存到 {csv_path}")
    
    return results_df

# 添加从预测结果中提取目标区域的函数
def crop_and_save_detections(
    model_path, 
    img_paths, 
    output_dir='cropped_objects',
    conf=0.5,
    add_margin=0.0,  # 可以添加一定比例的边距
    min_size=10,     # 最小裁剪尺寸
    iou=0.45,
    max_det=10,
    device='',
    classes_list=None,
    label_file=None
):
    """
    使用YOLOv12模型进行预测，并将检测到的目标区域裁剪保存为单独的图像    
    参数:
        model_path (str): 模型路径
        img_paths (list或str): 图像路径列表或目录
        output_dir (str): 保存裁剪图像的目录
        conf (float): 置信度阈值
        add_margin (float): 边框扩展比例，0.1表示在各方向扩展10%
        min_size (int): 最小裁剪尺寸，小于此尺寸的检测框不裁剪
        iou (float): 非极大值抑制的IoU阈值
        max_det (int): 每张图最大检测目标数
        device (str): 推理设备，为空表示自动选择 
        classes_list (list): 可选，类别名称列表
        label_file (str): 可选，类别名称文件路径   
    返回:
        int: 裁剪并保存的图像数量
    """
    # 如果提供了类别信息，设置类别
    if classes_list or label_file:
        set_class_names(classes_list, label_file)
        
    if not isinstance(img_paths, list):
        if os.path.isdir(img_paths):
            img_paths = glob.glob(os.path.join(img_paths, '*.jpg')) + \
                        glob.glob(os.path.join(img_paths, '*.png')) + \
                        glob.glob(os.path.join(img_paths, '*.jpeg'))
        elif os.path.isfile(img_paths):
            img_paths = [img_paths]
        else:
            raise ValueError(f"无效的输入路径: {img_paths}")
    
    # 加载模型
    model = YOLO(model_path)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 为每个类别创建子目录
    for cls_name in class_names.values():
        os.makedirs(os.path.join(output_dir, cls_name), exist_ok=True)
    
    # 配置预测参数
    predict_args = {
        'conf': conf,
        'iou': iou,
        'max_det': max_det,
    }
    
    if device:
        predict_args['device'] = device
    
    crop_count = 0
    for img_path in tqdm(img_paths, desc=f'裁剪检测目标'):
        # 获取图像文件名（不含路径和扩展名）
        img_name = Path(img_path).stem
        
        # 运行预测
        results = model(img_path, **predict_args)
        
        # 读取原始图像
        orig_img = cv2.imread(img_path)
        if orig_img is None:
            print(f"警告: 无法读取图像 {img_path}")
            continue
        
        img_height, img_width = orig_img.shape[:2]
        
        # 处理每个检测结果
        for r in results:
            boxes = r.boxes.xyxy.cpu().numpy() if hasattr(r.boxes, 'xyxy') else []
            scores = r.boxes.conf.cpu().numpy() if hasattr(r.boxes, 'conf') else []
            labels = r.boxes.cls.cpu().numpy() if hasattr(r.boxes, 'cls') else []
            
            for i, (box, score, label_idx) in enumerate(zip(boxes, scores, labels)):
                x1, y1, x2, y2 = box.astype(int)
                label = class_names.get(int(label_idx), 'Unknown')
                
                # 计算框宽高
                width = x2 - x1
                height = y2 - y1
                
                # 检查最小尺寸
                if width < min_size or height < min_size:
                    continue
                
                # 添加边距（如果指定）
                if add_margin > 0:
                    margin_x = int(width * add_margin)
                    margin_y = int(height * add_margin)
                    
                    # 确保坐标在图像范围内
                    x1 = max(0, x1 - margin_x)
                    y1 = max(0, y1 - margin_y)
                    x2 = min(img_width, x2 + margin_x)
                    y2 = min(img_height, y2 + margin_y)
                
                # 裁剪图像
                cropped_img = orig_img[y1:y2, x1:x2]
                
                # 生成保存路径
                save_filename = f"{img_name}_{label}_{i+1}_{int(score*100)}.jpg"
                save_path = os.path.join(output_dir, label, save_filename)
                
                # 保存裁剪的图像
                cv2.imwrite(save_path, cropped_img)
                crop_count += 1
    
    print(f"共裁剪并保存了 {crop_count} 个目标区域图像到 {output_dir}")
    return crop_count

#%% 分割数据集示例
# 分割数据集到训练集和验证集子文件夹
train_samples, val_samples = split_dataset(   
    copy_mode=True,  # 设置为True复制文件，False移动文件
    train_ratio=0.9   # 训练集比例
)
print(f"数据集已分割完成! 训练集: {train_samples}个样本, 验证集: {val_samples}个样本")


#%% 训练模型
# 示例: 使用3个类别训练模型
model, results = train_yolov12(
    model_type='yolov12n.pt', 
    epochs=50, 
    # classes_list=['zhong', 'cai']  # 可以指定任意数量的类别
    classes_list=['hcc'] 
)

#%%批量预测整个图片目录
batch_results = batch_predict(
    model_path='/root/autodl-tmp/yolov12-main/result/weights/best.pt',  
    input_dir='/root/autodl-tmp/yolov12-main/datasets/hcc/images/val',
    output_dir='/root/autodl-tmp/yolov12-main/result/batch_predictions',
    conf=0.45,
    classes_list=['hcc']  # 指定与训练模型相同的类别
)

#%%裁剪并保存2d检测到的目标区域
cropped_count = crop_and_save_detections(
    model_path='/root/autodl-tmp/yolov12-main/result/weights/best.pt',
    img_paths='/root/autodl-tmp/yolov12-main/datasets/hcc/images/val',  
    output_dir='/root/autodl-tmp/yolov12-main/result/cropped_objects',
    conf=0.45,
    add_margin=0.05,  # 添加5%的边距
    classes_list=['hcc']  # 指定与训练模型相同的类别     
)
