{"cells": [{"cell_type": "markdown", "id": "b88f3ebf199ead84", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# MONA<PERSON> @ Learn2Reg Challenge\n", "## Learn2Reg 2021 Task 3: MR Whole Brain\n", "\n", "This tutorial shows how to use MONAI for the registration of unpaired MR volumes of the same modality. It is a fully MONAI-based solution for the Task 3: MR whole brain of the [Learn2Reg challenge 2021](https://learn2reg.grand-challenge.org/Learn2Reg2021/). Given two volumes sampled randomly from the dataset with one being the fixed (target) volume, and the other being the moving (source) volume, the task is to register the moving volume to the fixed volume. This task is considered an unpaired registration (or inter subject registration). This type of registration is useful for downstream tasks including voxel-based morphometry (VBM), atlas construction, and atlas-based segmentation, etc.\n", "\n", "Each subject in the OASIS dataset has\n", "1. Original 3D volume (not affinely aligned) `orig.nii.gz`, its bias-corrected and skull-stripped version `norm.nii.gz`, and its corresponding 4-label `seg4.nii.gz` and 35-label `seg35.nii.gz` segmentation maps,\n", "2. Original 3D volume affinely aligned to <PERSON><PERSON><PERSON><PERSON>'s talairach space `aligned_orig.nii.gz`, its bias-corrected and skull-stripped version `aligned_norm.nii.gz`, and its corresponding 4-label `aligned_seg4.nii.gz` and 35-label `aligned_seg35.nii.gz` segmentation maps, and\n", "3. Original 2D slice (affinely aligned) `slice_orig.nii.gz`, its bias-corrected and skull-stripped version `slice_norm.nii.gz`, and its corresponding 4-label `slice_seg4.nii.gz` and 35-label `slice_seg35.nii.gz` segmentation maps.\n", "\n", "For the sake of this notebook, only `aligned_norm.nii.gz` and `aligned_seg4.nii.gz` are used for training, and `aligned_seg35.nii.gz` is used only for validation and visualization. However, readers should feel free to explore using any or all of the available images. For example, the script provided in this notebook can be easily extended to use `aligned_seg35.nii.gz` for training. \n", "\n", "The challenge evaluate the goodness of registration in terms of three metrics, namely, DSC (dice similarity coefficient), HD95 (95% percentile of Hausdorff distance of segmentations), and SDlogJ (standard deviation of log Jacobian determinant of the deformation field). DSC and HD95 captures the how well the model aligns different anatomical structures while SDlogJ measures the smoothness of the predicted deformation between the fixed and moving images. For the sake of this notebook, only DSC is evaluated. \n", "\n", "This tutorial demonstrates several MONAI principles and has the following features:\n", "1. Load OASIS challenge data (images and segmentation maps)\n", "1. Define transforms for reading NIfTI files\n", "1. Fast data loading and batching with `CacheDataset` and `DataLoader` \n", "1. Build `VoxelMorph` and custom forward function for prediction of dense displacement fields (DDF)\n", "1. Warp an image with predicted DDFs and `Warp` block\n", "1. Build a custom loss function using a combination of loss functions from MONAI and PyTorch\n", "1. Compute Dice similarity coefficient between warped and fixed segmentation maps.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_registration/learn2reg_oasis_brain_mr.ipynb)"]}, {"cell_type": "markdown", "id": "f1277555d2663d94", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "id": "7ad15603140b695b", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tensorboard]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "6f63505b012d5c2a", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "id": "e672bec37d6bd329", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.4.dev2345\n", "Numpy version: 1.24.4\n", "Pytorch version: 2.1.0\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 08162c4c41783cb0218bda0da4db71a64daab87f\n", "MONAI __file__: /home/<USER>/miniforge3/envs/tutorial/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: NOT INSTALLED or UNKNOWN VERSION.\n", "ITK version: 5.3.0\n", "Nibabel version: 5.1.0\n", "scikit-image version: 0.20.0\n", "scipy version: 1.9.1\n", "Pillow version: 10.1.0\n", "Tensorboard version: 2.15.1\n", "gdown version: 4.7.1\n", "TorchVision version: 0.16.0\n", "tqdm version: 4.66.1\n", "lmdb version: NOT INSTALLED or UNKNOWN VERSION.\n", "psutil version: 5.9.5\n", "pandas version: 2.0.3\n", "einops version: 0.7.0\n", "transformers version: 4.35.0\n", "mlflow version: 2.8.0\n", "pynrrd version: 0.4.2\n", "clearml version: 1.13.2\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n"]}], "source": ["# python imports\n", "import os\n", "import glob\n", "import tempfile\n", "import time\n", "import warnings\n", "from pprint import pprint\n", "import shutil\n", "\n", "# data science imports\n", "import matplotlib.pyplot as plt\n", "\n", "# PyTorch imports\n", "import torch\n", "from torch.nn import MSELoss\n", "from torch.utils.tensorboard import SummaryWriter\n", "\n", "# MONAI imports\n", "from monai.apps import extractall\n", "from monai.data import Dataset, DataLoader\n", "from monai.losses import BendingEnergyLoss, DiceLoss\n", "from monai.metrics import DiceMetric\n", "from monai.networks.blocks import Warp\n", "from monai.networks.nets import VoxelMorph\n", "from monai.networks.utils import one_hot\n", "from monai.utils import set_determinism, first\n", "from monai.visualize.utils import blend_images\n", "from monai.config import print_config\n", "from monai.transforms import LoadImaged\n", "\n", "set_determinism(seed=0)\n", "torch.backends.cudnn.benchmark = True\n", "\n", "# The flag below controls whether to allow TF32 on matmul. This flag defaults to False\n", "# in PyTorch 1.12 and later.\n", "torch.backends.cuda.matmul.allow_tf32 = True\n", "\n", "# The flag below controls whether to allow TF32 on cuDNN. This flag defaults to True.\n", "torch.backends.cudnn.allow_tf32 = True\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "fe1a1c187862a12b", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.\n", "This allows you to save results and reuse downloads.\n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "id": "b4430eb5d510eb75", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/DATA/MONAI/data\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "2d804c6f4cedb178", "metadata": {}, "source": ["## Download dataset and prepare datalist\n", "Download and extract the dataset:"]}, {"cell_type": "code", "execution_count": 4, "id": "31b63434e4e3606c", "metadata": {}, "outputs": [], "source": ["resource = \"https://surfer.nmr.mgh.harvard.edu/ftp/data/neurite/data/neurite-oasis.v1.0.tar\"\n", "!wget https://surfer.nmr.mgh.harvard.edu/ftp/data/neurite/data/neurite-oasis.v1.0.tar --no-check-certificate\n", "\n", "compressed_file = \"./neurite-oasis.v1.0.tar\"\n", "data_dir = os.path.join(root_dir, \"OASIS\")\n", "if not os.path.exists(data_dir):\n", "    os.mkdir(data_dir)\n", "    extractall(compressed_file, data_dir)"]}, {"cell_type": "markdown", "id": "ff0e606aeb2b079e", "metadata": {}, "source": ["Get list of samples in the dataset:"]}, {"cell_type": "code", "execution_count": 5, "id": "1f22bd46cf128f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'image': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0001_MR1/aligned_norm.nii.gz',\n", " 'label_35': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0001_MR1/aligned_seg35.nii.gz',\n", " 'label_4': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0001_MR1/aligned_seg4.nii.gz'}\n", "{'fixed_image': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0438_MR1/aligned_norm.nii.gz',\n", " 'fixed_label_35': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0438_MR1/aligned_seg35.nii.gz',\n", " 'fixed_label_4': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0438_MR1/aligned_seg4.nii.gz',\n", " 'moving_image': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0439_MR1/aligned_norm.nii.gz',\n", " 'moving_label_35': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0439_MR1/aligned_seg35.nii.gz',\n", " 'moving_label_4': '/DATA/MONAI/data/OASIS/OASIS_OAS1_0439_MR1/aligned_seg4.nii.gz'}\n"]}], "source": ["def get_files(data_dir):\n", "    \"\"\"\n", "    Get train/val files from the full OASIS dataset\n", "    \"\"\"\n", "\n", "    data_txt = os.path.join(data_dir, \"subjects.txt\")\n", "\n", "    folders = []\n", "\n", "    with open(data_txt, \"r\") as file:\n", "        for line in file:\n", "            folders.append(str(line).strip())\n", "\n", "    # load images and segmentations for training set\n", "    train_files = []\n", "    for folder in folders[:394]:\n", "        train_files.append(\n", "            {\n", "                \"image\": os.path.join(data_dir, folder, \"aligned_norm.nii.gz\"),\n", "                \"label_4\": os.path.join(data_dir, folder, \"aligned_seg4.nii.gz\"),\n", "                \"label_35\": os.path.join(data_dir, folder, \"aligned_seg35.nii.gz\"),\n", "            }\n", "        )\n", "\n", "    # load images and segmentations for validation set\n", "    val_files = []\n", "    for i, folder in enumerate(folders[394:-1]):\n", "        next_folder = folders[394:][i + 1]\n", "        val_files.append(\n", "            {\n", "                \"fixed_image\": os.path.join(data_dir, folder, \"aligned_norm.nii.gz\"),\n", "                \"moving_image\": os.path.join(data_dir, next_folder, \"aligned_norm.nii.gz\"),\n", "                \"fixed_label_4\": os.path.join(data_dir, folder, \"aligned_seg4.nii.gz\"),\n", "                \"moving_label_4\": os.path.join(data_dir, next_folder, \"aligned_seg4.nii.gz\"),\n", "                \"fixed_label_35\": os.path.join(data_dir, folder, \"aligned_seg35.nii.gz\"),\n", "                \"moving_label_35\": os.path.join(data_dir, next_folder, \"aligned_seg35.nii.gz\"),\n", "            }\n", "        )\n", "\n", "    return train_files, val_files\n", "\n", "\n", "train_files, val_files = get_files(data_dir)\n", "\n", "# print 1 training samples and 1 validation samples to illustrate the contents of the datalist\n", "pprint(train_files[0])\n", "pprint(val_files[0])"]}, {"cell_type": "markdown", "id": "c39f37b5-e073-4796-beae-5ed497f0e04f", "metadata": {}, "source": ["Define transforms for training and validation sets:"]}, {"cell_type": "code", "execution_count": 6, "id": "f1465b29e4ca06f0", "metadata": {}, "outputs": [], "source": ["transform_train = LoadImaged(keys=[\"image\", \"label_4\", \"label_35\"], ensure_channel_first=True)\n", "transform_val = LoadImaged(\n", "    keys=[\"fixed_image\", \"moving_image\", \"fixed_label_4\", \"moving_label_4\", \"fixed_label_35\", \"moving_label_35\"],\n", "    ensure_channel_first=True,\n", ")"]}, {"cell_type": "markdown", "id": "439e489a7a41f93c", "metadata": {}, "source": ["## Check transforms in DataLoader\n", "\n", "Visualize a single batch with a single sample to check the transforms."]}, {"cell_type": "code", "execution_count": 7, "id": "bab5adb60266cb32", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["set_determinism(seed=0)\n", "check_ds = Dataset(data=train_files, transform=transform_train)\n", "check_loader = DataLoader(check_ds, batch_size=1, shuffle=True)\n", "check_data = first(check_loader)\n", "\n", "image = check_data[\"image\"][0]\n", "label = check_data[\"label_35\"][0]\n", "blended = blend_images(image, label)\n", "\n", "# extract coronal slice 109\n", "image = image.permute(2, 1, 3, 0)[:, :, 109, :]\n", "blended = blended.permute(2, 1, 3, 0)[:, :, 109, :]\n", "\n", "fig, axs = plt.subplots(1, 2)\n", "axs[0].imshow(image, cmap=\"gray\")\n", "axs[0].title.set_text(\"Image\")\n", "axs[1].imshow(blended)\n", "axs[1].title.set_text(\"Image with overlaid label\")\n", "\n", "plt.setp(plt.gcf().get_axes(), xticks=[], yticks=[])\n", "\n", "plt.suptitle(\"Image and label visualization\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "53d95e642e810eb3", "metadata": {}, "source": ["<a id='hyperparameters'></a>\n", "## Set hyperparameters\n", "\n", "A lot of customization and hyperparameter search can go into the exploration loss weights below. In this example, we will use the same loss weights as the [original VoxelMorph paper](https://arxiv.org/pdf/1809.05231.pdf). "]}, {"cell_type": "code", "execution_count": 8, "id": "854836ae60ce8a86", "metadata": {}, "outputs": [], "source": ["# device, optimizer, epoch and batch settings\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "batch_size = 1\n", "lr = 1e-4\n", "weight_decay = 1e-5\n", "max_epochs = 50\n", "\n", "# Use mixed precision feature of GPUs for faster training\n", "amp_enabled = True\n", "\n", "# loss weights (set to zero to disable loss term)\n", "lam_sim = 1e0  # MSE (image similarity)\n", "lam_smooth = 1e-2  # Bending loss (smoothness)\n", "lam_dice = 2e-2  # Dice (auxiliary)\n", "\n", "# whether to use coarse (4-label) or fine (35-label) labels for training\n", "use_coarse_labels = True\n", "\n", "#  Write model and tensorboard logs?\n", "do_save = True\n", "dir_save = os.path.join(os.getcwd(), \"models\", \"voxelmorph\")\n", "if do_save and not os.path.exists(dir_save):\n", "    os.makedirs(dir_save)"]}, {"cell_type": "markdown", "id": "e25fd1e9d348b5b7", "metadata": {}, "source": ["## Create custom forward pass"]}, {"cell_type": "code", "execution_count": 9, "id": "3c2333a0cd547734", "metadata": {}, "outputs": [], "source": ["def forward(fixed_image, moving_image, moving_label, model, warp_layer, num_classes):\n", "    \"\"\"\n", "    Model forward pass:\n", "        - predict DDF,\n", "        - convert moving label to one-hot format, and\n", "        - warp one-hot encoded moving label using predicted DDF\n", "    \"\"\"\n", "\n", "    # predict DDF and warp moving image using predicted DDF\n", "    pred_image, ddf_image = model(moving_image, fixed_image)\n", "\n", "    # warp moving label\n", "    # num_classes + 1 to include background as a channel\n", "    moving_label_one_hot = one_hot(moving_label, num_classes=num_classes + 1)\n", "    pred_label_one_hot = warp_layer(moving_label_one_hot, ddf_image)\n", "\n", "    return ddf_image, pred_image, pred_label_one_hot"]}, {"cell_type": "markdown", "id": "c36d44be9f8a40ea", "metadata": {}, "source": ["## Flexible multi-target loss function for various registration tasks\n", "\n", "The custom method `loss_fun` below is a multi-target loss function. Feel free to tune the loss weights below and see their effect on the registration result. As a general rule-of-thumb, there is a trade-off between similarity loss and Dice loss, which encourage matching in appearance, and smoothness loss, which encourages smoothness of DDF.\n", "\n", "Out of the box, MONAI has several losses in store that can be used to train a deep (medical) image registration model. For image similarity, one has:\n", "- Single-modal image similarity via LNCC ([LocalNormalizedCrossCorrelationLoss](https://docs.monai.io/en/stable/losses.html#localnormalizedcrosscorrelationloss)).\n", "- Multi-modal image similarity via mutual information ([GlobalMutualInformationLoss](https://docs.monai.io/en/stable/losses.html#globalmutualinformationloss))\n", "\n", "For deformation smoothness, one has:\n", "- Warp field regularization ([BendingEnergyLoss](https://docs.monai.io/en/stable/losses.html#bendingenergyloss))\n", "\n", "The `loss_fun` further demonstrates that it is natively possible to incorporate PyTorch losses (e.g. [MESLoss](https://pytorch.org/docs/stable/generated/torch.nn.MSELoss.html)).\n", "\n", "To get started, the weights in this tutorial are set to match the [original VoxelMorph paper](https://arxiv.org/pdf/1809.05231.pdf). However, further experiments with different weight combinations are encouraged (see \"[Next Steps](#nextsteps)\")."]}, {"cell_type": "code", "execution_count": 10, "id": "17c87ca32279623", "metadata": {}, "outputs": [], "source": ["def loss_fun(\n", "    fixed_image,\n", "    pred_image,\n", "    fixed_label,\n", "    pred_label_one_hot,\n", "    ddf_image,\n", "    lam_sim,\n", "    lam_smooth,\n", "    lam_dice,\n", "):\n", "    \"\"\"\n", "    Custom multi-target loss:\n", "        - Image similarity: MSELoss\n", "        - Deformation smoothness: BendingEnergyLoss\n", "        - Auxiliary loss: <PERSON><PERSON><PERSON><PERSON>\n", "    \"\"\"\n", "    # Instantiate where necessary\n", "    if lam_sim > 0:\n", "        mse_loss = MSELoss()\n", "    if lam_smooth > 0:\n", "        regularization = BendingEnergyLoss()\n", "    if lam_dice > 0:\n", "        # we exclude the first channel (i.e., background) when calculating dice\n", "        label_loss = DiceLoss(include_background=False)\n", "\n", "    num_classes = 4 if use_coarse_labels else 35\n", "\n", "    # Compute loss components\n", "    sim = mse_loss(pred_image, fixed_image) if lam_sim > 0 else 0\n", "    smooth = regularization(ddf_image) if lam_smooth > 0 else 0\n", "    dice = label_loss(pred_label_one_hot, one_hot(fixed_label, num_classes=num_classes + 1)) if lam_dice > 0 else 0\n", "\n", "    # Weighted combination:\n", "    return lam_sim * sim + lam_smooth * smooth + lam_dice * dice"]}, {"cell_type": "markdown", "id": "517f3f98bc6aa8ca", "metadata": {}, "source": ["## Define CacheDataset and DataLoader for training and validation\n", "\n", "Here we use the regular `Dataset` but one can opt to use `CacheDataset` to accelerate training and validation process.\n", "\n", "To achieve best performance, set cache_rate=1.0 to cache all the data, if memory is not enough, set lower value.\n", "Users can also set cache_num instead of cache_rate, will use the minimum value of the 2 settings.\n", "And set num_workers to enable multi-threads during caching."]}, {"cell_type": "code", "execution_count": 11, "id": "cc1aef2b8cad39e4", "metadata": {}, "outputs": [], "source": ["# Cached datasets for high performance during batch generation\n", "# train_ds = CacheDataset(data=train_files, transform=transform_train, cache_rate=1.0, num_workers=1)\n", "# val_ds = CacheDataset(data=val_files, transform=transform_val, cache_rate=1.0, num_workers=1)\n", "train_ds = Dataset(data=train_files, transform=transform_train)\n", "val_ds = Dataset(data=val_files, transform=transform_val)\n", "\n", "# By setting batch_size=2 * batch_size, we randomly sample two images for each training iteration from\n", "# the training set. During training, we manually split along the batch dimension to obtain the fixed\n", "# and moving images.\n", "train_loader = DataLoader(train_ds, batch_size=2 * batch_size, shuffle=True, num_workers=1)\n", "\n", "# We obtain one sample for each validation iteration since the validation set is already arranged\n", "# into pairs of fixed and moving images.\n", "val_loader = DataLoader(val_ds, batch_size=batch_size, shuffle=False, num_workers=1)"]}, {"cell_type": "markdown", "id": "3e3ed0513fbfdb22", "metadata": {}, "source": ["## Create model/optimizer/metrics\n", "\n", "Here we load the model `VoxelMorph` as our framework, which is intended to be used as a general framework, based on which a deformable image registration network can be built with customizable backbones. An instance of `VoxelMorph` contains:\n", "1. A `backbone` model, which takes the fixed and moving image pair concatenated along the channel dimension as input and predicts a stationary velocity field (DVF),\n", "2. A velocity field integration layer ([DVF2DDF](https://docs.monai.io/en/latest/networks.html#monai.networks.blocks.DVF2DDF)), and\n", "3. A warp layer ([Warp](https://docs.monai.io/en/latest/networks.html#monai.networks.blocks.Warp)).\n", "\n", "In this notebook, we use the default `backbone` model, which is set to use `VoxelMorphUNet` as described in the [original VoxelMorph paper](https://arxiv.org/pdf/1809.05231.pdf). However, readers are encouraged to try any other appropriate model for the `backbone`.\n", "\n", "For a `backbone` model to fit the `VoxelMorph` framework, it must be able to take input of shape `(batch, 2*num_channels, H, W[, D])` and output a DVF of shape `(batch, spatial_dims, H, W[, D])`, where `num_channels` is the number of channels of the fixed/moving volume, `spatial_dims` is the number of spatial dimensions (i.e. `spatial_dims=3` for 3D images), and `batch` is the batch size.\n", "\n", "For more information, see the documentations on [VoxelMorph](https://docs.monai.io/en/latest/networks.html#monai.networks.nets.VoxelMorph) and [VoxelMorphUNet](https://docs.monai.io/en/latest/networks.html#monai.networks.nets.VoxelMorphUNet)."]}, {"cell_type": "code", "execution_count": 12, "id": "9d6418113cd022f5", "metadata": {}, "outputs": [], "source": ["# Model\n", "model = VoxelMorph().to(device)\n", "warp_layer = Warp().to(device)\n", "\n", "# Optimizer\n", "optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)\n", "lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=max_epochs)\n", "\n", "# Metrics\n", "dice_metric_before = DiceMetric(include_background=True, reduction=\"mean\", get_not_nans=False)\n", "dice_metric_after = DiceMetric(include_background=True, reduction=\"mean\", get_not_nans=False)"]}, {"cell_type": "markdown", "id": "e401e71de60fa792", "metadata": {}, "source": ["## Execute a typical PyTorch training process\n", "\n", "In the following cell, we provide a training snippet that trains for 50 epochs using (optional) automatic mixed precision (AMP) for acceleration, which is sufficient to reproduce the result in the [original VoxelMorph paper](https://arxiv.org/pdf/1809.05231.pdf)."]}, {"cell_type": "code", "execution_count": null, "id": "35252ae21c447d5", "metadata": {}, "outputs": [], "source": ["# Automatic mixed precision (AMP) for faster training\n", "amp_enabled = True\n", "scaler = torch.cuda.amp.GradScaler()\n", "\n", "# Tensorboard\n", "if do_save:\n", "    writer = SummaryWriter(log_dir=dir_save)\n", "\n", "# Start torch training loop\n", "val_interval = 1\n", "best_eval_dice = 0\n", "log_train_loss = []\n", "log_val_dice = []\n", "pth_best_dice, pth_latest = \"\", \"\"\n", "\n", "for epoch in range(max_epochs):\n", "    # ==============================================\n", "    # Train\n", "    # ==============================================\n", "    model.train()\n", "\n", "    epoch_loss, n_steps = 0, 0\n", "    t0_train = time.time()\n", "    for batch_data in train_loader:\n", "        # for batch_data in tqdm(train_loader):\n", "        # Get data: manually slicing along the batch dimension to obtain the fixed and moving images\n", "        fixed_image = batch_data[\"image\"][0:1, ...].to(device)\n", "        moving_image = batch_data[\"image\"][1:, ...].to(device)\n", "        if use_coarse_labels:\n", "            fixed_label = batch_data[\"label_4\"][0:1, ...].to(device)\n", "            moving_label = batch_data[\"label_4\"][1:, ...].to(device)\n", "        else:\n", "            fixed_label = batch_data[\"label_35\"][0:1, ...].to(device)\n", "            moving_label = batch_data[\"label_35\"][1:, ...].to(device)\n", "        n_steps += 1\n", "\n", "        # Forward pass and loss\n", "        optimizer.zero_grad()\n", "        with torch.cuda.amp.autocast(enabled=amp_enabled):\n", "            ddf_image, pred_image, pred_label_one_hot = forward(\n", "                fixed_image, moving_image, moving_label, model, warp_layer, num_classes=4\n", "            )\n", "            loss = loss_fun(\n", "                fixed_image,\n", "                pred_image,\n", "                fixed_label,\n", "                pred_label_one_hot,\n", "                ddf_image,\n", "                lam_sim,\n", "                lam_smooth,\n", "                lam_dice,\n", "            )\n", "        # Optimise\n", "        # loss.backward()\n", "        # optimizer.step()\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "        epoch_loss += loss.item()\n", "\n", "    # Scheduler step\n", "    lr_scheduler.step()\n", "    # Loss\n", "    epoch_loss /= n_steps\n", "    log_train_loss.append(epoch_loss)\n", "    if do_save:\n", "        writer.add_scalar(\"train_loss\", epoch_loss, epoch)\n", "    print(f\"{epoch + 1} | loss = {epoch_loss:.6f} \" f\"elapsed time: {time.time()-t0_train:.2f} sec.\")\n", "    # ==============================================\n", "    # Eval\n", "    # ==============================================\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "\n", "        n_steps = 0\n", "        with torch.no_grad():\n", "            for batch_data in val_loader:\n", "                # Get data\n", "                fixed_image = batch_data[\"fixed_image\"].to(device)\n", "                moving_image = batch_data[\"moving_image\"].to(device)\n", "                fixed_label_4 = batch_data[\"fixed_label_4\"].to(device)\n", "                moving_label_4 = batch_data[\"moving_label_4\"].to(device)\n", "                # fixed_label_35 = batch_data[\"fixed_label_35\"].to(device)\n", "                # moving_label_35 = batch_data[\"moving_label_35\"].to(device)\n", "                n_steps += 1\n", "                # Infer\n", "                with torch.cuda.amp.autocast(enabled=amp_enabled):\n", "                    ddf_image, pred_image, pred_label_one_hot = forward(\n", "                        fixed_image, moving_image, moving_label_4, model, warp_layer, num_classes=4\n", "                    )\n", "                # Dice\n", "                dice_metric_before(y_pred=moving_label_4, y=fixed_label_4)\n", "                dice_metric_after(y_pred=pred_label_one_hot.argmax(dim=1, keepdim=True), y=fixed_label_4)\n", "                # Note: DiceMetric does work with one-hot encoded inputs. However, recall that when we\n", "                # defined the forward pass, we first converted the discrete moving label into one-hot\n", "                # format, and then warp the one-hot encoded moving label (which by default uses\n", "                # bilinear interpolation). Passing a non-binary one-hot label as y_pred is not\n", "                # supported by DiceMetric. DiceHelper does, but we are not using DiceHelper since it\n", "                # does not inherit from CumulativeIterationMetric and is, thus, unable to accumulate\n", "                # statistics.\n", "\n", "                # # uncomment to show a pair of fixed and moved image at each validation\n", "                # if n_steps == 1:\n", "                #     fig, axs = plt.subplots(1, 2)\n", "                #     fixed = fixed_image.cpu().squeeze().permute(1, 0, 2)[:, :, 109]\n", "                #     axs[0].imshow(fixed, cmap=\"gray\")\n", "                #     axs[0].title.set_text(\"Fixed\")\n", "                #     moving = pred_image.detach().cpu().squeeze().permute(1, 0, 2)[:, :, 109]\n", "                #     axs[1].imshow(moving, cmap=\"gray\")\n", "                #     axs[1].title.set_text(\"Moved\")\n", "                #     plt.show()\n", "\n", "        # Dice\n", "        dice_before = dice_metric_before.aggregate().item()\n", "        dice_metric_before.reset()\n", "        dice_after = dice_metric_after.aggregate().item()\n", "        dice_metric_after.reset()\n", "        if do_save:\n", "            writer.add_scalar(\"val_dice\", dice_after, epoch)\n", "        log_val_dice.append(dice_after)\n", "        print(f\"{epoch + 1} | dice_before = {dice_before:.3f}, dice_after = {dice_after:.3f}\")\n", "\n", "        if dice_after > best_eval_dice:\n", "            best_eval_dice = dice_after\n", "            if do_save:\n", "                # Save best model based on Dice\n", "                if pth_best_dice != \"\":\n", "                    os.remove(os.path.join(dir_save, pth_best_dice))\n", "                pth_best_dice = f\"voxelmorph_loss_best_dice_{epoch + 1}_{best_eval_dice:.3f}.pth\"\n", "                torch.save(model.state_dict(), os.path.join(dir_save, pth_best_dice))\n", "                print(f\"{epoch + 1} | Saving best Dice model: {pth_best_dice}\")\n", "\n", "    if do_save:\n", "        # Save latest model\n", "        if pth_latest != \"\":\n", "            os.remove(os.path.join(dir_save, pth_latest))\n", "        pth_latest = \"voxelmorph_loss_latest.pth\"\n", "        torch.save(model.state_dict(), os.path.join(dir_save, pth_latest))"]}, {"cell_type": "markdown", "id": "21ad2600-1883-4e88-8424-af099d43e2c8", "metadata": {}, "source": ["**Training output should look like this:**<br>\n", "(epoch times reported for 1x RTX 3090, batch-size 1)\n", "\n", "```\n", "1 | loss = 0.010432 elapsed time: 120.92 sec.\n", "1 | dice_before = 0.497, dice_after = 0.573\n", "1 | Saving best Dice model: voxelmorph_loss_best_dice_1_0.573.pth\n", "2 | loss = 0.008264 elapsed time: 120.85 sec.\n", "2 | dice_before = 0.497, dice_after = 0.634\n", "2 | Saving best Dice model: voxelmorph_loss_best_dice_2_0.634.pth\n", "3 | loss = 0.007140 elapsed time: 121.80 sec.\n", "3 | dice_before = 0.497, dice_after = 0.659\n", "3 | Saving best Dice model: voxelmorph_loss_best_dice_3_0.659.pth\n", "...\n", "47 | loss = 0.004275 elapsed time: 124.96 sec.\n", "47 | dice_before = 0.497, dice_after = 0.767\n", "48 | loss = 0.004259 elapsed time: 125.38 sec.\n", "48 | dice_before = 0.497, dice_after = 0.767\n", "49 | loss = 0.004234 elapsed time: 125.50 sec.\n", "49 | dice_before = 0.497, dice_after = 0.767\n", "50 | loss = 0.004259 elapsed time: 125.80 sec.\n", "50 | dice_before = 0.497, dice_after = 0.767\n", "```"]}, {"cell_type": "code", "execution_count": 14, "id": "f480104aa6ca0d7a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(2, 1, figsize=(4, 6))\n", "axs[0].plot(log_train_loss)\n", "# axs[0].set_xlabel(\"Epoch\")\n", "axs[0].set_ylabel(\"Training Loss\")\n", "axs[1].plot(log_val_dice)\n", "axs[1].set_xlabel(\"Epoch\")\n", "axs[1].set_ylabel(\"Dice Score on Validation Set\")\n", "plt.suptitle(f\"VoxelMorph\\n(Smoothness weight: {lam_smooth}, Dice weight: {lam_dice})\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "df123f7b97a9b472", "metadata": {}, "source": ["## Visualize registration performance of trained network"]}, {"cell_type": "markdown", "id": "3ebacf28708deb47", "metadata": {}, "source": ["#### Load pretrained model and perform forward pass"]}, {"cell_type": "code", "execution_count": 15, "id": "b655b5d71701f63b", "metadata": {}, "outputs": [], "source": ["load_pretrained_model_weights = True\n", "if load_pretrained_model_weights:\n", "    dir_load = dir_save  # folder where network weights are stored\n", "    # instantiate warp layer\n", "    warp_layer = Warp().to(device)\n", "    # instantiate model\n", "    model = VoxelMorph()\n", "    # load model weights\n", "    filename_best_model = glob.glob(os.path.join(dir_load, \"voxelmorph_loss_best_dice_*\"))[0]\n", "    model.load_state_dict(torch.load(filename_best_model))\n", "    # to GPU\n", "    model.to(device)\n", "\n", "set_determinism(seed=1)\n", "check_ds = Dataset(data=val_files, transform=transform_val)\n", "check_loader = DataLoader(check_ds, batch_size=1, shuffle=True)\n", "check_data = first(check_loader)\n", "\n", "# Get data\n", "fixed_image = check_data[\"fixed_image\"].to(device)\n", "moving_image = check_data[\"moving_image\"].to(device)\n", "fixed_label_4 = check_data[\"fixed_label_4\"].to(device)\n", "moving_label_4 = check_data[\"moving_label_4\"].to(device)\n", "fixed_label_35 = check_data[\"fixed_label_35\"].to(device)\n", "moving_label_35 = check_data[\"moving_label_35\"].to(device)\n", "\n", "# Forward pass\n", "model.eval()\n", "with torch.no_grad():\n", "    with torch.cuda.amp.autocast(enabled=amp_enabled):\n", "        ddf_image, pred_image, pred_label_one_hot = forward(\n", "            fixed_image, moving_image, moving_label_35, model, warp_layer, num_classes=35\n", "        )"]}, {"cell_type": "markdown", "id": "8f1558ab60cddc92", "metadata": {}, "source": ["#### Visualization of fixed/moving/moved images and labels"]}, {"cell_type": "code", "execution_count": 16, "id": "1ec6e610794710bd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Image and label visualization\n", "def visualize(fixed_image, fixed_label, moving_image, moving_label, pred_image, pred_label_one_hot):\n", "    # remove the batch dimension and move to cpu\n", "    fixed_image = fixed_image.squeeze(0).cpu()\n", "    fixed_label = fixed_label.squeeze(0).cpu()\n", "    moving_image = moving_image.squeeze(0).cpu()\n", "    moving_label = moving_label.squeeze(0).cpu()\n", "    pred_image = pred_image.squeeze(0).cpu()\n", "    pred_label = pred_label_one_hot.argmax(dim=1, keepdim=True).squeeze(0).cpu()\n", "\n", "    # blend image and label\n", "    fixed_blended = blend_images(fixed_image, fixed_label)\n", "    moving_blended = blend_images(moving_image, moving_label)\n", "    pred_blended = blend_images(pred_image, pred_label)\n", "\n", "    # extract coronal slice 109 and transpose\n", "    fixed_image = fixed_image.permute(2, 1, 3, 0)[:, :, 109, :]\n", "    moving_image = moving_image.permute(2, 1, 3, 0)[:, :, 109, :]\n", "    pred_image = pred_image.permute(2, 1, 3, 0)[:, :, 109, :]\n", "\n", "    fixed_blended = fixed_blended.permute(2, 1, 3, 0)[:, :, 109, :]\n", "    moving_blended = moving_blended.permute(2, 1, 3, 0)[:, :, 109, :]\n", "    pred_blended = pred_blended.permute(2, 1, 3, 0)[:, :, 109, :]\n", "\n", "    # create plot\n", "    fig, axs = plt.subplots(2, 3)\n", "    axs[0, 0].imshow(fixed_image, cmap=\"gray\")\n", "    axs[0, 0].title.set_text(\"Fixed\")\n", "    axs[0, 1].imshow(moving_image, cmap=\"gray\")\n", "    axs[0, 1].title.set_text(\"Moving\")\n", "    axs[0, 2].imshow(pred_image, cmap=\"gray\")\n", "    axs[0, 2].title.set_text(\"Moved\")\n", "    axs[1, 0].imshow(fixed_blended)\n", "    axs[1, 1].imshow(moving_blended)\n", "    axs[1, 2].imshow(pred_blended)\n", "\n", "    # remove ticks on all axes\n", "    plt.setp(plt.gcf().get_axes(), xticks=[], yticks=[])\n", "\n", "    # set title\n", "    plt.suptitle(\"Image and label visualization pre-/post-registration\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "visualize(fixed_image, fixed_label_35, moving_image, moving_label_35, pred_image, pred_label_one_hot)"]}, {"cell_type": "markdown", "id": "5c897ace5b72b8e0", "metadata": {}, "source": ["<a id='nextsteps'></a>\n", "## Next steps\n", "\n", "The registration network has been trained and generalizes well to unseen image pairs in the validation set. As next steps, readers can:\n", "\n", "- Inspect other validation cases by looping through the validation and visualizing registration results.\n", "- Try different combinations of loss weights for the multi-target loss `loss_fun()` in code cell [Hyperparameters](#hyperparameters), e.g., increasing the loss weight for deformation smoothness (`lam_smooth`).\n", "- Try different loss functions, e.g., replacing `MSELoss` with `LocalNormalizedCrossCorrelationLoss`.\n", "- Train the network longer, i.e., use `max_epochs = 200` in cell [Hyperparameters](#hyperparameters). \n", "- With MONAI, it is very easy to swap out the `backbone` in the `VoxelMorph` framework for other SOTA models, e.g., 3D vision transformer-based architectures like [UNETR](https://docs.monai.io/en/stable/networks.html#unetr) or Swin-Transformer-based architectures like [SwinUNETR](https://docs.monai.io/en/stable/networks.html#swinunetr). It is also possible to bring-your-own-model (BYOM), as long as the model architecture derives from `torch.nn.Module` and is appropriate (see [Create model](#Create-model/optimizer/metrics)).\n", "\n", "For further information on how to prepare a challenge submission from these results, please visit official recommendations from the [Learn2Reg](https://learn2reg.grand-challenge.org/) website. "]}, {"cell_type": "markdown", "id": "fb248c08-57fc-4aba-b17d-d68183e86066", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used. Remove the directory where the models are saved."]}, {"cell_type": "code", "execution_count": 17, "id": "d512895b-0fe2-4d29-a82c-588283a7fa1b", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)\n", "shutil.rmtree(dir_save)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}