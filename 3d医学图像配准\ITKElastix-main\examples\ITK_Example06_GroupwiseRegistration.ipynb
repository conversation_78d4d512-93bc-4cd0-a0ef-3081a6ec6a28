{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 6. Groupwise Image Registration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Groupwise registration methods try to mitigate uncertainties associated with any one image by simultaneously registering all images in a population. This incorporates all image information in registration process and eliminates bias towards a chosen reference frame. The method described here uses a 3D (2D+time) free-form B-spline deformation model and a similarity metric that minimizes variance of intensities under the constraint that the average deformation over images is zero. This constraint defines a true mean frame of reference that lie in the center of the population without having to calculate it explicitly.\n", "\n", "The method can take into account temporal smoothness of the deformations and a cyclic transform in the time dimension. This may be appropriate if it is known a priori that the anatomical motion has a cyclic nature e.g. in cases of cardiac or respiratory motion."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Registration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Load folder containing images.\n", "images = itk.imread(\"data/00\", itk.F)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create Groupwise Parameter Object\n", "parameter_object = itk.ParameterObject.New()\n", "groupwise_parameter_map = parameter_object.GetDefaultParameterMap('groupwise')\n", "parameter_object.AddParameterMap(groupwise_parameter_map)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Registration can either be done in one line with the registration function..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "# both fixed and moving image should be set with the vector_itk to prevent elastix from throwing errors\n", "\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    images, images,\n", "    parameter_object=parameter_object,\n", "    log_to_console=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an elastix image filter object."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Elastix Image Filter Object\n", "# Fixed and moving image should be given to the Elastix method to ensure that\n", "# the correct 3D class is initialized.\n", "# Both fixed and moving image should be set with the vector_itk to prevent elastix from throwing errors\n", "\n", "elastix_object = itk.ElastixRegistrationMethod.New(images, images)\n", "elastix_object.SetParameterObject(parameter_object)\n", "\n", "# Set additional options\n", "elastix_object.SetLogToConsole(False)\n", "\n", "# Update filter object (required)\n", "elastix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Registration\n", "result_image = elastix_object.GetOutput()\n", "result_transform_parameters = elastix_object.GetTransformParameterObject()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}