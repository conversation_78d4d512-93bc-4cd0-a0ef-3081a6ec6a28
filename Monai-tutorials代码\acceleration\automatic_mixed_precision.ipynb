{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# Automatic mixed precision training and evaluation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This tutorial shows how to apply the automatic mixed precision (AMP) feature of PyTorch to training and validation programs.  \n", "It's modified from the Spleen 3D segmentation tutorial notebook, and compares the training speed and memory usage with/without AMP.\n", "\n", "The Spleen dataset can be downloaded from http://medicaldecathlon.com/.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/acceleration/automatic_mixed_precision.ipynb)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import torch\n", "from monai.apps import download_and_extract\n", "from monai.config import print_config\n", "from monai.data import CacheDataset, DataLoader, decollate_batch\n", "from monai.inferers import sliding_window_inference\n", "from monai.losses import DiceLoss\n", "from monai.metrics import DiceMetric\n", "from monai.networks.layers import Norm\n", "from monai.networks.nets import UNet\n", "from monai.transforms import (\n", "    EnsureChannelFirstd,\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    FgBgToIndicesd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    RandCropByPosNegLabeld,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", ")\n", "from monai.utils import get_torch_version_tuple, set_determinism\n", "\n", "print_config()\n", "\n", "if get_torch_version_tuple() < (1, 6):\n", "    raise RuntimeError(\"AMP feature only exists in PyTorch version greater than v1.6.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(f\"root dir is: {root_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download dataset\n", "\n", "Downloads and extracts the Decathlon Spleen dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["resource = \"https://msd-for-monai.s3-us-west-2.amazonaws.com/Task09_Spleen.tar\"\n", "md5 = \"410d4a301da4e5b2f6f86ec3ddba524e\"\n", "\n", "compressed_file = os.path.join(root_dir, \"Task09_Spleen.tar\")\n", "data_root = os.path.join(root_dir, \"Task09_Spleen\")\n", "if not os.path.exists(data_root):\n", "    download_and_extract(resource, compressed_file, root_dir, md5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set MSD Spleen dataset path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_images = sorted(glob.glob(os.path.join(data_root, \"imagesTr\", \"*.nii.gz\")))\n", "train_labels = sorted(glob.glob(os.path.join(data_root, \"labelsTr\", \"*.nii.gz\")))\n", "data_dicts = [{\"image\": image_name, \"label\": label_name} for image_name, label_name in zip(train_images, train_labels)]\n", "train_files, val_files = data_dicts[:-9], data_dicts[-9:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms for training and validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transformations():\n", "    train_transforms = Compose(\n", "        [\n", "            LoadImaged(keys=[\"image\", \"label\"]),\n", "            EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "            Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "            Spacingd(\n", "                keys=[\"image\", \"label\"],\n", "                pixdim=(1.5, 1.5, 2.0),\n", "                mode=(\"bilinear\", \"nearest\"),\n", "            ),\n", "            ScaleIntensityRanged(\n", "                keys=[\"image\"],\n", "                a_min=-57,\n", "                a_max=164,\n", "                b_min=0.0,\n", "                b_max=1.0,\n", "                clip=True,\n", "            ),\n", "            CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "            # pre-compute foreground and background indexes\n", "            # and cache them to accelerate training\n", "            FgBgToIndicesd(\n", "                keys=\"label\",\n", "                fg_postfix=\"_fg\",\n", "                bg_postfix=\"_bg\",\n", "                image_key=\"image\",\n", "            ),\n", "            # randomly crop out patch samples from\n", "            # big image based on pos / neg ratio\n", "            # the image centers of negative samples must be in valid image area\n", "            RandCropByPosNegLabeld(\n", "                keys=[\"image\", \"label\"],\n", "                label_key=\"label\",\n", "                spatial_size=(128, 128, 96),\n", "                pos=1,\n", "                neg=1,\n", "                num_samples=4,\n", "                fg_indices_key=\"label_fg\",\n", "                bg_indices_key=\"label_bg\",\n", "            ),\n", "        ]\n", "    )\n", "    val_transforms = Compose(\n", "        [\n", "            LoadImaged(keys=[\"image\", \"label\"]),\n", "            EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "            Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "            Spacingd(\n", "                keys=[\"image\", \"label\"],\n", "                pixdim=(1.5, 1.5, 2.0),\n", "                mode=(\"bilinear\", \"nearest\"),\n", "            ),\n", "            ScaleIntensityRanged(\n", "                keys=[\"image\"],\n", "                a_min=-57,\n", "                a_max=164,\n", "                b_min=0.0,\n", "                b_max=1.0,\n", "                clip=True,\n", "            ),\n", "            CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        ]\n", "    )\n", "    return train_transforms, val_transforms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define a typical PyTorch training process\n", "Usage of PyTorch AMP module refers to: https://pytorch.org/docs/stable/notes/amp_examples.html."]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["def train_process(amp=False):\n", "    train_trans, val_trans = transformations()\n", "    train_ds = CacheDataset(data=train_files, transform=train_trans, cache_rate=1.0, num_workers=8)\n", "    val_ds = CacheDataset(data=val_files, transform=val_trans, cache_rate=1.0, num_workers=5)\n", "\n", "    # use batch_size=2 to load images and use RandCropByPosNegLabeld\n", "    # to generate 2 x 4 images for network training\n", "    train_loader = DataLoader(\n", "        train_ds,\n", "        batch_size=2,\n", "        shuffle=True,\n", "        num_workers=1,\n", "    )\n", "    val_loader = DataLoader(val_ds, batch_size=1, num_workers=1)\n", "    device = torch.device(\"cuda:0\")\n", "    model = UNet(\n", "        spatial_dims=3,\n", "        in_channels=1,\n", "        out_channels=2,\n", "        channels=(16, 32, 64, 128, 256),\n", "        strides=(2, 2, 2, 2),\n", "        num_res_units=2,\n", "        norm=Norm.BATCH,\n", "    ).to(device)\n", "    loss_function = DiceLoss(to_onehot_y=True, softmax=True)\n", "    optimizer = torch.optim.Adam(model.parameters(), 1e-4)\n", "    scaler = torch.cuda.amp.GradScaler() if amp else None\n", "\n", "    post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])\n", "    post_label = Compose([AsDiscrete(to_onehot=2)])\n", "\n", "    dice_metric = DiceMetric(include_background=False, reduction=\"mean\", get_not_nans=False)\n", "\n", "    max_epochs = 300\n", "    val_interval = 1  # do validation for every epoch\n", "    best_metric = -1\n", "    best_metric_epoch = -1\n", "    best_metrics_epochs_and_time = [[], [], []]\n", "    epoch_loss_values = []\n", "    metric_values = []\n", "    epoch_times = []\n", "    total_start = time.time()\n", "    for epoch in range(max_epochs):\n", "        epoch_start = time.time()\n", "        print(\"-\" * 10)\n", "        print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "        model.train()\n", "        epoch_loss = 0\n", "        step = 0\n", "        for batch_data in train_loader:\n", "            step_start = time.time()\n", "            step += 1\n", "            inputs, labels = (\n", "                batch_data[\"image\"].to(device),\n", "                batch_data[\"label\"].to(device),\n", "            )\n", "            optimizer.zero_grad()\n", "            if amp and scaler is not None:\n", "                with torch.cuda.amp.autocast():\n", "                    outputs = model(inputs)\n", "                    loss = loss_function(outputs, labels)\n", "                scaler.scale(loss).backward()\n", "                scaler.step(optimizer)\n", "                scaler.update()\n", "            else:\n", "                outputs = model(inputs)\n", "                loss = loss_function(outputs, labels)\n", "                loss.backward()\n", "                optimizer.step()\n", "            epoch_loss += loss.item()\n", "            print(\n", "                f\"{step}/{len(train_ds) // train_loader.batch_size},\"\n", "                f\" train_loss: {loss.item():.4f}\"\n", "                f\" step time: {(time.time() - step_start):.4f}\"\n", "            )\n", "        epoch_loss /= step\n", "        epoch_loss_values.append(epoch_loss)\n", "        print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "        if (epoch + 1) % val_interval == 0:\n", "            model.eval()\n", "            with torch.no_grad():\n", "                for val_data in val_loader:\n", "                    val_inputs, val_labels = (\n", "                        val_data[\"image\"].to(device),\n", "                        val_data[\"label\"].to(device),\n", "                    )\n", "                    roi_size = (160, 160, 128)\n", "                    sw_batch_size = 4\n", "                    if amp:\n", "                        with torch.cuda.amp.autocast():\n", "                            val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)\n", "                    else:\n", "                        val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)\n", "                    val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]\n", "                    val_labels = [post_label(i) for i in decollate_batch(val_labels)]\n", "                    dice_metric(y_pred=val_outputs, y=val_labels)\n", "\n", "                metric = dice_metric.aggregate().item()\n", "                dice_metric.reset()\n", "                metric_values.append(metric)\n", "                if metric > best_metric:\n", "                    best_metric = metric\n", "                    best_metric_epoch = epoch + 1\n", "                    best_metrics_epochs_and_time[0].append(best_metric)\n", "                    best_metrics_epochs_and_time[1].append(best_metric_epoch)\n", "                    best_metrics_epochs_and_time[2].append(time.time() - total_start)\n", "                    torch.save(model.state_dict(), \"best_metric_model.pth\")\n", "                    print(\"saved new best metric model\")\n", "                print(\n", "                    f\"current epoch: {epoch + 1} current\"\n", "                    f\" mean dice: {metric:.4f}\"\n", "                    f\" best mean dice: {best_metric:.4f} \"\n", "                    f\"at epoch: {best_metric_epoch}\"\n", "                )\n", "        print(f\"time consuming of epoch {epoch + 1}\" f\" is: {(time.time() - epoch_start):.4f}\")\n", "        epoch_times.append(time.time() - epoch_start)\n", "    print(\n", "        f\"train completed, best_metric: {best_metric:.4f}\"\n", "        f\" at epoch: {best_metric_epoch}\"\n", "        f\" total time: {(time.time() - total_start):.4f}\"\n", "    )\n", "    return (\n", "        max_epochs,\n", "        epoch_loss_values,\n", "        metric_values,\n", "        epoch_times,\n", "        best_metrics_epochs_and_time,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable deterministic and train with AMP"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["set_determinism(seed=0)\n", "amp_start = time.time()\n", "(\n", "    max_epochs,\n", "    amp_epoch_loss_values,\n", "    amp_metric_values,\n", "    amp_epoch_times,\n", "    amp_best,\n", ") = train_process(amp=True)\n", "amp_total_time = time.time() - amp_start\n", "print(f\"total training time of {max_epochs} \" f\"epochs with AMP: {amp_total_time:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check the memory usage during training with AMP"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NVIDIA A100 80GB PCIe\n", "|===========================================================================|\n", "|                  PyTorch CUDA memory summary, device ID 0                 |\n", "|---------------------------------------------------------------------------|\n", "|            CUDA OOMs: 0            |        cudaMalloc retries: 0         |\n", "|===========================================================================|\n", "|        Metric         | Cur Usage  | Peak Usage | Tot Alloc  | Tot Freed  |\n", "|---------------------------------------------------------------------------|\n", "| Allocated memory      |       0 B  |    1829 MB |   47383 GB |   47383 GB |\n", "|---------------------------------------------------------------------------|\n", "| Active memory         |       0 B  |    1829 MB |   47383 GB |   47383 GB |\n", "|---------------------------------------------------------------------------|\n", "| GPU reserved memory   |    2366 MB |    2366 MB |    2366 MB |       0 B  |\n", "|---------------------------------------------------------------------------|\n", "| Non-releasable memory |       0 B  |  673569 KB |   32633 GB |   32633 GB |\n", "|---------------------------------------------------------------------------|\n", "| Allocations           |       0    |     608    |    4715 K  |    4715 K  |\n", "|---------------------------------------------------------------------------|\n", "| Active allocs         |       0    |     608    |    4715 K  |    4715 K  |\n", "|---------------------------------------------------------------------------|\n", "| GPU reserved segments |      42    |      42    |      42    |       0    |\n", "|---------------------------------------------------------------------------|\n", "| Non-releasable allocs |       0    |      50    |    2821 K  |    2821 K  |\n", "|---------------------------------------------------------------------------|\n", "| Oversize allocations  |       0    |       0    |       0    |       0    |\n", "|---------------------------------------------------------------------------|\n", "| Oversize GPU segments |       0    |       0    |       0    |       0    |\n", "|===========================================================================|\n", "\n"]}], "source": ["print(torch.cuda.get_device_name(0))\n", "print(torch.cuda.memory_summary(0, abbreviated=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable deterministic and train without AMP\n", "In order to correctly measure the memory usage, please restart the notebook and skip above AMP training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set_determinism(seed=0)\n", "start = time.time()\n", "(\n", "    max_epochs,\n", "    epoch_loss_values,\n", "    metric_values,\n", "    epoch_times,\n", "    best,\n", ") = train_process(amp=False)\n", "total_time = time.time() - start\n", "print(f\"total training time of {max_epochs} epochs without AMP: {total_time:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check the memory usage during training without AMP"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NVIDIA A100 80GB PCIe\n", "|===========================================================================|\n", "|                  PyTorch CUDA memory summary, device ID 0                 |\n", "|---------------------------------------------------------------------------|\n", "|            CUDA OOMs: 0            |        cudaMalloc retries: 0         |\n", "|===========================================================================|\n", "|        Metric         | Cur Usage  | Peak Usage | Tot Alloc  | Tot Freed  |\n", "|---------------------------------------------------------------------------|\n", "| Allocated memory      |       0 B  |    2892 MB |  130153 GB |  130153 GB |\n", "|---------------------------------------------------------------------------|\n", "| Active memory         |       0 B  |    2892 MB |  130153 GB |  130153 GB |\n", "|---------------------------------------------------------------------------|\n", "| GPU reserved memory   |    3732 MB |    3732 MB |    3732 MB |       0 B  |\n", "|---------------------------------------------------------------------------|\n", "| Non-releasable memory |       0 B  |     992 MB |   98960 GB |   98960 GB |\n", "|---------------------------------------------------------------------------|\n", "| Allocations           |       0    |     608    |    8597 K  |    8597 K  |\n", "|---------------------------------------------------------------------------|\n", "| Active allocs         |       0    |     608    |    8597 K  |    8597 K  |\n", "|---------------------------------------------------------------------------|\n", "| GPU reserved segments |      47    |      47    |      47    |       0    |\n", "|---------------------------------------------------------------------------|\n", "| Non-releasable allocs |       0    |      50    |    4507 K  |    4507 K  |\n", "|---------------------------------------------------------------------------|\n", "| Oversize allocations  |       0    |       0    |       0    |       0    |\n", "|---------------------------------------------------------------------------|\n", "| Oversize GPU segments |       0    |       0    |       0    |       0    |\n", "|===========================================================================|\n", "\n"]}], "source": ["print(torch.cuda.get_device_name(0))\n", "print(torch.cuda.memory_summary(0, abbreviated=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot training loss and validation metrics"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA9UAAAPxCAYAAAD0d44/AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy89olMNAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOydeZgU1fX3v1UzzAozwzAwDDCAIgiIQoJCEBAXFHdQo7iiRE3kJzERlUgSJXHjTVQ0cQmGaDBR3BCXuCCKEqMguIGoyKYwCzPDDLPvM133/aOmuqv3qu7qrq4+5/s881R1dS33c27dmj51zr1XEkIIsFgsFovFYrFYLBaLxTIt2e4CsFgsFovFYrFYLBaL5VSxU81isVgsFovFYrFYLFaEYqeaxWKxWCwWi8VisVisCMVONYvFYrFYLBaLxWKxWBGKnWoWi8VisVgsFovFYrEiFDvVLBaLxWKxWCwWi8ViRSh2qlksFovFYrFYLBaLxYpQ7FSzWCwWi8VisVgsFosVodipZrFYLBaLxWKxWCwWK0KxU81KGm3cuBGSJGHjxo12F8V2rVq1CpIk4bPPPrO7KCwWi8ViOUZO/i2xf/9+SJKEVatW2V2UhNbw4cNxzTXX2F0MVpKJnWpWWGkOmvaXmpqKwYMH45prrkF5ebndxbNFvjbx/fvkk0/sLqJlWrx4MSRJwty5c+0uSsJJkiQsXLjQ7mKwWCxWwot/S3jr/PPPR1ZWFpqamoLuc8UVVyAtLQ2HDx+29NraiwNJkvDMM88E3Gfq1KmQJAnjxo2z9NpW6+STT3azyLKMnJwcHH300bjqqqvw7rvv2l08FiGl2l0AlnN011134YgjjkB7ezs++eQTrFq1Ch999BG+/vprZGRk2F08W6TZxFdHHXWUDaWxXkIIPPfccxg+fDj+85//oKmpCX369LG7WCwWi8VyqPi3hKorrrgC//nPf/DKK69g3rx5ft+3trbitddew5lnnol+/frFpAwZGRlYvXo1rrzySq/t+/fvx6ZNmxxTH0OGDMGyZcsAAC0tLdi7dy/Wrl2LZ555BpdccgmeeeYZ9OrVy73/rl27IMscV2RZK3aqWYZ11lln4fjjjwcAXHfddSgoKMCf/vQnvP7667jkkktsLp31amlpQXZ2dsh99DZJRm3cuBFlZWV4//33MWvWLKxduxZXX311XMvQ3d0NRVGQlpYW1+uyWCwWy3rxbwlV559/Pvr06YPVq1cHdKpfe+01tLS04IorrohZ2c4++2y8/vrrqKmpQUFBgXv76tWrUVhYiJEjR6Kuri5m17dKubm5fi8G/t//+3+46aab8Pjjj2P48OH405/+5P4uPT093kVkERC/pmFFrOnTpwMA9u3b57X9u+++w09/+lPk5+cjIyMDxx9/PF5//XW/47/66ivMmDEDmZmZGDJkCO655x7885//hCRJ2L9/v3s/SZLwhz/8we94I31i/ve//+Hiiy/G0KFDkZ6ejuLiYtx8881oa2vz2u+aa65B7969sW/fPpx99tno06ePJf/ItP5NDzzwAB566CEMGzYMmZmZmDFjBr7++mu//d9//31Mnz4d2dnZyMvLw+zZs7Fz506//crLy3Httddi0KBBSE9PxxFHHIEFCxags7PTa7+Ojg4sWrQI/fv3R3Z2Ni644AJUV1cbLv+zzz6LsWPH4pRTTsHMmTPx7LPPur+rqqpCamoq/vjHP/odt2vXLkiShEcffdS9rb6+Hr/+9a9RXFyM9PR0HHXUUfjTn/4ERVEC2uvhhx/GiBEjkJ6ejm+//RadnZ248847MXHiROTm5iI7OxvTp0/HBx984Hf9w4cP46qrrkJOTg7y8vJw9dVXY/v27QH7mhm9XyNVS0sLbrnlFjf30UcfjQceeABCCK/93n33XUybNg15eXno3bs3jj76aPz2t7/12ueRRx7BMcccg6ysLPTt2xfHH388Vq9ebVlZWSwWK96i+lsiMzMTF154ITZs2IBDhw75fb969Wr06dMH559/Pmpra3Hrrbfi2GOPRe/evZGTk4OzzjoL27dvD1nucJo9ezbS09Px0ksv+V37kksuQUpKSsDjnnnmGUycOBGZmZnIz8/HpZdeitLSUq99zNqsvLwcc+bMQe/evdG/f3/ceuutcLlcEbOlpKTgr3/9K8aOHYtHH30UDQ0N7u8C1Xl9fT1uvvlmDB8+HOnp6RgyZAjmzZuHmpoa9z4dHR1YunQpjjrqKDfT4sWL0dHREXE5WckjjlSzIpb2z6pv377ubd988w2mTp2KwYMH4/bbb0d2djZefPFFzJkzBy+//DIuuOACAKpTeMopp0CSJCxZsgTZ2dn4xz/+Yfnbw5deegmtra1YsGAB+vXrh61bt+KRRx5BWVmZ3z+R7u5uzJo1C9OmTcMDDzyArKyssOdvaGjweuAC6j9u31Stf/3rX2hqasKNN96I9vZ2/OUvf8Gpp56KHTt2oLCwEADw3nvv4ayzzsKRRx6JP/zhD2hra8MjjzyCqVOn4osvvsDw4cMBAAcPHsSkSZNQX1+Pn//85xg9ejTKy8uxZs0atLa2ekV0f/nLX6Jv375YunQp9u/fj4cffhgLFy7ECy+8EJato6MDL7/8Mm655RYAwGWXXYb58+ejsrISAwcORGFhIWbMmIEXX3wRS5cu9Tr2hRdeQEpKCi6++GIAahrbjBkzUF5ejl/84hcYOnQoNm3ahCVLlqCiogIPP/yw1/H//Oc/0d7ejp///OdIT09Hfn4+Ghsb8Y9//AOXXXYZrr/+ejQ1NeHJJ5/ErFmzsHXrVkyYMAEAoCgKzjvvPGzduhULFizA6NGj8dprrwWMsBu9XyOVEALnn38+PvjgA1x77bWYMGEC3nnnHdx2220oLy/HQw895C7Hueeei+OOOw533XUX0tPTsXfvXnz88cfuc61cuRI33XQTfvrTn+JXv/oV2tvb8dVXX2HLli24/PLLoyoni8Vi2SXKvyWuuOIKPP3003jxxRe9xueora3FO++8g8suuwyZmZn45ptv8Oqrr+Liiy/GEUccgaqqKjzxxBOYMWMGvv32WwwaNCgirqysLMyePRvPPfccFixYAADYvn07vvnmG/zjH//AV1995XfMvffeizvuuAOXXHIJrrvuOlRXV+ORRx7BSSedhC+//BJ5eXmmbeZyuTBr1ixMnjwZDzzwAN577z08+OCDGDFihLtckSglJQWXXXYZ7rjjDnz00Uc455xzAu7X3NyM6dOnY+fOnfjZz36GH//4x6ipqcHrr7+OsrIyFBQUQFEUnH/++fjoo4/w85//HGPGjMGOHTvw0EMPYffu3Xj11VcjLicrSSRYrDD65z//KQCI9957T1RXV4vS0lKxZs0a0b9/f5Geni5KS0vd+5522mni2GOPFe3t7e5tiqKIE088UYwcOdK97Ze//KWQJEl8+eWX7m2HDx8W+fn5AoD44Ycf3NsBiKVLl/qVa9iwYeLqq692f/7ggw8EAPHBBx+4t7W2tvodt2zZMiFJkjhw4IB729VXXy0AiNtvv92UTQL9paenu/f74YcfBACRmZkpysrK3Nu3bNkiAIibb77ZvW3ChAliwIAB4vDhw+5t27dvF7Isi3nz5rm3zZs3T8iyLD799FO/cimK4lW+mTNnurcJIcTNN98sUlJSRH19fVjGNWvWCABiz549QgghGhsbRUZGhnjooYfc+zzxxBMCgNixY4fXsWPHjhWnnnqq+/Pdd98tsrOzxe7du732u/3220VKSoooKSnxsldOTo44dOiQ177d3d2io6PDa1tdXZ0oLCwUP/vZz9zbXn75ZQFAPPzww+5tLpdLnHrqqQKA+Oc//+nebvR+DSYA4sYbbwz6/auvvioAiHvuucdr+09/+lMhSZLYu3evEEKIhx56SAAQ1dXVQc81e/Zsccwxx4QtE4vFYiWi+LeEv7q7u0VRUZGYMmWK1/YVK1YIAOKdd94RQgjR3t4uXC6X1z4//PCDSE9PF3fddZfXNt//c4GkMb700kvijTfeEJIkuf8P33bbbeLII48UQggxY8YMr/87+/fvFykpKeLee+/1Ot+OHTtEamqq13azNtNzCCHEj370IzFx4sSQHIHK6KtXXnlFABB/+ctf3Nt86/zOO+8UAMTatWv9jtd+Q/373/8WsiyL//3vf17fa3X18ccfhy0rK7nF6d8sw5o5cyb69++P4uJi/PSnP0V2djZef/11DBkyBID6ZvX999/HJZdcgqamJtTU1KCmpgaHDx/GrFmzsGfPHvcIn+vWrcOUKVPc0UUAyM/Pt7zvUGZmpnu9paUFNTU1OPHEEyGEwJdffum3v9k3oo899hjeffddr7+3337bb785c+Zg8ODB7s+TJk3C5MmT8dZbbwEAKioqsG3bNlxzzTXIz89373fcccfh9NNPd++nKApeffVVnHfeeQH7ckuS5PX55z//ude26dOnw+Vy4cCBA2HZnn32WRx//PHuQdf69OmDc845xysF/MILL0RqaqpX5Pvrr7/Gt99+6zVa+EsvvYTp06ejb9++7vuipqYGM2fOhMvlwocffuh17Ysuugj9+/f32paSkuKOwiuKgtraWnR3d+P444/HF1984d5v3bp16NWrF66//nr3NlmWceONN3qdz8z9GqneeustpKSk4KabbvLafsstt0AI4b5XtDf7r732mlc6vF55eXkoKyvDp59+GlWZWCwWy07xbwmPUlJScOmll2Lz5s1eqepan+bTTjsNgNoHWBtYy+Vy4fDhw+5uQvr/f5HojDPOQH5+Pp5//nkIIfD888/jsssuC7jv2rVroSgKLrnkEq//5QMHDsTIkSO9umOZtdkNN9zg9Xn69On4/vvvo2IDgN69ewNAyFHWX375ZYwfPz5gdpr2G+qll17CmDFjMHr0aC/2U089FQACdkVj0RI71SzD0hzINWvW4Oyzz0ZNTY1XitXevXshhMAdd9yB/v37e/1p6cFav6EDBw4EHCHb6lGzS0pK3I6q1k9nxowZAODVvwYAUlNT3f/UjWrSpEmYOXOm198pp5zit9/IkSP9to0aNcr9T1Rzco8++mi//caMGYOamhq0tLSguroajY2Nhqe4GDp0qNdnLb0u3MAj9fX1eOuttzBjxgzs3bvX/Td16lR89tln2L17NwCgoKAAp512Gl588UX3sS+88AJSU1Nx4YUXurft2bMH69at87svZs6cCQB+/ckCjagOAE8//TSOO+44ZGRkoF+/fujfvz/efPNNr7o8cOAAioqK/FLufO8tM/drpDpw4AAGDRrkN2L6mDFj3N8DwNy5czF16lRcd911KCwsxKWXXooXX3zRy8H+zW9+g969e2PSpEkYOXIkbrzxRq/0cBaLxXKC+LeEt7QXANr4GGVlZfjf//6HSy+91N2nWVEUPPTQQxg5ciTS09NRUFCA/v3746uvvvK7vln16tULF198MVavXo0PP/wQpaWlQbsU7dmzB0IIjBw50q9udu7c6fU/04zNMjIy/F6k9+3b15JB0pqbmwEg5Mwl+/btC/u7as+ePfjmm2/8uEeNGgUg+t8LLOeL+1SzDGvSpEnu6OicOXMwbdo0XH755di1axd69+7tdgBuvfVWzJo1K+A5rPxHF24AC5fLhdNPPx21tbX4zW9+g9GjRyM7Oxvl5eW45ppr/CKC+jfByaJgg4wIn0GyfPXSSy+ho6MDDz74IB588EG/75999ln3AGWXXnop5s+fj23btmHChAl48cUXcdppp3mNJKooCk4//XQsXrw44PW0f0qa9G+4NT3zzDO45pprMGfOHNx2220YMGAAUlJSsGzZMr8Bbowo3vdrKGVmZuLDDz/EBx98gDfffBPr1q3DCy+8gFNPPRXr169HSkoKxowZg127duGNN97AunXr8PLLL+Pxxx/HnXfeGXCwOBaLxUpE8W8Jb02cOBGjR4/Gc889h9/+9rd47rnnIITwirbfd999uOOOO/Czn/0Md999N/Lz8yHLMn79618HzW4yo8svvxwrVqzAH/7wB4wfPx5jx44NuJ+iKJAkCW+//XbA3xdaVNiszYL9VrFC2qCw0d4ziqLg2GOPxfLlywN+X1xcHNX5Wc4XO9WsiKQ5M6eccgoeffRR3H777TjyyCMBqG89tQhkMA0bNgx79+712x5oW9++fVFfX++1rbOzExUVFSGvsWPHDuzevRtPP/2013QV7777bsjjYqE9e/b4bdu9e7d78LFhw4YBUEfN9tV3332HgoICZGdnIzMzEzk5OQFHDrdSzz77LMaNG+c3ABkAPPHEE1i9erXbkZszZw5+8YtfuFPAd+/ejSVLlngdM2LECDQ3N4e9L0JpzZo1OPLII7F27VqvlHbfMg4bNgwffPABWltbvaLVvveWmfs1Ug0bNgzvvfee3/ze3333nft7TbIs47TTTsNpp52G5cuX47777sPvfvc7fPDBB+7yZWdnY+7cuZg7dy46Oztx4YUX4t5778WSJUscM58oi8ViaeLfEqquuOIK3HHHHfjqq6+wevVqjBw5EieccIL7+zVr1uCUU07Bk08+6XVcfX291wvsSDVt2jQMHToUGzdu9Jp6ylcjRoyAEAJHHHGE38twvRLl95fL5cLq1auRlZWFadOmBd1vxIgRYX9XjRgxAtu3b8dpp53m19WOxQI4/ZsVhU4++WRMmjQJDz/8MNrb2zFgwACcfPLJeOKJJwL+k9JP5TRr1ixs3rwZ27Ztc2+rra316q+racSIEX59bv/+97+HfbusvfnUR2WFEPjLX/5iiM9Kvfrqq179c7du3YotW7bgrLPOAgAUFRVhwoQJePrpp73+6X/99ddYv349zj77bACq4zVnzhz85z//wWeffeZ3nXARaCMqLS3Fhx9+iEsuuQQ//elP/f7mz5+PvXv3YsuWLQDUvr6zZs3Ciy++iOeffx5paWmYM2eO1zkvueQSbN68Ge+8847f9err69Hd3R22XIHqc8uWLdi8ebPXfrNmzUJXVxdWrlzp3qYoCh577DGv/czcr5Hq7LPPhsvl8ppaDAAeeughSJLkrv/a2lq/Y7U+gtpUHYcPH/b6Pi0tDWPHjoUQAl1dXVGXlcVisewQ/5bwpIDfeeed2LZtm1+f8JSUFL//7y+99FLU435okiQJf/3rX7F06VJcddVVQfe78MILkZKSgj/+8Y9+5RFCuP9PJcLvL5fLhZtuugk7d+7ETTfdhJycnKD7XnTRRdi+fTteeeUVv+80hksuuQTl5eVevy00tbW1oaWlxbrCsxwpjlSzotJtt92Giy++GKtWrcINN9yAxx57DNOmTcOxxx6L66+/HkceeSSqqqqwefNmlJWVuedUXLx4MZ555hmcfvrp+OUvf+meBmPo0KGora31egt43XXX4YYbbsBFF12E008/Hdu3b8c777wT9u3s6NGjMWLECNx6660oLy9HTk4OXn75ZUv66Gh6++233VFHvU488UT323ZATTuaNm0aFixYgI6ODjz88MPo16+fVzr0/fffj7POOgtTpkzBtdde655SKzc312tuzfvuuw/r16/HjBkz3NM6VFRU4KWXXsJHH33kHvQqUq1evdo9FVQgnX322UhNTcWzzz6LyZMnA1D7BF955ZV4/PHHMWvWLL8y3HbbbXj99ddx7rnn4pprrsHEiRPR0tKCHTt2YM2aNdi/f3/Y+jz33HOxdu1aXHDBBTjnnHPwww8/YMWKFRg7dqy7zxSgRs4nTZqEW265BXv37sXo0aPx+uuvux1X/b1l9H4Npc8++wz33HOP3/aTTz4Z5513Hk455RT87ne/w/79+zF+/HisX78er732Gn79619jxIgRAIC77roLH374Ic455xwMGzYMhw4dwuOPP44hQ4a4366fccYZGDhwIKZOnYrCwkLs3LkTjz76KM4555yQfcVYLBYr0UX9t8QRRxyBE088Ea+99hoA+DnV5557Lu666y7Mnz8fJ554Inbs2IFnn33W63dGtJo9ezZmz54dcp8RI0bgnnvuwZIlS7B//37MmTMHffr0wQ8//IBXXnkFP//5z3HrrbfGxWZ6NTQ04JlnngGgTuG5d+9erF27Fvv27cOll16Ku+++O+Txt912G9asWYOLL74YP/vZzzBx4kTU1tbi9ddfx4oVKzB+/HhcddVVePHFF3HDDTfggw8+wNSpU+FyufDdd9/hxRdfxDvvvBNwAFkWIcVrmHGWc6VNgxFoCieXyyVGjBghRowYIbq7u4UQQuzbt0/MmzdPDBw4UPTq1UsMHjxYnHvuuWLNmjVex3755Zdi+vTpIj09XQwZMkQsW7ZM/PWvfxUARGVlpdc1fvOb34iCggKRlZUlZs2aJfbu3WtoGoxvv/1WzJw5U/Tu3VsUFBSI66+/Xmzfvt1vyomrr75aZGdnm7ZJsD/t3Nr0Fvfff7948MEHRXFxsUhPTxfTp08X27dv9zvve++9J6ZOnSoyMzNFTk6OOO+888S3337rt9+BAwfEvHnz3FORHHnkkeLGG290TzkVrM4C2chXxx57rBg6dGhI/pNPPlkMGDBAdHV1CSHU6bYyMzMFAPHMM88EPKapqUksWbJEHHXUUSItLU0UFBSIE088UTzwwAOis7PTz16+UhRF3HfffWLYsGEiPT1d/OhHPxJvvPGGuPrqq8WwYcO89q2urhaXX3656NOnj8jNzRXXXHON+PjjjwUA8fzzz3vta/R+DaRQ98Ddd9/t5r755pvFoEGDRK9evcTIkSPF/fff7zXV2YYNG8Ts2bPFoEGDRFpamhg0aJC47LLLvKYge+KJJ8RJJ50k+vXrJ9LT08WIESPEbbfdJhoaGsKWk8VisewW/5YIrccee0wAEJMmTfL7rr29Xdxyyy2iqKhIZGZmiqlTp4rNmzeLGTNmiBkzZrj3i2RKrVAKNl3Vyy+/LKZNmyays7NFdna2GD16tLjxxhvFrl273PtEa7OlS5cKI27KjBkzvP739u7dW4wcOVJceeWVYv369QGP8a1zIdSp2BYuXCgGDx4s0tLSxJAhQ8TVV18tampq3Pt0dnaKP/3pT+KYY44R6enpom/fvmLixInij3/8I/8vZglJCAvyRVksi/TrX/8aTzzxBJqbm2M6cEW8tH//fhxxxBG4//77ceutt9pdHNJ69dVXccEFF+Cjjz7C1KlT7S4Oi8VisWKkZPstwWKxEl/cp5plm9ra2rw+Hz58GP/+978xbdo0/ifIikq+95bL5cIjjzyCnJwc/PjHP7apVCwWi8WyWvxbgsViJYK4TzXLNk2ZMgUnn3wyxowZg6qqKjz55JNobGzEHXfcYXfRWA7XL3/5S7S1tWHKlCno6OjA2rVrsWnTJtx3330Bp+tisVgsljPFvyVYLFYiiJ1qlm06++yzsWbNGvz973+HJEn48Y9/jCeffBInnXSS3UVjOVynnnoqHnzwQbzxxhtob2/HUUcdhUceeQQLFy60u2gsFovFslD8W4LFYiWCuE81i8VisVgsFovFYrFYEYr7VLNYLBaLxWKxWCwWixWh2KlmsVgsFovFYrFYLBYrQjmiT7WiKDh48CD69OkDSZLsLg6LxWKxiEsIgaamJgwaNAiyzO+nrRD/r2exWCxWosno/3tHONUHDx5EcXGx3cVgsVgsFstLpaWlGDJkiN3FSArx/3oWi8ViJarC/b93hFPdp08fACpMTk5OxOdRFAU1NTUoKCggGVlgfuanzA+wDZjfOv7GxkYUFxe7/z+xohf/r7dGzE+bH2AbMD9tfsCe//cROdWPPfYY7r//flRWVmL8+PF45JFHMGnSpID7nnzyyfjvf//rt/3ss8/Gm2++aeh6WhpYTk5OVP9ohRBISUlB7969SaaWMT/zU+YH2AbMbz0/RTvGSvy/3hoxP21+gG3A/LT5AXv+35t23V944QUsWrQIS5cuxRdffIHx48dj1qxZOHToUMD9165di4qKCvff119/jZSUFFx88cVmLx21JEki3VeL+ZmfMj/ANmB+2vxURL2emZ82P8A2YH7a/IA9NjDtVC9fvhzXX3895s+fj7Fjx2LFihXIysrCU089FXD//Px8DBw40P337rvvIisryxanWlEUVFRUQFGUuF87EcT8zE+ZH2AbMD9tfiqiXs/MT5sfYBswP21+wB4bmHKqOzs78fnnn2PmzJmeE8gyZs6cic2bNxs6x5NPPolLL70U2dnZQffp6OhAY2Oj1x8At2GEEBBCuLcZXZckCb1793Zfx+ix2jVjvR4Jk1mOnJwc97ZkYDJTT0IId0phsjCZWQfgToNJFiaz9SSEcL+5TBamWD0DncJkZl17c23VM5CVmJIkCTk5OWSjNMxPmx9gGzA/bX7AHhuYcqpramrgcrlQWFjotb2wsBCVlZVhj9+6dSu+/vprXHfddSH3W7ZsGXJzc91/2migdXV17qW2Xltbi4aGBnf5NAe8uroazc3NAICqqiq0trZCkiQ0Njaio6MDAFBeXu5eLy0tRVdXFwCgpKQELpcLiqKgpKQEiqLA5XKhpKQEANDV1YXS0lIA6guA8vJyAEB7ezsqKioAAK2traiqqgIANDc3o7q6GoDa2b2mpgYA0NDQgNra2qiYAKCiogLt7e1hmRRFQWZmJkpLS5OGyUw9HTx4ENnZ2ejo6EgaJjP11NTUhLa2NkiSlDRMZuvp8OHDcLlckCQpaZjM1JMkSaivr0dnZ2fSMJmpJyEEMjIy3BzRMrESU5IkITs7m+wPSuanzQ+wDZifNj9gjw0kYeJ1+8GDBzF48GBs2rQJU6ZMcW9fvHgx/vvf/2LLli0hj//FL36BzZs346uvvgq5X0dHh/sHEuAZda2urg55eXnuCIEWbZIkydC6EALl5eUYNGgQUlJSDB8ry7I7ohLL9UiYzKwLIVBZWYnCwkKkpqYmBZOZenK5XKiqqsLAgQMhSVJSMJlZd7lcqKiowKBBg9ztwelMZuupu7sblZWVGDRokPt7pzPF6hnoFCYz69r/sYEDB0b9DGxoaEBeXh4aGhqiGlSL5VFjYyNyc3OjtqmiqGl/RUVFJEe+ZX7a/ADbgPlp8wPW2sDo/yZTo38XFBQgJSXF/dZek+aohFJLSwuef/553HXXXWGvk56ejvT0dL/tmlEkSfLbZnRdP7S6mWO1H2axXI+Uyei6EAL5+flISUlJGibf9VBlSUlJQX5+PmRZThomM+uyLKNfv37ubcnAZLaeUlJSvGyQDEyxfAY6hcnMM7Bfv36WPQNZiSlJkpCfn0+2jpifNj/ANmB+2vyAPTYw5bqnpaVh4sSJ2LBhg3uboijYsGGDV+Q6kF566SV0dHTgyiuvjKykFkiSJGRmZpK9yZif+SnzA2wD5qfNT0XU65n5afMDbAPmp80P2GMD0/HwRYsWYeXKlXj66aexc+dOLFiwAC0tLZg/fz4AYN68eViyZInfcU8++STmzJmDfv36RV/qCKUoirs/MUUxP/NT5gfYBsxPm5+KqNcz89PmB9gGzE+bH7DHBqbSvwFg7ty5qK6uxp133onKykpMmDAB69atcw9eVlJS4pe7vmvXLnz00UdYv369NaWOUJIkoX///mTf3DA/81PmB9gGzE+bn4qo1zPz0+YH2AbMT5sfsMcGpgYqs0tWDV7CYrFYLJYV4v9L1ottymKxWKxEk9H/TaSGhFMUBQcOHCCbDsH8zE+ZH2AbMD9tfiqiXs/MT5sfYBswP21+wB4bkHKqJUlCUVER2XQI5md+yvwA24D5afNTEfV6Zn7a/ADbgPlp8wP22MB0n2onS5IkpKWl2V0M28T8zE+ZH2AbMD9tfiqiXs/MT5sfYBswP21+wB4bkIpUK4qC/fv3k02HYH7mp8wPsA2YnzY/FVGvZ+anzQ+wDZifNj9gjw1IOdVSRweGVFZC+u9/7S6KLZIkCUOGDCGbDsL8tPkBtgHz0+anIur1zPy0+QG2AfPT5ofLBem55zAkK4vTv2MlqaICqVOmAFlZQEuL3cWJuyRJgizLZBsZ89PmB9gGzE+bn4qo1zPz0+YH2AbMHyG/EEB3N9Crl/939fVA795AagK4jg0NwBVXAJMmARdeCLz9NnDZZcCaNcDu3cDGjZB27kTKb38L6d5741YsUpFqpW9fdaW1FWhvt7cwNkhRFJSUlJBNB2F+2vwA24D5afNTEfV6Zn7a/ADbwHL+7m7gjTeAtjb/74QA/vUv4P/+DzhwwJrrRSk3f1ubWr6tW4HPP1fLv3Qp8NOfAnffDWzaBJx6KjB5MnDyyUBxMZCZCdx0k3fw8dlngcJC4JxzYlvw8nJg9Wpg0SLggw+C7/eXvwBvvqmyHHccsHgxMHQocPPNwN/+BuzcCdG3L2pTU+PaBmjNUy0ERK9ekFwuteIGDbKukA6RoiiQZVLvUrzE/LT5AbYB81vDz3MqWy8rbcr3OfNT5gccYgMhgM5OIC0N+Ogj4JhjgPx8S05tKf+CBcCKFcDcucDzz3t/d801wNNPq+t9+gDr1gEnnmj83Hv3qs7qaacBf/6zGg3W6/33gUceAe64A/jxj/2Pv+gi9RybN6uZuD1SVq+GfOWVnuzctDTgN79RnWlNqanqC4NAKiwEbrgBqKgA/v53z/YvvgB+9CPjfEbU3g5cfTXw4ouebSNHqlFnvdavV6PRK1d6b8/PB2prgX79gJ//XPXvrroKSp8+cf1/T8qpFkIAAwZAqqkBduwAxo2zsJSJLyEEXC4XUlJSSKbEMD9tfoBtwPzW8bNTbb2s/F/P9znzU+UHHGSD889Xo6XnnKNGe884A3jnnahPayn/+++rDq+m9euB009X1w8dUp1PQH0h8M03wMUXezuHn34KjBoF5OYGPv+DDwK33qquT5yo7q8v8wknAJ99pq6/9RZw1lme7777DhgzRl3fsEGNOgMQXV3A0UdD+uEH72vl5qqp0z/+seocA8BPfgIsWaJGsfv2Bbq6gF/+EvA9VtP113s72dFozx617uvrgepqQJaBY48Ftm9X11tbgfR0dV9FAQYPBior1c9HHgk88QRQU6OmgL/3nmqr/v1VG9jw/z7BX2FZKyEEujVjHD5sb2FskBACZWVlcMB7lJiI+WnzA2wD5qfNT0XU65n5afMDDrLBf/6j/h7/17/Uz+vXq1HXKBWU/7//Bc49FygpMX6ye+5Rl5rzfNttQEcH8L//qY4goDqjjzyirm/Z4jn28cfVfr/HHOO9Xa+vv/asf/656jxq+uEHj0MNqA779u2ez2vXetY1JxmAeP55SD/8ANGnj3rd665Tv2hoUJd33w2sWqVG2V9/XX25MXeu+lLjnHNUZ/2pp4BLLwXmzAE+/FD9A9RU8ObmYNby1yuvAC+9FDgi/vzzqmNdXQ1kZ6uO8ZdfqhF/RQG+/96bT3Ooc3KABx4AZs5Uy5iWBpx9ttuhBuxpA6ScalmW0UtrFLW19hbGBsmyjOHDhyd+OlCMxPy0+QG2AfPT5qci6vXM/LT5AQts8NZbaorvV19ZWzBfBRoQ68knoz5tQH5FUfsNv/kmsHy58ZNpEdsnn1TTpbdvB8aOBU46CfjHP9Tv8vKA449XI8wlJarzV1qqplsDapfTs88OPJ7Tjh3en/VO9Usvqcvp09XoeEuLGpXVHNQgTrW8YgUAQLr9dtWpP/ts72v85CdquvU//+nliLqVlgbMnw8895zqFE+fDkybBhQUqNHjfftCGEynmho1Pf2SS9QIdE2N9/eff64uZ89W7XzKKaoNR41St+/erbKuWeOJjl90kfpy4IILQl7ajucAqSeOEAJKXp76gaBTLYRAZ2dn4r+5jJGYnzY/wDZgftr8VES9npmfNj9ggQ3OOQfYtk2NUsZSWmrvq6+qjhOgRlCjHFwqIP+773rWs7ONngioqlLXx4zxpIFrEdQnnlCXffuq0dVjjlE/b9kC3HijGtH9yU/UPr61tZ5oryaXC/j2W+9tevYXXlCXl1+urvfvr177zTdVp11zSgGPU93eDvHpp2rxL71U3abv43300ZH1W5ckT7Te1zn2lculOt7l5aoNATX6/dFH3vtp5V+0yNu515zqPXvUFwsXX+zpR33uuYaKa8dzgJxT3ZqZqX4gmv5dUVFB9h8N89PmB9gGzE+bn4qo1zPz0+YHLLRBrANQLpe6HD8eOO88NRJcWak6Y4Carh2sb28IBeT/618965ozH07NzZ4RvwsL/aOjGRnqUgvYTZ6sLm+9VU1t79VLjWZr/aDfftv7+B9+8B9RXHOqt2xRHeVevdTodN++aro2oDqYmmPfr5+63L0baGoCvvwSUlcXXAUFEMOGecp+1FHquplB1HxVUKAutbT3YLr9dvV6q1Z5b9fStwH1ZUVZmeqs+w58po9Ub97s/Z2+T3kI2fEcIOVUy7KM3kOHqh8IRqplWcawYcPIpkQxP21+gG3A/LT5qYh6PTM/bX7AQhvEejoizalOTVVTjo84Qv28Z4+aZn3yyeqAVEZ1+DAwdy7k8eMx7M9/9vBXVHg7tF1dxs6nOYHZ2erf7Nneg4jV1alLbcpezanW+oX/5jdq9DqYU631p9acSMBj84ceUpeXXQYMGKCua32j335btREADBsGDBmiRoS3bQM++QQAkHLiiZBTUjznPf98dXneecbYA0mLJodzqr/8Ul2uW+e9Xe9Ua1Hqo49Wo/x6jRypLnfv9nRB6NtXHdRNi5aHkR3PAVJPHCEEurSBygg61UIItLe3k317y/y0+QG2AfPT5qci6vXM/LT5AQttEGsban2DNedPc6b27PHqI2xYy5apI29//TXw+OMQmtO7Zo03i1GnWkv91hy5gQPVqa18pUWqTztNjYLLsjoX9O9+p26fOVN9cbBrl/fgW5pTfdxxnm2Kor4E0NLhb77Z892oUWpUX1GAjz9Wt2Vleaba+vJLt1Pddfzx3vV/333q6ORh+iKHlFGn+tAhdek7JVZFhWddG4Bt4kT/47WXDLt2eZzq999X08QNyo7nADmnulkbFIFo+nd1dTXZfzTMT5sfYBswP21+KqJez8xPmx+w0AaxjFQL4Tl/IKdaS602orIy1Ql98EHvS5SVqSva3NJpaerSrFM9cKBn2x//CLzxhvd+WqT6yCNVR7K8XO0LrDHk5qp9qwHggw/UZXu7p1wTJnjOpShqf2SXS43c678DPP2hNcc2K8szRfC337pHGa8bNcq7/tPT1QHWopFZp1qrX8330iLVq1erL0AAT3RfL+0+qKxUswFSUz1ThxmUHc8BUk61LMvoq/UpIBiplmUZxcXFZFOimJ82P8A2YH7a/FREvZ6ZnzY/YKENYulU68+dmqougznV4RyjmTPVwawANZLbM2CYXFGhOribNqlp21dcoe6jRcgbG9XobTD5Rqo1+Q70pUWqAWDoUG8nXNO0aepy0yZ1uWSJeu3+/T19pQHVLlr5Ar1YyMpSl9pgYZmZHmf5gw+AAwcAScKAs8+2vg0YcaoVxf97LRJfUaGy/eIX6kuFM88Err3W/xx5ed4p8aNHG+8H3yM7ngOknjhCCHRoI/4RdKqFEGhrayP79pb5afMDbAPmp81PRdTrmflp8wMW2iCWTrV+3uJwkerOzuDnqapS04Q1/b//BzFkCICeSLUWrR46VP0DPJHqyZPVKK9+FG29tMiqr1OtDQ6mSYtUh9LUqery44/V6aEeflj9/NRT3k64onjKp71s0MvXqc7K8kRxe9KtxbhxaEtNtb4NGHGqa2v97xu9U71vnzoAXFaWGvHXeHy1eLFnXbsvTMiO5wA5p7pBa7hEnera2lqy/2iYnzY/wDZgftr8VES9npmfNj9goQ1i6VRrg5QB/pHqffu857BubQ1+nu3bPcd2d6vRz0GDAACivNzDkJrquU5Xl3rO775TP2/YEPjcgdK/gcic6ilT1OWuXWqkFgDuvFOdIkofTXW5PC8cAs3jrQUH9U716NHeA6hNnhybNmDEqdZSv/XSnOqqKk8f6WOO8bxMCaR58zzro0ebKyfseQ6QcqplWcYA7W0OwT7Vsixj8ODBZFOimJ82P8A2YH7a/FREvZ6ZnzY/YKENYumQBIpUDx2q9nvu7PQe1MqIUz1hgvs8Uk+kWj540OO8y7LHSe3q8gz0BagjaAdSsPTvvDxvJ1af/h1M/fp5j2Q+dy7whz+o65LkOZ/RSHVDg7rMzFS3DR/u3kWaMiU2bSBSp/rYY9VlV5c6TRrg6QceTL16qXV01VXALbeYLqodzwFSTxwhBFq0nPy2Nv+54ZJcQgi0tLSQfXvL/LT5AbYB89PmpyLq9cz8tPkBC20Qr0i15lSnpHgcT/3I0Uac6vHj3ZtEoEi1r1P9/vuecwQbuCyYU52S4h2dNhKpBjzOJQA89pi3Y645f/o+1YGcai1SrUlzsnUDeYlJk2LTBjSn+vBh7/rTK5BTPXiwJ7r/3nvqMpxTDahzav/rX/6ZAQZkx3OAnFPdCEBojVcbap+IhBBobGwk+4+G+WnzA2wD5qfNT0XU65n5afMDFtogljYM5FQDnkHAtEgsEDoItm2bugzgVEPvVKekeDvV+pTvYE51sD7VgLejZyRSDQBLl6pp4O+84+8oBnKqA6V/+/ZB1j5rg5Xl5ECMHh2bNqCVWYjg3WgDOdUDBnhS6LX+7/oXDDGQHc8BUk61LMsoGjQIktZgiaWAy7KMoqIisilRzE+bH2AbMD9tfiqiXs/MT5sfcIgNNMdRkrz7FGvreqc7UKT600+Bo47yjN6tc6rl4mL11OXlgdO/Gxu9BycL5FQLEbxPNeDtFBuNVP/oR+ro32ec4f+d3qk2kv6tKTNTXWrzPU+fDjk1NTb136uXhzVYCrivU52Sor50KCry3m4kUh2F7GgDCdzarJcQAk1NTRCaU6118iciNz/Rt7fMT5sfYBswP21+KqJez8xPmx9wiA00Z9d3sCqjTvVrr6kDmgGqg9vTjxrQpX8fOgR0dHjOqznVdXXeqe2BnOrmZk+E3KpIdSgZjVQHS/++5BJg1Srgb3+Lbf2H61ft61T376+y6V9M9OsX+EWFhbKjDZBzqltbWz0NTxtmn4g0/oR+yMZQzE+bH2AbMD9tfiqiXs/MT5sfsNEGtbXAsmVASUn4fYP1G9acS/1AZoHSv/Xp4U884dU/WfTrB9GrFyQh1BRwwDv9u6XF+1yBnGot9Ts729+RBTxOdWam6TmUAyrSSLX2WZaBq68GiotjW/9GnWrtRYO2f0/2AADgppu8+5PHQHa0gQC1lbySZRmFhYWeEfJ++MHW8sRbbn6iYn7a/ADbgPlp81MR9Xpmftr8gI02uOEG4KWXVCd3//7Q+waLVGvOVrhIteZU//nPwEUXeX0lp6aq02odOOBx8PWRaiNOdbBByjRpTrUVUWog+oHKvE4Vw/ovKFCXwbJ9Nad6wgRg40aPU71ggTqq++zZwPTpsSmbTna0AXKR6oaGBght6PxwDT7J5OYn+vaW+WnzA2wD5qfNT0XU65n5afMDNtpAG9n5wIHw+2pOc7BItVGnOjfX7yshBLo1509zjmXZcy0zTnWwNGXNqTbanzqcIh2oTOtTrVNM61+7Xnt74O81p/pnPwOOPx649lr1c3Ex8MADcXGoAXvaAKlItRACHR0dEMOGQQJIOtUdHR0QQkCKcdpFIor5afMDbAPmp81PRdTrmflp8wM22iAjw/i+muMYrE+10fTvIE61op1Hc5jNpn8bjVTHwqmOJP1bp5jWv34E9UDS0sJPOEEdTM4m2dEGSDnVsixjwIABwIgR6gZiTrWbn6iYnzY/wDZgftr8VES9npmfNj9gow0CRE2DKtxAZXqn2mSkWpZlpGkOvub86dO/fc8Xqk91MKf6Jz8B0tKsi7xqdnC5IhuoTKeY1n84p1qLYJu5F2IgO9oAufTv+vp6T/p3aal3o01yufmJpkQxP21+gG3A/LT5qYh6PTM/bX7ARhtE4lT7RmPN9qkOlv6tje4dyKn2VSTp3xMmAPX16sBsVijaKbV0imn9p6Wpy2BOdagXAnGUHW2AnFPd3d0NMXCgelN0d3tGBSQgNz/RfzTMT5sfYBswP21+KqJez8xPmx+w0QZWpn9H6VQrvhFvffq3ryJJ/wasjcYaHajMYPp3zOo/VKRaiNBlj6PsaAOknGpZllFQUKCOCkhwsDI3fxwnQk8kMT9tfoBtwPy0+amIej0zP21+wEYbmHGqww1UFqpPtRDh07+1aa4ijVSHS/+2WtHOU+11qhjWfyinWv8ixGan2o42QOqJI4RAbW2t+tZCm1aLkFPtxU9QzE+bH2AbMD9tfiqiXs/MT5sfsNEG+shtuGubGajMN1Ld2upx4IJEqjutSv+2w6m2IP07ZvWv2bCz0/87fZ0lQKQ63m2AlFPtJaJzVbNYLBaLxWKxWJZL7+AFGrFbr2jmqdai1Ckp/pFbTaFG//aVr1MtRPg+1VYr0im1AkSqY6pQkeoEcqrtECliSZKQn5+vfjjySHW5b599BYqzvPgJivlp8wNsA+anzU9F1OuZ+WnzAzbaQO8ENjaGdvjMzFPt66BrTnVOjscJ10mSpNCjf/vK10FsbvZcM9Ei1amp6rhQWqQ4QKQ6pvXvEKfajjZAKlKtKApqamqgKAowcqS6cc8eewsVR3nxExTz0+YH2AbMT5ufiqjXM/PT5gdstIHeqWpsNLZvJOnfIfpTAyp/h3a83qkO5uj5Oohaf+rs7OCRcKtldKAywPOyIiPDc5xOMa3/UKN/J5BTbUcbIOVUS5KE1NRUdRLwo45SNxJyqr34CYr5afMDbAPmp81PRdTrmflp8wM22kDvaIVzqq1I/w7iVEuSBElz6iJJ/453f2rAePo34HH0g4w+HtP6NxKpluWAzn48ZUcbIJf+nZeXp37QnOraWvWPQKqQFz9BMT9tfoBtwPy0+amIej0zP21+wEYbmHGqg0VjzaR/h3Cq3aN/6x29RHaqtZcLLlfo9G/AE6kOkl4f0/oP5VSHK3ccZUcbIBWpVhQFhw4dUlMBsrOBQYPUL4hEq734CYr5afMDbAPmp81PRdTrmflp8wM22kA/InSkkWqL0r/bNQcvkj7VLS3qMicnSOFjoEgi1UGc6pjWv5HRvxPAqbajDZByqiVJQnp6uicVQOtXvXevfYWKo/z4iYn5afMDbAPmp81PRdTrmflp8wM22iCS9G8jkeoI0r9l34hqoPRvzaH3dao1h1HrPxwPGR2oDPA40yHSv2NW/0bSvxPAqbajDZBzqnNzcz0GJtav2o+fmJifNj/ANmB+2vxURL2emZ82P2CjDSJJ/w7WpzqKSLXh0b+1iG+iOdVGByoLkf4ds/p3kFMd7zZAyqlWFAVVVVWeVABiI4D78RMT89PmB9gGzE+bn4qo1zPz0+YHbLSB3tFqagq9b7j07yj6VCuKgraODu8ymXGqtc/B0q9jIYvTv2NW/0ZG/04Ap9qONkDKqZYkCVlZWf7p37t321eoOMqPn5iYnzY/wDZgftr8VES9npmfNj9gow0i6VMdLP07ykh1qjZQWaj070SNVFuQ/h2z+jcSqY7ny4ggsqMN2P8qIY6SJAl9+vTxbBg3Tl3u2KE2oHg2Hhvkx09MzE+bH2AbMD9tfiqiXs/MT5sfsNEGVqR/B4pUd3Wp+2tOpgGnupevUx1onmqnRqoNpH/HrP4dNPp3vNsAqUi1oiioqKjwTv/u2xfo6AC++srewsVBfvzExPy0+QG2AfPT5qci6vXM/LT5ARttYMVAZYH6VAPeKeAG0r9bA6V/+86f7NRItYH075jVv4NG/453GyDlVEuShJycHE8qgCQBkyap61u22FewOMmPn5iYnzY/wDZgftr8VES9npmfNj9gow3MpH+biVQD3ingkUSqtevoo7+J5FRr5TMyUFkYpzqm9e+ggcri3QbIOdXZ2dneBp48WV0Scar9+AmJ+WnzA2wD5qfNT0XU65n5afMDFthAjtA9iCRSHSunWhv9W3P0tPMacaoTPf37gguAiROBSy4J+HVM24CDnOp4PwdIOdWKoqC8vNw7FYCQUx2Qn5CYnzY/wDZgftr8VES9npmfNj9ggQ3i6VQbGagMMOVUK4qCZm1/o0713/8OfPyx+tnO9G+XK3z69wknAJ99Bpx8csCvY9oGHOJU2/EcsJ86jpIkCfn5+d5vLbT07927gbo6tY91kiogPyExP21+gG3A/LT5qYh6PTM/bX7AAhtEepyV81T7RqpbWtSlEIYi1Rmaw6wpVPp3WRnwi1+o6+3tHg6756mOMFIe0zZgZEqtBBn9O97PAVKRakmSkJmZ6W3gggJgxAh1fetWewoWJwXkJyTmp80PsA2YnzY/FVGvZ+anzQ9YYINII9X6PtW+02ABanT13nvV/cymf2tOdVubx3kLNaWWr0McKlKt17p1Hg670r+jHEU7pm3AQaN/x/s5QMqpVhQFpaWl/qkARFLAg/ITEfPT5gfYBsxPm5+KqNcz89PmByywQSROtRDeKduBrr1kCfD73wMffBA8VThY+ndzs7rUotSyDPTuHbAoiqKg0depN+pUP/OM/aN/R5lGHdM24KDRv+P9HCDlVEuShP79+/u/tSDiVAflJyLmp80PsA2YnzY/FVGvZ+anzQ9YYAO9Uy2EsWN8neBAzozmGLe0hI9U+55Pi1RrTnVOTtA0dUmSkGUm/Vuv//wHqK313zfWsjj9O2ZtwCF9qu14DthPHUdJkoQMbTRAvTSneutW9eGRpA/ioPxExPy0+QG2AfPT5qci6vXM/LT5AQtsoHeqXS5jTpKvkxXIqda2KUr4eaqDpX+H6U+tnkJCqjalliajkeqODmDPHnU9UeepDqOYtgEHOdXxfg6QilQrioIDBw74pwJMmKA2nJoa4IcfbClbPBSUn4iYnzY/wDZgftr8VES9npmfNj9ggQ30wSVf5zaYfNOBwznVZuep9k3/DuFUK4qChqamwOfVO3yBnGoA0I61O/07wkh1TNuAQ5xqO54DpJxqSZJQVFTknwqQnq461gCweXPcyxUvBeUnIuanzQ+wDZifNj8VUa9n5qfND1hgA32k2jcNO5gijVQHc6p9ZTJS3dv3e6Pp34DHqY5n+rdWPosi1TFrA9qLhu5u/64BCeRU2/EcIOdUp6WlBTbw9Onq8v3341uoOCokPwExP21+gG3A/LT5qYh6PTM/bX7AAhv4pn8bkVXp3xY51Sm+DrHR9G/AExW3O1IdhVMdszagt5/vC5cEm1Ir3s+BiJzqxx57DMOHD0dGRgYmT56MrWGmoqqvr8eNN96IoqIipKenY9SoUXjrrbciKnA0UhQF+/fvD5wKcMYZ6nL9euODMjhMIfkJiPlp8wNsA+anzU9F1OuZ+WnzAxbYwCqn+n//A845B9i3z7MN8B4pPNg81b4ymf5d5ztPthmnWpNDByqLaRvQl8k35T+BptSy4zlg2ql+4YUXsGjRIixduhRffPEFxo8fj1mzZuHQoUMB9+/s7MTpp5+O/fv3Y82aNdi1axdWrlyJwYMHR114s5IkCUOGDAkeqU5PVyeA/+67uJctHgrJT0DMT5sfYBswP21+KqJez8xPmx+wwAaRpH/7OlguF/DUU8BbbwGvvKJui2OkOqdvX++NZtK/NdkRqXa5LEn/jlkb0Nuvo8M7EJlg6d/xfg6YdqqXL1+O66+/HvPnz8fYsWOxYsUKZGVl4amnngq4/1NPPYXa2lq8+uqrmDp1KoYPH44ZM2Zg/PjxURferCRJgizLgQ2cmelJAV+/Pr4Fi5NC8hMQ89PmB9gGzE+bn4qo1zPz0+YHLLCB3lGKJlKtbdOcLTMDlfnKpFMtBXPW9U5hRkboObntTv+OYkqtmLUBfZlGjwZOPdXzOcGc6ng/B0w51Z2dnfj8888xc+ZMzwlkGTNnzsTmIAN8vf7665gyZQpuvPFGFBYWYty4cbjvvvvgCtFIOzo60NjY6PUHwB3CF0JA9DR4RVEMr2sjwWnX9tunh0u8957Xdu2asV6PhMnMusvlQklJCbp7bvpkYDJTT93d3SgpKYHL5UoaJjPrLpfLPRJisjCZrafu7m63DZKFydJnoAOZzKxr/FY9A1mJKUVRUFJS4q4ramJ+2vyABTbQHxfNQGXaebTf/NpnxcRAZdrUWCbTv+u1/XzPq3cK09JCO652pX9HGamOaRuQZU9Zq6uBjRvV2ZNWrACqqtTtCeBU2/EcMOVU19TUwOVyobCw0Gt7YWEhKisrAx7z/fffY82aNXC5XHjrrbdwxx134MEHH8Q999wT9DrLli1Dbm6u+6+4uBgAUFdX515q67W1tWjoaTg1NTVuB7y6uhrNPQ2wqqoKra2tkGUZvXr1QmdPikp5eTk6OjoAAKWlpejuma/atXWr2/HSKkRzSAGgq6sLpaWlANQXAOXl5QCA9vZ2VFRUAABaW1tR1XNzNTc3o7q6GgDQ2NiImpoaAEBDQwNqeyaYj5QJACoqKtDe3h6QqaunYZaUlEAIgSFDhqCsrCxpmMzUU0VFBYYOHYrOzs6kYTJTT83NzcjKyoIsy0nDZLaeamtr0bdvX8iynDRMZupJlmWkpKS4OZKByUw9AcDgwYNRVlZmCRMrMSXLMoYOHQo5VAQqicX8tPkBC2ygf2lodkot/SjWeidavxQi/DzVmvr0UZcmItWyLCOvoMB7Y6D073BOtR2Rav2o2hE6pzFvA752ueACYMEC4IEH1M8J4FTb8hwQJlReXi4AiE2bNnltv+2228SkSZMCHjNy5EhRXFwsuru73dsefPBBMXDgwKDXaW9vFw0NDe6/0tJSAUDU1dUJIYRQFEUoiiKEEMLlchleVxRFdHR0CJfLFXifpiYhZFkIQChlZe7t2jVjvR4Jk5l1l8slurq63HWRDEz69XBl6e7uFl1dXW5bJAOTmXWXyyU6OjrcDMnAZLaeuru7RWdnp3vfZGCy9BnoQCaz652dnZY8A+vr6wUA0dDQIFjWqKGhwRKbKooiurq63HVPTcxPm18IC2yQlyeE6toJsW+fsWM+/ljdPytLXUqSEBdfrK7/4Q/qPkcdpX5euVKIa69V1++91/s8f/iD59qAEEccoS5POEH9/pRT1M+rVwctiqIoovuBB7zPs2yZ+uUll3i2lZYK0bev9376v08+MWm4KHTZZeo1//Qnz/V7/B6zinkb6NMnuM0AIf7v/2JzXROy0gZG/zeZct8LCgqQkpLifmuvqaqqCgMHDgx4TFFREUaNGoUUXXrHmDFjUFlZ6Y4Y+yo9PR05OTlefwDcbxskSXLnyOvz5cOtCyFw8OBB93X89undGxgzRr3G55/7XTPW65EwmVkHgLKyMve2ZGDy5QtXFi1ClSxMZtYB4ODBgxBCJA2T2XqSJAnl5eUQQiQNk6XPQAcymVkXQqC8vNyyZ2Cyy+xMHw8//DCOPvpoZGZmori4GDfffLM7myCeEkKgrKyMbIo+89PmByywgf44s+nfGRmecwRK+9aWRtO/I4hUCyFQr8017XtefRTVN1LtW5Z4Rqq1a/dkXQGIOOIb8zYQLi0+ASLVdjwHTDnVaWlpmDhxIjZs2ODepigKNmzYgClTpgQ8ZurUqdi7d69XTvvu3btRVFSEtHjerFB/VA0fPtzLyfDT8ceryx6nOplkiD+Jxfy0+QG2AfPT5jcjszN9rF69GrfffjuWLl2KnTt34sknn8QLL7yA3/72t3EuOdcz89PmByywgb4fqtmByrQ+0Ppt+rRv7XOwQa0scKplWUa//v29NxpJ//YdMdyO9G99wDHCPt0xbwMOcKrteA6YvtKiRYuwcuVKPP3009i5cycWLFiAlpYWzJ8/HwAwb948LFmyxL3/ggULUFtbi1/96lfYvXs33nzzTdx333248cYbraMwKCEEOjs7Q7+10Jzqzz6LT6HiKEP8SSzmp80PsA2Ynza/GZmd6WPTpk2YOnUqLr/8cgwfPhxnnHEGLrvssrDR7ViIej0zP21+wAIbGIlUV1YCf/+7x9nVnEEtUq0/1jdirY9ih5unWnOqTQxUJoRAty+75lyZcartGKjMokh1TNuAA5xqO54Dpp3quXPn4oEHHsCdd96JCRMmYNu2bVi3bp178LKSkhL3AC8AUFxcjHfeeQeffvopjjvuONx000341a9+hdtvv906CoMSQqCiosKYU/3pp94PlSSQIf4kFvPT5gfYBsxPm9+oIpnp48QTT8Tnn3/udqK///57vPXWWzj77LODXidWM31o3Ry08xg9VrtmrNcjYTKzrigKKioq3KP8JwOTmXpyuVyoqKhw2yIZmMyuB7OB4XL5RKoD7nPeecAvfgGxcKG6XXMGdZFqoYtUCyEgtLK4XBDaLAwpKd4cQZxq0dICCAGhc6qDMblcLtT7DiYpy+o+OodP9OoV0qlWUlPj9owQPdxC51QrPd/58ln5DIyovAacarvbk6Io7u5+VtVTOEUUE1+4cCEOHDiAjo4ObNmyBZN7Rs0GgI0bN2LVqlVe+0+ZMgWffPIJ2tvbsW/fPvz2t7/16mMdL8myjGHDhoVOBZgwQW1g1dXADz/ErWzxkCH+JBbz0+YH2AbMT5vfqCKZ6ePyyy/HXXfdhWnTpqFXr14YMWIETj755JDp33bN9JHsI94LIVBcXEx6po9hw4aRnemjsbERtbW1GDZsGJqamiJj0jsR3d2BmXoyOqWe3/w1PeUSQZzqrq4uuHo+d3d2oq3Hdl09L4E0pmYt8q2px6mW2tuB1lZI2jlzc0PO9JGlRbg19YwJ1a05r5KE9q4ub6c6L8/rkPJDh+L2jOjo4erQXgb06oXaurqEnOlDhItEp6ba3p6E8IwfFG09HT58ODSvJuEAWTkiaFtbW/iR4H7yEyEAIf71r6iul2gyzJ+kYn7a/EKwDZjfOn6r/i8loiKZ6eODDz4QhYWFYuXKleKrr74Sa9euFcXFxeKuu+4Kep1YzvTR0tJiaJR7/bp2TaePeO9yuURbWxvpmT7a2trctkgGJrPrwWxguFzp6UI/AnbAffSjPQshXM88o65PmuTerkybpq7fcot6/aIidftDDwnl/PPV9RUrvDn+/Gfvc//qV571/fs96z0zeQTi6O7uFh0rVnif57HH1H0WLVKvm56u7j9+vGefuXO9jnFVVsbtGaFcf71armuuUa+fmRnVTB9Gn4ERlXfcOG/b+v7ddZft7cnlcomWlhb3taOpm7q6OkP/70m9rhdCoLq62p0iEFQnnqguN22KfaHiKMP8SSrmp80PsA2Ynza/UUUy08cdd9yBq666Ctdddx2OPfZYXHDBBbjvvvuwbNmyoKlzsZzpQx9ZSLZR/I1wVFdXWzbKfSIwmaknSZLcUaZkYYqk7IFsYLhcPunfwfbXS9b6T+v6VEu6SLUkSZB6zisBnvVevQJyuJWV5elvrB/ROyUlKJMkSWjqiXR6Ciir+/QMPialpan7h0j/ljMy4vaMkHoyeN0267FLrJ+BEZU3XPp3r162tycAOHz4sFfEOtK6MZrdRsqplmUZxcXF4Y0zdaq6/Pjj2BcqjjLMn6Riftr8ANuA+WnzG1UkM31oadd6ad284v0Sg3o9Mz9tfsACG+jbbLCByrKyvD/7TqmlPzbQ1Frad+Gm1JJlIDtbXdf3kw7BJssGRv/WRvYOkf5t60BlUQz2FfM24ICByux4DpB64ggh0NbWZjxSvWMHcOedQE8/AKfLMH+Sivlp8wNsA+anzW9GZmf6OO+88/C3v/0Nzz//PH744Qe8++67uOOOO3DeeefFfQwV6vXM/LT5AQts4BOpDqj8fO/PRqbU0i+184abUiuQUx3GURJCoNO33Nox2vUCTZeVSFNqReGYxrwNOMCptuM5QM6prq2tDW/ggQOBo45S1+++G7Bhns1YyDB/kor5afMDbAPmp81vRmZn+vj973+PW265Bb///e8xduxYXHvttZg1axaeeOKJuJedej0zP21+wAIb+AxUFlD9+nnvH2pKrUBOtZlIde/e6rrmVId5USeEQHOA9G8A/pFqPZ+vUx3PF4LatTQ7RhElj3kbcIhTHe/ngP3UcZQsyxg8eLCxnVetAh5/HFi9GnjuOWD5ciAzM6bli7VM8SehmJ82P8A2YH7a/Ga1cOFCLOyZLsdXGzdu9PqcmpqKpUuXYunSpXEoWWhRr2fmp80PRGkDXyfESKS6ri7ySHW4eaolyXSkWpZl5BtN/9Y71T3jOri/D9B3PGayOP07pm3AAU61Hc8BcpHqlpYWY28tpk4F/v1vYOhQdaL5116LfQFjLFP8SSjmp80PsA2YnzY/FVGvZ+anzQ9EaQOjTrXeGT50yONA9+rlcUaDRaqFiC7920Ckul2L+Pqe19ep1soNeEfZ45n6Dfinf0cZqY5pGwhnmwRwqu14DpBzqhsbG40bWJaBq69W133m3naiTPMnmZifNj/ANmB+2vxURL2emZ82PxClDXyPCZb+rd9eVeXtVGtOr9Xp39ro3wb6VLdqEV/f84aKVOud6ngOUgZYGqmOeRswMPq33bLjOUDKqZZlGUVFReZGgtOc6nffBXomC3eqIuJPIjE/bX6AbcD8tPmpiHo9Mz9tfiBKG/hOgRcsUq3ffuiQJ8KaluZxEDVHO9Do38HSv33LrE//bmgIfIyPTKV/6yPV+tR1uyPVUaZ/x7QNOCT9O97PAVJPHCEEmpqazL21GDECmDZNfQA880zsChcHRcSfRGJ+2vwA24D5afNTEfV6Zn7a/ECUNvB1qoNFqn2dan2k2tepDhWp9nXAfPsx69O/zUSq29v9z6OVDwgcqU4Ep1qLVEeZ/h3TNuAAp9qO5wA5p7q1tdW8ga+5Rl2uWuWfFuMgRcyfJGJ+2vwA24D5afNTEfV6Zn7a/ECUNjDapzpU+rfmIBrpU20k/Vtz0rQoroE+1R2+LwO082qDkWnLJE3/jmkbcIhTHe/nACmnWpZlFBYWmk8FuPhi9e3Vd98Bu3fHpnBxUMT8SSLmp80PsA2YnzY/FVGvZ+anzQ9EaYNI07/NRqqNDlQmSf7nMzD6d1/9lF+AxxE/6yzgrruAe+/1PieQGJFqCwYqi3kbcIBTbcdzgNQTRwiBhoYG828tcnKAiRPV9a1brS9YnBQxf5KI+WnzA2wD5qfNT0XU65n5afMDUdog2EBlvgN/6SO8wfpUWzVQmbaP5gAbmae6rS3weTMzgTvuAMaP9+dIokh1TNuAQ0b/jvdzgJxT3dHREZmBJ01Sl59+am2h4qio+JNAzE+bH2AbMD9tfiqiXs/MT5sfiNIGgSLVN96oOpw7d3pv16SfpzrQ6N8ul+qsa+UxM0+13qnWHHcDfaq7fCPswY5JlD7VvoxROtUxbQNhXmokilMd7+cAKadalmUMGDAgslSAE05Qlw6OVEfFnwRiftr8ANuA+WnzUxH1emZ+2vxAlDYIFKl+/HF1fdky7+369UDp3/rItP68QgQfqCxQ+rfJSLUsy+hbUOC9MdgxSZr+HdM2EGzwOk0JMKWWHc8BUk8cIQTq6+uji1Rv2+a54R2mqPiTQMxPmx9gGzA/bX4qol7PzE+bH4jSBqH6VOsdYP12l8vjnOrTv/Xn1J/XzJRasuzfp9pA+ndTS0vo82pK0vTvmLYB/YuIQC8fEiRSHe/nADmnuru7OzIDjxgB9O2r3uxff2194eKgqPiTQMxPmx9gGzA/bX4qol7PzE+bH4jSBqGcar0zq9/e3e0dYTXjVIeLVAfqU20g/dtveLVgx8yerS6POy4xItWaopxSK6ZtQB9c1EZR1ytBnOp4PwdIOdWyLKOgoCCyVABJ8kSrn33W2oLFSVHxJ4GYnzY/wDZgftr8VES9npmfNj8QpQ2CDVQGeDvVRtK/NQVyqoMNVObbp1qf/m1wSi1ZlpGXn++9MdgxK1cCf/kL8M47qjPoO591vORrsygc05i3Ab1T3bu3//cJ4FTb8Rwg9cQRQqC2tjbytxY33aQuH34Y+OILy8oVL0XN73AxP21+gG3A/LT5qYh6PTM/bX4gShtEm/6tH6hM/73+vMLkPNURRKobmptDn1dTXp76+37gQNWB16LVdkeqo0z/jmkb0DvVubn+3yeAU23Hc4CUUx21zj4bmDtXfTD88Y92l4bFYrFYLBaLxbJOkUaqA02ppSna9G+TfaqDnseIEsWpToDBvoJK36d68WLg4ouBo4/2bEsAp9oOkXKqJUlCfn4+JN/UEjNaskRdvvce0N5uTcHiJEv4HSzmp80PsA2YnzY/FVGvZ+anzQ9EaYNI+1THM/07jIMsSRJyjaZ/+0obrMzB6d8xbwN6p/ryy4EXX1Qj/poS4IWAHc8BUk61oiioqamB4vvAMKPjjgMGDQJaW4H//c+6wsVBlvA7WMxPmx9gGzA/bX4qol7PzE+bH4jSBkbTv30j1dpnswOVRZL+HcZBVhQFdQ0Noc8bTHZFqn2ZonBMY94GAs2CpB/kLQEi1XY8B0g51ZIkITU1Nbq3FpIEnHmmuv7WW9YULE6yhN/BYn7a/ADbgPlp81MR9Xpmftr8QJQ2MJr+7dunWj/vdDin2sw81RH0qZYkCSm+TrFRpzpJItUxbQMnn6wu9Y50gjnVdjwHyDnVeXl50Rv47LPV5dtvR1+oOMoyfoeK+WnzA2wD5qfNT0XU65n5afMDUdrAaPp3sEh1IKfad6AyM5FqSTLdp1qSJOTo05ENHONWovSpjtKpjmkbWLwYWLEC2LnTsy0Bnep4PwdIOdWKouDQoUPRpwLMnKne/Lt2AQcPWlO4OMgyfoeK+WnzA2wD5qfNT0XU65n5afMDUdogkki1r1Pt68D6Rqq7uz3X8XXAfJ0gfaTaYJ9qRVFwuL7e/zxGlChOdZTp3zFtA+npwC9+ARxxhPc2TQngVNvxHCDlVEuShPT09OjfWuTmAuPHq+sO6ldtGb9Dxfy0+QG2AfPT5qci6vXM/LT5gShtEMmUWuEi1b5OtX6gqxj0qZYkCb20NO5g5w2mJEn/jnsb0L+ESACn2g4bkHOqc3NzrTHwSSepyw8/jP5ccZKl/A4U89PmB9gGzE+bn4qo1zPz0+YHorSBr1MdKFKtn2caMN+n2oxTHeHo345P/47CqbelDejtmyBOdbxtQMqpVhQFVVVV1qQCONCptpTfgWJ+2vwA24D5afNTEfV6Zn7a/ECUNvBN/w7kAAdyvCN1qmMwT7WiKKg+fDj0eYMpCSLVtrQBfZ0kwJRadtiAlFMtSRKysrKseWsxfbq6/PprwLfhJqgs5XegmJ82P8A2YH7a/FREvZ6ZnzY/EKUNfJ2Q9nbPuubo6aPX2mczA5WFilSH6lMdbG5rH0mShMzevb03Oi1SHWX6d9zbQAJGquNtA3JOdZ8+fawxcP/+wNix6vqbb0Z/vjjIUn4Hivlp8wNsA+anzU9F1OuZ+WnzA1HawDdSrXeqNcdJn/qtfY5lpDpciriPJElC75wcU8e4lZurLvv0Mba/VbI4/TvubSABnep424CUU60oCioqKqxLBbjsMnX5z39ac74Yy3J+h4n5afMDbAPmp81PRdTrmflp8wNR2iBUpNo3Yqypu9vjKBsZ/VvvVAdyovXS96n2LUcQKYqCqpqa0OcNpsWLgd/+1vMbP17yZYoy/TvubUBffqNZATGUHTYg5VRLkoScnBzr3lpcfbXa2DduBL7/3ppzxlCW8ztMzE+bH2AbMD9tfiqiXs/MT5sfiNIGkUSqAc8gYmYi1bIcON3b93M4x9tHASPVRh29o48G7r0XyM83tr9VsjhSHfc2oLdvArQ9O2xAzqnOzs62zsDFxcAZZ6jrzz1nzTljKMv5HSbmp80PsA2YnzY/FVGvZ+anzQ9EaYNQkWpNgZxqbT8zTnWgaGyoPtWaDPSpzo40/dsuWdynOu5tIAGi03rZYYMEv8OslaIoKC8vtzYVQHOqv/rKunPGSDHhd5CYnzY/wDZgftr8VES9npmfNj8QpQ1COdXad77p34C3oxxuoDItqh3IETOS/h3GQVYUBQcrK00dY7ssHv077m0gwexrhw0SywIxliRJyM/Pt/atxahR6nLXLuvOGSPFhN9BYn7a/ADbgPlp81MR9Xpmftr8QJQ2CJX+rX0XKFKtqVcv45FqI051hJHqvgUFpo6xXRanf8e9DSSYfe2wgf3Ds8VRkiQhMzPT2pMefbS63LNHfWAk2JsavWLC7yAxP21+gG3A/LT5qYh6PTM/bX4gShuEilRrTrV+aitfB9vMQGWBorEW9anOzM42dYztsjj9O+5tIAGd6njbIMHvMGulKApKS0utTQUYPly98VtbgYMHrTtvDBQTfgeJ+WnzA2wD5qfNT0XU65n5afMDUdogVKRaO5/mSGtzOutlpk91IEfMN7IY4ejfpb6/yZ3mVEcRqbalDSSYU22HDRL8DrNWkiShf//+1qYC9OoFHHmkur57t3XnjYFiwu8gMT9tfoBtwPy0+amIej0zP21+IEobmIlUB3Kgox2ozKJ5qvsXFnpvTDCnz08WR6rj3gYSzL522ICcU52RkWG9gR3Srzpm/A4R89PmB9gGzE+bn4qo1zPz0+YHorSBEadai1SnpPg7f0YGKjPbp9p3m4E+1RnE07/j3gYS0KmOtw0S/A6zVoqi4MCBA9anAmj9qhM8Uh0zfoeI+WnzA2wD5qfNT0XU65n5afMDUdrATPp3oP7T0aZ/WzT694GSElPH2C5fxijTv+PeBjIy4nctA7LDBgl+h1krSZJQVFREOlIdE36HiPlp8wNsA+anzU9F1OuZ+WnzA1HawNcJaWvzrAcaqMxIpNrXqdYf7194788Rjv5dVFQEod8vwSKpfrI4Uh33NnDDDao/tHhx/K4ZQnbYgNzo32lpadaf+Nhj1eWGDcD27cD48dZfwwLFjN8hYn7a/ADbgPlp81MR9Xpmftr8QJQ28I1U6z/7pn+npno7f5IU2AkOFqkOFD22aEqttLQ09VitrIkeqbZ4Sq24t4G8vIQKLtphgwS/w6yVoijYv3+/9akAP/kJcN556mT2V1zheQOXYIoZv0PE/LT5AbYB89PmpyLq9cz8tPmBKG0Q6hjtu2CRam3d6vRvk1NqafxekWqnOdVRRKq5DdhjgwS/w6yVJEkYMmSI9akAkgQ8+SSQkwN88w2wbZu157dIMeN3iJifNj/ANmB+2vxURL2emZ82PxClDUI5IYEi1XrH1ahTrTnlMYxUDxkyxPtcxNK/uQ3E3wbknGpZlmNj4P79gRNPVNc/+cT681ugmPI7QMxPmx9gGzA/bX4qol7PzE+bH4jSBr7p34G+Mxup9h39O9o+1Qam1JJlGZKTI9VRpn9zG4i/DRL8DrNWiqKgpKQkdqkAP/mJutyyJTbnj1Ix509wMT9tfoBtwPy0+amIej0zP21+IEobGEn/DjalltFItbZuJFIdaPTvMFFnjV/oz+U0pzrK9G9uA/G3AamBymRZxtChQyHHqmFNnqwuE9Spjjl/gov5afMDbAPmp81PRdTrmflp8wNR2sBMpDpY+ne4gco0GZ2n2rdMYbg0fnekWpL8I+CJJgsj1dwG7LEBKWsLIaAoCkSoB0Y0mjRJXe7ZAxw+HJtrRKGY8ye4mJ82P8A2YH7a/FREvZ6ZnzY/EKUNNOc3MzPQidVltJFqTTHqU+3m187lBOcy0HzfEYrbgD02cMBdZp2EECgrK4udgfPzPXNWv/FGbK4RhWLOn+Biftr8ANuA+WnzUxH1emZ+2vxAlDbQjgnkVPumf/tOqWXWqTbSpzpQ+ncYJ1njdx/nBKfawvRvbgP22MABd5l1kmUZw4cPj20qwLnnqstrrwXWro3ddSJQXPgTWMxPmx9gGzA/bX4qol7PzE+bH4jSBkYi1dEOVOYpaPhtEUSqNX5JO1eij/wNWJ7+zW0g/jYgZW0hBDo7O2P71mLZMuDyy9UHyB/+ELvrRKC48CewmJ82P8A2YH7a/FREvZ6ZnzY/EKUNzKR/RzqlliajfapNzlPt5iccqeY2EH8bOOAus05CCFRUVMTWwGlpwKOPqo1hxw5g167YXcuk4sKfwGJ+2vwA24D5afNTEfV6Zn7a/ECUNjCS/m02Uh1Nn+oIRv/W+N3ncqJTHUWkmtuAPTZwwF1mnWRZxrBhw2KfCtC3L3Daaer6yy/H9lomFDf+BBXz0+YH2AbMT5ufiqjXM/PT5geitIHm/Kan+38XbqAyzRGMZvRvI/NUG0j/HjZsmGf0byemf0cRqeY2YI8NSFlbCIH29vb4vLX46U/V5Zo1sb+WQcWVPwHF/LT5AbYB89PmpyLq9cz8tPmBKG2gHSPL/o61b59qo+nfQngccb0i7VNtIP27vb3dWaN/W5z+zW0g/jZwwF1mnYQQqK6ujo+BZ89Wl19+CVRVxf56BhRX/gQU89PmB9gGzE+bn4qo1zPz0+YHorSBFlGWZSAjI/B3ZqfUAjyOuF5G+lRLkv82A+nf1dXVzh79O8r0b24D8bdBRHfZY489huHDhyMjIwOTJ0/G1q1bg+67atUqSJLk9Zfh20jjJFmWUVxcHJ9UgP79gfHj1fUPPoj99QworvwJKOanzQ+wDZifNj8VUa9n5qfND0RpA81xlqTgkWqzU2oBgZ3qGEWqNX5Hj/4dZfo3t4H428D0lV544QUsWrQIS5cuxRdffIHx48dj1qxZOHToUNBjcnJyUFFR4f47cOBAVIWOVEIItLW1xe+txamnqssEcarjzp9gYn7a/ADbgPlp81MR9Xpmftr8QJQ20Kd/+wbBfNO/YxGptqBPtZvfSZFqk4yhxG3AHhuYvsuWL1+O66+/HvPnz8fYsWOxYsUKZGVl4amnngp6jCRJGDhwoPuvsLAwqkJHKiEEamtr4+9Uv/9+fK4XRnHnTzAxP21+gG3A/LT5qYh6PTM/bX4gShuEilQrCvDNN0BlpfrZaJ9qIPL07wj7VNfW1jp39O/UVP+XCybEbcAeG5i6yzo7O/H5559j5syZnhPIMmbOnInNmzcHPa65uRnDhg1DcXExZs+ejW+++SbkdTo6OtDY2Oj1BwBKT0MXQriNpCiK4XVZllFUVASp50Y1eqx2TdPr06apD4K9e6Hs3x92/0iYzKxLkoTBgwe77RwRk8n1WDOZqScAGDx4MCRJShomM+uSJKGoqAiyLCcNk9l6AoBBgwZBluWkYYrVM9ApTGbWZVnGoEGDoCnasrMSU7IsY/DgwWRTH5mfNj8QpQ20Z1uggcp27wbGjQP+8Af1c7BIdSBnOdL07wj6VGv8jh39O4rUb/VU3AbssIGpK9XU1MDlcvlFmgsLC1GpvbXy0dFHH42nnnoKr732Gp555hkoioITTzwRZWVlQa+zbNky5Obmuv+Ki4sBAHV1de6ltl5bW4uGhgZ3+TQHvLq6Gs3NzQCAqqoqtLa2QgiB/fv3o62tDQBQXl6Ojo4OAEBpaSm6uroAACUlJXC5XFAUBSUlJVAUBS6XCyUlJQCArq4ulJaWAlBfAJSXlwMA2tvb1XnxALS2tqKqrQ348Y8BAE3r1gEAGhsbUVNTAwBoaGhQ36RFwQQAFRUVaG9vD8vU3d2NpqYmHDhwIHKmnkHXmpub1UEgbGYyW08tLS1oa2tLKiaj9dTQ0ICysjIIIZKGKZJ6qqqqghAiqZiM1pMQAt9//72bLxmYzNSTy+VyPwOtYGIlpoQQaGlpIfvig/lp8wNR2kDRRap90799A2hm+lT3PJe9FMNIdUtLi7PSv/VljGKQMoDbAGCTDYQJlZeXCwBi06ZNXttvu+02MWnSJEPn6OzsFCNGjBC///3vg+7T3t4uGhoa3H+lpaUCgKirqxNCCKEoilAURQghhMvlMrzucrlEWVmZ6O7uNnWsds2I1n/5SyEAodx0U9j9I2Eys97d3S0OHjwourq6omMysR5rJjP11NXVJQ4ePCi6u7uThsnMend3tygvL/dqD05nMltPXV1dbhskC1OsnoFOYTKz7nK5RHl5uSXPwPr6egFANDQ0CJY1amhosMSmLpdLHDx40F1X1MT8tPmFiNIGzz8vBCDEKacIMXWquq79DRrk/fmqq4SYO9fz+fzz1XP85jfe+wFC/OUv/tsuvND/+k1N3vvs2ydEVZX3tkceMcSvnHCCuv8RR5i3Q7xVXu7h69s3qlNxG7DWBkb/N5nKLygoKEBKSor7rb2mqqoqDBw40NA5evXqhR/96EfYu3dv0H3S09ORHmDSeS2EL+n6GejD+uHWfdOfzR6rXdfU+k9+AjzyCKQtW8LuH0m5zK4XFRW5t0XMZGI9Hkz69VBlSU1N9eJPBiYz6ykpKV6pr8nAZLaeUlNTvWyQDEyxfAY6gcnsum8bsKLsrMSS1s2BqpifNj8QpQ20SHWg9O+0NO/P8ZpSK4LRv4uKijzHOS39O8pINbcBe2xgKh8iLS0NEydOxIYNG9zbFEXBhg0bMGXKFEPncLlc2LFjhy2VLYRAU1NTfFMBJk9Wl19+CfSkJ9olW/gTSMxPmx9gGzA/bX4qol7PzE+bH4jSBqHSv32d7HhNqWWyT7Wb38kDlUUhbgP22MD0XbZo0SKsXLkSTz/9NHbu3IkFCxagpaUF8+fPBwDMmzcPS5Ysce9/1113Yf369fj+++/xxRdf4Morr8SBAwdw3XXXWUdhUEIId9/quOnII4GCAqCzE9i40TMAhA2yhT+BxPy0+QG2AfPT5qci6vXM/LT5gShtEGqgskCR6kCjfxsdqCyGfapbW1s9xxF0qrkNxN8Gpmtt7ty5qK6uxp133onKykpMmDAB69atcw9eVlJS4pVyV1dXh+uvvx6VlZXo27cvJk6ciE2bNmHs2LHWURiULMvxn85LktRo9ZtvAmeeCVx1FfCvf8W3DD2yhT+BxPy0+QG2AfPT5qci6vXM/LT5gShtECpS7eucpqZ6B4vMDlQWaD/frjWB0r8NjP5dWFjoOb8T0r/1ZbQg/ZvbQPxtENGrm4ULF+LAgQPo6OjAli1bMFlLcQawceNGrFq1yv35oYcecu9bWVmJN998Ez/60Y+iLngkEj2jHsf9zc2FF3rWX3/dtmi1bfwJIuanzQ+wDZifNj8VUa9n5qfND0Rpg1CRas3h1hSPPtWBItUG0r8bGhqcO/q3BZFqbgPxt4ED7jLrJIRAR0dH/G+yn/0M0KYQa2gADh6M7/V7ZBt/goj5afMDbAPmp81PRdTrmflp8wMmbVBWBqxd63GYQw1U5nJ5f7arT7WB9O+Ojg5P1NtpTrUFU2pxG4i/DRxwl1knWZYxYMAAr/T0uGnwYGDMGHX966/jf33YzJ8AYn7a/ADbgPlp81MR9Xpmftr8gEkbjBsHXHQR8M9/qp9DpX/7OtXB+lRHE6m2KP17wIABkEL18U40WRip5jZgjw1IWVsIgfr6evve3Iwbpy5tcqpt57dZzE+bH2AbMD9tfiqiXs/MT5sfMGmDhgZ1+cor2sHq0kikOtr072AOj96xjjBSXV9fT3r0b24D8beBA+4y6ySEQHd3N2mn2lZ+m8X8tPkBtgHz0+anIur1zPy0+YEIbaA51/pIdaTp34Eiw4EGKgsWQdY7mLKslkXvaBvoU93d3e05j9OcagvSv7kNxN8G0b0KcZhkWUZBQYF9BTjmGHX5zTe2XN52fpvF/LT5AbYB89PmpyLq9cz8tPmBCG2gOdX6SLWR9G+9NGcw2ki1LHuupTnTKSmec4Rxkt38WvkIpn9zG4i/DRzw6sY6CSFQW1trf6T6m2/8R1CMg2znt1nMT5sfYBswP21+KqJez8xPmx+I0AaNjerSikh1NH2qtWtrCjQtloFIdW1trXNH/7YgUs1tIP42cMBdlkQaMUJ9QLW2Avv3210aFovFYrFYLBbLP/3bzj7Vvunf2rXCHRfsPE5zqqOMVLPskQPuMuskSRLy8/Mh+Y4sGC+lpgKjR6vrNvSrtp3fZjE/bX6AbcD8tPmpiHo9Mz9tfiBCG0SS/h2rSLX++EDTYoWJVLv5nZT+re83HqVTzW3AHhuQcqoVRUFNTQ0UG1Kv3dKngMdZCcFvo5ifNj/ANmB+2vxURL2emZ82PxChDTRn2kz6d7AptQI5sXGMVGv8wknzVAOeckaZ/s1twB4bOOQus0aSJCE1NdXeNzc2jgCeEPw2ivlp8wNsA+anzU9F1OuZ+WnzA1HawOxAZUYj1WZG/46yT7XGH7I8iSitnBZEqrkNxN8GpJL2JUlCXl6evYXQRgC3yam2nd9GMT9tfoBtwPy0+amIej0zP21+IEobmB2oTD8QVCz6VOtH/w53nPuQHv5ADnkiy6JINbcBe2zgkFc31khRFBw6dCgx0r+/+y7wAyaGSgh+G8X8tPkBtgHz0+Y3q8ceewzDhw9HRkYGJk+ejK1bt4bcv76+HjfeeCOKioqQnp6OUaNG4a233opTaT2iXs/MT5sfMGmD7GzPekeHd6Ta16n2PV+w9G8r+1QHGmwsjJOs8QsnDVQGWBap5jZgjw0ccpdZI0mSkJ6ebm86xLBh6gOssxPYuzeul04IfhvF/LT5AbYB89PmN6MXXngBixYtwtKlS/HFF19g/PjxmDVrFg4dOhRw/87OTpx++unYv38/1qxZg127dmHlypUYPHhwnEvO9cz8tPkBkzbo3duzXlfnPfp3rAcqi7RPtYH07/T0dGeN/g1Ymv7NbSD+NnDIXWaNJElCbm6uvTeZLHtSwHfsiOulE4LfRjE/bX6AbcD8tPnNaPny5bj++usxf/58jB07FitWrEBWVhaeeuqpgPs/9dRTqK2txauvvoqpU6di+PDhmDFjBsaPHx/nknM9Mz9tfsCkDfTp27W1odO/fR3jaKfUMtKnOsL079zcXEihBk5LRFmY/s1tIP42IOVUK4qCqqoq+9MhxoxRl7t2xfWyCcNvk5ifNj/ANmB+2vxG1dnZic8//xwzZ850b5NlGTNnzsTmzZsDHvP6669jypQpuPHGG1FYWIhx48bhvvvug8s3sqVTR0cHGhsbvf4AuOtHCAHR84NfURTD64qioKKiwn1to8dq14z1eiRMZtZdLheqqqrQ3ePEJAOTmXrq7u5GVVUVXC5X0jCZXQ9mg0DXF7o2qtTUQGj7SxJEWhq85PPsVGQZQuewautKAEdGBBqoTJYDc/hEqoUQXtfRzh+Mqbu7G5WVle7Rv7U08ESrp2DcbjtGWF4zz8CYM4Wop1i2J5fLhYqKCi+bRMsRTqScakmSkJWVZf+bm1Gj1OXu3XG9bMLw2yTmp80PsA2Ynza/UdXU1MDlcqGwsNBre2FhISorKwMe8/3332PNmjVwuVx46623cMcdd+DBBx/EPffcE/Q6y5YtQ25urvuvuLgYAFBXV+deauu1tbVo6JlHt6amxu2AV1dXo7m5GQBQVVWF1tZWSJKE1tZWdHR0AADKy8vd66Wlpejq+XFfUlLidjpKSkrcDmlJSQkAoKurC6WlpQDUFwDl5eUAgPb2dlRUVAAAWltbUVVVBQBobm5GdXU1AKCxsRE1NTUAgIaGBtTW1kbFBAAVFRVob28Py6QoCjIyMlBaWpo0TGbqqaKiAllZWe71ZGAyW0+1tbXIyspyr4diEroIcuP+/ejsKXtbezva9FFswDuqDeBwQwP0r80UWYaiKDjccx2vQ4OM/h2QyWegssbGRii6azf22D0YU21trepk9Tin3T1OUaLVk9+918Pd0VPeSO89SZLQ3NyMzs5O+5lC1FMs25OiKGhoaIAkSVEzHT58GEYkCeHbYhJPjY2NyM3NRUNDA3JycuwuTvRaswa4+GJg8mTgk0/sLg2LxWKxTCrp/i/pdPDgQQwePBibNm3ClClT3NsXL16M//73v9iyZYvfMaNGjUJ7ezt++OEHpPT8kF2+fDnuv/9+948WX3V0dLh/IAGqTYuLi1FXV4e8vDx35EGSJCiKAkmSYrouaxExIWK6zkzMlEhMok8fSD3OjPLkk5AqKiD9/vcQ114LLFkC6aijgj4rlOeeU89z6aUAALF6NaTLLoOyejXkK67w2leccAKkTz/1PsGf/wxx663+5S0uBg4ehJBlSC6XWvajj4a0Z4963S1bIE+aFL6ebroJePRRiIsvhvTii4lfT4WFQE0NxM03Q1q+POnvPacwNTQ0oG/fvmH/35OKVGvpEEbD+DGTFqnetcvvrV8slTD8Non5afMDbAPmp81vVAUFBUhJSXG/tddUVVWFgQMHBjymqKgIo0aNcjvUADBmzBhUVla6oyW+Sk9PR05OjtcfoKaaA3D/SNK2GV1XFDXNX/shZ/RY7ZqxXo+Eycy6EMLrRUYyMJmpJ0CNbmk/ipOByex6MBsEur6kS/+W6+shaQ5QSgqkzEyEktyrFyRd/19tXQ4w0JYUZKCygBxa2XRLSfds0c4fjAmAmv6tnbdnmWj15LeufdbsGGF5zTwDY84Uop5i2Z6EEO7uXlZxhBMpp1qSJOTk5Hg1OlukvfWrrwcMphRYoYTht0nMT5sfYBswP21+o0pLS8PEiROxYcMG9zZFUbBhwwavyLVeU6dOxd69e71eWOzevRtFRUVI8+2XGWNRr2fmp80PmLSBftyDcAOV+SrWU2rpz2Ny9O+cnBznjv5twUBl3AbibwOH3GXWSJIkZGdn23+TZWUBPX3H0JPKEg8lDL9NYn7a/ADbgPlp85vRokWLsHLlSjz99NPYuXMnFixYgJaWFsyfPx8AMG/ePCxZssS9/4IFC1BbW4tf/epX2L17N958803cd999uPHGG+Nedur1zPy0+QGTNvB1qrUMSlkG+vQBcnODH2tmSq0gA5UFVCBnONh6ALn5nTr6twVTanEbiL8NSDnViqKgvLw8MVL/bBisLKH4bRDz0+YH2AbMT5vfjObOnYsHHngAd955JyZMmIBt27Zh3bp17sHLSkpKvFKMi4uL8c477+DTTz/Fcccdh5tuugm/+tWvcPvtt8e97NTrmflp8wMmbCCEt1Pd3u4dqU5LU8f+ue22wMcHm1IrkBMbSaRa7xCZiFRr/Nro39Qi1dwG7LFBdK9CHCZJkpCfn58Yb25GjQI2bAC2bQPmzfN+cMRICcVvg5ifNj/ANmB+2vxmtXDhQixcuDDgdxs3bvTbNmXKFHySAINvUq9n5qfND5iwga/D0d3tHakGgNGjgaOPDnx8aqr371fNGTSa/h3M2Q3kDJucpzo/P99zjNOcagsi1dwG4m8Dh9xl1kiSJGRmZibGTaY9oB5+GDjjDEA3AmqslFD8Noj5afMDbAPmp81PRdTrmflp8wMmbOA7j7zL5R2p9pww8PHBItUJ0Kc6MzPTM7gZwfRvbgPxtwEpp1pRFPe8jbbr8suBCy5QB4F47z3gN7+J+SUTit8GMT9tfoBtwPy0+amIej0zP21+wIQNfJ3q7m6PU22kD7MsR+dUh+tTrXeITPSp1vhFIOc8kWVh+je3gfjbwCF3mTWSJAn9+/dPjDc3/fsDa9eqc1YDwF/+AvjO32exEorfBjE/bX6AbcD8tPmpiHo9Mz9tfsCEDQJFqn3Tv9UTBj6+uzthI9X9+/cH+vZVN2jLRJfGZUGkmttA/G1Ark91RkaG3cXw1rnnApdeCjz/PPDMM8AJJ8TsUgnJH0cxP21+gG3A/LT5qYh6PTM/bX7AhA1CRaqNpH93dgaeUsvoQGWR9qk24FRnZGQA114LZGermaFOkIVTanEbiL8NSEWqFUXBgQMHEi8d4oor1OWLL/o/4CxUwvLHScxPmx9gGzA/bX4qol7PzE+bHzBhA19HN1ikOpjz29kZ20h1sNG/DaR/HzhwAErv3sDPf65mhzpBFvWp5jZgjw1IOdWSJKGoqCjx0iHOOENNTamsBD78MGaXSVj+OIn5afMDbAPmp81PRdTrmflp8wMmbGBFpDqWfaqDOfYGItWOvAcsHKjMkfwWyg4bkHOq09LSEu8mS0sD5sxR199+O2aXSVj+OIn5afMDbAPmp81PRdTrmflp8wMmbBBtn+rjjguc/h3rPtUGptRy5D1gYfq3I/ktlB02IOVUK4qC/fv3J2Y6xLHHqssDB2J2iYTmj4OYnzY/wDZgftr8VES9npmfNj9gwgaRjv49aRKwaZM6h3UsItWaIxQs/TtMpNqx94CF6d+O5LdQdtiAlFMtSRKGDBmSmG9uiovVZWlpzC6R0PxxEPPT5gfYBsxPm5+KqNcz89PmB0zYINJ5qo88EpgyRV036lQHkplItYkptRx7D1gYqXYkv4WywwbknGpZlhPzJouTU52w/HEQ89PmB9gGzE+bn4qo1zPz0+YHTNjAN3rc3W0s/Vv/XSCnOkwkOeB5Am2PYvRvR94Dffqoy5ycqE7jWH4LZYcNSDnViqKgpKQkMdMhNKf64MHAKTIWKKH54yDmp80PsA2YnzY/FVGvZ+anzQ+YsIHRgcp8nd9gadlWR6qjGP3bkffAgw8Cf/4zMH16VKdxLL+FssMGpOaplmUZQ4cOhWy0scdThYXqw6i7G6io8DjZFiqh+eMg5qfND7ANmJ82PxVRr2fmp80PmLBBpAOVhYtUG7V9jOapduw9cPzx6l+Uciy/hbLDBqSsLYSAoigQ2gMjkZSSAgwZoq6XlMTkEgnNHwcxP21+gG3A/LT5qYh6PTM/bX7AhA2MDlTm61TrPydgn2rq9wB1fsAeG5BzqsvKyhL3Jotxv+qE54+xmJ82P8A2YH7a/FREvZ6ZnzY/YMIGRgcq83Vig0WQrYpUh0v/DhOppn4PUOcH7LEBufTv4cOH212M4IqxU53w/DEW89PmB9gGzE+bn4qo1zPz0+YHTNjAioHKcnLUlOWUFCArS91mdKCySOepNpD+TfkeoM4P2GMDcpHqzs7OxH1zE4dIdULzx1jMT5sfYBswP21+KqJez8xPmx8wYYNIp9TyjWJv2aLOWx2oL3QoRdqnOsyIztTvAer8gD02IOdUV1RUJO5NFgenOqH5Yyzmp80PsA2YnzY/FVGvZ+anzQ+YsEGgPtWBItWh0r+1zyb6PLtlZvRvbZuBKDj1e4A6P2CPDcilfw8bNszuYgTX0KHqcudO9aFm8dxqCc8fYzE/bX6AbcD8tPmpiHo9Mz9tfsCEDayIVAcuQPhrh9ovVPq3gXNTvweo8wP22IBcpLq9vT1x39yceKLaH2XXLmDdOstPn/D8MRbz0+YH2AbMT5ufiqjXM/PT5gdM2CDS0b/DObaxGP1b29dgpJryPUCdH7DHBuSc6urq6sS9yfr1AxYsUNfvvtuTgmOREp4/xmJ+2vwA24D5afNTEfV6Zn7a/IAJG/gOVBZsnupw6d++itapDtWn2sC5qd8D1PkBe2xAyqmWZRnFxcWJPRn6LbeoD47Nm4HycktP7Qj+GIr5afMDbAPmp81PRdTrmflp8wMmbKBFqtPS1KU+Uh1N+rfR0b/NTKllok819XuAOj9gjw1IWVsIgba2tsR+c1NUBAwZoq5bPGCZI/hjKOanzQ+wDZifNj8VUa9n5qfND5iwga9THSxSHU36d6h9I0n/NhippnwPUOcH7LEBOae6trY28W+ywYPVpcWRasfwx0jMT5sfYBswP21+KqJez8xPmx/wsUFnJ/Dmm0BTk/+OmlOdnq4ug0Wqo0n/Tg0xJnIkA5UZ7FNN+R6gzg/YYwNSTrUsyxg8eHDip0MMGqQuDx609LSO4Y+RmJ82P8A2YH7a/FREvZ6ZnzY/4GODf/8bOPdc4N57/XeMNFJtZvTvUE51uD7V+uuYHP2b8j1AnR+wxwakrC2EQEtLS+K/uYlhpNoR/DES89PmB9gGzE+bn4qo1zPz0+YHfGxQWalu1JZ6hYpUW5X+3auXsf0CbQ+URm4wUk35HqDOD9hjA3JOdWNjY+LfZJpTbXGk2jH8MRLz0+YH2AbMT5ufiqjXM/PT5gd8bKA5zr7TZwGe0b/DpX/HO1JtQZ9qyvcAdX7AHhuEuNOTT7Iso6ioyO5ihJeW/h2D0b8dwR8jMT9tfoBtwPy0+amIej0zP21+wMcGoZxq30i13gmPZkotvbMcSaQ6VPq3wdG/Kd8D1PkBe2xALlLd1NSU+G9uYpj+7Qj+GIn5afMDbAPmp81PRdTrmflp8wM+NtCcZN85qQH/PtX6/UJFqq1K/44kUm0w/ZvyPUCdH7DHBuSc6tbW1sS/yTSnevduYNEiYNs2S07rGP4Yiflp8wNsA+anzU9F1OuZ+WnzAz420NK5jUSqAaCrS13GY6CySPpUG0z/pnwPUOcH7LEBufTvwsJCu4sRXlr6NwA89BDw6qvA999HfVrH8MdIzE+bH2AbMD9tfiqiXs/MT5sf8LGBmfRvQJ2CSz2J/oS+FwhXAM96NH2qo0j/pnwPUOcH7LEBuUh1Q0ND4r+5yc4Gevf2fP7hB0tO6xj+GIn5afMDbAPmp81PRdTrmflp8wM+NgiV/u07UBngiVTHY6CycH2qoxiojPI9QJ0fsMcG5Jzqjo4OZ9xkzc2edVn2zBsYhRzFHwMxP21+gG3A/LT5qYh6PTM/bX7AxwZmI9VG0r+tGqgshn2qKd8D1PkBe2xALv17wIABdhfDmCTJ40grijq/YJSj2DmKPwZiftr8ANuA+WnzUxH1emZ+2vyAjw2M9KnWO76BItXRpH9HM091oDIYiFRTvweo8wP22IBcpLq+vt4Zb27+8x/gzDM9b+QsSAF3FH8MxPy0+QG2AfPT5qci6vXM/LT5AR8bGIlUp6R4fm/Gc6CyGEaqKd8D1PkBe2wQkVP92GOPYfjw4cjIyMDkyZOxdetWQ8c9//zzkCQJc+bMieSyUUsIge7ubmfcZOecA7z9NjBjhvrZgoHKHMUfAzE/bX6AbcD8tPmpiHo9Mz9tfsDHBkam1EpN9Ti/RvpUxzpSbUGfasr3AHV+wB4bmHaqX3jhBSxatAhLly7FF198gfHjx2PWrFk4dOhQyOP279+PW2+9FdOnT4+4sNFKlmUUFBRANtAgE0ZHHKEuLRr923H8For5afMDbAPmp81PRdTrmflp8wM+NggVqdYc7XCRal9bhotU67+3afRvyvcAdX7AHhuYvtLy5ctx/fXXY/78+Rg7dixWrFiBrKwsPPXUU0GPcblcuOKKK/DHP/4RRx55ZFQFjkZCCNTW1jrrzY1mL4vSvx3Hb6GYnzY/wDZgftr8VES9npmfNj/gYwMjfapTUvwj1dEMVKbfx6Z5qinfA9T5AXtsYMqp7uzsxOeff46ZM2d6TiDLmDlzJjZv3hz0uLvuugsDBgzAtddea+g6HR0daGxs9PoDAKXnwSCEcBtJUZSYr2vXjPV6wOsPH64a5fvvoXR1QXzyCdDV5WymZKwnhzBp504mpmSsJ2ZyBhOLxWIlvIykf+sj1do81dGkf2vnBGwZ/ZvFskOmnOqamhq4XC6/ybQLCwtRWVkZ8JiPPvoITz75JFauXGn4OsuWLUNubq77r7i4GABQV1fnXmrrtbW1aGhocJdPc8Crq6vR3DMtVVVVFVpbWyFJEjo6OtDR0QEAKC8vd6+Xlpaiq+ftXElJCVwuFxRFQUlJCRRFgcvlQklJCQCgq6sLpaWlANQXAOXl5QCA9vZ2VFRUAABaW1tRVVUFAGhubkZ1dTUAoLGxETU1NQCAhoYG1NbWhmSq79tXNcrOneg64wxIU6YAf/ubmwkAKioq0N7eHpZJURTk5eWhtLTUVqZw9WSGyUw9HTx4EPn5+ejo6EgaJjP11NTUBJfLBUmSkobJbD0dPnwYqampkCQpaZjM1JMkSWhra0Nnz4+mZGAyU09CCOTm5ro5omViJaYkSUJ+fj6kcCmqSSrmp80P+NjA6EBloSLVZtO/9cf4OtV6pzhcn+pA6d8GHHrq9wB1fsAmGwgTKi8vFwDEpk2bvLbfdtttYtKkSX77NzY2iuHDh4u33nrLve3qq68Ws2fPDnmd9vZ20dDQ4P4rLS0VAERdXZ0QQghFUYSiKEIIIVwul+F1l8slqqqqRHd3t6ljtWvGej3g9RsahNK3rxCA52/OHFPc2np3d7eorq4WXV1d9jLFaD1cWbq6ukR1dbXo7u5OGiYz693d3eLQoUNe7cHpTGbrqaury22DZGGK1TPQKUxm1l0ulzh06JAlz8D6+noBQDQ0NAiWNWpoaLDEpi6XS1RXV7vripqYnza/ED42uPxy9bfjccf573jHHep3CxcKMXCgup6Toy5fesmz3/bt3r9D//zn8IXIzFT3vfBC72PT0z3rPc9iP91wg/r9eed5tq1apW6bPt0cP0FR5xfCWhsY/d9kap7qgoICpKSkuN/aa6qqqsLAgQP99t+3bx/279+P8847z71N6UmdS01Nxa5duzBixAi/49LT05Gun4i+R1pnc/1bB30HdCPraWlp7s9mjpUkyX3dWK0HvH5ODvDOO+po4D0RErS1meaWZRlCCKSmpiKl522fbUwxXA9VlpSUFKSmpkKW5aRhMrMuyzJ69erl3pYMTGbrKSUlxcsGycAUy2egU5jMPAN79epl2TOQlZiSJMmdkUJRzE+bH/CxQc/v7oDp3/qBysz0qTZi22CR6tRUoCezyFSfahPp39TvAer8gD02MJX+nZaWhokTJ2LDhg3ubYqiYMOGDZgyZYrf/qNHj8aOHTuwbds299/555+PU045Bdu2bXOndcdLkiQhLy/PeTfZCScA330HPPCA+rknRdGsHMtvkZifNj/ANmB+2vxURL2emZ82P+BjAyvmqfZ1fqMZqMxI+neg0b9zctRl795hL039HqDOD9hjA9Ojfy9atAgrV67E008/jZ07d2LBggVoaWnB/PnzAQDz5s3DkiVLAAAZGRkYN26c119eXh769OmDcePGIS0tzVqaMFIUBYcOHXJHyx2l/HzgzDPV9YMHIzqFo/ktEPPT5gfYBsxPm5+KqNcz89PmB3xsYLZPtRa91jsjvo6JmYHKgjnVoc6hXU+/zxlnAMuWAffeG/bS1O8B6vyAPTYwlf4NAHPnzkV1dTXuvPNOVFZWYsKECVi3bp178LKSkpKEnRdNkiSkp6c7983NoEHqsrYWaG8HHnkEWL4ceP99YMyYsIc7nj9KMT9tfoBtwPy0+amIej0zP21+wMcGRkf/9nV+Y5n+rV0z3LH6MmRkALffHv664HuAOj9gjw1MO9UAsHDhQixcuDDgdxs3bgx57KpVqyK5pCWSJAm5ubm2XT9q5eUBmZlAWxvwwQfA4sXq9hdfBJYuDXu44/mjFPPT5gfYBsxPm5+KqNcz89PmB3xsYCRSnZrq7+TqnZFo0r+tcqpNiPo9QJ0fsMcGiRlSjpEURUFVVZVz0yEkyROtvvRSz3ZtTsEwcjx/lGJ+2vwA24D5afNTEfV6Zn7a/ICPDTQ7BHKqAw1UpinaSPXIkWp02Xf8JO06oRzmQH2qTYj6PUCdH7DHBqScakmSkJWV5ex0CM2p1s+ReuiQoUOTgj8KMT9tfoBtwPy0+amIej0zP21+wMcGZgcq0xTKqTYSQX7vPeCHH4D+/b23G4lUB+pTbULU7wHq/IA9Nogo/dupkiQJffr0sbsY0WnwYP9tJpxqx/NHIeanzQ+wDZifNj8VUa9n5qfND/jYINI+1dEOVJaVpf757mtkoDIL0r8p3wPU+QF7bEAqUq0oCioqKpydDqFFqgFg+HB1adCpTgr+KMT8tPkBtgHz0+anIur1zPy0+QEfG1gRqfZ1bs1E/3yPNdOnOor0b8r3AHV+wB4bkHKqJUlCTk6Os9Mh9E71ZZepSxORasfzRyHmp80PsA2YnzY/FVGvZ+anzQ/42CBUn2r9QGVWR6qD7WumT3UUkWrK9wB1fsAeG5BzqrOzs519kwnhWb/oInVpwql2PH8UYn7a/ADbgPlp81MR9Xpmftr8gI8NQqV/6wcqs7pPtSbf88apTzXle4A6P2CPDUg51YqioLy83NnpEHPmqA+bk04CjjpK3dbcDLS2hj00KfijEPPT5gfYBsxPm5+KqNcz89PmB3xsYDT9O1Sk2sr0bzN9qqNI/6Z8D1DnB+yxAbmByvLz85395uaoo4DSUnXO6qwsIC1NnVKruhoYNizkoUnBH4WYnzY/wDZgftr8VES9npmfNj/gYwO7Rv/WlJnp/TlO81RTvgeo8wP22IBUpFqSJGRmZjr/Jhs8GMjOVh9yAwao2wykgCcNf4Riftr8ANuA+WnzUxH1emZ+2vyAjw30far1XQi1bUBs5qnWpGVVaopTn2rK9wB1fsAeG5ByqhVFQWlpaXKlQ5hwqpOS34SYnzY/wDZgftr8VES9npmfNj/gYwN9hNrXJqEi1aHSv804u8Gc6lCR6pwcdRnhlEjU7wHq/IA9NiCX/t2/f//kenNjMlKddPwmxPy0+QG2AfPT5qci6vXM/LT5AR8b6J1ql8vbmdUGKgs0+rdV6d8ZGUBxsdp1UbtWuHNcc40aVZ871/h1dKJ+D1DnB+yxAalItSRJyMjISK6bTO9Uf/IJ8MQT/uk9PUpKfhNiftr8ANuA+WnzUxH1emZ+2vyAjw30TrXvCOBGI9XRpH8DwMiRnnXtOqEi1Xl5wM03e08ja0LU7wHq/IA9NiDlVCuKggMHDiRXOoTmVD/wADBlCnDDDcCnnwbcNSn5TYj5afMDbAPmp81PRdTrmflp8wM+NtDbwXewMqN9qqNJ/wa8U8CNRKqjFPV7gDo/YI8NSDnVkiShqKgoud7caE51TY1nm5Zi46Ok5Dch5qfND7ANmJ82PxVRr2fmp80P+NjAN/1br3gMVAZ4R6qN9KmOUtTvAer8gD02INenOi0tze5iWKvTTwdyc9XBHMrK1G2HDwfcNSn5TYj5afMDbAPmp81PRdTrmflp8wM+NjDqVJtJ/07wSDX1e4A6P2CPDUhFqhVFwf79+5MrHWLCBKCuTo1Oz5+vbgviVCclvwkxP21+gG3A/LT5qYh6PTM/bX7Axwah+lQbHags2vTv0aM963GIVFO/B6jzA/bYgFykesiQIcmXDqHx9OunLvWp4F67JSm/QTE/bX6AbcD8tPmpiHo9Mz9tfsDHBkb7VMdyoLKjjwauvVZ1qNvb1W0xjlRTvgeo8wP22ICcUy3LcvLeZAUF6jJE+ndS84cR89PmB9gGzE+bn4qo1zPz0+YHfGwQiz7VZh1iSQL+8Q91/Wc/81wzRqJ+D1DnB+yxAbn075KSkuRNhwgTqU56/jBiftr8ANuA+WnzUxH1emZ+2vyAjw0inVJL/9nXiY7GUdHOFePRvynfA9T5AXtsQMqplmUZQ4cOhRzDhmyrNKc6SKQ66fnDiPlp8wNsA+anzU9F1OuZ+WnzAz42iDRS3bu3Zz3aSHWgY2MYqaZ+D1DnB+yxASlrCyGgKAqEEHYXJTbSp38rCnDNNcBFF7n70yQ9fxgxP21+gG3A/LT5qYh6PTM/bX7AxwahnGr9QGW+Tm6snGrtXDF0dqjfA9T5AXtsQM6pLisrS96bTJ/+/a9/AU8/DaxdCxw4AIAAfxgxP21+gG3A/LT5qYh6PTM/bX7Axwb69NdQ6d+hItWxSP+OYaSa+j1AnR+wxwakBiqTZRnDhw+3uxixkxaprqsDbrvNs72uDjjiiOTnDyPmp80PsA2YnzY/FVGvZ+anzQ/42CCSeapTUoD0dM/nWKR/xzBSTf0eoM4P2GMDcpHqzs7O5H1zk5/vWdcPVlZXB4AAfxgxP21+gG3A/LT5qYh6PTM/Uf7OTqC+HoCPDSLpU927t7VTaukVp0g1yXugR9T5AXtsQM6prqioSN6bLDUVyM31365zqpOaP4yYnzY/wDZgftr8VES9npmfKP+55wJHHAH88IO3DSIZ/Vuf+g34R5UTPFJN9h7oEXV+wB4bkHKqZVnGsGHDkns0PC0FXK8ep5oEfwgxP21+gG3A/LT5qYh6PTM/QX4hgHffVSPVS5Z420DfpzrUQGX6SHV2tvd+sRioLMajf5O7B3Sizg/YYwNS1hZCoL29Pbnf3PTt61mfOFFd6iLVSc8fQsxPmx9gGzA/bX6zeuyxxzB8+HBkZGRg8uTJ2Lp1q6Hjnn/+eUiShDlz5sS2gEFEvZ6ZnyB/z+88AMDbb0N0dHhsEGn6t14OTP8mdw/oRJ0fsMcG5Jzq6urq5L7Jqqs961OmqEudU530/CHE/LT5AbYB89PmN6MXXngBixYtwtKlS/HFF19g/PjxmDVrFg4dOhTyuP379+PWW2/F9OnT41RSf1GvZ+YnyF9R4VlvbIR49VXVBooSOlJtNP3bYQOVkbwHdKLOD9hjA1JOtSzLKC4uTu50iJ7pswB4UsFrawEQ4Q8h5qfND7ANmJ82vxktX74c119/PebPn4+xY8dixYoVyMrKwlNPPRX0GJfLhSuuuAJ//OMfceSRR8axtN6iXs/MT5D/4EGvj/LOnaoNfPczOqVWPPpUxzj9m9w9oBN1fsAeG5CythACbW1tyf3mZu5cdXnmmZ5UcF2kOun5Q4j5afMDbAPmp81vVJ2dnfj8888xc+ZM9zZZljFz5kxs3rw56HF33XUXBgwYgGuvvdbQdTo6OtDY2Oj1BwBKT2RNCOGuK0VRDK8LIdDS0uI+j9FjtWvGej0SJjPriqKgra0Nrh6HKRmYzNSTy+VCW1ub2xbJwBR23cepVurrVRv4ONGi57P7+to9Ist+kWqvMvo8M7VPETH1ODmiJ/odi3vP5XKhtbXVvW/C1FOc7j0zz0CnMJmtJ0VR0NLS4r62FRzhRM6prq2tdRs+KfXYY8CjjwKrV3um2NI51UnPH0LMT5sfYBswP21+o6qpqYHL5UJhYaHX9sLCQlRWVgY85qOPPsKTTz6JlStXGr7OsmXLkJub6/4rLi4GANT1/M+qq6tzr9fW1qKhocFdPs0Br66uRnNzMwCgqqrK/UP6+++/R1tbGwCgvLwcHR0dAIDS0lJ0dXUBAEpKSuByuaAoCkpKSqAoClwuF0pKSgAAXV1dKC0tBaC+ACgvLwcAtLe3o6In3ba1tRVVVVUAgObmZlT3dMFqbGxETc/Ulg0NDajtyRiLlAkAKioq0N7eHpapu7sbNTU1ScVktp5qa2vR1taWVEyh6qlz/37o1V5Tg9raWtQfPuy1vbmHw83U42Q3tbX5Rap9mfTq7DkuIqYeZ7qtszMkU7T1VF5eDiFEQtVTvO49IQT27duXVExm66m7uxt79uyBECJqpsM+7SiYJOGAXxeNjY3Izc1FQ0MDcnJy7C6Oc/Tmm+oUCxMnAp99ZndpWCwWK2mUzP+XDh48iMGDB2PTpk2Yoo3NAWDx4sX473//iy1btnjt39TUhOOOOw6PP/44zjrrLADANddcg/r6erz66qtBr9PR0eH+gQSoNi0uLkZdXR3y8vLcLz8kSYKiKJAkKabrsiy7oxqxXGcmZrKc6Ze/hPToo0BWFtDaCnHxxZBefBGiuRlSnz7uNiZefx3Seed5rp+RAXR2Qhw4AGn9euD669Udb7oJykMPeZe3Vy9IWvTuo48gTZ0aGdMddwD33Qcxdy6k55+nVU/M5EimhoYG9O3bN+z/+9Sg3yShhBBobW1FVlYWJCmKkQudogDp36T4fcT8tPkBtgHz0+Y3qoKCAqSkpLjf2muqqqrCwIED/fbft28f9u/fj/POO8+9TUuXS01Nxa5duzBixAi/49LT05Genu63XesDp68jfb+4cOtCqGn+WVlZpo/VfpjFcj0SJjPrQqipnxp/MjD5rocri8YfTb0mElPYdS2DZPRo4IsvgKYm1QY+A5NpTrH7+j3fS75TavXu7V9GnU2knlTxiJi073quF4t7T5Ik97M+oeopTvee2WegE5giWddsEG3Z9ecMJXLp342Nje43JkmvAE41KX4fMT9tfoBtwPy0+Y0qLS0NEydOxIYNG9zbFEXBhg0bvCLXmkaPHo0dO3Zg27Zt7r/zzz8fp5xyCrZt2+ZO646XqNcz8xPk1/pUH320uuwZo0AEG+3b93O40b8B7xHADToZAaUdG805wojkPaATdX7AHhuQilTLsoyioiK7ixE/6Z3q886DfMIJKLr55pg+yBJZ5OrfR9T5AbYB89PmN6NFixbh6quvxvHHH49Jkybh4YcfRktLC+bPnw8AmDdvHgYPHoxly5YhIyMD48aN8zo+Ly8PAPy2x0PU65n5CfL7ONVSU5NqA9++oPqBy/SDL4Ub/Rvw/u0YTaaPdmyMR/8mdw/oRJ0fsMcGpLwrIQSamprovLnRnGoAeOMNYOlSuCZOhFixAjjiCOCDD+wrmw0iV/8+os4PsA2Ynza/Gc2dOxcPPPAA7rzzTkyYMAHbtm3DunXr3IOXlZSU+A1elCiiXs/MT4xfCM881T1OtWhsVG0QbAotwNvBTk1Nukg1qXvAR9T5AXtsQCpSrfWny87OptGfLj0dyMwEekZABYCUPXuABQvUD089BZxyik2Fi7/I1b+PqPMDbAPmp81vVgsXLsTChQsDfrdx48aQx65atcr6AhkU9XpmfmL8dXWANuCfLv27tbUV2d3d8LKA3qnWrxuJVOttGY1dtXEUAoynYJXI3QM+os4P2GMDUpFqWZZRWFhouMN5Ukj/0Hr6ae83gz1D0VMRyfrXiTo/wDZgftr8VES9npmfGP+BA+oyPx8YMAAAIDU2onDAAP8f+frotK9THS5SrbdnNLa99FJg3jzgF7+I/BxhRO4e8BF1fsAeG5CytjYsOql0iPp696q48kq0PvEERG6uuqFnLjkqIln/OlHnB9gGzE+bn4qo1zPzE+N//311ecIJgDZ9lsuFhqqq0OnfPfNEA1ADMGYi1dE4KsOGqUGe8eMjP0cYkbsHfESdH7DHBuSc6o6ODpo3WVYWBIDm88+HeOEFdduhQ7YWKd4iXf9gfoBtwPy0+amIej0zPzH+d95Rl7NmeTnDnTU1oZ1qLWVclv3Tv7Oz/a9jVfp3HETuHvARdX7AHhuQcqplWcaAAQNopUPMmaMu/9//8/Br84wSc6pJ1r9O1PkBtgHz0+anIur1zPyE+FtbgQ8/VNfPPFN1kHui1f3T0yH7OhSBnGqtm2C80r/jIFL3QABR5wfssQEpawshUF9fT+vNzapVwPr1wMKFHv7+/dXvamq8p1RIcpGsf52o8wNsA+anzU9F1OuZ+Qnxf/ih6hwXFwOjR6vbcnIAAM1ffglRXu69vz5yraV/p6WpSzOjfzsgUk3mHggg6vyAPTYgN/p3d3c3hBB0RsPLzQVOPx0AIBRF5e/XTx0N0uVSR43s18/WIsZLJOtfJ+r8ANuA+WnzUxH1emZ+Qvzr1qnLWbM8jm5PpLr33Ln++4eKVOsd7lj2qY6DSN0DAUSdH7DHBqScalmWUVBQYHcxbJMXf9++qkNdVqY+gLU3lYGkKOpfqrNvF65/2vwA24D5afNTEfV6Zn5C/Pr+1Jp6ItUBFWigMs2pbm/3fBeoT7XD0r/J3AMBRJ0fsMcGid0qLJYQArW1tWTTIbz4e6ZdwIQJasqQbi5rP510EjBmjPdIkQ4U1z9tfoBtwPy0+amIej0zPxH+khLgu+9UB/e00zzbQznV+mi0FqnWgir634H6VHBNDkv/JnEPBBF1fsAeG5Byqlk6aU41APzwA/DVV4H36+gAPv4Y2LsX2LYtLkVjsVgsFovFYoWQFqWePFnNPtRkNFLtm/4dzvlwUPo3i2WHnJ3Pa1KSJCE/P9/uYtgmL35tsDJN33yjPph91dTkWW9tjV3h4iCuf9r8ANuA+WnzUxH1emZ+IvzvvqsuzzzTe7s2V3UgBUr/1iLVs2ervwOnTw98rN6RTvBINZl7IIio8wP22IDUqyZFUVBTUwOF0IjXennx6yPVgOpUB5LeqT58OHaFi4O4/mnzA2wD5qfNT0XU65n5ifAfPKgux43z3m42/VuLVGdkAJ98Atx/f+BjHRSpJnMPBBF1fsAeGyR2q7BYkiQhNTWV7Eh4Xvy+TvXXX6upP3feCTz5pGd7Y6NnvaoqPgWNkbj+afMDbAPmp81PRdTrmfmJ8PsONKbJ7EBloQaq1ctBTjWZeyCIqPMD9tiAXPp3Xl6e3cWwTV78vtNoffMNsGcPcPfd6gP66qvV0b6TzKnm+s+zuxi2iroNmJ82PxVRr2fmJ8KvOcW9enlvz8oKfkyoPtXh5KCBysjcA0FEnR+wxwaJ/arJYimKgkOHDpFNh/Dib2jw/rK83DNYWUeHOngZ4O1UHzoUn4LGSFz/tPkBtgHz0+anIur1zPxx5P/qK2D+fHUk7nirq0td+kaa6+qCHxMq/TucHDSlFrcB2vyAPTZI7FZhsSRJQnp6Otl0CC/+iy9WN06ZAgwZoq6vX+/ZedcudanvU50EkWquf7r8ANuA+WnzUxH1emb+OPI/8QSwahWwenXsr+WrYOnb+vmmfWVV+neC31vcBmjzA/bYgJxTnZubS/Ym8+IfPVqNRr/3HnDMMeoO2kiSgMepTrL0b65/uvwA24D5afNTEfV6Zv448mtzO+vneI6XgjnFt9wCDB4c+Bir0r8TPFLNbYA2P2CPDRK7VVgsRVFQVVVFNh3Cj3/4cLXvzdFHq5/37/fsHMip3rkT+L//Az7/PB7FtVxc/7T5AbYB89PmpyLq9cz8ceTXnFR9WnW8FMypHj4cyoED6Jowwf+YaCLVDkv/5jZAlx+wxwaJ3SosliRJyMrKIvvmJij/qFH+O3/3nbrUp3/X1QF/+xtw7rmxK2QMxfVPmx9gGzA/bX4qcnQ9v/8+cOyxwKZNEZ/C0fwWKK78mjOt9W+Op4INVAZAkmVIGRn+x0TTp9ph6d/cBujyA/bYICKn+rHHHsPw4cORkZGByZMnY+vWrUH3Xbt2LY4//njk5eUhOzsbEyZMwL///e+ICxyNJElCnz59yN5kQfkDOdWBItWaKiutL1wcxPVPmx9gGzA/bX4qcnQ9v/aaOsXlG29EfApH81uguPLbGakONlAZeqYTCuRUE0r/5jZAlx+wxwamW8ULL7yARYsWYenSpfjiiy8wfvx4zJo1C4eCjAydn5+P3/3ud9i8eTO++uorzJ8/H/Pnz8c777wTdeHNSlEUVFRUkE2HCMo/cqT/zocOqZHpQE51ZmZsChhjcf3T5gfYBsxPm5+KHF3PmnOmd35MytH8Fiiu/Fo92RmpDuBUK4qCDiH8jyGU/s1tgC4/YI8NTLeK5cuX4/rrr8f8+fMxduxYrFixAllZWXjqqacC7n/yySfjggsuwJgxYzBixAj86le/wnHHHYePPvoo6sKblSRJyMnJIfvmJih/cbH3m8revdXl7t2BneoBA2JXyBiK6582P8A2YH7a/FTk6HrWnJ4ofgg6mt8CxZVfewkS70i1ECGdYkmSkBJovmpC6d/cBujyA/bYwJRT3dnZic8//xwzZ870nECWMXPmTGzevDns8UIIbNiwAbt27cJJJ50UdL+Ojg40NjZ6/QFwv20QQkD0vIFTFMXwuiRJyNRFWY0eq10z1uuRMJnlyM7Odm9zXz8lBeKoo1SjpKdDHHecul5SAqHvU62pri5hmMzUkxAC2dnZhvd3ApOZdQDIzMyEJElJw2S2noQQ7j42ycIUq2egU5jMrGt9rPyegRGusxJTkiQhOzvbmT8oNac6iki1o/ktUFz57Ur/drlUxxoInv4dKKvQqim1EjxSzW2ANj9gjw1MtYqamhq4XC4UFhZ6bS8sLERliH62DQ0N6N27N9LS0nDOOefgkUcewemnnx50/2XLliE3N9f9V1xcDACo65nQvq6uzr1eW1uLhoYGd/k0B7y6uhrNzc0AgKqqKrS2tkJRFHzzzTdobW0FAJSXl6Oj501daWkpunrSd0pKSuByuaAoCkpKSqAoClwuF0pKSgAAXV1dKC0tBaC+ACgvLwcAtLe3o6KiAgDQ2tqKqp4pqJqbm1FdXQ0AaGxsRE1NjdsutbW1UTEBQEVFBdp75iUMxaSV+8CBA35MYsQI1fhDhkAZNEhdLyuDUl/vX0GNjWhuaEgIJjP1VFZWhvLycrS2tiZ0PcXq3mtoaMCuXbugKErSMJmtp+rqauzduxeKoiQNk5l6UhQFX3/9tZsvGZjM1FN3d7f7GWgFEysxpSgKysvLvV4oOkYWRKodzW+B4spv10BlmkMMBByoTFEUtAZ6MRNNn2q9I53gzhq3Adr8gD02kISJ1+0HDx7E4MGDsWnTJkyZMsW9ffHixfjvf/+LLVu2BDxOURR8//33aG5uxoYNG3D33Xfj1Vdfxcknnxxw/46ODvcPJED98VJcXIy6ujrk5eW5IwRatEmSJEPrgPoDKTMzE7IsGz5WlmV3RCWW65EwmVnXbJuWloaUlBTv6y9eDOn++4GTToI4/nhIy5cDixZBvPMOpG++AZ55BqKrC9L8+QAAUV0NkZ9vnMnlUkejjCFfuLIoioLOzk6k9/wDSdR6itW9pygK2trakNWTEpYMTGbryeVyoaOjA5mZme7IrdOZYvUMdAqT2fW2tjakp6f7PwNNrjc0NCAvLw8NDQ3Iyckx8B+UFU6NjY3Izc2N2qZCCLS3tyMjIyOuUQpLdPXVwL/+BSxcCDzySESncDS/BYor/6xZwPr1wBVXAM88E9tr6dXQAOTlqesdHX7RZiEEXFddhdRnn/U+7qKLgDVr1PUrrwSefRZ48EFg0aLw1xwzxjMzTHMz0JP5l4jiNkCbH7DWBkb/N6WaOWlBQQFSUlLcb+01VVVVYeDAgUGPk2UZR/WkF0+YMAE7d+7EsmXLcHIQpzo9Pd3t+PieB4CXcbRtRtezdQ8BM8dqP8xiuR4pk5l1feqn1/WnTgXuvx/40Y8g9WQGoKwMkhaNGTUK0gknqP/oW1og1ddDKigwxlRVBXnSJOCqq4B77okpX6iypKSkePGH29/OejLKZGZdlmWv+z8ZmMzWU0pKivulgtXcTnlGmHkGOoXJzHqWrp+hVWVnJZYkSfJ71jtGWlQlyvRvx/JboLjy2zVQWZhItSRx+je3Abr8gD02MNUq0tLSMHHiRGzYsMG9TVEUbNiwwStyHU6KonhFouMlRVFQWlpKNh0iJP/55wM7dgB//rM6cBkAlJZ65qnW3sz07asuP/sMePNNYxd+9FGgpAS4997oAKIU1z9tfoBtwPy0+anI0fVsUfq3Y/ktUFz5Yz1QWU93Fz/p56gO8IJPURQ06R1vTYTSv7kN0OUH7LGB6VdNixYtwsqVK/H0009j586dWLBgAVpaWjC/Jy143rx5WLJkiXv/ZcuW4d1338X333+PnTt34sEHH8S///1vXHnlldZRGJQkSejfvz/ZCENIfkkCxo1T31gOGaJuKy31jP7t61Rfdhlw7rnA9u3hL5xqKiEiZuL6p80PsA2YnzY/FTm6ni0aqMyx/BYorvyxHKhs3TqgTx/glFOAb7/1/i5MlFmSJGQGSlPVlzPJI9XcBujyA/bYwLS3M3fuXFRXV+POO+9EZWUlJkyYgHXr1rkHLyspKfFKuWtpacH//d//oaysDJmZmRg9ejSeeeYZzJ071zoKg5IkCRkZGXG/bqLIML/mVJeVebb5OtWavvsOGD8+9Pm0KboA9YFuk5PN9U+bH2AbMD9tfipydD1b5FQ7lt8CxZU/lgOVff65mrGwcSNw4YWe/syAd6Q6gCRJQmqgKbWiiVTrnZMEd9a4DdDmB+yxQUSvmhYuXIgDBw6go6MDW7ZsweTJk93fbdy4EatWrXJ/vueee7Bnzx60tbWhtrYWmzZtssWhBtRUAG3ka4oyzD9wIJCS4vksy4D2cPZ1qntGzg0pfZ+Gw4eNFTYG4vqnzQ+wDZifNj8VObqeLUr/diy/BYorfywj1XpH/dAh7+/CRJkVRUF9W5v/F4GcaqORan10OsEj1dwGaPMD9tggsVuFxZIkCUVFRWTTIQzzp6QA2rRagJp+pB3j61Tro9nBpO/X0zMdjR3i+qfND7ANmJ82PxU5up4tilQ7lt8CxZU/lgOV6c/pOw6R9l2I9O/e+fn+XwQaqCySSHWCO9XcBmjzA/bYILFbhcWSJAlpaWlkbzJT/FoKOOBJ/QYii1Tr35b2zFVrh7j+afMDbAPmp81PRY6uZ4ucasfyW6C48sdyoDK9U+076JiBPtVeo39raeL6ciZ5+je3Abr8gD02IOVUK4qC/fv3k02HMMWvd6r79PGsRxKp1jvVNkaquf5p8wNsA+anzU9Fjq5ni9K/HctvgeLKH69IdXe39z1hIP27trnZs0HbL5optbTotAMcNW4DtPkBe2xAyqmWJAlDhgwh++bGFP9xx3nW583zrDvYqeb6p80PsA2YnzY/FTm6ni2KVDuW3wLFlT9ekWrAOwXcwEBluf37ezYEcqojjVQ74L7iNkCbH7DHBokx11GcJEkSZFkme5OZ4l+0SJ1i64QTgMGDPdvz8rz3q6hQH/zag11RgDfeAE46ybNvAjnVXP90+QG2AfPT5qciR9ezFlWJ0ql2LL8Fiit/vAYqA1QnWEvpNpD+LemdZW0/K6bUSvD+1AC3Aer8gD02SPyWYaEURUFJSQnZdAhT/FlZwJw53g414B+pFkJ1rDU9+ywwezZw882ebQniVHP90+YH2AbMT5ufihxdzxalfzuW3wLFlT9e6d+Ad7/qMAOVKYqCw/r0b83BtiJS7QCnmtsAbX7AHhuQilTLsoyhQ4d6zaNNSZbw5+Z61tPT1Ydyaan69rOjA/jkE/W79etVh1uSEsap5vqnzQ+wDZifNj8VObqeLUj/djS/BYorv93p30GcalmW0W/gQM8GLZswGqfaQX2quQ3Q5gfssQEpawshoCgKhBB2F8UWWcKv778zcaK63L8fmDYNmDQJ+PBDddvBg+p2IGFG/+b6p80PsA2YnzY/FTm6ni1wqh3Nb4Hiyh9ppPrQIeCWW4CdO4PvE4VTLYSA0P9eI5b+zW2ANj9gjw0Sv2VYKCEEysrKyN5klvAff7ya3n3bbcDw4eq2DRvUFPDmZuDrrz37fvSRukyQSDXXP21+gG3A/LT5qcjR9WxB+rej+S1QXPkjjVQ/+yywfDnw8MPB94lioDIhBGoaGz0bfNO/hYh8nmoHRKq5DdDmB+yxASmnWpZlDB8+nGw6hCX8KSnAq68Cf/4zMGKEum3t2sD7fvyxukwQp5rrnzY/wDZgftr8VOToerYo/dux/BYorvyRDlSm9XduaQm+T5Tp34XFxZ4NvqN/689tdkotB9xX3AZo8wP22ICUtYUQ6OzsJPvmxnL+adPUZUND4O8DRapraoCmJtUp37fPmnIYFNc/bX6AbcD8tPmpyNH1bFH6t2P5LVBc+SNN/9ac4lDOeBQDlQkh0KWPKPumf+sd9CQcqIzbAG1+wB4bJH7LsFBCCFRUVJC9ySznnzIl8MP12GPV5a5daoqR3qnu7gb++lfgN78Bli61phwGxfVPmx9gGzA/bX4qcnQ9W5T+7Vh+CxRX/kjTvzWn2IxTbbJP9eGmJs8Gbb+ODvV3mf5cZvtUOyT9m9sAXX7AHhuQcqplWcawYcPIpkNYzt+nD/CjH/lvnzFDXXZ3q1FpvVMNqCODA56BzOIkrn/a/ADbgPlp81ORo+vZovRvx/JboLjyRxupDnVclOnfA4cO9WwYPlztf11VBTz5pOf4lBT1z4gclv7NbYAuP2CPDUhZWwiB9vZ2sm9uYsJ/0knqMiUFOP98df2009R5rgE13VtzqrUUo82b1eXBg9aVw4C4/mnzA2wD5qfNT0WOrmctQh1l+rdj+S1QXPkj7VMdSfq3yYHKOvT8hYXAvfeq67/+NVBZqa4bTf0GHBep5jZAlx+wxwbknOrq6mqyN1lM+E85RV2OGwc89RTw9tvq6OAFBep2vVN9zDHqUvtHUVGhpiHFSVz/tPkBtgHz0+anIkfXs0Xp347lt0Bx5Y9n+re+T7WB9O9abTA0QA18LFoEFBWpg6NpM7UYTf0GHNenmtsAXX7AHhskfsuwULIso7i4mGw6REz4zz0XeOwx1aHu1w8480z1wRvIqR43zvvY9nagvt66soSRvHw5iq+7DrL+HxMhUb//AbYB89PmpyJH17NF6d+O5bdAceMXwjuzwMyP92jTv8MMVCbLMoqGDdNvUB3rfv3Uz4cPq0szkWqHpX9zG6DLD9hjA1LWFkKgra2N7JubmPBLEvB//wf8+Mfe2zWnurpadZ4BT6Rar4oK68oSRuLRR4H16yE++yxu10wkUb//AbYB89PmpyJH17NFo387lt8CxY3ft47MRKvNpH9nZKhLkwOVteuzHbR+0336qEttetNIItUOSf/mNkCXH7DHBuSc6traWrI3WVz5Nae6rMyzzTdSDcS3X3VjIwBAhJoXMolF/f4H2AbMT5ufihxdzxalfzuW3wLFjd/XqTYzWJm2r5FIdXa2ujTpVNfpf+toTnVOjrqsqVGXkfSpdkD0k9sAbX7AHhukxu1KCSBZljF48GC7i2Gb4sqvOdWlpZ5tgSLVcXSqpZ7+RbIWOScm6vc/wDZgftr8VOToerYo/dux/BYobvzxilT37q2ma5sYqMwv/VtzLLRINYH0b24DdPkBe2yQ+C3DQgkh0NLSQvbNTVz5fZ3q1FSguNj/AR6v9O/OTvc/KMqRasr3P8A2YH7a/FTk6Hq2KFLtWH4LFDd+X4fYTKTarFOtP0a/HiJS3aIvj3Yd30h1Eqd/cxugyw/YYwNyTnVjYyPZmyyu/L5OdUaG+nbzyCPVz1oqUrwi1bpRMCk71ZTvf4BtwPy0+anI0fVsUZ9qx/JboLjxRxOpNpP+rTnVJgYqE0KgUZ+Vp5VVi1QTSP/mNkCXH7DHBonfMiyULMsoKioiOxpeXPl9nerMTHU5fry61Oa3jlekuqnJvUo5/Zvy/Q+wDZifNj8VObqeLZin2tH8Fihu/L5OdCzTvwFTfaplWUZRcbFng3Y/aZHqqip1qQ2CZkSaPR0QqeY2QJsfsMcGpKwthEBTUxPZNzdx5dembdCmzNKc6r/+FfjPf4Bf/EL97Bup/sMfgJtusn7+ao5Uk7//AbYB89PmpyJH17NF6d+O5bdAceOPZqAyM051hAOVNennqfaNVGt9qvPzjZfZYZFqbgN0+QF7bJD4LcNCCSHQ2tpK9iaLK78WqdakOdX9+6tzWw8Zon7WR6pbW4E//hF45BGgvNza8uj/uWjzZhMT9fsfYBswP21+KnJ0PVuU/u1YfgsUN/54p38H6lMdZKAyzQZ+ZdOcak1aAMSIHOZUcxugyw/YY4PEbxkWSpZlFBYWkk2HiCt/MKda0/Dh6vLAAc98iSUlnu8rK6O7/tatwP/+5/msc6olok419fsfYBswP21+KnJ0PVs0+rdj+S1Q3PjjPVCZyfTvwsJCzwbf9G9NZpxqh6V/cxugyw/YYwNS1hZCoKGhgeybm7jy+z6ofZ3qwYOBH/9YfdC//LK6Te9Ua/199OrsBBYvBkaPBs47L3h6XFMTMHmy2m+7ocGzrUeU078p3/8A24D5afNTkaPr2aL0b8fyW6C48cdySi1F8dwDETjVmg38yuobqfYNgISSwyLV3Abo8gP22CDxW4aFEkKgo6OD7E0WV/70dO+Ht69TDQCXXaYun3tOXeqd6kOH/PffuBG4/35g1y7gjTfUKHcgbdzoWdci3vr0b31KFCFRv/8BtgHz0+anIkfXs0Xp347lt0Bx449moLJw6d/67RGO/t2h39+KSLXDptTiNkCXH7DHBqScalmWMWDAALLpEHHn79/fsx7IqZ47V13+739AWZm3kxwoUu2bEh4sRfyddzzr2mAcnP5N/v4H2AbMT5ufihxbz0JYNvq3I/ktUtz4YzlQmf5cEQxUptnALSv6VGv2dMB9xW2ANj9gjw1IWVsIgfr6erJvbuLOP3GiZz2QU11cDEydqv6QeO218OnfmoOsKdh0XOvXe9Zra9WlfvRvwpFqyvc/wDZgftr8VOTYetanfEeZ/u1IfosUN34rBioz4lRHOFBZvTb7ir6shNK/uQ3Q5QfssUHitwwLJYRAd3c32Zss7vynnOJZD+RUA8Ds2erSiFOtOciafCPV336rRr/37PFs0xxxXZ9qyunflO9/gG3A/LT5qcix9ax30qJM/3Ykv0WKG78VA5UZSf+OcEqtbn35CKZ/cxugyw/YY4PUuF0pASTLMgrMvJVLMsWd/9RTPevBHsKzZ6uDj23c6P0GVXOqhVD/ZDl0pLq6GjjzTKC01HufQOnfRJ1q6vc/wDZgftr8VOTYetZHp6NM/3Ykv0WKG79VA5UJ4f8bSXOqU1KAjAx13WT6t5cNgkWqk3RKLW4DtPkBe2yQ+C3DQgkhUFtbS/bNTdz5R43yrG/bFnyf0aPVfyD6SHRVlTqf9OjRwFlnqf90tO+1fwL6SPU116gO9ahRwCuvAPPmqds5/dst6vc/wDZgftr8VOTYetY7aVGmfzuS3yLFjd+KgcqAwC9QtO979VIHfgVMD1RWq/9NpfWvzsxUHXVAdY7z8oyX2UFTanEboM0P2GMDUk41K86SJCArS13X5qUOpIsv9t926BDw9dfA7t1qH+n//tcTdT7mGHWpRarfew946y31n8/atcCcOcARR6jfBUr/JjpQGYvFYrESWBalf7PipEgHKhPCu390IGdc71RrjnOgPtVBnGr3pV59Vf2Ndeed6gZJ8kSr+/b1ONhG5KBINYtlh0i1DEmSkJ+fD8kBb9liIVv4v/gC+PnPgYcfDr7P4sX+22pqgIMHPZ8ff9wTddac6spK9Z/Tb3+rfl6wwPNdfr665PRvt6jf/wDbgPlp81ORY+vZIqfasfwWKW78kaZ/G4lwh4tUh3Gq3TaYPRt48UUgN9fzpdav2kzqt3pSdekAp5rbAG1+wB4bJH7LsFCKoqCmpgZKFGlVTpYt/EcfDTzxhCdyHEi9ewMff6y+Pf3Zz9QHthBqpFrTK6+oA5EB3pHqb74BPv1UTWn63e88+2v/LAI41VTTv6nf/wDbgPlp81ORY+vZovRvx/JbpLjxRzpQme9+gY4z6lQHGf07pA20SLXZ/qYOSv/mNkCbH7DHBqScakmSkJqaSvbNTULzn3ii2o/6H//wPOi3b/d8390NtLer6/+fvfMOj6Ja//h3Nj2EJEBCCL1JbwqKKAgKgogoNop4KfaCDSv6U9TrFQt65XrtBculqGADFUUEVECULgoICARCCOkJ6dk5vz9OzrSd3exuNtlszvt5njw72Z3dne85szPznfc97xGmOjMT2L6dLw8cqI8ZAnRTbTOmGiUl3LRPmsSLm0ly0GnQ/V9PyN4GpF9u/bIQsv1sNdV+jgUMWf0Bot70+xupNqZxu3tfACLVbttAmOpGHqmm34C8+oHgtIFU1b8VRUGiL0UZGhkNXr+YdislhY+pNppqIz178oO70wn88AN/rl8/8zrW9G/DmGqlrIw///HH/ImjR4EOHQIkouHS4Pu/HpC9DUi/3PplIWT72WrSVNW3Ma/VhKz+AFFv+v0tVFZbU+106jdcPJhqt21Q2/TvEDBq9BuQWz8QnDZo+LebAoiqqjh58qS06RAho79VK/7411/8UczRCPCDeVKSHs3+7jv+aDXVHtK/AfC0cYFx7HYjJmT6vw6RvQ1Iv9z6ZSFk+9nOVPtByOoPEPWm399CZb6mf1sLlRkj1h7Sv922gb+RahGhDoFINf0G5NYPBKcNGv4vI4AoioKoqChp0yFCRn+XLub/hw7Vl0W1SmG8hSF2Z6pLSnjauCdTbZzvuhETMv1fh8jeBqRfbv2yELL9bDVpfhYrC1n9AaLe9Acr/dv4fvGaBY9tIK6f2rXzbnv1D+WPIWCq6Tcgt34gOG3Q8H8ZAURRFCQkJEi7k4WM/p49zf8bTbVI627f3rxOnz7m/xMS9LS53FxXU20sgiZJpDpk+r8Okb0NSL/c+mUhZPvZGlGphakOSf0Bot70+1uorLam2otItcc2mDOHz8gyc6Z326t/qPmxAUO/Abn1A8FpA6lMtaqqyMzMlDYdImT0ezLVIm3pjjvM64jnBYrCo9oAN83VJy0WXl1GQMJIdcj0fx0iexuQfrn1y0LI9nMA079DUn+AqDf9/kaq/a3+rar8O4Spjopya3A9tkGbNsBdd+ljq70lxNK/6Tcgr34gOG3Q8H8ZAURRFMTGxkp75yZk9FtN9emn68ui4NiYMcCgQXx57Fj7zxEp4EeOaE8x8ZyEY6pDpv/rENnbgPTLrV8WQrafA5j+HZL6A0S96Q90pDojgw9l++9/7U21eK/RVLuhTtogxNK/6Tcgr34gOG3Q8H8ZAURRFDRt2lTanSxk9LdpY/7feDc1L09f/u474NFHgRdftP8cq6mOiYEjIYEviwJmgFSmOiT6vw6RvQ1Iv9z6ZSFk+zmApjok9QeIetMfqDHVwkCvXw/8/juwaJF9oTKAG2ovTXXA2yDE0r/pNyCvfiA4bSCVqVZVFRkZGdKmQ4SMfusPwPi/mHca4OndTz4J9Ohh/zmtW/PHtWsBACwlBZV2449qk/69Zk3ImPKQ6f86RPY2IP1y6/eVV155BR07dkR0dDQGDx6MX3/91e26b731FoYNG4ZmzZqhWbNmGDVqlMf165KQ7ecApn+HpP4AUW/6A5X+Ld5XXMwfS0v1dSIjzeOmvTTVddIGIZb+Tb8BefUDwWmDhv/LCCCKoiA+Pl7aOzchpd84jRbAI9IA8Mor3n+GqAi+ahV/7N8fDuvYa8B/U7xwITBqFDBjhn/vr2dCqv/rCNnbgPTLrd8XPvroI8yePRtz587Ftm3b0L9/f4wZMwYnT560XX/dunWYMmUK1q5di02bNqFdu3YYPXo00tPT63nLQ7ifAxipDkn9AaLe9Ac6/VsUVC0rM0eqFcVcrMzLSHXA2yDEItX0G5BXPxCcNpDOVDdp0kTanSyk9I8Zwx9jYvjj448DBw4At9zi/Wf0788fq09YSr9+CDOadeNc1sZqmmVlPLpd0wny1lv54+rV3m9TEAmp/q8jZG8D0i+3fl948cUXceONN2LmzJno1asXXn/9dcTGxuLdd9+1XX/RokW47bbbMGDAAPTo0QNvv/02VFXFmjVr3H5HeXk5CgsLTX8AtMgCYwyMMe05b5cVRUGMOHf48F7xnXW97HZbrCZNVX3SbdTRpEkT7bmgaqqDZW+2pUn1ub5ONVmuEVj1/zXqsJhqtfr6g4maMaWlYGKdiAi+LdUGmpWVQS0t5ctRUT63Qa36QOhUlAb/e2KMaeNp63Pfayi/J1+OgaGiydd+AoCYmBgo1ftrIHTUhFSmWlVVpKenS5sOEVL6X3+dG+iff+b/Oxx8/mpfLoYHDDD9q/bti1Jj2tKYMfqd3hMn9OcffRS44ALgkUfcf/amTWYjXlDg/XYFiZDq/zpC9jYg/XLr95aKigps3boVo0aN0p5zOBwYNWoUNm3a5NVnlJSUoLKyEs3FNIg2zJs3DwkJCdpfu+p5c/Oqa2fk5eVpy7m5uSioPs5mZ2drBjwrKwunqiN8mZmZKCkpgaqq+OOPP1BSUgIASE9PR3n18fro0aOorDY/aWlpcDqdUFUVaWlpUFUVTqcTaWlpAIDKykocPXoUAL8BIKLuZWVlyKgeNlRSUoLMzEwAwKlTp5CVlQUAKCwsRHZ2NgCgoKAAudVDlzxpKq7WpOF0apoAICMjA2VlZTVqEtt95MiRoGvy1E++aPKln44dO4b09HSUlJTUqaZT+fmm7qqsNrs1arKY8ZPHj3NTICLVpaVwVr8fEREoLy+HWj1zSXlREXKrNakREW41ZWVlIT09HXl5eQHrJ2e1aSmrrGzwv6esrCwcOHAAqqrW677XUH5Pqqpi9+7dmr7GoMnXfqqsrMSuXbu05dpoyjHWYfIECwEKCgoYAFZQUFCrz1FVlZWUlDBVVQO0ZaGFdPpVlbHERMYAxgCm/vUXq7r0Uu1/tmoVYx078uWNG/X3idc9/TzuuMO83tatvm9fTg5jf/7p+/v8RLr+t0H2NiD9gdMfqPNSQyQ9PZ0BYBuNx0XG2P3338/OOussrz7j1ltvZZ07d2alpaVu1ykrK2MFBQXa39GjRxkAlpeXxxjj/SX6yul0er2sqio7deoUczqdPr1XfGddL7vdlp9/Np9XDh3ySbdYdjqdrKSkhFVVVQVfUx0s17QtVVVVrKSkRGuLOtO0YIGpv9S77/ZOxyefmN7nXLmSf+esWfy5uDimvvgiX54yhW9Lr178Oz7/nDk//ZQvn322z21Qqz6ovu5RL7qowf+eqqqqWHFxsbZufe17DeX35MsxMFQ0+dpPTqeTnTp1Svvu2mx7Xl6eV+f7cO+sd+PAmg4hG9LpVxSeAr5+PdCkCZQuXRC2YYP++siRvJjZ4cOAcdxfy5aAGDdYVQWE2/xMDNN0AQAOHgTOOMO37Rs/HvjlF/791RGaukS6/rdB9jYg/XLrry+eeeYZLF26FOvWrUN0dLTb9aKiohBlMy7UUZ1RZEzTdxiyjLxZbmIY6uPLexVF0b63rpbdfj9jMKGqPusWy8b9PKia6mjZ07aEhYW5/M7rRJMl40WpHgNfow5L+rej+n1KdWQOpaVQxFCAiAi+LUOHAn/+CeXHH6EMHszXj4qC4qY93LVBjZo8LYvvCgvTsgYb6u8pLCwMsbGxgdHdQDTV5TEwVDT5suxwOExtUJttN36+J6RL/z569Ki0qX9S6hfjqvv2hQqg6NJL+f8XX8zNspgT+5df9PekpOjL27fbf64obiam6DpwwPdtO3iQV3e1GvQ6Qsr+tyB7G5B+ufV7S1JSEsLCwrRUOEFmZiZatWrl8b3z58/HM888g++++w79RLHIeiZk+zlAhcpCVn+AqDf9/hYqc1f9W6R/O528AjigV/4ePpw/rl/vdfXvgLeBMBa+DMMLEvQbkFs/EJw2kMpUK4qC5ORk0x0TmZBS/+WX8xPAFVdAURREPP002IcfAp99xl8fOZI/GovpGMdO/Pij/ecKU33eefzx4EHft81wV7o+kLL/LcjeBqRfbv3eEhkZiYEDB5qKjKkqLzo2ZMgQt+977rnn8M9//hOrVq3CoEGD6mNTbQnZfg5g9e+Q1B8g6k1/oOaptk6pBQBifL3VVG/fDlSP9ayp+nfA20B8VghMqUW/Abn1A8Fpg4b/ywggiqIgOjpa2p1MSv0jRvC7v/fdx/W3agXl2mv53I8AL0gGADt2ANnZPP2uusgCAFdTnZfHT3yisJkw1b5GqhkLiqmWrv8tyN4GpF9u/b4we/ZsvPXWW3j//fexZ88e3HrrrSguLsbMmTMBANOmTcOcOXO09Z999lk8+uijePfdd9GxY0ecOHECJ06c0ArP1Cch288Bmqc6ZPUHiHrTb+0vfyPV4n/jb0VUAhemuk0bXqxVVYEffuDP1WCqA94GIWaq6Tcgr34gOG3Q8H8ZAURVVa0apoxIqz82FlAUe/0pKUDfvnz5hx/4icx4F/nwYX05Lw/o2BHo3p2f2MLCABG1Wb8e+Oc/zRXBPVFZqZ+QRZXPOkba/jcgexuQfrn1+8KkSZMwf/58PPbYYxgwYAB27NiBVatWIaV6eExaWppWNRUAXnvtNVRUVOCqq65Camqq9jd//vx63/aQ7ecApn+HpP4AUW/6rZHp2kaqjabaGqkG9OuNzZv5Yw3p3wFvA2FOQsCo0W9Abv1AcNpAukJlqamp0t65If1u9I8aBfz+O5+b2pqyaJwy4/ff+YlOnOxatQK6ddNff+wxID6eR7rbtQNuusn9xhij0/UYqZa5/wFqA9Ivt35fmTVrFmbNmmX72rp160z/HzbegAwyIdvPAUz/Dkn9AaLe9Hub/r18OTB3LnDNNcA997ia6oICPqTMU/o3AIjp6aqnKaopUh3wNhAR6hCJVNNvQF79QHDaQDpTHSnSfiWE9LvRLyLVhw/rY5UERlNtvWhs3RpISgKuvx545x3+3OOP6+9hDLj5ZvuNEanfQL2aapn7H6A2IP1y65eFkO3nAKZ/h6T+AFFv+g0VumEz/zQAIC0NmDmTZ8E98giwZw/Qo4d5nbvu4n9G7Ex1dTVrbb+owVQHvA1CKFJNvwG59QPBaYOGf7spgKiqisOHD0ubDkH63egX1b4zM3VT3aEDfyws1C90rFW6W7fmJ5e339YNt9GE3347cOyY/cYEwVTL3v8AtQHpl1u/LIRsPwcw/Tsk9QeIetMv+kdMG2cXqb7nHm6oxdQ++/a5RqrtsDPVhumBANSY/h3wNgihMdX0G5BbPxCcNvDrl/HKK6+gY8eOiI6OxuDBg/Hrr7+6Xfett97CsGHD0KxZMzRr1gyjRo3yuH5doigK2rZtK206BOl3o1+Y6hMn9CJlXbvqr4uTmzVS3aaNvtyhAzBwoPgiPl2X0wns32+/MUGKVMvc/wC1AemXW78shGw/BzD9OyT1B4h602811XaR6p9+4o8PPMAfy8r8N9UiUi2oIVId8DYIsfRv+g3Iqx8IThv4/Mv46KOPMHv2bMydOxfbtm1D//79MWbMGJw8edJ2/XXr1mHKlClYu3YtNm3ahHbt2mH06NFIT0+v9cb7ipjAW9adjPS70S/mXT15kv8BQGqqfgIT0We79G8jEyfyx0su0cdmFxbqFcWNd7GDZKpl7n+gAbcBY/WyHzRY/fWE7PplIWT72RpRqUX6d0jqDxD1pl+c0z1FqoU5FjfqS0u9qxIeAFMd8DYIsfRv+g3Iqx8IThv4bKpffPFF3HjjjZg5cyZ69eqF119/HbGxsXj33Xdt11+0aBFuu+02DBgwAD169MDbb7+tzXdZ36iqirS0NGnTIUi/G/0tW/JHp5OnZgF8rHRiIl8Wptqa/i3MuOCee4B33wUWLuQFywA+fqpDByA5GejVSz/pBqFQWYPu/++/5391TINtg7vvBlq08H1qNh9psPrrCdn1y0LI9nMA079DUn+AqDf9NaV/V1ToM4KI64zSUu8i1dYptQCfTHWdtEGIpX/Tb0Be/UBw2sCnX0ZFRQW2bt2KUaNG6R/gcGDUqFHYtGmTV59RUlKCyspKNBdVDG0oLy9HYWGh6Q+A1jCMMTDGtOe8XXY4HKZUAG/fK76zrpf90eTLsqIoaN++vdbOjUGTL/0EAO3bt4dSPb2Wtk5YmFZVk+3ezR+TksCqTTXLy4NaWckLjhhQrfrCw4GZM6E2awaWkMDf+803wNGj/A3794NVR8JVw9QZrLSUf87//R/YrFlg1Sdqj5oqK8G+/x5q9W/Dm/4QqTAOh6Nh9VNpKXDhhcCFF4IdPVqn+x4AtGvXDg6Ho173vRqXf/6ZX2xt3+69pvR0YPfuOjsGBqIPGtoxwuFwoF27dtq+UNttJxomDocD7du3hyMELv5NBMhUh6z+AFFv+q2RamsEWhhjQDfV3qZ/i8/y01TXSRuIzwqB6Cf9BuTWDwSnDXz6puzsbDidTm2eSkFKSgpOnDjh1Wc8+OCDaN26tcmYW5k3bx4SEhK0P3ERlJeXpz2K5dzcXBQUFGjbJwx4VlYWTlUbl8zMTJSUlIAxhoyMDJRWRwbT09NRXn0X8ejRo6isPoilpaXB6XSa7nI4nU6kVZuqyspKHK02SuXl5Voqe1lZmTZvZ0lJCTIzMwEAp06dQlZ1AazCwkJkV4/bLSgoQG711Aj+agKAjIwMlFXPdexJU1VVFaqqqrR52xqDJl/7SVVVlJaWumoSUefffwcAlDZtisrqoiAlx48jf88ebmTDw1F+2WVAUhLyhg1zq6kyJgYA4Dx4EEbKc3J4exgKmBVnZ6MyNxf417+gvPIK1M8/96gpfedOYMgQKBdeiOL77vPcT4WFKLvuOuCNN1BQUIDs7GwwxhpWP4lx7ACqVqyo832voKAAjLF63/c8aVLFVCrFxV5rUkeNAk4/HTl79njdT4wxpKena/p81XSsukZAqB4jnE4nKisrcaQ666S2xz2iYSJufoTcjY8AVf8OWf0Bot70i/4SZldEpQXiGBETA8TF8WVv078FfprqOmmDEIpU029Abv1AkNqA+UB6ejoDwDZu3Gh6/v7772dnnXVWje+fN28ea9asGdu5c6fH9crKylhBQYH2d/ToUQaA5eXlMcYYU1WVqarKGGPM6XR6vex0Otnff//NqqqqfHqv+M66XvZHky/LVVVV7NChQ6yysrLRaPKlnyorK9mhQ4dYVVWV6zrnn88YH9nKGMDU5cuZOnYsX37nHeb88Ue+3KkTU51OxioqPG/XPffw9R0O8+du2cLXWbhQf27yZKb+9Zf+//nne9SkXnutvu7QoZ5179rF123RglVVVbG///7b9HtoEP20f7+u59JL63Tfq6ys1NqgPve9Gpc7duRt8PLLTP3wQ+b89deaNcXEMAYw5+bNdXIMdPn+559nang4Y2vX+qbvhx+YmprKnJ995nU/1dWy0B+IY2B+fj4DwAoKChgRGAoKCgLSpk6nkx06dEjrq5DhnXdM5wv23Xd+fUzI6g8Q9ab/+ut5P40cyR979za/vmMHfz4lhbHjx/myw8HY1Knmfvb0t3Ch/nnr1plfe/NNt5tWJ23w+OP8e2fMCNxn1hH0G5BbP2OBbQNvz00+zVOdlJSEsLAw7a69IDMzE62s40stzJ8/H8888wy+//579OvXz+O6UVFRiLK5AydC+MZB58awfk3LiqKgU6dOfr9XfG9dLfuzXb4ud+zYUXuusWgyLnvalvDwcJN+0zqW7AulZUugWTO+XFAApTpap3TsyO/SOhymNA+XbalO/1YskQalOuLmMNzRVsrKTPNjK2vXAjt3wtG/v60O/Pabvm71eG9tHcagXHUV/8yVK6GI9LO8PIRZ9v8G008iSgtAWb0aSnk5EBNTJ/teeHi4qQ3qTJOvy2Jc/ZYtUN5/H0rv3sDu3e41AdpYfEdxsRZBCPQx0PT9GzfydMetW6GMGOG9vtWroWRkQFm1CpgwIejHCOtvIBD7GNGwcDgcLsf6kCCA6d8hqT9A1Jt+kf7dtCl/tNZHEeff+HgerQZ49oEoVBoV5RrdtlKL9O+At0GIpX/Tb6BjsDcjqASjDXzK4YiMjMTAgQNNRcZUlRcdGzJkiNv3Pffcc/jnP/+JVatWYZCoihwEGGOoqKiQNh2C9HvQb7wp5HAAffuaC5WJyt/e/kCrTbULYiy1tfq3tXr+1q3271dVcxVyaxX9Y8eATz8Fvv6af6YwrKoKVlTUMPvfML4cpaX6FCR1QIP9DYiLMVGozHCTxRbj/uNDGnKt9IvvrE659hpx0VhPBfk80WD7nwgoIdvPAUz/Dkn9ASKg+hkDvvySn1utiP5yZ6rFsblpU33cNQBUD3NxMcl2eDLVkZEeNrsO9oEQS/+m34C8+oHgtIHPv4zZs2fjrbfewvvvv489e/bg1ltvRXFxMWbOnAkAmDZtGubMmaOt/+yzz+LRRx/Fu+++i44dO+LEiRM4ceKENkauPmHVY6pl3clIvwf9xkj1gAHcFNuZ6g4dvPsyd6Za3Lm2mmpL9geqx167kJlpvrOdl2c+kYvq5QA31IYoMMvLq5v+r6gAjh/3//3WY4G1LQJIg/0NiD6sHsdbowE19KupGE4N1Eq/+M6aIitWRFGeBmKqG2T/EwElZPs5QJHqkNUfIAKqf8kS4LLLgO7dXV8TkWoxXtp4Xgd0Ux0fb44qi+e9MdXVGXO269cwpjrg+0BysvmxAUO/Abn1A8FpA59N9aRJkzB//nw89thjGDBgAHbs2IFVq1ZpxcvS0tK0Ai8A8Nprr6GiogJXXXUVUlNTtb/58+cHToWXOBwOdOjQQdpqeKTfg36jqT7vPP5oNNViOi1vI9ViSi0rdpHqsjLXSLU7U33oEH/s0EE/wRqj1Xv3mr/LYL4cRUV10/933cW3xxhh/vprfnNi+/aa32811UbDaOXHH4HZs/02aA3yN+B06oVrxLHTenFmxU9TXSv9vprqrCx+06cBRaobZP8TASdk+zmA6d8hqT9ABFT/ypX80e6YXFOk2pj+rSh6tNoXU200sD6mfwd8H5g+HVi+HHjoocB9Zh1BvwG59QPBaQO/vmnWrFk4cuQIysvLsXnzZgwePFh7bd26dXjvvfe0/w8fPqxNm2L8e/zxx2u77T7DGNMq4MoI6feg35j+PXw4fwxk+rf4X5xkrfNUu4tUP/UUMGqUnnIrTHWnTkCbNnzZaKr37NGXi4pcItV10v9bt/I79q+/rj83bhywcycwbVrN7/fFVD/6KPDvf3PT7gcu+4AvVVjrCrs5y41G2w5jG/mY/u33PuCLqS4u5lPING+ur1/TjYJ6QPZjoCyEbD9b071rkf4dkvoDRED1e5r+ymqqKyrMN0KM6d+APq7al/RvP011newD0dHAFVe4z8RrQNBvQG79QHDaQKpbGIzxaXRk3clIvwf9xpPEsGH8UZjq3Fw9Uu1t+rc1Ui3mB69pTHWPHvxRmOoFC4A1a/TiZEZz37o1XzamXtdgqr3qf6cT+PBDwDIdmFvEBcLnn7saZG+GeVhNtCdTLabu8zNF3LQPrF7NL3JefbXmN65Zo493DjTuIrieIru1SP/2+xjgy5hqY1uJfaABRKplPwbKQsj2cwDTv0NSf4AIqH5PptpaqAwwH+eM6d+AbqrF+V1cY3giKUlf9tFU0z5A+mXVDwSnDaQy1Q6HA+3atZM2HYL0e9A/cCAwaBAwYwbQogV/Tpzw9u3jJ9awMKBtW+++zHonV5jqmsZU9+zJH3Ny+HeKOZzFY02RamP6t8VUOwoLvev/Dz/kEeauXT2vJxAXDiUl3Fgb8eaOti+RanExYpjb2hdM+8CLL/KL1ttv1y+O7Ni5k2cLnHaaX99ZI/Voqmt1DPAlUm1cR+wfDcBUy34MlIWQ7ecApn+HpP4AEVD93kSqmzTRnzMe54zp34Ce/i3ON8bx0nZYx2KHhZn/ryH9m/YB0i+rfiA4bSBVazPGUFpaKu2dG9LvQX9UFI8GL1yoPydMtajE3LYtEO7lLHTWSHW7dvzRnakWkWqjqRZRWcDVVHfs6GqqCwr0MbkAN6uG72G5uSjdv7/m/ve1+rYx/XjRIl4tVeDNnXhvTbWq8jG6gPsx554oLjbvA8asg1Wr3L9vxw59uS5+O7U11T6mf/t9DPDFVBuj2cabLvXByZNu+0n2Y6AshGw/B7D6d0jqDxAB1e/peCfMcWSkbnC9iVQLajLVdgXBjNHqGiLVtA+Qfln1A8FpA+lMdW5urrQ7Gen3Ub81yupt6rfde4WptkuFtTPVubnmtG5vItXGKDXgEqlW7r4bMd26gX3wgedt93CidqGy0myWvvsO+PNP/X93BduMeGuq8/P1i0y7SPWaNe6nonr5ZSA+Huybb/R9wHix9NZb7rfPOBWKMPWBxJ159mRCa5H+7dcxoKpKj9h4Y6qNfSqGB9RHpPqDD3jRwQULbF+W/RgoCyHbzwFM/w5J/QEioPq9iVSHhemG2c5UW8dUC+xuOhvngK6lqaZ9gPTLqh8IThtIZaodDgfatGkjbToE6fdRv/WE58sk8jEx/EQL8JOgSCl3F6nOzeXLvXrxx5wcV1PtdAJHj+rbIky1WG/XLvM2WEy1wHHPPZ633RdTbTR0Z5zBTe+LL+rPebogEQgD1rIlf3Rnqo3RaWuk+uuveYr2tdfav/eXXwBVhWPrVn0fMEZ4N21yv33GqKsxeyBQ1HP6t9vfQFkZMHeuOTIvsFarrwlhpIH6Tf+ePp0/utnHZT8GykLI9nOAItUhqz9ABFS/8Sai9eJc9Fd4uG6YjcdKd+nfAmOk+o47gKefBt59V3+uFqaa9gHSL7N+IDhtIFVrM8ZQXJ0CKiOk30f9iYnmdG9fTLWi6NHqZs30O9V2hcoEYWH6uN2KCmD/fv21rCxuVES6WUqKXqjs2DH+uHWr+fPcmGpWU0q28cRfk4EShikmho9HB8wXBZ7GRwtEm4hpzbwx1dZI9Vdf8cfvv7ePVlcbOnbqlL4PGE11bq771G6jQWyIptrH9G+3v4E33wSefFLvR3ff502k2rhN9Zn+bY0EWZD9GCgLIdvPAYxUh6T+ABFQ/cYbw9YZGcT5OCxMN7v+pn/37AnMmaOf14FaR6ppHyD9suoHgtMG0pnqwsJCaXcy0u+j/shIbjIEnTv79oVGUx0Xx5ftItWC5GS+XmQk/3/3bv217Gzd3EVH83WEyT96lBsdYaq7d+ePlnmqNWoaxyUi7ACPXHbtqlc/tyK2KT4euOoq19cDaaqNRtoaqV63jj+qKvDll67vFRc6p07p+4DR+DmdZvNsxPi8ccx6oKjn6t9ufwNffMEfd+7kxfncfZ+vplq8t7S0bsakGzEOu7CJ8sl+DJSFkO3nAJrqkNQfIAKq32iqrTeZfU3/tkaqjccrcY1gXKeWppr2AdIvq34gOG0glal2OBxITU2VNh2C9Puhf84cYMUK4K67+PyMviDuTnsbqW7Zkke4Rar477/rrxlNtTgRp6by71BVPo5ZpH+PGMEf3USqlZpMtfHC4ZVX+NRa333nul5ODh/nLLYpNRXo1s28jjdTatU2Up2ZaR7H/dlnru+t1qSUlOj7gDXC6674WbAi1f6Oqc7MBB5+WB9/b8DtbyAvD1i/Xv//44/df5+vplrAmHfDAWqDMRJkrIoPAE4nHIDUx0BZqJNzXX1cmAUw/bvB7+cnTwIrV/qt0RMB1W88H1pNtYhUe5v+bTw+NWmi30AX/1vXsTPVxkrjNaR/N/h9oA4h/XLrB4LTBlK1NmMMRUVF0t65If1+6r/kEuCll8xzUXqDp0i1nZESY6SFqXYXqRafqyj6vNbLl3PDkpgI9Ounf5dd+ndNFcyNFwXi/WIst2DnTj5/5iWX8P/FRcN555nXs35/ZSWPfi9frj/nj6k+dUo3d8IMinZbvdrVvBnSv7V9wGpGvTHVoRCpfustYN484IUXXN7m9jewahW/oBdFcj76yPx6bcZUu/ucukDUJgCAv/7SlysqgN69wUaMkPoYKAsBP9d9+SW/6fnNN4H5PHdYDWYtItUNfj+/+25g/Hj7G7a1JKD6jcdXT5FqX9O/4+KAiAj9f29NtQ+R6ga/D9QhpF9u/UBw2kA6U11SUiLtTkb661m/XaTaU/q3mMtamEPjBZUYUw2YU8aEqV60iD+ecYb+vW5MdY3RY7ttS0sz///SS+bPcmeqrd91//08pd6YKu6PqTb+//PP/PGaa/hFSkWFa0TZkP6t7QPW1Dzj53/0EbBsGV/2FKnesQOYP9/zPNc14Y+pNvZRYaE5iia20SZS7fY38P33/FEU+vrjD3NE2ttI9fLlfB/cvNn+9bosVlZWZu4ro6k+eBDYtw/KTz+hJCdH2mOgLAT8WL9yJb+xWQcG0EQA078b/Lle1AIRjwEkYPqtw4TcmWpjpFoc4+zOMcbU7qZNzTVbxI13b021w+Fxis+Q2AfqENIvt34gOG0glal2OBxISUmRNh2C9NezfrtI9alT/GQrTJFxW8S0W8JUGzl1Sp92y2iqxRRchw/zx4EDzVFxu/RvYWK//hqYOlVP4RbYGR+rqTamoBm3yS5SLQ5ou3aZpzoSFxz+VP82/i+qn3fvzlPQjc8JRPp3cTHfBwD9BkenTvxRRDlzcoDJk4Grr3Y1alZTfc89/EaBMKV2iKrt7tIcaxupdjrNF3ti2i/jxerx48B558Hx8cf2vwHRlmeeqT9njNB4a6qvugrYvh349Vf71+vSVIvfh8BY6M+QaZESGSntMVAWAn6sz8zkj+4yMAJFANO/G/y5XpwD6+CYEDD9paXmG6bWbTUWKrOmf5eW6v1ZF5HqGmbpCIl9oA4h/XLrB4LTBlK1NmMMBQUF0t65If31rF8UEuvcWb9TXVXFDYqoImoc32yNVFs5eJA/Gqt3i0i14JJLzOO37dK/hVkaNw5YvBj497/NK9hFqq3p38YUNEC/aOjQwXw33unUTZgohCUQxc/8jVSLcdXCDDdvrptqa5q2SP8uLkb5bbeB3XKL/proJ6tJB/hFtPGmg/Vzxf9282YLbrmF921yMvDtt66v13ZMNWA2wKI9jKb6lluAn34Cpkyx/w2IPkhM1C/q/DHVNVGX6d/C+AiMkWpDWxQdPiztMVAWAn6sD5aprkWk2mf9P/7Ih4HUF+K4VwemOmD9b60N4Uv6tzh+KopumI3nxrg4c6TZbh27+idemmq63iP9MusHgtMG0pnq8vJyaXcy0l/P+h98kBfNuvFGPXoMmKd8MhpoYaqbN9efi4jQI7jCVNtFqgFuDocONaeau0v/No45tkb43JlqY7uJsbcCYaoB4O+/zXMdi22wTnUloutWU11WZn8x6S5SLSKzXphqHDmCqNdeg/LWW/z/iAh9ChPxecb3FhR4jlQLw21Mc8/I4BF5cQEmora5ucCSJa66ahupBswXf6I9cnP1vvzxR+1l29+A2P4mTfS+NH6mpzHVIppmHM/sjkBdQP/4o+u4b+t+fOCAvmy4KVR18qS0x0BZCPixPgRNtU/6VZXfkL30Up+m6KsVdRipDlj/W/vbm0JlQo9ox7g4PSPNGIV2l/5tvFYQ5zMjPphqut4j/bLqB4LTBlKZaofDgZYtW0qbDkH661l/06bAhAn8zrMxPUxcoBnnsgb09G9hrgFg2DDPpto4zdcFF/CTtzDVhYW2BlkpKgL27tWfsM6daWeqy8vNpth6sWHcptRUoH9//aQvDKDVFB8+zM29iNq3auV5G8T7xUWFNVLdrFmNplqxbkN8vH5jw12k2qg1N1eP1DKmG1ijqX7gAV6E57XX+P/Gi1S7YmieTDVjPLXc+j5vItWAXgHboKFlVRUc335rvkkitj8uznX8v/X7jJHqb77hfb9kCR+HXRNGraoKnH8+cOWVNb/PSFUVMHw4T9H/+2/9efG76tKFPxr70WCqm6mqtMdAWQjosZ6xkEz/9kl/SQn/vVdWug4HqivqMFIdsP73JVItzqOFhfx4I44/4vwNuKZ/G1PLRaQ6MhLYs4fPaGHNCDOu50X6N13vkX5Z9QPBaQOpWpsxhvz8fGnv3JD+IOsXZkVE1GJizGZFVP+eMYNXbv7yS25akpL48yLyZjSwERHA2LE8bffRR83fk59vPw1MaSmwZYv+vzVt1l2KrnFctfXi0hipFoiTvzBswgQLPYcPm81oUpIeAbeLsAtjedpp5v+NkWoRdbaaancVq+1MtXEqJqupBvT2KivTI/5GHaIa+bZt+mdYNRjxZKpXrwYuvBC4/Xbza55MtWgPgKc9G26GMEVB5bRpwMUXA5s26esZTbVdpNpqqsV+dfXV/L3XXGOuVu8Oo9ajR/n84p9+6tNc2ybzbuxn0S8DBvBHY6aGIf275NgxaY+BshDQY31xsb7fhlCk2if9xuNXXVfoF9Rx+ndA+t+f9O/nnuM3xt95h/8vzleAa6Ey4w1KY52SHj3MWWhGfIhU0/Ue6ZdVPxCcNpDOVFdVVUm7k5H+IOsXaV3CtMXFmcfiipNkbCwwezafbiQyUjeh4gRvNNUAH6t85Ig+NtiYPia4/HIw45RgGzboy+4qZVvxZKqt22TcDmFshNZBg/jj4cP6a5GR/E9cWHgy1d27659XWakbMmOk2hilrKx0f3FaU6Q6O1tvDzGnqFjPGNERF6Xp6XpUdPdu1+qxubl83nPjdFeeTLWYf9s4Phhwn/5tjJ4D3Ez+9ptpVYfIeDBWB/clUq2q9tXOvYlUGy/Yjdvpy1RlxsrixpsU4mZVly76hafYtw2RapadLe0xUBYCeqw33nQMIVPtk37j77u+THUdp38HpP9rSv8W/0dGumZ8ffghf+zWTX/OGqk2muqwMO+2yQdTTdd7pF9W/UBw2kAqU+1wOJCUlCRtOgTpD7J+YVaE+WjXznWcsR3G9DHA1cBGRJgjxVZTHR0NLF8OJTtbH8P100/668J4MMYNqLuLKmOxstpEqo2m2mjmjO+xmsbSUv0CRlyk5OSYjVlion36t6eLtpoi1UbNIpNAbJvxu4UOY/R3zx5uTI0Xxjk5wPXXA/fdpxtlT4XKhA6xn2Rm8owEYYzF+HthgK3fd+wYj3ZXozCGMKHP7qaAu0i1dZ8QF4PGGzW+pn8b289ard0TRlNt/P0I85OS4rofGCLVTcrLpT0GykJAj/XBNNW1SP/2SX99R6orK/Ubc3XwfQHr/5oi1eIY2qyZq6kWeIpUixu1vuBD9W+63iP9suoHgtMGUrU2Ywy5ubnS3rkh/UHWL4yjSJPt0ME+PdtKhw7m/+2iwkaMRVMAblQVBSwiAqowrcbphoSpvuoqbvSNphLQjZuv6d/WSLUwrf6YauP/bdvyx7w83ZglJPA7/Xam2l3qt9huYaozMngBLKN+oblJE73quthmO1P6yy/6c+Xlegq4ICdHN4IisuopUm001YwBd90FPPWUvo4Yhy5MtdGoAsDDD+tzigvEGHaxbmWlbpK9iVQLbWJ9ga+m2jj2O9CmWrRLRga/MDZcHJcdPy7tMVAWAnqsN5rq8vLaVb+vCauJrkWk2if99R2pNh4H6ihSHZD+9xSpLi3V9wVvTbU1Uj12LHD55cC8ed5vk9181jYE/XonyJB+ufUDwWkDqUw1QQQVUd1ajGc2FiSzVtM2ItK6BTWZasAcQTSM1WJ2qeG5ufzC8dNPXcdXA8BZZ/FHT5Fqu20yRqpLS/ULt4ED9e8VZsqbSDXAo/Ji7s7cXPN0WoA+pvrkST0S4m2kOiODF8AyjjcXpjohwXXbaopUA8DGjfxRRCiMB3fRhtb0coHRVJeX8++wTnkj9iFxk8SuAreiAI895jrnqbgpYGxrb8ZUA/rFpV1Ve09pjO7Svz2Z6t279XoChYV6SrzxOxnT26BtW/PNFct0cA7rjQeC8IT1mFiX0eoApX/7TCMz1QHDGqm2y7QJC+PnW7uiYoDn9O/wcH7efegh77dp5Eheqf2OO7x/D0EQ9YJUplpRFDRv3hyKJwPTiCH9QdYv5pQWFzAdOgDLlnGzvWaN+/f5Y6qNZqfaDCqKgjDje5OSuEkFgE8+cf9ZZ57JH/0tVFZcrEepw8J4GrUwsmLqLW9NdUyMbqBzc81FygBuHMPCzBV7PV20NW3qfl5wwGyqxTbaRarF9grDJ9pMmOrkZHMhGuP7hUEVY+fF/mk01QC/UWA1xhMn8sePP+ZRLjvDOGcO8MQT5qnaANfK5eHh3Nh7E6k+80w+z7k1spaaqt88ssPX9O+CAqBvXx7tcTp5NNx4Y0KY6r//5uY5IoJvmzDVK1cCI0aYPjKyqEjaY6AsBPRYHwxTLYbp+Jn+7bP++k7/rmNTrZSVofmOHVDsaj/4gqf0b3H8Skzkx2y7yHFkpPnmuTX92x+aNeN1OcSx3w1Bv94JMqRfbv1AcNpAKlOtqiqys7Oh+nmiCnVIf5D1W6t5tm/PpxPKyOBTC7nDaqpFGrInbCLVqqqi0nhSb9dON0BLl7r/LGEQRcTP6XSt1lxToTJhqkWF7zPO4P9/8AF/FBFmq6n+/nsePRZp1DEx/KIC4Bc1xum0AD6lmNAkDGlN6d+eblJ4MtXWSLVxOpqRI/mjMNUJCa6m1hqpFmPnhbk2jqkGuIE0zi8O8Orb8fF8Ozdu1LepSxfeVgMH8ig14LrfWOfYjovjfWOMVKsqN+VffGF+b0YG8PXXrgXGunWzL5Qn8NVUG58/dMj1poEYp79uHX8cPJhHjISpXr1aX6d6H6nKypL2GCgLAT3WB8NUi6wVPyPVPuuv70i18TvqIv173jxg5Eiob77p+uLixeZCnZ6wztZgZ6rFucfOVHfpYs7csUaq65CgX+8EGdIvt34gOG0glalWFAXh4eHS3rkh/UHWLyLVAjFWuqbtSU42pwb7mf6tKIr5+bZt9bGndhcZI0dysy9MdUYGN3V20x/Z3XU3pn8LYyOiwkOH8kcxz/Dw4eb3iIu8Cy/k45xFqps1Um1N/wZcx1V7umiLjfWcriw+35j+7W5MtVhXUfSUeXHhZUwzF4j3i+2bMQMYMwa47Tb9dWM6d0aG63j3uDg+Jg8Ann9eNwC9evF1N2zQC9pYTbXYNtHW4iLPGKleswZ45hm4xVpo77TTPF8sGi+mPY2prqrixdiMJnrPHtfIkfj+tWv5o7g5JfYBweTJwFtvAQAc+fnSHgNlIaDH+ppMdWWl3xFlFwJkqn3W38gi1WK4iLJvn/n5Q4eAqVP58UDgqY1FQUhxo/bAAZ6u7XS6mmq79G/jeGogMJFqLwn69U6QIf1y6weC0wbSmerExERpdzLSH2T9VlNtTAvzhKLoJ27AO1M9bJi+XH2yVxQFEUZjZTTVVqKjeZT4hx94BDUqiqfdpqfrxiYqihfCevBB8/YJhLn66CPgvff4sojCnnuueV1hhoym2nixI4yfMVJdWalXdTZ+v/gOYdo8XbSJyMMXXwDPPstNvB3GSLXYFqupFlGN5s31SuECO1NtjVSfcQYfM33BBfz/w4fN6+/caX8BeNNNfB/58kt9PutmzfifsUKsN5FqQL/YKyw0a/SG004DOnd2/7q3kepZs4CuXYG339af+/NP/YaOiOqLAm5WU23cr+Pjgf/9TyuQ58jNhZxHQHkI6LHek6kuKeH7vLvjhq+I37cYllOL9G+f9DeySLVSfWxRRDFIgTjOHDvGb9z9/Tc/Xj/wgP0HiVoOffrwx0WLeHbZzTd7jlSffjrQuzcwc6b58+oxUh30650gQ/rl1g8Epw2kMtWqquLkyZPSpkOQ/iDrj4vjKdcAP7kK8+cNRtMoLrg8ce+9+nJ1CrKqqigzvrdNG/em2njX3eHQt3vXLr16eUIC8K9/uY9kCoO8axc3NYCuefBgPULcvDkfN2t8T3Ex8Pvv+meJ9PCYGL6O0CEueoyRamEerZFgO4RJu/RSfmH13nv8hsTVV5vXa9as5vRvYzTe2q4JCTVHqsUFl2h7a9q6sYCakXPO4WPsjNkM1lRzsQ1232811SL9u6jINf2xJrp149XGzz3XfkiDJ1NtHCv9xhv8ceFC/TljpFoY96wsXg/g+HHefmefzZ83RqrPOYfva6JNysuh2s2DTjQaanWsX7aMm+R33uHT14ksHrFPGU31mjXAkSP85mMgKsxaTXUt0r990u8pUp2dzX+PgUx79zZSXVmpR4t9gFUf25j1hojxJmFODh8aU1jIs3ysFBfrJlycnwTvvKMPiRHnG6Nhvvhifp6cMMH8vnpO/6brPdIvq34gOG0glalWFAVRUVHS3rkh/Q1Av4hWt29fc9q3EbtIsCdatODRPkBLJ1YUBYrRWLVtazZiN96oL1vHhwlTPWECL1AF1BwxtxbmAnRTHRcH9O/Pl4cP58bd+J7iYvNc2sKAxcTwdhMGSVxwGdvHaqo9janu1Mn8f+vW5nRzQefOnguVGU11UpJrsS5v0r9Fm7ubKkWY6qFDgWnTdOMJ8D4ZPVr/325/cZf+7SlSbY301MRpp/GbNT//zNPZrbhL/y4t5TdnsrPdR+eMprpLF/3zxH774IN62xlNtTDacXFg1WZFsauSTjQa/D7W//kn8I9/8CydG27Qp6+77z59iIrRXNpNq1cbApj+7ZN+T5Hq+fOBW24BXn/dr22xxVtTfdttPGPl5599+3xxbLMev4z9dfKk55uGYmhS8+b6TV0jn37KH+3Svz1lgAnqIf076Nc7QYT0y60fCE4bSGeqExISpN3JSH8D0C+KlVnnnq4JX001APznP9wgPvIIgOoDjDGC2bYtMGUKP7nPn29OYbSOD7NLVber+G3E7k680VhedRV/NI5vM5pq44WU0VQDensIU20Xqc7LA558kqefW5k/H5g9W7/xYMV6w6BrV89TapWV6WmiSUk87drYZ/HxNRcqq8lUi8/v2hV4/32e9m3kssv0ZbtItdVUFxRwAyvMgNBnjFRbx0yLtGs7FEU3u4A59VzgLlIN8KEE8+bpF7NWjKa6TRs9mldYCHTvbp6WxpgF0q+ftn1KdbsoNK1Wo8bvY/3Mmfy33K8fT9+94AIelXz+ef2YYDTVRtMWiEhuANO/fdLvKVItorXWeg61wdv07127+ONvv/n08Vr6t6dIdVaWuUaDNdNAZEF17Wo2w1bs0r+tNR0E9Zz+HfTrnSBC+uXWDwSnDaQy1aqqIjMzU9p0CNLfAPSL8bLnnOPb+4YM8f27FIWnMhuqf58yHlzatuVRz/x8ni5uNCJWUy0i1UZqilTbmSrjex54gKdOGqcGEXfvCwrMcz6LCx5xUSJMozC4RhMpLnLWrwfmzrWdLky95BLghRfcG1irNmMBLrtINcC1APqNA2O02l36N2P6RaVoc+s2Wf93d0Nm/Hh92W5cpNVUM8YvKr2JVF95JZ+2q3t3++8GXC8+fTXVAO9zMbzASlERN9YAN/7G6cXuvNP8fQ4HcNddwNixemYFAFbdB6r1ZgHRqPDrWJ+VBfz6Kz9ufvMN3w/XrAGuu46/Lo4Jjz/OM1datzbPGx8IUy22NwDVv33Sb4xUW02u0OVrfQVPWCPV7lLnRfaPqJ3hDYyBiWNLTg4fOy3wZKqtmQa1MdXuItVxcbyI5Gmn2d/4DCAN4noniJB+ufUDwWmD8Hr7pgaAoiiIjY2V9s4N6W8A+i+7jN/5d3fSdcf993MTYjROPqIoCiLCDT95UUxLpF4bTZ83keqaTLWdaTKavbAw188V5n3fPn0KLyNWUy2wS//eu9ftpik1Rf6t2rp00c3ekSPAqFHA9u3mdYSpFjcnWrXStyE+3jXb4O+/+U0W6w0Da9v36WOO1Lgz1Skp/GL/77/tCyfZTcWWn+95TLUwn1dcwceZV1fQNtGzJ0+XHzDA/Lzdhajof1XVL3Dnz+c3ODIyeBu6M9WA3g7x8WYjcMUVruu+9JLrc716wRkeDke4VKc+6fDrWC9qOAjDbMV4TDh0iD8ap5QLhOkMYPq3T/o9pX8L4xlIU238Dsb4rBJ2N+FEerbducDDZyuVlfr/WVl65Nh4Tjp5EjhxQv8/O9uckr1/P3/0x1S7M8wOh37e8DTrRABoENc7QYT0y60fCE4bSHVloSgKmtbxOJaGDOlvIPrdpYZ5IiqKG49aoCgKoozji63pZ0ZTbY2O2qX9uovyCqZO5amT118PhIcDb77pmrJsRRSgcpfuZ03/Fhij7MI8ehhLrdQ017dxP4mM5EZXpEfv3Gn/HjtTLbAbU52Vpc+vHBHhPv177Fhze4jpuuzYsoVXDT/9dNfX7DTn5bmPVFdU6BEiERV2l31w662uz1sjx6rKo1JlZXzMqriZMGsW30eaNeM3nMTc3naIsdBNm5ojg17epFI++QR1eylLNAT8OtaLVGMxXMBKTTcRG1j6t0/6PaV/13WkWvxvPbZUVurfbWeqGeM381QVeOUVvUaJ9WZuZqZ+zjVqOHhQj4QDfNlYY8NdpLprV34zRdyIEOci4/nU08wexjomdUiDud4JEqRfbv1AcNpAuvTvjIwMadMhSD/pL/Y0ltRo+ox3+gE+3RNgvrtunfLJSvv2/MJkzhwead+/33WqKSviosZdhMYuUh0ZyatOC2owzGpMDNSaIpVGneKGgrsxcOKzRHuIdjQavYQEPR3c7rvfeUe/2LJWd7/mGn152jQ+1tMdzZrZG2rA+0i18SQkxjeLNrAz1e7G1hsvRIUhycvjc3GLjIvYWP6ZiYl6e33zjetnWSvlx8fz4lEArx3gJbIfA2TBr34WkWpvTPX117u+HkhTHYD0b5/0BzNSDdiPqzYWE7Qz1StXcjP92mvmac+s22kc92587ddfzesZDTagH/u6dDEfy5KTzdlCwlSHh3Ojvn+/fZHOekb2Yx3pl1s/EJw2kMpUK4qC+Ph4adMhSD/pV+6/HywlhU8lYsV44WC96GnXjkdyRDQW8DuK4pGUFHOk1hqRtjPV/fqZ7/67M9Wi332dt7AmU922LX8UqaDuItVnnMFvLrz6qvn9CxbwisPW7QR4WnP79no7PP2099ttxdAuTGyznakOD9fbWVzUe4pUuzPVxnW7d+fR6oMHefE8gbEfRRE/O6yvxccDTzwBbNvmWqndA7IfA2TBr36uKVJtPN498YTr68JU//23/5XAraa6FpFqn/Q3hEi1FaPJzcgwj41mzNwHRuNsF6kWGDVs3mxez1gJnDH9eN66tfmclJwMdOyo/288R3XuzCPZDQDZj3WkX279QHDaQDpT3aRJE2l3MtJP+mN79oSSkWF/UWjE7iKnb18eaf7sM26SFiyoi400p+D16WN+3S79e+BA8zruTHW1gXM0axYYU33eebxQkTWt285UJyRwU/ncc3rhI4HdRZiIRj/8MNe8ZQsfx1lTpN8ThnZRxHfapX8DrkZZaLIbW+guLdZoqnv0AN5919WUGwsQienmAD7HtRgKALgWSGvalEe53UXl3SD7MUAWfO5np1Mfy+/OVI8fz1979FH732FBAZ+Sq1s3Pk+xPwRonmqf9dd3pNpXU62q5vHrP/0EbN2q/++PqbbesDB+X0EBH/4C8Bu91ki1O1PdgJD9WEf65dYPBKcNpDLVqqoiPT1d2nQI0k/609PTobqrtGrE0zQnEybwAlyexvbWBqOp7tvX/JpdpNpqqt1d5FRHW8tjY73bB8TnXH01f7Sm9M2cyVOZ3Y1Nt0aqBWFh5hRr4zRUgu++4xf5QlvnzuYLOX9o1Qps0iQUT50KJgox2UWqAfP2JSbqkTN/07+jooDp0/l+s2OHXhzPiNFUz5xpbj/ja56+swZkPwbIgk/9LOZILyvjN2qMN3OMNGvG6yk8+aT+v5GCAn6Tzel0NX3eEiBT7fN+7i5SXV6um8uiInO02B2VlTVH2L1J/7bOIW1MAbembnsy1e7Sv60YTbUw4k2b8nOO8VjWsqX52NRATbXsxzrSL7d+IDhtIJWpVhQFzZs3l/bODekn/V7r92Sq6xrjRa27SLUxGm011e4ip5dcAnbuuVBuvNG7Nti6FVi0iBs8wNU8u4tgi6iucUotqwkUF6qAvVlu3drz2Gl/UBRgyRI43nrLPJe3iFIZdRjb11ikzt/0b2HKO3YE+vfnxciaNAH++U99HePNhSuu0C9cY2Ndp3Tz01TLfgyQBa/7mTFg8mTg//6P/3/OOfY3fOy44Qbz/wUF5syL117zfoMFAUz/9mk/dxepto4TF1Hrigrg5Zddp7pyOoGzz+bDNax1OYxYzy9vvKEXbRRYxzgbTbUY/y4wTpHnKVJtV1Nk8GDX7xPvEcdwa6TaeI7x81hU18h+rCP9cusHgtMG0pnqmJgYaXcy0k/6vdZvN89xfWGMVFuNpTDVRg1W4x0VZV+ZvEMHKD//jMibbvKuDTp14kXCxLremGpF0SMXxmi69cKrvNy8vfWEtg+IbXMXqR49Wl82zgdtLaIGeB+pNjJ4MP9uYWYAXuV8zhw+vKBJE/2CtkUL8zYA5ki6D8h+DJAFr/t5wQLgyy/5/vmf/9jOae+WJ5/kxlKM6S8oME/jt3ix72OrhYkOQPq31/s5Y+4j1cZ5nAE90nv33Xxu+MsvN7/+22+8zsFff5nrb1ixnl/efhuYNMn8nDVSbfw8YapFlW27SLWYmlAYZMZcI9VRUXomki+m2ljd29ubMPWM7Mc60i+3fiA4bdAwjwZ1hKqqOHr0qLTpEKSf9Neo/667+OOzz9bPRtlhNNW9eplfE2b5ggt4RGT2bPspSuzGVcfE1G4fiIy0rwpuTAtv1kxfp317fvH54IMNohosYNgHjNW47Uy1cYos41yuxpsBQpM3kWq7GwfWKugOBy/ENmEC/19EqpOSXKt/uysaVwOyHwNkwat+ZozXOAD4dIV33FHjzAEmoqP5dHBiDLbVVJeWAhs2+LbhAaz+7aK/vJyP+bYO/ykvN0fEPUWqhSkVUfgtW8yvGyv3G48bVuwyoU6eNJt4a6T6wQeBiRN5Cvqff/LnRo7U32vZxrL+/fn/69cDaWn8OGfdHwYN0sfH//orsHAh//yaTPX48XzKyPnz3WsMMrIf60i/3PqB4LSBVKZaURQkJydLe+eG9JP+GvW/+CKv0HzzzfW3YVZE5LlbN9fxauLiJjoa2LTJ/dzddhfH0dG12wcUxXyRK6KnRoMnph4T/PvffLymp8+sRzT9ol3dRarbttWN7Gmn6c8bL4aF6fUl/dsXxNyyycnmSHXTpn5Hh2Q/BsiCV/28fz8vfhUVZT9FlreIG1QnTujT6o0dyx/Xr/fts4qK+KPIxKhF+reL/jvu4Jk/a9eaVzamfgM8tVuMnbaLVFuNvtGkr1qlLxsLiwlycoAZM8zrGTGmeAtTLdKzAeDTT4E9e/iNgCZN9LoeNpHqsEsuARsxgo+Vf+QR/YaA8Vh04YX6ce7oUV5E8qmnPJvqFi34jdP//Q+49157HQ0A2Y91pF9u/UBw2kA6Ux1dfWEtI6Sf9Neo3+HgY5qD2UZdu/JCXV9+yS9ejBc0dmnddtgVj6lOAwrYPiC2xZhS+Pzz3r1XzKs9ZUrtt8MHNP1GUy2iUdbo7/btwC23mKu8G0210OCusFNEhL4f+ZPiPmECcNVVPBvBWGHdz9RvgI4BsuBVPwvDO3iwfVV7bxGmessWbjCbNdNTiles4NHwAwdq/hynU4/uihoCtUj/dtEvCqdZo8vipppxXfE7t4tUi6nHBMLQZmXx9G+BnameMAF4/33jhppfT0vTl0X69w036N/pdOqGvE8f3fTajKmOaNkSiogkL1rEU9IBfsN13jxeof3ee12zYObN44XmAP3zjcevBjqG2orsxzrSL7d+IDhtIJWpVlUVR44ckTYdgvST/pDRf+GF+jRKYmwc4L2p9pD+HfA2uPRS/jh7NjBggHfvWbUK+Ne/XOesrmM0/cIIZGTo6Z7WqcHatuVpnsI8A2ZT/d57fM7pQYPsv0xR9ItRf0x1cjIf4zpmDDfook9rcVEbUr8Bwm889jNjPNV35Ur+//Dhtfsya2HEHj2AESP48u7dPG1ZFDv0xMmTPDLtcOhZILVI/3bRL4qKGaPBgB6pNt6IFMcEu0i1cZ55gEf8AZ76bYxaW031ihXAzz+bn7Mep//4g0eAy8v1SHVyMp8FQmTMfPklf+zXTx+Cc/Ikj0iPHq31a1ZVFdTTT+fmmzG9vxMTgYceAr76it9INJrqsDAeqRc3XISpjovjQ5HatTMPT2rAyH6sI/1y6weC0wZSmWpFUZCamirtnRvST/pDUn9tTLXRKEZG1k0bXHkljzC5S0W3o1MnPge1u0rldYSmX1xAi+I/Dod322I01S1bAsOGeV5fRAD9Sf+2IlLAa2GqQ/Y3QPiEx35+9FEenRbmrC5MtbWi/88/8+m4NmwAbrpJTxM3cvw4f0xJCUj1b5P+igo9omyMBgPm4R/iWOvJVIsorkCY6uXL+aO4gWYdU71woeuGGos5AsD99wP/+AcvGici1eIYLtp040b+2Lev2VSvWQOsXq19VGKnTly/OEYJU23NYjKa6qeeMr8mTLWi8MydgwftizU2QGQ/1pF+ufUDwWkD6Ux1ZPWFtYyQftIfkvprY6p79tSfKy2tuzYwTp/VgNH0i4tZEQlr3ty7ccqigJkoEFQTtYlUWxEXv7VM/w7J3wDhE7b9fOoUr2/wr3/pz0VGAkOG1O7LrKb69NO5Cbv/fl6MT0RYBwwAhg4F3nqLV862IiK7rVvrxQ5rkf5t0i8MO+Bqqo1T6llNtV36t0jxFsfWAwf4WPBvv+X/z5pl1gPwSLEo2ta6tf68NTtG8NtveqRa/O6FqRY3Gvr21W+0FRXxyLMgLAwRvXpx/UOH6tsJuEbHo6OBZct4ivisWeZilMbjemRkyBhqgI51pF9u/UBw2kAqU62qKg4fPixtOgTpJ/0hqd9YOdtXU228gIuMDFwbGC+8QghNvzXa6+7i1sollwD79gFff+3d+oE01QGIVIfsb4DwCZd+rqri5mrOHP7/rFk8Kvnee+abdv5gNdUXXsgfn32WG8N33nF9z4oVPOppRBjf1FT9Blct0r9N+o3zSVvTv8VrzZvrbSEyUqyR6v37dVMupsDav58fD8rL+Q0Eod9oqg8e5NHkyEhep0Hgru23btVrVVhNtaBvX972wuguWcIf33wT6t69OOx0cv3CVAvshgZdeSWfPjEuzjycJURultoh+7GO9MutHwhOG4TXvErjQVEUtG3bVto7N6Sf9Iekfn8i1aIq7JAhPL1z61Zg1KjAtUFtL8SDhEm/ouhjIL011YB5jHVN1EX6dy0j1SH5GyB8wqWfP/+cp18nJvJo9Y03Bm5+YesxSdSCUBRu+oYO5bUJDh/mUez33uM1Ff7zH3MRQGGqjZHqWqR/m/QbTXVODo9Oi5uVmzfzx0GD9Ohwejq/6fDFF/z/Jk34e9as4f936wYMHMiXDxzgUV6Am1NRtd9oqsVY6kGDzCbXWnlc8Pff/DE11TX9G+BtJJ5v2ZJvrzDhEyZASUpCW6eT62/fnt/UE9MBTp1q/52CgQP1NglhUy37sY70y60fCE4bSGeqHQ6HtDsZ6Sf9IanfH1N92WVAbq7L+DkFqF0b3HYbLy4WzHm8a4G2D4gx1OJC1BdT7QuBjFSLi/VabGvI/gYIn3Dp5//8hz/ecUfgpws07ktnneVa0VpRzNHZZs24qX77beD//k+/WWRM//YnUl1SAqxbBwwdCuWXXxC2di2Um27i9RuMphrg33/mmdxwCgN59tl6ETIxV7ygQwc+N7Sosj1wIB87DgC//85vWADA9On67zMrC6is5JFkkfp97rnmaQeNc2LbcfbZensaTXXfvvqyMNUA0L8/kJzsepyfM4fvA2+8weeY9oRx+6wzIoQQsh/rSL/c+oHgtIF06d9paWnSpkOQftIfkvr9MdWA7bRatW6D//wH2LvXfJEcQpj0G9Mg68pUi0q51tRNf7j5Zj4Fzh13+P0RIfsbIHzC1M/bt/PiWuHhdf+7vfjimte58EIesS0pAV58UX/eLlItptn6+GNz1PrYMeDdd/U07Q0beOr1uHHAPfeATZ8O5Zln+HR3b7+tm07BVVfxAl6nTgE7dvDnzj7bvcnt0MH8/6BBfOrDsWN5ar2q8s/r0YMfS8Kr4zWZmTwbRsyNfe65/Gbehx/y6H1NpvXss/Vl4zGkXz99uXdvfXnGDAA2v/O5c3kU/qqrPH8fwCPZo0bxMfEhjOzHOtIvt34gSG3AQoCCggIGgBUUFNT6s5xOZwC2KHQh/aQ/5JgyhTF+acZYaWmtPy4k2yCAaPpPP11v13vvrZsvy89nbNu2uvlsPwlU/wfyvERw6uRcP30638evuabWn+mWd9/lx6mSEu/W/+ILvk1xcYwVFvLnzjiDP7diBWNffcWXBw5kbOhQvvzf//L1li1jrGlT/tyttzJWUcFYx476bzkyUl+u6e+pp/hjq1aMqSpjY8fqr40bpy8/+yxj4eH6/+vW8W3Zu1d//oMPdH1t2/LnLrqIsddf58tNmuhaBb/+ylhKCm8/8dnNm+vL69fr6zqdujbjd+XmMvbNN4ylpZk+WvbjPGPUBqRfbv2M1f/5Xqr0b8YYVFWFoihSpkSQftIfkvrF2D/jvMd+ErJtECBM+usjUp2QwMeRNhBk739Z0Po5KwuKKGBlV3E7UMyc6d1c1ILx43mk98ABnop99dXmSHVZGV/etYunVzoMjwABAABJREFUUAM8S2biRB6NFdNgvfcej0YfPswL+BUW8umzALBzzgHOPRfK88/r39u0Ka+ULfi//+OPgwfz4+uDD/JhFo88wqPgoqL2aafxKciuvpqnc4sU6e7dgfff5zUrJk/WPzc5mUfTV63ifwAvBGath3DmmTztXVF4lHvXLj7m+5FHeLTeWDTM4eAV1LdsMVdsb9YMuOgi08fS75zagPTLrR8IThtIlf7NGMOxY8fARHEeySD9pD8k9Yv07+ho1/GKPhKybRAgTPqN6fF1ZaobGLL3vyxo/fzee9xkDh7M/xoKigJcfjlffuABoEsXfV7n1q152vOwYbqhBoC//gL69OGG+vTTeQp0aamepvzoo6Z54wv69webN4+nXAvEcAwro0bxx+HDebXyzp2BXr301/v25aneBw5w42s0x9dcA7zwgnm6KfGdxht37sayi2P6kCF8HWGkzzrLtSDkypU8nb9rV/vPqoZ+59QGpF9u/UBw2kAqU+1wONCxY0c4AlX1M8Qg/aQ/JPWLCytfxlO7IWTbIECY9NdHpLqBIXv/y4LWz5s28SeMUdSGgigGdviwXu0a4FFeh4OPhY6O5hHbSy7hr508yR+feAK47z79PX368Dnkhw/Xnkq8+GI4wsK44RW89hqPDovoPQCcd579WHNF4dHzLVt0E9uqFdCuXc3aFizgEeidO7k5v/pqvVp4TVx4IbB4MfDBB66vJSebx1O7gX7n1AakX279QHDaQLr078rKSkREREiZDkH6SX9I6g+gqQ7ZNggQJv3GSHXz5sHbqHpE9v6XBa2ft2+HApgrOjcUjEW4AB6h7d9fL1LWrRuwaROPRvfqBdxzD0+lPvts3WSrKs84GTuWR4qHD+dTYQGoGDQIEYxBGTwYWLiQp5Sfcw7w66/8vRUVvIDbCy/ohcWspKbqVfd9weHgBhwA/vjDt/cqCjBliu/faYB+59QGpF9u/UBw2kCqWxiMMWRkZEibDkH6SX9I6g+wqQ7JNggQJv0SRqpl739ZYIwh888/oaSl8Sf69w/uBtnhcPCp+ZKTgfXrgd9+49FpIwMG8LTohARe7fv4ceDTT7nxVBQ+hdWll+qp18OGAZdcAnbXXcgoL9f38xkzXKPR06YBb73Fx2I3Muh3Tm1A+uXWDwSnDaSKVDscDnSwTg0hEaSf9IekflGoLEDp3yHZBgHCpF/CMdWy978sOBwOtMvN5f907sxNaUPkgQf4X6CIigJWrIACQOa9nH7n1AakX279QHDaQLpIdVlZmbR3bkg/6Q9J/QGOVIdkGwQIk35JI9Uy978sMMZQKdKcG1D1+fpC9v1cdv0AtQHpl1s/EJw2kM5UZ2VlSbuTkX7SH5L627Thj61b1/qjQrYNAoRJv4hUx8bygkgSIHv/ywJjDBXCVA8YENRtCQay7+ey6weoDUi/3PqB4LSBwkKgxQsLC5GQkICCggLEN8LxPwRBeEBVgRUreNXaABhroprffuPT1nTqZK4+THgFnZcCT0DbtE8fXiRr5Upg3LjAbCBBEAQhHd6em/yKVL/yyivo2LEjoqOjMXjwYPwq7gjb8Mcff+DKK69Ex44doSgKXnrpJX++MiAwxlBaWirtnRvST/pDUr/DAVx2WcAi1SHZBgHCpH/gQGDOHCCIx+T6Rvb+lwWmqqgcPRpsxAhp079l3s9l1w9QG5B+ufUDwWkDn031Rx99hNmzZ2Pu3LnYtm0b+vfvjzFjxuCkmD/RQklJCTp37oxnnnkGrcQUC0GCMYbc3FxpdzLST/pl1g9QG5j0OxzA00/z6sGSIHv/ywIDcPLee8HWrJEyu0X2/Vx2/QC1AemXWz8QnDbwOf178ODBOPPMM/Hf//4XAKCqKtq1a4c77rgDDz30kMf3duzYEXfffTfuvvtunzaS0uwIgiCIhgSdlwIPtSlBEATR0KiT9O+Kigps3boVo0aN0j/A4cCoUaOwadMm/7fWQnl5OQoLC01/ADfwAL/7IO4FqKrq9TJjDEVFRdrnePte8Z11veyPJl+WVVVFcXExnE5no9HkSz85nU4UFxdrbdEYNPmyrKoqioqKNA2NQZOv/eR0OnHq1Clt3cagqa6OgaGiydflU6dOBewY2NjxZagXAHzyySfo0aMHoqOj0bdvX3z99df1tKVmGGMoLi6Woo/sIP1y6weoDUi/3PqB4LSBT6Y6OzsbTqcTKSkppudTUlJw4sSJgG3UvHnzkJCQoP21a9cOAJCXl6c9iuXc3FwUFBRo2ycMeFZWFk6dOgUAyMzMRElJCRhjSEtLQ2lpKQAgPT0d5eXlAICjR4+isrISAJCWlgan0wlVVZGWlgZVVeF0OpGWlgYAqKysxNGjRwHwGwDp6ekAgLKyMmRkZADgae+ZmZkAgFOnTiErKwsAv9uRnZ0NACgoKEBu9Vya/moCgIyMDJSVldWoqaqqCvn5+Y1Kk6/9VFhYiNLS0kalyZd+Sk9PB2OsUWnytZ8yMzPBGGtUmrztJ8YYDh8+3Kg0+dJPTqdTOwYGQlNjxtehXhs3bsSUKVNw/fXXY/v27ZgwYQImTJiA3bt31/OW84upwsJCaS8oSb/c+gFqA9Ivt34gOG3gU/r38ePH0aZNG2zcuBFDhgzRnn/ggQewfv16bN682eP7vU3/Li8v1y6QAH7x0q5dO+Tl5SExMVFrIEVRoKoqFEWp02WHw6FFVOpymTSRJtJEmkhTaGgqKChAYmJio01V9nWo16RJk1BcXIyVK1dqz5199tkYMGAAXn/9da++k9K/CYIgiIZGnaR/JyUlISwsTLtrL8jMzAxoEbKoqCjEx8eb/gCeag5Au0gSz3m7zBhPBRB4+17xnXW97I8mX3UUFRVpzzUGTb70k6IoKCoqqnW/NiRNviwD0FJhGosmX/tJURQt/buxaKqrY2CoaPJlmTGe/h2oY2BjxZ+hXps2bTKtDwBjxozxODSsLod6FRQUeDXMwbgsvjPUhzyoKh/qI/NQLzHMpbFo8nXZXRuEsiZf+snpdGpRysaiqa6OgaGiydd+UlUVBQUF2ncHQkdN+GSqIyMjMXDgQKxZs0Z7TlVVrFmzxhS5bqgwxrQ0cBkh/aRfZv0AtQHpl1u/t/gz1OvEiRM+Dw2ry6Fe6enpUg/1OnXqVKPS5Gs/lZSUSD3UKzs7GyUlJcjPz29Umnzpp+zsbDAm71CvY8eONSpNvvZTVVUVjhw5AsZYrTXl5OTAG3yu/v3RRx9h+vTpeOONN3DWWWfhpZdewscff4y9e/ciJSUF06ZNQ5s2bTBv3jwA/I73n3/+CQC4+OKLMXXqVEydOhVxcXHo2rWrV99JKWEEQRBEQ6Ixn5f8GeoVGRmJ999/H1OmTNGee/XVV/HEE0+4ZLcJaKgXaSJNpIk0kaaGrqmgoADNmjWr8Xwf7unEasekSZOQlZWFxx57DCdOnMCAAQOwatUq7Q51Wlqalh4H8JPz6aefrv0/f/58zJ8/H8OHD8e6det8/fpawRgftB4fHw9Fabype+4g/aRfZv0AtQHpl1u/t/gz1KtVq1Y+Dw2LiopCVFSUy/N2KfbG64qalhnjVe6tQ8e8WRYXZnW57I8mX5bFRaDQ3xg0WZdr2hahvzb92pA0+bPtdm0Qypp86SdFUbRjfWPRVJfHwFDQ5M+yaIPabrvxMz3hU/q3YNasWThy5AjKy8uxefNmDB48WHtt3bp1eO+997T/O3bsqDl+4199G2qA72Tl5eXaHRPZIP2kX2b9ALUB6Zdbv7f4M9RryJAhpvUBYPXq1UEZGiZ7P5N+ufUD1AakX279QHDawOf072DQmNPsCIIgiNCjsZ+XfB3qtXHjRgwfPhzPPPMMxo0bh6VLl+Lpp5/Gtm3b0KdPH6++s7G3KUEQBBF61En171CHMYb8/Hxp79yQftIvs36A2oD0y63fFyZNmoT58+fjsccew4ABA7Bjxw6XoV6iwAsAnHPOOVi8eDHefPNN9O/fH8uWLcPnn3/utaEOJLL3M+mXWz9AbUD65dYPBKcNfB5THcowxlBVVQXGmJTj6Ug/6ZdZP0BtQPrl1u8rs2bNwqxZs2xfsxvCdfXVV+Pqq6+u462qGdn7mfTLrR+gNiD9cusHgtMGlP5NEARBED5C56XAQ21KEARBNDQo/dsGxhhyc3OlTYcg/aRfZv0AtQHpl1u/LMjez6Rfbv0AtQHpl1s/EJw2kMpUEwRBEARBEARBEEQgofRvgiAIgvAROi8FHmpTgiAIoqHh7bkpJAqVCd9fWFhYq89RVRV5eXlo1qyZ1xN5NyZIP+mXWT9AbUD6A6dfnI9C4L50yEDn+sBA+uXWD1AbkH659QPBOd+HhKkuKioCALRr1y7IW0IQBEEQOkVFRUhISAj2ZjQK6FxPEARBNFRqOt+HRPq3qqo4fvw4mjZtWquy6IWFhWjXrh2OHj0qZWoZ6Sf9MusHqA1If+D0M8ZQVFSE1q1bSxsJCDR0rg8MpF9u/QC1AemXWz8QnPN9SESqHQ4H2rZtG7DPi4+Pl3YnA0g/6ZdbP0BtQPoDo58i1IGFzvWBhfTLrR+gNiD9cusH6vd8T7fXCYIgCIIgCIIgCMJPyFQTBEEQBEEQBEEQhJ9IZaqjoqIwd+5cREVFBXtTggLpJ/0y6weoDUi/3PplQfZ+Jv1y6weoDUi/3PqB4LRBSBQqIwiCIAiCIAiCIIiGiFSRaoIgCIIgCIIgCIIIJGSqCYIgCIIgCIIgCMJPyFQTBEEQBEEQBEEQhJ+QqSYIgiAIgiAIgiAIP5HGVL/yyivo2LEjoqOjMXjwYPz666/B3qQ64fHHH4eiKKa/Hj16aK+XlZXh9ttvR4sWLRAXF4crr7wSmZmZQdzi2vPjjz9i/PjxaN26NRRFweeff256nTGGxx57DKmpqYiJicGoUaOwf/9+0zq5ubmYOnUq4uPjkZiYiOuvvx6nTp2qRxX+U5P+GTNmuOwTF110kWmdUNY/b948nHnmmWjatClatmyJCRMmYN++faZ1vNnv09LSMG7cOMTGxqJly5a4//77UVVVVZ9S/MIb/SNGjHDZB2655RbTOqGq/7XXXkO/fv0QHx+P+Ph4DBkyBN988432emPue8IVWc71gHznezrX07mezvV0rm/I53opTPVHH32E2bNnY+7cudi2bRv69++PMWPG4OTJk8HetDqhd+/eyMjI0P5+/vln7bV77rkHK1aswCeffIL169fj+PHjuOKKK4K4tbWnuLgY/fv3xyuvvGL7+nPPPYf//Oc/eP3117F582Y0adIEY8aMQVlZmbbO1KlT8ccff2D16tVYuXIlfvzxR9x00031JaFW1KQfAC666CLTPrFkyRLT66Gsf/369bj99tvxyy+/YPXq1aisrMTo0aNRXFysrVPTfu90OjFu3DhUVFRg48aNeP/99/Hee+/hscceC4Ykn/BGPwDceOONpn3gueee014LZf1t27bFM888g61bt2LLli244IILcNlll+GPP/4A0Lj7njAj27kekOt8T+d6OtfTuZ7O9Q36XM8k4KyzzmK333679r/T6WStW7dm8+bNC+JW1Q1z585l/fv3t30tPz+fRUREsE8++UR7bs+ePQwA27RpUz1tYd0CgH322Wfa/6qqslatWrHnn39eey4/P59FRUWxJUuWMMYY+/PPPxkA9ttvv2nrfPPNN0xRFJaenl5v2x4IrPoZY2z69Onssssuc/uexqSfMcZOnjzJALD169czxrzb77/++mvmcDjYiRMntHVee+01Fh8fz8rLy+tXQC2x6meMseHDh7O77rrL7Xsak37GGGvWrBl7++23pet72ZHpXM+Y3Od7OtfTuZ7O9XSub2jn+kYfqa6oqMDWrVsxatQo7TmHw4FRo0Zh06ZNQdyyumP//v1o3bo1OnfujKlTpyItLQ0AsHXrVlRWVpraokePHmjfvn2jbYtDhw7hxIkTJs0JCQkYPHiwpnnTpk1ITEzEoEGDtHVGjRoFh8OBzZs31/s21wXr1q1Dy5Yt0b17d9x6663IycnRXmts+gsKCgAAzZs3B+Ddfr9p0yb07dsXKSkp2jpjxoxBYWGhdhc0VLDqFyxatAhJSUno06cP5syZg5KSEu21xqLf6XRi6dKlKC4uxpAhQ6Tre5mR8VwP0PleQOd6Dp3r5Tne07m+4Z3rwwPyKQ2Y7OxsOJ1OUyMCQEpKCvbu3Rukrao7Bg8ejPfeew/du3dHRkYGnnjiCQwbNgy7d+/GiRMnEBkZicTERNN7UlJScOLEieBscB0jdNn1v3jtxIkTaNmypen18PBwNG/evFG0y0UXXYQrrrgCnTp1wsGDB/Hwww9j7Nix2LRpE8LCwhqVflVVcffdd+Pcc89Fnz59AMCr/f7EiRO2+4h4LVSw0w8A11xzDTp06IDWrVtj165dePDBB7Fv3z58+umnAEJf/++//44hQ4agrKwMcXFx+Oyzz9CrVy/s2LFDmr6XHdnO9QCd743QuZ7O9XSup3N9sPu+0Ztq2Rg7dqy23K9fPwwePBgdOnTAxx9/jJiYmCBuGREsJk+erC337dsX/fr1Q5cuXbBu3TqMHDkyiFsWeG6//Xbs3r3bNK5QJtzpN46Z69u3L1JTUzFy5EgcPHgQXbp0qe/NDDjdu3fHjh07UFBQgGXLlmH69OlYv359sDeLIOoUOt8TRuhcLw90rm+Y5/pGn/6dlJSEsLAwlwpwmZmZaNWqVZC2qv5ITExEt27dcODAAbRq1QoVFRXIz883rdOY20Lo8tT/rVq1cilkU1VVhdzc3EbZLp07d0ZSUhIOHDgAoPHonzVrFlauXIm1a9eibdu22vPe7PetWrWy3UfEa6GAO/12DB48GABM+0Ao64+MjETXrl0xcOBAzJs3D/3798eCBQuk6XuCzvWA3Od7Ote7Qud6TmM73tO5vuGe6xu9qY6MjMTAgQOxZs0a7TlVVbFmzRoMGTIkiFtWP5w6dQoHDx5EamoqBg4ciIiICFNb7Nu3D2lpaY22LTp16oRWrVqZNBcWFmLz5s2a5iFDhiA/Px9bt27V1vnhhx+gqqp2QGpMHDt2DDk5OUhNTQUQ+voZY5g1axY+++wz/PDDD+jUqZPpdW/2+yFDhuD33383XXCsXr0a8fHx6NWrV/0I8ZOa9NuxY8cOADDtA6Gq3w5VVVFeXt7o+57Qkf1cD8h9vqdzvSt0rm9cx3s617vS4M71ASl31sBZunQpi4qKYu+99x77888/2U033cQSExNNFeAaC/feey9bt24dO3ToENuwYQMbNWoUS0pKYidPnmSMMXbLLbew9u3bsx9++IFt2bKFDRkyhA0ZMiTIW107ioqK2Pbt29n27dsZAPbiiy+y7du3syNHjjDGGHvmmWdYYmIi++KLL9iuXbvYZZddxjp16sRKS0u1z7jooovY6aefzjZv3sx+/vlndtppp7EpU6YES5JPeNJfVFTE7rvvPrZp0yZ26NAh9v3337MzzjiDnXbaaaysrEz7jFDWf+utt7KEhAS2bt06lpGRof2VlJRo69S031dVVbE+ffqw0aNHsx07drBVq1ax5ORkNmfOnGBI8oma9B84cIA9+eSTbMuWLezQoUPsiy++YJ07d2bnnXee9hmhrP+hhx5i69evZ4cOHWK7du1iDz30EFMUhX333XeMscbd94QZmc71jMl3vqdzPZ3r6VxP5/qGfK6XwlQzxtjLL7/M2rdvzyIjI9lZZ53Ffvnll2BvUp0wadIklpqayiIjI1mbNm3YpEmT2IEDB7TXS0tL2W233caaNWvGYmNj2eWXX84yMjKCuMW1Z+3atQyAy9/06dMZY3yqjUcffZSlpKSwqKgoNnLkSLZv3z7TZ+Tk5LApU6awuLg4Fh8fz2bOnMmKioqCoMZ3POkvKSlho0ePZsnJySwiIoJ16NCB3XjjjS4XmaGs3047ALZw4UJtHW/2+8OHD7OxY8eymJgYlpSUxO69915WWVlZz2p8pyb9aWlp7LzzzmPNmzdnUVFRrGvXruz+++9nBQUFps8JVf3XXXcd69ChA4uMjGTJycls5MiR2kmWscbd94QrspzrGZPvfE/nejrX07mezvUN+VyvMMZYYGLeBEEQBEEQBEEQBCEXjX5MNUEQBEEQBEEQBEHUFWSqCYIgCIIgCIIgCMJPyFQTBEEQBEEQBEEQhJ+QqSYIgiAIgiAIgiAIPyFTTRAEQRAEQRAEQRB+QqaaIAiCIAiCIAiCIPyETDVBEARBEARBEARB+AmZaoIgCIIgCIIgCILwEzLVBEHUyLp166AoCvLz84O9KQRBEARB1AF0ricI/yFTTRAEQRAEQRAEQRB+QqaaIAiCIAiCIAiCIPyETDVBhACqqmLevHno1KkTYmJi0L9/fyxbtgyAnq711VdfoV+/foiOjsbZZ5+N3bt3mz5j+fLl6N27N6KiotCxY0e88MILptfLy8vx4IMPol27doiKikLXrl3xzjvvmNbZunUrBg0ahNjYWJxzzjnYt29f3QonCIIgCEmgcz1BhC5kqgkiBJg3bx4++OADvP766/jjjz9wzz334Nprr8X69eu1de6//3688MIL+O2335CcnIzx48ejsrISAD9BTpw4EZMnT8bvv/+Oxx9/HI8++ijee+897f3Tpk3DkiVL8J///Ad79uzBG2+8gbi4ONN2PPLII3jhhRewZcsWhIeH47rrrqsX/QRBEATR2KFzPUGEMIwgiAZNWVkZi42NZRs3bjQ9f/3117MpU6awtWvXMgBs6dKl2ms5OTksJiaGffTRR4wxxq655hp24YUXmt5///33s169ejHGGNu3bx8DwFavXm27DeI7vv/+e+25r776igFgpaWlAdFJEARBELJC53qCCG0oUk0QDZwDBw6gpKQEF154IeLi4rS/Dz74AAcPHtTWGzJkiLbcvHlzdO/eHXv27AEA7NmzB+eee67pc88991zs378fTqcTO3bsQFhYGIYPH+5xW/r166ctp6amAgBOnjxZa40EQRAEITN0rieI0CY82BtAEIRnTp06BQD46quv0KZNG9NrUVFRppOtv8TExHi1XkREhLasKAoAPgaMIAiCIAj/oXM9QYQ2FKkmiAZOr169EBUVhbS0NHTt2tX0165dO229X375RVvOy8vDX3/9hZ49ewIAevbsiQ0bNpg+d8OGDejWrRvCwsLQt29fqKpqGrdFEARBEET9QOd6gghtKFJNEA2cpk2b4r777sM999wDVVUxdOhQFBQUYMOGDYiPj0eHDh0AAE8++SRatGiBlJQUPPLII0hKSsKECRMAAPfeey/OPPNM/POf/8SkSZOwadMm/Pe//8Wrr74KAOjYsSOmT5+O6667Dv/5z3/Qv39/HDlyBCdPnsTEiRODJZ0gCIIgpIDO9QQR4gR7UDdBEDWjqip76aWXWPfu3VlERARLTk5mY8aMYevXr9cKi6xYsYL17t2bRUZGsrPOOovt3LnT9BnLli1jvXr1YhEREax9+/bs+eefN71eWlrK7rnnHpaamsoiIyNZ165d2bvvvssY04uX5OXlaetv376dAWCHDh2qa/kEQRAE0eihcz1BhC4KY4wF09QTBFE71q1bh/PPPx95eXlITEwM9uYQBEEQBBFg6FxPEA0bGlNNEARBEARBEARBEH5CppogCIIgCIIgCIIg/ITSvwmCIAiCIAiCIAjCTyhSTRAEQRAEQRAEQRB+QqaaIAiCIAiCIAiCIPyETDVBEARBEARBEARB+AmZaoIgCIIgCIIgCILwEzLVBEEQBEEQBEEQBOEnZKoJgiAIgiAIgiAIwk/IVBMEQRAEQRAEQRCEn5CpJgiCIAiCIAiCIAg/IVNNEARBEARBEARBEH5CppogCIIgCIIgCIIg/IRMNUEQBEEQBEEQBEH4CZlqgiAIgiAIgiAIgvATMtUEQRAEQRAEQRAE4SdkqgmCIAiCIAiCIAjCT8hUEwRBEARBEARBEISfkKkmCIIgCIIgCIIgCD8hU00QBEEQBEEQBEEQfkKmmiAaMOvWrYOiKFi2bFmwN4UgCIIgiHpCURQ8/vjjwd6MBs2IESMwYsSIYG8GQQAgU03UE6+++ioURcHgwYPdrqMoChRFwQ033GD7+iOPPKKtk52drT0/Y8YM7XlFURAfH4/+/fvjhRdeQHl5ucftEqbV3d/SpUv9E9wA8aYPZKVjx4645JJLgr0ZBEEQhAca4rXEnXfeCUVRcODAAbfriO/ctWuXFyq95/Dhw9r2PvXUU7brTJ06FYqiIC4uLqDfHWis7R8XF4fOnTvjqquuwvLly6GqarA3kSA8Eh7sDSDkYNGiRejYsSN+/fVXHDhwAF27drVdLzo6GsuXL8err76KyMhI02tLlixBdHQ0ysrKXN4XFRWFt99+GwCQn5+P5cuX47777sNvv/3mlTG+8847ceaZZ7o8P2TIEG/khQTe9gFBEARBNEQa4rXE1KlT8fLLL2Px4sV47LHHbNdZsmQJ+vbti379+vki12uio6OxZMkS/N///Z/p+eLiYnzxxReIjo6uk+8NNMb2Ly0txZEjR7BixQpcddVVGDFiBL744gvEx8dr63/33XfB2lSCcIURRB3z999/MwDs008/ZcnJyezxxx+3XQ8AmzBhAnM4HOzzzz83vbZhwwYGgF155ZUMAMvKytJemz59OmvSpIlpfafTyQYNGsQAsPT0dLfbtnbtWgaAffLJJ7VQWHcEavu87YO6xOl0stLS0nr/Xm/o0KEDGzduXLA3gyAIgnBDQ76W6Nq1K+vRo4ftaxs3bmQA2DPPPOOtVE3H3LlzPa5z6NAhBoBdccUVDADbsWOH6fVFixaxiIgINn78eBdtDQ279hfMmzePAWATJ06s560iCO+h9G+izlm0aBGaNWuGcePG4aqrrsKiRYvcrtumTRucd955WLx4sctn9O3bF3369PHqOx0OhzbO5vDhw/5uuglFUTBr1iwsWrQI3bt3R3R0NAYOHIgff/zRZd3t27dj7NixiI+PR1xcHEaOHIlffvnFZb38/Hzcc8896NixI6KiotC2bVtMmzbNlJIGAKqq4l//+hfatm2L6OhojBw50mOqmRVPfVBZWYnmzZtj5syZLu8rLCxEdHQ07rvvPu258vJyzJ07F127dkVUVBTatWuHBx54wCU9zthevXv3RlRUFFatWgUAmD9/Ps455xy0aNECMTExGDhwoO248dLSUtx5551ISkpC06ZNcemllyI9Pd12rFl6ejquu+46pKSkICoqCr1798a7777rdRvVRFVVFf75z3+iS5cuiIqKQseOHfHwww+76N6yZQvGjBmDpKQkxMTEoFOnTrjuuutM6yxduhQDBw5E06ZNER8fj759+2LBggUB21aCIIjGRkO+lpg6dSr27t2Lbdu2uby2ePFiKIqCKVOmoKKiAo899hgGDhyIhIQENGnSBMOGDcPatWu92h53DBkyBJ06dbLVe9FFF6F58+a27/vmm28wbNgwNGnSBE2bNsW4cePwxx9/mNbZtWsXZsyYgc6dOyM6OhqtWrXCddddh5ycHNN6jz/+uJYGP2PGDCQmJiIhIQEzZ85ESUlJrfQ99NBDGD16ND755BP89ddf2vN2Y6rLysrw+OOPo1u3boiOjkZqaiquuOIKHDx4UFtHVVW89NJL6N27N6Kjo5GSkoKbb74ZeXl5tdpOQm7IVBN1zqJFi3DFFVcgMjISU6ZMwf79+/Hbb7+5Xf+aa67BihUrcOrUKQDczHzyySe45pprfPpecQBt0aJFjesWFRUhOzvb5Y8xZlpv/fr1uPvuu3HttdfiySefRE5ODi666CLs3r1bW+ePP/7AsGHDsHPnTjzwwAN49NFHcejQIYwYMQKbN2/W1jt16hSGDRuGl19+GaNHj8aCBQtwyy23YO/evTh27Jjpe5955hl89tlnuO+++zBnzhz88ssvmDp1qtdt4akPIiIicPnll+Pzzz9HRUWF6X2ff/45ysvLMXnyZAD8RHTppZdi/vz5GD9+PF5++WVMmDAB//73vzFp0iSX7/3hhx9wzz33YNKkSViwYAE6duwIAFiwYAFOP/10PPnkk3j66acRHh6Oq6++Gl999ZXp/TNmzMDLL7+Miy++GM8++yxiYmIwbtw4l+/JzMzE2Wefje+//x6zZs3CggUL0LVrV1x//fV46aWXvG4nT9xwww147LHHcMYZZ+Df//43hg8fjnnz5mltAwAnT57E6NGjcfjwYTz00EN4+eWXMXXqVNMNldWrV2PKlClo1qwZnn32WTzzzDMYMWIENmzYEJDtJAiCaIw05GsJcT62mlqn04mPP/4Yw4YNQ/v27VFYWIi3334bI0aMwLPPPovHH38cWVlZGDNmDHbs2OHTdlmZMmUKli5dql23ZGdn47vvvnOr98MPP8S4ceMQFxeHZ599Fo8++ij+/PNPDB061HQDYfXq1fj7778xc+ZMvPzyy5g8eTKWLl2Kiy++2OUaCQAmTpyIoqIizJs3DxMnTsR7772HJ554olbaAOAf//gHGGNYvXq123WcTicuueQSPPHEExg4cCBeeOEF3HXXXSgoKDBdp9188824//77ce6552LBggWYOXMmFi1ahDFjxqCysrLW20pISnAD5URjZ8uWLQwAW716NWOMMVVVWdu2bdldd93lsi4Advvtt7Pc3FwWGRnJPvzwQ8YYY1999RVTFIUdPnyYzZ07123KVlZWFsvKymIHDhxgTz/9NFMUhfXr18/j9on0and/GRkZpu0DwLZs2aI9d+TIERYdHc0uv/xy7bkJEyawyMhIdvDgQe2548ePs6ZNm7LzzjtPe+6xxx7TUtmsqKpq2r6ePXuy8vJy7fUFCxYwAOz333/3qI8x7/rg22+/ZQDYihUrTO+9+OKLWefOnbX/P/zwQ+ZwONhPP/1kWu/1119nANiGDRu05wAwh8PB/vjjD5dtKikpMf1fUVHB+vTpwy644ALtua1btzIA7O677zatO2PGDJe0uOuvv56lpqay7Oxs07qTJ09mCQkJLt9npab07x07djAA7IYbbjA9f9999zEA7IcffmCMMfbZZ58xAOy3335z+1l33XUXi4+PZ1VVVR63iSAIguA09GsJxhg788wzWdu2bZnT6dSeW7VqFQPA3njjDcYYY1VVVaZzOWOM5eXlsZSUFHbddde56PA2/fv5559nu3fvZgC08/Mrr7zC4uLiWHFxsUtqdVFREUtMTGQ33nij6fNOnDjBEhISTM/bnT+XLFnCALAff/xRe060qVXH5Zdfzlq0aOFRB2Oe078ZY2z79u0MALvnnnu054YPH86GDx+u/f/uu+8yAOzFF190eb+4rvrpp58YALZo0SLT66KvrM8ThLdQpJqoUxYtWoSUlBScf/75AHhK8KRJk7B06VI4nU7b9zRr1gwXXXQRlixZAoDf+T3nnHPQoUMHt99TXFyM5ORkJCcno2vXrnj44YcxZMgQfPbZZ15t52OPPYbVq1e7/FlTpoYMGYKBAwdq/7dv3x6XXXYZvv32WzidTjidTnz33XeYMGECOnfurK2XmpqKa665Bj///DMKCwsBAMuXL0f//v1x+eWXu2yPoiim/2fOnGkqtjJs2DAAwN9//12jNm/64IILLkBSUhI++ugj7X15eXlYvXq1KQL9ySefoGfPnujRo4cpon/BBRcAgEsK2/Dhw9GrVy+XbYqJiTF9T0FBAYYNG2ZKnROp4rfddpvpvXfccYfpf8YYli9fjvHjx4MxZtquMWPGoKCgwDYlzxe+/vprAMDs2bNNz997770AoEXYExMTAQArV650e7c7MTERxcXFHu+2EwRBEDqhcC1x7bXX4tixY6YhYYsXL0ZkZCSuvvpqAEBYWJh2LldVFbm5uaiqqsKgQYNqfZ7q3bs3+vXrZ9J72WWXITY21mXd1atXIz8/H1OmTDGdM8PCwjB48GDTudx4vi4rK0N2djbOPvtsALDd5ltuucX0/7Bhw5CTk6Nd+/iLqF5eVFTkdp3ly5cjKSnJ5ToB0K+rPvnkEyQkJODCCy80aR84cCDi4uJqnYpPyAtV/ybqDKfTiaVLl+L888/HoUOHtOcHDx6MF154AWvWrMHo0aNt33vNNdfgH//4B9LS0vD555/jueee8/hd0dHRWLFiBQBePbJTp05o27at19vat29fjBo1qsb1TjvtNJfnunXrhpKSEmRlZQEASkpK0L17d5f1evbsCVVVcfToUfTu3RsHDx7ElVde6dX2tW/f3vR/s2bNAKDG8T/e9kF4eDiuvPJKLF68GOXl5YiKisKnn36KyspKk6nev38/9uzZg+TkZNvvO3nypOn/Tp062a63cuVKPPXUU9ixY4dpTLLxZsKRI0fgcDhcPsNa7TUrKwv5+fl488038eabb3q1Xb4itsX63a1atUJiYiKOHDkCgN9EuPLKK/HEE0/g3//+N0aMGIEJEybgmmuuQVRUFAB+k+Djjz/G2LFj0aZNG4wePRoTJ07ERRddVKttJAiCaIyEyrXE5MmTMXv2bCxevBgjRoxAWVkZPvvsM4wdO1Y7ZwPA+++/jxdeeAF79+413Xx1d770hWuuuQYvvPAC7rnnHmzcuBEPP/yw7Xr79+8HAO2GuBVjhe3c3Fw88cQTWLp0qcu5tKCgwOW9nq5XjJ/rKyKNv2nTpm7XOXjwILp3747wcPf2Zv/+/SgoKEDLli1tX6/t9QIhL2SqiTrjhx9+QEZGBpYuXWo7FcWiRYvcnggvvfRSREVFYfr06SgvL8fEiRM9fldYWJhXpjhUCQsLs32e2YxnMuJLH0yePBlvvPEGvvnmG0yYMAEff/wxevTogf79+2vrq6qKvn374sUXX7T9vnbt2pn+N97hFvz000+49NJLcd555+HVV19FamoqIiIisHDhQpfxaN4g5q689tprMX36dNt1AjWNiTWDwO71ZcuW4ZdffsGKFSvw7bff4rrrrsMLL7yAX375BXFxcWjZsiV27NiBb7/9Ft988w2++eYbLFy4ENOmTcP7778fkO0kCIJoLITKtUTLli1x4YUXYvny5XjllVewYsUKFBUVmeqf/O9//8OMGTMwYcIE3H///WjZsiXCwsIwb948UyEtf5kyZQrmzJmDG2+8ES1atHDbLuK8+eGHH6JVq1YurxtN6cSJE7Fx40bcf//9GDBgAOLi4qCqKi666CLbuaP9vV6pCTEmurbTgaqqipYtW7otdOcuaEAQNUGmmqgzFi1ahJYtW+KVV15xee3TTz/FZ599htdff93WeMXExGDChAn43//+h7FjxyIpKak+NrlGxN1dI3/99RdiY2O1A3FsbCz27dvnst7evXvhcDg049mlSxdT4Yy6wJc+OO+885CamoqPPvoIQ4cOxQ8//IBHHnnE9J4uXbpg586dGDlyZI0G0x3Lly9HdHQ0vv32Wy16CwALFy40rdehQweoqopDhw6ZMgSsVc+Tk5PRtGlTOJ3OOruxIrZl//796Nmzp/Z8ZmYm8vPzXdIJzz77bJx99tn417/+hcWLF2Pq1KlYunQpbrjhBgBAZGQkxo8fj/Hjx0NVVdx2221444038Oijj9L84QRBEAZC6Vpi6tSpWLVqFb755hssXrwY8fHxGD9+vPb6smXL0LlzZ3z66aemc+jcuXMD8v3t27fHueeei3Xr1uHWW291G7Ht0qULAH4jwNN5My8vD2vWrMETTzxhmoPb7lqorvnwww+hKAouvPBCt+t06dIFmzdvRmVlJSIiItyu8/333+Pcc8+13WcIwl9oTDVRJ5SWluLTTz/FJZdcgquuusrlb9asWSgqKsKXX37p9jPuu+8+zJ07F48++mg9brlnNm3aZBpDdPToUXzxxRcYPXo0wsLCEBYWhtGjR+OLL74wVc/MzMzE4sWLMXToUC396corr8TOnTttx2rV9o4u4HsfOBwOXHXVVVixYgU+/PBDVFVVuVT0njhxItLT0/HWW2/Zfl9xcXGN2xUWFgZFUUzj4A4fPozPP//ctN6YMWMAAK+++qrp+Zdfftnl86688kosX77c9iaFSMuvDRdffDEAuFQSFxF7UZE8Ly/Ppe8GDBgAAFqau3UaEofDoUXSrdNzEQRByEyoXUtMmDABsbGxePXVV/HNN9/giiuuQHR0tPa6iOIazxObN2/Gpk2bArYNTz31FObOnWs7rlgwZswYxMfH4+mnn7at/yHOm3bbC7ieC+uaZ555Bt999x0mTZpkOwxPcOWVVyI7Oxv//e9/XV4TGiZOnAin04l//vOfLutUVVUhPz8/YNtNyAVFqok64csvv0RRUREuvfRS29fPPvtsJCcnY9GiRbZTMQFA//79TanHdclPP/2EsrIyl+f79etnSh3u06cPxowZgzvvvBNRUVGa4TNOF/HUU09h9erVGDp0KG677TaEh4fjjTfeQHl5uWk81/33349ly5bh6quvxnXXXYeBAwciNzcXX375JV5//fVaa/enDyZNmoSXX34Zc+fORd++fU1RWYBPafHxxx/jlltuwdq1a3HuuefC6XRi7969+Pjjj/Htt99i0KBBHrdr3LhxePHFF3HRRRfhmmuuwcmTJ/HKK6+ga9eu2LVrl7bewIEDceWVV+Kll15CTk4Ozj77bKxfv16bo9J4l/+ZZ57B2rVrMXjwYNx4443o1asXcnNzsW3bNnz//ffIzc2tsb0OHDiAp556yuX5008/HePGjcP06dPx5ptvIj8/H8OHD8evv/6K999/HxMmTNCK57z//vt49dVXcfnll6NLly4oKirCW2+9hfj4eM2Y33DDDcjNzcUFF1yAtm3b4siRI3j55ZcxYMAAl/YmCIKQmVC7loiLi8OECRO0oUzWqS8vueQSfPrpp7j88ssxbtw4HDp0CK+//jp69eqljRmuLcOHD8fw4cM9rhMfH4/XXnsN//jHP3DGGWdg8uTJSE5ORlpaGr766iuce+65+O9//4v4+Hicd955eO6551BZWYk2bdrgu+++M41tDyRVVVX43//+B4AXRTty5Ai+/PJL7Nq1C+eff77buimCadOm4YMPPsDs2bPx66+/YtiwYSguLsb333+P2267DZdddhmGDx+Om2++GfPmzcOOHTswevRoREREYP/+/fjkk0+wYMECXHXVVXWij2jkBK3uONGoGT9+PIuOjmbFxcVu15kxYwaLiIjQpkFC9TQYnvA0DYY/1DSllnE6C7F9//vf/9hpp53GoqKi2Omnn87Wrl3r8rnbtm1jY8aMYXFxcSw2Npadf/75bOPGjS7r5eTksFmzZrE2bdqwyMhI1rZtWzZ9+nStTcT2ffLJJ6b3iWk0Fi5c6FabP32gqipr164dA8Ceeuop2/dUVFSwZ599lvXu3ZtFRUWxZs2asYEDB7InnniCFRQUuLSXHe+8847Whj169GALFy7U+tZIcXExu/3221nz5s1ZXFwcmzBhAtu3bx8DwJ555hnTupmZmez2229n7dq1YxEREaxVq1Zs5MiR7M0333SrX9ChQwe3+8D111/PGGOssrKSPfHEE6xTp04sIiKCtWvXjs2ZM4eVlZVpn7Nt2zY2ZcoU1r59exYVFcVatmzJLrnkEtM0bMuWLWOjR49mLVu2ZJGRkax9+/bs5ptvNk3fRhAEQYTOtYSRr776igFgqamppum1GOPn2Keffpp16NBBu4ZYuXIlmz59OuvQoYNpXes1iB3GKbU84U7b2rVr2ZgxY1hCQgKLjo5mXbp0YTNmzDCds44dO8Yuv/xylpiYyBISEtjVV1/Njh8/7rJ9dm3KGGMLFy5kANihQ4dq3EbjuTc2NpZ17NiRXXnllWzZsmUubcmY65RajPEpwB555BHtXN2qVSt21VVXmaY5ZYyxN998kw0cOJDFxMSwpk2bsr59+7IHHniAHT9+3ON2EoQ7FMYCkGdKEBKgKApuv/1227Qiov7YsWMHTj/9dPzvf/9ziQIQBEEQBEEQRH1DY6oJgmiwlJaWujz30ksvweFw4LzzzgvCFhEEQRAEQRCEGRpTTRBEg+W5557D1q1bcf755yM8PFybguqmm25ymb6LIAiCIAiCIIIBmWqCIBos55xzDlavXo1//vOfOHXqFNq3b4/HH3/cZaovgiAIgiAIgggWNKaaIAiCIAiCIAiCIPyExlQTBEEQBEEQBEEQhJ+QqSYIgiAIgiAIgiAIPwmJMdWqquL48eNo2rQpFEUJ9uYQBEEQksMYQ1FREVq3bg2Hg+5PBwI61xMEQRANDW/P9yFhqo8fP06VfgmCIIgGx9GjR9G2bdtgb0ajgM71BEEQREOlpvN9SJjqpk2bAuBi4uPj/f4cVVWRnZ2NpKQkKSMLpJ/0y6wfoDYg/YHTX1hYiHbt2mnnJ6L20Lk+MJB+ufUD1AakX279QHDO936Z6ldeeQXPP/88Tpw4gf79++Pll1/GWWedZbvuiBEjsH79epfnL774Ynz11VdefZ9IA4uPj6/ViZYxhrCwMMTFxUmZWkb6Sb/M+gFqA9IfeP0ytmNdQef6wED65dYPUBuQfrn1A8E53/ts3T/66CPMnj0bc+fOxbZt29C/f3+MGTMGJ0+etF3/008/RUZGhva3e/duhIWF4eqrr/b1q2uNoihSj9Ui/aRfZv0AtQHpl1u/LMjez6Rfbv0AtQHpl1s/EJw28NlUv/jii7jxxhsxc+ZM9OrVC6+//jpiY2Px7rvv2q7fvHlztGrVSvtbvXo1YmNjg2KqVVVFRkYGVFWt9+9uCJB+0i+zfoDagPTLrV8WZO9n0i+3foDagPTLrR8IThv4ZKorKiqwdetWjBo1Sv8AhwOjRo3Cpk2bvPqMd955B5MnT0aTJk3crlNeXo7CwkLTHwCtYRhjYIxpz3m7rCgK4uLitO/x9r3iO+t62R9NvuqIj4/XnmsMmnzpJ8aYllLYWDT5sgxAS4NpLJp87SfGmHbnsrFoqqtjYKho8mVZ3LkO1DGQaJgoioL4+HhpozSkX279ALUB6ZdbPxCcNvDJVGdnZ8PpdCIlJcX0fEpKCk6cOFHj+3/99Vfs3r0bN9xwg8f15s2bh4SEBO1PVAPNy8vTHsVybm4uCgoKtO0TBjwrKwunTp0CAGRmZqKkpASKoqCwsBDl5eUAgPT0dG356NGjqKysBACkpaXB6XRCVVWkpaVBVVU4nU6kpaUBACorK3H06FEA/AZAeno6AKCsrAwZGRkAgJKSEmRmZgIATp06haysLAB8sHt2djYAoKCgALm5ubXSBAAZGRkoKyurUZOqqoiJicHRo0cbjSZf+un48eNo0qQJysvLG40mX/qpqKgIpaWlUBSl0WjytZ9ycnLgdDqhKEqj0eRLPymKgvz8fFRUVDQaTb70E2MM0dHRmo7aaiIaJoqioEmTJtJeUJJ+ufUD1AakX279QHDaQGE+3G4/fvw42rRpg40bN2LIkCHa8w888ADWr1+PzZs3e3z/zTffjE2bNmHXrl0e1ysvL9cukAC96lpeXh4SExO1CIGINimK4tUyYwzp6elo3bo1wsLCvH6vw+HQIip1ueyPJl+WGWM4ceIEUlJSEB4e3ig0+dJPTqcTmZmZaNWqFRRFaRSafFl2Op3IyMhA69attd9DqGvytZ+qqqpw4sQJtG7dWns91DXV1TEwVDT5sizOY61atar1MbCgoACJiYkoKCioVVEtQqewsBAJCQm1blNV5Wl/qampUla+Jf1y6weoDUi/3PqBwLaBt+cmn6p/JyUlISwsTLtrLxBGxRPFxcVYunQpnnzyyRq/JyoqClFRUS7Pi0ZRFMXlOW+XjaXVfXmvuDCry2V/NXm7zBhD8+bNERYW1mg0WZc9bUtYWBiaN28Oh8PRaDT5suxwONCiRQvtucagydd+CgsLM7VBY9BUl8fAUNHkyzGwRYsWATsGEg0TRVHQvHlzafuI9MutH6A2IP1y6weC0wY+WffIyEgMHDgQa9as0Z5TVRVr1qwxRa7t+OSTT1BeXo5rr73Wvy0NAIqiICYmRtqdjPSTfpn1A9QGpF9u/bIgez+Tfrn1A9QGpF9u/UBw2sDnePjs2bPx1ltv4f3338eePXtw6623ori4GDNnzgQATJs2DXPmzHF53zvvvIMJEyagRYsWtd9qP1FVVRtPLCOkn/TLrB+gNiD9cuuXBdn7mfTLrR+gNiD9cusHgtMGPqV/A8CkSZOQlZWFxx57DCdOnMCAAQOwatUqrXhZWlqaS+76vn378PPPP+O7774LzFb7iaIoSE5OlvbODekn/TLrB6gNSL/c+mVB9n4m/XLrB6gNSL/c+oHgtIFPhcqCRaCKlxAEQRBEIKDzUuChNiUIgiAaGt6em6QqCaeqKo4cOSJtOgTpJ/0y6weoDUi/3PplQfZ+Jv1y6weoDUi/3PqB4LSBVKZaURSkpqZKmw5B+km/zPoBagPSL7d+WZC9n0m/3PoBagPSL7d+IDht4POY6lBGURRERkYGezOCBukn/TLrB6gNSL/c+mVB9n4m/XLrB6gNSL/c+oHgtIFUkWpVVXH48GFp0yFIP+mXWT9AbUD65dYvC7L3M+mXWz9AbUD65dYPBKcNpDLV5c5ynHCcwPoj64O9KUFBURS0bdtW2nQQ0i+3foDagPTLrV8WZO9n0i+3foDaoL71l1WVIb0w3fScU3Vib/ZelFWV1cs2GPFXP2MMxwqP1dFW1Y6ckhzkl+V7vX4wfgNSmeqMUxkYsnAILllySbA3JSgoigKHwyH1QZb0y6sfoDYg/XLrlwXZ+5n0y60foDYIhH7GGFYfXI1f03+tcd0rP74SHV7qgC/2fqE9d8vKW9DzlZ6InxeP+Rvn+70dVn449AMeWP0ACsoKUF5Vjh0ndmDlXyux7vA6qIxHZY36N6RtwNGCozV+rlN1YuyisWj373b47mBwp0C2UlRehJ6v9MSgNwdh2Z/LkDI/BV/99ZX2OmMMf2b9icxTmcgqzsJfOX+BgdX7b0CqMdXNopoBAEoqS1BWVYbo8Oggb1H9oqoq0tLS0L59e5e5xGWA9MutH6A2IP1y65cF2fuZ9MutH2j4bVBUXoSmUU3r7PNrq/9k8UlcvOhibM3YirjIOOQ8kIPIMPvxuRvSNuDr/V8DAGZ8MQPbW23HobxDeHv72wCASrUSD6x+AH1a9sE57c5BfFTtpgu8f/X92JaxDS//+jKiwqJQUF6gvfbRVR9hYu+Jmv6ciBwMXTgUKU1ScPjuwx59zyM/PIJvD34LANiVuQuju4yu1Xb6QkZRBiLDItEitoXt678d/w1ZJVnIKsnC1Z9cDQC497t7AQDrj6zHjhM7sPrv1ab3xEfF49pu1+LlCS/X22+g4f3S6pDEmESEKWEAgNzS3CBvTf3jcDga7AG2PiD9cusHqA1Iv9z6ZUH2fib9cusH6qcNthzfgmuWX4O0gjTtue0Z23H6G6dj2Z/L3L5vxb4VSHgmAQ+uftDj5xdXFKP3q71x5zd3+rxttdX/5b4vsTVjKwDgVMUpk0Yr//rpXwCAMCUM+WX5mPjJRFz/5fUAgFsH3YoZA2aAgWHsorFIeCYBp79xulfRb3dsy9gGgKecF5QXoFl0M+21XZm7UKVWAQrQvn17fHWAR3MzizPx2m+vuf3M0spS/PuXf2v/C69UH5wsPonu/+2OIe8MAWPMdp3tGdtdnsspzcHVn1yN5zc+j9V/r0a4Q48TR4dHo7C8EC2bt6zX44B0R5xmMXznyynJCfKW1D+MMaiq6nanbeyQfrn1A9QGpF9u/bIgez+Tfrn1A/XTBme+dSaW7F6Cycsma8/N3zQfO07swNWfXI292Xtt3/fNgW/AwPDcxufw+d7P3X7+4t8X48+sP/Hyry/7vG211X8g94Dp/7/z/rZdL6ckB98c+AYA8O213yIxOhG/Hf8Nh/IPoVNiJ8wbOQ8LLlqA/in9tffsOLEDL/3ykulzdmXu8mrsNWNMi5gPbT8US65cguwHsvH0BU8DAA7lH0LvV3tjyNtD4HQ68eORH7X3/uunf5m+4++8v/H53s/BGMPWjK2ocFZorzmZs8ZtCRQf7f4IRRVF2J+7HxmnMmzX2X7C1VRnl2SjtKoUAHDnWXfiz9v+RN6DeSh4qABFc4qw/abtmNZvWr0eB6Qy1YwxxIfztAsZI9WMMRw7dkzaEw3pl1s/QG1A+uXWLwuy9zPpl1s/UL9t8GfWn9ryHyf/0JZv//p22/Udim49Hl7zsNvPNZo8Iz8c+gE/HfnJ4zZ50p9flo/bv7od6w+7L1psNdHuTPXh/MMAgFZxrTCy80gsvGwhFChIaZKC1f9YjYToBMRHxWP7zdvB5jJ8MZmPuRZRcAD4bM9n6P96fwxbOAxF5UUedZ2qOKW1y6qpqzC5z2Q4FAfaxLcBAKw9tBZ/5fyFX4//im37t2HD0Q3ae3NKc7Sx1YwxjF8yHpd/dDmW7F6CDWkbTN9TpVZ53I5A8tnez7Tlv3L+sl1HmOpLul2CCT0mICY8Rnvt6l5XY8HYBTitxWlIjE5EfFQ8wh3h6JfSD0qhQqa6rnA4HEiJTwEgp6l2OBzo2LGjtClRpF9u/QC1AemXW78syN7PpF9u/UDg2+Cj3R9h3OJxyCvNc3lNRDVLKkuw++Ru7Xl3kerC8kJt+XjRcbffaRctPVVxCiM/GInz3jvPY2TXk/5bVt6CV7e8inGLx7l9vzDRnRI7mf63crSQm9R28e0AABN6TMAft/2BPbfvQZfmXbT1RLGsc9qdA4Cbx4KyAjDG8PTPPMq85fgWjF001mNRseySbABATHgMmkQ20Z5v05SbamOkd2PBRpRVlSGlSQoSohIAAAzcYG46tkm7GfLUj0/h56M/m77HqdZtpPrjPz7G/I3zkVGUYZqRyc5Ul1SWaPvSG5e8gc8mfYbzO52vvT6s/TDb7wjGcUCqIw5jTBt7kFMqZ/p3RUWFtHdvSb/c+gFqA9Ivt35ZkL2fSb/c+oHAt8F/f/svvt7/NX449APm/TTPVHlZVJzeenyryQi7M2ZGU11YXqi934rd8+VV5dryyeKT2vKGtA1Y/Pti7X93+nNKcvDRHx8BAIori/H+jvdx3RfXuRj0g3kHAQCjOo8C4MFUVxvgdgnttOd6JvfUhppaSYpNQoeEDgB4oa0BbwzAluNbEBUWhaaRTbHh6Aac/sbpmnm2klWSpX2OkbbxbV3WfX/n+wCAER1HIMzBx0iLNl24faG23p7sPVj510oAwIBWAwDwGxrrD6/HluNbbLfDiMpUfLjzQ1y65FLbsc9WyqrKMGnZJNy/+n6M/t9oUz/bmepdmbugMhUtm7REalwq386UAdrrwzrYm+pgHAekM9XRjFe+kzFSzRhDRkaGtCca0i+3foDagPTLrV8WZO9n0i+3fiDwbSBM5xf7vsDDPzxsmppWmOdfjv0CADit+WkA7E0xAFO1agaG4opi2/XcvV9grI00dOFQTP10qmYC3el/bYu5WNeML2Zg4Y6F+HDnh9pzeaV52nzINZpqS6TaGwa1HgQAeGf7O9iVuQsAML3/dGy5aQvaNG2DnNIcl3RsgTDbyU2STc+L9G8jOzN3AgDObH2mlnKvMhXlVeVY+sdSAMBFXS8yb1sq37ackhyMeH8EznzrTFQ6Kz3quWXlLZj2+TSs+GsFPtj5gcd1Ab3QGgAts+GSbnx/sjPVv2f+DoAbfhHxF+Y/PioefVv2tf2eYBwHpDLVDocD7ZPaA5CzUJnD4UCHDh2kTYki/XLrB6gNSL/c+mVB9n4m/XLrB2rfBieLT2Lh9oWa4RXGal/OPpd1RXR6c/pmAHqKsztTbIxUA2aTbcTu/cbn7DJORZqwO/0/pdmPxTZGvUWUulVcK/Rp2Ud7zs6c+WOqB6YONC1fN+A6PHn+k+jWohu6J3UHwFOe7cgqto9Ux0XGuZ2qq0/LPiZTnVuai1MVp+BQHFgxZQUWXLQAHRI64M6z7kRMBB+rbOwTdzcUBMZx26JwmCc2Hd1k+n9a/2m4e/DdAOz3L9Ef3Zp3054be9pYXNT1Ijw+/HEtCm8lGMcBqeapZowhIZKPK5A1Ul1eXo6oqKh6nQy9oUD65dYPUBuQfrn1y4Ls/Uz65dYP1L4NRn84Gjszd2LTsU14c/ybWnEsO7MnjO6xwmMAgJ5JPQG4ryBtNdXW/wV26eNiTDAA2xRpEVF3p19EQm8ddKspam2cMkuYyC7NuqBjYkdtG/PK8tA8prnp++zSv2vizDZnasvLJi7TvgMAYiNiAbg31UKz1VQDPAXcWDRO0Ltlb5OprlT5DZIIRwTCHeG4c/CduHMwn7Zs9rezAZiLxP2Z9adm9u04VXFKW3ZXXM7IL+k8o+G6Adeha/OumHXWLM3E/533NyqdlYgIi9DWF/3RuVln7bm4yDh8M/Ubj98TjOOAVLfxGGOIqOIdlVsmp6nOysqSNiWK9MutH6A2IP1y65cF2fuZ9MutH6h9G4jU4aW7eZqwMGKeTHW5k493FsbQbfp3WYHH/62f6+45EbU1UlrJI6V2+suqynAk/wgA4KaBN5nmYj6Uf0hbPpjLI6Odm3VGbESsNo73UJ6+jsCfSPUFnS7AEyOewBeTvzAZasB7U50cm+zymihWZiQhKgFtmraBAm4qVaZqlb2NxlUg2sRqqj1hTN/3xlSLSPW0/tMwZ9gcNI1qitZNWyM2IhZVapVWUV1gZ6q9IRjHAalMtcPhQNfWXQHIm/7drl07aVOiSL/c+gFqA9Ivt35ZkL2fSb/c+oHAtYEw08IsuRv/bFxHpBDXVKhMFA52F6mu0VSXuJpqEam2038g9wAYGBKiEtA/pT8+n/w55gydA8BsqsWyMHGdmtlXAHeqTqQXpgPwLVLtUBx4bPhjuLT7pS6v1WSq3RUqA+zHVfdp2QeKopgj1dWp/OEO12RlkUptMtXZ3FRXOiuRXpjuUtTNl0j1rsxdSC9KR5gSpo0tB3ibdGnGq6W7myPcV1MdjOOAVEccxhiahPES9LKmf5eWlkp795b0y60foDYg/XLrlwXZ+5n0y60fCFwbiKimMGLuzB6gV+YWcwi7q94tItrCiLobU22XPm7UYxepNqZ/W/WL1O9uLbpBURRc0u0S3DLoFgDAkfwj2k0AYRIToxP5dlZHoUVUWnDi1Ak4mRNhSpgWza4tseH+R6rbNuUVwI2Gu1dyLwBwm/5tRUSqRR8BPFJ9svgk2v27Hdr+uy06vtRR274qtcq0rjtTXaVW4d3t72LEeyMAAOd1OM80JRgAtE/gNa/EMAKAF43LK+PTuImbG94SjOOAdKY6rILvMLJOqZWbmyvtiYb0y60foDYg/XLrlwXZ+5n0y60fCFwbaKa62ogVV7qPVAtzpUWqbUyxMSotpoHyZUy10ahnl3oeU23VL0y1cXxwm6ZtEOGIQKVaqc2ZLbZbGEzNVFvmjxYmu3XT1m6LZflKbSLVoj17JvVEq7hWAIA+ybzQmtFUe0z/rtZhnLpsb/Ze/HjkR2QWZwIAMosztQi9NXPBnam+ecXNuP7L65FXloez256NxVcudllHbP/RwqPIKs7CG1vewFM/PgUAaNmkJeIi42w/2x3BOA5IVajM4XCgZwdeQEHGSLXD4UCbNq7pIbJA+uXWD1AbkH659cuC7P1M+uXWDwS+DYRZ8jTNlZb+7SFSLQx0XGSclv5tHVPNGIOiKP6Nqa6uPm2nX1SWNlaRDnOEoX1CexzMO4hD+YfQLqGdZuaFwRQRVGuk2p8iZTUhorc1RqqbuEaqJ/SYgK/2f4Ubz7gRb257Eyv/WqnN4ex1+rfNmOqyqjJ8d/A703riBoox9dv6PuP7F+/mJvpfF/wL9w65F1HhUS7riZsXG49uxL9/+bfps31N/QaCcxyQLlIdpfKOLKsq0woayAJjDMXFxdLevSX9cusHqA1Iv9z6ZUH2fib9cusHAt8GNc1VDOjRTU+FykSqd0JUgjYFlDDaFc4KnPnWmRj01iBkl2TbRrq9HVNtp9+Y/m1EpBWLQmQukeoE+/RvkabsS5GymtAi1VW+V/9OiUvBl1O+xPju47Ho8kXYPH2zNp+zbaTaJv1bGG1jSjcAfLnvS9P/2hh7S+aC9X0AN8llVWVIjUvFnKFzbA01oEeq1xxa42LW/THVwTgOSGeqWRnTdhrZUsAZYygsLJT2REP65dYPUBuQfrn1y4Ls/Uz65dYPBL4NvKnqbC1UpjLV5fuFgY6PikdCFJ/iVhjtL/d9iS3Ht2BbxjaMXzLepSAWYJ5Sq6Yx1Vb9ouDVaS1OM72nU2K1qa4uUGaNVAvTbJx2C/Cv8ndNeEr/rlKrtCxbO1NtJC4yDu2i2mn67cZUe1uoDICW+i0Qr3sTqf7h0A8AeNVzT1NbCVMtOL/j+dqy3Q2AmgjGcUAqU+1wONC6dWttnjnZUsAdDgdSU1OlrYhJ+uXWD1AbkH659cuC7P1M+uXWDwS2DVSmup1z2oiIUkaHR2vPGU0wYDbV1kj129ve1tb75dgvWHNoje22CHJKc1yi4cb0b6t+YbibRHgukOUuUp15KtNkGjVTHcD0b0+mWkSpFSgu82Vbser3eky14jqm2g531eDtTLXox5GdRnr8TKupHnfaOMwcMBMAcE3fazy+145gHAekOuIwxlBUVIQWMS0A2N/laswI/bLevSX9cusHqA1Iv9z6ZUH2fib9cusHAtsG3qR+V6lVmsEVxhBwLTYmxk8nRCcgIVqPVB/JP6KN2xXmKr8s3+V7jCZaZSrySvNMrxsj1Vb94r3WomLCE4jsVWukOjk2GVFhUWBgWoEuwDCmup4i1eK7W8W1so0yG7Hq93VKLbs0bkDvG28j1UXlRfgt/TcAPFLtCaup7pncE2+NfwsH7jiA0V1Ge3yvHcE4DkhnqktKSrS53KzjIxo7Qr+sJxrSL7d+gNqA9MutXxZk72fSL7d+ILBtINKFPWE0U6JQGeA6rtpdpHrTsU1gYBjcZjC6t+DVuW3Tvy16rOOqjabaql+YZWEwBS1iuakW2avWSLWiKLbjqus7Up1exE213XzUVqz6vR1TbS1U1iGhg/be5jHNtari1jHVYh2rqf795O9wMidaN22NDokdPG5zk8gmWvE6AOiR1ANhjjB0ad6lRr12BOM4IJWpdjgcSElJQedEPuBdFCWQBaFf1pQo0i+3foDagPTLrV8WZO9n0i+PfqfqxMajG13SdQPZBt6MpzZ+vxhTDXgw1ZGGMdVlBZqJTG6SrKUl26UgWz/PmnEqTLWdfi1SrZgj1dYhodZINeA6rrrSWYmMogzTa4HAm0i1NaJrh1W/ML2MMa/GVIu2j4+KR88kPmvSac1PQ2RYJADXSLUww9Z9ZVfmLgBAv5R+NW6zUVt0eDQ6JHg24TURjONA4z/iGGCMoaCgQLtbcrjgcHA3qJ4R+mW9e0v65dYPUBuQfrn1y4Ls/Uz65dH/1I9P4dx3z8W0z6cB4NqPFhwNaBt4VfnbkC4cFaZXd7aOxdaqf0ebq38LMxwdHq1FUI2RaqHDaqozTmWY/hez+tjpF9viEqkW6d8lOab1jOZbi1RXp3wfLzoOBobIsEjb6a38xZOpFmO+2zT1LlJt1G+X/m03ploYbWGOwxxhOCP1DABA1+ZdNVMtTLcYUy1uTFhvhPye+TsAoF9L30x1txbdaj33dzCOA9KZ6vLycu3ux+H8w8HdoHpG6JfhRGMH6ZdbP0BtQPrl1i8Lsvcz6ZdH/79++hcA4OM/PgYA3PPtPWj/Unu8ve3tgLWBL+nfkWGRJjPkKf1bjKkuLC/UzHB0eLQeqTYYdVHwzFr4bF/2PpNGY/q3Vb/YFqupNkaqGWO2kWqR4frujndx4tQJLfW7bXxbl8+rDV6lf3tpqo36/U3/DneE4x/9/oHWTVtjYu+JbiPVog1dItUneaS6b0rfGrcZ0E21iI7XhmAcB6Qy1Q6HAy1bttTy82VM/27ZsqUUKVF2kH659QPUBqRfbv2yIHs/k3559FsN74LNCwAAD//wcK3awGgUfUn/jgqLMkV4rYXK7MZUF5QXaGY4JjzG1uwJQ2w16Xuy95ieM6Z/W/XbmWVAH1Nd7ixHSWWJbaT6poE3oUNCBxzIPYCpn06tkyJlgHeRam/Tv436fZ1SS6wTpoThwi4XIn12Oi7tfqmLqRZjqu1MNWNMj1R7mf49stNIhClhuPi0i71a3xPBOA40/iOOAcYY8vPztUh1elG6VweLxoLQL8PdWztIv9z6AWoD0i+3flmQvZ9Jv9z6AW7OatMGRsPlTfq3uJaOCo8yGXKrCdbSv6MStDHVJZUlKKooAmCOVBtxZ6r3Zu81Ra/FlFrWfYAxpq1njSw3iWiiGfnc0lxb853aNBVfT/0aALDu8Drszd4LILBFygDdVFunqgJ8L1Rm1O/rlFra/5YbECK135tI9bHCYygoL0C4Ixw9knrUuM0AMKnPJBTNKcK0/tO8Wt8TwTgOSGeqq6qq0DK2JaLDo6EyVbvzIwNCv6wnGtIvt36A2oD0y61fFmTvZ9Ivt34AiA2PrVUbGKPFXkWqq1O1I8MiTabVOKa6pLIEe7L2AOCR6qZRTbXXThafBGAeU23EaI6N7M3ea4qGG9O/jfqNxttqHBVFn/c5pzTHNlINAL2Se6F109ZQmYov9n0BoO4i1ZVqpcvNDF8i1Vb9vk6pJbCu4xKprjb/olBZpVqpfacoUtYjqYf2Pm8wFrqrDcE4Dkhlqh0OB5KSkhAWFoaOiR0ByJUCLvTLkBJlB+mXWz9AbUD65dYvC7L3M+mXWz8AxEbG1qoNjFFMb8ZUi3TlqLAoKIoCBQoA4LuD3+G8hedhb/Ze3PDlDTiYdxBJsUkY0XEEIsMiER0eDUA31d6mf6fGpSIyLBKlVaU4UnBEW8+Y/m3UbzTedmOgjdNquUsTB4DBbQYDAHZm7gQAbfqvQGGc41tE3QGeNi+iwt6Mqbbq93VMtbv/XcZUV5oj1YC+v4ibAJ2bda5xe+uCYBwHpDriMMaQm8sLEXRK7ARArmJlRv0yQvrl1g9QG5B+ufXLguz9TPrl02+NKMaGx9aqDYyf502kWhi+qHCeHixM3Ac7P8BPaT9hye9LsGT3EgDA8onLtYrZYlx1ZnEmAO/TvyPDInFa89MAAH9m/amtZ4xUG/Ub08btzLIWqS5xH6kGgLPanKUtRzgiML77ePsG8ZOosCjthoRxXLWYTisxOhFNIpvU+DlW/d6OqbY+V1OkWptSK0afX1qMrxeviT6ub4JxHJDKVBvRItX58kSqCYIgCIIgiMZFkwiz0TJGPP3BGMX0Zkx1UTkfEy1MlzbfcXVauBgzDQBD2w/VlsV2izmi3aV/W021oijaOF2jqXZ3A8CYhm4bqY7xLVINAGO6jjFFaAOBoii2xcoO5h0E4F2U2g5vp9Syarb+r02p5bSfUgtwNdxxEXF+bXMoIpWpVhQFzZs3h6Io6NKMVwA/kHsgyFtVfxj1ywjpl1s/QG1A+uXWLwuy9zPpl0+/NXoZGxlbqzYwRiiNacjuEKZZFLISJk4YLBFBVqCYTK0wkGKO6JiIGFuzZ51Sy6E40K1FNwDA/tz95nUZc9kHjJFqO1NtnFbLU6R6UOtBWiR5cu/Jrg0RAKym+tM9n+LqT64GoAcEa8Kq39gOAU3/rjbOCVEJLn2umerI4JjqYBwHpDLVqqoiOzsbqqqia/OuAFx/jI0Zo34ZIf1y6weoDUi/3PplQfZ+Jv3y6W8S0cSU5hoTHlOrNjAaW2GOPCHW0SLV1WZMREWFqbamE4ubAaIquLeRaofi0MynSDcWlFWVuewDxjHVdmZZRKpzSnM8RqqbRjXFHWfdgZGdRmJCjwkurwcCq6n+5M9PUFZVhjNSz8BzFz7n1WdY9fs6pZagxkJl1VNqxUXGuTXcwTLVwTgOuLZoI0ZRFISHh0NRFJzWgo/F2J+zX7ur1dgx6pcR0i+3foDagPTLrV8WZO9n0i+f/iaRTUwR5diI2Fq1gdFMidRuT4h1rGOqhYET2+Yy9tuSpu7tmGoFejE0Y2o3wM1odHi0SX+gItUAsGDsAtvnA4XVVAuTP6P/DPRK7uXVZ1h/A34XKvNySq0mkU0QGRaJsqoylyJmxirv9UkwjgNSRaoVRUFiYiIURUHnZp2hQEFRRZFWdbCxY9QvI6Rfbv0AtQHpl1u/LMjez6RfDv3CHAE8Um2MKMeEx9SqDYzG05tItbv0bxGpLq3kptpqmK1jwd1V/7ZOqeVQHNp3GKPQADej1n2gpkJlovp3TZHq+sBqqt3Nr+0Jq/66nlLLGKnWxtFX32gJZvp3fR8HpDLVqqri5MmTUFUV0eHRaJ/QHoA8KeBG/TJC+uXWD1AbkH659cuC7P1M+uXQbzS7TSKbmCLKKqtdGxhNqLHIWE3bYi1UZh1THahItUNxaGbJLlJt3QeM64gItxFfItV1jdVUG4uzeYtVv22k2qadrf3j7Zjqhpr+Xd/HAalMtaIoiIqK0nZMYwq4DFj1ywbpl1s/QG1A+uXWLwuy9zPpl0O/0URHOCJMJluFWqs2MJpqnyLVbtK/axpTLfCl+re7SHVpValpH3CqTlszbkQbU13SACPVzL9ItbH/vR5TXUP6tzEa7VSdWlp/k4gmDc5UB+M4IJ2pTkhI0E119Rx3skSqrfplg/TLrR+gNiD9cuuXBdn7mfTLod8YQVaZajbVqlqrNjAaVa9Mdbk5/dtaqMztmOpwc6Tal+rfmqm2iVSLfeDbg98i/pl4vL/jfe19dohxv6cqTjXcSLVNhN0d1t+A12OqfUj/Nk75FRcZ53a8dTBNdX0fB6Qy1aqqIjMzU0sFkM1UW/XLBumXWz9AbUD65dYvC7L3M+mXQ78xUs3ATCbbyZy1agNT+rc3hcoqzPNUu5tSK1CRaofi0AuV2YypFvvA2EVjUVJZgofWPGTaLitiu5zM2fAi1dU3EnxN/zb2v9djqn2YUktU/lagIDo8WnvtgdUPYNKySSgsLwQQ3PTv+j4OSFf9OzY2VtsxxbRa+7L3BXOz6g2rftkg/XLrB6gNSL/c+mVB9n4m/XLoN0WmLZFqJ3PWqg2M0V9RxdkTLpHqakOqVf8Whcoshrk2Y6rdRapLK0u1fcCKu+izeL5KrWpwkWp/07+N/W+X/m3Xzr5EqsUNk6hwnmItXtucvhmb0zdr7wlmpLq+jwPSmeqmTfXS7gNaDQAA/Jn1J0oqS1x+3I0Nq37ZIP1y6weoDUi/3PplQfZ+Jv1y6Lemf5si14zVqg18jVRbC5VZq3+7jVT7WP3bNKWW4j5S7W4fqClSXaVWNbhItb/p30b9fk+pZflfjJk3mmrR5+LRSjBNdX0fB6RL/87IyNBSAdrGt0VqXCqczImtx7cGeevqHqt+2SD9cusHqA1Iv9z6ZUH2fib9cui3Vvs2Rqqr1KpatUGgC5UFap5q2ym13FT/zsjIcPkcd0bZZKobSKRatLs/U2pZfwPivQzMY6Eyl+rfbgqVGU21MOfuTHXTyODc4ArGcUAqU60oCuLj47W7W4qiYHDbwQBgSlVorFj1ywbpl1s/QG1A+uXWLwuy9zPpl0O/MVLNGHP5vzZtYIz+ejOllrtCZSIqWt9jqsU+ILDOn21FG1OtBn9Mdbv4dgCAg3kHAfg3pZb1N+DtlFr+pH/XFKm29nF9EYzjgHSmukmTJqYGPrvN2QDkMdVW/TJB+uXWD1AbkH659cuC7P1M+uXQ7ylS7WTOWrWBr5Fqd+nfAm/nqa6p+rftlFqWSPX2E9uhKAoiovXPSYpNAuA++tyQItW9W/YGAPxx8g8A/o+pNvZ/oAuVlVeVu5hqkaVgXd+d2a5rgnEckMpUq6qK9PR0UyqAiFT/cuyXYG1WvWGnXyZIv9z6AWoD0i+3flmQvZ9Jvxz6PY2pdqrOWrWBqVCZF6ZapBQLY2WNeLqLjhrHVIcpYQh3hHuMVNtOqWWJVP9v1/+QUZiBnQd3as+JKbPcGVNjYTXxXcGKVPdO5qb6SMERnKo45deYautvwN8ptbxJ//YUqQ7WeGogOMcBqUy1oiho3ry56a7FoNaD4FAcOFZ4DMeLjgdx6+oeO/0yQfrl1g9QG5B+ufXLguz9TPrl0O9S/bvS/H9t2sDXQmUCb9OsBcZIdXR4NAD7tGTb9G9RqKz6BkB0eDQGtxmMcmc5Xt/6OioiKrT3CyNZ03YZdQcrUt0itgVSmqQA4IWU/RlTbf0N2FX/9iZS7Sn9W0S8G6qpDsZxQDpTHRMTY2rguMg49GnZBwCw+VjjTgG30y8TpF9u/QC1AemXW78syN7PpF8O/S7zVBvTwaHWqg2M0V8xH7E3uEv/FngaUx0TEQPAPoJqV/3bGql2KA5cf/r1AIANRzcgvzJfe78wgDUVKjMSrEg1YE4B93dMtbH/7dK/vRlT7Wme6oYeqQ7GcUAqU62qKo4ePeqSCjC4jRzFytzplwXSL7d+gNqA9MutXxZk72fSL4d+a/q3qfq3s6pWbWCM2PqClv5dw9hlga+RauP4Yq1QWXWkWoGCZjHNAHAT/efRP7X3CwPordn3pKE+ECngf2T9oWn2Nf3b2P926d/eVP+2/i8yEUzVv6v7K9LRsEx1MI4DUplqRVGQnJzsctdCFlPtTr8skH659QPUBqRfbv2yIHs/k3459LuMqa4wR669bQOn6sSJUydMz/lrqn2OVBvGVGum2svq33aRamPBsRKlRHu/SHn21uwDQY5UG021n+nfxv63S//2ap7qEB5THYzjgHSmOjo62tVUVxcr+y39N5eCB40Jd/plgfTLrR+gNiD9cuuXBdn7mfTLob+kUjeNdtW/vW2Dm1fejNYvtMbOE3phL2tFbW/RptRyY0itRs4YqY4Jr07/9qL6t92YakVRdFPNqpBdlq29X6Q8h0qkuntSdwDAwdyDfqd/G/vfAf+m1PIl/duu+newTXV9HwekMtWqquLIkSMuqQA9k3qiaWRTFFcW44+sP4K0dXWPO/2yQPrl1g9QG5B+ufXLguz9TPrl0G+MJjPGXKp/e9sGO07sAAPD7yd/t/1sXxDGyp8x1b5Eqk1TatlFqp1V+Pvk39r7tUi1G7NvZ6CDGakWbVGlVvk1pZb1N+DvlFruCpWVO12n1LKLVAfzxkQwjgNSmWpFUZCamupy1yLMEYYz25wJANh4dGMwNq1ecKdfFki/3PoBagPSL7d+WZC9n0m/HPqF2QK4WRJzQYv/vW2DwvJCAHrhM8ZYrdO/vU2zFtFpwMsx1XZTahnGVBsj1YVqofb+msZU2z0fTEMoxk+rTPVrSi3rb0A8BmpKLZWpKK0qNT1nZ6qN+2R9E4zjgF+m+pVXXkHHjh0RHR2NwYMH49dff/W4fn5+Pm6//XakpqYiKioK3bp1w9dff+3XBtcGRVEQGRlp28AjOowAAKz+e3U9b1X94Um/DJB+ufUD1AakX279siB7P5N+OfQbja/RfIn/vW0DMRZbPArjakWkdnvC1ym1whxhmpn2VP3bdkotUajMJlJd6az8f/bOPE6Oqtz7v6qemZ59y0wmk8kkA4GEhJAEg4QE8YJGcl0QFxRRAXMVFIhyzeuGCpFFgoi4XFk0EoOowAUF4QIBjQQFQgKEsIQshCwzmczes/dMT3dXvX90Tk1VdVV3VW9V1c/58cmH6u6qrvN9zqmafup5znMwHp106JgjaeYoq1PHgZgD6+T4YfaTj/2nfs+K9NdAppfUAoDRiVHNe250qnN9H7DtVD/44INYs2YN1q5dix07dmDRokVYuXIluru7DfefmJjAhz70IRw6dAgPP/ww9u7di/Xr16OpqSntxtuVJEk4dOiQYSrAyhNWAgA2H9isXHz5pkT8FMT5afMD3AacnzY/FVHvZ85Pg1/t/Oqd6qgUtWwDFqFmc7LNotRW5sfaTf8GJudVJ4pUJ1xSSzWnmjnkESmC0bH4pcASOabqtjmZ+g1oI8upzKnWXwNG1b+N7Bz30MNkTjUwOV4SOdUsmu2EnLgP2Haqb7/9dlx22WVYtWoV5s+fj7vvvhulpaXYsGGD4f4bNmxAIBDAo48+ijPPPBMtLS34j//4DyxatCjtxtuVIAiYMWOG4cBc0rgENcU1GAwN4uX2l3PetlwoET8FcX7a/AC3AeenzU9F1PuZ89PgV6d/y5A1xcUkWbJkg6gUVdahZs61WcFeK061kv5tsVAZMFkB3Mqcas2SWoI2Uq1J/5YiEAviXRzLTrWDqd+A1glOZU61/hqwPKfaYvo3MOlUs/5yY6Q61/cBW071xMQEXn31VaxYsWLyC0QRK1aswNatWw2Peeyxx7Bs2TJcddVVaGhowIIFC3DzzTcjGjWvLBgKhTA0NKT5B0B52iDL8uRTK0myvK03rHofAQJWHB/j2rR/k+ZYds5sb6fCZGcbiPUXey8fmOz0kyzLEEUx7X51E5OdbSZBEPKGyW4/sfuAIAh5w5Spe6BXmexss77P1D2Qy50SBAGiKOa9U2Umzk+DP2GkWo5asoG6YjhL/zaLVFf4K5K2yW76NzAZqU5U/Tvhklqy8ZJazHlUK1EEWu1IOx2pVtK/ZTnlOdXq/k91SS19f6mzAfSRavX0APbeZe+5zHKbMy0n7gO2nOre3l5Eo1E0NDRo3m9oaEBnZ6fhMQcOHMDDDz+MaDSKJ598Etdeey1+9rOf4aabbjI9z7p161BVVaX8a25uBgD09/cr/2fbgUAAg4ODSvuYA97T04ORkViHd3V1IRgMQpIkvPXWWwgGY8sQtLe3IxQKAQDa2tpwzqxzAADP7HsG0WgUkiShtbUVkiQhGo2itbUVABAOh9HW1gYg9gCgvb0dADA+Po6Ojg4AQDAYRFdXFwBgZGQEPT09AIChoSH09sbK/A8ODiIQCKTFBAAdHR0YHx83ZAqHYxdPa2srwuEwDh06pFTDywcmO/105MgRtLa2IhgM5g2TnX4aHBzE7t27IUlS3jDZ7aeenh7s27cPkiTlDZOdfpIkCW+++abClw9MdvopEoko98BMMHG5U+oxTFGcnwa/+sGe0ZxqKzZQr23Nts2W06otqU3aJruFyoDJCuCJItX6JbWMqn9rltSSIhifiI+SeiVSrS5Ulsqcav01YJT+bdQX+nMY2YH18UjYPP372Uufxb++9C987bSvWW5zpuXEfSDeohmWJEmYOnUqfvvb38Ln82HJkiVob2/HT3/6U6xdu9bwmGuuuQZr1qxRXg8NDaG5uRk1NTUAoPwfAGpra5WnEHV1dcq2esHvhoYGJUKxYMEC+HyxQdLU1KTs09zcjOX+5QCAXYFdEMTYE46ZM2cqTzpmzpwJACgsLFQcfb/fr8wPLy4uRmNjIwCgtLQUJSWxp27l5eUoK4vdNCorK5UbYVVVlbKdKhMATXU7PRPbnjlzJgRBQEtLCwDkFRPjSMY0Y8YMALEbL+PwOpOdfqqqqkJ5eTlEUcwbJrv9VF9fj7q6OoiimDdMdvpJEASccsopyj0wH5js9lNLS4vyebpMXO6U+m83RXF+GvyJCpVF5aipDfb07sH6V9fjO2d+R7MMF9s2i1SvWrwKJ9efjIMDB7Fp/ybDfbI1p9qo+rdSqMwkUl1QYG/taTfNqdYUKjv299JOxFV/DRilfxvZmT2sYPY26q8iXxFGw6PKeDFyqqdXTEdLdYvl9mZDTtwHbJ2prq4OPp9PeWrP1NXVhWnTphke09jYiDlz5ig/4gBg3rx56OzsxMTEhOExfr8flZWVmn8AFMOwH0nsPavb+nQ9/T4nTz0ZJQUlGJ4Yxv7A/rhzZns7FSY728BkCmS+MOn5krVFeWqXJ0x2tplYGnA+MNntJ3YfYFMB8oEpk/dALzLZ2WZ9n6l7YL7L7kofv/jFLzB37lyUlJSgubkZ3/zmN5VsglyKpelTTdHn/DT41enf6jRh4JiTbWKDn734M9z+0u247437NJFqls5rNqd6VtUs3PnRO7Fw6kLTNrEUYDOn1DBSfWxOtZL+bbH6d1ykWjWnOiyFDYsOeyZSfezvS6pLaumvAcNCZQZ2BpKnwSuRal36t/pvYqXf+YfOTtwHbDnVRUVFWLJkCTZv3qy8J0kSNm/ejGXLlhkec+aZZ2L//v2a8Pu+ffvQ2NiIoqL4Se3ZlCzLOHLkiKmBC8QCnNp4KgDg5aP5V6wsGX++i/PT5ge4DTg/bX47srvSx5///Gd873vfw9q1a7F7927cc889ePDBB/H9738/xy3n/cz5afAnSv+OSlFTG/QEY1NY+oJ9yhrVQPI51cx5MnKM9fuYOa9G0VF9pNon+uIcSKU2hnpJLSE+Us2+PyJFEAqH4s6VKALtyki1nNqSWvprQP19iZbU0r+fMP1b51SHIpP2rihKPv8+23LiPmA7Jr5mzRqsX78e9957L3bv3o0rrrgCo6OjWLVqFQDgkksuwTXXXKPsf8UVVyAQCODqq6/Gvn378MQTT+Dmm2/GVVddlTkKixLFWOqfPnKn1mmNpwEAXjn6Sq6alTNZ4c9ncX7a/AC3AeenzW9Hdlf6ePHFF3HmmWfi85//PFpaWnDuuefioosuShrdzoao9zPnp8GfqFCZJEumNhgMDQKIOdF20r+Zw6p2OM9sPhMrZ69UXrO1pu2kf7OoJptbrT6XmgeYdK41S2qZzKmWhHiORI6pmsvxSDUMItU2sqP014DVJbUArR3M0r8BKFXjWcRbXenb7LtzKSfuA7bnVF944YXo6enBddddh87OTixevBibNm1Sipe1trZqAJqbm/H000/jm9/8JhYuXIimpiZcffXV+O53v5s5CouSZRnhcBiFhYWmg/O06fnrVFvhz2dxftr8ALcB56fNb1VspQ/1A3JRTLzSx/Lly/HHP/4R27dvx+mnn44DBw7gySefxMUXX2x6nlAopBSYA2C40gcAZeqOehpPom323UVFRRBF0fKx6ikC2dxOhcnONgBEIhH4fD74fL68YLLTT6zIIptTmw9MidoIGEeqJyYm4mwAAIPjx5zq0HBcoTJJmnS6BAgoKyqLWzpJ7XD6fX48ftHjuPWFW1FeVD5Z/dskZseOVXNcedqVCEfDuPDkC5X3C8VCTEQnp4hGpVjBSXUqtH5OtQBB+X6z6t8iRGUKkL4PjCLVTt0jGJt6TjVkmLY92T2QfV9EimjmSxsxqftXFMS4fdi8eWVc+AohSRJCUW1mgNPXkyRJmJiYgN/vV9qTan9YLXaWkvu+evVqHD58GKFQCNu2bcPSpUuVz7Zs2YKNGzdq9l+2bBleeukljI+P491338X3v/99zRzrXEmWZXR0dCRMBWBO9Y6OHYYXpJdlhT+fxflp8wPcBpyfNr9VpbLSx+c//3nccMMNeN/73ofCwkLMnj0bZ599dsL072yt9CHLMvbu3YuxsTEA+VfFPxlTJBJBe3t7XjHZ7aeOjg6MjY3lFZO+nyLRyTnDaocTAMKRMDo6OjAwMBDHxCLVfaN9cZHqnp4eDI/E3vMJPjRXNE+eIxL7/tHh0cnzTIQhR2X84P0/wPnTzleYzGopsGPVTKdMOQW3nnErmqualX7SRzpHgiPo7e1VGCPhiCb6CgCQgeBIrA8iUkTjlDNFI1HTfvIhPlLt1D1ieHhyzXDmi6iXGE429mRZxu7du5WxNzIc21dtEx98hkxqp1qOynFMLFI9NB5rixyR0dPTg1lVszS2dvp6ikQi2LVrl/IwPZ1+6uvrgxVlvfq3mySKImbNmpVwn7l1c1FdXI2B8QG83vW64mTng6zw57M4P21+gNuA89Pmz6a2bNmCm2++GXfeeSeWLl2K/fv34+qrr8aNN96Ia6+91vCYbK70ccoppyjv51sVfytMxx13nBKxyRcmq/00Y8YMJcKUzyt9COJkto0saAuVQQRmzZqlRNzUTCxSPS6Na+ZUj0XGUDulFqGRmNMiiiKOrz0eu/t2AwAqSmLzZGura5VjSktKUVhYGMdUUWY8p7aupi4hE+snfRGtt/rfwoN7H8T0iunK8XHLP/l8mraxucNqFfuLUVoam8Ot76fCgslzski1U/eIarlaaQvL/q2prlEKN1tZ6WPRokXK+9VVse9TR5OLCooMmdTp38VFxXFM+vTvitIK1NfX45NTP4mbP3AzzphxhiETU66up8LCQpx66qnKdib6KZlIOdWyLCMUCsHv9ysdoZcoiFg2Yxme2v8UXmx7Ma+caiv8+SzOT5sf4Dbg/LT5rSqVlT6uvfZaXHzxxfjKV74CADjllFMwOjqKyy+/HD/4wQ8M57X5/X4lNU8towrr6uOTbcuyrEn7s3Ms+2GWze1UmOxsy7KM8fFxhT8fmPTbydrC+NPpVzcxGW2rf+jrK11LsmRoA1mWtXOqVenfABCMBJW52j7Bp4k+FhUcK1Tmm3QdCn2TU2nUTGaFvlgEOhmfPlJ91yt34cjQEaU9PtEXdw9XFyozk/o4fR8YFehy6h6hpJ+r0voTtV2/rb8Hsu9TO9WFvkJDJrUdCnwFcfsohcmOfZffNznGrjlrcsqQ09cTAMUG6faH0d8vI+V3FQedZDmWopDsicOZzWcCAF5sezEXzcqZrPLnqzg/bX6A24Dz0+a3qlRW+ggGg3E/PNg0r1zbm3o/c34a/OpCZXqnOipHDW0wFhlT9h0OaQuVATFHW11NW73WsNGcarOCXmbvJ6ocrpY+Uj0WHlPaz9qmj1Srl9QyU8JCZUmWksqljOZU263+re5/dqw6/dvMVlYLlZm9doucuA+QilSLoqiE/xNpefNyAMALbS9ku0k5lVX+fBXnp80PcBtwftr8drRmzRpceumlOO2003D66afjF7/4RdxKH01NTVi3bh0A4LzzzsPtt9+OU089VUn/vvbaa3HeeeflvIYK9X7m/DT41c6Cfm1pSZYMbcBSvwHjSPVwaFhT5Xtm1UzlM6MltcycTzvVv42kjzizBwHqImr6ZbdEQTRde1lpb4Kq3m5ap1o9XzyVdar11wD7PrbsldFDCaZkD0284lQ7cR8g5VSzlKji4uKEqX+nN50On+DDkaEjWHDnAtz3yfuU9au9LKv8+SrOT5sf4Dbg/LT57cjuSh8//OEPIQgCfvjDH6K9vR319fU477zz8OMf/zjnbafez5yfBr96DnVcpFqKYmxsLM4GA+MDyvZwaFgzpxqIOdrVxdUAYo5XU2WT8pnRklpmTnLaTrWY2Kk2jFSr0qbNlCja66Z1qlmfSbKkZCTYGcv6a0AfqU708EGztJiBHfROtBuWzzKSE/cBcunfgUAgaSpAWVEZ3j/r/QCAXT27cO2zxkVWvCar/Pkqzk+bH+A24Py0+e3KzkofBQUFWLt2Lfbv34+xsTG0trbijjvuQHV1dc7bTb2fOT8N/kTp35IsGdqAzacGjCPVIxMjStRbFMTkkWqb6d9WHbBkkWpREOMcJbZ2dSLH2bJT7ZJItbrQnN30b3X/K5HqY/OgEz3cULMb7ceWTWNya6TaifsAqUi1KIpKxbdk+stn/4In3nkCFz9yMTbt34SO4Q40VjRmuYXZlR3+fBTnp80PcBtwftr8VES9nzk/Df5EhcqictTQBur0b0mW0D3arfl8ODSMhrJYNopPiKV/f2vZtyAIAkoLS5X3mbKW/q2LpLJK3oki1ex1gVhguJxWovbq2+Z0pDoT6d/q/o+LVCd4uKGJVBs8XCgvKte8dqtT7cR9gFykenR01NJTi5qSGnxx4RexbMYyROUo/vTmn3LQwuzKDn8+ivPT5ge4DTg/bX4qot7PnJ8GvzpSzYqLMUmyZGgDdaQaANqHYuv1ModSX6gMAH567k9x64duVY7RVIe2UOxKrVTnVCvrU6vmVBulfyc7R8JCZUmcyVxKU6gMqUWq1f3Pvo/NqU5ko2T9W1GkXS7NrU61E/cBck710NCQLQN/afGXAAC/3/l7z9+gU+HPJ3F+2vwAtwHnp81PRdT7mfPT4FfzadaoRmxOtZEN1JFqAOgb6wMANJbHMjGHQ8OaJZyMZMX5zHSkmom1TRREw0Jlyc5huVCZGyPVNudUq/tfn/6dcE51kkwEr0SqnbgPkHKqRVFEY2Oj5fXGAODCky9EcUEx3u55G68cfSWLrcu+UuHPJ3F+2vwAtwHnp81PRdT7mfPT4Nc70vrPjGygj1QzsYJkwxPDmjnVRkpnTnWqkWq9zJbUAhI7jF6ZU612oNUPEqxKfw3o078TPnhI8tCkwu+NSLUT94H8vuPoJMsyhoeHbT21qCquwqfmfQpALFrtZaXCn0/i/LT5AW4Dzk+bn4qo9zPnp8GvTv/WKypHDW2gj1QzTa+YDiBWqEyJVFtwmO3OqU625JXV/QRBiIvcWopUe2xONYCU5lTrrwH9kloJ51QnKVSmj1Rb7dNcy4n7ADmnOhgM2jbwlxZ9CQBw/1v3KwPSi0qVP1/E+WnzA9wGnJ82PxVR72fOT4M/EZ8kS4Y2MItUTy+POdXq9G/TdYxzsaRWKpHqNOdUuypSrXKgk2UOGEl/DWRySS0vzanO9X2AlFMtiiIaGhpspwJ84LgPoL60HgPjA9jRsSNLrcu+UuXPF3F+2vwAtwHnp81PRdT7mfPT4E8YqZaihjYwcqprS2oxrXwagGNLaskZSP9Ot1BZkuhnovRv9TmKC4rjjjOTWyPVrD/szKnWXwOZXFLLS+nfub4P5PcdRydZljE4OGj7qYVP9GHpjNj6nNvbt2ejaTlRqvz5Is5Pmx/gNuD8tPmpiHo/c34a/Mki1UY2GBgfiNt3zpQ5imM0IU0kL1SWiyW1kkSqhWP/GZ1TfQ79msqJItAaLpesUw1MRqrtpn+r+9/OklrJHpp4qVBZru8D5JzqUCiUkoFPn346AODloy9nulk5Uzr8+SDOT5sf4Dbg/LT5qYh6P3N+GvyJCpVF5ajGBmzqIptTrY4En1h7ouJkhaNhW4XKTJfUSrdQWSqRaoP0b3+BP+44M7kpUq2OSifLHDCS/hpIuVCZx9O/c30fIOVUi6KIqVOnppQKcHpTzKn2cqQ6Hf58EOenzQ9wG3B+2vxURL2fOT8N/kTp35IsKTZ45t1nULGuAp97+HNoHWwFAMyonKHse2LtiYoTG5bCyQuVpbGkVrIItNX9REE0LVSmPtZOpNpNc6oNI9U207/V14CtOdU2C5W51al24j6Q33ccnWRZxsDAQEpPLd7b9F4AwDuBdxAYC2S6aTlROvz5IM5Pmx/gNuD8tPmpiHo/c34a/Mn4Av0ByLKMl9tfRlgK48FdD+LgwEEAWqd6zpQ5iiM6EZ2wN6fawlrWZscmUqbmVHs2Uo30I9Xqa0B/LIUltZy4D5BzqiORSEoGri2pxQm1JwAAth3Zlumm5UTp8OeDOD9tfoDbgPPT5qci6v3M+WnwJ4pUA8BEeAKyLMeliZcXlWNJ4xLl9YlTTlQco3A0bGtOddaqf1tZUsvCnOpin7ZQmeUltdwYqbY5p1p9Dej7I6NLalnMPsi1nLgPkHKqRVFEXV1dyqkAZ886GwBw479uTDiXxa1Kl9/r4vy0+QFuA85Pm5+KqPcz56fBn8xZqKmtgSiKSqTzitOuwNE1R3Hkm0dQW1Kr7GeW/p1O9W+3LKllJ1JtpQBbrpTunGr9NZBypNrCnGq3rlPtxH0gv+84OsmyjEAgkPJTi7Vnr0V5UTm2HtmK9a+uz3Drsq90+b0uzk+bH+A24Py0+amIej9zfhr8RsEdtbPUG+iFLMtKpNMn+NBY0Yiq4ip0jnQq+1X4K2wVKkvmdLFzGSnn6d++FNO/XRSpZv1sZ061/hqw41Qns4P+QYXTDyDM5MR9gJRTna5mVM7ADWffAAD4zau/cbg1XFxcXFxcXFxcFGWU/q12iJhzbJTOvWT6Es1xtgqVWVh6yrRQmcWopqUltUwKlaltUOgr1LTFcqEypyPVBqnediLVyY5NZ51qLnORcqoFQUBtba2tpz16fWHhFwAAr3W+hqPDRzPVtJwoE/xeFuenzQ9wG3B+2vxURL2fOT8NfqMInNppra6phiAIhunDlyy6BL/92G+xb/W+2HHqSLWNQmWmS2o5UajMIP27QCzQOIlejFQz2ZlTrb8GUk3/TseRd1pO3Ae8a60UJEkSent7IUmpz4eeWjYV750eqwS+af+mTDUtJ8oEv5fF+WnzA9wGnJ82PxVR72fOT4M/WaS6u6cbkiRp0r/V+1225DKcOOVEAJNO7ER0InmhMgvp3+nOqZ5dOzvh56IgmhYqUzvkBWKBNlLtwUJlTHacQ/01oP++RHzsM5/g8/SDKSfuA6ScakEQUFBQkPYg+ciJHwEAPLX/qUw0K2fKFL9Xxflp8wPcBpyfNj8VUe9nzk+D3zBSrUqbFn2xtZyTOcnq48JS8jnVVpzPdOdUX7zwYrzxtTfwyZM+afi5IAiW5lQXioWWI69WHhbkSkZj107UWH8NpBKpTjheXFqcTC0n7gPknOrq6uq0DfzhEz4MAHjm3WeUm48XlCl+r4rz0+YHuA04P21+KqLez5yfBr9RoTK1M1teWW6a/q2X0ZJapoXKcrCkliAIOKXhFNO51aIgxvWvUfp3oa/Q0hxw/XFOR6qB+HRvu+nf6msgLlKdwGFWR6rNpC9W5kY5cR8g5VRLkoTu7u60UwFOm34aKv2VGAoN4fWu1zPUuuwrU/xeFeenzQ9wG3B+2vxURL2fOT8NfqP0b5/oUxyoROnfetkpVGaloJdpoTKbaxqbfY/RnGqjQmU+wWc5Uu2mQmVAfLTaTqRafw2kUv070T76qupulBP3AVJOtSAI8Pv9aT+18Ik+vG/m+wAA/zr8r0w0LSfKFL9Xxflp8wPcBpyfNj8VUe9nzk+D3yj9W+1s+gp9mki1pfRvC4XKNGnSZunfaRYqYzKLzgqwlv5d5CuyvP602yLVZoXYrEh/DeiPtTSnOoGtWGaDm+XEfYCcU11VVZURA79/5vsBeM+pzhS/F8X5afMD3AacnzY/FVHvZ86fH/w7O3fihF+dgJqf1ODbz3w77nOjSLUoiIpTVF5erp1TbTdSbcExzlb6d7LvSVSoTNM+X4F3I9UmfJaO1V0DKc2pzoP071zfB0g51ZIkoaurKyOpAO+fNelU53Jh8XSUSX4vivPT5ge4DTg/bX4qot7PnD8/+Dft34R3+9/FwPgA/vDGH+I+Txap7uzu1KR/J3LKNJHqJPtbifymW6iMyU76N3OeNNW/BW3174SFyizOvc6VzCLxVqS/BlKp/p0P6d+5vg+QcqoFQUBpaWlGnlosmb4EJQUl6Bvrw66eXRloXfaVSX4vivPT5ge4DTg/bX4qot7PnD8/+CNSRNk2KkpmVqiMObr+Yr/19G+jJbWszKk22cfIeTVyhJPJrA+NCpWZRqpTKVTmgki12ZxxK9JfA5mu/u2V9O9c3wfIOdUVFRUZMXCRrwjnHHcOAOD+N+9P+/tyoUzye1GcnzY/wG3A+WnzUxH1fub8+cGvXl3GyIE2S/9mDlRJaYnWqU6U/q1eUsvOnGoLa1kz2Y1SJ2qDIFicUy0WpZb+7YJItVl1c6vHqq8BW061lUi1R9K/c30fIOVUS5KEjo6OjKUCfGnRlwAAG1/f6ImltTLN7zVxftr8ALcB56fNT0XU+5nz5wc/c24BE6faJP2bOUUs/TvZEllAbpbUSsmpNnFTEqV/6yPOKRUq83ikWn8N2FlSi9kh0YOFU6aeYrktTsmJ+4D9Ee5hCYKAysrKjD21+Pjcj2NKyRQcHT6Kp999Gh858SMZ+d5sKdP8XhPnp80PcBtwftr8VES9nzl/fvBnLFItWU//liEjHA0n3D/V9O9MRqqtFiorFAu9G6lOc51q9TWQ6fTv2869DQIEXLr4UsttyrWcuA+QilQLgoCysrKMGdhf4MfnFnwOAPDUO09l5DuzqUzze02cnzY/wG3A+WnzUxH1fub8+cGfcqT6mDNUXFJsO/0bAELRkPJdRrKU/m1wrlScarM+tLqkVqGvMG/mVNtN/1ZfA5kuVFZbUot7zr9HKdrsRjlxHyDlVEuShPb29oymAryn8T0AgH2BfRn7zmwpG/xeEuenzQ9wG3B+2vxURL2fOX9+8Ksj1UZTDJNFqjs6OzTp31Yi1QAwHhmP7W+hUJmd9G/1OawqYaTaQqEydeG2RN8HWFt/O5cy47Mi/TWQ6SW1vCAn7gOknGpBEFBbW5vRpxZzpswBAOzt3Zux78yWssHvJXF+2vwAtwHnp81PRdT7mfPnB3+ySLXRe5p1qivLNenfVpbUAiad6kQOLZOZ45XtQmUJl9RSsRT6Ci0vqeX6SLXN9G/1NWBnTrWVSLUX5MR9wNsWsylBEFBSUpLR75w7ZS4AoHWwFWPhMZQUZvb7M6ls8HtJnJ82P8BtwPlp81MR9X7m/PnBn3ROdZJ1qguLCi2nf6s/UyLVSYpZRaSI6T6ZmlNt5kgKgmB9TnUqhcpcEKVNd0kt9TWQ6TnVXpAT9wFSkWpJktDW1pbRVIC60jpUF1dDhoz9gf0Z+95sKBv8XhLnp80PcBtwftr8VES9nzl/fvAnnVNtkv7NnCGW/m2lUJkgCEoF8FAk8ZxqIHk0M1Nzqm1Fqg3mVBeIBakVKnOBQxlXqMxGxFV/DdiZU22l+rcX5MR9gJRTLQgC6uvrM5oKIAiCEq3e1+fuedXZ4PeSOD9tfoDbgPPT5qci6v3M+fODP1mk2khqZ7OiKrZGrzKnOomTxOY8K+nfCVyEZI5XLqp/W1lSq0AsSK1QmQscynQj1eprINPrVHtBTtwHyDnVxcXFGTcwm1ftBac6G/xeEeenzQ9wG3B+2vxURL2fOX9+8Ksj1TJkTbq3Ueo3oJ1TXVioTf9O5pSxucis+nfCebdJUoQNC5X57BcqM+tDq4XKinxF1guVWUwTz5X0fHbnVKuvAVtzqvMo/TvX9wFSTrUkSTh8+HDGUwFYpHpvn7uLlWWL3yvi/LT5AW4Dzk+bn4qo9zPnzw9+fcVvdbTaLHKtjuC2d7RbTv8GJiPVyZbUAiykf2e5UJnVJbXUDxnM2mXUPq9HqvXXAMVItRP3AVJOtSAIaGxszFqk+uWjLyMiRTL63ZlUtvi9Is5Pmx/gNuD8tPmpiHo/c/784FdHqgGtI62eT62vbs0cx5raGnvp3z5t+nc6827VbTJydq0q4ZJaJoXK1Et3FfmKPFv9O5051fprwNY61XmypJYT9wFyTnVRUVHGDfz+We9HRVEF3u55Gzc+d2NGvzuTyha/V8T5afMD3AacnzY/FVHvZ86fH/wJnWpV+rfa+VFHqkWfaC/9Wz+nOsH+/3Xqf+H9s96PefXzDD9Xt8lf4AeQ2erfVudUF/oKUytU5gKHMt0ltdTXQCqRajc8WEhHTtwHSDnVkiTh0KFDGU8FaChvwG8+9hsAwE3/vgmdI50Z/f5MKVv8XhHnp80PcBtwftr8VES9nzl/fvAnSv9WR6r1TiNzio52HrWX/q2PVCfY/5YVt+C5Lz2nVAzXS+3EFRcUA8hw+rcgWJpT7RN8qRUqc4FDmW76t/oasDOnurSwVPN/r8qJ+wApp1oQBMyYMSMrTy0uOuUinDL1FEiyhJeOvJTx78+EssnvBXF+2vwAtwHnp81PRdT7mfPnB78+Uq0pXGYhUl07pVYTqU4WfbWzpFYyqY9tKGsAEFuCNp3v0b9vZU61nUi1ej83RKrjCpXZTP9WXwN2ItUfm/Mx/PfS/8Y177vGRmvdJyfuA+ScalGMrxiYKS1tWgoA2HZkW1a+P11lm9/t4vy0+QFuA85Pm5+KqPcz588PfquFyuIi1cdey5C1c6otFiqzkv6dTOpznTv7XPzpU3/CL1b+wvb3JKr+bcWpLvIVpVaozIWRarvp3+prQH9soocGVcVV+Pl//hynN51uo7XukxP3AVJOtSRJaG1tzVoqwNIZMad6+9HtWfn+dJVtfreL89PmB7gNOD9tfiqi3s+cPz/4rRYq0zuNzBnr7O7UpH/bXlIrjWit+lyFYiE+f8rnMat6Vlrfo5Zw7D+jfTXVvyF6dk51OoXK9NeAnUh1vsiJ+wApp1oURcycOROimB1s9lTn5faX454wukHZ5ne7OD9tfoDbgPPT5qci6v3M+fODP+Gcajn5nOopdVMgiqLl9O+MRqoztOazrfRvg0Jl/gJ/XsypttsX+muAolPtxH3A23ccm5JlGZIkaW5GmdT8+vkoLSzF8MSwK9eszja/28X5afMD3AacnzY/FVHvZ86fH/ypLqnFXkeiEciynJVCZcmkblM6Ud9E1b/NCpUxDnbulJbUckOkWsVnJ/UbiL8G7BQqyxc5cR8g51QfOXIkawYuEAtw2vTTAADfeOob6A32ZuU8qSrb/G4X56fND3AbcH7a/FREvZ85f37w6yPV6tdqNk26s2pOdU9vT8yxkI1TgPVikepMFCrTFP3KUqQa0DqbRnOqC8QC64XKMhRdz5TSiVTrrwGKkWon7gOknGpRFNHS0pLVVIAfnPUDlBSUYPPBzbjqyauydp5UlAt+N4vz0+YHuA04P21+KqLez5w/P/gTRao1hcpMqn/bTf9m1b+VSHWG5lRn6nvUMqpqbTSnuqggxUJlLohUq9nsFtvSXwNxkWoX8GVbTtwHvH3HsSlZljExMZHVpxbnzj4Xf7/47wCAv+35GwbGB7J2LrvKBb+bxflp8wPcBpyfNj8VUe9nzp8f/OmuUz0Rnkgp/ZsVKsvUklrpRH0TVf/Wf240p9on+FIrVOaCSLVRFN6q9NcA1Uh1ru8D5Jzqjo6OrBt4efNyzK+fj1A0hEd2P5LVc9lRrvjdKs5Pmx/gNuD8tPmpiHo/c/784E84p1rFZjanuq+/T5P+bbVQmdV08USyWhwsmZKlf2uiuUbp30JBaoXKXBDJTTf9W30NUJ1Tnev7ACmnWhRFzJo1K+upAIIg4KIFFwEA7n/r/qyey45yxe9WcX7a/AC3AeenzU9F1PuZ8+cHv+VItS79mzlMtVNqNenfVpfUUr43U4XKsjCnmjnQ6giuafq3VyPVBlF4q9JfAxQj1U7cB7x9x7EpWZYxPj6ek6cWn1vwOQDAPw/+E0Ohoayfz4pyye9GcX7a/AC3AeenzU9F1PuZ8+cHvz5SrX6dqFAZc6DGQ+P20r9FrVOdsUJluYpUH3M81RwFYgFEWHPw8y1Srb4GKM6pduI+QM6p7unpyYmBT6g9AbNrZiMqR/Hvw//O+vmsKJf8bhTnp80PcBtwftr8VES9nzl/fvAnilRrCpWZzKkeHB6MOdU216lWvjdThcrSmVOdYEktQBvBNYpU25lTnak2Z0rpzqlWXwMUI9VO3AdScqrvuOMOtLS0oLi4GEuXLsX27dtN9924cSMEQdD8Ky4uTrnB6UgURTQ3N+csFeADx30AQCxa7Qblmt9t4vy0+QFuA85Pm5+KqPcz588P/nTXqa6qroIoipNzqi0WKjP6XrvSp6SnKjvVv43mVPsL/JbbIgiCsq8bIrnpRKr11wDFOdVO3Adsn+nBBx/EmjVrsHbtWuzYsQOLFi3CypUr0d3dbXpMZWUlOjo6lH+HDx9Oq9GpSpZljI2N5eypBXOqNx/cnJPzJVOu+d0mzk+bH+A24Py0+amIej9z/vzgTzinWsVmNqdan/6dzDFjS2op35upOdU5Sv+2EqlO1hZ2rBucznTmVOuvAaqR6lzfB2w71bfffjsuu+wyrFq1CvPnz8fdd9+N0tJSbNiwwfQYQRAwbdo05V9DQ0NajU5VsiwjEAjkzMDntJwDAHi963X0Bntzcs5EyjW/28T5afMD3AacnzY/FVHvZ86fH/zpRqqHR4fTSv/2xJJaSLykVoFYYCtqrjjVHo9U668BqnOqc30fsNVLExMTePXVV7FixYrJLxBFrFixAlu3bjU9bmRkBLNmzUJzczPOP/987Nq1K+F5QqEQhoaGNP8AQJJiNxRZlhUjSZJkeVsURTQ2NioXntVj2TntbteX1mN+/XwAwHOHnku6fypMdrYFQUBTU5Ni51SY7G5nm8lOPwFAU1MTBEHIGyY724IgoLGxEaIo5g2T3X4CgOnTp8dS4vKEKVv3QK8w2dkWRRHTp08HU7pt53KnRFFEU1OT59N/UxXnzw9+K5FqURC184oxOae6srIyVv3b5jrVynd5rVCZQfq3vvp3Mhu4KVJtxGb5WN01QDFS7cR9wNaZent7EY1G4yLNDQ0N6OzsNDxm7ty52LBhA/72t7/hj3/8IyRJwvLly3HkyBHT86xbtw5VVVXKv+bmZgBAf3+/8n+2HQgEMDg4qLSPOeA9PT0YGRkBAHR1dSEYDEKWZRw6dAhjY2MAgPb2doRCsUXu29raEA6HAQCtra2IRqOQJAmtra2QJAnRaBStra0AgHA4jLa2NgCxBwDt7e0AgPHxcXR0dAAAgsEgurq6cGbzmQCAf+6LzaseGhpCb28saj04OIhAIJAWEwB0dHRgfHw8KVMkEsHw8DAOHz6cFhMQe1DS09PjOJPdfhodHcXY2FheMVntp8HBQRw5cgSyLOcNUyr91NXVBVmW84rJaj/JsowDBw4ofPnAZKefotGocg/MBBOXOyXLMkZHR8k++OD8+cEfV/1b5WQzB1uAYBqpHgvFUl+trjvtxkJlSZfUMihUpn44oF+nOpkNqourAQBV/qqU2ptJGUXhrUp/DVCcU+3EfSDrjyqWLVuGZcuWKa+XL1+OefPm4Te/+Q1uvPFGw2OuueYarFmzRnk9NDSE5uZm1NTUAIDyfwCora1VBltdXZ2yXV9fr2w3NDRAEATIsgy/3w+/3w9gMmoJAM3Nzcr2zJkzlaJqM2fOhCiKyjYAFBYWKo6+3+9Xor/FxcVobGwEAJSWlqKkpATLZizD+h3r8UbgDQCxJ4esg6uqqpTtVJkAaCJPiZhkWcbIyIgycT9VJgAoLy9HWVmZ40x2+qmxsRE9PT2YOnVq3jDZ6aeKigrl4VK+MNntp9raWnR3d0OW5bxhstNPsiyjpKQERUVFecNkp58AKPfATDBxuVOyLGNoaAglJSW2f4zmgzh/fvBbWadaEOKdauYwBcdif+8tp39nqVBZOs55surfRktq6edU25nf/adP/QmHBw+juao55TZnSummf6uvAYqRaifuA7asWldXB5/Ppzy1Z+rq6sK0adMsfUdhYSFOPfVU7N+/33QfteOrFgvha55MqcL6ybb16c92j2XntbO9vHk5AOCVjlcwEZ1Aka/IdP9U2mV3m/1QTIfJznYumNTbidpSUFCg4c8HJjvbPp9Pk/qaD0x2+6mgoEBjg3xgyuY90AtMdrf110Am2s7lLrFpDlTF+fODP+Gc6mMPJhNFqsvKy+ylf+sj1ZkqVJaFSLXRnGr2XnlROUoLS1EoFqK4sNjykloAcObMM3Emzky5vZmU+m9MKunf6muAolPtxH3A1qOPoqIiLFmyBJs3T1azliQJmzdv1kSjEykajeLNN9905IYnyzKGh4dzmgowZ8oc1JbUYjwyjtc7X8/ZeY3kBL+bxPlp8wPcBpyfNj8VUe9nzp8f/KlEqn2iT4nGjo1r07+TRWn11b/dPKeaOZxG846LC4rxj4v/gccueEyzbre+XW5XupFq9TVAtVBZru8Dtq+YNWvWYP369bj33nuxe/duXHHFFRgdHcWqVasAAJdccgmuueYaZf8bbrgBzzzzDA4cOIAdO3bgi1/8Ig4fPoyvfOUrmaOwKFmWlfTXXEkQBJwx4wwAwOf/+nn8+c0/5+zcejnB7yZxftr8ALcB56fNT0XU+5nz5we/lUi1KIhx0VrmQI1PjGvSv5POqfa5b061WUZQoiW1AGBp01LMrZgLWZZtRardpHTnVKuvAf3xFCLVTtwHbFv1wgsvRE9PD6677jp0dnZi8eLF2LRpk1K8rLW1VZNy19/fj8suuwydnZ2oqanBkiVL8OKLL2L+/PmZo7AoURQdWc7rUyd9Ck++8yT2B/bj+5u/j8+f8vmctwFwjt8t4vy0+QFuA85Pm5+KqPcz588Pfn2kWu1kJypUxpzh0rJSCIIwGam2mf6dsSW1slj9W5MirZtOxMaAnUJlblI6kWr9NUCxUJkT94GURtfq1atx+PBhhEIhbNu2DUuXLlU+27JlCzZu3Ki8/vnPf67s29nZiSeeeAKnnnpq2g1PRazqca6fXn75PV/GW1e8BQA4PHgYQyFnqsY6xe8WcX7a/AC3AeenzU9F1PuZ8+cHv5V1qvXp3+pI9ejYqMYxd6pQWaacc7VYFNds2Sn1GMhUKnqulc6SWvprgOKcaifuA955ZJMBybKMUCjkyI325Kkno6kiViDore63cn5+wFl+N4jz0+YHuA04P21+KqLez5w/P/itrFNtGKk+5kSGw2HNd9heUssFhcqSVf82KlQGaMdAOhFfJ6WJvKcwp1p9Dejt6KWHC6nKifuAd0ZXBiSKIqZOnZrThcDVOqXhFADAm11vOnJ+p/mdFuenzQ9wG3B+2vxURL2fOX9+8OuXwjKLVOudL+aAFZcWQxYmHYqk6d+ZjFRnuVBZoiW1AO0YyIdCZXbnVOuvAYpzqp24D3j7jmNTsixjYGDAsaeXp0w95lR3O+NUO83vtDg/bX6A24Dz0+anIur9zPnzg59FmZmzazlSLUyuUx2JRpTPkqZ/6yPVLihUZsep1keq2RjIh0JlqUSqE10DXnq4kKqcuA94Z3RlQLIsIxKJkHaqneR3WpyfNj/AbcD5afNTEfV+5vz5wc8i1WypK3UqN3Ow1ZFp/etINKKdU53EkcrkklqZKlRmFqFl75ut5aweA5pItYfSntOdU53oGvCSHVKVE/eB/I//qySKIurq6hw7vzr9W5Zl2+kc6cppfqfF+WnzA9wGnJ82PxVR72fOnx/8SqRaNIhUJyhUxpxnf7Ffk/5te0mtNKOZoiBCkiVHItXqMeDZSLVJZXMrSnYN5Nr/cEJO3Ae8M7oyIFmWEQgEHHt6Oa9uHnyCD/3j/Tg6fDTn53ea32lxftr8ALcB56fNT0XU+5nz5wc/i1QnS/82W6d6dGw0rfTvdB1QdnxWl9QyWctZPQYoLqmVL9dAOnLCBt4ZXXkgf4Efc6bMAeBcCjgXFxcXFxcXF5e7xZxo25FqVWEz9bJcdiPV6TqgrB3ZqP6dbEktTTvEPChUZjP9m8sZkXKqBUFAbW2to2kPTlYAdwO/k+L8tPkBbgPOT5ufiqj3M+f3Pr/agbZbqIy9LiwqVJxvURCT2iOThcrYOdP9nqSRapNlp9RjwLNLaqVRqCwfroF05YQNvDO6MiBJktDb2wtJkpLvnCU5WazMDfxOivPT5ge4DTg/bX4qot7PnN+b/JIs4YG3HsChgUOaAmNKoTLZWqEyFo0dHRtFOBJW3k+mjEeqxfQj1akuqaUeA3lRqMymY+jVayCTcsIGpAqVCYKAgoICR5/cLJi6AIAzTrUb+J0U56fND3AbcH7a/FREvZ85vzf5f//a7/GVx78CABj7wZjyfrL0b7N1qmXIkBA7xoozWVZYpnmdiUJl6v+n8x16MWazKLR6DORDobJUItVevAYyKSdsQM6prq6udrQNLFK9u2c3IlIkpwuwu4HfSXF+2vwAtwHnp81PRdT7mfN7k3/L4S3KtjpSneo61YVFhcoxVhzk42uOVyp2s+9KR8qc6iwsqWVYqExXtIyNgXwoVGZ3TrVXr4FMygkbeGd0ZUCSJKG7u9vRdIjjao5DWWEZQtEQ3ul7J6fndgO/k+L8tPkBbgPOT5vfru644w60tLSguLgYS5cuxfbt2xPuPzAwgKuuugqNjY3w+/2YM2cOnnzyyRy1dlLU+5nze5Nf7USpU73tFipjr0eCI0r1byvOZElhCY6vOV55nbE51Q4sqaUeA+z8AgRPRW7TmVPt1Wsgk3LCBqScakEQ4Pf7Hb2oREHEyVNPBpD7FHA38Dspzk+bH+A24Py0+e3owQcfxJo1a7B27Vrs2LEDixYtwsqVK9Hd3W24/8TEBD70oQ/h0KFDePjhh7F3716sX78eTU1NOW4572fO701+jVOdYqTaJ/gmnUhRsJX+DQDz6+cbticV1ZbUav6fiuwUKtNvszHA2L0UpQbSm1Pt1Wsgk3LCBuTSv6uqqpxuBk6uPxnb27djT++enJ7XLfxOifPT5ge4DTg/bX47uv3223HZZZdh1apVAIC7774bTzzxBDZs2IDvfe97cftv2LABgUAAL774IgoLY05AS0tLLpusiHo/c35v8otIHKlWO9qJCpWxKLavwGcr/RuI/T59bO9jynelowcveBCHBw9jZtXMlL8j1SW11GMgExFzJ5Ru+rcXr4FMygkbeOuxTZqSJAldXV2Op0Owtar39e3L6Xndwu+UOD9tfoDbgPPT5reqiYkJvPrqq1ixYoXyniiKWLFiBbZu3Wp4zGOPPYZly5bhqquuQkNDAxYsWICbb74Z0WjUcH8ACIVCGBoa0vwDoPSPLMtKRE6SJMvbkiSho6NDObfVY9k5s72dCpOd7Wg0iq6uLkQikbxhstNPkUgEXV1diEajnmISxcmf5CxtG4BSe0eSJ8e3plAZjAuVBceCCEdj1b99gs8S00lTTlK+S4CQFtOpjafi/Lnnp9UHppIn26h5+9gxkUgEnZ2dkCRJ44Bne+xl8nrSFypL9x5oZKd8uu/pmaLRKDo6OjQ2SZcjmUg51YIgoLS01PF0CKecarfwOyXOT5sf4Dbg/LT5raq3txfRaBQNDQ2a9xsaGtDZ2Wl4zIEDB/Dwww8jGo3iySefxLXXXouf/exnuOmmm0zPs27dOlRVVSn/mpubAQD9/f3K/9l2IBDA4OCg0j7mgPf09GBkZAQA0NXVhWAwCEEQEAwGEQqFAADt7e3KdltbG8LhmKPR2tqqOF6tra2KQ9ra2goACIfDaGtrAxB7ANDe3g4AGB8fR0dHBwAgGAyiq6sLADAyMoKenh4AwNDQEHp7ewEAg4ODCAQCaTEBQEdHB8bHx5MySZKE4uJitLW15Q2TnX7q6OhAaWmpsu0VJnWkenwidrxP8EGQY/crSZaUflIciKgUF6meCE0cewEMDQ8p71thml44Xfmu/v7+rPaTlbE3PDQMIw0NDin8TGPBMYUpEAgojuno8Khiy2yPvUxeT/r0bztjTxAEjIyMYGJiQmFSKx/ve3omSZIwODgIQRDSZurr64MVCXLCR0Hu0NDQEKqqqjA4OIjKykqnm5O2dnXvwoK7FqDKX4X+7/bzH3hcXFxcHlO+/V1S6+jRo2hqasKLL76IZcuWKe9/5zvfwXPPPYdt27bFHTNnzhyMj4/j4MGD8PliaZa33347fvrTnyo/WvQKhULKDyQgZtPm5mb09/ejurp6ct6oIMQiTseWD8rWtiiKSkQlm9uciTMZMV35xJW465W7AACHrz6MWb+chSJfEVYctwJP7n8SGz6+AV9a/CXIsoxXOl7B0t8txcyqmTin5Rzc+/q9AIBff/jXkGQJ39j0DXz25M/iu8u/iyXrl2B6xXQc+eaRpEwjoRFU3FIBANj0hU04d/a5jvbT0/ufxof//OG4e0fbf7dhRtUMnPG7M7CtPXY/uuujd+GrS74a15b/2/t/OO+B81BRVIGB7w54Zuxd+uiluO+N+wAApzedjq3/tTWttvtunEx/l66T8v56ymTbBwcHUVNTk/TvPalINUuHsBrGz5Zm186GAAGDoUH0BHtydl638Dslzk+bH+A24Py0+a2qrq4OPp9PeWrP1NXVhWnTphke09jYiDlz5igONQDMmzcPnZ2dSrREL7/fj8rKSs0/YDINlv1IYu9Z3ZakWJo/+yFn9Vh2zmxvp8JkZ1uWZc2DjHxgstNPAJRorpeY1MXE1AXG2OeSLCltVJwUoyW1js0dHg2OIiJFlO+xwlTuL8esqlkAgNOmn5bVfrKyXeAzLv3E3mfnUzMysfRvVujNJ/qyPvYyeT3p51Snew9UKx/ve3omWZaV6V6Z4kgmUk61IAiorKzUXHROqLigGLOqYzetXKaAu4XfKXF+2vwAtwHnp81vVUVFRViyZAk2b96svCdJEjZv3qyJXKt15plnYv/+/ZoHFvv27UNjYyOKioqy3ma1qPcz5/cmv1H1b5/oU943WlJLFMS4OdVKtesCUbOfVe1ZvQfd3+rGlNIpKZJkTqkuqaUeA+whg9eqf6vHr922e/UayKScsIG3RliaEgQBZWVlrhhkbF713t69OTunm/idEOenzQ9wG3B+2vx2tGbNGqxfvx733nsvdu/ejSuuuAKjo6NKNfBLLrkE11xzjbL/FVdcgUAggKuvvhr79u3DE088gZtvvhlXXXVVzttOvZ85vzf5jdap9gmTTrW6IjhzsPURTXWhMkEUbFf/BmKBn/qy+hQpMqtk1b/1Fb/V22wMeHZJLWgfEtiRV6+BTMoJG3hrhKUpSZLQ3t7uitS/uVPmAgC+/fdv493Auzk5p5v4nRDnp80PcBtwftr8dnThhRfitttuw3XXXYfFixdj586d2LRpk1K8rLW1VZNi3NzcjKeffhovv/wyFi5ciG984xu4+uqrDZffyrao9zPn9ya/2vFVR6qZU2hlnWp1+ncwqK3+7UXZiVSrHWz1GFCW1PKYDcyi8Fbk1Wsgk3LCBuTWqa6trXXFk5v59fMBAP3j/Vh09yJs+8o2nDz15Kye0038Tojz0+YHuA04P21+u1q9ejVWr15t+NmWLVvi3lu2bBleeumlLLcquaj3M+f3Jn+ySLVR+rdRpFqZW1qoWrPaY2s0MyVzqs1SpNVjIB/Sv1NZp9qL10Am5YQNvDXC0pQgCCgpKXHFIPvCKV/ANe+7BqdMPQWj4VF8+n8/jWA4mNVzuonfCXF+2vwAtwHnp81PRdT7mfN7k9/WnGpVpFrvWCoRWWHyGK85lExmfag8OBCMU6TVY4DZw2sPFszYrCjRNeDVsWBXTtwHaFj2mCRJUtZtdFoV/grc/MGbsfmSzWgsb8Tevr346+6/ZvWcbuJ3QpyfNj/AbcD5afNTEfV+5vze5FenJ1uNVKvnUOtfB8dopX+rt9VjoNIfW1Wgoqgiiy3NvPQF6Owo0TVgN+rtVTlxHyDlVAuCgPr6elc9vawvq8eqxbHCL4/tfSyr53Ijfy7F+WnzA9wGnJ82PxVR72fO703+ZJFq9h6QuFAZi8j6CnwpFSpzk5Kmf6sLlemKlrExML9+Pn794V/jNx/7TXYbm2GZzRe3okTXAKVIda7vAzQse0yCIKC4uNh1N9rzTzofAPDU/qcQioSydh638udKnJ82P8BtwPlp81MR9X7m/N7kVzu+oWjst6BP8CnvWy1UprwWkNKSWm6SmTNpZUktNgYEQcBVp1+Fs2adld3GZlhm88WtHmt2DXh1LNiVE/cBGpY9JkmScPjwYdelBJ02/TQ0ljdiZGIEzx56NmvncSt/rsT5afMD3AacnzY/FVHvZ87vTX61s8MCLMnWqRYEIS5NuECM1SAeHRvN2/RvZUkt3TxqJq+OAbXSmVOdiN9rD5tSlRNjgJRTLQgCGhsbXTegREHEx+Z8DADwjwP/yNp53MqfK3F+2vwAtwHnp81PRdT7mfN7k1/jVKsi1Wy9YquRauZUiwUiJOR3+rdZirRXx4Ba6cypTsRPKVKd6zFAbkmtoqIip5thqMXTFgMA9vXty9o53MyfC3F+2vwAtwHnp81PRdT7mfN7k1/t7IxHxgEkj1QbFSorFAsBABE5MjmnOs8i1VbSv704BtRKd061GT+VQmVOjAEajyuOSZIkHDp0yJXpICfWnggAeCfwTtbO4Wb+XIjz0+YHuA04P21+KqLez5zfm/xqZ0dJ/1ZV/2YVwQHzQmU+0YdCX8ypHh0fRSQaAeDd6GSyJbX00Wkmr44BtcweGFhRIn6vjgW7cmIM0LDsMQmCgBkzZrgyHeTEKTGn+kD/AU2Fx0zKzfy5EOenzQ9wG3B+2vxURL2fOb/3+ZX0bzF5oTJ9QSsWqRZEgVT6tz5S7fUxYDZf3OqxZvxetokdOTEGyDnVoii6ckA1VzbD7/NjIjqB1sHWrJzDzfy5EOenzQ9wG3B+2vxURL2fOb83+VlKN2AcqTYrVBaX/n0sUh2Wwnmb/m1YqEwXtfbiGFAr3fRvM34qkWonxgANyx6TJElobW11ZTqIT/Rhdu1sANlLAXczfy7E+WnzA9wGnJ82PxVR72fO701+tdOsjlQbOtUJCpWxSPXYxJiS/u3VSLWZM8kcJbNItVfHgFrpFCpLxE9lTrUTY4CUUy2KImbOnAlRdCc2m1edrWJlbufPtjg/bX6A24Dz0+anIur9zPm9ya9xqi1Gqg0LlR2LVMuC7Pl1qo3anajit7KPR8eAWuksqZWI36tjwa6cGAM0LHtMsixDkiTlCZ/bpBQr68tOpNrt/NkW56fND3AbcH7a/FREvZ85vzf5k0Wq1fV2zAqVqSPVYSmMiHQsUp1H6d+m86hVDrZXx4Ba+rnydpSI38sp8XbkxBgg51QfOXLEtRcZK1aWrfRvt/NnW5yfNj/AbcD5afNTEfV+5vze5DeLVDOH2LRQmS5NmEWqQ+HQpFPt1fTvJHOCzba9OgbUSmdOdSJ+KpFqJ8YAqXWqRVFES0uL080w1fz6+QCA7e3bEY6GlRtjpuR2/myL89PmB7gNOD9tfiqi3s+c35v86h//SedUJyhUViDGftqzyt/sfS/KqN1mKd/69G8vjgG10llSKxE/lTnVTowBb15lKUqWZUxMTLj2ydUZM87A1LKp6Bvrw+aDmzP+/W7nz7Y4P21+gNuA89PmpyLq/cz5vcmvdprHI+MAEsyptlCojFr6tz5S7cUxoJbZwwMrSsTv1QcsduXEGKBh2WOSZRkdHR2uvcgKxAJ8Zv5nAAD3v3V/xr/f7fzZFuenzQ9wG3B+2vxURL2fOb+3+Dcf2IxfbfsVovLknGkl/TtJpDpRobJwNKzMw/Zs+rdBVNW0UJluTrWXxoCR0olUJ+Kn5FTnegzQsOwxiaKIWbNmuboa4EULLgIAPLL7EeWmmil5gT+b4vy0+QFuA85Pm5+KqPcz5/cW/9ee+Bqu3nQ19vTuUd5T0r9VkWq1060uVKYvaMUi1TJkROT8i1SbFfDSbHtsDBjJbA1uK0rET8WpdmIM0LDsMcmyjPHxcVc/uVrWvAwVRRUYnhjGwYGDGf1uL/BnU5yfNj/AbcD5afNTEfV+5vze4h+ZGAEADIWGlPfSWqdaVY+HpZF71ZFKuqSWyZxqr40BI6WzpFYifq+OBbtyYgzQsOwxybKMnp4eV19koiCiqbIJANAx3JHR7/YCfzbF+WnzA9wGnJ82PxVR72fO7y1+1k7D9G/Bp6RuWy1UxiLVADAePjY326vp3zaqf+vTv700Boykr+puR4n4KS2plesxQMqpFkURzc3Nrk8HmV4xHQBwdPhoRr/XK/zZEuenzQ9wG3B+2vxURL2fOb+3+JmzzIqKAZORavWc6VQi1eo0ci8qWfXvROnfXhoDRkpnSa1E/FQi1U6MARqWPSZZljE2Nub6J1fMqe4YyXyk2gv82RLnp80PcBtwftr8VES9nzm/t/iZsxyOhpX37BQq0zuZagd6LDKmvO9FJU3/NqmQ7bUxYKR0C5WZ8VNZUsuJMeDNqyxFybKMQCDg+oussbwRQOYj1V7hz5Y4P21+gNuA89PmpyLq/cz5vcWfKFJttqSWulCZ3vkSBEFJAVenkXtRyap/J1pSy0tjwEhm88WtKBG/Vx+w2JUTY4CGZY9JFEU0NTW5Ph0km+nfXuDPljg/bX6A24Dz0+anIur9zPm9xW/kVCvrVIvG1b8TpX8DUFLA1QXPvCijqLSVJbW8NgaMlE6k2oj/0/M+DQD49vJvZ6aBLpcTY8C7oy0FybKM0dFR1z+5ymb6txf4syXOT5sf4Dbg/LT5qYh6P3N+b/EbRqrVhcoE64XK2L4FYgEAlXPu0Ui1mo09KLCypJbXxoCRzB4YWJER/wMXPIC3r3wbX3nPVzLWRjfLiTFAzqkeGhpy/UWWzfRvL/BnS5yfNj/AbcD5afNTEfV+5vze4mfOsqb6d5L0b3Wk2sjJZOnfXl9SS83GmEyrf+vmVHtpDBgp3TnVev4CsQDz6ueRqv6d6zHgzassRYmiiMbGRteng7BI9eGBw/j19l/j0MChjHyvV/izJc5Pmx/gNuD8tPmpiHo/c35v8adbqCxR+vdEdEL5Hi/KKFJtuk61Lv3bS2PASGZsVpQP/OnKCRuQsrYsyxgeHnb9k6vGilikOiyF8fWnvo5LHrkkI9/rFf5sifPT5ge4DTg/bX4qot7PnN9b/JkuVAbER6rzIf2bpbRbWVLLa2PASOlGqr3On66csAE5pzoYDLp+kJUWlqLSX6m8/nfrvzPyvV7hz5Y4P21+gNuA89PmpyLq/cz5vcWfcE61ulCZZL9QmbrgmReliVQbpH8nWlLLS2PASGYReSvKB/505YQNCnJ2JhdIFEU0NDQ43QxLGgoNKdulhaUZ+U4v8WdDnJ82P8BtwPlp81MR9X7m/N7iTxapZg6xWaEyo8itsqTWse/x7JxqFZtR+rfptsfGgJHSKVSWD/zpygkbePMqS1GyLGNwcNBzT26C4aDytDEdeZU/U+L8tPkBbgPOT5ufiqj3M+f3Fr/VSLVZoTJLkeo8SP9mDwrM1m9WO55eGwNGSjf92+v86coJG5BzqkOhkCcG2R8/+UcsalikvD4ydCTt7/QSfzbE+WnzA9wGnJ82PxVR72fO7y1+FnVWO9WsErhp9W+TJbVM51TnQ/p3kki1Pv3bS2PASGYPD6woH/jTlRM2IOVUi6KIqVOneqIa3hcWfgE7v7YTc6fMBQC0Dram/Z1e4s+GOD9tfoDbgPPT5qci6v3M+b3Dr/7Br3aqmcwi1Ww7WfXvfFpSixUqs5r+7ZUxYKZ0ItX5wJ+unLBBSme644470NLSguLiYixduhTbt2+3dNwDDzwAQRDwiU98IpXTpi1ZljEwMOCpJzfNVc0AgLbBtrS/y4v8mRTnp80PcBtwftr8VES9nzm/d/jVjrKhU62KVKvXsU6a/s3mVEcm52Z7UbYKlenSv70yBsyUzpzqfOBPV07YwLZT/eCDD2LNmjVYu3YtduzYgUWLFmHlypXo7u5OeNyhQ4fwrW99C2eddVbKjU1XsiwjEol4apA1Vx5zqocy41R7jT+T4vy0+QFuA85Pm5+KqPcz5/cOf1KnWvQpDrFpoTIhvlAZi+rmY/q31SW1vDIGzJTunGqv86crJ2xg26m+/fbbcdlll2HVqlWYP38+7r77bpSWlmLDhg2mx0SjUXzhC1/A9ddfj+OPPz6tBqcjURRRV1fnqXQIxanOQKTai/yZFOenzQ9wG3B+2vxURL2fOb93+I0cZbX0c6qD4WBsX4uFytRVxL0oTfVvo0i1ybxjL40BM6WzpFY+8KcrJ2xg60wTExN49dVXsWLFiskvEEWsWLECW7duNT3uhhtuwNSpU/HlL3/Z0nlCoRCGhoY0/wBAkmI3H1mWlRuKJEmWt2VZRm9vr/I9Vo9l58z2ttH5Z1TOABCLVL/V9RY+/5fPY0/vHlvcbFuSJAQCAUSjUUeZsrWdrC3RaBSBQECxRT4w2dmWJAl9fX0KQz4w2e2naDSq2CBfmLJ1D/QKk93tvr6+jN0DudwpWZYRCATI9hHn9w6/2qk2knpO9T8O/ANlN5fh0T2PWi5UxqLf+TCn2s6SWl4aA2YyykCwqnzgT1dO2MBWL/X29iIajcat+9XQ0IDOzk7DY55//nncc889WL9+veXzrFu3DlVVVcq/5uZYtLa/v1/5P9sOBAIYHBxU2scc8J6eHoyMjAAAurq6EAzGnu719fVhfDyWDtPe3o5QKPYUr62tDeFwGADQ2tqKaDQKSZLQ2toKSZIQjUbR2horFhYOh9HWFosch0IhtLe3AwDGx8fR0dEBAAgGg+jq6gIAjIyMoKenBwAwNDSE3t5eAMDg4CACgUBCpipUxdo02IoP3PsB3P/W/fjCX7+gYero6PAUk5V+ygbT0aNH847Jbj8NDAzkHZOdfurr61Pani9Mdvupt7c375js9BPbzgQTFxcXVzpK6lSrItVMlz56afJCZcecauV7PJr+DcSntJsto2V33rHbZTZ3nMu9EmQbLvzRo0fR1NSEF198EcuWLVPe/853voPnnnsO27Zt0+w/PDyMhQsX4s4778SHP/xhAMCXvvQlDAwM4NFHHzU9TygUUn4gAbEfL83Nzejv70d1dbXy1EEQBEiSpMwpyda2KIpKRCWb20ZMu7p2YcHdC1BRVIHhiWEAsQst/MOwZ5nysZ84E2fiTLSYBgcHUV1djcHBQVRWVlr9M8qVQENDQ6iqquI25SKj4dAwKm8xH+s/WfETHFd9HD778GeV92qKa3DjOTdi9VOr8el5n8ZFCy7CBQ9dAAAIfCeAmpIaXPC/F+Avu/+iHPOr//wVvr7069kDyaIKbihAVI7iwyd8GE/tfwqLpy3Ga199DQBw7T+vxU3/vgkAsPuq3Tip7iQnm5pR3ff6fbjk0UsAAN9e/m3c+qFbHW4RXVn921Rg50vr6urg8/mUp/ZMXV1dmDZtWtz+7777Lg4dOoTzzjtPeY+lzhUUFGDv3r2YPXt23HF+vx9+vz/ufZYXLwhC3HtWtiUplv5cW1sLQRBsHct+mGVz2+j8J9adiCJfkeJQA0B9ab2ttrNtSYql/9bW1jrKlM3tRG2R5Vjqa21tbVr96iYmO9uyLCvjXxTFvGCy208AlGsgX5iyeQ/0ApOdbTV/JtvO5S6p+1l//VMQ5/cOv5VItT7KXCAWJE3/ZlFd/ftelCiIiMpRW+nfXhoDZkqnUFk+8KcrJ2xg6yxFRUVYsmQJNm/erLwnSRI2b96siVwznXTSSXjzzTexc+dO5d/HP/5xnHPOOdi5c6eS1p0rCYKAgoICT/0YKvIVYfG0xZr3akpqUvouL/JnUpyfNj/AbcD5afNTEfV+5vze4bczp1r9npLlY1KorMhXFHeMV6VPadekfKv6WP++V8aAmczYrB7rdf505YQNbEWqAWDNmjW49NJLcdppp+H000/HL37xC4yOjmLVqlUAgEsuuQRNTU1Yt24diouLsWDBAs3x1dXVABD3fi4kCIJyfi/pvdPfi+3tk2uBD44PpvQ9XuXPlDg/bX6A24Dz0+anIur9zPm9w5/KnOpEkWrmPMfNqfZo9W9g0rm0E6n20hgwUzqR6nzgT1dO2MB2PPzCCy/Ebbfdhuuuuw6LFy/Gzp07sWnTJqV4WWtrq1LgxW2SJAnd3d1KCrpXdHrT6ZrXPcGepDdiI3mVP1Pi/LT5AW4Dzk+bn4qo9zPn9w5/KpHqArFAU6hMM43GJP07HyLVjMmsgJfaDl4aA2YyY7OifOBPV07YwHakGgBWr16N1atXG362ZcuWhMdu3LgxlVNmRIIgwO/3ey4d4r3T36t5HZEiGBgfQG1Jra3v8Sp/psT5afMD3AacnzY/FVHvZ87vHX6jtanVMopU+4Tk6d8sqqt/34vSp39bjVR7ZQyYKd1Itdf505UTNvDuVZaCBEFAVVWV5wbZ3Lq5qCiq0LzXPdpt+3u8yp8pcX7a/AC3AeenzU9F1PuZ83uHP9VItdV1qpXv8XL697GIrTKn2iAyr96P7eOVMWCmdJbUygf+dOWEDUg51ZIkoaury3PpEKIg4htLv4HF0xZjSskUAJNONXtaaUVe5c+UOD9tfoDbgPPT5qci6v3M+b3Db6n6t2BQ/TtJpDof07+N5lRrinnp0r+9MgbMZPbwwIrygT9dOWEDUk61IAgoLS315JObmz5wE1776mvKGnwH+g9g1d9WoeG2BrzR9Yal7/AyfybE+WnzA9wGnJ82PxVR72fO7x3+lKt/qyLVmggti+r68idSnWr6t1fGgJnMHh5YUT7wpysnbJDSnGqvShAEVFRUJN/RxZpaNhUAsOpvq5T3/nnwn1jYsDDpsfnAn444P21+gNuA89PmpyLq/cz5vcOfcvXvY5FqURCVzwUIigOhX1LLy3Oq9dW/jR4ixG17aAyYyYzN0rF5wJ+unLCBd6+yFCRJEjo6OjydDsGcarU6hq1VW88H/nTE+WnzA9wGnJ82PxVR72fO7x3+lCLVgk85Tp3+rd6vQNDGzMqKyjLRXEdUWlgKAEptISuRai+NATOlU6gsH/jTlRM2IBeprqys9HQ6hJFT3TnaaenYfOBPR5yfNj/AbcD5afNTEfV+5vze4U/mVBf5iiwXKlPvp0//rvRXZqK5jujOj9yJvX17sbRpKQBgRuUM5TOzOdVeGgNmMmOzeqzX+dOVEzYg51SXlXn3aR2gdarPbD4TL7S9gM4R60611/nTEeenzQ9wG3B+2vxURL2fOb93+JM51TXFNXFFxvSFypjToHaq9enfXnaqz5t7Hs7DeZBlGa9c9grm1s1VPks0p9orY8BM6S6p5XX+dOWEDcilf7e3t+dNOsRX3vMVALDsVOcbv11xftr8ALcB56fNT0XU+5nze4c/mVNdW1KbuFCZWfq3rvq3l51qJkEQsGT6EpQXlSvvmS075aUxYKZ0ltTKB/505YQNSDnVgiCgtrbW0+kQK45fAQCYM2UO3tP4HgDW51TnA3864vy0+QFuA85Pm5+KqPcz5/cOfypOtVmhsnxN/04kfXEy9bZXxoCZ1GypRKq9zp+unLABufTvkpISp5uRlk6qOwm7r9qNaeXTMBGdAAD0BnsRjobjbqJ65QN/OuL8tPkBbgPOT5ufiqj3M+f3Dr8Vp7prtEvznqZQmcmcan36NyvylW9KlP7tlTFgpnSX1PI6f7pywgakItWSJKGtrc3z6RAn1Z2E6uJqTCmZAp8QSwPqCfYkPS5f+FMV56fND3AbcH7a/FREvZ85v3f4WcTZSEW+IpQWliYuVGaS/i2qft6XFJQkDbp4VZrotC792ytjwExqtlSqf3udP105YQNSTrUgCKivr8+bdAif6FMKl1mZV51v/HbF+WnzA9wGnJ82PxVR72fO7x3+RJHq2pJY6qpPSFCoTBAUZ1ITqS6YjFTna+o3kDhS7ZUxYKZ05lTnA3+6csIG5Jzq4uLivBpkjRWNAKzNq85Hfjvi/LT5AW4Dzk+bn4qo9zPn9wZ/MBxM6lQD8VFKQRCSRqrV6d/57FQnmlPthTGQSGZslo7NA/505YQNSDnVkiTh8OHDeZUOMa18GoBYpPqpd57C2mfXmt6k85Hfjjg/bX6A24Dz0+anIur9zPndz//Mu8+gcl0lfrXtV6b7mDnVkiwlLVSmjm7ns1NtFqn2whhIpnSW1MoH/nTlhA3IFSprbGzMqyc308piTvWT+5/EX3f/FQDwweM/iPfPen/cvvnIb0ecnzY/wG3A+WnzUxH1fub87ud/9eiriMpRbD+63XQfM6c6KkVtFSqj4lTrI7tuHwPJlG76t9f505UTNiDnVBcVFSXf0UNikWrmUAPA4YHDwKz4ffOR3444P21+gNuA89PmpyLq/cz53c8flaMAgHA0bLqPqVMtRzXp33Pr5mLxtMU4a+ZZyj5UnGp9yrd62+1jIJnSKVSWD/zpygkbkEv/PnToUF6lQ8ysmhn33tHho4b75iO/HXF+2vwAtwHnp81PRdT7mfO7n59FmiNSxHSf2uKYU+0TtYXK1OnfgiCguKAYr331Nfzqw5Op5Or07wp/fi6nBSRO/3b7GEimdJbUygf+dOWEDchFqmfMmJFX6RBfXPhFHBw4iJbqFrze+TrufvVutA+3G+6bj/x2xPlp8wPcBpyfNj8VUe9nzu9+/qh0LFItpRCplrSRaiNpItVFeRypVqd869K/3T4GkknNk0qk2uv86coJG5BzqkVRzKtBVlZUhltW3AIA+PX2XwMwj1TnI78dcX7a/AC3AeenzU9F1PuZ87ufn6V/J4xUJ0r/VhUqMxJfUsv9YyCZ0p1T7XX+dOWEDcilf7e2tuZtOsT0iukAEqd/5zN/MnF+2vwAtwHnp81PRdT7mfO7n1+JVKcyp1pXqMxIPtCr/q22hRfGQDKlM6c6H/jTlRM2IOVUi6KImTNnQhTzE5s51Sz9u3+sH92j3crn+c6fTJyfNj/AbcD5afNTEfV+5vzu57cSqa4pqQFgsqRWsvRvIpFqM8fTC2MgmdKZU50P/OnKCRuQsrYsy5CkyQIP+aamiiYAQMdwB94NvIvjfnkc5v56LkYnRgHkP38ycX7a/AC3AeenzU9F1PuZ87ufn0WqEznVVf4qANqiY4A2/dvM2SoQJ2d35rNTbZYi7YUxkEzprFOdD/zpygkbkHOqjxw5kreDjC2vFZbCOOOeMzAYGsTA+ACODB0BkP/8ycT5afMD3AacnzY/FVHvZ87vfn4rkWrmDKdSqKxAoOFU64uTMXlhDCSTWRE2K8oH/nTlhA1IFSoTRREtLS1ONyNrKvQVYmrZVHSPdqM32Ku83zfWByD/+ZOJ89PmB7gNOD9tfiqi3s+c3/38Vqp/15XWATBJ/05SqMxf6Fe289mpZvx6p9MLYyCZ0k3/9jp/unLCBuQi1RMTE3n95IalgKsVGAsAoMGfSJyfNj/AbcD5afNTEfV+5vzu50+0TvWXT/0yfvmfv0R9WT0A4+rfyQqVkYlUH+PX28ELYyCZ0ilUlg/86coJG5Bzqjs6OvJ6kE1EJ5Ttk+tPBqB1qvOdP5E4P21+gNuA89PmpyLq/cz53c/P0r+N9I2l38A3ln5DeZ1S+rdqTnV5UXk6TXW1mG30NvLCGEimdJbUygf+dOWEDUg51aIoYtasWXldDU/9ZGthw0IAQF9wMv073/kTifPT5ge4DTg/bX4qot7PnN/9/Cz920h6BzFR+rdZpLrQV6hslxSWpNpM1ytR+rfbx0AyqZnsRqrzgT9dOWEDUtaWZRnj4+N5/eTmro/ehUUNi/D8queVNQ7Vkep8508kzk+bH+A24Py0+e3qjjvuQEtLC4qLi7F06VJs377d0nEPPPAABEHAJz7xiew20ETU+5nzu58/UaRa70D5RIPq30ki1SUFk450fWl9qs10vRi/UaTa7WMgmdKZU50P/OnKCRuQc6p7enryepC9b+b7sPNrO3HmzDMxpWQKgMlCZRT4E4nz0+YHuA04P21+O3rwwQexZs0arF27Fjt27MCiRYuwcuVKdHd3Jzzu0KFD+Na3voWzzjorRy2NF/V+5vzu50/kVMdFXY3Sv5MUKivyFeHvn/w73vjqGzQi1QZzqt0+BpIp3TnVXudPV07YgJRTLYoimpubyaRD6CPV1Pj14vy0+QFuA85Pm9+Obr/9dlx22WVYtWoV5s+fj7vvvhulpaXYsGGD6THRaBRf+MIXcP311+P444/PYWu1ot7PnN/9/Omkf1spVCaKIlYsXIFTpp2SZkvdLaVQmUH6t9vHQDKlM6c6H/jTlRM2IGVtWZYxNjZG5snNlNL4SDUlfr04P21+gNuA89Pmt6qJiQm8+uqrWLFihfKeKIpYsWIFtm7danrcDTfcgKlTp+LLX/6ypfOEQiEMDQ1p/gGAJMUcBlmWlb6SJMnytizLGB0dVb7H6rHsnNneToXJzrYkSRgbG0M0Gs0bJjv9FI1GMTY2ptjCjUyJ1qcWBVHTFr1DJcmSkv6t/M+iDdzUT5nYVqd/q88fjUYRDAaVfb3ExPpJ7VTr+TJ5D8wlUyb6wGo/SZKE0dFR5dyZ4Egmck51IBBQDJ/vMppTTYlfL85Pmx/gNuD8tPmtqre3F9FoFA0NDZr3Gxoa0NnZaXjM888/j3vuuQfr16+3fJ5169ahqqpK+dfc3AwA6O/vV/7PtgOBAAYHB5X2MQe8p6cHIyMjAICuri7lh/SBAwcwNjYGAGhvb0coFAIAtLW1IRyOrQ3c2tqKaDQKSZLQ2toKSZIQjUbR2toKAAiHw2hrawMQewDQ3t4OABgfH0dHRwcAIBgMoqurCwAwMjKCnp4eAMDQ0BB6e3sBAIODgwgEAmkxAUBHRwfGx8eTMkUiEfT29uYVk91+CgQCGBsbcy3T8MgwzCQKooYpPKFdy3oiPLlM0OjoqClTIBDAwMCAq/sJSG/sjY7E+AVBiGNqb2+HLMueY2L9pH6YIgiCrX6SZRnvvvuu65iA3I29SCSCd955B7Isp83U19cHKxJkD/y6GBoaQlVVFQYHB1FZmb/r7WVa245swxn3nIFZVbNw6L8POd0cLi4urrxRPv9dOnr0KJqamvDiiy9i2bJlyvvf+c538Nxzz2Hbtm2a/YeHh7Fw4ULceeed+PCHPwwA+NKXvoSBgQE8+uijpucJhULKDyQgZtPm5mb09/ejurpacRwEQYAkSRAEIavboihORomyuM2ZONOnH/w0/rrnr4bXxYFvHEBLdYvSFkmS4LtxsljZ9IrpOG/OefjNq7/B2vevxY/O+ZErmJzop03vbMKH//xhVPor0f+d/rxgYv10aOAQjv9VbBrNw595GJ886ZOeZ/Lq2BscHERNTU3Sv/cFpp/koWRZRjAYRGlpKQTB3vwEL8ooUk2JXy/OT5sf4Dbg/LT5raqurg4+n095as/U1dWFadOmxe3/7rvv4tChQzjvvPOU91i6XEFBAfbu3YvZs2fHHef3++H3++PeZ3Pg1H2knheXbFuWY2n+paWlto9lP8yyuZ0Kk51tWY6lfjL+fGDSbydrC+NPp1+zySHBPJ1UFERNW/RzQtVLarHK4FZtkE0mJ64nhR+C5vyCICj3eq8xGY5BQczqPTAf7xEAFBuk23b9NWgmcunfQ0NDys0o38Wc6uGJYdyx/Q60DbaR4teLWv/rRZ0f4Dbg/LT5raqoqAhLlizB5s2blfckScLmzZs1kWumk046CW+++SZ27typ/Pv4xz+Oc845Bzt37lTSunMl6v3M+d3Pn6hQWbIHflEpeaEyL9ggE1IePBgsqeV1/nSX1PI6f7pywgakItWiKKKxsdHpZuRM1cXVECBAhozVT63GA7sewL9X/RvjkXG8cOgFnDXrLBT5ipxuZs5Erf/1os4PcBtwftr8drRmzRpceumlOO2003D66afjF7/4BUZHR7Fq1SoAwCWXXIKmpiasW7cOxcXFWLBggeb46upqAIh7Pxei3s+c3/38dtapNjo22TrVXrBBJmS2pFY+8Kv71u6SWvnAn66csAG5SPXw8DCZJzc+0Yfq4mrl9fOtz2N4eBhXPnElVty3Aj95/ifONc4BUet/vajzA9wGnJ82vx1deOGFuO2223Dddddh8eLF2LlzJzZt2qQUL2ttbVUKvLhN1PuZ87uf386SWnqp078TRardboNMiDme+ocL+cCfzpJa+cCfrpywATmnmlUGpaKwpK0a2TfUh9/v/D0A4Ccv0HOqqfW/WtT5AW4Dzk+b365Wr16Nw4cPIxQKYdu2bVi6dKny2ZYtW7Bx40bTYzdu3JiwSFk2Rb2fOb/7+dOKVEvJI9VesEEmxGxllP7tdX79klp2lA/86coJG5BL/9YvEZLvGpkY0bxuj7Yr2yfVnZTr5jgqiv2vFnV+gNuA89PmpyLq/cz53c+fTqRanf5ttq8XbJAJJUr/9jq/msnunOp84E9XTtiAXKR6cHCQ1JOb6RXTNa/v3n63sl3hr8h1cxwVxf5Xizo/wG3A+WnzUxH1fub87udPFKlOluoryZKlQmVut0EmlKhQmdf5041Ue50/XTlhA3JOdSgUIjXInvz8k/j++76PLy3+EgDgj7v+qHzWF7S2mHm+iGL/q0WdH+A24Py0+amIej9zfvfzM6fYSJbSv+Xk6d9ut0EmpESqDeZUe51fzZTKnGqv86crJ2xAyqkWRRFTp061vN5YPmjRtEX48Qd/jNOnnx73WW+w14EWOSeK/a8WdX6A24Dz0+anIur9zPndz5+p9G+zSLUXbJAJMWdTb7N84E9nSa184E9XTtiAlLVlWcbAwADJJzcnTz057r3eYC8pW1Duf4DzA9wGnJ82PxVR72fO737+dAqVaap/J4hUu90GmZDZnOp84FczpZL+7XX+dOWEDcg51ZFIhOQge0/je9BY3ohZlbPwj4v/ASBWGfzqTVfj8scvJ2ETyv0PcH6A24Dz0+anIur9zPndz59OpBqYdMoTzal2uw0yIcZvlP7tdf50l9TyOn+6csIG5Kp/19XVOd0MR1ReVI4DVx9AoVgIn+hDaWEpguEg/mf7/wAArl56tWE0O59Euf8Bzg9wG3B+2vxURL2fOb/7+dOJVANARIok3NcLNsiE5kyZg/rSeixvXq55Px/40ylUlg/86coJG5CLVAcCAbJPbvw+PwYHYpXw6kq1A+21ztccalXuRL3/qfMD3AacnzY/FVHvZ87vfv5EkWqj6PMJtSdoXjOnOlH6t9ttkAlVF1fjyJojuP/T92vezwd+TaEym3Oq84E/XTlhA1JONdekppRM0bx+rSPmVN/58p34256/OdEkLi4uLi4uLq68l91I9a4rd+Hwfx9WXitOtU1nKx9V5CvKSzukE6nmckak0r8FQUBtba3TzXBMan6jSPXe3r246smrAACRayPwib6ctzGb4v1Pmx/gNuD8tPmpiHo/c37389udU13kK0J9aX3c8WaRai/YIJvKB371gwK7c6rzgT9dOWEDUo8+JElCb28vJMl8fcB8lprfyKnuCfYorw8NHMpx67Iv3v+0+QFuA85Pm5+KqPcz53c/fyrrVKvfTxap9oINsql84E8nUp0P/OnKCRuQcqoFQUBBQUFepolYkZpf71QPjA8oKeAAsLt3d66bl3Xx/qfND3AbcH7a/FREvZ85v/v5UylUps4eTFaozAs2yKbygT+dOdX5wJ+unLABOae6urqa7CBT86vnVBf5igAAfz/wd+W93T2TTvUlj1yClX9cmTBdyQvi/U+bH+A24Py0+amIej9zfvfzJyxUZpLq6xPinepE6d9ut0E2lQ/86SyplQ/86coJG5ByqiVJQnd3N9l0CDV/WVGZ8v5ZM88CALxy9BXlPRapnohO4L437sMz7z6DVztezW2DMyze/7T5AW4Dzk+bn4qo9zPndz9/oki1mROgft9K+rfbbZBN5QN/uunfXudPV07YgJRTLQgC/H4/2Sc3av7h0LDy/sKGhQCAjpEO5b23e94GAPQF+5T3uka6ctTS7Ij3P21+gNuA89PmpyLq/cz53c9vFqlO5jyxaLWVSLXbbZBN5QO/plBZCunfXudPV07YgJxTXVVVRXaQqfnPbjkbAFBSUIKmiqa4fXf37oYsy+gbm3SqDw4czFVTsyLe/7T5AW4Dzk+bn4qo9zPndz+/WaQ6qVMt6pzqBFFtt9sgm8oHfvUDE7uR6nzgT1dO2ICUUy1JErq6usimQ6j5zznuHPz94r9j39f3obGiMW7fodAQOkY6NJHqd/rewWN7H0P/WH8um50x8f6nzQ9wG3B+2vxURL2fOb/7+TMVqTbb3ws2yKbygT+dJbXygT9dOWGDlJzqO+64Ay0tLSguLsbSpUuxfft2033/+te/4rTTTkN1dTXKysqwePFi3HfffSk3OB0JgoDS0lKyT270/CuOX4EZlTMwvWK64f7v9L2jiVT/+uVf4/wHzsdlj1+Wk/ZmWrz/afMD3AacnzY/FVHvZ87vfv5UI9Xscyvp3263QTaVL/ysv1OJVOcDfzpywga2neoHH3wQa9aswdq1a7Fjxw4sWrQIK1euRHd3t+H+tbW1+MEPfoCtW7fijTfewKpVq7Bq1So8/fTTaTfergRBQEVFBdlBZsavd6rZTbpzpFMTqWb6y+6/ZK+RWRTvf9r8ALcB56fNT0XU+5nzO88fjoax4g8r8P3N3zf83Gyd6mQRSTvp307bwEnlCz8bD6nMqc4H/nTkhA1sO9W33347LrvsMqxatQrz58/H3XffjdLSUmzYsMFw/7PPPhuf/OQnMW/ePMyePRtXX301Fi5ciOeffz7txtuVJEno6Oggmw5hxt9Yrk3/XjB1AQCga7RLE6n2unj/0+YHuA04P21+KqLez5zfef7H9j6GzQc3Y93z6ww/Tzf9m0W6zZxwN9jASeULf6qR6nzhT0dO2MBWL01MTODVV1/FihUrJr9AFLFixQps3bo16fGyLGPz5s3Yu3cv3v/+95vuFwqFMDQ0pPkHQDGMLMuQZVl5z+q2IAgoLy9XzmP1WHbObG+nwmSXo7KyUnmPnb/CX4Hyokm7MKe6c6QTvcHeuP5pKGtwDZOdfpJlGZWVlZb39wKTnW0AKC8vhyAIecNkt59kWVaeXOYLU7bugV5hsrPNnlzr74GpbnO5U4IgoLKykmyUhvM7zz8WGUv4ecbSvxNEqp22gZPKF37W36msU50P/OnICRvYcqp7e3sRjUbR0NCgeb+hoQGdnZ2mxw0ODqK8vBxFRUX46Ec/iv/5n//Bhz70IdP9161bh6qqKuVfc3MzAKC/v1/5P9sOBAIYHBxU2scc8J6eHoyMjAAAurq6EAwGIQgChoaGEAqFAADt7e3KdltbG8LhMACgtbUV0WgUkiShtbUVkiQhGo2itbUVABAOh9HW1gYg9gCgvb0dADA+Po6OjtiyVMFgEF1dsSWoRkZG0NPTAwAYGhpCb2+vYpdAIJAWEwB0dHRgfHw8KZMkSSgpKUFbW1scE4tWCxBwYvWJAGJOdfdIfFr/wPgAhoeHXcFkp5+OHj2KsrIyhEIhV/dTtsbe8PAwxsbGIAhC3jDZ7ae+vj5Eo1EIgpA3THb6SRAEDAwMYGJiIm+Y7PSTLMsoLi5WONJl4nKnBEFAWVkZ2R+UnN95frP0bqaUI9WitUJlbrCBk8oXftb+VNK/84E/HTlhA0G28bj96NGjaGpqwosvvohly5Yp73/nO9/Bc889h23bthkeJ0kSDhw4gJGREWzevBk33ngjHn30UZx99tmG+4dCIeUHEhD78dLc3Iz+/n5UV1crEQIWbRIEwdK2LMtob2/H9OnT4fP5LB8riqISUcnmdipMdrZlWUZnZycaGhpQUFCgOf85G8/BlsNbUFtSi1tX3IqvPP4VfOTEj6BAKMBj+x6L66Oh7w2hrLDMcSY7/RSNRtHV1YVp06ZBEATX9lO2xl40GkVHRwemT5+uXA9eZ7LbT5FIBJ2dnZg+fbryudeZsnUP9AqTnW32d2zatGlx90C724ODg6iursbg4KCSAcOVnoaGhlBVVZW2TSUplvbX2NgIUSS1yAkAd/EHw0EU+YpQIBbk7Jxu4N+4cyNW/W0VAEBeG/8z23eDz9DxrimuQeC7AdPvnf6z6egY6cDUsqnoHu3GHz7xB1y86OK4/dxgAyeVL/xlN5chGA5i15W7ML9+vuXj8oU/HWXSBlb/Ntm6y9XV1cHn8ylP7ZmYo2ImURRxwgknAAAWL16M3bt3Y926dTjbxKn2+/3w+/2G3wNon9ioDWVlu66uTnlt51j2wyyb26kyWd2WZRm1tbXw+Xxx52fLatWX1mNaeawvO0c6UVJQAgC49v3XAgBu/NeNAIC+sT5U+CscZ9JvJ2qLz+dDbW0tRFF0dT/ZYbKzLYoipkyZoryXD0x2+8nn82lskA9M2bwHeoXJzj1wypQphvfAdNrO5S4JgoDa2lqyfeQW/pGJERz3y+OwYOoCPHvpszk7rxv4E0WqZVk2/TyT6d9O28BJ5Qu/UqgshfTvfOBPR07YwJbrXlRUhCVLlmDz5s3Ke5IkYfPmzZrIdTJJkqSJROdKgiCgpKSE7CBLxM8qgNeV1mmcalao7OyWs3HDOTdgRuUMAMDT+5/G73b8zlPzCnn/0+YHuA04P21+KqLez27hbx1sRW+wFzs6duT0vG7gT+RUJ/osWZvjqn8nWFLLaRs4qXzhT2dJrXzgT0dO2MB2PHzNmjVYv3497r33XuzevRtXXHEFRkdHsWpVLM3lkksuwTXXXKPsv27dOvz973/HgQMHsHv3bvzsZz/Dfffdhy9+8YuZo7AoSZKU+cQUlYi/qaIJAFBfNhmp7hrpUgqVTSmZAiDmdAPA1574Gi57/DK8fPTlXDQ9I+L9T5sf4Dbg/LT5qYh6P7uFnzmPZvOHs3ZeF/AncpzNipQBNqp/H7OpmcPgBhs4qXzhrympgQABVcVVto7LF/505IQNbE9yufDCC9HT04PrrrsOnZ2dWLx4MTZt2qQUL2ttbdWk3I2OjuLKK6/EkSNHUFJSgpNOOgl//OMfceGFF2aOwqIEQUB9fT3ZJzeJ+C9ccCFeaHsBq09fjallUwHEbvyKU12qdaqZ9vXtw+lNp2e55ZkR73/a/AC3AeenzU9F1PvZLfzM8UtWtCvTcgO/OotPlmVNWxLZw2qhsrAUTri/G2zgpPKF/6HPPISO4Q4l2GVV+cKfjpywQUqVI1avXo3Vq1cbfrZlyxbN65tuugk33XRTKqfJuARBQHFxsdPNcEyJ+KdXTMfDn31YeT2lZIpmjWp9pJqpdbA1Cy3Njnj/0+YHuA04P21+KqLez27hZxHZRJHZbMgN/GrHWZIlJcIMJI7c255TnSD922kbOKl84U81aJUv/OnICRuQKgknSRIOHz5MNh3CDr/6qVhJQQlKCmMFy+pKvOtU8/6nzQ9wG3B+2vxURL2f3cLPnEcn0r+d5lc71fqHCplI/2bfnyj922kbOCnOT5sfcMYGpJxqQRDQ2NhINh3CDn9D+eRa5Cz1G/B+pJr3P11+gNuA89PmpyLq/ewWficj1U7za5xq3UOFdCLVLP2bKVGk2mkbOCnOT5sfcMYGuVs40AUSBAFFRUVON8Mx2eFn86oBYFbVLGXb604173+6/AC3AeenzU9F1PvZLfz6FGi7FYxTlRv4U41UJ1s6SW/DREtqOW0DJ8X5afMDztiAVKRakiQcOnSIbDqEHf55dfMAANXF1fjlf/5SeV/vVB8ePGxpWa1QJOT48lu8/2nzA9wGnJ82PxVR72e38KsjsrlMAXcDf9Yi1YI2Um22vxts4KQ4P21+wBkbkHKqBUHAjBkzyKZD2OH/1vJv4bHPPYa2b7ZhyfQlyvt6p3pkYgQD4wMJv6t1sBX1P63HVU9elVK7MyXe/7T5AW4Dzk+bn4qo97Nb+NUR2VymgLuBP2tzqm2kfzttAyfF+WnzA87YgJxTLYoi2UFmh7+0sBTnzT0P5UXlmvf9BX5lu6ywDEDyFPDXOl7D8MQwnjv8XAqtzpx4/9PmB7gNOD9tfiqi3s9u4VdHZHO5rJYb+DUPFDIYqbaT/u20DZwU56fNDzhjA1JOtSRJaG1tJZsOkQn+U6edivrSerx3+nsxt24ugHinWv8HY3hiOPb/0HDK582EeP/T5ge4DTg/bX4qot7PbuFP5FhmU27gZ0teAfGR6bTWqRasRardYAMnxflp8wPO2ICUUy2KImbOnAlRJIWtKBP8ZUVlOHj1Qbz45Rcxs2omgJhTPRwaxuD4IF468hJKby7Fzf++WTmGOdNDoaH0ANIU73/a/AC3AeenzU9F1PvZLfyJUqCzKTfwh6NhZTsuUp3J9G+TKJwbbOCkOD9tfsAZG5CytizLkCTJ8YJZTilT/GVFZSgQC9BS1QIA2B/YjzPuOQMn33kybt96OyaiE/jBP3+AI0NHAMTmXQOxiLUsyzgydASfeegzeO5QbtPBef/T5ge4DTg/bX4qot7PbuF3qlCZG/jDksqp1s+pTmCLZKmqVguVucEGTorz0+YHnLEBOaf6yJEjZAdZpvkXTF0AAHhkzyN4u+dttA+3a+ZN3/SvmwBMpn9LsoRgOIiH334YD7/9MH6x7RcZaYdV8f6nzQ9wG3B+2vxURL2f3cLvVKEyN/BnK1IdN6faJP3bDTZwUpyfNj/gjA1IOdWiKKKlpYVsOkSm+RdPWwwgtqwWU/dot7L95zf/DEA7l3p4YhiBsQAAoH2oPSPtsCre/7T5AW4Dzk+bn4qo97Nb+J2KVLuBP9VIdSbTv522gZPi/LT5AWdsQMrasixjYmKC7JObTPOfPPXkuFQktYYnhhEMB5X0byDmYPeP9QMAOkY6MtIOq+L9T5sf4Dbg/LT5qYh6P7uF38lItdP86ki1umgZkOacaouFytxgAyfF+WnzA87YgJxT3dHRQXaQZZq/uKAYJ9WdFPd+dXE1CsVCAEBfsE9J/wZixcr6x2NOdedIZ06X2eD9T5sf4Dbg/LT5qYh6P7uFX/33ndrfek2k2oEltdxgAyfF+WnzA87YgJRTLYoiZs2aRTYdIhv8i6Ytinvv5PqTUVdaBwDoCfZonOrhiWEMjA8AiD297Qv2ZawtycT7nzY/wG3A+WnzUxH1fnYLv5Pp307za+ZU69O/E0SqzSLPTPr0bzMn3A02cFKcnzY/4IwNSFlblmWMj4+TfXKTDf7FDYsBAIViIUoLSwEA8+vnK051b7BXk/6tjlQDwNHhoxlrSzLx/qfND3AbcH7a/FREvZ/dwu9k+rfT/Iki1blYp9oNNnBSnJ82P+CMDcg51T09PWQHWTb43zfzfQCA5c3LccrUUwAA8+rmob6sHgDQM9qjLVQWmoxUA7mdV837nzY/wG3A+WnzUxH1fnYLv5NLajnN73ShMjfYwElxftr8gDM2KMjZmVwgURTR3NzsdDMcUzb4lzUvw7OXPosTa0/Ezs6duPvVu3HxoovxUvtLAGKR6rg51WOTkeqO4dw51dc/dz02vbsJ/7zknygrKsvZed0i6uMf4Dbg/LT5qYh6P7uF36lItRv4nV5Syw02cFKcnzY/4IwNyEWqx8bGyD65yRb/2S1no6myCR+d81E8ftHjqCutQ12Jcfq3ek41kNtI9e93/h7b27djR8eOnJ3TTaI+/gFuA85Pm5+KqPezW/jVac65jlQ7zZ+1SLU+/TtBpNppGzgpzk+bH3DGBuSc6kAgQHaQ5ZJfSf8OatO/e4O9GIuMKa+PDh/NWX+wiPnoxGhOzuc2UR//ALcB56fNT0XU+9kt/Jr07xzPqXaaP1uRaquFytxgAyfF+WnzA87YgJRTLYoimpqayFbDyyU/K1TWPdqN0fCkE9s62KrZ746X78CsX8zKehVwWZYV5z4UDWX1XG4V9fEPcBtwftr8VES9n93Cr3Yec7mklhv4U41Um0WemeykfzttAyfF+WnzA87YgJS1ZVnG6Ogo2Sc3ueSvL41Fqg8NHNK8r3eqAaBtqA3/OvyvrLYnFA0pf9jUTj4lUR//ALcB56fNT0XU+9kt/E4WKnOaP2uRahvp307bwElxftr8gDM2IOdUDw0NkR1kueRnkeqDAwc17xs51Ub7ZVrqFHTK6d+Uxz/AbcD5afNTEfV+dgu/k0tqOc2fsznVCZbUctoGTorz0+YHnLEBuerfjY2NTjfDMeWSnznV6qJkANA+3A4AmFk1Ez2jPcr86ncD72a1PeoK5OOR8ayey62iPv4BbgPOT5ufiqj3s1v4nSpU5gb+RJHqdNapjkv/NolUu8EGTorz0+YHnLEBuUj18PAw2Sc3ueRnhcrMdELtCXjtq6/hhrNvAAC82/8u3ul7B0OhoYyc/9+H/41n3n1Gea2uQE45/Zvy+Ae4DTg/bX4qot7PbuF3slCZ0/wJI9U5KlTmtA2cFOenzQ84YwNyTnUwGCQ7yHLJP6VkSsLPa4prMLduLs6adRYA4Ol3n8a8O+bhkkcuSfvcE9EJvH/j+7HyjysRGAsA0KZ/ByeCaZ/Di6I+/gFuA85Pm5+KqPezW/g16d85nlPtNH/COdU5Sv922gZOivPT5gecsQEpp1oURTQ0NJCthpdLfn+BH5X+SuW1/g9BdXE1AGB2zWzlvagcxT8O/CPtKqHqVPKe0R4A2kj1eJRu+jfl8Q9wG3B+2vxURL2f3cLvVKTaDfzqSHVEimg+S2QLMyeZSR+pTpT+7bQNnBTnp80POGMDUtaWZRmDg4Nkn9zkmn9q2VRle1r5NM1nNcU1AICmyib4fX7l/dHwKA70H0jrvPv69inb/eP9ALRzqimnf1Me/wC3AeenzU9F1PvZLfxORqqd5tdEqjNYqMzqklpusIGT4vy0+QFnbEDOqQ6FQmQHWa75lzcvV7anV0zXfMYi1aIgoqSwRPPZ652vp3XevX17lW22/jVP/+bjH+A24Py0+amIej+7hV+ddZbLdardwK+ZU+3QklpO28BJcX7a/IAzNiDlVIuiiKlTp5JNh8g1/0dP/KiyrXeqa0pqlG19hfCdnTvTOq86Us3mVKvTv1nFcWqiPv4BbgPOT5ufiqj3s1v4nUz/dpo/W5HquPRvk0i1G2zgpDg/bX7AGRuQsrYsyxgYGCD75CbX/OfOPlfZHg2PospfpbxePG2xsv2N078BYPIJ7Otd6UWqjZxqdfp3MEw3Uk15/APcBpyfNj8VUe9nt/A7mf7tNL9RpJq1J51Itf7zRNW/nbaBk+L8tPkBZ2xAap1qWZYRiUQgy7Jpykw+K9f8LMUbiKVhP/H5J7CrZxdWHL8Cx9ccr3x28wdvxgeO+wAq/BX44B8+mNCplmUZX/2/r2I0PIr7Pnmf4R8Udfq3YfVvwk415fEPcBtwftr8VES9n93C7+SSWk7z6yPVrYOtOON3Z+DK914ZV2NGrWTttZP+7bQNnBTnp80POGMDUpFqURRRV1dHNh3CCf6/fPYvmFo2FT9Z8ROcOfNMXL7kco1DDQBlRWU4/6Tz8Z7G9wAAWgdb41LCmV4++jLW71iPP7/5Z+wP7Nd8NhYew8D4ALpHu5X3+sZic6p5+jcf/wC3AeenzU9F1PvZLfxORardwK+PVP/wnz9Ex0gHrn322pylfzttAyfF+WnzA87YgJS1ZVlGIBAgmw7hBP+n5n0KXd/qwodmfyjpvtXF1coTXPWyWGpt3LlR2Vanea/62yqU3lyK2p/Uavbn6d+Toj7+AW4Dzk+bn4qo97Nb+NXFyXIdqXaaXx+pVj/Mz2T6d6JItdM2cFKcnzY/4IwNSDnVXO4Xi2If6D+AcDSM9214Hy743wswHhnHeGQc9791v7Lv3t5YmndEiuCBtx4AAMjQXjzcqebi4uLi4sq9NOnfOYxUOy1ZluOi9BonO51ItT79O8m61lxcXLkTqTnVgiCgtrY2+Y55Ki/wH1d9HF5sexEH+g9gT+8evND2AgBAfETEl0/9siYtnEWq9/buxXhkHOVF5dhz1R6MR8bxVvdb+MSDnzCs/k3VqfZC/2db1G3A+WnzUxH1fnYLv9qxzOWSWk7zq1O/gZgdIlJE89pMdtO/zfZ32gZOi/PT5gecsQGpSLUkSejt7YUk5e7m7iZ5gV8dqe4N9irvP/T2Q/jL7r9o9mUFydgSXIsaFqGpsgmza2ejvqwewOScaqNCZfqnx/kuL/R/tkXdBpyfNj8VUe9nt/A7VajMaX7974qoFE24brVamVqn2mkbOC3OT5sfcMYGpJxqQRBQUFBAthKeF/gVp3rggOIQMz13+DkAUAqasUj1a52vAQBOnXaqsm9tSezplOE61eExyLKM0393Ok6+82QyjrUX+j/bom4Dzk+bn4qo97Nb+J0qVJYL/v2B/egL9hl+ZhSpTrRutVrJ0rnj5lSb7O+WMeCUOD9tfsAZG5Bzqqurq8kOMi/wm0WqgUkn+pyWcwAAHSMdGAoNKZFq9drXzKkeGB9AVIpq5lSPRcZwdPgodnTswDuBd9A21JYtHFfJC/2fbVG3AeenzU9F1PvZLfxOFSrLNv/R4aM48X9ORN1P6ww/z2qkWl/924TRLWPAKXF+2vyAMzYg5VRLkoTu7m6y6RBe4GdO9eGBw5qlsdQ6uf5kTC2bCiDmaCuR6sbJSHVNcY2y3T/er0n/BoC3ut9StjtHOjPTeJfLC/2fbVG3AeenzU9F1PvZLfxORaqzza/+/TARnYj7PFmk2mh+OXOmM1WozC1jwClxftr8gDM2IOVUC4IAv99P9smNF/inV0xHka8IUTmqRKCr/FWafZoqmzB3ylwAwO9f+z0CYwEUiAWYXz9f2afQV4hKfyWAWAq4Ov0bAF7vel3ZpuJUe6H/sy3qNuD8tPmpiHo/u4XfqTnV2eavLq5Wto8MHYn7XB+pjkiRpIXKCsRY3WC7S2olKlTmhjHglDg/bX7AGRuQc6qrqqrIDjIv8IuCiOOqjwMAbG/fDmByDjXTjMoZ+NS8TwEA7nzlTgDAR078CIoLijX7sRTwrpEuhKIhzWdvdL2hbHcMd2SQwL3yQv9nW9RtwPlp81MR9X52C7+Tc6qzya9mOTRwSNne0bEDrxx9JT5SbSH926pTbSf92w1jwClxftr8gDM2IOVUS5KErq4usukQXuFnKeDtw+0AtAXIAKCpogmXL7lcSQEHgB/9x4/ivoc51YcHDyvvlRWWAQDe7H5TeY9KpNor/Z9NUbcB56fNT0XU+9kt/GrnMZdLamWbX+0gHx6I/b6ISBGcc+85OHvj2XGZcVYKlVl2qm2kf7thDDglzk+bH3DGBqScakEQUFpaSvbJjVf4mVPNpJ4rXVZYhkp/JUoLS/G9M78HAPjM/M9o9mGaUjIFQKxKJwD4fX4llVwTqR6hE6n2Qv9nU9RtwPlp81MR9X52C7+Thcqyya92kFmkOhgOYig0hNHwqLLqCJM+Uh2KaDPngEmn2nb17wSRajeMAafE+WnzA87YgJxTXVFRQXaQeYVf71QfV32cModpRuUMpf3/fcZ/45+X/BMbP7HR8Hvm1c0DADzxzhMAgIbyBpQVlcXtRyVS7ZX+z6ao24Dz0+a3qzvuuAMtLS0oLi7G0qVLsX37dtN9169fj7POOgs1NTWoqanBihUrEu6fTVHvZ7fwO5n+nU1+TaT6WCbceGRceS9ZpFq9L1PK6d8JltRywxhwSpyfNj/gjA1IOdWSJKGjo4NsOoRX+PVO9ZTSKZhZNRNArEgZkyAIOOe4c1BaWGr4PWyJrVeOvgIAeM+096BQKIzbzymnWpZljIXHcnY+r/R/NkXdBpyfNr8dPfjgg1izZg3Wrl2LHTt2YNGiRVi5ciW6u41XZdiyZQsuuugiPPvss9i6dSuam5tx7rnnor29Pcct5/3sFn6nCpVlm1/tIDOnWh191q82oo9Uj0Xi/+6nmv5ttr9bxoBT4vy0+QFnbEDKqRYEAZWVlWSf3HiFX+9U15XWKU71jMoZlr9HvW41AJw2/TSUF5XH7edU+vcFD12A6bdPR1+wLyfn80r/Z1PUbcD5afPb0e23347LLrsMq1atwvz583H33XejtLQUGzZsMNz/T3/6E6688kosXrwYJ510En73u99BkiRs3rw5xy3n/ewWficj1dnkVzvILP1bXQw1p5HqBOnfbhgDTonz0+YHnLEBOae6rKyM7CDzCj+r/g3EUptqimtwQs0JcZ8l0/z6+cofKgA4vel0lBdPOtWsAFrXSJdm7lfbYBtueO4G9AZ7U2awohfbXsTA+AD29O7J6nmYvNL/2RR1G3B+2vxWNTExgVdffRUrVqxQ3hNFEStWrMDWrVstfUcwGEQ4HEZtba3pPqFQCENDQ5p/AJTIgizLkGVZec/qtiAIKCkpUc5j9Vh2zmxvp8Jkl6OsrEx5zykm9TJSESmSUb5kbSkrK0u7X83Or16b+sjQEUSlKMYmJqPPQ6EhqBWRIprodCKnmt2bzNqlT/eWJeM+NrNBtseeW64nWZaV+bT5wpSte6BXmOz2EwCUlJRAEISMcSQTKadakiS0t7eTTYfwCn+FvwL1pfUAYutB+kQf/t/y/4cbzr4BV773Ssvf4y/wa9auPnXaqRAjk0P+4oUXQ4CAqBzVONCf+8vnsHbLWnzhr1/IAI25RidGAcQKnORCXun/bIq6DTg/bX6r6u3tRTQaRUNDg+b9hoYGdHZamy7z3e9+F9OnT9c45nqtW7cOVVVVyr/m5mYAQH9/v/J/th0IBDA4OKi0jzngPT09GBmJRQa7uroQDAYhSRJ27dqFYDB2b21vb0coFIsktrW1IRyORQ1bW1sRjUYhSRJaW1shSRKi0ShaW1sBAOFwGG1tbQBiDwBYKvv4+Dg6OmIZTsFgEF1dXQCAkZER9PT0AACGhobQ2xv7uzI4OIhAIJAWEwB0dHRgfHw8KRNr9+HDhx1lYu0DgOGR4bSY7PTTkSNH0N7ejmAwmJV+Gh6dTO+OSBEcHT6Koz1Hlfc6+rTZb+FIWPN3njnVage5UIxNTRsZHknYT1JEe+/q7es1ZOrp6UF7ezv6+/tzOvbccj319PRg//79kCQpb5js9JMkSXjrrbcUvnxgsttP4XAYb7zxhrKdDlNfn7WMUkFmrr2LNTQ0hKqqKgwODqKysjLl75FlGePj4yguLiYZqfAS/xm/OwPb2rfhxNoTse/r+1L+ni89+iXc+/q9mF0zG+98/R2svG8l/n7w7wCAd77+Dpbfsxw9wR68/rXXsbBhIQBAuH7SNvLa7Fwesiyj8MZCROUoHr3wUZx/0vlZOY/+nF7p/2yJug04f+b4M/V3yY06evQompqa8OKLL2LZsmXK+9/5znfw3HPPYdu2bQmPv+WWW3Drrbdiy5YtWLhwoel+oVBI43gNDQ2hubkZ/f39qK6uViIPLNokCIKlbSD2A6mkpASiKFo+VhRFJaKSze1UmOxsM9sWFRXB5/M5xrTiDyuw+WAs/f+aM6/Bjz/444zwJWuLJEmYmJiA3+8HgIz3072v34v/euy/lHH78mUvYzw8jrM2ngUA+OYZ38TPX/q58vmliy7Fva/fq7z+wHEfwD8P/hNFviIl6n1y/cnY1bMLa85Yg5+t/Jlpux7a9RA++/Bnle869I1DmFUzy7INsj323HI9RaNRhEIhlJSUKJFbrzNl6x7oFSa7/SRJEsbGxlBaGqu7lE7bBwcHUVNTk/TvfYHpJ3kofToENXmJ//ia47GtfRumlE5J63uWNy/Hva/fi7NmnQVBEPByx8vKZyfUnoDGikb0BHvQOdKpONW5UFgKK/PNchWp9lL/Z0vUbcD5afNbVV1dHXw+n/LUnqmrqwvTpk1LeOxtt92GW265Bf/4xz8SOtQA4Pf7lR/9aoliLKNI/eCDvWd1m6W+2j2W/TDL5naqTHa21ePcKSb1tCoJkz/2M8GXqC0+ny/uOs9kP6nT2gFgIjqhmWfNstCY9OngrECp2qlm6d9szrRZuyr8FZrvKvAVGO5vZgMzpmxtOzX2fD6f4kxlmtsr9wg790CvMNnZFkVRY4N02q7+/kQil/7d1tZGNvXPS/ysWBlbazpVffnUL+NPn/oTfvqhn0KSJMyrji2zNXfKXABAc2Us3VC9bnVdaZ2ynag6d0SKYO2za/FC6wua92VZxm9e+Q3+e9N/mxZnUf/RzWX6t1f6P1uibgPOT5vfqoqKirBkyRJNkTFJihUdU0eu9br11ltx4403YtOmTTjttNNy0VRDUe9nt/A7Vags2/xqBxqIOdXqQmXDE9rq3/rXLP2bpXwD1guVNZY3al6rnRW13DIGnBLnp80POGMDUk61IAior683vQnlu7zE/4HjPgBREHFm85lpfY9P9OHzp3wedaV1EAQBvz3vt/j66V/H5ktiPxY/euJHAQD3v3X/5DGqJSv29u01/e4/vP4H3PCvG/C+379PSW0BgG898y187Ymv4ZfbfolXO141PFbtSOcyUu2V/s+WqNuA89Pmt6M1a9Zg/fr1uPfee7F7925cccUVGB0dxapVqwAAl1xyCa655hpl/5/85Ce49tprsWHDBrS0tKCzsxOdnZ3KHLlcino/u4XfqSW1ss2vruQNHHOqVUtq6at/65fYYkXLCn32nepp5dpMkUTrVLthDDglzk+bH3DGBuTSv4uLi51uhmPyEv8HjvsABr47EJfqlI4EQcCC6Qvwq+m/Ut77zMmfwTc2fQM7OnZgT+8ezJ0yF31jkwUJdnXviluai0ldtfu1ztfwnsb3YF/fPtz+0u3K+/1j/YbHOuVUe6X/syXqNuD8tPnt6MILL0RPTw+uu+46dHZ2YvHixdi0aZNSvKy1tVWTEnfXXXdhYmICF1xwgeZ71q5dix/96Ee5bDr5fnYLvzr9O9dLamWT326kWp/+bRSpZg52Mqe6rrQOPsGnPKQwcxjcMgacEuenzQ84YwNSkWpJkpRqmBTlNf5MOtSAMX9daR3+84T/BAD8+c0/Yyg0pJkv9XbP26bfx9anBIC/vP0XADEnXK3B0KDhsaPh+PTvx/Y+hod2PWSRxr681v/ZEHUbcH7a/Ha1evVqHD58GKFQCNu2bcPSpUuVz7Zs2YKNGzcqrw8dOqQUd1H/y7VDDfB+dgu/Jv07h5HqbPPbjlTrnGz1nGomliFnFnlW9hN9mFo2VXlttr9bxoBT4vy0+QFnbEDKqRYEAY2NjWTTITi/Mf9/zo451W91vxW3NvWuHq2TrJba4f7L7r9AlmW8E3hHs4/+CTWTPlI9EZ3A+Q+cj88+/FnN/O5Minr/A9wGnJ82PxVR72e38GvSv3Mcqc4mv1GhMvXa0/p0b9NIdQrp34A2Bdxsf7eMAafE+WnzA87YgJxTXVRURHaQcX5j/vqy2JrYfWN9cU61OhqtVjgaxr6+yaW+9vbtxQttL2jeA4DBcZNIta5QWWAsoLz+4xt/TA6Tgqj3P8BtwPlp81MR9X52C79Tkeps82cq/VsdqbbjVDeUT64fnyj92w1jwClxftr8gDM2SMmpvuOOO9DS0oLi4mIsXboU27dvN913/fr1OOuss1BTU4OamhqsWLEi4f7ZlCRJOHToENl0CM5vzF9bUgsACIwFNPOpAWhej06MYunvlmLV31Zhf2A/wlIY5UXluOw9lwEAbn3hViVSXeWvAhD7Y7qndw8+8qeP4FMPfkopaKaJVEe0TvV9b9wX9yQ8E6Le/wC3AeenzU9F1PvZLfzq6LR6fnW2lW3+ZOnf+ki1XswBT6X6NwA0lKmc6gTp324YA06J89PmB5yxgW2n+sEHH8SaNWuwdu1a7NixA4sWLcLKlSvR3d1tuP+WLVtw0UUX4dlnn8XWrVvR3NyMc889F+3t7Wk33q4EQcCMGTPIPrnh/Mb8bNmuwFhAiVSfUHuC8h7T3w/8Hdvbt2Pjzo3YcmgLAGBe3Tx8a/m3IEDA4/sex78O/wsAcNr02HIyb/e+jSW/XYKn9j+FR/Y8gp5gD4D49G/1eTpHOvHswWcTshzsP4jPPvRZvHTkpaTczJGn3v8AtwHnp81PRdT72S38mkJlOY5UZ5PfaqRaHYk2UqqRanX6d6JItRvGgFPi/LT5AWdsYNupvv3223HZZZdh1apVmD9/Pu6++26UlpZiw4YNhvv/6U9/wpVXXonFixfjpJNOwu9+9ztlvUszhUIhDA0Naf4BUJ42sAIo7D2r23rDWj2WnTPb26kw2eUQRVF5Lx+Y7PSTLMtKtVr1+9X+agBAX7AP3SOxh0NzaucAiDm8Y+Gx2Jg9MDlmf/3yrwEAJ9efjDlT5uDjcz8OtZY0LgEAPLHvCY0DPRKKFTBRp4eNTowiEAxojj84cDAh06K7F+Ghtx/ClU9cmZB7dGIUc389Fxc/crHy3YIguLqfsjn22H1AEARXMT31zlO4/PHLMRYes8z06J5H8bMXf5a1e6D+/EcGj+Cul+9CMBz07D2C9X2m7oFc7pQgCBBFkewPSrfwO7VOdbb5k0WqmVjGmpnUc6qZo1xXWpf0/FYi1W4ZA06J89PmB5yxgS2nemJiAq+++ipWrFgx+QWiiBUrVmDr1q2WviMYDCIcDqO2ttZ0n3Xr1qGqqkr519zcDADo7+9X/s+2A4EABgdj81Z7e3sVB7ynp0dZH7OrqwvBYBCSJOGtt95CMBhzctrb2xEKxW6EbW1tCIdjN8rW1lZEo1FIkoTW1lZIkoRoNIrW1lYAQDgcRltbG4DYAwAWdR8fH0dHR4fC2dXVBQAYGRlBT08sQjk0NITe3lg0dHBwEIFAIC0mAOjo6MD4+HhSpnA4jEOHDinV8PKByU4/HTlyBK2trQgGgxqmyHAs1ToUDWFfV2xO9IzyGUo1zkNdhxAIBPDPQ/8EEytSNr9mPgDgi3O+qHxWUlCCqcWx6pxsPUqmvpFYOnlHT4fyXv9IP3pGerT7BftMmZ7a+ZTilLcPxvfTc+8+h8dffxwAsKN1B94JvIPH9j6GwcFB7N69G5Ikubqfsjn2enp6sG/fPkiS5Cqm67dcj/U71uMfB/5hmemrj38V3/r7t/Dyuy9b7idJkvDmm28qfHaY1m5ZiyufvBIPvPWAZ+8RkUhEuQem0k96Ji53Sj2GKcot/E6tU51tfqNItbpQGVOlvzLh96gj1d8/6/t46DMP4ZJFlyQ9v3pOtVlk2y1jwClxftr8gDM2sLVOdW9vL6LRqLJOJVNDQwP27NljcpRW3/3udzF9+nSNY67XNddcgzVr1iivh4aG0NzcjJqaGgBQ/g8AtbW1ylOIuro6ZVu94HdDQ4MSoViwYAF8vpiz1NTUpOzT3NysbM+cOVPZf+bMmcqTjpkzZwIACgsLFUff7/ejqakJAFBcXIzGxkYAQGlpKUpKSgAA5eXlKCsrAwBUVlYqEY6qqiplO1UmAJrqdsmYWlpaACCvmKz204wZMwDEnl4xjtLSUhw/43gUioUIS2G0jcV+YDdVN6GmpAa9wV6EC8IIFYbilteq9Ffi8qWXAwA+ueiTQMyPxVhkDFMrp8JIYcT+GBeWTj6hlkQJQ2HtD/SB8QFTpj8d/JOy35SyWOo666ex8Bg+9r8fQ1SOIrAgALkg1hfDoWFUVlZi3rx5EEXR1f2UzbFXX1+Puro6iKLoKqbRSKxw3WBo0BJTTU0NAuMxxzTqj6KystJSPwmCgFNOOUW5B9phYlMjeoO9tvpJlmX0Rfswa8osy/2UzbHX0tKifJ7u2ONyp0RRVPqbotzC71SkOtv8hpHqaHykOplTrZ5TXVtSiwvmX5Bg70lpItUmUTi3jAGnxPlp8wPO2CCn1r7lllvwwAMP4JFHHkm4ILff70dlZaXmHwDFMOxHEnvP6rY+Xc/qseyc2d5OhckuB0uBzBcmO/0kCIImDZ697/P5lGJlrHp3fWm9Mte6f7wfz7U+BwCYMyWWFg4An5n/GVQVx9K7fD4fbvvQbQCAby//tvK+Xmx9anUEOxgJon+8X7NfYCxgyrGzc6eyX9dol2afvX17MRoexXhkHIGxAIKRWFRPhqycm6UBu7Wfsjn22H2ATQWw2vaIFElop3Tby9Yt7R/rxxf++gX85tXfJGQKy2GlmN1oeDQr90D9+dn4CUVCtvj+8PofMPt/ZuN/Xv4fy/2UrW3W95m6B3K5UyxNn2qKvlv4nYpUZ5vfcE61Ufq3ye8AJnX6t5W51Eya6t8m6d9uGQNOifPT5gecsYEtp7qurg4+n09JhWPq6urCtGnTTI6K6bbbbsMtt9yCZ555BgsXLrTf0gxIlmUcOXKE7CDj/Ob8zKk+0H8AQGxe05TSmFPdN9aHd/piVb3/Y9Z/YNXiVWipbsH1Z1+v+Y41y9Zg+1e240dn/8j0CfXIRCyNVV+orH+sX9MOFoXUS5IlHBw4qLwOjAU0T8139+xWtocnhpXzAbHlvdza/xt3bsQ9O+7J+nlSuQYGxgfQ/PNmfP6vn89au9hDln+1/gv3v3U/bnnhloT7q6vL6pdvSaR07gFszE5EJ2wdt6c3lsWkz/RwQtTvgVREvZ/dwq8pVJbDSHW2+Y3WqU4lUu33+ZVtO071rKpZyjYrcKaXW8aAU+L8tPkBZ2xgK/27qKgIS5YswebNm/GJT3wCQCzyuXnzZqxevdr0uFtvvRU//vGP8fTTT+O0005Lq8HpiKX+URXnN+dnDjRTXWmdZqmtjpHY/MrG8kZcf871cccDscjVe5veC8C8QAlzclnUDzhWqOyYE31C7QnY3r5dUw1crY7hDoxHxiFAgIzYjaJ7tBtNlbG01d29Kqc6NKxZD3skPIKTWk4y/F4nFQwHsepvqwAACxsWKjbMhlK5BnZ170LnSCeeefeZ7DQKk5Hq9qHYnF51vxlJ/bAk2fItaqVzD1Ai1QY/HhOJOeHqB0lOifo9kIqo97Nb+NXR6VwuqZVtfhapLi0sRTAcNHWqkxUqUzvddpzqCn8Fnr30WUiyhJLCEsN93DIGnBLnp80POGMD2+nfa9aswfr163Hvvfdi9+7duOKKKzA6OopVq2I/ii+55BJcc801yv4/+clPcO2112LDhg1oaWlBZ2cnOjs7lcIzuZQsy5iYmCD75Ibzm/MzB5qpuapZSf/uC/ahc6QTANBY0WjpXPon1BVFFQCSR6pn18wGMLmU11h4DB3Dk0XN3u1/FwDQUt2CxvJYW1gKOKCNBg5PDGuc96HxIVf2v9opvOPlO7J6Lv0Y2NO7B4/sfgRtg23m7TsWCe4f689atIVFqo8OH9W8TtYm/XYypXMPYGPWKM0xkdiPTTc41dTvgVREvZ/dwu9k+nc2+Vl2WFlhrL5CqoXK1E63HacaAM5uORsfOO4Dpp+7ZQw4Jc5Pmx9wxga2neoLL7wQt912G6677josXrwYO3fuxKZNm5TiZa2trUrVVAC46667MDExgQsuuACNjY3Kv9tuuy1zFBYlyzI6OjrIDjLOb86vdqr9Pj+Oqz5Oea9vrE8TqbYi/VyqluoWAJPRPrWzq16nWu9Uf/hPH0bLL1sUZ4ulp8+una3Mq+oamXSq1ZHqkYkRTcRzMDToyv5X2+L+t+5H96jxmveZkHoMPPz2w5h3xzx86n8/hU8++EnTY5jTL0PGwPhAVtrEfpCxccaWrUrWJv22lXOlOgbYWPJypJr6PZCKqPezW/idKlSWKf6J6ARebn85ru0sUl1WNOlUW1lSiz1cZ1I73WZzo1OVW8aAU+L8tPkBZ2yQUqGy1atX4/DhwwiFQti2bRuWLl2qfLZlyxZs3LhReX3o0CGlOIz6349+9KN0225boihi1qxZZKvhcX5zfhaVBmLFyHyiT3kvMBZQosVsLclk0v/xPK7mOADGkeqwFFYcydm1k061LMvY1r4NE9EJJQL9biAWqT6++nilAiiLVIejYWXuNxBzttRpwiMTI67sf7XjPxGdwHOHnjPd947td2DhXQuVhwx2pR4Db3S9obz/TuAd02PUkeC+sb6UzptI6giHer5yIudV3a9DIetLO6VzD1Ai1R52qqnfA6mIej+7hT8Xkeru0W48se8JTXp5pvgvf/xynP6703HL89oaF0aRaitzqvUP21NN/7Yit4wBp8T5afMDztiAlLVlWcb4+DjZJzec35xfHameVz9P815vsNd2+re/wK8pQsIKixg51QDQPhybS3tC7QnK50eHjyoOF1vO6MCAeaT63f53NVVJ49K/Q0OW+j8UCeH6Lddje/t2S6xmslrQSt1GIHE6872v34s3u9/EPw/+03SfRFKPAbUzPxQaMo2kqCPBfcHsOtVqJXJC00n/TvUekC/p35TvgVREvZ/dwp+LQmXfeOob+Nj9H8M/DvxDeS9T/Pe+fi8A4Obnb9a8bzlSncCJ1r/O9GoCbhkDTonz0+YHnLEBObYPIVkAAFnRSURBVKe6p6eH7CDj/Ob8Gqe6LuZUs+Jl+wP7lT+i6vUhk4n9QS0vKkddaR0AVaEyXSEq5oC2VLcoT6zVkdSe0R4Aqkh1TXykWl35G4gvVDYUGrLU/3e+fCd+9NyPsPR3SxPuxyTLclxhtd+++ltUrKvA0/ufTnq83hbqKKxe7Dx2ndvxyDi2tm1FJBpRbKB35s0ivtmOVJvNn2bFywzblEb171TuAeFoWLkG3B6pTvTjnfo9kIqo97Nb+DXp31mKVLMH0qzII5B5fvUDcsB6pFqf/h33+thvhExHqQH3jAGnxPlp8wPO2ICUUy2KIpqbm8mmQ3B+c351+jdzqpmjvatnl/LaX+CPO9ZM7Cl0fWk9yovKAZhHqtXtqCmuAQC83vW68n5PMOZUK3Oqa2bHO9W9Oqd6YhgjYW36t5X+Z7xWtfrJ1Wi4rQH3vX6f8t5X/++rmIhO4OJHLk56vN4WiZxqtp63Xef2huduwPINy/HArgcUG+idarP50moH1qwqezoyc54TFStLp/q32RgYmRjB15/8Ol5ofSHuM3Uf2Y1UM6dab+9s6PG9j6Pqlio88NYDhp9TvwdSEfV+dgu/Jv07S5Fqdj9SZ/xkmr/IV6R5zZbUKi0sBRCLXFuJVOtfs2li2XCq3TIGnBLnp80POGMDUtaWZRljY2Nkn9xwfnP+mpIaZZulf6sdbcB6kTIm9lS6rrTOklPt9/lRUliiOPNqp7o32IvxyLjiXM+qnhWX/s2c6kKxEEB8pLptqA0PvvFgwggoABQXFNvi3LBzAyJSBJc8egmePfis7e/SO1tmy0lJsqRUSWfp8FbF1vZ+t/9dZQzoz2PqVE9kN/3bzHnOVvq32TVw18t34dcv/xqXPX5Z3OfqPrK7TjX7sZmLSPUz7z6D0fAoHt/3uOHn1O+BVES9n93Cn4sltZgzrXaqM82vd6qN0r+tVP9WR6pLC0tR6Iv9rc5WpNoNY8ApcX7a/IAzNiDnVAcCAbKDjPOb8xeIk0u2z5kyB0D8MltWi5QxsT+oRk61UdSOnU9xqju1kWrm9AkQUF1cjallUwFAKXLGipktmrZIOZf6PHe9chc+98jnsPbZtQnbrXaErYwV9cOGLz/2ZY3TzrgTyWr69+D4oLI2t91INWvT6MSoMgasRqrVaeFZSf82i1RbTf+2Wf3b7Bp4bN9jAGIPZ97qfkvzmSZS7eL0b5a1waZJ6EX9HkhF1PvZLfy5KFTG7kd6pzqT/PoMNSvp336fP84ZVzvV5UXlijOdLafaDWPAKXF+2vyAMzYg5VSLooimpiay6RCc35z/jBlnYF7dPHxuwecUp5LNqWayWqSMSUn/LqtX/vgyR445GGoHlkXLmVOtTufuGe3B4PggAKDCXwFREBUn/+jwUUiyhD29ewAAp08/HcCxQmUGUd8NOzckbLe6TV2jXdjevj3hTWkwNKhsHxw4iFtfuFV5XeGvMDpEI71za+ZUs9RvILU51excbAy4PVJtOf3bRqTa7BroGe3Bi20vKq8f3PWg5nO1raykf8uyjE//76fxlce+onGqs/3HTXGq+42daur3QCqi3s9u4c9FoTKjSHWm+U0j1YXmhcrKi8rhE3ya99SRa/Xn2Ur/dsMYcEqcnzY/4IwNSFlblmWMjo6SfXLD+c35iwuKsevKXbj/0/cr75UVluGUqacor6eV2YtUs/lTdSXaSLUkS4pTXV9ar+w/vWI6AG0qOlNvsFdxXtnT7tk1syFAQN9YH3Z07EAwHEShWKhEqocnhg0d1Ori6oTtVq+X+ZmHPoOlv1uKfx3+l+G+kiwpzujPV/489v+Xfq58rl9azEhxc6rDxk61ej6z7Uh1ZDJSzcYAc+bZDxorc6pzGam2nP5tM1JtdA088U5sSRo2dUDvVNuNVHeMdOCvu/+Ke167R/PQxKzSeabEpkL0BnuVh1BMzx58FtuObCN9D6Qi/rfOeX5ZlpXMIiB7kWp2T1E/hMw0f5xTHTWo/q27L5YVlcEnap1q9ZzqXESqnR4DTorz0+YHnLEBOad6aGiI7CDj/In59UtaCIKAG865QXnNipJY1cKpCwEApzaeqnGq1Y4FqwoOAMtnLAcA1BZr086BWPo3cxLYH+ayojJl/euHdj0EADhxyolKobPh0LBhmrm+Aqle6h8nLx15CYC2Erlaw6FhJRrxxYVfBKCNXFtZJoRFQdlTf7NItcapTjFSPTIxoowBdt6miiYAFiPVOqd6R8cO/OT5nyg/slKRnerfL7a9iBN+dQL+/OafDduXTGbXwLOHYnPhL3vPZQBiFe/V51ePIyuRarWjr06fz3YKOItUA9podWAsgJV/XIn//NN/YnBwkOw9kIr43zrn+fVOdC4LlWWCX32fi6v+nWKkWp/+zZzubDnVTo8BJ8X5afMDztiAlFMtiiIaGxvJpkNwfvv85889X3G6ljcvt3W+NcvW4NDVh/CFU76gcarVjoU6xfx9M98HIH4uNxBzIln6s/oP88n1JwMAHt79MABgfv18JeXaLP27uqQ6YbvVzhSrcnp0+Khmn+HQMC555BL88Y0/AohF+utK6+KWHDMrOqbZ55jDxuaIm6Z/j02mf9stVMaYgpGgMgbYeZsqkzjVCdap/n/P/D98b/P3NGuk2pWdSPVFf7kobj3yoZD1Pxpm1wBjP6VhMjND7azbjVSr+zBXTnUoEtL0oXpe9YH+AwhLYQyMD6C0tpTsPZCK+N865/n1TnS2I9X69O90+dX3P1ZQjMkoUq3PwikrdDZS7YYx4KQ4P21+wBkbkLK2LMsYHh4m++SG89vnFwQBu67chWe++AzOnX2urfMJgoBZ1bMgCILGqWaOZnFBscYJOGPGGQCAxdMWx31XVI7i8MBhANo/zMypZkttza+br6Rc6wuVKe1C4uhxMBLv+LC1QJlu33o77nvjPqx+ajUAKNHxE2pP0Oyndq5GJkbw0xd+iq889hX8bc/flPeZPZhTbeaIqyPVY5ExxRk92H8QN/3rJlOnmO0PACOhEWUMpBKp1i+p1TnSCWCyWJyZEkVp7MypNlpLW5KluNTHQwOHDMe52TXA+qnSX6mMVfXDBLtzqtU2S+RU/9++/4urGJ+q9H2gjlQfGTqibB/uOUz2HkhF/G+d8/y5iFTLsmxaqCxdfvX9T5+JpF9Sayw8Fserj1T7BJ8m2y3bc6rdMAacFOenzQ84YwNyTnUwmP1iOW4V50+Nv6q4Ch+a/SFLqcxmYo7KeGRciTiXFZbhtY7XlH1YhPkTJ30Ct597OwrFQixtWqpEpvcH9sfao45UTz1Zc56PzvnoZKQ6ZDynOtkcXKPIqd6p1keu2TztRE71htc24Dv/+A7uee0eXP5/lyvvM8efLRFmJf0bmEzFvuChC3Dts9fi8scvNzoMgLZQGRsDzMFjTrW6EJpaieZUM0dc7TiOR8axo2OHMs5++M8fwn+TH/PumId/H/533Pfbqf5tVk1d3cYNr23Acb88Dtc9e13cfmbXgDoFnxXTMXOGrSyppe5D9gNU/z0D4wP45IOfxMcf+HhGfnCrU78BbaRa7VQf7T9K9h5IRZn+WxeRItjZudNwnB4ZOqLJonGD3PC3Xr+EVjaW1FLfi/ROdbr86vufPjtHn/5tNAVHP6e6tLBUMzdbHalO9qA7FblhDDgpzk+bH3DGBqScalEU0dDQQDYdgvM7x692hvb27gUQK0zG1sQ+sfZE5XNBEPDNZd/E0f93FM9e+izqy2LFzFjkTe1UL5i6QNk+vuZ4vHf6e5Vz9QZ7DX/IsB8AP9/6c8z5nzk4NHBI87lRiq7eiWZpb0ysuJreqVZHytsG25Tt7tFuxZFj52Op45ad6mOp2Ds6dgAAHnr7IcPjANWSWuFR/Lvv33h498PKDyN9+rckSzjn3nNw7n3nQpIlTXuC4aDmxxs7Rv2j6vot12PJb5dg/Y71AGJFwKJyFHt69+D3O38f3zYb61SbOtWq81/15FUAgJv+fVPcfmbXAGMsKypTMh3MnGr9D0yjP1hmD27U39M50omIFMHIxIjtdP51/16HL//ty5rxzYqUMZlFqlECsvdAKsr0vf77m7+PU39zKn787x9r3h8KDeGkX5+EMzecmZHzZEpu+Fufi/Rv9b1Yn/6dLr/+Qala+vRv9f2Ozb/WR6pLCkuUQpAAUF6Y3TnVbhgDTorz0+YHnLEBKWvLsky6SA3nd46/yFekrIXNlr6aWTUTf/7Un3HpokvxzMXPxB1TV1qHksISpZiZEqlWpX+fVHeSsn1OyzkQBEFxisx+xDAHbM0za/BO4B3cvvV2zedGTl77kDZSrV52C7CW/q2P8rYOtgKwMadaF0m244gxpu7Rblz0l4tw4cMXKp/p07/3B/Zjy6Et+PuBv6N9qF1TwRaYdObHI+PKjy31D7BbXrgFAPDV//sqAGiqUBu12TRSnWBJLb3UP+rU44NpPDKOG567ATs7dqKzrxP7evdpPmd9UF5UbhipNitUtvnAZjTc1oC/vP0XzfeZ9aH6e9TRPX2UOZGGQ8P4/j+/jw07N2gK6LH0b9Z+9cOitqHJBzptfW1k74FUlOl7/U9f/CkAYO2WtZr3WwdbMRoexZ7ePa4aU8n4X+t4Le5haqaVi/Rv9QM+faQ63f5P6FTrItWsHQIExdHWz6kuKSiJi1Sz4+0WQbUi/nuP81PmB5yxATmnOhQKkR1knN85fkEQlD+gbP3p5spmnNJwCjZ+YiNaqltMj2XLbh0ePDanWhWpLi4oxn/M+g+UFJTg28u/DSB+bWj9U/Dh0LDGkdNXKDWKkA5PDGscN/0+ZpHqYDioRBP1kWbFqT4WsVZHqo36yCz9u6SgRHmPzXFWS5Zl5UfRwPiAJh25QCxQnHnmVLN2AZM2FwVRebjB2qF2ltkPMPV3s+9Uz9U2WpLLTqRaXyiNsasj1eol05gdf7D5B1i7ZS0+eN8HcfmTl2PuHXPxeufryn520r9D0clr6DMPfQY9wR5c8NAFAGKp58vvWa5kDyRiUj8k0UeZE4lVpAe08+CZY76oIbakXOdIp9JOdaS6e6Sb7D2QinJ1r2fXiAw568vF2VEi/t5gL5b+binOvc9ejRC7ynWkWl9XIt3+16R/6+pI6CPVTP4Cv/LAuaywLC5SrXeqT6g9AbeuuBW//sivU26nmfjvPc5PmR9wxgaknGpRFDF16lSy6RCc31l+lrr7ds/bAIDmqmZLx6nXsgbiI5GPXfQY9n19H+bWzQUQSz9jUXEgfl3q4YlhvNX9lvJa73SbRU7V86rVy2YBk5Hq2TWz445jjlSySDWbUx2Vo4YVppkzy+af9QX7EI6GNT+sXj36atxxYSlsOp+vrLBMsY86Us3EisNVFFUoGQCsvWqHjv0Ae6fvHc33P7HvCY2t+oJ92Nq2FQ+89YDyXqI51eORcdz43I3Y2bkTkizFPVhorGgEoI1UM6cYmLT5Pa/dAyBmw32DsSg1e7gDTEaWTSPVuuJxLFKjfohw78578eXHvoytR7Zi4+sbDZk0TnWKkernW5+fPE7ljLNt5lSPRcaUhw1qp3rCN0H2HkhF2brX6yOK6gdrRkUhnVIi/o7hDoSlsPLAMFvKRaQ6Ufp3qv0vyzJuef4W/OGNPxh+tyzLCht7UM7k90061eols4DY2FFXES8rKoMgCPj2md/Gx+Z8zHY7k8np3ztOi/PT5gecsQEpa8uyjIGBAbJPbji/s/zMqWbOzMyqmZaOm1E5Q/Nav850pb9Ss486BRyI/8MvyZIm2tcT7NF8brbskXpetfrHJDDpVNeU1CiRXybmsLEo69wpMeef/ahj51MfZ1QBnEU2md36xvrQPdqtSc/+r8f+C0/vf1pznJnTCsR+2OidanWBK9bGCn+FEpVgPEZO9c7OnZrvv/+t+zUOfd9YHz778Gdx0V8uwsH+g7H2Jaj+/bc9f8N1W67D9/7xPQyOD8b9UG0sP+ZUmyx/dbD/YCwFKhSfgs7aH5WiShvKiiYj1WbfCUxGbtQPeNY8s0bZNovapRKpvuX5W3D3K3crr59vm3Sq1RW/mWN+XM1xCkPHcAckWdI41e397WTvgVSUrXu93qlWP3gym/LghBLxs2twIjqRtbWjgfjCZNmIVKsjyHrHN9X+f+XoK7hm8zXYtH+T4XerlzPUR6qLC4onI9VFZZqH28UFxXGR6mzK6d87Tovz0+YHnLEBOac6EomQHWSc31l+9keURfeaK61FqvXOt9GcWbNzAbE/7stmLNN8rnZM9E61mZOnnletX9pJHQ2/+6N347r3X6c488xBZlHWUxtPBRCf/l3pr1R+kBj9QGXHz5kyB0DMSdene3ePduPjD3xc4/QnSsssKyxTUteHJ4YRkSLY328cqWY2Ze1VO9XMAX29K5ZSPb1iOgDEpUEHxgKKg9cx0gEgcfr3wYGY490+3G6YOs7WOWftZOdgOtB/AO8EJqPnPsGnOLPMRmpH1yz9Wx+FY5kE6qi4PopuxmS0v9mSZO1D7bhm8zW44okrEAwHEY6GNQ+E1BFutt1Q1oBp5dMAxFLAe4O9mirBfWN9ZO+BVJTJe736XhQXqVY9rDJbCtAJJeJXX4PZTFmPS//OYaQ6nf43WrZQPeVFvbyWfjz4C/ymhcqKfEXaQmU5cKr57z3OT5UfcMYGpJxqURRRV1dHNh2C8zvLr/8jajX9e1b1LM1rfaTaSOp51eVF5djypS3o/Xav0gZ1Ci1zaEYnRtE50hkX2WXzdhOmfx9zTAHgk/M+ievPuV5pA5sjzZzCU6fpnOpjDltpYalmPW+9mBPGKqX3jvUqjun0iulY0rgEQCwCoy5glajgV1lRmcaeQ6Eh80h1YfJINXOqVxy/AsCk48ycT3X0hh1v9sN2LDKmOODdo92GRc7Y1IDvbf4ebnn+FsiyHOdUbzm0RXkdlaNKG9j5mf0FCCgpLEk6pxqYXMpGnRFhRXbTv9U23tu7F291v6X5DjZ2ZVlWqurPqJyhcao1lb8BjERHyN4DqSiT93p1ho7eMXRrpDoRv/r6MctKyoT0kelsLKllVqgsnf43+nshyZLyMFw95SVR+re+UFmRryinkWqnf+84Lc5Pmx9wxgakrC3LMgKBANknN5zfWf6FDQuVbQGCUnU6mWZV6ZxqC5Fqffp3ka8ItSW1yo8A9Q/FntFYpHrZPcsw6xez4iLXi6ctBqCNVJulf6uldkJHJkaUHyPMqWYOq7pIlplTra60zYqhqSPVp047Fa9c/go+PvfjALRp2ImiMWyeG2vrL176hTLnXd3GSn/lZKQ6wZxqNlf9g8d9UHOeqWVT436AsePZQwy9gxoMB5UHGX3BPqWf1LruP67Dp+d9GgDwy22/xGh4VBOVPThwEJsPbjZkV5zqicmHGqIgGi6ppY9UP73/abx05CVLa1brmZg06d8mTrV6nz29e+Ii2uy4fX370D7cjiJfEd7b9F6NU82cbabuYV6oLN+VyXu9+l6pvuYB986pTsSvdhrtrDBgV04uqZVO/+v7WP/9idK//QV+JXuoobwhPlLty22kmv/e4/xU+QFnbEDKqebiclIXzL9A2a4tqYW/wG/pOH1E20qk+ria45Rt9R/+8sL4P+Tdo91oG2zDm91vGjpJ753+XgDaZYn0KXLqSLVyLpUTyqLUxQXFyjJgR4aOIBQJTS5PUmTuVKtTK1nkvm9s0qlmTtTihsUAtE51wjnVxxxdtt73jf+6UfPjT53+nWxOtSRL6BiORaZZ1Jypurha+bHFpDjVx37YsjnlU0qmKO1mDzKiclRJ4145eyX+76L/w+6rdmNm1Uz86VN/QmlhKTpHOvHcoec059jXty9ujrly/tCAhofZXh2pDkVCOP+B8zXzC4HY3PWVf1wZ9wNUP/9fL7tzqtXR7N29u+PGHTuOPThY3rwcpYWlmFYWGw+P7n1UWdqMPcwJjCdPU+fiYlI71aPhUU36r1sj1YmkvgYT3RvTVdyc6hynf6szq+xI/8CYiUXFWf+LgohCsVApnAnEItW3feg23PGRO7By9kpNpNrv8+c0Us3FxZV7kXKqBUFAbW0tBEFIvnMeivM7y39m85nKttH8WDMVFxRr5ixbiVRff/b1yjarSC0IAqpLJr+HRZdD0RD+uvuvht8jCiKWNcfmY6vXNTWr/q2W2gllRcpqS2oxvWI6fIIPESmCd/snU60TRarZD8FCsVBxPvuCfYoTywp2saj6zq6dyrHJ0r8B4OkvPo1ffzh+WRN2bIW/QnkgYTSneig0hIHxAcUhP6H2BE2Uorq4WnGWmfSR6ksXXYoPHf8hXPneK5Vzq38Y7u6JFbibUjoFH53zUeXhhL/Aj7NbzgYQK4ym1nOHn8NgaBB1pXU4vuZ4zWfsxyOLsDFbqJ3qvx/4Ox7b+1icXdjn+tTq05tON9yXyW76tzqVfU/vHsWJYePt5aMvY9k9y3DVk1cBmMwQYA9ZthzaguGJYZzdcjbu+XisAnp/qJ/sPZCKMnmvVzvVgPa6d+uc6kT8TqV/57JQ2c9f+jkWblyIP7/5Z9vfmShSLcuyMg2nUCyEIAia6HNJYQnm1s3Fle+9EoW+wrhItdqp1mcuZVpO/95xWpyfNj/gjA1IOdWSJKG3txeSlPm5PV4Q53eW3yf6cMrUUwBMOoFWpXZarcxjnTNlDm44+wYAwBf/f3vnHR9Ftf7/z+xudtMTQnqnd0IPEaRICVzgBvAn1QuCFdGriL0B+lVQrIiCHb0q4QIKVwGvSIkXCFVCE0IxIbQkEEjv2fP7Y53JzJZkd7PJZnOe9+vFi83O7Oz5PGd2zjzzPOc5Pe8GYNDvrnKX9ukc2Fka2L898a3JMb74+xf4MulLKW1dTIWurKk0SamuK1JdXFksOUetPVpDrVJLDt6+S/sAGJx3rVprkmKdfiMdT29/WvpuDzcPab3oG6U3kF1iFKn+y6k+mXtSiijUV6gMMDyomD9gPp4b/JzZ/YI9g00i1fJIa3l1uXTz7afzg06jk9opvmccqRadWtFx7xveF7/84xfJMSyqKFIUYjuTdwYAEOgRCGMS2yUCgLRUV4eADtLccwAY12GcydJs4s2jpUh1UWVRvY6CPBUSqM1qsERdhcrkka2dGTuRlJykmBsvd6o7tDZoM65kL9pOXGpM5N3Ed6VzrrSqFKWVjedMEM7Hkdf6upzq5hqprku/IlLdlOnfjRyprtZXS1OMxAKRB64csPmYxg+M5d81f+t89FxtGA9FZ1ruKBs/ODWeUy0vVCbOvW4snH2/42xIP9/6AefYQFP/Li0HQRCg0Wi4fXJD+p2vf+vMrXjylyexYOACmz4nj1TLB+q6eGnoS5jRY4aULm281Fa0XzSyi7ORkZ+BQ1cPKT6rFtS4p9c9EARBEZnNL89XFGpx17hDgKBwIEXk1bLFyLzoWA6JGYJzN89hy7ktAAzOrSAIJpHq7qu6o1pfjd8u/gbAMO9XvHEpqCiQIqWiUx3rHwtfnS8KKwpx5sYZ9AjpYVX6t8hrd7yGkW1H4mzeWczbMk96v11AO+nm2lz6N1C7FJcYSQ/0DJQisP7u/orlVeSfF9snFoTzcDP8n5mfqXA05ZFqY0a3Gw2gNhoU4h2ChQkLMWndJADA+I7jTeYjG8+pFm0hj1Qbz6+vD2uc6oLyAuzO3K3I1qjWV+Ns3lkp+j7i6xEmnz2bd1Zqc4eADjh45aBie4RPBPpHGL5fPB8Aw0OoHsE9oBJU0Kg0qNZX42bZTXhqlZV7iZaDI6/1xmnElpzq5jSnui79TZX+3RSRauMHpuXV5fDWeksPPI1Xh7AGS5HqiuoKrDq8SvpbdJDlTrXxg0uVUBu30qq1ynRwK6d/2UtzuN9xJqSfb/2Ac2zAVaRaEAT4+/tze5KRfufrj/SNRPL/S0Z8ZLxNn7Mm5dsc7QLaSc6cIAgI8A6QtsX4xSDIK8js5zzcPCQ7eWm9JKf5Yv5FKcLq5eaFnbN2Yvs/tpt96i46ab/8+Qs2p28GYEj/BoDhscMBAD+m/yh9B6CMbjPGJAdenCPtofFQRMXFomJiZFIQBGl+9JkbhsiuNenfIoIg4I42d0hraYu0D2hfu0SYmUJlAKRUdtGmctv66fxM07//mtMstk90psVlWoyjwKITau4BRqfWnSTbAgY7J3VKwj96/gO3Rd2Gse3HKrYDtRGZutK/LS11ZYm+4X0VcwyNKakqwaPbHsXEdRNNbni7fNgFKw+apuCLVNRU4HiuIXId5h2mOOc2TtmIIw8ckc51uVM9MHIg1Cq14fz/ywY0r7pl48hrvbXp380pUl2X/iZL//4rMi1eDxojUi2v/g3UOtliFoxx31mDpUi18ThiLlJtaTwFDHOqfbQ+mNh5IsZ3HG91oVJ7aQ73O86E9POtH3CODbhyqvV6PXJzc7lNhyD9rqtfHqm2F71eD01NbbQ02i9aiqoaY7z+Zqx/LABD9FSMzvi5+yEhKgGDogcZfxxArYO86cwmKS1ZdCyHtzE41WL0QnRYxXnLxZXFklMMQErd9XDzgEalkewh3sjKb1BEp7O+Javk32uMsQParlU7kyi6sVN9/qZhfWsxWiF3fussVGYcqf7rf0sYO+eAYfDoF95P0X5BEPD1pK+xd+5eeGm9LM7prqtQmdypjguJk6YvmCPIMwj+7v51nqv55fnSAxYR+bzDXy78UmfK+ZGrR6Q2yvt1QscJCPEOkf42dqpFRBuYq6ROtBwacq3fnbkb92y6B2tPrMV/0v+D1EupAGqn3eSX52P9qfW4/cvbFdeo5jSnui79TZX+LWbZiE4nA3N4FV5zkWqgYU61eF38v+H/h31z90mrTchrigCQHuDJnWpzDzxFtGotBEHAD1N/wI/Tf2z0G31Xvt9xBKSfb/2Ac2zAlVMtCAJ0Oh23T25Iv+vqn9ptKgAgxCuknj0tY1yoLMY/RnETMK7DOOm1sWMnOtX3/udeDP/K4BDXV4XcXHVT0VkN9wlXRIPFVDj5vGUx5RswzO8Fap19ebvVghoRvrVOtejU3Sq/hUW7FpkU75LDYP4mT+5Ua1QaRPlFSW2zFKn+89afAGrTv+WpgHUWKrMQqbZEu4B2Zt+XVxwPcA8w2W78oKC0qhSVNZUm6d/y9cXFaPLKsSuR9lCa5HDL8dP5YUz7MXhm0DNmv0fOvkv7FCmzAgQsHrZY+jszPxPpeelmPmlATPc3boe8WBCgtH23oG7S6/YB7dGhVQcQLRt7r/UX8y9iYvJEfHXsK8z4fgaSkpNQw2rwj57/kIoB3iq/hSkbpmBP1h5FdLq5Raot6W/q9G+50+notarlhcoA8061rY68mInVI6QHEqISpIwY+VKLgHXp33Lk+zUFrny/4whIP9/6AefYgLs51X5+9qXRtgRIv+vqn9ptKrzcvNAnrI/dxxAEAYE+tc5ojF8Mzuadlf6e0WOGNMfZJFLtFwtAWbXcnIMlx1wUWO5YTuo8Ccv2LgNQGwWSR4NTLtYuDyVWXBWd/dYerXEehshwlF+UYr6yv84fgKHy9dZzW+tso6WbSrljGOQZBI1Ko2hbVU2V5BSL83Sl9G8zkWo/dz/F/Dqg/jnVIuLxAcMcdvl653IUTrUZx9bcewXlBRYj1YDpgwJz8wCDvYKxbeY2xffIq7rXBQPDi0NexLTu09Dhgw44f/O8yc0rYLghrayplB6CyNsoLpUlR61SY0aPGUi/kS6tXQ4A/5luvpI50bKw91p/z+Z7UFBRgE6tO0ElqJCel45eob2watwqPLzVUJX/1z9/NftZcY34J/77BMa0H4PxHcc3SENDqEu/PDrdFOnf8gdeNawGalhXE8QazEWq9UwvzamuqKnArfJbdT7oM0a8LosPjXVqwzXPxKm2Mf3bGU61q97vOALSz7d+wDk24CpSrdfrkZOTw206BOl3Xf2CIGBCpwmKiKyt6PV6lJXW3lBF+0Vjfn/DUkQTO09EuE+4tM3YsROLncmpb563uUi1/CZu8bDFWDZiGToEdMD07tMB1Dp+10uvS5XBgdqbJ7Fd8lRqMYouIkaq5amZlrCU/ih/qCDO4RYfEhy6cgj+b/hLRciifA3riEvp314W0r+NItVn886ix6oe0txpS5FqcY44APQJ62Px5kye/m0u2m2uwFlBRUHtnOq/9OnUOikKYzxP3Nx3i5FtEXM3sPVNX4j1j4VGpUFZdZlZp0VeyRwwONWbpm5CfEQ8kv9fstljfjv5Wxx+4LDiXHblawBhPfb0c15pHnZn7gZgKCj5x/w/UPZCGQ7dfwheWi/pYd36P9ab/XxJVQl2ZezCh4c+xLO/PttQCQ2iLv1NVv37r0i1vOK1o+dVm3OqCysKFRFxW1PAxTnV4jXLlkh1XenfjV2YzBjer3Wkn2/9gHNswJVTLQgCPD09uU2HIP2kv6Smdt6fn7sfpnWfhj1z9mDd/1uncPqM078jfSNNjldf+re8kFTH1h0BAEmdk6T3dBodnhn8DM4+ehaPDXwMgMHRBwwOalZBlskxRWdR3tYYP6XDLzrBF/Mvmm2XfA6vvJK5HPk5IjqJ4kOCipoKxY1plF+U4rPm0r/9dH7SfvKHDSdzT0qvRZvLb0IB4IE+D0ivB0YMhCVE2wHK5aqMdcjJL8+vTf/+K71dEAQpEizqlCLVatMbQ+OMBXPf0yu0F76f8j0GRAzAbVG3mWjUqDTSvHnjOdcApHmN8u9M6pyE/fftl84ta+D9GsAL9vTzsZxjAAz1E8RzUavWShkm5pYNlFNcWYxrxdcAABn5GQ6fP2wLdelvsvRvvWn6t6MrgBsXKiurKjO59tnqVEuR6r8eGovj2OkbpxX7iZFq+bWsrvRv42teY8P7tY70860fcI4NuHOqfXx8uD3JSD/p93RXRjBVggqDogdBq9YqIpnG1bzjQuJMjlffOpvywi5pD6bh0oJL0jrSlhAd5OM5x83OdxYdT3lEwFKk2tINnL+7P54f/Dz83f3x7OD6I0riGuHGlcIB4Pbo200eOFgqVNY9uDs+nfApNty1weQ4nQM7Sw8M5OdnfEQ8bou6rfbvOqrGC4KA+/vcD3eNO2bFzTLZLnd2Rec4vzwfxVXK9G/A1FGuK/3beF9zhdS0ai0mdZmEA/cdwN65ezG9x3STfUTn2NySNuYi1fbA+zWAF+zpZ3GFgbhQ02sdUH+2RUlliVQAr7Sq1OyDraaiLv1NVf3buFAZ0DSRamO7Xyu6ZvK55JPJWPjfhSZzvCuqK6Rjiv0tXvOMH8CKTnJlTaX0Xn2FypoS3q91pJ9v/YBzbMCVU63X63Ht2jVu0yFIP+mf0XYG4iPisWrcKpPtcmfIeDmnNq3a4H9z/of0R2qLSInzbS0xuctkAIa0ZA83D7PRbmPEaKulgjbyOdUilpxqY0Tn2NfNF68OfxU3nrohRaTMIRa4mtt7LgDTdPYXbn8BKfekmETs5etUi4hRj/v63IdR7UYp5levHrcaRx88qrjwi/OEV4xdoZinFx9R91JsH4//GHlP56FDa9NiXHKbtfFvA8AoUi2bAy93WlWCSnLI7Y1UG99QrhizAvf2vhcp99TOm5c7zgIExUMbYz32OtW8XwN4wZ5+Fp3qXiG9zG6XX1ce7vewyfbiymJFtXxzmTZNRV36mzz922hOtSMxt6SWNZHqf277J97Z/45iihGgXE5LrPNR38NjeeFFcw9eRZraqeb9Wkf6+dYPOMcG3BUq8/X15fbJDekn/W1D2iL13lSzNpBHIc1FMAZHD1b8Xd+c6oSoBJycd9LsfGxLBHgEwNPNU/p++Wvxb0A5P9g4/duSUx3oGWgoWuNpWG7KuHCYMSn3pOBk7kkMiRkCwNSpDvUONTwJ1SrnFJtbp1reJpWggp/OTyqm0yu0l8mN27aZ25Bfno9OgZ3AGMOMHjPgqfFUpHibQxAEi9XDQ7xD4K5xh06tQ5tWbXAm74zZQmUA0DWoq5QOG+gZKNnKnFNtrN8ap9rP3Q+f/f0zxXtyp3pW3Cycu3kO+y7tg1pQmzw4aUikmudrAC/Y08/i+W4pm0ZeDPHh/g/jo8MfKbaXVJXgemntUm1ZBVnoHWZaRK8pqEu/M9O/Vx1ahUfjH7X792uMuUi1WCNCxNipzivNk/rp9PXTinFNrPztq/OFWmWYJmTJqRYdcEvrWhsjVo9vKni/1pF+vvUDzrEBV5FqQRDg5eXF7UlG+km/tfrrutn635z/YWz7sXhn9Dv1HqdbcDezBcvqaqPcSe4a1FWxXSw6JXfcrI1UD4oeBJ1ah6GxQ62yQWvP1op9jauZi8ubGTu6YoQ61DsU7QPao2tQV5ObSPn3dw7sbPLdId4h6BTYSdr328nf4tO/f9qgc9db640ds3Zgx6wdkv3yy/NrC5XJoixj24+VXsvXMrcm/Vt8iCKfX2jOGTdGPjf6leGvIMw7DIBhLqtxWmVDnGqerwG8YGs/V1RXSIWoLDnVt0ffDq1ai/Edx6NbcDeT7cWVxQqn+mKB+ZoOTUFd+hXp39WNWP37r6i0/GHEi7texOrDqx32Hdakf18tVjrV8iX7jItZGlf+BpTXLvlYJjrg4v+WyHkyB8ceOoYuQV3q3M/R8H6tI/186wecYwOunGq9Xo8rV65wmw5B+km/tfrrmms3OHowts7cKjl9jkbupBo71WIUVj6/zTitXEzzNqZvWF/ceOoGnuj+hF3ngHFqX6h3KABgTu85iuJnYmRGo9Lg5LyTOPrgUZOouPzGr76IvyMZGDEQoSxUummUp3/LbxgT2ydKr+W2NpfCaOzgju84Ht9M+gYr/7ayzs8ZM7zNcDw/+Hl8P+V7RPtFS/Zt5d5KkbouQKgzzbIueL8G8IIt/VxSWYLndjyHan01AjwCLE5TifGPwfWnrmPjlI0ATDMySipLmlX6tyX9TR2pVgtqxfXRkXYxt061eG1tpTOMA5cKLin2Sb9R61TLHWyg1qmWP5iVR6rFlR6A2rTv+lLag72CLS6D2Jjwfq0j/XzrB5xjA66cakEQEBAQwO2TG9JP+q3V35hz7epD7lSL85pFxDnV8rWJ5XP2AMuRaneNO7y0XnafAxqVRhG1CPE2RKo93Txx5IEjCPAIQFKnJMVndBpdk8+lqwvxHBBtVFBRm/4tj8TLo9PyaI65CrbGTrVGpcHMnjMVFbutsYFKUOG1Ea9hUpdJAGAxUu2j86k3dd8SvF8DeMHafmaMYfrG6Xh3/7sAgNlxs+v8jK/OVzqXJ3WepNhWXFksFSoDnOtU16Vf7kg35nVerIuhVqkVv9cbpTcc9h1ipFqAIP0tOtVi/YkzN84oKrHXFakWU7nlDzrlTrV8bDKez93c4P1aR/r51g84xwbcOdUeHh7cnmSkn/Rbq78xq8LWhzXp312CumDPnD3IeCzD5PM+Oh/pJkvxWY1Hg88B+c2hGEkFDBWDLy24hB+m/mD38ZoCUb/oVFtK/wYgrWH+9G1PS+/JK92K0W7jOdUi8ptRa9K/jWnTylBMLdwnHJ5untIxGjIfk/drAC9Y288fHPwAP579EVq1FhunbMTbo9+2+jveTXwXLw95GXvn7gVgKO54peiKtN3ZTrU5/YyxJqv+LUZw1YJamp8MAHlleQ77DtGpFq8JZdW1S2olRCdALahRVFmkmFctd6oz8jOQW5IrZeOYi1TLr13ySHVzh/drHennWz/gHBtw5VTr9XpcunSJ23QI0k/669O/eOhiADBbHbypqCtSLS/CNSh6kMl8auCvQmBmUqo93DwafA7IIzvGzqSnm/XrIc7rNw+A4ca8KRH1izeh+eX5KKooAmBaiO39Me/jv3f/F4uGLZLek+sP9wkHYNnJla91bk+0fnKXyXhz5JtYOmIpBEGQitM1xKnm/RrAC9b0M2MMb+x9AwDw1qi3MLnLZJtuvnx0PlgyfAn6hfeT3pNPlXDmnGpL+qv0VYp05aZI/1YJKkWatiMj1WK0WLzeyyPV2kot2rVqB0C5xrQ8Oq1neoS8FYLwt8OxfO9yXC68DEA5hchSpFrkbx3+BqD2mt5c4P1aR/r51g84xwZcOdWCICAoKIjbJzekn/TXp//loS8j98lczOgxowlbpkS8cfHQeJjcxMgdtbowlwLurnF36DnQkGO8P+Z9HHngCB4Z8EiD22ELov5WHoabxhulN1BUaXCqjeeiq1VqjG43WvEgQ+5UT+4yGWHeYRbXzpbfjNrjVLtr3PHUoKekQm5iCrilyLg18H4N4AVr+vn8zfO4WnQVWrUW9/W5z+7v0qq1imkRYpZMdnE28svzsSdrj1S3oKmwpN84Mm0p/VvP9Ji/ZT5WHFhhdxukSLVKDYba9Ou8UmWkuqK6Ajv+3IGJyROR+E2iNB1F5FTuKcStjsP3p7+X3sspzkHvj3vj92u/A6i93sud6uigaKk42OnrBqe6Wl+NCzcvAFBmCV0vvY6nf30aKw8a6kDIK3Ur5lT7RZlcy76b/B02TtmIdxLrL9zZlPB+rSP9fOsHnGMD7pxqd3d3bk8y0k/669MvCIJiKShn0C+8H+JC4jCn1xyoVWqFIy2mf9eHuWJlolPdHM4BN7Ub+oT1cUr6t7u7u2QfeTTN0lx0OfLI1v/d8X+48sQVKWJtjCL920zVcFsRi5U1NP27OfQ/0bhY088pFw1rpMdHxFt9XbGEfOpEpG+k9DCw7fttcfuXt+Oxnx8DAPz6569o+35b7Mna06Dvqw9L+o2dakvp38dzjuOjwx/hmV+fUcxHvlx4GZn5mVa1QV6oTI48Ur3/8n4ELQ/CyH+NxOb0zfjlwi/46exPiv1XHV6F4znHsXj3Ytz9/d1o+35bfH70c2ldcUDpVIvHD/ULRZdAg1P97v538Y8f/oHXfnsNVfoqeLp5KubET+02FYAhNV2n1uHOLndK2+TXrmi/aDzU9yEAhmrwgCFKPrnL5HrXs25qeL/WkX6+9QPOsQFXTrVer8fFixe5TYcg/aTfFfR7ab2Q9lAaPhz3IQBlyrelNZiNEW+y5CnNFdUVLmODxkLUL0Z7xbmG3lpvk4Jv5jCObNU1WDU0Um2MGKluaPo3z/3PC3X1M2MM+y/vl5y3oTFDG/x9xkX+ku9MhofGQ1qLfv0f66Fneqw8uBIZ+Rn417F/ISk5CQmfJyhSxh2FJf0mkWoL6d/nb54HYHBSxTnQ1fpqJHyegD4f95GWkdp2bhuOXD1i9hhV+ioAUMynBgzXkLUn1iL5ZDK+P/09iiqLFJXUd2XsUuy/K9Pw94ncE/j2xLfIyM/A8n3LFfuI9R02ndmE9Lx0qAQV3EvdpSyXjPwMfHP8GyxOWQwAeHTAo1gybAnu7X0vzj96Hi8OeVE61viO4xXTh4znVL8x6g0k35mMTdM2mdXdXOD9Wkf6+dYPOMcGmvp3aTkIgoCwsDBun9yQftLvivo93TylGztb07/DfcJxNu+s9L6jbNDUEWZHIeovL1Su72ppGTJjbo++HT+d/clsIThj5BEeRzjVjopUu+JvgLCNuvr5+R3PY9neZdLfQ2Mb7lS39mwtFSkL9gpGQlQCfr77Z/zr2L/w2dHPUFhRiGPZx7Dv0j4AwN5Le3Hq+ikAhiWezK173RAs6bc2/VtMkQaAK4VXEOgZiPM3z0tzjg9cOYBW7q3wt+8M84n1L+ul72KM4dFtj+LDQ4aHosaRagCY8f0MCBCkQpRLRyxFmHcY/p78d8mJBgxp3uL64XLEgmIi4vU+I99QuPKfA/6Jfh36we167YNCP50fCioKMLfXXLw+4nWoBBU++/tn0vakTknYnL4ZD/R9wKKNIn0j4a5xx9TuU82ZrVnB+7WO9POtH3CODbhzqrXa5rO8TVND+km/K+qXp1Zam6Yp3mS19miNZSOW4eDVgxjVbpTDbGCtE9rcEPWLc6pFjP+2xOMDH4efzg8j246sd1+VoIJWrUVlTaVDnOq2rdoCaFgFXlf9DRC2Ya6fCysK8cGBDxQOtZvKDQmRCQ3+vgf7Poj5Ww3V8sUHbkNihmBIzBBcLb6Kree2YtXhVbhealhyS3SoAeBs3tlGcarNneeiUy1AAENtJfCK6gq4qd2ktl+4VetUXy68jLjQOJzMPSm9t//yfoWDnpmfKVXr/+TIJ5JDDVh+AMnAJDv0DOmJLoFdoBJUOHfzHC4XXkakbyR2Z+5WtNcSg6MH47sT36GG1aCNfxu8eser0Gq10pxqANg4ZSP6R/S3+FDuuzu/Q2Z+psmKE/K1xxs6TaAp4f1aR/r51g84xwauGW6xE71ej8zMTG7TIUg/6XdF/Q1J/27t2RrPDH4GG6dshEalabANPpvwGby13tg4ZaNdn3c2on4fN2WxL2sfEmjVWjzY70G0C2hn1f5iCrg9S2oZ83D/h5F8ZzIW3rbQ7mO46m+AsA3jfq6qqcKgLwbhxV2GNN9/Dvgnlgxbgi+SvjBZSs4e5NHNCJ8IxbbRbUcDAD79/VOzn5Vn0jgKS+e5mO4tpluXVZXhws0LaPVGK8z8fqa0n5j+DUCKwBs71f9J/4/097GcYwAMzvnCX5S/T+P0b2MECOge3B1+7n7oG9YXQG0K+I6MHQCAe3rdg06tO5k4vCLjOozDtYXXcPC+gzj8wGF4ajyRmZkJT40nNty1AV9P/Boj2o6oM8vF083T7PGDPJ1bY8ReeL/WkX6+9QPOsQF3kerIyEhu0yFIP+l3Rf3y+YrWpn+L829DvEIU7zfUBvf2uRdzes9x6fTvyMhIqNVquGvcpXVe5XMaHYm7xh2FFYUOiVR7ab0anHbpqr8BwjaM+3nTmU04mXsSrdxb4c1Rb2Ju77kO/Q1rVBqcnn8ab+97G88OflaxLbF9IvBfy59tDKfa0nkuRpdbe7ZGXlkeGBjW/7EeZdVlSD6ZjFk9Z2Fsh7EmkWpA6VRvO79Ncdy07DRM7DwRx3KOSevei+hZ3Te07QLaSbUvhscOx6Grh7ArcxfGtB+D7058BwCY1n0avkj6AowxBC0PQl5ZHiJ8IiSHX6fRIdAzUCqyyRiT9N/Z9U7zX2wl8/rPw5+3/sTEzhMbdJymhvdrHennWz/gHBu45p2hnQiCAJVKxe1JRvpJvyvql0enrU2/mxU3C/f1vg+PxT+meN8RNnBVhxpQ6pdX+26sdHYxUu0Ip9oRuOpvgLAN435+/8D7AAwFqu7rc1+j/IY7B3bGp3//VEqDlr+/aGjtWu8alTKWce7mOcXfW85uQeQ7kSYFu2zheM5xrPtjncn7klP9V30CADh89bD0euEvC1FaVYpLBZek964UGhzXE7knLH6fWIn70JVDAIDEdonStmPZx7Bk2BIMihokreksJy4kTnp9R5s7AAA7M3bizb1voqSqBH3D+mJU21EADP0qLuH3tw5/w/z+83FPr3ukh6gijvyde7p54sNxH2JUu1ENPlZTwvu1jvTzrR9wjg1c9+7QDvR6PbKysrhNhyD9pN8V9duT/h3pG4lP//4peoT0ULzvqjZwFHL9YsVcwPo51bYipX87YEktR8B7//OCvJ+PXD2CvZf2QqPS4KF+DzmlPYuHLcY3k77BwoSFmNZ9mmKbcaT6rdS3cKXoCj75/ROLx6vR10gRZMAwX/y3i7+BMYbKmkqM+XYM7v7hbnxz/BvF50Sn2lfnKxUQS72cKm0/feM0Pj78sWL+cmZBJnb8uUNqZ8fWHQEYfturxq0CUJv+feiqwamOj6hdu/5S4SW8PPRl7Jm7x2w9hJ4hPaXXg6IHQaPS4GLBRelByCvDX1HcFD/c72HE+sdibu+5WPm3lfgy6UuTY9LvnGxA+vnWDzjHBlw51SqVCtHR0VCpuJItQfpJvyvqVxQqszL92xKuagNHIdfPY6Sa9/7nBXk/f3DwAwDAlG5TEOYT5rQ2zew5E2+NfgtdA5XzdnNKcqQlqvLL8/G/i/8DAOzN2mv2OLszd6PXx70Q9W6UwQFmDBPWTsDQNUPxdurbWH9qPbKLswEAL+56UbFslrguvZfWS8r6EZfVG99xPADg2R3K9PWdGTsx8l+GwoQBHgH4bvJ3eHbQs7j4+EVpfefM/Ezkl+dLTnX/iP6Y02sOAENKt4g8ojw0ZijUghrjOoyT3vPWemNAxAAAhiW5BkYOxNj2YxXtGddxHDIey8DAyIFm7QPQ7xwgG5B+vvUDzrEBV3OqGWPQ6w1LP/CYEkH6Sb8r6vfUGKLTAoQGO2euagNHIdevcKobKVLdJbALTuScQIeADo1yfFvhvf95Qezn66XXsfbkWgAwmQriLMRIb6x/LMqqypBTkoNT10/hlwu/YEfGDtSwGgCGCO/T25+Gp5snXhryEm6U3sDak2vx5C9PSvu8lfoWPNw88NvF3wAAL+x8ATF+MdJ3ZRVkYcF/F2DVuFXYdn4bXv3tVQDA4KjB+N/F/6G4shiAwZldOmIpfjr7EyprKgEA3YK6KaqUA8DY9mPRN7wv+ob3ld5r26ot/rz1J57f8TxOXz8NAOgf3h8j245EfEQ8JnSaIO0rL1j4/dTvoVFpTIqH3RF7h7T02CvDXrHrd0q/c7IB6edbP+AcG3D1CIMxhsuXL4Mxy0sztGRIP+l3Rf1ipNrTzbPBF0ZXtYGjkOv3c5elfzdSpPpfk/6FrAVZiqVtnAnv/c8LYj+vSVuDyppKxEfESxFQZ5PYPhFJnZKwaOgiycFOSk7CkpQl2JO1R7Hv8n3LsSRlCRI+T0D4O+FY8N8FqGE1UtGs8zfPY/am2QAMUeDKmkqcu3kOWrUWrye8DgECPj7yMYZ/NRwTkyeiWl+NGT1m4LGBjymm0vQM6Ynuwd1xe/TtAAzL1n0yoTb93NPNE5mPZeKLpC9M9CwdsRQAsOrwKjAwRPlGIcQ7BO4adzzY70GE+4RL+94qvyW9DvAIMFuNe1KXSVAJKiS2S7Rq6T5z0O+cbED6+dYPOMcGXEWqVSoVYmNjnd0Mp0H6Sb8r6hdv/hyxRqir2sBRyPX76/yl9xsrUu2mdkOkb2SjHNseeO9/XhD7OXW/Yb6w8TxmZ+Kt9camaZsAGAqGHbxyEDdKb8BD44EaVoPKmkoMiBiAg1cOSp8R06p7hvTE7LjZWDBwAeZtmYePj3wMAOgb1hebp23GG3vfQFlVGcZ1HIeJnSciODAY9/94P1IupgAApnabii+TvoRKUGFk25H4/OjnAIDeob0BAP++69/Y8ecOJHVOkipyA8Cw2GGI8a+NgMuZ0m0KTl8/jcUpixHgEYCnBz1tUfv4juPx7v53Ee0XbXGfPmF9kPFYBoK9gu1+iEq/c7IB6edbP+AcG3DlVDPGUFVVBTc3Ny7TIUg/6XdF/eKSWg2dTw24rg0chVx/U8ypbm7w3v+8IPbz0eyjAAyOWnNkQqcJODHvBD458gmmdJsCf3d//HnrT9wqv4XpG6cDAN4a9Ra2nNuCZwY9Y1ie6y8eGfAI1qStQZ+wPtg6cyv83f2xYuwKAAb9lZWVmNt7LgZGDsTG0xvhrfXG4wMflyqffzrhUwyJGYL1f6zHvH7zAACh3qGY2bN2verR7UZj+4XteO2O1+rUsWjYIiy8bSG83Lzq/F0Njx2OPXP2oFNgpzqPV5fTbQ30OycbkH6+9QPOsQF3TvW1a9cQFRXF5UlG+km/K+oXI9XWVv6uC1e1gaOQ62+KOdXNDd77nxcYY/gj4w9kFWQBUC7b1Nzo0LoDlo9ervi7Wl+NEzknMCx2GEa1G4WFty00+Vz34O7IeTIHPjofkyXC5Od5t+Bu6BbczeTzgiBgVtwszIqbZbFtG+7agPzyfET5mVbtNkYe2baEIAgYFD2o3v0aCv3OyQakn2/9gHNswJVTrVKpEBNjPoWJB0g/6XdF/Y5O/3ZFGzgKuf6mmFPd3OC9/3lBpVLhpvYmAEMhLfm57gpoVBq8NqLu6DAAi7ocdZ776Hzgo/Np8HGaGvqdkw1IP9/6AefYgLtCZeXl5dxO3Cf9pN8V9YuFyhyV/u2KNnAUcv3ySLX8dUuG9/7nBcYYDl4yzEkW5wvzBO/nOe/6AbIB6edbP+AcG3DnVF+/fp3bk4z0k35X1J8QmYAQrxDFeqb24qo2cBRy/aIj7aP1gZvazbkNayJ4739eYIzhYJbBqe4V2su5jXECvJ/nvOsHyAakn2/9gHNsYJdT/eGHHyI2Nhbu7u6Ij4/HwYMHLe576tQp3HnnnYiNjYUgCHjvvffsbWuDUalUiIqK4nYxdNJP+l1Rf5tWbXBt4TW8MOSFBh/LVW3gKOT6Az0DAQBBXkFOblXTwXv/84JKpcLZwrMA+IxU836e864fIBuQfr71A86xgc3ftG7dOjzxxBNYtGgRfv/9d8TFxSExMRG5ublm9y8tLUXbtm2xbNkyhIaGNrjBDYExhrKyMm6f3JB+0u+q+h1VZMKVbeAI5Pr7hffDc4Ofwzuj33F2s5oM3vufF/R6PUa3GY1hMcPQO4w/p5r385x3/QDZgPTzrR9wjg1sdqrfeecd3H///ZgzZw66du2K1atXw9PTE1988YXZ/fv374/ly5dj2rRp0Ol0DW5wQ2CM4ebNm9yeZKSf9POsHyAbyPWrBBVeH/E6kjonObtZTQbv/c8TC3suxI5ZOxDuE+7spjQ5vJ/nvOsHyAakn2/9gHNsYJNTXVlZiSNHjmDkyJG1B1CpMHLkSKSmpjqsURUVFSgsLFT8AwxPnwGDoUQj6fV6q1+rVCqEhYVJUS9rPyt+Z2O/tkeTLa8FQUBERIRk55agyZZ+AoCIiAgIgtBiNNnyWhAEhIWFQaVStRhNtvYTAISHh0OlUrUYTY11DXQVTba8VqlUCA+vdbIa2naieaJSqRAREcFt6iPp51s/QDYg/XzrB5xjA5u+6caNG6ipqUFISIji/ZCQEGRnZzusUUuXLoWfn5/0LyrKsEbirVu3pP/F1zdv3kRBQYHUPtEBv379OoqLiwEAOTk5KC0tBWMMmZmZKCsrAwBcuXIFFRUVAIBLly6hqqoKAJCVlYWamhro9XpkZWVBr9ejpqYGWVmGNS+rqqpw6dIlAIYHAFeuXAEAlJeX49q1awAMae85OTkAgOLiYly/fh0AUFhYiBs3bgAACgoKcPPmzQZpAoBr166hvLy8Xk3V1dUoKirCxYsXW4wmW/uppKQEZWVlLUqTtf1UUFCAy5cvgzHWYjTZ0085OTlgjLUoTdb2E2MMf/75p6SvJWiypZ9qamqka6AjNLV0bKmfAgDr169H586d4e7ujh49emDr1q1N1FIljDGUlJRw++CD9POtHyAbkH6+9QPOsYHAbPi2q1evIiIiAvv27UNCQoL0/tNPP42UlBQcOHCgzs/Hxsbi8ccfx+OPP17nfhUVFdINEmC4eYmKisKtW7fg7+8vGUiMOAqCYNVrxgwLgYeGhkKtVlv9WTGyJ0Z6Guu1PZpsec0YQ25uLoKCgqDRaFqEJlv6qaamBtevX0dwcDAEQWgRmmx5XVNTg5ycHISGhkq/B1fXZGs/VVdXIzc3V6rv0BI0NdY10FU02fIaALKzsxEcHNzga2BBQQH8/f1RUFAAX1/fekZP12PdunWYNWsWVq9ejfj4eLz33ntYv3490tPTERwcbLL/vn37MGTIECxduhTjx4/Hd999hzfeeAO///47unfvbtV3FhYWws/Pr8E21ev1yMnJQUhICJeRGtLPt36AbED6+dYPONYG1o5NNjnVlZWV8PT0xIYNGzBx4kTp/dmzZyM/Px+bN2+u8/PWOtXGOGqgJQiCIAhH0NLHpfj4ePTv3x8rV64EYLhBiYqKwqOPPopnn33WZP+pU6eipKQEP/30k/TewIED0atXL6xevdqq72zpNiUIgiBcD2vHJptcd61Wi759+2LHjh3Se3q9Hjt27FBErpsrjDEUFRXBhucILQrST/p51g+QDUg/3/qtxZ76KampqYr9ASAxMbHOeiuNVT+FMcMUF/E41n5W/E5XryOg1+tRVFSEmpqaFqPJln4Sp3mItmgJmmx9bckGrqzJln6qqalBYWGhtG9L0NRY10BX0WRrP+n1ehQUFEjf7Qgd9WFzPPyJJ57Ap59+iq+++gqnT5/GvHnzUFJSgjlz5gAAZs2aheeee07av7KyEmlpaUhLS0NlZSWuXLmCtLQ0nD9/3tavbjCMMWluNY+QftLPs36AbED6+dZvLfbUT8nOzra53kpj1k+5cuUK1/VTiouLW5QmW/uptLSU2/opoqbS0lLk5+e3KE229NONGzfAGL/1Uy5fvtyiNNnaT9XV1bh48SIYYw3WlJeXB2uwKf1bZOXKlVi+fDmys7PRq1cvrFixAvHx8QCAYcOGITY2FmvWrAEAZGZmok2bNibHGDp0KHbv3m3V91FKGEEQBNGcaMnjkj31U7RaLb766itMnz5deu+jjz7CkiVLpBsVYxqrfkpLr41AmkgTaSJNpKnpNBUUFKBVq1b1jveaugZWSzzyyCN45JFHzG4zdpRjY2MlYzobxhgKCwvh6+sLQRCc3Zwmh/STfp71A2QD0s+3fmsJDAyEWq02cYbFQofmCA0NtWl/ANDpdNDpdCbvi0Vl5H0kLzRT32vGDGn+4s2PLZ8Vb8wa87U9mmx5Ld4Eivpbgibj1/W1RdTfkH5tTprsabs5G7iyJlv6SRAE6VrfUjQ15jXQFTTZ81q0QUPbLj9mXXBVEo4xhoqKimbj5Dc1pJ/086wfIBuQfr71W4s99VMSEhIU+wPA9u3bnVJvhfd+Jv186wfIBqSfb/2Ac2xgV/p3U9OS0+wIgiAI16Olj0vr1q3D7Nmz8fHHH2PAgAF477338O9//xtnzpxBSEgIZs2ahYiICCxduhSAYUmtoUOHYtmyZRg3bhySk5Px+uuvO2VJLYIgCIJwFI1S/dvVYYwhPz+f2yc3pJ/086wfIBuQfr7128LUqVPx1ltv4eWXX0avXr2QlpaGn3/+WSpGlpWVJRV4AYDbbrsN3333HT755BPExcVhw4YN2LRpk9UOtSPhvZ9JP9/6AbIB6edbP+AcG9g1p9pVYYyhuroajDEu59ORftLPs36AbED6+dZvK7bUTwGAu+66C3fddVcjt6p+eO9n0s+3foBsQPr51g84xwaU/k0QBEEQNkLjkuMhmxIEQRDNDUr/NgNjDDdv3uQ2HYL0k36e9QNkA9LPt35e4L2fST/f+gGyAennWz/gHBtw5VQTBEEQBEEQBEEQhCOh9G+CIAiCsBEalxwP2ZQgCIJoblg7NrlEoTLR7y8sLGzQcfR6PW7duoVWrVpZvZB3S4L0k36e9QNkA9LvOP3ieOQCz6VdBhrrHQPp51s/QDYg/XzrB5wz3ruEU11UVAQAiIqKcnJLCIIgCKKWoqIi+Pn5ObsZLQIa6wmCIIjmSn3jvUukf+v1ely9ehU+Pj4NKoteWFiIqKgoXLp0icvUMtJP+nnWD5ANSL/j9DPGUFRUhPDwcG4jAY6GxnrHQPr51g+QDUg/3/oB54z3LhGpVqlUiIyMdNjxfH19uT3JANJP+vnWD5ANSL9j9FOE2rHQWO9YSD/f+gGyAennWz/QtOM9PV4nCIIgCIIgCIIgCDshp5ogCIIgCIIgCIIg7IQrp1qn02HRokXQ6XTObopTIP2kn2f9ANmA9POtnxd472fSz7d+gGxA+vnWDzjHBi5RqIwgCIIgCIIgCIIgmiNcRaoJgiAIgiAIgiAIwpGQU00QBEEQBEEQBEEQdkJONUEQBEEQBEEQBEHYCTnVBEEQBEEQBEEQBGEn3DjVH374IWJjY+Hu7o74+HgcPHjQ2U1qFBYvXgxBEBT/OnfuLG0vLy/H/Pnz0bp1a3h7e+POO+9ETk6OE1vccH777TdMmDAB4eHhEAQBmzZtUmxnjOHll19GWFgYPDw8MHLkSJw7d06xz82bNzFz5kz4+vrC398f9957L4qLi5tQhf3Up/+ee+4xOSfGjBmj2MeV9S9duhT9+/eHj48PgoODMXHiRKSnpyv2sea8z8rKwrhx4+Dp6Yng4GA89dRTqK6ubkopdmGN/mHDhpmcAw899JBiH1fVv2rVKvTs2RO+vr7w9fVFQkICtm3bJm1vyX1PmMLLWA/wN97TWE9jPY31NNY357GeC6d63bp1eOKJJ7Bo0SL8/vvviIuLQ2JiInJzc53dtEahW7duuHbtmvRvz5490rYFCxbgxx9/xPr165GSkoKrV69i8uTJTmxtwykpKUFcXBw+/PBDs9vffPNNrFixAqtXr8aBAwfg5eWFxMRElJeXS/vMnDkTp06dwvbt2/HTTz/ht99+wwMPPNBUEhpEffoBYMyYMYpzYu3atYrtrqw/JSUF8+fPx/79+7F9+3ZUVVVh9OjRKCkpkfap77yvqanBuHHjUFlZiX379uGrr77CmjVr8PLLLztDkk1Yox8A7r//fsU58Oabb0rbXFl/ZGQkli1bhiNHjuDw4cO44447kJSUhFOnTgFo2X1PKOFtrAf4Gu9prKexnsZ6Guub9VjPOGDAgAFs/vz50t81NTUsPDycLV261ImtahwWLVrE4uLizG7Lz89nbm5ubP369dJ7p0+fZgBYampqE7WwcQHAfvjhB+lvvV7PQkND2fLly6X38vPzmU6nY2vXrmWMMfbHH38wAOzQoUPSPtu2bWOCILArV640WdsdgbF+xhibPXs2S0pKsviZlqSfMcZyc3MZAJaSksIYs+6837p1K1OpVCw7O1vaZ9WqVczX15dVVFQ0rYAGYqyfMcaGDh3KHnvsMYufaUn6GWOsVatW7LPPPuOu73mHp7GeMb7HexrraaynsZ7G+uY21rf4SHVlZSWOHDmCkSNHSu+pVCqMHDkSqampTmxZ43Hu3DmEh4ejbdu2mDlzJrKysgAAR44cQVVVlcIWnTt3RnR0dIu1RUZGBrKzsxWa/fz8EB8fL2lOTU2Fv78/+vXrJ+0zcuRIqFQqHDhwoMnb3Bjs3r0bwcHB6NSpE+bNm4e8vDxpW0vTX1BQAAAICAgAYN15n5qaih49eiAkJETaJzExEYWFhdJTUFfBWL/It99+i8DAQHTv3h3PPfccSktLpW0tRX9NTQ2Sk5NRUlKChIQE7vqeZ3gc6wEa70VorDdAYz0/13sa65vfWK9xyFGaMTdu3EBNTY3CiAAQEhKCM2fOOKlVjUd8fDzWrFmDTp064dq1a1iyZAluv/12nDx5EtnZ2dBqtfD391d8JiQkBNnZ2c5pcCMj6jLX/+K27OxsBAcHK7ZrNBoEBAS0CLuMGTMGkydPRps2bXDhwgU8//zzGDt2LFJTU6FWq1uUfr1ej8cffxyDBg1C9+7dAcCq8z47O9vsOSJucxXM6QeAGTNmICYmBuHh4Th+/DieeeYZpKen4/vvvwfg+vpPnDiBhIQElJeXw9vbGz/88AO6du2KtLQ0bvqed3gb6wEa7+XQWE9jPY31NNY7u+9bvFPNG2PHjpVe9+zZE/Hx8YiJicG///1veHh4OLFlhLOYNm2a9LpHjx7o2bMn2rVrh927d2PEiBFObJnjmT9/Pk6ePKmYV8gTlvTL58z16NEDYWFhGDFiBC5cuIB27do1dTMdTqdOnZCWloaCggJs2LABs2fPRkpKirObRRCNCo33hBwa6/mBxvrmOda3+PTvwMBAqNVqkwpwOTk5CA0NdVKrmg5/f3907NgR58+fR2hoKCorK5Gfn6/YpyXbQtRVV/+HhoaaFLKprq7GzZs3W6Rd2rZti8DAQJw/fx5Ay9H/yCOP4KeffsKuXbsQGRkpvW/NeR8aGmr2HBG3uQKW9JsjPj4eABTngCvr12q1aN++Pfr27YulS5ciLi4O77//Pjd9T9BYD/A93tNYbwqN9QZa2vWexvrmO9a3eKdaq9Wib9++2LFjh/SeXq/Hjh07kJCQ4MSWNQ3FxcW4cOECwsLC0LdvX7i5uSlskZ6ejqysrBZrizZt2iA0NFShubCwEAcOHJA0JyQkID8/H0eOHJH22blzJ/R6vXRBaklcvnwZeXl5CAsLA+D6+hljeOSRR/DDDz9g586daNOmjWK7Ned9QkICTpw4objh2L59O3x9fdG1a9emEWIn9ek3R1paGgAozgFX1W8OvV6PioqKFt/3RC28j/UA3+M9jfWm0Fjfsq73NNab0uzGeoeUO2vmJCcnM51Ox9asWcP++OMP9sADDzB/f39FBbiWwsKFC9nu3btZRkYG27t3Lxs5ciQLDAxkubm5jDHGHnroIRYdHc127tzJDh8+zBISElhCQoKTW90wioqK2NGjR9nRo0cZAPbOO++wo0ePsosXLzLGGFu2bBnz9/dnmzdvZsePH2dJSUmsTZs2rKysTDrGmDFjWO/evdmBAwfYnj17WIcOHdj06dOdJckm6tJfVFTEnnzySZaamsoyMjLYr7/+yvr06cM6dOjAysvLpWO4sv558+YxPz8/tnv3bnbt2jXpX2lpqbRPfed9dXU16969Oxs9ejRLS0tjP//8MwsKCmLPPfecMyTZRH36z58/z1555RV2+PBhlpGRwTZv3szatm3LhgwZIh3DlfU/++yzLCUlhWVkZLDjx4+zZ599lgmCwH755RfGWMvue0IJT2M9Y/yN9zTW01hPYz2N9c15rOfCqWaMsQ8++IBFR0czrVbLBgwYwPbv3+/sJjUKU6dOZWFhYUyr1bKIiAg2depUdv78eWl7WVkZe/jhh1mrVq2Yp6cnmzRpErt27ZoTW9xwdu3axQCY/Js9ezZjzLDUxksvvcRCQkKYTqdjI0aMYOnp6Ypj5OXlsenTpzNvb2/m6+vL5syZw4qKipygxnbq0l9aWspGjx7NgoKCmJubG4uJiWH333+/yU2mK+s3px0A+/LLL6V9rDnvMzMz2dixY5mHhwcLDAxkCxcuZFVVVU2sxnbq05+VlcWGDBnCAgICmE6nY+3bt2dPPfUUKygoUBzHVfXPnTuXxcTEMK1Wy4KCgtiIESOkQZaxlt33hCm8jPWM8Tfe01hPYz2N9TTWN+exXmCMMcfEvAmCIAiCIAiCIAiCL1r8nGqCIAiCIAiCIAiCaCzIqSYIgiAIgiAIgiAIOyGnmiAIgiAIgiAIgiDshJxqgiAIgiAIgiAIgrATcqoJgiAIgiAIgiAIwk7IqSYIgiAIgiAIgiAIOyGnmiAIgiAIgiAIgiDshJxqgiAIgiAIgiAIgrATcqoJgqiX3bt3QxAE5OfnO7spBEEQBEE0AjTWE4T9kFNNEARBEARBEARBEHZCTjVBEARBEARBEARB2Ak51QThAuj1eixduhRt2rSBh4cH4uLisGHDBgC16VpbtmxBz5494e7ujoEDB+LkyZOKY2zcuBHdunWDTqdDbGws3n77bcX2iooKPPPMM4iKioJOp0P79u3x+eefK/Y5cuQI+vXrB09PT9x2221IT09vXOEEQRAEwQk01hOE60JONUG4AEuXLsXXX3+N1atX49SpU1iwYAHuvvtupKSkSPs89dRTePvtt3Ho0CEEBQVhwoQJqKqqAmAYIKdMmYJp06bhxIkTWLx4MV566SWsWbNG+vysWbOwdu1arFixAqdPn8bHH38Mb29vRTteeOEFvP322zh8+DA0Gg3mzp3bJPoJgiAIoqVDYz1BuDCMIIhmTXl5OfP09GT79u1TvH/vvfey6dOns127djEALDk5WdqWl5fHPDw82Lp16xhjjM2YMYONGjVK8fmnnnqKde3alTHGWHp6OgPAtm/fbrYN4nf8+uuv0ntbtmxhAFhZWZlDdBIEQRAEr9BYTxCuDUWqCaKZc/78eZSWlmLUqFHw9vaW/n399de4cOGCtF9CQoL0OiAgAJ06dcLp06cBAKdPn8agQYMUxx00aBDOnTuHmpoapKWlQa1WY+jQoXW2pWfPntLrsLAwAEBubm6DNRIEQRAEz9BYTxCujcbZDSAIom6Ki4sBAFu2bEFERIRim06nUwy29uLh4WHVfm5ubtJrQRAAGOaAEQRBEARhPzTWE4RrQ5FqgmjmdO3aFTqdDllZWWjfvr3iX1RUlLTf/v37pde3bt3C2bNn0aVLFwBAly5dsHfvXsVx9+7di44dO0KtVqNHjx7Q6/WKeVsEQRAEQTQNNNYThGtDkWqCaOb4+PjgySefxIIFC6DX6zF48GAUFBRg79698PX1RUxMDADglVdeQevWrRESEoIXXngBgYGBmDhxIgBg4cKF6N+/P1599VVMnToVqampWLlyJT766CMAQGxsLGbPno25c+dixYoViIuLw8WLF5Gbm4spU6Y4SzpBEARBcAGN9QTh4jh7UjdBEPWj1+vZe++9xzp16sTc3NxYUFAQS0xMZCkpKVJhkR9//JF169aNabVaNmDAAHbs2DHFMTZs2MC6du3K3NzcWHR0NFu+fLlie1lZGVuwYAELCwtjWq2WtW/fnn3xxReMsdriJbdu3ZL2P3r0KAPAMjIyGls+QRAEQbR4aKwnCNdFYIwxZzr1BEE0jN27d2P48OG4desW/P39nd0cgiAIgiAcDI31BNG8oTnVBEEQBEEQBEEQBGEn5FQTBEEQBEEQBEEQhJ1Q+jdBEARBEARBEARB2AlFqgmCIAiCIAiCIAjCTsipJgiCIAiCIAiCIAg7IaeaIAiCIAiCIAiCIOyEnGqCIAiCIAiCIAiCsBNyqgmCIAiCIAiCIAjCTsipJgiCIAiCIAiCIAg7IaeaIAiCIAiCIAiCIOyEnGqCIAiCIAiCIAiCsJP/D3yqheyf/+80AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 12))\n", "plt.subplot(2, 2, 1)\n", "plt.title(\"Regular Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"red\")\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.title(\"Regular <PERSON> Mean <PERSON>ce\")\n", "x = [i + 1 for i in range(len(metric_values))]\n", "y = metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"red\")\n", "\n", "plt.subplot(2, 2, 3)\n", "plt.title(\"AMP Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(amp_epoch_loss_values))]\n", "y = amp_epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"green\")\n", "\n", "plt.subplot(2, 2, 4)\n", "plt.title(\"AMP Val Mean Dice\")\n", "x = [i + 1 for i in range(len(amp_metric_values))]\n", "y = amp_metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"green\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot total time and every epoch time"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Total Train Time(300 epochs)\")\n", "plt.bar(\"regular\", total_time, 1, label=\"Regular training\", color=\"red\")\n", "plt.bar(\"AMP\", amp_total_time, 1, label=\"AMP training\", color=\"green\")\n", "plt.ylabel(\"secs\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Epoch Time\")\n", "x = [i + 1 for i in range(len(epoch_times))]\n", "plt.xlabel(\"epoch\")\n", "plt.ylabel(\"secs\")\n", "plt.plot(x, epoch_times, label=\"Regular training\", color=\"red\")\n", "plt.plot(x, amp_epoch_times, label=\"AMP training\", color=\"green\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot total time to achieve metrics"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def get_best_metric_time(threshold, best_values):\n", "    for i, v in enumerate(best_values[0]):\n", "        if v > threshold:\n", "            return best_values[2][i]\n", "    return -1\n", "\n", "\n", "def get_best_metric_epochs(threshold, best_values):\n", "    for i, v in enumerate(best_values[0]):\n", "        if v > threshold:\n", "            return best_values[1][i]\n", "    return -1\n", "\n", "\n", "plt.set_loglevel(\"WARNING\")\n", "\n", "plt.figure(\"train\", (18, 6))\n", "plt.subplot(1, 3, 1)\n", "plt.title(\"Metrics Time\")\n", "plt.xlabel(\"secs\")\n", "plt.ylabel(\"best mean_dice\")\n", "plt.plot(best[2], best[0], label=\"Regular training\", color=\"red\")\n", "plt.plot(amp_best[2], amp_best[0], label=\"AMP training\", color=\"green\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.title(\"Typical Metrics Time\")\n", "plt.xlabel(\"best mean_dice\")\n", "plt.ylabel(\"secs\")\n", "plt.bar(\n", "    \"0.80\",\n", "    get_best_metric_time(0.8, best),\n", "    0.5,\n", "    label=\"Regular training\",\n", "    color=\"red\",\n", ")\n", "plt.bar(\n", "    \"0.80 \",\n", "    get_best_metric_time(0.8, amp_best),\n", "    0.5,\n", "    label=\"AMP training\",\n", "    color=\"green\",\n", ")\n", "plt.bar(\"0.85\", get_best_metric_time(0.85, best), 0.5, color=\"red\")\n", "plt.bar(\"0.85 \", get_best_metric_time(0.85, amp_best), 0.5, color=\"green\")\n", "plt.bar(\"0.90\", get_best_metric_time(0.9, best), 0.5, color=\"red\")\n", "plt.bar(\"0.90 \", get_best_metric_time(0.9, amp_best), 0.5, color=\"green\")\n", "plt.bar(\"0.93\", get_best_metric_time(0.93, best), 0.5, color=\"red\")\n", "plt.bar(\"0.93 \", get_best_metric_time(0.93, amp_best), 0.5, color=\"green\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.title(\"Typical Metrics Epochs\")\n", "plt.xlabel(\"best mean_dice\")\n", "plt.ylabel(\"epochs\")\n", "plt.bar(\n", "    \"0.80\",\n", "    get_best_metric_epochs(0.8, best),\n", "    0.5,\n", "    label=\"Regular training\",\n", "    color=\"red\",\n", ")\n", "plt.bar(\n", "    \"0.80 \",\n", "    get_best_metric_epochs(0.8, amp_best),\n", "    0.5,\n", "    label=\"AMP training\",\n", "    color=\"green\",\n", ")\n", "plt.bar(\"0.85\", get_best_metric_epochs(0.85, best), 0.5, color=\"red\")\n", "plt.bar(\"0.85 \", get_best_metric_epochs(0.85, amp_best), 0.5, color=\"green\")\n", "plt.bar(\"0.90\", get_best_metric_epochs(0.9, best), 0.5, color=\"red\")\n", "plt.bar(\"0.90 \", get_best_metric_epochs(0.9, amp_best), 0.5, color=\"green\")\n", "plt.bar(\"0.93\", get_best_metric_epochs(0.93, best), 0.5, color=\"red\")\n", "plt.bar(\"0.93 \", get_best_metric_epochs(0.93, amp_best), 0.5, color=\"green\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}