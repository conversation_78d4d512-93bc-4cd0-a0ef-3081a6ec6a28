---
name: Bug report
about: Create a report to help us improve
title: "[BUG]"
labels: bug
assignees: ''

---

**Describe the bug**
<!--
A clear and concise description of what the bug is.
Example: "Joblib error thrown when calling fit on VietorisRipsPersistence."
-->

**To reproduce**
<!--
Steps to reproduce the behavior:
1. Import '....'
2. Apply to '....'
3. See error

If the code is too long, feel free to put it in a public gist and link
it in the issue: https://gist.github.com.
-->

**Expected behavior**
<!--
A clear and concise description of what you expected to happen.
Example: "No error should be thrown."
-->

**Actual behaviour**
<!--
Please paste or specifically describe the actual output or traceback.
Alternatively, add screenshots to help explain your problem.
-->

**Versions**
<!--
Please run the following snippet and paste the output below.
import platform; print(platform.platform())
import sys; print("Python", sys.version)
import numpy; print("NumPy", numpy.__version__)
import scipy; print("SciPy", scipy.__version__)
import joblib; print("Joblib", joblib.__version__)
import sklearn; print("Scikit-learn", sklearn.__version__)
import gtda; print("Giotto-tda", gtda.__version__)
-->

**Additional context**
<!--
Add any other context about the problem here.
-->

<!-- Thanks for contributing! -->
