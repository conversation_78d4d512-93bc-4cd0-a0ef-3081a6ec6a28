#!/usr/bin/env python
# coding: utf-8

"""
HCC Segmentation Training Script using SwinUNETR
Modified for single-channel HCC images with binary segmentation
"""
# pip install pytorch-lightning monai matplotlib nibabel pandas
import pip
pip.main(['install', 'monai'])
pip.main(['install', 'monai[nibabel, tqdm, skimage, einops]'])
pip.main(['install', 'matplotlib'])
pip.main(['install', 'pytorch_lightning'])
pip.main(['install', 'pandas'])


import pytorch_lightning
import monai
import numpy as np
import pandas as pd
import os
import glob
import json
import tempfile
import torch
import matplotlib.pyplot as plt
import nibabel as nib
import shutil
import psutil

from typing import Union
from PIL import Image
from monai.utils import set_determinism
from monai.utils.enums import MetricReduction
from monai.transforms import (
    AsDiscrete,
    ScaleIntensityRangePercentilesd,
    EnsureChannelFirstd,
    Compose,
    CropForegroundd,
    LoadImaged,
    Orientationd,   
    ScaleIntensityRanged,
    Spacingd,
    EnsureType,
    MapTransform,
    Activations,
    Activationsd,
    Invertd,
    NormalizeIntensityd,
    RandFlipd,
    RandScaleIntensityd,
    RandShiftIntensityd,
    RandSpatialCropd,
    RandCropByPosNegLabeld,#注意一定要用这个，不能用RandSpatialCropd
    EnsureTyped,
    SpatialPadd,
    SaveImage,
    RandRotate90d,
)
from monai.networks.utils import one_hot
from monai.networks.nets import SwinUNETR,UNETR,SegResNet
from monai.networks.layers import Norm
from monai.metrics import DiceMetric,HausdorffDistanceMetric,ConfusionMatrixMetric, compute_hausdorff_distance, CumulativeIterationMetric
from monai.losses import DiceCELoss,DiceLoss, TverskyLoss, FocalLoss
from monai.inferers import sliding_window_inference

from monai.data import PersistentDataset, list_data_collate, decollate_batch, DataLoader, load_decathlon_datalist, CacheDataset

from monai.config import print_config
from monai.apps import download_and_extract,DecathlonDataset
from monai.handlers.utils import from_engine

from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from pytorch_lightning.callbacks.early_stopping import EarlyStopping
from pytorch_lightning.callbacks.timer import Timer

# Setup environment
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.enabled = True
print_config()

# Setup data directory - 使用数据盘而不是系统盘
# 建议将模型和日志保存到数据盘，避免系统盘满
data_disk_path = '/root/autodl-tmp'  # 数据盘路径
results_dir = os.path.join(data_disk_path, 'results')
os.makedirs(results_dir, exist_ok=True)
print(f"Results directory: {results_dir}")

# 创建子目录
models_dir = os.path.join(results_dir, 'models')
logs_dir = os.path.join(results_dir, 'logs')
metrics_dir = os.path.join(results_dir, 'metrics')
os.makedirs(models_dir, exist_ok=True)
os.makedirs(logs_dir, exist_ok=True)
os.makedirs(metrics_dir, exist_ok=True)
print(f"Models directory: {models_dir}")
print(f"Logs directory: {logs_dir}")
print(f"Metrics directory: {metrics_dir}")

roi_size = (64, 64, 32)  # Reduced z-dimension to accommodate smaller images
sw_batch_size = 4

def check_disk_space(path="/", min_free_gb=5):
    """检查磁盘空间，如果剩余空间不足则发出警告"""
    try:
        disk_usage = shutil.disk_usage(path)
        free_gb = disk_usage.free / (1024**3)
        total_gb = disk_usage.total / (1024**3)
        used_gb = disk_usage.used / (1024**3)

        print(f"磁盘空间状态 ({path}):")
        print(f"  总空间: {total_gb:.2f} GB")
        print(f"  已使用: {used_gb:.2f} GB")
        print(f"  剩余空间: {free_gb:.2f} GB")

        if free_gb < min_free_gb:
            print(f"⚠️  警告: 剩余磁盘空间不足 {min_free_gb} GB!")
            print("   建议清理磁盘空间或更改保存路径")
            return False
        return True
    except Exception as e:
        print(f"检查磁盘空间时出错: {e}")
        return True

def cleanup_old_logs(logs_directory, keep_latest=3):
    """清理旧的日志文件，只保留最新的几个版本"""
    try:
        lightning_logs_dir = os.path.join(logs_directory, 'lightning_logs')
        if os.path.exists(lightning_logs_dir):
            version_dirs = [d for d in os.listdir(lightning_logs_dir)
                           if d.startswith('version_') and os.path.isdir(os.path.join(lightning_logs_dir, d))]
            version_dirs.sort(key=lambda x: int(x.split('_')[1]))

            if len(version_dirs) > keep_latest:
                dirs_to_remove = version_dirs[:-keep_latest]
                for dir_name in dirs_to_remove:
                    dir_path = os.path.join(lightning_logs_dir, dir_name)
                    shutil.rmtree(dir_path)
                    print(f"已清理旧日志目录: {dir_path}")
    except Exception as e:
        print(f"清理日志时出错: {e}")

def save_training_metrics(net, metrics_directory):
    """保存训练指标到CSV文件"""
    try:
        import pandas as pd

        # 保存训练指标（每个epoch）
        if len(net.epoch_loss_values) > 0:
            train_data = {
                'epoch': list(range(1, len(net.epoch_loss_values) + 1)),
                'train_loss': net.epoch_loss_values,
                'train_dice': net.train_dice_values if len(net.train_dice_values) > 0 else [0] * len(net.epoch_loss_values)
            }
            train_df = pd.DataFrame(train_data)
            train_path = os.path.join(metrics_directory, 'training_metrics.csv')
            train_df.to_csv(train_path, index=False)
            print(f"📊 Training metrics saved to: {train_path}")

        # 保存验证指标（每check_val个epoch）
        if len(net.metric_values) > 0:
            # 计算验证epoch（每check_val个epoch验证一次）
            val_epochs = [i * net.check_val for i in range(1, len(net.metric_values) + 1)]

            val_data = {
                'epoch': val_epochs,
                'val_loss': net.val_loss_values if len(net.val_loss_values) > 0 else [0] * len(net.metric_values),
                'val_dice_overall': net.metric_values,
                'val_dice_hcc': net.metric_values_hcc,
                'val_hausdorff_hcc': net.haursdoff_values_hcc
            }
            val_df = pd.DataFrame(val_data)
            val_path = os.path.join(metrics_directory, 'validation_metrics.csv')
            val_df.to_csv(val_path, index=False)
            print(f"📈 Validation metrics saved to: {val_path}")

        # 保存详细的训练摘要
        summary_data = {
            'metric': [
                'best_val_dice', 'best_val_epoch', 'final_epoch',
                'final_train_loss', 'final_train_dice',
                'final_val_loss', 'final_val_dice'
            ],
            'value': [
                net.best_val_dice,
                net.best_val_epoch,
                net.current_epoch,
                net.epoch_loss_values[-1] if len(net.epoch_loss_values) > 0 else 0,
                net.train_dice_values[-1] if len(net.train_dice_values) > 0 else 0,
                net.val_loss_values[-1] if len(net.val_loss_values) > 0 else 0,
                net.metric_values[-1] if len(net.metric_values) > 0 else 0
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(metrics_directory, 'training_summary.csv')
        summary_df.to_csv(summary_path, index=False)
        print(f"📋 Training summary saved to: {summary_path}")

        # 打印最终统计信息
        print("\n" + "=" * 60)
        print("📊 TRAINING COMPLETED - FINAL METRICS")
        print("=" * 60)
        print(f"🏆 Best Validation Dice: {net.best_val_dice:.4f} (Epoch {net.best_val_epoch})")
        if len(net.epoch_loss_values) > 0:
            print(f"📉 Final Training Loss: {net.epoch_loss_values[-1]:.4f}")
        if len(net.train_dice_values) > 0:
            print(f"🎯 Final Training Dice: {net.train_dice_values[-1]:.4f}")
        if len(net.val_loss_values) > 0:
            print(f"📊 Final Validation Loss: {net.val_loss_values[-1]:.4f}")
        if len(net.metric_values) > 0:
            print(f"🏥 Final Validation Dice: {net.metric_values[-1]:.4f}")
        print(f"⏱️  Total Epochs: {net.current_epoch}")
        print("=" * 60)

    except Exception as e:
        print(f"保存训练指标时出错: {e}")

def save_metrics_realtime(net, metrics_directory, epoch):
    """实时保存指标，每个epoch后调用"""
    try:
        import pandas as pd

        # 实时保存训练指标
        if len(net.epoch_loss_values) > 0:
            train_data = {
                'epoch': list(range(1, len(net.epoch_loss_values) + 1)),
                'train_loss': net.epoch_loss_values,
                'train_dice': net.train_dice_values if len(net.train_dice_values) > 0 else [0] * len(net.epoch_loss_values)
            }
            train_df = pd.DataFrame(train_data)
            train_path = os.path.join(metrics_directory, 'training_metrics_realtime.csv')
            train_df.to_csv(train_path, index=False)

        # 实时保存验证指标（如果有的话）
        if len(net.metric_values) > 0:
            val_epochs = [i * net.check_val for i in range(1, len(net.metric_values) + 1)]
            val_data = {
                'epoch': val_epochs,
                'val_loss': net.val_loss_values if len(net.val_loss_values) > 0 else [0] * len(net.metric_values),
                'val_dice_overall': net.metric_values,
                'val_dice_hcc': net.metric_values_hcc,
                'val_hausdorff_hcc': net.haursdoff_values_hcc
            }
            val_df = pd.DataFrame(val_data)
            val_path = os.path.join(metrics_directory, 'validation_metrics_realtime.csv')
            val_df.to_csv(val_path, index=False)

    except Exception as e:
        print(f"实时保存指标时出错: {e}")

def create_dataset_json():
    """Create JSON file for HCC dataset"""
    # HCC dataset paths
    image_dir = '/root/autodl-tmp/120HCC/image/ap'
    mask_dir = '/root/autodl-tmp/120HCC/mask/ap'

    # Get all image files
    image_files = glob.glob(os.path.join(image_dir, "*.nii.gz"))
    image_files.sort()

    # Create corresponding mask file paths
    images, labels = [], []
    for img_file in image_files:
        # Extract patient name from image file
        img_name = os.path.basename(img_file)
        patient_name = img_name.replace('-ap.nii.gz', '')
        
        # Create corresponding mask file path
        mask_file = os.path.join(mask_dir, f"{patient_name}-ap-mask.nii.gz")
        
        # Only add if both image and mask exist
        if os.path.exists(mask_file):
            images.append(img_file)
            labels.append(mask_file)

    print(f"Found {len(images)} image-mask pairs")

    # Create dataset structure
    file_list = []
    for i in range(len(images)):
        file_list.append({"image": images[i], "label": labels[i]})
        
    file_json = {
        "training": file_list
    }

    # Save JSON file
    json_path = '/root/autodl-tmp/hcc_dataset.json'
    with open(json_path, 'w') as json_file:
        json.dump(file_json, json_file, indent=2)
    
    print(f"Dataset JSON saved to: {json_path}")
    return json_path, len(images)

def inspect_dataset_statistics(dataset_json_path):
    """Inspect dataset statistics to understand the data distribution"""
    with open(dataset_json_path, 'r') as f:
        data = json.load(f)

    training_data = data['training']
    print(f"\n📊 Dataset Statistics:")
    print(f"Total samples: {len(training_data)}")

    lesion_sizes = []
    image_shapes = []

    for i, sample in enumerate(training_data[:10]):  # Check first 10 samples
        try:
            mask_data = nib.load(sample['label']).get_fdata()
            image_data = nib.load(sample['image']).get_fdata()

            lesion_pixels = np.count_nonzero(mask_data)
            total_pixels = mask_data.size
            lesion_ratio = lesion_pixels / total_pixels

            lesion_sizes.append(lesion_ratio)
            image_shapes.append(image_data.shape)

            if i < 3:  # Print details for first 3 samples
                print(f"  Sample {i}: shape={image_data.shape}, lesion_ratio={lesion_ratio:.6f}")

        except Exception as e:
            print(f"  Error loading sample {i}: {e}")

    if lesion_sizes:
        print(f"Average lesion ratio: {np.mean(lesion_sizes):.6f}")
        print(f"Min lesion ratio: {np.min(lesion_sizes):.6f}")
        print(f"Max lesion ratio: {np.max(lesion_sizes):.6f}")

    print(f"Common image shapes: {set(image_shapes)}")

def visualize_sample_data(dataset_json_path, sample_idx=15):
    """Visualize a sample from the dataset"""
    with open(dataset_json_path, 'r') as f:
        data = json.load(f)
    
    training_data = data['training']
    
    if sample_idx < len(training_data):
        sample = training_data[sample_idx]
        image_data = nib.load(sample['image']).get_fdata()
        mask_data = nib.load(sample['label']).get_fdata()

        slice_number = image_data.shape[2] // 2  # Middle slice
        print(f'Sample {sample_idx}:')
        print(f'slice = {slice_number}')
        print(f'image shape = {image_data.shape}')
        print(f'mask shape = {mask_data.shape}')

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        ax1.imshow(image_data[:,:,slice_number], cmap='gray')
        ax1.set_title('HCC Image (AP phase)')
        ax2.imshow(mask_data[:,:,slice_number], cmap='jet', alpha=0.7)
        ax2.set_title('HCC Mask')

        # 保存到results目录
        viz_path = os.path.join(results_dir, 'sample_visualization.png')
        plt.savefig(viz_path, dpi=150, bbox_inches='tight')
        print(f"Sample visualization saved to: {viz_path}")
        plt.show()
    else:
        print(f"Index {sample_idx} is out of range. Available images: {len(training_data)}")

class HCCSegmentationNet(pytorch_lightning.LightningModule):
    def __init__(self):
        super().__init__()

        self._model = SwinUNETR(
            in_channels=1,  # Single channel for HCC images
            out_channels=2,  # Binary segmentation: background + HCC
            img_size=(64, 64, 32),  # Match the ROI size
            feature_size=24,
            drop_rate=0.0,
            attn_drop_rate=0.0,
            dropout_path_rate=0.0,
            use_checkpoint=False,
        ).to(device)

        # Use Tversky loss for better handling of class imbalance
        # Tversky loss with alpha=0.3, beta=0.7 focuses more on recall (finding lesions)
        self.loss_function = TverskyLoss(
            to_onehot_y=True,
            softmax=True,
            alpha=0.3,
            beta=0.7,
            include_background=False
        )
        self.post_pred = AsDiscrete(argmax=True, to_onehot=2)  # 2 classes
        self.post_label = AsDiscrete(to_onehot=2)  # 2 classes

        self.dice_metric = DiceMetric(include_background=False, reduction="mean")
        self.dice_metric_batch = DiceMetric(include_background=False, reduction="mean_batch")
        self.haursdoff = HausdorffDistanceMetric(include_background=False, distance_metric='euclidean',
                                                 percentile=None, directed=False, reduction="mean_batch", get_not_nans=False)
        self.check_val = 2  # Validate every 2 epochs
        self.best_val_dice = 0
        self.best_val_epoch = 0

        # 训练指标存储
        self.epoch_loss_values = []  # 每个epoch的训练损失
        self.train_dice_values = []  # 每个epoch的训练dice

        # 验证指标存储
        self.val_loss_values = []    # 每个验证epoch的验证损失
        self.metric_values = []      # Overall dice
        self.metric_values_hcc = []  # HCC dice
        self.haursdoff_values_hcc = []  # HCC Hausdorff distance

        self.validation_step_outputs = []
        self.training_step_outputs = []

    def forward(self, x):
        return self._model(x)

    def prepare_data(self):
        # setting up the correct data path
        datasets = "/root/autodl-tmp/hcc_dataset.json"
        datalist = load_decathlon_datalist(datasets, True, "training")

        # Split data into train and validation sets (80/20 split)
        split_idx = int(0.8 * len(datalist))
        train_files, val_files = datalist[:split_idx], datalist[split_idx:]

        print(f"Training samples: {len(train_files)}")
        print(f"Validation samples: {len(val_files)}")

        # setting deterministic training for reproducibility
        set_determinism(seed=0)

        # defining the data transforms for single-channel HCC images
        # Use foreground-based cropping to ensure lesions are included
        train_transform = Compose([
            LoadImaged(keys=["image", "label"]),
            EnsureChannelFirstd(keys=["image", "label"]),
            Orientationd(keys=["image", "label"], axcodes="RAS"),
            Spacingd(keys=["image", "label"], pixdim=(1.0, 1.0, 1.0), mode=("bilinear", "nearest")),
            NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
            # Pad images to ensure minimum size before cropping
            SpatialPadd(keys=["image", "label"], spatial_size=[64, 64, 32], mode="constant"),
            # Use positive/negative sampling to ensure lesions are included
            RandCropByPosNegLabeld(
                keys=["image", "label"],
                label_key="label",
                spatial_size=[64, 64, 32],  # Match the ROI size
                pos=2,  # 2 positive samples (with lesions)
                neg=1,  # 1 negative sample (without lesions)
                num_samples=3,  # Total 3 samples per image
                image_key="image",
                image_threshold=0,
            ),
            RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=0),
            RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=1),
            RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=2),
            RandScaleIntensityd(keys="image", factors=0.1, prob=0.5),
            RandShiftIntensityd(keys="image", offsets=0.1, prob=0.5),
        ])

        val_transform = Compose([
            LoadImaged(keys=["image", "label"]),
            EnsureChannelFirstd(keys=["image", "label"]),
            Orientationd(keys=["image", "label"], axcodes="RAS"),
            Spacingd(keys=["image", "label"], pixdim=(1.0, 1.0, 1.0), mode=("bilinear", "nearest")),
            NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
            # For validation, use center crop to ensure consistent evaluation
            SpatialPadd(keys=["image", "label"], spatial_size=[64, 64, 32], mode="constant"),
        ])

        # Use Dataset without caching to avoid disk space issues
        from monai.data import Dataset
        self.train_ds = Dataset(data=train_files, transform=train_transform)
        self.val_ds = Dataset(data=val_files, transform=val_transform)

    def train_dataloader(self):
        return DataLoader(self.train_ds, batch_size=4, shuffle=True, num_workers=8,
                         pin_memory=True, collate_fn=list_data_collate)

    def val_dataloader(self):
        return DataLoader(self.val_ds, batch_size=1, pin_memory=True, shuffle=False, num_workers=4)

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(self._model.parameters(), lr=2e-4, weight_decay=1e-5)
        # Add learning rate scheduler
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50, eta_min=1e-6)
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss",
            },
        }

    def training_step(self, batch, batch_idx):
        images, labels = (batch["image"].cuda(), batch["label"].cuda())
        output = self.forward(images)
        loss = self.loss_function(output, labels)

        # 计算训练时的dice
        train_outputs = [self.post_pred(i) for i in decollate_batch(output)]
        train_labels = [self.post_label(i) for i in decollate_batch(labels)]

        # 计算dice指标
        self.dice_metric(y_pred=train_outputs, y=train_labels)
        train_dice = self.dice_metric.aggregate().item()
        self.dice_metric.reset()

        # 记录指标
        self.log("train_loss", loss.item(), on_step=True, on_epoch=True, prog_bar=True)
        self.log("train_dice", train_dice, on_step=True, on_epoch=True, prog_bar=True)

        self.training_step_outputs.append({
            "loss": loss,
            "dice": train_dice
        })

        return {"loss": loss, "train_dice": train_dice}

    def on_train_epoch_end(self):
        # 计算平均训练损失和dice
        avg_loss = torch.stack([x["loss"] for x in self.training_step_outputs]).mean()
        avg_dice = sum([x["dice"] for x in self.training_step_outputs]) / len(self.training_step_outputs)

        # 保存指标
        self.epoch_loss_values.append(avg_loss.detach().cpu().numpy())
        self.train_dice_values.append(avg_dice)

        # 打印训练指标
        print(f"Epoch {self.current_epoch} Training - Loss: {avg_loss:.4f}, Dice: {avg_dice:.4f}")

        # 实时保存训练指标
        save_metrics_realtime(self, metrics_dir, self.current_epoch)

        self.training_step_outputs.clear()

    def validation_step(self, batch, batch_idx):
        images, labels = batch["image"], batch["label"]
        outputs = sliding_window_inference(images, roi_size, sw_batch_size, self.forward)
        loss = self.loss_function(outputs, labels)
        outputs = [self.post_pred(i) for i in decollate_batch(outputs)]
        labels = [self.post_label(i) for i in decollate_batch(labels)]

        self.dice_metric(y_pred=outputs, y=labels)
        self.dice_metric_batch(y_pred=outputs, y=labels)
        self.haursdoff(y_pred=outputs, y=labels)

        d = {"val_loss": loss, "val_number": len(outputs)}
        self.validation_step_outputs.append(d)
        return {"val_loss": loss, "val_number": len(outputs)}

    def on_validation_epoch_end(self):
        val_loss, num_items = 0, 0
        for output in self.validation_step_outputs:
            val_loss += output["val_loss"].sum().item()
            num_items += output["val_number"]

        mean_val_dice = self.dice_metric.aggregate().item()
        self.metric_values.append(np.array(mean_val_dice))
        self.dice_metric.reset()

        # For binary segmentation, we only have HCC class (index 0 after excluding background)
        metric_batch = self.dice_metric_batch.aggregate()
        metric_hcc = metric_batch[0].item()  # HCC dice score
        self.metric_values_hcc.append(metric_hcc)
        self.dice_metric_batch.reset()

        # Hausdorff distance for HCC
        haursdoff = self.haursdoff.aggregate()
        hd_hcc = haursdoff[0].item()  # HCC Hausdorff distance
        self.haursdoff_values_hcc.append(hd_hcc)
        self.haursdoff.reset()

        mean_val_loss = torch.tensor(val_loss / num_items)

        # 保存验证损失
        self.val_loss_values.append(mean_val_loss.item())

        # 检查是否是最佳模型
        is_best = False
        if mean_val_dice > self.best_val_dice:
            self.best_val_dice = mean_val_dice
            self.best_val_epoch = self.current_epoch
            is_best = True
            # 保存最佳模型到models目录
            best_model_path = os.path.join(models_dir, "best_model_SwinUNETR_HCC.pt")
            torch.save(self._model, best_model_path)
            print(f"🎉 New best model saved to: {best_model_path}")

        # 详细打印验证指标
        print(f"Epoch {self.current_epoch} Validation:")
        print(f"  📊 Val Loss: {mean_val_loss:.4f}")
        print(f"  🎯 Val Dice Overall: {mean_val_dice:.4f}")
        print(f"  🏥 Val Dice HCC: {metric_hcc:.4f}")
        print(f"  📏 Val Hausdorff HCC: {hd_hcc:.4f}")
        print(f"  🏆 Best Dice: {self.best_val_dice:.4f} (Epoch {self.best_val_epoch})")
        if is_best:
            print(f"  ⭐ NEW BEST MODEL!")
        print("-" * 50)

        # 记录到Lightning日志
        self.log("val_loss", mean_val_loss.item(), on_epoch=True, prog_bar=True)
        self.log("val_dice", mean_val_dice, on_epoch=True, prog_bar=True)
        self.log("val_dice_hcc", metric_hcc, on_epoch=True, prog_bar=True)
        self.log("val_hausdorff", hd_hcc, on_epoch=True, prog_bar=True)

        # 实时保存验证指标
        save_metrics_realtime(self, metrics_dir, self.current_epoch)

        self.validation_step_outputs.clear()
        return {"log": {"val_dice": mean_val_dice, "val_loss": mean_val_loss}}


def test_data_pipeline(net):
    """Test the data pipeline to ensure it's working correctly"""
    print("\n🔍 Testing data pipeline...")

    # Test training dataloader
    train_loader = net.train_dataloader()
    val_loader = net.val_dataloader()

    print(f"Training batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")

    # Test one training batch
    try:
        batch = next(iter(train_loader))
        images = batch["image"]
        labels = batch["label"]

        print(f"Training batch - Images shape: {images.shape}, Labels shape: {labels.shape}")
        print(f"Images range: [{images.min():.3f}, {images.max():.3f}]")
        print(f"Labels unique values: {torch.unique(labels)}")
        print(f"Positive pixels in batch: {torch.sum(labels > 0).item()}")
        print(f"Total pixels in batch: {labels.numel()}")
        print(f"Positive ratio in batch: {torch.sum(labels > 0).item() / labels.numel():.6f}")

    except Exception as e:
        print(f"❌ Error testing training dataloader: {e}")
        return False

    # Test one validation batch
    try:
        val_batch = next(iter(val_loader))
        val_images = val_batch["image"]
        val_labels = val_batch["label"]

        print(f"Validation batch - Images shape: {val_images.shape}, Labels shape: {val_labels.shape}")
        print(f"Val positive pixels: {torch.sum(val_labels > 0).item()}")

    except Exception as e:
        print(f"❌ Error testing validation dataloader: {e}")
        return False

    print("✅ Data pipeline test passed!")
    return True

def train_model():
    """Train the HCC segmentation model"""
    # 检查磁盘空间
    print("=" * 50)
    print("检查磁盘空间...")
    if not check_disk_space("/", min_free_gb=10):  # 系统盘至少需要10GB
        print("系统盘空间不足，建议清理后再训练")

    if not check_disk_space(data_disk_path, min_free_gb=20):  # 数据盘至少需要20GB
        print("数据盘空间不足，可能影响训练")
        response = input("是否继续训练? (y/n): ")
        if response.lower() != 'y':
            print("训练已取消")
            return None

    # 清理旧日志
    cleanup_old_logs(logs_dir)

    # Create dataset JSON
    dataset_json_path, num_samples = create_dataset_json()
    print(f"Dataset preparation complete. Found {num_samples} samples.")

    # Inspect dataset statistics
    inspect_dataset_statistics(dataset_json_path)

    # Initialize model
    net = HCCSegmentationNet()

    # Prepare data (this creates train_ds and val_ds)
    net.prepare_data()

    # Test data pipeline before training
    if not test_data_pipeline(net):
        print("❌ Data pipeline test failed. Stopping training.")
        return None

    # Set up callbacks - 优化检查点保存策略
    # 只保存最佳模型和最后一个模型
    checkpoint_callback = ModelCheckpoint(
        dirpath=models_dir,
        filename="checkpoint_{epoch:02d}_{val_loss:.4f}",
        save_last=True,   # 保存最后一个模型
        save_top_k=1,     # 只保存最好的1个模型
        monitor="val_loss",
        mode="min",
        auto_insert_metric_name=False
    )

    early_stop_callback = EarlyStopping(
        monitor="val_loss",
        min_delta=0.00,
        patience=5,
        verbose=False,
        mode='min'
    )

    # Initialize trainer - 优化日志和存储设置
    trainer = pytorch_lightning.Trainer(
        accelerator='gpu' if torch.cuda.is_available() else 'cpu',
        devices=1,
        max_epochs=50,  # Reduced for testing with better data pipeline
        check_val_every_n_epoch=2,  # 验证频率，与模型中的check_val一致
        callbacks=[checkpoint_callback, early_stop_callback],
        default_root_dir=logs_dir,  # 使用专门的日志目录
        limit_val_batches=1.0,  # Use all validation samples for better evaluation
        enable_progress_bar=True,
        log_every_n_steps=1,
        precision="16-mixed",
    )

    # Start training
    print("Starting training...")
    trainer.fit(net)

    print(f"Training completed! Best dice: {net.best_val_dice:.4f} at epoch {net.best_val_epoch}")

    # 保存最后一个epoch的模型（如果checkpoint没有保存的话）
    last_model_path = os.path.join(models_dir, "last_model_SwinUNETR_HCC.pt")
    torch.save(net._model, last_model_path)
    print(f"Last model saved to: {last_model_path}")

    # 保存训练指标
    print("\n" + "=" * 50)
    print("保存训练指标...")
    save_training_metrics(net, metrics_dir)

    # 训练完成后再次检查磁盘空间
    print("\n" + "=" * 50)
    print("训练完成后的磁盘空间状态:")
    check_disk_space("/", min_free_gb=5)
    check_disk_space(data_disk_path, min_free_gb=10)

    # 打印结果文件夹内容
    print("\n" + "=" * 50)
    print("训练结果文件:")
    print(f"Results directory: {results_dir}")
    print("├── models/")
    for f in os.listdir(models_dir):
        print(f"│   ├── {f}")
    print("├── metrics/")
    for f in os.listdir(metrics_dir):
        print(f"│   ├── {f}")
    print("└── logs/")
    if os.path.exists(os.path.join(logs_dir, 'lightning_logs')):
        print("    └── lightning_logs/")

    return net


if __name__ == "__main__":
    # Create dataset and inspect statistics first
    dataset_json_path, num_samples = create_dataset_json()
    inspect_dataset_statistics(dataset_json_path)

    # Visualize sample data
    visualize_sample_data(dataset_json_path)

    # Start training directly
    trained_model = train_model()
