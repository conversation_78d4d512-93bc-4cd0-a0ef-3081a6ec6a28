import os
from pathlib import Path

def compare_filename_prefixes(path1, path2):
    """
    比较两个路径下文件名第一个点前面的字符，找出不一致的文件名
    
    Args:
        path1 (str): 第一个路径
        path2 (str): 第二个路径
    """
    
    # 检查路径是否存在
    if not os.path.exists(path1):
        print(f"路径不存在: {path1}")
        return
    
    if not os.path.exists(path2):
        print(f"路径不存在: {path2}")
        return
    
    # 获取两个路径下的所有文件
    files1 = [f for f in os.listdir(path1) if os.path.isfile(os.path.join(path1, f))]
    files2 = [f for f in os.listdir(path2) if os.path.isfile(os.path.join(path2, f))]
    
    print(f"路径1 ({path1}) 中的文件数量: {len(files1)}")
    print(f"路径2 ({path2}) 中的文件数量: {len(files2)}")
    print("-" * 80)
    
    # 提取文件名第一个点前面的字符
    def get_prefix(filename):
        """获取文件名第一个点前面的字符"""
        if '.' in filename:
            return filename.split('.')[0]
        else:
            return filename
    
    # 创建前缀到完整文件名的映射
    prefixes1 = {}
    prefixes2 = {}
    
    for file in files1:
        prefix = get_prefix(file)
        if prefix in prefixes1:
            prefixes1[prefix].append(file)
        else:
            prefixes1[prefix] = [file]
    
    for file in files2:
        prefix = get_prefix(file)
        if prefix in prefixes2:
            prefixes2[prefix].append(file)
        else:
            prefixes2[prefix] = [file]
    
    # 找出所有的前缀
    all_prefixes = set(prefixes1.keys()) | set(prefixes2.keys())
    
    # 找出不一致的文件名
    inconsistent_files = []
    
    print("文件前缀比较结果:")
    print("=" * 80)
    
    for prefix in sorted(all_prefixes):
        in_path1 = prefix in prefixes1
        in_path2 = prefix in prefixes2
        
        if not in_path1:
            print(f"❌ 前缀 '{prefix}' 只存在于路径2中:")
            for file in prefixes2[prefix]:
                print(f"   路径2: {file}")
            inconsistent_files.extend([(file, "只在路径2") for file in prefixes2[prefix]])
            print()
            
        elif not in_path2:
            print(f"❌ 前缀 '{prefix}' 只存在于路径1中:")
            for file in prefixes1[prefix]:
                print(f"   路径1: {file}")
            inconsistent_files.extend([(file, "只在路径1") for file in prefixes1[prefix]])
            print()
            
        else:
            # 两个路径都有这个前缀，检查文件名是否完全一致
            files1_set = set(prefixes1[prefix])
            files2_set = set(prefixes2[prefix])
            
            if files1_set != files2_set:
                print(f"⚠️  前缀 '{prefix}' 在两个路径中都存在，但文件名不完全一致:")
                print(f"   路径1文件: {prefixes1[prefix]}")
                print(f"   路径2文件: {prefixes2[prefix]}")
                
                # 找出只在路径1中的文件
                only_in_path1 = files1_set - files2_set
                if only_in_path1:
                    print(f"   只在路径1: {list(only_in_path1)}")
                    inconsistent_files.extend([(file, "只在路径1") for file in only_in_path1])
                
                # 找出只在路径2中的文件
                only_in_path2 = files2_set - files1_set
                if only_in_path2:
                    print(f"   只在路径2: {list(only_in_path2)}")
                    inconsistent_files.extend([(file, "只在路径2") for file in only_in_path2])
                print()
            else:
                print(f"✅ 前缀 '{prefix}' 在两个路径中一致")
    
    # 总结不一致的文件
    print("\n" + "=" * 80)
    print("总结 - 不一致的文件:")
    print("=" * 80)
    
    if inconsistent_files:
        print(f"发现 {len(inconsistent_files)} 个不一致的文件:")
        for i, (file, status) in enumerate(inconsistent_files, 1):
            print(f"{i:3d}. {file} ({status})")
    else:
        print("✅ 所有文件的前缀都是一致的！")
    
    return inconsistent_files

def main():
    # 定义两个路径
    path1 = r"M:\新建文件夹\HCC数据集\HCC新增待整理\46HCC\image\ap"
    path2 = r"M:\新建文件夹\HCC数据集\HCC新增待整理\46HCC\image\hbp"
    
    print("HCC数据集文件名前缀一致性检查")
    print("=" * 80)
    print(f"路径1: {path1}")
    print(f"路径2: {path2}")
    print("=" * 80)
    
    # 执行比较
    inconsistent_files = compare_filename_prefixes(path1, path2)
    
    # 可选：将结果保存到文件
    if inconsistent_files:
        output_file = "inconsistent_files_report.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("HCC数据集文件名前缀不一致报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"路径1: {path1}\n")
            f.write(f"路径2: {path2}\n")
            f.write(f"检查时间: {os.path.getctime}\n\n")
            
            f.write(f"发现 {len(inconsistent_files)} 个不一致的文件:\n")
            for i, (file, status) in enumerate(inconsistent_files, 1):
                f.write(f"{i:3d}. {file} ({status})\n")
        
        print(f"\n报告已保存到: {output_file}")

if __name__ == "__main__":
    main()