#%%xml格式标签转txt格式
import os
import xml.etree.ElementTree as ET

def parse_xml_and_save_to_txt(xml_file_path, output_txt_path):
    """
    解析XML文件，提取'zhong'和'cai'对象的边界框坐标，并保存到txt文件
    
    参数:
        xml_file_path: XML文件路径
        output_txt_path: 输出的txt文件路径
    """
    try:
        # 解析XML文件
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        
        # 打开输出文件
        with open(output_txt_path, 'w') as f:
            # 遍历所有object元素
            for obj in root.findall('.//object'):
                # 获取name元素的文本
                name = obj.find('name').text
                
                # 只处理'zhong'和'cai'对象
                if name in ['zhong', 'cai']:
                    # 获取边界框坐标
                    bbox = obj.find('.//bndbox')
                    xmin = bbox.find('xmin').text
                    ymin = bbox.find('ymin').text
                    xmax = bbox.find('xmax').text
                    ymax = bbox.find('ymax').text
                    
                    # 将'zhong'替换为1，'cai'替换为2
                    class_id = '1' if name == 'zhong' else '2'
                    
                    # 写入txt文件，格式为: class_id xmin ymin xmax ymax
                    f.write(f"{class_id} {xmin} {ymin} {xmax} {ymax}\n")
        
        print(f"成功将'{xml_file_path}'中的数据保存到'{output_txt_path}'")
        return True
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def process_all_xml_files(xml_dir, output_dir):
    """
    处理目录中的所有XML文件
    
    参数:
        xml_dir: 包含XML文件的目录
        output_dir: 输出txt文件的目录
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有XML文件
    xml_files = [f for f in os.listdir(xml_dir) if f.endswith('.xml')]
    
    if not xml_files:
        print(f"在'{xml_dir}'中没有找到XML文件")
        return
    
    success_count = 0
    for xml_file in xml_files:
        xml_path = os.path.join(xml_dir, xml_file)
        # 创建相同名称的txt文件
        txt_file = os.path.splitext(xml_file)[0] + '.txt'
        txt_path = os.path.join(output_dir, txt_file)
        
        if parse_xml_and_save_to_txt(xml_path, txt_path):
            success_count += 1
    
    print(f"处理完成: 成功转换{success_count}/{len(xml_files)}个文件")

# 主函数，直接使用指定路径进行批量转换
if __name__ == "__main__":
    # 设置固定的输入和输出路径
    xml_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/anno'
    output_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/labels'
    
    print(f"开始处理XML文件，从'{xml_dir}'转换到'{output_dir}'")
    process_all_xml_files(xml_dir, output_dir)


#%%xml格式标签转yolo格式txt
import os
import xml.etree.ElementTree as ET

def parse_xml_and_save_to_txt(xml_file_path, output_txt_path):
    """
    解析XML文件，提取'zhong'和'cai'对象的边界框坐标，并保存到txt文件
    
    参数:
        xml_file_path: XML文件路径
        output_txt_path: 输出的txt文件路径
    """
    try:
        # 解析XML文件
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        
        # 打开输出文件
        with open(output_txt_path, 'w') as f:
            # 遍历所有object元素
            for obj in root.findall('.//object'):
                # 获取name元素的文本
                name = obj.find('name').text
                
                # 只处理'zhong'和'cai'对象
                if name in ['zhong', 'cai']:
                    # 获取边界框坐标
                    bbox = obj.find('.//bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    
                    # 获取图片宽高
                    size = root.find('size')
                    img_width = float(size.find('width').text)
                    img_height = float(size.find('height').text)
                    
                    # VOC转YOLO格式
                    x_center = (xmin + xmax) / 2.0 / img_width
                    y_center = (ymin + ymax) / 2.0 / img_height
                    width = (xmax - xmin) / img_width
                    height = (ymax - ymin) / img_height
                    
                    # 将'zhong'替换为1，'cai'替换为2
                    class_id = '1' if name == 'zhong' else '2'
                    
                    # 写入txt文件，格式为: class_id x_center y_center width height
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        print(f"成功将'{xml_file_path}'中的数据保存到'{output_txt_path}'")
        return True
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def process_all_xml_files(xml_dir, output_dir):
    """
    处理目录中的所有XML文件
    
    参数:
        xml_dir: 包含XML文件的目录
        output_dir: 输出txt文件的目录
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有XML文件
    xml_files = [f for f in os.listdir(xml_dir) if f.endswith('.xml')]
    
    if not xml_files:
        print(f"在'{xml_dir}'中没有找到XML文件")
        return
    
    success_count = 0
    for xml_file in xml_files:
        xml_path = os.path.join(xml_dir, xml_file)
        # 创建相同名称的txt文件
        txt_file = os.path.splitext(xml_file)[0] + '.txt'
        txt_path = os.path.join(output_dir, txt_file)
        
        if parse_xml_and_save_to_txt(xml_path, txt_path):
            success_count += 1
    
    print(f"处理完成: 成功转换{success_count}/{len(xml_files)}个文件")

# 主函数，直接使用指定路径进行批量转换
if __name__ == "__main__":
    # 设置固定的输入和输出路径
    xml_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/anno'
    output_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/labels'
    
    print(f"开始处理XML文件，从'{xml_dir}'转换到'{output_dir}'")
    process_all_xml_files(xml_dir, output_dir)

#%%json格式标签转txt格式
import os
import json

def convert_json_to_txt(json_file, output_dir=None):
    """将JSON格式的目标检测标注文件转换为TXT格式"""
    # 设置默认输出目录
    if output_dir is None:
        output_dir = os.path.dirname(json_file)
        if not output_dir:
            output_dir = '.'
    
    # 创建输出目录（如果不存在）
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(json_file))[0]
    txt_file = os.path.join(output_dir, f"{base_name}.txt")
    
    # 读取JSON文件
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取坐标信息
    coordinates = []
    if 'shapes' in data:
        for shape in data['shapes']:
            if shape['shape_type'] == 'rectangle' and len(shape['points']) == 2:
                label = shape['label']
                # 提取左上角和右下角坐标
                x1, y1 = shape['points'][0]
                x2, y2 = shape['points'][1]
                
                # 格式: 标签 x1 y1 x2 y2
                coordinates.append(f"{label} {x1} {y1} {x2} {y2}")
    
    # 写入TXT文件
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(coordinates))
    
    print(f"已将 {json_file} 转换为 {txt_file}")
    return txt_file

def batch_convert(json_dir, output_dir=None):
    """批量转换目录中的所有JSON文件为TXT格式"""
    # 获取目录中的所有JSON文件
    json_files = [os.path.join(json_dir, f) for f in os.listdir(json_dir) 
                  if f.lower().endswith('.json')]
    
    for json_file in json_files:
        convert_json_to_txt(json_file, output_dir)
    
    print(f"共转换了 {len(json_files)} 个JSON文件")


# 转换指定目录下的所有JSON文件
json_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\1.日月光华-pyorch深度学习课程\9.目标检测和分类\twins识别实例\twins\labels"
output_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\1.日月光华-pyorch深度学习课程\9.目标检测和分类\twins识别实例\twins\txt_labels"
batch_convert(json_dir, output_dir) 

# %%json格式标签转YOLO格式txt
import os
import json

def convert_json_to_yolo(json_file, output_dir=None, image_dir=None):
    """将JSON格式的目标检测标注文件转换为YOLO格式的TXT文件"""
    # 设置默认输出目录
    if output_dir is None:
        output_dir = os.path.dirname(json_file)
        if not output_dir:
            output_dir = '.'
    
    # 创建输出目录（如果不存在）
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取JSON文件
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 直接从JSON中获取图像尺寸
    img_width = data.get('imageWidth', 1)
    img_height = data.get('imageHeight', 1)
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(json_file))[0]
    txt_file = os.path.join(output_dir, f"{base_name}.txt")
    
    # 提取坐标信息并转换为YOLO格式
    yolo_annotations = []
    if 'shapes' in data:
        for shape in data['shapes']:
            if shape['shape_type'] == 'rectangle' and len(shape['points']) == 2:
                try:
                    # 获取标签，YOLO格式使用数字类别ID（从0开始）
                    # 这里假设标签就是类别ID，如果不是，需要映射
                    class_id = int(shape['label']) - 1  # 标签从1开始，转为从0开始
                    
                    # 提取左上角和右下角坐标
                    x1, y1 = shape['points'][0]
                    x2, y2 = shape['points'][1]
                    
                    # 确保坐标顺序正确（x1 < x2, y1 < y2）
                    x1, x2 = min(x1, x2), max(x1, x2)
                    y1, y2 = min(y1, y2), max(y1, y2)
                    
                    # 计算YOLO格式的坐标（归一化的中心点坐标和宽高）
                    x_center = (x1 + x2) / 2 / img_width
                    y_center = (y1 + y2) / 2 / img_height
                    width = (x2 - x1) / img_width
                    height = (y2 - y1) / img_height
                    
                    # YOLO格式: 类别ID 中心点x坐标 中心点y坐标 宽度 高度
                    yolo_annotations.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
                except ValueError:
                    # 如果标签不是数字，可以使用映射字典或跳过
                    print(f"警告：标签 '{shape['label']}' 不是有效的类别ID，已跳过")
    
    # 写入TXT文件
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(yolo_annotations))
    
    print(f"已将 {json_file} 转换为YOLO格式的 {txt_file}")
    return txt_file

def batch_convert(json_dir, output_dir=None):
    """批量转换目录中的所有JSON文件为YOLO格式的TXT文件"""
    # 获取目录中的所有JSON文件
    json_files = [os.path.join(json_dir, f) for f in os.listdir(json_dir) 
                  if f.lower().endswith('.json')]
    
    for json_file in json_files:
        convert_json_to_yolo(json_file, output_dir)
    
    print(f"共转换了 {len(json_files)} 个JSON文件到YOLO格式")


# 转换指定目录下的所有JSON文件
# 设置不同的输入和输出文件夹
json_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\1.日月光华-pyorch深度学习课程\9.目标检测和分类\twins识别实例\twins\labels"
output_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\1.日月光华-pyorch深度学习课程\9.目标检测和分类\twins识别实例\twins\yolo_labels2"

# 传入不同的输入和输出文件夹
batch_convert(json_dir, output_dir)

# %% 3d nii.gz格式标签转3d的COCO和YOLO格式  待调试
import os
import json
import numpy as np
import SimpleITK as sitk
from glob import glob
import datetime

def itk_to_detection_formats(seg_file, original_file=None, output_dir="./output", dataset_name="liver_tumor"):
    """将ITK-SNAP分割掩码转换为COCO和YOLO格式
    
    Args:
        seg_file: 分割掩码文件路径(.nii.gz)
        original_file: 原始图像文件路径(可选)
        output_dir: 输出目录
        dataset_name: 数据集名称
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取分割掩码
    seg_img = sitk.ReadImage(seg_file)
    seg_array = sitk.GetArrayFromImage(seg_img)
    
    # 获取原图像信息
    spacing = seg_img.GetSpacing()
    origin = seg_img.GetOrigin()
    
    # 获取图像尺寸
    depth, height, width = seg_array.shape
    
    # 为每个标签值提取边界框
    bboxes = []
    unique_labels = np.unique(seg_array)
    # 排除背景标签(通常为0)
    unique_labels = unique_labels[unique_labels > 0]
    
    for label_val in unique_labels:
        # 获取当前标签的掩码
        label_mask = (seg_array == label_val)
        
        # 找到非零元素的索引
        z_indices, y_indices, x_indices = np.where(label_mask)
        
        if len(z_indices) == 0:
            continue
            
        # 计算3D边界框
        x_min, x_max = np.min(x_indices), np.max(x_indices)
        y_min, y_max = np.min(y_indices), np.max(y_indices)
        z_min, z_max = np.min(z_indices), np.max(z_indices)
        
        # 计算边界框中心和尺寸
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2
        center_z = (z_min + z_max) / 2
        
        width_box = x_max - x_min
        height_box = y_max - y_min
        depth_box = z_max - z_min
        
        # 将物理单位考虑进来(如果需要)
        center_x_mm = center_x * spacing[0] + origin[0]
        center_y_mm = center_y * spacing[1] + origin[1]
        center_z_mm = center_z * spacing[2] + origin[2]
        
        width_mm = width_box * spacing[0]
        height_mm = height_box * spacing[1]
        depth_mm = depth_box * spacing[2]
        
        # 添加到边界框列表
        bboxes.append({
            'label': int(label_val),
            'label_name': f"tumor_{label_val}",  # 可根据标签值映射到具体名称
            'voxel': {
                'center': [float(center_x), float(center_y), float(center_z)],
                'dimensions': [float(width_box), float(height_box), float(depth_box)],
                'x_min': int(x_min), 'y_min': int(y_min), 'z_min': int(z_min),
                'x_max': int(x_max), 'y_max': int(y_max), 'z_max': int(z_max)
            },
            'physical': {
                'center': [float(center_x_mm), float(center_y_mm), float(center_z_mm)],
                'dimensions': [float(width_mm), float(height_mm), float(depth_mm)]
            }
        })
    
    # 文件基本名(不带路径和扩展名)
    base_name = os.path.splitext(os.path.basename(seg_file))[0]
    if base_name.endswith('.nii'):
        base_name = os.path.splitext(base_name)[0]
    
    # ====== 创建COCO格式数据 ======
    coco_annotations = []
    coco_images = []
    
    # 对于3D图像，我们可以为每个切片创建一个标注
    # 或者仅创建肿瘤存在的切片的标注
    for z in range(depth):
        # 检查该切片是否有目标
        has_annotation = False
        
        for bbox in bboxes:
            z_min = bbox['voxel']['z_min']
            z_max = bbox['voxel']['z_max']
            
            if z_min <= z <= z_max:
                has_annotation = True
                break
        
        if not has_annotation:
            continue
            
        # 创建图像条目
        image_id = int(f"{os.path.basename(base_name)[-4:].replace('.', '0')}{z:03d}")
        coco_images.append({
            "id": image_id,
            "file_name": f"{base_name}_slice_{z:03d}.png",
            "width": width,
            "height": height,
            "date_captured": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        })
        
        # 为每个边界框创建标注
        for bbox_idx, bbox in enumerate(bboxes):
            z_min = bbox['voxel']['z_min']
            z_max = bbox['voxel']['z_max']
            
            if z_min <= z <= z_max:
                # 计算该切片上的2D边界框
                x_min = bbox['voxel']['x_min']
                y_min = bbox['voxel']['y_min']
                x_max = bbox['voxel']['x_max']
                y_max = bbox['voxel']['y_max']
                
                # COCO格式需要[x,y,width,height]
                coco_bbox = [int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min)]
                
                # 计算区域面积
                area = (x_max - x_min) * (y_max - y_min)
                
                coco_annotations.append({
                    "id": len(coco_annotations) + 1,
                    "image_id": image_id,
                    "category_id": bbox['label'],
                    "bbox": coco_bbox,
                    "area": float(area),
                    "segmentation": [],  # 这里可以根据需要添加分割信息
                    "iscrowd": 0
                })
    
    # 创建COCO格式的JSON
    coco_output = {
        "info": {
            "description": f"{dataset_name} Dataset",
            "url": "",
            "version": "1.0",
            "year": datetime.datetime.now().year,
            "contributor": "ITK-SNAP to COCO converter",
            "date_created": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "licenses": [{"id": 1, "name": "Unknown", "url": ""}],
        "categories": [{"id": label, "name": f"tumor_{label}", "supercategory": "tumor"} 
                       for label in unique_labels],
        "images": coco_images,
        "annotations": coco_annotations
    }
    
    # 保存COCO JSON
    with open(os.path.join(output_dir, f"{base_name}_coco.json"), 'w') as f:
        json.dump(coco_output, f, indent=2)
    
    # ====== 创建YOLO格式数据 ======
    # YOLO需要为每个切片创建单独的标注文件
    os.makedirs(os.path.join(output_dir, "labels"), exist_ok=True)
    
    for z in range(depth):
        yolo_annotations = []
        
        for bbox in bboxes:
            z_min = bbox['voxel']['z_min']
            z_max = bbox['voxel']['z_max']
            
            if z_min <= z <= z_max:
                # 计算该切片上的2D边界框
                x_min = bbox['voxel']['x_min']
                y_min = bbox['voxel']['y_min']
                x_max = bbox['voxel']['x_max']
                y_max = bbox['voxel']['y_max']
                
                # YOLO格式需要归一化的中心点坐标和宽高: [class_id, center_x, center_y, w, h]
                # 所有值都是相对于图像宽高的比例
                center_x = (x_min + x_max) / 2 / width
                center_y = (y_min + y_max) / 2 / height
                w = (x_max - x_min) / width
                h = (y_max - y_min) / height
                
                # class_id 从0开始
                class_id = bbox['label'] - 1 if bbox['label'] > 0 else 0
                
                yolo_annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {w:.6f} {h:.6f}")
        
        if yolo_annotations:
            # 保存YOLO格式标注
            with open(os.path.join(output_dir, "yolo_labels", f"{base_name}_slice_{z:03d}.txt"), 'w') as f:
                f.write('\n'.join(yolo_annotations))
    
    # 额外保存一个包含3D边界框的原始信息JSON(方便后处理)
    with open(os.path.join(output_dir, f"{base_name}_3d_bboxes.json"), 'w') as f:
        json.dump(bboxes, f, indent=2)
        
    print(f"转换完成! 输出文件保存在: {output_dir}")
    print(f"- COCO格式: {base_name}_coco.json")
    print(f"- YOLO格式: yolo_labels/{base_name}_slice_*.txt")
    print(f"- 3D边界框: {base_name}_3d_bboxes.json")
    
    return bboxes

# 批处理多个文件
def batch_convert(seg_dir, output_dir="./output"):
    """批量转换目录中的所有.nii.gz文件"""
    seg_files = glob(os.path.join(seg_dir, "*.nii.gz"))
    
    for seg_file in seg_files:
        print(f"处理: {seg_file}")
        itk_to_detection_formats(seg_file, output_dir=output_dir)

# 使用示例 - 直接在这里修改文件路径
if __name__ == "__main__":
    # 配置参数 - 修改这里的路径和参数即可
    SEG_FILE = "D:/path/to/your/segmentation.nii.gz"  # 单个分割文件路径
    SEG_DIR = "D:/path/to/your/segmentations/"  # 分割文件夹路径
    OUTPUT_DIR = "D:/path/to/your/output/"  # 输出目录
    
    # 取消注释下面的行来执行单个文件转换
    # itk_to_detection_formats(SEG_FILE, output_dir=OUTPUT_DIR)
    
    # 取消注释下面的行来执行批量转换
    # batch_convert(SEG_DIR, output_dir=OUTPUT_DIR)

#%% 2d json格式标签转3d的nii.gz格式mask  待调试
import os
import json
import numpy as np
import SimpleITK as sitk

def get_bbox_diameter(points):
    # points: [[x1, y1], [x2, y2]]
    x1, y1 = points[0]
    x2, y2 = points[1]
    return max(abs(x2 - x1), abs(y2 - y1))

def get_bbox_center(points):
    x1, y1 = points[0]
    x2, y2 = points[1]
    return int(round((x1 + x2) / 2)), int(round((y1 + y2) / 2))

def convert_jsons_to_cube_nii(json_dir, output_dir):
    """
    批量将json序列文件夹下的所有json转为3D立方体mask，输出为nii.gz，output_dir为输出文件夹。
    支持两种情况：
    1. json_dir下为子文件夹，每个子文件夹为一个病例（原有逻辑）
    2. json_dir下直接是json文件，每个json文件为一个病例（新增逻辑）
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    has_subdir = any(os.path.isdir(os.path.join(json_dir, f)) for f in os.listdir(json_dir))
    if has_subdir:
        # 每个子文件夹为一个病例
        for case_name in os.listdir(json_dir):
            case_path = os.path.join(json_dir, case_name)
            if not os.path.isdir(case_path):
                continue
            json_files = sorted([os.path.join(case_path, f) for f in os.listdir(case_path) if f.endswith('.json')])
            if not json_files:
                continue
            # 读取第一张获取shape
            with open(json_files[0], 'r', encoding='utf-8') as f:
                data0 = json.load(f)
            img_h = data0.get('imageHeight', 512)
            img_w = data0.get('imageWidth', 512)
            num_slices = len(json_files)
            mask_3d = np.zeros((num_slices, img_h, img_w), dtype=np.uint8)
            # 找最大病灶
            max_diameter = 0
            max_info = None
            for idx, jf in enumerate(json_files):
                with open(jf, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if 'shapes' in data:
                    for shape in data['shapes']:
                        if shape['shape_type'] == 'rectangle' and len(shape['points']) == 2:
                            diameter = get_bbox_diameter(shape['points'])
                            if diameter > max_diameter:
                                max_diameter = diameter
                                max_info = {
                                    'file_idx': idx,
                                    'points': shape['points'],
                                    'img_width': data.get('imageWidth', 512),
                                    'img_height': data.get('imageHeight', 512)
                                }
            if max_info is None:
                print(f"未找到任何病灶: {case_name}")
                continue
            cube_size = int(round(max_diameter))
            half = cube_size // 2
            center_slice = max_info['file_idx']
            center_x, center_y = get_bbox_center(max_info['points'])
            z_start = max(center_slice - half, 0)
            z_end = min(center_slice + half + 1, num_slices)
            y_start = max(center_y - half, 0)
            y_end = min(center_y + half + 1, img_h)
            x_start = max(center_x - half, 0)
            x_end = min(center_x + half + 1, img_w)
            mask_3d[z_start:z_end, y_start:y_end, x_start:x_end] = 1
            mask_img = sitk.GetImageFromArray(mask_3d)
            out_path = os.path.join(output_dir, f"{case_name}.nii.gz")
            sitk.WriteImage(mask_img, out_path)
            print(f"已保存: {out_path}")
    else:
        # 直接处理所有json文件，每个json输出(1, H, W)的mask，mask区域为cube
        json_files = sorted([f for f in os.listdir(json_dir) if f.endswith('.json')])
        for jf in json_files:
            json_path = os.path.join(json_dir, jf)
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            img_h = data.get('imageHeight', 512)
            img_w = data.get('imageWidth', 512)
            mask_3d = np.zeros((1, img_h, img_w), dtype=np.uint8)
            max_diameter = 0
            max_points = None
            if 'shapes' in data:
                for shape in data['shapes']:
                    if shape['shape_type'] == 'rectangle' and len(shape['points']) == 2:
                        diameter = get_bbox_diameter(shape['points'])
                        if diameter > max_diameter:
                            max_diameter = diameter
                            max_points = shape['points']
            if max_points is not None:
                cube_size = int(round(max_diameter))
                half = cube_size // 2
                center_x, center_y = get_bbox_center(max_points)
                y_start = max(center_y - half, 0)
                y_end = min(center_y + half + 1, img_h)
                x_start = max(center_x - half, 0)
                x_end = min(center_x + half + 1, img_w)
                mask_3d[0, y_start:y_end, x_start:x_end] = 1
                mask_img = sitk.GetImageFromArray(mask_3d)
                out_path = os.path.join(output_dir, os.path.splitext(jf)[0] + '.nii.gz')
                sitk.WriteImage(mask_img, out_path)
                print(f"已保存: {out_path}")
            else:
                print(f"未找到任何病灶: {jf}")

if __name__ == "__main__":
    json_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\2.pytorch深度学习和R代码总结版\深度学习代码总结最新版\2d目标检测\hcc\labels"
    output_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\2.pytorch深度学习和R代码总结版\深度学习代码总结最新版\2d目标检测\hcc\nii"
    convert_jsons_to_cube_nii(json_dir, output_dir)



# %%
