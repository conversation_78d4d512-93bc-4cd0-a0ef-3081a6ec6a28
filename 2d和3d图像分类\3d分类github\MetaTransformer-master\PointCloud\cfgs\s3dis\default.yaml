# data augmentation
dataset:
  common:
    NAME: S3DIS
    data_root: data/S3DIS/s3disfull
    test_area: 5
    voxel_size: 0.04
  train:
    split: train
    voxel_max: 24000
    loop: 30  # here, the training has been looped 30 times. therefore, the training epochs do not need much.
    presample: False
  val:
    split: val
    voxel_max: null
    presample: True 
  test:
    split: test
    voxel_max: null
    presample: False 

feature_keys: x,heights
num_classes: 13
batch_size: 32
val_batch_size: 1

dataloader:
  num_workers: 6

datatransforms:
  train: [ChromaticAutoContrast, PointsToTensor, PointCloudScaling, PointCloudXYZAlign, PointCloudRotation, PointCloudJitter, ChromaticDropGPU, ChromaticNormalize]
  val: [<PERSON>T<PERSON><PERSON><PERSON><PERSON>, PointCloudXYZAlign, ChromaticNormalize]
  vote: [ChromaticDropGPU]
  kwargs:
    color_drop: 0.2
    gravity_dim: 2
    scale: [0.9, 1.1]
    angle: [0, 0, 1]
    jitter_sigma: 0.005
    jitter_clip: 0.02

# ---------------------------------------------------------------------------- #
# Training cfgs
# ---------------------------------------------------------------------------- #
val_fn: validate
ignore_index: null 
epochs: 100

cls_weighed_loss: False

criterion_args:
  NAME: CrossEntropy
  label_smoothing: 0.2

optimizer:
 NAME: 'adamw'  # performs 1 point better than adam
 weight_decay: 1.0e-4

# lr_scheduler:
sched: cosine
warmup_epochs: 0

min_lr: 1.0e-5 #
lr: 0.01 # LR linear rule.

grad_norm_clip: 10
use_voting: False
# ---------------------------------------------------------------------------- #
# io and misc
# ---------------------------------------------------------------------------- #
log_dir: 's3dis'
save_freq: -1 # save epoch every xxx epochs, -1 only save last and best. 
val_freq: 1

wandb:
  project: PointNeXt-S3DIS