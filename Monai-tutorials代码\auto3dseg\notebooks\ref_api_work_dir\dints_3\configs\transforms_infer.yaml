_meta_: {}
image_key: image
transforms_infer:
  _target_: <PERSON>mpo<PERSON>
  transforms:
  - {_target_: LoadImaged, image_only: false, keys: '@image_key'}
  - {_target_: EnsureChannelFirstd, keys: '@image_key'}
  - {_target_: NormalizeIntensityd, channel_wise: true, keys: '@image_key', nonzero: true}
  - {_target_: Orientationd, axcodes: RAS, keys: '@image_key'}
  - {_target_: Spacingd, align_corners: true, keys: '@image_key', mode: bilinear,
    pixdim: '@training#transforms#resample_resolution'}
  - {_target_: CastToTyped, dtype: $torch.float32, keys: '@image_key'}
