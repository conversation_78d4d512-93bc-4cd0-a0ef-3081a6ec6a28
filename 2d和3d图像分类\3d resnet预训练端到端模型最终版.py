
##%% 3d resnet 基于Med3d的预训练模型  已经成功  版本1
#根据剪切后的tumor图像nii格式进行训练 
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
import math
from functools import partial
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import torchvision.models as models
import nibabel as nib
import pandas as pd
import numpy as np
import os
import SimpleITK as sitk
import cv2

def get_file_list(directory):
    file_list = [os.path.join(directory, file) for file in os.listdir(directory) if file.endswith('.nii.gz')]
    return file_list

train_dir = '/root/autodl-tmp/train'  # '/mnt/hcc/ap'
train_image_list = get_file_list(train_dir)
print(train_image_list[:5])
print(len(train_image_list))

test_dir = '/root/autodl-tmp/validation'  # '/mnt/hcc/ap'
test_image_list = get_file_list(test_dir)
print(test_image_list[:5])
print(len(test_image_list))

# train_dir =  '/root/autodl-tmp/train'     #'/mnt/hcc/ap'
# train_image = [file for file in os.listdir(train_dir) if file.endswith('.nii.gz')]
# train_image_list = []
# for i in train_image:
#     file_path = os.path.join(train_dir, i)
#     # print(file_path)
#     train_image_list.append(file_path)
# train_image_list[:5]
#
# test_dir =  '/root/autodl-tmp/test'     #'/mnt/hcc/ap'
# test_image = [file for file in os.listdir(test_dir) if file.endswith('.nii.gz')]
# test_image_list = []
# for i in test_image:
#     file_path = os.path.join(test_dir, i)
#     # print(file_path)
#     test_image_list.append(file_path)

def get_label_list(file_path):
    # 读取Excel文件
    label_file = pd.read_excel(file_path)
    # 从'name'和'VETC'列中获取数值
    name_values = label_file['name'].tolist()
    label_values = label_file['VETC'].tolist()
    # 根据'name'列对数值进行排序
    sorted_label_values = [label for _, label in sorted(zip(name_values, label_values))]
    # 将排序后的VETC数值存储在label_list中
    label_list = sorted_label_values
    return label_list

# 指定Excel文件路径
train_file_path = '/root/autodl-tmp/train.xlsx'
test_file_path = '/root/autodl-tmp/validation.xlsx'
# 调用函数获取标签列表
train_label_list = get_label_list(train_file_path)
test_label_list = get_label_list(test_file_path)
# 打印标签列表
print(train_label_list[:5])
print(len(train_label_list))
print(len(test_label_list))

# label_list = []
#  # Read the Excel file
# label_file  = pd.read_excel('/root/autodl-tmp/HCC临床资料1-345完整版.xlsx')
#  # Get the values from the 'name' and 'VETC' columns
# name_values = label_file ['name'].tolist()
# vetc_values = label_file ['VETC'].tolist()
#  # Sort the values based on the 'name' column
# sorted_vetc_values = [vetc for _, vetc in sorted(zip(name_values, vetc_values))]
#  # Store the sorted VETC values in the label_list
# label_list = sorted_vetc_values
#  # Print the label_list
# label_list[:5]
# len(label_list)

#获取最大的图像的shape
image_list = train_image_list + test_image_list

max_shape = (0, 0, 0)
for image_path in image_list:
    image = nib.load(image_path)
    shape = image.shape
    max_shape = tuple(max(max_shape[i], shape[i]) for i in range(3))
print("Maximum shape of the nii images:", max_shape)
max_num = max_shape[2]
max_size = max(max_shape[0],max_shape[1])

class TumorDataset(Dataset):
    def __init__(self, image_list, label_list, transform=None):
        self.image_list = image_list
        self.label_list = label_list
        self.transform = transform
        for img, label in zip(image_list, label_list):
            image = sitk.ReadImage(img)
            img_array = sitk.GetArrayFromImage(image)
            img_array = img_array.astype(np.float32)
            if not np.any(img_array == 0):
                self.image_list.append(img)
                self.label_list.append(label)
    
    def __len__(self):
        return len(self.image_list)
    
    def __getitem__(self, index):
        image_path = self.image_list[index]
        label = self.label_list[index]
        image = sitk.ReadImage(image_path)
        img_array = sitk.GetArrayFromImage(image)
        img_array = img_array.astype(np.float32)
        if img_array.ndim == 3:
            img_array = img_array.squeeze()
            pad_shape = ((0, max(0,max_num - img_array.shape[0])), # 图像数量大小可以修改
                         (0, max(0, max_size - img_array.shape[1])), # 图像大小可以修改
                         (0, max(0,max_size - img_array.shape[2]))) # 图像大小可以修改
        else:
            pad_shape = ((0, max(0,max_num - img_array.shape[0])), # 图像数量大小可以修改
                         (0, max(0, max_size - img_array.shape[1])), # 图像大小可以修改
                         (0, max(0,max_size - img_array.shape[2]))) # 图像大小可以修改
        img_array = np.pad(img_array, pad_shape, mode='constant')
        img_array = np.stack([img_array] * 1, axis=0)  # 将通道数改为1
        tensor_image = torch.from_numpy(img_array).float()
        label_array = np.array(label)
        tensor_label = torch.from_numpy(label_array).float()
        return tensor_image, tensor_label

train_dataset = TumorDataset(train_image_list,train_label_list, transform=None)
test_dataset = TumorDataset(test_image_list,test_label_list, transform=None)
train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False)

## 创建Resnet3D分类模型并加载预训练模型
import sys
# sys.path.append(r'D:\anaconda3\Lib\site-packages\MedicalNet-master')
# sys.path.append(r'D:\anaconda3\envs\MedicalNet-master')
sys.path.append('/root/autodl-tmp/MedicalNet-master')  #把models文件夹的上一级文件夹路径加到系统变量
sys.path
from models import resnet  #把models文件夹的上一级文件夹路径加到系统变量

def generate_model(model_type='resnet', model_depth=50,
                   input_W=224, input_H=224, input_D=70, resnet_shortcut='B',
                   no_cuda=False, gpu_id=[0],
                   pretrain_path = '/root/autodl-tmp/resnet_50.pth',
                   nb_class=1):
    assert model_type in [
        'resnet'
    ]

    if model_type == 'resnet':
        assert model_depth in [10, 18, 34, 50, 101, 152, 200]

    if model_depth == 10:
        model = resnet.resnet10(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 256
    elif model_depth == 18:
        model = resnet.resnet18(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 512
    elif model_depth == 34:
        model = resnet.resnet34(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 512
    elif model_depth == 50:
        model = resnet.resnet50(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 101:
        model = resnet.resnet101(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 152:
        model = resnet.resnet152(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 200:
        model = resnet.resnet200(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048

    model.conv_seg = nn.Sequential(nn.AdaptiveAvgPool3d((1, 1, 1)), nn.Flatten(),
                                   nn.Linear(in_features=fc_input, out_features=nb_class, bias=True))

    if not no_cuda:
        if len(gpu_id) > 1:
            model = model.cuda()
            model = nn.DataParallel(model, device_ids=gpu_id)
            net_dict = model.state_dict()
        else:
            import os
            os.environ["CUDA_VISIBLE_DEVICES"]=str(gpu_id[0])
            model = model.cuda()
            model = nn.DataParallel(model, device_ids=None)
            net_dict = model.state_dict()
    else:
        net_dict = model.state_dict()

    print('loading pretrained model {}'.format(pretrain_path))
    pretrain = torch.load(pretrain_path)
    pretrain_dict = {k: v for k, v in pretrain['state_dict'].items() if k in net_dict.keys()}
    # k 是每一层的名称，v是权重数值
    net_dict.update(pretrain_dict) #字典 dict2 的键/值对更新到 dict 里。
    model.load_state_dict(net_dict) #model.load_state_dict()函数把加载的权重复制到模型的权重中去

    print("-------- pre-train model load successfully --------")

    return model


model = generate_model(model_type='resnet', model_depth=34,#注意修改
                   input_W=224, input_H=224, input_D=70, resnet_shortcut='B',
                   no_cuda=False, gpu_id=[0],
                   # pretrain_path = '/root/autodl-tmp/resnet_50.pth',
                   # pretrain_path = '/root/autodl-tmp/resnet_34.pth',
                   pretrain_path = '/root/autodl-tmp/resnet_34.pth', #预训练权重要与前面的深度匹配
                   nb_class=2)

device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =',device)

## 使用较小的学习率进行微调
from torch.optim import lr_scheduler
optimizer  = torch.optim.Adam(model.parameters(), lr=0.0006)

# 定义L2正则化项的权重；定义优化器，并添加L2正则化
# l2_lambda = 0.01
# optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=l2_lambda)

criterion = nn.CrossEntropyLoss()
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

 # Train and test the model
num_epochs = 30
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, (images, labels) in enumerate(train_loader):
        images = images.float().to(device)
        labels = labels.long().to(device)
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
        torch.cuda.empty_cache()
    train_loss = running_loss / len(train_dataset)
    train_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Training Accuracy: {train_acc:.2f}%')
    model.eval()
    correct = 0
    total = 0
    best_acc = 0.0
    with torch.no_grad():
        for images, labels in test_loader:
            images = images.float().to(device)
            labels = labels.long().to(device)
            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            running_loss += loss.item() * images.size(0)
            torch.cuda.empty_cache()
    test_loss = running_loss / len(test_dataset)
    test_acc = 100 * correct / total

    if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), '/root/autodl-tmp/best_model.pt')

    print(f'Testing Epoch [{epoch+1}/{num_epochs}],Test Loss: {test_loss:.4f}, Testing Accuracy: {test_acc:.2f}%')

##% 加载训练好的模型对测试集进行预测
import pandas as pd

classes = [0, 1]  # 类别
ids = []  # 存储行名
labels = []  # 存储真实标签
pred_labels = []  # 存储预测标签
pred_scores = []  # 存储预测概率

new_model = model
new_model.load_state_dict(torch.load('/root/autodl-tmp' + '/best_model.pt'))

for id, data in enumerate(test_dataset):
    img, label = data
    img = np.expand_dims(img, axis=0)
    img = torch.from_numpy(img).float()
    
    outputs = new_model(img)
    pred_score = torch.nn.functional.softmax(outputs, dim=1)[0].tolist()
    _, pred_label = torch.max(outputs, 1)
    
    ids.append(id)
    labels.append(label.item())
    pred_labels.append(pred_label.item())
    pred_scores.append(pred_score)
    
data = {
    'ids': ids,
    'labels': labels,
    'pred_labels': pred_labels,
    'pred_scores': pred_scores
}

#   df18 = pd.DataFrame(data)

# df34 = pd.DataFrame(data)
df50 = pd.DataFrame(data)

# # 展示某个DataFrame数据
df50.head()

# 将DataFrame导出到Excel文件
df50.to_excel('/root/autodl-tmp/resnet50.xlsx', index=False)  # 指定文件名和是否包含索引列

##%%
#绘制ROC曲线(区分度)
import sklearn
from sklearn.metrics import roc_curve,auc,accuracy_score,confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings("ignore")
def fun(x,index):
    #返回列表中第几个元素
    return x[index]

#绘制roc曲线
plt.figure(figsize=(8,8))
colors=['red','yellow','blue']
for index, df in enumerate([df50]) : #[df18,34,50]
    y_true = df['labels']
    y_pred = df['pred_labels']
    y_score = df['pred_scores'].apply(lambda x:fun(x,1))
    #计算roc和 auc
    fpr, tpr, thresholds=roc_curve(y_true,y_score,pos_label=1)
    ACC = accuracy_score(y_true,y_pred)
    AUC = auc(fpr, tpr)
    plt.plot(fpr,tpr,colors[index],label='Resnet3D-50'+' ACC=%0.2f AUC = %0.2f'% (ACC,AUC))
plt.legend(loc='lower right',fontsize = 12)
plt.plot([0,1],[0,1],color='black',linestyle='dashed')
plt.ylabel('True Positive Rate',fontsize = 14)
plt.xlabel('Flase Positive Rate',fontsize = 14)
plt.show()


#绘制混淆矩阵
plt.figure(figsize=(15,5))
for index, df in enumerate([df50]) :#[df18,df34,df50]
    y_true = df['labels']
    y_pred = df['pred_labels']
    plt.subplot(1,3,index+1)
    sns.heatmap(confusion_matrix(y_true, y_pred),annot=True, cmap='Blues')
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title("Resnet3D-50")
plt.show()


#之前都是用R绘制DCA曲线，现在在网上找到用python绘制DCA方法，感谢作者的分享
#用python绘制DCA曲线参考资料http://t.csdn.cn/dQv8s

def calculate_net_benefit_model(thresh_group, y_pred_score, y_label):
    net_benefit_model = np.array([])
    for thresh in thresh_group:
        y_pred_label = y_pred_score > thresh
        tn, fp, fn, tp = confusion_matrix(y_label, y_pred_label).ravel()
        n = len(y_label)
        net_benefit = (tp / n) - (fp / n) * (thresh / (1 - thresh))
        net_benefit_model = np.append(net_benefit_model, net_benefit)
    return net_benefit_model

def calculate_net_benefit_all(thresh_group, y_label):
    net_benefit_all = np.array([])
    tn, fp, fn, tp = confusion_matrix(y_label, y_label).ravel()
    total = tp + tn
    for thresh in thresh_group:
        net_benefit = (tp / total) - (tn / total) * (thresh / (1 - thresh))
        net_benefit_all = np.append(net_benefit_all, net_benefit)
    return net_benefit_all

def plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all,color='crimson',index=50):

    ax.plot(thresh_group, net_benefit_model, color = color, label = 'Resnet3D-50')

    #Figure Configuration， 美化一下细节
    ax.set_xlim(0,1)
    ax.set_ylim(net_benefit_model.min() - 0.15, net_benefit_model.max() + 0.15)#adjustify the y axis limitation
    ax.set_xlabel(
        xlabel = 'High Risk Threshold',
        fontdict= {'family': 'Times New Roman', 'fontsize': 13}
        )
    ax.set_ylabel(
        ylabel = 'Net Benefit',
        fontdict= {'family': 'Times New Roman', 'fontsize': 13}
        )
    ax.grid('off')
    ax.spines['right'].set_color((0.8, 0.8, 0.8))
    ax.spines['top'].set_color((0.8, 0.8, 0.8))
    ax.legend(loc = 'upper right')
    return ax

fig, ax = plt.subplots()
fig.set_size_inches(10, 8)
for index, df in enumerate([df50]) :
    y_label = df['labels']
    y_pred_score = df['pred_scores'].apply(lambda x:fun(x,1))

    thresh_group = np.arange(0,1,0.01)
    net_benefit_model = calculate_net_benefit_model(thresh_group, y_pred_score, y_label)
    net_benefit_all = calculate_net_benefit_all(thresh_group, y_label)
    ax = plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all,color=colors[index],index= 50)
ax.plot(thresh_group, net_benefit_all, color = 'black',label = 'ALL')
ax.plot((0, 1), (0, 0), color = 'black', linestyle = ':', label = 'None')
plt.show()

# 绘制校准曲线（校准度）
from sklearn.calibration import calibration_curve
for index, df in enumerate([df50]) :
    y_true = df['labels']
    y_score = df['pred_scores'].apply(lambda x:fun(x,1))
    prob_true, prob_pred = calibration_curve(list(y_true), list(y_score), n_bins=5)
    plt.plot(prob_pred, prob_true, linewidth=1, color=colors[index], marker="o",label="Mean value")
plt.plot([0,1],[0,1],color='black',linestyle='dashed')
plt.legend(["Resnet3D-50"],loc="upper left")
plt.xlabel('Predicted Probability of hypertension')
plt.ylabel('Actual probability')
plt.show()
# Text(0,0.5,'Actual probability')

##%% 使用ResNet3D加载医学图像预训练权重进行影像组学分析  根据scdn代码修改的pytorch版本
# 版本2  根据image和mask进行剪切，再训练

#安装 SimpleITK，用来读取医疗数据
!pip install SimpleITK

#生成txt文档，每一行:xxx.nii.gz 空格 类别
import os
import random
import numpy as np
import SimpleITK as sitk

random.seed(2022)
classifier = {"VETC-":0,"VETC+":1} #分类文件夹名必须要与classifier中的名字一致

path_origin = r'H:\1.HCC-VETC\datasets\HCC-suzhou\paper-data\aplist' #路径下为分类文件夹，如VETC-和VETC+的文件夹D:\HCC\nii\tumor
train_txt = open(os.path.join(path_origin,'train_list.txt'),'w')
val_txt = open(os.path.join(path_origin,'val_list.txt'),'w')

for k in classifier.keys():
    classifier_path = os.path.join(path_origin,k)
    fileList = []
    for root, dirs, files in os.walk(classifier_path):
            for fileObj in files:
                fileList.append(os.path.join(root, fileObj))
    #只使用ap模态
    files = list(filter(lambda x: x.endswith('ap.nii.gz'), fileList))
    random.shuffle(files)
    rate = int(len(files) * 0.8)#训练集和测试集8：2; 如果1.0则全是训练集
    for i,f_path in enumerate(files):
        if i < rate:
            train_txt.write(f_path + ' ' + str(classifier[k])+ '\n')
        else:
            val_txt.write(f_path + ' ' + str(classifier[k])+ '\n')
train_txt.close()
val_txt.close()
print('完成')

import torch
from torch.utils.data import Dataset
import numpy as np
import os
import SimpleITK as sitk
from torchvision.transforms import Compose, Resize
import torch.nn as nn
# 定义转换

class MyDataset(Dataset):
    def __init__(self, data_dir, mode='train', transform=None, labels=None):
        super().__init__()
        self.data_dir = data_dir
        self.transform = transform
        self.labels = labels
        self.data_paths = []
        self.labels = []
        if mode == 'train':
            with open(os.path.join(self.data_dir, "train_list.txt"), "r", encoding="utf-8") as f:
                info = f.readlines()
            for img_info in info:
                img_path, label = img_info.split()
                self.data_paths.append(img_path)
                self.labels.append(int(label))
        elif mode == 'val':
            with open(os.path.join(self.data_dir, "val_list.txt"), "r", encoding="utf-8") as f:
                info = f.readlines()
            for img_info in info:
                img_path, label = img_info.split()
                self.data_paths.append(img_path)
                self.labels.append(int(label))
    
    def __getitem__(self, index):
        img_path = self.data_paths[index]
        mask_path = img_path.replace('.nii', '-mask.nii') #img和mask放一个文件夹
        data = self.read_Nifit(img_path)
        mask = self.read_Nifit(mask_path)
        data = self.maskcroppingbox(data, mask)
        if data.ndim != 3:
            raise ValueError("Data should have 3 dimensions")
        if self.transform is not None:
            data = self.transform(data)
        data = np.expand_dims(data, axis=0)
        label = self.labels[index]
        label = np.array([label], dtype=np.int32)
        return data, label
    
    def __len__(self):
        return len(self.data_paths)
    
    def read_Nifit(self, path):
        sitkImg = sitk.ReadImage(path)
        npImg = sitk.GetArrayFromImage(sitkImg)
        npImg = npImg.astype('float32')
        return npImg
    
    def maskcroppingbox(self, img, mask, use2D=False):# 根据mask范围进行裁剪        
        if np.count_nonzero(mask) == 0:
            raise ValueError("Mask should have non-zero values")
        mask_2 = np.argwhere(mask)
        (zstart, ystart, xstart), (zstop, ystop, xstop) = mask_2.min(axis=0), mask_2.max(axis=0) + 1
        roi_image = img[zstart - 1:zstop + 1, ystart:ystop, xstart:xstop]
        roi_mask = mask[zstart - 1:zstop + 1, ystart:ystop, xstart:xstop]
        roi_image[roi_mask < 1] = 0
        return roi_image

train_path = []
train_label = []
with open(os.path.join(path_origin, "train_list.txt"), "r", encoding="utf-8") as f:
        info = f.readlines()
        for img_info in info:
                img_path, label = img_info.split()
                train_path.append(img_path)
                train_label.append(int(label))

val_path = []
val_label = []
with open(os.path.join(path_origin, "val_list.txt"), "r", encoding="utf-8") as f:
        info = f.readlines()
        for img_info in info:
                img_path, label = img_info.split()
                val_path.append(img_path)
                val_label.append(int(label))
val_label

train_dataset = MyDataset(path_origin, mode='train', labels=train_label)
val_dataset = MyDataset(path_origin, mode='val', labels=val_label)
print("训练集样本数量:", len(train_dataset))
print("验证集样本数量:", len(val_dataset))
train_dataset.labels
val_dataset.labels

# # 方法2 待调试
# class MyDataset(Dataset):
#     def __init__(self, image_paths, mode='train', transform=None, labels=None):
#         super().__init__()
#         self.image_paths = image_paths
#         self.transform = transform
#         self.labels = labels
#         self.data_paths = []
#         self.labels = []
#         if mode == 'train':
#             for img_path, label in zip(self.image_paths, self.labels):
#                 self.data_paths.append(img_path)
#                 self.labels.append(label)
#         elif mode == 'val':
#             for img_path, label in zip(self.image_paths, self.labels):
#                 self.data_paths.append(img_path)
#                 self.labels.append(label)
    
#     # 省略其他方法的代码...
#     def __getitem__(self, index):
#         img_path = self.data_paths[index]
#         mask_path = img_path.replace('.nii', '-mask.nii') #img和mask放一个文件夹
#         data = self.read_Nifit(img_path)
#         mask = self.read_Nifit(mask_path)
#         data = self.maskcroppingbox(data, mask)
#         if data.ndim != 3:
#             raise ValueError("Data should have 3 dimensions")
#         if self.transform is not None:
#             data = self.transform(data)
#         data = np.expand_dims(data, axis=0)
#         label = self.labels[index]
#         label = np.array([label], dtype=np.int32)
#         return data, label
    
#     def __len__(self):
#         return len(self.data_paths)
    
#     def read_Nifit(self, path):
#         sitkImg = sitk.ReadImage(path)
#         npImg = sitk.GetArrayFromImage(sitkImg)
#         npImg = npImg.astype('float32')
#         return npImg
    
#     def maskcroppingbox(self, img, mask, use2D=False):# 根据mask范围进行裁剪        
#         if np.count_nonzero(mask) == 0:
#             raise ValueError("Mask should have non-zero values")
#         mask_2 = np.argwhere(mask)
#         (zstart, ystart, xstart), (zstop, ystop, xstop) = mask_2.min(axis=0), mask_2.max(axis=0) + 1
#         roi_image = img[zstart - 1:zstop + 1, ystart:ystop, xstart:xstop]
#         roi_mask = mask[zstart - 1:zstop + 1, ystart:ystop, xstart:xstop]
#         roi_image[roi_mask < 1] = 0
#         return roi_image

# train_path = []
# train_label = []
# with open(os.path.join(path_origin, "train_list.txt"), "r", encoding="utf-8") as f:
#     info = f.readlines()
#     for img_info in info:
#         img_path, label = img_info.split()
#         train_path.append(img_path)
#         train_label.append(int(label))

# val_path = []
# val_label = []
# with open(os.path.join(path_origin, "val_list.txt"), "r", encoding="utf-8") as f:
#     info = f.readlines()
#     for img_info in info:
#         img_path, label = img_info.split()
#         val_path.append(img_path)
#         val_label.append(int(label))

# train_dataset = MyDataset(train_path, mode='train', labels=train_label)
# val_dataset = MyDataset(val_path, mode='val', labels=val_label)


# print("训练集标签:", train_dataset.labels)
# print("验证集标签:", val_dataset.labels)


train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=1, shuffle=True)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=1, shuffle=False)
imgs_batch, labels_batch = next(iter(train_loader ))

imgs_batch.shape

length = len(imgs_batch)
print(length)

# 创建Resnet3D分类模型并加载预训练模型
import sys
sys.path.append(r'D:\anaconda3\Lib\site-packages\MedicalNet-master\models')
sys.path.append(r'D:\anaconda3\envs\MedicalNet-master')
# D:\anaconda3\Lib\site-packages\MedicalNet-master\models
sys.path
from models import resnet

def generate_model(model_type='resnet', model_depth=50,
                   input_W=224, input_H=224, input_D=70, resnet_shortcut='B',
                   no_cuda=False, gpu_id=[0],
                   pretrain_path = r'D:\2. python深度学习课程\MedicalNet_pytorch_files2\pretrain\resnet_50.pth',
                   nb_class=1):
    assert model_type in [
        'resnet'
    ]

    if model_type == 'resnet':
        assert model_depth in [10, 18, 34, 50, 101, 152, 200]

    if model_depth == 10:
        model = resnet.resnet10(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 256
    elif model_depth == 18:
        model = resnet.resnet18(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 512
    elif model_depth == 34:
        model = resnet.resnet34(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 512
    elif model_depth == 50:
        model = resnet.resnet50(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 101:
        model = resnet.resnet101(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 152:
        model = resnet.resnet152(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 200:
        model = resnet.resnet200(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048

    model.conv_seg = nn.Sequential(nn.AdaptiveAvgPool3d((1, 1, 1)), nn.Flatten(),
                                   nn.Linear(in_features=fc_input, out_features=nb_class, bias=True))

    if not no_cuda:
        if len(gpu_id) > 1:
            model = model.cuda()
            model = nn.DataParallel(model, device_ids=gpu_id)
            net_dict = model.state_dict()
        else:
            import os
            os.environ["CUDA_VISIBLE_DEVICES"]=str(gpu_id[0])
            model = model.cuda()
            model = nn.DataParallel(model, device_ids=None)
            net_dict = model.state_dict()
    else:
        net_dict = model.state_dict()

    print('loading pretrained model {}'.format(pretrain_path))
    pretrain = torch.load(pretrain_path)
    pretrain_dict = {k: v for k, v in pretrain['state_dict'].items() if k in net_dict.keys()}
    # k 是每一层的名称，v是权重数值
    net_dict.update(pretrain_dict) #字典 dict2 的键/值对更新到 dict 里。
    model.load_state_dict(net_dict) #model.load_state_dict()函数把加载的权重复制到模型的权重中去

    print("-------- pre-train model load successfully --------")

    return model


model = generate_model(model_type='resnet', model_depth=50,
                   input_W=224, input_H=224, input_D=70, resnet_shortcut='B',
                   no_cuda=False, gpu_id=[0],
                   pretrain_path = r'D:\anaconda3\envs\MedicalNet-master\pretrain\resnet_50.pth',
                   nb_class=2)

device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =',device)


# 使用较小的学习率进行微调
from torch.optim import lr_scheduler
optimizer  = torch.optim.Adam(model.parameters(), lr=0.0001)
criterion = nn.CrossEntropyLoss()
# exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

def train(model, optimizer, criterion, train_loader, val_loader, epochs):
    best_acc = 0.0
    for epoch in range(epochs):
        model.train()
        train_loss = 0.0
        train_corrects = 0
        
        for inputs, labels in train_loader:
            inputs = inputs.float()
            labels = labels.squeeze()
            
            optimizer.zero_grad()
            
            outputs = model(inputs)
            _, preds = torch.max(outputs, 1)
            
            loss = criterion(outputs, labels)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item() * inputs.size(0)
            train_corrects += torch.sum(preds == labels.data)
        
        train_loss = train_loss / len(train_loader.dataset)
        train_acc = train_corrects.double() / len(train_loader.dataset)
        
        model.eval()
        val_loss = 0.0
        val_corrects = 0
        
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs = inputs.float()
                labels = labels.squeeze()
                
                outputs = model(inputs)
                _, preds = torch.max(outputs, 1)
                
                loss = criterion(outputs, labels)
                
                val_loss += loss.item() * inputs.size(0)
                val_corrects += torch.sum(preds == labels.data)
        
        val_loss = val_loss / len(val_loader.dataset)
        val_acc = val_corrects.double() / len(val_loader.dataset)
        
        if val_acc > best_acc:
            best_acc = val_acc
            torch.save(model.state_dict(), 'best_model.pt')
        
        print('Epoch: {} Train Loss: {:.4f} Train Acc: {:.4f} Val Loss: {:.4f} Val Acc: {:.4f}'.format(
            epoch, train_loss, train_acc, val_loss, val_acc))


train(model, optimizer, criterion, train_loader, val_loader, epochs=20)