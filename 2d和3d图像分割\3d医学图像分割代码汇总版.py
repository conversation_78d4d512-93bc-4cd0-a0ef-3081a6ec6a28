#!/usr/bin/env python
# coding: utf-8

#%% Autodl云服务器和本地图像快速传输方法
# 复制您的ssh登录指令，指令格式为：ssh -p 37328 <EMAIL>

# （注意35394为端口号，region-1.autodl.com为远程地址，请更换为您的实例端口和地址）

# 那么scp远程拷贝文件的指令为：
# scp -rP 37328 ""H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\598HCC\image.zip"" <EMAIL>:/root/autodl-tmp 
# （注意需要在您本地的机器的终端上执行）

# 如果是将实例中的数据拷贝到本地，那么scp远程拷贝指令为：
# scp -rP 35394 root@************:<实例中的文件/文件夹> <本地文件/文件夹>

#%% 检查nii.gz图像shape
import nibabel as nib
import os

# 指定图像路径
image_dir = r'h:\1.HCC-dataset\734HCC\all-HCC\585HCC-suzhou\585HCC\image\ap2'

# 遍历目录中的所有 .nii.gz 文件
for filename in os.listdir(image_dir):
    if filename.endswith('.nii.gz'):
        # 加载图像
        img_path = os.path.join(image_dir, filename)
        img = nib.load(img_path)
        
        # 获取图像数据
        image_data = img.get_fdata()
        
        # 打印形状和维度数
        print(f"Image shape for {filename}: {image_data.shape}, Number of dimensions: {image_data.ndim}")

#%%检查nii.gz图像shape,保留前3个维度,并保存图像
import nibabel as nib
import os

# 指定图像路径
image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset110_HCC/imagesTr'
output_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset110_HCC/imagesTr'  # 输出路径

# 确保输出目录存在
os.makedirs(output_dir, exist_ok=True)

# 遍历目录中的所有 .nii.gz 文件
for filename in os.listdir(image_dir):
    if filename.endswith('.nii.gz'):
        # 加载图像
        img_path = os.path.join(image_dir, filename)
        img = nib.load(img_path)
        
        # 获取图像数据
        image_data = img.get_fdata()
        
        # 只保留前三个维度
        if image_data.ndim > 3:
            # image_data = image_data[..., :3]  # 保留前三个维度
            image_data = image_data[:, :, :, 0, 0]  # 直接去掉最后两个维度
        
        # 创建新的 NIfTI 图像对象
        new_img = nib.Nifti1Image(image_data, img.affine, img.header)
        
        # 保存新的图像
        new_filename = os.path.join(output_dir, filename)
        nib.save(new_img, new_filename)
        
        # 打印新的形状
        print(f"Image shape for {filename}: {image_data.shape}, Number of dimensions: {image_data.ndim}")

#%% 要检查image和mask目录下的文件形状shape是否匹配
import os
import SimpleITK as sitk

# 目标目录
# image_dir = r"H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp"
# mask_dir = r"H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\mask\hbp"

image_dir =  '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTr'
mask_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTr'

# 获取目录下的所有文件
image_files = sorted(os.listdir(image_dir))
mask_files = sorted(os.listdir(mask_dir))

# 检查文件形状是否匹配
for image_file, mask_file in zip(image_files, mask_files):
    image_path = os.path.join(image_dir, image_file)
    mask_path = os.path.join(mask_dir, mask_file)
    
    # 读取图像和掩码文件
    image = sitk.ReadImage(image_path)
    mask = sitk.ReadImage(mask_path)
    
    # 获取形状
    image_shape = image.GetSize()
    mask_shape = mask.GetSize()
    
    # 检查形状是否匹配
    if image_shape != mask_shape:
        print(f"Shape mismatch: {image_file} (image) vs {mask_file} (mask)")
        print(f"Image shape: {image_shape}, Mask shape: {mask_shape}")


#%%3D Unet 医学图像分割代码O1-preview优化版
#!/usr/bin/env python
# coding: utf-8
import os
import random
import numpy as np
import SimpleITK as sitk
import torch
from PIL import Image
import glob
import matplotlib.pyplot as plt
import cv2
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from torch.optim import lr_scheduler
from tqdm import tqdm
import math

# 设置随机种子以保证结果可复现
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

# 配置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 数据路径
image_dir = r'h:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\108HCC-13个重复\image\ap'
label_dir = r'h:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\108HCC-13个重复\mask\ap'

# 获取图像和标签路径列表
image_list = [os.path.join(image_dir, file_name) for file_name in os.listdir(image_dir) if file_name.endswith('.nii.gz')]
label_list = [os.path.join(label_dir, file_name) for file_name in os.listdir(label_dir) if file_name.endswith('.nii.gz')]

# 确保图像和标签列表排序一致
image_list.sort()
label_list.sort()

print(f"Total images: {len(image_list)}, Total labels: {len(label_list)}")

# 定义数据集类
class D3UnetData(Dataset):
    def __init__(self, image_list, label_list, transform=None):
        self.image_list = image_list
        self.label_list = label_list
        self.transform = transform
        # 计算数据集中最大的深度（切片数量）
        self.max_depth = self.get_max_depth()

    def get_max_depth(self):
        max_depth = 0
        for label_path in self.label_list:
            label = sitk.ReadImage(label_path)
            label_array = sitk.GetArrayFromImage(label)
            depth = label_array.shape[0]
            if depth > max_depth:
                max_depth = depth
        return max_depth

    def __len__(self):
        return len(self.image_list)

    def __getitem__(self, index):
        # 读取图像和标签
        image = sitk.ReadImage(self.image_list[index])
        label = sitk.ReadImage(self.label_list[index])

        image_array = sitk.GetArrayFromImage(image).astype(np.float32)
        label_array = sitk.GetArrayFromImage(label).astype(np.int64)

        # 数据归一化
        if image_array.max() != image_array.min():
            image_array = (image_array - image_array.min()) / (image_array.max() - image_array.min())
        else:
            image_array = np.zeros_like(image_array)

        # 调整深度（切片数量），在前后进行对称填充
        depth_diff = self.max_depth - image_array.shape[0]
        if depth_diff > 0:
            pad_before = depth_diff // 2
            pad_after = depth_diff - pad_before
            image_array = np.pad(image_array, ((pad_before, pad_after), (0, 0), (0, 0)), mode='constant', constant_values=0)
            label_array = np.pad(label_array, ((pad_before, pad_after), (0, 0), (0, 0)), mode='constant', constant_values=0)
        elif depth_diff < 0:
            crop_before = (-depth_diff) // 2
            crop_after = (-depth_diff) - crop_before
            if crop_after == 0:
                image_array = image_array[crop_before:, :, :]
                label_array = label_array[crop_before:, :, :]
            else:
                image_array = image_array[crop_before:-crop_after, :, :]
                label_array = label_array[crop_before:-crop_after, :, :]

        # 转换为 Tensor
        image_tensor = torch.from_numpy(image_array).unsqueeze(0)  # Shape: [1, D, H, W]
        label_tensor = torch.from_numpy(label_array).long()        # Shape: [D, H, W]

        # 应用变换
        if self.transform:
            image_tensor = self.transform(image_tensor)
            label_tensor = self.transform(label_tensor.unsqueeze(0)).squeeze(0)

        return image_tensor, label_tensor

# 定义图像变换
transform = transforms.Compose([
    transforms.Resize((96, 96)),  # 调整图像尺寸
])

# 划分训练集和验证集
split_idx = int(len(image_list) * 0.7)
train_images = image_list[:split_idx]
train_labels = label_list[:split_idx]
test_images = image_list[split_idx:]
test_labels = label_list[split_idx:]

# 创建数据集和数据加载器
train_dataset = D3UnetData(train_images, train_labels, transform=transform)
test_dataset = D3UnetData(test_images, test_labels, transform=transform)

batch_size = 1  # 根据显存大小调整
train_dl = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
test_dl = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

# 可视化样本
def visualize_sample(image_tensor, label_tensor):
    image_array = image_tensor.squeeze(0).numpy()  # [D, H, W]
    label_array = label_tensor.numpy()  # [D, H, W]
    slice_idx = image_array.shape[0] // 2  # 选择中间的切片
    plt.figure(figsize=(10, 5))
    plt.subplot(1, 2, 1)
    plt.imshow(image_array[slice_idx], cmap='gray')
    plt.title('Image Slice')
    plt.subplot(1, 2, 2)
    plt.imshow(label_array[slice_idx], cmap='gray')
    plt.title('Label Slice')
    plt.show()

# 检查数据和标签是否正确
image_tensor, label_tensor = train_dataset[0]
visualize_sample(image_tensor, label_tensor)

# 定义模型
class DoubleConv(nn.Module):
    def __init__(self, in_channels, out_channels, num_groups=8):
        super(DoubleConv, self).__init__()
        self.double_conv = nn.Sequential(
            nn.Conv3d(in_channels, out_channels, kernel_size=3, stride=1, padding=1),
            nn.GroupNorm(num_groups=num_groups, num_channels=out_channels),
            nn.ReLU(inplace=True),

            nn.Conv3d(out_channels, out_channels, kernel_size=3, stride=1, padding=1),
            nn.GroupNorm(num_groups=num_groups, num_channels=out_channels),
            nn.ReLU(inplace=True),
        )

    def forward(self, x):
        return self.double_conv(x)

class Down(nn.Module):

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.MaxPool3d(2, 2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.encoder(x)

class Up(nn.Module):

    def __init__(self, in_channels, out_channels, trilinear=True):
        super().__init__()

        if trilinear:
            self.up = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose3d(in_channels // 2, in_channels // 2, kernel_size=2, stride=2)

        self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)

        diffZ = x2.size()[2] - x1.size()[2]
        diffY = x2.size()[3] - x1.size()[3]
        diffX = x2.size()[4] - x1.size()[4]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2,
                        diffZ // 2, diffZ - diffZ // 2])

        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class Out(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size=1)

    def forward(self, x):
        return self.conv(x)

class UNet3d(nn.Module):
    def __init__(self, in_channels, n_classes, n_channels):
        super().__init__()
        self.in_channels = in_channels
        self.n_classes = n_classes
        self.n_channels = n_channels

        self.conv = DoubleConv(in_channels, n_channels)
        self.enc1 = Down(n_channels, 2 * n_channels)
        self.enc2 = Down(2 * n_channels, 4 * n_channels)
        self.enc3 = Down(4 * n_channels, 8 * n_channels)
        self.enc4 = Down(8 * n_channels, 8 * n_channels)

        self.dec1 = Up(16 * n_channels, 4 * n_channels)
        self.dec2 = Up(8 * n_channels, 2 * n_channels)
        self.dec3 = Up(4 * n_channels, n_channels)
        self.dec4 = Up(2 * n_channels, n_channels)
        self.out = Out(n_channels, n_classes)

    def forward(self, x):
        x1 = self.conv(x)
        x2 = self.enc1(x1)
        x3 = self.enc2(x2)
        x4 = self.enc3(x3)
        x5 = self.enc4(x4)

        mask = self.dec1(x5, x4)
        mask = self.dec2(mask, x3)
        mask = self.dec3(mask, x2)
        mask = self.dec4(mask, x1)
        mask = self.out(mask)
        return mask

# 实例化模型
model = UNet3d(1, 2, 24).to(device)

# 检查模型输出
img, label = next(iter(train_dl))
print(img.shape, label.shape)
img = img.to(device)
pred = model(img)
print(pred.shape)

# 定义损失函数
class DiceLoss(nn.Module):
    def __init__(self, smooth=1e-5):
        super(DiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, inputs, targets):
        # Apply softmax to get probabilities
        inputs = torch.softmax(inputs, dim=1)[:, 1, :, :, :]  # 选择第1类（病灶）
        targets = (targets == 1).float()  # 将目标转换为二值化的病灶类别
        inputs = inputs.contiguous().view(-1)
        targets = targets.contiguous().view(-1)
        intersection = (inputs * targets).sum()
        union = inputs.sum() + targets.sum()
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        loss = 1 - dice
        return loss

# 定义综合损失函数（交叉熵 + Dice Loss）
dice_loss = DiceLoss()
# 计算类别权重，解决类别不平衡问题
background_weight = 0.1
lesion_weight = 0.9
class_weights = torch.tensor([background_weight, lesion_weight]).to(device)
cross_entropy_loss = nn.CrossEntropyLoss(weight=class_weights)

def combined_loss_fn(inputs, targets):
    loss_ce = cross_entropy_loss(inputs, targets)
    loss_dice = dice_loss(inputs, targets)
    return loss_ce + loss_dice

# 定义优化器和学习率调度器
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-5)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 定义IOU计算函数
def calculate_iou(y_pred, y_true):
    y_pred = (y_pred == 1).float()
    y_true = (y_true == 1).float()
    intersection = (y_pred * y_true).sum()
    union = y_pred.sum() + y_true.sum() - intersection
    if union == 0:
        return float('nan')  # 如果分母为零，IOU未定义
    else:
        return (intersection / union).item()

# 定义模型预测结果可视化函数
def visualize_predictions(x, y_true, y_pred):
    x = x.cpu().numpy()[0, 0, :, :, :]  # 输入图像
    y_true = y_true.cpu().numpy()[0, :, :, :]  # 真实标签
    y_pred = y_pred.cpu().numpy()[0, :, :, :]  # 预测结果

    slice_idx = y_true.shape[0] // 2  # 中间的切片

    plt.figure(figsize=(15, 5))
    plt.subplot(1, 3, 1)
    plt.imshow(x[slice_idx], cmap='gray')
    plt.title('Input Image')
    plt.subplot(1, 3, 2)
    plt.imshow(y_true[slice_idx], cmap='gray')
    plt.title('Ground Truth')
    plt.subplot(1, 3, 3)
    plt.imshow(y_pred[slice_idx], cmap='gray')
    plt.title('Predicted Mask')
    plt.show()

# 定义训练函数
def train(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []

    model.train()
    for x, y in tqdm(trainloader):
        x, y = x.to(device), y.to(device)
        y_pred = model(x)
        loss = combined_loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred_classes = torch.argmax(y_pred, dim=1)
            correct += (y_pred_classes == y).sum().item()
            total += y.numel()
            running_loss += loss.item()

            batch_iou = calculate_iou(y_pred_classes, y)
            if not math.isnan(batch_iou):
                epoch_iou.append(batch_iou)

    epoch_loss = running_loss / len(trainloader)
    epoch_acc = correct / total

    # 更新学习率
    exp_lr_scheduler.step()

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    epoch_test_iou = []

    model.eval()
    with torch.no_grad():
        for x, y in tqdm(testloader):
            x, y = x.to(device), y.to(device)
            y_pred = model(x)
            loss = combined_loss_fn(y_pred, y)
            y_pred_classes = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred_classes == y).sum().item()
            test_total += y.numel()
            test_running_loss += loss.item()

            batch_iou = calculate_iou(y_pred_classes, y)
            if not math.isnan(batch_iou):
                epoch_test_iou.append(batch_iou)

    epoch_test_loss = test_running_loss / len(testloader)
    epoch_test_acc = test_correct / test_total

    # 可视化部分结果
    visualize_predictions(x, y, y_pred_classes)

    if np.mean(epoch_test_iou) > 0.9:
        static_dict = model.state_dict()
        torch.save(static_dict, './checkpoint/{}_trainIOU_{}_testIOU_{}.pth'.format(
            epoch, round(np.mean(epoch_iou), 3), round(np.mean(epoch_test_iou), 3)))
    print('epoch: ', epoch,
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean(epoch_iou), 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
          'test_iou:', round(np.mean(epoch_test_iou), 3)
          )

    return epoch_loss, epoch_acc, epoch_iou, epoch_test_loss, epoch_test_acc, epoch_test_iou

# 开始训练
epochs = 50

train_loss = []
train_acc = []
train_iou = []
test_loss = []
test_acc = []
test_iou = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou_values, epoch_test_loss, epoch_test_acc, epoch_test_iou_values = train(epoch, model, train_dl, test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(np.mean(epoch_iou_values))
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(np.mean(epoch_test_iou_values))

# 检查模型输出
img, label = next(iter(train_dl))
print(img.shape, label.shape)

#%%模型预测最终版
from tqdm import tqdm

# 配置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 加载模型
model_path = 'best_model.pth'
model = UNet3d(1, 2, 24).to(device)  # 请确保 UNet3d 定义在同一文件或已正确导入

# 如果模型是在 GPU 上训练的，而现在在 CPU 上加载，需要使用 map_location 参数
# model.load_state_dict(torch.load(model_path, map_location=device))
model.load_state_dict(torch.load(model_path))

# 测试模型并保存预测结果
def predict_and_save(model, dataset, save_dir):
    model.eval()
    os.makedirs(save_dir, exist_ok=True)
    with torch.no_grad():
        for idx in tqdm(range(len(dataset)), desc="Predicting"):
            try:
                inputs, _ = dataset[idx]
                inputs = inputs.unsqueeze(0).to(device)
                outputs = model(inputs)
                _, preds = torch.max(outputs, 1)

                # 将预测结果转换为 numpy 数组
                pred_array = preds.squeeze().cpu().numpy().astype(np.uint8)
     
                # 获取原始图像路径以命名保存的文件
                image_path = dataset.image_list[idx]
                image_name = os.path.basename(image_path)
                pred_name = image_name.replace('.nii.gz', '_pred.nii.gz')
                save_path = os.path.join(save_dir, pred_name)

                # 保存预测结果为 NIfTI 格式
                original_image = sitk.ReadImage(image_path)
                pred_image = sitk.GetImageFromArray(pred_array)
                pred_image.CopyInformation(original_image)
                sitk.WriteImage(pred_image, save_path) 
                print(f"Saved prediction to {save_path}")
            except Exception as e:
                print(f"Error processing {idx}-th sample: {e}")

# 保存测试集的预测结果
save_prediction_dir = r'H:\predicted_masks_optimized'
os.makedirs(save_prediction_dir, exist_ok=True)

predict_and_save(model, test_dataset, save_prediction_dir)


#%% In[1]:3dUnet修改版，已成功，效果不错
from posixpath import join
import os
import sys
import random
from torchvision.transforms import RandomCrop
import numpy as np
import SimpleITK as sitk
import torch
print(torch.__version__)
from PIL import Image
import torch
import torchvision
import glob 
import pandas as pd
import matplotlib   #pycharm中要加这行代码，否则不能出图
# matplotlib.use('TkAgg') #pycharm中要加这行代码，否则不能出图
import matplotlib.pyplot as plt
import time
import cv2
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import PIL
from torch.optim import lr_scheduler  # 导入 lr_scheduler

torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

#%% 第1种方法：获取image和mask的列表list

image_dir = r'h:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\108HCC-13个重复\image\ap'
label_dir = r'h:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\108HCC-13个重复\mask\ap'

# image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTr'
# label_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTr'


image_list = []
label_list = []

for file_name in os.listdir(image_dir):
    file_path = os.path.join(image_dir, file_name)
    print(file_path)
    image_list.append(file_path)

for file_name in os.listdir(label_dir):
    file_path = os.path.join(label_dir, file_name)
    print(file_path)
    label_list.append(file_path)

image_list [:5];label_list[:5]
len(label_list);len(image_list)

#%找到肿瘤区域最大层数
def get_array_from_slice(file_path):
    image = sitk.ReadImage(file_path)
    img_array = sitk.GetArrayFromImage(image)
    shape = img_array.shape
    print(shape)

for i in image_list:
    get_array_from_slice(i)

#找到肿瘤区域最大层数
max_layers = 0
for label in label_list:
    label_ct = sitk.ReadImage(label, sitk.sitkInt8)
    label_array = sitk.GetArrayFromImage(label_ct)
    label_array[label_array > 0] = 1
    non_zero_indices = np.nonzero(label_array)
    if len(non_zero_indices[0]) > 0:
        min_indices = np.min(non_zero_indices, axis=1)
        max_indices = np.max(non_zero_indices, axis=1)
        num_layers = max_indices[0] - min_indices[0] + 1
        if num_layers > max_layers:
            max_layers = num_layers
print("Maximum number of layers in the non-zero region of masks:", max_layers)
print(label)

# In[5]: 第2种方法获取图像和标签列表的方法
# jpg图像数组堆叠代码
# img_list=glob.glob(jpg图像路径)
# def stackImg(img_slice):
#     img_read_list=[]
#     for i in img_slice:
#         img_read = cv2.imread(i)
#         img_array=img_read[:,:,0]
#         img_read_list.append(img_array)
#     img_stack=np.stack(img_read_list,axis=0)#
#     return img_stack#
# img_for3dnet=stackImg(img_list)
# img_for3dnet.shape

## 第2种方法获取图像和标签列表的方法
# image_list  = glob.glob(r'I:\1.HCC-VETC\datasets\HCC-suzhou2\image\*\*.nii.gz')
# image_list [:5]
# len(image_list)

# label_list = glob.glob(r'I:\1.HCC-VETC\datasets\HCC-suzhou2\mask\*\*.nii.gz')
# len(label_list)
# label_list [:5]

#%%3d模型需要所有病例的层数一样；
# 首先找到所有病灶label_array对应的非0区域的最大层数，
# 使得所有病灶的label_array和ct_array的层数均扩展为最大层数

class D3UnetData(Dataset):
    def __init__(self, image_list, label_list, transformer):
        self.image_list = image_list
        self.label_list = label_list
        self.transformer = transformer
        # Calculate the maximum number of layers in the label_list
        max_layers = 0
        for label in label_list:
            label_ct = sitk.ReadImage(label, sitk.sitkInt8)
            label_array = sitk.GetArrayFromImage(label_ct)
            label_array[label_array > 0] = 1
            non_zero_indices = np.nonzero(label_array)
            if non_zero_indices[0].size > 0:
                min_indices = np.min(non_zero_indices, axis=1)
                max_indices = np.max(non_zero_indices, axis=1)
                num_layers = max_indices[0] - min_indices[0] + 1
                if num_layers > max_layers:
                    max_layers = num_layers
        print(max_layers)
        self.max_layers = max_layers

    def __getitem__(self, index):
        image = self.image_list[index]
        label = self.label_list[index]
        image_ct = sitk.ReadImage(image, sitk.sitkInt16)
        label_ct = sitk.ReadImage(label, sitk.sitkInt8)
        ct_array = sitk.GetArrayFromImage(image_ct)
        label_array = sitk.GetArrayFromImage(label_ct)
        label_array[label_array > 0] = 1
        non_zero_indices = np.nonzero(label_array)
        if non_zero_indices[0].size > 0:
            min_indices = np.min(non_zero_indices, axis=1)
            max_indices = np.max(non_zero_indices, axis=1)
        # min_indices = np.min(non_zero_indices, axis=1)
        # max_indices = np.max(non_zero_indices, axis=1)
        # Adjust minimum indices if they are negative
        else:
            min_indices = np.array([0, 0, 0])
            max_indices = np.array([0, 0, 0])
        min_indices = np.maximum(min_indices, 0)
        max_indices = np.maximum(max_indices, 0)
        # Ensure max_indices are at least as large as min_indices
        max_layers = max_indices[0] - min_indices[0] + 1
        # Pad the ct_array and label_array to match the max_layers
        if max_layers < self.max_layers:
            pad_layers = self.max_layers - max_layers
            ct_padding = np.zeros((pad_layers, ct_array.shape[1], ct_array.shape[2]), dtype=np.float32)
            label_padding = np.zeros((pad_layers, label_array.shape[1], label_array.shape[2]), dtype=np.int64)
            ct_array = np.concatenate((ct_array, ct_padding), axis=0)
            label_array = np.concatenate((label_array, label_padding), axis=0)
        ct_array = ct_array[min_indices[0]:min_indices[0] + self.max_layers, min_indices[1]:max_indices[1] + 1,
                   min_indices[2]:max_indices[2] + 1]
        label_array = label_array[min_indices[0]:min_indices[0] + self.max_layers, min_indices[1]:max_indices[1] + 1,
                      min_indices[2]:max_indices[2] + 1]
        ct_array = ct_array.astype(np.float32)
        ct_array = torch.FloatTensor(ct_array).unsqueeze(0)
        label_array = torch.LongTensor(label_array)
        ct_array = self.transformer(ct_array)
        label_array = self.transformer(label_array)
        return ct_array, label_array

    def __len__(self):
        return len(self.image_list)

##划分训练和验证集
i = int(len(image_list)*0.7)
train_images = image_list[:i]
train_labels = label_list[:i]
len(train_images)

test_images = image_list[i: ]
test_labels = label_list[i: ]
len(test_images)
test_images[:5]
test_labels[:5]

# In[6]:

transformer=transforms.Compose([
    transforms.Resize((96,96)),  
])

# transformer=transforms.Compose([
#     transforms.Resize((96,96)),
#     transforms.ToTensor(),
# ])

# In[7]:

train_ds=D3UnetData(train_images,train_labels,transformer)
test_ds=D3UnetData(test_images,test_labels,transformer)
len(train_ds)
len(test_ds)

# In[8]:

train_dl=DataLoader(train_ds,batch_size=3,shuffle=True)
test_dl=DataLoader(test_ds,batch_size=3,shuffle=True)

# In[9]:

img,label=next(iter(train_dl))
print(img.shape,label.shape)

## In[10]:

img_show=img[0,0,5,:,:].cpu().numpy()
plt.imshow(img_show,cmap='gray')

# In[11]:

label_show=label[0,5,:,:].cpu().numpy()
plt.imshow(label_show,cmap='gray')

img.shape

#%% In[13]:
class DoubleConv(nn.Module):
    def __init__(self,in_channels,out_channels,num_groups=1):
        super(DoubleConv,self).__init__()
        self.double_conv=nn.Sequential(
            nn.Conv3d(in_channels,out_channels,kernel_size=3,stride=1,padding=1),
            #nn.BatchNorm3d(out_channels),
            nn.GroupNorm(num_groups=num_groups,num_channels=out_channels),
            nn.ReLU(inplace=True),
            
            nn.Conv3d(out_channels,out_channels,kernel_size=3,stride=1,padding=1),
            nn.GroupNorm(num_groups=num_groups,num_channels=out_channels),
            nn.ReLU(inplace=True),
        )
                
    def forward(self,x):
        return self.double_conv(x)
    
# img.shape
# net=DoubleConv(1,64,num_groups=1)
# out=net(img)
# print(out.shape)
# In[15]:
class Down(nn.Module):

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.MaxPool3d(2, 2),
            DoubleConv(in_channels, out_channels)
        )
    def forward(self, x):
        return self.encoder(x)
    
class Up(nn.Module):

    def __init__(self, in_channels, out_channels, trilinear=True):
        super().__init__()
        
        if trilinear:
            self.up = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose3d(in_channels // 2, in_channels // 2, kernel_size=2, stride=2)
            
        self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)

        diffZ = x2.size()[2] - x1.size()[2]
        diffY = x2.size()[3] - x1.size()[3]
        diffX = x2.size()[4] - x1.size()[4]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2, diffZ // 2, diffZ - diffZ // 2])

        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

    
class Out(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size = 1)

    def forward(self, x):
        return self.conv(x)

class UNet3d(nn.Module):
    def __init__(self, in_channels, n_classes, n_channels):
        super().__init__()
        self.in_channels = in_channels
        self.n_classes = n_classes
        self.n_channels = n_channels

        self.conv = DoubleConv(in_channels, n_channels)
        self.enc1 = Down(n_channels, 2 * n_channels)
        self.enc2 = Down(2 * n_channels, 4 * n_channels)
        self.enc3 = Down(4 * n_channels, 8 * n_channels)
        self.enc4 = Down(8 * n_channels, 8 * n_channels)

        self.dec1 = Up(16 * n_channels, 4 * n_channels)
        self.dec2 = Up(8 * n_channels, 2 * n_channels)
        self.dec3 = Up(4 * n_channels, n_channels)
        self.dec4 = Up(2 * n_channels, n_channels)
        self.out = Out(n_channels, n_classes)

    def forward(self, x):
        x1 = self.conv(x)
        x2 = self.enc1(x1)
        x3 = self.enc2(x2)
        x4 = self.enc3(x3)
        x5 = self.enc4(x4)

        mask = self.dec1(x5, x4)
        mask = self.dec2(mask, x3)
        mask = self.dec3(mask, x2)
        mask = self.dec4(mask, x1)
        mask = self.out(mask)
        return mask
#%% 
# n_classes=2
model=UNet3d(1,2,24).cuda() #1表示输入的channel；2表示2个分类，病灶和背景；24是通道数，不用改
img,label=next(iter(train_dl))
print(img.shape,label.shape)
img=img.cuda()
pred=model(img)
pred.shape

# In[17]:
loss_fn=nn.CrossEntropyLoss()
optimizer=torch.optim.Adam(model.parameters(),lr=0.00005)
# 使用 L2 正则化（权重衰减）来限制模型的复杂性
# optimizer = torch.optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-5)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

## 定义训练函数
from tqdm import tqdm
def train(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []
    
    model.train()
    for x, y in tqdm(trainloader): 
        x, y = x.to('cuda'), y.to('cuda')        
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_iou.append(batch_iou.item())
            
    epoch_loss = running_loss / len(trainloader.dataset)       
    # 计算准确率时，考虑每个体素的数量
    epoch_acc = correct / (total * x.size(2) * x.size(3) * x.size(4))  
    # epoch_acc = correct / (total*96*96*max_layers)#max_layers为训练集最大层数 
    
    # 更新学习率
    exp_lr_scheduler.step()  # 在每个 epoch 结束时更新学习率
           
    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    epoch_test_iou = []
    
    model.eval()
    with torch.no_grad():
        for x, y in tqdm(testloader):

            x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_test_iou.append(batch_iou.item())            
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    # epoch_test_acc = test_correct / (test_total*96*96*max_layers) #96*96为图像大小，64为验证集最大层数
    epoch_test_acc = test_correct / (test_total* x.size(2) * x.size(3) * x.size(4)) 
    
    if np.mean(epoch_test_iou)>0.9:
            static_dict=model.state_dict()
            torch.save(static_dict,'./checkpoint/{}_trainIOU_{}_testIOU_{}.pth'.format(epoch,round(np.mean(epoch_iou), 3),round(np.mean(epoch_test_iou),3)))
    print('epoch: ', epoch, 
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean(epoch_iou), 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
           'test_iou:', round(np.mean(epoch_test_iou), 3)
             )
        
    return epoch_loss, epoch_acc,epoch_iou,epoch_test_loss, epoch_test_acc,epoch_test_iou

# In[19]:
epochs = 10

train_loss = []
train_acc = []
train_iou = []
test_loss = []
test_acc = []
test_iou = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou, epoch_test_loss, epoch_test_acc,epoch_test_iou = train(epoch,model,train_dl, test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)

##
img,label=next(iter(train_dl))
print(img.shape,label.shape)

#In[]:
img=img.to('cuda')
pred=model(img)
pred.shape

# In[ ]:
label_show=label[0,28,:,:]
plt.imshow(label_show,cmap='gray')

# In[ ]:
pred=pred[:,:,20,:,:]
pred.shape

# In[ ]:
preds = pred.cpu()
print(preds.shape)
plt.imshow(torch.argmax(preds[0].permute(1, 2, 0), axis=-1).detach().numpy(),cmap='gray')

# plt.imshow(torch.argmax(preds.permute(0, 2, 3, 1), axis=-1).detach().numpy()[0], cmap='gray')
# plt.imshow(label,cmap='gray')

label.shape
# In[ ]:
import matplotlib.pyplot as plt
pred[0].shape

# plt.imshow(torch.argmax(pred.permute(1,2,0), axis=-1).detach().numpy())
plt.imshow(torch.argmax(pred[0].permute(1, 2, 0).cpu(), axis=-1).detach().numpy())
plt.axis('off')  # 关闭坐标轴
plt.show()

## 保存模型
PATH = r'h:\weight\hcc_3dunet_10epoch.pth' #路径不能有中文
# PATH = r'/root/autodl-tmp/3dunet/hcc_3dunet_model.pth' 
torch.save(model.state_dict(), PATH)

# 测试模型
model = UNet3d(1,2,24)
model.load_state_dict(torch.load(PATH))

## In[71]:
image, mask = next(iter(test_dl))
pred_mask = model(image)
pred_mask.shape
pred_mask = torch.squeeze(pred_mask)
pred_mask.shape

# 将 image 和 mask 移动到 GPU
image, mask = next(iter(test_dl))
image = image.to('cuda')
mask = mask.to('cuda')
print("Image shape:", image.shape)
print("Mask shape:", mask.shape)

num = 3
plt.figure(figsize=(10, 10))
for i in range(num):
    # 选择中间的深度切片
    depth_index = image.shape[2] // 3

    plt.subplot(num, 3, i * 3 + 1)
    plt.imshow(image[i, 0, depth_index, :, :].cpu().numpy(), cmap='gray')  # 选择第一个通道的深度切片
    plt.title("Image")

    plt.subplot(num, 3, i * 3 + 2)
    plt.imshow(mask[i, depth_index, :, :].cpu().numpy(), cmap='gray')  # 显示掩膜的深度切片
    plt.title("Mask")

    plt.subplot(num, 3, i * 3 + 3)
    plt.imshow(torch.argmax(pred_mask[i, :, depth_index, :, :], dim=0).detach().cpu().numpy(), cmap='gray')  # 显示预测掩膜的深度切片
    plt.title("Predicted Mask")

plt.tight_layout()
plt.show()

#%%批量预测测试集图像，获得预测的mask，优化完整版
import os
import glob
import numpy as np
import nibabel as nib
import torch
from torchvision import transforms
from PIL import Image
import cv2

# 加载模型
PATH = r'h:\weight\hcc_3dunet_100epoch.pth'
model = UNet3d(1, 2, 24)
model.load_state_dict(torch.load(PATH))
model.cuda()
model.eval()  # 确保模型在评估模式

# 预处理转换
transformer = transforms.Compose([
    transforms.Resize((96, 96)),  # 根据模型的输入尺寸调整
])

predmask_path = r'H:\predicted_masks2'
predimg_path = r'h:\ap2\*.nii.gz'
original_image_filepaths = glob.glob(predimg_path)
original_image_filepaths[:5]


def predict_volume(model, image_data, original_shape, device='cuda'):
    """
    预测3D图像体积的分割掩膜
    """
    with torch.no_grad():
        try:
            # 预处理
            processed_data = preprocess_volume(image_data)
            processed_data = processed_data.to(device)
            
            # 预测
            pred_mask_volume = model(processed_data)
            pred_mask_volume = (torch.argmax(pred_mask_volume, dim=1) > 0).float()[0]
            pred_mask_volume = pred_mask_volume.cpu().numpy()
            
            # 后处理
            full_mask = np.zeros(original_shape, dtype=np.uint8)
            for z in range(pred_mask_volume.shape[0]):
                resized_slice = cv2.resize(
                    pred_mask_volume[z].astype(np.float32),
                    (original_shape[1], original_shape[0]),
                    interpolation=cv2.INTER_NEAREST
                )
                full_mask[:, :, z] = (resized_slice > 0.5).astype(np.uint8)
                
            return full_mask
            
        except Exception as e:
            print(f"Error during prediction: {e}")
            return None

def preprocess_volume(image_data, target_size=(96, 96)):
    """
    预处理3D图像体积
    """
    processed_data = np.zeros((1, 1, image_data.shape[2], *target_size), dtype=np.float32)
    
    for i in range(image_data.shape[2]):
        # 获取切片并归一化
        img_slice = image_data[:, :, i].astype(float)
        if img_slice.max() != img_slice.min():
            img_slice = (img_slice - img_slice.min()) / (img_slice.max() - img_slice.min())
            
        # 调整大小
        img_slice_pil = Image.fromarray(img_slice).convert("L")
        img_slice_resized = transformer(img_slice_pil)
        processed_data[0, 0, i] = np.array(img_slice_resized)
    
    return torch.from_numpy(processed_data)

# 使用示例
for original_image_filepath in original_image_filepaths:
    try:
        # 加载原始图像
        original_image = nib.load(original_image_filepath)
        image_data = original_image.get_fdata()
        
        # 预测掩膜
        full_mask = predict_volume(model, image_data, original_image.shape)
        if full_mask is None:
            continue
            
        # 保存结果
        mask_nifti = nib.Nifti1Image(full_mask, original_image.affine)
        mask_filename = os.path.splitext(os.path.basename(original_image_filepath))[0] + '_mask.nii.gz'
        mask_filepath = os.path.join(predmask_path, mask_filename)
        nib.save(mask_nifti, mask_filepath)
        
    except Exception as e:
        print(f"Error processing {original_image_filepath}: {e}")
        continue


#%%批量预测测试集图像，获得预测的mask，并保存为nii.gz格式，已成功  方法1
import os
import glob
import numpy as np
import nibabel as nib
import torch
from torchvision import transforms
from PIL import Image
import cv2

# 加载模型
PATH = r'h:\weight\hcc_3dunet_100epoch.pth'
model = UNet3d(1, 2, 24)
model.load_state_dict(torch.load(PATH))
model.cuda()
model.eval()  # 确保模型在评估模式

# 预处理转换
transformer = transforms.Compose([
    transforms.Resize((96, 96)),  # 根据模型的输入尺寸调整
])

predmask_path = r'H:\predicted_masks2'
predimg_path = r'h:\ap2\*.nii.gz'
original_image_filepaths = glob.glob(predimg_path)
original_image_filepaths[:5]

for original_image_filepath in original_image_filepaths:  # 遍历原始图像文件路径列表
    original_image = nib.load(original_image_filepath)  # 加载原始图像
    original_shape = original_image.shape  # 获取原始图像的形状    
    original_affine = original_image.affine  # 获取原始图像的仿射变换矩阵    
    image_data = original_image.get_fdata()  # 获取原始图像的数据
    full_mask = np.zeros(original_shape, dtype=np.uint8)  # 初始化与原始图像大小相同的空掩膜
    print("full_mask shape:", full_mask.shape)
    
    # 将整个3D图像预处理并调整尺寸
    processed_data = torch.zeros((1, 1, original_shape[2], 96, 96), dtype=torch.float32)  # 初始化处理后的数据张量
    for i in range(original_shape[2]):
        img_slice = image_data[:, :, i]
        img_slice_pil = Image.fromarray(img_slice).convert("L")
        img_slice_resized = transformer(img_slice_pil)  # 应用转换并获取调整后的图像
        
        # 将调整后的图像转换为 NumPy 数组并赋值给处理后的数据张量
        processed_data[0, 0, i, :, :] = torch.from_numpy(np.array(img_slice_resized))  # 转换为张量并赋值

    processed_data = processed_data.to('cuda')  # 移到GPU

    with torch.no_grad():
        pred_mask_volume = model(processed_data)  # 对整个体积进行预测
        pred_mask_volume = torch.argmax(pred_mask_volume.squeeze(), dim=0).float().cpu().numpy()  
        print("pred_mask_volume shape:", pred_mask_volume.shape)    
        
    # 将预测结果调整为与 full_mask 的形状相同
    if pred_mask_volume.shape != full_mask.shape:
        # 使用 cv2.resize 进行形状调整
        resized_pred_mask_volume = np.zeros(full_mask.shape, dtype=np.uint8)  # 创建调整后的掩膜
        
        for z in range(full_mask.shape[2]):
            if z < pred_mask_volume.shape[0]:  # 确保索引不越界
                resized_pred_mask_volume[:, :, z] = cv2.resize(pred_mask_volume[z], (full_mask.shape[1], full_mask.shape[0]), interpolation=cv2.INTER_NEAREST)

        full_mask = resized_pred_mask_volume  # 将调整后的预测结果赋值给完整掩膜
        print("full_mask shape:", full_mask.shape)
  
    # 将完成的掩膜转换为 Nifti1Image 对象
    mask_nifti = nib.Nifti1Image(full_mask, affine=original_affine)
    # 为掩膜文件创建一个新的文件名
    mask_filename = os.path.splitext(os.path.basename(original_image_filepath))[0] + '_mask.nii.gz'
    # 在与原始图像相同的文件夹中保存掩膜文件
    mask_filepath = os.path.join(predmask_path, mask_filename)
    nib.save(mask_nifti, mask_filepath) 

#%%批量预测测试集图像，获得预测的mask，并保存为nii.gz格式，已成功  方法2
import os
import glob
import numpy as np
import nibabel as nib
import torch
from torchvision import transforms
from PIL import Image

# 加载模型
PATH = r'h:\weight\hcc_3dunet_model.pth'
model = UNet3d(1, 2, 24)
model.load_state_dict(torch.load(PATH))
model.cuda()
model.eval()  # 确保模型在评估模式

# 预处理转换
transformer = transforms.Compose([
    transforms.Resize((96, 96)),  # 根据模型的输入尺寸调整
])

predmask_path = r'H:\predicted_masks'
predimg_path = r'H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp\*.nii.gz'
original_image_filepaths = glob.glob(predimg_path)
original_image_filepaths[:5]

for original_image_filepath in original_image_filepaths:  # 遍历原始图像文件路径列表
    original_image = nib.load(original_image_filepath)  # 加载原始图像
    original_shape = original_image.shape  # 获取原始图像的形状    
    original_affine = original_image.affine  # 获取原始图像的仿射变换矩阵    
    image_data = original_image.get_fdata()  # 获取原始图像的数据
    full_mask = np.zeros(original_shape, dtype=np.uint8)  # 初始化与原始图像大小相同的空掩膜
    print("full_mask shape:", full_mask.shape)
    
    # 将整个3D图像预处理并调整尺寸
    processed_data = torch.zeros((1, 1, original_shape[2], 96, 96), dtype=torch.float32)  # 初始化处理后的数据张量
    for i in range(original_shape[2]):
        img_slice = image_data[:, :, i]
        img_slice_pil = Image.fromarray(img_slice).convert("L")
        img_slice_resized = transformer(img_slice_pil)  # 应用转换并获取调整后的图像
        
        # 将调整后的图像转换为 NumPy 数组并赋值给处理后的数据张量
        processed_data[0, 0, i, :, :] = torch.from_numpy(np.array(img_slice_resized))  # 转换为张量并赋值

    processed_data = processed_data.to('cuda')  # 移到GPU

    with torch.no_grad():
        pred_mask_volume = model(processed_data)  # 对整个体积进行预测
        # pred_mask_volume = torch.argmax(pred_mask_volume.squeeze(), dim=0).float().cpu().numpy()   
        binary_pred_mask_volume = (torch.argmax(pred_mask_volume, dim=1) > 0).float() 
        # 将二进制掩膜转换为 NumPy 数组
        binary_pred_mask_volume_numpy = binary_pred_mask_volume.cpu().numpy()  
        print("binary_pred_mask_volume_numpy shape:", binary_pred_mask_volume_numpy.shape)       

        # 这里我们只需要访问第一个切片
        for z in range(binary_pred_mask_volume_numpy.shape[1]):  # 遍历深度
            # 获取当前切片
            current_slice = binary_pred_mask_volume_numpy[0, z]  # 访问第一个维度的切片

            # 调整当前切片的大小
            resized_binary_pred_mask = cv2.resize(current_slice, (full_mask.shape[1], full_mask.shape[0]))

            # 将预测的掩膜添加到空掩膜中
            full_mask[:, :, z] = resized_binary_pred_mask                    
       
    # 将完成的掩膜转换为Nifti1Image对象
    mask_nifti = nib.Nifti1Image(full_mask, affine=original_affine)    
    mask_filename = os.path.splitext(os.path.basename(original_image_filepath))[0] + '_mask.nii.gz'
    mask_filepath = os.path.join(predmask_path, mask_filename)
    nib.save(mask_nifti, mask_filepath)


#%% 伪3D医学图像分割，实际是2d模型进行分割的;已运行成功
# 基于pytorch；b站多组学进击君代码  
# pip install scikit-learn segmentation_models_pytorch opencv-python
import os
import numpy as np
import nibabel as nib
from tqdm import tqdm
from sklearn.model_selection import train_test_split
import matplotlib
matplotlib.use('Qt5Agg')
%matplotlib inline
import matplotlib.pyplot as plt
from torchvision import transforms
from torch.utils import data
import numpy as np
from torch.utils.data import Dataset
import torchvision.transforms as transforms
from PIL import Image
import torch
import torch.nn as nn
import torchvision
from torch.optim import lr_scheduler
import cv2
import segmentation_models_pytorch as smp
# 获取所有模型的名称
model_names = dir(smp)
segmentation_models = [name for name in model_names]
print(segmentation_models)


def make_dataset(root):
    imgs = []
    for filename in os.listdir(root):
        if filename.endswith('.nii.gz'):
            img_path = os.path.join(root, filename)
            imgs.append(img_path)
    return imgs

def load_nii_to_array(filepath):
    img = nib.load(filepath)
    img_array = np.array(img.dataobj)
    return img_array

def load_data(image_path, mask_path):
    images = []
    masks = []
    image_files = make_dataset(image_path)
    mask_files = make_dataset(mask_path)
    for img_file, mask_file in zip(image_files, mask_files):
        img_arr = load_nii_to_array(img_file)
        mask_arr = load_nii_to_array(mask_file)
        for layer in range(img_arr.shape[2]):
            img_layer = img_arr[:,:,layer]
            mask_layer = mask_arr[:,:,layer]
            if np.sum(mask_layer) == 0:
                continue
            images.append(img_layer)
            masks.append(mask_layer)
    return np.array(images, dtype=object), np.array(masks, dtype=object)

 # Load training data
# train_image_path = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\image\ap'
# train_mask_path = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\mask\ap'

# train_image_path = '/root/autodl-tmp/3dunet/image/ap'
# train_mask_path = '/root/autodl-tmp/3dunet/mask/ap'

train_image_path  = r'h:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp'
train_mask_path  = r'h:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\mask\hbp'

train_images, train_masks = load_data(train_image_path, train_mask_path)
print(train_images.shape)
print(train_masks.shape)

#  # Load validation data
# val_image_path = r'C:\Users\<USER>\Desktop\gz-sa\ls\ls\images'
# val_mask_path = r'C:\Users\<USER>\Desktop\gz-sa\ls\ls\masks'
# val_images, val_masks = load_data(val_image_path, val_mask_path)
 # Split data into training and validation
train_images, val_images, train_masks, val_masks = train_test_split(train_images, train_masks, test_size=0.3, random_state=42)


def display_image_and_mask(image, mask):
    """
    Display the image and mask.
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    ax1.imshow(image)
    ax1.set_title('Image')
    ax1.axis('off')
    ax2.imshow(mask, cmap='gray')
    ax2.set_title('Mask')
    ax2.axis('off')
    plt.show()
 # Load the third nii.gz file
first_image_file = make_dataset(train_image_path)[2]
first_mask_file = make_dataset(train_mask_path)[2]
 # Load the image and mask as numpy arrays
first_image_array = load_nii_to_array(first_image_file)
first_mask_array = load_nii_to_array(first_mask_file)
 # Extract the 21st image and mask
image_20 = first_image_array[:, :, 20]
mask_20 = first_mask_array[:, :, 20]
 # Display the 20th image and mask
display_image_and_mask(image_20, mask_20)

class LiverDataset(Dataset):
    def __init__(self, img, mask, img_transformer, mask_transformer):
        self.img = img
        self.mask = mask
        self.img_transformer = img_transformer
        self.mask_transformer = mask_transformer
    def __getitem__(self, index):
        img = self.img[index]
        mask = self.mask[index]
        # print(img.shape)
        # Convert numpy arrays to PIL.Image
        img_pil = Image.fromarray(img)
        img_pil = img_pil.convert("RGB")
        mask_pil = Image.fromarray(mask)
        # Apply transformers
        img_tensor = self.img_transformer(img_pil).float()
        mask_tensor = self.mask_transformer(mask_pil).long()
        # Convert mask tensor to long type and remove unnecessary dimensions
        mask_tensor = torch.squeeze(mask_tensor).type(torch.LongTensor)
        return img_tensor, mask_tensor
    def __len__(self):
        return len(self.img)

# class LiverDataset(Dataset):
#     def __init__(self, img, mask, img_transformer, mask_transformer):
#         self.img = img
#         self.mask = mask
#         self.img_transformer = img_transformer
#         self.mask_transformer = mask_transformer
#     def __getitem__(self, index):
#         img = self.img[index]
#         mask = self.mask[index]
#          # Convert numpy arrays to PIL.Image
#         img_pil = Image.fromarray(img).convert("RGB")
#         mask_pil = Image.fromarray(mask).convert("L")
#          # Apply transformers
#         img_tensor = self.img_transformer(img_pil).float()
#         mask_tensor = self.mask_transformer(mask_pil).long()
#          # Convert mask tensor to long type and remove unnecessary dimensions
#         mask_tensor = torch.squeeze(mask_tensor).type(torch.LongTensor)
#         return img_tensor, mask_tensor
#     def __len__(self):
#         return len(self.img)

 # Define transformers
train_img_transforms = transforms.Compose([
    transforms.Resize((64, 64)),
    transforms.ToTensor(),
])
train_mask_transforms = transforms.Compose([
    transforms.Resize((64, 64), interpolation=Image.NEAREST),
    transforms.ToTensor(),
])

test_img_transforms = transforms.Compose([
    transforms.Resize((64, 64)),
    transforms.ToTensor(),
])

test_mask_transforms = transforms.Compose([
    transforms.Resize((64, 64), interpolation=Image.NEAREST),
    transforms.ToTensor()
])

train_data = LiverDataset(train_images, train_masks, train_img_transforms,train_mask_transforms)
test_data = LiverDataset(val_images, val_masks, test_img_transforms,test_mask_transforms)

dl_train = data.DataLoader(train_data, batch_size=16, shuffle=True)
dl_test = data.DataLoader(test_data, batch_size=16, shuffle=False)


##伪3d分割，实际是2d模型进行分割的  
# 可换成 smp.Unet；smp.PSPNet；smp.Linknet；smp.FPN；smp.DeepLabV3Plus
model = smp.PSPNet(
    encoder_name="resnet34",  # choose encoder
    encoder_weights="imagenet",  # use "imagenet" pre-training
    in_channels=3,  # grayscale1,彩色3
    classes=2,  # number of output classes
)
print(model)

img_pil, mask_pil = next(iter(dl_train))
# img = img.to('cuda')
# model = model.to('cuda')
pred = model(img_pil)
print(pred.shape)
# Size([16, 2, 128, 128])


if torch.cuda.is_available():
    model.to('cuda')

 # Define optimizer and learning rate scheduler
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
 # Define loss function
loss_fn = nn.CrossEntropyLoss()


from tqdm import tqdm
import numpy as np

def mean_iou(y_true, y_pred, num_classes=2):
    ious = []
    for cls in range(num_classes):
        y_true_cls = (y_true == cls)
        y_pred_cls = (y_pred == cls)
        intersection = torch.logical_and(y_true_cls, y_pred_cls).sum().item()
        union = torch.logical_or(y_true_cls, y_pred_cls).sum().item()
         # Avoid division by zero
        if union == 0:
            iou = 1.0
        else:
            iou = intersection / union
        ious.append(iou)
    return np.mean(ious)

def fit(epoch, model, train_loader, test_loader):
    """
    Train the model for one epoch.
    """
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []
    model.train()
    for (x, y) in tqdm(train_loader):
        x = x.to('cuda')
        y = y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
         # Calculate IoU for the training set
            batch_iou = mean_iou(y, y_pred)
            epoch_iou.append(batch_iou)
    epoch_loss = running_loss / len(train_loader.dataset)
    # epoch_acc = correct / (total * 128 * 128)  
    epoch_acc = correct / (total * x.size(2) * x.size(3))  

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    epoch_test_iou = []
    model.eval()

    with torch.no_grad():
        for x, y in tqdm(test_loader):
            x = x.to('cuda')
            # print(x.size(2))           
            y = y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_test_iou.append(batch_iou.item())
        epoch_test_loss = test_running_loss / len(test_loader.dataset)
        # epoch_test_acc = test_correct / (test_total * 128 * 128)
        epoch_test_acc = test_correct / (test_total * x.size(2) * x.size(3))
        print('epoch:', epoch,
              'loss:', round(epoch_loss, 3),
              'accuracy:', round(epoch_acc, 3),
              'IOU:', round(np.mean(epoch_iou), 3),
              'test_loss:', round(epoch_test_loss, 3),
              'test_accuracy:', round(epoch_test_acc, 3),
              'test_iou:', round(np.mean(epoch_test_iou), 3))
        return epoch_loss, epoch_acc, np.mean(epoch_iou),epoch_test_loss, epoch_test_acc, np.mean(epoch_test_iou)

# In[19]:
epochs = 20
        
train_loss = []
train_acc = []
test_loss = []
test_acc = []
test_iou = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou,epoch_test_loss, epoch_test_acc, epoch_test_iou = fit(epoch, model,dl_train,dl_test)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)

#%%保存最佳参数模型
import torch
import shutil

def save_checkpoint(state, is_best_iou, is_best_acc, filename):
    torch.save(state, filename)
    if is_best_iou:
        shutil.copyfile(filename, 'model_best_iou.pth')
    if is_best_acc:
        shutil.copyfile(filename, 'model_best_acc.pth')

train_loss = []
train_acc = []
test_loss = []
test_acc = []
train_iou = []
test_iou = []

 # Initialize best metric variables
best_test_iou = 0
best_test_acc = 0

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou,epoch_test_loss, epoch_test_acc, epoch_test_iou = fit(epoch, model,dl_train,dl_test)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)

    # Check if there is a better validation IoU
    is_best_iou = epoch_test_iou > best_test_iou
    best_test_iou = max(epoch_test_iou, best_test_iou)

    # Check if there is a better validation accuracy
    is_best_acc = epoch_test_acc > best_test_acc
    best_test_acc = max(epoch_test_acc, best_test_acc)

# Save the model for the current epoch
    save_checkpoint({
    'epoch': epoch + 1,
    'state_dict': model.state_dict(),
    'best_test_iou': best_test_iou,
    'best_test_acc': best_test_acc,
    'optimizer': optimizer.state_dict(),
    }, is_best_iou, is_best_acc,filename='h:/weight/model_best_iou.pth')

##
PATH = 'h:/weight/model_best_iou.pth' #路径不能有中文
torch.save(model.state_dict(), PATH)


 # Create a new instance of the model with the same structure
model = smp.PSPNet(
    encoder_name='resnet34',
    in_channels=3,
    classes=2
)

model.load_state_dict(torch.load(PATH))


 # Move the image to the appropriate device (e.g. GPU)
image = next(iter(test_dl))
# image = image.to('cuda')
 # Set the model to evaluation mode
model.eval()
 # Make predictions on the image
pred_mask = model(image)
pred_mask
 # Get the shape of the predicted mask
pred_mask.shape
 # Move the predicted mask to CPU
pred_mask = pred_mask.cpu()

# ##
# num = 3
# plt.figure(figsize=(10, 10))
# i= 0
# for i in range(num):
#     plt.subplot(num, 3, i*num+1)
#     plt.imshow(image[i].permute(1, 2, 0).cpu().numpy(), cmap='gray')
#     plt.subplot(num, 3, i*num+2)
#     binary_pred_mask = (torch.argmax(pred_mask[i], dim=0).cpu().numpy() == 1)
#     plt.imshow(binary_pred_mask, cmap='gray')

#%%预测新的数据集
import cv2
import glob

from torchvision import transforms
img_transformer = transforms.Compose([
    transforms.Resize((64, 64)),
    transforms.ToTensor()
])

predmask_path = r'H:\predicted_masks'
predimg_path = r'H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp2\*.nii.gz'

original_image_filepaths= glob.glob(predimg_path)
original_image_filepaths[:5]


for original_image_filepath in original_image_filepaths:  # 遍历原始图像文件路径列表
    original_image = nib.load(original_image_filepath)  # 加载原始图像
    original_shape = original_image.shape  # 获取原始图像的形状
    original_affine = original_image.affine  # 获取原始图像的仿射变换矩阵
    full_mask = np.zeros(original_shape, dtype=np.uint8)  # 初始化与原始图像大小相同的空掩膜

    for z in range(original_shape[2]):
        # 从原始图像中提取一个切片
        slice_image = original_image.get_fdata()[:, :, z]
        # 将numpy数组转换为PIL.Image格式
        img_pil = Image.fromarray(slice_image)
        img_pil = img_pil.convert("RGB")
        # 应用图像变换器
        input_image = img_transformer(img_pil).unsqueeze(0)
        # 对输入图像进行预测
        model.eval()
        pred_mask = model(input_image)
        # 获取二进制掩膜
        binary_pred_mask = (torch.argmax(pred_mask.squeeze(), dim=0) > 0).float()
        # 将二进制掩膜调整为与全掩膜相同的形状
        resized_binary_pred_mask = cv2.resize(binary_pred_mask.cpu().numpy(),
                                              (full_mask.shape[1], full_mask.shape[0]))
        # 将预测的掩膜添加到空掩膜中
        full_mask[:, :, z] = resized_binary_pred_mask
    # 将完成的掩膜转换为Nifti1Image对象
    mask_nifti = nib.Nifti1Image(full_mask, affine=original_affine)
    # 为掩膜文件创建新的文件名
    mask_filename = os.path.splitext(os.path.basename(original_image_filepath))[0] + '_mask.nii.gz'
    # 在与原始图像相同的文件夹中保存掩膜文件
    mask_filepath = os.path.join(predmask_path, mask_filename)
    # 将掩膜保存为nii.gz文件
    nib.save(mask_nifti, mask_filepath)


# %% 基于monai的3d图像分割最终版  已成功
#报错解决的关键就是image和label统一大小
# 安装依赖包
# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]"')
# get_ipython().system('python -c "import matplotlib" || pip install -q matplotlib')
# get_ipython().run_line_magic('matplotlib', 'inline')

## 引入必要的包
import os
import numpy as np
from monai.data import Dataset, DataLoader
import shutil
import tempfile
import time
import matplotlib.pyplot as plt
from monai.apps import DecathlonDataset
from monai.config import print_config
from monai.data import Dataset,DataLoader, decollate_batch
from monai.handlers.utils import from_engine
from monai.losses import DiceLoss
from monai.inferers import sliding_window_inference
from monai.metrics import DiceMetric
from monai.networks.nets import SegResNet
from monai.transforms import (
    Activations,
    Activationsd,
    AsDiscrete,
    AsDiscreted,
    Compose,
    Invertd, #可能用于反转或撤销某些转换的效果
    LoadImaged,
    MapTransform, #应用一个函数或转换到数据集的每一个元素
    NormalizeIntensityd, #应用一个函数或转换到数据集的每一个元素
    Orientationd, #调整或确保图像的方向或坐标轴对齐
    RandFlipd, #随机地对图像进行翻转，通常用于数据增强
    RandScaleIntensityd,#随机地改变图像的强度尺度，也是数据增强的一种手段
    RandShiftIntensityd, #随机地改变图像的强度尺度
    RandSpatialCropd,  #随机地改变图像的强度尺度，也是数据增强的一种手段
    Spacingd, #调整图像的体素间距或分辨率
    EnsureTyped, #确保数据具有特定的数据类型
    EnsureChannelFirstd, #确保图像数据的通道维度是第一个维度，这对于某些深度学习模型是必要的   
    Transposed
)
from monai.transforms import SpatialPad
from monai.utils import set_determinism
import torch
print_config()


#设置随机种子
set_determinism(seed=0)

# ## 根据BRATS类别将标签转换为多通道，为数据预处理做准备
# 通过创建类并继承MapTransform来实现
# - 标签1是肿瘤周围的脑水肿（Peritumoral edema）
# - 标签2是GD增强的肿瘤（GD-enhancing tumor）
# - 标签3是坏死和非增强的肿瘤核心（Necrotic and non-enhancing tumor core）
# 可能的类别是TC（肿瘤核心）、WT（整个肿瘤）和ET（增强的肿瘤）。这意味着，为了将上述的标签转换为可能的类别，我们需要进行一些组合：
# - TC（肿瘤核心）可能只包括标签3，因为标签3表示坏死和非增强的肿瘤核心。
# - WT（整个肿瘤）可能包括标签1、2和3，因为它涵盖了水肿、增强的肿瘤和非增强的肿瘤核心。
# - ET（增强的肿瘤）只包括标签2，因为标签2表示GD增强的肿瘤。
# 通过这种方式，我们可以将原始的三个标签转换为三个可能的类别，每个类别都代表肿瘤的不同方面
# class ConvertToMultiChannelBasedOnBratsClassesd(MapTransform):#适用于多个label

from monai.transforms import RandCropByPosNegLabeld
from monai.transforms import Resize, MapTransform

class ResizeTransform(MapTransform):
    def __init__(self, keys, spatial_size):
        super().__init__(keys)
        self.spatial_size = spatial_size

    def __call__(self, data):
        for key in self.keys:
            data[key] = Resize(spatial_size=self.spatial_size)(data[key])
        return data

# 假设你希望裁剪的大小为 (96, 96, 96)
crop_size = (128, 128, 128)

## 为训练集和测试集创建transform，用于数据预处理
train_transform = Compose(
    [        
        LoadImaged(keys=["image", "label"]),        
        EnsureChannelFirstd(keys=["image", "label"]),  # 确保图像和标签的通道在第一维  
        EnsureTyped(keys=["image", "label"]),  
        # ConvertToMultiChannelBasedOnBratsClassesd(keys="label"),       
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.0, 1.0, 1.0), mode=("bilinear", "nearest")),
        ResizeTransform(keys=["image", "label"], spatial_size=crop_size),  # 使用自定义 ResizeTransform
        RandSpatialCropd(keys=["image", "label"], roi_size=crop_size, random_size=False),
        RandFlipd(keys=["image", "label"], prob=0.2, spatial_axis=0),
        RandFlipd(keys=["image", "label"], prob=0.2, spatial_axis=1),
        RandFlipd(keys=["image", "label"], prob=0.2, spatial_axis=2),
        NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
        RandScaleIntensityd(keys="image", factors=0.1, prob=1.0),
        RandShiftIntensityd(keys="image", offsets=0.1, prob=1.0),
    ]
)
val_transform = Compose(
    [
        LoadImaged(keys=["image", "label"]),        
        EnsureChannelFirstd(keys=["image", "label"]),  # 确保图像和标签的通道在第一维   
        EnsureTyped(keys=["image", "label"]),
         # ConvertToMultiChannelBasedOnBratsClassesd(keys="label"), 
        Orientationd(keys=["image", "label"], axcodes="RAS"),       
        Spacingd(keys=["image", "label"], pixdim=(1.0, 1.0, 1.0), mode=("bilinear", "nearest")),
        ResizeTransform(keys=["image", "label"], spatial_size=crop_size), # 使用自定义 ResizeTransform
        NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
    ]
)


#创建dataset，划分数据集
# 定义数据集的根目录和图像、标签路径
# root_dir = "你的数据集根目录"  # 设置你的自定义数据集根目录
# image_dir = os.path.join(root_dir, "images")  # 图像存放目录
# label_dir = os.path.join(root_dir, "labels")  # 标签存放目录

# 定义图像和标签的目录
# root_dir = r'H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC'
# image_dir = r'H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp'
# label_dir = r'H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\mask\hbp'

root_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp'
image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTr'
label_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTr'

# 获取图像和标签文件列表
image_files = [os.path.join(image_dir, f) for f in os.listdir(image_dir) if f.endswith('.nii.gz')]
label_files = [os.path.join(label_dir, f) for f in os.listdir(label_dir) if f.endswith('.nii.gz')]

# 确保图像和标签数量一致
assert len(image_files) == len(label_files), "图像和标签数量不一致"

# 将图像和标签结合并打乱顺序，以增加随机性
combined_data = list(zip(image_files, label_files))
np.random.shuffle(combined_data)

# 将数据按70%训练集和30%验证集进行划分
train_size = int(0.7 * len(combined_data))
train_data = [{"image": img, "label": lbl} for img, lbl in combined_data[:train_size]]  # 训练集
val_data = [{"image": img, "label": lbl} for img, lbl in combined_data[train_size:]]  # 验证集
print(len(train_data))  # 打印训练数据的数量


# 创建训练集和验证集的数据集
train_ds = Dataset(data=train_data, transform=train_transform)
val_ds = Dataset(data=val_data, transform=val_transform)


# 创建数据加载器
train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=2)  # 训练集加载器
val_loader = DataLoader(val_ds, batch_size=2, shuffle=False, num_workers=2)  # 验证集加载器


# 查看训练集和验证集的形状
for batch_data in train_loader:
    print("训练集批次 - 图像形状:", batch_data['image'].shape, "标签形状:", batch_data['label'].shape)
    break  # 只查看第一个批次

#%% 模型训练
max_epochs = 30
val_interval = 1
VAL_AMP = True

device = torch.device("cuda:0")

#创建SegResNet模型
model = SegResNet(
    blocks_down=[1, 2, 2, 4],
    blocks_up=[1, 1, 1],
    init_filters=16,
    in_channels=1,
    out_channels=2, #label就2个值，所以是2
    dropout_prob=0.2,
).to(device)

#定义DiceLoss损失函数
# loss_function = DiceLoss(smooth_nr=0, smooth_dr=1e-5, squared_pred=True, to_onehot_y=False, sigmoid=True)
loss_function = DiceLoss(smooth_nr=0, smooth_dr=1e-5, squared_pred=True, to_onehot_y=True, sigmoid=True)

#定义优化器
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001, weight_decay=1e-5)
#定义scheduler
lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=max_epochs)


dice_metric = DiceMetric(include_background=True, reduction="mean")
dice_metric_batch = DiceMetric(include_background=True, reduction="mean_batch")

post_trans = Compose([Activations(sigmoid=True), AsDiscrete(threshold=0.5)])


# 定义推理计算方法
def inference(input):
    def _compute(input):
        return sliding_window_inference(
            inputs=input,
            roi_size=crop_size,
            sw_batch_size=2,
            predictor=model,
            overlap=0.5,
        )
    if VAL_AMP:
            with torch.cuda.amp.autocast():
                return _compute(input)
    else:
        return _compute(input)


# 使用amp来进行加速训练
scaler = torch.cuda.amp.GradScaler()
# enable cuDNN benchmark---加速训练用的
torch.backends.cudnn.benchmark = True


#训练部分的代码
best_metric = -1
best_metric_epoch = -1
best_metrics_epochs_and_time = [[], [], []]
epoch_loss_values = []
metric_values = []
metric_values_tumor = []


def calculate_iou(preds, labels):
    # 将 MetaTensor 转换为标准 Tensor
    preds = [pred.as_tensor() for pred in preds]  # 转换为标准 Tensor
    labels = [label.as_tensor() for label in labels]  # 转换为标准 Tensor

    preds = torch.cat(preds).cpu().numpy()  # 合并预测
    labels = torch.cat(labels).cpu().numpy()  # 合并标签

    # 假设我们只关心类别 1
    preds_binary = (preds == 1).astype(np.float32)  # 将 preds 转换为二进制
    labels_binary = (labels == 1).astype(np.float32)  # 将 labels 转换为二进制

    # 确保 preds 和 labels 的形状一致
    if preds_binary.shape[0] != labels_binary.shape[0]:
        preds_binary = preds_binary[0]  # 选择第一个样本进行比较
        labels_binary = labels_binary  # 保持 labels 不变

    intersection = np.sum((preds_binary == 1) & (labels_binary == 1))
    union = np.sum((preds_binary == 1) | (labels_binary == 1))
    
    if union == 0:
        return 0.0
    return intersection / union


#%%
max_epochs = 30

total_start = time.time()
train_acc_values = []  # 用于保存训练准确率
train_iou_values = []  # 用于保存训练 IoU
val_acc_values = []    # 用于保存验证准确率
val_iou_values = []    # 用于保存验证 IoU

for epoch in range(max_epochs):
    epoch_start = time.time()
    print("-" * 10)
    print(f"epoch {epoch + 1}/{max_epochs}")
    model.train()
    epoch_loss = 0
    step = 0
    for batch_data in train_loader:
        step_start = time.time()
        step += 1
        inputs, labels = (
            batch_data["image"].to(device),
            batch_data["label"].to(device),
        )
        optimizer.zero_grad()
        with torch.cuda.amp.autocast():
            outputs = model(inputs)
            loss = loss_function(outputs, labels)
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        epoch_loss += loss.item()
        
        # 计算训练准确率和 IoU
        preds = torch.argmax(outputs, dim=1)  # 获取预测类别
        acc = (preds == labels).float().mean()  # 计算准确率
        iou = calculate_iou(preds, labels)  # 计算 IoU（需要实现 calculate_iou 函数）
        
        train_acc_values.append(acc.item())
        train_iou_values.append(iou.item())
        
        print(
            f"{step}/{len(train_ds) // train_loader.batch_size}"
            f", train_loss: {loss.item():.4f}"
            f", train_acc: {acc:.4f}"
            f", train_iou: {iou:.4f}"
            f", step time: {(time.time() - step_start):.4f}"
        )
    
    lr_scheduler.step()
    epoch_loss /= step
    epoch_loss_values.append(epoch_loss)
    print(f"epoch {epoch + 1} average loss: {epoch_loss:.4f}")

    if (epoch + 1) % val_interval == 0:
        model.eval()
        with torch.no_grad():
            for val_data in val_loader:
                val_inputs, val_labels = (
                    val_data["image"].to(device),
                    val_data["label"].to(device),
                )
                val_outputs = inference(val_inputs)
                val_outputs = [post_trans(i) for i in decollate_batch(val_outputs)]
                
                # 计算验证准确率和 IoU
                val_preds = [torch.argmax(output, dim=1) for output in val_outputs]
                val_acc = (torch.cat(val_preds) == torch.cat(val_labels)).float().mean()  # 计算准确率
                val_iou = calculate_iou(val_preds, val_labels)  # 计算 IoU
                
                val_acc_values.append(val_acc.item())
                val_iou_values.append(val_iou.item())
                
                dice_metric(y_pred=val_outputs, y=val_labels)
                dice_metric_batch(y_pred=val_outputs, y=val_labels)

            metric = dice_metric.aggregate().item() # 计算整体的 Dice 系数
            metric_values.append(metric)
            metric_batch = dice_metric_batch.aggregate()
            metric_tumor = metric_batch[1].item() # 取目标类别1的 Dice 系数
            metric_values_tumor.append(metric_tumor)           
            dice_metric.reset()
            dice_metric_batch.reset() 

            if metric > best_metric:
                best_metric = metric
                best_metric_epoch = epoch + 1                
                torch.save(
                    model.state_dict(),
                    os.path.join(root_dir, "best_metric_model.pth"),
                )
                print("saved new best metric model")
            print(
                f"current epoch: {epoch + 1} current mean dice: {metric:.4f}"
                # f" tc: {metric_tc:.4f} "
                f"\nbest mean dice: {best_metric:.4f}"
                f" at epoch: {best_metric_epoch}"
                f"\nValidation Accuracy: {val_acc:.4f}, Validation IoU: {val_iou:.4f}"
            )

# In[11]:
#对训练过程的loss和average 以及一些其他指标进行可视化
plt.figure("train", (12, 6))
plt.subplot(1, 2, 1)
plt.title("Epoch Average Loss")
x = [i + 1 for i in range(len(epoch_loss_values))]
y = epoch_loss_values
plt.xlabel("epoch")
plt.plot(x, y, color="red")
plt.subplot(1, 2, 2)
plt.title("Val Mean Dice")
x = [val_interval * (i + 1) for i in range(len(metric_values))]
y = metric_values
plt.xlabel("epoch")
plt.plot(x, y, color="green")
plt.show()

plt.figure("train", (18, 6))
plt.subplot(1, 3, 1)
plt.title("Val Mean Dice TC")
x = [val_interval * (i + 1) for i in range(len(metric_values_tumor))]
y = metric_values_tumor
plt.xlabel("epoch")
plt.plot(x, y, color="blue")
plt.subplot(1, 3, 2)
plt.title("Val Mean Dice WT")

plt.xlabel("epoch")
plt.plot(x, y, color="purple")
plt.show()

# In[12]:# # 模型的推理预测
model.load_state_dict(torch.load(os.path.join(root_dir, "best_metric_model.pth")))
model.eval()
with torch.no_grad():
    # select one image to evaluate and visualize the model output
    val_input = val_ds[6]["image"].unsqueeze(0).to(device)
    roi_size = (128, 128, 64)
    sw_batch_size = 4
    val_output = inference(val_input)
    val_output = post_trans(val_output[0])
    plt.figure("image", (24, 6))
    for i in range(4):
        plt.subplot(1, 4, i + 1)
        plt.title(f"image channel {i}")
        plt.imshow(val_ds[6]["image"][i, :, :, 70].detach().cpu(), cmap="gray")
    plt.show()
    # visualize the 3 channels label corresponding to this image
    plt.figure("label", (18, 6))
    for i in range(3):
        plt.subplot(1, 3, i + 1)
        plt.title(f"label channel {i}")
        plt.imshow(val_ds[6]["label"][i, :, :, 70].detach().cpu())
    plt.show()
    # visualize the 3 channels model output corresponding to this image
    plt.figure("output", (18, 6))
    for i in range(3):
        plt.subplot(1, 3, i + 1)
        plt.title(f"output channel {i}")
        plt.imshow(val_output[i, :, :, 70].detach().cpu())
    plt.show()


# In[13]:
val_org_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image"]),
        ConvertToMultiChannelBasedOnBratsClassesd(keys="label"),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.0, 1.0, 1.0), mode="bilinear"),
        NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
    ]
)

val_org_ds = DecathlonDataset(
    root_dir=root_dir,
    task="Task01_BrainTumour",
    transform=val_org_transforms,
    section="validation",
    download=False,
    num_workers=4,
    cache_num=0,
)
val_org_loader = DataLoader(val_org_ds, batch_size=1, shuffle=False, num_workers=4)

post_transforms = Compose(
    [
        Invertd(
            keys="pred",
            transform=val_org_transforms,
            orig_keys="image",
            meta_keys="pred_meta_dict",
            orig_meta_keys="image_meta_dict",
            meta_key_postfix="meta_dict",
            nearest_interp=False,
            to_tensor=True,
            device="cpu",
        ),
        Activationsd(keys="pred", sigmoid=True),
        AsDiscreted(keys="pred", threshold=0.5),
    ]
)


# In[14]:

model.load_state_dict(torch.load(os.path.join(root_dir, "best_metric_model.pth")))
model.eval()

with torch.no_grad():
    for val_data in val_org_loader:
        val_inputs = val_data["image"].to(device)
        val_data["pred"] = inference(val_inputs)
        val_data = [post_transforms(i) for i in decollate_batch(val_data)]
        val_outputs, val_labels = from_engine(["pred", "label"])(val_data)
        dice_metric(y_pred=val_outputs, y=val_labels)
        dice_metric_batch(y_pred=val_outputs, y=val_labels)

    metric_org = dice_metric.aggregate().item()
    metric_batch_org = dice_metric_batch.aggregate()

    dice_metric.reset()
    dice_metric_batch.reset()

metric_tc, metric_wt, metric_et = metric_batch_org[0].item(), metric_batch_org[1].item(), metric_batch_org[2].item()

print("Metric on original image spacing: ", metric_org)
print(f"metric_tc: {metric_tc:.4f}")
print(f"metric_wt: {metric_wt:.4f}")
print(f"metric_et: {metric_et:.4f}")

#%% 基于Monai的3d分割代码最终版,已经成功了
# 关键是要把image和label统一大小
# 脾脏分割代码修改而来

# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[gdown, nibabel, tqdm, ignite]"')
# get_ipython().system('python -c "import matplotlib" || pip install -q matplotlib')
# get_ipython().run_line_magic('matplotlib', 'inline')

from monai.utils import first, set_determinism
from monai.transforms import (
    AsDiscrete,
    AsDiscreted,
    EnsureChannelFirstd,
    Compose,
    CropForegroundd,
    LoadImaged,
    Orientationd,
    RandCropByPosNegLabeld,
    SaveImaged,
    ScaleIntensityRanged,
    Spacingd,
    Invertd,
)
from monai.handlers.utils import from_engine
from monai.networks.nets import UNet
from monai.networks.layers import Norm
from monai.metrics import DiceMetric
from monai.losses import DiceLoss
from monai.inferers import sliding_window_inference
from monai.data import CacheDataset, DataLoader, Dataset, decollate_batch
from monai.config import print_config
from monai.apps import download_and_extract
import torch
import matplotlib.pyplot as plt
import tempfile
import shutil
import os
import glob
print_config()

# In[5]:
root_dir = '/root/autodl-tmp/108hcc'
image_dir = '/root/autodl-tmp/108hcc/image'
label_dir = '/root/autodl-tmp/108hcc/mask'

# 获取图像和标签文件列表
# image_files = [os.path.join(image_dir, f) for f in os.listdir(image_dir) if f.endswith('.nii.gz')]
# label_files = [os.path.join(label_dir, f) for f in os.listdir(label_dir) if f.endswith('.nii.gz')]

train_images = sorted(glob.glob(os.path.join(image_dir, "*.nii.gz")))
train_labels = sorted(glob.glob(os.path.join(label_dir, "*.nii.gz")))
train_images[:5]
train_labels[:5]

data_dicts = [{"image": image_name, "label": label_name} for image_name, label_name in zip(train_images, train_labels)]
# train_files, val_files = data_dicts[:-9], data_dicts[-9:]

# 按照 7:3 划分数据集
split_index = int(len(data_dicts) * 0.7)  # 计算 70% 的索引
train_files, val_files = data_dicts[:split_index], data_dicts[split_index:]
print(len(train_files))  
print(len(val_files))  

# In[6]: Set deterministic training for reproducibility

set_determinism(seed=0)

# ## Setup transforms for training and validation
# Here we use several transforms to augment the dataset:
# 1. `LoadImaged` loads the spleen CT images and labels from NIfTI format files.
# 1. `EnsureChannelFirstd` ensures the original data to construct "channel first" shape.
# 1. `Orientationd` unifies the data orientation based on the affine matrix.
# 1. `Spacingd` adjusts the spacing by `pixdim=(1.5, 1.5, 2.)` based on the affine matrix.
# 1. `ScaleIntensityRanged` extracts intensity range [-57, 164] and scales to [0, 1].
# 1. `CropForegroundd` removes all zero borders to focus on the valid body area of the images and labels.
# 1. `RandCropByPosNegLabeld` randomly crop patch samples from big image based on pos / neg ratio.  
# The image centers of negative samples must be in valid body area.
# 1. `RandAffined` efficiently performs `rotate`, `scale`, `shear`, `translate`, etc. together based on PyTorch affine transform.

# In[7]:
from monai.transforms import Resize
from monai.transforms import RandCropByPosNegLabeld


## Check transforms in DataLoader
from monai.transforms import Resize, MapTransform

class ResizeTransform(MapTransform):
    def __init__(self, keys, spatial_size):
        super().__init__(keys)
        self.spatial_size = spatial_size

    def __call__(self, data):
        for key in self.keys:
            data[key] = Resize(spatial_size=self.spatial_size)(data[key])
        return data

# 假设你希望裁剪的大小为 (96, 96, 96)
crop_size = (96, 96, 96)

train_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-57,
            a_max=164,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image", "label"], source_key="image"),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.5, 1.5, 2.0), mode=("bilinear", "nearest")),
        ResizeTransform(keys=["image", "label"], spatial_size=crop_size),  # 使用自定义 ResizeTransform
        RandCropByPosNegLabeld(
            keys=["image", "label"],
            label_key="label",
            spatial_size=crop_size,
            pos=1,
            neg=1,
            num_samples=4,
            image_key="image",
            image_threshold=0,
            allow_smaller=True
        ),
    ]
)

val_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-57,
            a_max=164,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image", "label"], source_key="image"),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.5, 1.5, 2.0), mode=("bilinear", "nearest")),
        ResizeTransform(keys=["image", "label"], spatial_size=crop_size),  # 使用自定义 ResizeTransform
    ]
)

# In[8]:

check_ds = Dataset(data=val_files, transform=val_transforms)
check_loader = DataLoader(check_ds, batch_size=1)
check_data = first(check_loader)
image, label = (check_data["image"][0][0], check_data["label"][0][0])
print(f"image shape: {image.shape}, label shape: {label.shape}")
# plot the slice [:, :, 80]
plt.figure("check", (12, 6))
plt.subplot(1, 2, 1)
plt.title("image")
plt.imshow(image[:, :, 25], cmap="gray")
plt.subplot(1, 2, 2)
plt.title("label")
plt.imshow(label[:, :, 80])
plt.show()

# ## Define CacheDataset and DataLoader for training and validation
# 
# Here we use CacheDataset to accelerate training and validation process, it's 10x faster than the regular Dataset.  
# To achieve best performance, set `cache_rate=1.0` to cache all the data, if memory is not enough, set lower value.  
# Users can also set `cache_num` instead of `cache_rate`, will use the minimum value of the 2 settings.  
# And set `num_workers` to enable multi-threads during caching.  
# If want to to try the regular Dataset, just change to use the commented code below.

# In[9]:

from monai.data import pad_list_data_collate
# train_ds = CacheDataset(data=train_files, transform=train_transforms, cache_rate=1.0, num_workers=4)
train_ds = Dataset(data=train_files, transform=train_transforms)

# use batch_size=2 to load images and use RandCropByPosNegLabeld
# to generate 2 x 4 images for network training
train_loader = DataLoader(train_ds, batch_size=4, shuffle=True, num_workers=4)
# train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=4, collate_fn=pad_list_data_collate)
# val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_rate=1.0, num_workers=4)
val_ds = Dataset(data=val_files, transform=val_transforms)
val_loader = DataLoader(val_ds, batch_size=4, num_workers=4)

# val_loader = DataLoader(train_ds, batch_size=2, shuffle=False, num_workers=4, collate_fn=pad_list_data_collate)

# ## Create Model, Loss, Optimizer

# In[10]:

# standard PyTorch program style: create UNet, DiceLoss and Adam optimizer
device = torch.device("cuda:0")
model = UNet(
    spatial_dims=3,
    in_channels=1,
    out_channels=2,
    channels=(16, 32, 64, 128, 256),
    strides=(2, 2, 2, 2),
    num_res_units=2,
    norm=Norm.BATCH,
).to(device)

loss_function = DiceLoss(to_onehot_y=True, softmax=True)
optimizer = torch.optim.Adam(model.parameters(), 1e-4)
dice_metric = DiceMetric(include_background=False, reduction="mean")


#%%模型训练，方法1
# pip install torchmetrics
import os
import torch
import torchmetrics
from torchmetrics import Accuracy, JaccardIndex

# 确定设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 初始化度量
accuracy_metric = Accuracy(task='binary').to(device)  # 或者 'multiclass'，根据你的任务
iou_metric = JaccardIndex(num_classes=2, task='binary').to(device)  # 根据你的类别数选择multiclass或binary

post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])
post_label = Compose([AsDiscrete(to_onehot=2)])

# iou计算方法2
# def mean_iou(y_true, y_pred, num_classes=2):
#     ious = []
#     for cls in range(num_classes):
#         y_true_cls = (y_true == cls)
#         y_pred_cls = (y_pred == cls)
#         intersection = torch.logical_and(y_true_cls, y_pred_cls).sum().item()
#         union = torch.logical_or(y_true_cls, y_pred_cls).sum().item()
#         # Avoid division by zero
#         if union == 0:
#             iou = 1.0
#         else:
#             iou = intersection / union
#         ious.append(iou)
#     return np.mean(ious)

max_epochs = 250
val_interval = 2
best_metric = -1
best_metric_epoch = -1
epoch_loss_values = []
metric_values = []

# 新增列表用于保存训练和验证的指标
train_acc = []
train_iou = []
train_dice = []
val_acc = []
val_iou = []
val_dice = []

for epoch in range(max_epochs):
    # print("-" * 10)
    # print(f"epoch {epoch + 1}/{max_epochs}")
    model.train()
    epoch_loss = 0
    step = 0
    dice_metric.reset()  # 在每个 epoch 开始前重置 Dice 指标

    for batch_data in train_loader:
        step += 1
        inputs, labels = (
            batch_data["image"].to(device),
            batch_data["label"].to(device),
        )
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = loss_function(outputs, labels)
        loss.backward()
        optimizer.step()
        epoch_loss += loss.item()
        # print(f"{step}/{len(train_ds) // train_loader.batch_size}, " f"train_loss: {loss.item():.4f}")

        # 调整标签形状并确保是二进制格式
        labels = labels.squeeze(1).long()

        # 计算训练的 Accuracy、IoU 和 Dice
        preds = torch.argmax(outputs, dim=1).to(device)
        acc = accuracy_metric(preds, labels)
        iou = iou_metric(preds, labels)
        dice_metric(y_pred=preds, y=labels)  # 更新 Dice 指标
        train_acc.append(acc.item())  # 保存训练准确率
        train_iou.append(iou.item())   # 保存训练 IoU

    # 在每个 epoch 结束后获取训练 Dice
    train_dice_value = dice_metric.aggregate().item()  # 获取标量值
    train_dice.append(train_dice_value)  # 保存训练 Dice
    dice_metric.reset()  # 重置指标以备下次使用

    epoch_loss /= step
    epoch_loss_values.append(epoch_loss)
    print(f"epoch {epoch + 1} average loss: {epoch_loss:.4f}")   
    print(f"Train Accuracy: {train_acc[-1]:.4f}, Train IoU: {train_iou[-1]:.4f}, Train Dice: {train_dice_value:.4f}")
     
    # 在验证阶段
    if (epoch + 1) % val_interval == 0:
        model.eval()
        with torch.no_grad():
            val_dice_metric = DiceMetric()  # 创建新的 DiceMetric 实例
            for val_data in val_loader:
                val_inputs, val_labels = (
                    val_data["image"].to(device),
                    val_data["label"].to(device),
                )
                roi_size = crop_size
                sw_batch_size = 4
                val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
                val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]
                val_labels = [post_label(i) for i in decollate_batch(val_labels)] 
                
                # 计算验证的 Accuracy 和 IoU
                val_preds = [torch.argmax(output, dim=1).to(device) for output in val_outputs]

                # 将 val_preds 和 val_labels 转换为张量
                val_preds_tensor = torch.cat(val_preds, dim=0)
                val_labels_tensor = torch.cat(val_labels, dim=0)

                # 确保形状一致
                if val_preds_tensor.shape != val_labels_tensor.shape:
                    print(f"Shape mismatch: preds {val_preds_tensor.shape}, labels {val_labels_tensor.shape}")
                else:
                    val_acc = accuracy_metric(val_preds_tensor, val_labels_tensor)
                    val_iou = iou_metric(val_preds_tensor, val_labels_tensor)

                    # 更新验证 Dice 指标
                    val_dice_metric(y_pred=val_preds_tensor, y=val_labels_tensor)

                    # 在验证结束后获取验证 Dice
                    val_dice_value = val_dice_metric.aggregate().item()
                    val_dice.append(val_dice_value)

                    # 打印验证指标
                    print(f"Validation Accuracy: {val_acc:.4f}, Validation IoU: {val_iou:.4f}, Validation Dice: {val_dice_value:.4f}")

                    # 保存最佳模型
                    if val_dice_value > best_metric:
                        best_metric = val_dice_value
                        best_metric_epoch = epoch + 1
                        torch.save(model.state_dict(), os.path.join(root_dir, "best_metric_model.pth"))
                        print("saved new best metric model")

                    # 打印当前 epoch 的信息
                    print(
                        f"current epoch: {epoch + 1} current mean dice: {val_dice_value:.4f} "
                        f"\nbest mean dice: {best_metric:.4f} at epoch: {best_metric_epoch}"
                    )
  
   
# max_epochs = 20 
# val_interval = 2
# best_metric = -1
# best_metric_epoch = -1
# epoch_loss_values = []
# metric_values = []
# post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])
# post_label = Compose([AsDiscrete(to_onehot=2)])


# for epoch in range(max_epochs):
#     print("-" * 10)
#     print(f"epoch {epoch + 1}/{max_epochs}")
#     model.train()
#     epoch_loss = 0
#     step = 0
#     for batch_data in train_loader:
#         step += 1
#         inputs, labels = (
#             batch_data["image"].to(device),
#             batch_data["label"].to(device),
#         )
#         optimizer.zero_grad()
#         outputs = model(inputs)
#         loss = loss_function(outputs, labels)
#         loss.backward()
#         optimizer.step()
#         epoch_loss += loss.item()
#         print(f"{step}/{len(train_ds) // train_loader.batch_size}, " f"train_loss: {loss.item():.4f}")

#         # 调整标签形状并确保是二进制格式
#         labels = labels.squeeze(1).long()  # 从 [8, 1, 96, 96, 96] 变为 [8, 96, 96, 96] 并转换为 long 类型

#         # 计算训练的 Accuracy 和 IoU
#         preds = torch.argmax(outputs, dim=1).to(device)  # 将输出转换为类别索引并移动到设备
#         acc = accuracy_metric(preds, labels)
#         iou = iou_metric(preds, labels)
#         print(f"Train Accuracy: {acc:.4f}, Train IoU: {iou:.4f}")

#     epoch_loss /= step
#     epoch_loss_values.append(epoch_loss)
#     print(f"epoch {epoch + 1} average loss: {epoch_loss:.4f}")

#     if (epoch + 1) % val_interval == 0:
#         model.eval()
#         with torch.no_grad():
#             for val_data in val_loader:
#                 val_inputs, val_labels = (
#                     val_data["image"].to(device),
#                     val_data["label"].to(device),
#                 )
#                 roi_size = (96, 96, 96)
#                 sw_batch_size = 4
#                 val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
#                 val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]
#                 val_labels = [post_label(i) for i in decollate_batch(val_labels)]
                
#                 # 调整验证标签形状并确保是二进制格式
#                 val_labels = [label.squeeze(1).long().to(device) for label in val_labels]  # 从 [8, 1, 96, 96, 96] 变为 [8, 96, 96, 96] 并转换为 long 类型并移动到设备

#                 # 计算验证的 Accuracy 和 IoU
#                 val_preds = [torch.argmax(output, dim=1).to(device) for output in val_outputs]  # 将输出转换为类别索引并移动到设备
#                 val_acc = accuracy_metric(val_preds, val_labels)
#                 val_iou = iou_metric(val_preds, val_labels)
#                 print(f"Validation Accuracy: {val_acc:.4f}, Validation IoU: {val_iou:.4f}")

#                 # compute metric for current iteration
#                 dice_metric(y_pred=val_preds, y=val_labels)

#             # aggregate the final mean dice result
#             metric = dice_metric.aggregate().item()
#             # reset the status for next validation round
#             dice_metric.reset()

#             metric_values.append(metric)
#             if metric > best_metric:
#                 best_metric = metric
#                 best_metric_epoch = epoch + 1
#                 torch.save(model.state_dict(), os.path.join(root_dir, "best_metric_model.pth"))
#                 print("saved new best metric model")
#             print(
#                 f"current epoch: {epoch + 1} current mean dice: {metric:.4f}"
#                 f"\nbest mean dice: {best_metric:.4f} "
#                 f"at epoch: {best_metric_epoch}"
            # )  

# 打印保存的指标
# print("Train Accuracy:", train_acc)
# print("Train IoU:", train_iou)
# print("Train Dice:", train_dice)
# print("Validation Accuracy:", val_acc)
# print("Validation IoU:", val_iou)
# print("Validation Dice:", val_dice)

#%%定义训练函数，训练成功了  方法2 
# loss_fn=nn.CrossEntropyLoss()
# optimizer=torch.optim.Adam(model.parameters(),lr=0.00001)

from tqdm import tqdm
import numpy as np

def train(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []
    
    model.train()
    for batch in tqdm(trainloader):  # 修改这里
        x = batch["image"].to('cuda')
        y = batch["label"].to('cuda')
        
        y_pred = model(x)
        loss = loss_function(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_iou.append(batch_iou.item())
            
    epoch_loss = running_loss / len(trainloader.dataset)       
    epoch_acc = correct / (total * x.size(2) * x.size(3)* x.size(4))  # 这里假设 x 是 4D 张量
    
    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    epoch_test_iou = []
    
    model.eval()
    with torch.no_grad():
        for batch in tqdm(testloader):  # 修改这里
            x = batch["image"].to('cuda')
            y = batch["label"].to('cuda')  
            
            y_pred = model(x)
            loss = loss_function(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_test_iou.append(batch_iou.item())            
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / (test_total * x.size(2) * x.size(3)* x.size(4)) 
    
    if np.mean(epoch_test_iou) > 0.8:
        static_dict = model.state_dict()
        torch.save(static_dict, './checkpoint/{}_trainIOU_{}_testIOU_{}.pth'.format(epoch, round(np.mean(epoch_iou), 3), round(np.mean(epoch_test_iou), 3)))
    
    print('epoch: ', epoch, 
          'loss: ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean(epoch_iou), 3),
          'test_loss: ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
          'test_iou:', round(np.mean(epoch_test_iou), 3)
         )
        
    return epoch_loss, epoch_acc, epoch_iou, epoch_test_loss, epoch_test_acc, epoch_test_iou

#训练
epochs = 250

train_loss = []
train_acc = []
train_iou = []
test_loss = []
test_acc = []
test_iou = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou, epoch_test_loss, epoch_test_acc,epoch_test_iou = train(epoch,model,train_loader, val_loader)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)

#%%
img,label=next(iter(train_dl))
print(img.shape,label.shape)

## 保存模型
PATH = os.path.join(root_dir, "best_metric_model.pth") #路径不能有中文
# PATH = r'/root/autodl-tmp/hcc_monai_model.pth' 
torch.save(model.state_dict(), PATH)

#%%保存训练结果到Excel文件
import pandas as pd

# 在训练和验证结束后
# 创建一个字典来存储所有指标
data = {
    "Epoch": list(range(1, max_epochs + 1)),
    "Train Accuracy": train_acc,
    "Train IoU": train_iou,
    "Train Dice": train_dice,
    "Validation Accuracy": val_acc,
    "Validation IoU": val_iou,
    "Validation Dice": val_dice,
}

# 将字典转换为 DataFrame
df = pd.DataFrame(data)

# 保存到 Excel 文件
excel_file_path = "/root/autodl-tmp/training_metrics.xlsx"
df.to_excel(excel_file_path, index=False)

print(f"Metrics saved to {excel_file_path}")


# In[18]:

print(f"train completed, best_metric: {best_metric:.4f} " f"at epoch: {best_metric_epoch}")

# ## Plot the loss and metric

# In[19]:

plt.figure("train", (12, 6))
plt.subplot(1, 2, 1)
plt.title("Epoch Average Loss")
x = [i + 1 for i in range(len(epoch_loss_values))]
y = epoch_loss_values
plt.xlabel("epoch")
plt.plot(x, y)
plt.subplot(1, 2, 2)
plt.title("Val Mean Dice")
x = [val_interval * (i + 1) for i in range(len(metric_values))]
y = metric_values
plt.xlabel("epoch")
plt.plot(x, y)
plt.show()


# ## Check best model output with the input image and label

# In[11]:

# model.load_state_dict(torch.load(os.path.join(root_dir, "best_metric_model.pth")))

model.load_state_dict(torch.load(PATH))
model.eval()
with torch.no_grad():
    for i, val_data in enumerate(val_loader):
        roi_size = (160, 160, 160)
        sw_batch_size = 4
        val_outputs = sliding_window_inference(val_data["image"].to(device), roi_size, sw_batch_size, model)
        # plot the slice [:, :, 80]
        plt.figure("check", (18, 6))
        plt.subplot(1, 3, 1)
        plt.title(f"image {i}")
        plt.imshow(val_data["image"][0, 0, :, :, 80], cmap="gray")
        plt.subplot(1, 3, 2)
        plt.title(f"label {i}")
        plt.imshow(val_data["label"][0, 0, :, :, 80])
        plt.subplot(1, 3, 3)
        plt.title(f"output {i}")
        plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, 80])
        plt.show()
        if i == 2:
            break


# ## Evaluation on original image spacings

# In[12]:
val_org_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.5, 1.5, 2.0), mode="bilinear"),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-57,
            a_max=164,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image"], source_key="image"),
    ]
)

val_org_ds = Dataset(data=val_files, transform=val_org_transforms)
val_org_loader = DataLoader(val_org_ds, batch_size=1, num_workers=4)

post_transforms = Compose(
    [
        Invertd(
            keys="pred",
            transform=val_org_transforms,
            orig_keys="image",
            meta_keys="pred_meta_dict",
            orig_meta_keys="image_meta_dict",
            meta_key_postfix="meta_dict",
            nearest_interp=False,
            to_tensor=True,
            device="cpu",
        ),
        AsDiscreted(keys="pred", argmax=True, to_onehot=2),
        AsDiscreted(keys="label", to_onehot=2),
    ]
)

# In[16]:
model.load_state_dict(torch.load(PATH))
model.eval()

with torch.no_grad():
    for val_data in val_org_loader:
        val_inputs = val_data["image"].to(device)
        roi_size = (80, 80, 80)
        sw_batch_size = 4
        val_data["pred"] = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
        val_data = [post_transforms(i) for i in decollate_batch(val_data)]
        val_outputs, val_labels = from_engine(["pred", "label"])(val_data)
        # compute metric for current iteration
        dice_metric(y_pred=val_outputs, y=val_labels)

    # aggregate the final mean dice result
    metric_org = dice_metric.aggregate().item()
    # reset the status for next validation round
    dice_metric.reset()

print("Metric on original image spacing: ", metric_org)


# ## Inference on Test Set

#%%模型批量预测
# data_dir= '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp'
# test_images = sorted(glob.glob(os.path.join(data_dir, "imagesTs", "*.nii.gz")))

test_images = r'H:\hbp2'

test_data = [{"image": image} for image in test_images]


test_org_transforms = Compose(
    [
        LoadImaged(keys="image"),
        EnsureChannelFirstd(keys="image"),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.5, 1.5, 2.0), mode="bilinear"),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-57,
            a_max=164,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image"], source_key="image"),
        # ResizeTransform(keys=["image"], spatial_size=crop_size),
    ]
)


test_org_ds = Dataset(data=test_data, transform=test_org_transforms)

test_org_loader = DataLoader(test_org_ds, batch_size=2, num_workers=0,pin_memory=True, drop_last=True)
# output_dir = "/root/autodl-tmp/108hcc/predmask"  #output_dir="./predmask"

post_transforms = Compose(
    [
        Invertd(
            keys="pred",
            transform=test_org_transforms,
            orig_keys="image",
            meta_keys="pred_meta_dict",
            orig_meta_keys="image_meta_dict",
            meta_key_postfix="meta_dict",
            nearest_interp=False,
            to_tensor=True,
        ),
        AsDiscreted(keys="pred", argmax=True, to_onehot=2),
        SaveImaged(keys="pred", meta_keys="pred_meta_dict", output_dir= "./predmask", output_postfix="seg", resample=False),
    ]
)


# In[ ]:
## uncomment the following lines to visualize the predicted results
from monai.transforms import LoadImage
loader = LoadImage()

#%%模型批量预测
device = torch.device("cuda:0")
model = UNet(
    spatial_dims=3,
    in_channels=1,
    out_channels=2,#表示分类的类别数（如前景和背景）
    channels=(16, 32, 64, 128, 256),
    strides=(2, 2, 2, 2),
    num_res_units=2,
    norm=Norm.BATCH,
).to(device)

PATH = r'H:\weight\hcc_monai_model.pth' 
model.load_state_dict(torch.load(PATH))
model.eval()

with torch.no_grad():
    for test_data in test_org_loader:
        test_inputs = test_data["image"].to(device)
        roi_size = (80, 80, 80)
        sw_batch_size = 4
        test_data["pred"] = sliding_window_inference(test_inputs, roi_size, sw_batch_size, model)

        test_data = [post_transforms(i) for i in decollate_batch(test_data)]

#       # uncomment the following lines to visualize the predicted results
        # test_output = from_engine(["pred"])(test_data)

        # original_image = loader(test_output[0].meta["filename_or_obj"])

        # plt.figure("check", (18, 6))
        # plt.subplot(1, 2, 1)
        # plt.imshow(original_image[:, :, 20], cmap="gray")
        # plt.subplot(1, 2, 2)
        # plt.imshow(test_output[0].detach().cpu()[1, :, :, 20])
        # plt.show()




# In[ ]:pip3 install mambatransformer zetascale swarms
import torch
from mamba_transformer import MambaTransformer

# Generate a random tensor of shape (1, 10) with values between 0 and 99
x = torch.randint(0, 100, (1, 10))
print(x)

# Create an instance of the MambaTransformer model
model = MambaTransformer(
    num_tokens=100,  # Number of tokens in the input sequence
    dim=512,  # Dimension of the model
    heads=8,  # Number of attention heads
    depth=4,  # Number of transformer layers
    dim_head=64,  # Dimension of each attention head
    d_state=512,  # Dimension of the state
    dropout=0.1,  # Dropout rate
    ff_mult=4,  # Multiplier for the feed-forward layer dimension
    return_embeddings=False,  # Whether to return the embeddings,
    transformer_depth=2,  # Number of transformer blocks
    mamba_depth=10,  # Number of Mamba blocks,
    use_linear_attn=True,  # Whether to use linear attention
)
model
# Pass the input tensor through the model and print the output shape
out = model(x)

print(out.shape)



# %% segmentation-models-pytorch-3d 项目
# https://github.com/ZFTurbo/segmentation_models_pytorch_3d?tab=readme-ov-file
#  pip install segmentation-models-pytorch-3d
import segmentation_models_pytorch_3d as smp
import torch

model = smp.Unet(
    encoder_name="resnet50",        
    in_channels=1,                  
    strides=((2, 2, 2), (4, 2, 1), (2, 2, 2), (2, 2, 1), (1, 2, 3)),
    classes=3, 
)
print(model)
res = model(torch.randn(4, 1, 224, 128, 12)) 
res.shape

# %%  https://github.com/ZFTurbo/timm_3d
# pip install timm-3d
import timm_3d
import torch

m = timm_3d.create_model(
    'tf_efficientnet_b0.in1k',
    pretrained=True,
    num_classes=0,
    global_pool=''
)
print(m)
# Shape of input (B, C, H, W, D). B - batch size, C - channels, H - height, W - width, D - depth
res = m(torch.randn(2, 3, 128, 128, 128))
print(f'Output shape: {res.shape}') 

#%% Monai中调用3d swin-unetr最新分割方法
# Code: https://monai.io/research/swin-unetr. 
# 论文Self-Supervised Pre-Training of Swin Transformers for 3D Medical Image Analysis

import torch
from monai.networks.nets import SwinUNETR

# 定义模型参数
in_channels = 1  # 输入通道数
out_channels = 2  # 输出通道数
img_size = (96,96,96)  # 输入图像大小

# 创建3d Swin-UNETR 模型
model = SwinUNETR(
    in_channels=in_channels,
    out_channels=out_channels,
    img_size=img_size,
)

# 打印模型结构
print(model)

# %% pip install nnunet
import os
import nnunet
from nnunet.paths import default_plans_identifier, network_training_output_dir
from nnunet.experiment_planning.plan_and_preprocess import main as plan_and_preprocess
from nnunet.run.default_configuration import get_default_configuration
from nnunet.training.model_restore import load_model_and_checkpoint_files
from nnunet.inference.predict import predict_from_folder

# 设置nnU-Net环境变量
os.environ['nnUNet_raw_data_base'] = '/path/to/nnUNet_raw_data_base'
os.environ['nnUNet_preprocessed'] = '/path/to/nnUNet_preprocessed'
os.environ['RESULTS_FOLDER'] = '/path/to/nnUNet_results'

# 数据准备
plan_and_preprocess('/path/to/dataset.json')

# 训练模型
trainer_class, params = get_default_configuration('3d_fullres', 'Task001_BrainTumour', 'nnUNetTrainerV2')
trainer = trainer_class(output_folder=network_training_output_dir, dataset_directory=os.environ['nnUNet_raw_data_base'], fold=0, **params)
trainer.initialize(True)
trainer.run_training()

# 推理
model_folder = os.path.join(network_training_output_dir, '3d_fullres', 'Task001_BrainTumour', 'nnUNetTrainerV2__nnUNetPlansv2.1')
input_folder = '/path/to/input/folder'
output_folder = '/path/to/output/folder'
predict_from_folder(model_folder, input_folder, output_folder, save_npz=False, num_threads_preprocessing=2, num_threads_nifti_save=2, lowres_segmentations=None, part_id=0, num_parts=1, tta=True, overwrite_existing=True, mode='normal', overwrite_all_in_gpu=None, mixed_precision=True, step_size=0.5, checkpoint_name='model_final_checkpoint')