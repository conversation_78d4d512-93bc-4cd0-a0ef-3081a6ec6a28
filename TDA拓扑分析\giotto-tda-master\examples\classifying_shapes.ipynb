{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Classifying 3D shapes\n", "\n", "Techniques for analyzing 3D shapes are becoming increasingly important due to the vast number of sensors such as LiDAR that are capturing 3D data, as well as numerous computer graphics applications.  These raw data are typically collected in the form of a _point cloud_, which corresponds to a set of 3D points $\\{p_i | i = 1, \\ldots, n\\}$, where each point $p_i$ is a vector of its $(x, y, z)$ coordinates plus extra\n", "feature channels such as color, intensity etc. Typically, Euclidean distance is used to calculate the distances between any two points.\n", "\n", "By finding suitable representations of these point clouds, machine learning can be used to solve a variety of tasks such as those shown in the figure below.\n", "\n", "![3d-tasks](images/3d_tasks.png)\n", "<div style=\"text-align: left\">\n", "   <p style=\"text-align: left;\"> <b>Figure reference:</b> adapted from <a href=\"https://arxiv.org/abs/1612.00593\">arxiv.org/abs/1612.00593</a>. </p>\n", "</div>\n", "\n", "This notebook examines how ``giotto-tda`` can be used to extract topological features from point cloud data and fed to a simple classifier to distinguish 3D shapes.\n", "\n", "If you are looking at a static version of this notebook and would like to run its contents, head over to [GitHub](https://github.com/giotto-ai/giotto-tda/blob/master/examples/classifying_shapes.ipynb) and download the source.\n", "\n", "**License: AGPLv3**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate simple shapes\n", "\n", "To get started, let's generate a synthetic dataset of 10 noisy circles, spheres, and tori, where the effect of noise is to displace the points that sample the surfaces by a random amount in a random direction:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["((30, 400, 3), (30,))"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from data.generate_datasets import make_point_clouds\n", "\n", "point_clouds_basic, labels_basic = make_point_clouds(n_samples_per_shape=10, n_points=20, noise=0.5)\n", "point_clouds_basic.shape, labels_basic.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here the labels are defined to that a circle is 0, a sphere is 1, and a torus is 2. Each point cloud corresponds to a sampling of the continuous surface – in this case 400 points are sampled per shape. As a sanity check, let's visualise these points clouds using ``giotto-tda``'s plotting API:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399], "colorscale": [[0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1, "#fde725"]], "opacity": 0.8, "size": 4}, "mode": "markers", "type": "scatter3d", "x": {"bdata": "PA0rmvvIzz84KiFUmLnoPwaaZv9lze0/rImFfs9up7/I0f91E2Duv8iC+e7uk/G/God+/oHp279yriODMIHnPy5BAu/YXew/ACMI0aFKzT81DUd4+grev32fyXalye6/RkuRJSVy4b8MwF3B7hTUP8KGSbJ/Se4/7elRe7lV3z9Tq6v6hjzXv4gp7HBQVeu/CR6UmaaG7r8PXDBRi/nFP7L5LkrUS+c/sw1qTWkx5z/F99pBi13IP2YtprNL+uO/5uXVUew35r8gFAH1RonWvz6AtJMn7+I/s2LguEsC6z+QOHKmujyuP4csIpa9SOO/3s+xcyrB579nLqxrARrWv4YVHAfo59s/0NFSHjRH6D9KsHHuMgnnP3sxzSaml9W//GxylFUW8r+ErBYpFljsv5A1MEBnJ9k/bYYqncze6D/KMHY4t0TjPzQlOpVdScG/9uAH0Ze06r++RYWcRwPrv/gxwU89Bs4/EkE2AC4a6j9URYB6g1nqPwh6bZoV25a/YARKyP9t5r8YAr/tfxruv7+gI0dqH96/ir7RIW2C6j+0fXLKqcjuP5gprtxIF+I/9Lsw160x4r/Ge+o9ejnxv5fqiP+mQtO/TJP6SK1X2T/y/TdAqRvoP0nN2aczU9w/GsrGl0uPzr/sS+3Gpe7svx5ETsfFnOO/4hHJlNqz2T/QI7dUQYPoP2o3jnnBK+0/QPyVD59Aur8gSPaDrVbmv8iBghn0YOW/QHs6DXZFqb/Ur0RJ6ePoP9gxu9Epoe4/UgcHSQfSwj/oAA29eK/hv05VUJnzB++/aR9l+rYR17+MjEdJQknnP1R6A9HWvO0/wH0cKMFW4z+ACMrrjvThvy8uFKRs4vG/4EFg4n5q5L+jt2JtcuLhP3mCOVzrWvM/Q6cNEJTx7D9KjOgiwivXv2xm6Tjtmu2/3mBcr70U7b9OP/ufZ+jKv/A1Wr1bUeY/c4U5N5Ne5z+ygl8A1YOoPz40hvuCZOi/jdl1nr3d8b/Eyb0mVAHfvxi2/KmOUt4/Dn4+soOH8z+0G07QYx7gP9xqCBJ6dOe/IBNpkVlZ8b8Gdb8DinTYvxyLi0mpS+A//8OeypWT6T+CZK1jhAfpP0Tw5LJ7pcy//u+OV2wg578wNP9QvF/lv/7TLuaM+NE/nMbesaXJ5z8gBKpR3XLnP5ixv1AeKH8/9G/TnrEn8L+wCbRvg8Htv+I1Cd0c1cm/UMCwcTmh5T84TbckyYbvPxepOR1DVNk/fMFperym478Wu3ksrRHvv2mo2mrE/tC/Rhog7HiC4T9qJOzB0rbwP1QKiVGPb9g/x1A62T/H2b++R/GcpTDxv4qzPmsyxOG/hmSaLLCC3j/wlNqiwLbtP7tPwOHvnus/UA/6w/f0pr/pKI1ChHTrv5XHFlqpFeW/o9Is06fW0j9IxDOaZqrxP48HoUUpIug/PXw6XBUms7/uXqoNn7vkv2WsB7kxlvK/Z2xIC2Rhyr+02zLlm93sPx8pujGlcec/NBJDFS4c4T9bzE/cEI3gv1Q+iEs+Y+m/UG/wb4NX4L8r2AwA5LzVP2Cqdg73Ke0/bC2hx38W6T/bQrnHzqDevxCI1oM3Xu+/4wJ0Xcwl479kV1Bf4qCnv7IZKLmMk+8/Tr+G0hvp7j8rTOjcfY/SvzXpNpjSqO+/8tvWQycK6r/o0/eIfJ+RvxD2wGSXZ+Y/hByonHrz6z/kJeh5yVixPwNujB5J3Oy/Y0B1O7sR879ppKTb0vDgvzwhD7OriNs/PERAQqTn7j9BqSOje9fiPxpNz4G2n+K/1I41FWUv8L9gJVqIzDTmvxvCbLwDpNM/QquVCNmj5z9kzFg5I4fhP+kzS53Y1cG/jA9z2dxF8r/WVjmyIYDwvziD8YKdW9M/bPTkLOZS6T+6fmT4Z0DoP313tAI3IcS/3ReQJPDM8L86se3DAy7ovwNa+coHXde/lPOFKHnm6T864qkeO7DtPy6JKuUhS8I/Vnlijhwz5b/q4AEThqTovw1Xmg3Cn9S/n+uIvJV24D/Iig6PpTrrP/DEEp4qG+o/uNr8GlE6479lSCZYN+jsv7rniFAmSOy/KPckAPdo2T884COJKVnrPxtdyc7Aguw/4eaYWh9Hyb9IAWHS2o/svw7q1LSvlu2/gHW37POUfz8psXPLZGTpPyKwCqj9ie0/cNEKayWkmr90JU5e7Ljkv2qQFHLvA+y/aEV5LN/g0L93wemQe93XP2tFNIO8UPA/pEnXsmeG4z9iLcqHV0DRv1FSVxgH9/O/8D5HprIR6b9kD9jCWxrjP0rnbl8Fa+0/8hkXngLz6D9IEGTVGGHFv6btXLcbPOe/NqR2JYnJ478qzBg7r4Gjv+hFFvqv8e4/1C6ys+A26D8oeSC5YDV5P5NjPdhY4+m/VioWT3tr5782cLrXBXvIvwx1qoXP/d4/tAkZP8dt6D8D2921pgnaP1FFpd2wpue/S4op4nO/8b9stKcImYvhv258UyqiZ90/JNQInmD06j80AbH/MrrmPwW5D80RGuC/jKVXq9hP8b+YIa6eOSzkv14YIdbPrtE/Uy7tHhmU5j+iyQlmD/TsP3lNO46DHLs/tHQOivN17r9YS82C1Svxvy7s3jsKkZU/EnZ5G7wH5j/PYuCW8mfmP5ZSTwU/cdE/rH7DJItX5L96uny1xgHpv4Z5lpA3edm/MlQYWiR44z+psOs8fvToPy3RLdE+HuY/t2HUW7rz5b/twxRs1z7zv0W5U6iEwOS/baaZbBsE0j9MIfWJBAPpPxIQJOgdTOI/YHjS6ELMo79ORRts1k7wv06JvLX1o+a/QIbM1SLDvL8tFa0fNjfxPwIadyjg8+0/tgP+1FWKvz/QVfhskhHxvzcvf4YNFvK/GC+cYZWIv79bHZ2hIgHiP4+z3TrRG/E/6ox9MPLk3D+9q+8lVsbov+bV9vnS6PC/ds/xaEUL2L/Oen8mjYLSP0bXhdg6YvE/pu0aFlVO6T/eJvgS90LDvxjgcRwQx++/wJ540lwT6L/c6tB+IWLPP2wfcoJGM+w/RA1279k16T9SzcLxmgjHv3mGHQFG7PG/DF4i9dqi6L+UEYANZQSmP5yl2DzMFe8/ZNvbKEb57j+rYD0Azs/EP6T1qxCm/+u/KwCy9qXt8b/KiUiqCjrOvwCCBKS5bus//o3Yc6s+7D+ir8IDFXfkP9Swz2HaTei/mzs12rPq6L8vCerRS+Lkvy8dyWdbz9E/5BvbYbUJ7D+g+6VYfJ/oP9QD4iz3fL2/NEJmNlDs679eSNszP0jtvzzAqnuy2ru/rNt/S4zp6D+kCGu8/vHnPzONNumjVbi/EG1KKj/y5L8HaJVfZY/nv1wO9et2pqk/+hYH3VhV5D8FZZF5LzrrP6T6vcwHurk/moYDGbJu3L+c3WpWGibqv22eQCLvPNm/Tr5Tj6JF5T9439PnIMPzPz4tCU9cUuc/3iLVD6BT5L8a1bCG97HzvynsFrRvduu/cOvmWugdpj/sn6025qXyP+QcFv9bEeg/kKhtr3sN0L/g/47LKGDwvwqDxCrkSeO/8vI9GXNFoD9FbpnJh6jlP+QtF0qexvA/8EnqYw9a0j9W8MHer1npvwjSEvzaLee/gG4yehTGs7+eSwEmICTsP0BgNoM1QPI/mo2Sey/R5D+682D2sPvov4JjSIVU6vO/RCuDUHZr5b9IEMq71XHgP0P6kK4v0PM/qE/BgWgK7D9ocvgJNKC+v6mY5WYqMu2/BDcQmZNe4L906wdbMLbQP9YMERTEmOY/fZmwcQxR5z+7nwj+5SPKv6uQ3WyFzuW/AuYvgvtt6L/aOFnbC1q2PybNqdgAsec/nH/HhE6j7z/iRNwTM5zbPwxiBiUjpeC/7M9U6EZ967/0I52qvsvgvyFTiCdu2tI/cF/P80uC8T9v6nKAbkjhP/a2utQzjtK/pXVT+mkw77/ipFlsG37rv5Htahu7V9k/FYaKm6fJ6j9Q+Prbi0zoPyBx5yAWXNm/+hcxX7wK6L8s7XJEymTrv+5y6yuRb6O/ILJobR3g5T/DEpUIysDwP3gBQTx3QZG/0F1cn5hT6r/4EhCR+nDwv+gmR6rYjci/FtFUtvUU3T8C79XPTT7xP5G1OT3CzeA/Kh5+1uhq3b9drvYBzBbyv2C5eXzGDeO/AKqa7Q3j0T8xGJurbBnqP8KTdipAh+I/tGaakQQt4L+IhbELqmzyv5OsNIY+m+G/nWWpauhqwz+UzP66x1ryPwRrLUisZu4/0hnGz5SnrD8=", "dtype": "f8"}, "y": {"bdata": "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", "dtype": "f8"}, "z": {"bdata": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=", "dtype": "f8"}}], "layout": {"scene": {"xaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "0th"}, "type": "linear"}, "yaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "1st"}, "type": "linear"}, "zaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "2nd"}, "type": "linear"}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["from gtda.plotting import plot_point_cloud\n", "\n", "plot_point_cloud(point_clouds_basic[0])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399], "colorscale": [[0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1, "#fde725"]], "opacity": 0.8, "size": 4}, "mode": "markers", "type": "scatter3d", "x": {"bdata": "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", "dtype": "f8"}, "y": {"bdata": "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", "dtype": "f8"}, "z": {"bdata": "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", "dtype": "f8"}}], "layout": {"scene": {"xaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "0th"}, "type": "linear"}, "yaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "1st"}, "type": "linear"}, "zaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "2nd"}, "type": "linear"}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_point_cloud(point_clouds_basic[10])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399], "colorscale": [[0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1, "#fde725"]], "opacity": 0.8, "size": 4}, "mode": "markers", "type": "scatter3d", "x": {"bdata": "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", "dtype": "f8"}, "y": {"bdata": "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", "dtype": "f8"}, "z": {"bdata": "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", "dtype": "f8"}}], "layout": {"scene": {"xaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "0th"}, "type": "linear"}, "yaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "1st"}, "type": "linear"}, "zaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "2nd"}, "type": "linear"}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_point_cloud(point_clouds_basic[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## From data to persistence diagrams\n", "In raw form, point cloud data is not well suited for most machine learning algorithms because we ultimately need a _feature matrix_ $X$ where each row corresponds to a single sample (i.e. point cloud) and each column corresponds to a particular feature. In our example, each point cloud corresponds to a _collection_ of 3-dimensional vectors, so we need some way of representing this information in terms of a _single_ vector $x^{(i)}$ of feature values.\n", "\n", "In this notebook we will use persistent homology to generate a topological summary of a point cloud in the form of a so-called persistence diagram. Perhaps the simplest way to understand persistent homology is in terms of _growing balls around each point._ The basic idea is that by keeping track of when the balls intersect we can quantify when topological features like connected components and loops are \"born\" or \"die\".\n", "\n", "For example, consider two noisy clusters and track their connectivity or \"0-dimensional persistent homology\" as we increase the radius of the balls around each point:\n", "\n", "![phdim0](images/persistent_homology_0d.gif)\n", "<div style=\"text-align: left\">\n", "   <p style=\"text-align: left;\"> <b>Figure reference:</b> <a href=\"https://towardsdatascience.com/persistent-homology-with-examples-1974d4b9c3d0\">towardsdatascience.com/persistent-homology-with-examples-1974d4b9c3d0</a>. </p>\n", "</div>\n", "\n", "As the ball radius is grown from 0 to infinity, 0-dimensional persistent homology records when the ball in one connected component first intersects a ball of a _different_ connected component (denoted by a different colour in the animation).  At radius 0, a connected component for each point is _born_ and once any two balls touch we have a _death_ of a connected component with one color persisting and the other color vanishing. The vanishing of a color corresponds to a death, and therefore another point being added to the persistence diagram."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Behind the scenes, this process generating a persistence diagram from data involves several steps:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Construct a simplicial complex\n", "\n", "The effect of connecting points as we increase some radius $\\epsilon$ results in the creation of geometric objects called simplices.  \n", "\n", "![vr-complex](images/vr_complex.png)\n", "<div style=\"text-align: left\">\n", "   <p style=\"text-align: left;\"> <b>Figure reference:</b> <a href=\"https://bit.ly/2z9yP1d\">bit.ly/2z9yP1d</a>. </p>\n", "</div>\n", "\n", "A $k$-simplex $[p_0,\\ldots,p_k]$ is the convex hull of a set of $k+1$ affinely independent points in $\\mathbb{R}^n$. For example the 0-simplex is a point, the 1-simplex is a line, the 2-simplex is a triangular disc, and a 3-simplex a regular tetrahedron:\n", "\n", "![simplex](images/simplex.png)\n", "<div style=\"text-align: left\">\n", "   <p style=\"text-align: left;\"> <b>Figure reference:</b> <a href=\"https://bit.ly/2X8AsUX\">bit.ly/2X8AsUX</a>. </p>\n", "</div>\n", "\n", "Linking simplices together results in a simplicial complex $X$, which for computational efficiency is often represented abstractly as finite subsets of the vertex set of $X$. For example, the 2-complex shown below can be written as the set \n", "\n", "$$X=\\{a,b,c,d,e,[a,b],[b,c],[c,d],[d,e],[e,a],[b,e],[a,b,e]\\}\\,.$$\n", "\n", "![2-complex](images/2_complex.png)\n", "<div style=\"text-align: left\">\n", "   <p style=\"text-align: left;\"> <b>Figure reference:</b> <a href=\"https://arxiv.org/abs/1904.11044\">arxiv.org/abs/1904.11044</a>. </p>\n", "</div>\n", "\n", "One of the most common simplicial complexes one encounters in topological data analysis is the [Vietoris-Rips complex](https://en.wikipedia.org/wiki/Vietoris%E2%80%93Rips_complex), which is defined as the set of simplices $[p_0,\\ldots,p_k]$ such that the distance metric $d(p_i,p_j) \\leq \\epsilon$ for all $i,j$.\n", "\n", "#### 2. <PERSON><PERSON><PERSON> numbers\n", "\n", "Once the simplicial complex is constructed, we can ask questions about its topology. In particular, we can identify the presence of topological invariants such as connected pieces, holes and voids. This is achieved by computing quantities known as _Betti numbers_ which are informally [defined](https://en.wikipedia.org/wiki/Bet<PERSON>_number) as follows:\n", "\n", "> The $k$th Betti number refers to the number of $k$-dimensional holes on a topological surface. The first few Betti numbers have the following definitions for 0-dimensional, 1-dimensional, and 2-dimensional simplicial complexes:\n", "> \n", "> * $b_0$ is the number of connected components,\n", "> * $b_1$ is the number of one-dimensional or \"circular\" holes,\n", "> * $b_2$ is the number of two-dimensional \"voids\" or \"cavities\".\n", "\n", "By computing Betti numbers of a range of scale values $\\epsilon$, we can track which topological features _persist_ over this range. We can represent these changes in topology (technically homology) in terms of a persistence diagram, where each point corresponds to (birth, death) pairs, and points which are furthest away from the birth = death line correspond to the most persistent features.\n", "\n", "> Note: the reason we are talking about homology is because the $n$th Betti number represents the rank of the $n$th homology group $H_n$.\n", "\n", "An example showing the birth and death of \"loops\" (technically the homology group $H_1$) is shown below.\n", "\n", "![ph1D](images/persistent_homology_1d.gif)\n", "<div style=\"text-align: left\">\n", "   <p style=\"text-align: left;\"> <b>Figure reference:</b> <a href=\"https://towardsdatascience.com/persistent-homology-with-examples-1974d4b9c3d0\">towardsdatascience.com/persistent-homology-with-examples-1974d4b9c3d0</a>. </p>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's descend from abstraction and apply these concepts to our shape classification problem! In ``giotto-tda`` we can derive persistence diagrams from data by selecting the desired transformer in [gtda.homology](https://giotto-ai.github.io/gtda-docs/latest/modules/homology.html) and instantiating the class just like a ``scikit-learn`` estimator. Once the transformer is instantiated, we can make use of the fit-transform paradigm to generate the diagrams:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from gtda.homology import VietorisRipsPersistence\n", "\n", "# Track connected components, loops, and voids\n", "homology_dimensions = [0, 1, 2]\n", "\n", "# Collapse edges to speed up H2 persistence calculation!\n", "persistence = VietorisRipsPersistence(\n", "    metric=\"euclidean\",\n", "    homology_dimensions=homology_dimensions,\n", "    n_jobs=6,\n", "    collapse_edges=True,\n", ")\n", "\n", "diagrams_basic = persistence.fit_transform(point_clouds_basic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Tip: in our example each point cloud has the same number of points so can be fed as single NumPy array. If you have varying number of points per point cloud, you can pass a list of arrays instead."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once we have computed our persistence diagrams, we can compare how the circle, sphere and torus produce different patterns at each homology dimension:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hoverinfo": "none", "line": {"color": "black", "dash": "dash", "width": 1}, "mode": "lines", "showlegend": false, "type": "scatter", "x": [-0.027928228378295897, 1.424339647293091], "y": [-0.027928228378295897, 1.424339647293091]}, {"hoverinfo": "text", "hovertext": ["(0.0, 0.003752290504053235)", "(0.0, 0.0039034592919051647)", "(0.0, 0.004329046234488487)", "(0.0, 0.0059424955397844315)", "(0.0, 0.011055801063776016)", "(0.0, 0.011935543268918991)", "(0.0, 0.01203297358006239)", "(0.0, 0.012620999477803707)", "(0.0, 0.013332584872841835)", "(0.0, 0.013925213366746902)", "(0.0, 0.01519363559782505)", "(0.0, 0.0160863995552063)", "(0.0, 0.016449961811304092)", "(0.0, 0.016852473840117455)", "(0.0, 0.01690182276070118)", "(0.0, 0.017268307507038116)", "(0.0, 0.01730217970907688)", "(0.0, 0.017816510051488876)", "(0.0, 0.018342915922403336)", "(0.0, 0.01888106018304825)", "(0.0, 0.018946897238492966)", "(0.0, 0.01903974637389183)", "(0.0, 0.019253991544246674)", "(0.0, 0.019938236102461815)", "(0.0, 0.02007741667330265)", "(0.0, 0.020082825794816017)", "(0.0, 0.020290274173021317)", "(0.0, 0.020305326208472252)", "(0.0, 0.02032085694372654)", "(0.0, 0.02044585905969143)", "(0.0, 0.020706260576844215)", "(0.0, 0.020706746727228165)", "(0.0, 0.021458882838487625)", "(0.0, 0.021475765854120255)", "(0.0, 0.021563367918133736)", "(0.0, 0.021986452862620354)", "(0.0, 0.02228054590523243)", "(0.0, 0.022342387586832047)", "(0.0, 0.0224286075681448)", "(0.0, 0.02247452177107334)", "(0.0, 0.02251460589468479)", "(0.0, 0.022688711062073708)", "(0.0, 0.023442748934030533)", "(0.0, 0.02347499318420887)", "(0.0, 0.023518921807408333)", "(0.0, 0.023556234315037727)", "(0.0, 0.023963134735822678)", "(0.0, 0.02400902286171913)", "(0.0, 0.024465162307024002)", "(0.0, 0.024546463042497635)", "(0.0, 0.02474566362798214)", "(0.0, 0.02495894953608513)", "(0.0, 0.025156842544674873)", "(0.0, 0.025251884013414383)", "(0.0, 0.02550937980413437)", "(0.0, 0.025900855660438538)", "(0.0, 0.026369165629148483)", "(0.0, 0.02747093141078949)", "(0.0, 0.027855506166815758)", "(0.0, 0.02797398716211319)", "(0.0, 0.02824202924966812)", "(0.0, 0.028484199196100235)", "(0.0, 0.028699548915028572)", "(0.0, 0.02908225916326046)", "(0.0, 0.029336443170905113)", "(0.0, 0.030083615332841873)", "(0.0, 0.030088743194937706)", "(0.0, 0.030259184539318085)", "(0.0, 0.030277682468295097)", "(0.0, 0.03028886392712593)", "(0.0, 0.0304112546145916)", "(0.0, 0.03060051053762436)", "(0.0, 0.030673732981085777)", "(0.0, 0.030796412378549576)", "(0.0, 0.030799489468336105)", "(0.0, 0.031190114095807076)", "(0.0, 0.0312067698687315)", "(0.0, 0.03131554275751114)", "(0.0, 0.03215111419558525)", "(0.0, 0.032260991632938385)", "(0.0, 0.032416507601737976)", "(0.0, 0.03253590315580368)", "(0.0, 0.032768312841653824)", "(0.0, 0.03306613489985466)", "(0.0, 0.03336155787110329)", "(0.0, 0.03431921452283859)", "(0.0, 0.035190775990486145)", "(0.0, 0.03536991775035858)", "(0.0, 0.035525184124708176)", "(0.0, 0.03571460023522377)", "(0.0, 0.03583371639251709)", "(0.0, 0.0362367145717144)", "(0.0, 0.036500606685876846)", "(0.0, 0.03662848472595215)", "(0.0, 0.03673809394240379)", "(0.0, 0.037127427756786346)", "(0.0, 0.03713834658265114)", "(0.0, 0.03733912110328674)", "(0.0, 0.03797855228185654)", "(0.0, 0.03814934194087982)", "(0.0, 0.038418494164943695)", "(0.0, 0.03850327432155609)", "(0.0, 0.03851344808936119)", "(0.0, 0.039021432399749756)", "(0.0, 0.03917965665459633)", "(0.0, 0.03921753913164139)", "(0.0, 0.03925003483891487)", "(0.0, 0.0393204391002655)", "(0.0, 0.03938811644911766)", "(0.0, 0.03966895490884781)", "(0.0, 0.03974441438913345)", "(0.0, 0.03979437053203583)", "(0.0, 0.040792565792798996)", "(0.0, 0.04092630371451378)", "(0.0, 0.04113222286105156)", "(0.0, 0.04129871353507042)", "(0.0, 0.04131101444363594)", "(0.0, 0.041450802236795425)", "(0.0, 0.041715919971466064)", "(0.0, 0.04190693795681)", "(0.0, 0.04203375428915024)", "(0.0, 0.042040273547172546)", "(0.0, 0.04240577295422554)", "(0.0, 0.04241570830345154)", "(0.0, 0.042488884180784225)", "(0.0, 0.04252379387617111)", "(0.0, 0.04256555065512657)", "(0.0, 0.042592693120241165)", "(0.0, 0.04283994808793068)", "(0.0, 0.04294582083821297)", "(0.0, 0.043113838881254196)", "(0.0, 0.04333319887518883)", "(0.0, 0.04363085702061653)", "(0.0, 0.043748628348112106)", "(0.0, 0.04399392753839493)", "(0.0, 0.0442972369492054)", "(0.0, 0.044308535754680634)", "(0.0, 0.04457734525203705)", "(0.0, 0.04477807134389877)", "(0.0, 0.04480993002653122)", "(0.0, 0.045022256672382355)", "(0.0, 0.045065008103847504)", "(0.0, 0.04510049521923065)", "(0.0, 0.045109592378139496)", "(0.0, 0.045672424137592316)", "(0.0, 0.046258240938186646)", "(0.0, 0.04634988680481911)", "(0.0, 0.0465366430580616)", "(0.0, 0.046669475734233856)", "(0.0, 0.04702324792742729)", "(0.0, 0.04733942449092865)", "(0.0, 0.04748528078198433)", "(0.0, 0.04788254201412201)", "(0.0, 0.04901517555117607)", "(0.0, 0.049131084233522415)", "(0.0, 0.049139007925987244)", "(0.0, 0.04919552057981491)", "(0.0, 0.049225471913814545)", "(0.0, 0.04959066957235336)", "(0.0, 0.04970088601112366)", "(0.0, 0.050339411944150925)", "(0.0, 0.05043950304389)", "(0.0, 0.05056227371096611)", "(0.0, 0.0510377362370491)", "(0.0, 0.05151395499706268)", "(0.0, 0.05172884836792946)", "(0.0, 0.05179813876748085)", "(0.0, 0.05236238241195679)", "(0.0, 0.05238659679889679)", "(0.0, 0.05253699794411659)", "(0.0, 0.05264810845255852)", "(0.0, 0.05276035889983177)", "(0.0, 0.0527779646217823)", "(0.0, 0.0528387613594532)", "(0.0, 0.053069133311510086)", "(0.0, 0.05339178815484047)", "(0.0, 0.05357787013053894)", "(0.0, 0.05369826778769493)", "(0.0, 0.05391717702150345)", "(0.0, 0.05418892949819565)", "(0.0, 0.05418939143419266)", "(0.0, 0.05443127825856209)", "(0.0, 0.0545923113822937)", "(0.0, 0.054913152009248734)", "(0.0, 0.05510953441262245)", "(0.0, 0.05519048869609833)", "(0.0, 0.05520809814333916)", "(0.0, 0.0552864745259285)", "(0.0, 0.05530728027224541)", "(0.0, 0.05567760765552521)", "(0.0, 0.05594275891780853)", "(0.0, 0.05596563592553139)", "(0.0, 0.056507304310798645)", "(0.0, 0.05686759203672409)", "(0.0, 0.056992072612047195)", "(0.0, 0.05707903206348419)", "(0.0, 0.05856141820549965)", "(0.0, 0.058962382376194)", "(0.0, 0.05901115760207176)", "(0.0, 0.059029437601566315)", "(0.0, 0.05915270745754242)", "(0.0, 0.05933982506394386)", "(0.0, 0.05950607731938362)", "(0.0, 0.060047365725040436)", "(0.0, 0.06008581817150116)", "(0.0, 0.060778431594371796)", "(0.0, 0.06110065430402756)", "(0.0, 0.06110946461558342)", "(0.0, 0.061441343277692795)", "(0.0, 0.06154276430606842)", "(0.0, 0.061696287244558334)", "(0.0, 0.061795566231012344)", "(0.0, 0.06212679669260979)", "(0.0, 0.06233225762844086)", "(0.0, 0.06272640824317932)", "(0.0, 0.06275905668735504)", "(0.0, 0.06294901669025421)", "(0.0, 0.06308935582637787)", "(0.0, 0.06323521584272385)", "(0.0, 0.06324490904808044)", "(0.0, 0.06329835951328278)", "(0.0, 0.06376277655363083)", "(0.0, 0.06382355093955994)", "(0.0, 0.06396830081939697)", "(0.0, 0.06411099433898926)", "(0.0, 0.0642276480793953)", "(0.0, 0.0643458291888237)", "(0.0, 0.06438857316970825)", "(0.0, 0.0644383579492569)", "(0.0, 0.06449054181575775)", "(0.0, 0.0646001324057579)", "(0.0, 0.0646594688296318)", "(0.0, 0.06503503769636154)", "(0.0, 0.0651240348815918)", "(0.0, 0.06528571993112564)", "(0.0, 0.06531170010566711)", "(0.0, 0.06555669009685516)", "(0.0, 0.06568079441785812)", "(0.0, 0.06572634726762772)", "(0.0, 0.0662037804722786)", "(0.0, 0.06713847815990448)", "(0.0, 0.06715212762355804)", "(0.0, 0.06716714054346085)", "(0.0, 0.06728895753622055)", "(0.0, 0.06730718165636063)", "(0.0, 0.06740468740463257)", "(0.0, 0.06744761019945145)", "(0.0, 0.06768257915973663)", "(0.0, 0.0678204745054245)", "(0.0, 0.06784934550523758)", "(0.0, 0.06789715588092804)", "(0.0, 0.06795836985111237)", "(0.0, 0.0679778903722763)", "(0.0, 0.06880106776952744)", "(0.0, 0.06950496882200241)", "(0.0, 0.07021094858646393)", "(0.0, 0.07044633477926254)", "(0.0, 0.07065241038799286)", "(0.0, 0.07067427784204483)", "(0.0, 0.07072563469409943)", "(0.0, 0.07103005051612854)", "(0.0, 0.07199982553720474)", "(0.0, 0.07201308012008667)", "(0.0, 0.07215490937232971)", "(0.0, 0.07338259369134903)", "(0.0, 0.0734768956899643)", "(0.0, 0.073764368891716)", "(0.0, 0.07383523136377335)", "(0.0, 0.0739220455288887)", "(0.0, 0.07418413460254669)", "(0.0, 0.0742119625210762)", "(0.0, 0.07429207861423492)", "(0.0, 0.07435115426778793)", "(0.0, 0.07515324652194977)", "(0.0, 0.07563910633325577)", "(0.0, 0.07571905851364136)", "(0.0, 0.07575442641973495)", "(0.0, 0.0764542892575264)", "(0.0, 0.07646681368350983)", "(0.0, 0.07647992670536041)", "(0.0, 0.07654200494289398)", "(0.0, 0.07665254175662994)", "(0.0, 0.07725181430578232)", "(0.0, 0.07742246240377426)", "(0.0, 0.07869531214237213)", "(0.0, 0.07881389558315277)", "(0.0, 0.07892682403326035)", "(0.0, 0.07902264595031738)", "(0.0, 0.07942860573530197)", "(0.0, 0.07954651117324829)", "(0.0, 0.08029244840145111)", "(0.0, 0.08062092959880829)", "(0.0, 0.08062180131673813)", "(0.0, 0.08109297603368759)", "(0.0, 0.08135867863893509)", "(0.0, 0.08192528039216995)", "(0.0, 0.08249000459909439)", "(0.0, 0.08254916965961456)", "(0.0, 0.0829564556479454)", "(0.0, 0.08373991400003433)", "(0.0, 0.08374439924955368)", "(0.0, 0.08431881666183472)", "(0.0, 0.08487749099731445)", "(0.0, 0.0853656455874443)", "(0.0, 0.08565310388803482)", "(0.0, 0.08568833023309708)", "(0.0, 0.08589325845241547)", "(0.0, 0.0860925167798996)", "(0.0, 0.08612992614507675)", "(0.0, 0.08625219762325287)", "(0.0, 0.08625897020101547)", "(0.0, 0.08627360314130783)", "(0.0, 0.08683986216783524)", "(0.0, 0.08697472512722015)", "(0.0, 0.08698758482933044)", "(0.0, 0.08719253540039062)", "(0.0, 0.08876658976078033)", "(0.0, 0.08953840285539627)", "(0.0, 0.08966386318206787)", "(0.0, 0.08978027105331421)", "(0.0, 0.09030774980783463)", "(0.0, 0.09040846675634384)", "(0.0, 0.0906008630990982)", "(0.0, 0.0906776636838913)", "(0.0, 0.09099868685007095)", "(0.0, 0.09161803871393204)", "(0.0, 0.09244763851165771)", "(0.0, 0.09250571578741074)", "(0.0, 0.09267670661211014)", "(0.0, 0.09268489480018616)", "(0.0, 0.09285405278205872)", "(0.0, 0.09337415546178818)", "(0.0, 0.09350976347923279)", "(0.0, 0.09352051466703415)", "(0.0, 0.09367308765649796)", "(0.0, 0.09377079457044601)", "(0.0, 0.09388821572065353)", "(0.0, 0.09423036873340607)", "(0.0, 0.0944986492395401)", "(0.0, 0.09498218446969986)", "(0.0, 0.09541880339384079)", "(0.0, 0.09544660151004791)", "(0.0, 0.09552031010389328)", "(0.0, 0.0958486944437027)", "(0.0, 0.09606137126684189)", "(0.0, 0.09626384824514389)", "(0.0, 0.09672155231237411)", "(0.0, 0.097076416015625)", "(0.0, 0.09748868644237518)", "(0.0, 0.09782441705465317)", "(0.0, 0.09786062687635422)", "(0.0, 0.09856148064136505)", "(0.0, 0.0999329686164856)", "(0.0, 0.10080665349960327)", "(0.0, 0.10089987516403198)", "(0.0, 0.10133806616067886)", "(0.0, 0.10269702225923538)", "(0.0, 0.10331039875745773)", "(0.0, 0.10448630899190903)", "(0.0, 0.10496830940246582)", "(0.0, 0.10535896569490433)", "(0.0, 0.10572569072246552)", "(0.0, 0.10596086829900742)", "(0.0, 0.10622087866067886)", "(0.0, 0.10632048547267914)", "(0.0, 0.10716274380683899)", "(0.0, 0.10755086690187454)", "(0.0, 0.1084800735116005)", "(0.0, 0.10848038643598557)", "(0.0, 0.1099744662642479)", "(0.0, 0.11001934856176376)", "(0.0, 0.11015383154153824)", "(0.0, 0.11062018573284149)", "(0.0, 0.11247046291828156)", "(0.0, 0.11263342201709747)", "(0.0, 0.11348109692335129)", "(0.0, 0.11376718431711197)", "(0.0, 0.11410672217607498)", "(0.0, 0.11470124125480652)", "(0.0, 0.1154208853840828)", "(0.0, 0.11586757004261017)", "(0.0, 0.11611150205135345)", "(0.0, 0.11778286099433899)", "(0.0, 0.1185096800327301)", "(0.0, 0.12057032436132431)", "(0.0, 0.12088611721992493)", "(0.0, 0.12145046889781952)", "(0.0, 0.12180634588003159)", "(0.0, 0.12240874767303467)", "(0.0, 0.12387378513813019)", "(0.0, 0.12487979233264923)", "(0.0, 0.1255578249692917)", "(0.0, 0.13501380383968353)", "(0.0, 0.1498793214559555)", "(0.0, 0.15311209857463837)", "(0.0, 0.16967463493347168)", "(0.0, 0.17361535131931305)", "(0.0, 0.18300014734268188)", "(0.0, 0.20459480583667755)"], "mode": "markers", "name": "H0", "type": "scatter", "x": {"bdata": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "dtype": "f8"}, "y": {"bdata": "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", "dtype": "f8"}}, {"hoverinfo": "text", "hovertext": ["(0.3086487054824829, 0.3124566376209259)", "(0.21787579357624054, 0.21991349756717682)", "(0.21646089851856232, 0.23114438354969025)", "(0.17850255966186523, 0.19778911769390106)", "(0.17762383818626404, 0.2579253911972046)", "(0.17234815657138824, 0.19956226646900177)", "(0.17029714584350586, 0.1839011311531067)", "(0.16713853180408478, 0.16812019050121307)", "(0.15477924048900604, 0.15711656212806702)", "(0.1531434804201126, 0.229735866189003)", "(0.15242624282836914, 0.17360639572143555)", "(0.1517374962568283, 0.1543605774641037)", "(0.14905960857868195, 0.15917125344276428)", "(0.1466209590435028, 0.16892120242118835)", "(0.14439527690410614, 0.180717334151268)", "(0.1441238820552826, 0.15526968240737915)", "(0.1425427347421646, 0.21720725297927856)", "(0.14249734580516815, 0.15479253232479095)", "(0.1413927972316742, 0.21517281234264374)", "(0.13960032165050507, 0.15169274806976318)", "(0.1387653797864914, 0.1825575828552246)", "(0.13617756962776184, 0.17752890288829803)", "(0.13560500741004944, 0.15461869537830353)", "(0.13283929228782654, 0.1605701744556427)", "(0.1326659470796585, 0.14642728865146637)", "(0.13149558007717133, 0.14042484760284424)", "(0.13129845261573792, 0.14512109756469727)", "(0.1312120258808136, 0.13147541880607605)", "(0.1305760145187378, 0.19319649040699005)", "(0.12672607600688934, 0.13919033110141754)", "(0.12662853300571442, 0.15320846438407898)", "(0.126034215092659, 0.15285907685756683)", "(0.12493228912353516, 0.13570158183574677)", "(0.12397391349077225, 0.1857668161392212)", "(0.12372573465108871, 0.12694978713989258)", "(0.12268400937318802, 0.14834727346897125)", "(0.12240919470787048, 0.14852602779865265)", "(0.12216435372829437, 1.269079566001892)", "(0.12169910967350006, 0.20642440021038055)", "(0.11781390011310577, 0.14528627693653107)", "(0.117327980697155, 0.13106635212898254)", "(0.11632116883993149, 0.11804336309432983)", "(0.11608856916427612, 0.21069082617759705)", "(0.11598274856805801, 0.16276025772094727)", "(0.11557261645793915, 0.11595287919044495)", "(0.11549633741378784, 0.1401454657316208)", "(0.11475483328104019, 0.11554806679487228)", "(0.11422505229711533, 0.13321919739246368)", "(0.11240358650684357, 0.11254169046878815)", "(0.11176200211048126, 0.11760435253381729)", "(0.11126507818698883, 0.12891820073127747)", "(0.11055494844913483, 0.13856817781925201)", "(0.109856516122818, 0.12967611849308014)", "(0.10775864124298096, 0.14441724121570587)", "(0.10680603981018066, 0.14048413932323456)", "(0.1052137017250061, 0.12991240620613098)", "(0.10397718101739883, 0.12084843963384628)", "(0.10259589552879333, 0.1313692182302475)", "(0.1017727330327034, 0.14824238419532776)", "(0.09992389380931854, 0.10263043642044067)", "(0.098690927028656, 0.13040024042129517)", "(0.09753072261810303, 0.12007635086774826)", "(0.09713185578584671, 0.1336844265460968)", "(0.09699999541044235, 0.12703819572925568)", "(0.09373968094587326, 0.09846086800098419)", "(0.09341999888420105, 0.09502628445625305)", "(0.09331512451171875, 0.09382155537605286)", "(0.08927565813064575, 0.09025686979293823)", "(0.088057741522789, 0.10920052975416183)", "(0.0869426429271698, 0.10514694452285767)", "(0.08691985160112381, 0.12528258562088013)", "(0.08576036244630814, 0.0905953198671341)", "(0.08244810253381729, 0.08680528402328491)", "(0.0817088782787323, 0.08651018142700195)", "(0.07862652093172073, 0.08823138475418091)", "(0.0776326060295105, 0.11395908147096634)", "(0.07634872943162918, 0.08563991636037827)", "(0.07263640314340591, 0.07574329525232315)", "(0.07190831005573273, 0.07862616330385208)", "(0.07138599455356598, 0.08084586262702942)", "(0.06920086592435837, 0.0761745497584343)", "(0.06813828647136688, 0.07136018574237823)", "(0.06224418804049492, 0.06762181222438812)", "(0.05458964407444, 0.05609622970223427)", "(0.04640237241983414, 0.0479305200278759)"], "mode": "markers", "name": "H1", "type": "scatter", "x": {"bdata": "AAAAgObA0z8AAACgWuPLPwAAAKD9tMs/AAAAACzZxj8AAADAYLzGPwAAACCBD8Y/AAAAAEzMxT8AAACgy2TFPwAAAGDOz8M/AAAAoDSawz8AAAAAtILDPwAAAGAibMM/AAAAoGIUwz8AAADAecTCPwAAAGCLe8I/AAAAwKZywj8AAAAg1z7CPwAAAGBaPcI/AAAAwCgZwj8AAABgbN7BPwAAAGAQw8E/AAAAQERuwT8AAABAgVvBPwAAAMDgAME/AAAAoDL7wD8AAADg2NTAPwAAAEBjzsA/AAAAQI7LwD8AAAAAt7bAPwAAAGCPOMA/AAAAIF01wD8AAACg4yHAPwAAAACQ+78/AAAAIMG8vz8AAABgfay/PwAAACA4aL8/AAAAgDVWvz8AAADAKUa/PwAAAECsJ78/AAAAQA0pvj8AAADgNAm+PwAAAGA5x70/AAAAAPu3vT8AAACgC7G9PwAAAMAqlr0/AAAAACuRvT8AAACgkmC9PwAAAGDaPb0/AAAAQHvGvD8AAABAb5y8PwAAAEDee7w/AAAAQFRNvD8AAACAjh+8PwAAAAASlrs/AAAAAKRXuz8AAAAASe+6PwAAAKA/nro/AAAAgLlDuj8AAAAgxw26PwAAAMCclLk/AAAAAM9DuT8AAAAAxve4PwAAACCi3bg/AAAA4P3UuD8AAADgUv+3PwAAAIBf6rc/AAAAAIDjtz8AAAAAxdq2PwAAAMDzirY/AAAAgN9Btj8AAAAgYUC2PwAAACBk9LU/AAAAoFEbtT8AAACA3+q0PwAAACDeILQ/AAAAALvfsz8AAAAgl4uzPwAAAKBMmLI/AAAAQJVosj8AAABAWkayPwAAAOAlt7E/AAAAwIJxsT8AAABgeN6vPwAAAIAs86s/AAAAQA3Cpz8=", "dtype": "f8"}, "y": {"bdata": "AAAAIEr/0z8AAAAgICbMPwAAAKAjls0/AAAAYCdRyT8AAACA2YHQPwAAAKBBi8k/AAAAgBKKxz8AAABg9oTFPwAAAEBlHMQ/AAAAIPxnzT8AAAAAvDjGPwAAAGAWwsM/AAAAQLlfxD8AAADANZ/FPwAAAOC+Icc/AAAAgODfwz8AAACAcs3LPwAAAOA90MM/AAAAYMiKyz8AAAAAq2rDPwAAAAAMXsc/AAAAYES5xj8AAACgi8rDPwAAAECQjcQ/AAAAICG+wj8AAAAAcfnBPwAAAABUk8I/AAAAwC/UwD8AAACgqbrIPwAAACD90ME/AAAAwFWcwz8AAADg4pDDPwAAAGCrXsE/AAAAADXHxz8AAAAA5D/APwAAACAL/cI/AAAAoOYCwz8AAABgJk70PwAAAGAdbMo/AAAAoL2Ywj8AAABAyMbAPwAAAAAXOL4/AAAAwOr3yj8AAAAAVNXEPwAAAIAWr70/AAAAYEnwwT8AAADgjpS9PwAAAKBTDcE/AAAAQIjPvD8AAACgURu+PwAAAEBkgMA/AAAAIJq8wT8AAAAgOpnAPwAAAKBDfMI/AAAAYGL7wT8AAABA+KDAPwAAAGDs774/AAAA4LTQwD8AAABAm/nCPwAAAAD9Rbo/AAAAgPSwwD8AAADgUr2+PwAAAECSHME/AAAAoMlCwD8AAABAuzS5PwAAAICkU7g/AAAAgLAEuD8AAAAAExu3PwAAAOCQ9Ls/AAAAAOnquj8AAACAQgnAPwAAAEBBMbc/AAAAAN84tj8AAAAAiCW2PwAAAABVlrY/AAAAIGwsvT8AAABgf+y1PwAAAKDpY7M/AAAAINggtD8AAACAULK0PwAAAOAsgLM/AAAAQKlEsj8AAADAqU+xPwAAACCluKw/AAAAYFmKqD8=", "dtype": "f8"}}, {"hoverinfo": "text", "hovertext": ["(1.3525043725967407, 1.396411418914795)", "(1.348767876625061, 1.3553743362426758)"], "mode": "markers", "name": "H2", "type": "scatter", "x": {"bdata": "AAAAoNuj9T8AAACgjZT1Pw==", "dtype": "f8"}, "y": {"bdata": "AAAAgLNX9j8AAAAAna/1Pw==", "dtype": "f8"}}], "layout": {"height": 500, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 500, "xaxis": {"autorange": false, "exponentformat": "e", "linecolor": "black", "linewidth": 1, "mirror": false, "range": [-0.027928228378295897, 1.424339647293091], "showexponent": "all", "showline": true, "side": "bottom", "ticks": "outside", "title": {"text": "Birth"}, "type": "linear", "zeroline": true}, "yaxis": {"autorange": false, "exponentformat": "e", "linecolor": "black", "linewidth": 1, "mirror": false, "range": [-0.027928228378295897, 1.424339647293091], "scaleanchor": "x", "scaleratio": 1, "showexponent": "all", "showline": true, "side": "left", "ticks": "outside", "title": {"text": "Death"}, "type": "linear", "zeroline": true}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["from gtda.plotting import plot_diagram\n", "\n", "# Circle\n", "plot_diagram(diagrams_basic[0])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hoverinfo": "none", "line": {"color": "black", "dash": "dash", "width": 1}, "mode": "lines", "showlegend": false, "type": "scatter", "x": [-0.02551668643951416, 1.3013510084152222], "y": [-0.02551668643951416, 1.3013510084152222]}, {"hoverinfo": "text", "hovertext": ["(0.0, 0.011052747257053852)", "(0.0, 0.02783695049583912)", "(0.0, 0.027927633374929428)", "(0.0, 0.03852146118879318)", "(0.0, 0.04293694347143173)", "(0.0, 0.04574672877788544)", "(0.0, 0.05002463608980179)", "(0.0, 0.051806192845106125)", "(0.0, 0.052681855857372284)", "(0.0, 0.0564553365111351)", "(0.0, 0.05778425186872482)", "(0.0, 0.058533746749162674)", "(0.0, 0.05886944755911827)", "(0.0, 0.05946122854948044)", "(0.0, 0.059876926243305206)", "(0.0, 0.06318411976099014)", "(0.0, 0.06385596096515656)", "(0.0, 0.06760020554065704)", "(0.0, 0.06766650080680847)", "(0.0, 0.06876637786626816)", "(0.0, 0.0690993219614029)", "(0.0, 0.06939253211021423)", "(0.0, 0.07301205396652222)", "(0.0, 0.0746137872338295)", "(0.0, 0.07470384240150452)", "(0.0, 0.07743465155363083)", "(0.0, 0.07799232006072998)", "(0.0, 0.08144237101078033)", "(0.0, 0.08151404559612274)", "(0.0, 0.08287358283996582)", "(0.0, 0.08390219509601593)", "(0.0, 0.08400259166955948)", "(0.0, 0.08432729542255402)", "(0.0, 0.08590447902679443)", "(0.0, 0.08685093373060226)", "(0.0, 0.08707360923290253)", "(0.0, 0.08746687322854996)", "(0.0, 0.08813770115375519)", "(0.0, 0.0887928232550621)", "(0.0, 0.08906053751707077)", "(0.0, 0.08910586684942245)", "(0.0, 0.08925366401672363)", "(0.0, 0.09015674144029617)", "(0.0, 0.0921521708369255)", "(0.0, 0.09227343648672104)", "(0.0, 0.09236778318881989)", "(0.0, 0.09364709258079529)", "(0.0, 0.09427925944328308)", "(0.0, 0.09448136389255524)", "(0.0, 0.09565263986587524)", "(0.0, 0.09600365906953812)", "(0.0, 0.09685783833265305)", "(0.0, 0.09782258421182632)", "(0.0, 0.09784320741891861)", "(0.0, 0.09792215377092361)", "(0.0, 0.09799054265022278)", "(0.0, 0.09818661212921143)", "(0.0, 0.09842820465564728)", "(0.0, 0.0990871861577034)", "(0.0, 0.0991077870130539)", "(0.0, 0.09961406886577606)", "(0.0, 0.10021570324897766)", "(0.0, 0.10039374977350235)", "(0.0, 0.10076957195997238)", "(0.0, 0.10244391858577728)", "(0.0, 0.10254018008708954)", "(0.0, 0.10324562340974808)", "(0.0, 0.10335762053728104)", "(0.0, 0.10427158325910568)", "(0.0, 0.10508476197719574)", "(0.0, 0.10516242682933807)", "(0.0, 0.10622654110193253)", "(0.0, 0.10700555890798569)", "(0.0, 0.10744845867156982)", "(0.0, 0.10791979730129242)", "(0.0, 0.10877599567174911)", "(0.0, 0.11010491102933884)", "(0.0, 0.11114950478076935)", "(0.0, 0.11115734279155731)", "(0.0, 0.11195996403694153)", "(0.0, 0.11250519752502441)", "(0.0, 0.11351662129163742)", "(0.0, 0.11415791511535645)", "(0.0, 0.11499416828155518)", "(0.0, 0.1151462122797966)", "(0.0, 0.11522356420755386)", "(0.0, 0.11546114832162857)", "(0.0, 0.11566022783517838)", "(0.0, 0.11650028079748154)", "(0.0, 0.11680381745100021)", "(0.0, 0.11740053445100784)", "(0.0, 0.1182313933968544)", "(0.0, 0.11855193972587585)", "(0.0, 0.11927014589309692)", "(0.0, 0.11979611963033676)", "(0.0, 0.11998564004898071)", "(0.0, 0.12039066851139069)", "(0.0, 0.12066525220870972)", "(0.0, 0.12141167372465134)", "(0.0, 0.12145515531301498)", "(0.0, 0.12245112657546997)", "(0.0, 0.122476726770401)", "(0.0, 0.12373095750808716)", "(0.0, 0.12407118827104568)", "(0.0, 0.12437640130519867)", "(0.0, 0.1244548112154007)", "(0.0, 0.12522496283054352)", "(0.0, 0.12613381445407867)", "(0.0, 0.1266264170408249)", "(0.0, 0.12662771344184875)", "(0.0, 0.1270006000995636)", "(0.0, 0.12703725695610046)", "(0.0, 0.1272905170917511)", "(0.0, 0.12767617404460907)", "(0.0, 0.12812398374080658)", "(0.0, 0.12847675383090973)", "(0.0, 0.12911714613437653)", "(0.0, 0.12965154647827148)", "(0.0, 0.12993478775024414)", "(0.0, 0.13003842532634735)", "(0.0, 0.1308041512966156)", "(0.0, 0.13080886006355286)", "(0.0, 0.13092902302742004)", "(0.0, 0.1317032426595688)", "(0.0, 0.13187429308891296)", "(0.0, 0.13224510848522186)", "(0.0, 0.13258446753025055)", "(0.0, 0.13301116228103638)", "(0.0, 0.13318824768066406)", "(0.0, 0.1339152306318283)", "(0.0, 0.13447201251983643)", "(0.0, 0.13548320531845093)", "(0.0, 0.13657046854496002)", "(0.0, 0.13681107759475708)", "(0.0, 0.1373511701822281)", "(0.0, 0.1389031857252121)", "(0.0, 0.139739528298378)", "(0.0, 0.14036476612091064)", "(0.0, 0.1409495770931244)", "(0.0, 0.141577810049057)", "(0.0, 0.14210695028305054)", "(0.0, 0.1421193778514862)", "(0.0, 0.14251457154750824)", "(0.0, 0.14281976222991943)", "(0.0, 0.14365527033805847)", "(0.0, 0.1441553682088852)", "(0.0, 0.14416323602199554)", "(0.0, 0.14416785538196564)", "(0.0, 0.1445799022912979)", "(0.0, 0.14539147913455963)", "(0.0, 0.14591416716575623)", "(0.0, 0.14670763909816742)", "(0.0, 0.14773942530155182)", "(0.0, 0.1479095071554184)", "(0.0, 0.14796090126037598)", "(0.0, 0.1481233686208725)", "(0.0, 0.14824075996875763)", "(0.0, 0.14849185943603516)", "(0.0, 0.14875900745391846)", "(0.0, 0.14877234399318695)", "(0.0, 0.14921075105667114)", "(0.0, 0.14943048357963562)", "(0.0, 0.14991477131843567)", "(0.0, 0.15041367709636688)", "(0.0, 0.15056398510932922)", "(0.0, 0.1506715565919876)", "(0.0, 0.15084704756736755)", "(0.0, 0.15085925161838531)", "(0.0, 0.15151594579219818)", "(0.0, 0.15166623890399933)", "(0.0, 0.15203803777694702)", "(0.0, 0.1521720439195633)", "(0.0, 0.15227054059505463)", "(0.0, 0.1525464802980423)", "(0.0, 0.1527405083179474)", "(0.0, 0.15324579179286957)", "(0.0, 0.1535041332244873)", "(0.0, 0.15554702281951904)", "(0.0, 0.15630482137203217)", "(0.0, 0.15659336745738983)", "(0.0, 0.15660054981708527)", "(0.0, 0.15715892612934113)", "(0.0, 0.15745395421981812)", "(0.0, 0.1580546349287033)", "(0.0, 0.15829062461853027)", "(0.0, 0.15833760797977448)", "(0.0, 0.15861335396766663)", "(0.0, 0.15897247195243835)", "(0.0, 0.1609157770872116)", "(0.0, 0.16106484830379486)", "(0.0, 0.1614246368408203)", "(0.0, 0.16150915622711182)", "(0.0, 0.1615166813135147)", "(0.0, 0.16214226186275482)", "(0.0, 0.16222940385341644)", "(0.0, 0.16361859440803528)", "(0.0, 0.16409064829349518)", "(0.0, 0.16446684300899506)", "(0.0, 0.16466455161571503)", "(0.0, 0.16487395763397217)", "(0.0, 0.1663770079612732)", "(0.0, 0.1666596680879593)", "(0.0, 0.16693715751171112)", "(0.0, 0.16788242757320404)", "(0.0, 0.16812853515148163)", "(0.0, 0.16831965744495392)", "(0.0, 0.16873997449874878)", "(0.0, 0.1695794016122818)", "(0.0, 0.16964884102344513)", "(0.0, 0.1697162538766861)", "(0.0, 0.17050336301326752)", "(0.0, 0.1706865429878235)", "(0.0, 0.17092816531658173)", "(0.0, 0.17108671367168427)", "(0.0, 0.17202527821063995)", "(0.0, 0.17235101759433746)", "(0.0, 0.1729905754327774)", "(0.0, 0.173350989818573)", "(0.0, 0.17342989146709442)", "(0.0, 0.1735275685787201)", "(0.0, 0.17444922029972076)", "(0.0, 0.17465151846408844)", "(0.0, 0.1748443841934204)", "(0.0, 0.1761474609375)", "(0.0, 0.1768401712179184)", "(0.0, 0.1769769936800003)", "(0.0, 0.17720620334148407)", "(0.0, 0.17748507857322693)", "(0.0, 0.17941857874393463)", "(0.0, 0.1800309717655182)", "(0.0, 0.18202786147594452)", "(0.0, 0.18213441967964172)", "(0.0, 0.18265871703624725)", "(0.0, 0.18314199149608612)", "(0.0, 0.1835460513830185)", "(0.0, 0.18375389277935028)", "(0.0, 0.18408679962158203)", "(0.0, 0.18442265689373016)", "(0.0, 0.18514448404312134)", "(0.0, 0.18584299087524414)", "(0.0, 0.1858503371477127)", "(0.0, 0.18626710772514343)", "(0.0, 0.18629366159439087)", "(0.0, 0.18693813681602478)", "(0.0, 0.18699555099010468)", "(0.0, 0.18788643181324005)", "(0.0, 0.18791332840919495)", "(0.0, 0.18895550072193146)", "(0.0, 0.18934258818626404)", "(0.0, 0.1895010620355606)", "(0.0, 0.1897246092557907)", "(0.0, 0.18986868858337402)", "(0.0, 0.19069169461727142)", "(0.0, 0.19101311266422272)", "(0.0, 0.19128024578094482)", "(0.0, 0.19228850305080414)", "(0.0, 0.19290480017662048)", "(0.0, 0.1929943561553955)", "(0.0, 0.19323234260082245)", "(0.0, 0.19432350993156433)", "(0.0, 0.19525876641273499)", "(0.0, 0.19539695978164673)", "(0.0, 0.19733639061450958)", "(0.0, 0.1984279304742813)", "(0.0, 0.19846513867378235)", "(0.0, 0.19848115742206573)", "(0.0, 0.1987106055021286)", "(0.0, 0.19876629114151)", "(0.0, 0.1992374211549759)", "(0.0, 0.19934354722499847)", "(0.0, 0.19934727251529694)", "(0.0, 0.2004234939813614)", "(0.0, 0.20083989202976227)", "(0.0, 0.2011989802122116)", "(0.0, 0.20290151238441467)", "(0.0, 0.2032196968793869)", "(0.0, 0.20395375788211823)", "(0.0, 0.20435206592082977)", "(0.0, 0.2047387957572937)", "(0.0, 0.20482882857322693)", "(0.0, 0.20562700927257538)", "(0.0, 0.2056892067193985)", "(0.0, 0.20577089488506317)", "(0.0, 0.20578955113887787)", "(0.0, 0.20598629117012024)", "(0.0, 0.20652908086776733)", "(0.0, 0.20717397332191467)", "(0.0, 0.20952697098255157)", "(0.0, 0.20971301198005676)", "(0.0, 0.20975999534130096)", "(0.0, 0.21204446256160736)", "(0.0, 0.21337436139583588)", "(0.0, 0.21409675478935242)", "(0.0, 0.21444422006607056)", "(0.0, 0.2146688848733902)", "(0.0, 0.21536098420619965)", "(0.0, 0.21540291607379913)", "(0.0, 0.21545451879501343)", "(0.0, 0.21550749242305756)", "(0.0, 0.2155090570449829)", "(0.0, 0.21554799377918243)", "(0.0, 0.2160346508026123)", "(0.0, 0.21628005802631378)", "(0.0, 0.21728356182575226)", "(0.0, 0.21782173216342926)", "(0.0, 0.21783797442913055)", "(0.0, 0.21799427270889282)", "(0.0, 0.220185786485672)", "(0.0, 0.2203816920518875)", "(0.0, 0.22121553122997284)", "(0.0, 0.22276313602924347)", "(0.0, 0.2228258103132248)", "(0.0, 0.22337943315505981)", "(0.0, 0.2235483080148697)", "(0.0, 0.2236521989107132)", "(0.0, 0.22379204630851746)", "(0.0, 0.224196195602417)", "(0.0, 0.22553308308124542)", "(0.0, 0.2268686294555664)", "(0.0, 0.22741568088531494)", "(0.0, 0.22751855850219727)", "(0.0, 0.22858919203281403)", "(0.0, 0.22886961698532104)", "(0.0, 0.22968104481697083)", "(0.0, 0.23024672269821167)", "(0.0, 0.2302936166524887)", "(0.0, 0.23101283609867096)", "(0.0, 0.23130908608436584)", "(0.0, 0.23151244223117828)", "(0.0, 0.23160827159881592)", "(0.0, 0.23249198496341705)", "(0.0, 0.23310773074626923)", "(0.0, 0.23330800235271454)", "(0.0, 0.23409858345985413)", "(0.0, 0.23475049436092377)", "(0.0, 0.23513531684875488)", "(0.0, 0.23546777665615082)", "(0.0, 0.23636838793754578)", "(0.0, 0.23638583719730377)", "(0.0, 0.23844517767429352)", "(0.0, 0.23856237530708313)", "(0.0, 0.23908905684947968)", "(0.0, 0.23996348679065704)", "(0.0, 0.24030187726020813)", "(0.0, 0.24066363275051117)", "(0.0, 0.24126337468624115)", "(0.0, 0.24229837954044342)", "(0.0, 0.24263375997543335)", "(0.0, 0.24408841133117676)", "(0.0, 0.2441176325082779)", "(0.0, 0.24507346749305725)", "(0.0, 0.24508652091026306)", "(0.0, 0.24546638131141663)", "(0.0, 0.24621045589447021)", "(0.0, 0.24698130786418915)", "(0.0, 0.24725639820098877)", "(0.0, 0.2508688271045685)", "(0.0, 0.25118619203567505)", "(0.0, 0.25207534432411194)", "(0.0, 0.25251534581184387)", "(0.0, 0.2535569667816162)", "(0.0, 0.2548133134841919)", "(0.0, 0.25557681918144226)", "(0.0, 0.2558121383190155)", "(0.0, 0.2560396194458008)", "(0.0, 0.2570784389972687)", "(0.0, 0.2575647532939911)", "(0.0, 0.2580404579639435)", "(0.0, 0.25823724269866943)", "(0.0, 0.25835663080215454)", "(0.0, 0.25856801867485046)", "(0.0, 0.25909024477005005)", "(0.0, 0.2591477334499359)", "(0.0, 0.2593221962451935)", "(0.0, 0.2605345845222473)", "(0.0, 0.26086094975471497)", "(0.0, 0.2618495225906372)", "(0.0, 0.26245710253715515)", "(0.0, 0.2660021483898163)", "(0.0, 0.26621997356414795)", "(0.0, 0.2699089050292969)", "(0.0, 0.27068644762039185)", "(0.0, 0.27105242013931274)", "(0.0, 0.2710959315299988)", "(0.0, 0.27169570326805115)", "(0.0, 0.2717646360397339)", "(0.0, 0.2724766433238983)", "(0.0, 0.2726620137691498)", "(0.0, 0.274238258600235)", "(0.0, 0.27630606293678284)", "(0.0, 0.27847588062286377)", "(0.0, 0.28093770146369934)", "(0.0, 0.28113633394241333)", "(0.0, 0.2865860164165497)", "(0.0, 0.29799553751945496)", "(0.0, 0.29968446493148804)", "(0.0, 0.30553311109542847)", "(0.0, 0.31589770317077637)", "(0.0, 0.3509260416030884)"], "mode": "markers", "name": "H0", "type": "scatter", "x": {"bdata": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "dtype": "f8"}, "y": {"bdata": "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", "dtype": "f8"}}, {"hoverinfo": "text", "hovertext": ["(0.40932247042655945, 0.4094308018684387)", "(0.3907747268676758, 0.43382516503334045)", "(0.3829698860645294, 0.4720827341079712)", "(0.37655094265937805, 0.49612703919410706)", "(0.37333306670188904, 0.40867212414741516)", "(0.36728087067604065, 0.41669800877571106)", "(0.3628998100757599, 0.37039706110954285)", "(0.36275866627693176, 0.3727697432041168)", "(0.3618111312389374, 0.38610249757766724)", "(0.3614921569824219, 0.3786066174507141)", "(0.35938844084739685, 0.4169984757900238)", "(0.3583545386791229, 0.46613267064094543)", "(0.3572031855583191, 0.3769189715385437)", "(0.35421252250671387, 0.38482099771499634)", "(0.3499879240989685, 0.3985734283924103)", "(0.34743160009384155, 0.35390347242355347)", "(0.3455209732055664, 0.3642767071723938)", "(0.34547683596611023, 0.3750114142894745)", "(0.3419654071331024, 0.36913537979125977)", "(0.3391810953617096, 0.3519081771373749)", "(0.33812224864959717, 0.3568938970565796)", "(0.33755481243133545, 0.4169931709766388)", "(0.33686742186546326, 0.3463802933692932)", "(0.33604103326797485, 0.34407564997673035)", "(0.334993839263916, 0.33592382073402405)", "(0.3322736322879791, 0.3769189715385437)", "(0.3318377137184143, 0.3730716407299042)", "(0.3315344750881195, 0.3937021493911743)", "(0.32922178506851196, 0.4272178113460541)", "(0.32651305198669434, 0.42878854274749756)", "(0.32452675700187683, 0.34284549951553345)", "(0.3215622901916504, 0.40925145149230957)", "(0.3209097385406494, 0.34468644857406616)", "(0.3200034499168396, 0.424144446849823)", "(0.3189390301704407, 0.3980537950992584)", "(0.31879422068595886, 0.5080282092094421)", "(0.31570735573768616, 0.34358081221580505)", "(0.3151988983154297, 0.3205784559249878)", "(0.31462255120277405, 0.3884718120098114)", "(0.314486026763916, 0.386690229177475)", "(0.3121269941329956, 0.36775973439216614)", "(0.3097468614578247, 0.4110228419303894)", "(0.30797311663627625, 0.3655473291873932)", "(0.30755165219306946, 0.3744462728500366)", "(0.30671948194503784, 0.4696343243122101)", "(0.30497804284095764, 0.3273793160915375)", "(0.3041970729827881, 0.43400993943214417)", "(0.3038255274295807, 0.3516017496585846)", "(0.3012024462223053, 0.36276331543922424)", "(0.3009306490421295, 0.42558857798576355)", "(0.3005829155445099, 0.32063451409339905)", "(0.29954591393470764, 0.42473888397216797)", "(0.29941093921661377, 0.36376532912254333)", "(0.29854869842529297, 0.4244839549064636)", "(0.2985389828681946, 0.3147631287574768)", "(0.29817503690719604, 0.35267844796180725)", "(0.2977418303489685, 0.34592145681381226)", "(0.2961139678955078, 0.33776620030403137)", "(0.29598483443260193, 0.3237927556037903)", "(0.2948088049888611, 0.43147388100624084)", "(0.29410451650619507, 0.3605020344257355)", "(0.2927302420139313, 0.376042902469635)", "(0.29227787256240845, 0.48548629879951477)", "(0.29183414578437805, 0.3217354118824005)", "(0.28797662258148193, 0.3087678849697113)", "(0.2878251075744629, 0.4781864583492279)", "(0.2877430021762848, 0.31531450152397156)", "(0.2848837673664093, 0.4281824231147766)", "(0.284723699092865, 0.37731432914733887)", "(0.28459542989730835, 0.49998944997787476)", "(0.28342896699905396, 0.34026429057121277)", "(0.2819497585296631, 0.366848349571228)", "(0.2810092568397522, 0.374653160572052)", "(0.28071779012680054, 0.38746434450149536)", "(0.2788403332233429, 0.2917792499065399)", "(0.2776511013507843, 0.2922899127006531)", "(0.27681562304496765, 0.4997853934764862)", "(0.2746533751487732, 0.32079240679740906)", "(0.2743299603462219, 0.49827247858047485)", "(0.27422764897346497, 0.4874180853366852)", "(0.2738967537879944, 0.30924147367477417)", "(0.2734629213809967, 0.45224204659461975)", "(0.2734296917915344, 0.2925124764442444)", "(0.2731078863143921, 0.5268570184707642)", "(0.2713245451450348, 0.3943535089492798)", "(0.2710236608982086, 0.3084299862384796)", "(0.27067115902900696, 0.42863398790359497)", "(0.2694754898548126, 0.28549957275390625)", "(0.2689670920372009, 0.2845906913280487)", "(0.2682531177997589, 0.3116636276245117)", "(0.2678254246711731, 0.29792600870132446)", "(0.2671755850315094, 0.3995225131511688)", "(0.26606273651123047, 0.3064042627811432)", "(0.2638966143131256, 0.279345840215683)", "(0.26210296154022217, 0.2641526162624359)", "(0.26164641976356506, 0.30515483021736145)", "(0.261364221572876, 0.266035795211792)", "(0.26098018884658813, 0.3039052188396454)", "(0.25950247049331665, 0.27273717522621155)", "(0.2588420808315277, 0.2902478575706482)", "(0.25870418548583984, 0.2882777452468872)", "(0.25825387239456177, 0.26813367009162903)", "(0.25790640711784363, 0.3054094612598419)", "(0.25712117552757263, 0.2705063819885254)", "(0.25662142038345337, 0.2940005958080292)", "(0.2541598677635193, 0.3111809492111206)", "(0.24963246285915375, 0.2668808698654175)", "(0.2488253116607666, 0.285357266664505)", "(0.24769316613674164, 0.2518177330493927)", "(0.24716012179851532, 0.36103078722953796)", "(0.24636942148208618, 0.3078741729259491)", "(0.24606047570705414, 0.29699936509132385)", "(0.2412470430135727, 0.25016599893569946)", "(0.24012961983680725, 0.272428035736084)", "(0.23834733664989471, 0.28982290625572205)", "(0.23802438378334045, 0.2507866621017456)", "(0.23606432974338531, 0.24459421634674072)", "(0.23302355408668518, 0.28624600172042847)", "(0.2292092740535736, 0.2818244993686676)", "(0.2275477647781372, 0.29291242361068726)", "(0.2271127849817276, 0.2922899127006531)", "(0.22656020522117615, 0.2298925817012787)", "(0.2262042909860611, 0.25815629959106445)", "(0.225302591919899, 0.2302870750427246)", "(0.22350001335144043, 0.22415533661842346)", "(0.22234392166137695, 0.2269907295703888)", "(0.2216910570859909, 0.24900023639202118)", "(0.22060242295265198, 0.22542718052864075)", "(0.21970781683921814, 0.24135281145572662)", "(0.2189723551273346, 0.2268468737602234)", "(0.21866239607334137, 0.2668575048446655)", "(0.21470434963703156, 0.2565750181674957)", "(0.21259981393814087, 0.2668575048446655)", "(0.21190251410007477, 0.2315348982810974)", "(0.21035078167915344, 0.24379923939704895)", "(0.20423614978790283, 0.24732047319412231)", "(0.20282840728759766, 0.24410755932331085)", "(0.20209096372127533, 0.21828673779964447)", "(0.19604988396167755, 0.20001308619976044)", "(0.19506923854351044, 0.20356710255146027)", "(0.18965071439743042, 0.19033190608024597)", "(0.18742063641548157, 0.21511472761631012)", "(0.1834898293018341, 0.18650075793266296)", "(0.1823257952928543, 0.22919948399066925)", "(0.17991672456264496, 0.1827383041381836)", "(0.17893296480178833, 0.20099085569381714)", "(0.16464722156524658, 0.17793580889701843)", "(0.16367998719215393, 0.2062191218137741)", "(0.15927426517009735, 0.18042431771755219)", "(0.1588282436132431, 0.15927982330322266)", "(0.15751603245735168, 0.198969304561615)", "(0.15748517215251923, 0.20387081801891327)", "(0.1555960327386856, 0.2048788070678711)", "(0.15437336266040802, 0.17174068093299866)"], "mode": "markers", "name": "H1", "type": "scatter", "x": {"bdata": "AAAA4FYy2j8AAAAAdALZPwAAACCUgtg/AAAAIGkZ2D8AAABgsOTXPwAAAKCHgdc/AAAAIMA51z8AAAAgcDfXPwAAAODpJ9c/AAAAALAi1z8AAABgOADXPwAAAOBH79Y/AAAAwGrc1j8AAAAAa6vWPwAAAMAzZtY/AAAAwFE81j8AAAAABB3WPwAAAOBKHNY/AAAA4MLi1T8AAACgJLXVPwAAAIDLo9U/AAAAgH+a1T8AAABgPI/VPwAAAECygdU/AAAAAIpw1T8AAACg+EPVPwAAAEDUPNU/AAAAYNw31T8AAABA+BHVPwAAAACX5dQ/AAAA4AvF1D8AAAAAepTUPwAAAADJidQ/AAAAwO961D8AAABAf2nUPwAAAOAfZ9Q/AAAAoIw01D8AAAAAOCzUPwAAAKDGItQ/AAAAAIog1D8AAACA4/nTPwAAAIDk0tM/AAAA4NS10z8AAAAg7a7TPwAAAMBKodM/AAAAoMKE0z8AAAAA93fTPwAAAKDgcdM/AAAAoOZG0z8AAACgckLTPwAAACDAPNM/AAAAoMIr0z8AAACAjCnTPwAAAABsG9M/AAAAQEMb0z8AAADATBXTPwAAAMAzDtM/AAAAAIjz0j8AAABgavHSPwAAAMAl3tI/AAAAwJvS0j8AAACgF7zSPwAAAECutNI/AAAAIGmt0j8AAACANW7SPwAAAAC6a9I/AAAAoGFq0j8AAAAgiTvSPwAAAMDpONI/AAAAwM820j8AAABAsyPSPwAAAAB3C9I/AAAAQA780T8AAADAR/fRPwAAACCF2NE/AAAAIAnF0T8AAADgWLfRPwAAAMDrk9E/AAAAQJ+O0T8AAAAg8ozRPwAAAECGh9E/AAAAoGqA0T8AAABA33/RPwAAAICZetE/AAAAoGFd0T8AAACgc1jRPwAAACCtUtE/AAAAIBY/0T8AAADAwTbRPwAAACAPK9E/AAAAQA0k0T8AAACgZxnRPwAAAAAsB9E/AAAAoK7j0D8AAACAS8bQPwAAAKDQvtA/AAAAADG60D8AAABA5rPQPwAAAECwm9A/AAAAYN6Q0D8AAAAAnI7QPwAAAEA7h9A/AAAA4ImB0D8AAABgrHTQPwAAAEB8bNA/AAAAwCdE0D8AAADg9PPPPwAAAACC2c8/AAAA4Gi0zz8AAABg8aLPPwAAAIAIic8/AAAA4Oh+zz8AAADgLuHOPwAAAECRvM4/AAAAYCqCzj8AAABAlXfOPwAAACBbN84/AAAAQLfTzT8AAADAulbNPwAAAABJIM0/AAAAIAgSzT8AAADA7P/MPwAAACBD9Mw/AAAAILfWzD8AAAAAppvMPwAAAADEdcw/AAAAYF9gzD8AAABAszzMPwAAAMBiH8w/AAAAQEkHzD8AAAAgIf3LPwAAAKBue8s/AAAAgHg2yz8AAAAgnx/LPwAAAEDG7Mo/AAAAAGkkyj8AAAAASPbJPwAAAOAd3sk/AAAAoCkYyT8AAABgB/jIPwAAAIB5Rsg/AAAAQGb9xz8AAABAmHzHPwAAAKBzVsc/AAAA4IIHxz8AAACARufGPwAAAAApE8U/AAAAQHfzxD8AAABgGWPEPwAAAOB7VMQ/AAAAQHwpxD8AAABgeSjEPwAAACCS6sM/AAAAoIHCwz8=", "dtype": "f8"}, "y": {"bdata": "AAAAQB002j8AAACgysPbPwAAAICaNt4/AAAAoIvA3z8AAAAgryfaPwAAACAuq9o/AAAA4JW01z8AAACgddvXPwAAAEDntdg/AAAAQBc72D8AAABgGrDaPwAAACAe1d0/AAAAwHAf2D8AAABA6KDYPwAAACA6gtk/AAAAwFqm1j8AAABAT1DXPwAAAOAvANg/AAAAAOqf1z8AAADgqYXWPwAAAIBZ19Y/AAAAIASw2j8AAABAGCvWPwAAAOBVBdY/AAAAoMZ/1T8AAADAcB/YPwAAAOBn4Nc/AAAAgGoy2T8AAABgiVfbPwAAAIBFcds/AAAAQC7x1T8AAAAALTHaPwAAAMBXD9Y/AAAAwC4l2z8AAACgtnnZPwAAAGDEQeA/AAAAYDr91T8AAACAW4TUPwAAAOC43Ng/AAAAYIi/2D8AAAAgYInXPwAAAMAyTto/AAAAoCBl1z8AAACA7fbXPwAAACB9Dt4/AAAAYMjz1D8AAACg0cbbPwAAAKCkgNY/AAAAoIM31z8AAADg1zzbPwAAAKBGhdQ/AAAAAOwu2z8AAABg7kfXPwAAAMC+Kts/AAAAQBQl1D8AAACgSJLWPwAAAMCTI9Y/AAAAIPad1T8AAABABbnUPwAAAKBEnds/AAAAIHcS1z8AAABAFhHYPwAAACA1Et8/AAAAIFCX1D8AAABg2sLTPwAAAGCbmt4/AAAA4Bwu1D8AAABAV2fbPwAAAADrJdg/AAAAwNP/3z8AAADg48bVPwAAAIBxetc/AAAAQFH61z8AAABAN8zYPwAAAOCCrNI/AAAAwOC00j8AAADge/zfPwAAAODch9Q/AAAAQLLj3z8AAACg2zHfPwAAAMCcytM/AAAAoIjx3D8AAABAhrjSPwAAAEAD3OA/AAAAgBY92T8AAAAgUb3TPwAAAEC9bts/AAAAAKBF0j8AAADguzbSPwAAAABM8tM/AAAAQDgR0z8AAADgxpHZPwAAAKAgnNM/AAAAYM3g0T8AAABg4OfQPwAAACCoh9M/AAAAALsG0T8AAADgLnPTPwAAAKCGdNE/AAAAwGuT0j8AAACAJHPSPwAAACAaKdE/AAAAINSL0z8AAAAA+k/RPwAAAODn0NI/AAAAgGPq0z8AAACAkxTRPwAAACBLQ9I/AAAAIMgd0D8AAADgIBvXPwAAAOA1tNM/AAAAoAkC0z8AAABAuALQPwAAAAB2b9E/AAAAYHWM0j8AAACA4wzQPwAAAADdTs8/AAAAwNpR0j8AAACgaQnSPwAAAMATv9I/AAAAwOC00j8AAADAHm3NPwAAAACihdA/AAAAAAx6zT8AAABAH7HMPwAAAEAIDs0/AAAAYD3fzz8AAABAzNrMPwAAACCm5M4/AAAAgFEJzT8AAACAMRTRPwAAAKC5a9A/AAAAgDEU0T8AAACA76LNPwAAAEDQNM8/AAAAgDKozz8AAACg6j7PPwAAAODR8Ms/AAAAYAeayT8AAACgfA7KPwAAAMDLXMg/AAAAIOGIyz8AAADAQd/HPwAAAKBoVs0/AAAAAPhjxz8AAACAEbrJPwAAAMCZxsY/AAAAYGNlyj8AAADgJBjHPwAAAABIY8Q/AAAAgNN3yT8AAABgcBjKPwAAAAB4Oco/AAAAQJn7xT8=", "dtype": "f8"}}, {"hoverinfo": "text", "hovertext": ["(0.5914751291275024, 1.275834321975708)", "(0.5156301856040955, 0.5259906053543091)", "(0.49635276198387146, 0.5059112906455994)", "(0.4743731915950775, 0.517972469329834)", "(0.4469393193721771, 0.45108163356781006)", "(0.3804836869239807, 0.3832988142967224)", "(0.36293530464172363, 0.3757505714893341)", "(0.34538716077804565, 0.36464396119117737)", "(0.3262971043586731, 0.332173615694046)", "(0.3175906240940094, 0.34551185369491577)", "(0.31380075216293335, 0.31555330753326416)", "(0.29979947209358215, 0.30086004734039307)", "(0.2884212136268616, 0.3085225820541382)", "(0.279345840215683, 0.2815909683704376)", "(0.26090314984321594, 0.2708180248737335)", "(0.253129780292511, 0.2585084140300751)"], "mode": "markers", "name": "H2", "type": "scatter", "x": {"bdata": "AAAAQF3t4j8AAADgCoDgPwAAAGA+xN8/AAAAYCFc3j8AAABgp5rcPwAAAEDYWdg/AAAAAFU61z8AAADA0hrWPwAAAEAN4tQ/AAAAoGdT1D8AAADATxXUPwAAACDqL9M/AAAAQH510j8AAABgzeDRPwAAACCjstA/AAAAQEcz0D8=", "dtype": "f8"}, "y": {"bdata": "AAAAQNFp9D8AAABA6tTgPwAAAOBsMOA/AAAAADuT4D8AAACAhd7cPwAAAMD3h9g/AAAAIEwM2D8AAACgU1bXPwAAACBVQtU/AAAAwN0c1j8AAACABjLUPwAAAIBKQdM/AAAAgNW+0z8AAAAglgXSPwAAACAVVdE/AAAA4GaL0D8=", "dtype": "f8"}}], "layout": {"height": 500, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 500, "xaxis": {"autorange": false, "exponentformat": "e", "linecolor": "black", "linewidth": 1, "mirror": false, "range": [-0.02551668643951416, 1.3013510084152222], "showexponent": "all", "showline": true, "side": "bottom", "ticks": "outside", "title": {"text": "Birth"}, "type": "linear", "zeroline": true}, "yaxis": {"autorange": false, "exponentformat": "e", "linecolor": "black", "linewidth": 1, "mirror": false, "range": [-0.02551668643951416, 1.3013510084152222], "scaleanchor": "x", "scaleratio": 1, "showexponent": "all", "showline": true, "side": "left", "ticks": "outside", "title": {"text": "Death"}, "type": "linear", "zeroline": true}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Sphere\n", "plot_diagram(diagrams_basic[10])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hoverinfo": "none", "line": {"color": "black", "dash": "dash", "width": 1}, "mode": "lines", "showlegend": false, "type": "scatter", "x": [-0.03421533346176148, 1.7449820065498352], "y": [-0.03421533346176148, 1.7449820065498352]}, {"hoverinfo": "text", "hovertext": ["(0.0, 0.05279902368783951)", "(0.0, 0.07644922286272049)", "(0.0, 0.08457716554403305)", "(0.0, 0.09880837798118591)", "(0.0, 0.10115370899438858)", "(0.0, 0.10750050097703934)", "(0.0, 0.11098519712686539)", "(0.0, 0.11264052242040634)", "(0.0, 0.11600442975759506)", "(0.0, 0.1184571236371994)", "(0.0, 0.12587209045886993)", "(0.0, 0.12675505876541138)", "(0.0, 0.1309504210948944)", "(0.0, 0.13212329149246216)", "(0.0, 0.1347953826189041)", "(0.0, 0.1364476978778839)", "(0.0, 0.1375502049922943)", "(0.0, 0.13772359490394592)", "(0.0, 0.1377856582403183)", "(0.0, 0.14264437556266785)", "(0.0, 0.1455816775560379)", "(0.0, 0.14561234414577484)", "(0.0, 0.14991380274295807)", "(0.0, 0.15072016417980194)", "(0.0, 0.15257471799850464)", "(0.0, 0.15279027819633484)", "(0.0, 0.15579287707805634)", "(0.0, 0.1601802110671997)", "(0.0, 0.16423490643501282)", "(0.0, 0.16429200768470764)", "(0.0, 0.16432693600654602)", "(0.0, 0.16634319722652435)", "(0.0, 0.1663789451122284)", "(0.0, 0.16735728085041046)", "(0.0, 0.16783089935779572)", "(0.0, 0.1679607629776001)", "(0.0, 0.17360761761665344)", "(0.0, 0.17445766925811768)", "(0.0, 0.17485904693603516)", "(0.0, 0.17648422718048096)", "(0.0, 0.17739878594875336)", "(0.0, 0.1784851849079132)", "(0.0, 0.18175522983074188)", "(0.0, 0.18294687569141388)", "(0.0, 0.1835223138332367)", "(0.0, 0.1860504299402237)", "(0.0, 0.18799522519111633)", "(0.0, 0.18875032663345337)", "(0.0, 0.1909380406141281)", "(0.0, 0.19486398994922638)", "(0.0, 0.20230235159397125)", "(0.0, 0.20381489396095276)", "(0.0, 0.20468337833881378)", "(0.0, 0.20483006536960602)", "(0.0, 0.20933295786380768)", "(0.0, 0.21266022324562073)", "(0.0, 0.213112011551857)", "(0.0, 0.21623742580413818)", "(0.0, 0.2178603708744049)", "(0.0, 0.2199334055185318)", "(0.0, 0.22086721658706665)", "(0.0, 0.22105766832828522)", "(0.0, 0.22283172607421875)", "(0.0, 0.2237122654914856)", "(0.0, 0.2241290807723999)", "(0.0, 0.2248246669769287)", "(0.0, 0.2250627875328064)", "(0.0, 0.2251855581998825)", "(0.0, 0.22694221138954163)", "(0.0, 0.22762112319469452)", "(0.0, 0.22912129759788513)", "(0.0, 0.23052603006362915)", "(0.0, 0.23054735362529755)", "(0.0, 0.23181137442588806)", "(0.0, 0.232948899269104)", "(0.0, 0.2355463057756424)", "(0.0, 0.235980823636055)", "(0.0, 0.23603886365890503)", "(0.0, 0.2365807145833969)", "(0.0, 0.2392069697380066)", "(0.0, 0.23933599889278412)", "(0.0, 0.24103428423404694)", "(0.0, 0.24349941313266754)", "(0.0, 0.2435433268547058)", "(0.0, 0.24595749378204346)", "(0.0, 0.24721713364124298)", "(0.0, 0.24728699028491974)", "(0.0, 0.24822992086410522)", "(0.0, 0.24900613725185394)", "(0.0, 0.24904799461364746)", "(0.0, 0.24971246719360352)", "(0.0, 0.2503529191017151)", "(0.0, 0.25069138407707214)", "(0.0, 0.2518141269683838)", "(0.0, 0.25199076533317566)", "(0.0, 0.25255778431892395)", "(0.0, 0.2531742453575134)", "(0.0, 0.2535524368286133)", "(0.0, 0.2550327181816101)", "(0.0, 0.2551785707473755)", "(0.0, 0.25624433159828186)", "(0.0, 0.25686174631118774)", "(0.0, 0.2574712038040161)", "(0.0, 0.2587069272994995)", "(0.0, 0.2592521607875824)", "(0.0, 0.25944894552230835)", "(0.0, 0.2606731355190277)", "(0.0, 0.2627972960472107)", "(0.0, 0.26366207003593445)", "(0.0, 0.2650776505470276)", "(0.0, 0.26641327142715454)", "(0.0, 0.26661416888237)", "(0.0, 0.2671639621257782)", "(0.0, 0.2688523530960083)", "(0.0, 0.2694632411003113)", "(0.0, 0.27047476172447205)", "(0.0, 0.2709202170372009)", "(0.0, 0.2722237706184387)", "(0.0, 0.2731560468673706)", "(0.0, 0.2736165523529053)", "(0.0, 0.2743082642555237)", "(0.0, 0.27455228567123413)", "(0.0, 0.2746943235397339)", "(0.0, 0.2751176059246063)", "(0.0, 0.2759435176849365)", "(0.0, 0.27729108929634094)", "(0.0, 0.27995237708091736)", "(0.0, 0.28329938650131226)", "(0.0, 0.28420332074165344)", "(0.0, 0.2852270007133484)", "(0.0, 0.2895078957080841)", "(0.0, 0.2907457649707794)", "(0.0, 0.29088884592056274)", "(0.0, 0.2922855317592621)", "(0.0, 0.2939661741256714)", "(0.0, 0.2944338917732239)", "(0.0, 0.2947002649307251)", "(0.0, 0.2949916422367096)", "(0.0, 0.29575684666633606)", "(0.0, 0.2961517870426178)", "(0.0, 0.2971493601799011)", "(0.0, 0.29772186279296875)", "(0.0, 0.29846441745758057)", "(0.0, 0.2989664077758789)", "(0.0, 0.29928484559059143)", "(0.0, 0.3000231087207794)", "(0.0, 0.30082789063453674)", "(0.0, 0.30185651779174805)", "(0.0, 0.30318471789360046)", "(0.0, 0.3035561442375183)", "(0.0, 0.30448615550994873)", "(0.0, 0.3046984374523163)", "(0.0, 0.30483320355415344)", "(0.0, 0.3067575693130493)", "(0.0, 0.3075353801250458)", "(0.0, 0.3084324896335602)", "(0.0, 0.30845311284065247)", "(0.0, 0.3101870119571686)", "(0.0, 0.31043773889541626)", "(0.0, 0.3114679455757141)", "(0.0, 0.31174248456954956)", "(0.0, 0.31177160143852234)", "(0.0, 0.3128125071525574)", "(0.0, 0.31363511085510254)", "(0.0, 0.31367725133895874)", "(0.0, 0.3139689862728119)", "(0.0, 0.3142406940460205)", "(0.0, 0.3149275779724121)", "(0.0, 0.31554678082466125)", "(0.0, 0.3169180154800415)", "(0.0, 0.3200583755970001)", "(0.0, 0.3223497271537781)", "(0.0, 0.32237470149993896)", "(0.0, 0.3230229318141937)", "(0.0, 0.3231058120727539)", "(0.0, 0.32320472598075867)", "(0.0, 0.3240647315979004)", "(0.0, 0.3249731659889221)", "(0.0, 0.3253367841243744)", "(0.0, 0.3262324333190918)", "(0.0, 0.3279157280921936)", "(0.0, 0.3287995457649231)", "(0.0, 0.32898420095443726)", "(0.0, 0.32960349321365356)", "(0.0, 0.3299713730812073)", "(0.0, 0.33085665106773376)", "(0.0, 0.3312009871006012)", "(0.0, 0.33518919348716736)", "(0.0, 0.3355499804019928)", "(0.0, 0.3359343111515045)", "(0.0, 0.3359420895576477)", "(0.0, 0.33609533309936523)", "(0.0, 0.3403759002685547)", "(0.0, 0.3407193422317505)", "(0.0, 0.3425990045070648)", "(0.0, 0.3437194526195526)", "(0.0, 0.3448050320148468)", "(0.0, 0.34672045707702637)", "(0.0, 0.3482095003128052)", "(0.0, 0.3486398756504059)", "(0.0, 0.34869521856307983)", "(0.0, 0.34976083040237427)", "(0.0, 0.3519587218761444)", "(0.0, 0.35271087288856506)", "(0.0, 0.3529551923274994)", "(0.0, 0.35323190689086914)", "(0.0, 0.353953093290329)", "(0.0, 0.35423144698143005)", "(0.0, 0.35641083121299744)", "(0.0, 0.3568316698074341)", "(0.0, 0.3571716248989105)", "(0.0, 0.35722246766090393)", "(0.0, 0.35723191499710083)", "(0.0, 0.35733869671821594)", "(0.0, 0.35754722356796265)", "(0.0, 0.3579021692276001)", "(0.0, 0.35937488079071045)", "(0.0, 0.3596899211406708)", "(0.0, 0.3599863350391388)", "(0.0, 0.360840767621994)", "(0.0, 0.36114031076431274)", "(0.0, 0.3617323040962219)", "(0.0, 0.36209702491760254)", "(0.0, 0.3634846806526184)", "(0.0, 0.3652915060520172)", "(0.0, 0.36549386382102966)", "(0.0, 0.36614641547203064)", "(0.0, 0.3663873076438904)", "(0.0, 0.3667066991329193)", "(0.0, 0.3692426085472107)", "(0.0, 0.370295912027359)", "(0.0, 0.3703102767467499)", "(0.0, 0.37046411633491516)", "(0.0, 0.37231793999671936)", "(0.0, 0.374450147151947)", "(0.0, 0.3748633861541748)", "(0.0, 0.3751668632030487)", "(0.0, 0.37594902515411377)", "(0.0, 0.37596070766448975)", "(0.0, 0.37687820196151733)", "(0.0, 0.3769703209400177)", "(0.0, 0.3776925206184387)", "(0.0, 0.378831684589386)", "(0.0, 0.3790507912635803)", "(0.0, 0.37950369715690613)", "(0.0, 0.3800501227378845)", "(0.0, 0.3817523419857025)", "(0.0, 0.3818083107471466)", "(0.0, 0.38215014338493347)", "(0.0, 0.3825959265232086)", "(0.0, 0.38402891159057617)", "(0.0, 0.38466763496398926)", "(0.0, 0.3846960961818695)", "(0.0, 0.38520383834838867)", "(0.0, 0.3852900564670563)", "(0.0, 0.3853444755077362)", "(0.0, 0.3859785497188568)", "(0.0, 0.38618651032447815)", "(0.0, 0.38682132959365845)", "(0.0, 0.3879738450050354)", "(0.0, 0.3895525336265564)", "(0.0, 0.38981014490127563)", "(0.0, 0.3916205167770386)", "(0.0, 0.3920069932937622)", "(0.0, 0.39261946082115173)", "(0.0, 0.3952826261520386)", "(0.0, 0.3954220414161682)", "(0.0, 0.3958035111427307)", "(0.0, 0.39670032262802124)", "(0.0, 0.3979417383670807)", "(0.0, 0.39867186546325684)", "(0.0, 0.3994971215724945)", "(0.0, 0.3999686539173126)", "(0.0, 0.4000636041164398)", "(0.0, 0.4007628560066223)", "(0.0, 0.4008364677429199)", "(0.0, 0.4013388454914093)", "(0.0, 0.4021221995353699)", "(0.0, 0.40465012192726135)", "(0.0, 0.40564537048339844)", "(0.0, 0.40628212690353394)", "(0.0, 0.4070538580417633)", "(0.0, 0.40751132369041443)", "(0.0, 0.40751761198043823)", "(0.0, 0.4079320728778839)", "(0.0, 0.40853452682495117)", "(0.0, 0.4090249836444855)", "(0.0, 0.41089943051338196)", "(0.0, 0.41411325335502625)", "(0.0, 0.41421690583229065)", "(0.0, 0.4154760539531708)", "(0.0, 0.4179871082305908)", "(0.0, 0.4185335338115692)", "(0.0, 0.41858774423599243)", "(0.0, 0.41881754994392395)", "(0.0, 0.4191267788410187)", "(0.0, 0.4200718402862549)", "(0.0, 0.42236119508743286)", "(0.0, 0.42256560921669006)", "(0.0, 0.4228525459766388)", "(0.0, 0.42433738708496094)", "(0.0, 0.4254894256591797)", "(0.0, 0.42570021748542786)", "(0.0, 0.4263574481010437)", "(0.0, 0.42663997411727905)", "(0.0, 0.4285106062889099)", "(0.0, 0.42957592010498047)", "(0.0, 0.4296903610229492)", "(0.0, 0.4317895174026489)", "(0.0, 0.4323040246963501)", "(0.0, 0.434827983379364)", "(0.0, 0.43503108620643616)", "(0.0, 0.4350685477256775)", "(0.0, 0.43612006306648254)", "(0.0, 0.43948063254356384)", "(0.0, 0.4402889311313629)", "(0.0, 0.4403891861438751)", "(0.0, 0.44380316138267517)", "(0.0, 0.4441244900226593)", "(0.0, 0.44473811984062195)", "(0.0, 0.44482436776161194)", "(0.0, 0.4448961019515991)", "(0.0, 0.44789791107177734)", "(0.0, 0.44968220591545105)", "(0.0, 0.4501465857028961)", "(0.0, 0.4504949450492859)", "(0.0, 0.4505176246166229)", "(0.0, 0.45430248975753784)", "(0.0, 0.4577072858810425)", "(0.0, 0.4582616984844208)", "(0.0, 0.4585065245628357)", "(0.0, 0.4594779312610626)", "(0.0, 0.4599040150642395)", "(0.0, 0.46169042587280273)", "(0.0, 0.4623507857322693)", "(0.0, 0.4647795855998993)", "(0.0, 0.4652152955532074)", "(0.0, 0.4658709466457367)", "(0.0, 0.46637019515037537)", "(0.0, 0.467437744140625)", "(0.0, 0.4691028892993927)", "(0.0, 0.4724135994911194)", "(0.0, 0.4733167290687561)", "(0.0, 0.47685450315475464)", "(0.0, 0.4817869961261749)", "(0.0, 0.48340684175491333)", "(0.0, 0.48477447032928467)", "(0.0, 0.48674970865249634)", "(0.0, 0.48779016733169556)", "(0.0, 0.4904191792011261)", "(0.0, 0.49394458532333374)", "(0.0, 0.4943540394306183)", "(0.0, 0.4978518784046173)", "(0.0, 0.4998150169849396)", "(0.0, 0.5011432766914368)", "(0.0, 0.5013524889945984)", "(0.0, 0.5050827860832214)", "(0.0, 0.5060660243034363)", "(0.0, 0.5062568783760071)", "(0.0, 0.5067396759986877)", "(0.0, 0.5067502856254578)", "(0.0, 0.5077885985374451)", "(0.0, 0.5080316662788391)", "(0.0, 0.5100839138031006)", "(0.0, 0.5101384520530701)", "(0.0, 0.5114667415618896)", "(0.0, 0.5116550922393799)", "(0.0, 0.5125912427902222)", "(0.0, 0.5132551193237305)", "(0.0, 0.5142974853515625)", "(0.0, 0.5191919207572937)", "(0.0, 0.5214583873748779)", "(0.0, 0.522706151008606)", "(0.0, 0.5252507925033569)", "(0.0, 0.5265967845916748)", "(0.0, 0.5290037393569946)", "(0.0, 0.5331819653511047)", "(0.0, 0.5442649722099304)", "(0.0, 0.5463943481445312)", "(0.0, 0.551291286945343)", "(0.0, 0.5529220104217529)", "(0.0, 0.5540581345558167)", "(0.0, 0.5568457841873169)", "(0.0, 0.5584785342216492)", "(0.0, 0.5642218589782715)", "(0.0, 0.5644572973251343)", "(0.0, 0.5656794905662537)", "(0.0, 0.5709843039512634)", "(0.0, 0.5735125541687012)", "(0.0, 0.5771370530128479)", "(0.0, 0.5782506465911865)", "(0.0, 0.582745373249054)", "(0.0, 0.582861065864563)", "(0.0, 0.6074760556221008)", "(0.0, 0.6167699098587036)", "(0.0, 0.6249123811721802)", "(0.0, 0.6544438600540161)", "(0.0, 0.6675966382026672)", "(0.0, 0.669188380241394)"], "mode": "markers", "name": "H0", "type": "scatter", "x": {"bdata": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "dtype": "f8"}, "y": {"bdata": "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", "dtype": "f8"}}, {"hoverinfo": "text", "hovertext": ["(1.27296781539917, 1.2943605184555054)", "(1.0723545551300049, 1.0815215110778809)", "(1.0629185438156128, 1.134692907333374)", "(0.9548443555831909, 1.0643492937088013)", "(0.8944393396377563, 1.0358182191848755)", "(0.8580814003944397, 1.0302892923355103)", "(0.8400965929031372, 1.1081167459487915)", "(0.8257724642753601, 0.9119561910629272)", "(0.8042849898338318, 0.9430632591247559)", "(0.8025068640708923, 0.8826267719268799)", "(0.7850164771080017, 0.8014243245124817)", "(0.7808776497840881, 0.8211212754249573)", "(0.7793661952018738, 0.8614873290061951)", "(0.7705633044242859, 0.8438242673873901)", "(0.7682592868804932, 1.1146031618118286)", "(0.7636735439300537, 0.8110827803611755)", "(0.7627946138381958, 0.7841975092887878)", "(0.7619931697845459, 0.849540114402771)", "(0.7583670020103455, 0.7941729426383972)", "(0.7553260326385498, 0.8984114527702332)", "(0.7533589601516724, 0.9475491642951965)", "(0.7498665452003479, 0.8292903304100037)", "(0.7494407296180725, 0.8986906409263611)", "(0.7493764162063599, 0.7640904188156128)", "(0.7362769246101379, 0.9723108410835266)", "(0.7362573742866516, 0.7842551469802856)", "(0.735046923160553, 0.7667003870010376)", "(0.734015941619873, 0.8036472797393799)", "(0.7248587012290955, 0.7524430751800537)", "(0.7211064696311951, 0.9507767558097839)", "(0.7175022959709167, 0.9490920305252075)", "(0.7164784073829651, 0.9538277983665466)", "(0.7162042856216431, 0.7643997669219971)", "(0.7141921520233154, 0.7791951894760132)", "(0.71397465467453, 0.7560427188873291)", "(0.7093854546546936, 0.8692880868911743)", "(0.7078983783721924, 0.7362502813339233)", "(0.706623911857605, 0.7665752172470093)", "(0.7053007483482361, 0.8517529368400574)", "(0.7023000121116638, 0.8439575433731079)", "(0.7004956603050232, 1.0682570934295654)", "(0.6936165690422058, 0.8491518497467041)", "(0.6880965828895569, 0.7152233123779297)", "(0.6872125267982483, 0.9065274596214294)", "(0.6870605945587158, 0.7365856766700745)", "(0.683204174041748, 0.7804729342460632)", "(0.6795910596847534, 0.6816402077674866)", "(0.6791473627090454, 0.7348583936691284)", "(0.6777933835983276, 1.1542444229125977)", "(0.6710798740386963, 0.9259032011032104)", "(0.6676198840141296, 0.7835780382156372)", "(0.6661140322685242, 0.8486100435256958)", "(0.6637097597122192, 0.7736252546310425)", "(0.6631413698196411, 0.6886531114578247)", "(0.6473133563995361, 0.6660614609718323)", "(0.6471129655838013, 1.089508056640625)", "(0.646333634853363, 0.6720251441001892)", "(0.6460177898406982, 0.7325382232666016)", "(0.6447422504425049, 0.6814063191413879)", "(0.6423043608665466, 0.657995343208313)", "(0.6406659483909607, 0.6585080623626709)", "(0.6289052367210388, 0.7522477507591248)", "(0.6288715600967407, 0.6353156566619873)", "(0.6261193752288818, 0.8526010513305664)", "(0.6228402256965637, 0.7469189167022705)", "(0.6212131381034851, 0.8332523703575134)", "(0.6162766814231873, 0.713300347328186)", "(0.6144985556602478, 0.7046969532966614)", "(0.6127641797065735, 0.7758352756500244)", "(0.6081743836402893, 0.6925637125968933)", "(0.6080910563468933, 0.6514901518821716)", "(0.6060186624526978, 0.61148601770401)", "(0.5971608757972717, 0.6479186415672302)", "(0.5956575870513916, 0.6864045262336731)", "(0.5881749391555786, 0.5908814072608948)", "(0.5864530205726624, 0.5881356596946716)", "(0.585283637046814, 1.2099602222442627)", "(0.584902286529541, 0.660759449005127)", "(0.580934464931488, 0.6338878870010376)", "(0.5808920860290527, 0.6969886422157288)", "(0.5790859460830688, 0.77625972032547)", "(0.5778791308403015, 0.6251271963119507)", "(0.5775319337844849, 0.8489511013031006)", "(0.5765342712402344, 1.0133072137832642)", "(0.5754053592681885, 0.780533492565155)", "(0.5753580331802368, 0.589640736579895)", "(0.5753027200698853, 1.1162426471710205)", "(0.574821412563324, 0.6892139315605164)", "(0.5738446116447449, 0.7878193855285645)", "(0.5660274624824524, 0.654684841632843)", "(0.5641191601753235, 0.6108936667442322)", "(0.5582539439201355, 0.770722508430481)", "(0.5570322275161743, 0.6244540810585022)", "(0.5569689869880676, 0.5774365663528442)", "(0.5534232258796692, 0.8041425347328186)", "(0.5478278994560242, 0.6509455442428589)", "(0.5446637272834778, 1.2105908393859863)", "(0.5415908694267273, 1.0473911762237549)", "(0.535772442817688, 0.5791604518890381)", "(0.5321086049079895, 0.6925421953201294)", "(0.5283817052841187, 0.6086333394050598)", "(0.5277361869812012, 0.5443817973136902)", "(0.5253564715385437, 0.7168360352516174)", "(0.5249626040458679, 0.5871845483779907)", "(0.5244683027267456, 0.5461137294769287)", "(0.5233286023139954, 0.5831258893013)", "(0.5216326713562012, 0.5971288084983826)", "(0.5209904909133911, 0.5230473279953003)", "(0.5199660658836365, 0.5621531009674072)", "(0.5181984901428223, 0.56954425573349)", "(0.5150274038314819, 0.6555582880973816)", "(0.5139774680137634, 0.5657111406326294)", "(0.5130816698074341, 0.8280240297317505)", "(0.507422685623169, 0.5144800543785095)", "(0.5043633580207825, 0.5399295091629028)", "(0.5023165345191956, 0.5703901648521423)", "(0.5023128390312195, 0.6825911402702332)", "(0.5019271373748779, 0.5164299011230469)", "(0.48426756262779236, 0.522894024848938)", "(0.47810491919517517, 0.48008018732070923)", "(0.4753201901912689, 1.5425246953964233)", "(0.471629798412323, 0.5432391166687012)", "(0.46801427006721497, 0.5249553322792053)", "(0.46603924036026, 0.5440280437469482)", "(0.4612556993961334, 1.4323123693466187)", "(0.46110016107559204, 0.5316460132598877)", "(0.4598829448223114, 0.6149746775627136)", "(0.4455551505088806, 0.4472957253456116)", "(0.4428369998931885, 0.5550373196601868)", "(0.4419107437133789, 0.5971474051475525)", "(0.43721893429756165, 0.7604094743728638)", "(0.4224380552768707, 0.5494737029075623)", "(0.4220702052116394, 0.4233405590057373)", "(0.41752058267593384, 0.5507180094718933)", "(0.4092416763305664, 0.5514543056488037)", "(0.40599489212036133, 0.48468369245529175)", "(0.4006582200527191, 0.4477216899394989)", "(0.3864942491054535, 0.5134772062301636)", "(0.36134469509124756, 0.3625212609767914)", "(0.3601541221141815, 0.3699987828731537)", "(0.34788697957992554, 0.4888603985309601)", "(0.3379228711128235, 0.3478315472602844)", "(0.29849180579185486, 0.31743139028549194)"], "mode": "markers", "name": "H1", "type": "scatter", "x": {"bdata": "AAAAgBNe9D8AAABAXSjxPwAAAOC2AfE/AAAAwBWO7j8AAABAP5/sPwAAACBndes/AAAAQBLi6j8AAABgumzqPwAAAOCzvOk/AAAA4CKu6T8AAADg2h7pPwAAACDz/Og/AAAAYJHw6D8AAABgdKjoPwAAAICUleg/AAAAgANw6D8AAABA0GjoPwAAAIA/Yug/AAAA4IpE6D8AAACAoSvoPwAAAECEG+g/AAAAIOj+5z8AAAAga/vnPwAAAEDk+uc/AAAAoJSP5z8AAACga4/nPwAAACCBhec/AAAAAA995z8AAADgCjLnPwAAAOBNE+c/AAAAYMf15j8AAAAgZO3mPwAAAEAl6+Y/AAAAgKna5j8AAABg4djmPwAAACBJs+Y/AAAAgBqn5j8AAADAqZzmPwAAAODSkeY/AAAA4D155j8AAADgdWrmPwAAAGAbMuY/AAAAIOME5j8AAAAgpf3lPwAAAIBm/OU/AAAAAM/c5T8AAADANb/lPwAAAECTu+U/AAAAwHuw5T8AAACAfHnlPwAAAGAkXeU/AAAAYM5Q5T8AAABAHD3lPwAAAEB0OOU/AAAAgMq25D8AAABAJrXkPwAAAODDruQ/AAAAgC2s5D8AAACAuqHkPwAAAODBjeQ/AAAA4FWA5D8AAADg/R/kPwAAAEC3H+Q/AAAAgCsJ5D8AAACgTu7jPwAAAGD64OM/AAAA4Im44z8AAADg+KnjPwAAAKDDm+M/AAAAICp24z8AAABge3XjPwAAAECBZOM/AAAAIPEb4z8AAACAoA/jPwAAAEBU0uI/AAAAIDnE4j8AAADApLriPwAAAACFt+I/AAAA4AOX4j8AAAAAq5biPwAAAEDfh+I/AAAAYPx94j8AAABAJHviPwAAAAD4cuI/AAAAgLhp4j8AAABAVWniPwAAAEDhaOI/AAAA4O9k4j8AAABg71ziPwAAAKDlHOI/AAAAoEMN4j8AAABgN93hPwAAAEA10+E/AAAAoLDS4T8AAACgpLXhPwAAAGDOh+E/AAAAoOJt4T8AAABgtlThPwAAAEAMJeE/AAAAoAgH4T8AAADAgOjgPwAAAAA34+A/AAAAYLjP4D8AAABgfszgPwAAAMBxyOA/AAAAoBu/4D8AAAAAN7HgPwAAAED0q+A/AAAA4I+j4D8AAAAAFZXgPwAAAMAae+A/AAAA4IBy4D8AAABAKmvgPwAAAIDOPOA/AAAAoL4j4D8AAAAg+hLgPwAAAGDyEuA/AAAAgMkP4D8AAABgPf7ePwAAAGBFmd4/AAAAYKVr3j8AAADALi/ePwAAACDy890/AAAAQJbT3T8AAACgNoXdPwAAAECqgt0/AAAA4Lhu3T8AAADA+YPcPwAAAABxV9w/AAAAAERI3D8AAAAgZfvbPwAAAKA5Cds/AAAAwDID2z8AAABAqLjaPwAAAAAEMdo/AAAAANL72T8AAABgYqTZPwAAAGBSvNg/AAAAgEUg1z8AAADgwwzXPwAAAMDHQ9Y/AAAAQIeg1T8AAABgfRrTPw==", "dtype": "f8"}, "y": {"bdata": "AAAAYLO19D8AAACA6U3xPwAAAMCzJ/I/AAAAIJMH8T8AAAAgtpLwPwAAAKAQfPA/AAAAoNi68T8AAADAvi7tPwAAAACTLe4/AAAAgHo+7D8AAACgRKXpPwAAACCgRuo/AAAA4E2R6z8AAADAmwDrPwAAACBq1fE/AAAA4GP06T8AAABgJRjpPwAAAMBuL+s/AAAAYN1p6T8AAABgyb/sPwAAAKBSUu4/AAAA4IuJ6j8AAADgEsLsPwAAAMBtc+g/AAAAoCsd7z8AAABAnhjpPwAAAEDPiOg/AAAAgHq36T8AAACAAxToPwAAAGDDbO4/AAAAQPZe7j8AAADgwYXuPwAAAID2deg/AAAAwCrv6D8AAACAgDHoPwAAAEA10es/AAAAwFyP5z8AAADAyIfoPwAAAGCPQes/AAAAQLMB6z8AAADAlBfxPwAAAIBALOs/AAAAABzj5j8AAADgRQLtPwAAACAckuc/AAAAYKL56D8AAAAg/8/lPwAAAMD1g+c/AAAAAMl38j8AAADA/6DtPwAAAEASE+k/AAAAQNAn6z8AAADAicHoPwAAAEByCeY/AAAAIGBQ5T8AAAAAoG7xPwAAAOA6geU/AAAAAPRw5z8AAACgFM7lPwAAAEBMDuU/AAAAgH8S5T8AAADgaRLoPwAAAICBVOQ/AAAAAIJI6z8AAACAwubnPwAAAOAAquo/AAAAQFvT5j8AAACg4IzmPwAAAICk0+g/AAAAYHsp5j8AAADgAdnkPwAAACBLkeM/AAAA4L+75D8AAACgBvflPwAAACCA6OI/AAAA4AHS4j8AAABA/1vzPwAAAADxJOU/AAAAQM9I5D8AAAAgu03mPwAAAKAe1+g/AAAAwAoB5D8AAACAmyrrPwAAAKCBNvA/AAAAYCH66D8AAABAVt7iPwAAAEAh3PE/AAAAYAoO5j8AAAAA0TXpPwAAAKAt8+Q/AAAA4HCM4z8AAABAwqnoPwAAACCH++M/AAAAQFx64j8AAAAgibvpPwAAAMCL1OQ/AAAAgJRe8z8AAABAHcLwPwAAAIB7iOI/AAAAQE4p5j8AAACg7HnjPwAAAGCTa+E/AAAAIFLw5j8AAABAN8riPwAAAIDDeeE/AAAAoPeo4j8AAADgrRvjPwAAAMDNvOA/AAAAgCj94T8AAADgtDniPwAAAGBV+uQ/AAAAQE4a4j8AAABALH/qPwAAAOCeduA/AAAAQBpH4T8AAADgokDiPwAAAGDJ1+U/AAAAAJiG4D8AAABAjLvgPwAAAECiud4/AAAAYC6u+D8AAAAAN2LhPwAAACBvzOA/AAAAgK1o4T8AAABgwOr2PwAAAIA+A+E/AAAAYN+t4z8AAABAfqDcPwAAAKDdwuE/AAAA4NQb4z8AAABARlXoPwAAAOBJleE/AAAAAAMY2z8AAABge5/hPwAAAICDpeE/AAAAwA4F3z8AAADgeKfcPwAAAMBnbuA/AAAAYIwz1z8AAABgD67XPwAAACB9Sd8/AAAAQN9C1j8AAADAy1DUPw==", "dtype": "f8"}}, {"hoverinfo": "text", "hovertext": ["(1.7049123048782349, 1.7107666730880737)", "(1.6336230039596558, 1.6656590700149536)", "(1.6238683462142944, 1.6421080827713013)", "(1.5954368114471436, 1.6169501543045044)", "(1.5865511894226074, 1.628610610961914)", "(1.582240104675293, 1.6222710609436035)", "(1.580055594444275, 1.6271158456802368)", "(1.5730602741241455, 1.5939278602600098)", "(1.5539873838424683, 1.5722734928131104)", "(1.5517927408218384, 1.6091138124465942)", "(1.547387957572937, 1.6476863622665405)", "(1.5315622091293335, 1.6129873991012573)", "(1.5301932096481323, 1.577997088432312)", "(1.5272295475006104, 1.5419927835464478)", "(1.5236103534698486, 1.5776094198226929)", "(1.5085591077804565, 1.5849119424819946)", "(1.5073144435882568, 1.6423710584640503)", "(1.4871430397033691, 1.5667665004730225)", "(1.4848061800003052, 1.6194158792495728)", "(1.457363247871399, 1.6570451259613037)", "(1.3165502548217773, 1.6950751543045044)", "(1.042771816253662, 1.0430631637573242)", "(0.9016094207763672, 0.918231189250946)", "(0.8965924978256226, 0.8999415636062622)", "(0.8438820838928223, 0.8704828023910522)", "(0.5627572536468506, 0.5687136054039001)"], "mode": "markers", "name": "H2", "type": "scatter", "x": {"bdata": "AAAAIFJH+z8AAADgUSP6PwAAAGBd+/k/AAAAwOiG+T8AAACAg2L5PwAAAADbUPk/AAAAYOhH+T8AAABAQSv5PwAAAOAh3fg/AAAAoCTU+D8AAADgGcL4PwAAAGBHgfg/AAAA4Kt7+D8AAABAiG/4PwAAAEC1YPg/AAAA4A4j+D8AAADA9R34PwAAAIBWy/c/AAAAIMTB9z8AAAAgXFH3PwAAAACXEPU/AAAAgDGv8D8AAAAA/NnsPwAAAMDisOw/AAAAABUB6z8AAACAGwLiPw==", "dtype": "f8"}, "y": {"bdata": "AAAA4Exf+z8AAAAgiqb6PwAAACATRvo/AAAAIAff+T8AAAAAyg76PwAAAIDS9Pk/AAAAoKoI+j8AAACAuoD5PwAAAEAIKPk/AAAAIO6++T8AAABg7Fz6PwAAAODLzvk/AAAA4Hk/+T8AAACgAKz4PwAAAGDjPfk/AAAAoMxb+T8AAADgJkf6PwAAAMB5Efk/AAAAoCDp+T8AAADAQYP6PwAAACAHH/s/AAAAAGOw8D8AAABgJmLtPwAAAEBSzOw/AAAAwP7a6z8AAADg5jLiPw==", "dtype": "f8"}}], "layout": {"height": 500, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 500, "xaxis": {"autorange": false, "exponentformat": "e", "linecolor": "black", "linewidth": 1, "mirror": false, "range": [-0.03421533346176148, 1.7449820065498352], "showexponent": "all", "showline": true, "side": "bottom", "ticks": "outside", "title": {"text": "Birth"}, "type": "linear", "zeroline": true}, "yaxis": {"autorange": false, "exponentformat": "e", "linecolor": "black", "linewidth": 1, "mirror": false, "range": [-0.03421533346176148, 1.7449820065498352], "scaleanchor": "x", "scaleratio": 1, "showexponent": "all", "showline": true, "side": "left", "ticks": "outside", "title": {"text": "Death"}, "type": "linear", "zeroline": true}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Torus\n", "plot_diagram(diagrams_basic[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the persistence diagrams we can see which persistent generators $H_{1,2,3}$ are most persistent for each shape. In particular, we see that:\n", "\n", "* the circle has one persistent generator $H_1$ corresponding to a loop,\n", "* the sphere has one persistent generator $H_2$ corresponding to a void,\n", "* the torus has three persistent generators, two for $H_1$ and one for $H_2$."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Although persistence diagrams are useful descriptors of the data, they cannot be used directly for machine learning applications. This is because different persistence diagrams may have different numbers of points, and basic operations like the addition and multiplication of diagrams are not well-defined.\n", "\n", "To overcome these limitations, a variety of proposals have been made to \"vectorize\" persistence diagrams via embeddings or kernels which are well-suited for machine learning. In ``giotto-tda``, we provide access to the most common vectorizations via the ``gtda.diagrams`` module.\n", "\n", "For example, one such feature is known as [persistence entropy](https://giotto-ai.github.io/gtda-docs/latest/theory/glossary.html#persistence-entropy) which measures the entropy of points in a diagram $D = \\{(b_i, d_i)\\}_{i\\in I}$ according to\n", "\n", "$$ E(D) = - \\sum_{i\\in I} p_i \\log p_i$$\n", "\n", "where $p_i =(d_i - b_i)/L_D$ and $L_D = \\sum_i (d_i - b_i)$. As we did for the persistence diagram calculation, we can use a transformer to calculate the persistent entropy associated with each homology generator $H_{0,1,2}$:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(30, 3)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from gtda.diagrams import PersistenceEntropy\n", "\n", "persistence_entropy = PersistenceEntropy()\n", "\n", "# calculate topological feature matrix\n", "X_basic = persistence_entropy.fit_transform(diagrams_basic)\n", "\n", "# expect shape - (n_point_clouds, n_homology_dims)\n", "X_basic.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Nice - we have found a way to represent each point cloud in terms of just three numbers! Note that this does not depend on the number of points in the original point cloud, although calculating the $H_2$ generators becomes time consuming for point clouds of $O(10^3)$ points (at least on a standard laptop). \n", "\n", "By visualising the feature matrix"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], "colorscale": [[0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1, "#fde725"]], "opacity": 0.8, "size": 4}, "mode": "markers", "type": "scatter3d", "x": {"bdata": "bZCjJIDlIEAQaC3alOggQF57yPZR6iBAc1jcqoX0IED8wYK31vogQJM/EUhK7yBA0no5x5vwIECcopjTj9sgQBcgSD1d5SBAJ2gaBUnjIEBYSSPXrBMhQDAJzE+PCyFAcgMXSdYMIUAVCx44sBEhQPdHXAWUCSFAPcCYT7wQIUDhlok/DgshQMWe+ce7DyFAz19KbckVIUAMrC+0KQ0hQOKIy46SHCFAzKMysxsXIUDpHiMtyxshQDQlxZlBGyFAiWIddFsaIUBVBTEtVhohQIX4KB3VFyFAOHS0LtMYIUArmqEJChwhQGCpNyq9GCFA", "dtype": "f8"}, "y": {"bdata": "Wd8BpGr4EUB1KP9ZVGMRQMSYrdSlWBFA80mFRxfcEkBfMTYcQ6MRQA8CunCWvRBAj1TD7a9aEkDCRVydbmMRQNEwFV0b6RFAit8doYJeEkCOZLW8fawaQHSmXkwBZxpAZyJYCUkVG0BmJ9OFtfUaQIL05CgN4RpAOCDO82uWGkDngCzWW/0aQNO826T1BRpARLWtzDoVG0COvny6AJoaQK+VAZ6SlhlAflXrYOTOGUBfNIEdh6oZQObVeCVo8BlAeOLmEMg8GUDBmlcyb3cZQEorpaPWoxlAMNpdk7+YGUCMRmk+jMYZQEjpGAIckhlA", "dtype": "f8"}, "z": {"bdata": "6dngBCno4T8JkGF657nzPw08nBXJ+fE/WDKFDSqg9T/VOxZcPATzP8/gzlv6n+8/FfERWA8+/j8BL9yEhK7yP9EwZryGNtI/kxLyy4Mh9z/+qpno96X2P2U2wXABe/4/iXZ/k13FAECurQrZ+CH9P3h/dysf6f4/NQoxENN7BEAARNT39c38PyNLrE9a/gBA/s6z2q8s/z81Fr56g47+P5BxRoYL7QhAv8xmCd40CkDsDwLBJyMKQDCi28AARQxAiwjYDa42DkDL/TfdGfAMQGht44uJNAhAkZOGQQsACkC3FgxtbWMOQEvtxlzEVg9A", "dtype": "f8"}}], "layout": {"scene": {"xaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "0th"}, "type": "linear"}, "yaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "1st"}, "type": "linear"}, "zaxis": {"exponentformat": "e", "showexponent": "all", "title": {"text": "2nd"}, "type": "linear"}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_point_cloud(X_basic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["we see that there are three distinct clusters, suggesting that a classifier should have no trouble finding a clean decision boundary!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train a classifier\n", "\n", "With our topological features at hand, the last step is to train a classifier. Since our dataset is small, we will use a Random Forest and make use of the OOB score to simulate accuracy on a validation set:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OOB score: 1.000\n"]}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "\n", "rf = RandomForestClassifier(oob_score=True)\n", "rf.fit(X_basic, labels_basic)\n", "\n", "print(f\"OOB score: {rf.oob_score_:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unsurprisingly, our classifier has perfectly separated the 3 classes. Next let's try to combine all the steps as a single pipeline!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Putting it all together\n", "\n", "Evidently, there are several data transformation steps that need to be executed in the right order to go from point clouds to predictions. Fortunately ``giotto-tda`` provides a ``Pipeline`` class to collect such sequences of transformations. Below is a small pipeline that reproduces all our steps:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from gtda.pipeline import Pipeline\n", "\n", "steps = [\n", "    (\"persistence\", V<PERSON>orisRipsPersistence(metric=\"euclidean\", homology_dimensions=homology_dimensions, n_jobs=6)),\n", "    (\"entropy\", PersistenceEntropy()),\n", "    (\"model\", RandomForestClassifier(oob_score=True)),\n", "]\n", "\n", "pipeline = Pipeline(steps)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By calling the ``fit`` method, the pipeline calls ``fit_transform`` on all transformers before calling ``fit`` on the final estimator:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {color: black;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>Pipeline(steps=[(&#x27;persistence&#x27;,\n", "                 VietorisRipsPersistence(homology_dimensions=[0, 1, 2],\n", "                                         n_jobs=6)),\n", "                (&#x27;entropy&#x27;, PersistenceEntropy()),\n", "                (&#x27;model&#x27;, RandomForestClassifier(oob_score=True))])</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" ><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">Pipeline</label><div class=\"sk-toggleable__content\"><pre>Pipeline(steps=[(&#x27;persistence&#x27;,\n", "                 VietorisRipsPersistence(homology_dimensions=[0, 1, 2],\n", "                                         n_jobs=6)),\n", "                (&#x27;entropy&#x27;, PersistenceEntropy()),\n", "                (&#x27;model&#x27;, RandomForestClassifier(oob_score=True))])</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" ><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">VietorisRipsPersistence</label><div class=\"sk-toggleable__content\"><pre>VietorisRipsPersistence(homology_dimensions=[0, 1, 2], n_jobs=6)</pre></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" ><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">PersistenceEntropy</label><div class=\"sk-toggleable__content\"><pre>PersistenceEntropy()</pre></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" ><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">RandomForestClassifier</label><div class=\"sk-toggleable__content\"><pre>RandomForestClassifier(oob_score=True)</pre></div></div></div></div></div></div></div>"], "text/plain": ["Pipeline(steps=[('persistence',\n", "                 VietorisRipsPersistence(homology_dimensions=[0, 1, 2],\n", "                                         n_jobs=6)),\n", "                ('entropy', PersistenceEntropy()),\n", "                ('model', RandomForestClassifier(oob_score=True))])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pipeline.fit(point_clouds_basic, labels_basic)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can then access the Random Forest model by its ``model`` key to pick out the OOB score:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["pipeline[\"model\"].oob_score_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A more realistic example"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the above example, the shapes were sufficiently distinct that it was easy to classify them according to their topological features. Here we consider a slightly more realistic example using point clouds from a variety of real-world objects. We will use the 3D dataset from Princeton's COS 429 Computer Vision [course](https://www.cs.princeton.edu/courses/archive/fall09/cos429/assignment3.html). The dataset consists of 200 models organised into 20 classes of 10 objects each. We'll use a subset consisting of a human, vase, dining_chair, and biplane:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'openml'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mopenml\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdatasets\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfunctions\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_dataset\n\u001b[0;32m      3\u001b[0m df \u001b[38;5;241m=\u001b[39m get_dataset(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mshapes\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mget_data(dataset_format\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdataframe\u001b[39m\u001b[38;5;124m'\u001b[39m)[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m      4\u001b[0m df\u001b[38;5;241m.\u001b[39mhead()\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'openml'"]}], "source": ["from openml.datasets.functions import get_dataset\n", "\n", "df = get_dataset('shapes').get_data(dataset_format='dataframe')[0]\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As a sanity check, let's filter our ``pandas.DataFrame`` for a single member of a class, e.g. a biplane:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_point_cloud(df.query('target == \"biplane0\"')[[\"x\", \"y\", \"z\"]].values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, let's collect all these point clouds in a single NumPy array:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "point_clouds = np.asarray(\n", "    [\n", "        df.query(\"target == @shape\")[[\"x\", \"y\", \"z\"]].values\n", "        for shape in df[\"target\"].unique()\n", "    ]\n", ")\n", "point_clouds.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we did with the simple shapes, we can calculate persistence diagrams for each of these point clouds:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["persistence = VietorisRipsPersistence(\n", "    metric=\"euclidean\",\n", "    homology_dimensions=homology_dimensions,\n", "    n_jobs=6,\n", "    collapse_edges=True,\n", ")\n", "persistence_diagrams = persistence.fit_transform(point_clouds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By zooming in on the resulting diagrams, we can spot some persistent generators, but with far less signal than before:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Index - (human_arms_out, 0), (vase, 10), (dining_chair, 20), (biplane, 30)\n", "index = 30\n", "plot_diagram(persistence_diagrams[index])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we convert each diagram into a 3-dimensional vector using persistent entropy and plot the resulting feature matrix:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["persistence_entropy = PersistenceEntropy(normalize=True)\n", "# Calculate topological feature matrix\n", "X = persistence_entropy.fit_transform(persistence_diagrams)\n", "# Visualise feature matrix\n", "plot_point_cloud(X)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unlike our simple shapes example, we do not see distinct clusters so we expect our classifier performance to be less than perfect in this case. Before we can train a model, we need to define a target vector for each point cloud. A crude, but simple way is to label each class with an integer starting from 0 to $n_\\mathrm{classes} - 1$:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["labels = np.zeros(40)\n", "labels[10:20] = 1\n", "labels[20:30] = 2\n", "labels[30:] = 3\n", "\n", "rf = RandomForestClassifier(oob_score=True, random_state=42)\n", "rf.fit(X, labels)\n", "rf.oob_score_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Improving our model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the analysis above we used persistence entropy to generate 3 topological features per persistence diagram (one per homology dimension). However, this choice of vectorisation is by no means unique, and in practice one can augment this information with other topological features to produce better models. \n", "\n", "For example, a simple feature to include is the number of off-diagonal points per homology dimension:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from gtda.diagrams import NumberOfPoints\n", "\n", "# Reshape single diagram to (n_samples, n_features, 3) format\n", "diagram = persistence_diagrams[0][None, :, :]\n", "# Get number of points for (H0, H1, H2)\n", "NumberOfPoints().fit_transform(diagram)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A more sophisticated feature is to calculate a vector of _amplitudes_ for each persistence diagram. Here the main idea is to partition a diagram into subdiagrams (one per homology dimension) and use a _metric_ to calculate the amplitude of each subdiagram relative to the trivial diagram (i.e. the one with points on the main diagonal). The result is a vector $\\boldsymbol{a} = (a_{q_1}, \\ldots, a_{q_n})$, where the $q_i$ range over the specified homology dimensions.\n", "\n", "For example, we can calculate the [<PERSON><PERSON><PERSON> amplitudes](https://giotto-ai.github.io/gtda-docs/latest/theory/glossary.html#wasserstein-and-bottleneck-distance) for a single diagram as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from gtda.diagrams import Amplitude\n", "\n", "Amplitude(metric='wasserstein').fit_transform(diagram)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So now that we know how to generate new topological features, let's combine them using ``scikit-learn``'s utility function for feature unions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.pipeline import make_union\n", "\n", "# Select a variety of metrics to calculate amplitudes\n", "metrics = [\n", "    {\"metric\": metric}\n", "    for metric in [\"bottleneck\", \"wasserstein\", \"landscape\", \"persistence_image\"]\n", "]\n", "\n", "# Concatenate to generate 3 + 3 + (4 x 3) = 18 topological features\n", "feature_union = make_union(\n", "    PersistenceEntropy(normalize=True),\n", "    NumberOfPoints(n_jobs=-1),\n", "    *[Amplitude(**metric, n_jobs=-1) for metric in metrics]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The final step is to combine our feature extraction step with a classifier, fit the pipeline, and extract the OOB score:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipe = Pipeline(\n", "    [\n", "        (\"features\", feature_union),\n", "        (\"rf\", RandomForestClassifier(oob_score=True, random_state=42)),\n", "    ]\n", ")\n", "pipe.fit(persistence_diagrams, labels)\n", "pipe[\"rf\"].oob_score_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By creating additional features, we've managed to improve our baseline model by about 30% – not bad!"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}