---
bundle_root: null
ckpt_path: "$@bundle_root + '/model_fold' + str(@fold)"
data_file_base_dir: null
data_list_file_path: null
fold: 0

mlflow_tracking_uri: "$@bundle_root + '/model_fold' + str(@fold) + '/mlruns/'"
mlflow_experiment_name: "Auto3DSeg"
show_cache_progress: false

training:
  # hyper-parameters
  amp: true
  auto_scale_allowed: true
  data_list_key: null
  epoch_divided_factor: 36
  input_channels: null
  learning_rate: 0.2
  log_output_file: "$@bundle_root + '/model_fold' + str(@fold) + '/training.log'"
  num_images_per_batch: 2
  num_epochs: 200
  num_epochs_per_validation: 2
  num_crops_per_image: 1
  num_patches_per_iter: 1
  num_sw_batch_size: null
  num_workers: 8
  num_workers_validation: 2
  num_cache_workers: 8
  output_classes: null
  overlap_ratio_train: 0.125
  overlap_ratio: 0.625
  roi_size: null
  roi_size_valid: null
  random_seed: 0
  resample_resolution: null
  sw_input_on_cpu: false
  softmax: true
  valid_at_orig_resolution_at_last: true
  valid_at_orig_resolution_only: false

  adapt_valid_mode: true
  adapt_valid_progress_percentages: [10, 40, 70]
  adapt_valid_num_epochs_per_validation: [2, 4, 2]

  early_stop_mode: true
  early_stop_delta: 0
  early_stop_patience: 20

  cache_rate: 0
  train_cache_rate: "@training#cache_rate"
  validate_cache_rate: "@training#cache_rate"
  transforms:
    resample_resolution: "@training#resample_resolution"

  loss:
    _target_: DiceFocalLoss
    include_background: true
    to_onehot_y: "@training#softmax"
    softmax: "@training#softmax"
    sigmoid: "$not @training#softmax"
    squared_pred: true
    batch: true
    smooth_nr: 1.0e-05
    smooth_dr: 1.0e-05

  optimizer:
    _target_: torch.optim.SGD
    lr: "@training#learning_rate"
    momentum: 0.9
    weight_decay: 4.0e-05

  lr_scheduler:
    _target_: torch.optim.lr_scheduler.PolynomialLR
    optimizer: "@training#optimizer"
    power: 0.5
    total_iters: '$@training#num_epochs // @training#num_epochs_per_validation + 1'

# fine-tuning
finetune:
  activate_finetune: false
  pretrained_ckpt_name: "$@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'"

  overwrite:
    learning_rate: 0.001
    lr_scheduler:
      _target_: torch.optim.lr_scheduler.ConstantLR
      optimizer: "@training#optimizer"
      factor: 1.0
      total_iters: '$@training#num_epochs // @training#num_epochs_per_validation + 1'
    adapt_valid_mode: false
    early_stop_mode: false
    num_epochs: 20
    num_epochs_per_validation: 1

# validation
validate:
  ckpt_name: "$@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'"
  log_output_file: "$@bundle_root + '/model_fold' + str(@fold) + '/validation.log'"
  save_mask: true
  data_list_key: null
  output_path: "$@bundle_root + '/prediction_fold' + str(@fold)"

# inference
infer:
  ckpt_name: "$@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'"
  save_prob: false
  fast: true
  data_list_key: testing
  log_output_file: "$@bundle_root + '/model_fold' + str(@fold) + '/inference.log'"
  output_path: "$@bundle_root + '/prediction_' + @infer#data_list_key"
