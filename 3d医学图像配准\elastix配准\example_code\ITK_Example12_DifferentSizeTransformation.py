#!/usr/bin/env python
# coding: utf-8

# ### 12. Different Size Transformation
# The process of image registration can be made faster, when smaller version of the fixed and moving images are used for calculation of the transformation parameters. These parameters can subsequently be used for the transformation of larger images. 
# 
# In this example image generators are used to get sparsely sampled and densely sampled versions of hypothetical fixed and moving images. An important note is that the width and the height of an image should remain the same. Downsampling of an image means decreasing the number of pixels by increasing the size (or spacing) of a pixel and thereby remaining the width and height of the image the same.

# ### Elastix

# In[1]:


import itk
import numpy as np
import matplotlib.pyplot as plt


# In[2]:


# To generate a downsampled image, the spacing of the image should be increased 10-fold in both directions,
# when the number of pixels is decreased 10-fold in both directions.
def image_generator(x1, x2, y1, y2, downsampled=False):
    if downsampled:
        image = np.zeros([100, 100], np.float32)
    else:
        image = np.zeros([1000, 1000], np.float32)
    image[y1:y2, x1:x2] = 1
    image_itk = itk.image_view_from_array(image)
    if downsampled:
        old_spacing = 1.0
        factor = 1000 / 100
        new_spacing = old_spacing * factor
        image_itk.SetSpacing([new_spacing, new_spacing])
        old_origin = 0.0
        # The start of image's domain, origin-spacing/2.0, should be the same
        new_origin = (new_spacing - old_spacing) / 2
        image_itk.SetOrigin([new_origin, new_origin])
    return image_itk


# In[3]:


# Create sparsely sampled images (fewer pixels) for registration
fixed_image_sparse = image_generator(25,75,25,75, downsampled=True)
moving_image_sparse = image_generator(0,45,10,75, downsampled=True)

# .. and a densely sampled moving image (more pixels) for transformation
moving_image_dense = image_generator(0,450,100,750)

# Import Default Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_map_rigid = parameter_object.GetDefaultParameterMap('affine')
parameter_object.AddParameterMap(parameter_map_rigid)


# Registration with the registration function...

# In[4]:


# Call registration function
result_image_small, result_transform_parameters = itk.elastix_registration_method(
    fixed_image_sparse, moving_image_sparse,
    parameter_object=parameter_object)


# ### Transformix

# In order for the resampling to work as expected, the spatial domain of the densely sampled moving image should remain the same as the sparsely sampled moving image.
# 
# The Origin is the location of the center of the lower left pixel. The start of the first pixel's domain is 0.5 * the spacing in a given direction. 

# In[5]:


dense_origin = moving_image_dense.GetOrigin()
sparse_origin = moving_image_sparse.GetOrigin()

dense_spacing = moving_image_dense.GetSpacing()
sparse_spacing = moving_image_sparse.GetSpacing()

for dim in range(moving_image_dense.GetImageDimension()):
    assert dense_origin[dim] - 0.5*dense_spacing[dim] == sparse_origin[dim] - 0.5*sparse_spacing[dim]


# The spatial extent of an image with identity orientation/direction matrix is the spacing times the number of pixels in a given direction. The spatial extent should be the same, so:
# 
# $$origin_1 * spacing_1 = origin_2 * spacing_2$$

# In[6]:


dense_size = itk.size(moving_image_dense)
sparse_size = itk.size(moving_image_sparse)

for dim in range(moving_image_dense.GetImageDimension()):
    assert dense_size[dim] * dense_spacing[dim] == sparse_size[dim] * sparse_spacing[dim]


# Transformation can either be done in one line with the transformix function...

# In[7]:


result_image_large = itk.transformix_filter(
    moving_image_dense,
    transform_parameter_object=result_transform_parameters,
    log_to_console=False)


# .. or by initiating an transformix image filter object.

# In[8]:


# Load Transformix Object
transformix_object = itk.TransformixFilter.New(moving_image_dense)
transformix_object.SetTransformParameterObject(result_transform_parameters)

# Update object (required)
transformix_object.UpdateLargestPossibleRegion()

# Results of Transformation
result_image_large = transformix_object.GetOutput()


# In[ ]:




