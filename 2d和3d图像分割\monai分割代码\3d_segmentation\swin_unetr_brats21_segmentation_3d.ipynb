{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License. \n", "\n", "# 3D Brain Tumor Segmentation with <PERSON><PERSON> UNETR (BraTS 21 Challenge)\n", "\n", "\n", "This tutorial uses the [Swin UNETR](https://arxiv.org/pdf/2201.01266.pdf) [1,2] model for the task of brain tumor segmentation using the [BraTS 21](http://braintumorsegmentation.org/) challenge dataset [3,4,5,6]. Swin UNETR ranked among top-performing models in the BraTS 21 validation phase. The architecture of Swin UNETR is demonstrated below\n", "\n", "![swin_brats](../figures/swin_brats21.png)\n", "\n", "The following features are included in this tutorial:\n", "1. Transforms for dictionary format data.\n", "1. Define a new transform according to MONAI transform API.\n", "1. Load Nifti image with metadata, load a list of images and stack them.\n", "1. Randomly rotate across each axes for data augmentation.\n", "1. Randomly adjust the intensity for data augmentation.\n", "1. <PERSON><PERSON> and transforms to accelerate training and validation.\n", "1. Swin <PERSON>ETR model, Dice loss function, Mean Dice metric for brain tumor segmentation task.\n", "\n", "For more information access to pre-trained models and distributed training, please refer to Swin UNETR BraTS 21 official repository:\n", "\n", "https://github.com/Project-MONAI/research-contributions/tree/main/SwinUNETR/BRATS21\n", "\n", "## Data Description\n", "\n", "Modality: MRI\n", "Size: 1470 3D volumes (1251 Training + 219 Validation)  \n", "Challenge: RSNA-ASNR-MICCAI Brain Tumor Segmentation (BraTS) Challenge\n", "\n", "The dataset needs to be downloaded from the official BraTS 21 challenge portal as in the following\n", "\n", "https://www.synapse.org/#!Synapse:syn27046444/wiki/616992\n", "\n", "The JSON file containing training and validation sets (internal split) needs to be downloaded from this [link](https://drive.google.com/file/d/1i-BXYe-wZ8R9Vp3GXoajGyqaJ65Jybg1/view?usp=sharing) and placed in the same folder as the dataset. As discussed in the following, this tutorial uses fold 1 for training a Swin UNETR model on the BraTS 21 challenge.\n", "\n", "### Tumor Characteristics\n", "\n", "The sub-regions considered for evaluation in the BraTS 21 challenge are the \"enhancing tumor\" (ET), the \"tumor core\" (TC), and the \"whole tumor\" (WT). The ET is described by areas that show hyper-intensity in T1Gd when compared to T1, but also when compared to “healthy” white matter in T1Gd. The TC describes the bulk of the tumor, which is what is typically resected. The TC entails the ET, as well as the necrotic (NCR) parts of the tumor. The appearance of NCR is typically hypo-intense in T1-Gd when compared to T1. The WT describes the complete extent of the disease, as it entails the TC and the peritumoral edematous/invaded tissue (ED), which is typically depicted by the hyper-intense signal in FLAIR [[BraTS 21]](http://braintumorsegmentation.org/).\n", "\n", "The provided segmentation labels have values of 1 for NCR, 2 for ED, 4 for ET, and 0 for everything else.\n", "\n", "![image](../figures/fig_brats21.png)\n", "\n", "Figure from [<PERSON><PERSON> et al.](https://arxiv.org/pdf/2107.02314v1.pdf) [3]\n", "\n", "\n", "\n", "## References\n", "\n", "\n", "If you find this tutorial helpful, please consider citing [1] and [2]:\n", "\n", "[1]: <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, D., 2022. Swin UNETR: Swin Transformers for Semantic Segmentation of Brain Tumors in MRI Images. arXiv preprint arXiv:2201.01266.\n", "\n", "[2]: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON>, A., 2022. Self-supervised pre-training of swin transformers for 3d medical image analysis. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (pp. 20730-20740).\n", "\n", "\n", "### BraTS Dataset References\n", "\n", "[3] <PERSON><PERSON>, et al., The RSNA-ASNR-MICCAI BraTS 2021 Benchmark on Brain Tumor Segmentation and Radiogenomic Classification, arXiv:2107.02314, 2021.\n", "\n", "[4] <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. \"The Multimodal Brain Tumor Image Segmentation Benchmark (BRATS)\", IEEE Transactions on Medical Imaging 34(10), 1993-2024 (2015) DOI: 10.1109/TMI.2014.2377694\n", "\n", "[5] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al., \"Advancing The Cancer Genome Atlas glioma MRI collections with expert segmentation labels and radiomic features\", Nature Scientific Data, 4:170117 (2017) DOI: 10.1038/sdata.2017.117\n", "\n", "[6] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al., \"Segmentation Labels and Radiomic Features for the Pre-operative Scans of the TCGA-GBM collection\", The Cancer Imaging Archive, 2017. DOI: 10.7937/K9/TCIA.2017.KLXWJJ1Q\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/swin_unetr_brats21_segmentation_3d.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Swin UNETR Model\n", "\n", "The inputs to [Swin UNETR](https://arxiv.org/pdf/2201.01266.pdf) are 3D multi-modal MRI images with 4 channels.\n", "The patch partition block creates non-overlapping patches of the input data and projects them into embedding tokens with a resolution of 128x128x128.\n", "The projected tokens are then encoded by using a 3D [Swin Transformer](https://openaccess.thecvf.com/content/ICCV2021/papers/Liu_Swin_Transformer_Hierarchical_Vision_Transformer_Using_Shifted_Windows_ICCV_2021_paper.pdf) in which the self-attention is computed within local windows.\n", "The interaction between different windows is obtained by using 3D window shifting as illustrated below. \n", "\n", "![image](../figures/shift_patch.png)\n", "\n", "The transformer-based encoder is connected to a CNN-decoder via skip connection at multiple resolutions.\n", "The segmentation output consists of 3 output channels corresponding to ET, WT, and TC sub-regions and is computed by using a 1x1x1 convolutional layer followed by Sigmoid activation function.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": [" ## Download dataset and json file\n", "\n", "- Register and download the official BraTS 21 dataset from the link below and place them into \"TrainingData\" in the dataset folder:\n", "\n", "  https://www.synapse.org/#!Synapse:syn27046444/wiki/616992\n", "  \n", "  For example, the address of a single file is as follows:\n", "  \n", "  \"TrainingData/BraTS2021_01146/BraTS2021_01146_flair.nii.gz\"\n", "  \n", "\n", "- Download the json file from this [link](https://drive.google.com/file/d/1i-BXYe-wZ8R9Vp3GXoajGyqaJ65Jybg1/view?usp=sharing) and placed in the same folder as the dataset.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 0.9.0rc3\n", "Numpy version: 1.22.2\n", "Pytorch version: 1.10.0a0+0aef44c\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False\n", "MONAI rev id: a20ff4ebc84776b29744fc7a3ba177f724442f05\n", "MONAI __file__: /opt/conda/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.8\n", "Nibabel version: 3.1.1\n", "scikit-image version: 0.19.1\n", "Pillow version: 9.0.1\n", "Tensorboard version: 2.6.0\n", "gdown version: 4.2.1\n", "TorchVision version: 0.11.0a0\n", "tqdm version: 4.62.3\n", "lmdb version: 1.2.1\n", "psutil version: 5.8.0\n", "pandas version: 1.4.1\n", "einops version: 0.3.2\n", "transformers version: 4.16.2\n", "mlflow version: 1.23.1\n", "pynrrd version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import json\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import nibabel as nib\n", "\n", "from monai.losses import DiceLoss\n", "from monai.inferers import sliding_window_inference\n", "from monai import transforms\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    Activations,\n", ")\n", "\n", "from monai.config import print_config\n", "from monai.metrics import DiceMetric\n", "from monai.utils.enums import MetricReduction\n", "from monai.networks.nets import SwinUNETR\n", "from monai import data\n", "from monai.data import decollate_batch\n", "from functools import partial\n", "\n", "import torch\n", "\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup average meter, fold reader, checkpoint saver"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class AverageMeter(object):\n", "    def __init__(self):\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        self.val = 0\n", "        self.avg = 0\n", "        self.sum = 0\n", "        self.count = 0\n", "\n", "    def update(self, val, n=1):\n", "        self.val = val\n", "        self.sum += val * n\n", "        self.count += n\n", "        self.avg = np.where(self.count > 0, self.sum / self.count, self.sum)\n", "\n", "\n", "def datafold_read(datalist, basedir, fold=0, key=\"training\"):\n", "    with open(datalist) as f:\n", "        json_data = json.load(f)\n", "\n", "    json_data = json_data[key]\n", "\n", "    for d in json_data:\n", "        for k in d:\n", "            if isinstance(d[k], list):\n", "                d[k] = [os.path.join(basedir, iv) for iv in d[k]]\n", "            elif isinstance(d[k], str):\n", "                d[k] = os.path.join(basedir, d[k]) if len(d[k]) > 0 else d[k]\n", "\n", "    tr = []\n", "    val = []\n", "    for d in json_data:\n", "        if \"fold\" in d and d[\"fold\"] == fold:\n", "            val.append(d)\n", "        else:\n", "            tr.append(d)\n", "\n", "    return tr, val\n", "\n", "\n", "def save_checkpoint(model, epoch, filename=\"model.pt\", best_acc=0, dir_add=root_dir):\n", "    state_dict = model.state_dict()\n", "    save_dict = {\"epoch\": epoch, \"best_acc\": best_acc, \"state_dict\": state_dict}\n", "    filename = os.path.join(dir_add, filename)\n", "    torch.save(save_dict, filename)\n", "    print(\"Saving checkpoint\", filename)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup dataloader"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def get_loader(batch_size, data_dir, json_list, fold, roi):\n", "    data_dir = data_dir\n", "    datalist_json = json_list\n", "    train_files, validation_files = datafold_read(datalist=datalist_json, basedir=data_dir, fold=fold)\n", "    train_transform = transforms.Compose(\n", "        [\n", "            transforms.LoadImaged(keys=[\"image\", \"label\"]),\n", "            transforms.ConvertToMultiChannelBasedOnBratsClassesd(keys=\"label\"),\n", "            transforms.CropForegroundd(\n", "                keys=[\"image\", \"label\"],\n", "                source_key=\"image\",\n", "                k_divisible=[roi[0], roi[1], roi[2]],\n", "            ),\n", "            transforms.RandSpatialCropd(\n", "                keys=[\"image\", \"label\"],\n", "                roi_size=[roi[0], roi[1], roi[2]],\n", "                random_size=False,\n", "            ),\n", "            transforms.RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=0),\n", "            transforms.RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=1),\n", "            transforms.RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=2),\n", "            transforms.NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n", "            transforms.RandScaleIntensityd(keys=\"image\", factors=0.1, prob=1.0),\n", "            transforms.RandShiftIntensityd(keys=\"image\", offsets=0.1, prob=1.0),\n", "        ]\n", "    )\n", "    val_transform = transforms.Compose(\n", "        [\n", "            transforms.LoadImaged(keys=[\"image\", \"label\"]),\n", "            transforms.ConvertToMultiChannelBasedOnBratsClassesd(keys=\"label\"),\n", "            transforms.NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n", "        ]\n", "    )\n", "\n", "    train_ds = data.Dataset(data=train_files, transform=train_transform)\n", "\n", "    train_loader = data.DataLoader(\n", "        train_ds,\n", "        batch_size=batch_size,\n", "        shuffle=True,\n", "        num_workers=8,\n", "        pin_memory=True,\n", "    )\n", "    val_ds = data.Dataset(data=validation_files, transform=val_transform)\n", "    val_loader = data.DataLoader(\n", "        val_ds,\n", "        batch_size=1,\n", "        shuffle=False,\n", "        num_workers=8,\n", "        pin_memory=True,\n", "    )\n", "\n", "    return train_loader, val_loader"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set dataset root directory and hyper-parameters\n", "\n", "The following hyper-parameters are set for the purpose of this tutorial. However, additional changes, as described below, maybe beneficial. \n", "\n", "If GPU memory is not sufficient, reduce sw_batch_size to 2 or batch_size to 1. \n", "\n", "Decrease val_every (validation frequency) to 1 for obtaining more accurate checkpoints."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["data_dir = \"/data/brats2021challenge\"\n", "json_list = \"./brats21_folds.json\"\n", "roi = (128, 128, 128)\n", "batch_size = 2\n", "sw_batch_size = 4\n", "fold = 1\n", "infer_overlap = 0.5\n", "max_epochs = 100\n", "val_every = 10\n", "train_loader, val_loader = get_loader(batch_size, data_dir, json_list, fold, roi)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check data shape and visualize"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image shape: (240, 240, 155), label shape: (240, 240, 155)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["img_add = os.path.join(data_dir, \"TrainingData/BraTS2021_00006/BraTS2021_00006_flair.nii.gz\")\n", "label_add = os.path.join(data_dir, \"TrainingData/BraTS2021_00006/BraTS2021_00006_seg.nii.gz\")\n", "img = nib.load(img_add).get_fdata()\n", "label = nib.load(label_add).get_fdata()\n", "print(f\"image shape: {img.shape}, label shape: {label.shape}\")\n", "plt.figure(\"image\", (18, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"image\")\n", "plt.imshow(img[:, :, 78], cmap=\"gray\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"label\")\n", "plt.imshow(label[:, :, 78])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Swin UNETR model\n", "\n", "In this scetion, we create Swin UNETR model for the 3-class brain tumor semantic segmentation. We use a feature size of 48. We also use gradient checkpointing (use_checkpoint) for more memory-efficient training. However, use_checkpoint for faster training if enough GPU memory is available.  "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "model = SwinUNETR(\n", "    img_size=roi,\n", "    in_channels=4,\n", "    out_channels=3,\n", "    feature_size=48,\n", "    drop_rate=0.0,\n", "    attn_drop_rate=0.0,\n", "    dropout_path_rate=0.0,\n", "    use_checkpoint=True,\n", ").to(device)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optimizer and loss function"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["torch.backends.cudnn.benchmark = True\n", "dice_loss = DiceLoss(to_onehot_y=False, sigmoid=True)\n", "post_sigmoid = Activations(sigmoid=True)\n", "post_pred = AsDiscrete(argmax=False, threshold=0.5)\n", "dice_acc = DiceMetric(include_background=True, reduction=MetricReduction.MEAN_BATCH, get_not_nans=True)\n", "model_inferer = partial(\n", "    sliding_window_inference,\n", "    roi_size=[roi[0], roi[1], roi[2]],\n", "    sw_batch_size=sw_batch_size,\n", "    predictor=model,\n", "    overlap=infer_overlap,\n", ")\n", "\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)\n", "scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=max_epochs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Train and Validation Epoch"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [], "source": ["def train_epoch(model, loader, optimizer, epoch, loss_func):\n", "    model.train()\n", "    start_time = time.time()\n", "    run_loss = AverageMeter()\n", "    for idx, batch_data in enumerate(loader):\n", "        data, target = batch_data[\"image\"].to(device), batch_data[\"label\"].to(device)\n", "        logits = model(data)\n", "        loss = loss_func(logits, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "        run_loss.update(loss.item(), n=batch_size)\n", "        print(\n", "            \"Epoch {}/{} {}/{}\".format(epoch, max_epochs, idx, len(loader)),\n", "            \"loss: {:.4f}\".format(run_loss.avg),\n", "            \"time {:.2f}s\".format(time.time() - start_time),\n", "        )\n", "        start_time = time.time()\n", "    return run_loss.avg\n", "\n", "\n", "def val_epoch(\n", "    model,\n", "    loader,\n", "    epoch,\n", "    acc_func,\n", "    model_inferer=None,\n", "    post_sigmoid=None,\n", "    post_pred=None,\n", "):\n", "    model.eval()\n", "    start_time = time.time()\n", "    run_acc = AverageMeter()\n", "\n", "    with torch.no_grad():\n", "        for idx, batch_data in enumerate(loader):\n", "            data, target = batch_data[\"image\"].to(device), batch_data[\"label\"].to(device)\n", "            logits = model_inferer(data)\n", "            val_labels_list = decollate_batch(target)\n", "            val_outputs_list = decollate_batch(logits)\n", "            val_output_convert = [post_pred(post_sigmoid(val_pred_tensor)) for val_pred_tensor in val_outputs_list]\n", "            acc_func.reset()\n", "            acc_func(y_pred=val_output_convert, y=val_labels_list)\n", "            acc, not_nans = acc_func.aggregate()\n", "            run_acc.update(acc.cpu().numpy(), n=not_nans.cpu().numpy())\n", "            dice_tc = run_acc.avg[0]\n", "            dice_wt = run_acc.avg[1]\n", "            dice_et = run_acc.avg[2]\n", "            print(\n", "                \"Val {}/{} {}/{}\".format(epoch, max_epochs, idx, len(loader)),\n", "                \", dice_tc:\",\n", "                dice_tc,\n", "                \", dice_wt:\",\n", "                dice_wt,\n", "                \", dice_et:\",\n", "                dice_et,\n", "                \", time {:.2f}s\".format(time.time() - start_time),\n", "            )\n", "            start_time = time.time()\n", "\n", "    return run_acc.avg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Trainer"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def trainer(\n", "    model,\n", "    train_loader,\n", "    val_loader,\n", "    optimizer,\n", "    loss_func,\n", "    acc_func,\n", "    scheduler,\n", "    model_inferer=None,\n", "    start_epoch=0,\n", "    post_sigmoid=None,\n", "    post_pred=None,\n", "):\n", "    val_acc_max = 0.0\n", "    dices_tc = []\n", "    dices_wt = []\n", "    dices_et = []\n", "    dices_avg = []\n", "    loss_epochs = []\n", "    trains_epoch = []\n", "    for epoch in range(start_epoch, max_epochs):\n", "        print(time.ctime(), \"Epoch:\", epoch)\n", "        epoch_time = time.time()\n", "        train_loss = train_epoch(\n", "            model,\n", "            train_loader,\n", "            optimizer,\n", "            epoch=epoch,\n", "            loss_func=loss_func,\n", "        )\n", "        print(\n", "            \"Final training  {}/{}\".format(epoch, max_epochs - 1),\n", "            \"loss: {:.4f}\".format(train_loss),\n", "            \"time {:.2f}s\".format(time.time() - epoch_time),\n", "        )\n", "\n", "        if (epoch + 1) % val_every == 0 or epoch == 0:\n", "            loss_epochs.append(train_loss)\n", "            trains_epoch.append(int(epoch))\n", "            epoch_time = time.time()\n", "            val_acc = val_epoch(\n", "                model,\n", "                val_loader,\n", "                epoch=epoch,\n", "                acc_func=acc_func,\n", "                model_inferer=model_inferer,\n", "                post_sigmoid=post_sigmoid,\n", "                post_pred=post_pred,\n", "            )\n", "            dice_tc = val_acc[0]\n", "            dice_wt = val_acc[1]\n", "            dice_et = val_acc[2]\n", "            val_avg_acc = np.mean(val_acc)\n", "            print(\n", "                \"Final validation stats {}/{}\".format(epoch, max_epochs - 1),\n", "                \", dice_tc:\",\n", "                dice_tc,\n", "                \", dice_wt:\",\n", "                dice_wt,\n", "                \", dice_et:\",\n", "                dice_et,\n", "                \", Dice_Avg:\",\n", "                val_avg_acc,\n", "                \", time {:.2f}s\".format(time.time() - epoch_time),\n", "            )\n", "            dices_tc.append(dice_tc)\n", "            dices_wt.append(dice_wt)\n", "            dices_et.append(dice_et)\n", "            dices_avg.append(val_avg_acc)\n", "            if val_avg_acc > val_acc_max:\n", "                print(\"new best ({:.6f} --> {:.6f}). \".format(val_acc_max, val_avg_acc))\n", "                val_acc_max = val_avg_acc\n", "                save_checkpoint(\n", "                    model,\n", "                    epoch,\n", "                    best_acc=val_acc_max,\n", "                )\n", "            scheduler.step()\n", "    print(\"Training Finished !, Best Accuracy: \", val_acc_max)\n", "    return (\n", "        val_acc_max,\n", "        dices_tc,\n", "        dices_wt,\n", "        dices_et,\n", "        dices_avg,\n", "        loss_epochs,\n", "        trains_epoch,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["start_epoch = 0\n", "\n", "(\n", "    val_acc_max,\n", "    dices_tc,\n", "    dices_wt,\n", "    dices_et,\n", "    dices_avg,\n", "    loss_epochs,\n", "    trains_epoch,\n", ") = trainer(\n", "    model=model,\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    optimizer=optimizer,\n", "    loss_func=dice_loss,\n", "    acc_func=dice_acc,\n", "    scheduler=scheduler,\n", "    model_inferer=model_inferer,\n", "    start_epoch=start_epoch,\n", "    post_sigmoid=post_sigmoid,\n", "    post_pred=post_pred,\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train completed, best average dice: 0.7828 \n"]}], "source": ["print(f\"train completed, best average dice: {val_acc_max:.4f} \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot the loss and Dice metric"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Epoch Average Loss\")\n", "plt.xlabel(\"epoch\")\n", "plt.plot(trains_epoch, loss_epochs, color=\"red\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val Mean Dice\")\n", "plt.xlabel(\"epoch\")\n", "plt.plot(trains_epoch, dices_avg, color=\"green\")\n", "plt.show()\n", "plt.figure(\"train\", (18, 6))\n", "plt.subplot(1, 3, 1)\n", "plt.title(\"Val Mean Dice TC\")\n", "plt.xlabel(\"epoch\")\n", "plt.plot(trains_epoch, dices_tc, color=\"blue\")\n", "plt.subplot(1, 3, 2)\n", "plt.title(\"Val Mean Dice WT\")\n", "plt.xlabel(\"epoch\")\n", "plt.plot(trains_epoch, dices_wt, color=\"brown\")\n", "plt.subplot(1, 3, 3)\n", "plt.title(\"Val Mean Dice ET\")\n", "plt.xlabel(\"epoch\")\n", "plt.plot(trains_epoch, dices_et, color=\"purple\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create test set dataloader"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["case_num = \"01619\"\n", "\n", "test_files = [\n", "    {\n", "        \"image\": [\n", "            os.path.join(\n", "                data_dir,\n", "                \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_flair.nii.gz\",\n", "            ),\n", "            os.path.join(\n", "                data_dir,\n", "                \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_t1ce.nii.gz\",\n", "            ),\n", "            os.path.join(\n", "                data_dir,\n", "                \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_t1.nii.gz\",\n", "            ),\n", "            os.path.join(\n", "                data_dir,\n", "                \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_t2.nii.gz\",\n", "            ),\n", "        ],\n", "        \"label\": os.path.join(\n", "            data_dir,\n", "            \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_seg.nii.gz\",\n", "        ),\n", "    }\n", "]\n", "\n", "test_transform = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\", \"label\"]),\n", "        transforms.ConvertToMultiChannelBasedOnBratsClassesd(keys=\"label\"),\n", "        transforms.NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n", "    ]\n", ")\n", "\n", "test_ds = data.Dataset(data=test_files, transform=test_transform)\n", "\n", "test_loader = data.DataLoader(\n", "    test_ds,\n", "    batch_size=1,\n", "    shuffle=False,\n", "    num_workers=8,\n", "    pin_memory=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the best saved checkpoint and perform inference \n", "\n", "We select a single case from the validation set and perform inference to compare the model segmentation output with the corresponding label. "]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["model.load_state_dict(torch.load(os.path.join(root_dir, \"model.pt\"))[\"state_dict\"])\n", "model.to(device)\n", "model.eval()\n", "\n", "model_inferer_test = partial(\n", "    sliding_window_inference,\n", "    roi_size=[roi[0], roi[1], roi[2]],\n", "    sw_batch_size=1,\n", "    predictor=model,\n", "    overlap=0.6,\n", ")\n", "\n", "\n", "with torch.no_grad():\n", "    for batch_data in test_loader:\n", "        image = batch_data[\"image\"].cuda()\n", "        prob = torch.sigmoid(model_inferer_test(image))\n", "        seg = prob[0].detach().cpu().numpy()\n", "        seg = (seg > 0.5).astype(np.int8)\n", "        seg_out = np.zeros((seg.shape[1], seg.shape[2], seg.shape[3]))\n", "        seg_out[seg[1] == 1] = 2\n", "        seg_out[seg[0] == 1] = 1\n", "        seg_out[seg[2] == 1] = 4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize segmentation output and compare with label"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["slice_num = 67\n", "img_add = os.path.join(\n", "    data_dir,\n", "    \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_t1ce.nii.gz\",\n", ")\n", "label_add = os.path.join(\n", "    data_dir,\n", "    \"TrainingData/BraTS2021_\" + case_num + \"/BraTS2021_\" + case_num + \"_seg.nii.gz\",\n", ")\n", "img = nib.load(img_add).get_fdata()\n", "label = nib.load(label_add).get_fdata()\n", "plt.figure(\"image\", (18, 6))\n", "plt.subplot(1, 3, 1)\n", "plt.title(\"image\")\n", "plt.imshow(img[:, :, slice_num], cmap=\"gray\")\n", "plt.subplot(1, 3, 2)\n", "plt.title(\"label\")\n", "plt.imshow(label[:, :, slice_num])\n", "plt.subplot(1, 3, 3)\n", "plt.title(\"segmentation\")\n", "plt.imshow(seg_out[:, :, slice_num])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}