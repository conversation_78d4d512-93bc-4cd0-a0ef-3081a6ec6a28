2025-01-03 16:56:30,506 - INFO - Found 10 files to process
2025-01-03 16:56:30,508 - INFO - Processing file 1/10: baoyongxing-hbp.nii.gz
2025-01-03 16:56:30,509 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\baoyongxing-hbp.nii.gz
2025-01-03 16:56:30,510 - ERROR - Error processing file baoyongxing-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\baoyongxing-hbp.nii.gz
2025-01-03 16:56:30,510 - INFO - Processing file 2/10: chencanzhen-hbp.nii.gz
2025-01-03 16:56:30,511 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\chencanzhen-hbp.nii.gz
2025-01-03 16:56:30,512 - ERROR - Error processing file chencanzhen-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\chencanzhen-hbp.nii.gz
2025-01-03 16:56:30,513 - INFO - Processing file 3/10: duanxinqi-hbp.nii.gz
2025-01-03 16:56:30,514 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\duanxinqi-hbp.nii.gz
2025-01-03 16:56:30,515 - ERROR - Error processing file duanxinqi-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\duanxinqi-hbp.nii.gz
2025-01-03 16:56:30,515 - INFO - Processing file 4/10: linwanying-hbp.nii.gz
2025-01-03 16:56:30,516 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\linwanying-hbp.nii.gz
2025-01-03 16:56:30,517 - ERROR - Error processing file linwanying-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\linwanying-hbp.nii.gz
2025-01-03 16:56:30,517 - INFO - Processing file 5/10: lixiangdong-hbp.nii.gz
2025-01-03 16:56:30,517 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\lixiangdong-hbp.nii.gz
2025-01-03 16:56:30,518 - ERROR - Error processing file lixiangdong-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\lixiangdong-hbp.nii.gz
2025-01-03 16:56:30,518 - INFO - Processing file 6/10: ludailin-hbp.nii.gz
2025-01-03 16:56:30,519 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\ludailin-hbp.nii.gz
2025-01-03 16:56:30,519 - ERROR - Error processing file ludailin-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\ludailin-hbp.nii.gz
2025-01-03 16:56:30,520 - INFO - Processing file 7/10: qiuxiaoya-hbp.nii.gz
2025-01-03 16:56:30,521 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\qiuxiaoya-hbp.nii.gz
2025-01-03 16:56:30,521 - ERROR - Error processing file qiuxiaoya-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\qiuxiaoya-hbp.nii.gz
2025-01-03 16:56:30,522 - INFO - Processing file 8/10: xuanjianming-hbp.nii.gz
2025-01-03 16:56:30,523 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuanjianming-hbp.nii.gz
2025-01-03 16:56:30,524 - ERROR - Error processing file xuanjianming-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuanjianming-hbp.nii.gz
2025-01-03 16:56:30,524 - INFO - Processing file 9/10: xuyaodong-hbp.nii.gz
2025-01-03 16:56:30,525 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuyaodong-hbp.nii.gz
2025-01-03 16:56:30,525 - ERROR - Error processing file xuyaodong-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuyaodong-hbp.nii.gz
2025-01-03 16:56:30,526 - INFO - Processing file 10/10: zhaochuanling-hbp.nii.gz
2025-01-03 16:56:30,527 - ERROR - Error in AffBsp: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\zhaochuanling-hbp.nii.gz
2025-01-03 16:56:30,528 - ERROR - Error processing file zhaochuanling-hbp.nii.gz: Input file not found: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\zhaochuanling-hbp.nii.gz
2025-01-03 16:56:30,528 - INFO - Registration process completed
2025-01-03 17:14:51,336 - INFO - Found 10 files to process
2025-01-03 17:14:51,337 - INFO - Processing files for patient baoyongxing
2025-01-03 17:14:51,338 - ERROR - Error processing patient baoyongxing: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,338 - INFO - Processing files for patient chencanzhen
2025-01-03 17:14:51,339 - ERROR - Error processing patient chencanzhen: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,340 - INFO - Processing files for patient duanxinqi
2025-01-03 17:14:51,341 - ERROR - Error processing patient duanxinqi: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,342 - INFO - Processing files for patient linwanying
2025-01-03 17:14:51,342 - ERROR - Error processing patient linwanying: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,343 - INFO - Processing files for patient lixiangdong
2025-01-03 17:14:51,344 - ERROR - Error processing patient lixiangdong: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,344 - INFO - Processing files for patient ludailin
2025-01-03 17:14:51,345 - ERROR - Error processing patient ludailin: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,346 - INFO - Processing files for patient qiuxiaoya
2025-01-03 17:14:51,346 - ERROR - Error processing patient qiuxiaoya: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,347 - INFO - Processing files for patient xuanjianming
2025-01-03 17:14:51,347 - ERROR - Error processing patient xuanjianming: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,348 - INFO - Processing files for patient xuyaodong
2025-01-03 17:14:51,349 - ERROR - Error processing patient xuyaodong: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:14:51,349 - INFO - Processing files for patient zhaochuanling
2025-01-03 17:14:51,350 - ERROR - Error processing patient zhaochuanling: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,414 - INFO - Found 10 files to process
2025-01-03 17:15:21,415 - INFO - Processing files for patient baoyongxing
2025-01-03 17:15:21,415 - ERROR - Error processing patient baoyongxing: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,416 - INFO - Processing files for patient chencanzhen
2025-01-03 17:15:21,417 - ERROR - Error processing patient chencanzhen: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,417 - INFO - Processing files for patient duanxinqi
2025-01-03 17:15:21,418 - ERROR - Error processing patient duanxinqi: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,419 - INFO - Processing files for patient linwanying
2025-01-03 17:15:21,419 - ERROR - Error processing patient linwanying: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,420 - INFO - Processing files for patient lixiangdong
2025-01-03 17:15:21,420 - ERROR - Error processing patient lixiangdong: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,421 - INFO - Processing files for patient ludailin
2025-01-03 17:15:21,422 - ERROR - Error processing patient ludailin: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,422 - INFO - Processing files for patient qiuxiaoya
2025-01-03 17:15:21,423 - ERROR - Error processing patient qiuxiaoya: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,423 - INFO - Processing files for patient xuanjianming
2025-01-03 17:15:21,424 - ERROR - Error processing patient xuanjianming: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,424 - INFO - Processing files for patient xuyaodong
2025-01-03 17:15:21,425 - ERROR - Error processing patient xuyaodong: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:15:21,425 - INFO - Processing files for patient zhaochuanling
2025-01-03 17:15:21,425 - ERROR - Error processing patient zhaochuanling: AffBsp() takes 3 positional arguments but 5 were given
2025-01-03 17:18:59,550 - INFO - Found 10 files to process
2025-01-03 17:18:59,552 - INFO - Processing files for patient baoyongxing
2025-01-03 17:18:59,595 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\baoyongxing-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\baoyongxing-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\baoyongxing-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\baoyongxing-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\baoyongxing_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,638 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,640 - ERROR - Error processing patient baoyongxing: Registration failed with exit code 1
2025-01-03 17:18:59,640 - INFO - Processing files for patient chencanzhen
2025-01-03 17:18:59,643 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\chencanzhen-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\chencanzhen-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\chencanzhen-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\chencanzhen-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\chencanzhen_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,683 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,685 - ERROR - Error processing patient chencanzhen: Registration failed with exit code 1
2025-01-03 17:18:59,686 - INFO - Processing files for patient duanxinqi
2025-01-03 17:18:59,689 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\duanxinqi-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\duanxinqi-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\duanxinqi-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\duanxinqi-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\duanxinqi_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,732 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,735 - ERROR - Error processing patient duanxinqi: Registration failed with exit code 1
2025-01-03 17:18:59,736 - INFO - Processing files for patient linwanying
2025-01-03 17:18:59,739 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\linwanying-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\linwanying-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\linwanying-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\linwanying-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\linwanying_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,778 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,779 - ERROR - Error processing patient linwanying: Registration failed with exit code 1
2025-01-03 17:18:59,780 - INFO - Processing files for patient lixiangdong
2025-01-03 17:18:59,785 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\lixiangdong-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\lixiangdong-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\lixiangdong-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\lixiangdong-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\lixiangdong_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,828 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,829 - ERROR - Error processing patient lixiangdong: Registration failed with exit code 1
2025-01-03 17:18:59,830 - INFO - Processing files for patient ludailin
2025-01-03 17:18:59,833 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\ludailin-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\ludailin-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\ludailin-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\ludailin-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\ludailin_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,872 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,874 - ERROR - Error processing patient ludailin: Registration failed with exit code 1
2025-01-03 17:18:59,875 - INFO - Processing files for patient qiuxiaoya
2025-01-03 17:18:59,877 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\qiuxiaoya-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\qiuxiaoya-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\qiuxiaoya-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\qiuxiaoya-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\qiuxiaoya_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,916 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,918 - ERROR - Error processing patient qiuxiaoya: Registration failed with exit code 1
2025-01-03 17:18:59,919 - INFO - Processing files for patient xuanjianming
2025-01-03 17:18:59,921 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\xuanjianming-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\xuanjianming-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuanjianming-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\xuanjianming-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\xuanjianming_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:18:59,961 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:18:59,963 - ERROR - Error processing patient xuanjianming: Registration failed with exit code 1
2025-01-03 17:18:59,966 - INFO - Processing files for patient xuyaodong
2025-01-03 17:18:59,969 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\xuyaodong-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\xuyaodong-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuyaodong-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\xuyaodong-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\xuyaodong_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:19:00,005 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:19:00,007 - ERROR - Error processing patient xuyaodong: Registration failed with exit code 1
2025-01-03 17:19:00,008 - INFO - Processing files for patient zhaochuanling
2025-01-03 17:19:00,010 - INFO - Executing registration command: f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe -f H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\zhaochuanling-hbp.nii.gz -fMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\zhaochuanling-hbp-mask.nii.gz -m H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\zhaochuanling-ap.nii.gz -mMask H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\zhaochuanling-ap-mask.nii.gz -out H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\zhaochuanling_aff -p f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt
2025-01-03 17:19:00,053 - ERROR - Error in AffBsp: Registration failed with exit code 1
2025-01-03 17:19:00,054 - ERROR - Error processing patient zhaochuanling: Registration failed with exit code 1
2025-01-03 17:20:28,621 - INFO - Found 10 files to process
2025-01-03 17:20:28,623 - INFO - Processing files for patient baoyongxing
2025-01-03 17:20:28,624 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\baoyongxing-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\baoyongxing-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\baoyongxing-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\baoyongxing-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\baoyongxing_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:20:56,578 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\baoyongxing-ap.nii.gz
2025-01-03 17:20:56,592 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\baoyongxing-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\baoyongxing-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\baoyongxing-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\baoyongxing-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\baoyongxing_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:21:22,109 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\baoyongxing-pp.nii.gz
2025-01-03 17:21:22,111 - INFO - Processing files for patient chencanzhen
2025-01-03 17:21:22,113 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\chencanzhen-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\chencanzhen-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\chencanzhen-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\chencanzhen-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\chencanzhen_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:22:16,345 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\chencanzhen-ap.nii.gz
2025-01-03 17:22:16,349 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\chencanzhen-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\chencanzhen-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\chencanzhen-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\chencanzhen-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\chencanzhen_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:22:54,600 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\chencanzhen-pp.nii.gz
2025-01-03 17:22:54,602 - INFO - Processing files for patient duanxinqi
2025-01-03 17:22:54,603 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\duanxinqi-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\duanxinqi-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\duanxinqi-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\duanxinqi-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\duanxinqi_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:23:20,673 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\duanxinqi-ap.nii.gz
2025-01-03 17:23:20,676 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\duanxinqi-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\duanxinqi-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\duanxinqi-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\duanxinqi-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\duanxinqi_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:23:44,798 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\duanxinqi-pp.nii.gz
2025-01-03 17:23:44,801 - INFO - Processing files for patient linwanying
2025-01-03 17:23:44,802 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\linwanying-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\linwanying-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\linwanying-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\linwanying-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\linwanying_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:24:14,210 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\linwanying-ap.nii.gz
2025-01-03 17:24:14,212 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\linwanying-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\linwanying-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\linwanying-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\linwanying-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\linwanying_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:24:48,925 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\linwanying-pp.nii.gz
2025-01-03 17:24:48,927 - INFO - Processing files for patient lixiangdong
2025-01-03 17:24:48,928 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\lixiangdong-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\lixiangdong-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\lixiangdong-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\lixiangdong-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\lixiangdong_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:25:19,021 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\lixiangdong-ap.nii.gz
2025-01-03 17:25:19,024 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\lixiangdong-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\lixiangdong-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\lixiangdong-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\lixiangdong-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\lixiangdong_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:26:10,627 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\lixiangdong-pp.nii.gz
2025-01-03 17:26:10,630 - INFO - Processing files for patient ludailin
2025-01-03 17:26:10,631 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\ludailin-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\ludailin-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\ludailin-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\ludailin-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\ludailin_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:26:36,084 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\ludailin-ap.nii.gz
2025-01-03 17:26:36,092 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\ludailin-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\ludailin-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\ludailin-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\ludailin-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\ludailin_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:27:01,172 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\ludailin-pp.nii.gz
2025-01-03 17:27:01,177 - INFO - Processing files for patient qiuxiaoya
2025-01-03 17:27:01,178 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\qiuxiaoya-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\qiuxiaoya-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\qiuxiaoya-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\qiuxiaoya-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\qiuxiaoya_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:27:49,575 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\qiuxiaoya-ap.nii.gz
2025-01-03 17:27:49,578 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\qiuxiaoya-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\qiuxiaoya-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\qiuxiaoya-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\qiuxiaoya-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\qiuxiaoya_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:28:28,738 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\qiuxiaoya-pp.nii.gz
2025-01-03 17:28:28,742 - INFO - Processing files for patient xuanjianming
2025-01-03 17:28:28,744 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\xuanjianming-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\xuanjianming-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuanjianming-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\xuanjianming-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\xuanjianming_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:28:55,549 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\xuanjianming-ap.nii.gz
2025-01-03 17:28:55,574 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\xuanjianming-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\xuanjianming-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\xuanjianming-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\xuanjianming-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\xuanjianming_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:29:22,169 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\xuanjianming-pp.nii.gz
2025-01-03 17:29:22,171 - INFO - Processing files for patient xuyaodong
2025-01-03 17:29:22,172 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\xuyaodong-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\xuyaodong-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\xuyaodong-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\xuyaodong-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\xuyaodong_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:30:03,126 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\xuyaodong-ap.nii.gz
2025-01-03 17:30:03,142 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\xuyaodong-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\xuyaodong-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\xuyaodong-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\xuyaodong-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\xuyaodong_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:31:01,143 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\xuyaodong-pp.nii.gz
2025-01-03 17:31:01,145 - INFO - Processing files for patient zhaochuanling
2025-01-03 17:31:01,146 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\zhaochuanling-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\zhaochuanling-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap\zhaochuanling-ap.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap\zhaochuanling-ap-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap\zhaochuanling_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:31:30,858 - INFO - Successfully registered and saved AP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap\zhaochuanling-ap.nii.gz
2025-01-03 17:31:30,862 - INFO - Executing registration command: "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\elastix.exe" -f "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp\zhaochuanling-hbp.nii.gz" -fMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp\zhaochuanling-hbp-mask.nii.gz" -m "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp\zhaochuanling-pp.nii.gz" -mMask "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp\zhaochuanling-pp-mask.nii.gz" -out "H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp\zhaochuanling_aff" -p "f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\0.深度学习和R代码总结\深度学习代码总结最新版\3d图像配准\elastix\Par0057Bspline.txt"
2025-01-03 17:32:12,570 - INFO - Successfully registered and saved PP: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp\zhaochuanling-pp.nii.gz
2025-01-03 21:14:20,461 - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-01-03 21:14:20,463 - INFO - NumExpr defaulting to 8 threads.
