import nibabel as nb
import matplotlib.pyplot as plt
import time
from IPython.display import clear_output
import scipy.ndimage.interpolation as interpolation
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rc('image', aspect='equal')
import SimpleITK as sitk
import numpy as np
import os

from nipype.interfaces.ants import N4BiasFieldCorrection
from nipype.interfaces.fsl import FLIRT, BET, ApplyMask, ApplyXFM, ConvertXFM, BinaryMaths, ChangeDataType, \
    MultiImageMaths
from nipype.interfaces.ants import N4BiasFieldCorrection


def resampleit(image, dims, isseg=False):
    order = 0 if isseg == True else 5

    image = interpolation.zoom(image, np.array(dims) / np.array(image.shape, dtype=np.float32), order=order,
                               mode='nearest')

    if image.shape[-1] == 3:  # rgb image
        return image
    else:
        return image


def func_resample_isovoxel(img_original, isseg=False):
    oldimage = sitk.ReadImage(img_original)
    coord = oldimage.GetDirection()
    oldimage_arr = sitk.GetArrayFromImage(oldimage)

    size = oldimage.GetSize()
    spacing = oldimage.GetSpacing()

    fovx = size[0] * spacing[0]
    fovy = size[1] * spacing[1]
    fovz = size[2] * spacing[2]

    dimx = int(round(fovx / 1.0))
    dimy = int(round(fovy / 1.0))
    dimz = int(round(fovz / 1.0))

    newimage_arr = resampleit(oldimage_arr, (dimz, dimy, dimx), isseg=isseg)
    newimage = sitk.GetImageFromArray(newimage_arr)
    newimage.SetDirection(coord)
    return newimage


def func_register(img_original, img_template, img_registered):
    # img_original : original T2 or FLAIR image file
    # img_template : isovoxel T1C file used for registration template
    # img_registered :file name that stores registered (isovoxel) T2 or FLAIR file
    coregi_iso = FLIRT(bins=640, cost_func='mutualinfo', dof=12, output_type="NIFTI_GZ", verbose=0,
                       datatype='float', interp='trilinear',
                       in_file=img_original,
                       reference=img_template,
                       out_file=img_registered)
    coregi_iso.run()


def func_n4bias(img_original, img_biascorrected):
    n4_correct = N4BiasFieldCorrection(dimension=3, bspline_fitting_distance=300, bspline_order=3,
                                       shrink_factor=2, n_iterations=[100, 100, 100, 100],
                                       convergence_threshold=1e-06,
                                       terminal_output='none',
                                       input_image=img_original,
                                       output_image=img_biascorrected)

    n4_correct.run()


# SI normalization for U-Net segmentation
def func_norm_unet(img_arr, brain_mask_arr):
    img_arr = img_arr * brain_mask_arr  # Assign 0 to background pixels

    mean = np.mean(img_arr[img_arr > 0])
    sd = np.std(img_arr[img_arr > 0])

    img_norm_arr = (img_arr - mean) / sd
    img_norm_arr[img_norm_arr < - 5] = - 5
    img_norm_arr[img_norm_arr > 5] = 5
    img_norm_arr = img_norm_arr / 10
    img_norm_arr = img_norm_arr + 0.5
    img_norm_arr = img_norm_arr * brain_mask_arr  ## Assign 0 to the background

    return img_norm_arr


# Background crop -> resample to 128 x 128 x 128
def func_get_cropdown_info(img_arr):
    # finding a 3D bounding box

    x_len, y_len, z_len = img_arr.shape

    for x in range(x_len):
        if np.sum(img_arr[x, :, :]) > 0:
            x_min = x
            break

    for x in range(1, x_len):
        if np.sum(img_arr[(x_len - x), :, :]) > 0:
            x_max = x_len - x
            break

    for y in range(y_len):
        if np.sum(img_arr[:, y, :]) > 0:
            y_min = y
            break

    for y in range(1, y_len):
        if np.sum(img_arr[:, (y_len - y), :]) > 0:
            y_max = y_len - y
            break

    for z in range(z_len):
        if np.sum(img_arr[:, :, z]) > 0:
            z_min = z
            break

    for z in range(1, z_len):
        if np.sum(img_arr[:, :, (z_len - z)]) > 0:
            z_max = z_len - z
            break

    # calculate center location and width of even number
    x_center = int(np.mean((x_min, x_max)))
    x_width = int((x_max - x_min) / 2) * 2
    y_center = int(np.mean((y_min, y_max)))
    y_width = int((y_max - y_min) / 2) * 2
    width = max(x_width, y_width)  # to make XY plane square, from rectangle

    z_center = int(np.mean((z_min, z_max)))
    z_width = int((z_max - z_min) / 2) * 2

    x_min = int(x_center - width / 2)
    x_max = int(x_center + width / 2)
    y_min = int(y_center - width / 2)
    y_max = int(y_center + width / 2)

    z_min = int(z_center - z_width / 2)
    z_max = int(z_center + z_width / 2)

    # Give ~6 pixels of background margin for xy plane, ~2 pixels for z-axis, within original image border
    img_arr_crop = img_arr[max(0, x_min - 6):min(x_len, x_max + 6),
                   max(0, y_min - 6):min(y_len, y_max + 6),
                   max(0, z_min - 2):min(z_len, z_max + 2)]

    res_orig = img_arr_crop.shape  # this image is to resampled to 128x128x128.

    return_list = [[res_orig,
                    max(0, x_min - 6), min(x_len, x_max + 6), x_len,  ## Give some background margin
                    max(0, y_min - 6), min(y_len, y_max + 6), y_len,
                    max(0, z_min - 2), min(z_len, z_max + 2), z_len]]
    return_df = pd.DataFrame(return_list)
    return_df.columns = ('res_orig',
                         'x_min2', 'x_max2', 'x_len',
                         'y_min2', 'y_max2', 'y_len',
                         'z_min2', 'z_max2', 'z_len')

    return return_df


def func_img_cropdown(img_arr, cropdown_info):
    x_min2 = cropdown_info.loc[:, 'x_min2'].iloc[0]
    x_max2 = cropdown_info.loc[:, 'x_max2'].iloc[0]
    y_min2 = cropdown_info.loc[:, 'y_min2'].iloc[0]
    y_max2 = cropdown_info.loc[:, 'y_max2'].iloc[0]
    z_min2 = cropdown_info.loc[:, 'z_min2'].iloc[0]
    z_max2 = cropdown_info.loc[:, 'z_max2'].iloc[0]

    img_arr_crop = img_arr[x_min2:x_max2,
                   y_min2:y_max2,
                   z_min2:z_max2]
    img_arr_cropdown = resampleit(img_arr_crop, (128, 128, 128), isseg=False)

    return img_arr_cropdown


def func_img_proc(T1C_bet, T2_bet, FLAIR_bet, mask_T1C_bet,
                  T1C_isovoxel, T2_isovoxel, FLAIR_isovoxel, mask_T1C_bet_iso,
                  T1C_corrected, T2_corrected, FLAIR_corrected):
    t1c_isovoxel = func_resample_isovoxel(T1C_bet, isseg=False)
    sitk.WriteImage(t1c_isovoxel, T1C_isovoxel)

    bmask_isovoxel = func_resample_isovoxel(mask_T1C_bet, isseg=True)
    sitk.WriteImage(bmask_isovoxel, mask_T1C_bet_iso)

    print("resampling T1C & brain mask - completed")

    func_register(T2_bet, T1C_isovoxel, T2_isovoxel)
    print("register T2 to T1C_isovoxel - completed")

    func_register(FLAIR_bet, T1C_isovoxel, FLAIR_isovoxel)
    print("register FLAIR to T1C_isovoxel - completed")

    func_n4bias(T1C_isovoxel, T1C_corrected)
    print("T1C bias correction done...")
    func_n4bias(T2_isovoxel, T2_corrected)
    print("T2 bias correction done...")
    func_n4bias(FLAIR_isovoxel, FLAIR_corrected)
    print("FLAIR bias correction done...")


def func_norm_model1(T1C_corrected, FLAIR_corrected, mask_T1C_bet_iso):
    t1c_corrected = nb.load(T1C_corrected)
    t1c_corrected_arr = t1c_corrected.get_data()
    flair_corrected = nb.load(FLAIR_corrected)
    flair_corrected_arr = flair_corrected.get_data()
    brain_mask = nb.load(mask_T1C_bet_iso)
    brain_mask_arr = brain_mask.get_data()

    # normalization for UNet  -> resize to 128 x 128 x 128

    t1c_norm_unet_arr = func_norm_unet(t1c_corrected_arr, brain_mask_arr)
    flair_norm_unet_arr = func_norm_unet(flair_corrected_arr, brain_mask_arr)

    cropdown_info = func_get_cropdown_info(t1c_norm_unet_arr)
    t1c_norm_unet_cropdown_arr = func_img_cropdown(t1c_norm_unet_arr, cropdown_info)
    flair_norm_unet_cropdown_arr = func_img_cropdown(flair_norm_unet_arr, cropdown_info)

    print("Image SI normalization & resizing :  done...")

    return (t1c_norm_unet_cropdown_arr, flair_norm_unet_cropdown_arr, cropdown_info)

T1C_bet_file = 'D:/python日月光华深度学习/t1c_bet.nii.gz'
T2_bet_file = 'D:/python日月光华深度学习/t2_bet.nii.gz'
# T2_registered = 'D:/python日月光华深度学习/t2_registered'
FLAIR_bet_file = 'D:/python日月光华深度学习/flair_bet.nii.gz'
brainmask_T1C_file = 'D:/python日月光华深度学习/t1c_mask.nii'

# func_register(T2_bet_file, T1C_bet_file , T2_registered)

func_img_proc(T1C_bet_file, T2_bet_file, FLAIR_bet_file, brainmask_T1C_file)