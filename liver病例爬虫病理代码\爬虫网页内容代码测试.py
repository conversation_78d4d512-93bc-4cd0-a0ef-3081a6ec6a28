#%% 下载一个网页全部内容
import requests
from bs4 import BeautifulSoup
import chardet
import pandas as pd

def fetch_web_content(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36'
    }
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        # 检测编码
        encoding = chardet.detect(response.content)['encoding']
        response.encoding = encoding  # 设置正确的编码
        return BeautifulSoup(response.text, 'html.parser').get_text(separator='\n', strip=True)
    else:
        raise Exception(f'请求失败，状态码: {response.status_code}')

def save_to_excel(content, file_path):
    # 将内容分行并转换为DataFrame
    lines = content.split('\n')
    df = pd.DataFrame(lines, columns=['内容'])
    df.to_excel(file_path, index=False)

def main():
    url = 'https://medical.suda.edu.cn/'
    excel_file_path = r'D:\HCC\output.xlsx'  # 保存的Excel文件路径
    
    try:
        web_content = fetch_web_content(url)
        print(web_content)  # 打印网页内容
        
        save_to_excel(web_content, excel_file_path)  # 保存为Excel文件
        print(f'内容已保存到: {excel_file_path}')
    except Exception as e:
        print(e)

if __name__ == '__main__':
    main()

#%%下载图片
import requests
from lxml import html
import os
url = "https://pubmed.ncbi.nlm.nih.gov/34589181/"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.289 Safari/537.36",
    "Referer": url  # 添加Referer头信息
}

response = requests.get(url, headers=headers)
tree = html.fromstring(response.content)

# print(html.tostring(tree, encoding='utf-8').decode('utf-8'))


# 获取图片网址
element_text = tree.xpath('//*[@id="slides-container"]/figure/a/@href')
print(element_text)

# 创建保存图片的目录

save_dir = r'd:\data\1'
if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 遍历图片网址,下载并保存图片
# 遍历图片网址,下载并保存图片
for i, img_url in enumerate(element_text):
    # 构建图片文件名
    img_filename = f'image_{i+1}.jpg'
    img_path = os.path.join(save_dir, img_filename)

    # 下载图片并保存
    img_response = requests.get(img_url, headers=headers) 
    
    # 打印状态码
    print(f'Status code for {img_url}: {img_response.status_code}')
    
    # 检查状态码是否为200（成功）
    if img_response.status_code == 200:
        with open(img_path, 'wb') as f:
            f.write(img_response.content)
        print(f'Image {i+1} saved: {img_filename}')
    else:
        print(f'Failed to download image {i+1}')
#%% 下载pdf
import requests
from lxml import html
import os
url = "https://pubmed.ncbi.nlm.nih.gov/34589181/" 

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.289 Safari/537.36",
    "Referer": url  # 添加Referer头信息
}

response = requests.get(url, headers=headers)
tree = html.fromstring(response.content)

# print(html.tostring(tree, encoding='utf-8').decode('utf-8'))

# 获取图片网址
path1 = tree.xpath('//*[@id="full-view-identifiers"]/li[2]/span/a/text()')
print(path1)
text = path1[0].strip()
print(text) 

url2 = "https://www.ncbi.nlm.nih.gov/pmc/articles/" + text +"/"
url2
response2 = requests.get(url2, headers=headers)
tree2 = html.fromstring(response2.content)
path2 = tree2.xpath('//*[@id="main-content"]/aside/div/section[1]/ul/li/a/@href')
print(path2)

path3 = "https://www.ncbi.nlm.nih.gov" + path2[0]
path3_list = [path3]
print(path3_list)

# 创建保存图片的目录

save_dir = r'd:\data\1'
if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 遍历图片网址,下载并保存图片
for i, img_url in enumerate(path3_list):
    print(img_url)

    # 构建图片文件名
    img_filename = f'image_{i+1}.pdf'
    img_path = os.path.join(save_dir, img_filename)

    # 下载图片并保存
    img_response = requests.get(img_url, headers=headers) 
    
    # 打印状态码
    print(f'Status code for {img_url}: {img_response.status_code}')
    
    # 检查状态码是否为200（成功）
    if img_response.status_code == 200:
        with open(img_path, 'wb') as f:
            f.write(img_response.content)
        print(f'Image {i+1} saved: {img_filename}')
    else:
        print(f'Failed to download image {i+1}')
 
#%% 获取IE浏览器内容
import requests
from lxml import html
import os
# url = "https://pubmed.ncbi.nlm.nih.gov/34589181/"
url = "https://yjs.suda.edu.cn/main.htm"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.289 Safari/537.36",
    "Referer": url  # 添加Referer头信息
}

response = requests.get(url, headers=headers)
tree = html.fromstring(response.content)
print(html.tostring(tree, encoding='utf-8').decode('utf-8'))

element_text = tree.xpath('/html/body/section[2]/div/div/div[1]/div[2]/div[2]/div/div/ul/li[1]/a/@href')
print(element_text)

# element_text = tree.xpath("//div[@class='post-title']/text()")
# text = element_text[0].strip()
# print(text) 

# %%

import pandas as pd

def process_data(input_file, output_file):
    # 读取原始Excel文件
    df = pd.read_excel(input_file)

    # 检查是否包含所需的列
    required_columns = ['姓名', '性别', '工作单位', '职称', '手机号码', '身份证号码', '开户行', '银行卡号']
    
    # 过滤出所需的列
    processed_df = df[required_columns]

    # 保存到新Excel文件
    processed_df.to_excel(output_file, index=False)

def main():
    input_file_path = r'K:\2024智能影像学组会议\123.xlsx'  # 原始Excel文件路径
    output_file_path = r'K:\2024智能影像学组会议\processed_output.xlsx'  # 处理后的Excel文
    
    try:
        # 处理数据并保存到新表格
        process_data(input_file_path, output_file_path)
        print(f'处理后的数据已保存到: {output_file_path}')
    except Exception as e:
        print(e)

if __name__ == '__main__':
    main()
# %%
