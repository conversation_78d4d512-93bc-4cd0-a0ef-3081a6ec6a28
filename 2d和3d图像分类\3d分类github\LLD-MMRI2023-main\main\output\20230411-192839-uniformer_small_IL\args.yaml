amp: false
angle: 45
apex_amp: false
batch_size: 8
bce_loss: false
bce_target_thresh: null
bn_eps: null
bn_momentum: null
bn_tf: false
checkpoint_hist: 1
clip_grad: null
clip_mode: norm
cooldown_epochs: 10
crop_size:
- 14
- 112
- 112
data_dir: data/classification_dataset/images/
decay_epochs: 100
decay_rate: 0.1
dist_bn: reduce
drop: 0.0
drop_block: null
drop_path: 0.2
epoch_repeats: 0.0
epochs: 300
eval_metric: f1
experiment: ''
flip_prob: 0.5
gp: null
img_size:
- 16
- 128
- 128
initial_checkpoint: ''
interpolation: ''
local_rank: 0
log_interval: 25
log_wandb: false
lr: 0.0001
lr_cycle_decay: 0.5
lr_cycle_limit: 1
lr_cycle_mul: 1.0
lr_k_decay: 1.0
lr_noise: null
lr_noise_pct: 0.67
lr_noise_std: 1.0
min_lr: 1.0e-05
model: uniformer_small_IL
model_ema: false
model_ema_decay: 0.9998
model_ema_force_cpu: false
momentum: 0.9
native_amp: false
no_ddp_bb: false
no_resume_opt: false
num_classes: 7
opt: adamw
opt_betas: null
opt_eps: null
output: output/
patience_epochs: 10
pin_mem: false
pretrained: false
rcprob: 0.25
recovery_interval: 0
report_metrics:
- acc
- f1
- recall
- precision
- kappa
reprob: 0.25
resume: ''
sched: cosine
seed: 42
smoothing: 0
start_epoch: null
sync_bn: false
torchscript: false
train_anno_file: data/classification_dataset/labels/train_fold1.txt
train_transform_list:
- random_crop
- z_flip
- x_flip
- y_flip
- rotation
val_anno_file: data/classification_dataset/labels/val_fold1.txt
val_transform_list:
- center_crop
validation_batch_size: null
warmup_epochs: 5
warmup_lr: 1.0e-06
weight_decay: 0.05
worker_seeding: all
workers: 8
