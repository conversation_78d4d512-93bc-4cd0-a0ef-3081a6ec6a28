#!/usr/bin/env python
# coding: utf-8

#%%In[5]:
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils import data
import numpy as np
import matplotlib
matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
# get_ipython().run_line_magic('matplotlib', 'inline')
import torchvision
from torchvision import transforms
import os
import glob
from PIL import Image

# In[6]:

torch.__version__

## In[7]:

BATCH_SIZE = 8

# In[3]:
pil_img = Image.open(r'G:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\training\00001.png')
np_img = np.array(pil_img)
plt.imshow(np_img)
plt.show()

# In[4]:

pil_img = Image.open(r'G:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\training\00001_matte.png')
np_img = np.array(pil_img)
plt.imshow(np_img)
plt.show()

## In[5]:

np.unique(np_img)
np_img.max(), np_img.min()
np_img.shape

all_pics = glob.glob(r'G:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\training\*.png')

all_pics[:5]


# In[10]:

images = [p for p in all_pics if 'matte' not in p]

len(images)

annotations = [p for p in all_pics if 'matte' in p]

len(annotations)


# In[14]:

images[:5]
annotations[:5]
annotations[-5:]

# In[17]:

np.random.seed(2021)
index = np.random.permutation(len(images))


# In[18]:

images = np.array(images)[index]
images[:5]

# In[20]:

anno = np.array(annotations)[index]
anno[:5]

# In[22]:

all_test_pics = glob.glob(r'G:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\*.png')

# In[23]:

test_images = [p for p in all_test_pics if 'matte' not in p]
test_anno = [p for p in all_test_pics if 'matte' in p]


## In[24]:


transform = transforms.Compose([
                    transforms.Resize((256, 256)),
                    transforms.ToTensor(),
])


# In[25]:


class Portrait_dataset(data.Dataset):
    def __init__(self, img_paths, anno_paths):
        self.imgs = img_paths
        self.annos = anno_paths
        
    def __getitem__(self, index):
        img = self.imgs[index]
        anno = self.annos[index]
        
        pil_img = Image.open(img)    
        img_tensor = transform(pil_img)
        
        pil_anno = Image.open(anno)    
        anno_tensor = transform(pil_anno)
        anno_tensor = torch.squeeze(anno_tensor).type(torch.long)
        anno_tensor[anno_tensor > 0] = 1
        
        return img_tensor, anno_tensor
    
    def __len__(self):
        return len(self.imgs)


## In[26]:


train_dataset = Portrait_dataset(images, anno)

test_dataset = Portrait_dataset(test_images, test_anno)


train_dl = data.DataLoader(
                           train_dataset,
                           batch_size=BATCH_SIZE,
                           shuffle=True,
)


test_dl = data.DataLoader(
                          test_dataset,
                          batch_size=BATCH_SIZE,
)


## In[30]:

imgs_batch, annos_batch = next(iter(train_dl))
imgs_batch.shape
imgs_batch[0].shape

img = imgs_batch[0].permute(1,2,0).numpy()
anno = annos_batch[0].numpy()

# In[32]:

plt.subplot(1,2,1)
plt.imshow(img)
plt.subplot(1,2,2)
plt.imshow(anno)

##In[33]:

class Downsample(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(Downsample, self).__init__()
        self.conv_relu = nn.Sequential(
                            nn.Conv2d(in_channels, out_channels, 
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True),
                            nn.Conv2d(out_channels, out_channels, 
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True)
            )
        self.pool = nn.MaxPool2d(kernel_size=2)
    def forward(self, x, is_pool=True):
        if is_pool:
            x = self.pool(x)
        x = self.conv_relu(x)
        return x

# In[34]:


class Upsample(nn.Module):
    def __init__(self, channels):
        super(Upsample, self).__init__()
        self.conv_relu = nn.Sequential(
                            nn.Conv2d(2*channels, channels, 
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True),
                            nn.Conv2d(channels, channels,  
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True)
            )
        self.upconv_relu = nn.Sequential(
                               nn.ConvTranspose2d(channels, 
                                                  channels//2, 
                                                  kernel_size=3,
                                                  stride=2,
                                                  padding=1,
                                                  output_padding=1),
                               nn.ReLU(inplace=True)
            )
        
    def forward(self, x):
        x = self.conv_relu(x)
        x = self.upconv_relu(x)
        return x


# In[35]:


class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.down1 = Downsample(3, 64)
        self.down2 = Downsample(64, 128)
        self.down3 = Downsample(128, 256)
        self.down4 = Downsample(256, 512)
        self.down5 = Downsample(512, 1024)
        
        self.up = nn.Sequential(
                               nn.ConvTranspose2d(1024, 
                                                  512, 
                                                  kernel_size=3,
                                                  stride=2,
                                                  padding=1,
                                                  output_padding=1),
                               nn.ReLU(inplace=True)
            )
        
        self.up1 = Upsample(512)
        self.up2 = Upsample(256)
        self.up3 = Upsample(128)
        
        self.conv_2 = Downsample(128, 64)
        self.last = nn.Conv2d(64, 2, kernel_size=1)

    def forward(self, x):
        x1 = self.down1(x, is_pool=False)
        x2 = self.down2(x1)
        x3 = self.down3(x2)
        x4 = self.down4(x3)
        x5 = self.down5(x4)
        
        x5 = self.up(x5)
        
        x5 = torch.cat([x4, x5], dim=1)           # 32*32*1024
        x5 = self.up1(x5)                         # 64*64*256)
        x5 = torch.cat([x3, x5], dim=1)           # 64*64*512  
        x5 = self.up2(x5)                         # 128*128*128
        x5 = torch.cat([x2, x5], dim=1)           # 128*128*256
        x5 = self.up3(x5)                         # 256*256*64
        x5 = torch.cat([x1, x5], dim=1)           # 256*256*128
        
        x5 = self.conv_2(x5, is_pool=False)       # 256*256*64
        
        x5 = self.last(x5)                        # 256*256*3
        return x5


## In[36]:

model = Net()

# In[37]:

if torch.cuda.is_available():
    model.to('cuda')

# In[38]:

loss_fn = nn.CrossEntropyLoss()

# In[39]:

from torch.optim import lr_scheduler
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

## In[40]:计算acc和loss

def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    
    model.train()
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
    exp_lr_scheduler.step()
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / (total*256*256)

    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    
    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / (test_total*256*256)
        
    print('epoch: ', epoch, 
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
             )
        
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc

epochs = 5

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)

#查看训练的设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Training on: ", device)

##计算iou值和acc值

def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []

    model.train()
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.true_divide(torch.sum(intersection),
                                          torch.sum(union))
            epoch_iou.append(batch_iou.cpu())

    exp_lr_scheduler.step()
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / (total * 256 * 256)

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    epoch_test_iou = []

    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
               x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.true_divide(torch.sum(intersection),
                                          torch.sum(union))
            epoch_test_iou.append(batch_iou.cpu())

    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / (test_total * 256 * 256)

    print('epoch: ', epoch,
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean([iou.numpy() for iou in epoch_iou]), 3))
    # print()
    print('     ', 'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
          'test_iou:', round(np.mean([iou.numpy() for iou in epoch_test_iou]), 3)
          )
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc


# In[44]:

epochs = 5

# In[45]:

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)

#查看训练的设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Training on: ", device)

## 保存模型

PATH = r'H:\data\unet_model.pth' #路径不能有中文
torch.save(model.state_dict(), PATH)

# 测试模型
my_model = Net()
my_model.load_state_dict(torch.load(PATH))

## In[71]:

num=3

image, mask = next(iter(test_dl))
pred_mask = my_model(image)

plt.figure(figsize=(10, 10))
for i in range(num):
    plt.subplot(num, 3, i*num+1)
    plt.imshow(image[i].permute(1,2,0).cpu().numpy())
    plt.subplot(num, 3, i*num+2)
    plt.imshow(mask[i].cpu().numpy())
    plt.subplot(num, 3, i*num+3)
    plt.imshow(torch.argmax(pred_mask[i].permute(1,2,0), axis=-1).detach().numpy())

## 在train数据上测试

image, mask = next(iter(train_dl))
pred_mask = my_model(image)

plt.figure(figsize=(10, 10))
for i in range(num):
    plt.subplot(num, 3, i*num+1)
    plt.imshow(image[i].permute(1,2,0).cpu().numpy())
    plt.subplot(num, 3, i*num+2)
    plt.imshow(mask[i].cpu().numpy())
    plt.subplot(num, 3, i*num+3)
    plt.imshow(torch.argmax(pred_mask[i].permute(1,2,0), axis=-1).detach().numpy())

##%%批量图-模型预测
import os
import cv2
import numpy as np

def add_mask2image_binary(images_path, masks_path):
# Add binary masks to images
    for img_item in os.listdir(images_path):
        print(img_item)
        img_path = os.path.join(images_path, img_item)        #image和mask的文件名字要一样
        pil_imgs = Image.open(img_path)
        img_tensor = transform(pil_imgs)
        img_tensor.shape
        img_tensor_batch = torch.unsqueeze(img_tensor,0)
        img_tensor_batch.shape
        pred = my_model(img_tensor_batch)
        pred.shape
        pred = torch.argmax(pred[0].permute(1,2,0),axis=-1).numpy()
        pred = pred * 255.0
        cv2.imwrite(os.path.join(masks_path, img_item),pred)
       
images_path = r'H:\data\predimg'      #注意路径r'H:\data\predimg'或者'H:/data/predimg'，不能有中文
masks_path = r'H:\data\predmask'       #注意路径r'H:\data\predimg'或者'H:/data/predimg'，不能有中文

add_mask2image_binary(images_path, masks_path)



