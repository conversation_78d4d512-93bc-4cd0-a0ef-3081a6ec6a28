import os
import re
import json
from datetime import datetime

def rename_files(directory_path, create_backup=True):
    """
    重命名文件：去掉文件名中第2个和第3个"-"之前的数字
    """
    # 确保路径存在
    if not os.path.exists(directory_path):
        print(f"目录不存在: {directory_path}")
        return
    
    # 获取目录中的所有文件
    files = os.listdir(directory_path)
    
    renamed_count = 0
    rename_log = {}  # 记录重命名信息
    
    for filename in files:
        # 跳过目录
        if os.path.isdir(os.path.join(directory_path, filename)):
            continue
            
        # 分析文件名结构
        parts = filename.split('-')
        
        # 确保至少有3个"-"分隔的部分
        if len(parts) < 4:
            print(f"跳过文件 {filename}：没有足够的'-'分隔符")
            continue
        
        # 构建新文件名：保留第1部分，去掉第2和第3部分前的数字，保留其余部分
        new_parts = [parts[0]]  # 保留第一部分
        
        # 处理第2部分：去掉前面的数字
        part2_no_digits = re.sub(r'^\d+', '', parts[1])
        if part2_no_digits:  # 如果去掉数字后还有内容
            new_parts.append(part2_no_digits)
        
        # 处理第3部分：去掉前面的数字
        part3_no_digits = re.sub(r'^\d+', '', parts[2])
        if part3_no_digits:  # 如果去掉数字后还有内容
            new_parts.append(part3_no_digits)
        
        # 添加剩余部分
        new_parts.extend(parts[3:])
        
        # 构建新文件名
        new_filename = '-'.join(new_parts)
        
        # 如果文件名有变化，执行重命名
        if new_filename != filename:
            old_path = os.path.join(directory_path, filename)
            new_path = os.path.join(directory_path, new_filename)
            
            # 检查新文件名是否已存在
            if os.path.exists(new_path):
                print(f"警告：目标文件已存在，跳过: {new_filename}")
                continue
            
            try:
                os.rename(old_path, new_path)
                print(f"重命名: {filename} -> {new_filename}")
                rename_log[new_filename] = filename  # 记录新名称 -> 原名称的映射
                renamed_count += 1
            except Exception as e:
                print(f"重命名失败 {filename}: {str(e)}")
    
    # 保存重命名日志
    if create_backup and renamed_count > 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(directory_path, f"rename_backup_{timestamp}.json")
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump({
                'directory': directory_path,
                'timestamp': timestamp,
                'rename_count': renamed_count,
                'rename_map': rename_log
            }, f, ensure_ascii=False, indent=2)
        print(f"\n重命名日志已保存到: {backup_file}")
    
    print(f"\n总共重命名了 {renamed_count} 个文件")

def restore_filenames(backup_file):
    """
    从备份文件恢复原始文件名
    """
    if not os.path.exists(backup_file):
        print(f"备份文件不存在: {backup_file}")
        return
    
    # 读取备份信息
    with open(backup_file, 'r', encoding='utf-8') as f:
        backup_data = json.load(f)
    
    directory_path = backup_data['directory']
    rename_map = backup_data['rename_map']
    
    print(f"从备份恢复文件名")
    print(f"目录: {directory_path}")
    print(f"备份时间: {backup_data['timestamp']}")
    print(f"需要恢复的文件数: {len(rename_map)}")
    print("-" * 80)
    
    restored_count = 0
    
    for new_name, old_name in rename_map.items():
        new_path = os.path.join(directory_path, new_name)
        old_path = os.path.join(directory_path, old_name)
        
        if not os.path.exists(new_path):
            print(f"警告：文件不存在，跳过: {new_name}")
            continue
        
        if os.path.exists(old_path):
            print(f"警告：原始文件名已存在，跳过: {old_name}")
            continue
        
        try:
            os.rename(new_path, old_path)
            print(f"恢复: {new_name} -> {old_name}")
            restored_count += 1
        except Exception as e:
            print(f"恢复失败 {new_name}: {str(e)}")
    
    print(f"\n总共恢复了 {restored_count} 个文件")

def preview_rename(directory_path):
    """
    预览重命名结果，不实际执行
    """
    if not os.path.exists(directory_path):
        print(f"目录不存在: {directory_path}")
        return
    
    files = os.listdir(directory_path)
    
    print("预览重命名结果（前5个）：")
    print("-" * 80)
    
    preview_count = 0
    total_changes = 0
    
    for filename in files:
        if os.path.isdir(os.path.join(directory_path, filename)):
            continue
            
        parts = filename.split('-')
        
        if len(parts) < 4:
            continue
        
        new_parts = [parts[0]]
        
        part2_no_digits = re.sub(r'^\d+', '', parts[1])
        if part2_no_digits:
            new_parts.append(part2_no_digits)
        
        part3_no_digits = re.sub(r'^\d+', '', parts[2])
        if part3_no_digits:
            new_parts.append(part3_no_digits)
        
        new_parts.extend(parts[3:])
        new_filename = '-'.join(new_parts)
        
        if new_filename != filename:
            total_changes += 1
            if preview_count < 5:
                print(f"{filename} -> {new_filename}")
                preview_count += 1
    
    if total_changes > 5:
        print(f"\n... 还有 {total_changes - 5} 个文件将被重命名")

if __name__ == "__main__":
    # Windows路径
    directory_path = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output"
    
    print("=" * 80)
    print("文件重命名工具（带恢复功能）")
    print("=" * 80)
    print(f"目标目录: {directory_path}")
    print("\n功能选择:")
    print("1. 重命名文件（去掉第2个和第3个'-'之前的数字）")
    print("2. 从备份恢复原始文件名")
    print("=" * 80)
    
    choice = input("\n请选择操作 (1/2): ")
    
    if choice == '1':
        # 重命名操作
        print("\n功能: 去掉文件名中第2个和第3个'-'之前的数字")
        print("=" * 80)
        
        # 首先预览
        print("\n1. 预览模式:")
        preview_rename(directory_path)
        
        # 询问是否执行
        print("\n" + "=" * 80)
        user_input = input("\n是否执行重命名操作？(y/n): ")
        
        if user_input.lower() == 'y':
            print("\n2. 执行重命名:")
            rename_files(directory_path, create_backup=True)
        else:
            print("操作已取消")
    
    elif choice == '2':
        # 恢复操作
        print("\n查找备份文件...")
        backup_files = [f for f in os.listdir(directory_path) if f.startswith('rename_backup_') and f.endswith('.json')]
        
        if not backup_files:
            print("未找到备份文件")
        else:
            print("\n找到以下备份文件:")
            for i, bf in enumerate(backup_files, 1):
                print(f"{i}. {bf}")
            
            file_choice = input("\n请选择要恢复的备份文件编号: ")
            try:
                selected_file = backup_files[int(file_choice) - 1]
                backup_path = os.path.join(directory_path, selected_file)
                
                confirm = input(f"\n确认从 {selected_file} 恢复？(y/n): ")
                if confirm.lower() == 'y':
                    restore_filenames(backup_path)
                else:
                    print("操作已取消")
            except (ValueError, IndexError):
                print("无效的选择")
    else:
        print("无效的选择")