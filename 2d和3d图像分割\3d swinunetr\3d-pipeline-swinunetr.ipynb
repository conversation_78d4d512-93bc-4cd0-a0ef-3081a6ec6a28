{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.12", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "gpu", "dataSources": [{"sourceId": 6322203, "sourceType": "datasetVersion", "datasetId": 3638341}], "dockerImageVersionId": 30636, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": "## Setup environment", "metadata": {}}, {"cell_type": "code", "source": "!pip3 install monai\n!python3 -c \"import monai\" || pip3 install \"monai[nibabel, tqdm, skimage, einops]\"\n!python3 -c \"import matplotlib\" || pip3 install -q matplotlib\n!python3 -c \"import pytorch_lightning\" || pip3 install pytorch_lightning\n%matplotlib inline\n", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "!pip3 install 'monai[einops]'", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Pipeline SwinUNETR", "metadata": {}}, {"cell_type": "markdown", "source": "## Setup imports", "metadata": {}}, {"cell_type": "code", "source": "import pytorch_lightning\nimport monai\nimport numpy as np\nimport pandas as pd\n\nfrom typing import Union\nfrom PIL import Image\nfrom monai.utils import set_determinism\nfrom monai.utils.enums import MetricReduction\nfrom monai.transforms import (\n    AsDiscrete,\n    ScaleIntensityRangePercentilesd,\n    EnsureChannelFirstd,\n    Compose,\n    CropForegroundd,\n    LoadImaged,\n    Orientationd,\n    RandCropByPosNegLabeld,\n    ScaleIntensityRanged,\n    Spacingd,\n    EnsureType,\n    MapTransform,\n    Activations,\n    Activationsd,\n    Invertd,\n    NormalizeIntensityd,\n    RandFlipd,\n    RandScaleIntensityd,\n    RandShiftIntensityd,\n    RandSpatialCropd,\n    EnsureTyped,\n    SpatialPadd,\n    SaveImage,\n    RandRotate90d,\n    ConcatItemsd,\n    DeleteItemsd,\n)\nfrom monai.networks.utils import one_hot\nfrom monai.networks.nets import SwinUNETR,UNETR,SegResNet\nfrom monai.networks.layers import Norm\nfrom monai.metrics import DiceMetric,HausdorffDistanceMetric,ConfusionMatrixMetric, compute_hausdorff_distance, CumulativeIterationMetric\nfrom monai.losses import <PERSON>ceCELoss,DiceLoss, TverskyLoss\nfrom monai.inferers import sliding_window_inference\n\nfrom monai.data import PersistentDataset, list_data_collate, decollate_batch, DataLoader, load_decathlon_datalist, CacheDataset # NiftiSaver\n\nfrom monai.config import print_config\nfrom monai.apps import download_and_extract,DecathlonDataset\nfrom monai.handlers.utils import from_engine\n\nimport torch\nimport matplotlib.pyplot as plt\nimport tempfile\nimport shutil\nimport os\nimport glob\nimport nibabel as nib\n\nfrom pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint\nfrom pytorch_lightning.callbacks.early_stopping import EarlyStopping\nfrom pytorch_lightning.callbacks.timer import Timer\n\nos.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\ntorch.backends.cudnn.benchmark = True\ntorch.backends.cudnn.enabled = True\nprint_config()", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Setup data directory", "metadata": {}}, {"cell_type": "code", "source": "directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n# root_dir = tempfile.mkdtemp() if directory is None else directory\nroot_dir=os.path.join(os.getcwd(), 'Best_modell/')\nprint(root_dir)\n\nroi_size = (96, 96, 96)\nsw_batch_size = 1", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Creating a json file", "metadata": {}}, {"cell_type": "code", "source": "file_paths1 = glob.glob('/kaggle/input/brats2023-part-1' + \"/*\")\nfile_paths1.sort()\n\nfile_names1 = os.listdir('/kaggle/input/brats2023-part-1')\nfile_names1.sort()\n\nt1c, t1n, t2f, t2w, label = [], [], [], [], []\nfor i in range(330):\n    t1c.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t1c.nii'))\n    t1n.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t1n.nii'))\n    t2f.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t2f.nii'))\n    t2w.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t2w.nii'))\n    label.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-seg.nii'))\n\ndata = {\n    't1c': t1c,\n    't1n': t1n,\n    't2f': t2f,\n    't2w': t2w,\n    'label': label\n}", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "file= []\nfor i in range(330):\n    file.append({\"t1c\": data['t1c'][i], \"t1n\": data['t1n'][i], \"t2f\": data['t2f'][i], \"t2w\": data['t2w'][i], \"label\": data['label'][i]})\n    \nfile_json = {\n    \"training\": file\n}\n\nimport json\nfile_path = '/kaggle/working/dataset.json'\nwith open(file_path, 'w') as json_file:\n    json.dump(file_json, json_file)", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Check data shape and visualize", "metadata": {}}, {"cell_type": "code", "source": "num = 15\nflair = nib.load(data['t2f'][num]).get_fdata()\nt1 = nib.load(data['t1n'][num]).get_fdata()\nt1ce = nib.load(data['t1c'][num]).get_fdata()\nt2 = nib.load(data['t2w'][num]).get_fdata()\nmask = nib.load(data['label'][num]).get_fdata()\n\nslice_number = 95\nprint('slice =', slice_number)\nprint('shape =', flair.shape)\n\nfig, (ax1, ax2, ax3, ax4, ax5) = plt.subplots(1,5, figsize = (20, 10))\nax1.imshow(flair[:,:,slice_number], cmap='gray')\nax1.set_title('Image flair')\nax2.imshow(t1[:,:,slice_number], cmap='gray')\nax2.set_title('Image t1')\nax3.imshow(t1ce[:,:,slice_number], cmap='gray')\nax3.set_title('Image t1ce')\nax4.imshow(t2[:,:,slice_number], cmap='gray')\nax4.set_title('Image t2')\nax5.imshow(mask[:,:,slice_number])\nax5.set_title('Mask')", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Define the LightningModule", "metadata": {}}, {"cell_type": "code", "source": "torch.multiprocessing.set_sharing_strategy(\"file_system\")", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "class Net(pytorch_lightning.LightningModule):\n    def __init__(self):\n        super().__init__()\n        \n        self._model = SwinUNETR(\n            in_channels=4,\n            out_channels=4,\n            img_size=(96, 96, 96),\n            feature_size=48,\n            drop_rate=0.0,\n            attn_drop_rate=0.0,\n            dropout_path_rate=0.0,\n            use_checkpoint=True,\n        ).to(device)\n        \n\n        self.loss_function = DiceCELoss(to_onehot_y=True, softmax=True)\n        self.post_pred = AsDiscrete(argmax=True, to_onehot=4)\n        self.post_label = AsDiscrete(to_onehot=4)\n        \n        self.dice_metric = DiceMetric(include_background=False, reduction=\"mean\")\n        self.dice_metric_batch = DiceMetric(include_background=False, reduction=\"mean_batch\")\n        self.haursdoff = HausdorffDistanceMetric(include_background=False, distance_metric='euclidean', \n                                                 percentile=None, directed=False, reduction=\"mean_batch\", get_not_nans=False)\n        self.check_val = 10\n        self.best_val_dice = 0\n        self.best_val_epoch = 0\n        self.epoch_loss_values = []\n        self.metric_values = []\n        self.metric_values_tc = []\n        self.metric_values_wt = []\n        self.metric_values_et = []\n        self.metric_values_back = []\n\n        self.haursdoff_values_tc = []\n        self.haursdoff_values_wt = []\n        self.haursdoff_values_et = []\n        self.haursdoff_values_back = []\n        \n        self.validation_step_outputs = []\n        self.training_step_outputs = []\n    \n\n    def forward(self, x):\n        return self._model(x)\n\n    def prepare_data(self):\n        # setting up the correct data path\n        datasets = \"/kaggle/working/dataset.json\"\n        datalist = load_decathlon_datalist(datasets, True, \"training\")  \n        train_files, val_files = datalist[:300], datalist[300:]\n\n        # setting deterministic training for reproducibility\n        set_determinism(seed=0)\n\n        # defining the data transforms\n        train_transform = Compose(\n            [\n                LoadImaged(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\", \"label\"]),\n                EnsureChannelFirstd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\", \"label\"]),\n                Orientationd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\", \"label\"], axcodes=\"RAS\"),\n                \n                ConcatItemsd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\"], name=\"image\", dim=0),\n                DeleteItemsd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\"]),\n                \n                Spacingd(\n                    keys=[\"image\", \"label\"],\n                    pixdim=(1.0, 1.0, 1.0),\n                    mode=(\"bilinear\", \"nearest\"),\n                ),\n                NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n                RandSpatialCropd(keys=[\"image\", \"label\"], roi_size=[96, 96, 96], random_size=False),\n                RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=0),\n                RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=1),\n                RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=2),\n                RandScaleIntensityd(keys=\"image\", factors=0.1, prob=1.0),\n                RandShiftIntensityd(keys=\"image\", offsets=0.1, prob=1.0),\n            ]\n        )\n        \n        val_transform = Compose(\n            [\n                LoadImaged(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\", \"label\"]),\n                EnsureChannelFirstd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\", \"label\"]),\n                Orientationd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\", \"label\"], axcodes=\"RAS\"),\n                \n                ConcatItemsd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\"], name=\"image\", dim=0),\n                DeleteItemsd(keys=[\"t2f\", \"t1n\", \"t1c\", \"t2w\"]),\n                \n                Spacingd(\n                    keys=[\"image\", \"label\"],\n                    pixdim=(1.0, 1.0, 1.0),\n                    mode=(\"bilinear\", \"nearest\"),\n                ),\n                NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n            ]\n        )\n        \n        persistent_cache = os.path.join(tempfile.mkdtemp(), \"persistent_cache\")\n        \n        self.train_ds = monai.data.PersistentDataset(\n            data=train_files,\n            transform= train_transform,\n            cache_dir=persistent_cache,\n        )\n        self.val_ds = monai.data.PersistentDataset(\n            data=val_files,\n            transform= val_transform,\n            cache_dir=persistent_cache,\n        )\n\n    def train_dataloader(self):\n        train_loader = DataLoader(\n            self.train_ds,\n            batch_size=2,\n            shuffle=True,\n            num_workers=3,\n            pin_memory=True,\n            collate_fn=list_data_collate,\n        )\n        return train_loader\n\n    def val_dataloader(self):\n        val_loader = DataLoader(\n            self.val_ds, \n            batch_size=1, \n            pin_memory=True,\n            shuffle=False,\n            num_workers=3\n        )\n        return val_loader\n\n    def configure_optimizers(self):\n        optimizer = torch.optim.AdamW(\n            self._model.parameters(), lr=1e-4, weight_decay=1e-5\n        )\n        return optimizer\n\n    def training_step(self, batch, batch_idx):\n        images, labels = (batch[\"image\"].cuda(), batch[\"label\"].cuda())\n        output = self.forward(images)\n        loss = self.loss_function(output, labels)\n        tensorboard_logs = {\"train_loss\": loss.item()}\n        self.log(\"train_loss\", loss.item())\n        self.training_step_outputs.append({\"loss\": loss})\n        return {\"loss\": loss, \"log\": tensorboard_logs}\n    \n    def on_train_epoch_end(self):\n        avg_loss = torch.stack([x[\"loss\"] for x in self.training_step_outputs]).mean()\n        self.epoch_loss_values.append(avg_loss.detach().cpu().numpy())\n        self.training_step_outputs.clear()\n\n    def validation_step(self, batch, batch_idx):\n        images, labels = batch[\"image\"], batch[\"label\"]\n\n        outputs = sliding_window_inference(images, roi_size, sw_batch_size, self.forward)\n\n        loss = self.loss_function(outputs, labels)\n        outputs = [self.post_pred(i) for i in decollate_batch(outputs)]\n        labels = [self.post_label(i) for i in decollate_batch(labels)]\n        \n        self.dice_metric(y_pred=outputs, y=labels)\n        self.dice_metric_batch(y_pred = outputs,y = labels)\n        self.haursdoff(y_pred = outputs,y = labels)\n        \n        d = {\"val_loss\": loss, \"val_number\": len(outputs)}\n        self.validation_step_outputs.append(d)\n    \n        return {\"val_loss\": loss, \"val_number\": len(outputs)}\n\n\n    def on_validation_epoch_end(self):\n        val_loss, num_items = 0, 0\n        for output in self.validation_step_outputs:\n            val_loss += output[\"val_loss\"].sum().item()\n            num_items += output[\"val_number\"]\n        \n        mean_val_dice = self.dice_metric.aggregate().item()\n        self.metric_values.append(np.array(mean_val_dice))\n        self.dice_metric.reset()\n        \n        metric_batch = self.dice_metric_batch.aggregate()\n        #metric_back = metric_batch[0].item()\n        #self.metric_values_back.append(metric_back)\n        metric_wt = metric_batch[0].item()\n        self.metric_values_wt.append(metric_wt)\n        metric_et = metric_batch[1].item()\n        self.metric_values_et.append(metric_et)\n        metric_tc = metric_batch[2].item()\n        self.metric_values_tc.append(metric_tc)\n        self.dice_metric_batch.reset()\n        \n        haursdoff = self.haursdoff.aggregate()\n        #hd_back = haursdoff[0].item()\n        #self.haursdoff_values_back.append(hd_back)\n        hd_wt = haursdoff[0].item()\n        self.haursdoff_values_wt.append(hd_wt)\n        hd_et = haursdoff[1].item()\n        self.haursdoff_values_et.append(hd_et)\n        hd_tc = haursdoff[2].item()\n        self.haursdoff_values_tc.append(hd_tc)\n        self.haursdoff.reset()\n        \n        mean_val_loss = torch.tensor(val_loss / num_items)\n\n        tensorboard_logs = {\n            \"val_dice\": mean_val_dice,\n            \"val_loss\": mean_val_loss,\n        }\n        \n        if mean_val_dice > self.best_val_dice:\n            self.best_val_dice = mean_val_dice\n            self.best_val_epoch = self.current_epoch\n            \n            torch.save(\n                self._model, f\"Model_SwinUNETR.pt\")\n        \n        print(\n            f\"current epoch: {self.current_epoch}\"\n            f\"current mean dice: {mean_val_dice:.4f}\"\n            f\"\\nbest mean dice: {self.best_val_dice:.4f} \"\n            f\"at epoch: {self.best_val_epoch}\"\n            f\" tc: {metric_tc:.4f} wt: {metric_wt:.4f} et: {metric_et:.4f}\"\n            \n            f\" hd_tc: {hd_tc:.4f} hd_wt:{hd_wt:.4f} hd_et:{hd_et}\"\n        )\n        self.log(\"val_loss\", mean_val_loss.item()) \n        self.validation_step_outputs.clear() \n        return {\"log\": tensorboard_logs}", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Run the training", "metadata": {}}, {"cell_type": "code", "source": "%%time\n# initialising the LightningModule\nnet = Net()\n\n# set up checkpoints\ncheckpoint_callback = ModelCheckpoint(dirpath=root_dir, filename=\"best_metric_model\", save_last=True)\nearly_stop_callback = EarlyStopping(\n   monitor=\"val_loss\",\n   min_delta=0.00,\n   patience=3,\n   verbose=False,\n   mode='min'\n)\n\n# stop training after 10 hours\ntimer_callback = Timer(duration=\"00:10:00:00\")\n\n# initialising Lightning's trainer\ntrainer = pytorch_lightning.Trainer(\n    precision = 16-mixed,\n    accelerator='gpu',\n    devices=\"auto\",\n    max_epochs=150,\n    check_val_every_n_epoch=net.check_val,\n    callbacks=[checkpoint_callback, early_stop_callback, timer_callback],\n    default_root_dir=root_dir,\n    limit_val_batches = 20,\n)\n\n# training\ntrainer.fit(net)", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "print(f\"train completed, best_metric: {net.best_val_dice:.4f} \" f\"at epoch {net.best_val_epoch}\")", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Plot the loss and metric", "metadata": {}}, {"cell_type": "code", "source": "plt.figure(\"train\", (12, 6))\nplt.subplot(1, 2, 1)\nplt.title(\"Epoch Average Loss\")\nx = [i + 1 for i in range(len(net.epoch_loss_values))]\ny = net.epoch_loss_values\nplt.xlabel(\"epoch\")\nplt.plot(x, y, color=\"red\")\nplt.subplot(1, 2, 2)\nplt.title(\"Val Mean Dice\")\nx = [i*10 for i in range(len(net.metric_values))]\ny = net.metric_values\nplt.xlabel(\"epoch\")\nplt.plot(x, y, color=\"green\")\nplt.savefig(\"EpochAverageLoss_ValMeanDice1.png\")\nplt.show()\n\n\nplt.figure(\"train\", (12, 6))\nplt.subplot(1, 2, 1)\nplt.title(\"Val Mean Dice\")\nx = [i*10 for i in range(len(net.metric_values_wt))]\n#y_back = net.metric_values_back\ny_tc = net.metric_values_tc\ny_wt = net.metric_values_wt\ny_et = net.metric_values_et\nplt.xlabel(\"epoch\")\n#plt.plot(x, y_back, color=\"m\")\nplt.plot(x, y_tc, color=\"blue\")\nplt.plot(x, y_wt, color=\"brown\")\nplt.plot(x, y_et, color=\"purple\")\nplt.legend(['Val Mean Dice Back', 'Val Mean Dice TC', 'Val Mean Dice WT', 'Val Mean Dice ET'])\n#plt.savefig(\"ValMeanDiceTC_WT_ET.png\")\n\nplt.subplot(1, 2, 2)\nplt.title(\"Haursdoff Values\")\nx = [i*10 for i in range(len(net.haursdoff_values_tc))]\n#y_back = net.haursdoff_values_back\ny_tc = net.haursdoff_values_tc\ny_wt = net.haursdoff_values_wt\ny_et = net.haursdoff_values_et\nplt.xlabel(\"epoch\")\n#plt.plot(x, y_back, color='purple')\nplt.plot(x, y_tc, color='m')\nplt.plot(x, y_wt, color='c')\nplt.plot(x, y_et, color='k')\nplt.legend(['Haursdoff Values back', 'Haursdoff Values TC', 'Haursdoff Values WT', 'Haursdoff Values ET'])\nplt.savefig(\"HaursdoffValuesTC_WT_ET1.png\")\nplt.show()", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Save to CSV", "metadata": {}}, {"cell_type": "code", "source": "import csv\n\nfilename = 'MetricsSwinUNETR.csv'\nwith open(filename, 'w', newline='') as file:\n    writer = csv.writer(file)\n\n    writer.writerow(['Index', 'Mean Dice', '<PERSON>ce tc', '<PERSON>ce wt', '<PERSON>ce et',\n                     '<PERSON><PERSON>doff tc', '<PERSON><PERSON>doff wt', 'Haursdoff et',])\n\n    for i in range(len(net.metric_values)):\n        writer.writerow([[i*10 for i in range(len(net.metric_values))][i], net.metric_values[i], \n                         net.metric_values_tc[i], net.metric_values_wt[i], net.metric_values_et[i],\n                         net.haursdoff_values_tc[i], net.haursdoff_values_wt[i], net.haursdoff_values_et[i]])\n\n        \nfilename = \"AverageLossSeg.csv\" \nwith open(filename, 'w', newline='') as file:\n    writer = csv.writer(file)\n   \n    writer.writerow(['Index', 'AverageLoss'])\n\n    for i in range(len(net.epoch_loss_values)):\n        writer.writerow([[i + 1 for i in range(len(net.epoch_loss_values))][i], net.epoch_loss_values[i]])", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Check best model output with the input image and label", "metadata": {}}, {"cell_type": "code", "source": "model = Net.load_from_checkpoint(os.path.join('/kaggle/working/Best_modell/best_metric_model.ckpt'))\nmodel.eval()\nmodel.to(device)\n\ncase_num = 0 # Select image number from val_ds [0,9)\nsize = 90 # Select the slice number\n\nwith torch.no_grad():\n    img_name = os.path.split(net.val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n    print(img_name)\n    image, label = net.val_ds[case_num][\"image\"], net.val_ds[case_num][\"label\"]         #torch.Size([4/1, 240, 240, 155])\n    val_inputs = torch.unsqueeze(image, 0).cuda()                                       #torch.Size([1, 4, 240, 240, 155])\n    pred = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)         #torch.Size([1, 4, 240, 240, 155])\n    pred_discret = torch.argmax(pred, dim=1)                                            #torch.Size([1, 240, 240, 155])\n\n    plt.figure(\"check\", (18, 6))\n    plt.subplot(1, 3, 1)\n    plt.title(f\"image\")\n    plt.imshow(image[0, :, :, size], cmap=\"gray\")\n    plt.subplot(1, 3, 2)\n    plt.title(f\"label\")\n    plt.imshow(label[0, :, :, size])\n    plt.subplot(1, 3, 3)\n    plt.title(f\"SegResNet\")\n    plt.imshow(pred_discret.cpu()[0, :, :, size] )\n    plt.show()", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Metrics", "metadata": {}}, {"cell_type": "code", "source": "import warnings\nfrom typing import Callable, Optional, Union\n\nimport torch\n\nfrom monai.networks import one_hot\nfrom monai.utils import MetricReduction\n\ndef compute_meandice(\n    y_pred: torch.Tensor,\n    y: torch.Tensor,\n    include_background: bool = True,\n    to_onehot_y: bool = False,\n    mutually_exclusive: bool = False,\n    sigmoid: bool = False,\n    other_act: Optional[Callable] = None,\n    logit_thresh: float = 0.5,\n) -> torch.Tensor:\n\n    n_classes = y_pred.shape[1]\n    n_len = len(y_pred.shape)\n    if sigmoid and other_act is not None:\n        raise ValueError(\"Incompatible values: sigmoid=True and other_act is not None.\")\n    if sigmoid:\n        y_pred = y_pred.float().sigmoid()\n\n    if other_act is not None:\n        if not callable(other_act):\n            raise TypeError(f\"other_act must be None or callable but is {type(other_act).__name__}.\")\n        y_pred = other_act(y_pred)\n\n    if n_classes == 1:\n        if mutually_exclusive:\n            warnings.warn(\"y_pred has only one class, mutually_exclusive=True ignored.\")\n        if to_onehot_y:\n            warnings.warn(\"y_pred has only one channel, to_onehot_y=True ignored.\")\n        if not include_background:\n            warnings.warn(\"y_pred has only one channel, include_background=False ignored.\")\n        # make both y and y_pred binary\n        y_pred = (y_pred >= logit_thresh).float()\n        y = (y > 0).float()\n    else:  # multi-channel y_pred\n        # make both y and y_pred binary\n        if mutually_exclusive:\n            if sigmoid:\n                raise ValueError(\"Incompatible values: sigmoid=True and mutually_exclusive=True.\")\n            y_pred = torch.argmax(y_pred, dim=1, keepdim=True)\n            y_pred = one_hot(y_pred, num_classes=n_classes)\n        else:\n            y_pred = (y_pred >= logit_thresh).float()\n        if to_onehot_y:\n            y = one_hot(y, num_classes=n_classes)\n\n    if not include_background:\n        y = y[:, 1:] if y.shape[1] > 1 else y\n        y_pred = y_pred[:, 1:] if y_pred.shape[1] > 1 else y_pred\n\n    assert y.shape == y_pred.shape, \"Ground truth one-hot has differing shape (%r) from source (%r)\" % (\n        y.shape,\n        y_pred.shape,\n    )\n    y = y.float()\n    y_pred = y_pred.float()\n\n    # reducing only spatial dimensions (not batch nor channels)\n    reduce_axis = list(range(2, n_len))\n    intersection = torch.sum(y * y_pred, dim=reduce_axis)\n\n    y_o = torch.sum(y, reduce_axis)\n    y_pred_o = torch.sum(y_pred, dim=reduce_axis)\n    denominator = y_o + y_pred_o\n\n    f = torch.where(y_o > 0, (2.0 * intersection) / denominator, torch.tensor(float(\"nan\"), device=y_o.device))\n    return f  # returns array of Dice shape: [batch, n_classes]", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "val_labels = torch.unsqueeze(label, 0).cuda()\n\ndef dice_metric_(y_pred: Union[np.ndarray, torch.Tensor],\n                y: Union[np.ndarray, torch.Tensor],\n                n_classes: int):\n    if isinstance(y_pred, np.ndarray):\n        y_pred = torch.from_numpy(y_pred)\n    if isinstance(y, np.ndarray):\n        y = torch.from_numpy(y)\n    \n    y = one_hot(y, n_classes, dim=1)\n    y_pred = one_hot(y_pred, 4, dim=0).expand(1, 4, 240, 240, 155)\n    \n    dice = compute_meandice(y_pred=y_pred, y=y, include_background=True)\n    return np.around(dice[0].numpy(), decimals=3)", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "for dice_organ in dice_metric_(y=val_labels.cpu(), y_pred=pred_discret.cpu(), n_classes=4):\n    print(dice_organ, end=', ')", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "def hausdorff_metric(y_pred: Union[np.n<PERSON><PERSON>, torch.Tensor],\n                y: Union[np.n<PERSON>ray, torch.Tensor],\n                n_classes: int):\n    if isinstance(y_pred, np.ndarray):\n        y_pred = torch.from_numpy(y_pred)\n    if isinstance(y, np.ndarray):\n        y = torch.from_numpy(y)\n    \n    y = one_hot(y, n_classes, dim=1)\n    y_pred = one_hot(y_pred, 4, dim=0).expand(1, 4, 240, 240, 155)\n    \n    hausdorff = compute_hausdorff_distance(y_pred=y_pred, y=y, include_background=True)\n    return np.around(hausdorff[0].numpy(), decimals=3)", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "for hausdorff_organ in hausdorff_metric(y=val_labels.cpu(), y_pred=pred_discret.cpu(), n_classes=4):\n    print(hausdorff_organ, end=', ')", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "## Saving predictions based on the model", "metadata": {}}, {"cell_type": "code", "source": "# predictions softmax\nsoftmax = torch.nn.Softmax(dim = 1)\nout = softmax(pred)\nsaver = SaveImage(output_dir=\"Out\", output_ext=\".nii.gz\", output_postfix=\"seg\")\nsaver(out[0])\n\nlabel = ['background', 'WT', 'ET', 'TC']\n\nplt.figure(\"output\", (18, 6))\nfor l, name in enumerate(label):\n    plt.subplot(1, 4, l + 1)\n    plt.title(f\"{name}\")\n    plt.imshow(out[0, l, :, :, 90].detach().cpu())\nplt.show()", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "model = Net.load_from_checkpoint(os.path.join(root_dir, \"best_metric_model.ckpt\"))\nmodel.eval()\nmodel.to(device)\n\nval_loader = DataLoader(net.val_ds, batch_size=1, shuffle=True, num_workers=4, pin_memory=True)\nsaver = SaveImage(output_dir=\"Output\", output_ext=\".nii.gz\", resample=True)\n\nwith torch.no_grad():\n    for val_data in val_loader:\n        images = val_data[\"image\"].to(device)  #torch.Size([1, 4, 240, 240, 155])\n        pred = sliding_window_inference(images, roi_size,  sw_batch_size, model)\n        \n        #Saving prediction without discretization\n        output = softmax(pred)   #torch.Size([1, 4, 240, 240, 155])\n        saver(output[0])", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}]}