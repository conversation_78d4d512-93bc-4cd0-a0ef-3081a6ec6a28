{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ActivityNet Challenge Proposal Task\n", "This notebook is intended as demo on how to format and evaluate the performance of a submission file for the proposal task. Additionally, a helper function is given to visualize the performance on the evaluation metric."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["import sys\n", "sys.path.append('../Evaluation')\n", "from eval_proposal import ANETproposal\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import json\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Help functions to evaluate a proposal submission file and plot the metric results"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["def run_evaluation(ground_truth_filename, proposal_filename, \n", "                   max_avg_nr_proposals=100, \n", "                   tiou_thresholds=np.linspace(0.5, 0.95, 10),\n", "                   subset='validation'):\n", "\n", "    anet_proposal = ANETproposal(ground_truth_filename, proposal_filename,\n", "                                 tiou_thresholds=tiou_thresholds,\n", "                                 max_avg_nr_proposals=max_avg_nr_proposals,\n", "                                 subset=subset, verbose=True, check_status=True)\n", "    anet_proposal.evaluate()\n", "    \n", "    recall = anet_proposal.recall\n", "    average_recall = anet_proposal.avg_recall\n", "    average_nr_proposals = anet_proposal.proposals_per_video\n", "    \n", "    return (average_nr_proposals, average_recall, recall)\n", "\n", "def plot_metric(average_nr_proposals, average_recall, recall, tiou_thresholds=np.linspace(0.5, 0.95, 10)):\n", "\n", "    fn_size = 14\n", "    plt.figure(num=None, figsize=(6, 5))\n", "    ax = plt.subplot(1,1,1)\n", "    \n", "    colors = ['C0', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9']\n", "\n", "    area_under_curve = np.zeros_like(tiou_thresholds)\n", "    for i in range(recall.shape[0]):\n", "        area_under_curve[i] = np.trapz(recall[i], average_nr_proposals)\n", "\n", "    for idx, tiou in enumerate(tiou_thresholds[::2]):\n", "        ax.plot(average_nr_proposals, recall[2*idx,:], color=colors[idx+1],\n", "                label=\"tiou=[\" + str(tiou) + \"], area=\" + str(int(area_under_curve[2*idx]*100)/100.), \n", "                linewidth=4, linestyle='--', marker=None)\n", "\n", "    # Plots Average Recall vs Average number of proposals.\n", "    ax.plot(average_nr_proposals, average_recall, color=colors[0],\n", "            label=\"tiou = 0.5:0.05:0.95,\" + \" area=\" + str(int(np.trapz(average_recall, average_nr_proposals)*100)/100.), \n", "            linewidth=4, linestyle='-', marker=None)\n", "\n", "    handles, labels = ax.get_legend_handles_labels()\n", "    ax.legend([handles[-1]] + handles[:-1], [labels[-1]] + labels[:-1], loc='best')\n", "    \n", "    plt.ylabel('Average Recall', fontsize=fn_size)\n", "    plt.xlabel('Average Number of Proposals per Video', fontsize=fn_size)\n", "    plt.grid(b=True, which=\"both\")\n", "    plt.ylim([0, 1.0])\n", "    plt.setp(plt.axes().get_xticklabels(), fontsize=fn_size)\n", "    plt.setp(plt.axes().get_yticklabels(), fontsize=fn_size)\n", "\n", "    plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate uniform random proposal for the validation subset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 12.5 s, sys: 344 ms, total: 12.9 s\n", "Wall time: 12.9 s\n"]}], "source": ["%%time\n", "\n", "# seed the random number generator to get consistent results across multiple runs \n", "np.random.seed(42)\n", "\n", "with open(\"../Evaluation/data/activity_net.v1-3.min.json\", 'r') as fobj:\n", "    gd_data = json.load(fobj)\n", "\n", "subset='validation'\n", "avg_nr_proposals = 100\n", "proposal_data = {'results': {}, 'version': gd_data['version'], 'external_data': {}}\n", "\n", "for vid_id, info in gd_data['database'].iteritems():\n", "    if subset != info['subset']:\n", "        continue\n", "    this_vid_proposals = []\n", "    for _ in range(avg_nr_proposals):\n", "        # generate random proposal center, length, and score\n", "        center = info['duration']*np.random.rand(1)[0]\n", "        length = info['duration']*np.random.rand(1)[0]\n", "        proposal = {\n", "                    'score': np.random.rand(1)[0],\n", "                    'segment': [center - length/2., center + length/2.],\n", "                   }\n", "        this_vid_proposals += [proposal]\n", "    \n", "    proposal_data['results'][vid_id] = this_vid_proposals\n", "\n", "with open(\"../Evaluation/data/uniform_random_proposals.json\", 'w') as fobj:\n", "    json.dump(proposal_data, fobj)\n"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["## Evaluate the uniform random proposals and plot the metric results"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INIT] Loaded annotations from validation subset.\n", "\tNumber of ground truth instances: 7271\n", "\tNumber of proposals: 471000\n", "\tFixed threshold for tiou score: [ 0.5   0.55  0.6   0.65  0.7   0.75  0.8   0.85  0.9   0.95]\n", "[RESULTS] Performance on ActivityNet proposal task.\n", "\tArea Under the AR vs AN curve: 44.8843969193%\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAY8AAAFKCAYAAADlv9PgAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAIABJREFUeJzsnXd8VFX6/993Sia9J0ASWgg1JISmopKlSJG2qCiiK9gX\nBEVUUFxR9Ks/3MWFtSE2jGsviLLqKiIGUHEBEeldSkghvU8/vz9uMmQyk2QGkpCQ83697msy555z\n7jkzk/vcU57PowghkEgkEonEGzQXugESiUQiaX1I4yGRSCQSr5HGQyKRSCReI42HRCKRSLxGGg+J\nRCKReI00HhKJRCLxGmk8JBKJROI1zWo8FEVJVRRlraIopxVFEYqi3OpBmSRFUTYqilJZVe5xRVGU\nZmiuRCKRSOqguUcegcAeYC5Q2VBmRVGCge+AHGBwVbn5wANN2EaJRCKRNIByoTzMFUUpA+YIIdLq\nyTML+DvQTghRWZX2GDALiBPSPV4ikUguCC19zWMIsLnacFTxLRADdLkgLZJIJBIJugvdgAZoD2TU\nSsupce6PmicURbkbuBvAz89vYMeOHb26mN1uR6Np6fa0cWmLfYa22e+22Gdom/0+nz4fOnQoTwgR\n1VC+lm48vEII8RrwGsCgQYPE9u3bvSqfnp7OsGHDmqBlLZe22Gdom/1ui32Gttnv8+mzoignPMnX\n0s1xNtCuVlq7GuckEolEcgFo6cZjCzBUURTfGmmjgEzg+AVpkUQikUia3c8jUFGUFEVRUqqu3anq\nfaeq80sURfm+RpH3gQogTVGUvoqiXAs8AiyTO60kEonkwtHcax6DgB9qvH+y6ngbuBXoAHSrPimE\nKFYUZRTwMrAdKAT+CSw7l4tbLBYyMjIwGo1uz4eEhLB///5zqbrV0hb7DG2z35722dfXl7i4OPR6\nfTO0StJaaVbjIYRIB+r0DhdC3OombTeQ2hjXz8jIICgoiC5duuDOSb20tJSgoKDGuFSroS32Gdpm\nvz3psxCC/Px8MjIy6Nq1azO1TNIaaelrHo2K0WgkIiLCreGQSCSgKAoRERF1js4lkmralPEApOGQ\nSBpA/o9IPKHNGQ+JRCKRnD/SeDQjRUVFrFixwvE+MzOTKVOmXJC2LFmyhISEBAYMGMC3337rNs/i\nxYuJjY0lJSWFlJQUvv76a7f5vvnmG3r27ElCQgLPPvus2zxCCO677z4SEhJITk5mx44djnNdunQh\nKSmJlJQUBg0a5HX5mmzYsIEBAwbQt29fZsyYgdVqBVSnqZCQEEdf6mpnS2PSpEn07dvXJX316tUo\nikJdjrAffPABSUlJJCcnM3bsWPLy8gA4efIkw4cPp3///iQnJ9f5nUokDSKEuCiPgQMHitrs27fP\nJa0mJSUl9Z4/X/744w+RmJjYpNfwhL1794rk5GRhNBrFrl27RHx8vLBarS75nnjiCbF06dJ667Ja\nrSI+Pl4cPXpUmEwmkZycLPbu3euS76uvvhJjx44VdrtdbNmyRVxyySWOc507dxa5ubn1Xqe+8tXY\nbDYRFxcnDh48KIQQYtGiReKNN94QQgjxww8/iPHjxzvyNtZ3bbFYGqUed6xevVpMmzbN5TdTUlIi\nhg4dKi699FKxbds2t22KiopyfKbz588XTzzxhCgpKRF33XWXWLFihRBC/R107tzZ7bUb+l9pTfzw\nww8XugnNzvn0GdguPLjHXlTyJJ7S5ZGvmrT+48+Od5v+yCOPcPToUVJSUhg1ahSzZ89mwoQJ7Nmz\nB6PRyKxZs9i+fTs6nY5ly5YxfPhw0tLS2L59Oy+99BIAEyZM4KGHHjovuYUvvviCG2+8EYPBQJcu\nXUhISGDr1q0MGTLE67q2bt1KQkIC8fHxANx444188cUX9OnTx+Wa06dPR1EULrvsMoqKisjKyqJD\nhw4et7mh8vn5+fj4+NCjRw8ARo0axZIlS7jjjju87hfAU089xX/+8x8qKyu5/PLLefXVV1EUhWHD\nhpGSksKPP/7ItGnTmD59OjNnzuTkyZMA/Otf/+KKK65g69atzJ07F6PRiJ+fH2+99RY9e/b06Npl\nZWUsW7aM1157jRtuuMHp3KJFi3j44YdZunSp27LV/9zl5eVERERQUlJCQkICoK5nlJSUAFBcXExM\nTMw5fTYSiZy2akaeffZZunXrxs6dO13+8V9++WUURWH37t188MEHzJgxw6sdL/PmzXNMydQ83E3P\nnD59mpqikXFxcZw+fdptvS+++CLJycncfvvtFBYWAup027hx47yqq758iqJw1VVXMXDgQF577TVH\nnpUrV7Jy5UqPrxMZGYnVanVM5Xz66aecOnXKcf7nn38mOTmZq6++2iN/hzlz5rBt2zb27NlDZWUl\nX375peOc2Wxm+/btPPjgg8ydO5d58+axbds2Vq9ezZ133glAr1692Lx5M7/99htPPfUUjz76KAAH\nDx50+12lpKRQVFQEqAbiwQcfxN/f36lNO3bs4NSpU4wf7/4BBUCv1/PKK6+QlJRETEwM+/btcxjQ\nxYsX8+677xIXF8e4ceN48cUXG/wcJBJ3tMmRR0vkxx9/5N577wXUm07nzp05dOiQx+WXL1/e6G2a\nNWsWixYtQlEUx81s1apVxMTENOpc+Y8//khsbCxnzpxh1KhR9OrVi9TUVGbOnOlVPYqi8OGHHzJv\n3jxMJhOjR49Gq9UCMGDAAE6ePElgYCBff/0106ZN4+jRo/XW98MPP/CPf/yDiooKCgoKSExMZOLE\niQBMnTrVkW/9+vXs27fP8b6kpISysjKKi4uZMWMGhw8fRlEULBYLAD179mTnzp11Xnfnzp0cPXqU\n5cuXc/z4cUe63W7ngQceIC0trd52WywWXnnlFX777Tfi4+O59957WbJkCXPnzuWDDz7g1ltv5cEH\nH2TLli3ccsst7Nmzp82pzkrOH2k8Wjg6nQ673e54X9doZN68efzwww8u6TfeeCOPPPKIU1psbKzT\nE3lGRgaxsbEuZdu1O6tJeddddzFhwgSXPJ7WVV++6tfo6GiuueYatm7dSmpqqsflazJkyBA2b94M\nwLp16xwGODg42JFn3LhxzJo1i7y8PCIjI13qAPVzvueee9i+fTsdO3Zk8eLFTp99QECA42+73c4v\nv/yCr6+vUx1z5sxh+PDhrFmzhuPHjzumGg8ePOhkfGqSnp7Oli1b2L59O126dMFqtXLmzBmGDRvG\nF198wZ49exz1ZGdnM2nSJNauXeu00aDaMHXrpoo13HDDDTz77LPMnTuXN998k2+++cbxWRmNRvLy\n8oiOjnbbHomkTjxZGGmNR0tcMM/LyxOdOnVyvK+5gP7Pf/5T3H777UIIIQ4ePCg6deokjEaj2Lx5\nsxgyZIiw2Wzi5MmTIigo6LwXAPfs2eO0YN61a1e3C+aZmZmOv5ctWyamTp3qksdisYiuXbuKY8eO\nORbM9+zZ45Lvyy+/dFrwHjx4sBBCiLKyMsfnXlZWJoYMGSL++9//ely+Njk5OUIIIYxGoxgxYoT4\n/vvvhRBCZGVlCbvdLoQQ4n//+5+Ii4tzvB8xYoTIyMhwqqewsFBER0eLiooKUVpaKhITE8UTTzwh\nhBDiT3/6k9NC9bRp08Q//vEPx/vffvtNCCHE5MmTxaeffiqEUDcf1LU4XR/1bbKo3Y5qTp8+Ldq3\nby/OnDkjhBDiscceEw888IAoKSkRY8eOFW+99ZYQQv1/6NChg+NzqIlcMG/dyAXzi4yIiAiuuOIK\n+vbty9VXX83s2bMd5+655x5mzZpFUlISOp2OtLQ0DAYDV1xxBV27dqVPnz707t2bAQMGnHc7EhMT\nueGGG+jTpw8ajYaXX37ZMb1z5513MnPmTAYNGsSCBQvYuXMniqLQpUsXXn31VUBd87jzzjv5+uuv\n0el0vPTSS4wZMwabzcbtt99OYmIigGO9YubMmYwbN46vv/6ahIQE/P39eeuttwDIycnhmmuuAcBq\ntXLTTTcxduxYj8uDOpJ44403iImJYenSpXz55ZfY7XZmzZrFiBEjAHX945VXXkGn0zkWrxVFwW63\nc+TIEcLDw50+o9DQUO666y769u1L+/btGTx4cJ2f5wsvvMDs2bNJTk7GarWSmprKypUrWbBgATNm\nzODpp5+ud42isUhJSWHnzp3ExMTwxBNPkJqail6vp3Pnzo6prn/+85/cddddLF++HEVRSEtLk06B\nknPigsUwb2rcBYPav38/vXv3rrOM1DtqO1T3e8+ePaxatYply85Ja7NV4c133dD/SmtCBoPyDkVR\nfhVCuHe4qoFcJZO0afr27dsmDIdE0thI4yGRSCQSr5HGQyKRSCReI42HRCKRSLxGGg+JRCKReI00\nHhKJRCLxGmk8JBKJROI10ng0I80dz+PWW2+la9euDmc7k8nE1KlTSUhI4NJLL3XSTarJsGHD6Nmz\np0Os78yZM4Cqn9WpUyfmzJnTZG1uDD7++GP69OlDYmIiN910kyP95MmTjB49mt69ezN48GC3/a8v\n3sWCBQtITEykd+/e3HfffVysPlISiSe0bQ/zxSFOb+t1n7o7HWL611uexcX1Xq7aeNxzzz0AxMTE\n8Omnn3rU1HNl6dKlDgP15ptvEhYWxpEjR/jwww95+OGHeeONN9yWe++991wCM82bN4+wsLA6AxB5\ng9VqRadr/J/f4cOHWbJkCT/99BNhYWEOwwcwffp0/va3vzFq1CiysrIICQlxKf/0009zww03MGvW\nLPbt28e4ceM4fvw4P//8Mz/99BO7du0C4Morr2Tjxo1tzvlMIqlGjjyakZrxPObPn8/x48cdUeKM\nRiO33XYbSUlJ9O/f3yFymJaW5vSkP2HCBNLT08/p+l988QUzZswAYMqUKXz//fdN8vRcHRukf//+\nXH755Rw8eBBQ+zJp0iRGjBjByJEjAdW4DR48mOTkZJ544glHHZMnT2bgwIEkJiY6ybQ3xOuvv87s\n2bMJCwsDcAj+7du3D6vVyqhRowAIDAx0kTuHuuNdKIqC0WjEbDZjMpmwWCxOwpESSVujbY88mpln\nn32WPXv2OFRPa06b1IznceDAAUaPHt2gJPvUqVMdN+aaPPDAA0yfPt0lvWZMDJ1OR0hICAUFBU6K\ns9XMmDEDvV7Pddddx2OPPeaV/lF1HAudTsf69et59NFHWb16NaDGo9i1axfh4eGsW7eOw4cPs3Xr\nVoQQTJo0iU2bNpGamsqqVasIDw+nsrKSwYMHc9111xEREdFgn6s/syuuuAKbzcbixYsZO3Yshw4d\nIjQ0lGuvvZY//viD1NRUli1b5tD0qmbx4sWMHj2aF198kfLyctavXw+oCrTDhw+nQ4cOCCGYM2fO\nRSPfIZGcC9J4tBDOJZ7HRx991CRtee+994iNjaW0tJTrrruOd955x60xqou64liAGt2vWoRw3bp1\nrFu3jv791enAsrIyDh8+TGpqKi+88AJr1qwB4NSpUxw+fJiIiIgG+2y1Wjl8+DDp6elkZGSQmprK\n7t27sVqtjsBMnTp14rrrriMtLc0lymBd8S6OHTvG/v37ycjIcPRj8+bNDB061OPPRSK5mGjbxqPW\nGoXXIoENrHE0BvXF8/B25FEdEyMuLg6r1UpxcbGLmmx1PoCgoCBuuukmtm7d6pXxWLRokds4FuAc\nB0MIwcKFC/nrX//qVD49PZ3169ezZcsW/P39GTZsmKPfDfU5Li6OSy+9FL1eT9euXenRoweHDx8m\nLi6OlJQUR7jc8ePHs2PHDhfjUVe8izVr1nDZZZcRGBgIwNVXX82WLVuk8ZC0WeSaRzMSFBREaWmp\n23NDhw7lvffeA+DQoUOcPHmSnj170qVLF3bu3IndbufUqVNs3brVUeajjz5i586dLkddN/pJkybx\n9ttvA6pE+YgRI1ymo6xWK3l5eYAake7LL790rMvU5qWXXnLEVq9JcXGxwwDVF/VuzJgxrFq1irKy\nMkCdVjtz5gzFxcWEhYXh7+/PgQMH+OWXXzzu8+TJkx1rQnl5eRw6dIj4+HgGDx5MUVERubm5AGza\ntMklzjpAp06d+P777wFVWdZoNBIVFUWnTp3YuHEjVqsVi8XCxo0b5bSVpE0jjUczUjOex/z5853O\n3XPPPdjtdpKSkpg6darbeB733XffecXzuOOOO8jPzychIYFly5Y5xTdPSUkB1O28Y8aMITk5mZSU\nFGJjY7nrrrvc1nfgwAEiIiJc0hcsWMDChQvp378/Vqu1zvaMHj2am266iSFDhpCUlMSUKVMoLS1l\n7NixWK1WevfuzSOPPMJll13mcR/HjBlDREQEffr0Yfjw4SxdupSIiAi0Wi3PPfccI0eOJCkpCSGE\no1+PP/44a9euBdR4F6+//jr9+vVj2rRpjngXU6ZMoVu3biQlJdGvXz/69evnCEkrkbRJPIkY1RqP\nlhhJsLmZMWOG+OSTT+rN422f33rrLTF79mwhhBDjx48XJpPpnNt3IbnYvmtP8KbPMpJg66Y5IgnK\nkcdFTEhICIsWLXI4CZ4vy5cvZ8mSJY7dWV9++SU+Pj6NUrdEImldtO0F84uc559/vlHrmzdvHvPm\nzWvUOiUSSetEjjwkEolE4jXSeEgkEonEa6TxkEgkEonXSOMhkUgkEq+RxqMZaS2S7Gazmbvvvpse\nPXrQq1cvhy5Va5dkf/vtt+nevTvdu3d3OGTWJi0tjaioKIccfU3V4ZqS7n369Knz85NI2gJterdV\n0ttJHuf9cMKHJEYk1lt+94zd9dbRWiTZn3nmGaKjozl06BB2u52CggKgdUuyFxQU8OSTT7J9+3YU\nRWHAgAFMnTrVob5bk6lTp7r1nK8p6V5WVoZGI5+9JG0X+etvRlqLJPuqVatYuHAhABqNhsjISK+u\n0xIl2b/99luHKGNYWBjDhw93aFh5gqeS7hJJW6FNjzyam9YgyV5UVASo4obp6el069aNl156yavY\nFS1Rkr1m30Ed9Z0+fdpt+1evXs3GjRvp2bMny5cvp2PHji6S7ldddRXPPvusi6S7RNJWkMajhdBS\nJNmtVisZGRlcfvnlLFu2jGXLlvHQQw/xzjvveFxHS5Rk95SJEycybdo0DAYDr776KjNmzGDDhg0u\nku7V+mO1VXklkrZCmzYetdcovJVkb2iNozFobkn2iIgI/P39ufbaawG4/vrrefPNN71qc0uUZI+N\njXWa7svMzCQxMdGlnppCj3feeScLFiwAcJF0nzx5Mr/88os0HpI2S5s2Hs2NJ5LsI0aMcJJkLykp\nYcWKFdjtdk6fPu0iye4N1ZLsQ4YMqVOSXVEUJk6cSHp6OiNGjOD77793K10OOBaVa+++8kaSfdGi\nRdx8880EBgZy+vRp9Hp9g5Ls9TF58mQ++OADbrvtNidJ9m7duvHoo49SWFgIwIYNG3juuedcymdl\nZdGhQwcA1q5d65BdrynpHhUVxYYNG1xivEskbQlpPJqRmpLsV199NbNnz3acu+eee5g1axZJSUno\ndDq3kuy9e/c+b0n2W265hYSEBMLDw/nwww8d51JSUhxrMX//+9+55ZZbuP/++4mKiuKtt95yW9+B\nAwe44oorXNIXLFjAjBkzePrppxk/fnyd7Rk9ejT79+9nyJAhgLoI/e677zJ27FhWrlxJ79696dmz\np9eS7OvWraNPnz5otVqHJDuoI6LBgwcD8PDDDztGXY8//jiDBg1i0qRJvPDCC6xduxadTkd4eLjD\n+NWUdBeqanOdUvUSSZvAE+nd1nhISXYpyV4fF9t37QlSkr3tICXZJeeFlGSXSCRNRbNPWymKcg8w\nH+gA7AXuF0Jsrif/GGAx0BcwAT8B84UQ9W9FkkhJdolE0mQ068hDUZSpwPPA/wP6Az8D/1UUpVMd\n+bsCXwCbq/JfBfgCXzdLgyUSiUTiluaetnoASBNCvC6E2C+EuBfIAmbVkX8goAcWCiGOCCF2As8C\n3RRF8c7tWSKRSCSNRrNNWymK4oNqDGrvj1wHXF5HsW2ABbhTUZQ3AH/gVmCbECLPzTXuBu4GaNeu\nnYuMR0hISJ1bZQFsNlu95y9G2mKfoW3225s+G43Gc5bBaWmUlZVdNH3xlOboc3OueUQCWiCnVnoO\n6nSUC0KIE4qijAI+AV5GHSn9BlxdR/7XgNcABg0aJGo6pwHs37+/XidAb50ELwbaYp+hbfbbmz77\n+vo6PP9bO+np6dS+F1zsNEefW/RuK0VR2gNvAu8Ag4FhQCnwsaIoLbrt7mgNkuylpaUOOfKUlBQi\nIyO5//77gdYhyV6XpPrOnTsZMmQIiYmJJCcnO7S2anPixAlGjhxJcnIyw4YNIyMjw235ppCGkUha\nE8058sgDbEBthb12QHYdZWYD5UKI+dUJiqL8BTiFOtX14/k0aH+v3h7n7fLpp/j1dZazqF2+94H9\n9dbRGiTZg4KCHM6CAAMHDnRIlbQGSXZwL6nu7+/Pv//9b7p3705mZiYDBgxg8uTJhIaGOuV76KGH\nmD59ukPTauHChbzzzjsu5QcOHMiYMWNcykskbYVme3oXQpiBX4FRtU6NQt115Q5/VINTk+r3rW7k\n0Vok2as5dOgQZ86cYejQoV5d50JKstdFjx496N69O6Aa7aioKHJzc13y7du3jxEjRgAwfPhwvvji\nC7flo6Oj3ZaXSNoKze3nsQx4R1GUraj+GjOBGGAlgKIoS4BLhBAjq/J/BcxTFOVx4AMgCHWb7ylU\nQ9SqaA2S7DX58MMPmTp1qov+VUNcSEl2cC+pXpOtW7diNpvp1q2bSz39+vXjs88+Y+7cuaxZs4bS\n0lLy8/OdBBPrKy+RtBWa1XgIIT5SFCUCeAzVSXAPME4IcaIqSwegW438GxRFuQlYUHVUAL8AY4UQ\n5c3Z9qampUiy1+TDDz/0Soq9mgspyV6XpHo1WVlZ3HLLLaxYscJtJMDnnnuOOXPmkJaWRmpqKrGx\nsU4xO6rLv/322zKSoKRN0+we5kKIFcCKOs7d6ibtQ+BD19znT+01Cm934DS0xtEYNLckezW///47\nVquVgQMHet3mCynJXpekOkBJSQnjx4/nmWee4ZJLLnHb9piYGD777DNANWarV692rGvULO+NWKNE\ncjEiVXWbkdYgyV7NBx98wLRp0+qtryVKstclqW42m7nmmmuYPn06U6ZMqfN7yMvLIzw8HI1Gw5Il\nS7j99tvdlpdILihWExiLQasHvzDncz89D/Rr8iZI49GMtBZJdoCPP/6Yr7+uXwWmJUqy1yWp/vHH\nH7Np0yby8/NJS0vDbrfz73//m5SUFCdJ9vT0dBYuXIiiKKSmpvLyyy+7LQ+qYUxJSfG4bRIJAEKA\nsQhKc8DHH0JrqTP9/hFkbIXKQqgoUF8tFWAxqq+mUrCZ1LxX3A+jnnQuf/J/0KHpjYdS326b1syg\nQYNE7S2l+/fvdzyJuuNicxy79dZbmTBhQr1Pyt72OS0tje3bt/PSSy8xYcIEPvvss1aprHuxfdee\n4E2fG/pfaU00qsOcEGAuA1MZ2Myg94PAaOc8v38IJ35WDYSxGIwl6g3fUgnWyqqyVTf/IXNgzDPO\n5T/7K+zycKZ+8F0wvpZox+ez+TFgLFeOmnhOXVQU5VchRIORzuTI4yKmWpI9Ly+PmTNnnnd9y5cv\nZ+XKlVx33XWAKskukVx0nNgCWb9D4XEoP1P19F+gvpbngvXsuiPJN8K1rzqXP7YRfn/fs2sZi13T\nfAJc0+rCXOaaFtkd//wMz+s4R6TxuIiRkuwSSQ2EgPI8yDsE+UegOAOie0Hf65zzbX0V9q7xrE6b\n2TXN3/0mFLeYSlzTPDEeGh0YgtWRT22SplD5v/N35G0IaTwkEknrxW4DjdY5LeNXOPq9OlVkKiHp\njz2w/zEoPqVOJdUk8VpX4xHW1fPruzMetRew3aHzg6D2EBTjeq7nOAiJA99QtS6/MDAEqoZC5weG\nIPXvuvyvQuKw+BzxvA/niDQeEomkZSGEukhckqkeZTnq+8oCKMuFktNQmgUlWdB5CNxUawdexjb4\n4ew6QgT1UJLpmhbWpf72aQ3gF6q+uhtlJIwE3xD1pu8bCr7BZ0cJ1YdPYN03/85D1KOFI42HRCJp\nWqqNQfEpdaqoLAfK89X1A78wGL7QOf+vb8GXHk6PltUW6ca7NQN3xiMmBQZMV41IcCz4hatGwi9M\nXRyv78YPENNfPS5ypPGQSCTnRvXOI0OtHVx/bIbtq9Qbe2m2+upuYRcgsoer8TC4l8txS6kb42EI\nrDu/zg8iu6tHWFfXbbKgbnOd9KLnbWijSH2FZqQ1SLKD6iCYlJREcnIyY8eOJS9PjbvVGiTZq1m9\nejWKojgpAI8dO5bQ0FAmTJhQZ7mVK1eSlJRESkoKV155Jfv27XM6X1JSQlxcXKv4DBoFcwWc2gY7\n/g0bnoE1MyFtArzQH55pDyuvdC1TlgN7P4MTP0HB0boNB6g7mGrj64VScWUh1FBgACCqNwx9EEY+\nAeOeY0/iI3DnBnhgPzyaCTM3w5RVMHIRDJzh+bUkTrTpkcfLMzc0nKmK6xcOIrqz8xNR7fKzV46o\nt47WIMlutVqZO3cu+/btIzIykgULFvDSSy+xePHiViPJXlpayvPPP8+ll17qlD5//nwqKip49dVX\n6ygJN910k2Nb89q1a3nggQf45ptvHOcXLVpEampqk7T7giCE6xSMuULdlfTyZZB3EITdfVlQp59q\nExzr+fUrC13b4BsM+gAIjlGPoPbgH3F2+ig49uw5v3CorTEW3QtGPu54m1eRDnHey+xI6keOPJqR\n1iDJLoRACEF5eTlCCEpKSoiJcbMjpB4utCT7okWLePjhh/H19XVKHzlyZINOcjUVhsvLy53kW379\n9VdycnIYPXq0V+1pEditaK0V6iJz3hHI2QtZuyBrpxvjIFRP5tz99RsOAHOV81tNgt38XnS+6hRV\nt5HQ/y9w5TwY8/9g8grXa8QNhr9lwr3bYcZauPY1GLsE/jQfBt8BPcdCh2QIiHQ1HJJmo02PPJqb\n1iDJrtfreeWVV0hKSiIgIIDu3bs7JDo85UJKsu/YsYNTp04xfvx4li5d6lW7q3n55ZdZtmwZZrPZ\nochrt9s5tqB/AAAgAElEQVR58MEHeffdd1m/fv051dts2CzqllRzuaqBZDWBsOFfV367DbQ1bsJa\nvefX0geoo4ea/gZBHWDyK+qIIbC9+uoXVv8ic028DAEguTBI49FCaCmS7BaLhVdeeYXffvuN+Ph4\n7r33XpYsWcJjjz3mcR0XSpLdbrfzwAMP1CvG6AmzZ89m9uzZvP/++zz99NO8/fbbrFixgnHjxhEX\nF3dedZ8XdpvqV1B9VI8aq/f9V2Mzq7uaPK7X6mwwNHqgxg08PB7aJ0F4NwjtCCGdIKRq6sgQ7Hqz\n1/lAyk1ed0/SumjTxqP2GoW3ekcNrXE0Bs0tyV49KqoOdHTDDTfw7LPPetXmCyXJ/uc//5k9e/Y4\nrpednc2kSZNYu3YtgwY1KNXjwo033sisWbMA2LJlC5s3b2bFihWUlZVhNpsJDAz0+rPxCCGgIk8V\nwrOZ1JGEzVz3FFJoJ2fjofcDRdPwlFM19lrBOhVFnRK6/VuI7q36LEgktWjTxqO5aQ2S7LGxsezb\nt4/c3FyioqL47rvv6hTIa4mS7NU7wwCGDRvGc88955XhOHz4sCPc7FdffeX4+7333nPkqRaH9Npw\n2K1gNYO9yhhUG4WAKFffhJIsELUjMHuIolGnk8w1f2sKNo0erW+Qei29vzrC0GjdTxPp/aDTuSs4\nS5ofi91CZllmvaGlGxNpPJqR1iDJHhMTwxNPPEFqaip6vZ7OnTvXaQBaoiR7fQwdOpQDBw5QVlZG\nr169WLVqFWPGjHGSZH/ppZdYv349er2esLAw3n777XO7WGWhupBss6hrDjaTajzc4RPobDwUBXQG\nddH6XPEPV/0dfAJUT2itnoqysjanJHyxkFeZx6GCQ5Rbyyk0FlJoLKTAWEBuZS55lXnkVuSSXZ6N\nVVgZ3Xk0Ezk3RV1vkJLsNbjYZLqlJHvdnPd3bbOq6qo2k+p45lNrOTr3EFg8jJQc2M51h1LhCVWO\nwwkFtD7q+oTOB5QqTSe/MI+8qqUk+4Wj1FzKqdJTlFvKKTOXUWhSb/6FxkKKTEUUm4odrzGBMbw6\nynk7+dqja/nbj3/z6Fo9w3oyJ3jOOfdZSrJLpCR7YyDs6sjBUlkVkKdSNRo1RxGB7V2Nh9bHc+Nh\ns7im+YWqow+db5XB8Kl7iknSLFRaK8mryCPPmEexqZgKSwXl1nKKTcXkVuQ6RgERvhEsH77cqezP\nmT/z0MaHPLqOxe76ewjQeS65crL0JCKo6QcF0nhcxEhJ9vOgNFuderKagAb+Ea0m1zSdu+2uyllD\noNWffXUnq+0bIheqmxGzzUxORQ455TmYbCauiHWejv3q2Fc8svkRj+pqH9DeJS1A7/nNv9jkGuPD\nz91vxA2RfpF0CuqEURgbznyeSOMhaXsIgWK3nA3vqTWou4tqYrc6B/2pD5sb42EIxslY6Azqqxw5\nXBCyy7PZlLGJ7PJscipyOFNxhhJzCaXmUkrNpRSZzkq1xwbG8s113ziVD/P1QGa9irzKPIQQTptR\n/HV1etm4UGYpw2K3oNecfQCJ8ovi0g6X4q/zJ9QQSphvGOG+4UT4RRDlF0WUXxTtAto5jNS5OhJ7\ngzQekraB3aqGAzWWgLmUQLsVqmeVfAJdjUeDT3rK2Wkld3kNQa6CgZLzxi7s5JTncLzkODkVOY6b\nf4m5hILKAgpMBVRYKnh/vHMkvxMlJ/i/X/7Po2tklWe5vXl7itVupchU5GRwQg2h9Arvhb/OH3+9\nP2EG9eYf5htGmG8YIT4hhBhCCDWEEuobik5xvjV3D+vOG6PfqH2pC4o0HpKLE7tdXXMwlalbVs0V\n1Dn9ZKl01VfS1TAIGn2NWAz+oPdVRytyFHHO2Ow2yixlVForMVqNGG1GjFYjJpsJk83ElbFXolHO\ner0fLjzMA+kPkFWehcndSK8WFZYK/PVnn/bb+bfzuG12YSe7LJuOwR0daVF+UegUneNJP8w3jAB9\nAAH6AAL1gUT6RRLpH+kYBQT5OD84xIfG88nETzxuQ2ugTuOhKIrHe0KFEDsapzkSSSNhLIKiE57l\nFVWe2zrD2TS9L0R0V1818hnLUyw2C9kV2cQFxjlN22SVZfHQxofIq8yj2FxMeQObCTZP3UxoDXVd\nf70/x0uOe9yOQlOhs/EIqN94aBQNkX6RtPdvT/uA9ohaDxohhhB+veVXJ4PW1qnvv2I76qNaQ49X\nAtA2kEeCqqr7/vvvO1R1MzMzue+++5pMWffWW29l48aNPPzww8ycOROTycT06dP59ddfHVIfERGu\ncdY++ugjnnnmGWw2GxMmTODvf/87oO62Wr58ucMf4oIg7Kqjnc2krklYjaondkQCaLRs2rSJ+++/\nn127dvHhiiVMmXCVo+jDzzzPV9//CMBjc+/ixuuvUZ3p9H6OUKYnTpzg9ttvJzc3l/DwcN59912H\nJMnYsWP55ZdfuPLKK9vGTrNa2IWdUnMp+ZX5ZFdkc6biDFnlWWSUZqhHWQa5FbkIBD/e+CMhhrML\n/gadgV15uzy+VoGxwMl4RPtFe9XWgsoCYgPPqvv66fyY2nMqoYZQ2gW0o51/O8IMYQT5BBHoE0io\nIRRdPQ8JiqKgNHgrbFvUZzy8COTbOvnn1LrjOtTmL0v+Rbv4hHrLP/hR/TeU1iDJnp+fz/z58/n1\n11+JiopixowZfP/994wcObL5JdmFXZ1uMpdVifwZ3ceMBnXqyRBIp06dSEtL47nnnnPSa/pq/WZ2\n7DnEzp+/x4QvQ6+6mnE3z3IShQR46KGHmD59OjNmzGDDhg0sXLiQd955B/BM0r01I4TALuxY7VYX\nL2WTzcRl712GVdTh6FiLrPIsJ+MRalDn8T0tn2/MJ554x3u9Vk+EbwT5xnyCfYLpEtyF2KBYQg2h\nBPkEEewT7LSGEB8S71LnY5d5rs8maZg6/3uFEB6O+SWeUlOSfdSoUcyePZsJEyawZ88ejEYjs2bN\nYvv27eh0OpYtW8bw4cOdnPJAlWR/6KGHzskB6IsvvmDx4sWAKsk+Z84cl5vEsWPH6N69O1FR6gLh\nVVddxerVqx0S6p6wdetW5s6di9FoxM/Pj7feeouePXuSlpbGZ599RllZGTabjY0bN7J06VI+/vhj\nTCYT11xzDU8++SQAk/88iVN/HMFoMjH3jmnc/Zfr6r+opRwMgXTp0gUAjUajOs75qZ7W+7LKSb3q\nanThndEBffv25ZtvvuGGG25wqmbfvn0sW7YMgOHDhzN58mTHuZEjRzbLLpamwiZslJvLMdqMWOwW\nbHYbVmHFarc6/q7+PVhrecMbtAb0Wj1Wq2c3/8yyTHqF93K81ygaIvwiyKk4G/kvUB+Iv84fX52v\nemh9MegMGLQGfLSujqdpY9PUxWWD3MLcEpBrHs1Ia5BkT0hI4ODBgxw/fpy4uDg+//xzzOY6nvbr\nwCtJ9kOH2Lp5A0KjZdK1N9SQZH+LcGsOlWXFDB5/C9eNG0lEeChTZz7MwaOuzzUPzL6L6ffUcsLy\nC4OwzgD06z+QJ598kgcffJCKigo2b95Mv379XOrp168fn332GXPnzmXNmjWUlpaSn5/vdnqvNWEX\ndk6bT4OHX6XNja5WuG84p8tON1g2yi8Ks5sR4gsjXiBAH0CoIZRAfSBajXez3V1CuniVX9K0yDWP\nFkJLkWQPCwvjlVdeYerUqWg0Gi6//HKOHj3qVR0NSrKHBEFZDuu++Jh133xN/4GbQKujrMJ0VpL9\nxRdZs/pjsFk5lZnD4T9OEhEeykcr/67uftL5qNtkqw99/fvoR48ezbZt27j88suJiopi8ODBaLWu\nP9vnnnuOOXPmkJaWRmpqKrGxsW7zXUiEEJhtZseirkBgtVux2C2YbWbKLeV0DOro9PSuUTReTRvZ\n3SjyhvuGU2gsJNw3nGj/aNoFtKO9f3tiA2OJC4ojNjCWDoEdMGgNbmqEPhF9zqG3kpZKm17zqL1G\n4a3eUUNrHI1Bc0uyA0ycOJGJE1Vhtddee83rm2edkux2OwF64Mw+EHaE1cTCObfx11umqAvXUT2A\nGpLsP27GvzKTYVPuwqgLhaheTL15BgfdGNW6+lyTv/3tb/ztb6o+0PXXX0+PHj1c8sTExPDZZ58B\nanyR1atXExrqRUztJiSvMo8SUwlGm7FB5dQySxnhWufvVq/oGzQeiqKg0+iopNLl3NtXv+3k+yBp\n28g1j2akNUiyA5w5c4bo6GgKCwtZsWIFH3/8sdv6GpRkN5eT9vI/VQny3ANQmql6dFc91Y4ZNoRF\nS1/h5mvHERigcPrUSfQG37OS7MHhHMg8wy/bd4JfCOj9+KiOtjSEzWajqKiIiIgIdu3axd69e92G\nk83LyyM8PByNRsOSJUu4/fbbz+l6niCEwCqsmG1mLDYLFvvZI8gniHBf55u/0Wqk0up6U3dHmbnM\npbyP4oPQCQxadV1Bp9GhVbToNDrH3xpFg6Io7M/e71KnNBySmni1gV1RlBigE+C0miWE2NSYjbpY\naQ2S7ABz587l999/B+Dxxx93+4QOnkiy/x/jUwepDniWSpegQ6P/NIT9h/9gyKRbQdESGBzKu++9\nd1aSvU8fryXZt23bxjXXXENhYSH/+c9/eOKJJ9i7dy8Wi4WhQ4cCapzy119/3bHbq6Yke3p6OgsX\nLkRRFFJTU51C8NaUdI+Li+PNN99kzJgxHretxFTikJ6w2C1YbBa300OA6mHsHIIdvRfhYcst5S4S\nGSG6kItKNVpyYfFIkr3KaLwPpHJ2HcRRUAjRsiaFkZLs0EyS7J9+jI+tQpUPD4sHba3nkTMHwN3T\nsqKFoHZgCFGd85rZW7spvmuLzUKFtYIKSwV6rZ5IP2fJk5zyHPIq8+oo7Yyfzo/4UOftpoXGQjLL\nMgHUkUKNBWetosVH44Neq8egNRCgD3DxW5CS7G2H8+lzY0uy/wuwAX2AbcBYoB3wFNBGZFZbH00m\nyX7tNVBZyJdvvwAFh3A8R1QWQGAtZy7fYCiraTw0EBil5mulntt2YcdkNVFprXTIaZhsJqftrf56\nfxfj4W77aV2YbCaXkUOAPoBOwZ3w1fp6NQqRXHwIIRCVldgKC7EbjRiqwkY3J57+9/4JGC+EOKAo\nigByhRA/KYpiAv4P+K7JWtjI1P6HvJhpVEl2u415M29j3ozJYCqBwuOuecrz1JCqNT/fgKgqafGq\nNJ3B4c3d2ig1l3Km4ozjxl4f7vSX3BkPjaLBR+uDj9YHvUaPXqNHp9G53bFUna+puVgDxLUmLNnZ\nVGzbhjU/H1t+AdbcXPXIy8NWWIitqAhRtYVe37kTCd9+2+xt9NR4+AHV4+0CIBo4BOwDkpugXU2C\nr6+vY89+WzEg542lUo2nbSqhwbgWNpO6IF4zqp1W7+Tp3ZKxCztGq5EKawVmm5mYwBiXPEYPZdpt\ndhtWu9Vp6sigNdA+oL2TkdBpdC3qtyiEID8/H19f34Yzt1GEEGC1IiwWhMWC3WRCH+084racOUPp\nuu8QZrOax1iJvbgYW1ERtqJibGVl2EtKsJWXAdBj82an8sZ9+8icv8Cj9tgKChunY17iqfE4APQC\njgM7gZmKopwCZgMNew21EOLi4sjIyCA3N9fteaPR2Ob+aZz6XFtZFlQdqbLs+ivR6NVIej4BUHyy\naRraiAgEFZUVaPQaLHbLWQ9ru9VJEK8woNBJCM8mbOSU57irEgAFBb1Wj49GHSEcOnOoRRkGT3/f\nvr6+Dj2vtoq9shLT0WNYc7KxnM7EkpmJOeMUllMZWE6dwl5xNr68JjCQntu3OZW3ZmeT8/TTHl+v\n9oyIzs0W+jrbWlqK3WxG08zhoD01Hs8D1eGxngK+AaYBJmBGE7SrSdDr9XTtWrf7Snp6Ov3792/G\nFl1ghGDr1+/QP8oEx9Lh9A6Y+7vqgFeN1QzPjnYNjBQcC4nXQN9rIWZAq5AnP1p0lBU7V/Bz5s+U\nWcoazP/B+A/oHem8aHzfx/eRW5lL+4D29I3oS++I3nQL6UbXkK50DOrYotci2tzvuw6subkYDx7C\nmpODNS8Pu7GS6LlznfKYjhzh+PU31FGDM/ayMoTFgqI/+90rBu8eQoXZjGI4O1Wp9UDRQNHr0YaF\noY2MwF5e3jKNhxDivRp/71AUpQvqSOSkEMKz7SOSlkPRSdjxb9j5PpeU1Bo4Hv0eel599r3OBzqk\nwKlfIKQT9J4Aff4McZeApuXKU5eaS9EqWidZbgWFdSfWeVzH4cLD9I3s65T24ogXaRfQzmUxXNKy\nsJvNGHfvxpqbh//mzWT/+BPmUycx7duPtfbMg0ZD1KxZKDVuvlovHUNtJSXoatzwNX5eGo/KSqhh\nPHQREQSPuxpteATa8DB0kZHoIqPQRUWhiwhHGxqK4ud3QUe2HhkPRVF8AI0QamBcIUQFsENRFF9F\nUXyEEN6JH0maH7sdjqyHba/D4e+oc/1i9yfOxgNg+EJVJ6p9coscYVjsFo4UHmFP/h525+5mV+4u\njhUfY8nQJYyPH+/IFx8aT2JEInvz99ZZV6RfJIPaDWJgu4Fc1sHVvyQxMrFJ+iCpG2G3YysowJKd\ngyUrE2umOo1kyTmDrVhdQ+i48hX07c7G7LCXlnLi5r8AEATUuypgt2PJysKnc2dHkjakAfFFjQbF\nxwfFxweNwaBOY9UwHtqQEMJumoai93Hk04aEoA0NQRsSgiY4GG1QEJqgIDR+fmhqbaHW+PsTWyXQ\n2VLxdNrqE+AH1C27NZkJDAMm1y4gaUHs+Df8uBwKjjWcN2efamhqjirihzVVy86J7PJsdubuZFfu\nLnbl7uJAwQG3u5v25+93Mh4AE7tNZG/+Xvw0fiRHJ9MrvBcJYQl0Du5Mp6BOhPuGt6h1iosZe2Wl\nYxeRb+/eaPzPjhLtRiMnbv4L1oICrHl5UEMfzR22/Hwn46ENDVUfdDzcOWY+leFkPDRBQRj69EYX\nFo4+JgZ9bAz62Fj0cXH4dOqENrz+34k2JIT2jz/u0bVbK54ajyuAhW7SvwMebbzmSJqE3IN1Gg6b\nxoA2fih0GQpdUyGmf4scXVSzYNMC/vvHfz3Ku7/AVWJjfNfx9InoQ9HeIoYPH97YzZPUQJjNmI4e\nxXjgIJaMDHW0kJWlrjXk5mIvO7vu1OWTT/BLOjtFqBgMmA4fdmxHbQhbUZHTe0WrRRsWhq2gwCWv\n4uODoWdPfDp3VqeBoqLw6ei8QUBRFOKrNM4k7vHUePgD7nQU7KijQklLwG5TRQfbJzmnD7wVttSK\n/Nc+GQbdxs9F7Rl61bhma2JDVFgqSD+VzjfHv+GKmCuY2muq0/muwZ7pdSooWOyuT6uhvqH09+1P\n+r70xmhum0dYrVgyM9FFRjqNHKyFhRxJ/ROigRGDI3+e8zqEoihow8OxZjew06+K2sYDIGDIEOwV\nFZyx2ehyyWD0MTH4dEvAEN/VaXFbcm54ajx2oe6ueqJW+k3AHm8uqCjKPcB8oAOwF7hfCLG5nvwK\nMBd1iqwrqp/J20KIR7y57kWN3Q67P4aNf4fSbJi3F/xrbPWL7K6OLE79T90hdcndEDsQFAXbBQxu\nZLFZOF5ynMOFh9mdt5tdebvYn7/fcdPPN+a7GI+U6BS3dUX7RZMYmUhiRCLJUcn0jexLkI98rjkf\nrIWFWHNysGRnYz1zBltBAdb8Amz5eViyc7BmZ2PJzQWLhY6vv07g0CsdZbWhoWhCQrDlebafxmUR\nG3W7arXx0AQHo2/XDl379uo0UkwM+g7t0YaFow0NcZpyqib2n88BcDQ9nYg2Jk/SHHhqPJ4CvlAU\nJQHYUJU2ErgeuMbTiymKMhV12+89wI9Vr/9VFKWPEKIuB4F/AhNQDc5uIATV8EgATv4PvnkEMmvE\n49r6Ogx72DnfuKXgH+EqH9LM2IWdz498zgcHPuBI4ZF6JcJ35e7idNlpp1jUyVHJ+On86B3em35R\n/UiOSiYpMol2Ae3qrEfiir2yUjUGBQVYz5zB0L07Pp06OeU5PuV6LKc9c+MynzgBNYyHoij49ulN\n+aY6nwtBp1N3EUVFoQkIcDkd89xSFL0eXUSE06hG0jLwdKvu14qiTAQeA16oSv4NmCSE8GwCWuUB\nIE0I8XrV+3sVRRkLzMLNmoqiKD2Be4FkIUTNCezfvLjmxUnW77BpKez/j+u5ra/C5feqjnvVRLcM\nkbvndzzPqj2rPM6/KWMT03pNc7wP0Afw07SfpDy4BwghsJ45g+nQYUxHjhD842aOv/Y65qNHsRUX\nO+Vt9+hCwmvFQ9F37Oid8aiFb58+mA4ewrdPHwwJ3RwjBl379uiiotTtpvVs9zbEu8Yhl7QcPFam\nE0J8g+oceE5UbfcdCDxX69Q64PI6iv0ZOAaMVRTlK0ADbATmCyHOnGtbWjWZOyF9CRyq46vQ6KHH\n1VUyIRfmac1oNbIpYxPfHP8Gu7Dzr+FnN+ld3+N63t//PkZb3TIfsYGxjO48mjFdx9An3DX6nDQc\nDSPMZo6MGo0156xHvB+4CfGk4s5I+HTsSMUvvzR4LW1EBIrO9VYSNWcO0fff72mTJa0MjyTZARRF\n8UWdPooHXhNCFCmK0g0oFEK4bmlwLR+DKmXyp5rxPxRFeRy4WQjR002ZlcCtwO+o01aCs8ZniBDO\nwRAURbkbuBugXbt2A2vGq/CEsrIyAgMDvSrTXGit5XT94z1iT3+NUoePRm7kEI52uxWjX3u3593R\nmH3Ot+azvng928q3YRLq1tkEQwJz2zt7764rXsd/itQRU6g2lA76DsT6xNLF0IUuPl0I0TWwx74R\naMnfdX0olZXoTp5Cl5mJUr0YLezYotth6u+8HhT6wgsY9rnuOHOHMSWF4pl/dUrz//57/Db/iC00\nBHtoKPbgEOyBgdiDg7CHhmILDcUeGopo4ZI+rfW7Ph/Op8/Dhw9vPEn2qrWO9UAgEAp8ChShTjeF\nAneeUysbRgMYgFuEEIeq2nILcBAYDPyvZmYhxGvAa6DG8/BWz75F6/7n7IWfvsGtc1+HFBj9NFFd\nhxLlZbWN0eejRUd5a89bfHnyS2zCOeBTSGiIS/1X2K4gcHsgN/e+mc7BrgudzUGL/q6rMJ86RcXW\nbZj/OIbp6DFMR45gOXXKbd7A4cPpOM/5Kb8wO5vsxU+6r1ynQxcejjYiAl1EBFGDBtG/9ufRwj8f\nT2kN33Vj0xx99iaexzpUY1FzT9xa4C0P68hDjQlSe2WzHVDXfrwswFptOKo4XFVPJ2oZj4uadokw\nZDb8/MLZtLjBkLoAuo9qdt+MYlMx3x7/ls+PfM7uvN115hNCYBd2J4FBvVbPo5e2LfcgYbdjLy/H\nVlyCrSBfldouKMRWpbSq8fcjslbMlfItW8h+vPYGR/cYDx5wSQscMQLN0ufwSeiGISGBDEWhz+jR\n+MTHo4+JqXe9QSJpCE+Nx+XAZUIIWy2vypOAq261G4QQZkVRfgVGoXqsVzMKWF1HsZ8AnaIo3YQQ\nR6vS4gEtcHHHWK/t5Q0w7BHYu0aNh3H10gtiNACOFB7hhi9vcOtHUU2UXxRjuoxhbNexzdiylkXp\nhg3kvvgSltOnsZeW1uvtrI2MdDEePh07enwta2YWtpIStMHBjjR9dDQ9tv4PRavGTzmUnk5gaqqX\nvZBI3ONNKDd3q5SdgGI36XWxDHhHUZStqIZhJqrxWQmgKMoS4BIhxMiq/OuBHcAqRVGqx+T/Qh1x\nOMeYvZg4tA6+fwpu/Y+qKVWNTwDc/AmEdQG9X7M0xWKzYBVW/HRnrxcfGk+EXwTZ5a4Dxi7BXbgr\n+S6u7nr1Rb+wbTcaMR08iHHfPmxFRUTOmlUrh4Jpv2drDra8POxGI5oa6wd6d8ZDo8HQLR5D797o\nIs6KM+oiI9wap2rDIZE0Np4aj3Wo22zvqHovFEUJBp4EvvL0YkKIjxRFiUDd8tsB1cFwnBCiehTR\nAehWI79dUZQJqNuDN6FuFvkOeKD2YvlFgRDw0/OwfjEg4PPZcON7zqOLZthym1eZx6aMTWzK2MSW\nzC3MGziPG3vd6DivUTRMiJ/AG7vfcKRd2v5SpvSYwqjOo5xia18sCJsN05EjVO78ncrduzDu3oPp\nyBGwqWs8il5PxB13OCmz+ia67hSrD0tmFob4sx70+vbtCR53NfrOnTHEx+MTH48hPh6NX/M8OEgk\n9eGp8XgA+EFRlIOAL/ARkADkAJ6J3lchhFgBrKjj3K1u0rJQnREvbixGWDtHVbWt5uBXsOVluHxO\nk19eCMHW7K18dPAjNpzc4LTwve7EOifjATAxfiLfHv+Wid0mMqnbJCdHvosFW3ExeStfxbh3L8Y9\ne5wCANVGWCwYDx5y0mfSRUejjYjAlp8PgOLvjzYoCG1EOLoqqW1taKh6VCmu1kTR6Vq8sqqk7eKp\nk2CmoigpqBIlA1B3Qb0GvCeEqGvruMRTTKXw4U3wxybndEUDGm9mFr3HJmx8ceQLVu1ZxbFi9+KJ\nv+b8Sl5lnlMMi/jQeL665quLQoHWbjRi3LMH3759naaNFIOBgn//2zG6aAjj3j3O4n6KQsfXXkUf\nHa06xEk9JUkzIISg3CI4mV9Bp4im8/XyxkmwElhVdThQFCVACFHe2A1rM5TnwXtTILOW07xvKFz/\nFnQb0SSXNdlMrD26lpczXyb/ZH69ecMMYRwvPu4SAKm1Gg5htaI/coQzv/9OxZZfqNy7FywWOqWl\nEXDZpY58Gl9fDN26YTp0qM669B074puYiG/v3vj1H+By3i9Rxv+QNB4mq428MjN5pSZyS01klxjJ\nKq4kq8hIVrGRnBL1tdJiI/bXX/jpkaa5f4B3C+ZOVDkN3ovqvHdhBZNaKyVZ8PZEyD/snB7ZA6Z9\nCBHd3JdrBI4VHeOpLU/Veb5bSDfGdB1DalwqvcN7O221bY3YKysp27SZ0m+/pWzzZsJLS6ltMit/\n2+FkPKBKYqPKeGjDwvBLScEvOQnfpGT8+iZ6HXFOIqmJ2WqnuNJCcaWZwgoL+WVm8spMjtfcUhP5\n5cDW6KUAACAASURBVCbVYJSZKDXWrQVXm5wSI3a7QKNpmoe8eo1HlaTIE8BowAL8QwjxuaIo04Fn\nUT3WljdJyy52jMXqiKO24YgdCDd/6qyK2wT0jujNkA5D2JK1xZGmVbSM6jyKqT2nMrDdwFY7sqhJ\n0ZrPKV6zhsrdu9VQn/VQscNVMi30husJ/FMqvomJ6Dt2vCg+E0nTY7XZKSg3O2761aOEnBIj2cVG\nMqtGC/nlTReE1WoX5JWbiA5qGgWAhkYei1GVb9ejBoT6RFGU11EVdRcC7wshPBPslzjz6R2QU0vN\nvuuf4Mb3wdC4Ugo/nf4Jf70//aP7O6Xf1vc2tmRtQa/Rc03CNdyedHurXvi2V1S4qK9ac3Ko2Lq1\nwbL62FiXgEAA/gNcp6IkbRMhBAXlZo7nV5BbaqLUaKHUaKWg3OwwDLlV00kFFWZPgxg2CT4aiA0P\noNRoJbqJIhM0ZDxuAG4TQqxRFKUfqpptGJAoRD1a2pKGufJ+OLUVTFVuMt3HwNR3QGdotEvszt3N\nyztf5qfMn+gW0o1PJn3i5HtxWYfL+HPon5lz1RzaB3iuh9VSEEJQuXMnpevXU7bhB3w6daLjqyud\n8hh6dHdb1h4YSOjw4QQOvRL/Sy9D307OvErU31RemZmTBeWcyK/geF45f1S9Hs8v92raqCnQABFB\nBiIDDUQFGYgOMlC0txBNgZkgu6IeQiHlWoWRo4Y1aVsaMh4dgW0AQojfFUUxA3+XhqMR6HIl3P5f\neHcKhMTC9WmNZjiOFR1j2a/L2Jix0ZF2tPgo7+9/nxmJMxxpiqJwVchVrc5wWLKyKP5iLcVr1jhJ\ngVtOn3YZfRgSEhx/69q3J3jMaILGjGFrURGJI5puMVHScskvM3Esr5zsYiNnSk3klBg5mV/BiYIK\nTuaXU272bHddY6BRwA8NPlaBrwB/oeBvV9RXoRBgp+pVfZ86viuXTnCWqv+2aA9Hsp1FxoWt6adX\nGzIeesBU470F7zzKJfXRLhHuWAd6/0aRT7fZbby7/11e2PECZrvrXOqrv7/K9T2ux1/fugLrCLsd\n89GjlP6QTul332Hc7V5LS5jNlG/ZQtDIkY40fVwccS+9iCEhAX3nzmfXLC5gBEVJ0yGEoLDC4phC\nyi4x8tNhM2vP7ORUQQVHzpRRWNE8M+2+dgioMgYBVYagV/dwLr0yjpgQX2JC/YgOMvDdG3s59ptr\nJEV3mEpd254wMJqI2AD8gw3ofbXoDVqO5dStN9dYeLLbaomiKNXeUT7AYkVRnAyIEOK+Rm/ZxYYQ\n6lFbryrUc/2i+jhVeorHfnyMHWd2uD0/sN1A5g+a3/oMhxAcvWoUlszMhjPr9ZhPOAekVDQagq66\nqolaJ7kQ5JaaOJhdyrG8Mk4XVXK6sJKsYnUhOrfUhNnmTnzCs6BWDeGjUYiPDiQ21I9gPz3BvjoK\nj5RQeaKMQLtCoGOUAFpcn/676f0Y289ZDjA40o/wmAB8fHX4BujwDdBjCNTjH+SDf4gPAcEG/IJ9\n8AvS4xvo6ivUbYDrlOvx/As/8thEDbkQ4GdUPauaXMBloVbEb+/Ab+/CxOcbVWJECMHnRz7n/7N3\n3uFxFWffvs8W9b7qvdmSJduyLfdeKca0UBNIMAmQAiQQ4E1CSIBAQkiAwPslJMCbQIAQE2qoNja2\nbOOCbblbsnq3eu/aMt8fK0s62pW0siVZsua+Ll3SmTNzzuxK2t+ZedrvD/yeNpNtBHRKQAr3zL6H\nBcELxrWnkLBY6Dh5Eq2fAafwXqO9oijooyIHFg+9Hs9Vq/C6/DLcly5F6ynrlk90Ok1mKhs7rR5J\nje2U1LVTXNdGcW0bedUto+qhBKAX4GNR8DEr+Fg0+FoU65dZw7REP67+idrx5Kt3cjiWM3BxMwCd\nsxZvfxc8/Ww9n5ZcF8+S6+LtjBrfDCoeQoiVYzSPi5vmSvjiEat77t+WWY3lyx4E/fm70B2uOsyv\n9/7apt3LyYuHFzzM+pj141Y0hMlE6/6vad6yhea0HZirazD84Ps21efc5qTStk9d0c41JQWvK6/E\n64r16Hx9kUwcOk1mSuraKaptpbiujZK6dkrr2zjT2E5FYwc1LaMrDs46DbEBHkT4uhLk5YK5qoP6\nE3X4Wqxi4SZAsbNqAKgubkYIofqfCoz2ZOqCIPTOOpxctLh6OOHqpcfV0wkPH2c8fJ1xctWN2//D\nc2V0c19IrGz9tVU4ACxGa76q2d8G3/MvhJQalMqG2A18kv9JT9vSsKU8vvhxAt3GpwdRV2kZtf/3\nCs1bvsBcX68617J9h414uM6ZjcbDA9fZs/FYtRLPNWvQB/UvCyMZT5jMFkrq28mtaiGvuoWiWqv3\nUlGtVSRG043VSYCHxbp95CEUvCzWL2+LQqSPK3f/ZgnaPoFzGXvOsONwk91ruXo5YQh1xy/UHUOY\nB4FRtivbqfOCmTpvYjmdjARSPEab8uNw/G1126qHR0Q4zvLwgodJr0ynvqOeh+Y9xA1Tbxi3Tzkt\ne/ZQdt/91voWdujMzqartEy1deW+cCFTv94v04uPQ9q6TBTWtFFQ00pBTQvZlS1kVzaTX906gO3h\n/HHWaZga5MnUIE8i/FzRN5ko3l6GZ7dgOA2wagCgpouOpi7cfXo9G738XYlJ8cfTzwVPgwteBle8\nAlzx8nfByUV+RA6EfGdGmy8fR2UWCkyCBf3rPjhGTXsNT339FE8seUJl+PZ08uTZFc/i4eRBjHfM\nIFe4cAghqH/rLSp/99SAiQY1bm64L1+O6OpUtUvRuHCYLYLS+jbya1opq2+ntN66xXT2+6hsMQlw\nF+DVHbfgZdFYbRAWBYNOx4N/XI5e3/s3UXemlX9/MVAxUlC04O3vhqfBBf8w2wDc8ARfwhPk1udw\nkeIxmhTsgtxt6ra1j4F2+G97eUs5d269k6KmIjydPHls8WOq8zMCZpzzNMeCjhMnqHziSZt2jZcX\nnmvX4nX5ZbgtWICmTz0MydgghKC2tcsaENcdDFdYYzVO59e00mUa+RVEqLcLUQZ3Iv3c8BEKpbvK\ne7aX3IVi11MJAJOg/kwrgVG9FRO9A12JTDbg6We1L7j7OOPu7YybtzPu3k7sP7SHVasWjvhrmOxI\n8RgthICt/epPRy2BKZcM+1LFTcXc8cUdlLeWA/BeznssC1vGmqg1Q4wcW0z19TS8/R/M9fUE/eLn\nqnNab3WtChSFgPvvx7DxNlUBJcnoIISgrsNCWlYVOZUtFNW1Ut7QwZnGDkrr2mjuHPm4XzcBBrMG\nP7OCn0XT7b2kcO8TS/Az9Ba0MnWZeWVHDRbL4IYQRQEPXxfamtSrHa1Ow5X3pgwybnxu4U50HBYP\nRVGCgG9jdd39lRCiRlGUJcAZIUTBaE1wwpLxXzjTL+Zi7ePDrjle0lzCbZtvo6a9RtW+tXjruBGP\njqxs6t98k8aPPkJ0doJej+GuO9EZDHb7K25uhP3xD6pgPsnI0mky83V+HYeK6jla0sCxkgYa242Q\ndnBE76PVKIR6uaCr7sTXouBn1uBvVvC3aHAV9v/Wm860qsRD56TFP8KD2jOt+AS54RvshneAK25e\nzrh6WuMdPA0uuPs6o9VO7OzOFxMOiYeiKKnAl0ABkAz8EagB1gFTgW+N1gQnJELAzqfVbYkbIGLe\nsC7T2NnIj7b9yEY41kWt44nFT5zvLM8LS3s7TZu30PD227QfPao+aTTS+MEHGO64Q92u0eCSmEjI\nb5/EZdrol9OdbNS0dJJeVM+WkxVszawcsTxM7ooGg0XB0wjeFoXla6JISQog3NeVYG8X9FoN/3ho\nN+2tjkVuVxU2ET1DXRtm/Y9m4uqhRyPFYcLg6MrjGeAFIcSjiqL0dZPZAtw+8tOa4BTthaqM3mNF\nA2seHbi/HbrMXdy34z4KmwpV7VfFXcXjix9HN8oVBgfC0tFB/b83UfvKK5jr6gbs1/jRxyrxcIqK\nYlrGqbGY4kWJxSIormvjdEUTmeXN5Fa30Gm02iLMFgvZldZo63PFWaNg0Gjx6gLvLrqD4qzbTW79\nVhBLvT2ZHqdeVRrCPCg9rXa7BnB203WvJKwGa08/F4JivGz6uXuPXEJQydjg6CdQKvA9O+3lgHS4\n70/gNFj3BBz6O9QXQuIVEDDV4eFCCB7d+yiHKg+p2i+LvownljxxwQozNf73v1Q+8wzm6poB++hC\nQ/C75VZ8rr9uDGd2cWEyW8XgWGkDx0sbySxvIquimXbj+SXsc9ZAlLsr08K9mR7jS1j3yiHMx5Uv\n/3SU+vKBa7T3paHKtl9QtBcdrUYMYR74h3tgCPfAL8QdNy8naXO4SHFUPNqxpmLvTyJQZad9cuPm\nB0t+DIvugbzt4O4/9Jg+vJ7xuiroD2B24GyeXPrkBa3oZ6qvH1A43BYuxPfmm/FcuwZFJ/0wHEEI\nQVlDO6fLm8mqbCavqoXc6hZyKlvOWygAfJx1pLi7EdBiwavGiJ9FQamD+bP8mLdc7dIdFO01pHho\nNAqe/i44u9r+fhdeE8fCa0av8qVk/OHof/l/gUcVRbmh+1goihINPA28NwrzujjQaGDK8JLyFTQW\n8Hz686q2SM9IXlj1As7asVva90/BAOBz/fXU/PkvWFpaAGtchs9NN+F70404RUeP2dwmEkIIqpo7\neyKtC2vaONPQTllD+4jXh9BrFaYGeBKBlsCyTgIqLWg4a4fofeioKrIN0AyM8uL0PmushM5Jg1+I\nO34h7viGuOMb7IZvsDue/i7SYC3pwVHxeBD4DKgG3ICvsG5X7QEeGZ2pTU6ivaJ5aN5DPHPoGYwW\nI556T15c+yK+LmMTxCSEoGXHDqr/9/8R8fJL6AN7U5xoPTzwufFG6t96C99bvoXhjjtkXqk+CCEo\nqm3jWGkDJ8saOXWmiVNnmqxeTiOAj5uexGBPEgI9mRbqhZ97r4uzqbKDhn1VNGS10Fvc0/52UVWR\nbSqOqOkG1n0vicBIL7wDXFFGqe615OLBIfEQQjQBSxVFWQ3MwfoYc1gIsW3wkZOMM0dBo4Xgcw/Y\nUxSFb037FimBKTy08yEeSH2AKK+RS2UyGB3Z2VT9/mla9+4FoPrZZwl9Wu01Zrjjexi+ezs6/+Ft\nxV2M1LZ0cqy0gaPFDRwtbeR4aQMNI1Qrwt/DiVkRPqSE+5Ac4oWhS9CS10xZVgOWyjZuvma6qn+h\nuYZPS/IHvqACzt4QlxxCYLQXFotA00cgvPxd8fJ3HXi8RNKPYW1OCyG2A9tHaS4Tn22PQn4aRCyE\neXdA0lXnXB0w2ZDMh1d/iJN29APozC0tVD/3HPWb3gZLbzRx438/wufGG3FLTe1p0/n5jfp8xiNG\ns4WsimaOljRwuLiew0X1FNY6ZmAeDA9nnXU1EWzN1RQf6EF8oAduJsGZ7AaKM+vI/zSLzDb19lZr\nY6fKQykk3tu60OgTZ+fkoiVhQTCRyQaC47zZf3APK1dKF2nJyOBonIdtzm8rAugAcoHNQohz9xWc\n6LRUQX532deS/davgN0QMnPIoY2djXg6edoYw8dCOHSlpRQ+9XtVOdceNBraT5xQicdkobKpg/Qi\nq0ikF9dz6kzTOafpcHPSWkUhwIPYAHci/NwI9XElzMeVEG8XFEVBCMHRrSXUZFWyrTCXhsrBhan0\ndD0JC3ozuTq76fEP96CmpAVDuAfJS0NJWBgsE/tJRg1H/7JuwFoEyh04W5UnFGjD6m0VAVQpirJC\nCDHI2vkiJuszVI99wTMcEg6LsPCTHT9BCMGTS54kwmtkKgs6QsP7H+D39B/oMtputbgtWEDQw7/A\nJSFhzOZzIalt6eRAQR178mrYm1tLfk3rOV3Hw1nHzHBvZoR7MyPMm+RQb6L83FRbRK0NnWj1Glzc\ne6vCKYrCyV2lNNUMXlToLPbEZc1tSbh7O+HqKdO9SEYfR8XjD8BtwEYhRCmAoijhwD+AfwGfAP8B\nngOuGYV5jn8y1a61TLvKoWFvZb5FemU6ANd9fB33p97PzQk3j6pvvKW1lYonnqTxww9tTKr6yEiC\n/uchPNasuWj984UQVLRa+M+hEg4V1nGosP6cxEKrUUgK8bLaJiJ8SAn3Ji7AQyUUAC31nRRn1FKS\nUUd5bgOtjV0sv3kqM1aGq/oZwjwGFA9ndx3R0/2JmmEgPMHXrkD4h9tmjJVIRgtHxeNx4JqzwgEg\nhChVFOV/gA+FEP9UFOWXWF16Jx8djVZbR18SNww5rLCxkBcOv9Bz3G5qZ2fpTm5OuHmEJ6im9rXX\naPzwQ5t2nxtuIOiXD6NxOf8Kh+MNi0Wwv6CW/x45w/asKqqbO4Hjw7pGsJcLKRHepET4kBrpy8xw\nH1ydbNPFm4xmynMbKT5VS3FGHXVnbIWpqtDW48k/3IOCY9Y4Go1GITDai7AEHyKm+RES5y1Td0jG\nFY6KRzBg7xPFGTjry1mJ1Y138pGz1Voh8Cx+cUPWKbcIC4/ufZQOc++Tpqfek8cWPTbqT/yGO+6g\nees2Ok+fBkBxcSH40UfxufbiWjTWtnTydUEd+/Jq2ZZZSXmjY1tCADqNwoxwb1IjfUmN8mV2pC/B\n3oOLavaBCg59VkhDVTtiiAyxlXZiLWJSAnB21+Mf5kFAlKe0V0jGNY7+dW4DXlIU5S4gvbstFfgr\nsLX7eAbWxImTj8yP1cfTNgyZPXfT6U0crlJn3f3Z/J8R7D765Sw1zs6EPfcsBdddj9HbmykvvYRL\nguPpU8YrQggyypvYmlHJtsxKTpbZLy1qD61GITnUi3nRfiyN92dejB8ezvb/PYxdZlrqOvANdrc5\nV18xtAeWVqfBxU2HxWxRrSYCIj0JiLQtcyqRjEccFY87gNeBr4GzeRM0wBfAnd3HzViDCScXxnbr\nyqMviVcOOqSspYznD6ujyJeFLeOqOMfsJMOhIzOTpk8/JeCBB1QrGufYWCJfeZkDdXXMmMDCYTJb\nOFhYz5ZTFWzNqHQ4OaCTFubFGJgX7ce8aD9mRfjgPoBYgNU1tuhELQXHayjJrMPL35VvPbpA1ccv\n1FZMwPocERTjRWSygYgkPwIiPWWktmTC42iQYBVwmaIoCcBZ95vTQojsPn12jML8xj/5aWDss6ft\nGQJhA7u2CiF4fO/jtJt6P+Tc9e78etGvR3y7qvnLLyl78CFEezv6iEh8b7pRdd5t7lxISxvRe44F\nFY0d7MyuYld2DV/l1jgcwe3mpOXS5GCumhWKuewUa1cPXl2uvaWLnIOVZB+opLJAvYqpL2+loaoN\nn8DenVqfIDcUjYKwCDwNLoQn+BKZbCA80VflWSWRXAwMN0gwC8gapblMTPp7WSVeYc1pNQAf53/M\nvvJ9qrafpv50RLerhBDUvfZPqv7wB2ttEaDyt7/FdeaMCVlHw2wRHCttYMfpKr7MrCKj3LHtKEWB\n5FAvFsUaWBRnYGGsATcn6598WnmG3TEWi6D4VC2n95ZTcLwGi3lg20Xh8RpmrY3sOdbptdzw87l4\nB7jiZCd5oERyMTGcSoJTgeuxxnuo/ASFEN8d4XlNHGJXQkuFNUDQYhzUy6rL3MX/Hv5fVdv84Plc\nP/X6EZuOMBqp+O1vadj0trq9q4uWtLQJIx5tXSZ2nK7my8xK0rKrqWvtGnoQ4KLXsDQ+gEuSgliV\nGEiAp+MR/qYuM2899jXNdUMb1j38nO16P0mbhWSy4GiE+RVYs+cewWooP4i1HK0zsHvUZjcRmHmD\n9auj0Wr7iF46YNeK1go8nTypbKsEwEnjxGOLHhuxNOvmpibK7ru/JzdVD1otwb96BN+bR9cF+Hzp\nMlnYlV3NR8fOsC2zkrYux9KS+7jpWZ0QyCXJwayYGmDXfbY/Qgg6Wo2q7SSdkxa/UPcBxSMwypOY\nFH+iZ/pjCPO4aONgJBJHcHTl8RvgcSHEU92VBL+NNdL8DWDfoCMnCy7eMGPwFUSkVyTvXfUeWwq3\n8OLRF1katnTEIsq7Sksp+f4P6MrLU7VrPDwIe/55PJYuGZH7jDQms4UDBXV8dOwMn5+scMh+oSgw\nO8KHlQmBLJ8awIwwb7QOZIEVQlCSWUfW1xXkHRV0ZJ3m8h+ok1gmLQml6GRtz7F3oCvTFocwdX4w\nnn4XX/yLRHKuOCoeCcDZfRAj4CaE6FAU5TfAp1gjyyUOoFE0XB5zOeui1tFldmwrZig6c3Mpuv12\nm0JN+tBQIl76G85TpozIfUYKo9nC1oxKtmZUsv10lUOC4emiY/nUANYkBrJiagAGD8e3o4QQlGbV\nc+CjAiryG3vaS07XYTZbVJ5PUTMNePm7EDrVl6TFIQTHecsVhkRiB0fFo5neIMFyIB442T1+chZ0\nEGLIWI7B0Gl0I1KH3NzYSNF3brOpJ+6akkL4X/487lKn51Y1c89bRzhdYRsk159gLxeumBnCuqQg\nUqN80Q/TvbWrw0RuehUZX52x8ZYCMHaYqcxvInSKT0+bVqvhlt8sskkxIpFI1Dj66fU1sBTIwLrS\neFZRlBTgWibrttWJd2Dn0xC/zlotMGoJ6O3XQyhpKiHcM3xUnmC13t4Y7rqTqt/31t3wWn85Ib/7\n3bhIM9LaaaKju6TqtsxKHv3oFB3GgbPT+rrpWT8jhKtSQpkX7XdOH+L1Fa0c215K9tcVGDsHtps4\nuWjt2jekcEgkQ+OoePwUOJt17THAE7gOyO4+N/nI2Qq1udavr/8Kyx6ANbaZ640WIxs3b8TfzZ+7\nZ93NsrBlIy4iho0bEV1Gqp97Dp9v3kzwr36FMoi78GjT1mXik+PlvH2whPSi+iH7nzV4XzkrlKXx\n/sNeYfTFbLTw3h/T6WwduLxr9AwD+Ndx2fXLZLCeRHKODCkeiqLogESsqw+EEG3AD0d5XuMbiwXy\nvlS3xduvVb6jeAdV7VVUtVdx95d3syhkES+te2nEBcT/rjtxmTYN96VLLtgefW5VC6/vK+SDw2U0\ndw5dm/uKmSHctiiaOZE+6EboQ1yr15C8NIzDW2zrk0Qm+THvyhiCY7xJS0uTwiGRnAdDiocQwqQo\nyvtYBaR2qP6TgvIj0NbnrXD2hvD5dru+naWOt4jwjDivD3dzUxN1/3wd/x98H0Wvjlr2WDawm/Bo\nYTJb2JFVzev7CtmdUzNkfwBXvZbHr07mhtRz38qzmC3kHammubaDOZeqy/TOXBXO0W3FWMwCNy8n\nEheHMG1xiCoaXCKRnB+Oblsdw2okLzzfGyqK8iPgISAEOAXcJ4QYMlZEUZQpwGFAEUJc2MIFuf1W\nHbErQGv7VuY35HOg4oCq7abEm875tsJopPSee2k7cID2E8cJ/9Of0Ljbz6c02lQ0drDpYDFvHywZ\nMlutr5seRVHQKDAv2o8HLplKfOC5BdMZu8xk7D7Dse0lNNd2oNEoxM8NxMvQa29y93Fm3hUxeAW4\nEDcnUK4wJJJRwFHxeAyrkfxRrFl1VQUKhBB19gb1R1GUm4AXgB8BX3V//1xRlCQhRPEg45yATcAu\nYIWDcx49+idCnLLObrdNWZtUx3MC5zDV99yTEFY9+xxtB6xi1LprN0W3bSTilZfR+Y6Nw5sQggMF\ndby+r4jNpyowD5J23N/DmetTw7l5XgTR/ucvcF0dJk6klXLsyxLam3tdey0WwZEvilnxTXXFw7nr\no8/7nhKJZGAcFY9Pu7+/j6rWKkr38dAhvVZ+CrwmhHil+/heRVEuw2pD+cUg457GWrlnJxdaPDoa\noeyQui1ujU23NmMbH+V9pGq7OfHcI7ybv/ySutdeU7Upej0aV/seXiNJXWsXHxwp4+2DxWRXtgza\nd06kD7ctjuby6SE46c7/id9kNHMirYz0zwvpbLNvR8ncW86Cq2Jl8kGJZAxxVDxWne+NulcPqcAz\n/U59ASweZNwVwAZgNtbcWheW8uMg+ria+ieAd5hNt0/yP6G1T7Zdg4uBtZH2jepD0VVaypmfq7VV\nFxJC+It/GVV33MPF9by6p5AtJyvoMg/sXuuq13JVSii3LIxkZrjPgP2Gg7AIcg5Vsv/D/AHThWh1\nGhIWBJGyJlIKh0QyxihCDF7xbMRupCihQBmwQgixq0/7r4FbhBAJA4w5BFwrhPhaUZSNwJ8Hsnl0\nF6u6CyAoKCh106ZN9roNSEtLCx4eg5tTwkv+S3zeP3qOK4JWcXrafTb9ni5/mtKunqq9XOp9KRt8\nhi5Na4PRiN8zz6Iv6vUeEhoN9Q8+gDE2dvjX60f/12wRgkMVZjYXGslvHFgwAELdFVZH6lkcqsNN\nP7IeXrXZgorD9v82NTrwmwKGBAWdy7nd15Hf9cXGZHzNMDlf9/m85lWrVqULIeYO1W84WXVnAN/H\nmhDxu0KIckVRrgGKhBBHzmmWQ/MG8FchxNeOdBZCvAy8DDB37lyxcuXKYd0sLS2NIce89y/VYfDs\nSwleqB6T15BHaVGvcGgUDQ+ue/Cc0q5XvfACtUVqt9Oghx4i6faNw76WPc6+ZiEEO7Kq+MPmLE4P\nUg1Pq1FYNy2I7yyOYlGsYdTcgrsWmngzZ5/KvuHkoiVlTQQzV0ec90rDod/1RcZkfM0wOV/3WLxm\nR7PqXgJ8BHwOrAbObrTHARsBR4pf12CtQhjUrz0IqBhgzGpgRbehHqw2Fo2iKCbgR91iMbaUH1Mf\nh6TYdPms4DPV8YLgBeckHO2nTlH78iuqNo81a/DbeNuwrzUYX+fX8uwX2RwoHNjvIdrgxk3zIrku\nNYxAz5HdKqs904KiKPiF9BrWnVx0zL8ylp1vZaHRKkxfEcbc9dG4ejgNciWJRDJWOLryeAL4qRDi\nxe6sumdJAx5w5AJCiC5FUdKBdcA7fU6tw5ru3R4z+h1fDfwSmI91C2zs+e5mqDgB5UfhzFEI7j9F\niPOOY1bALI5WHwVgfez6Yd9GdHVR/vAvwdybXkMb4E/ob58csaf9I8X1/PFgO6c27x+wz8qEAL63\nNIal8f4jusowGy3kH6smc285JZl1RCYZuPJetRAnLQmhobKN6SvCZIyGRDLOcFQ8pgOf2Wmv4cL1\n6wAAIABJREFUA/yGcb/ngDcURTkA7AF+AIQCfwNQFOUpYL4QYg2AEOJk38GKoswFLP3bxxQ3P2tc\nR+zATl/rY9ezPnY9pc2lfF7wOWsibb2xhqL95Cm6CgtVbSGPP47W5/wN0rlVLfxh82m+yKi0e15R\n4OqUUO5ZPYX4wJHfK87aX85X7+bS0dK7JVV8qpaKgkaCY7x72jRaDUtvGF8ZgSUSiRVHxaMOCMM2\nSHAOUGrTewCEEG8rimIAHsEaJHgSWC+EOLupH4J1K+yiINwznDtn3nlOY93mzCbmgw8o/+UvaT9y\nBK8rr8Rz9erzmk9Vcwd/2prN2wdLGChEY1VCAA9dmkhSqNd53WsgMvacYccbp+2eO/hJoc3qQyKR\njE8cFY+3gD8qinIj1rgOnaIoK7C63b46nBsKIV4EXhzg3MYhxr4GvDac+01knGNjiHrzDer/vQmv\nK4a/9XWWDqOZf+wp4C/bc2kdoDrfolgDD1wylbnRw1lIDo/c9CrS3rQvHIFRniQvDR21e0skkpHF\nUfF4BOuHdhFWo3VG9/e3gN+OyszGI2XpYJgCLvafytuMbbjqXEfUNqBotfjdess5jRVCsOVUJb/9\nLIOSuna7fWK9NTxxwzyWxI9e3Y/q4mZ2vHma6mJ1DQ+NTmH6sjCmLQnFP3xyuVJKJBMdh8RDCGEE\nbumOyZgNaIAjQoic0ZzcuMLYDv+3DoQZDPFWL6trX1bltHpy/5OcqDnB+hirzSPKK2qQC9pibmjA\n0tGBPnj4nln9yals5vGPM/gq136ywhh/dx66NAHXmtOjKhwALfUdNsKhaBQuvWM6sbMCRvXeEolk\ndHAof4SiKNcoiqIXQuQJId4VQvxnUgkHQGWGVTjAWsOjLF0lHEazke0l2ylsKuTFYy+y4YMNHK8+\nPqxbVD37HPnrr6D21dcQxqFLs9qjw2jmqc8zueyF3XaFw9tVz6NXJvHF/ctZPyNkRFdJbU1dFGc4\nlnh59XcSpXBIJBOY4dg82hVFeQd4QwixZxTnND4pP6o+7hffcbDyoE06kmRDssOXbz96lIZ3rB7M\nVU8/TeP77xP+17/iFG6b+mQg0ovqeOjd4+RXt9qc02oUvr0wivvWTsHHbWRjJTrbjBzZWsyx7aVo\nFPj2bxcPHMSnwLIbp5K4MGRE5yCRSMYWR8UjCGteqW8BOxVFKcYqKG8KIexbQC82hggO3FmyU3W8\nImIFWo1j+SKFEFT2KSMLIMxm9IGOPZkbzRae2ZLFy7vzsZdtZnGcgceuSmZq0LmlQR8Is9HCiZ2l\nHPpMnbTwyBfFLLq212nOP8KTNRunoQD+kZ4YQqV9QyKZ6Dhq82jG6lX1ane+qZuxCskvFEU5LISY\nN4pzHB8MIh5CCHaW9hOPcMeT/zZv3Ur7UfXKJvhXj6A4Db1CONPQzj1vHeZwcYPNuRBvF369IYnL\npgeP6PaUEILcQ1Xs+zCP5lrbpIXHt5cwc3U47t7OAHj6uciVhkRykeFwbquzCCHOKIryZ6yeV49g\njfW4uDF1QVWGui24VzzyGvIoa+kNeHfSOLEwZKFDlxZGI9XPPqdq81i1CvdFi4Ycu/10JT/9zzEa\n2mztI9+cH8nD6xPxdBnZbLN15a3s2pRFWZatWAG4eupJvSwaZ7dh/2lJJJIJxLD+wxVFWQXcAlzX\n3fQ+1hodFze1OWDu6j32CgOP3i2ltNI0Vff5IfNx0zuWTqP+P/+hq2/iQ42GwAcGf0s7jGae3nya\nV/cU2pwL8XbhmRtSRtyDythl5tCnhT3lXfujd9Ey55IoZq4Ox8lFCodEMhYYOzrQOTmhaMa+Wqaj\niRH/iHWrKhDYjDXt+UdCiM5RnNv4oS5ffRygzh7f396xMnylQ5c1t7RQ8xd1vKTP9dfjHB8/4Jjc\nqmbu/fdRMsubbM6tSgjguRtn4es+8skDW+o67AqHRqOQvCKMeeujcfWUSQslktGivbmJmpIiqgry\nqSrIpbIgj7qyUm575i8YwiPGfD6OPiIuBn4HvO1oydmLiroC9bFvTO+pjjqOVavtISsiHLN31P79\n75jret9Oxc0N/3vuHrD/jtNV3PPWYZsoca1G4cFLEvj+8lg0mtFJke4b7M6cS6M49FlhT1tEkh/L\nb5qKT5BMWiiRjCSV+VZxqC0poqa0mNqSIlob6u32rcjLHr/iIYRYMtA5RVHWCiG2jdyUxiH1hepj\n3+ieH3eX7kb0qcyb6JfoUPp1Y1UVda/9U9VmuP129IGBdvv/c28hj398yiYnVbivKy/cPJvUqJGt\nY27sNKN3VnuLpV4WRfbBSsxGC0tvmELcnIBRq+chkVzMCCForq2hqjAf3+AQDOGRqvNf/v2vlOdm\nOXStyvxcklcMP/nq+XJOm9OKooQBtwPfBaJwvIb5xKSzmd5y7YBf78qjur0aZ60znWbrDt7y8OUO\nXbLj1CnVsdZgwO/22236WSyCJz7NsGvfuCollCevnY7XCBrFhUWQvrmQU7vPcMMv5uHm1bsVpXPS\ncsUPZ+Lh5yztGhLJMOhsa6U8N5vynNNU5GZTkZdDW6PV6WThdd9kyY3qFEQ+IaEOi0dro33nldFm\nOJUEtVjraXwPuAQ4jjWV+juDjbsouO4VuPrP0FAC9QUQMqvn1B0z7uCWabdwoPwAaaVpDtcp91y1\nivgvtlD94os0vPMu/j/6IVoPd1Ufk9nCQ+8e54Mj6tIlGgUevTKZ7yyKGtEnf5PRzLZXM8k7XAXA\nl69lsOGeFJQ+W2F+oe4DDZdIJN00VVdRknGCM1mZlGVlUFtWgt0gLKDujG1ict8Q2yShGq0Ov7Bw\n/COiCIqNJygmjoDoWFzcL0zc1JDioShKAnAH8B2gFWtw4CXAt4UQGYONvajQOYN/vPWrH646V1ZE\nrHDY1tFzyYAAQh59FMPGjehD1HEQnSYzP/73EbacUtfccHfS8udvzWFVov3trXOls93E5389Tll2\n71NMcUYdR7YWM+fS4eXokkgmC0IIOltbcelXL/zQJx9wZPPHDl2jrqzEpi04bipTFizGPyIKQ3gU\n/hGR+ASHotWNnxX/oDNRFGU31kJQ7wE3CiF2drf/bAzmNmlwilJ/OHcYzdz1Rjq7sqtV7UFezry6\ncf6I19pobejk4/93jNqyFlW7q6ceg8x2K5EA0NnWRnnOac5kZ1JbUkx9xRkaKsoJiovnpkd/r+ob\nHDd0ETOdkzOBMXF2+8bMSiVmVuqIzX00GErGFgF/AV4WQpwaoq/EAUw1Nej8B47B6DJZ+OGbtsIR\n7uvKW3csJNIwsp5N7bWCd546SGtjl6rdN9iNDfem4GVwHWCkRHJxY+zsoOx0BkUnjlJ84hjVRQUI\nYbHpV1WQj7BYVLEWQf0FQVHwD48kZEoCwfEJhMRPxRAeiUY7cc3FQ4nHPKxbVl8pilIIvA78e7Qn\nNa6oyoTGMquR3DsCdFYDcnlLOf84+Q+WhS9jXvA8XHVDf8i27v+akjvvxOemm/D//l3oAtS5q0xm\nC/e/fZQdWWrhiAtw5193LCTY22XkXheQfbCCgu0CYVYLR1CMFxvuTsHFY2Sj0yWSiULhscN8+Iff\nYDaZhuzb1d5GQ1UFvsG9dgq/kDBi58wjMDqW0IQkQqYkXDDbxGgxqHgIIY4AdyuK8gBwA1bvqj9g\nTeV+haIo5UII+87HFwvH/g17XrD+rGhg7eOw5MfsKt3FpqxNbMrahLPWmVun3cp9qfcNeBkhBNXP\nP48wGql/800a3nuP4Ecewee6bwBWr6qfv3+CT0+Uq8YlBHnyrzsX4O/hPGIvSQjBwU8KOPhpoc25\n6BkGLrlzOnqniftEJJEMRmdbK3VlpdSdKaWurITaslIu/cGPcfXs3Q72j4hySDgAnN3daa6pVomH\notFw7c8eHfG5jyccjfPoAN4A3lAUJR7rauR+4ElFUbYLIS4fxTleWPoGCAoLuFu3nHaX7e5p7jR3\n4uU8uB2iZedOVfJD0d6OU2yvy+9zW7N5N13tdRHj784bd8wfUeEAawxH3pFqm/ZZ6yJZdG3cqAUa\nSiRjiRCCpupK6nJPs7M0n6qCPOrKSmipt41zri66ksjpvfnqPPwM+IaEUV+u9nQ0hEcSmjCNkPgE\n/ELD8Q0JxdXLe1LGO51LYsRc4OeKovwS2IB1NXLxYhMgGEOnuZOvy79WNS8NWzrgJYTFQvUL/6tq\n81ixArfZswF4/3Apf96Rqzof5uPKm3csINBzZLeqAJxcdGy4J4V3nz5EW2MXGp3Cym8lMm2xzHwr\nuTiwmM28cu/3aKm1FkQrGKJ/dVGhSjwAIqfPxGI2ETVjNlEzZxGRPFO1OpnsnLPflxDCDPy3++vi\nRAhb8fCL4VDFITrMvanIg9yCmOIzsHdF8xdf0JmZqWoLuO8nABwqrOPn751QnfP3cOLNOxYQ5jN6\nxmpPPxc23J3CBy8cZMMPZxMa7zNq95JIRhrrqqKK8pzTlOdkMWXhEsITe4uvabRa3L19e8RjKKqL\nbOVl1ca70Oqk3W8gxo/T8HikvR46+yQg1LmCRxC7M19TdVsWvmzAZasQgpqXXla1eV5+GS7TplFS\n18Zdb6TTZe714HDSaXjp23OJ8R+5YLycg5W01Hcy+xJ1CoSASE+mXKFI4ZBMCIwdHRSfOkb+4YMU\nHEmnubZ361Xv4qISD4CwhGlU5ttWy9ZotfgEheAXFo5fWASGsAiCYm3jt6RwDI4Uj8GwSYgYDYrC\n7tLdquZlYcsGvER7erp61aEoBNx7L62dJu58/RB1rWpPpz9eP3PE8lQJIdj/YR6HtxSDAoZwdyKT\nDKo+irRvSMYpxs4OynOyKck4Tsmp45TnZGMx2zdil+fYFjQNTUji+PYtuBgCSEydT3DcFPwjo/EJ\nChlXwXYTFfkODka9rXiUtZRR3Fzc06TT6AYt/FT35r9Uxx6rV+MUE8P9bx3mdEWz6tyP10zh6lmO\n1ywfDItFsPNfp8nY0+29JWDr3zO44eG5MnZDMiHY994mDv73XYf6lufmYLGY0fQp/Rw/bwH3zHub\n3V99xYqVK0dplpMXKR6D0V88/GJIr0xXNc0KmDVg4SdjRQXNW7eqL3HrLfxlRy6fnahQta+fEcx9\na4aOSnUEs8nCtlczyE2vUrV3dZqoKW6R4iEZF7S3NFOWeYrSzJN0trVx6Q9+rDofPi15UPHQ6Z0I\nnjKVkCmJhExJQFiENYigG7ntNLpI8RiMukL1sa+teKQGDZxCoH7TJjD31t5wio/ja59Ynv3okKpf\nYrAnz9yQMiIusmajhc9fPkHRiVpVu4uHng13pxAUI71FJBcGk9FI2elTFJ04StHxI1QV5vckC9Ro\ntay+/S70zr3ehWEJSSiKRhXV7RMUQvSsOcTOnkd48gz0TiPrxi5xHCkeg2GnjsfhU+pkZ3OCBi7h\n7nfLLSg6PfVvb8JcXYO48VYeeve4Krmmr5ueV74zFzen8/9VDCQcHr7OXPWTWfgGy4y4krFDWCxU\nFxdSmnmKouOHKT51HFOn/eKjFrOZ8pwslbuss5s7cXPn4+zmTkTyTCKSZ+LlH2B3vGTskeIxGB4B\n4BkKzWcAEL7R/Hz+z0mvTCe9Mp3MukxmBcwacLguIICAe+7G/647adyylfsq/aht7Q1Q0moU/vKt\nOUT4nX++KrPJwuZXTtoIh3egK1f9ZJbcqpKMOSaTkX89/NMBjdz9Kc08aRNrcfWDj4zG1CQjgBSP\nwbjhNet3Ywc0FKH4xbBEO5UlYdbCikaLEb1m6H1VxcmJj/yTSdujzi1535opLI4fOEmio1jMFra8\ncpLC42qfdt9gN66+fzbu3nJpLxl5ujraqSkupDzHWuRo7pXfUGWI1Ts5ExQbR3nOwEWNAqJiCE+a\nTvg065dk4iDFwxH0LhCQYNvsgHAA5FY189tP1UGCc6N8+dEqW9/yc0FgrfLXF58gKRySkcFsMlJT\nUkxNcSHVRfnUlBRTW1ZiE4DnGxpuk148LDFZJR4efgaiZs4meuZsIqen4OYtY4wmKlI8RoHWfftw\nio5GHxKCxSJ46N3jdJp6jX4ezjr+dNMstCMUY6HValh3exLObjpO7izDJ8iNa34qhUNy/ux88x8c\n3fwJJmPXkH0Lj6XblFONmp5CfXkZ4YnJRKfMwRAxstUvJRcOKR4O0mXuwmQxDeiWexZhNFL24EOY\n6+pwX7yYLYuu5Uixus9vrk4eETtHXxSNwvKbp+Id4Ep8apAUDonDmIxGKvKyMXV1ET1ztuqck4ur\nQ8IBUJGXQ3tzkyr/U/SsVKLHeVEjybkhxWMgivZBXT54BoNXKPtbS/jJ7p8xzTCN1KBUlocvZ17w\nPJthLbt3Y661Gq1LDh3neZ/Lrdte3VySFMS1s88/ELAko47wRF9VhLiiKMxaGznIKMlkp6O1haqC\nfGpKCqkpKaKmpIiqgjzMRiOB0XE24hGWmDTgtRSNBp+gEAKjY3tiLZxcR/ahSDJ+keIxEMc3Qfpr\nPYfpqddgEiZO1JzgRM0J2k3tdsWj4f33e35+ZfqVtPYRDncnLY9fnXzey/aj24rZ824uSUtCWHlL\nokwxIhkQY0cH+UcOknNgHwUnjpH+12cG7FtVlE9Ha4uqaFFIfAKKRoObtw9BMXEERMUSEBXTXVM7\nRAbiTWKkeAxEk7ooU3qnuv6FveBAU20tLWk7rf0DppIWoY4BuX/dVEK8z89l9tj2Eva8a03fnrGn\nHIuAVbcmyhocErvkHznEJ88/7VhnITiTlUnsnN6HIr2LC3f95VXcff2krUKiQjN0l0lKd2wHQLui\ncKrtjOr0nEDb4MDGjz8GkwmTouFvM69RnUsK8WLj4ujzmtLp/eV89R91ltDcg5XUl7ee13UlFwdN\n1VWYjEZVW8zsVHQORGF7GgKYtmyV3XoVHn4GKRwSG+TKYyCae3NPZTnpMYneNCPhHuEEuQepugsh\naHz/AwA2Ry2g1DOw55yiwO++MQOd9ty1uuhULTteV2cO1eo1rL97Joawi6s2smRg2luaqczLobq4\nEFOXNVrb1NlJ4fEjVBXkcc3//Jq41Pk9/Z1cXImZlUrOgb2A1U5hCI8kMDoW/4go/COiMEREycht\nybCR4mEPUxe09m5T5To5qU5PM0yzGdKRkUFndjatOmf+lXiJ6tyNqRHMijh3f/aqoiY2v3wSi6U3\nr4lGp3DFD2cSkeh3zteVTAxyD+4n5+s9nMk5TUNF+RB996nEA2D6qnV4BQbRonPm0utukPmgJCOC\nFA97tKgz3ua5qZfy9qoGNn7wIQDvTllFg4tnT7urXstPL5l6zlNprG7nkz8fw9TZu/JBgXW3JxOR\nJIVjMlCaeYKM3Tsc6pt36Gub1OSxc+YRO2ceaWlpUjgkI4a0edijn7E811ldRzzOJ051bOnqounj\nj6l28eaDuOWqc3cujyXI69zqkHe0Gvnkz8dob1bvYy+9YQrxqYEDjJJMNIxd1m2nPW+/wZf/+KvN\n+dCEgd1lVSgKviFhtDU0jPAMJRJb5MrDHs39xEOd+YN4X3VakZYdaZgbG3lj9o106nq3uPw9nPn+\n8thzmoIQgs0vn6Chsk3VPntdJCmrI87pmpLxQ1dHO8Unj5O1dxd56QcwdrQDoNHqWH7L7Tapyc+i\naDT4R0QRFDsFD9/eipNeAUHEzpmHu8/IVKGUSIZCioc9+ohHg0ZDDb1ZQfUaPZGe6kA8j2VLEY//\nnu2H1Qu5n66birvzub3FiqKQvCyMirwmzN2pTabMDWTRtXFDjJSMN0xGI4XHDlNy8hhVhfnUV5yh\ntb7Obl+L2UR5TjaR02f2tLn7+LL69u8TEBlDUFy8SlgkkguFFA97NPW65Rbo1W9RjHcMOo26TePm\nxtsucZiVop622AB3bpwbfl7TmDI3CA9fFz578Ti+IW6svm2aDAicAAghVK6twmzm4+eeGmZq8pmq\nttmXXTmic5RIzhcpHvbwCoWwudBczuzmCnZOu4e8yDnkNuTiorV96qtp6WTTwRJV2w9XxJ2Xa+5Z\nQuK8ue5nqbi46dHptUMPkIw5Ha0tFB0/SvHJo5ScOsGq2+4kZvbcnvN6FxeC46dyJitjwGt4+PoR\nPWsuEUnTiegnHBLJeGTMxUNRlB8BDwEhwCngPiHE7gH6rgTuB+YD3kAu8LwQ4h+jOsmFP7R+AVjM\n+FnM+Omc7KYjAfjn3kJV1twQbxeunjX8/FXtLV3onbS26dUDZb6g8UZTdRVZ+3aTf/ggZVkZCEvv\n77/41HGVeABETp+pEg9rXqhgImfMJnHRMsISk1A00n9FMnEYU/FQFOUm4AXgR8BX3d8/VxQlSQhR\nbGfIYuAE8AegHLgUeFlRlA4hxFtjMmmN1vplh86CAhqKy/jnXnWE9x3LYnHSDe+DwGK2sOXlk3S2\nm7jsrhl4B8jKf+MJi8VMbWkJpZknydq7i7LTA68iSk4dt2mLmzOfztZWIpJm4B8ZhVdAEFqdXPhL\nJi5j/df7U+A1IcQr3cf3KopyGfBD4Bf9Owshftev6a+KoqwCrgPGRjwGoe711/n7wXKapvfuR/u4\n6bl53vC9oQ59VkhZttXF8j+/O8jajdOISZFRv+OFo1s+ZcdrLzvUt6akiK6Odpxceh8AguOnEhx/\n7vE+Esl4QxFCDN1rJG6kKE5AG/BNIcQ7fdr/AkwXQqxw8DqbgVIhxB12zt0F3AUQFBSUumnTpmHN\nsaWlBQ+P3lQfnZZOzhjPEKwPxlXTbyVgNOLz84f53tL7qXX17mm+Ok7PtVPUEelD0VYjKPhSWEsC\nduMeBFErlVHPKdT/NU8WBnrdHQ11tNfW4Bun/qBvqThD1gcDP6+4+BrwjozBMzwKj5AwtPrh/Q2M\nBfJ3PXk4n9e8atWqdCHE3KH6jeXKwx/QApX92iuBtY5cQFGUDcAaYIm980KIl4GXAebOnStWrlw5\nrAmmpaWxcs5UOPw6eAaz39LCgxkvARDsHszayLX8bP7PAGj89FNe9U9SCYerXsOj31qJn7vjHxxd\n7SY2PXkAREfvdbycuPGB+bh5jf4HUFpaGsN9ny4Gzr5uIQS1pcXkHfqanAN7qczPRe/swoZbvoPe\npdc5wmQ0kvvxfzCbej2mQqYkkLhkBXGpC/AODLJ3m3HFZP9dTybG4jVPmE1XRVGWYN2q+rEQ4sCo\n3aj6NKQ9BUCelwcYrClAKloraOpq6ulW9/4HvBO/UjX0pnmRwxIOgF1vZ9Nc26FqW3PbtDERjslK\nR2sL9fk5bMvLpPBYOo2V6nQ0xs4O8tK/JnFJ72JYp9cT1V0oKSwxmakLl+ITFDym85ZIxhNjKR41\ngBno/4gWBFTYdu9FUZSlwGfAr4UQtvkbRpI+qUly9epCN/E+1shyY3k52wqaKZvfmyJEp1hTkQyH\nnEOVZO1Xv/SZq8KJSjYMd9aSIRBCcHzbZrL27aY04yRCWAbtn7lnp0o8AK792aOjOUWJZEIxZr6B\nQoguIB1Y1+/UOmDvQOMURVkOfA48JoR4fvRm2E2fOh79s+mezWnV8OF/eWfKStW5q2aHEebjuIdU\na0MnO/+dpWrzC3Vn0TdkBPlooCgKx7dtpuTU8cGFQ1GISJ7J1AV2d0YlEkk3Y71t9RzwhqIoB4A9\nwA+AUOBvAIqiPAXMF0Ks6T5eCXwKvAi8pSjK2X0CsxCimtGge+UhgDw7Kw8hBGnbDpI95Ruqcz9Y\n4fiHvhCC7W+cprO1d/9co1NY991kGQg4AtSWltDe0kR4YrKqPWHxMqoK82z6a3U6IqenEDd3AbGp\n8/H08x+rqUokE5YxFQ8hxNuKohiAR7AGCZ4E1gshzub1CAH6fgpvBNyAB7u/zlIERI/KJLuLQFVq\ntTT3iRB307kR4h5Cx6kM3vJWfyitjvdlapAnjpK5t5ziU7WqtgVXxeIfPrk8QkaSjtYWTqV9Scau\n7VQV5hEYHce3n35B1Sdh0TJ2v/UaYPWOSlq0lKgZswhPmq5yq5VIJEMz5gZzIcSLWFcS9s5ttHO8\n0V7fUaN72yrfyXbVoSgKJzencSQwQXXu7nWJw7pF1HQDUdMNFJ20CkhIvDez1kYOMUrSH4vZTFVB\nHid2fEHG7h2YOjt7zlUV5lFbWoIhvDfmxjswiHV33UtE0nSOZeVMOg8ciWQkmTDeVmNG97aVvYSI\nAB9m1EJgdE97ioeF1KjhFWVy93bmirtnkvHVGQ58XMCa25LQyISHDtFQUc7pvbsoOXWc8pwsjJ0d\nA/Y9vXcnS268VdU2c82l1h+ycuyMkEgkjiLFow+KxQytVQAU9rN3RHtHYzZb2B41F9p7o/m+uezc\noobPplxPWBgs7RwOYjIaeePnP6arvX3wjopCZPJMAqPPrZaKRCIZGikefdAbG6DbE6ew38oj2iua\nrwvqKO8jHC5ahSsWOPYB1dVuwsnV9u2WwmGL2WSkNPMUemdnQqf21ovX6fXEz104YElWDz8DKevW\nM33lWjz8pLuzRDKaSPHog3NnrxFbI0AvwNi9mxTtFc2LW0tV/S+bEYKni3qFYo+OViNvP3mAuNnW\nYk5avcyeao8z2Zmkf/IhBccOY+xoJ27uAq556FeqPolLV6rEw83bh7CEJBIWLyd+3kKZbFAiGSPk\nf1ofLBonSL4Wmsp5qfkMZp8ZlG/4A4VNhRicQ9l8cpeq/3WpjhV72vlWFi31nRzbXkJpdj2XfC8Z\nvxD30XgJE5LqogK+2vQ6+YcPqtqLThzFZDSi67OFGDk9hdjU+cTOnkvkjFn4BIWMev4viURiixSP\nPrR6RMOG13qOtUA4EO4Zzqatx2jrMvecC/ZyYXHc0PEAOYcqyU2v6jmuLW0ha385i66NH2TUxY/J\naCTv0H5ObP+CouNH7Pfp7KQ04wTRKXN62rQ6Hdf+z6/HapoSiWQApHg4yNufHQbn3nQk10wPQDuE\nh1RbUxe7/p2tavOP8GD+lZPXkCuE4NjWz9nznzfpaG4asJ+7jy8xs+fi5u0zhrOTSCRtgpEcAAAb\no0lEQVSOIsXDAcpKKjnSRzgAro5wHnSMEIKdb2XR0WrsadNoFdZuTEI7zEJRFxPlOVl8+Xe7YT4A\nhExNZMmNtxKZPFNW1pNIxjFSPOyQVpKGyWIi2iuaSK9IvvhSva2S0FbJtNlXDHqNnIOV5B9VZ1CZ\nf2UMhrDJE0XeUFFO4bHD+IaGETVjFgChUxOZd9V1HPzoPVXf4PipLLrum8TMnittGBLJBECKhx1e\nOvYSJ2tPAqBRNCSf/gl9kwGv9B28gFZTbTs7+21XBUZ7MXvd5IgiL8vKJP2TD8g5uA+EYPblV/aI\nB8DSb36HqsJ8yjJPMWPNpcxYfQkBUTEXcMYSiWS4SPHohxCCwqbCnmOzBY6afazW825WzYoacLzF\nbGHbqxl0tfcmPdTqNKy5bRoa7cW7DdNUU03W3l1kfpVGdVHBoH01Gi1X/OR/aGtswBA2/JK9Eonk\nwiPFox+1HbW0GFt6jr0b4yjT9to3vDtbSF2zbMDxh7cUUZ7bqGpbdG3cRema297STM7+PWR+lUZp\n5slhjXX18MTVw/FkkhKJZHwhxaMfhY2FqmNDzUzK+hzP76xE7+U14PiwBD88DeU91QEjk/2Yudqx\neJCJRPqn/2XXv17FYjYN2s8/IoqwhKQxmpVEIhkrpHj0o++WFUBte5TqXVoaOriXVUicNzc9Mp9d\nm7IoyahjzW1JF6UBOCgmbkDhUBQN0bPmkHrFNUROT7koX79EMtmR4tGPoqainp8tJg/KdL0uuoqw\nsGrh0OnXnV11rLs9mdbGzgldi7ytqZFTads4vXcX1/7sUTx8e7MHh01Lxi8sgrqykp624PipTFuy\ngoTFy3H38b0QU5ZIJGOEFI9+9N22cq6ZQmufc1MaywhftNrha7l7D75KGY8IISjPOc3RLz4je99u\nzCbr6iJ7/x7mXH5lTz9FUZi55jKObf2MaUtXkrh0Bb7BoRdq2hKJZIyR4tGPvttWlhZ10aeF1KNx\ntzV8H95SRHCsN6FTJm40tLGjg8w9aRz94jOqC/Ntzmft260SD4DZl21gzvqr5LaURDIJkeLRB7Mw\nU9pszZwrhEKDZYrq/Lq1qTZjqoub2fdhHgiYMjeQRd+Ix9PPZUzme75YzGaaSgrZ/OKfyDmwd9A6\nGeU5p2lrasTNy7unTaOV6eQlksmKFI8+1JpqMQnrNo2lIxzMvasMb1c9i65dq+ovLIJdm7KhO2Yw\n51AVDVXt3PCLiRElfWrXl+R88u6gfbyDgklZt56kZatUwiGRSCY3Ujz6YBZmloUto7CpkLzaONW5\nJfEGdP2C/I6nlVKRr47pWHB17LgUDmGx2OSKip+3iK0v/xlhsajaFUVDzJy5zLrkCqJnzpY5piQS\niQ1SPPoQ4hTCiyutSftu/b/9fFXVWxxqUb/0643Vbez/ME/VFj3Tn6jk8VXBrrm2hpNpWznx5Rdc\n9/BvMIT3RnS7enjiFRlLY2EuYM1kO33VOmauuQyvgMCBLimRSCRSPOzRZbJwKK8G6F1BLIrtFQVh\nEWx//TSmrt4ndidXHSu+qTawXyhMRiO5B/ZyMm0bRSeOgrDuqx3b9hmrN35f1dc/cTrh0dFMW7KS\niOkz0GikHUMikQyNFA87HC9toEP0CodfZzNhtSUQaK2nfXJXGWdyGlRjllwfj4fvhXXNbayq4Ni2\nzZzc/gXtdmplZOzczrKbb0Pv0mvQ94mJZ+XKlWM4S4lEcjEgxcMOe46qE/vNrMnDOeYaAFobOtn3\ngXq7KjLJj2mLQ8Zsfv3paG1h5xt/52Tatp5Vhj3MJhOV+bmEJ00fw9lJJJKLESkedtibcQboNRKn\nuhnRdD+t730/F2NnbzlavYuWlbcmXjAjeVlWJp88/3ta6moH7BMYE0fyirUkLV+Fi/vkqScikUhG\nDyke/eg0mTnSpH56X5xgreXRUNlG9sFK1bmFV8dd0LgOdx9fOltbbdqd3d1JXr6G6avWyVoZEolk\nxJHi0Y+jxQ109SneYWhvIGGRtZCRT5Ab1z2Uyq5N2VQXN2MI82D68rFLyWExm2mpr8XLv9cTyico\nmOW33M6X//grAH6h4cy96hskLl6O3nliBCtKJJKJhxSPfuw5Vqg6TqktwG3WN3qOg2O9uf7nczm9\ntxyfYLcxKfDU1tjAie1fcGzr57h6eXHrU8+rtslS1l1O7qH9BMXGs+j6b6HT60d9ThKJZHIjxaMf\nezPV9o65bl1oXF1VfTQahaSlo7viMHZ2kH/4IBm7tlNwNL0nkK+5tpoz2acJS5jW01fRaPjGLx6T\nbrYSiWTMkOLRhy6z4Hg/D9fFicFjdv+WulpyD+4n/8hBSk4ex2TsstvvyOaPVeIBSOGQSCRjihSP\nPuQ1WOjqs+oIbKsnYnYK7zx1kJmrI5gyLwiNZuS9qoTFwpa/vUDmV2lYzOZB++qcnHHx8EQIMS7T\noEgkksmBFI8+nC5roe9bMrM2n+y6eVQVlbHt1QzSPy9k4TVxxM4KGNH7KhoNxs7OQYXDEB7J9JVr\nSV61Ttb+lkgkFxwpHn1wc9UT7Soo7M5MPstdcGJXRc/5+oo26itagXMXj7amRsoyTzFlwWJV+9wN\n15K9/ytVm6chgCnzF5G0Yg2B0eMz4aJEIpmcSPHow6VT3HjqzpVUNXWwL78WfUY0Bbt64zpcPfXM\nWBl+TtfubGsj/dMPSP/0QywWCz+YkYKzW2/K95ApCYQmJNHe1Mj0VeuInT0XQ0SUFAyJRDIukeJh\nh0AvF9ZG+/P/2zvzOKuKK49/fw0CsogCCYsgBDGAQGIaUIhsIYpRVBzcsphI1OioUWfU0cmYz6QT\ndZJMwOUj2TAxJJF84ijRGFCMxEBcwICIiOASlCgiu0Kj7NT8cerR1Zf3ut97vTz6vfp+Pu/TfavO\nrVunbt177q2qe879971RLb38tJ60aJVbk320bSsvzpnF0idmsXN75YH0FU//lc+cdmY12bOv/xat\nj2gfXaBHIpFDnmg8MrDwkVXVvOa2bt+CgaOOzmpft38/7762gpXPzGPF/KfSrppaNncOJ4wbX+3N\nos2RR9W94pFIJNIIROORhvVvbePVheuqpQ09oxfNW9S8HHbrhvW88NgjvL7wWT58f0tGuVbtjmDA\n6M/j3H6kuMQ2Eok0PaLxSOCc45kHX6+W1qFbm6w+Clz06ExeevKxjPkt27Sh/PQJDB5/Di1bt65z\nXSORSKRQROOR4I1F61n3ZvUvBUecf1xWbkiGTbyQV+b/hb27d1VLb9uxE0PGn8OgseNocXg0GpFI\npOkTjUeAc45Fs1dXS+v1qU706N+hWtq2jRtY9Kc/UH76WRzVtWoepG2HjpSfcTZ/f+RBDmt1OMcO\nPpFPnnQyvQefSLPmsakjkUjxEO9oAZI4+7oTWPDwKt5YtJ6yZuLkc/scyN+85m3+/seHePXZ+ezf\nt499e3Yz7oprq5Ux9Oxz6dCtO32Hj6R5ixaNrUIkEok0CtF4JGjXoRXjLh3AoDHd2bymkvYfP5w1\nK5azePbDrFr8fDXZV+Y/xfDzvky7jp0OpLVq05YBoz/f2NWORCKRRiUajwx07tWWreuWMuO/prD+\nzTfSyuzft5dlcx/n5Au/2si1i0QikcLS6MZD0lXAfwBdgVeAf3POPV2D/CBgKnAisAX4OXCrczUE\n664D+/buYekTs1ny+KNs27gho9xR3bpz4tnn0n/kmIaoRiQSiRzSNKrxkHQhcDdwFfCM//u4pOOd\nc2+nkT8CeBL4GzAU6Af8CvgQmNIgdSwrY+kTszMajs69+3DiOefTZ+iw6AY9EomULI395nE9MN05\nd6/fvkbSF4ArgW+lkf8K0Bq42Dm3A1guqR9wvaQ7GuLto6ysGeXjJ/DUfT+rlt67fChDzppI9/4D\no7+pSCRS8jSa8ZDUAhgMTE5k/Rn47MF7ADAceNobjhRPALcCvYC36rmaAAwcfQrP/d8M9u7axfGj\nxlI+fgIdj+7REIeKRCKRJkljvnl0ApoB6xPp64FTMuzTBViTRj6VV814SLocuNxvbpf0Wh513FQt\nZcbDwDU5FtOkOFjn0qAU9S5FnaE09a6Lzj2zESqq1VbOuWnAtHz3l7TYOTekHqt0yFOKOkNp6l2K\nOkNp6t0YOjem7+9NwD6gcyK9M7DuYHHw6enkU3mRSCQSKQCNZjycc7uBF4BTE1mnAs9l2G0BMFJS\nq4T8WmB1fdcxEolEItnR2FGH7gAmSbpMUn9JdwPdgJ8BSPq+pL8E8r8DPgKmSxooaSLwn0CDrLSi\nDkNeTZhS1BlKU+9S1BlKU+8G11kN9K1d5gPaR4I3YR8JLgf+3Tn3N583HRjjnOsVyA8Cfox9JPg+\nZmi+11AfCUYikUikdhrdeEQikUik6RODZUcikUgkZ6LxwIbSJL0laaekFySNLHSd6gtJ35K0SNI2\nSRsl/UnSwISMJFVIWitph6R5kgYUqs71jW8DJ2lqkFaUOkvqKunX/lzvlLRC0uggv+j0ltRM0q3B\nNfyWpNskNQ9kmrTekkZJelTSu74vT0rk16qfpJaS7pG0SdKHvrzu+dap5I1H4G/rf4DPYCu/Hpd0\nTEErVn+MAX6CfcU/FtgLzJUURri6CbgB+xpyKLABeFJSu8atav0jaRj24eiyRFbR6SzpSOBZQMB4\noD+mX+iorej0Bm4GrgauxfzfXYf5zQtdHjV1vdtic8TXATvS5Gej313AucCXgJHAEcAsSfk56XPO\nlfQPeB64N5H2BvD9QtetgfRti31vc5bfFvAecEsgczhQCVxR6PrWUdf2wCrgc8A8YGox64w9AD1b\nQ36x6j0L+HUi7dfArGLUG9gOTMrlvPprYTfwlUCmB7AfOC2fepT0m0fgb+vPiaya/G01ddphb5zv\n++1PYK5eDrSBM19if6Ppt8E04CHn3F8T6cWq8znA85IekLRB0lJJ31SVJ89i1fsZ4HPeaSqSjsfe\nsh/z+cWqd4ps9BsMHJaQeQdYSZ5tUFTuSfIgH39bTZ27gaXYB5hgnQ7St8HRNFEkfQPoA1yUJrso\ndQZ6Y8M1dwI/AE4A7vF5UylevX+IPRStkLQPu6/d7pz7ic8vVr1TZKNfF2zEIenvan2wf06UuvEo\nKSTdAYwARjjn9hW6Pg2FpL7YEM4I59yeQtenESkDFjvnUmP9L0o6DpsPmJp5tybPhcDXgC9jAeZO\nAO6W9JZz7pcFrVkRU9LDVuTnb6tJIulObKJsrHPuzSArpWcxtcFw7K3yFUl7Je0FRgNX+f83e7li\n0hls3HtFIm0lkFr8UYznGuBHwGTn3O+dcy87536LebNIGdFi1TtFNvqtw0ZZOtUgkxMlbTxcfv62\nmhzeDUzKcLyayH4L6zynBvKtsNUYTbUNHgEGYU+gqd9i4Pf+/9cpPp3BVlr1TaR9Evin/78YzzVY\nwLjkm/Q+qu5vxap3imz0ewHYk5Dpjq3Iy68NCr1yoNA/7JV3N3CZb8i7sdUMPQtdt3rS78fANmwC\nsUvwaxvI3AxsBSYCA7Gb7FqgXaHrX4/tMA+/2qpYdcaWaO4BbsHme873Ol5d5HpPx+L+jMeCxP0L\nsBGYUix6Y6skUw9CHwH/7f8/Jlv9gJ/6djoF+yzhr9j8Z7O86lToRjkUftgk42pgF2ahRxW6TvWo\nm8vwqwhkBFRgwx47gfnAwELXvZ7bIWk8ilJnfwN9yev0Ovbtg4pZb2yy/C7sDWsH8CY259WqWPTG\nvtdKdx1Pz1Y/oCW2gGKzN0B/AnrkW6fo2yoSiUQiOVPScx6RSCQSyY9oPCKRSCSSM9F4RCKRSCRn\novGIRCKRSM5E4xGJRCKRnInGIxKJRCI5E41HpCjwwW8OKf9Nki6X9Lak/ZIqCl2fQnAonpe64oMu\nLa9FZqqkeY1UpYIQjUc9I6lc0j5Jzxa6Lo2Fv0E4SV9NpE+StL1Q9Sokko7Cvu7/EebZdHIGudW+\n7ZykjyQtl3R5Y9Y1cuC6dcoQRdS7uU+58ZiM+UoraaLxqH8uwyL3DZTUvzEOKOmwxjhOLewEbpXU\nstAVqU/q0LY9Ma/Vs5xz7znnajKi3wO6Ap/C/HL9XNIFGerTPIjPEcmDdG3onFuCueq4JI18RyxW\nyi+87Hbn3OakXKkRjUc9IulwzC30NOAh4NJE/nOSpiTSjvAxhyf67RaSfihpjX8SXSTptEB+jH9C\nOkPS3yXtBk6TdKykP0pa5+MTL5F0ZuJYnX3c4h3+ifdr/km3IpBpL2maDyZUKWm+pCFZqP8AFr3s\n6hra56A3kUCfTqGMpNMlverb4FFfr/MkvSFpq6Tf+vYOaS7pbknv+9+PJJUFx8qrbTPocoykh30b\nVUr6g3c0hyy+9Ite9E1fZq8a2q7SObfOOfcP59y3sUiW5/iyKvw5miRpFeZCp40sHvVdktbL4nYv\nlDQijS5nyoJC7ZT0gqTBCT0mSnpZ0i5J70i6Jbyx+vxlvs9s8f2hs8+rtc+labeM5WWQd7KAVrP9\nOfunpIsSMkdL+n1w3mfLXNGn8tO2YZrD/QI4X1LbRPpFfp8HwvKC8ptJmhwc/y7Mg21YR0m6SdIq\nr/vLafQYJGlu0DbTJbWvqT0LSqF9thTTD/gq8FLgi2YDcFiQfxXwLlAWpH0di+rX0m/PABYCo7Dg\nPt/EHDd+OuHj5mVgnJf5GPBp4F8xb7J9MOd4u4F+wbHmYH6PhmNO1f6ChaqsCPzjPAPMBk705dyK\nOVbsWoPe87B4EVdhfnOO9OmTgO2BXLXthD6dApk9wFws+tlwzMHbXMwXz6ewsLLvAzck6lCJ+e7p\nB1yAOYq7PpDJq23T6FuGGYfngCH+txDz3CvMiJ7myxqKOaJM63wO86l2YyJtGTDT/18BfIhFgCvH\nnN41xxx4vkdVrPJ7MYeeXRO6vOrrMhB40O/T2ssMxrzPfhfzvvsVX8Y1Pr+Lb58bMIeDA7E3684+\nP5s+N4+q8L81lpehfRzWp67wdbwFC506xOe3xnx4Tfd9ox9mBP4Z6Jm2DdMc60jMN9alac7HvcF2\nBbA82L4J62sX+OPfg10z8wKZ24HXgC9gkf++7Os03ue3wfp5yiP0aK/XzELf1zKem0JXoJh+/kK5\n0f8v7MZwXpDf0V88nw/S5gLT/P/H+gvjmES5jwA/8f+P8RfUuVnUZyHwbf9/X7/fsCC/B3bzqPDb\nY7Gbx+GJcpYCN9Wi91TspvY68AOfPon8jIcD+gYyk309OwVp0/ExqoM6vE51J4DfBtbUd9tibq33\nAb2CtN6+/FP89hBfVq9aylod9Jnmgf5X+rQKzJh2DvZp4/vR14K0Zli89tsSuoQxq9sCHwCX+e0Z\nwFOJ+lQEbVbuy+iZwzVwoM+FfaMO5TmCG3dwzdzv/78Ee1MLz3szzOBckKkNazje/cBzwfZQX4eT\nEm0UGo+1VI8fXub74rzgfO0ARiaOdRfwmP//G5gBCr3gps5hn2zbqzF/cdiqnpDUB4vS9zsAZ2d/\nBsHQlbNx0jnYEx6SumFP0fd7kXLM6KzwQzfbZcM847GbX8jixPHbSPpfSSv8q/N27AaWCgTUD7u5\nHdjPWQzjtUExg7EnuY2J4w9Mc/yDcM7txZ4Mr5VUl/Ceu5xzrwXb64F1zrlNibSPJ/Zb6Ns9xQLg\naElHUIe2TUN/YK1zbnUqwVmArbXA8bXsm47bfV12UDXJ/vMgf41zLgwxeiwWj/rAogxnkSEXpDn+\ngkBmO/ZWlZLpH5bheYaqNnsJu1EvlzRT0pWSPpYSzKLPJamxvBpYkGY7pcNg7Em+MjinW4GjqH5e\nk22YiV8Aw+XjoWPGablz7vl0wn5YqSvV23k/EMofD7QC5iT63pVBHfsDy5xzlcF+z2HXbD59qsGJ\nYWjrj8uwJ563wyFjAEk9/I0azFDcK+kq4IvAO8DTPq+MqqGOZPjUHYntDxPbk7FX4huxJ7GPgN8A\nLXLQoQy7KadbcbItmwKccw9KuhGbBH46kb0f3yYB6Sak9yaL5eD2cOQ2Z1eXts2FfNxU3wH8Ejtn\n7yUMYK71qS832c45t0/SOGAYNox3KfB9SaOdcy+RY5/Lorx8KMPejL+YJm9L8H+2bTgf+AdwiaTv\nYEHUKvKsW4pUPz0LeDuRl02Y5EPS9Xl886gHJDUHLsbCXobR6z6NjZd+PRB/1P89E3sD+V1ws3gR\nu7l2cTZ5Gv7eraUaI4DfOOdmOueWYUFfwievV7HzfWDCVDbB2y2QWYKFpdyf5vgbsmsNwMaALwYG\nJNI3Aq39U22KE3IotzZOCid7sZvUWufcNurWtklWAt0UTIJL6o21ZTIMbDZs9vVYm8ZwpGMVNmx1\ncnD8Ztj8UPL4wwKZNthb5EqftDIswzMCe0qvBLMgzrkFzrnvYoZ3LRZALSVbU587iFrKy8SwNNsp\nHZZg8y2b0pzXLeSIb//7sJjoX8Lmr35bg/xWbB4pbGdhc4YpVmAT7j3T1DEV5XElMEhSu2C/z2LX\n7EoORQo9blYMP2AC9gTRMU3ezViYyHBM9j7sackBxyfk78cm+87DxtGHYE92E33+GII5gmC/mZih\nKscm3B7CXt+nBzJzsJvoMOym/SQ2x/Edny/sbeFl4HRsOGA4NqE6sgb95xEEWvJps7An+nDOo4M/\n3o+xC/5c3zbJOY/kvMiNwOpE2g+AxYk6VGITyX19+31AMBmdb9um0Ve+HZ+lasJ8AX7C3MvkPOeR\nIb+CYHw9SL8Lu2mdgQ15TCP9hPkKbI5mALZaaD3QxsuU4+e8qJowr6RqwnwYNm80FBuKmuDzL8qh\nzx3oG7WVl0F/B2zC5gSOwx7Q9gNDfX5rbCJ6PjbJ/AlsQcQU4Lia2rCGY3bD3n63AA/Udk6wa/wD\n36/6Yn0wOWF+GzYPcwnW90/AFhtcHuixFnjYt+Uor1ecMC/mH/Y28ecMeb39BTAuSBvr05akkT/M\nd843safLdb78wT4/dVNIGo+e2Hjyh9gT4I3YDXx6INMFW7G0E3t9/jr2FHtzINPOd/41/vjvYCEt\nj61B/wM3iCBtIHZjShqCCdhk4g7gCWwZZH0Zj59hE/cfYKuxphCscsq3bTPofAw22V7pfw8D3YP8\nhjYeLTEDsh57ql0IjAjyU7qcjd3gd2FP6UMT5UzEHhZS5/oWqgxgf+Dx4Bj/IFg4kWWfO9A3aisv\ng/4OWxU3x/eZt4GLEzKdgV9hqxt3YQ8k9wV9Km0bZnFNO+DU2s4JNvx/p+93H2CrrX5KdeMh4Bqq\n3kI2Yg9vpwYyg7AVkDuw/jsdaJ/vfamhfzGSYAkj+7ZiLfAl59zMQtcnUn9IGoPFqP6Yq77QoEkh\nyQHnO+ceKnRdItWJE+YlhKSx2JvFy9hKpduxIYE5haxXJBJpekTjUVocho299sZWxiwERjnn6rK6\nKBKJlCBx2CoSiUQiOROX6kYikUgkZ6LxiEQikUjOROMRiUQikZyJxiMSiUQiORONRyQSiURyJhqP\nSCQSieTM/wMULXp6Z6E5XgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7ff313fbf590>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["CPU times: user 36.3 s, sys: 2.06 s, total: 38.4 s\n", "Wall time: 37 s\n"]}], "source": ["%%time\n", "\n", "uniform_average_nr_proposals_valid, uniform_average_recall_valid, uniform_recall_valid = run_evaluation(\n", "    \"../Evaluation/data/activity_net.v1-3.min.json\",\n", "    \"../Evaluation/data/uniform_random_proposals.json\",\n", "    max_avg_nr_proposals=100,\n", "    tiou_thresholds=np.linspace(0.5, 0.95, 10),\n", "    subset='validation')\n", "\n", "plot_metric(uniform_average_nr_proposals_valid, uniform_average_recall_valid, uniform_recall_valid)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.13"}}, "nbformat": 4, "nbformat_minor": 2}