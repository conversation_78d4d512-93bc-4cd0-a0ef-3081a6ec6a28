#!/usr/bin/env python
# coding: utf-8

"""
HCC Segmentation Inference Script
Load trained model and perform inference on new images
Compatible with the updated training script structure

IMPORTANT UPDATES (to match training parameters):
- ROI size: (64, 64, 32) - matches training img_size and spatial padding
- Sliding window batch size: 4 - matches training sw_batch_size
- Spatial padding: [64, 64, 32] - matches training validation transforms
- Model architecture: SwinUNETR with in_channels=1, out_channels=2, img_size=(64,64,32)
- Added model compatibility verification to ensure correct architecture
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os
import glob
import pandas as pd
from monai.transforms import (
    Compose, LoadImaged, EnsureChannelFirstd, Orientationd,
    Spacingd, NormalizeIntensityd, SpatialPadd
)
from monai.inferers import sliding_window_inference
from monai.networks.nets import SwinUNETR

# Setup
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# Match training parameters exactly
roi_size = (64, 64, 32)  # Must match training ROI size
sw_batch_size = 4  # Match training sliding window batch size

# Results directory structure (same as training)
data_disk_path = '/root/autodl-tmp'
results_dir = os.path.join(data_disk_path, 'results')
models_dir = os.path.join(results_dir, 'models')
inference_dir = os.path.join(results_dir, 'inference')
os.makedirs(inference_dir, exist_ok=True)

def create_model_architecture():
    """
    Create a fresh SwinUNETR model with the same architecture as training
    This can be used for debugging or loading state_dict instead of full model
    """
    model = SwinUNETR(
        in_channels=1,  # Single channel for HCC images
        out_channels=2,  # Binary segmentation: background + HCC
        img_size=(64, 64, 32),  # Must match training img_size
        feature_size=24,  # Must match training feature_size
        drop_rate=0.0,
        attn_drop_rate=0.0,
        dropout_path_rate=0.0,
        use_checkpoint=False,
    ).to(device)
    return model

def verify_model_compatibility(model):
    """
    Verify that the loaded model has the expected architecture for inference
    """
    try:
        # Check if model has the expected input/output dimensions
        # Create a dummy input tensor with the expected dimensions
        dummy_input = torch.randn(1, 1, 64, 64, 32).to(device)

        with torch.no_grad():
            output = model(dummy_input)

        expected_output_shape = (1, 2, 64, 64, 32)  # [batch, classes, H, W, D]

        if output.shape == expected_output_shape:
            print(f"✅ Model compatibility verified!")
            print(f"   Input shape: {dummy_input.shape}")
            print(f"   Output shape: {output.shape}")
            return True
        else:
            print(f"❌ Model output shape mismatch!")
            print(f"   Expected: {expected_output_shape}")
            print(f"   Got: {output.shape}")
            return False

    except Exception as e:
        print(f"❌ Model compatibility check failed: {e}")
        return False

def load_trained_model(model_path=None, model_type="last"):
    """
    Load the trained SwinUNETR model with compatibility verification
    Args:
        model_path: Custom model path (optional)
        model_type: "last", "best", or custom path
    """
    if model_path is None:
        if model_type == "last":
            model_path = os.path.join(models_dir, "last_model_SwinUNETR_HCC.pt")
        elif model_type == "best":
            model_path = os.path.join(models_dir, "best_model_SwinUNETR_HCC.pt")
        else:
            print(f"Unknown model type: {model_type}")
            return None

    if os.path.exists(model_path):
        print(f"Loading model from: {model_path}")
        try:
            model = torch.load(model_path, map_location=device)
            model.eval()
            print(f"✅ Model loaded successfully!")

            # Verify model compatibility
            if verify_model_compatibility(model):
                return model
            else:
                print("❌ Model failed compatibility check. Please retrain with correct parameters.")
                return None

        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return None
    else:
        print(f"❌ Model file not found: {model_path}")
        print("Available models:")
        if os.path.exists(models_dir):
            for f in os.listdir(models_dir):
                if f.endswith('.pt'):
                    print(f"  - {f}")
        return None

def preprocess_image(image_path):
    """Preprocess a single image for inference (exactly matching validation transforms from training)"""
    # Define preprocessing transforms (EXACTLY same as validation in training script)
    preprocess = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.0, 1.0, 1.0), mode="bilinear"),
        NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
        # CRITICAL: Must match training spatial padding size [64, 64, 32]
        SpatialPadd(keys=["image"], spatial_size=[64, 64, 32], mode="constant"),
    ])

    # Create data dictionary
    data = {"image": image_path}

    # Apply transforms
    data = preprocess(data)

    return data



def perform_inference(model, image_data):
    """Perform inference on preprocessed image"""
    # Add batch dimension
    image_tensor = torch.unsqueeze(image_data["image"], 0).to(device)

    # Perform sliding window inference
    with torch.no_grad():
        pred = sliding_window_inference(image_tensor, roi_size, sw_batch_size, model)

        # Convert to discrete predictions
        pred_discrete = torch.argmax(pred, dim=1)

    return pred, pred_discrete



def visualize_results(original_image, prediction, slice_idx=None):
    """Visualize original image and prediction"""
    # Convert to numpy if needed
    if torch.is_tensor(original_image):
        original_image = original_image.cpu().numpy()
    if torch.is_tensor(prediction):
        prediction = prediction.cpu().numpy()
    
    # Remove batch and channel dimensions
    if original_image.ndim == 5:  # [batch, channel, H, W, D]
        original_image = original_image[0, 0]
    elif original_image.ndim == 4:  # [channel, H, W, D]
        original_image = original_image[0]
        
    if prediction.ndim == 4:  # [batch, H, W, D]
        prediction = prediction[0]
    
    # Select middle slice if not specified
    if slice_idx is None:
        slice_idx = original_image.shape[2] // 2
    
    # Create visualization
    _, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Original image
    axes[0].imshow(original_image[:, :, slice_idx], cmap='gray')
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # Prediction
    axes[1].imshow(prediction[:, :, slice_idx], cmap='jet')
    axes[1].set_title('Prediction')
    axes[1].axis('off')
    
    # Overlay
    axes[2].imshow(original_image[:, :, slice_idx], cmap='gray')
    axes[2].imshow(prediction[:, :, slice_idx], cmap='jet', alpha=0.5)
    axes[2].set_title('Overlay')
    axes[2].axis('off')
    
    plt.tight_layout()

    # Save to inference directory
    viz_filename = f'inference_result_slice_{slice_idx}.png'
    viz_path = os.path.join(inference_dir, viz_filename)
    plt.savefig(viz_path, dpi=150, bbox_inches='tight')
    print(f"Visualization saved to: {viz_path}")
    plt.show()

def save_prediction(prediction, original_image_path, output_dir=None):
    """Save prediction as NIfTI file"""
    if output_dir is None:
        output_dir = inference_dir
    os.makedirs(output_dir, exist_ok=True)

    # Load original image to get header info
    original_nii = nib.load(original_image_path)

    # Convert prediction to numpy if needed
    if torch.is_tensor(prediction):
        prediction = prediction.cpu().numpy()

    # Remove batch dimension if present
    if prediction.ndim == 4:
        prediction = prediction[0]

    # Create new NIfTI image with same header as original
    pred_nii = nib.Nifti1Image(prediction.astype(np.uint8),
                               original_nii.affine,
                               original_nii.header)

    # Generate output filename
    base_name = os.path.basename(original_image_path)
    output_name = base_name.replace('.nii.gz', '_prediction.nii.gz')
    output_path = os.path.join(output_dir, output_name)

    # Save
    nib.save(pred_nii, output_path)
    print(f"Prediction saved to: {output_path}")

    return output_path



def run_inference_on_image(model_path=None, image_path=None, model_type="last",
                          visualize=True, save_result=True):
    """Complete inference pipeline for a single image"""
    print(f"🔍 Running inference on: {image_path}")

    # Load model
    model = load_trained_model(model_path, model_type)
    if model is None:
        return None

    # Preprocess image
    print("📋 Preprocessing image...")
    image_data = preprocess_image(image_path)

    # Perform inference
    print("🧠 Performing inference...")
    _, pred_discrete = perform_inference(model, image_data)

    # Visualize results
    if visualize:
        print("🖼️  Visualizing results...")
        visualize_results(image_data["image"], pred_discrete)

    # Save prediction
    output_path = None
    if save_result:
        print("💾 Saving prediction...")
        output_path = save_prediction(pred_discrete, image_path)

    return {
        'prediction': pred_discrete,
        'output_path': output_path
    }

def batch_inference(model_path=None, image_dir=None, model_type="last", save_predictions=True):
    """
    Run inference on all images in a directory
    Args:
        model_path: Custom model path (optional)
        image_dir: Directory containing images
        model_type: "last" or "best"
        save_predictions: Whether to save prediction files
    """
    print(f"🚀 Running batch inference on images in: {image_dir}")
    print(f"📊 Using {model_type} model")

    # Load model
    model = load_trained_model(model_path, model_type)
    if model is None:
        return None

    # Get all NIfTI files
    image_files = []
    for ext in ['*.nii', '*.nii.gz']:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))

    print(f"Found {len(image_files)} images")

    # Store processing results
    processed_files = []
    successful_inferences = 0

    # Process each image
    for i, image_path in enumerate(image_files):
        print(f"\n📋 Processing {i+1}/{len(image_files)}: {os.path.basename(image_path)}")

        try:
            # Run inference
            result = run_inference_on_image(
                model_path=model_path,
                image_path=image_path,
                model_type=model_type,
                visualize=False,
                save_result=save_predictions
            )

            if result:
                processed_files.append({
                    'image_name': os.path.basename(image_path),
                    'output_path': result['output_path'],
                    'status': 'success'
                })
                successful_inferences += 1

        except Exception as e:
            print(f"❌ Error processing {image_path}: {e}")
            processed_files.append({
                'image_name': os.path.basename(image_path),
                'output_path': None,
                'status': f'error: {str(e)}'
            })

    # Save processing summary
    if processed_files:
        print(f"\n📊 Saving processing summary...")

        # Save processing log
        summary_df = pd.DataFrame(processed_files)
        summary_path = os.path.join(inference_dir, f"processing_log_{model_type}.csv")
        summary_df.to_csv(summary_path, index=False)
        print(f"📋 Processing log saved to: {summary_path}")

    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 BATCH INFERENCE COMPLETED ({model_type.upper()} MODEL)")
    print("=" * 60)
    print(f"✅ Successfully processed: {successful_inferences}/{len(image_files)} images")
    print(f"� Results saved to: {inference_dir}")
    if save_predictions:
        print(f"� Prediction files: *_prediction.nii.gz")
    print("=" * 60)

    return {
        'processed_files': processed_files,
        'successful_inferences': successful_inferences,
        'total_images': len(image_files)
    }

if __name__ == "__main__":
    print("🏥 HCC Segmentation Inference Script")
    print("=" * 50)
    print("📋 UPDATED TO MATCH TRAINING PARAMETERS:")
    print(f"   - ROI size: {roi_size}")
    print(f"   - Sliding window batch size: {sw_batch_size}")
    print(f"   - Spatial padding: [64, 64, 32]")
    print(f"   - Model verification: Enabled")
    print("=" * 50)

    # Configuration
    image_dir = "/root/autodl-tmp/120HCC/image/ap"

    # Check if directories exist
    if not os.path.exists(image_dir):
        print(f"❌ Image directory not found: {image_dir}")
        print("Please update the image directory path.")
        exit(1)

    print(f"📁 Image directory: {image_dir}")
    print(f" Results will be saved to: {inference_dir}")

    # Example 1: Single image inference
    print("\n" + "=" * 50)
    print("🔍 SINGLE IMAGE INFERENCE EXAMPLE")
    print("=" * 50)

    # Find a sample image
    sample_images = glob.glob(os.path.join(image_dir, "*.nii.gz"))
    if sample_images:
        sample_image = sample_images[0]
        sample_name = os.path.basename(sample_image)

        print(f"📋 Sample image: {sample_name}")

        # Run inference with last model
        print("\n🧠 Using LAST model:")
        result_last = run_inference_on_image(
            image_path=sample_image,
            model_type="last",
            visualize=True,
            save_result=True
        )

        if result_last:
            print("✅ Single image inference completed!")
            print(f"💾 Prediction saved to: {result_last['output_path']}")

    else:
        print(f"❌ No images found in {image_dir}")

    # Example 2: Batch inference
    print("\n" + "=" * 50)
    print("🚀 BATCH INFERENCE")
    print("=" * 50)

    # Compare last vs best model if both exist
    last_model_path = os.path.join(models_dir, "last_model_SwinUNETR_HCC.pt")
    best_model_path = os.path.join(models_dir, "best_model_SwinUNETR_HCC.pt")

    if os.path.exists(last_model_path):
        print("\n🔄 Running inference with LAST model...")
        results_last = batch_inference(
            image_dir=image_dir,
            model_type="last",
            save_predictions=True
        )

    if os.path.exists(best_model_path):
        print("\n🏆 Running inference with BEST model...")
        results_best = batch_inference(
            image_dir=image_dir,
            model_type="best",
            save_predictions=True
        )

    print("\n🎉 All inference tasks completed!")
    print(f"📊 Check results in: {inference_dir}")

    print("\n" + "=" * 50)
    print("✅ INFERENCE SCRIPT COMPLETED")
    print("=" * 50)
