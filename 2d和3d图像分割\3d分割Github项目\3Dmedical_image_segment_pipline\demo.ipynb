{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# video 导入"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 显示切片"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "\n", "from monai.losses import DiceCELoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    EnsureChannelFirstd,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    RandCropByPosNegLabeld,\n", "    RandShiftIntensityd,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", "    SpatialPadd,\n", "    RandRotate90d,\n", "    CenterSpatialCropd,\n", "    ResizeWithPadOrCropd,\n", "    <PERSON>lip<PERSON>,\n", "    Rotate90d,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "from monai.config import print_config\n", "from monai.metrics import DiceMetric\n", "from monai.networks.nets import UNETR\n", "\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    load_decathlon_datalist,\n", "    decollate_batch,\n", "    pad_list_data_collate,\n", "    SmartCacheDataset,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Dataset\n", ")\n", "\n", "import numpy as np\n", "import torch"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running on local URL:  http://127.0.0.1:7860\n", "Running on public URL: https://f8338d877679ad9d93.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from Terminal to deploy to Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/html": ["<div><iframe src=\"https://f8338d877679ad9d93.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["monai.transforms.io.dictionary LoadImaged.__init__:image_only: Current default value of argument `image_only=False` has been deprecated since version 1.1. It will be changed to `image_only=True` in version 1.3.\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  6.94it/s]\n", "cache_num is greater or equal than dataset length, fall back to regular monai.data.CacheDataset.\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  9.33it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  9.07it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.13it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.72it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.71it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  8.96it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 15.40it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 12.04it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 12.93it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.22it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 12.66it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.29it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 12.48it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  8.86it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.37it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.68it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 13.59it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 12.13it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 10.34it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  7.49it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 11.52it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 10.04it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  9.01it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  9.31it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  7.68it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 10.92it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  6.96it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  8.87it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  9.57it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00, 10.20it/s]\n", "Loading dataset: 100%|██████████| 1/1 [00:00<00:00,  8.53it/s]\n"]}], "source": ["import gradio as gr\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import nibabel as nib\n", "import numpy as np\n", "import SimpleITK as sitk\n", "\n", "def calculate_volume(mask_image_path):\n", "    # 读取分割结果的图像文件\n", "    mask_image = sitk.ReadImage(mask_image_path)\n", "\n", "    # 获取图像的大小、原点和间距\n", "    size = mask_image.GetSize()\n", "    origin = mask_image.GetOrigin()\n", "    spacing = mask_image.GetSpacing()\n", "\n", "    # 将 SimpleITK 图像转换为 NumPy 数组\n", "    mask_array = sitk.GetArrayFromImage(mask_image)\n", "\n", "    # if len(np.unique(mask_array)) != 5:\n", "    #     print(mask_image_path[-15:-12])\n", "    #     print(np.unique(mask_array))\n", "    \n", "    # 计算非零像素的数量\n", "    one_voxels = (mask_array == 1).sum()\n", "    two_voxels = (mask_array == 2).sum()\n", "    three_voxels = (mask_array == 3).sum()\n", "    four_voxels = (mask_array == 4).sum()\n", "    # print(one_voxels,two_voxels,three_voxels,four_voxels)\n", "    # 计算像素的体积（以立方毫米为单位）\n", "    voxel_volume_mm3 = spacing[0] * spacing[1] * spacing[2]\n", "\n", "    # 计算体积（以 mm³ 为单位）\n", "    V_Right_ventricular_cistern = one_voxels * voxel_volume_mm3 / 1000.0\n", "    V_Right_cerebral_sulcus = two_voxels * voxel_volume_mm3 / 1000.0\n", "    V_Left_ventricular_cistern = three_voxels * voxel_volume_mm3 / 1000.0\n", "    V_Left_cerebral_sulcus = four_voxels * voxel_volume_mm3 / 1000.0\n", "    # 如果需要以其他单位（例如 cm³）显示，请进行适当的单位转换\n", "    # volume_cm3 = volume_mm3 / 1000.0\n", "\n", "    return size,spacing,V_Right_ventricular_cistern, V_Right_cerebral_sulcus, V_Left_ventricular_cistern, V_Left_cerebral_sulcus\n", "    \n", "def process_nii_file(input_nii_file, slice, mode):\n", "            \n", "    if mode == \"Step1:Segment\":\n", "        device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "        root_dir = \"./run\"\n", "        model = UNETR(\n", "            in_channels=1,\n", "            out_channels=5,\n", "            img_size=(96, 96, 16),\n", "            feature_size=16,\n", "            hidden_size=768,\n", "            mlp_dim=3072,\n", "            num_heads=12,\n", "            pos_embed=\"perceptron\",\n", "            norm_name=\"instance\",\n", "            res_block=True,\n", "            dropout_rate=0.0,\n", "        ).to(device)\n", "        model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model67v2.pth\")))\n", "        \n", "        test_transforms = Compose(\n", "            [\n", "            LoadImaged(keys=[\"image\"]),\n", "            EnsureChannelFirstd(keys=[\"image\"]),\n", "            Orientationd(keys=[\"image\"], axcodes=\"RAS\"),\n", "            ScaleIntensityRanged(\n", "                keys=[\"image\"],\n", "                a_min=-50,\n", "                a_max=100,\n", "                b_min=0.0,\n", "                b_max=1.0,\n", "                clip=True,\n", "            ),\n", "            Rotate90d(keys=[\"image\"], k=1)\n", "            # ResizeWithPadOrCropd(keys=[\"image\"], spatial_size=(512, 512, 16)),\n", "            ]\n", "        )\n", "        test_file = [{'image':input_nii_file.name}]\n", "        test_image = SmartCacheDataset(data=test_file, transform=test_transforms)[0]['image']\n", "\n", "        with torch.no_grad():\n", "\n", "            inputs = torch.unsqueeze(test_image, 1).cuda()\n", "\n", "            val_outputs = sliding_window_inference(inputs, (96, 96, 16), 8, model, overlap=0.8)\n", "        \n", "        # Process the output image\n", "        output_image = torch.argmax(val_outputs, dim=1).detach().cpu().squeeze(0)\n", "        \n", "        # Display the images\n", "        fig1 = plt.figure()\n", "        plt.title(\"image\")\n", "        plt.axis('off') # Remove axis\n", "        plt.imshow(inputs.cpu().numpy()[0, 0, :, :, slice], cmap=\"gray\")\n", "\n", "        fig2 = plt.figure()\n", "        plt.title(\"output\")\n", "        plt.axis('off') # Remove axis\n", "        plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, slice])\n", "\n", "\n", "\n", "        val_outputs = torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, :]\n", "        val_outputs = val_outputs.numpy().astype('int16')\n", "        # val_outputs = np.transpose(val_outputs, (2, 1, 0))\n", "        val_outputs = np.rot90(val_outputs, k=3)\n", "        val_outputs = nib.Nifti1Image(val_outputs, np.eye(4))\n", "        output_path = f'{input_nii_file.name[-15:-7]}_mask.nii.gz'\n", "        nib.save(val_outputs, output_path)\n", "\n", "        return [\"指定切片分割结果如下,\", output_path, fig1, fig2]\n", "    \n", "    if mode == \"Step2:Volumn\":\n", "        maskFilePath = input_nii_file.name\n", "        size,spacing,V_Right_ventricular_cistern, V_Right_cerebral_sulcus, V_Left_ventricular_cistern, V_Left_cerebral_sulcus = calculate_volume(maskFilePath)\n", "\n", "        vol = f\"\"\"右侧脑室脑池的体积为{V_Right_ventricular_cistern}cm³\\n 右侧脑沟的体积为{V_Right_cerebral_sulcus}cm³\\n 左侧脑室脑池的体积为{V_Left_ventricular_cistern}cm³\\n 左侧脑沟的体积为{V_Left_cerebral_sulcus}cm³\"\"\"\n", "        fig1 = plt.figure()\n", "        fig2 = plt.figure()\n", "        return [vol, None, fig1, fig2]\n", "\n", "\n", "# Define the Gradio interface\n", "iface = gr.Interface(fn=process_nii_file,\n", "    inputs= \n", "    [gr.File(file_count='single', file_types=['.nii.gz']), \n", "    gr.<PERSON><PERSON><PERSON>(0, 24, value=8, label=\"Select Slice\", step=1),\n", "    gr.Radio(\n", "            [\"Step1:Segment\", \"Step2:Volumn\"], label=\"mode\"\n", "        ),\n", "    ],\n", "    \n", "    outputs=[gr.Text(label=\"Output\"), gr.File(label=\"下载mask文件\"), gr.Plot(label=\"image\"), gr.Plot(label=\"mask\")],  # Display both \"image\" and \"output\"\n", ")\n", "\n", "iface.launch(share=True)\n"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}