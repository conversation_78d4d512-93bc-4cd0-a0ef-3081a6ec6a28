#!/usr/bin/env python
# coding: utf-8

#%% In[1]:3dUnet修改版，已成功

from posixpath import join
from torch.utils.data import DataLoader
import os
import sys
import random
from torchvision.transforms import RandomCrop
import numpy as np
import SimpleITK as sitk
import torch
from PIL import Image
import torch
import torchvision
import os
import numpy as np
import glob 
import pandas as pd
import matplotlib   #pycharm中要加这行代码，否则不能出图
matplotlib.use('TkAgg') #pycharm中要加这行代码，否则不能出图
import matplotlib.pyplot as plt
from PIL import Image
import random
import time
import cv2
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import PIL
from torch.utils.data import Dataset

## 第1种方法：获取image和mask的列表list

import os
import glob
import SimpleITK as sitk
image_dir = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\image\hbp'
label_dir = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\mask\hbp'

image_list = []
label_list = []

for file_name in os.listdir(image_dir):
    file_path = os.path.join(image_dir, file_name)
    print(file_path)
    image_list.append(file_path)

for file_name in os.listdir(label_dir):
    file_path = os.path.join(label_dir, file_name)
    print(file_path)
    label_list.append(file_path)

image_list [:5]
label_list [:5]

def get_array_from_slice(file_path):
    image = sitk.ReadImage(file_path)
    img_array = sitk.GetArrayFromImage(image)
    shape = img_array.shape
    print(shape)

for i in image_list:
    get_array_from_slice(i)

#找到肿瘤区域最大层数
max_layers = 0
for label in label_list:
    label_ct = sitk.ReadImage(label, sitk.sitkInt8)
    label_array = sitk.GetArrayFromImage(label_ct)
    label_array[label_array > 0] = 1
    non_zero_indices = np.nonzero(label_array)
    if len(non_zero_indices[0]) > 0:
        min_indices = np.min(non_zero_indices, axis=1)
        max_indices = np.max(non_zero_indices, axis=1)
        num_layers = max_indices[0] - min_indices[0] + 1
        if num_layers > max_layers:
            max_layers = num_layers
print("Maximum number of layers in the non-zero region of masks:", max_layers)
print(label)

# In[5]:

# jpg图像数组堆叠代码
# img_list=glob.glob(jpg图像路径)
# img_list[:5]
#
# def stackImg(img_slice):
#     img_read_list=[]
#     for i in img_slice:
#         img_read = cv2.imread(i)
#         img_array=img_read[:,:,0]
#         img_read_list.append(img_array)
#     img_stack=np.stack(img_read_list,axis=0)
#
#     return img_stack
#
# img_for3dnet=stackImg(img_list)
# img_for3dnet.shape

## 第2种方法获取图像和标签列表的方法
import os
import glob
import SimpleITK as sitk
image_list  = glob.glob(r'I:\1.HCC-VETC\datasets\HCC-suzhou2\image\*\*.nii.gz')
image_list [:5]
len(image_list)

label_list = glob.glob(r'I:\1.HCC-VETC\datasets\HCC-suzhou2\mask\*\*.nii.gz')
len(label_list)
label_list [:5]

#找到肿瘤区域最大层数
max_layers = 0
for label in label_list:
    label_ct = sitk.ReadImage(label, sitk.sitkInt8)
    label_array = sitk.GetArrayFromImage(label_ct)
    label_array[label_array > 0] = 1
    non_zero_indices = np.nonzero(label_array)
    if len(non_zero_indices[0]) > 0:
        min_indices = np.min(non_zero_indices, axis=1)
        max_indices = np.max(non_zero_indices, axis=1)
        num_layers = max_indices[0] - min_indices[0] + 1
        if num_layers > max_layers:
            max_layers = num_layers
print("Maximum number of layers in the non-zero region of masks:", max_layers)
print(label)
##3d模型需要所有病例的层数一样；
# 首先找到所有病灶label_array对应的非0区域的最大层数，
# 使得所有病灶的label_array和ct_array的层数均扩展为最大层数
class D3UnetData(Dataset):
    def __init__(self, image_list, label_list, transformer):
        self.image_list = image_list
        self.label_list = label_list
        self.transformer = transformer
        # Calculate the maximum number of layers in the label_list
        max_layers = 0
        for label in label_list:
            label_ct = sitk.ReadImage(label, sitk.sitkInt8)
            label_array = sitk.GetArrayFromImage(label_ct)
            label_array[label_array > 0] = 1
            non_zero_indices = np.nonzero(label_array)
            if non_zero_indices[0].size > 0:
                min_indices = np.min(non_zero_indices, axis=1)
                max_indices = np.max(non_zero_indices, axis=1)
                num_layers = max_indices[0] - min_indices[0] + 1
                if num_layers > max_layers:
                    max_layers = num_layers
        print(max_layers)
        self.max_layers = max_layers

    def __getitem__(self, index):
        image = self.image_list[index]
        label = self.label_list[index]
        image_ct = sitk.ReadImage(image, sitk.sitkInt16)
        label_ct = sitk.ReadImage(label, sitk.sitkInt8)
        ct_array = sitk.GetArrayFromImage(image_ct)
        label_array = sitk.GetArrayFromImage(label_ct)
        label_array[label_array > 0] = 1
        non_zero_indices = np.nonzero(label_array)
        if non_zero_indices[0].size > 0:
            min_indices = np.min(non_zero_indices, axis=1)
            max_indices = np.max(non_zero_indices, axis=1)
        # min_indices = np.min(non_zero_indices, axis=1)
        # max_indices = np.max(non_zero_indices, axis=1)
        # Adjust minimum indices if they are negative
        else:
            min_indices = np.array([0, 0, 0])
            max_indices = np.array([0, 0, 0])
        min_indices = np.maximum(min_indices, 0)
        max_indices = np.maximum(max_indices, 0)
        # Ensure max_indices are at least as large as min_indices
        max_layers = max_indices[0] - min_indices[0] + 1
        # Pad the ct_array and label_array to match the max_layers
        if max_layers < self.max_layers:
            pad_layers = self.max_layers - max_layers
            ct_padding = np.zeros((pad_layers, ct_array.shape[1], ct_array.shape[2]), dtype=np.float32)
            label_padding = np.zeros((pad_layers, label_array.shape[1], label_array.shape[2]), dtype=np.int64)
            ct_array = np.concatenate((ct_array, ct_padding), axis=0)
            label_array = np.concatenate((label_array, label_padding), axis=0)
        ct_array = ct_array[min_indices[0]:min_indices[0] + self.max_layers, min_indices[1]:max_indices[1] + 1,
                   min_indices[2]:max_indices[2] + 1]
        label_array = label_array[min_indices[0]:min_indices[0] + self.max_layers, min_indices[1]:max_indices[1] + 1,
                      min_indices[2]:max_indices[2] + 1]
        ct_array = ct_array.astype(np.float32)
        ct_array = torch.FloatTensor(ct_array).unsqueeze(0)
        label_array = torch.LongTensor(label_array)
        ct_array = self.transformer(ct_array)
        label_array = self.transformer(label_array)
        return ct_array, label_array

    def __len__(self):
        return len(self.image_list)

##划分训练和验证集
i = int(len(image_list)*0.7)
train_images = image_list[:i]
train_labels = label_list[:i]
len(train_images)

test_images = image_list[i: ]
test_labels = label_list[i: ]
len(test_images)
test_images[:5]
test_labels[:5]

# In[6]:

transformer=transforms.Compose([
    transforms.Resize((96,96)),
])
#
# transformer = transforms.Compose([
#                     transforms.Resize((96, 96)),
#                     transforms.ToTensor(),
# ])
# In[7]:

train_ds=D3UnetData(train_images,train_labels,transformer)
test_ds=D3UnetData(test_images,test_labels,transformer)
len(train_ds)
len(test_ds)


# In[8]:

train_dl=DataLoader(train_ds,batch_size=3,shuffle=True)
test_dl=DataLoader(test_ds,batch_size=3,shuffle=True)

# In[9]:

img,label=next(iter(train_dl))
print(img.shape,label.shape)

## In[10]:

img_show=img[0,0,25,:,:].cpu().numpy()
plt.imshow(img_show,cmap='gray')

# In[11]:

label_show=label[0,25,:,:].cpu().numpy()
plt.imshow(label_show,cmap='gray')

img.shape

## In[13]:

class DoubleConv(nn.Module):
    def __init__(self,in_channels,out_channels,num_groups=1):
        super(DoubleConv,self).__init__()
        self.double_conv=nn.Sequential(
            nn.Conv3d(in_channels,out_channels,kernel_size=3,stride=1,padding=1),
            #nn.BatchNorm3d(out_channels),
            nn.GroupNorm(num_groups=num_groups,num_channels=out_channels),
            nn.ReLU(inplace=True),
            
            nn.Conv3d(out_channels,out_channels,kernel_size=3,stride=1,padding=1),
            nn.GroupNorm(num_groups=num_groups,num_channels=out_channels),
            nn.ReLU(inplace=True),
        )
        
        
    def forward(self,x):
        return self.double_conv(x)


# In[14]:


# img.shape
# net=DoubleConv(1,64,num_groups=1)
# out=net(img)
# print(out.shape)


# In[15]:


class Down(nn.Module):

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.MaxPool3d(2, 2),
            DoubleConv(in_channels, out_channels)
        )
    def forward(self, x):
        return self.encoder(x)

    
class Up(nn.Module):

    def __init__(self, in_channels, out_channels, trilinear=True):
        super().__init__()
        
        if trilinear:
            self.up = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose3d(in_channels // 2, in_channels // 2, kernel_size=2, stride=2)
            
        self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)

        diffZ = x2.size()[2] - x1.size()[2]
        diffY = x2.size()[3] - x1.size()[3]
        diffX = x2.size()[4] - x1.size()[4]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2, diffZ // 2, diffZ - diffZ // 2])

        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

    
class Out(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size = 1)

    def forward(self, x):
        return self.conv(x)


class UNet3d(nn.Module):
    def __init__(self, in_channels, n_classes, n_channels):
        super().__init__()
        self.in_channels = in_channels
        self.n_classes = n_classes
        self.n_channels = n_channels

        self.conv = DoubleConv(in_channels, n_channels)
        self.enc1 = Down(n_channels, 2 * n_channels)
        self.enc2 = Down(2 * n_channels, 4 * n_channels)
        self.enc3 = Down(4 * n_channels, 8 * n_channels)
        self.enc4 = Down(8 * n_channels, 8 * n_channels)

        self.dec1 = Up(16 * n_channels, 4 * n_channels)
        self.dec2 = Up(8 * n_channels, 2 * n_channels)
        self.dec3 = Up(4 * n_channels, n_channels)
        self.dec4 = Up(2 * n_channels, n_channels)
        self.out = Out(n_channels, n_classes)

    def forward(self, x):
        x1 = self.conv(x)
        x2 = self.enc1(x1)
        x3 = self.enc2(x2)
        x4 = self.enc3(x3)
        x5 = self.enc4(x4)

        mask = self.dec1(x5, x4)
        mask = self.dec2(mask, x3)
        mask = self.dec3(mask, x2)
        mask = self.dec4(mask, x1)
        mask = self.out(mask)
        return mask


## In[16]:

# n_classes=2

model=UNet3d(1,2,24).cuda() #1表示输入的channel；2表示2个分类，病灶和背景
img,label=next(iter(train_dl))
print(img.shape,label.shape)
img=img.cuda()
pred=model(img)
pred.shape

# In[17]:

loss_fn=nn.CrossEntropyLoss()
optimizer=torch.optim.Adam(model.parameters(),lr=0.00001)

##  In[18]:

from tqdm import tqdm
def train(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []
    
    model.train()
    for x, y in tqdm(trainloader):
 
        x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_iou.append(batch_iou.item())
            
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / (total*96*96*46)#46为训练集最大层数
    # epoch_acc = correct / (total * 96 * 96*50)
        
    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    epoch_test_iou = []
    
    model.eval()
    with torch.no_grad():
        for x, y in tqdm(testloader):

            x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_test_iou.append(batch_iou.item())
            
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / (test_total*96*96*64) #96*96为图像大小，64为验证集最大层数
    # epoch_test_acc = test_correct / (test_total * 96 * 96*50)

    if np.mean(epoch_test_iou)>0.9:
            static_dict=model.state_dict()
            torch.save(static_dict,'./checkpoint/{}_trainIOU_{}_testIOU_{}.pth'.format(epoch,round(np.mean(epoch_iou), 3),round(np.mean(epoch_test_iou),3)))
    print('epoch: ', epoch, 
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean(epoch_iou), 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
           'test_iou:', round(np.mean(epoch_test_iou), 3)
             )
        
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc

# In[19]:

epochs = 5

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    train(epoch,model,train_dl,test_dl)

##  In[ ]:

img,label=next(iter(train_dl))
print(img.shape,label.shape)

# In[ ]:

img=img.to('cuda')
pred=model(img)
pred.shape

# In[ ]:

label_show=label[0,20,:,:]
plt.imshow(label_show,cmap='gray')


# In[ ]:

pred=pred[:,:,20,:,:]
pred.shape

# In[ ]:
preds = pred.cpu()
print(preds.shape)

plt.imshow(torch.argmax(preds[0].permute(1, 2, 0), axis=-1).detach().numpy(),cmap='gray')
# plt.imshow(torch.argmax(preds.permute(0, 2, 3, 1), axis=-1).detach().numpy()[0], cmap='gray')

# plt.imshow(label,cmap='gray')


label.shape

# In[ ]:

pred[0].shape

# plt.imshow(torch.argmax(pred.permute(1,2,0), axis=-1).detach().numpy())
plt.imshow(torch.argmax(pred[0].permute(1, 2, 0).cpu(), axis=-1).detach().numpy())

## 保存模型

PATH = r'H:\data\hcc_3dunet_model.pth' #路径不能有中文
torch.save(model.state_dict(), PATH)

# 测试模型
PATH = r'H:\data\hcc_3dunet_model.pth' #路径不能有中文
model = UNet3d(1,2,24)
model.load_state_dict(torch.load(PATH))

## In[71]:

image, mask = next(iter(test_dl))
pred_mask = model(image)
pred_mask.shape
pred_mask = torch.squeeze(pred_mask)
pred_mask.shape

num = 3
plt.figure(figsize=(10, 10))
for i in range(num):
    plt.subplot(num, 3, i*num+1)
    plt.imshow(image[i].permute(1,2,0).cpu().numpy())
    # plt.imshow(torch.argmax(preds.permute(0, 2, 3, 1), axis=-1).detach().numpy()[0], cmap='gray')
    plt.subplot(num, 3, i*num+2)
    plt.imshow(mask[i].cpu().numpy())
    plt.subplot(num, 3, i*num+3)
    plt.imshow(torch.argmax(pred_mask[i].permute(1,2,0), axis=-1).detach().numpy())
    # plt.imshow(torch.argmax(pred_mask[i].permute(0, 2, 3, 1), axis=-1).detach().numpy()[0], cmap='gray')

## 部分成功，仍需调试
import torch
import torch.nn.functional as F
import os
import SimpleITK as sitk
import numpy as np
import nibabel as nib
import torch
import cv2
from torchvision import transforms

transformer = transforms.Compose([
                    transforms.Resize((255, 255)),
                    # transforms.ToTensor(),
])

import nibabel as nib
import numpy as np
def add_mask2image_binary(images_path, masks_path):
    # Add binary masks to images
    for img_item in os.listdir(images_path):
        print(img_item)
        img_path = os.path.join(images_path, img_item)
        img_nii = nib.load(img_path)
        img_data = img_nii.get_fdata()
        # img_data_3d = np.repeat(img_data[:, :, np.newaxis, :], 3, axis=2)  # Add a third dimension by repeating the 2D image
        img_tensor = torch.from_numpy(img_data).unsqueeze(0).float()
        img_tensor = transformer(img_tensor)
        print(img_tensor.shape)
        img_tensor_batch = torch.unsqueeze(img_tensor, 0)
        img_tensor_batch.shape
        pred = model(img_tensor_batch)
        print(pred[0].shape)
        # pred = pred.cpu()
        pred = torch.argmax(pred[0].permute(1,2,3,0), axis=-1).detach().numpy()
        # pred = torch.argmax(pred[0].permute(1,2,0), axis=-1).detach().numpy()
        print(pred.shape)
        # pred = pred * 255.0
        # pred_nii = nib.Nifti1Image(pred.astype(np.uint8), img_nii.affine)
        pred_nii = nib.Nifti1Image(pred.astype(np.uint8), img_nii.affine, header=img_nii.header)
        nib.save(pred_nii, os.path.join(masks_path, img_item))
image_path = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\image\pp'
mask_path = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\unet_data\premask'
add_mask2image_binary(image_path, mask_path)

##%%批量nii图像预测 需要调试
import cv2
import glob
import nibabel as nib

from torchvision import transforms
img_transformer = transforms.Compose([
    transforms.Resize((96,96)),
    transforms.ToTensor()
])

pre_image_path = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\unet_data\pre_image_path'
original_image_filepaths= glob.glob(r'I:\1.HCC-VETC\datasets\HCC-suzhou2\unet_data\pre_image_path\*.nii.gz')
original_image_filepaths[:5]

for original_image_filepath in original_image_filepaths:
    # Load the original image
    original_image = nib.load(original_image_filepath)
    # Get the shape and affine transformation of the original image
    original_shape = original_image.shape
    original_affine = original_image.affine
    # Initialize an empty mask with the same size as the original image
    full_mask = np.zeros(original_shape, dtype=np.uint8)

    for z in range(original_shape[2]):
        # Extract a slice from the original image
        slice_image = original_image.get_fdata()[:, :, z]
        # Convert the numpy array to PIL.Image
        img_pil = Image.fromarray(slice_image)
        # img_pil = img_pil.convert("RGB")
        # Apply the image transformer
        input_image = img_transformer(img_pil).unsqueeze(0)
        # Make predictions on the input image
        model.eval()
        pred_mask = model(input_image)
        # Get the binary mask
        binary_pred_mask = (torch.argmax(pred_mask.squeeze(), dim=0) > 0).float()
        # Resize the binary_pred_mask to the same shape as full_mask
        resized_binary_pred_mask = cv2.resize(binary_pred_mask.cpu().numpy(),
                                              (full_mask.shape[1], full_mask.shape[0]))
        # Add the predicted mask to the empty mask
        full_mask[:, :, z] = resized_binary_pred_mask
    # Convert the completed mask to a Nifti1Image object
    mask_nifti = nib.Nifti1Image(full_mask, affine=original_affine)
    # Create a new filename for the mask file
    mask_filename = os.path.splitext(os.path.basename(original_image_filepath))[0] + '_mask.nii.gz'
    # Save the mask file in the same folder as the original image
    mask_filepath = os.path.join(pre_image_path, mask_filename)
    # Save the mask as a nii.gz file
    nib.save(mask_nifti, mask_filepath)

## 3D 语义分割  b站多组学进击君代码  已运行成功
import os
import numpy as np
import nibabel as nib
from tqdm import tqdm
from sklearn.model_selection import train_test_split
import matplotlib
matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
from torchvision import transforms
from torch.utils import data
import numpy as np
from torch.utils.data import Dataset
import torchvision.transforms as transforms
from PIL import Image
import torch
import torch.nn as nn
import torchvision
import segmentation_models_pytorch as smp
from torch.optim import lr_scheduler
import cv2

def make_dataset(root):
    imgs = []
    for filename in os.listdir(root):
        if filename.endswith('.nii.gz'):
            img_path = os.path.join(root, filename)
            imgs.append(img_path)
    return imgs

def load_nii_to_array(filepath):
    img = nib.load(filepath)
    img_array = np.array(img.dataobj)
    return img_array

def load_data(image_path, mask_path):
    images = []
    masks = []
    image_files = make_dataset(image_path)
    mask_files = make_dataset(mask_path)
    for img_file, mask_file in zip(image_files, mask_files):
        img_arr = load_nii_to_array(img_file)
        mask_arr = load_nii_to_array(mask_file)
        for layer in range(img_arr.shape[2]):
            img_layer = img_arr[:,:,layer]
            mask_layer = mask_arr[:,:,layer]
            if np.sum(mask_layer) == 0:
                continue
            images.append(img_layer)
            masks.append(mask_layer)
    return np.array(images, dtype=object), np.array(masks, dtype=object)

 # Load training data
train_image_path = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\image\pp'
train_mask_path = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\mask\pp'
train_images, train_masks = load_data(train_image_path, train_mask_path)
print(train_images.shape)
print(train_masks.shape)
#  # Load validation data
# val_image_path = r'C:\Users\<USER>\Desktop\gz-sa\ls\ls\images'
# val_mask_path = r'C:\Users\<USER>\Desktop\gz-sa\ls\ls\masks'
# val_images, val_masks = load_data(val_image_path, val_mask_path)
 # Split data into training and validation
train_images, val_images, train_masks, val_masks = train_test_split(train_images, train_masks, test_size=0.3, random_state=42)


def load_pre_images(image_path):
    pre_images = []
    image_files = make_dataset(image_path)
    for img_file in image_files:
        img_arr = load_nii_to_array(img_file)
        for layer in range(img_arr.shape[2]):
            img_layer = img_arr[:, :, layer]
            pre_images.append(img_layer)
    return np.array(pre_images)

pre_image_path= r'I:\1.HCC-VETC\datasets\HCC-suzhou2\unet_data\pre_image_path'
pre_images=load_pre_images(pre_image_path)
print(pre_images.shape)

def display_image_and_mask(image, mask):
    """
    Display the image and mask.
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    ax1.imshow(image)
    ax1.set_title('Image')
    ax1.axis('off')
    ax2.imshow(mask, cmap='gray')
    ax2.set_title('Mask')
    ax2.axis('off')
    plt.show()
 # Load the third nii.gz file
first_image_file = make_dataset(train_image_path)[2]
first_mask_file = make_dataset(train_mask_path)[2]
 # Load the image and mask as numpy arrays
first_image_array = load_nii_to_array(first_image_file)
first_mask_array = load_nii_to_array(first_mask_file)
 # Extract the 21st image and mask
image_20 = first_image_array[:, :, 20]
mask_20 = first_mask_array[:, :, 20]
 # Display the 20th image and mask
display_image_and_mask(image_20, mask_20)

class LiverDataset(Dataset):
    def __init__(self, img, mask, img_transformer, mask_transformer):
        self.img = img
        self.mask = mask
        self.img_transformer = img_transformer
        self.mask_transformer = mask_transformer
    def __getitem__(self, index):
        img = self.img[index]
        mask = self.mask[index]
        # print(img.shape)
        # Convert numpy arrays to PIL.Image
        img_pil = Image.fromarray(img)
        img_pil = img_pil.convert("RGB")
        mask_pil = Image.fromarray(mask)
        # Apply transformers
        img_tensor = self.img_transformer(img_pil).float()
        mask_tensor = self.mask_transformer(mask_pil).long()
        # Convert mask tensor to long type and remove unnecessary dimensions
        mask_tensor = torch.squeeze(mask_tensor).type(torch.LongTensor)
        return img_tensor, mask_tensor
    def __len__(self):
        return len(self.img)

# class LiverDataset(Dataset):
#     def __init__(self, img, mask, img_transformer, mask_transformer):
#         self.img = img
#         self.mask = mask
#         self.img_transformer = img_transformer
#         self.mask_transformer = mask_transformer
#     def __getitem__(self, index):
#         img = self.img[index]
#         mask = self.mask[index]
#          # Convert numpy arrays to PIL.Image
#         img_pil = Image.fromarray(img).convert("RGB")
#         mask_pil = Image.fromarray(mask).convert("L")
#          # Apply transformers
#         img_tensor = self.img_transformer(img_pil).float()
#         mask_tensor = self.mask_transformer(mask_pil).long()
#          # Convert mask tensor to long type and remove unnecessary dimensions
#         mask_tensor = torch.squeeze(mask_tensor).type(torch.LongTensor)
#         return img_tensor, mask_tensor
#     def __len__(self):
#         return len(self.img)

class PreLiverDataset(Dataset):
    def __init__(self, img, img_transformer):
        self.img = img
        self.img_transformer = img_transformer
    def __getitem__(self, index):
        img = self.img[index]
        # Convert numpy array to PIL.Image
        # img = np.squeeze(img)  # Remove the extra dimension if present
        # img = img.astype(np.uint8)
        img_pil = Image.fromarray(img)
        img_pil = img_pil.convert("RGB")
        # Apply transformer
        img_tensor = self.img_transformer(img_pil).float()
        return img_tensor
    def __len__(self):
        return len(self.img)

 # Define transformers
train_img_transforms = transforms.Compose([
    transforms.Resize((128, 128)),
    transforms.ToTensor(),
])
train_mask_transforms = transforms.Compose([
    transforms.Resize((128, 128), interpolation=Image.NEAREST),
    transforms.ToTensor(),
])

test_img_transforms = transforms.Compose([
    transforms.Resize((128, 128)),
    transforms.ToTensor(),
])

test_mask_transforms = transforms.Compose([
    transforms.Resize((128, 128), interpolation=Image.NEAREST),
    transforms.ToTensor()
])

train_data = LiverDataset(train_images, train_masks, train_img_transforms,train_mask_transforms)
test_data = LiverDataset(val_images, val_masks, test_img_transforms,test_mask_transforms)
pre_data = PreLiverDataset(pre_images, test_img_transforms)
len(pre_data)
dl_train = data.DataLoader(train_data, batch_size=16, shuffle=True)
dl_test = data.DataLoader(test_data, batch_size=16, shuffle=False)
dl_pre = data.DataLoader(pre_data, batch_size=16, shuffle=False)

## Define model   可换成 smp.Unet；smp.PSPNet；smp.Linknet；smp.FPN；smp.DeepLabV3Plus
model = smp.PSPNet(
    encoder_name="resnet34",  # choose encoder
    encoder_weights="imagenet",  # use "imagenet" pre-training
    in_channels=3,  # grayscale1,彩色3
    classes=2,  # number of output classes
)

img_pil, mask_pil = next(iter(dl_train))
# img = img.to('cuda')
# model = model.to('cuda')
pred = model(img_pil)
print(pred.shape)
# Size([16, 2, 128, 128])

img_pil = next(iter(dl_pre))
# img = img.to('cuda')
# model = model.to('cuda')
pred = model(img_pil)
print(pred.shape)
# Size([16, 2, 128, 128])

if torch.cuda.is_available():
    model.to('cuda')

 # Define optimizer and learning rate scheduler
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
 # Define loss function
loss_fn = nn.CrossEntropyLoss()


from tqdm import tqdm
import numpy as np
def mean_iou(y_true, y_pred, num_classes=2):
    ious = []
    for cls in range(num_classes):
        y_true_cls = (y_true == cls)
        y_pred_cls = (y_pred == cls)
        intersection = torch.logical_and(y_true_cls, y_pred_cls).sum().item()
        union = torch.logical_or(y_true_cls, y_pred_cls).sum().item()
         # Avoid division by zero
        if union == 0:
            iou = 1.0
        else:
            iou = intersection / union
        ious.append(iou)
    return np.mean(ious)

def fit(epoch, model, train_loader, test_loader):
    """
    Train the model for one epoch.
    """
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []
    model.train()
    for (x, y) in tqdm(train_loader):
        x = x.to('cuda')
        y = y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
         # Calculate IoU for the training set
            batch_iou = mean_iou(y, y_pred)
            epoch_iou.append(batch_iou)
    epoch_loss = running_loss / len(train_loader.dataset)
    epoch_acc = correct / (total * 128 * 128)

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    epoch_test_iou = []
    model.eval()

    with torch.no_grad():
        for x, y in tqdm(test_loader):
            x = x.to('cuda')
            y = y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            # Calculate IoU for the test set
            batch_iou = mean_iou(y, y_pred)
            epoch_test_iou.append(batch_iou)
        epoch_test_loss = test_running_loss / len(test_loader.dataset)
        epoch_test_acc = test_correct / (test_total * 128 * 128)
        print('epoch:', epoch,
              'loss:', round(epoch_loss, 3),
              'accuracy:', round(epoch_acc, 3),
              'IOU:', round(np.mean(epoch_iou), 3),
              'test_loss:', round(epoch_test_loss, 3),
              'test_accuracy:', round(epoch_test_acc, 3),
              'test_iou:', round(np.mean(epoch_test_iou), 3))
        return epoch_loss, epoch_acc, np.mean(epoch_iou),epoch_test_loss, epoch_test_acc, np.mean(epoch_test_iou)


epochs = 5
        # Train the model
train_loss = []
train_acc = []
test_loss = []
test_acc = []
test_iou = []
for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou,epoch_test_loss, epoch_test_acc, epoch_test_iou = fit(epoch, model,dl_train,dl_test)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_acc.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)

## 保存最佳参数模型
import torch
import shutil

def save_checkpoint(state, is_best_iou, is_best_acc, filename):
    torch.save(state, filename)
    if is_best_iou:
        shutil.copyfile(filename, 'model_best_iou.pth')
    if is_best_acc:
        shutil.copyfile(filename, 'model_best_acc.pth')

train_loss = []
train_acc = []
test_loss = []
test_acc = []
train_iou = []
test_iou = []

 # Initialize best metric variables
best_test_iou = 0
best_test_acc = 0

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou,epoch_test_loss, epoch_test_acc, epoch_test_iou = fit(epoch, model,dl_train,dl_test)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)

    # Check if there is a better validation IoU
    is_best_iou = epoch_test_iou > best_test_iou
    best_test_iou = max(epoch_test_iou, best_test_iou)

    # Check if there is a better validation accuracy
    is_best_acc = epoch_test_acc > best_test_acc
    best_test_acc = max(epoch_test_acc, best_test_acc)

# Save the model for the current epoch
    save_checkpoint({
    'epoch': epoch + 1,
    'state_dict': model.state_dict(),
    'best_test_iou': best_test_iou,
    'best_test_acc': best_test_acc,
    'optimizer': optimizer.state_dict(),
    }, is_best_iou, is_best_acc,filename='H:/data/model1_best_iou.pth')

##
PATH = r'H:\data\model1_best_iou.pth' #路径不能有中文
torch.save(model.state_dict(), PATH)


 # Create a new instance of the model with the same structure
model = smp.PSPNet(
    encoder_name='resnet34',
    in_channels=3,
    classes=2
)

model.load_state_dict(torch.load(PATH))


 # Move the image to the appropriate device (e.g. GPU)
image = next(iter(dl_pre))
# image = image.to('cuda')
 # Set the model to evaluation mode
model.eval()
 # Make predictions on the image
pred_mask = model(image)
pred_mask
 # Get the shape of the predicted mask
pred_mask.shape
 # Move the predicted mask to CPU
pred_mask = pred_mask.cpu()

##
num = 3
plt.figure(figsize=(10, 10))
i= 0
for i in range(num):
    plt.subplot(num, 3, i*num+1)
    plt.imshow(image[i].permute(1, 2, 0).cpu().numpy(), cmap='gray')
    plt.subplot(num, 3, i*num+2)
    binary_pred_mask = (torch.argmax(pred_mask[i], dim=0).cpu().numpy() == 1)
    plt.imshow(binary_pred_mask, cmap='gray')
##预测新的数据集
import cv2
import glob

from torchvision import transforms
img_transformer = transforms.Compose([
    transforms.Resize((128, 128)),
    transforms.ToTensor()
])

original_image_filepaths= glob.glob(r'I:\1.HCC-VETC\datasets\HCC-suzhou2\unet_data\pre_image_path\*.nii.gz')
original_image_filepaths[:5]

for original_image_filepath in original_image_filepaths:
    # Load the original image
    original_image = nib.load(original_image_filepath)
    # Get the shape and affine transformation of the original image
    original_shape = original_image.shape
    original_affine = original_image.affine
    # Initialize an empty mask with the same size as the original image
    full_mask = np.zeros(original_shape, dtype=np.uint8)

    for z in range(original_shape[2]):
        # Extract a slice from the original image
        slice_image = original_image.get_fdata()[:, :, z]
        # Convert the numpy array to PIL.Image
        img_pil = Image.fromarray(slice_image)
        img_pil = img_pil.convert("RGB")
        # Apply the image transformer
        input_image = img_transformer(img_pil).unsqueeze(0)
        # Make predictions on the input image
        model.eval()
        pred_mask = model(input_image)
        # Get the binary mask
        binary_pred_mask = (torch.argmax(pred_mask.squeeze(), dim=0) > 0).float()
        # Resize the binary_pred_mask to the same shape as full_mask
        resized_binary_pred_mask = cv2.resize(binary_pred_mask.cpu().numpy(),
                                              (full_mask.shape[1], full_mask.shape[0]))
        # Add the predicted mask to the empty mask
        full_mask[:, :, z] = resized_binary_pred_mask
    # Convert the completed mask to a Nifti1Image object
    mask_nifti = nib.Nifti1Image(full_mask, affine=original_affine)
    # Create a new filename for the mask file
    mask_filename = os.path.splitext(os.path.basename(original_image_filepath))[0] + '_mask.nii.gz'
    # Save the mask file in the same folder as the original image
    mask_filepath = os.path.join(pre_image_path, mask_filename)
    # Save the mask as a nii.gz file
    nib.save(mask_nifti, mask_filepath)

#%%pip install mumba_ssm      csdn：VM-UNet:视觉Mamba UNet用来医学图像分割 论文及代码解读
import time
import math
from functools import partial
from typing import Optional, Callable

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint as checkpoint
from einops import rearrange, repeat
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
try:
    from mamba_ssm.ops.selective_scan_interface import selective_scan_fn, selective_scan_ref
except:
    pass

# an alternative for mamba_ssm (in which causal_conv1d is needed)
# try:
#     from selective_scan import selective_scan_fn as selective_scan_fn_v1
#     from selective_scan import selective_scan_ref as selective_scan_ref_v1
# except:
#     pass

DropPath.__repr__ = lambda self: f"timm.DropPath({self.drop_prob})"


def flops_selective_scan_ref(B=1, L=256, D=768, N=16, with_D=True, with_Z=False, with_Group=True, with_complex=False):
    """
    u: r(B D L)
    delta: r(B D L)
    A: r(D N)
    B: r(B N L)
    C: r(B N L)
    D: r(D)
    z: r(B D L)
    delta_bias: r(D), fp32
    
    ignores:
        [.float(), +, .softplus, .shape, new_zeros, repeat, stack, to(dtype), silu] 
    """
    import numpy as np
    
    # fvcore.nn.jit_handles
    def get_flops_einsum(input_shapes, equation):
        np_arrs = [np.zeros(s) for s in input_shapes]
        optim = np.einsum_path(equation, *np_arrs, optimize="optimal")[1]
        for line in optim.split("\n"):
            if "optimized flop" in line.lower():
                # divided by 2 because we count MAC (multiply-add counted as one flop)
                flop = float(np.floor(float(line.split(":")[-1]) / 2))
                return flop
    

    assert not with_complex

    flops = 0 # below code flops = 0
    if False:
        ...
        """
        dtype_in = u.dtype
        u = u.float()
        delta = delta.float()
        if delta_bias is not None:
            delta = delta + delta_bias[..., None].float()
        if delta_softplus:
            delta = F.softplus(delta)
        batch, dim, dstate = u.shape[0], A.shape[0], A.shape[1]
        is_variable_B = B.dim() >= 3
        is_variable_C = C.dim() >= 3
        if A.is_complex():
            if is_variable_B:
                B = torch.view_as_complex(rearrange(B.float(), "... (L two) -> ... L two", two=2))
            if is_variable_C:
                C = torch.view_as_complex(rearrange(C.float(), "... (L two) -> ... L two", two=2))
        else:
            B = B.float()
            C = C.float()
        x = A.new_zeros((batch, dim, dstate))
        ys = []
        """

    flops += get_flops_einsum([[B, D, L], [D, N]], "bdl,dn->bdln")
    if with_Group:
        flops += get_flops_einsum([[B, D, L], [B, N, L], [B, D, L]], "bdl,bnl,bdl->bdln")
    else:
        flops += get_flops_einsum([[B, D, L], [B, D, N, L], [B, D, L]], "bdl,bdnl,bdl->bdln")
    if False:
        ...
        """
        deltaA = torch.exp(torch.einsum('bdl,dn->bdln', delta, A))
        if not is_variable_B:
            deltaB_u = torch.einsum('bdl,dn,bdl->bdln', delta, B, u)
        else:
            if B.dim() == 3:
                deltaB_u = torch.einsum('bdl,bnl,bdl->bdln', delta, B, u)
            else:
                B = repeat(B, "B G N L -> B (G H) N L", H=dim // B.shape[1])
                deltaB_u = torch.einsum('bdl,bdnl,bdl->bdln', delta, B, u)
        if is_variable_C and C.dim() == 4:
            C = repeat(C, "B G N L -> B (G H) N L", H=dim // C.shape[1])
        last_state = None
        """
    
    in_for_flops = B * D * N   
    if with_Group:
        in_for_flops += get_flops_einsum([[B, D, N], [B, D, N]], "bdn,bdn->bd")
    else:
        in_for_flops += get_flops_einsum([[B, D, N], [B, N]], "bdn,bn->bd")
    flops += L * in_for_flops 
    if False:
        ...
        """
        for i in range(u.shape[2]):
            x = deltaA[:, :, i] * x + deltaB_u[:, :, i]
            if not is_variable_C:
                y = torch.einsum('bdn,dn->bd', x, C)
            else:
                if C.dim() == 3:
                    y = torch.einsum('bdn,bn->bd', x, C[:, :, i])
                else:
                    y = torch.einsum('bdn,bdn->bd', x, C[:, :, :, i])
            if i == u.shape[2] - 1:
                last_state = x
            if y.is_complex():
                y = y.real * 2
            ys.append(y)
        y = torch.stack(ys, dim=2) # (batch dim L)
        """

    if with_D:
        flops += B * D * L
    if with_Z:
        flops += B * D * L
    if False:
        ...
        """
        out = y if D is None else y + u * rearrange(D, "d -> d 1")
        if z is not None:
            out = out * F.silu(z)
        out = out.to(dtype=dtype_in)
        """
    
    return flops


# 只使用了一次，将数据使用4*4的卷积和LN产生patch，然后转化为b*w*h*c
class PatchEmbed2D(nn.Module):
    r""" Image to Patch Embedding
    Args:
        patch_size (int): Patch token size. Default: 4.
        in_chans (int): Number of input image channels. Default: 3.
        embed_dim (int): Number of linear projection output channels. Default: 96.
        norm_layer (nn.Module, optional): Normalization layer. Default: None
    """
    def __init__(self, patch_size=4, in_chans=3, embed_dim=96, norm_layer=None, **kwargs):
        super().__init__()
        if isinstance(patch_size, int):
            patch_size = (patch_size, patch_size)
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        if norm_layer is not None:
            self.norm = norm_layer(embed_dim)
        else:
            self.norm = None

    def forward(self, x):
        x = self.proj(x).permute(0, 2, 3, 1)
        if self.norm is not None:
            x = self.norm(x)
        return x


class PatchMerging2D(nn.Module):
    r""" Patch Merging Layer.
    Args:
        input_resolution (tuple[int]): Resolution of input feature.
        dim (int): Number of input channels.
        norm_layer (nn.Module, optional): Normalization layer.  Default: nn.LayerNorm
    """

    def __init__(self, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)

    def forward(self, x):
        B, H, W, C = x.shape

        SHAPE_FIX = [-1, -1]
        if (W % 2 != 0) or (H % 2 != 0):
            print(f"Warning, x.shape {x.shape} is not match even ===========", flush=True)
            SHAPE_FIX[0] = H // 2
            SHAPE_FIX[1] = W // 2

        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C

        if SHAPE_FIX[0] > 0:
            x0 = x0[:, :SHAPE_FIX[0], :SHAPE_FIX[1], :]
            x1 = x1[:, :SHAPE_FIX[0], :SHAPE_FIX[1], :]
            x2 = x2[:, :SHAPE_FIX[0], :SHAPE_FIX[1], :]
            x3 = x3[:, :SHAPE_FIX[0], :SHAPE_FIX[1], :]
        
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, H//2, W//2, 4 * C)  # B H/2*W/2 4*C

        x = self.norm(x)
        x = self.reduction(x)

        return x
    

class PatchExpand2D(nn.Module):
    def __init__(self, dim, dim_scale=2, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim*2
        self.dim_scale = dim_scale
        self.expand = nn.Linear(self.dim, dim_scale*self.dim, bias=False)
        self.norm = norm_layer(self.dim // dim_scale)

    def forward(self, x):
        B, H, W, C = x.shape
        x = self.expand(x)

        x = rearrange(x, 'b h w (p1 p2 c)-> b (h p1) (w p2) c', p1=self.dim_scale, p2=self.dim_scale, c=C//self.dim_scale)
        x= self.norm(x)

        return x
    

class Final_PatchExpand2D(nn.Module):
    def __init__(self, dim, dim_scale=4, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim  # 96
        self.dim_scale = dim_scale  # 4
                          #        96             384
        self.expand = nn.Linear(self.dim, dim_scale*self.dim, bias=False)
        #                          24  
        self.norm = norm_layer(self.dim // dim_scale)

    def forward(self, x):
        B, H, W, C = x.shape
        x = self.expand(x)

        x = rearrange(x, 'b h w (p1 p2 c)-> b (h p1) (w p2) c', p1=self.dim_scale, p2=self.dim_scale, c=C//self.dim_scale)
        x= self.norm(x)

        return x


class SS2D(nn.Module):
    def __init__(
        self,
        d_model,   # 96
        d_state=16,
        d_conv=3,
        expand=2,
        dt_rank="auto",
        dt_min=0.001,
        dt_max=0.1,
        dt_init="random",
        dt_scale=1.0,
        dt_init_floor=1e-4,
        dropout=0.,
        conv_bias=True,
        bias=False,
    ):
        super().__init__()
        self.d_model = d_model  # 96
        self.d_state = d_state  # 16
        self.d_conv = d_conv   # 3
        self.expand = expand   # 2
        self.d_inner = int(self.expand * self.d_model)  # 192
        self.dt_rank = math.ceil(self.d_model / 16)  # 6

        #                           96                 384
        self.in_proj = nn.Linear(self.d_model, self.d_inner * 2, bias=bias)
        self.conv2d = nn.Conv2d(
            in_channels=self.d_inner,  # 192
            out_channels=self.d_inner,  # 192
            kernel_size=d_conv,  # 3
            padding=(d_conv - 1) // 2,    # 1
            bias=conv_bias,  
            groups=self.d_inner,  # 192  
        )
        self.act = nn.SiLU()

        self.x_proj = (
            nn.Linear(self.d_inner, (self.dt_rank + self.d_state * 2), bias=False), 
            nn.Linear(self.d_inner, (self.dt_rank + self.d_state * 2), bias=False), 
            nn.Linear(self.d_inner, (self.dt_rank + self.d_state * 2), bias=False), 
            nn.Linear(self.d_inner, (self.dt_rank + self.d_state * 2), bias=False), 
        )
        # 4*38*192的数据 初始化x的数据
        self.x_proj_weight = nn.Parameter(torch.stack([t.weight for t in self.x_proj], dim=0)) # (K=4, N, inner)
        del self.x_proj

        # 初始化dt的数据吧
        self.dt_projs = (
            self.dt_init(self.dt_rank, self.d_inner, dt_scale, dt_init, dt_min, dt_max, dt_init_floor),
            self.dt_init(self.dt_rank, self.d_inner, dt_scale, dt_init, dt_min, dt_max, dt_init_floor),
            self.dt_init(self.dt_rank, self.d_inner, dt_scale, dt_init, dt_min, dt_max, dt_init_floor),
            self.dt_init(self.dt_rank, self.d_inner, dt_scale, dt_init, dt_min, dt_max, dt_init_floor),
        )
        self.dt_projs_weight = nn.Parameter(torch.stack([t.weight for t in self.dt_projs], dim=0)) # (K=4, inner, rank)
        self.dt_projs_bias = nn.Parameter(torch.stack([t.bias for t in self.dt_projs], dim=0)) # (K=4, inner)
        del self.dt_projs
        # 初始化A和D
        self.A_logs = self.A_log_init(self.d_state, self.d_inner, copies=4, merge=True) # (K=4, D, N)
        self.Ds = self.D_init(self.d_inner, copies=4, merge=True) # (K=4, D, N)

        # ss2d
        self.forward_core = self.forward_corev0

        self.out_norm = nn.LayerNorm(self.d_inner)
        self.out_proj = nn.Linear(self.d_inner, self.d_model, bias=bias)
        self.dropout = nn.Dropout(dropout) if dropout > 0. else None

    @staticmethod
    def dt_init(dt_rank, d_inner, dt_scale=1.0, dt_init="random", dt_min=0.001, dt_max=0.1, dt_init_floor=1e-4, **factory_kwargs):
        dt_proj = nn.Linear(dt_rank, d_inner, bias=True, **factory_kwargs)

        # Initialize special dt projection to preserve variance at initialization
        dt_init_std = dt_rank**-0.5 * dt_scale
        if dt_init == "constant":
            nn.init.constant_(dt_proj.weight, dt_init_std)
        elif dt_init == "random":
            nn.init.uniform_(dt_proj.weight, -dt_init_std, dt_init_std)
        else:
            raise NotImplementedError

        # Initialize dt bias so that F.softplus(dt_bias) is between dt_min and dt_max
        dt = torch.exp(
            torch.rand(d_inner, **factory_kwargs) * (math.log(dt_max) - math.log(dt_min))
            + math.log(dt_min)
        ).clamp(min=dt_init_floor)
        # Inverse of softplus: https://github.com/pytorch/pytorch/issues/72759
        inv_dt = dt + torch.log(-torch.expm1(-dt))
        with torch.no_grad():
            dt_proj.bias.copy_(inv_dt)
        # Our initialization would set all Linear.bias to zero, need to mark this one as _no_reinit
        dt_proj.bias._no_reinit = True
        
        return dt_proj

    @staticmethod
    def A_log_init(d_state, d_inner, copies=1, device=None, merge=True):
        # S4D real initialization
        A = repeat(
            torch.arange(1, d_state + 1, dtype=torch.float32, device=device),
            "n -> d n",
            d=d_inner,
        ).contiguous()
        A_log = torch.log(A)  # Keep A_log in fp32
        if copies > 1:
            A_log = repeat(A_log, "d n -> r d n", r=copies)
            if merge:
                A_log = A_log.flatten(0, 1)
        A_log = nn.Parameter(A_log)
        A_log._no_weight_decay = True
        return A_log

    @staticmethod
    def D_init(d_inner, copies=1, device=None, merge=True):
        # D "skip" parameter
        D = torch.ones(d_inner, device=device)
        if copies > 1:
            D = repeat(D, "n1 -> r n1", r=copies)
            if merge:
                D = D.flatten(0, 1)
        D = nn.Parameter(D)  # Keep in fp32
        D._no_weight_decay = True
        return D

    def forward_corev0(self, x: torch.Tensor):
        self.selective_scan = selective_scan_fn
        
        B, C, H, W = x.shape
        L = H * W
        K = 4

        x_hwwh = torch.stack([x.view(B, -1, L), torch.transpose(x, dim0=2, dim1=3).contiguous().view(B, -1, L)], dim=1).view(B, 2, -1, L)
        xs = torch.cat([x_hwwh, torch.flip(x_hwwh, dims=[-1])], dim=1) # (b, k, d, l)

        x_dbl = torch.einsum("b k d l, k c d -> b k c l", xs.view(B, K, -1, L), self.x_proj_weight)
        dts, Bs, Cs = torch.split(x_dbl, [self.dt_rank, self.d_state, self.d_state], dim=2)
        dts = torch.einsum("b k r l, k d r -> b k d l", dts.view(B, K, -1, L), self.dt_projs_weight)

        xs = xs.float().view(B, -1, L) # (b, k * d, l)
        dts = dts.contiguous().float().view(B, -1, L) # (b, k * d, l)
        Bs = Bs.float().view(B, K, -1, L) # (b, k, d_state, l)
        Cs = Cs.float().view(B, K, -1, L) # (b, k, d_state, l)
        Ds = self.Ds.float().view(-1) # (k * d)
        As = -torch.exp(self.A_logs.float()).view(-1, self.d_state)  # (k * d, d_state)
        dt_projs_bias = self.dt_projs_bias.float().view(-1) # (k * d)

        out_y = self.selective_scan(
            xs, dts, 
            As, Bs, Cs, Ds, z=None,
            delta_bias=dt_projs_bias,
            delta_softplus=True,
            return_last_state=False,
        ).view(B, K, -1, L)
        assert out_y.dtype == torch.float

        inv_y = torch.flip(out_y[:, 2:4], dims=[-1]).view(B, 2, -1, L)
        wh_y = torch.transpose(out_y[:, 1].view(B, -1, W, H), dim0=2, dim1=3).contiguous().view(B, -1, L)
        invwh_y = torch.transpose(inv_y[:, 1].view(B, -1, W, H), dim0=2, dim1=3).contiguous().view(B, -1, L)

        return out_y[:, 0], inv_y[:, 0], wh_y, invwh_y

    def forward(self, x: torch.Tensor):
        B, H, W, C = x.shape

        xz = self.in_proj(x)
        x, z = xz.chunk(2, dim=-1) # (b, h, w, d)  # x走的是ss2d的路径

        x = x.permute(0, 3, 1, 2).contiguous()
        x = self.act(self.conv2d(x)) # (b, d, h, w)  
        y1, y2, y3, y4 = self.forward_core(x)
        assert y1.dtype == torch.float32
        y = y1 + y2 + y3 + y4
        y = torch.transpose(y, dim0=1, dim1=2).contiguous().view(B, H, W, -1)
        y = self.out_norm(y)
        y = y * F.silu(z)   # 这里的z忘记了一个Linear吧
        out = self.out_proj(y)
        if self.dropout is not None:
            out = self.dropout(out)
        return out


class VSSBlock(nn.Module):
    def __init__(
        self,
        hidden_dim: int = 0,  # 96
        drop_path: float = 0,  # 0.2
        norm_layer: Callable[..., torch.nn.Module] = partial(nn.LayerNorm, eps=1e-6),  # nn.LN
        attn_drop_rate: float = 0,  # 0
        d_state: int = 16,
    ):
        super().__init__()
        self.ln_1 = norm_layer(hidden_dim)# 96             0.2                   16
        self.self_attention = SS2D(d_model=hidden_dim, dropout=attn_drop_rate, d_state=d_state)
        self.drop_path = DropPath(drop_path)

    def forward(self, input: torch.Tensor):
        # print(input.shape, "传入模块的大小")
        x = input + self.drop_path(self.self_attention(self.ln_1(input)))
        return x


class VSSLayer(nn.Module):
    """ A basic Swin Transformer layer for one stage.
    Args:
        dim (int): Number of input channels.
        depth (int): Number of blocks.
        drop (float, optional): Dropout rate. Default: 0.0
        attn_drop (float, optional): Attention dropout rate. Default: 0.0
        drop_path (float | tuple[float], optional): Stochastic depth rate. Default: 0.0
        norm_layer (nn.Module, optional): Normalization layer. Default: nn.LayerNorm
        downsample (nn.Module | None, optional): Downsample layer at the end of the layer. Default: None
        use_checkpoint (bool): Whether to use checkpointing to save memory. Default: False.
    """

    def __init__(  # 以第一个为例
        self, 
        dim,  # # 96
        depth,  # 2
        d_state=16,
        drop = 0.,
        attn_drop=0.,
        drop_path=0.,   # 每一个模块都有一个drop
        norm_layer=nn.LayerNorm, 
        downsample=None,  # PatchMergin2D
        use_checkpoint=False,  
    ):
        super().__init__()
        self.dim = dim
        self.use_checkpoint = use_checkpoint

        self.blocks = nn.ModuleList([
            VSSBlock(
                hidden_dim=dim,   # 96
                drop_path=drop_path[i],  # 0.2
                norm_layer=norm_layer,  # nn.LN
                attn_drop_rate=attn_drop, # 0 
                d_state=d_state,  # 16
            )
            for i in range(depth)])
        
        if True: # is this really applied? Yes, but been overriden later in VSSM!
            def _init_weights(module: nn.Module):
                for name, p in module.named_parameters():
                    if name in ["out_proj.weight"]:
                        p = p.clone().detach_() # fake init, just to keep the seed ....
                        nn.init.kaiming_uniform_(p, a=math.sqrt(5))
            self.apply(_init_weights)

        if downsample is not None:
            self.downsample = downsample(dim=dim, norm_layer=norm_layer)
        else:
            self.downsample = None

    def forward(self, x):
        for blk in self.blocks:
                x = blk(x)
        
        if self.downsample is not None:
            x = self.downsample(x)

        return x
    


class VSSLayer_up(nn.Module):
    """ A basic Swin Transformer layer for one stage.
    Args:
        dim (int): Number of input channels.
        depth (int): Number of blocks.
        drop (float, optional): Dropout rate. Default: 0.0
        attn_drop (float, optional): Attention dropout rate. Default: 0.0
        drop_path (float | tuple[float], optional): Stochastic depth rate. Default: 0.0
        norm_layer (nn.Module, optional): Normalization layer. Default: nn.LayerNorm
        downsample (nn.Module | None, optional): Downsample layer at the end of the layer. Default: None
        use_checkpoint (bool): Whether to use checkpointing to save memory. Default: False.
    """

    def __init__(
        self, 
        dim, 
        depth, 
        attn_drop=0.,
        drop_path=0., 
        norm_layer=nn.LayerNorm, 
        upsample=None, 
        use_checkpoint=False, 
        d_state=16,
        **kwargs,
    ):
        super().__init__()
        self.dim = dim
        self.use_checkpoint = use_checkpoint

        self.blocks = nn.ModuleList([
            VSSBlock(
                hidden_dim=dim,
                drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path,
                norm_layer=norm_layer,
                attn_drop_rate=attn_drop,
                d_state=d_state,
            )
            for i in range(depth)])
        
        if True: # is this really applied? Yes, but been overriden later in VSSM!
            def _init_weights(module: nn.Module):
                for name, p in module.named_parameters():
                    if name in ["out_proj.weight"]:
                        p = p.clone().detach_() # fake init, just to keep the seed ....
                        nn.init.kaiming_uniform_(p, a=math.sqrt(5))
            self.apply(_init_weights)

        if upsample is not None:
            self.upsample = upsample(dim=dim, norm_layer=norm_layer)
        else:
            self.upsample = None


    def forward(self, x):
        if self.upsample is not None:
            x = self.upsample(x)
        for blk in self.blocks:
            if self.use_checkpoint:
                x = checkpoint.checkpoint(blk, x)
            else:
                x = blk(x)
        return x
    


class VSSM(nn.Module):
    def __init__(self, patch_size=4, in_chans=3, num_classes=1000, depths=[2, 2, 9, 2], depths_decoder=[2, 9, 2, 2],
                 dims=[96, 192, 384, 768], dims_decoder=[768, 384, 192, 96], d_state=16, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, patch_norm=True,
                 use_checkpoint=False):
        super().__init__()
        self.num_classes = num_classes # 1
        self.num_layers = len(depths)  # 4
        if isinstance(dims, int):
            dims = [int(dims * 2 ** i_layer) for i_layer in range(self.num_layers)]
        self.embed_dim = dims[0]   # 96
        self.num_features = dims[-1]  # 768 
        self.dims = dims   # [96, 192, 384, 768]

        # 4*4+LN-> b*w*h*c
        self.patch_embed = PatchEmbed2D(patch_size=patch_size, in_chans=in_chans, embed_dim=self.embed_dim,
            norm_layer=norm_layer if patch_norm else None)
        
        
        self.pos_drop = nn.Dropout(p=drop_rate)

        # 生成对应的sum(depths)随机深度衰减数值 dpr是正序，dpr_decoder是倒序（用到了[start:end:-1] 反向步长）
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]  # stochastic depth decay rule
        dpr_decoder = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths_decoder))][::-1]

        self.layers = nn.ModuleList()
        for i_layer in range(self.num_layers):  # 以第一个为例 num_layers = 4
            layer = VSSLayer(
                dim=dims[i_layer],  # 96
                depth=depths[i_layer],  # 2
                d_state=d_state,  # 16
                drop=drop_rate,   # 0
                attn_drop=attn_drop_rate, # 0
                drop_path=dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])], # ，每一个模块传一个概率值
                norm_layer=norm_layer, # nn.LN
                downsample=PatchMerging2D if (i_layer < self.num_layers - 1) else None,
                use_checkpoint=use_checkpoint,
            )
            
            self.layers.append(layer)

        self.layers_up = nn.ModuleList()
        for i_layer in range(self.num_layers):  # 以第一个为例，num_layers=2
            layer = VSSLayer_up(
                dim=dims_decoder[i_layer],  # 768
                depth=depths_decoder[i_layer], # 2
                d_state=d_state,  # 16
                drop=drop_rate,  # 0
                attn_drop=attn_drop_rate, # 0
                drop_path=dpr_decoder[sum(depths_decoder[:i_layer]):sum(depths_decoder[:i_layer + 1])],
                norm_layer=norm_layer, # nn.LN
                upsample=PatchExpand2D if (i_layer != 0) else None,
                use_checkpoint=use_checkpoint,
            )
            self.layers_up.append(layer)

        #  输入 64*64*96 ->linear+LN b*256*256*24                          96                             nn.LN
        self.final_up = Final_PatchExpand2D(dim=dims_decoder[-1], dim_scale=4, norm_layer=norm_layer)
        #     维度变换 输出b*1*256*256         24                 1 
        self.final_conv = nn.Conv2d(dims_decoder[-1]//4, num_classes, 1)
        self.apply(self._init_weights)

    def _init_weights(self, m: nn.Module):
        """
        out_proj.weight which is previously initilized in VSSBlock, would be cleared in nn.Linear
        no fc.weight found in the any of the model parameters
        no nn.Embedding found in the any of the model parameters
        so the thing is, VSSBlock initialization is useless
        
        Conv2D is not intialized !!!
        """
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
   
    def forward_features(self, x):
        skip_list = []
        x = self.patch_embed(x)
        x = self.pos_drop(x)

        for layer in self.layers:
            skip_list.append(x)
            x = layer(x)
        return x, skip_list
    
    def forward_features_up(self, x, skip_list):
        for inx, layer_up in enumerate(self.layers_up):
            if inx == 0:
                x = layer_up(x)
            else:
                x = layer_up(x+skip_list[-inx])

        return x
    
    def forward_final(self, x):
        # input 3*64*64*96   out=3 256 256 24
        x = self.final_up(x)   
        x = x.permute(0,3,1,2)
        # out=3 24 256 256 
        x = self.final_conv(x)
        return x

    def forward(self, x):
        x, skip_list = self.forward_features(x)
        x = self.forward_features_up(x, skip_list)
        x = self.forward_final(x)  
        return x


import torch
from torch import nn


class VMUNet(nn.Module):
    def __init__(self, 
                 input_channels=3, 
                 num_classes=1,
                 depths=[2, 2, 9, 2], 
                 depths_decoder=[2, 9, 2, 2],
                 drop_path_rate=0.2,
                 load_ckpt_path=None,
                ):
        super().__init__()

        self.load_ckpt_path = load_ckpt_path
        self.num_classes = num_classes

        self.vmunet = VSSM(in_chans=input_channels, # 3
                           num_classes=num_classes,  # 1
                           depths=depths,  # [2,2,9,2]
                           depths_decoder=depths_decoder, # [2,9,2,2]
                           drop_path_rate=drop_path_rate, # 0.2
                        )
    
    def forward(self, x):
        if x.size()[1] == 1:
            x = x.repeat(1,3,1,1)
        logits = self.vmunet(x)
        if self.num_classes == 1: return torch.sigmoid(logits)
        else: return logits
    
    # 加载预训练权重，暂时用不到
    def load_from(self):
        if self.load_ckpt_path is not None:
            model_dict = self.vmunet.state_dict()
            modelCheckpoint = torch.load(self.load_ckpt_path)
            pretrained_dict = modelCheckpoint['model']
            # 过滤操作
            new_dict = {k: v for k, v in pretrained_dict.items() if k in model_dict.keys()}
            model_dict.update(new_dict)
            # 打印出来，更新了多少的参数
            print('Total model_dict: {}, Total pretrained_dict: {}, update: {}'.format(len(model_dict), len(pretrained_dict), len(new_dict)))
            self.vmunet.load_state_dict(model_dict)

            not_loaded_keys = [k for k in pretrained_dict.keys() if k not in new_dict.keys()]
            print('Not loaded keys:', not_loaded_keys)
            print("encoder loaded finished!")

            model_dict = self.vmunet.state_dict()
            modelCheckpoint = torch.load(self.load_ckpt_path)
            pretrained_odict = modelCheckpoint['model']
            pretrained_dict = {}
            for k, v in pretrained_odict.items():
                if 'layers.0' in k: 
                    new_k = k.replace('layers.0', 'layers_up.3')
                    pretrained_dict[new_k] = v
                elif 'layers.1' in k: 
                    new_k = k.replace('layers.1', 'layers_up.2')
                    pretrained_dict[new_k] = v
                elif 'layers.2' in k: 
                    new_k = k.replace('layers.2', 'layers_up.1')
                    pretrained_dict[new_k] = v
                elif 'layers.3' in k: 
                    new_k = k.replace('layers.3', 'layers_up.0')
                    pretrained_dict[new_k] = v
            # 过滤操作
            new_dict = {k: v for k, v in pretrained_dict.items() if k in model_dict.keys()}
            model_dict.update(new_dict)
            # 打印出来，更新了多少的参数
            print('Total model_dict: {}, Total pretrained_dict: {}, update: {}'.format(len(model_dict), len(pretrained_dict), len(new_dict)))
            self.vmunet.load_state_dict(model_dict)
            
            # 找到没有加载的键(keys)
            not_loaded_keys = [k for k in pretrained_dict.keys() if k not in new_dict.keys()]
            print('Not loaded keys:', not_loaded_keys)
            print("decoder loaded finished!")

x = torch.randn(3, 3, 256, 256).to("cuda:0")
net = VMUNet(3,1).to("cuda:0")
print(net(x).shape)



# %%
sudo apt install python3-pip
# %%
