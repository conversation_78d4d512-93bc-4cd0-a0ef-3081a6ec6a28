#%% nnUnet使用教程,autodl云服务器已经跑成功
# 项目地址https://gitcode.com/gh_mirrors/3d/3D-TransUNet

#方法1：创建虚拟环境
from enum import auto
from filecmp import cmp
from hashlib import scrypt

from pydicom import Dataset
from sympy import imageset


conda create -n TransUNet python=3.10
# 如果用镜像则直接运行：
conda activate TransUNet
cd autodl-tmp
git clone https://gitcode.com/gh_mirrors/3d/3D-TransUNet.git
cd 3D-TransUNet



# 数据预处理的自定义设置见nnUNet，跟nnUnet一样
cd auto-tmp
git clone https://github.com/MIC-DKFZ/nnUNet.git
cd nnUNet

cd /root/autodl-tmp/3D-TransUNet 
pip install nnunet #安装老版本的 ，因为新版本不支持


# 自己在nnUNet文件夹下创建3个文件夹nnUNet_raw/nnUNet_preprocessed/nnUNet_results
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw
mkdir /root/autodl-tmp/nnUNet/nnUNet_preprocessed
mkdir /root/autodl-tmp/nnUNet/nnUNet_results


# 数据集放在nnUNet_raw下面imagesetimageset
# 如数据集Dataset001中存放的是训练集（imagesTr）、训练集标注(labelsTr)、测试集（imagesTs）、dataset.json文件
mkdir /root/auto-tmp/nnUNet/nnUNet_raw/Dataset320
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTr
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTs
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset/labelsTr  
#labelsTr中的文件名跟image的名字要完全一样比如 dingdaoping-ap.nii.gz 这种格式


# linux设置环境变量
export nnUNet_raw="/root/autodl-tmp/nnUNet/nnUNet_raw"
export nnUNet_preprocessed="/root/autodl-tmp/nnUNet/nnUNet_preprocessed"
export nnUNet_results="/root/autodl-tmp/nnUNet/nnUNet_results"


#%%3.终端运行,需要在nnUnet路径下训练 
#自己的3d数据集训练，需要在nnUnet路径下训练 ~/autodl-tmp/nnUNet#
nnUNetv2_plan_and_preprocess -d 320 --verify_dataset_integrity #数字598为数据集Dataset的编号 


#%% 复制image到imagesTr，mask到labelsTr
import os
import shutil

# 源文件夹路径
source_dir = '/root/autodl-tmp/HCC320/image/ap'

# 目标文件夹路径 (请修改为您想要的目标路径)
target_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTr'

# 创建目标文件夹(如果不存在)
if not os.path.exists(target_dir):
    os.makedirs(target_dir)
    print(f"已创建目标文件夹: {target_dir}")

# 复制文件
files_copied = 0
for filename in os.listdir(source_dir):
    source_file = os.path.join(source_dir, filename)
    target_file = os.path.join(target_dir, filename)
    
    # 确保是文件而不是子文件夹
    if os.path.isfile(source_file):
        shutil.copy2(source_file, target_file)
        files_copied += 1

print(f"复制完成! 共复制了 {files_copied} 个文件到 {target_dir}")


#%% imagesTr和imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
# 单序列数据格式如下
# nnUNet_raw/Dataset001/
# ├── dataset.json
# ├── imagesTr #_0000表示第一个序列，0001表示第2个序列，0002表示第3个序列
# │   ├── baohanqing-ap_0000.nii.gz
# │   ├── bianlianying-ap_0000.nii.gz
# │   ├── caomeifang-ap_0000.nii.gz
# │   ├── ...
# ├── imagesTs
# │   ├── caohan-ap_0000.nii.gz
# │   ├── zhagnwen-ap_0000.nii.gz
# │   ├── ...
# └── labelsTr
#     ├── baohanqing-ap.nii.gz
#     ├── bianlianying-ap.nii.gz
#     ├── caomeifang-ap.nii.gz
#     ├── ...

import os

target_dir = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTr"

# 遍历目标目录下的所有文件
for filename in os.listdir(target_dir):
    # 获取文件路径
    file_path = os.path.join(target_dir, filename)
    
    # 检查文件是否是.nii.gz格式
    if filename.endswith('.nii.gz'):
        name = filename[:-7]  # 去掉.nii.gz
        ext = '.nii.gz'
    else:
        name, ext = os.path.splitext(filename)
    
    # 新文件名
    new_filename = f"{name}_0000{ext}"
    print(new_filename)
    
    # 新文件路径
    new_file_path = os.path.join(target_dir, new_filename)
    
    # 重命名文件
    os.rename(file_path, new_file_path)

#%% labelsTr重命名，去掉-mask，名字跟imagesTr的名字一样，不包括_0000
# labelsTr名字不包括_0000，否则报错np.str_('zongjinzhuan-pp'), np.str_('zouyudong-pp')]
import os

# 目标目录
target_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/labelsTr'

str = "-mask"

# 遍历目标目录下的所有文件
for filename in os.listdir(target_dir):
    # 检查文件名中是否包含'-mask'
    if str in filename:
        # 获取文件路径
        file_path = os.path.join(target_dir, filename)
        
        # 新文件名
        new_filename = filename.replace(str, '')
        
        # 新文件路径
        new_file_path = os.path.join(target_dir, new_filename)
        
        # 重命名文件
        os.rename(file_path, new_file_path)
        print(f"Renamed: {file_path} -> {new_file_path}")

#%%替换指定路径下文件名中的 "vp" 字符为 "pp"
import os

# 指定路径
path = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr"

# 遍历路径下的所有文件
for filename in os.listdir(path):
    if "-t" in filename:
        # 构造新的文件名
        new_filename = filename.replace("-t", "-t1")
        
        # 完整的文件路径
        src = os.path.join(path, filename)
        dst = os.path.join(path, new_filename)
        
        # 检查目标文件是否存在
        if not os.path.exists(dst):
            try:
                os.rename(src, dst)
                print(f"重命名: {src} -> {dst}")
            except Exception as e:
                print(f"无法重命名 {src}，错误: {e}")
        else:
            print(f"目标文件已存在，跳过重命名: {dst}")
#%% 统计train文件夹内文件数量
import os

target_dir = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/labelsTr"
file_count = len([name for name in os.listdir(target_dir) if os.path.isfile(os.path.join(target_dir, name))])

print(f"文件数量: {file_count}")

#%% dataset.json内容输入，保存为json文件即可
{ 
    "channel_names": {
        "0": "ap"
        "1": "pp"
        "2": "hbp"
    }, 
    "labels": {
        "background": 0,
        "HCC": 1
    }, 
    "numTraining": 320, #训练集imagesTr的图像数
    "file_ending": ".nii.gz",
    "overwrite_image_reader_writer": "SimpleITKIO"
}

#%% 生成数据集的json文件代码 成功
import json

# 定义 JSON 数据的结构
data = {
    "channel_names": {
        "0": "ap",
        # "1": "pp",
#       "2": "hbp"
    },
    "labels": {
        "background": 0,
        "HCC": 1
    },
    "numTraining": 320,  # 替换为训练集 imagesTr 的图像数量
    "file_ending": ".nii.gz",
    "overwrite_image_reader_writer": "SimpleITKIO"
}

# 保存 JSON 文件的路径
output_file = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/dataset.json"  # 替换为你的保存路径

# 写入 JSON 文件
with open(output_file, 'w') as f:
    json.dump(data, f, indent=4)

print(f"JSON 文件已保存到: {output_file}")

#%%图像报错处理：使用重抽样，使得image和mask的Direction、origin和spacing一致
# pip install SimpleITK
import SimpleITK as sitk
import os

# 指定图像和标签路径
image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTr'
label_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/labelsTr'

# 获取所有 .nii.gz 文件并排序
nii_images = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])
nii_labels = sorted([filename for filename in os.listdir(label_dir) if filename.endswith('.nii.gz')])

# 遍历图像和标签
for img_filename, label_filename in zip(nii_images, nii_labels):
    # 构建完整路径
    image_path = os.path.join(image_dir, img_filename)
    label_path = os.path.join(label_dir, label_filename)
    
    try:
        # 检查文件大小是否为0
        if os.path.getsize(image_path) == 0 or os.path.getsize(label_path) == 0:
            print(f"警告: 文件 {image_path} 或 {label_path} 大小为0，跳过")
            continue
            
        # 读取图像和标签
        image = sitk.ReadImage(image_path)
        label = sitk.ReadImage(label_path)
        
        # 检查方向、原点和间距是否一致
        if (image.GetDirection() != label.GetDirection() or
            image.GetOrigin() != label.GetOrigin() or
            image.GetSpacing() != label.GetSpacing()):
            
            # 创建一个默认的 Transform（Identity Transform）
            transform = sitk.Transform()
            
            # 使用图像作为参考对标签进行重采样
            resampled_label = sitk.Resample(
                label,  # 要重采样的图像
                image,  # 参考图像
                transform,  # 使用单位变换
                sitk.sitkNearestNeighbor,  # 插值方式
                0,  # 默认像素值
                label.GetPixelID()  # 输出像素类型
            )
            
            # 保存重采样后的标签
            resampled_label_path = os.path.join(label_dir, label_filename)
            sitk.WriteImage(resampled_label, resampled_label_path)
            print(f"Resampled label saved for {label_filename}")

        # 打印图像和标签的形状
        print(f"Image shape for {img_filename}: {image.GetSize()}, Label shape for {label_filename}: {label.GetSize()}")
    
    except Exception as e:
        print(f"错误处理文件 {image_path} 和 {label_path}: {e}")
        continue


# 使用方法
# 安装
# 请参考scripts/install.sh进行安装。
bash scripts/install.sh
conda activate transunet 
pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu121

# 训练
#参见scripts/train.sh。
bash scripts/train.sh

# train.sh需要修改路径
export nnUNet_N_proc_DA=36
export nnUNet_codebase="/root/autodl-tmp/3D-TransUNet/nnUNet/" # replace to your codebase
export nnUNet_raw_data_base="/root/autodl-tmp/nnUNet/nnUNet_raw/" # replace to your database
export nnUNet_preprocessed="/root/autodl-tmp/nnUNet/nnUNet_preprocessed"
export RESULTS_FOLDER="/root/autodl-tmp/nnUNet/nnUNet_results/"


CONFIG=$1

echo $CONFIG

### unit test
fold=0
echo "run on fold: ${fold}"
nnunet_use_progress_bar=1 CUDA_VISIBLE_DEVICES=0 \
        python3 -m torch.distributed.launch --master_port=4322 --nproc_per_node=8 \
        /root/autodl-cmp/3D-TransUNet/train.py --fold=${fold} --config=$CONFIG --resume=''


# 推断与评估
# 参见scripts/inference.sh。


