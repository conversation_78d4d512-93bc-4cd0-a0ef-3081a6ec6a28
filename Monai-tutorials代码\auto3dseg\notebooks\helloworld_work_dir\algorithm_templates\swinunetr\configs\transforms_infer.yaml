image_key: image
transforms_infer:
  _target_: Compose
  transforms:
  - _target_: LoadImaged
    keys: "@image_key"
    image_only: True
  - _target_: EnsureChannelFirstd
    keys: "@image_key"
  - PLACEHOLDER_INTENSITY_NORMALIZATION
  - _target_: Orientationd
    keys: "@image_key"
    axcodes: RAS
  - _target_: Spacingd
    keys: "@image_key"
    pixdim: "$@transforms#resample_resolution"
    mode: bilinear
    align_corners: true
  - _target_: CastToTyped
    keys: "@image_key"
    dtype: "$torch.float32"
