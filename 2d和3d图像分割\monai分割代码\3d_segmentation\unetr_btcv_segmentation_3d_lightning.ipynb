{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# 3D Multi-organ Segmentation with UNETR  (BTCV Challenge)\n", "# PyTorch Lightning Tutorial\n", "\n", "\n", "This tutorial demonstrates how MONAI can be used in conjunction with PyTorch Lightning framework to construct a training workflow of UNETR on multi-organ segmentation task using the BTCV challenge dataset.\n", "\n", "![image](../figures/btcv_transformer.png)\n", "\n", "And it contains the following features:\n", "1. Transforms for dictionary format data.\n", "2. Define a new transform according to MONAI transform API.\n", "3. Load Nifti image with metadata, load a list of images and stack them.\n", "4. Randomly adjust intensity for data augmentation.\n", "5. <PERSON><PERSON> and transforms to accelerate training and validation.\n", "6. 3D UNETR model, Dice loss function, Mean Dice metric for multi-oorgan segmentation task.\n", "\n", "The dataset comes from https://www.synapse.org/#!Synapse:syn3193805/wiki/217752.  \n", "\n", "Under Institutional Review Board (IRB) supervision, 50 abdomen CT scans of were randomly selected from a combination of an ongoing colorectal cancer chemotherapy trial, and a retrospective ventral hernia study. The 50 scans were captured during portal venous contrast phase with variable volume sizes (512 x 512 x 85 - 512 x 512 x 198) and field of views (approx. 280 x 280 x 280 mm3 - 500 x 500 x 650 mm3). The in-plane resolution varies from 0.54 x 0.54 mm2 to 0.98 x 0.98 mm2, while the slice thickness ranges from 2.5 mm to 5.0 mm. \n", "\n", "Target: 13 abdominal organs including 1. <PERSON><PERSON><PERSON> 2. <PERSON> <PERSON><PERSON> 3. <PERSON> <PERSON><PERSON> 4.<PERSON><PERSON><PERSON><PERSON> 5.Esopha<PERSON> 6. <PERSON><PERSON> 7. <PERSON><PERSON><PERSON> 8.<PERSON>ort<PERSON> 9. IVC 10. <PERSON> and <PERSON><PERSON><PERSON> Veins 11. <PERSON>cre<PERSON> 12 Right adrenal gland 13 Left adrenal gland.\n", "\n", "Modality: CT\n", "Size: 30 3D volumes (24 Training + 6 Testing)  \n", "Challenge: BTCV MICCAI Challenge\n", "\n", "The following figure shows image patches with the organ sub-regions that are annotated in the CT (top left) and the final labels for the whole dataset (right).\n", "\n", "Data, figures and resources are taken from: \n", "\n", "\n", "1. [UNETR: Transformers for 3D Medical Image Segmentation](https://arxiv.org/abs/2103.10504)\n", "\n", "2. [High-resolution 3D abdominal segmentation with random patch network fusion (MIA)](https://www.sciencedirect.com/science/article/abs/pii/S1361841520302589)\n", "\n", "3. [Efficient multi-atlas abdominal segmentation on clinically acquired CT with SIMPLE context learning (MIA)](https://www.sciencedirect.com/science/article/abs/pii/S1361841515000766?via%3Dihub)\n", "\n", "\n", "![image](../figures/BTCV_organs.png)\n", "\n", "\n", "\n", "The image patches show anatomies of a subject, including: \n", "1. large organs: spleen, liver, stomach. \n", "2. Smaller organs: gallbladder, esophagus, kidneys, pancreas. \n", "3. Vascular tissues: aorta, IVC, P&S Veins. \n", "4. G<PERSON>: left and right adrenal gland\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/unetr_btcv_segmentation_3d_lightning.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, einops]\"\n", "!pip install -q pytorch-lightning~=2.0\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 0.9.1\n", "Numpy version: 1.22.4\n", "Pytorch version: 1.13.0a0+340c412\n", "MONAI flags: HAS_EXT = True, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 356d2d2f41b473f588899d705bbc682308cee52c\n", "MONAI __file__: /opt/monai/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.9\n", "Nibabel version: 4.0.1\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.0.1\n", "Tensorboard version: 2.9.1\n", "gdown version: 4.5.1\n", "TorchVision version: 0.13.0a0\n", "tqdm version: 4.64.0\n", "lmdb version: 1.3.0\n", "psutil version: 5.9.1\n", "pandas version: 1.3.5\n", "einops version: 0.4.1\n", "transformers version: 4.20.1\n", "mlflow version: 1.27.0\n", "pynrrd version: 0.4.3\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "from monai.losses import DiceCELoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    EnsureChannelFirstd,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    RandCropByPosNegLabeld,\n", "    RandShiftIntensityd,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", "    RandRotate90d,\n", ")\n", "\n", "from monai.config import print_config\n", "from monai.metrics import DiceMetric\n", "from monai.networks.nets import UNETR\n", "\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    load_decathlon_datalist,\n", "    decollate_batch,\n", "    list_data_collate,\n", ")\n", "\n", "import torch\n", "import pytorch_lightning\n", "from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint\n", "\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "torch.backends.cudnn.benchmark = True\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpmqlv_tg7\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download dataset and format in the folder.\n", "    1. Download dataset from here: https://www.synapse.org/#!Synapse:syn3193805/wiki/89480\\n\n", "    2. Put images in the ./data/imagesTr\n", "    3. Put labels in the ./data/labelsTr\n", "    4. make JSON file accordingly: ./data/dataset_0.json\n", "    Example of JSON file:\n", "     {\n", "    \"description\": \"btcv yucheng\",\n", "    \"labels\": {\n", "        \"0\": \"background\",\n", "        \"1\": \"spleen\",\n", "        \"2\": \"rkid\",\n", "        \"3\": \"lkid\",\n", "        \"4\": \"gall\",\n", "        \"5\": \"eso\",\n", "        \"6\": \"liver\",\n", "        \"7\": \"sto\",\n", "        \"8\": \"aorta\",\n", "        \"9\": \"IVC\",\n", "        \"10\": \"veins\",\n", "        \"11\": \"pancreas\",\n", "        \"12\": \"rad\",\n", "        \"13\": \"lad\"\n", "    },\n", "    \"licence\": \"yt\",\n", "    \"modality\": {\n", "        \"0\": \"CT\"\n", "    },\n", "    \"name\": \"btcv\",\n", "    \"numTest\": 20,\n", "    \"numTraining\": 80,\n", "    \"reference\": \"Vanderbilt University\",\n", "    \"release\": \"1.0 06/08/2015\",\n", "    \"tensorImageSize\": \"3D\",\n", "    \"test\": [\n", "        \"imagesTs/img0061.nii.gz\",\n", "        \"imagesTs/img0062.nii.gz\",\n", "        \"imagesTs/img0063.nii.gz\",\n", "        \"imagesTs/img0064.nii.gz\",\n", "        \"imagesTs/img0065.nii.gz\",\n", "        \"imagesTs/img0066.nii.gz\",\n", "        \"imagesTs/img0067.nii.gz\",\n", "        \"imagesTs/img0068.nii.gz\",\n", "        \"imagesTs/img0069.nii.gz\",\n", "        \"imagesTs/img0070.nii.gz\",\n", "        \"imagesTs/img0071.nii.gz\",\n", "        \"imagesTs/img0072.nii.gz\",\n", "        \"imagesTs/img0073.nii.gz\",\n", "        \"imagesTs/img0074.nii.gz\",\n", "        \"imagesTs/img0075.nii.gz\",\n", "        \"imagesTs/img0076.nii.gz\",\n", "        \"imagesTs/img0077.nii.gz\",\n", "        \"imagesTs/img0078.nii.gz\",\n", "        \"imagesTs/img0079.nii.gz\",\n", "        \"imagesTs/img0080.nii.gz\"\n", "    ],\n", "    \"training\": [\n", "        {\n", "            \"image\": \"imagesTr/img0001.nii.gz\",\n", "            \"label\": \"labelsTr/label0001.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0002.nii.gz\",\n", "            \"label\": \"labelsTr/label0002.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0003.nii.gz\",\n", "            \"label\": \"labelsTr/label0003.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0004.nii.gz\",\n", "            \"label\": \"labelsTr/label0004.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0005.nii.gz\",\n", "            \"label\": \"labelsTr/label0005.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0006.nii.gz\",\n", "            \"label\": \"labelsTr/label0006.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0007.nii.gz\",\n", "            \"label\": \"labelsTr/label0007.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0008.nii.gz\",\n", "            \"label\": \"labelsTr/label0008.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0009.nii.gz\",\n", "            \"label\": \"labelsTr/label0009.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0010.nii.gz\",\n", "            \"label\": \"labelsTr/label0010.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0021.nii.gz\",\n", "            \"label\": \"labelsTr/label0021.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0022.nii.gz\",\n", "            \"label\": \"labelsTr/label0022.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0023.nii.gz\",\n", "            \"label\": \"labelsTr/label0023.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0024.nii.gz\",\n", "            \"label\": \"labelsTr/label0024.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0025.nii.gz\",\n", "            \"label\": \"labelsTr/label0025.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0026.nii.gz\",\n", "            \"label\": \"labelsTr/label0026.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0027.nii.gz\",\n", "            \"label\": \"labelsTr/label0027.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0028.nii.gz\",\n", "            \"label\": \"labelsTr/label0028.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0029.nii.gz\",\n", "            \"label\": \"labelsTr/label0029.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0030.nii.gz\",\n", "            \"label\": \"labelsTr/label0030.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0031.nii.gz\",\n", "            \"label\": \"labelsTr/label0031.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0032.nii.gz\",\n", "            \"label\": \"labelsTr/label0032.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0033.nii.gz\",\n", "            \"label\": \"labelsTr/label0033.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0034.nii.gz\",\n", "            \"label\": \"labelsTr/label0034.nii.gz\"\n", "        }\n", "    ],\n", "    \"validation\": [\n", "        {\n", "            \"image\": \"imagesTr/img0035.nii.gz\",\n", "            \"label\": \"labelsTr/label0035.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0036.nii.gz\",\n", "            \"label\": \"labelsTr/label0036.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0037.nii.gz\",\n", "            \"label\": \"labelsTr/label0037.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0038.nii.gz\",\n", "            \"label\": \"labelsTr/label0038.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0039.nii.gz\",\n", "            \"label\": \"labelsTr/label0039.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0040.nii.gz\",\n", "            \"label\": \"labelsTr/label0040.nii.gz\"\n", "        }\n", "    ]\n", "}\n", " "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the LightningModule (transform, network)\n", "The LightningModule contains a refactoring of your training code. The following module is a refactoring of the code in spleen_segmentation_3d.ipynb:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class Net(pytorch_lightning.LightningModule):\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "        self._model = UNETR(\n", "            in_channels=1,\n", "            out_channels=14,\n", "            img_size=(96, 96, 96),\n", "            feature_size=16,\n", "            hidden_size=768,\n", "            mlp_dim=3072,\n", "            num_heads=12,\n", "            pos_embed=\"perceptron\",\n", "            norm_name=\"instance\",\n", "            res_block=True,\n", "            conv_block=True,\n", "            dropout_rate=0.0,\n", "        ).to(device)\n", "\n", "        self.loss_function = DiceCELoss(to_onehot_y=True, softmax=True)\n", "        self.post_pred = AsDiscrete(argmax=True, to_onehot=14)\n", "        self.post_label = AsDiscrete(to_onehot=14)\n", "        self.dice_metric = DiceMetric(include_background=False, reduction=\"mean\", get_not_nans=False)\n", "        self.best_val_dice = 0\n", "        self.best_val_epoch = 0\n", "        self.max_epochs = 1300\n", "        self.check_val = 30\n", "        self.warmup_epochs = 20\n", "        self.metric_values = []\n", "        self.epoch_loss_values = []\n", "        self.validation_step_outputs = []\n", "\n", "    def forward(self, x):\n", "        return self._model(x)\n", "\n", "    def prepare_data(self):\n", "        # prepare data\n", "        data_dir = \"/dataset/dataset0/\"\n", "        split_json = \"dataset_0.json\"\n", "        datasets = data_dir + split_json\n", "        datalist = load_decathlon_datalist(datasets, True, \"training\")\n", "        val_files = load_decathlon_datalist(datasets, True, \"validation\")\n", "\n", "        train_transforms = Compose(\n", "            [\n", "                LoadImaged(keys=[\"image\", \"label\"]),\n", "                EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "                ScaleIntensityRanged(\n", "                    keys=[\"image\"],\n", "                    a_min=-175,\n", "                    a_max=250,\n", "                    b_min=0.0,\n", "                    b_max=1.0,\n", "                    clip=True,\n", "                ),\n", "                CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "                Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "                Spacingd(\n", "                    keys=[\"image\", \"label\"],\n", "                    pixdim=(1.5, 1.5, 2.0),\n", "                    mode=(\"bilinear\", \"nearest\"),\n", "                ),\n", "                RandCropByPosNegLabeld(\n", "                    keys=[\"image\", \"label\"],\n", "                    label_key=\"label\",\n", "                    spatial_size=(96, 96, 96),\n", "                    pos=1,\n", "                    neg=1,\n", "                    num_samples=4,\n", "                    image_key=\"image\",\n", "                    image_threshold=0,\n", "                ),\n", "                RandFlipd(\n", "                    keys=[\"image\", \"label\"],\n", "                    spatial_axis=[0],\n", "                    prob=0.10,\n", "                ),\n", "                RandFlipd(\n", "                    keys=[\"image\", \"label\"],\n", "                    spatial_axis=[1],\n", "                    prob=0.10,\n", "                ),\n", "                RandFlipd(\n", "                    keys=[\"image\", \"label\"],\n", "                    spatial_axis=[2],\n", "                    prob=0.10,\n", "                ),\n", "                RandRotate90d(\n", "                    keys=[\"image\", \"label\"],\n", "                    prob=0.10,\n", "                    max_k=3,\n", "                ),\n", "                RandShiftIntensityd(\n", "                    keys=[\"image\"],\n", "                    offsets=0.10,\n", "                    prob=0.50,\n", "                ),\n", "            ]\n", "        )\n", "        val_transforms = Compose(\n", "            [\n", "                LoadImaged(keys=[\"image\", \"label\"]),\n", "                EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "                ScaleIntensityRanged(\n", "                    keys=[\"image\"],\n", "                    a_min=-175,\n", "                    a_max=250,\n", "                    b_min=0.0,\n", "                    b_max=1.0,\n", "                    clip=True,\n", "                ),\n", "                CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "                Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "                Spacingd(\n", "                    keys=[\"image\", \"label\"],\n", "                    pixdim=(1.5, 1.5, 2.0),\n", "                    mode=(\"bilinear\", \"nearest\"),\n", "                ),\n", "            ]\n", "        )\n", "\n", "        self.train_ds = CacheDataset(\n", "            data=datalist,\n", "            transform=train_transforms,\n", "            cache_num=24,\n", "            cache_rate=1.0,\n", "            num_workers=8,\n", "        )\n", "        self.val_ds = CacheDataset(\n", "            data=val_files,\n", "            transform=val_transforms,\n", "            cache_num=6,\n", "            cache_rate=1.0,\n", "            num_workers=8,\n", "        )\n", "\n", "    def train_dataloader(self):\n", "        train_loader = DataLoader(\n", "            self.train_ds,\n", "            batch_size=1,\n", "            shuffle=True,\n", "            num_workers=8,\n", "            pin_memory=True,\n", "            collate_fn=list_data_collate,\n", "        )\n", "        return train_loader\n", "\n", "    def val_dataloader(self):\n", "        val_loader = DataLoader(self.val_ds, batch_size=1, shuffle=False, num_workers=4, pin_memory=True)\n", "        return val_loader\n", "\n", "    def configure_optimizers(self):\n", "        optimizer = torch.optim.AdamW(self._model.parameters(), lr=1e-4, weight_decay=1e-5)\n", "        return optimizer\n", "\n", "    def training_step(self, batch, batch_idx):\n", "        images, labels = (batch[\"image\"].cuda(), batch[\"label\"].cuda())\n", "        output = self.forward(images)\n", "        loss = self.loss_function(output, labels)\n", "        tensorboard_logs = {\"train_loss\": loss.item()}\n", "        return {\"loss\": loss, \"log\": tensorboard_logs}\n", "\n", "    def training_epoch_end(self, outputs):\n", "        avg_loss = torch.stack([x[\"loss\"] for x in outputs]).mean()\n", "        self.epoch_loss_values.append(avg_loss.detach().cpu().numpy())\n", "\n", "    def validation_step(self, batch, batch_idx):\n", "        images, labels = batch[\"image\"], batch[\"label\"]\n", "        roi_size = (96, 96, 96)\n", "        sw_batch_size = 4\n", "        outputs = sliding_window_inference(images, roi_size, sw_batch_size, self.forward)\n", "        loss = self.loss_function(outputs, labels)\n", "        outputs = [self.post_pred(i) for i in decollate_batch(outputs)]\n", "        labels = [self.post_label(i) for i in decollate_batch(labels)]\n", "        self.dice_metric(y_pred=outputs, y=labels)\n", "        d = {\"val_loss\": loss, \"val_number\": len(outputs)}\n", "        self.validation_step_outputs.append(d)\n", "        return d\n", "\n", "    def on_validation_epoch_end(self):\n", "        val_loss, num_items = 0, 0\n", "        for output in self.validation_step_outputs:\n", "            val_loss += output[\"val_loss\"].sum().item()\n", "            num_items += output[\"val_number\"]\n", "        mean_val_dice = self.dice_metric.aggregate().item()\n", "        self.dice_metric.reset()\n", "        mean_val_loss = torch.tensor(val_loss / num_items)\n", "        tensorboard_logs = {\n", "            \"val_dice\": mean_val_dice,\n", "            \"val_loss\": mean_val_loss,\n", "        }\n", "        if mean_val_dice > self.best_val_dice:\n", "            self.best_val_dice = mean_val_dice\n", "            self.best_val_epoch = self.current_epoch\n", "        print(\n", "            f\"current epoch: {self.current_epoch} \"\n", "            f\"current mean dice: {mean_val_dice:.4f}\"\n", "            f\"\\nbest mean dice: {self.best_val_dice:.4f} \"\n", "            f\"at epoch: {self.best_val_epoch}\"\n", "        )\n", "        self.metric_values.append(mean_val_dice)\n", "        self.validation_step_outputs.clear()  # free memory\n", "        return {\"log\": tensorboard_logs}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# initialise the LightningModule\n", "net = Net()\n", "\n", "# set up checkpoints\n", "checkpoint_callback = ModelCheckpoint(dirpath=root_dir, filename=\"best_metric_model\")\n", "\n", "# initialise Lightning's trainer.\n", "trainer = pytorch_lightning.Trainer(\n", "    devices=[0],\n", "    max_epochs=net.max_epochs,\n", "    check_val_every_n_epoch=net.check_val,\n", "    callbacks=checkpoint_callback,\n", "    default_root_dir=root_dir,\n", ")\n", "\n", "# train\n", "trainer.fit(net)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot the loss and metric"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsYAAAGDCAYAAAAoFdb3AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzs3Xl8XFX9//HXZzLZ2qbpknRfoXtZCpQCsshOAQVcQFAQ/aK4gLiLKyLfn4p8EUXBBQUFFbCCQtWylq1lbWlLoXtL22xtmjZbs2dmzu+PuSnTdJJMkplMZvJ+Ph7zYObOmXs/k5Q775w59xxzziEiIiIiMtD5kl2AiIiIiEh/oGAsIiIiIoKCsYiIiIgIoGAsIiIiIgIoGIuIiIiIAArGIiIiIiKAgrEkgJnVmdlhya5DRER6x8ymmJkzM3+ya4knfU5JRxSM04yZ7TCzs737nzKz5Qk+3gtm9pnIbc65Ic65dxN8zCozy07UMfpStJ+hiEg8mNlTZnZLlO0Xm9nu3gZe7zOnxcwK2m1f4wXqKb3Zfw/qOd3MQl7wrTOzEjNbZGbHR7ZL9OeUpC4FY+lQf+wh8E6ypwIOuChBx+h371tEpIf+DFxlZtZu+1XA35xzgTgcYztwRdsDMzsSyI3DfnuqzDk3BMgDTgQ2AsvM7Kwk1iQpQsE4TZnZbOB3wEneX83V3vZsM7vdzIrMrNzMfmdmud5zp3t/Xd9oZruBP5nZcDP7j5lVeL20/zGzCV77HxMOqXd5x7jL2+7MbJp3P9/MHvBev9PMvm9mPu+5T5nZcq+eKjPbbmbnd/HWPgm8Rvhkf3XE+z3R6/3IiNj2ITNb6933mdm3zWybme3zehBGeM+1fVV4jZkVAc952//h7bPGzF4ys7kR+x5pZv82s1ozW2Fm/y+yd97MZpnZM2ZWaWabzOyybv8Sw/u5yMzWmVm117M8O+K5G82s1Mz2e8c4y9u+wMxWerWVm9kdPTm2iKSFx4ARhM/VAJjZcOADwAPe4wvNbLV3zig2s5u7eYy/ED43t7m6bd8Rx+zss6fDzxnv+RfM7H/N7GXvfPd0+x7qaFxYiXPuJuCPwM8i9hn5OZVrZj/3PqNqvM+lttpONLNXvHPwW2Z2ejd/NpJiFIzTlHNuA/B54FXvK6Nh3lM/A2YA84BpwHjgpoiXjiF8Ep0MXEv438ifvMeTgEbgLu8Y3wOWAdd7x7g+Sim/BvKBw4D3Ez55fjri+ROATUABcBtwb5SejUifBP7m3c4zs9FeLa8B9cCZEW0/Djzo3b8BuMSrYRxQBdzdbt/vB2YD53mPnwCmA6OAVd4x29ztHW8M4Q+ByJA+GHjGO/Yowj0pv4kM1rEwsxnAQ8BXgEJgCfBvM8sys5nA9cDxzrk8r+Yd3kvvBO50zg0FDgcWdee4IpI+nHONhM8BkcH1MmCjc+4t73G99/ww4ELgC2Z2STcO8xow1Mxme50THwP+2q5NZ589HX7ORPg44c+OUUAW8I1u1AfwT+BY7/zc3u3AccD7CH/+fQsImdl44L/A//O2fwN41MwKu3lsSSXOOd3S6EY4HJ3t3f8UsDziOSN8Ajw8YttJwHbv/ulAC5DTyf7nAVURj18APtOujSN84ssAmoE5Ec99Dnghor6tEc8N8l47poNjnwK0AgXe443AVyOe/3/Afd79PO+9TvYebwDOimg71tuXH5jiHfewTt73MK9Nvve+WoGZ7Y693Lv/MWBZu9f/HvhhB/s+5Gfobf8BsCjisQ8o9X5P04A9wNlAZrvXvQT8qO3npJtuug3sm3furAFyvccvR547o7T/JfAL737b+dHfQdsd3nno+8BPgYWEOwb83uumdPXZE2Wf0T5nvh/x+IvAkx289nSgJMr2WV49473HbZ9TPsJB/Ogor7kR+Eu7bU8BVyf7d6pb4m7qMR5YCgmHzze9r4WqgSe97W0qnHNNbQ/MbJCZ/d77iqmWcOgaFjlkoRMFhP+y3xmxbSfhnoI2u9vuOOcavLtDOtjf1cDTzrm93uMHieip9R5/2MIX5X0YWOWcazv2ZOBfEe97AxAERke8vrjtjpllmNmt3tCLWt7rjS0g/PPyR7Zvd38ycELbsbzjfYJw73J3jCPiZ+ecC3nHGe+c20q4J/lmYI+ZPWxm47ym1xDumdnoDfP4QDePKyJpxDm3HKgALrbwTAzH8963aZjZCWb2vDeUoYbwt41dDlVo5y+Ee3U/RbthFHTx2RPj58zuiPsNdPw50ZHxhMNwdbvtBUAOsC3KayYDl7Y7l59CuGNF0pSCcXpz7R7vJfyX8Vzn3DDvlu/CFyl09JqvAzOBE1z4q/nTvO3WQfv2x2slfHJpM4lwr2e3eOO9LgPe74373Q18FTjazI4GcM6tJxwkz+fgYRQQDpTnR7zvYc65HOdcZC2R7+XjwMWEe0LyCfd6QPh9VwABYEJE+4ntjvViu2MNcc59oZtvu4yIn503xGQi3s/POfegc+4Ur43DGz/nnNvinLuC8FeOPwMe6eDrQxEZOB4gPFziKsIdDOURzz0ILAYmOufyCV+f0tmQtkN4nRDbgQsID1uI1NVnT1efM/HwIcKdJfVRamsiPOysvWLCPcaR5/LBzrlb41iX9DMKxumtHJhgZllwoMfxD8AvzGwUgJmNN7PzOtlHHuETWrWFL1b7YZRjRJ0L0jkXJDy27cdmlmdmk4GvcejYs1hcQriHdw7hr9nmER4PvIyDx849SHg88WnAPyK2/86rYzKAmRWa2cWdHC+P8DCQfYR7On7S7n39E7jZ6+mY1a6G/wAzzOwqM8v0bsdHXjgXhd/MciJumYR/dhea2Vne4697Nb1iZjPN7Eyvd7yJ8O8o6L23K82s0Pt9t/WOBDs5toikvwcI/6H/WeD+ds/lAZXOuSYzW0C4Y6AnrgHObB8+Y/js6epzpkcsbLyZ/RD4DPDd9m282u4D7jCzcd63hSd559a/Ah80s/O87TkWvkh9Qvv9SPpQME5vzwHrgN1m1jb84EZgK/Ca95XVs4T/Uu/ILwlPu7OX8AUWT7Z7/k7goxa+kvhXUV7/JcJjy94FlhMOrvf14L1cDfzJOVfknNvddiN8gcYn7L0p1h4iPMbsuYghF211LgaeNrP93ns5oZPjPUC497kUWO+1j3Q94Z7k3YS/QnyIcGjFObcfOBe4nHCv727CPbedzbv8W8IfDG23PznnNgFXEr6AcS/wQeCDzrkWb1+3ett3E+4dbjvpLwTWmVmd974vjxweIyIDj3NuB/AKMJjwuTDSF4FbvHPjTfTwgl3n3Dbn3MoOnu7ss6erz5nuGued/+qAFcCRwOnOuac7aP8N4G2vbSXh87XPOVdM+JvD7xL+prAY+CbKTmnNnOvsm3ARiYWZ/YzwRYNXd9lYRERE+iX91SPSAxaep/go76u6BYS/QvxXsusSERGRntMKXyI9k0d4+MQ4wtOm/Rx4PKkViYiISK9oKIWIiIiICBpKISIiIiICKBiLiIiIiABJHGNcUFDgpkyZkqzDi4j0yptvvrnXOVfYdcv0oHO2iKSyWM/ZSQvGU6ZMYeXKjqY7FBHp38xsZ9et0ofO2SKSymI9Z2sohYiIiIgICsYiIiIiIoCCsYiIiIgIoGAsIiIiIgLEEIzNLMfM3jCzt8xsnZn9KEqbT5lZhZmt8W6fSUy5IiIiIiKJEcusFM3Amc65OjPLBJab2RPOudfatfu7c+76+JcoIiIiIpJ4XQZjF14zus57mOndtI60iIiIiKSVmMYYm1mGma0B9gDPOOdej9LsI2a21sweMbOJHeznWjNbaWYrKyoqelG2iIh0xMwWmtkmM9tqZt+O8vwvIoa+bTaz6mTUKSLS38QUjJ1zQefcPGACsMDMjmjX5N/AFOfcUcCzwP0d7Oce59x859z8wsIBs2CUiEifMbMM4G7gfGAOcIWZzYls45z7qnNunnde/zXwz76vVESk/+nWrBTOuWrgBWBhu+37nHPN3sM/AMfFpToREemuBcBW59y7zrkW4GHg4k7aXwE81CeViYj0c7HMSlFoZsO8+7nA2cDGdm3GRjy8CNgQzyJFRCRm44HiiMcl3rZDmNlkYCrwXB/UJSLS78UyK8VY4H7v6zkfsMg59x8zuwVY6ZxbDNxgZhcBAaAS+FQiil1bUk1rMMRxk0ckYvciIunAomzr6ILpy4FHnHPBqDsyuxa4FmDSpEnxqU7S0r66ZkYMzsIs2j8/6Y59dc1UN7ZyeOGQZJcyIMUyK8Va4Jgo22+KuP8d4DvxLe1Qtz+9mZrGVh6/7uREH0pEJFWVAJEXQE8AyjpoezlwXUc7cs7dA9wDMH/+fM1GJFGt3FHJpb9/lWMmDuMrZ8/g1OkFCsg9UFrdyB9eepeHVxQRDDl+dfkxnH/k2K5fmGQtgRD/fbuMsuomrjppMkNzMhN2rLUl1YwemsPooTkJO0YsPcb9ht9nhEI6N4uIdGIFMN3MpgKlhMPvx9s3MrOZwHDg1b4tT9LN/a/uZHCWn901TXzyvjc4bvJwvnL2dE6Z1r8DcijkaAoEaWoN0dQa9G4hGluDjMrLZuKIQX1Sx7aKOn73wjb+tboUgEuOGc+7FXVc/9Bqfh4IcckxUUdCJV1NYysPvVHEn1/ewe7aJgDuf2UHP/zgXC44ckzcf/d1zQG+8NdVjB6azaNfeF/C/m2lVDD2mRFQMBYR6ZBzLmBm1wNPARnAfc65de2Gv0H4oruHvbnqRXpkb10zT76ziytPnMy3z5/FopUl/Ob5rVx17xvMnzycr5w9g5Onjex3AfnxNaV871/vUNcc6LDN5JGDOHV6AadOL+Skw0fGrSc0EAyxt66FHfvqeeDVHTzxzm6y/T6uPHEynz3tMMYPy6W+OcA196/gq4vW0BwI8rHj+89QpuLKBu57eTuLVhRT3xLk5Gkj+elHjmT4oCy+96+3ue7BVZwxs5BbLj6iwz8uQiHHurJacrN8TBuVF9Nxf7JkA7tqGvnVFcck9N9TSgVj9RiLiHTNObcEWNJu203tHt/clzVJ6mhoCTAoK7Z48MibJbQGHZ84YRLZ/gyuOnEyl82fwKIVxdz9/DauvPd1Zo3J44Ijx7LwiDFMHzUkIaFmbUk1k0cMJn9Q5+E1GHLc9tRGfv/iu8yfPJyzZo8mN9NHTmZGxM3Hzn0NLNtSwb9WlfLX14rI8BnzJg7j5MNHcljhECaOyGXi8EEUDMnG5zv4/TS2BCmqbGDHvnp27qunuLKR3bVNlNc2sbumib11zbRFmbwcP188/XA+ffJUCoZkH9jH4Gw/f/rUAj731ze58dG3aQ6E+ORJU6K+p5376nl4RTH76po5asIwjpk0jJmj8/BndGvisaiaA0G2761nc3kdW8r3s66slhc27cFnxgePHsdnTp3K3HH5B9o/ft3J/PmVHdzxzGbO+cWLfPXsGfzPKVPJzPBRXNnA8q17Wb51L69s3UtVQyuDsjJ45PPvY864oZ3WsXzLXh58vYhrTzuM4yYP7/X76owlq7Ng/vz5buXKld16zXV/W8XG3bUs/frpiSlKRCRGZvamc25+suvoKz05Z0vq2bR7P5fc/TLfPG8m/3PK1E7bhkKO029/gTH5OSz63EmHPN8cCPKPlSU8trqUN4uqcA4OKxjMeUeM4by5Yzh6Qj5mRlNrkOqGVqobW6huaGV/U4AFU0Z0GXLbvLi5gqvve4P83Ey+dOY0rjppMtn+jEPa1TS2csNDq3lxcwVXnjiJmz4wlyx/5+GxJRBidVEVy7bsZdnWvawtqSYyNmX7fYwfHg7Jja1Bdu6rp7y2+aB95OX4GZsfHhc7ZmgOYyLuLzhsRKc90c2BINc/uJpn1pfz3Qtmce1phwPhXudnN+zhb6/vZNmWvWT4jPzcTCrrWwDIzczgyPH5zJs0jHkTh3HEuHwmjsjt9I+S1mCId0prWLGjktVF1Wwq38/OfQ0EvRSf4TMmjxzEObNH86mTpzA2P7fDfZVWN3Lz4nU8s76cwwsHEww5duxrAGD00GxOmVbICYeN4I6nN5PhMx677mQK87Kj7quuOcB5v3iJ7EwfS244lZzMQ3+3sYj1nJ1SwfiGh1bzdmkNz3/j9MQUJSISIwVjSTfOOT7xx9d5Zds+hub4eelbZzBsUFaH7ZdtqeCqe9/gzsvncfG8zsfB7qlt4un15Ty1bjevbttHIOTIy/HTEgjRHAgd0n7m6Dz++cX3MTi7857rfXXNLLxzGfm5mYzNz2HZlr1MGjGIby2cyYVHjj0QBLfuqePaB1ZSVNnAjy6eyydOmBzDT+RQTa1BSqoaKK5qpKQy/N/iygZKqhrJyfQxeeRgJo8YxOSCwUwZOSimXuyutAZDfOXva/jv2l188fTD8Wf4+PuKIsprmxmbn8MVCybxseMnMiovm5KqRlYVVbGmuJrVRdWsL6ulJRj++eZl+5k9bihzxw1lztihzBk3lJrGVlZsr+KNHftYtbOaxtbwBDWTRgxi1pg8ZozOY/roIcwYncdhhYOj/sHRmafW7eZXS7cwemgOp0wr4NTpBUyL+Nbg7ZIaLv39K8wdl8+Dnz0h6v6/+6+3eeiNIh75/Pt61Vsc6zk7pYZSZPiMQOjQ/4FERESkd55aV84r2/Zx5YmT+NvrRfzmhW1894LZHbb/22tFjBicxcIjxnS571FDc7jyxMlceeJkqhtaWLphD28WVTEk28+wQZkMy83y/ptJ+f4mvr7oLb716Fru6mQ8qXOOGx99m5qGVu7/9ALmjBvKi5sr+OmSDVz/4GrunbSd710wm9qmVr780Bqy/D4e/OyJLJja8ylfczIzmDYqL+ZxsfGQmeHjV5cfQ7bfx29e2IYZvH9GIT++ZDKnzyw8aMjExBGDmDhi0IE/VJoDQTbs2s/6slrW76phXVktD79RfCAAA5jBrDFD+djxEzl+ygiOnzqcUXnxmfXhvLnhbwc6cuSEfH5+6Tyue3AV3/3nO9x+6VEH/b7bhlB89tSpCR9C0SblgrFysYiISHw1tQb58ZL1zBg9hJs/OJem1hB/fmUHV79vCuOHHfqVeXltE89sKOczp0ztdi/isEFZfOS4CXzkuAkdtimvbebWJzZy1Ph8Pvf+w6O2efCNIp7dUM73L5x9YIzq+2cUcsq0Ah59s4SfP7OJj/4uPOnK3HFDueeT86O+l1SQ4TNu/+jRnD17NEeOz495xoxsfwbzJoaHU7QJhhzb99azYVctg7MzOG7yCPJzEzfFWlcuPGosm8unc+fSLcwcM+TAcJG65gA3PrqWwwoG8/VzZ/ZZPSkVjP3qMRYREYm7e5dvp7iykb995gT8GT6+ds4MFr9Vxs+f3sQdl807pP2iFcUEQ44rFiRmtoTPnXYYb5fU8LMnNzJn3FBOnV540PNb99Txv/9Zz6nTC/ifkw8eC53hMy47fiIfOHos9y3fzt66Fm5cOIvcrJ6NTe0vfD7jgjjMa5zhM6aNGsK0Uf1nAZEvnzWdrXvq+OkTGzm8cAhnzR7NT5dsoKymkUc+f1KPxxX3RO8vWexDPp8dGAQuIiIivbe7pom7n9/KeXNHc/K0AgDGDcvl0++bwr9Wl7JhV+1B7YMhx8MrijllWgFTCgYnpCYz47aPHsX0UXl86aHVFFc2HHiuJRDiK39fTW5mBrdfevQhs0K0GZTl5/ozp3PzRXNTPhSnO5/PuP3So5k7big3PLSaP7+8nb+9XsQ1J0/t89WOUyoY+xWMRURE4upnT24kEHJ874I5B23/4unTyMv287MnNx60/cXNeyitbuTjJyR2bt3B2X5+f9VxhEKOa//yJo0t4XGxdzyzmXdKa7n1I0cldAU06Vu5WRn84ZPzGZTt5+Z/r+ewgsF847y+G0LRJqWCsRb4EBERiZ83d1bxr9WlfPbUqUwaefC41fxBmVx3xjRe2FTBK9v2Htj+t9eKKMzL5pw5oxNe35SCwdx5xTFs3F3Ld/65lle27eX3L23jigUTO72oS1LT2Pxc/vDJ+cwdN5TbLzu6T4dQtEmpYKwFPkREROIjFHL86N/rGD00my+ePi1qm6vfN4Vx+Tnc+sRGnHOUVjfy/KY9XDZ/AplxWEAiFmfMHMU3zp3JY2vKuObPK5k6cjA/+MCcrl8oKWnexGH894ZTOXZS38xC0V5KBePwdG0KxiIiIr31yKoS1pbU8J3zZ3c4X3BOZgZfPWcGa0tq+O/bu/j7G0U44PI+XqL4i6cfzsK5YwiEQvzy8nkxr8wn0l0p9S8rw2eEkrQgiYiISLrY39TKbU9u4thJw7h43rhO23742An8cdl2/u+pTTS1Bjl9RmHM04XFi5lx9yeOpWJ/M2PyNa5YEkc9xiIiIgPM/z21iX31zdx80dxOlwmG8GfvjefPZOe+Bsprm/l4D1eN660MnykUS8KlXDB2Do0zFhER6aHnNpbzwKs7+fT7pnLUhGFdv4DwON+TDhvJ+GG5nDGzsOsXiKSolBpK4ffmKgw6h4/O/8IVERGRg+3Z38Q3/7GWWWPy+NbC2KfCMjP+cPV8GluCBy1BLJJuUioYt03iHQw5kjCDh4iISMoKhRzf/Mda6poDPHztid2eCmtItp8hHVykJ5IuUurPPn9EMBYREZHY/fmVHby4uYLvXzib6aPzkl2OSL+UUsHY510goAvwREREYrdhVy23PrGRs2eP4soTk3PxnEgqSKlg3NZjrIvvREREYtPUGuTLD68mf1AmP/vIUV3OQiEykKXUYKEMn3qMRUREuuOnSzawubyOB/5nASOHZCe7HJF+LaV6jDN84XK1yIeIiEjXnttYzv2v7uSaU6Zy2gxNsybSlZQKxn71GIuIiMQkEAzx7UffZtaYPL55XuxTs4kMZCkVjA9M1xZUMBYREenMmzur2LO/mS+dOb3bU7OJDFQpFYwjF/gQERGRjj23cQ+ZGcZpMwqSXYpIykipYPzeAh+hJFciIiLSvz27oZwTpo4kLycz2aWIpIyUCsbvLfCR5EJERET6sR1769lWUc9Zs0cluxSRlJJSwbjtortXtu1NciUiIiL919KNewA4a9boJFciklpSKhhv3VMHwI/+vT7JlYiIiPRfSzeUM33UECaNHJTsUkRSSkoF4w8fMx6AeROHJbkSERGR/qm2qZU3tldy1mz1Fot0V0oF4ykFg5k9digFWrlHREQkqpc2VxAIOc7W+GKRbkupYAyQ7ffRoqvvREREolq6YQ/DB2VyzKThyS5FJOWkZDBubg0muwwREZF+JxAM8fymPZwxcxQZ3kxOIhK7lAvGWX4fzQH1GIuIiLS3qqia6oZWjS8W6aGUC8bZ/gwFYxERkSiWbizH79NqdyI9lXrBONNHS0BDKURERNpbumEPJxw2QqvdifRQ6gXjDA2lEBHpjJktNLNNZrbVzL7dQZvLzGy9ma0zswf7ukaJv5376tm6p06Leoj0gr+rBmaWA7wEZHvtH3HO/bBdm2zgAeA4YB/wMefcjrhXS7jHWMFYRCQ6M8sA7gbOAUqAFWa22Dm3PqLNdOA7wMnOuSoz07xeaWDphvBqd2drfLFIj8XSY9wMnOmcOxqYByw0sxPbtbkGqHLOTQN+AfwsvmW+J9ufQYuCsYhIRxYAW51z7zrnWoCHgYvbtfkscLdzrgrAObenj2uUBFi6UavdifRWl8HYhdV5DzO9m2vX7GLgfu/+I8BZZpaQeWLCs1JojLGISAfGA8URj0u8bZFmADPM7GUze83MFkbbkZlda2YrzWxlRUVFgsqVeKhtauX1dys5U4t6iPRKTGOMzSzDzNYAe4BnnHOvt2ty4ETsnAsANcDIKPvp9Uk225uuzbn22VxERIBonRLtT5h+YDpwOnAF8EczG3bIi5y7xzk33zk3v7CwMO6FSvws27zXW+1OwyhEeiOmYOycCzrn5gETgAVmdkS7JrGciONyks32+3AOWoMKxiIiUZQAEyMeTwDKorR53DnX6pzbDmwiHJQlRS3dUM7wQZkcq9XuRHqlW7NSOOeqgReA9l+7HTgRm5kfyAcq41DfIbL84ZI1nEJEJKoVwHQzm2pmWcDlwOJ2bR4DzgAwswLCQyve7dMqJW6CIafV7kTiJJZZKQqBVudctZnlAmdz6MV1i4GrgVeBjwLPuQSNdRg+KAuAfXUtmqdRRKQd51zAzK4HngIygPucc+vM7BZgpXNusffcuWa2HggC33TO7Ute1dJmb10zP3x8HUNz/UwYPogJw3OZMHwQE0fkUjgkG4DK+hZ21zZRXtvErpomNu/eT1VDq8YXi8RBl8EYGAvc700B5AMWOef+0+4key/wFzPbSrin+PJEFTxuWC4AZTWNTCkYnKjDiIikLOfcEmBJu203Rdx3wNe8m/Qjj60u5b9v72Lk4Cz21bcc9Fy234eDQ2Zm8hlMHzWE98/QOHCR3uoyGDvn1gLHRNkeeZJtAi6Nb2nRtfUY1za29sXhRERE+szT68qZPXYoT3z5VBpaApRUNVJS1UBxZfi/ZsaYoTmMzc9hdH74v4VDsvFnpNx6XSL9Uiw9xv1KXk645P1NgSRXIiIiEj/76ppZubOSL50Zvg5yUJafGaPzmDE6L8mViQwcKfcn5pBsBWMREUk/SzfuIeTgnDmack0kWVIvGHs9xnXNCsYiIpI+nl5XzvhhucwdNzTZpYgMWCkXjDMzfORk+hSMRUQkbTS0BFi2pYJz5owmQQvHikgMUi4YAwzJzmR/ky6+ExGR9LBsy16aAyHO1TAKkaRKyWA8NMevMcYiIpI2nl5XTn5uJsdPHZHsUkQGtJQMxkNy/BpKISIiaSEQDLF0YzlnzRpFpqZdE0mqlPw/ME89xiIikiZW7KiiuqFVs1GI9AMpGYyHZPupUzAWEZE08Mz6crL8Pk7TynUiSZeiwVgX34mISOpzzvH0+t2cOq2Awdkpt+aWSNpJyWA8ODuD+pZgsssQERHplQ279lNS1ahhFCL9REoG42y/j5ZAKNlliIiI9Moz68sxg7NmKxiL9AcpGowzaAkqGIuISGp7ev1ujps0nMK87GSXIiKkbDD2EQw5AgrHIiKSokqqGlhXVqthFCL9SEoG4yx/uOxmDacQEZEU9cz6cgDOnTtC9KTlAAAgAElEQVQmyZWISJuUDMZt44s37q5NciUiIiI988z6cqaPGsLUgsHJLkVEPCkZjF/YXAHAR377apIrERER6b7qhhZe316pYRQi/UxKBuMbF85KdgkiIiI99tzGPQRDTsMoRPqZlAzGC6aOSHYJIiIiPfbUut2MysvmqPH5yS5FRCKkZDAG+ODR4zQuS0REUk5tUyvPb6rggiPH4vNZsssRkQgpG4xzM300avU7ERFJMU+9s5uWQIiL541Ldiki0k7KBuOczAyaAgrGIiKSWha/VcakEYOYN3FYsksRkXZSOhirx1hERFJJxf5mXt66l4uOHoeZhlGI9DcpG4yzMny0auU7ERFJIf9dW0bIwUUaRiHSL6VsMPZnGCEHu2oak12KiIhITBa/VcasMXnMGJ2X7FJEJIqUDcbbKuoB+MJfVyW5EhERGcjWllRz/ys7umxXtK+BVUXV6i0W6cf8yS6gp9pGZlU3tCS1DhERGbhagyFueGg1O/Y1MHnkIE6fOarDtv9eWwbAB49SMBbpr1K2xzgzI1x6a9AluRIRERmo/r6imB37Ghia4+eWf6+nJdDxtS+L15Qxf/JwJo4Y1IcVikh3pHAwDvcZ6wI8ERFJhsaWIHcu3cLxU4bzy8vn8e7eev78yvaobTfurmVT+X4NoxDp51I2GLfNchMIqcdYRET63p9e2U7F/ma+tXAWZ84azZmzRnHns1vYU9t0SNvH15SR4TMuOHJsEioVkVilbDB2Xh5Wj7GIiPS16oYWfvvCNs6aNYrjp4wA4AcfmENr0PGzJzcd1NY5x+I1ZZw8rYCCIdnJKFdEYqRgLCIi0k2/fXEbdc0Bvrlw5oFtUwsGc82pU3l0VQmriqoObF9VVEVpdSMXH61hFCL9XcoG45CXjHXxnYiI9KXdNU38+eUdfGjeeGaNGXrQc9efMY3RQ7O5efE6Qt5Qv8Vrysj2+zh37uhklCsi3ZCywbhNUGOMRUQOYmYLzWyTmW01s29Hef5TZlZhZmu822eSUWequnPpFkLO8dVzZhzy3OBsP985fzZrS2r4x5vFBIIh/rN2F2fNHkVeTmYSqhWR7kjZYHzkhPxklyAi0u+YWQZwN3A+MAe4wszmRGn6d+fcPO/2xz4tMoVtq6hj0cpiPnHC5A6nXbt43jjmTx7ObU9u4ol3drOvvoWLjh7fx5WKSE+kbDC+6sTJHDF+KOOH5Sa7FBGR/mQBsNU5965zrgV4GLg4yTWljTue3ky238d1Z0zrsI2ZcfNFc6lsaOFbj6wlL9vP6TML+7BKEemplA3GZsaR4/Np0cV3IiKRxgPFEY9LvG3tfcTM1prZI2Y2MdqOzOxaM1tpZisrKioSUWtKWVtSzX/f3sVnTj2MwrzOZ5c4Ynw+VyyYRGNrkIVHjCEnM6OPqhSR3kjZYAyQleHrdJUhEZEByKJsa38xxr+BKc65o4Bngfuj7cg5d49zbr5zbn5hoXo8/++pTQwflMlnT50aU/tvnDuTU6cXcPX7piS2MBGJmy6DsZlNNLPnzWyDma0zsy9HaXO6mdVEXMhxU2LKPViW36fp2kREDlYCRPYATwDKIhs45/Y555q9h38Ajuuj2lJWxf5mlm3Zy6dPnhrzRXQjBmfxl2tO4IjxuiZGJFX4Y2gTAL7unFtlZnnAm2b2jHNufbt2y5xzH4h/iR3L8qvHWESknRXAdDObCpQClwMfj2xgZmOdc7u8hxcBG/q2xNSz2puX+H2Hj0xyJSKSSF32GDvndjnnVnn39xM+gfaLy2sbWoIEQo71ZbXJLkVEpF9wzgWA64GnCJ+vFznn1pnZLWZ2kdfsBu8bwLeAG4BPJafa1LG6uBq/z9T7K5LmujXG2MymAMcAr0d5+iQze8vMnjCzuXGorUvrvED8wKs7+uJwIiIpwTm3xDk3wzl3uHPux962m5xzi73733HOzXXOHe2cO8M5tzG5Ffd/q3ZWMXfcUF1EJ5LmYg7GZjYEeBT4inOufRftKmCyc+5o4NfAYx3sI65XOP/4kiMAGD44q9f7EhERiSYQDLG2pIZjJg1PdikikmAxBWMzyyQciv/mnPtn++edc7XOuTrv/hIg08wKorSL6xXO00fnkZftp7lV44xFRCQxNu7eT2NrkGMmDUt2KSKSYLHMSmHAvcAG59wdHbQZ47XDzBZ4+90Xz0I7kp2ZQWNrsC8OJSIiA9Dq4moAjlWPsUjai2VWipOBq4C3zWyNt+27wCQA59zvgI8CXzCzANAIXO6caz9vZkLkZvloVjAWEZEEWb2zioIh2UwYrpVWRdJdl8HYObec6BPGR7a5C7grXkV1R45fPcYiIpI4q4urOWbSMLwvRkUkjaX0yncAuVkZNCkYi4hIAlTVt7B9b73GF4sMECkfjNVjLCIiibK6OLywh8YXiwwMqR+MszJo1KwUIiKSAKuLqsnwGUdN0MIeIgNB6gdjvy6+ExGRxFhVVMWsMXkMyorlWnURSXUpH4xzszSUQkRE4i8YcrxVXKPxxSIDSMoH4xy/Lr4TEZH427JnP3XNAY0vFhlAUj4YZ/qN1mCfTJksIiIDyOqi8MIeWgpaZOBI/WCc4aM1qIvvREQkvlYXVTF8UCZTRg5Kdiki0kcUjEVERKJYVVTNMZOGa2EPkQEkDYKxEdBQChERiaOaxla27qnjWF14JzKgpHww9vt8BEKOUEjhWERE4mNNscYXiwxEKR+Ms/zht9Aa0nAKERGJj9VFVZjB0RPVYywykKR8MPb7wmO/NJxCRETiZVVRNTNH5zEkWwt7iAwkKR+MMzO8HmNdgCciInEQCjnWFFVpGIXIAJQGwTjcY6y5jEVEJB7e3VtPbVNAK96JDEApH4yz/RkAPPHOriRXIiIi6WBVURWAZqQQGYBSPhjnZoWD8U2Pr0tyJSIikg5WF1UzNMfPYQVDkl2KiPSxlA/GGkAhIiLxtLqoinmThuPzaWEPkYEm5YPxObNHAzBheG6SKxERkVRX1xxgU/l+DaMQGaBSfh6a3KwMTptRSE1ja7JLERGRFPdWcTXOaWEPkYEq5XuMITyXcVALfIiISC/9Z20ZmRnGPC3sITIgpU0w1gIfIiLSG9sq6li0soRPnDCZ/NzMZJcjIkmQFsG4ORBi4+793PHMZi77/avJLkdERFLQHU9vJtvv4/ozpyW7FBFJkpQfYwzw4uYKAH61dEuSKxERkVS0tqSa/769ixvOmk7BkOxklyMiSZIWPcYiIiK9cduTmxgxOIvPnjo12aWISBIpGIuIyIC2fMtelm/dy3VnTCMvR2OLRQYyBWMRERmwnHPc9tRGxg/L5RMnTEp2OSKSZArGIiIyYD3xzm7WltTw1XNmkJOZkexyRCTJFIxFRGRACgRD3P7UJqaPGsKHjhmf7HJEpB9Ii2A8NCctJtcQEYkLM1toZpvMbKuZfbuTdh81M2dm8/uyvv7ikTdLeHdvPd88byYZPkt2OSLSD6RFMH7yK6cluwQRkX7BzDKAu4HzgTnAFWY2J0q7POAG4PW+rbB/aGoN8stnt3DspGGcM2d0sssRkX4iLYLxuGG5HD9F69qLiAALgK3OuXedcy3Aw8DFUdr9L3Ab0NSXxfUX97+yg921Tdy4cBZm6i0WkbC0CMYAja3BZJcgItIfjAeKIx6XeNsOMLNjgInOuf90tiMzu9bMVprZyoqKivhXmiQtgRD3vPQu759RyAmHjUx2OSLSj6RNMH6ntDbZJYiI9AfRuj/dgSfNfMAvgK93tSPn3D3OufnOufmFhYVxLDG5nttYzr76Fj518pRklyIi/UzaBOMPHDU22SWIiPQHJcDEiMcTgLKIx3nAEcALZrYDOBFYPJAuwFu0soQxQ3M4bXr6hH0RiY+0Ccb/c4qW8RQRAVYA081sqpllAZcDi9uedM7VOOcKnHNTnHNTgNeAi5xzK5NTbt8qr23ihU17+Mhx4zUThYgcIm2CcaYvbd6KiEiPOecCwPXAU8AGYJFzbp2Z3WJmFyW3uuR7dFUJIQeXHjex68YiMuCkzQTAmX795S8iAuCcWwIsabftpg7ant4XNfUHzjn+sbKEBVNHMKVgcLLLEZF+qMtuVjObaGbPm9kGM1tnZl+O0sbM7FfeZPJrzezYxJTbMb96jEVEpBMrd1axfW89l81Xb7GIRBdLj3EA+LpzbpU3IfybZvaMc259RJvzgene7QTgt95/+0xmxns9xs45zUspIiIHWbSimMFZGVxw5JhklyIi/VSX3azOuV3OuVXe/f2Ex6y1X1T+YuABF/YaMMzM+nSaCH/Ge28l5DppKCIiA05dc4D/vr2LDx49jkFZaTOKUETirFvjD8xsCnAMhy4h2uWE8okW2WMcckrGIiLyniVrd9HQEuRSDaMQkU7EHIzNbAjwKPAV51z71TQ6nVA+Yh8JW0UpclYKBWMREYm0aGUxhxcO5thJw5Jdioj0YzEFYzPLJByK/+ac+2eUJl1NKA8kdhWlTP97b0W5WERE2myrqGPlzioumz9R15+ISKdimZXCgHuBDc65Ozpothj4pDc7xYlAjXNuVxzr7FJuZsaB+z9ZsoGlG8r78vAiItJP/WNlCRk+40PH9ukIPxFJQbFcgXAycBXwtpmt8bZ9F5gE4Jz7HeH5Mi8AtgINwKfjX2rnIlcweuDVnTzw6k523HphX5chIiL9SCAY4tFVJZwxcxSj8nKSXY6I9HNdBmPn3HKijyGObOOA6+JVlIiISDy8uLmCiv3NXDZ/QrJLEZEUoFUxREQkbS1aWUzBkCzOmDUq2aWISApIq2B89UmTk12CiIj0E3vrmlm6YQ8fPnYCmRlp9XEnIgmSVmeK196tTHYJIiLSTzy2upRAyHHpcRpGISKxSatgvK++OdkliIhIP+Cc45E3Szh64jCmj85LdjkikiLSKhj/7CNHJbsEERHpB9aV1bJx934+qt5iEemGtArGM9QrICIiwCNvlpCV4eOio8YluxQRSSFpFYz9GVrRSERkoGsJhHh8TSnnzB1N/qDMZJcjIikkrYKxrjoWEZHnNu6hqqGVjx6rYRQi0j1plSQzfWn1dkREpAceXVVCYV42p04vSHYpIpJi0ipJaiiFiMjAtreumec37uHDx4zHr28RRaSb0uqsoaEUIiID2+NrygiEHB/RbBQi0gNplSQz1WMsIjKgPfJmCUdPyNcsRSLSI2kVjM0UjEVEBqp1ZTVs2FWruYtFpMfSKhgDHDk+/8D9d0prkliJiIj0pba5iz94tOYuFpGeSbtgXDAk68D9D/x6OXv2NyWxGhER6QvhuYvLOGfOaIYNyur6BSIiUaRdMG5vf1Mg2SWIiEiCPb9pD5X1LRpGISK9knbBuP04Y+dckioREZG+8sibmrtYRHov/YJxu8ehHuTiP728nbeKq+NSj4iIJFbb3MUf0tzFItJL/mQXkGgh52gOBMnK8MU8a8WP/r0egB23XpjI0kREJA4OzF2sJaBFpJfS7k/r9tm3qr6Vmd9/kt++uC05BYmISEI9u76c2WOHMnOM5i4Wkd5Ju2Dc3iNvlgDwr1WlSa5EREQSobiqgZmjhyS7DBFJA2kXjKe3W+3o0VUlSapEREQSLRhy7K5pYtyw3GSXIiJpIO2C8dfPmRF1uxbFExFJP3v2NxEIOQVjEYmLtAvGuiJZRGTgKKtuBGD8cAVjEem9tEyR4/Jzkl2CiIj0gZIqLxirx1hE4iAtg/EvPjYv2SWIiCSNmS00s01mttXMvh3l+c+b2dtmtsbMlpvZnGTUGQ9l1U0AGkohInGRlsF4UFbaT88sIhKVmWUAdwPnA3OAK6IE3wedc0c65+YBtwF39HGZcVNW3Uh+biZDsnXeF5HeS8tgnOHTlXYiMmAtALY65951zrUADwMXRzZwztVGPBwM9GCN0P6htLpRvcUiEjdp+Sd2ZsahwdgOWSxaRCQtjQeKIx6XACe0b2Rm1wFfA7KAM6PtyMyuBa4FmDRpUtwLjYey6kYmDB+U7DJEJE0MmB7jsppGmgPBJFQjItKnovUCHNIj7Jy72zl3OHAj8P1oO3LO3eOcm++cm19YWBjnMuOjtLqR8cN0wbWIxEdaBmO/79C3tb8pwNcXvZWEakRE+lQJMDHi8QSgrJP2DwOXJLSiBKltamV/U0BDKUQkbtIzGEcZSgGwdMOePq5ERKTPrQCmm9lUM8sCLgcWRzYws+kRDy8EtvRhfXGjOYxFJN7ScoyxXxfficgA5ZwLmNn1wFNABnCfc26dmd0CrHTOLQauN7OzgVagCrg6eRX3XFswVo+xiMRLWgbjji6v1rLQIjIQOOeWAEvabbsp4v6X+7yoBCjV4h4iEmdpOZRi5OCsqNsbWoLc9Pg7fVyNiIgkQml1E5kZRuGQ7GSXIiJpIi2DsT+j47f1wKs7+7ASERFJlLLqRsbm5+LT8DkRiZO0DMa94VzKznMvIjKghBf30FRtIhI/aRuM//DJ+ckuQUREEqisupHxw7S4h4jET9oG43PmjO7R69RhLCLS/7UGQ5TXNmlxDxGJqy6DsZndZ2Z7zCzqVWtmdrqZ1ZjZGu92U7R2qUK5WESk/9td00TIaao2EYmvWHqM/wws7KLNMufcPO92S+/Lio83vntWt1+jMcYiIv2fFvcQkUToMhg7514CKvuglrgbnN39aZoVi0VE+r+yGi3uISLxF68xxieZ2Vtm9oSZze2okZlda2YrzWxlRUVFnA7dsWx/2g6hFhEZ0NoW9xiXr2AsIvETj+S4CpjsnDsa+DXwWEcNnXP3OOfmO+fmFxYWxuHQnetsPuOOaCSFiEj/V1rdxMjBWeRmZSS7FBFJI70Oxs65WudcnXd/CZBpZgW9rixJnAZTiIj0e2XVjRpGISJx1+tgbGZjzMy8+wu8fe7r7X6TRT3GIiL9X2l1I+MVjEUkzmKZru0h4FVgppmVmNk1ZvZ5M/u81+SjwDtm9hbwK+By14+mdlgwdUTU7cu37OWVrXv7uBoREekt55x6jEUkIbqctsE5d0UXz98F3BW3iuJsSAczU1x57+sA7Lj1wr4sR0REeqmmsZWGlqCWgxaRuEv7aRu6O2Vb/+nrFhGRaEq8GSkmaA5jEYmztA/Gp03v3nWAuvhORKR/a1vcQ0MpRCTe0j4YXzp/4iHbtu+t77C9eoxFRPo3BWMRSZS0D8bRnHH7C3Hf5+qiKn7xzOa471dERA5WWt1Itt/HyMFZyS5FRNLMgAzGnelph/GHfvMKdy7dEtdaRETkUGXVTYwflos3U6iISNwoGLfTj2aaExGRKEo0VZuIJMiACMYLpkSfyzgaxWIRkf6tTIt7iEiCDIhg/PfPncjkkYNiaqsOYxGR/qs5EKRif7N6jEUkIQZEMDazDhf6EBGR1LGruglAi3uISEIMiGAMkOWP8a2qx1hEpN9qm6ptvBb3EJEEGDDBONZrl3u7wIcu3hMRSZzStmCsoRQikgADJxjHOK1Pb3NtSLlYRCRh2oLxmHwNpRCR+Bs4wTjGdr3NteoxFhFJnLLqRkblZZPtz0h2KSKShgZOMO6jeeAVi0VEEqesukkzUohIwgyYYHztaYfH1K63Pb7qMBYRSZzS6kZdeCciCTNggvE5c0az49YLu2zX66EU6jMWEUkI51w4GKvHWEQSZMAE446cfOtz3PPSNi781TKO//GzMfX4llQ1MOXb/+Wd0ppDnlOPsYhIYuyrb6ElEGKcLrwTkQQZ8MG4tLqRnyzZyLqyWir2N8fU4/vcxj0APLyi6JDnFIxFRBKjtKptDuPYVjIVEemuAReMV//gnITuX0MpREQSo21xD616JyKJMuCC8eCulobuZa5Vj7GIJJuZLTSzTWa21cy+HeX5r5nZejNba2ZLzWxyMursLi3uISKJNuCCcVdLQwdjSLadzfymXCwiyWRmGcDdwPnAHOAKM5vTrtlqYL5z7ijgEeC2vq2yZ0qrGxmclUF+bmaySxGRNDXggnFXfvDYugP39ze1dvv1WuBDRJJsAbDVOfeuc64FeBi4OLKBc+5551yD9/A1YEIf19gjZdWNjBuWG/NKpiIi3aVg3M6zG8oP3D/y5qepbw502DZaBlYsFpEkGw8URzwu8bZ15BrgiYRWFCda3ENEEk3BuAv7m6IE4056K1wogcWIiHQt2gkq6t/sZnYlMB/4vw6ev9bMVprZyoqKijiW2DNa3ENEEm1ABuO1N5/L7686Lqa2dc2t/O7FbYRCsfUFa1YKEUmyEmBixOMJQFn7RmZ2NvA94CLnXHO0HTnn7nHOzXfOzS8sLExIsbFqbAlSWd+iC+9EJKG6mKIhPQ3NyWT4oKyY2v50yUaWbtzDhOG5nH/EWDJ8nY9t0xBjEUmyFcB0M5sKlAKXAx+PbGBmxwC/BxY65/b0fYndV6qp2kSkDwzIHmMAf0ZsF29UNrQAcP2Dq7noruUHPRctAysXi0gyOecCwPXAU8AGYJFzbp2Z3WJmF3nN/g8YAvzDzNaY2eIklRuz4qrwtYITtbiHiCTQgOwxhthnjwgE32u3rqw2bvsVEUkU59wSYEm7bTdF3D+7z4vqpZLKcDCeNELBWEQSZ8D2GHc+G/F7WoPdu5pOsVhEJP6KKhvI9vsozMtOdikiksYGbDA+ZuIwvnD64Xz+/Yd32i4Y5aK7tkgddbo2JWMRkbgrqmxg4ohBmsNYRBJqwAZjn8+4ceEsvn3+rE7bbdlT1639BkIhtu+t701pIiLSTlFlo4ZRiEjCDdhgHOl3V8Y2dRvAmuLqTp+/7clNnHH7CxRXNnTaDmDZlgoCwRBrS6pZ/NYhsymJiAjhazdKKhsUjEUk4RSMgflThgNw8rSRXba95O6XO73A7rV39wFQURd1WtCD2l117xv88tktXHTXy9zw0OpuVCwiMnBUN7SyvznARAVjEUkwBWOgYEg2O269kAuPHBdT+/fGHXcckLsaa7zXC84adiEi0rmiyrap2rS4h4gkloJxhGAothkoWr0p3JpbQx3OWhHrtG1aKU9EpHNtwXjSSPUYi0hiKRhHaA3GFlJbvDD8z9WlXPvAyqhtutqTeXNbaBYLEZHOaXEPEekrCsYRzpw1KqZ2kb3Ez2+qiNpr3FXgbZtxSMFYRKRzxZUNjBycxeDsAbsmlYj0EQXjCFMKBvPrK47psl1L4OAg/MPF6w5p09VQigNzIcdxKEVDS4C65kDc9ici0h+0zWEsIpJoXQZjM7vPzPaY2TsdPG9m9isz22pma83s2PiX2Xf8vq4nj//NC9sOevzK1r2HtImyLshBEjFH/XH/+yxH/PCp+O9YRCSJijRVm4j0kVh6jP8MLOzk+fOB6d7tWuC3vS8reeZPGdHt1wSj9A4HQiH27G/ivuXbO+09judQisbWYPx2JiLSDwSCIcqqmxSMRaRPdBmMnXMvAZWdNLkYeMCFvQYMM7Ox8SqwrxXmZXPFgondek3F/vfmLG4LuoGg40sPruaW/6xnW0V49bw1xdXUNLZ6Lb2L7zrZ77qyGl7YtKdbtYiIpJNdNU0EQ46JIzRVm4gkXjzGGI8HiiMel3jbDmFm15rZSjNbWVFREYdDJ8aPLzmSTf9vIQu83uPDCgZ32r6p9dCL7+pbAry+Pfz3RGvQEQo5Lrn7Zd7306VU1bfEdPHdhb9azqf+tKJnb0JEJA0cmMNYPcYi0gfiEYyjjZaNGvecc/c45+Y75+YXFhbG4dCJ4fMZ2f4M/vqZE1j3o/NY9PmTKBiS3a19XP/geyvZnX/nsgNTvNW3BLno7uVRf2iJtHNfPfe/sqOPjyoi0jsH5jBWMBaRPhCPYFwCRI49mACUxWG/SZfl9zE420/BkGwumRfbqngdaY6YyaK4sjHimb6Zr+1jv3+NHy5eR0NL92ataA4EueXf66ltau26sYhInBVXNuD3GWPzNZRCRBIvHsF4MfBJb3aKE4Ea59yuOOy3X8nPzezV66/5c/QhEX01j3Hb2ObuHu8fK0u47+Xt/PKZLQmoSkSkc0WVDYwfnktGDDMGiYj0VpezpZvZQ8DpQIGZlQA/BDIBnHO/A5YAFwBbgQbg04kqNpnG5OfE1O69i+sOtnJn1UGPX313H3Bwf7FzDkvEPG6Rx+hm+7Y5m0NaiURS3DulNTS1Bns084wkT7GmahORPtRlMHbOXdHF8w64Lm4V9VMfPnYC33xkbZftYp0y7U8v7wAOXggk5CCjB7n4rue2sKqoOqa23Q24be19CQ7snVm2pYLhg7I4Ynx+0mpINTWNrRRXNuhnFuEDv14OwI5bL0xyJdIdRZUNnH9kyk50JCIpRivfxSjDZ7x107kHbRs+qHfDKyC8pHSbnvbK3v70Zp7b2Pm0bm25NtTVyiPttNWUkcR/KVfd+8aBUCOx+eS9r+tnJilvf1MrVQ2t6jEWkT6jYNwN+YMyufLESQB874LZvPbds+K6/2AHobU1GGLrnjr+/Vbvr2nsZi7Gm0wjqT3G0n1vldQAXS9NLtKftV2oPHG4grGI9A0F425qC4j+jPCUbl3NcdyVYRG9zh1lmC89uJqz73iRLz20msr6ll4dLzJ8NweCvBxlOWuAFTsqCYXce0MpIi58aWoN0hQxZKSyvoXy2qZe1SWJoVwsqUxTtYlIX1Mw7qa2YNwWMJd8+VT+e8MpPd5fZFCNHEqxaOV7a6Y8uW73gfs3PLSary1a0+PjRR7jp0s28ok/vs47pTUHtVm2pYJLf/cqf1j27oH6MiJ6jI/932eY9YMnD3p8wk+W8viaUrbvrY+5ltff3ccHfr2M5kDH47K7O/RDDqafnqSyYgVjEeljCsbdNHfcUAAOKwz3FOdkZjB3XD6/u/LYHu1vf9N78woHvdC6dEM53+rgQr/lW/fyz1Wlne4zWphsi7WRwXjrnvBS1VUNB/dCt319uX1vfdQe44aW6EH2yw+v4YzbX2B9WW2Htb26bR/f+9fbvLG9ku8/9vUJp6kAACAASURBVA7vlNayc1/DQW0+c/8Kjv/xs+yta+aw7y7pcF/pYHP5fvZ4ve3FlQ1sLt8PwNqSal7dtq/L1+/cV899y7dz8+J1UZ8POcc7pTVR/00EgiFW7OhstffE2d/U2qNvP4orGw7MlBKr0urGqMdqDgQPLNcu/VNxVQNDc/zkx+F6DhGRWHQ5K4Uc7KPHTWDuuHzmeAG5zXlzx/Rof+PycyirCQcj533e3/bkpl7V2NAaZEh29F9tR+OYD2pz4II7OxCoujOFaHFVwyE/H4C65gBX/OE1AP72ehHTRw2J+vpnN4QvJGz7GjWdnfuLl/D7jK0/uYBTb3seCM+acNFdLx+435G5Nz1JfcQfKTdfNPeQNqt2VvGxe17jm+fN5Lozph303B3PbOY3L2zjsetOZt7EYfF4OzE76afPUdcc6NYMEfXNAU697Xk+fOx47rhsXsyvO/nW56LOgfvtR9/mX6tLeeuH5/Z6nnJJjKLKBi0FLSJ9Sj3G3WRmUUOfmTF5ZPdP4IMjAmxb72xvV5mrb+54dbtQlM62N7ZXHnRhX9C74s7vs/dCcpSL7zo6TkcXfLV20NM30MfBBno4XKS+g577SCVV4d7/dWU1hzzX1jtdsb+5R8fvjbpO/o12pG0qxBcjZnKJVbQ/CF/ZFh5f393VIKXvFGkOYxHpYwrGcfSPz5/U7ddEfmBXNrRQsb+ZXTWxXci2+K0yivYd2qv64qYK7n9lx0Hb2hYOCUZJof+/vTOPk6K63v5zumdjZmBYB4EBhk0QBNkXQQRFVncwQTHuwRhxTYygiRj1pyQaXxNF4pIYNQruShRFFBURBdkXAQUdYFhHFh0YYLb7/tFVPdXVVd3VPT3d1c3z/XwGum/dqjq3qrv6qVPnnvP4wi24afYq/3tdqHk9Hn8WC4+Fx63b9PmO7IyWSATzvXM3YNKzX8V0/9/uLU36rA5W5zvZidWIBMy04maqqxWKDxylMCaExBWGUsSQBlm+x7E3DOuAEac0x/hZSwKWXzmoLZ7/cltAm1G4nP23zyLa382zV1k+Av7DG7745CtPLwxaVlWtUFWtQpZX1cV6mtcYSuFcRDjVYrHMAPcf041Abfns2xJc+e9leHhCD1zSt3VMtx1PdGEfSgQmi/jXR5As9pLasa/0OMqrqhlKQQiJK/QYx5CsdC823T8ad4zsjD5tGwUttyr3bJ545pRSLdzCrgS1kXfX7vI/ur559ip0CDGhbcW2g/ho414APjHsz0oRwSfFXOZ6xbaDUEo58vS5Je1bkZZdY93O4BAEN+BUHIaO0kguj2ldlUunznYn+hwDCmNCSDyhMI4xWelef9jBf67uF7AsmrhKO7rf+6Gjflv2HcaUl2vCJL7ZbZ8xAgDGz1qCr4sOAvDFGPtDKSIQJcbMFx+s34Pxs5bgteXFjir77T9szB6QOMWSrt0J6BkQyiursa44diL5UFk5Kqoiy65gxGlYsv+Yhzh9yaYLYxZKkVz3BScczGFMCEkEFMZ1yLDO+bhtxMn+9068u7Fk3rrdGPFoZOEZRjwe8Qsru0Igobh59io8vnALAGBryWFbz5xRMNeFWCmKILeyTkaaJow18frgvI0474nFEeVpNrL/8HHMWbYdgC92sud9C3CnTUo+JzgtHx4qDzSFIXEz2w+UQQRo1bBeok0hhJxAUBjXMbeM6OR/fc3gdnHd9wPvflOr9b2GUIpPNpdgzY5DjtbTNdvcNbv8HmoF+8f/xgmIb6+qydEcSvsdOFIeUH3PyO6fjvoF7FurijHskU/x+Xf2mQwOlZWjcOp7+Oibvf62dK9PNeoe4zXFh/z7jYab56zC1DfX+W4QtLa3VofORx0KJ2n3gBrPcigNnCyhBPrNQKztTZLhn3AUHyhDy7x6/ptUQgiJB7zixJFBHZrgykFt47a/XWGyW5ijfq28wsbJgYccerytvJlKKdvH/7rI21pyGE8t+t52u3/7cDP2lfrG1Pv+BbjiX8ss+w16aCGGP/IpAGCtFv6weU+p7XY37vYte+bzmn1nmEIpDCOx3U4o9JRoFVXVMZk85nQT+rFVKjiUp0Ysu1savvBlEd5du8s/5lhNvqPD3N1sP1CGgkb0FhNC4guFcZwpbJoT821GUnxDZ8hfFuKLLYGV1SY9uzSon1GEWO3HSqTYeTPtJtbp4jtcVo7HF24JqAi4rOgASkqPo3Dqe7brOEnJ5c/cYOiqZ+1Yse0gHp6/yd++72dnOX8rq6oDRPW3e30V1hZ/96Pj+GDdNivPuFUaNquQkcXazc5763bj1Onzbb3s8WLLvlIcdZB/2cg972zAlJdXBd3IkdSGOYwJIYmAwjgOPDmpNxbcNhQA0LtNcLaK2hJNfQi98EM4jCL3t/9dGbT8WEXwBLLKKiuPMXDBzC8s92EXB2vVahZ24Uoa62I+lJNRX2QU0brXe/+Rcsz8ZKu//YaXVvpLOJuZ/MJyFE59D2+tKsaYv3+Ok//4flCfB97b6M+37MTx+eryHejypw+wbX+g6LXyyg/TvORGFm7aF/BeF6VKKXxoCB3RqapW6Hnfh3h9RXHQsh0HyvDI/M0ReWwfmb8Zl2nVDo9XVmHEo4sw5eWaz9ED736DQ2XOwlP8HmMAH27YgwmzlkAphcqqasxds4tp3AyIyGgR2SwiW0RkqsXyoSKyUkQqRWRCImwMxbGKKuwrPU5hTAiJOxTGcWBs9xbo1Lw+AKBHQR7+dG7XoD5NcjLibVZYRAKrspVaZNUoPR4cXlFhUV4vlGRZuf0gHnp/Y/A6FiuZPcDhMjvoXmAFhYWb9loWRPEnbjBs2lyNzmjLF1utJyLqQvO2V9bgu32H/e13vbUuoJ+e9cMJ76/fA8AXZhJgT/QJLQAAPx+tOZfGsZWVV+JQWQWmv7M+aJ3rX1yBJz7Zgq0lzicgPvHJFizZ6nsyod8wGY/fs4t/wIPzgs+9FbqdpccqMfnFFVi+7SAqqxVmfboVN89ehXnr9gT0Lz1WEfJpQqoiIl4AMwGMAdAVwKUiYr7obAdwFYCX42udM4oPahkpoqgmSgghtYEFPuKMiODaIe0w4pR8zN+wBw/O8z2m/+DWoVBKof+DHyfYwkDCOeGOW3iMrUo/W21HDy/Qj4ETzJkUbpmzOkx/CdiH1yPY+uDYQNsQHEphDgdxOtnNipeXbo96XR3zDUG0Fe30MZYctvZ6+73nFikrKrUbnkqruuIOsLPZ6qmDFVZe8mqlsEfz4B8weZ73JaDUtUvoD2CLUup7ABCROQAuAOCfjauUKtKW1fIWq27QU7UVNKIwJoTEF3qME0TbJjmYPLSD/326V0JWo0sEz33xA77cGhiHbH5v9qzatcUKkdApyI7YTjDzYSVwdb1lzNVs7mccUzyf2Bv3teNAmT+UxGm6NjsOH68JSbnhpZX46nvfedU90VafRP34RHuTUGURYgM4F/lWvUKtGjxx0ma7qReC0QrADsP7Yq0tadCf7DCUghASbyiMXUKa14N6GV7/++eu6ofurfISaBFwsKzC743TmfzC8oD3lRahDBVVKkaZF6y3EUp4d5s+3//6g/V7guwHgPWmanZWIjPYY1wzTmP3o+VVmPbmWts42Ztmr7Jsd4q+q6pqhTP++gnOf2IxznrkU1MhFEP/MMf9j2+vx4Ej5UFjnvamL9zDL1ItlLF+4xalw9gyxAZwLkyt+oW6QQg30VD3iqeeLra8r4lqlCIyWUSWi8jykhL7lIexZsfBo6iX7kXTXPeFmBFCUhsK4wRz3wXdAABZaR5kZ9REtvRq0xA9Wzd0vJ3mDTJjbpsV5jjj4xZeucqq6iCxEU1GAas1BIKSw84ekf/mvyvwzupdQe3nPr7Ycj9OPcZGXl2+A7OX7cBjH31nufx/a4L3Hwm6GNRjqb/dexjf/3gE8zfssewfzpn77trd+OsHm4K87vrQQ4VJpGnCOOpQCm2f5tCJ/YfLcfGTX+DTzfusVvNjJWCtvNeHynw5rkOFaCz4Zi92Hjpqu90kpxhAa8P7AgBRfRCVUk8rpfoqpfo2a9YsJsY5Qc9IUVdlwAkhxA4K4wRzxaBCFM0YhzQtb+5fJ/TAJX0K0DA7IyIx2Sm/fl2ZGJLnlxQFtVVUVQd58qIRH1bewMVbfsQdr62JfGMWLNy0F2XllX5lHBBjrMweY0MohU17XVIRYjKgESeiVQyFW3SswiTmLNuOFdtqJgrqpc6jDeOwylYCAEt/OICV2w/hque+Drm+1W6tDn/P+xbgsme+wrFKe4/xrw1PPlIwDdzXADqJSDsRyQAwEcDcBNsUETsOlKF1Y+YwJoTEHwpjl/GLvq3x8CWnAQjt/TOHWSyOomRzLNhrMcGpotq+mEck2OmvJaY452gonPoervnPctzx2lr/RB+BL0b5ljmrcPdbgVkZ7ERdrCTVhFlL/K+tYmPNExrtxOmM98NPZMzwCv624NuANj28XR+nAJj65jqMn7UEq7WKh15NPBuPxcbdP2Pvz8dCFlDRqW3sueXkO9M2j2tieOX2QzjuMGdznO5t4oZSqhLAFADzAWwE8KpSaoOI3Cci5wOAiPQTkWIAlwB4SkQ2JM7iQJRS2H6gDK0ZX0wISQAUxi7GSvu0yMsCAPRpG5gPOVET96wmwlVaeIyL9jtP8eXfdhyeca/ecQjT5/o0QWW1Qrfp8y3DL8pNsdQvfFmEwqnv+WOLa/vEd7nBM7tuZ03pbf0YOA1feO6LorB90rweLPshMP+z7jGet243gMCsFBfO/ALb95f5PcZGgTvm759jwIMfY9Rji4Iq6xk5+Y/v48ARZyEwRT8ewartwSntrD4N5s/ITkN+bjshvujbwFjZFJx8B6XUPKXUyUqpDkqp/9Pa7lFKzdVef62UKlBK5SilmiiluiXW4hoOHClHWXkVJ94RQhIChbGLsfrBHt4lHwBQaMrvmZWWmFNpFUrwzOc/YPF3gR7snxyWkw637Vjzs8Guz7+z97ob8yUrpXDPOz4x/fjCLQCcCVKnZHhrJmHqx6Dc5LEOVRTjvbW7Q24/zRus4nUh/JDmcTYL/QNl5f4YY31Sm/nzGWqyW3lltb/0diiUUhj2yKe46MkllsvMmENe9MI1rRrWs/UEX/HvwFLiqeYxTnb0JzitmaqNEJIAmMfYxVhNPLn3vG743Tkn+x9v62Sle3EkwlK7scDOq3udKXtFNA7V3T9Z59qNJU690sZQhrm1nFAXjuOG2Nivvvd5dv/0dmBox/NfbrNd/8aXgysUGrEqk71x988YPytYjOpcaKhauP+IT5SbJ7eZD+W64sDsH+kWgtzM/zNMYtyyrxQdDbHzVgI2YJ9K+dP15Wam4Wi5vQfbtBWH/Ug80IUxi3sQQhIBPcYuJtuQvk0nI82DJrmZOKtLPp69oq+/PSvdi49u95Wd7tAsJ2421uVTaD2FWF3icRgDYZz8FsqzHAuOOoyNjZZ/frbVst040S4Ueknsy/+1NKDdPIntvCcCs38cOR5+XP/4uEYYj3h0EeYsMxZHCZ+uTRdVm/eW4s43nH1+UjCSIqnZQY8xISSBUBi7mHrpNcI4wxQqISIY0bV5wPK8er6cn8M758fHQDiPfU32tEtW+ZrrirIEeP7NHCqzD33RJwcGCWmDwLQK9fjHQuuUdqGYarg5cpKu7SEHkw/NMJTCXWzaU4r8+pkBed0JISReUBi7GOMPw7K7zsbSu84O6vPbYb7qeQKgWf1MLJl6FqaO6WK5vUbZ6TG3ceX2Q+E7wbk3Mt441UTxFE/hClMkGrtKdcb2nvctCFoeSmw7IWwoRZSkYLq2pKX0WAU+2rg34KafEELiCYWxi5nYrzV6t2mIxXcOR8PsDDRvkBXU5+LeBQBqBF7LhvX8OZHNFJgeTa7/86iY2puMuDEjgRs8xqGwu0moqvZVPKwr77qVgDV6jDfvDT+5z3K77vsInLC8v24PjlVUY7x2XSOEkHhDYeximuRm4s3fDg4StEb0LG1OBJ7HI7jl7E4AgJFdmyM3M3Du5ce/OzNonc/uGObc4CTEjZro4417E21CSKxS9PnagRkfbELHu9+vk/2Ofuzz4H0aPvf//Wp70HInxCMtIHHG6yuK0b5pDnq3cV71kxBCYgmFcZKjx+46+Wn3CjBpQBsAwMT+rYOWd2iWi3E9WvjfZ6R5LL3UqYQbRdFHG0OXRk40G/eUYv3On4Laq5TCU599H1dbYnH+XPgROCHZtv8IlhUdwPg+BUk/J4EQkrxQGCc5+s+Hkx/3i3oXIL9BFopmjMNZXaxj+GZe1tv/evU951j2eWXyQMv2u8Zaxza7GYqiyFn0bQnOfXxxUHuVw4mYsYQT51KHN1buhAhwUa9WiTaFEHICQ2Gc5OiOlXATiJ6/pj8u17zFTsnOSLOs6DagfRPb/oM7Wi9zKxTGsSOOiTv8xMJj7ManBica1dUKb64sxuAOTdGyYb1Em0MIOYGhME5y9FLQ2emha7VkeD0RPZ5sVj8TgPM8vwCQk+nF3WO7Ou7vBpiRIHaMemxR3Pf59KLvg8pbR8qmPdFN2iOxY1nRARQfPIoJfTjpjhCSWFj5Lslp1bAe7hjVGeef1jKg/fXfDMJbq3bipaW+CUm6gHbCmukj/VXKIon0y8lIsyw37GYqqiiMk5k3V+6s9TYWfLMXv+gbHHNP4sfrK4qRm5mGUd1OSrQphJATHHqMkxwRwY3DO6J148DMFX0LG+MPo2pifm0yuKG9ViXv8z8M97fl1UtHdkaaf/tOyclMi8jDTIgbSIvgppHEniPHKzFv3W6M696CRT0IIQmHwjiF8Rq8t3aC9bXrB2HO5IFBwrpmPef7y87wRtTfKdcOaVer9V/7zaAYWUJSkUieppDY88H6PSgrr8J4hlEQQlyAI2EsIqNFZLOIbBGRqRbLrxKREhFZrf1dF3tTSaTkZqbh0v6+CXftm+Za9mmSm4mBNpPpgMg8xr7JeqH7TxvTBf+bMsT//u6xp+AGrXqfHa1qORkn2vV/0bfAn96OJC/92zUOuZwe48TyxspitGmcjX6FjRJtCiGEhBfGIuIFMBPAGABdAVwqIlYzrF5RSvXU/p6NsZ0kSh66uDuKZoxDXh2UgzaTneENiEl+9oq+QX2uHdIO3QvycMOwDnj6V33w66Htcefo0Gne9HjnaNM4RRve0TA7g7GnKYA3zPn3evjgLFEUHyzDkq37Mb43cxcTQtyBk1+E/gC2KKW+V0qVA5gD4IK6NYu4iaV3ne2oX1Z6YHyg1zQR76wu+f5y1XeO7oKRFhNt8utnostJ9QO3owmX9Cgn9kWre9I8wpjpFCBcqES0nytSe97SJk9e3Ju5iwkh7sCJZGgFYIfhfbHWZma8iKwVkddFhG62FCJc9bs/jjsFANAwOz0g77H5EXUogTJn8kBcPrAN5kweiL9P7BWwzLiaPlkwEsJ5DO1I83qiFtUkNlx1emGtt+EJI4zDLSd1g1IKb6wsxsD2jW3nOBBCSLxx8rNv9athznH1PwCFSqkeAD4C8LzlhkQmi8hyEVleUlISmaXElWSle3DdGe1RNGMc0r2egOT8uZmB2QBPCiGwB7Zvggcu7I72zXKRkxnoeR7SqSkAYGL/Nlj4u2E4rSAvYLlR19w6olPQtq28vuZtWLF9/xF6jGPMo784DQ2ynGeJPL1DE/zp3Nrlxg6nexljnBhWbDuIov1lGN+bk+4IIe7BiTAuBmD0ABcA2GXsoJTar5Q6rr19BkAfqw0ppZ5WSvVVSvVt1qxZNPYSl2H2xqZ7PVj/51H477UDkK8J4RZ5WXjwou64a+wpjrZpFqMFjbJRNGMcerfxTc6ZM9mXSUPHeJd264iTA1LPAdYewVeuH4TFdw4Pajey+6djUQnjs7vkI18rkEICubh3AWZblBS3m3iV7vUgp5YpvMLHGFMYJ4I3VhYjO8OLsd1bJNoUQgjx40QYfw2gk4i0E5EMABMBzDV2EBHjle18ABtjZyJxM89d3T+oLTczDUM6NUV5pa9GcEaaB5cNaOM4R6lRjH50+9Cg5fUyvBhgyDRgruhrFsJm3XP14EJkpXvDZqs4fLzSNv9zXdO+aeQhI7ecHewtdyNWNxs3Du9o2VcElmXJI9pfGOFLj3H8KSuvxLtrdmP0qSchJ5N1pggh7iHsz75SqhLAFADz4RO8ryqlNojIfSJyvtbtZhHZICJrANwM4Kq6MpgkBjsRGSoVVl49XyaMSD1CRp3SMb++ZZ9QM9jNOsfOIxhuFvxFvVrZ9jnzZPsnHiLBsUaR0qpR4PFO8wga52RY9t10/2jMv3UoRpzSvJZ7DcQqLCUWWAljBWC0xWRMEYFEVH8xmNU7DoVc3qdt6HRuJPa8vWoXSo9XYmI/pkMkhLgLR/4wpdQ8pdTJSqkOSqn/09ruUUrN1V5PU0p1U0qdppQarpTaVJdGk/jzxdSz8NHtQ/GKxWNwOxrnZGDln87BHSM7R7Sv2qZtMj86jzZO+Noh7Wwfw2em2X91zB5sJ5g9xGabO+bnolfrhra2dD6pfsQTBa8eXBjU9sI1NU8Aapuq7sKegWXK62uxxVb3KUop/PNXwRFY4v8nmKz04AHPnTI4qK2k9HhQ24MXdfe/Hn0qyxDHE6UUXviyCKe0aMDcxYQQ18E598QxHfPrY0CIYiBWNM7JiHjWv949Wn1sFtZmkenUAylin67t8oFtbdfzeiRicZxhEtrm3Xo9giqbjerjjTRW9oKewclljPtNjyCOpH6YCXVzJg/Eh7cN1fYRbGe1L+oG08YE57S2G9XSaSOC2noUNMSC24bijRtY7dCtLP3hADbtKcVVp7dl7mJCiOugMCauQxejkf5kXj+0vbZ+YLtZMEaSt9b4u23MZDE0RCiFlUB9ysIbqnPX2C4or6oO3K+pj0cEVdWh1XYknvEnJ/VGTwsPtPGmoVkEEwgbZIUuIDOwfRO0yPOFh1h6jLX/rz+zA+42TNIMNSRz9hKdTs3rhw2PULUOdiHR8vySIjTMTre8MSOEkERDYUyiYmK/1phxcffwHaPAL4wj9CbZlbY2C7G0EMJ40/2jA95XGsToMxaV/KwwC+NO+bkYZRE/q3PFoEI8fmkv3DGqs39ds8j1CFAdxg1tJYyzbSY82rVHi9UxtRPqVjcOxrH9emh7DO5Ycy7tPgfMJpF87Dp0FB9+sxe/7Nc6qCAQIYS4AQpjEhUzxvfAxP51M3FGj5WNVPdUaF5Xs3w0C6tQIQLmH+tKoyfXwp7hnYM9x74sBzVWPB1GUIsA3Vrm4cbhHdG2SbalzSKCzs0bhNyO1fH69PfDLPtaTR7MTPNgQPvQnlbdK2/GSqTaZSExCmY9VlvZhYlAbD8HIoJ3bgyOKbajVxvrGG0SP15aug1KKVw+wD4UiRBCEgmFMXEdkXqM9Qk8unc3XHyvlTB+eEIPy4lb9Q0hAmbRvPmB0Xj2yn5B63g9ngAbwo3COMFPH7suBnUB6xFg6pguuP/CU223Y3W88m2Kqpj7fnT7mdh0/2ikez34/ciTbQugTDPlom6amxlgd+A+bE3V1s3A8M75AILPWSctG0mjnPSQ2znNZkKiFca0bEfLqxyvR2LDsYoqzF62A2ef0pyV7gghroXCmLgOXQg59RjrJat1j3G4iWBWeWs7Na+PHgXBIuukvCy8c+Ng/G/KkKA42sw0L7wewQe3noGnDTHE5u1HEgJhtkwZ+mSkefw3AZ3yc0NuE6gp1e2E9k1z/GJ5ylmd8M6UIbZ9OzevSaH3gCbULctj2gxbj6fOzUzDSXm+c5dtymU7bWwXvPzrAejWMnyFwrd+e7pl+5OTemOMIeOEMX46k4/x4867a3fjwJHymJT5JoSQuoLCmLgOXVA5nUymC1F9clpWuhdFM8bZ97fwGIfa02mtG6K75kFddMdwfHDrGQHLu5zUAO2b1QhVr9daGC+4raZYiTEvtDFrhz5kPbRZDzHQj4Xx2MyZPBDXn1kT2mDc6+COTXDdGdZhD1Y4yRyi51GeOamXv00PobA6VQrAGzecjk9M4RwZ2vHvV9gYU8d0wcMTemCoVvZbJzPNi9M7+NrCZRHpcpJ1iMnY7i0w63LrSY+X1VEYELFGKYXnlxShU34uTu8QWWYbQgiJJxTGxHXocad3jg5O3WXFuB6+fLndW4X3LgJAhsVEMafz/No0ybYRYjXuUbPHWA9T7mTwtIr4SkcH2aGJQLOX2ZyjWMQ32XDamOAMDm0aZ+Ol6+zzTYcKxwiFnm6tfdOam4AmuRl+u286K7B6XZpH0KdtI7Qz5Whu3Tgbb9wwCA9cdCqy0r24pG/rkGEz4c6NOdWdHb8fVZNPmxP34suqHYewbudPuOL0QqZoI4S4GtbiJK4jzesJ6fHVefQXp6HoxyM4p2tz/PDQ2KAf3FHdmmP+hr0Bbd1aNvAL6brC65GACYBWadZEgJmTeuNQWUVQu3EdXR/rgtlJfuRQqchaNayHX4XIwRwKfzyxR/zn55tdP/vskxrhP6pbc7RpnI0pZ9lXzouk2pzVREFjoZlwIje/fib2lR73i3gSf55fUoT6mWm4uBdTtBFC3A2FMUlaLu5d4H9t5YV6clIff9zxPy/vg5XbD+Iu0+SxqwcX4rkvitC2cU7Q+tHSqmE9jOveAi9+tQ2AdYyxQJCV7sVJeYGxrvo49HV0kat7jPX3VuN1Uriksro6bJ9IMIpwfe9pHg/uHtc1ZvtomB0saCMpNLPg9jNRVl6JYxWxHTtxxr7SY5i3bjcuH9gWOZn8ySGEuBtepUjK4vUIvB6f8Bx96kmWpX+nn9cN08/rFtD22C97okuL+kF9Q9GmcQ66t8pDn7aNcPXgdlBKobJaYfay7f7YXCN2T5P1ZqUC/9djjPUJgKeEsC+UV7myKrLCFovuGI6hD38Sdl8i4g+BOV7prowPefXSkVcvHUU/Hkm0KSck2ftyaAAADPdJREFULy/djooqhSsGFSbaFEIICQtjjAkxcWGvVrYTuuzISPPgfzcNwb3nd4PXI0jzevDAhadi8Z3D0dIw0W7rg2NxYc+WmHlZb8vt3HdBN3Rt0QDTz+uKlnlZ+N1IX1zsxb19j6BbN87Gq9cPwoMXBRdX0SvBWVW007lmSLuIxtVGy6s8rnsLy+W5mgewfbMcfzq7oxWxF8bjtacD7940BLMmWR+7cLRqVC/g/a/PaIczTJP+UgURGS0im0Vki4hMtVieKSKvaMuXikhhXdhRXlmNl5Zux7DOzYJizQkhxI3QY0xIHeH1CAoaZQe1PTaxl80aQN/Cxph3iy/rxZJpZwNAULx1/3bW8blNcjMxd8pgfw5gnenndUVVtbLMUnHveV1x6GhFULuRNdNH2lbKK2yag+eu7of+hY2x+6ejAOyzRNSGhyf0wEMXd0dGmgenOpxkacacvzqW4R5uQkS8AGYCOAdAMYCvRWSuUuobQ7drARxUSnUUkYkA/gLgl7G25YMNe1BSehxXMkUbISRJoDAmJIWwysV89WB7L/FVIZbp5NVLD7lcL9LRMb8+3rlxME5pEXth7PEIMkJMsvvz+d1Q6MAjOWfyQOw8eDSWprmR/gC2KKW+BwARmQPgAgBGYXwBgHu1168DeEJERNmVIKwFgzs2wZmdgidQEkKIG6EwJoTEjEgq0cUSpx7JgRFM2ktiWgHYYXhfDGCAXR+lVKWI/ASgCYAfY2nI+ae1xPmn1W0WGEIIiSWMMSaEkNTCshBhFH0gIpNFZLmILC8pKYmJcYQQ4mYojAkhJLUoBtDa8L4AwC67PiKSBiAPwAHzhpRSTyul+iql+jZrxnAIQkjqQ2FMCCGpxdcAOolIOxHJADARwFxTn7kArtReTwCwsC7iiwkhJNlgjDEhhKQQWszwFADzAXgB/FsptUFE7gOwXCk1F8C/ALwoIlvg8xRPTJzFhBDiHiiMCSEkxVBKzQMwz9R2j+H1MQCXxNsuQghxOwylIIQQQgghBBTGhBBCCCGEAKAwJoQQQgghBACFMSGEEEIIIQAojAkhhBBCCAFAYUwIIYQQQggACmNCCCGEEEIAUBgTQgghhBACAJBEVQEVkRIA26JYtSmAH2NsTl2QLHYCtLUuSBY7geSx1W12tlVKNUu0EfHCwTXbbeenNqTSWIDUGk8qjQVIrfG4fSyOrtkJE8bRIiLLlVJ9E21HOJLFToC21gXJYieQPLYmi50nKql0flJpLEBqjSeVxgKk1nhSZSwMpSCEEEIIIQQUxoQQQgghhABITmH8dKINcEiy2AnQ1rogWewEksfWZLHzRCWVzk8qjQVIrfGk0liA1BpPSowl6WKMCSGEEEIIqQuS0WNMCCGEEEJIzEkaYSwio0Vks4hsEZGpcd53kYisE5HVIrJca2ssIgtE5Dvt/0Zau4jIPzQ714pIb8N2rtT6fyciVxra+2jb36KtKw7t+reI7BOR9Ya2OrfLbh9R2HqviOzUjutqERlrWDZN2+9mERllaLf8HIhIOxFZqtn0iohkaO2Z2vst2vLCMHa2FpFPRGSjiGwQkVvcelxD2Oqq4yoiWSKyTETWaHb+Odptx8p+Ejvsjr3biMd3OwFj8orIKhF5V3sfs+9UnMfRUEReF5FN2vkZlOTn5TbtM7ZeRGZr18CkOTeSIG3hGpRSrv8D4AWwFUB7ABkA1gDoGsf9FwFoamr7K4Cp2uupAP6ivR4L4H0AAmAggKVae2MA32v/N9JeN9KWLQMwSFvnfQBjHNo1FEBvAOvjaZfdPqKw9V4Av7fo21U7x5kA2mnn3hvqcwDgVQATtdf/BHCD9vq3AP6pvZ4I4JUwdrYA0Ft7XR/At5o9rjuuIWx11XHVxpmrvU4HsFQ7VhFtO5b28y9m18aEXpsjtLXOv9sJGNPtAF4G8K72PibfqQSM43kA12mvMwA0TNbzAqAVgB8A1DOck6uS6dwgQdrCLX8JN8DhSRoEYL7h/TQA0+K4/yIEC+PNAFpor1sA2Ky9fgrApeZ+AC4F8JSh/SmtrQWATYb2gH4ObCs0fXjr3C67fURh672wFnAB5xfAfO0zYPk50L5cPwJIM39e9HW112laP4ng+L4D4Bw3H1cLW117XAFkA1gJYECk246l/fyLzZ/dsU+0XQ5tj+l3OwH2FwD4GMBZAN6N5vtq952K8zgawCckxdSerOelFYAd8AnCNO3cjEq2c4MEaAu3/CVLKIX+QdMp1trihQLwoYisEJHJWltzpdRuAND+z9fa7WwN1V5s0R4t8bDLbh/RMEV7/PJvqQkdiNTWJgAOKaUqLWz1r6Mt/0nrHxbtkVYv+Dycrj6uJlsBlx1X7ZHvagD7ACyAz/sR6bZjaT+JDYm+NkdFHX23481jAP4AoFp7H8331Q1jaQ+gBMBzWljIsyKSgyQ9L0qpnQAeAbAdwG74jvUKJOe5MeJWzRNzkkUYW8WfqDjuf7BSqjeAMQBuFJGhIfra2Rppe6xxo12zAHQA0BO+C8jftPZY2hrVOEQkF8AbAG5VSv0cqmuENsX8uFrY6rrjqpSqUkr1hM/L1R/AKVFsO+HHmgSRdMe4Dr/bcUNEzgWwTym1wths0TXa71Q8SYPvsf0spVQvAEfge1Rvh5vHAs0RcQF84Q8tAeTApx3MJMO5cULKXZeTRRgXA2hteF8AYFe8dq6U2qX9vw/AW/D9sO8VkRYAoP2/L4ytodoLLNqjJR522e0jIpRSezXBVA3gGfiOazS2/gigoYikWdjqX0dbngfgQCi7RCQdvh/Ol5RSb2rNrjyuVra69bhqth0C8Cl8sWiRbjuW9pPYkNBrc6TU8Xc7ngwGcL6IFAGYA184xWOI3XcqnhQDKFZK6U+7XodPKCfjeQGAEQB+UEqVKKUqALwJ4HQk57kx4lbNE3OSRRh/DaCTNqszA74A9bnx2LGI5IhIff01gJEA1mv7v1LrdiV88WrQ2q/QZmoOBPCT9thhPoCRItJIu6McCV+M0W4ApSIyUJuZeYVhW9EQD7vs9hER+pdM4yL4jqu+/YnabN12ADrBF6xv+TlQvkClTwBMsBm3busEAAu1/nY2CYB/AdiolHrUsMh1x9XOVrcdVxFpJiINtdf14Pvh2BjFtmNpP4kNCbs2R0pdf7fjMggNpdQ0pVSBUqoQvmO+UCk1CbH7TsUNpdQeADtEpLPWdDaAb5CE50VjO4CBIpKtfeb08STduTHhVs0TexId5Oz0D76Zj9/CF5t4dxz32x6+maFrAGzQ9w1fDNDHAL7T/m+stQuAmZqd6wD0NWzrGgBbtL+rDe194RMvWwE8AYeTwwDMhu9ReQV8d2HXxsMuu31EYeuLmi1r4ftytTD0v1vb72YYZqzafQ6087RMG8NrADK19izt/RZtefswdg6B77HOWgCrtb+xbjyuIWx11XEF0APAKs2e9QDuiXbbsbKff7H7szv2bvuLx3c7QeMahpqsFDH7TsV5DD0BLNfOzdvwZTFI2vMC4M8ANmnXuxfhyyyRNOcGCdIWbvlj5TtCCCGEEEKQPKEUhBBCCCGE1CkUxoQQQgghhIDCmBBCCCGEEAAUxoQQQgghhACgMCaEEEIIIQQAhTFxCSJyWPu/UEQui/G27zK9XxLL7RNCyIkGr9kkVaEwJm6jEEBEF1kR8YbpEnCRVUqdHqFNhBBCrCkEr9kkhaAwJm5jBoAzRGS1iNwmIl4ReVhEvhaRtSJyPQCIyDAR+UREXoYvqThE5G0RWSEiG0RkstY2A0A9bXsvaW26p0O0ba8XkXUi8kvDtj8VkddFZJOIvKRV6CGEEBIIr9kkpUgL34WQuDIVwO+VUucCgHax/Ekp1U9EMgF8ISIfan37AzhVKfWD9v4apdQBrezw1yLyhlJqqohMUUr1tNjXxfBVXDoNQFNtnUXasl4AusFXw/0LAIMBLI79cAkhJKnhNZukFPQYE7czEr467KsBLIWvLGUnbdkywwUWAG4WkTUAvgLQ2tDPjiEAZiulqpRSewF8BqCfYdvFSqlq+ErHFsZkNIQQktrwmk2SGnqMidsRADcppeYHNIoMA3DE9H4EgEFKqTIR+RS+GvThtm3HccPrKvC7QgghTuA1myQ19BgTt1EKoL7h/XwAN4hIOgCIyMkikmOxXh6Ag9oFtguAgYZlFfr6JhYB+KUWE9cMwFAAy2IyCkIIOTHgNZukFLyjIm5jLYBK7fHafwD8Hb5HYiu1yRQlAC60WO8DAL8RkbUANsP3aE7naQBrRWSlUmqSof0tAIMArAGgAPxBKbVHu0gTQggJD6/ZJKUQpVSibSCEEEIIISThMJSCEEIIIYQQUBgTQgghhBACgMKYEEIIIYQQABTGhBBCCCGEAKAwJoQQQgghBACFMSGEEEIIIQAojAkhhBBCCAFAYUwIIYQQQggA4P8DeGYDorCUhP4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 864x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["eval_num = 250\n", "plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Iteration Average Loss\")\n", "x = [eval_num * (i + 1) for i in range(len(net.epoch_loss_values))]\n", "y = net.epoch_loss_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val Mean Dice\")\n", "x = [eval_num * (i + 1) for i in range(len(net.metric_values))]\n", "y = net.metric_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check best model output with the input image and label"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["slice_map = {\n", "    \"img0035.nii.gz\": 170,\n", "    \"img0036.nii.gz\": 230,\n", "    \"img0037.nii.gz\": 204,\n", "    \"img0038.nii.gz\": 204,\n", "    \"img0039.nii.gz\": 204,\n", "    \"img0040.nii.gz\": 180,\n", "}\n", "case_num = 4\n", "net.load_from_checkpoint(os.path.join(root_dir, \"best_metric_model-v1.ckpt\"))\n", "net.eval()\n", "net.to(device)\n", "\n", "with torch.no_grad():\n", "    img_name = os.path.split(net.val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n", "    img = net.val_ds[case_num][\"image\"]\n", "    label = net.val_ds[case_num][\"label\"]\n", "    val_inputs = torch.unsqueeze(img, 1).cuda()\n", "    val_labels = torch.unsqueeze(label, 1).cuda()\n", "    val_outputs = sliding_window_inference(val_inputs, (96, 96, 96), 4, net, overlap=0.8)\n", "    plt.figure(\"check\", (18, 6))\n", "    plt.subplot(1, 3, 1)\n", "    plt.title(\"image\")\n", "    plt.imshow(val_inputs.cpu().numpy()[0, 0, :, :, slice_map[img_name]], cmap=\"gray\")\n", "    plt.subplot(1, 3, 2)\n", "    plt.title(\"label\")\n", "    plt.imshow(val_labels.cpu().numpy()[0, 0, :, :, slice_map[img_name]])\n", "    plt.subplot(1, 3, 3)\n", "    plt.title(\"output\")\n", "    plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, slice_map[img_name]])\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}