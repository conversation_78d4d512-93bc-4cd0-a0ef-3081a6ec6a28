name: Test notebooks

on: [push, pull_request]

jobs:
  nbmake:
    runs-on: ${{ matrix.os }}
    strategy:
      max-parallel: 3
      matrix:
        os: [ubuntu-22.04, macos-12]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.9'
      - name: Install build dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install -r ./.binder/requirements.txt
          python -m pip install pytest nbmake
      - name: Test notebooks
        shell: bash
        run: |
          pytest --nbmake --nbmake-timeout=3000 examples/*.ipynb
