{"cells": [{"cell_type": "markdown", "metadata": {"id": "2UwQsv99wCMB"}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# Spleen 3D segmentation with MONAI\n", "\n", "This tutorial shows how to integrate MONAI into an existing PyTorch medical DL program.\n", "\n", "And easily use below features:\n", "1. Transforms for dictionary format data.\n", "1. Load Nifti image with metadata.\n", "1. Add channel dim to the data if no channel dimension.\n", "1. Scale medical image intensity with expected range.\n", "1. <PERSON><PERSON> out a batch of balanced images based on positive / negative label ratio.\n", "1. <PERSON><PERSON> and transforms to accelerate training and validation.\n", "1. 3D UNet model, Dice loss function, Mean Dice metric for 3D segmentation task.\n", "1. Sliding window inference method.\n", "1. Deterministic training for reproducibility.\n", "\n", "The Spleen dataset can be downloaded from http://medicaldecathlon.com/.\n", "\n", "![spleen](http://medicaldecathlon.com/img/spleen0.png)\n", "\n", "Target: Spleen  \n", "Modality: CT  \n", "Size: 61 3D volumes (41 Training + 20 Testing)  \n", "Source: Memorial Sloan Kettering Cancer Center  \n", "Challenge: Large ranging foreground size\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/spleen_segmentation_3d.ipynb)"]}, {"cell_type": "markdown", "metadata": {"id": "kSmTplwYwCMF"}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DxWItTn7wCMG", "outputId": "18013027-e54a-4839-99db-26b28ecf22c6"}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[gdown, nibabel, tqdm, ignite]\"\n", "!python -c \"import aim\" || pip install -q aim\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZejZZonxwCMH", "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.0+2.g97918e46\n", "Numpy version: 1.22.2\n", "Pytorch version: 1.13.0a0+d0d6b1f\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 97918e46e0d2700c050e678d72e3edb35afbd737\n", "MONAI __file__: /workspace/monai/monai-in-dev/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.0.1\n", "Tensorboard version: 2.10.1\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.0a0\n", "tqdm version: 4.64.1\n", "lmdb version: 1.3.0\n", "psutil version: 5.9.2\n", "pandas version: 1.4.4\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.0.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["from monai.utils import first, set_determinism\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    EnsureChannelFirstd,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    RandCropByPosNegLabeld,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", "    Invertd,\n", ")\n", "from monai.handlers.utils import from_engine\n", "from monai.networks.nets import UNet\n", "from monai.networks.layers import Norm\n", "from monai.metrics import DiceMetric\n", "from monai.losses import DiceLoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.data import CacheDataset, DataLoader, Dataset, decollate_batch\n", "from monai.config import print_config\n", "from monai.apps import download_and_extract\n", "import aim\n", "from aim.pytorch import track_gradients_dists, track_params_dists\n", "import torch\n", "import matplotlib.pyplot as plt\n", "import tempfile\n", "import shutil\n", "import os\n", "import glob\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {"id": "OvWUJiLUwCMJ"}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vaeW5JrmwCMK", "outputId": "e7148532-53e7-430a-a68b-88854343f6ca", "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/workspace/data\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {"id": "NW2wdPO7wCMN"}, "source": ["## Download dataset\n", "\n", "Downloads and extracts the dataset.  \n", "The dataset comes from http://medicaldecathlon.com/."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1_4y2EtCwCMN", "outputId": "2a874ea6-b270-4032-b072-268082a9e651", "scrolled": true, "tags": []}, "outputs": [], "source": ["resource = \"https://msd-for-monai.s3-us-west-2.amazonaws.com/Task09_Spleen.tar\"\n", "md5 = \"410d4a301da4e5b2f6f86ec3ddba524e\"\n", "\n", "compressed_file = os.path.join(root_dir, \"Task09_Spleen.tar\")\n", "data_dir = os.path.join(root_dir, \"Task09_Spleen\")\n", "if not os.path.exists(data_dir):\n", "    download_and_extract(resource, compressed_file, root_dir, md5)"]}, {"cell_type": "markdown", "metadata": {"id": "w3EPRPqBwCMN"}, "source": ["## Set MSD Spleen dataset path"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lVZr7-kBwCMO"}, "outputs": [], "source": ["train_images = sorted(glob.glob(os.path.join(data_dir, \"imagesTr\", \"*.nii.gz\")))\n", "train_labels = sorted(glob.glob(os.path.join(data_dir, \"labelsTr\", \"*.nii.gz\")))\n", "data_dicts = [{\"image\": image_name, \"label\": label_name} for image_name, label_name in zip(train_images, train_labels)]\n", "train_files, val_files = data_dicts[:-9], data_dicts[-9:]"]}, {"cell_type": "markdown", "metadata": {"id": "Q1Wi6EtAwCMO"}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dr8HRsffwCMO"}, "outputs": [], "source": ["set_determinism(seed=0)"]}, {"cell_type": "markdown", "metadata": {"id": "13ZnlKGCwCMO"}, "source": ["## Setup transforms for training and validation\n", "\n", "Here we use several transforms to augment the dataset:\n", "1. `LoadImaged` loads the spleen CT images and labels from NIfTI format files.\n", "1. `EnsureChannelFirstd` ensures the original data to construct \"channel first\" shape.\n", "1. `Spacingd` adjusts the spacing by `pixdim=(1.5, 1.5, 2.)` based on the affine matrix.\n", "1. `Orientationd` unifies the data orientation based on the affine matrix.\n", "1. `ScaleIntensityRanged` extracts intensity range [-57, 164] and scales to [0, 1].\n", "1. `CropForegroundd` removes all zero borders to focus on the valid body area of the images and labels.\n", "1. `RandCropByPosNegLabeld` randomly crop patch samples from big image based on pos / neg ratio.  \n", "The image centers of negative samples must be in valid body area.\n", "1. `RandAffined` efficiently performs `rotate`, `scale`, `shear`, `translate`, etc. together based on PyTorch affine transform.\n", "1. `EnsureTyped` converts the numpy array to PyTorch Tensor for further steps."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jf7siKPOwCMO"}, "outputs": [], "source": ["train_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-57,\n", "            a_max=164,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(keys=[\"image\", \"label\"], pixdim=(1.5, 1.5, 2.0), mode=(\"bilinear\", \"nearest\")),\n", "        RandCropByPosNegLabeld(\n", "            keys=[\"image\", \"label\"],\n", "            label_key=\"label\",\n", "            spatial_size=(96, 96, 96),\n", "            pos=1,\n", "            neg=1,\n", "            num_samples=4,\n", "            image_key=\"image\",\n", "            image_threshold=0,\n", "        ),\n", "        # user can also add other random transforms\n", "        # RandAffined(\n", "        #     keys=['image', 'label'],\n", "        #     mode=('bilinear', 'nearest'),\n", "        #     prob=1.0, spatial_size=(96, 96, 96),\n", "        #     rotate_range=(0, 0, np.pi/15),\n", "        #     scale_range=(0.1, 0.1, 0.1)),\n", "    ]\n", ")\n", "val_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-57,\n", "            a_max=164,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(keys=[\"image\", \"label\"], pixdim=(1.5, 1.5, 2.0), mode=(\"bilinear\", \"nearest\")),\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "au4rmQfDwCMP"}, "source": ["## Check transforms in DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 408}, "id": "qqcFPuVkwCMP", "outputId": "4189428e-4569-4453-e379-df4466208c85", "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image shape: torch.<PERSON><PERSON>([228, 158, 113]), label shape: torch.<PERSON><PERSON>([228, 158, 113])\n"]}, {"data": {"image/png": "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*******************************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***************************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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["check_ds = Dataset(data=val_files, transform=val_transforms)\n", "check_loader = DataLoader(check_ds, batch_size=1)\n", "check_data = first(check_loader)\n", "image, label = (check_data[\"image\"][0][0], check_data[\"label\"][0][0])\n", "print(f\"image shape: {image.shape}, label shape: {label.shape}\")\n", "# plot the slice [:, :, 80]\n", "plt.figure(\"check\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"image\")\n", "plt.imshow(image[:, :, 80], cmap=\"gray\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"label\")\n", "plt.imshow(label[:, :, 80])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "D0_EHJ7FwCMQ"}, "source": ["## Define CacheDataset and DataLoader for training and validation\n", "\n", "Here we use CacheDataset to accelerate training and validation process, it's 10x faster than the regular Dataset.  \n", "To achieve best performance, set `cache_rate=1.0` to cache all the data, if memory is not enough, set lower value.  \n", "Users can also set `cache_num` instead of `cache_rate`, will use the minimum value of the 2 settings.  \n", "And set `num_workers` to enable multi-threads during caching.  \n", "If want to to try the regular Dataset, just change to use the commented code below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kKA4gboPwCMQ", "outputId": "2496df99-8445-4c70-a3b1-721f9e552b34", "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 32/32 [00:20<00:00,  1.55it/s]\n", "Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 9/9 [00:04<00:00,  2.10it/s]\n"]}], "source": ["train_ds = CacheDataset(data=train_files, transform=train_transforms, cache_rate=1.0, num_workers=4)\n", "# train_ds = monai.data.Dataset(data=train_files, transform=train_transforms)\n", "\n", "# use batch_size=2 to load images and use RandCropByPosNegLabeld\n", "# to generate 2 x 4 images for network training\n", "train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=4)\n", "\n", "val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_rate=1.0, num_workers=4)\n", "# val_ds = Dataset(data=val_files, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=1, num_workers=4)"]}, {"cell_type": "markdown", "metadata": {"id": "nOgy1x1BwCMQ"}, "source": ["## Create Model, Loss, Optimizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VM-5g2bmwCMQ"}, "outputs": [], "source": ["# standard PyTorch program style: create UNet, DiceLoss and Adam optimizer\n", "device = torch.device(\"cuda:0\")\n", "\n", "UNet_meatdata = {\n", "    \"spatial_dims\": 3,\n", "    \"in_channels\": 1,\n", "    \"out_channels\": 2,\n", "    \"channels\": (16, 32, 64, 128, 256),\n", "    \"strides\": (2, 2, 2, 2),\n", "    \"num_res_units\": 2,\n", "    \"norm\": Norm.BATCH,\n", "}\n", "\n", "model = UNet(**UNet_meatdata).to(device)\n", "loss_function = DiceLoss(to_onehot_y=True, softmax=True)\n", "loss_type = \"DiceLoss\"\n", "optimizer = torch.optim.Adam(model.parameters(), 1e-4)\n", "dice_metric = DiceMetric(include_background=False, reduction=\"mean\")\n", "\n", "Optimizer_metadata = {}\n", "for ind, param_group in enumerate(optimizer.param_groups):\n", "    optim_meta_keys = list(param_group.keys())\n", "    Optimizer_metadata[f\"param_group_{ind}\"] = {\n", "        key: value for (key, value) in param_group.items() if \"params\" not in key\n", "    }"]}, {"cell_type": "markdown", "metadata": {"id": "4nD1pAY-wCMR"}, "source": ["## Execute a typical PyTorch training process"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KayxFseYwCMR", "scrolled": true, "tags": []}, "outputs": [], "source": ["max_epochs = 600\n", "val_interval = 10\n", "best_metric = -1\n", "best_metric_epoch = -1\n", "epoch_loss_values = []\n", "metric_values = []\n", "post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])\n", "post_label = Compose([AsDiscrete(to_onehot=2)])\n", "\n", "# initialize a new Aim Run\n", "aim_run = aim.Run()\n", "# log model metadata\n", "aim_run[\"UNet_meatdata\"] = UNet_meatdata\n", "# log optimizer metadata\n", "aim_run[\"Optimizer_metadata\"] = Optimizer_metadata\n", "\n", "slice_to_track = 80\n", "\n", "for epoch in range(max_epochs):\n", "    print(\"-\" * 10)\n", "    print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    for batch_data in train_loader:\n", "        step += 1\n", "        inputs, labels = (\n", "            batch_data[\"image\"].to(device),\n", "            batch_data[\"label\"].to(device),\n", "        )\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = loss_function(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "        print(f\"{step}/{len(train_ds) // train_loader.batch_size}, \" f\"train_loss: {loss.item():.4f}\")\n", "        # track batch loss metric\n", "        aim_run.track(loss.item(), name=\"batch_loss\", context={\"type\": loss_type})\n", "\n", "    epoch_loss /= step\n", "    epoch_loss_values.append(epoch_loss)\n", "\n", "    # track epoch loss metric\n", "    aim_run.track(epoch_loss, name=\"epoch_loss\", context={\"type\": loss_type})\n", "\n", "    print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        if (epoch + 1) % val_interval * 2 == 0:\n", "            # track model params and gradients\n", "            track_params_dists(model, aim_run)\n", "            # THIS SEGMENT TAKES RELATIVELY LONG (Advise Against it)\n", "            track_gradients_dists(model, aim_run)\n", "\n", "        model.eval()\n", "        with torch.no_grad():\n", "            for index, val_data in enumerate(val_loader):\n", "                val_inputs, val_labels = (\n", "                    val_data[\"image\"].to(device),\n", "                    val_data[\"label\"].to(device),\n", "                )\n", "                roi_size = (160, 160, 160)\n", "                sw_batch_size = 4\n", "                val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)\n", "\n", "                # tracking input, label and output images with Aim\n", "                output = torch.argmax(val_outputs, dim=1)[0, :, :, slice_to_track].float()\n", "\n", "                aim_run.track(\n", "                    aim.Image(val_inputs[0, 0, :, :, slice_to_track], caption=f\"Input Image: {index}\"),\n", "                    name=\"validation\",\n", "                    context={\"type\": \"input\"},\n", "                )\n", "                aim_run.track(\n", "                    aim.Image(val_labels[0, 0, :, :, slice_to_track], caption=f\"Label Image: {index}\"),\n", "                    name=\"validation\",\n", "                    context={\"type\": \"label\"},\n", "                )\n", "                aim_run.track(\n", "                    aim.Image(output, caption=f\"Predicted Label: {index}\"),\n", "                    name=\"predictions\",\n", "                    context={\"type\": \"labels\"},\n", "                )\n", "\n", "                val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]\n", "                val_labels = [post_label(i) for i in decollate_batch(val_labels)]\n", "                # compute metric for current iteration\n", "                dice_metric(y_pred=val_outputs, y=val_labels)\n", "\n", "            # aggregate the final mean dice result\n", "            metric = dice_metric.aggregate().item()\n", "            # track val metric\n", "            aim_run.track(metric, name=\"val_metric\", context={\"type\": loss_type})\n", "\n", "            # reset the status for next validation round\n", "            dice_metric.reset()\n", "\n", "            metric_values.append(metric)\n", "            if metric > best_metric:\n", "                best_metric = metric\n", "                best_metric_epoch = epoch + 1\n", "                torch.save(model.state_dict(), os.path.join(root_dir, \"best_metric_model.pth\"))\n", "\n", "                best_model_log_message = f\"saved new best metric model at the {epoch+1}th epoch\"\n", "                aim_run.track(aim.Text(best_model_log_message), name=\"best_model_log_message\", epoch=epoch + 1)\n", "                print(best_model_log_message)\n", "\n", "            message1 = f\"current epoch: {epoch + 1} current mean dice: {metric:.4f}\"\n", "            message2 = f\"\\nbest mean dice: {best_metric:.4f} \"\n", "            message3 = f\"at epoch: {best_metric_epoch}\"\n", "\n", "            aim_run.track(aim.Text(message1 + \"\\n\" + message2 + message3), name=\"epoch_summary\", epoch=epoch + 1)\n", "            print(message1, message2, message3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "d1WMn7DFKkbV"}, "outputs": [], "source": ["# finalize Aim Run\n", "aim_run.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ygo9hrWswCMR", "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train completed, best_metric: 0.9505 at epoch: 580\n"]}], "source": ["print(f\"train completed, best_metric: {best_metric:.4f} \" f\"at epoch: {best_metric_epoch}\")"]}, {"cell_type": "markdown", "metadata": {"id": "90la_Hq8wCMR"}, "source": ["## Run Aim UI to deeply explore tracked insights\n", "\n", "NOTE: Aim UI may require port 53800 exposure. For more details, please refer to the [AIM documentation](https://aimstack.readthedocs.io/en/latest/quick_start/setup.html#running-aim-ui-and-tracking-server-inside-docker-container)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jGhCvBg-wCMS"}, "outputs": [], "source": ["%load_ext aim\n", "%aim up"]}, {"cell_type": "markdown", "metadata": {"id": "zV7fV0CIwCMS"}, "source": ["Once the above cell is executed, you will see the Aim UI running in output cell\n", "\n", "![Aim UI](https://user-images.githubusercontent.com/13848158/156644374-ba04963f-4f63-4fb9-b3ef-4d4e1ae521cc.jpg)"]}, {"cell_type": "markdown", "metadata": {"id": "KRw5pgLiwCMS"}, "source": ["## Explore the loss and metric"]}, {"cell_type": "markdown", "metadata": {"id": "MeX1wBjXwCMS"}, "source": ["Compare metrics curves with Metrics Explorer - group and aggregate by any hyperparameter to easily compare training runs\n", "\n", "![Metrics Explorer](https://user-images.githubusercontent.com/13848158/156642623-8cf4911d-bed2-42b8-9f39-374f8d31def8.jpg)\n"]}, {"cell_type": "markdown", "metadata": {"id": "zGZ5vozGwCMS"}, "source": ["## Compare and analyze model outputs"]}, {"cell_type": "markdown", "metadata": {"id": "mZIUa0aNwCMS"}, "source": ["Compare models of different runs with input images and labels\n", "\n", "![Images Explorer](https://user-images.githubusercontent.com/13848158/156642615-c003fb3c-9f37-40f4-b499-ee6623db59ef.jpg)\n", "\n", "![Images Explorer](https://user-images.githubusercontent.com/13848158/156642618-0c0c380a-75aa-45b1-b431-149f735b3fde.jpg)"]}, {"cell_type": "markdown", "metadata": {"id": "uZKhs2DFwCMS"}, "source": ["## Evaluation on original image spacings"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Ws5wpqPlwCMT"}, "outputs": [], "source": ["val_org_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        Spacingd(keys=[\"image\"], pixdim=(1.5, 1.5, 2.0), mode=\"bilinear\"),\n", "        Orientationd(keys=[\"image\"], axcodes=\"RAS\"),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-57,\n", "            a_max=164,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        CropForegroundd(keys=[\"image\"], source_key=\"image\"),\n", "    ]\n", ")\n", "\n", "val_org_ds = Dataset(data=val_files, transform=val_org_transforms)\n", "val_org_loader = DataLoader(val_org_ds, batch_size=1, num_workers=4)\n", "\n", "post_transforms = Compose(\n", "    [\n", "        Invertd(\n", "            keys=\"pred\",\n", "            transform=val_org_transforms,\n", "            orig_keys=\"image\",\n", "            meta_keys=\"pred_meta_dict\",\n", "            orig_meta_keys=\"image_meta_dict\",\n", "            meta_key_postfix=\"meta_dict\",\n", "            nearest_interp=False,\n", "            to_tensor=True,\n", "        ),\n", "        AsDiscreted(keys=\"pred\", argmax=True, to_onehot=2),\n", "        AsDiscreted(keys=\"label\", to_onehot=2),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JTkKUwRGwCMT"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metric on original image spacing:  0.9597647190093994\n"]}], "source": ["model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "\n", "with torch.no_grad():\n", "    for val_data in val_org_loader:\n", "        val_data[\"image\"] = val_data[\"image\"].to(device)\n", "        val_data[\"label\"] = val_data[\"label\"].to(device)\n", "        roi_size = (160, 160, 160)\n", "        sw_batch_size = 4\n", "        val_data[\"pred\"] = sliding_window_inference(val_data[\"image\"], roi_size, sw_batch_size, model)\n", "        val_data = [post_transforms(i) for i in decollate_batch(val_data)]\n", "        val_outputs, val_labels = from_engine([\"pred\", \"label\"])(val_data)\n", "        # compute metric for current iteration\n", "        dice_metric(y_pred=val_outputs, y=val_labels)\n", "\n", "    # aggregate the final mean dice result\n", "    metric_org = dice_metric.aggregate().item()\n", "    # reset the status for next validation round\n", "    dice_metric.reset()\n", "\n", "print(\"Metric on original image spacing: \", metric_org)"]}, {"cell_type": "markdown", "metadata": {"id": "chILRaduwCMT"}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yuCFCxOcwCMT"}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "spleen_segmentation_3d_visualization.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 4}