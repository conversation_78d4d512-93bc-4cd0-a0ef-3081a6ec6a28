#%% 根据nnunet预测后的summary.json文件，批量获取Dice和IoU值
import json
import pandas as pd
import os

# 读取JSON文件
json_path = r"j:\nnUNet\nnUNet_results\Dataset503_ap\nnUNetTrainer__nnUNetPlans__3d_fullres\fold_0\validation\summary.json"

# 读取JSON文件
with open(json_path, 'r') as f:
    data = json.load(f)

# 创建结果字典
results = {
    'Filename': [],
    'Dice': [],
    'IoU': [],
    'TP': [],
    'TN': [],
    'FP': [],
    'FN': []
}

# 遍历metric_per_case中的每个病例
if 'metric_per_case' in data:
    for case in data['metric_per_case']:
        if isinstance(case, dict) and 'prediction_file' in case:
            # 获取文件名
            pred_file = os.path.basename(case['prediction_file'])
            if pred_file.endswith('.nii.gz'):
                try:
                    # 获取指标
                    metrics = case['metrics']['1']
                    
                    # 添加到结果中
                    results['Filename'].append(pred_file)
                    results['Dice'].append(round(metrics['Dice'], 3))
                    results['IoU'].append(round(metrics['IoU'], 3))
                    results['TP'].append(round(metrics['TP'], 3))
                    results['TN'].append(round(metrics['TN'], 3))
                    results['FP'].append(round(metrics['FP'], 3))
                    results['FN'].append(round(metrics['FN'], 3))
                except Exception as e:
                    print(f"Error processing file {pred_file}: {str(e)}")

# 创建DataFrame
df = pd.DataFrame(results)

# 按Dice值降序排序
df = df.sort_values(by='Dice', ascending=False)

# 保存到Excel文件
output_path = os.path.join(os.path.dirname(json_path), 'dice_iou.xlsx')
df.to_excel(output_path, index=False)

# 打印结果
print(f"\nResults saved to: {output_path}")
print("\nResults (sorted by Dice score):")
print(df)

if not df.empty:
    # 打印统计信息
    print("\nSummary Statistics:")
    print(f"Average Dice: {df['Dice'].mean():.3f}")
    print(f"Median Dice: {df['Dice'].median():.3f}")
    print(f"Min Dice: {df['Dice'].min():.3f}")
    print(f"Max Dice: {df['Dice'].max():.3f}")
    print(f"Total number of cases: {len(df)}")
else:
    print("\nNo data was processed. Please check if the JSON structure is correct.")
    
# 如果数据为空，打印更多调试信息
    if 'metric_per_case' in data:
        print("\nDebug info:")
        print(f"Number of cases in metric_per_case: {len(data['metric_per_case'])}")
        if len(data['metric_per_case']) > 0:
            print("First case structure:")
            print(json.dumps(data['metric_per_case'][0], indent=2))
# %%
