{"cells": [{"cell_type": "markdown", "id": "b974cf01-3d75-4e1b-99d6-bba8b738c92e", "metadata": {}, "source": ["### 17. MONAI deep learning registration with affine pre-alignment using itk-elastix"]}, {"cell_type": "markdown", "id": "54fad620-2c6a-4272-8f28-8a6fed7a66e2", "metadata": {}, "source": ["A global (rigid/affine) registration is often used as a pre-processing step for deep learning applications. This notebook shows how to apply affine pre-alignment using Elastix before local (non-linear) deep learning registration via project MONAI. The pre-aligning registration is calculated only once, and then it is cached allowing for a faster training."]}, {"cell_type": "code", "execution_count": 1, "id": "ec508d4e-4464-462c-adbb-8e2272268ad6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Device:  cuda:0\n"]}], "source": ["import os\n", "import itk\n", "import torch\n", "import numpy as np\n", "from tqdm import tqdm\n", "import matplotlib.pyplot as plt\n", "\n", "from monai.utils import set_determinism\n", "from monai.transforms import MapTransform, Compose, LoadImaged, EnsureChannelFirstD, ScaleIntensityRanged\n", "from monai.data import CacheDataset, ITKReader, DataLoader\n", "from monai.config import print_config, USE_COMPILED\n", "from monai.networks.nets import GlobalNet, LocalNet\n", "from monai.networks.blocks import Warp\n", "from monai.apps import MedNISTDataset\n", "from monai.losses import GlobalMutualInformationLoss, BendingEnergyLoss\n", "\n", "set_determinism(42)\n", "\n", "if torch.cuda.is_available():\n", "    device = \"cuda:0\"\n", "    max_epochs = 500\n", "else:\n", "    device = \"cpu\"\n", "    max_epochs=3\n", "\n", "print(\"Device: \", device)"]}, {"cell_type": "markdown", "id": "8bf43d93-a977-4de6-bf94-31e3b642fe2c", "metadata": {}, "source": ["### Dataset"]}, {"cell_type": "markdown", "id": "b6a4ae54-c2dc-4413-a68a-cb4171822aab", "metadata": {}, "source": ["After downloading the MedNISTDataset, we keep only the x-ray hand images dataset and create random pairs of images (fixed and moving) to register."]}, {"cell_type": "code", "execution_count": 2, "id": "624e35ad-50c0-45bf-832a-fc78b784df6e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-10 13:26:15,909 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-01-10 13:26:15,909 - INFO - File exists: MedNIST.tar.gz, skipped downloading.\n", "2023-01-10 13:26:15,909 - INFO - Non-empty folder exists in MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:00<00:00, 137391.99it/s]\n"]}], "source": ["# Download MedNISTDataset\n", "root_dir = './'\n", "train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, transform=None)\n", "\n", "# Keep only the hand x-rays\n", "hands = [os.path.join(root_dir, item[\"image\"]) for item in train_data.data if item[\"label\"]==4] # label 4 is for xray hands\n", "hands = hands[:1000] # use only a subset of the whole data\n", "\n", "# Create a dictionary with random pairs of fixed-moving images \n", "training_datadict = [\n", "    {\"fixed_hand\": fixed_hand, \"moving_hand\": moving_hand}\n", "    for fixed_hand, moving_hand in np.random.choice(hands, size=(len(hands)//2, 2), replace=False)\n", "]"]}, {"cell_type": "markdown", "id": "00cca67b-19d2-4b3f-a927-e974fa9a7be0", "metadata": {}, "source": ["### Elastix affine parameter map"]}, {"cell_type": "code", "execution_count": 3, "id": "6aa50c9b-9929-4986-a59a-c063559286cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AutomaticParameterEstimation ('true',)\n", "AutomaticScalesEstimation ('true',)\n", "CheckNumberOfSamples ('true',)\n", "DefaultPixelValue ('0',)\n", "FinalBSplineInterpolationOrder ('3',)\n", "FixedImagePyramid ('FixedSmoothingImagePyramid',)\n", "ImageSampler ('RandomCoordinate',)\n", "Interpolator ('LinearInterpolator',)\n", "MaximumNumberOfIterations ('64',)\n", "MaximumNumberOfSamplingAttempts ('8',)\n", "Metric ('AdvancedMattesMutualInformation',)\n", "MovingImagePyramid ('MovingSmoothingImagePyramid',)\n", "NewSamplesEveryIteration ('true',)\n", "NumberOfResolutions ('2',)\n", "NumberOfSamplesForExactGradient ('4096',)\n", "NumberOfSpatialSamples ('2048',)\n", "Optimizer ('AdaptiveStochasticGradientDescent',)\n", "Registration ('MultiResolutionRegistration',)\n", "ResampleInterpolator ('FinalBSplineInterpolator',)\n", "Resampler ('DefaultResampler',)\n", "ResultImageFormat ('nii',)\n", "Transform ('AffineTransform',)\n", "WriteIterationInfo ('false',)\n", "WriteResultImage ('true',)\n"]}], "source": ["# Elastix affine parameter map\n", "parameter_object = itk.ParameterObject()\n", "affine_parameter_map = parameter_object.GetDefaultParameterMap('affine')\n", "affine_parameter_map['NumberOfResolutions'] = \"2\"\n", "affine_parameter_map['MaximumNumberOfIterations'] = (\"64\", )\n", "parameter_object.AddParameterMap(affine_parameter_map)\n", "\n", "for key in affine_parameter_map.keys():\n", "    print(key, affine_parameter_map[key])"]}, {"cell_type": "markdown", "id": "3e7ee356-49cb-467b-b78a-e049e0faa59e", "metadata": {}, "source": ["### Pre-alignment steps"]}, {"cell_type": "markdown", "id": "61e60e16-7eec-419c-a8c3-c5c37c80bdc0", "metadata": {}, "source": ["The custom pre-processing is as follows: 1) Read the images using itk, 2) Register the pairs using Elastix, 3) Convert to numpy array. The rest of the steps are more general (add channel dimension and scale intensities). "]}, {"cell_type": "code", "execution_count": 4, "id": "791e2fcf-7af0-47a9-821b-e3a2e08300cf", "metadata": {}, "outputs": [], "source": ["# Pre-processing steps as MONAI transforms\n", "class ITKReadD(MapTransform):\n", "    def __init__(self, keys):\n", "        super().__init__(keys)\n", "        self.keys = keys\n", "\n", "    def __call__(self, data):\n", "        new_data = dict.fromkeys(self.keys)\n", "        for key in self.keys:\n", "            new_data[key] = itk.imread(data[key], itk.F)\n", "        return new_data\n", "\n", "\n", "class ElastixPreregistrationD(MapTransform):\n", "    def __init__(self, keys):\n", "        super().__init__(keys)\n", "        self.keys = keys\n", "\n", "    def __call__(self, data):\n", "        data[\"aligned_hand\"], result_transform_parameters = itk.elastix_registration_method(\n", "                                                                    data[self.keys[0]], data[self.keys[1]],\n", "                                                                    parameter_object=parameter_object,\n", "                                                                    log_to_console=False)\n", "        return data\n", "\n", "\n", "class ConvertToArrayD(MapTransform):\n", "    def __init__(self, keys):\n", "        super().__init__(keys)\n", "        self.keys = keys\n", "\n", "    def __call__(self, data):\n", "        for key in self.keys:\n", "            data[key] = itk.GetArrayFromImage(data[key]) \n", "        return data\n", "\n", "keys = [\"fixed_hand\", \"moving_hand\", \"aligned_hand\"]\n", "transforms = Compose([\n", "                        ITKReadD(keys=keys[:2]),\n", "                        ElastixPreregistrationD(keys=keys[:2]),\n", "                        ConvertToArrayD(keys=keys), \n", "                        EnsureChannelFirstD(keys=keys, channel_dim=\"no_channel\"),\n", "                        ScaleIntensityRanged(keys=keys, a_min=0., a_max=255., b_min=0.0, b_max=1.0, clip=True,),\n", "                     ])"]}, {"cell_type": "markdown", "id": "e5f15eda-7658-4985-9c7a-6df4e7ea0ff3", "metadata": {}, "source": ["### Perform pre-alignment, and store the calculations"]}, {"cell_type": "markdown", "id": "46d708a9-fff2-43b1-a87a-bda83769dd6e", "metadata": {}, "source": ["We are using CacheDataset from MONAI to conveniently perform the pre-alignment and store the result. Later during the training loop, directly the already aligned images will be loaded for training of the model."]}, {"cell_type": "code", "execution_count": 5, "id": "d6109397-b9a0-4691-86af-9ac831e98e0f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████| 500/500 [01:11<00:00,  6.98it/s]\n"]}], "source": ["dataset = CacheDataset(data=training_datadict, transform=transforms)\n", "dataloader = DataLoader(dataset=dataset, batch_size=16, shuffle=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "bf09527b-deaa-4f76-882f-d293ece25c5c", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1400x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Show examples of the pre-alignment\n", "fixed, moving, aligned = dataset[0].values()\n", "\n", "fig = plt.figure(figsize=(14, 6))\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(fixed.squeeze(), cmap='gray')\n", "plt.title('fixed', fontsize=10)\n", "plt.axis('off')\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(moving.squeeze(), cmap='gray')\n", "plt.title('original moving', fontsize=10)\n", "plt.axis('off')\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(aligned.squeeze(), cmap='gray')\n", "plt.title('moving after elastix pre-alignment', fontsize=10)\n", "plt.axis('off')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "724c078c-f4ef-4f2a-9c30-82425b6f730f", "metadata": {}, "source": ["### Deep learning registration model"]}, {"cell_type": "code", "execution_count": 7, "id": "c8a5932c-4b91-4108-b394-85188dabe176", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["monai.networks.blocks.Warp: Using PyTorch native grid_sample.\n"]}], "source": ["device = torch.device(device)\n", "    \n", "model = LocalNet(\n", "    spatial_dims=2,\n", "    in_channels=2,\n", "    out_channels=2,\n", "    num_channel_initial=16,\n", "    extract_levels=[0, 1, 2, 3],\n", "    out_activation=None,\n", "    out_kernel_initializer=\"zeros\").to(device)\n", "warp_layer = Warp(mode=\"bicubic\").to(device)\n", "criterion = GlobalMutualInformationLoss()\n", "regularization = BendingEnergyLoss()\n", "optimizer = torch.optim.Adam(model.parameters(), 1e-3)"]}, {"cell_type": "markdown", "id": "bae99718-54dc-48f2-8c62-edeaae8ecf82", "metadata": {}, "source": ["### Training loop"]}, {"cell_type": "code", "execution_count": 8, "id": "25cba48e-ce9e-4f17-8390-0dd0a26c749c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████| 500/500 [14:46<00:00,  1.77s/it]\n"]}], "source": ["epoch_loss_values = []\n", "\n", "for epoch in tqdm(range(max_epochs)):\n", "    model.train()\n", "    epoch_loss = 0\n", "    for i, batch_data in enumerate(dataloader):\n", "        optimizer.zero_grad()\n", "\n", "        fixed = batch_data[\"fixed_hand\"].to(device)\n", "        pre_aligned = batch_data[\"aligned_hand\"].to(device)\n", "        ddf = model(torch.cat((pre_aligned, fixed), dim=1))\n", "        pred_image = warp_layer(pre_aligned, ddf)\n", "\n", "        loss = criterion(pred_image, fixed) + 0.3*regularization(ddf)\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_loss += loss.item()*fixed.shape[0]\n", "\n", "    epoch_loss /= len(dataset)\n", "    epoch_loss_values.append(epoch_loss)"]}, {"cell_type": "code", "execution_count": 9, "id": "4ea6ba46-a8a8-431b-9b44-f80b91a528bf", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(epoch_loss_values, linewidth=2)\n", "plt.xlabel('epoch', fontsize=14)\n", "plt.ylabel('loss', fontsize=14)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9114039f-9c9e-437b-96f9-ae26603994e1", "metadata": {}, "source": ["### Visualise example results"]}, {"cell_type": "markdown", "id": "9a08aeff-3ea2-414e-a6ac-62b64732f28d", "metadata": {}, "source": ["For the shake of simplicity we visualise the results in the training set and not in the validation/test set."]}, {"cell_type": "code", "execution_count": 10, "id": "7d6154f0-747a-4cfd-b8c7-0ded58d40a8d", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1400x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ind = 3\n", "example_fixed = batch_data['fixed_hand'][ind, 0, :, :]\n", "example_moving = batch_data['moving_hand'][ind, 0, :, :]\n", "example_prealigned = batch_data['aligned_hand'][ind, 0, :, :]\n", "example_result = pred_image[ind, 0, :, :].detach().cpu()\n", "\n", "fig = plt.figure(figsize=(14, 6))\n", "plt.subplot(1, 4, 1)\n", "plt.imshow(example_fixed, cmap='gray')\n", "plt.title('fixed', fontsize=10)\n", "plt.axis('off')\n", "\n", "plt.subplot(1, 4, 2)\n", "plt.imshow(example_moving, cmap='gray')\n", "plt.title('original moving', fontsize=10)\n", "plt.axis('off')\n", "\n", "plt.subplot(1, 4, 3)\n", "plt.imshow(example_prealigned, cmap='gray')\n", "plt.title('moving after elastix pre-alignment', fontsize=10)\n", "plt.axis('off')\n", "\n", "plt.subplot(1, 4, 4)\n", "plt.imshow(example_result, cmap='gray')\n", "plt.title('final registered', fontsize=10)\n", "plt.axis('off')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "815e6076-3d65-4a29-9529-63fa713749d9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}