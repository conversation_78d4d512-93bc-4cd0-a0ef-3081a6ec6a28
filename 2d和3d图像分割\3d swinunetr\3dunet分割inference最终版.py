#!/usr/bin/env python
# coding: utf-8

"""
HCC Segmentation Inference Script using 3D U-Net
Load trained U-Net model and perform inference on new images
Compatible with the U-Net training script structure

IMPORTANT UPDATES (to match U-Net training parameters):
- ROI size: (64, 64, 32) - matches training spatial padding
- Sliding window batch size: 4 - matches training sw_batch_size  
- Spatial padding: [64, 64, 32] - matches training validation transforms
- Model architecture: MONAI UNet with specific parameters
- Added model compatibility verification to ensure correct architecture

KEY CHANGES FROM SwinUNETR INFERENCE VERSION:
- Replaced SwinUNETR with MONAI UNet
- Updated model parameters to match training script
- Changed model file names to *_UNet_HCC.pt
- Updated compatibility verification for U-Net architecture
- All other components remain unchanged
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os
import glob
import pandas as pd
from monai.transforms import (
    Compose, LoadImaged, EnsureChannelFirstd, Orientationd,
    Spacingd, NormalizeIntensityd, SpatialPadd
)
from monai.inferers import sliding_window_inference
# CHANGED: Import UNet instead of SwinUNETR
from monai.networks.nets import UNet
from monai.networks.layers import Norm

# Setup
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# Match training parameters exactly
roi_size = (64, 64, 32)  # Must match training ROI size
sw_batch_size = 4  # Match training sliding window batch size

# Results directory structure (same as training)
data_disk_path = '/root/autodl-tmp'
results_dir = os.path.join(data_disk_path, 'results')
models_dir = os.path.join(results_dir, 'models')
inference_dir = os.path.join(results_dir, 'inference')
os.makedirs(inference_dir, exist_ok=True)

def create_model_architecture():
    """
    Create a fresh UNet model with the same architecture as training
    This can be used for debugging or loading state_dict instead of full model
    """
    # CHANGED: Use UNet with exact same parameters as training script
    model = UNet(
        spatial_dims=3,       # 3D segmentation
        in_channels=1,        # Single channel for HCC images
        out_channels=2,       # Binary segmentation: background + HCC
        channels=(32, 64, 128, 256, 512),  # Feature channels for each level
        strides=(2, 2, 2, 2),  # Strides between levels
        num_res_units=2,      # Number of residual units per level
        norm=Norm.BATCH,      # Batch normalization
        dropout=0.1,          # Dropout for regularization
    ).to(device)
    return model

def verify_model_compatibility(model):
    """
    Verify that the loaded model has the expected architecture for inference
    """
    try:
        # Check if model has the expected input/output dimensions
        # Create a dummy input tensor with the expected dimensions
        dummy_input = torch.randn(1, 1, 64, 64, 32).to(device)
        
        with torch.no_grad():
            output = model(dummy_input)
        
        expected_output_shape = (1, 2, 64, 64, 32)  # [batch, classes, H, W, D]
        
        if output.shape == expected_output_shape:
            print(f"✅ U-Net model compatibility verified!")
            print(f"   Input shape: {dummy_input.shape}")
            print(f"   Output shape: {output.shape}")
            return True
        else:
            print(f"❌ U-Net model output shape mismatch!")
            print(f"   Expected: {expected_output_shape}")
            print(f"   Got: {output.shape}")
            return False
            
    except Exception as e:
        print(f"❌ U-Net model compatibility check failed: {e}")
        return False

def load_trained_model(model_path=None, model_type="last"):
    """
    Load the trained U-Net model with compatibility verification
    Args:
        model_path: Custom model path (optional)
        model_type: "last", "best", or custom path
    """
    if model_path is None:
        if model_type == "last":
            # CHANGED: Update model file names for U-Net
            model_path = os.path.join(models_dir, "last_model_UNet_HCC.pt")
        elif model_type == "best":
            model_path = os.path.join(models_dir, "best_model_UNet_HCC.pt")
        else:
            print(f"Unknown model type: {model_type}")
            return None

    if os.path.exists(model_path):
        print(f"Loading U-Net model from: {model_path}")
        try:
            model = torch.load(model_path, map_location=device)
            model.eval()
            print(f"✅ U-Net model loaded successfully!")
            
            # Verify model compatibility
            if verify_model_compatibility(model):
                return model
            else:
                print("❌ U-Net model failed compatibility check. Please retrain with correct parameters.")
                return None
                
        except Exception as e:
            print(f"❌ Error loading U-Net model: {e}")
            return None
    else:
        print(f"❌ U-Net model file not found: {model_path}")
        print("Available models:")
        if os.path.exists(models_dir):
            for f in os.listdir(models_dir):
                if f.endswith('.pt'):
                    print(f"  - {f}")
        return None

def preprocess_image(image_path):
    """
    Preprocess a single image for inference with dimension tracking
    Stores original metadata to enable proper dimension restoration
    """
    # Load original image to get metadata
    original_nii = nib.load(image_path)
    original_data = original_nii.get_fdata()
    original_shape = original_data.shape
    original_affine = original_nii.affine
    original_header = original_nii.header

    print(f"📏 Original image shape: {original_shape}")

    # Define preprocessing transforms (EXACTLY same as validation in training script)
    preprocess = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.0, 1.0, 1.0), mode="bilinear"),
        NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
        # CRITICAL: Must match training spatial padding size [64, 64, 32]
        SpatialPadd(keys=["image"], spatial_size=[64, 64, 32], mode="constant"),
    ])

    # Create data dictionary
    data = {"image": image_path}

    # Apply transforms
    data = preprocess(data)

    # Store original metadata for dimension restoration
    data["original_shape"] = original_shape
    data["original_affine"] = original_affine
    data["original_header"] = original_header
    data["padded_shape"] = data["image"].shape[1:]  # Remove channel dimension

    print(f"📐 Padded image shape: {data['padded_shape']}")

    return data



def restore_prediction_dimensions(prediction, image_data):
    """
    Restore prediction to original image dimensions
    Handles cropping/resizing from padded dimensions back to original
    """
    original_shape = image_data["original_shape"]
    padded_shape = image_data["padded_shape"]

    print(f"🔄 Restoring dimensions from {padded_shape} to {original_shape}")

    # Convert to numpy if needed
    if torch.is_tensor(prediction):
        prediction = prediction.cpu().numpy()

    # Remove batch dimension if present
    if prediction.ndim == 4:
        prediction = prediction[0]

    # Calculate how much padding was added in each dimension
    pad_diff = [padded_shape[i] - original_shape[i] for i in range(3)]

    # If no padding was added (original was larger than target), use resizing
    if all(diff <= 0 for diff in pad_diff):
        # Original image was larger than or equal to padded size
        # Use simple resizing
        try:
            from scipy.ndimage import zoom
            zoom_factors = [original_shape[i] / padded_shape[i] for i in range(3)]
            restored_prediction = zoom(prediction, zoom_factors, order=0)  # Nearest neighbor for labels
            print(f"📐 Used resizing with zoom factors: {zoom_factors}")
        except ImportError:
            print("❌ scipy not available, using simple slicing instead")
            # Fallback to simple slicing if scipy not available
            restored_prediction = prediction[:original_shape[0], :original_shape[1], :original_shape[2]]
    else:
        # Padding was added, so we need to crop
        # Calculate crop boundaries (remove padding symmetrically)
        crop_start = [max(0, pad_diff[i] // 2) for i in range(3)]
        crop_end = [crop_start[i] + original_shape[i] for i in range(3)]

        # Ensure we don't exceed padded dimensions
        crop_end = [min(crop_end[i], padded_shape[i]) for i in range(3)]

        # Crop the prediction
        restored_prediction = prediction[
            crop_start[0]:crop_end[0],
            crop_start[1]:crop_end[1],
            crop_start[2]:crop_end[2]
        ]

        print(f"📐 Used cropping: start={crop_start}, end={crop_end}")

        # If cropped size doesn't exactly match original, resize
        if restored_prediction.shape != original_shape:
            try:
                from scipy.ndimage import zoom
                zoom_factors = [original_shape[i] / restored_prediction.shape[i] for i in range(3)]
                restored_prediction = zoom(restored_prediction, zoom_factors, order=0)
                print(f"📐 Applied final resize with zoom factors: {zoom_factors}")
            except ImportError:
                print("❌ scipy not available, dimension mismatch may persist")
                print(f"   Cropped shape: {restored_prediction.shape}, Target: {original_shape}")

    print(f"✅ Final prediction shape: {restored_prediction.shape}")

    return restored_prediction

def perform_inference(model, image_data):
    """Perform inference on preprocessed image"""
    # Add batch dimension
    image_tensor = torch.unsqueeze(image_data["image"], 0).to(device)

    # Perform sliding window inference
    with torch.no_grad():
        pred = sliding_window_inference(image_tensor, roi_size, sw_batch_size, model)

        # Convert to discrete predictions
        pred_discrete = torch.argmax(pred, dim=1)

    return pred, pred_discrete



def visualize_results(original_image, prediction, slice_idx=None, image_data=None):
    """
    Visualize original image and prediction with dimension handling
    If dimensions don't match, will resize prediction for visualization
    """
    # Convert to numpy if needed
    if torch.is_tensor(original_image):
        original_image = original_image.cpu().numpy()
    if torch.is_tensor(prediction):
        prediction = prediction.cpu().numpy()

    # Remove batch and channel dimensions
    if original_image.ndim == 5:  # [batch, channel, H, W, D]
        original_image = original_image[0, 0]
    elif original_image.ndim == 4:  # [channel, H, W, D]
        original_image = original_image[0]

    if prediction.ndim == 4:  # [batch, H, W, D]
        prediction = prediction[0]

    # Handle dimension mismatch for visualization
    if original_image.shape != prediction.shape:
        print(f"⚠️  Dimension mismatch for visualization:")
        print(f"   Original: {original_image.shape}, Prediction: {prediction.shape}")

        # If we have image_data with original info, load original image for visualization
        if image_data and "original_shape" in image_data:
            # Load original image for visualization
            original_nii = nib.load(image_data.get("original_image_path", ""))
            if original_nii:
                original_for_viz = original_nii.get_fdata()
                print(f"📷 Using original image for visualization: {original_for_viz.shape}")
                original_image = original_for_viz

        # Resize prediction to match original for visualization only
        if original_image.shape != prediction.shape:
            try:
                from scipy.ndimage import zoom
                zoom_factors = [original_image.shape[i] / prediction.shape[i] for i in range(3)]
                prediction_resized = zoom(prediction, zoom_factors, order=0)
                print(f"📐 Resized prediction for visualization: {prediction.shape} -> {prediction_resized.shape}")
                prediction = prediction_resized
            except ImportError:
                print("❌ scipy not available, visualization may show dimension mismatch")

    # Select middle slice if not specified
    if slice_idx is None:
        slice_idx = min(original_image.shape[2] // 2, prediction.shape[2] // 2)

    # Ensure slice index is valid for both images
    slice_idx = min(slice_idx, original_image.shape[2] - 1, prediction.shape[2] - 1)

    # Create visualization
    _, axes = plt.subplots(1, 3, figsize=(15, 5))

    # Original image
    axes[0].imshow(original_image[:, :, slice_idx], cmap='gray')
    axes[0].set_title(f'Original Image\nShape: {original_image.shape}')
    axes[0].axis('off')

    # Prediction
    axes[1].imshow(prediction[:, :, slice_idx], cmap='jet')
    axes[1].set_title(f'U-Net Prediction\nShape: {prediction.shape}')
    axes[1].axis('off')

    # Overlay (only if dimensions match)
    if original_image.shape == prediction.shape:
        axes[2].imshow(original_image[:, :, slice_idx], cmap='gray')
        axes[2].imshow(prediction[:, :, slice_idx], cmap='jet', alpha=0.5)
        axes[2].set_title('Overlay')
    else:
        axes[2].text(0.5, 0.5, 'Dimension\nMismatch\nCannot Overlay',
                    ha='center', va='center', transform=axes[2].transAxes, fontsize=12)
        axes[2].set_title('Overlay (N/A)')
    axes[2].axis('off')

    plt.tight_layout()

    # Save to inference directory
    viz_filename = f'unet_inference_result_slice_{slice_idx}.png'
    viz_path = os.path.join(inference_dir, viz_filename)
    plt.savefig(viz_path, dpi=150, bbox_inches='tight')
    print(f"Visualization saved to: {viz_path}")
    plt.show()

def save_prediction(prediction, original_image_path, output_dir=None, image_data=None):
    """
    Save prediction as NIfTI file with proper dimension handling
    Ensures output has same dimensions as original image
    """
    if output_dir is None:
        output_dir = inference_dir
    os.makedirs(output_dir, exist_ok=True)

    # Load original image to get header info
    original_nii = nib.load(original_image_path)
    original_shape = original_nii.get_fdata().shape

    # Convert prediction to numpy if needed
    if torch.is_tensor(prediction):
        prediction = prediction.cpu().numpy()

    # Remove batch dimension if present
    if prediction.ndim == 4:
        prediction = prediction[0]

    print(f"💾 Saving prediction:")
    print(f"   Original image shape: {original_shape}")
    print(f"   Prediction shape: {prediction.shape}")

    # Check if dimensions match
    if prediction.shape != original_shape:
        print(f"⚠️  Dimension mismatch detected!")

        # If we have image_data with restoration info, use it
        if image_data:
            print(f"🔄 Restoring prediction to original dimensions...")
            prediction = restore_prediction_dimensions(prediction, image_data)
        else:
            # Fallback: simple resize
            print(f"🔄 Using fallback resize to match original dimensions...")
            try:
                from scipy.ndimage import zoom
                zoom_factors = [original_shape[i] / prediction.shape[i] for i in range(3)]
                prediction = zoom(prediction, zoom_factors, order=0)  # Nearest neighbor for labels
                print(f"📐 Applied zoom factors: {zoom_factors}")
            except ImportError:
                print("❌ scipy not available, cannot resize prediction to match original")

    # Verify final dimensions
    if prediction.shape != original_shape:
        print(f"❌ Warning: Final prediction shape {prediction.shape} still doesn't match original {original_shape}")
        print(f"   This may cause issues with downstream analysis.")
    else:
        print(f"✅ Prediction dimensions match original image!")

    # Create new NIfTI image with same header as original
    pred_nii = nib.Nifti1Image(prediction.astype(np.uint8),
                               original_nii.affine,
                               original_nii.header)

    # Generate output filename
    base_name = os.path.basename(original_image_path)
    output_name = base_name.replace('.nii.gz', '_unet_prediction.nii.gz')
    output_path = os.path.join(output_dir, output_name)

    # Save
    nib.save(pred_nii, output_path)
    print(f"💾 U-Net prediction saved to: {output_path}")
    print(f"📏 Final saved shape: {prediction.shape}")

    return output_path



def run_inference_on_image(model_path=None, image_path=None, model_type="last",
                          visualize=True, save_result=True):
    """Complete inference pipeline for a single image with dimension restoration"""
    print(f"🔍 Running U-Net inference on: {image_path}")

    # Load model
    model = load_trained_model(model_path, model_type)
    if model is None:
        return None

    # Preprocess image
    print("📋 Preprocessing image...")
    image_data = preprocess_image(image_path)

    # Store original image path for later use
    image_data["original_image_path"] = image_path

    # Perform inference
    print("🧠 Performing U-Net inference...")
    _, pred_discrete = perform_inference(model, image_data)

    # Restore prediction to original dimensions
    print("🔄 Restoring prediction dimensions...")
    pred_restored = restore_prediction_dimensions(pred_discrete, image_data)

    # Visualize results (use restored prediction)
    if visualize:
        print("🖼️  Visualizing results...")
        # For visualization, we'll use the original preprocessed image and restored prediction
        visualize_results(image_data["image"], pred_restored, image_data=image_data)

    # Save prediction (use restored prediction)
    output_path = None
    if save_result:
        print("💾 Saving prediction...")
        output_path = save_prediction(pred_restored, image_path, image_data=image_data)

    return {
        'prediction': pred_restored,
        'prediction_padded': pred_discrete,  # Keep padded version for debugging
        'output_path': output_path,
        'original_shape': image_data["original_shape"],
        'padded_shape': image_data["padded_shape"]
    }

def batch_inference(model_path=None, image_dir=None, model_type="last", save_predictions=True):
    """
    Run inference on all images in a directory
    Args:
        model_path: Custom model path (optional)
        image_dir: Directory containing images
        model_type: "last" or "best"
        save_predictions: Whether to save prediction files
    """
    print(f"🚀 Running batch U-Net inference on images in: {image_dir}")
    print(f"📊 Using {model_type} U-Net model")

    # Load model
    model = load_trained_model(model_path, model_type)
    if model is None:
        return None

    # Get all NIfTI files
    image_files = []
    for ext in ['*.nii', '*.nii.gz']:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))

    print(f"Found {len(image_files)} images")

    # Store processing results
    processed_files = []
    successful_inferences = 0

    # Process each image
    for i, image_path in enumerate(image_files):
        print(f"\n📋 Processing {i+1}/{len(image_files)}: {os.path.basename(image_path)}")

        try:
            # Run inference
            result = run_inference_on_image(
                model_path=model_path,
                image_path=image_path,
                model_type=model_type,
                visualize=False,
                save_result=save_predictions
            )

            if result:
                processed_files.append({
                    'image_name': os.path.basename(image_path),
                    'output_path': result['output_path'],
                    'status': 'success'
                })
                successful_inferences += 1

        except Exception as e:
            print(f"❌ Error processing {image_path}: {e}")
            processed_files.append({
                'image_name': os.path.basename(image_path),
                'output_path': None,
                'status': f'error: {str(e)}'
            })

    # Save processing summary
    if processed_files:
        print(f"\n📊 Saving processing summary...")

        # Save processing log
        summary_df = pd.DataFrame(processed_files)
        summary_path = os.path.join(inference_dir, f"unet_processing_log_{model_type}.csv")
        summary_df.to_csv(summary_path, index=False)
        print(f"📋 Processing log saved to: {summary_path}")

    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 BATCH U-NET INFERENCE COMPLETED ({model_type.upper()} MODEL)")
    print("=" * 60)
    print(f"✅ Successfully processed: {successful_inferences}/{len(image_files)} images")
    print(f"💾 Results saved to: {inference_dir}")
    if save_predictions:
        print(f"📁 Prediction files: *_unet_prediction.nii.gz")
    print("=" * 60)

    return {
        'processed_files': processed_files,
        'successful_inferences': successful_inferences,
        'total_images': len(image_files)
    }

if __name__ == "__main__":
    print("🏥 HCC Segmentation Inference Script - U-Net Version")
    print("=" * 60)
    print("📋 UPDATED TO MATCH U-NET TRAINING PARAMETERS:")
    print(f"   - ROI size: {roi_size}")
    print(f"   - Sliding window batch size: {sw_batch_size}")
    print(f"   - Spatial padding: [64, 64, 32]")
    print(f"   - Model: MONAI UNet with channels (32,64,128,256,512)")
    print(f"   - Model files: *_UNet_HCC.pt")
    print(f"   - Model verification: Enabled")
    print("🔧 DIMENSION RESTORATION FEATURES:")
    print(f"   - Automatic dimension tracking during preprocessing")
    print(f"   - Smart cropping/resizing to restore original dimensions")
    print(f"   - Prediction outputs match original image dimensions")
    print(f"   - Enhanced visualization with dimension handling")
    print("=" * 60)

    # Configuration
    image_dir = "/root/autodl-tmp/120HCC/image2"

    # Check if directories exist
    if not os.path.exists(image_dir):
        print(f"❌ Image directory not found: {image_dir}")
        print("Please update the image directory path.")
        exit(1)

    print(f"📁 Image directory: {image_dir}")
    print(f"💾 Results will be saved to: {inference_dir}")

    # Example 1: Single image inference
    print("\n" + "=" * 50)
    print("🔍 SINGLE IMAGE INFERENCE EXAMPLE")
    print("=" * 50)

    # Find a sample image
    sample_images = glob.glob(os.path.join(image_dir, "*.nii.gz"))
    if sample_images:
        sample_image = sample_images[0]
        sample_name = os.path.basename(sample_image)

        print(f"📋 Sample image: {sample_name}")

        # Run inference with last model
        print("\n🧠 Using LAST U-Net model:")
        result_last = run_inference_on_image(
            image_path=sample_image,
            model_type="last",
            visualize=True,
            save_result=True
        )

        if result_last:
            print("✅ Single image inference completed!")
            print(f"💾 Prediction saved to: {result_last['output_path']}")

    else:
        print(f"❌ No images found in {image_dir}")

    # Example 2: Batch inference
    print("\n" + "=" * 50)
    print("🚀 BATCH INFERENCE")
    print("=" * 50)

    # Ask user for confirmation
    response = input("Run batch inference on all images? (y/n): ")
    if response.lower() == 'y':

        # Compare last vs best model if both exist
        last_model_path = os.path.join(models_dir, "last_model_UNet_HCC.pt")
        best_model_path = os.path.join(models_dir, "best_model_UNet_HCC.pt")

        if os.path.exists(last_model_path):
            print("\n🔄 Running inference with LAST U-Net model...")
            results_last = batch_inference(
                image_dir=image_dir,
                model_type="last",
                save_predictions=True
            )

        if os.path.exists(best_model_path):
            print("\n🏆 Running inference with BEST U-Net model...")
            results_best = batch_inference(
                image_dir=image_dir,
                model_type="best",
                save_predictions=True
            )

        print("\n🎉 All U-Net inference tasks completed!")
        print(f"📊 Check results in: {inference_dir}")

    else:
        print("Batch inference skipped.")

    print("\n" + "=" * 50)
    print("✅ U-NET INFERENCE SCRIPT COMPLETED")
    print("=" * 50)
