#!/usr/bin/env python
# coding: utf-8

# In[1]:monai框架的代码，自己买的，已运行成功
#pip install monai[all]  如果遇到下面报错，安装所有依赖可解决问题
#RuntimeError: applying transform <monai.transforms.compose.Compose object at 0x7fd4ecf32940>
import monai
from monai.networks.nets import *
print(dir(monai.networks.nets))

import numpy as np
import os
import torch
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
from monai.config import print_config
from monai.data import DataLoader,Dataset,decollate_batch
from monai.networks import eval_mode
from monai.networks.nets import densenet121,resnet50,resnet152,HighResNet
from monai.metrics import ROCAUCMetric
from monai.transforms import Activations, AsDiscrete,LoadImageD, EnsureChannelFirstD, ScaleIntensityRangeD,RandRotate90D,SpacingD,OrientationD, ResizeD,ScaleIntensityD,Compose
import random
from monai.networks.nets import EfficientNet

# In[2]:
train_dir = "/root/autodl-tmp/3dnii/train"
val_dir = "/root/autodl-tmp/3dnii/validation"

# data_dir = r"K:\2020-2023HCC\579hcc\578hcc\ap\nii3\val"

# In[3]:

class_names = sorted(x for x in os.listdir(train_dir) if os.path.isdir(os.path.join(train_dir, x)))

num_class = len(class_names)
print(num_class)

train_image_files = [
    [os.path.join(train_dir, class_names[i], x) for x in os.listdir(os.path.join(train_dir, class_names[i]))]
    for i in range(num_class)
]
train_num_each = [len(train_image_files[i]) for i in range(num_class)]
train_image_files_list = []
train_image_class = []
for i in range(num_class):
    train_image_files_list.extend(train_image_files[i])
    train_image_class.extend([i] * train_num_each[i])
train_image_class = np.array(train_image_class, dtype=np.int64)

val_image_files = [
    [os.path.join(val_dir, class_names[i], x) for x in os.listdir(os.path.join(val_dir, class_names[i]))]
    for i in range(num_class)
]
val_num_each = [len(val_image_files[i]) for i in range(num_class)]
val_image_files_list = []
val_image_class = []
for i in range(num_class):
    val_image_files_list.extend(val_image_files[i])
    val_image_class.extend([i] * val_num_each[i])
val_image_class = np.array(val_image_class, dtype=np.int64)

random.seed(2023)
random.shuffle(train_image_files_list)
random.shuffle(val_image_files_list)

print(f"Total train image count: {len(train_image_files_list)}")
print(f"Total val image count: {len(val_image_files_list)}")
print(f"Label names: {class_names}")
print(f"Train label counts: {train_num_each}")
print(f"Val label counts: {val_num_each}")

train_files = [{"image": img, "label": label} for img, label in zip(train_image_files_list, train_image_class)]
val_files = [{"image": img, "label": label} for img, label in zip(val_image_files_list, val_image_class)]

print("train_files数量:", len(train_files))
print("val_files数量:", len(val_files))

# In[9]:

num_class = 2
train_transform = Compose(
    [
        LoadImageD(keys="image", image_only=True),
        EnsureChannelFirstD(keys="image"),
        ScaleIntensityRangeD(
            keys="image",
            a_min=0,
            a_max=600,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        OrientationD(keys="image", axcodes="RAS"),
        SpacingD(keys="image", pixdim=(1, 1, 1), mode="bilinear"),
        ResizeD(keys="image",spatial_size=(96,96,96)),
        RandRotate90D(keys="image", prob=0.8, spatial_axes=[0, 2]),
    ]
)

val_transforms = Compose(
    [
        LoadImageD(keys="image", image_only=True),
        EnsureChannelFirstD(keys="image"),
        ScaleIntensityRangeD(
            keys="image",
            a_min=0,
            a_max=600,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        OrientationD(keys="image", axcodes="RAS"),
        SpacingD(keys="image", pixdim=(1, 1, 1), mode="bilinear"),
        ResizeD(keys="image",spatial_size=(96,96,96)),

    ]
)
post_pred = Compose([Activations(softmax=True)])
post_label = Compose([AsDiscrete(to_onehot=num_class)])

# In[11]:
#构建Dataset
tra_ds = Dataset(data=train_files, transform=train_transform)
tra_loader = DataLoader(tra_ds, batch_size=2, num_workers=4, pin_memory=torch.cuda.is_available())
val_ds = Dataset(data=val_files, transform=val_transforms)
val_loader = DataLoader(val_ds, batch_size=2, num_workers=4, pin_memory=torch.cuda.is_available())

# In[ ]:

check_data = monai.utils.misc.first(tra_loader)
print(check_data["image"].shape, check_data["label"])
image = check_data["image"][0][0]
plt.figure("check", (12, 6))
plt.imshow(image[40,:, :], cmap="gray")
plt.show()

# In[4]:构建模型
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = densenet121(spatial_dims=3, in_channels=1, out_channels=num_class).to(device)
# model = resnet152(spatial_dims=3,n_input_channels=1,num_classes=num_class).to(device)
# model = HighResNet(spatial_dims=3,in_channels=1,out_channels=num_class).to(device)
   
# In[ ]:开始训练

loss_function = torch.nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), 1e-4)
auc_metric = ROCAUCMetric()

max_epochs = 20
val_interval = 2
best_metric = -1
best_metric_epoch = -1
writer = SummaryWriter()

for epoch in range(max_epochs):
    print("-" * 10)
    print(f"epoch {epoch + 1}/{max_epochs}")
    model.train()
    epoch_loss = 0
    step = 0
    for batch_data in tra_loader:
        step += 1
        inputs, labels = batch_data["image"].to(device), batch_data["label"].to(device)
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = loss_function(outputs, labels)
        loss.backward()
        optimizer.step()
        epoch_loss += loss.item()
        epoch_len = len(tra_ds) // tra_loader.batch_size
        #print(f"{step}/{epoch_len}, train_loss: {loss.item():.4f}")
        writer.add_scalar("train_loss", loss.item(), epoch_len * epoch + step)
    epoch_loss /= step
    print(f"epoch {epoch + 1} average loss: {epoch_loss:.4f}")


    if (epoch + 1) % val_interval == 0:
        model.eval()
        with torch.no_grad():
            y_pred = torch.tensor([], dtype=torch.float32, device=device)
            y = torch.tensor([], dtype=torch.long, device=device)
            for val_data in val_loader:
                val_images, val_labels = val_data["image"].to(device), val_data["label"].to(device)
                y_pred = torch.cat([y_pred, model(val_images)], dim=0)
                y = torch.cat([y, val_labels], dim=0)

            acc_value = torch.eq(y_pred.argmax(dim=1), y)
            acc_metric = acc_value.sum().item() / len(acc_value)
            y_onehot = [post_label(i) for i in decollate_batch(y, detach=False)]
            y_pred_act = [post_pred(i) for i in decollate_batch(y_pred)]
            auc_metric(y_pred_act, y_onehot)
            auc_result = auc_metric.aggregate()
            auc_metric.reset()
            del y_pred_act, y_onehot
            if acc_metric > best_metric:
                best_metric = acc_metric
                best_metric_epoch = epoch + 1
                torch.save(model.state_dict(), "best_metric_model_classification3d_dict.pth")
                print("saved new best metric model")
            print(
                "current epoch: {} current accuracy: {:.4f} current AUC: {:.4f} best accuracy: {:.4f} at epoch {}".format(
                    epoch + 1, acc_metric, auc_result, best_metric, best_metric_epoch
                )
            )
            writer.add_scalar("val_accuracy", acc_metric, epoch + 1)
print(f"train completed, best_metric: {best_metric:.4f} at epoch: {best_metric_epoch}")
writer.close()

# In[20]:
#验证
import pandas as pd
dataframe_list = []

model.load_state_dict(torch.load("best_metric_model_classification3d_dict.pth"))
model.eval()
with torch.no_grad():
    num_correct = 0.0
    metric_count = 0
    for val_data in val_loader:
        val_images, val_labels = val_data["image"].to(device), val_data["label"].to(device)
        val_outputs = model(val_images).argmax(dim=1)
        val_pred_score = torch.nn.Softmax(dim=1)(model(val_images)).cpu().numpy()

        value = torch.eq(val_outputs, val_labels)
        metric_count += len(value)
        num_correct += value.sum().item()
        for i in range(len(val_data["label"].numpy())):
            pd_data_featrues = pd.DataFrame({
                'index': [val_data["image"].meta['filename_or_obj'][i]],
                'label': [val_labels.cpu().numpy()[i]],
                'predict':[val_outputs.cpu().numpy()[i]],
                'pre_score' :[list(val_pred_score)[i]]
                    })
            dataframe_list.append(pd_data_featrues)
    dataframe_total = pd.concat(dataframe_list,axis=0)
    dataframe_total= dataframe_total.reset_index(drop=True)     
    dataframe_total.to_csv('predict_result3d.csv',index=False)
    metric = num_correct / metric_count
    print("evaluation metric:", metric)


# In[34]:

#绘制ROC曲线
from sklearn.metrics import roc_curve,auc,accuracy_score,confusion_matrix
import matplotlib.pyplot as plt
import warnings
import seaborn as sns
warnings.filterwarnings("ignore")
def fun(x,index):
    #返回列表中第几个元素
    return x[index]
#绘制roc曲线
plt.figure(figsize=(8,8))

y_true = dataframe_total['label']
y_pred = dataframe_total['predict']
y_score = dataframe_total['pre_score'].apply(lambda x:fun(x,1))
#计算roc和 auc
fpr, tpr, thresholds=roc_curve(y_true,y_score,pos_label=1)
ACC = accuracy_score(y_true,y_pred)
AUC = auc(fpr, tpr) 
plt.plot(fpr,tpr,label=' ACC=%0.2f AUC = %0.2f'% (ACC ,AUC))
plt.legend(loc='lower right',fontsize = 12)
plt.plot([0,1],[0,1],color='black',linestyle='dashed')
plt.ylabel('True Positive Rate',fontsize = 14)
plt.xlabel('Flase Positive Rate',fontsize = 14)
plt.show()

# In[27]:

#绘制混淆矩阵
plt.figure(figsize=(5,5))

y_true = dataframe_total['label']
y_pred = dataframe_total['predict']

sns.heatmap(confusion_matrix(y_true, y_pred),annot=True, cmap='Blues')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title("confusion_matrix")
plt.show()


# In[29]:
#绘制DCA曲线
def calculate_net_benefit_model(thresh_group, y_pred_score, y_label):
    net_benefit_model = np.array([])
    for thresh in thresh_group:
        y_pred_label = y_pred_score > thresh
        tn, fp, fn, tp = confusion_matrix(y_label, y_pred_label).ravel()
        n = len(y_label)
        net_benefit = (tp / n) - (fp / n) * (thresh / (1 - thresh))
        net_benefit_model = np.append(net_benefit_model, net_benefit)
    return net_benefit_model

def calculate_net_benefit_all(thresh_group, y_label):
    net_benefit_all = np.array([])
    tn, fp, fn, tp = confusion_matrix(y_label, y_label).ravel()
    total = tp + tn
    for thresh in thresh_group:
        net_benefit = (tp / total) - (tn / total) * (thresh / (1 - thresh))
        net_benefit_all = np.append(net_benefit_all, net_benefit)
    return net_benefit_all

def plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all,color='crimson',index=18):

    ax.plot(thresh_group, net_benefit_model, color = color, label = 'Resnet3D-'+str(index))

    #Figure Configuration， 美化一下细节
    ax.set_xlim(0,1)
    ax.set_ylim(net_benefit_model.min() - 0.15, net_benefit_model.max() + 0.15)#adjustify the y axis limitation
    ax.set_xlabel(
        xlabel = 'High Risk Threshold', 
        fontdict= {'family': 'Times New Roman', 'fontsize': 13}
        )
    ax.set_ylabel(
        ylabel = 'Net Benefit', 
        fontdict= {'family': 'Times New Roman', 'fontsize': 13}
        )
    ax.grid('off')
    ax.spines['right'].set_color((0.8, 0.8, 0.8))
    ax.spines['top'].set_color((0.8, 0.8, 0.8))
    ax.legend(loc = 'upper right')
    return ax

fig, ax = plt.subplots()
fig.set_size_inches(10, 8)

y_label = dataframe_total['label']
y_pred_score = dataframe_total['pre_score'].apply(lambda x:fun(x,1))

thresh_group = np.arange(0,1,0.01)
net_benefit_model = calculate_net_benefit_model(thresh_group, y_pred_score, y_label)
net_benefit_all = calculate_net_benefit_all(thresh_group, y_label)
ax = plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all)
ax.plot(thresh_group, net_benefit_all, color = 'black',label = 'ALL')
ax.plot((0, 1), (0, 0), color = 'black', linestyle = ':', label = 'None')
plt.show()

# In[33]:
#校准曲线
from sklearn.calibration import calibration_curve

y_true = dataframe_total['label']
y_score = dataframe_total['pre_score'].apply(lambda x:fun(x,1))
prob_true, prob_pred = calibration_curve(list(y_true), list(y_score), n_bins=10)
plt.plot(prob_pred, prob_true, linewidth=1, marker="o",label="Mean value")
plt.plot([0,1],[0,1],color='black',linestyle='dashed')
plt.legend(["model"],loc="upper left")
plt.xlabel('Predicted Probability of hypertension')
plt.ylabel('Actual probability')

# %%基于monai框架的3d CNN   根据视频用chatgpt写的  已成功
import monai
from monai.networks.nets import *
print(dir(monai.networks.nets))

import numpy as np
import os
import torch
import matplotlib.pyplot as plt
import monai
from torch.utils.tensorboard import SummaryWriter
from monai.config import print_config
from monai.data import DataLoader, Dataset
from monai.networks.nets import resnet50,densenet121
from monai.transforms import Compose, LoadImage, EnsureChannelFirst, ScaleIntensityRange, Orientation, Spacing, Resize
import random
import pandas as pd
from torch import nn, optim
from monai.transforms import Compose, LoadImageD, EnsureChannelFirstD, ScaleIntensityRangeD, OrientationD, SpacingD, ResizeD, Activations, AsDiscrete
from monai.data import Dataset, DataLoader
from monai.metrics import ROCAUCMetric

# data_dir = r"K:\2020-2023HCC\579hcc\578hcc\ap\nii3\val" #路径下要有VETC分组文件夹
data_dir = "/root/autodl-tmp/3dnii/val"
# data_dir = '/hy-tmp/'  
class_names = sorted(x for x in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, x)))
num_class = len(class_names)
print(num_class)
image_files = [[os.path.join(data_dir, class_names[i], x) for x in os.listdir(os.path.join(data_dir, class_names[i]))] for i in range(num_class)]
num_each = [len(image_files[i]) for i in range(num_class)]
image_files_list = []
image_class = []

for i in range(num_class):
    image_files_list.extend(image_files[i])
    image_class.extend([i] * num_each[i])

image_class = np.array(image_class, dtype=np.int64)
random.seed(2023)
templist = list(zip(image_files_list, image_class))
random.shuffle(templist)
image_files_list[:], image_class[:] = zip(*templist)

print(f"Total image count: {len(image_files_list)}")
print(f"Label names: {class_names}")
print(f"Label counts: {num_each}")

#%%
train_transforms = Compose([
    LoadImageD(keys="image",image_only=True),
    EnsureChannelFirstD(keys="image"),
    ScaleIntensityRangeD(keys="image", a_min=-17.3, a_max=1280.5, b_min=0.0, b_max=1.0, clip=True),
    OrientationD(keys="image", axcodes="RAS"),
    SpacingD(keys="image", pixdim=(1, 1, 1), mode="bilinear"),
    ResizeD(keys="image", spatial_size=(96, 96, 96))
])

val_transforms = Compose([
    LoadImageD(keys="image",image_only=True),
    EnsureChannelFirstD(keys="image"),
    ScaleIntensityRangeD(keys="image", a_min=-17.3, a_max=1280.5, b_min=0.0, b_max=1.0, clip=True),
    OrientationD(keys="image", axcodes="RAS"),
    SpacingD(keys="image", pixdim=(1, 1, 1), mode="bilinear"),
    ResizeD(keys="image", spatial_size=(96, 96, 96))
])

post_pred = Compose([Activations(softmax=True)])
post_label = Compose([AsDiscrete(to_onehot=num_class)])

val_frac = 0.3
slicer = int(len(image_files_list) * val_frac)

train_files = [{"image": img, "label": label} for img, label in zip(image_files_list[slicer:], image_class[slicer:])]
val_files = [{"image": img, "label": label} for img, label in zip(image_files_list[:slicer], image_class[:slicer])]

train_ds = Dataset(data=train_files, transform=train_transforms)
train_loader = DataLoader(train_ds, batch_size=2, num_workers=1)

val_ds = Dataset(data=val_files, transform=val_transforms)
val_loader = DataLoader(val_ds, batch_size=2, num_workers=1)

check_data = monai.utils.misc.first(train_loader)
print(check_data["image"].shape, check_data["label"])

image = check_data["image"][0][0]
plt.figure("check", (12, 6))
plt.imshow(image[40, :, :], cmap='gray')
plt.show()

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
#model = densenet121(spatial_dims=3,in_channels=1,out_channels=2)
model = resnet50(spatial_dims=3, n_input_channels=1,num_classes=2) #spatial_dims=3 表达3d模型
model = model.to(device)
print(model)

# # 实例化预训练的 ResNet50 模型
# model = resnet50(pretrained=True)
# model
# # 可以选择冻结模型的参数
# for param in model.parameters():
#     param.requires_grad = False

# # 替换模型的最后一层，以适应特定的任务
# model.fc = torch.nn.Linear(model.fc.in_features, 2)

loss_function = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=1e-4)
auc_metric = ROCAUCMetric()

#%%训练过程

import pandas as pd
# 初始化ROCAUCMetric对象
auc_metric = ROCAUCMetric()

max_epochs = 5
val_interval = 2
best_metric = -1
best_metric_epoch = -1
writer = SummaryWriter()

train_results = []
val_results = []

for epoch in range(max_epochs):
    print("-"*10)
    print(f"Epoch [{epoch + 1}/{max_epochs}")

    correct = 0
    total = 0
    model.train()
    epoch_loss = 0.0
    step = 0
    for batch_data in train_loader:
        step += 1
        inputs, labels = batch_data["image"].to(device), batch_data["label"].to(device)
        optimizer.zero_grad()
        outputs = model(inputs)
        _, predicted = torch.max(outputs, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()
        loss = loss_function(outputs, labels)        
        loss.backward()
        optimizer.step()
        epoch_loss += loss.item()
    epoch_loss /= step
    train_accuracy = correct / total
    print(f"Epoch [{epoch + 1}] average loss: {epoch_loss}")  
    print(f"Epoch [{epoch + 1}] training accuracy: {train_accuracy}")   
    # 收集训练结果
    train_results.append({
        'Epoch': epoch + 1,
        'Accuracy': train_accuracy,  # 将训练准确率添加到train_results中
    })  

    
    if (epoch + 1) % val_interval == 0:
        model.eval()
        with torch.no_grad():
            y_pred = torch.tensor([], dtype=torch.float32, device=device)
            y = torch.tensor([], dtype=torch.long, device=device)
            for val_data in val_loader:
                val_images, val_labels = val_data["image"].to(device), val_data["label"].to(device)
                val_outputs = model(val_images)
                val_outputs = torch.nn.functional.softmax(val_outputs, dim=1)  # 将logits转换为概率值
                y_pred = torch.cat([y_pred, val_outputs], dim=0)
                y = torch.cat([y, val_labels], dim=0)
            # 获取预测标签
            y_pred_label = torch.argmax(y_pred, dim=1)
            # 累积预测标签和真实标签
            auc_metric(y_pred_label, y)

            # 计算验证集准确率
            correct = (y_pred_label == y).sum().item()
            total = y.size(0)
            val_accuracy = correct / total
            print(f"Epoch [{epoch + 1}] val accuracy: {val_accuracy}")    
            # 收集验证结果
            val_results.append({
                'Epoch': epoch + 1,
                'Accuracy': val_accuracy,
                'True Label': y.cpu().numpy(),
                'Predicted Label': y_pred_label.cpu().numpy(),                
            })

# 将结果转换为DataFrame
train_df = pd.DataFrame(train_results)
val_df = pd.DataFrame(val_results)

# 计算ROC AUC值
auc_value = auc_metric.aggregate()
writer.add_scalar("val_auc", auc_value)
auc_metric.reset()

print("Training completed.")
writer.close()

# 保存为Excel文件
# file_path = r'k:\2020-2023HCC\579hcc\578hcc\ap\training_validation_results.xlsx'
# with pd.ExcelWriter(file_path) as writer:
#     train_df.to_excel(writer, sheet_name='Training Results', index=False)
#     val_df.to_excel(writer, sheet_name='Validation Results', index=False)
# print(f"Training and validation results saved to {file_path}")

#%% 测试过程
# 加载模型
# model.load_state_dict(torch.load("best_model.pth"))
model.eval()

# 预测并计算评估指标
num_correct = 0.0
metric_count = 0
dataframe_list = []

with torch.no_grad():
    for val_data in val_loader:
        val_images, val_labels = val_data["image"].to(device), val_data["label"].to(device)
        val_outputs = model(val_images)
        val_pred_score = torch.nn.Softmax(dim=1)(val_outputs)
        value = torch.eq(val_outputs.argmax(dim=1), val_labels)
        metric_count += len(value)
        num_correct += value.sum().item()

        for i in range(len(val_data["label"])):
            dataframe_list.append({
                "image": val_data["image"][i],
                "label": val_data["label"][i].item(),
                "predict": val_outputs[i].argmax().item(),
                "score": val_pred_score[i].tolist()
            })

# 创建DataFrame
dataframe_total = pd.DataFrame(dataframe_list)

# 计算评估指标
metric = num_correct / metric_count
print("Evaluation metric:", metric)

#将结果保存为CSV文件
# dataframe_total.to_csv("predict_results.csv", index=False)
#%% 
# 绘制ROC曲线
from sklearn.metrics import roc_curve, auc, accuracy_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

plt.figure(figsize=(8, 8))
y_true = dataframe_total['label']
y_score = dataframe_total['pre_score']
fpr, tpr, thresholds = roc_curve(y_true, y_score)
AUC = auc(fpr, tpr)
plt.plot(fpr, tpr, label=f'ACC={accuracy_score(y_true, y_pred):.2f} AUC={AUC:.2f}')
plt.legend(loc='lower right', fontsize=12)
plt.plot([0, 1], [0, 1], color='black', linestyle='--')
plt.ylabel('True Positive Rate', fontsize=12)
plt.xlabel('False Positive Rate', fontsize=12)
plt.show()
#%% 
# 绘制混淆矩阵
plt.figure(figsize=(5, 5))
y_pred = dataframe_total['predict']
sns.heatmap(confusion_matrix(y_true, y_pred), annot=True, fmt='d')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title("Confusion Matrix")
plt.show()
#%% 
# 绘制DCA曲线
def calculate_net_benefit_model(thresh_group, y_pred_score, y_label):
    net_benefit_model = np.array([])
    for thresh in thresh_group:
        y_pred_label = y_pred_score > thresh
        tn, fp, fn, tp = confusion_matrix(y_label, y_pred_label).ravel()
        net_benefit = (tp / len(y_label)) - (fp / len(y_label))
        net_benefit_model = np.append(net_benefit_model, net_benefit)
    return net_benefit_model

def calculate_net_benefit_all(thresh_group, y_pred_score, y_label):
    net_benefit_all = np.array([])
    total = len(y_label)
    for thresh in thresh_group:
        y_pred_label = y_pred_score > thresh
        tn, fp, fn, tp = confusion_matrix(y_label, y_pred_label).ravel()
        net_benefit = (tp / total) - (tn / total)
        net_benefit_all = np.append(net_benefit_all, net_benefit)
    return net_benefit_all

def plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all):
    ax.plot(thresh_group, net_benefit_model, label='Model')
    ax.plot(thresh_group, net_benefit_all, label='All')
    ax.set_xlabel('High Risk Threshold', fontdict={'family': 'Times New Roman', 'size': 12})
    ax.set_ylabel('Net Benefit', fontdict={'family': 'Times New Roman', 'size': 12})
    ax.set_xlim(0, 1)
    ax.set_ylim(net_benefit_model.min(), net_benefit_model.max())
    ax.grid(False)
    ax.spines['right'].set_color((0.8, 0.8, 0.8))
    ax.spines['top'].set_color((0.8, 0.8, 0.8))
    ax.legend(loc='upper right')
    return ax

fig, ax = plt.subplots()
fig.set_size_inches(10, 8)

y_label = dataframe_total['label']
y_pred_score = dataframe_total['pre_score']
thresh_group = np.arange(0, 1, 0.01)
net_benefit_model = calculate_net_benefit_model(thresh_group, y_pred_score, y_label)
net_benefit_all = calculate_net_benefit_all(thresh_group, y_pred_score, y_label)

ax = plot_DCA(ax, thresh_group, net_benefit_model, net_benefit_all)
ax.plot([0, 1], [0, 0], color='black', linestyle='--')
plt.show()

#%%  绘制_calibration_curve
from sklearn.calibration import calibration_curve
import matplotlib.pyplot as plt

y_true = dataframe_total['label']
y_score = dataframe_total['pre_score']

prob_true, prob_pred = calibration_curve(y_true, y_score, n_bins=10)

plt.plot(prob_pred, prob_true, linewidth=1)
plt.plot([0, 1], [0, 1], color='black', linestyle='--')
plt.legend(["model"], loc="upper left")
plt.xlabel('Predicted Probability of Event')
plt.ylabel('Actual probability')
plt.show()
