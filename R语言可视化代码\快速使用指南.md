# R语言火山图快速使用指南

## 🚀 快速开始

### 1. 最简单的使用方法
直接运行完整代码（包含示例数据）：
```r
source("完整火山图代码.R")
```

### 2. 使用您自己的数据
将您的数据保存为CSV格式，包含以下列：
- `Gene`: 基因名称
- `log2FC`: log2 fold change值  
- `p_value`: p值

然后运行：
```r
source("使用真实数据绘制火山图.R")
```

## 📊 数据格式示例

### 最简格式
```csv
Gene,log2FC,p_value
TP53,2.5,0.001
BRCA1,-1.8,0.01
MYC,3.2,0.0001
EGFR,-2.1,0.005
```

### 完整格式（推荐）
```csv
Gene,log2FC,p_value,significance
TP53,2.5,0.001,Up-regulated
BRCA1,-1.8,0.01,Down-regulated
MYC,3.2,0.0001,Up-regulated
EGFR,-2.1,0.005,Down-regulated
```

## ⚙️ 主要参数设置

```r
# 阈值设置
FC_THRESHOLD <- 1.0      # log2 fold change阈值
P_THRESHOLD <- 0.05      # p值阈值

# 颜色设置
colors <- c("Up-regulated" = "#E74C3C",     # 上调基因-红色
            "Down-regulated" = "#3498DB",    # 下调基因-蓝色  
            "Not Significant" = "#95A5A6")   # 无显著差异-灰色

# 图片参数
point_size <- 2          # 点的大小
label_size <- 3          # 标签字体大小
```

## 🎨 火山图类型

1. **基础火山图** - 简单散点图
2. **带标注火山图** - 标注重要基因名称
3. **统计信息火山图** - 显示基因数量统计
4. **期刊风格火山图** - 符合发表标准的样式

## 📁 输出文件

运行后会在 `volcano_plots/` 目录生成：
- `basic_volcano.png` - 基础火山图
- `labeled_volcano.png` - 带标注火山图  
- `stats_volcano.png` - 统计信息火山图
- `journal_volcano.png` - 期刊风格火山图
- `volcano_data.csv` - 处理后的数据
- `top_genes.csv` - 最显著的基因列表

## 🔧 常见问题解决

### Q1: 如何修改阈值？
```r
FC_THRESHOLD <- 2.0      # 改为2倍变化
P_THRESHOLD <- 0.01      # 改为更严格的p值
```

### Q2: 如何更改颜色？
```r
scale_color_manual(values = c("Up-regulated" = "red", 
                             "Down-regulated" = "blue", 
                             "Not Significant" = "gray"))
```

### Q3: 如何标注特定基因？
```r
# 手动指定要标注的基因
genes_to_label <- volcano_data[volcano_data$Gene %in% c("TP53", "BRCA1", "MYC"), ]
```

### Q4: 如何调整图片大小？
```r
ggsave("volcano_plot.png", plot, width = 12, height = 10, dpi = 300)
```

## 📋 完整工作流程

1. **准备数据** - 确保CSV格式正确
2. **设置参数** - 修改阈值和颜色
3. **运行代码** - 执行R脚本
4. **检查结果** - 查看生成的图片和数据
5. **调整优化** - 根据需要修改参数重新运行

## 💡 使用技巧

- 使用 `示例数据.csv` 作为数据格式参考
- 先用示例数据测试代码是否正常运行
- 根据基因数量调整标注数量
- 保存为PDF格式适合期刊投稿
- 使用高DPI设置获得高质量图片

## 📞 需要帮助？

如果遇到问题，请检查：
1. R包是否正确安装
2. 数据格式是否正确
3. 文件路径是否存在
4. R版本是否兼容（推荐R >= 4.0）
