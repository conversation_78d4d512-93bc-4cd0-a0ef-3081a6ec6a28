{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 12. Different Size Transformation\n", "The process of image registration can be made faster, when smaller version of the fixed and moving images are used for calculation of the transformation parameters. These parameters can subsequently be used for the transformation of larger images. \n", "\n", "In this example image generators are used to get sparsely sampled and densely sampled versions of hypothetical fixed and moving images. An important note is that the width and the height of an image should remain the same. Downsampling of an image means decreasing the number of pixels by increasing the size (or spacing) of a pixel and thereby remaining the width and height of the image the same."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Elastix"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# To generate a downsampled image, the spacing of the image should be increased 10-fold in both directions,\n", "# when the number of pixels is decreased 10-fold in both directions.\n", "def image_generator(x1, x2, y1, y2, downsampled=False):\n", "    if downsampled:\n", "        image = np.zeros([100, 100], np.float32)\n", "    else:\n", "        image = np.zeros([1000, 1000], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    image_itk = itk.image_view_from_array(image)\n", "    if downsampled:\n", "        old_spacing = 1.0\n", "        factor = 1000 / 100\n", "        new_spacing = old_spacing * factor\n", "        image_itk.SetSpacing([new_spacing, new_spacing])\n", "        old_origin = 0.0\n", "        # The start of image's domain, origin-spacing/2.0, should be the same\n", "        new_origin = (new_spacing - old_spacing) / 2\n", "        image_itk.SetOrigin([new_origin, new_origin])\n", "    return image_itk"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create sparsely sampled images (fewer pixels) for registration\n", "fixed_image_sparse = image_generator(25,75,25,75, downsampled=True)\n", "moving_image_sparse = image_generator(0,45,10,75, downsampled=True)\n", "\n", "# .. and a densely sampled moving image (more pixels) for transformation\n", "moving_image_dense = image_generator(0,450,100,750)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_map_rigid = parameter_object.GetDefaultParameterMap('affine')\n", "parameter_object.AddParameterMap(parameter_map_rigid)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Registration with the registration function..."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image_small, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image_sparse, moving_image_sparse,\n", "    parameter_object=parameter_object)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transformix"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In order for the resampling to work as expected, the spatial domain of the densely sampled moving image should remain the same as the sparsely sampled moving image.\n", "\n", "The Origin is the location of the center of the lower left pixel. The start of the first pixel's domain is 0.5 * the spacing in a given direction. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["dense_origin = moving_image_dense.GetOrigin()\n", "sparse_origin = moving_image_sparse.GetOrigin()\n", "\n", "dense_spacing = moving_image_dense.GetSpacing()\n", "sparse_spacing = moving_image_sparse.GetSpacing()\n", "\n", "for dim in range(moving_image_dense.GetImageDimension()):\n", "    assert dense_origin[dim] - 0.5*dense_spacing[dim] == sparse_origin[dim] - 0.5*sparse_spacing[dim]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The spatial extent of an image with identity orientation/direction matrix is the spacing times the number of pixels in a given direction. The spatial extent should be the same, so:\n", "\n", "$$origin_1 * spacing_1 = origin_2 * spacing_2$$"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["dense_size = itk.size(moving_image_dense)\n", "sparse_size = itk.size(moving_image_sparse)\n", "\n", "for dim in range(moving_image_dense.GetImageDimension()):\n", "    assert dense_size[dim] * dense_spacing[dim] == sparse_size[dim] * sparse_spacing[dim]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Transformation can either be done in one line with the transformix function..."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["result_image_large = itk.transformix_filter(\n", "    moving_image_dense,\n", "    transform_parameter_object=result_transform_parameters,\n", "    log_to_console=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an transformix image filter object."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Load Transformix Object\n", "transformix_object = itk.TransformixFilter.New(moving_image_dense)\n", "transformix_object.SetTransformParameterObject(result_transform_parameters)\n", "\n", "# Update object (required)\n", "transformix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Transformation\n", "result_image_large = transformix_object.GetOutput()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}