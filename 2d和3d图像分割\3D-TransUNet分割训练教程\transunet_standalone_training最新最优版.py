#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D-TransUNet 独立训练脚本
整合nnUNet数据预处理和3D-TransUNet模型训练

特性:
- nnUNet风格的数据预处理
- 3D-TransUNet模型训练
- 完整的训练和验证循环
- 保存最佳和最后的模型权重
- 保存训练指标(dice, loss, lr)
- 支持断点续训
- 内存优化和错误处理

作者: AI Assistant
日期: 2024
"""

import os
import sys
import json
import pickle
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.amp import autocast, GradScaler
import SimpleITK as sitk
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.ndimage import binary_fill_holes
import time
import argparse

# 设置警告过滤
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

def setup_logging(log_file: str = "transunet_training.log") -> logging.Logger:
    """设置日志系统"""
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    logger = logging.getLogger('TransUNet')
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        # 文件handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

def setup_environment():
    """设置环境变量和路径"""
    base_dir = "/root/autodl-tmp"
    
    # 设置nnUNet环境变量
    env_vars = {
        'nnUNet_N_proc_DA': '8',
        'nnUNet_codebase': os.path.join(base_dir, "nnUNet"),
        'nnUNet_raw_data_base': os.path.join(base_dir, "nnUNet_raw_data_base"),
        'nnUNet_preprocessed': os.path.join(base_dir, "nnUNet_preprocessed"),
        'RESULTS_FOLDER': os.path.join(base_dir, "nnUNet_results")
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        os.makedirs(value, exist_ok=True) if key != 'nnUNet_N_proc_DA' else None
    
    # 添加3D-TransUNet路径
    transunet_path = os.path.join(base_dir, "3D-TransUNet")
    if os.path.exists(transunet_path) and transunet_path not in sys.path:
        sys.path.insert(0, transunet_path)
    
    return env_vars

class Config:
    """训练配置类"""
    def __init__(self):
        # 数据路径
        self.image_dir = "/root/autodl-tmp/120HCC/image/ap"
        self.mask_dir = "/root/autodl-tmp/120HCC/mask/ap"
        self.preprocessed_dir = "/root/autodl-tmp/TransUNet_preprocessed"
        self.output_dir = "/root/autodl-tmp/TransUNet_results"

        # 模型配置
        self.num_classes = 2
        self.input_channels = 1
        self.base_num_features = 32

        # 训练配置 - 自动优化版本
        self.batch_size = 4  # 增加batch size以提高训练稳定性
        self.num_epochs = 300  # 增加训练轮数
        self.initial_lr = 5e-4  # 提高初始学习率
        self.min_lr = 1e-8  # 降低最小学习率
        self.weight_decay = 1e-6  # 进一步减少权重衰减
        self.warmup_epochs = 30  # 增加预热轮数

        # 数据配置 - 自动优化版本
        self.patch_size = [64, 160, 160]  # 调整patch size平衡性能和内存
        self.target_spacing = [1.0, 1.0, 1.0]
        self.normalization_scheme = "zscore_robust"  # 使用更鲁棒的标准化
        self.crop_to_nonzero = True

        # 预处理优化配置
        self.skip_resampling_threshold = 0.1
        self.fast_preprocessing = True

        # TransUNet特定配置 - 简化版本
        self.vit_depth = 8  # 减少深度
        self.vit_hidden_size = 512  # 减少隐藏层大小
        self.max_hidden_dim = 128  # 减少最大隐藏维度
        self.num_queries = 16
        self.is_max_hungarian = False
        self.is_max_cls = True
        self.is_vit_pretrain = False
        self.is_max_bottleneck_transformer = False
        self.is_masked_attn = True
        self.max_dec_layers = 2  # 减少解码层数
        self.is_max_ms = False
        self.max_ms_idxs = [-2, -1]  # 减少多尺度索引
        self.mw = 1.0
        self.is_max_ds = True  # 启用深度监督
        self.is_max = False
        self.is_masking = False
        self.is_mhsa_float32 = True
        self.vit_layer_scale = True

        # 其他配置 - 自动优化版本
        self.mixed_precision = True
        self.deep_supervision = True
        self.save_interval = 5
        self.validation_interval = 1
        self.early_stopping_patience = 30  # 减少早停耐心
        self.gradient_clip_norm = 1.0  # 增加梯度裁剪

        # 类别权重配置（强化解决类别不平衡）
        self.use_class_weights = True
        self.class_weights = [0.05, 20.0]  # 进一步增强前景类权重

        # 数据增强配置
        self.use_data_augmentation = True
        self.augmentation_prob = 0.3  # 减少过于激进的数据增强

        # 自动优化配置
        self.auto_optimize = True
        self.target_dice = 0.5  # 目标Dice分数
        self.optimization_patience = 10  # 优化耐心
        self.max_optimization_rounds = 5  # 最大优化轮数

        # 移动平均配置
        self.moving_average_window = 5  # 移动平均窗口大小
        self.use_moving_average = True  # 启用移动平均
        
        # 创建输出目录
        os.makedirs(self.preprocessed_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
    
    def validate(self) -> bool:
        """验证配置"""
        if not os.path.exists(self.image_dir):
            print(f"错误: 图像目录不存在: {self.image_dir}")
            return False
        if not os.path.exists(self.mask_dir):
            print(f"错误: 标签目录不存在: {self.mask_dir}")
            return False
        return True
    
    def print_summary(self):
        """打印配置摘要"""
        print("\n" + "="*60)
        print("3D-TransUNet 训练配置")
        print("="*60)
        print(f"数据路径:")
        print(f"  图像目录: {self.image_dir}")
        print(f"  标签目录: {self.mask_dir}")
        print(f"  预处理目录: {self.preprocessed_dir}")
        print(f"  输出目录: {self.output_dir}")
        print(f"\n模型配置:")
        print(f"  类别数: {self.num_classes}")
        print(f"  输入通道: {self.input_channels}")
        print(f"  补丁大小: {self.patch_size}")
        print(f"\n训练配置:")
        print(f"  批次大小: {self.batch_size}")
        print(f"  训练轮数: {self.num_epochs}")
        print(f"  初始学习率: {self.initial_lr}")
        print(f"  权重衰减: {self.weight_decay}")
        print(f"  预热轮数: {self.warmup_epochs}")
        print(f"\nTransUNet配置:")
        print(f"  vit_depth: {self.vit_depth}")
        print(f"  max_hidden_dim: {self.max_hidden_dim}")
        print(f"  is_max_bottleneck_transformer: {self.is_max_bottleneck_transformer}")
        print(f"  is_masked_attn: {self.is_masked_attn}")
        print("="*60)

def install_dependencies():
    """安装必要的依赖包"""
    import subprocess
    import importlib
    
    required_packages = [
        'ml_collections', 'batchgenerators', 'medpy', 'einops',
        'fvcore', 'tensorboardX', 'termcolor', 'tabulate',
        'iopath', 'portalocker', 'omegaconf', 'hydra-core'
    ]
    
    print("检查并安装依赖包...")
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             capture_output=True, text=True, timeout=300)
                print(f"✅ {package} 安装成功")
            except Exception as e:
                print(f"❌ {package} 安装失败: {e}")

class nnUNetPreprocessor:
    """nnUNet风格的数据预处理器"""

    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.transpose_forward = [0, 1, 2]

    def load_image(self, image_path: str) -> Tuple[np.ndarray, Dict]:
        """加载医学图像"""
        try:
            sitk_image = sitk.ReadImage(image_path)
            image_array = sitk.GetArrayFromImage(sitk_image)

            # 获取图像属性
            properties = {
                'original_spacing': sitk_image.GetSpacing()[::-1],  # ITK uses xyz, we use zyx
                'original_size': sitk_image.GetSize()[::-1],
                'original_origin': sitk_image.GetOrigin()[::-1],
                'original_direction': sitk_image.GetDirection()
            }

            return image_array.astype(np.float32), properties

        except Exception as e:
            self.logger.error(f"加载图像失败 {image_path}: {e}")
            raise

    def crop_to_nonzero(self, data: np.ndarray, seg: np.ndarray = None) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """裁剪到非零区域"""
        nonzero_mask = data != 0

        # 找到非零区域的边界
        nonzero_coords = np.where(nonzero_mask)
        if len(nonzero_coords[0]) == 0:
            self.logger.warning("图像中没有非零像素")
            return data, seg, {}

        bbox = []
        for i in range(len(nonzero_coords)):
            bbox.append([np.min(nonzero_coords[i]), np.max(nonzero_coords[i]) + 1])

        # 裁剪数据
        cropped_data = data[bbox[0][0]:bbox[0][1],
                           bbox[1][0]:bbox[1][1],
                           bbox[2][0]:bbox[2][1]]

        cropped_seg = None
        if seg is not None:
            cropped_seg = seg[bbox[0][0]:bbox[0][1],
                             bbox[1][0]:bbox[1][1],
                             bbox[2][0]:bbox[2][1]]

        crop_properties = {
            'crop_bbox': bbox,
            'original_shape': data.shape,
            'cropped_shape': cropped_data.shape
        }

        return cropped_data, cropped_seg, crop_properties

    def resample_image(self, data: np.ndarray, original_spacing: List[float],
                      target_spacing: List[float], is_mask: bool = False) -> np.ndarray:
        """重采样图像到目标spacing（优化版本）"""
        from scipy.ndimage import zoom

        # 计算缩放因子
        zoom_factors = [orig / target for orig, target in zip(original_spacing, target_spacing)]

        # 检查是否需要重采样
        if all(abs(factor - 1.0) < 0.01 for factor in zoom_factors):
            return data.astype(np.float32)

        # 对于标签使用最近邻插值，对于图像使用三次插值
        order = 0 if is_mask else 1  # 降低插值阶数以提高速度

        # 重采样
        resampled_data = zoom(data, zoom_factors, order=order, mode='nearest', prefilter=False)

        return resampled_data.astype(np.float32)

    def normalize_image(self, data: np.ndarray, scheme: str = "noNorm",
                       mask: np.ndarray = None) -> np.ndarray:
        """图像归一化"""
        if scheme == "noNorm":
            return data
        elif scheme == "zscore":
            if mask is not None:
                mean = data[mask > 0].mean()
                std = data[mask > 0].std()
            else:
                mean = data.mean()
                std = data.std()
            return (data - mean) / (std + 1e-8)
        elif scheme == "zscore_robust":
            # 使用更鲁棒的标准化方法
            if mask is not None:
                foreground_data = data[mask > 0]
                if len(foreground_data) > 0:
                    # 使用中位数和MAD进行鲁棒标准化
                    median = np.median(foreground_data)
                    mad = np.median(np.abs(foreground_data - median))
                    return (data - median) / (mad * 1.4826 + 1e-8)
                else:
                    # 如果没有前景像素，使用全图像
                    median = np.median(data)
                    mad = np.median(np.abs(data - median))
                    return (data - median) / (mad * 1.4826 + 1e-8)
            else:
                # 使用百分位数进行鲁棒标准化
                p1, p99 = np.percentile(data, [1, 99])
                mean = np.mean(data[(data >= p1) & (data <= p99)])
                std = np.std(data[(data >= p1) & (data <= p99)])
                return (data - mean) / (std + 1e-8)
        elif scheme == "minmax":
            data_min = data.min()
            data_max = data.max()
            return (data - data_min) / (data_max - data_min + 1e-8)
        else:
            self.logger.warning(f"未知的归一化方案: {scheme}")
            return data

    def preprocess_case(self, image_path: str, mask_path: str) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """预处理单个病例"""
        # 加载图像和标签
        image, image_props = self.load_image(image_path)
        mask, mask_props = self.load_image(mask_path)

        # 确保图像和标签形状一致
        if image.shape != mask.shape:
            self.logger.error(f"图像和标签形状不匹配: {image.shape} vs {mask.shape}")
            raise ValueError("图像和标签形状不匹配")

        # 添加通道维度
        if len(image.shape) == 3:
            image = image[np.newaxis, ...]  # (1, D, H, W)
        if len(mask.shape) == 3:
            mask = mask[np.newaxis, ...]

        # 裁剪到非零区域
        if self.config.crop_to_nonzero:
            image, mask, crop_props = self.crop_to_nonzero(image[0], mask[0])
            image = image[np.newaxis, ...]
            mask = mask[np.newaxis, ...]
        else:
            crop_props = {}

        # 重采样（优化版本）
        original_spacing = image_props['original_spacing']
        target_spacing = self.config.target_spacing

        # 检查是否需要重采样
        spacing_diff = [abs(orig - target) for orig, target in zip(original_spacing, target_spacing)]
        max_diff = max(spacing_diff)

        if max_diff > self.config.skip_resampling_threshold:
            image_resampled = self.resample_image(
                image[0], original_spacing, target_spacing, is_mask=False
            )
            mask_resampled = self.resample_image(
                mask[0], original_spacing, target_spacing, is_mask=True
            )
            image = image_resampled[np.newaxis, ...]
            mask = mask_resampled[np.newaxis, ...]
        else:
            # 跳过重采样，spacing已经足够接近
            pass

        # 归一化
        image = self.normalize_image(image, self.config.normalization_scheme, mask)

        # 合并属性
        properties = {
            **image_props,
            **crop_props,
            'target_spacing': self.config.target_spacing,
            'normalization_scheme': self.config.normalization_scheme
        }

        return image.astype(np.float32), mask.astype(np.uint8), properties

    def preprocess_dataset(self, image_dir: str, mask_dir: str, output_dir: str,
                          max_workers: int = 4) -> bool:
        """预处理整个数据集并保存（优化版本）"""
        self.logger.info("开始数据预处理...")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 获取所有图像文件
        image_files = sorted(list(Path(image_dir).glob("*.nii.gz")))

        # 过滤出有对应标签的文件
        valid_pairs = []
        for img_file in image_files:
            img_name = img_file.stem.replace('.nii', '')
            mask_name = img_name + "-mask.nii.gz"
            mask_file = Path(mask_dir) / mask_name
            if mask_file.exists():
                valid_pairs.append((img_file, mask_file))
            else:
                self.logger.warning(f"跳过文件（未找到对应标签）: {img_file.name}")

        self.logger.info(f"找到 {len(valid_pairs)} 个有效的图像-标签对")

        # 创建数据集信息文件
        dataset_info = {
            'preprocessed_files': [],
            'preprocessing_config': {
                'target_spacing': self.config.target_spacing,
                'normalization_scheme': self.config.normalization_scheme,
                'crop_to_nonzero': self.config.crop_to_nonzero
            }
        }

        processed_count = 0
        progress_bar = tqdm(valid_pairs, desc="预处理数据")

        for img_file, mask_file in progress_bar:
            try:
                # 构建输出文件名
                img_name = img_file.stem.replace('.nii', '')
                output_name = img_name.replace('-ap', '')  # 简化文件名

                # 检查是否已经预处理过
                image_output = os.path.join(output_dir, f"{output_name}_image.npz")
                mask_output = os.path.join(output_dir, f"{output_name}_mask.npz")
                props_output = os.path.join(output_dir, f"{output_name}_props.pkl")

                if all(os.path.exists(f) for f in [image_output, mask_output, props_output]):
                    # 已存在，跳过预处理
                    try:
                        # 验证文件完整性
                        test_image = np.load(image_output)['data']
                        test_mask = np.load(mask_output)['data']
                        with open(props_output, 'rb') as f:
                            test_props = pickle.load(f)

                        # 记录文件信息
                        dataset_info['preprocessed_files'].append({
                            'case_name': output_name,
                            'original_image': str(img_file),
                            'original_mask': str(mask_file),
                            'preprocessed_image': image_output,
                            'preprocessed_mask': mask_output,
                            'properties': props_output,
                            'shape': test_image.shape
                        })

                        processed_count += 1
                        progress_bar.set_postfix({'已处理': processed_count, '状态': '跳过(已存在)'})
                        continue

                    except Exception:
                        # 文件损坏，重新预处理
                        pass

                # 预处理
                image, mask, properties = self.preprocess_case(str(img_file), str(mask_file))

                # 保存为压缩格式
                np.savez_compressed(image_output, data=image)
                np.savez_compressed(mask_output, data=mask)

                # 保存属性
                with open(props_output, 'wb') as f:
                    pickle.dump(properties, f)

                # 记录文件信息
                dataset_info['preprocessed_files'].append({
                    'case_name': output_name,
                    'original_image': str(img_file),
                    'original_mask': str(mask_file),
                    'preprocessed_image': image_output,
                    'preprocessed_mask': mask_output,
                    'properties': props_output,
                    'shape': image.shape
                })

                processed_count += 1
                progress_bar.set_postfix({'已处理': processed_count, '状态': '新处理'})

            except Exception as e:
                self.logger.error(f"预处理失败 {img_file.name}: {e}")
                continue

        # 保存数据集信息
        dataset_info_path = os.path.join(output_dir, 'dataset_info.json')
        with open(dataset_info_path, 'w') as f:
            json.dump(dataset_info, f, indent=2)

        self.logger.info(f"数据预处理完成！共处理 {processed_count} 个样本")
        self.logger.info(f"预处理数据保存在: {output_dir}")

        return processed_count > 0

class TransUNetDataset(Dataset):
    """3D-TransUNet数据集类"""

    def __init__(self, preprocessed_dir: str, patch_size: List[int], logger: logging.Logger,
                 use_preprocessed: bool = True):
        self.preprocessed_dir = Path(preprocessed_dir)
        self.patch_size = patch_size
        self.logger = logger
        self.use_preprocessed = use_preprocessed

        if use_preprocessed:
            # 加载预处理后的数据
            self._load_preprocessed_data()
        else:
            # 这里可以添加直接从原始数据加载的逻辑
            raise NotImplementedError("直接从原始数据加载暂未实现")

    def _load_preprocessed_data(self):
        """加载预处理后的数据信息"""
        dataset_info_path = self.preprocessed_dir / 'dataset_info.json'

        if not dataset_info_path.exists():
            raise FileNotFoundError(f"未找到数据集信息文件: {dataset_info_path}")

        with open(dataset_info_path, 'r') as f:
            self.dataset_info = json.load(f)

        self.preprocessed_files = self.dataset_info['preprocessed_files']
        self.logger.info(f"加载预处理数据集，包含 {len(self.preprocessed_files)} 个样本")

    def __len__(self):
        return len(self.preprocessed_files)

    def extract_patch(self, image: np.ndarray, mask: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """提取随机补丁"""
        _, D, H, W = image.shape  # 修复未使用变量警告
        pd, ph, pw = self.patch_size

        # 如果图像小于补丁大小，进行填充
        if D < pd or H < ph or W < pw:
            pad_d = max(0, pd - D)
            pad_h = max(0, ph - H)
            pad_w = max(0, pw - W)

            image = np.pad(image, ((0, 0), (0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            mask = np.pad(mask, ((0, 0), (0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            D, H, W = image.shape[1:]

        # 随机选择起始位置
        start_d = np.random.randint(0, max(1, D - pd + 1))
        start_h = np.random.randint(0, max(1, H - ph + 1))
        start_w = np.random.randint(0, max(1, W - pw + 1))

        # 提取补丁
        image_patch = image[:, start_d:start_d+pd, start_h:start_h+ph, start_w:start_w+pw]
        mask_patch = mask[:, start_d:start_d+pd, start_h:start_h+ph, start_w:start_w+pw]

        return image_patch, mask_patch

    def __getitem__(self, idx):
        try:
            # 加载预处理后的数据
            file_info = self.preprocessed_files[idx]

            # 加载图像和标签
            image_data = np.load(file_info['preprocessed_image'])['data']
            mask_data = np.load(file_info['preprocessed_mask'])['data']

            # 提取补丁
            image_patch, mask_patch = self.extract_patch(image_data, mask_data)

            # 转换为tensor
            image_tensor = torch.from_numpy(image_patch).float()
            mask_tensor = torch.from_numpy(mask_patch).long()

            return image_tensor, mask_tensor

        except Exception as e:
            self.logger.error(f"加载数据时出错 (idx={idx}): {e}")
            # 返回零张量作为fallback
            image_tensor = torch.zeros((1, *self.patch_size), dtype=torch.float32)
            mask_tensor = torch.zeros((1, *self.patch_size), dtype=torch.long)
            return image_tensor, mask_tensor

def try_import_transunet():
    """尝试导入TransUNet模块"""
    try:
        from nn_transunet.networks.transunet3d_model import Generic_TransUNet_max_ppbp, InitWeights_He
        from nn_transunet.trainer.loss_functions import DC_and_CE_loss, SoftDiceLoss
        return Generic_TransUNet_max_ppbp, DC_and_CE_loss, SoftDiceLoss, True
    except ImportError as e:
        print(f"无法导入TransUNet模块: {e}")
        print("将使用简化的模型定义")
        return None, None, None, False

class SimpleTransUNet(nn.Module):
    """简化的TransUNet模型（如果无法导入原始模块时使用）"""

    def __init__(self, input_channels=1, num_classes=2, base_features=32):
        super().__init__()

        # 编码器
        self.encoder1 = self._make_layer(input_channels, base_features)
        self.encoder2 = self._make_layer(base_features, base_features * 2)
        self.encoder3 = self._make_layer(base_features * 2, base_features * 4)
        self.encoder4 = self._make_layer(base_features * 4, base_features * 8)

        # 瓶颈层
        self.bottleneck = self._make_layer(base_features * 8, base_features * 16)

        # 解码器
        self.decoder4 = self._make_layer(base_features * 16 + base_features * 8, base_features * 8)
        self.decoder3 = self._make_layer(base_features * 8 + base_features * 4, base_features * 4)
        self.decoder2 = self._make_layer(base_features * 4 + base_features * 2, base_features * 2)
        self.decoder1 = self._make_layer(base_features * 2 + base_features, base_features)

        # 输出层
        self.final_conv = nn.Conv3d(base_features, num_classes, kernel_size=1)

        # 池化和上采样
        self.pool = nn.MaxPool3d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=False)

    def _make_layer(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv3d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm3d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv3d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm3d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        # 编码路径
        enc1 = self.encoder1(x)
        enc2 = self.encoder2(self.pool(enc1))
        enc3 = self.encoder3(self.pool(enc2))
        enc4 = self.encoder4(self.pool(enc3))

        # 瓶颈
        bottleneck = self.bottleneck(self.pool(enc4))

        # 解码路径
        dec4 = self.decoder4(torch.cat([self.upsample(bottleneck), enc4], dim=1))
        dec3 = self.decoder3(torch.cat([self.upsample(dec4), enc3], dim=1))
        dec2 = self.decoder2(torch.cat([self.upsample(dec3), enc2], dim=1))
        dec1 = self.decoder1(torch.cat([self.upsample(dec2), enc1], dim=1))

        # 输出
        output = self.final_conv(dec1)
        return output

class DiceLoss(nn.Module):
    """Dice损失函数"""

    def __init__(self, smooth=1e-5):
        super().__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        # 应用softmax
        pred = torch.softmax(pred, dim=1)

        # 确保target的维度正确
        if target.dim() == 5:  # (B, 1, D, H, W)
            target = target.squeeze(1)  # (B, D, H, W)

        # 转换为one-hot编码
        target_one_hot = torch.zeros_like(pred)
        target_one_hot.scatter_(1, target.unsqueeze(1), 1)

        # 计算Dice系数
        intersection = (pred * target_one_hot).sum(dim=(2, 3, 4))
        union = pred.sum(dim=(2, 3, 4)) + target_one_hot.sum(dim=(2, 3, 4))

        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        return 1 - dice.mean()

class CombinedLoss(nn.Module):
    """组合损失函数（Dice + CrossEntropy）"""

    def __init__(self, dice_weight=0.5, ce_weight=0.5, class_weights=None):
        super().__init__()
        self.dice_weight = dice_weight
        self.ce_weight = ce_weight
        self.dice_loss = DiceLoss()

        # 支持类别权重的交叉熵损失
        if class_weights is not None:
            self.class_weights = torch.tensor(class_weights, dtype=torch.float32)
            self.use_class_weights = True
        else:
            self.class_weights = None
            self.use_class_weights = False

        self.ce_loss = nn.CrossEntropyLoss()

    def forward(self, pred, target):
        dice = self.dice_loss(pred, target)

        # 确保target的维度正确用于CrossEntropy
        if target.dim() == 5:  # (B, 1, D, H, W)
            target_ce = target.squeeze(1)  # (B, D, H, W)
        else:
            target_ce = target

        # 计算交叉熵损失，支持类别权重
        if self.use_class_weights and self.class_weights is not None:
            # 将类别权重移动到正确的设备
            if self.class_weights.device != pred.device:
                self.class_weights = self.class_weights.to(pred.device)

            # 手动计算加权交叉熵
            log_probs = torch.log_softmax(pred, dim=1)
            target_one_hot = torch.zeros_like(log_probs)
            target_one_hot.scatter_(1, target_ce.unsqueeze(1), 1)

            # 应用类别权重
            weights = self.class_weights[target_ce]
            ce = -(target_one_hot * log_probs).sum(dim=1) * weights
            ce = ce.mean()
        else:
            ce = self.ce_loss(pred, target_ce)

        return self.dice_weight * dice + self.ce_weight * ce

def calculate_dice_score(pred, target, num_classes=2):
    """计算Dice分数"""
    pred = torch.softmax(pred, dim=1)
    pred = torch.argmax(pred, dim=1)

    # 确保target的维度正确
    if target.dim() == 5:  # (B, 1, D, H, W)
        target = target.squeeze(1)  # (B, D, H, W)

    dice_scores = []
    for class_idx in range(num_classes):
        pred_class = (pred == class_idx).float()
        target_class = (target == class_idx).float()

        intersection = (pred_class * target_class).sum()
        union = pred_class.sum() + target_class.sum()

        if union == 0:
            dice = 1.0 if intersection == 0 else 0.0
        else:
            dice = (2 * intersection) / union

        # 安全地获取dice值
        if hasattr(dice, 'item'):
            dice_scores.append(dice.item())
        else:
            dice_scores.append(float(dice))

    return dice_scores

class Trainer:
    """训练器类"""

    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 尝试导入TransUNet模型
        TransUNet, DCCELoss, SoftDiceLoss, import_success = try_import_transunet()

        if import_success:
            self.logger.info("成功导入TransUNet模块，使用原始模型")
            try:
                # 计算合适的num_pool和max_ms_idxs
                num_pool = 3  # 减少池化层数以适应较小的patch size
                # 确保max_ms_idxs不超出ds_feats的范围
                # ds_feats长度为num_pool+1，所以有效索引为0到num_pool
                safe_max_ms_idxs = [-3, -2, -1]  # 对应ds_feats的最后3个元素

                # 使用原始TransUNet模型，修复参数
                self.model = TransUNet(
                    input_channels=config.input_channels,
                    base_num_features=config.base_num_features,
                    num_classes=config.num_classes,
                    num_pool=num_pool,
                    patch_size=config.patch_size,
                    conv_op=torch.nn.Conv3d,  # 明确指定3D卷积
                    norm_op=torch.nn.BatchNorm3d,  # 明确指定3D BatchNorm
                    dropout_op=torch.nn.Dropout3d,  # 明确指定3D Dropout
                    deep_supervision=True,  # 启用深度监督
                    vit_depth=config.vit_depth,
                    vit_hidden_size=config.vit_hidden_size,
                    max_hidden_dim=config.max_hidden_dim,
                    is_max_bottleneck_transformer=config.is_max_bottleneck_transformer,
                    is_masked_attn=config.is_masked_attn,
                    max_dec_layers=config.max_dec_layers,
                    is_max_ms=config.is_max_ms,
                    max_ms_idxs=safe_max_ms_idxs,  # 使用安全的索引
                    mw=config.mw,
                    is_max_ds=config.is_max_ds,
                    is_masking=config.is_masking,
                    num_queries=config.num_queries,
                    is_max_cls=config.is_max_cls,
                    is_mhsa_float32=config.is_mhsa_float32,
                    vit_layer_scale=config.vit_layer_scale,
                    is_max=config.is_max
                )
                self.criterion = DCCELoss({'batch_dice': True, 'smooth': 1e-5, 'do_bg': False}, {})
                self.logger.info("原始TransUNet模型初始化成功")
            except Exception as e:
                self.logger.warning(f"原始TransUNet模型初始化失败: {e}")
                self.logger.info("回退到简化模型")
                self.model = SimpleTransUNet(
                    input_channels=config.input_channels,
                    num_classes=config.num_classes,
                    base_features=config.base_num_features
                )
                # 使用类别权重的损失函数
                class_weights = config.class_weights if config.use_class_weights else None
                self.criterion = CombinedLoss(class_weights=class_weights)
        else:
            self.logger.info("使用简化的TransUNet模型")
            self.model = SimpleTransUNet(
                input_channels=config.input_channels,
                num_classes=config.num_classes,
                base_features=config.base_num_features
            )
            # 使用类别权重的损失函数
            class_weights = config.class_weights if config.use_class_weights else None
            self.criterion = CombinedLoss(class_weights=class_weights)

        self.model.to(self.device)

        # 优化器和调度器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.initial_lr,
            weight_decay=config.weight_decay
        )

        # 使用更稳定的学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',  # 监控验证Dice分数（越大越好）
            factor=0.5,  # 学习率减半
            patience=15,  # 15个epoch没有改善就降低学习率
            min_lr=config.min_lr,
            verbose=True
        )

        # 混合精度训练
        self.scaler = GradScaler() if config.mixed_precision else None

        # 训练状态
        self.current_epoch = 0
        self.best_dice = 0.0
        self.train_losses = []
        self.val_losses = []
        self.train_dice_scores = []
        self.val_dice_scores = []
        self.learning_rates = []

        # 早停
        self.patience_counter = 0

        # 自动优化相关
        self.optimization_round = 0
        self.best_config = None
        self.optimization_history = []

        # 移动平均相关
        self.train_dice_history = []
        self.val_dice_history = []
        self.train_loss_history = []
        self.val_loss_history = []
        self.lr_history = []

    def save_checkpoint(self, is_best=False, is_final=False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_dice': self.best_dice,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_dice_scores': self.train_dice_scores,
            'val_dice_scores': self.val_dice_scores,
            'learning_rates': self.learning_rates,
            'config': self.config.__dict__
        }

        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()

        # 保存最新检查点
        latest_path = os.path.join(self.config.output_dir, 'model_latest.pth')
        torch.save(checkpoint, latest_path)

        # 保存最佳检查点
        if is_best:
            best_path = os.path.join(self.config.output_dir, 'model_best.pth')
            torch.save(checkpoint, best_path)
            self.logger.info(f"保存最佳模型，Dice分数: {self.best_dice:.4f}")

        # 保存最终检查点
        if is_final:
            final_path = os.path.join(self.config.output_dir, 'model_final.pth')
            torch.save(checkpoint, final_path)
            self.logger.info("保存最终模型")

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        if os.path.exists(checkpoint_path):
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            self.current_epoch = checkpoint['epoch']
            self.best_dice = checkpoint['best_dice']
            self.train_losses = checkpoint.get('train_losses', [])
            self.val_losses = checkpoint.get('val_losses', [])
            self.train_dice_scores = checkpoint.get('train_dice_scores', [])
            self.val_dice_scores = checkpoint.get('val_dice_scores', [])
            self.learning_rates = checkpoint.get('learning_rates', [])

            if self.scaler and 'scaler_state_dict' in checkpoint:
                self.scaler.load_state_dict(checkpoint['scaler_state_dict'])

            self.logger.info(f"从epoch {self.current_epoch}恢复训练，最佳Dice: {self.best_dice:.4f}")
            return True
        return False

    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_dice = [0.0] * self.config.num_classes
        num_batches = len(train_loader)

        progress_bar = tqdm(train_loader, desc=f"训练 Epoch {self.current_epoch}")

        for batch_idx, (images, masks) in enumerate(progress_bar):
            images = images.to(self.device)
            masks = masks.to(self.device)

            self.optimizer.zero_grad()

            if self.scaler:
                with autocast('cuda'):
                    outputs = self.model(images)
                    # 处理深度监督输出
                    if isinstance(outputs, (tuple, list)):
                        # 深度监督模式：只使用主输出计算损失
                        main_output = outputs[0]
                        loss = self.criterion(main_output, masks)
                    else:
                        loss = self.criterion(outputs, masks)

                self.scaler.scale(loss).backward()

                # 梯度裁剪
                if self.config.gradient_clip_norm > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip_norm)

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(images)
                # 处理深度监督输出
                if isinstance(outputs, (tuple, list)):
                    # 深度监督模式：只使用主输出计算损失
                    main_output = outputs[0]
                    loss = self.criterion(main_output, masks)
                else:
                    loss = self.criterion(outputs, masks)
                loss.backward()

                # 梯度裁剪
                if self.config.gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip_norm)

                self.optimizer.step()

            # 计算指标
            total_loss += loss.item()
            # 使用主输出计算指标
            if isinstance(outputs, (tuple, list)):
                outputs_for_metrics = outputs[0]
            else:
                outputs_for_metrics = outputs
            dice_scores = calculate_dice_score(outputs_for_metrics, masks, self.config.num_classes)
            for i, score in enumerate(dice_scores):
                total_dice[i] += score

            # 更新进度条
            avg_loss = total_loss / (batch_idx + 1)
            avg_dice = total_dice[1] / (batch_idx + 1) if len(total_dice) > 1 else 0  # 前景类Dice
            progress_bar.set_postfix({
                'Loss': f'{avg_loss:.4f}',
                'Dice': f'{avg_dice:.4f}',
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
            })

        # 计算平均指标
        avg_loss = total_loss / num_batches
        avg_dice_scores = [score / num_batches for score in total_dice]

        return avg_loss, avg_dice_scores

    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        total_dice = [0.0] * self.config.num_classes
        num_batches = len(val_loader)

        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc=f"验证 Epoch {self.current_epoch}")

            for batch_idx, (images, masks) in enumerate(progress_bar):
                images = images.to(self.device)
                masks = masks.to(self.device)

                if self.scaler:
                    with autocast('cuda'):
                        outputs = self.model(images)
                        # 处理深度监督输出
                        if isinstance(outputs, (tuple, list)):
                            main_output = outputs[0]
                            loss = self.criterion(main_output, masks)
                            # 使用主输出计算指标
                            outputs_for_metrics = main_output
                        else:
                            loss = self.criterion(outputs, masks)
                            outputs_for_metrics = outputs
                else:
                    outputs = self.model(images)
                    # 处理深度监督输出
                    if isinstance(outputs, (tuple, list)):
                        main_output = outputs[0]
                        loss = self.criterion(main_output, masks)
                        # 使用主输出计算指标
                        outputs_for_metrics = main_output
                    else:
                        loss = self.criterion(outputs, masks)
                        outputs_for_metrics = outputs

                # 计算指标
                total_loss += loss.item()
                dice_scores = calculate_dice_score(outputs_for_metrics, masks, self.config.num_classes)
                for i, score in enumerate(dice_scores):
                    total_dice[i] += score

                # 更新进度条
                avg_loss = total_loss / (batch_idx + 1)
                avg_dice = total_dice[1] / (batch_idx + 1) if len(total_dice) > 1 else 0
                progress_bar.set_postfix({
                    'Loss': f'{avg_loss:.4f}',
                    'Dice': f'{avg_dice:.4f}'
                })

        # 计算平均指标
        avg_loss = total_loss / num_batches
        avg_dice_scores = [score / num_batches for score in total_dice]

        return avg_loss, avg_dice_scores

    def save_metrics(self):
        """保存训练指标为CSV格式"""
        import pandas as pd

        # 准备数据
        max_len = max(len(self.train_losses), len(self.val_losses),
                     len(self.train_dice_scores), len(self.val_dice_scores),
                     len(self.learning_rates))

        # 创建DataFrame
        data = {
            'epoch': list(range(1, max_len + 1)),
            'train_loss': self.train_losses + [None] * (max_len - len(self.train_losses)),
            'val_loss': self.val_losses + [None] * (max_len - len(self.val_losses)),
            'learning_rate': self.learning_rates + [None] * (max_len - len(self.learning_rates))
        }

        # 添加Dice分数（前景类）
        train_dice_fg = [scores[1] if len(scores) > 1 else 0.0 for scores in self.train_dice_scores]
        val_dice_fg = [scores[1] if len(scores) > 1 else 0.0 for scores in self.val_dice_scores]

        data['train_dice'] = train_dice_fg + [None] * (max_len - len(train_dice_fg))
        data['val_dice'] = val_dice_fg + [None] * (max_len - len(val_dice_fg))

        # 添加移动平均Dice
        if self.config.use_moving_average:
            train_dice_ma = []
            val_dice_ma = []

            for i in range(len(train_dice_fg)):
                # 计算到当前epoch的移动平均
                window_start = max(0, i + 1 - self.config.moving_average_window)
                train_ma = sum(train_dice_fg[window_start:i+1]) / (i + 1 - window_start)
                train_dice_ma.append(train_ma)

            for i in range(len(val_dice_fg)):
                window_start = max(0, i + 1 - self.config.moving_average_window)
                val_ma = sum(val_dice_fg[window_start:i+1]) / (i + 1 - window_start)
                val_dice_ma.append(val_ma)

            data['train_dice_ma'] = train_dice_ma + [None] * (max_len - len(train_dice_ma))
            data['val_dice_ma'] = val_dice_ma + [None] * (max_len - len(val_dice_ma))

        # 保存为CSV
        df = pd.DataFrame(data)
        metrics_path = os.path.join(self.config.output_dir, 'training_metrics.csv')
        df.to_csv(metrics_path, index=False)

        # 保存摘要信息
        summary_data = {
            'metric': ['best_dice', 'best_epoch', 'final_train_loss', 'final_val_loss', 'total_epochs'],
            'value': [
                self.best_dice,
                getattr(self, 'best_epoch', 0),
                self.train_losses[-1] if self.train_losses else 0.0,
                self.val_losses[-1] if self.val_losses else 0.0,
                len(self.train_losses)
            ]
        }

        if self.config.use_moving_average and val_dice_fg:
            summary_data['metric'].append('final_val_dice_ma')
            summary_data['value'].append(val_dice_ma[-1] if val_dice_ma else 0.0)

        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(self.config.output_dir, 'training_summary.csv')
        summary_df.to_csv(summary_path, index=False)

        self.logger.info(f"训练指标已保存到: {metrics_path}")
        self.logger.info(f"训练摘要已保存到: {summary_path}")

        # 绘制训练曲线
        self.plot_training_curves()

    def plot_training_curves(self):
        """绘制训练曲线"""
        if len(self.train_losses) == 0:
            return

        epochs = range(1, len(self.train_losses) + 1)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 损失曲线
        ax1.plot(epochs, self.train_losses, 'b-', label='训练损失')
        if self.val_losses:
            ax1.plot(epochs, self.val_losses, 'r-', label='验证损失')
        ax1.set_title('损失曲线')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)

        # Dice分数曲线
        if self.train_dice_scores:
            train_dice_fg = [scores[1] if len(scores) > 1 else 0 for scores in self.train_dice_scores]
            ax2.plot(epochs, train_dice_fg, 'b-', alpha=0.7, label='训练Dice')

            # 添加移动平均线
            if self.config.use_moving_average and len(train_dice_fg) > 1:
                train_dice_ma = []
                for i in range(len(train_dice_fg)):
                    window_start = max(0, i + 1 - self.config.moving_average_window)
                    ma = sum(train_dice_fg[window_start:i+1]) / (i + 1 - window_start)
                    train_dice_ma.append(ma)
                ax2.plot(epochs, train_dice_ma, 'b-', linewidth=2, label='训练Dice(移动平均)')

        if self.val_dice_scores:
            val_dice_fg = [scores[1] if len(scores) > 1 else 0 for scores in self.val_dice_scores]
            ax2.plot(epochs, val_dice_fg, 'r-', alpha=0.7, label='验证Dice')

            # 添加移动平均线
            if self.config.use_moving_average and len(val_dice_fg) > 1:
                val_dice_ma = []
                for i in range(len(val_dice_fg)):
                    window_start = max(0, i + 1 - self.config.moving_average_window)
                    ma = sum(val_dice_fg[window_start:i+1]) / (i + 1 - window_start)
                    val_dice_ma.append(ma)
                ax2.plot(epochs, val_dice_ma, 'r-', linewidth=2, label='验证Dice(移动平均)')

        ax2.set_title('Dice分数曲线')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Dice Score')
        ax2.legend()
        ax2.grid(True)

        # 学习率曲线
        if self.learning_rates:
            ax3.plot(epochs, self.learning_rates, 'g-', label='学习率')
            ax3.set_title('学习率曲线')
            ax3.set_xlabel('Epoch')
            ax3.set_ylabel('Learning Rate')
            ax3.legend()
            ax3.grid(True)

        # 训练总结
        ax4.axis('off')
        # 安全获取最终值
        final_val_loss = f"{self.val_losses[-1]:.4f}" if self.val_losses else "N/A"
        final_lr = f"{self.learning_rates[-1]:.6f}" if self.learning_rates else "N/A"

        summary_text = f"""训练总结:

总轮数: {len(self.train_losses)}
最佳验证Dice: {self.best_dice:.4f}
最终训练损失: {self.train_losses[-1]:.4f}
最终验证损失: {final_val_loss}
最终学习率: {final_lr}
        """
        ax4.text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center')

        plt.tight_layout()
        plt.savefig(os.path.join(self.config.output_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def calculate_moving_average(self, values, window_size):
        """计算移动平均"""
        if len(values) < window_size:
            return sum(values) / len(values) if values else 0.0

        return sum(values[-window_size:]) / window_size

    def get_moving_average_dice(self, dice_type='val'):
        """获取移动平均Dice分数"""
        if not self.config.use_moving_average:
            return self.best_dice

        if dice_type == 'val':
            history = self.val_dice_history
        else:
            history = self.train_dice_history

        if not history:
            return 0.0

        return self.calculate_moving_average(history, self.config.moving_average_window)

    def optimize_hyperparameters(self):
        """自动优化超参数"""
        if not self.config.auto_optimize:
            return False

        if self.best_dice >= self.config.target_dice:
            self.logger.info(f"已达到目标Dice分数 {self.config.target_dice}，停止优化")
            return False

        if self.optimization_round >= self.config.max_optimization_rounds:
            self.logger.info(f"已达到最大优化轮数 {self.config.max_optimization_rounds}，停止优化")
            return False

        self.optimization_round += 1
        self.logger.info(f"开始第 {self.optimization_round} 轮超参数优化")

        # 记录当前配置和结果
        current_config = {
            'round': self.optimization_round,
            'lr': self.config.initial_lr,
            'batch_size': self.config.batch_size,
            'class_weights': self.config.class_weights.copy(),
            'patch_size': self.config.patch_size.copy(),
            'best_dice': self.best_dice
        }
        self.optimization_history.append(current_config)

        # 根据当前结果调整参数
        if self.best_dice < 0.1:
            # Dice分数太低，可能是类别不平衡问题
            self.config.class_weights[1] *= 2.0  # 增加前景类权重
            self.config.initial_lr *= 1.5  # 增加学习率
            self.logger.info(f"Dice分数过低，增加前景类权重到 {self.config.class_weights[1]}")

        elif self.best_dice < 0.3:
            # 中等Dice分数，调整学习率和patch size
            self.config.initial_lr *= 0.8  # 降低学习率
            if self.config.patch_size[0] > 32:
                self.config.patch_size = [max(32, self.config.patch_size[0] - 16),
                                        max(96, self.config.patch_size[1] - 32),
                                        max(96, self.config.patch_size[2] - 32)]
            self.logger.info(f"调整学习率到 {self.config.initial_lr}，patch size到 {self.config.patch_size}")

        elif self.best_dice < 0.5:
            # 接近目标，微调参数
            self.config.initial_lr *= 0.7
            self.config.class_weights[1] *= 1.2
            self.logger.info(f"微调参数：学习率 {self.config.initial_lr}，前景类权重 {self.config.class_weights[1]}")

        # 重新初始化优化器和调度器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config.initial_lr,
            weight_decay=self.config.weight_decay
        )

        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',
            factor=0.5,
            patience=15,
            min_lr=self.config.min_lr,
            verbose=True
        )

        # 重新初始化损失函数（如果使用类别权重）
        if hasattr(self.criterion, 'class_weights'):
            self.criterion.class_weights = torch.tensor(self.config.class_weights, dtype=torch.float32)

        # 重置训练状态
        self.patience_counter = 0

        return True

def create_data_loaders(config: Config, logger: logging.Logger):
    """创建数据加载器"""
    # 创建数据集
    dataset = TransUNetDataset(
        config.preprocessed_dir,
        config.patch_size,
        logger,
        use_preprocessed=True
    )

    # 划分训练集和验证集
    dataset_size = len(dataset)
    train_size = int(0.8 * dataset_size)
    val_size = dataset_size - train_size

    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )

    logger.info(f"数据集划分: 训练集 {train_size}, 验证集 {val_size}")

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )

    return train_loader, val_loader

def run_training(config: Config, logger: logging.Logger):
    """运行完整的训练流程"""
    logger.info("开始训练流程")

    # 创建预处理器
    preprocessor = nnUNetPreprocessor(config, logger)

    # 检查是否需要预处理数据
    dataset_info_path = os.path.join(config.preprocessed_dir, 'dataset_info.json')
    if not os.path.exists(dataset_info_path):
        logger.info("未找到预处理数据，开始数据预处理...")
        success = preprocessor.preprocess_dataset(
            config.image_dir,
            config.mask_dir,
            config.preprocessed_dir
        )
        if not success:
            logger.error("数据预处理失败")
            return
    else:
        logger.info("发现预处理数据，跳过预处理步骤")

    # 创建数据加载器
    train_loader, val_loader = create_data_loaders(config, logger)

    # 创建训练器
    trainer = Trainer(config, logger)

    # 尝试加载检查点
    checkpoint_path = os.path.join(config.output_dir, 'model_latest.pth')
    if os.path.exists(checkpoint_path):
        logger.info("发现检查点，尝试恢复训练...")
        trainer.load_checkpoint(checkpoint_path)

    start_epoch = max(1, trainer.current_epoch + 1)
    logger.info(f"开始训练，从epoch {start_epoch}到{config.num_epochs}")

    try:
        for epoch in range(start_epoch, config.num_epochs + 1):
            trainer.current_epoch = epoch

            # 训练
            train_loss, train_dice = trainer.train_epoch(train_loader)
            trainer.train_losses.append(train_loss)
            trainer.train_dice_scores.append(train_dice)

            # 记录移动平均历史
            train_dice_fg = train_dice[1] if len(train_dice) > 1 else train_dice[0]
            trainer.train_dice_history.append(train_dice_fg)
            trainer.train_loss_history.append(train_loss)

            # 验证
            if epoch % config.validation_interval == 0:
                val_loss, val_dice = trainer.validate_epoch(val_loader)
                trainer.val_losses.append(val_loss)
                trainer.val_dice_scores.append(val_dice)

                # 记录学习率
                current_lr = trainer.optimizer.param_groups[0]['lr']
                trainer.learning_rates.append(current_lr)

                # 记录移动平均历史
                val_dice_fg = val_dice[1] if len(val_dice) > 1 else val_dice[0]
                trainer.val_dice_history.append(val_dice_fg)
                trainer.val_loss_history.append(val_loss)
                trainer.lr_history.append(current_lr)

                # 检查是否为最佳模型
                val_dice_fg = val_dice[1] if len(val_dice) > 1 else val_dice[0]
                is_best = val_dice_fg > trainer.best_dice
                if is_best:
                    trainer.best_dice = val_dice_fg
                    trainer.best_epoch = epoch
                    trainer.patience_counter = 0
                else:
                    trainer.patience_counter += 1

                # 计算移动平均Dice
                train_dice_ma = trainer.calculate_moving_average(
                    trainer.train_dice_history, config.moving_average_window
                )
                val_dice_ma = trainer.calculate_moving_average(
                    trainer.val_dice_history, config.moving_average_window
                )

                # 记录日志
                log_msg = (
                    f"Epoch {epoch}/{config.num_epochs} - "
                    f"训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}, "
                    f"训练Dice: {train_dice[1] if len(train_dice) > 1 else train_dice[0]:.4f}, "
                    f"验证Dice: {val_dice_fg:.4f}, "
                    f"学习率: {current_lr:.6f}"
                )

                if config.use_moving_average:
                    log_msg += f", 训练Dice(MA): {train_dice_ma:.4f}, 验证Dice(MA): {val_dice_ma:.4f}"

                logger.info(log_msg)

                # 保存检查点
                trainer.save_checkpoint(is_best=is_best)

                # 早停检查
                if trainer.patience_counter >= config.early_stopping_patience:
                    logger.info(f"验证Dice在{config.early_stopping_patience}个epoch内没有改善，提前停止训练")

                    # 尝试自动优化
                    if trainer.optimize_hyperparameters():
                        logger.info("开始新一轮优化训练")
                        continue  # 继续训练
                    else:
                        break  # 停止训练

            # 更新学习率（基于验证Dice分数）
            if epoch % config.validation_interval == 0:
                trainer.scheduler.step(val_dice_fg)

            # 定期保存指标
            if epoch % config.save_interval == 0:
                trainer.save_metrics()

        # 保存最终模型和指标
        trainer.save_checkpoint(is_final=True)
        trainer.save_metrics()

        # 保存优化历史
        if trainer.optimization_history:
            optimization_path = os.path.join(config.output_dir, 'optimization_history.json')
            with open(optimization_path, 'w') as f:
                json.dump(trainer.optimization_history, f, indent=2)
            logger.info(f"优化历史已保存到: {optimization_path}")

        logger.info("训练完成！")
        logger.info(f"最佳验证Dice分数: {trainer.best_dice:.4f}")
        logger.info(f"总共进行了 {trainer.optimization_round} 轮参数优化")

    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        trainer.save_checkpoint()
        trainer.save_metrics()
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        trainer.save_checkpoint()
        trainer.save_metrics()
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='3D-TransUNet 独立训练脚本')
    parser.add_argument('--image_dir', type=str, default='/root/autodl-tmp/120HCC/image/ap',
                       help='图像目录路径')
    parser.add_argument('--mask_dir', type=str, default='/root/autodl-tmp/120HCC/mask/ap',
                       help='标签目录路径')
    parser.add_argument('--output_dir', type=str, default='/root/autodl-tmp/TransUNet_results',
                       help='输出目录路径')
    parser.add_argument('--batch_size', type=int, default=4,
                       help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=300,
                       help='训练轮数')
    parser.add_argument('--initial_lr', type=float, default=5e-4,
                       help='初始学习率')
    parser.add_argument('--patch_size', nargs=3, type=int, default=[64, 160, 160],
                       help='补丁大小 [D, H, W]')
    parser.add_argument('--auto_optimize', action='store_true', default=True,
                       help='启用自动参数优化')
    parser.add_argument('--target_dice', type=float, default=0.5,
                       help='目标Dice分数')

    args = parser.parse_args()

    # 设置环境
    setup_environment()
    logger = setup_logging()

    # 安装依赖
    install_dependencies()

    # 创建配置
    config = Config()

    # 更新配置参数
    config.image_dir = args.image_dir
    config.mask_dir = args.mask_dir
    config.output_dir = args.output_dir
    config.batch_size = args.batch_size
    config.num_epochs = args.num_epochs
    config.initial_lr = args.initial_lr
    config.patch_size = args.patch_size
    config.auto_optimize = args.auto_optimize
    config.target_dice = args.target_dice

    # 创建输出目录
    os.makedirs(config.output_dir, exist_ok=True)

    if not config.validate():
        logger.error("配置验证失败")
        return

    config.print_summary()

    # 检查GPU
    if torch.cuda.is_available():
        logger.info(f"使用GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        logger.warning("未检测到GPU，将使用CPU训练（速度较慢）")

    # 运行训练
    run_training(config, logger)

if __name__ == "__main__":
    main()
