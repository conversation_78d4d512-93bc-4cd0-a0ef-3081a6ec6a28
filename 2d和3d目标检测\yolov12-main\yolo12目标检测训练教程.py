#%% 项目地址 https://github.com/sunsmarterjie/yolov12
wget https://github.com/Dao-AILab/flash-attention/releases/download/v2.7.3/flash_attn-2.7.3+cu11torch2.2cxx11abiFALSE-cp311-cp311-linux_x86_64.whl
conda create -n yolov12 python=3.11
conda activate yolov12
cd yolov12-main
pip install -r requirements.txt
pip install -e .

# %%训练YOLOv12模型，成功了
from ultralytics import YOLO

model = YOLO('yolov12n.yaml')

# Train the model
results = model.train(
  data='twins.yaml', # VOC.yaml  coco128.yaml 
  epochs=50, 
  batch=8, 
  imgsz=320,
  scale=0.5,  # S:0.9; M:0.9; L:0.9; X:0.9
  mosaic=1.0,
  mixup=0.0,  # S:0.05; M:0.15; L:0.15; X:0.2
  copy_paste=0.1,  # S:0.15; M:0.4; L:0.5; X:0.6
  device="0",
)

#%% Evaluate model performance on the validation set
metrics = model.val()

# Perform object detection on an image
results = model("/root/autodl-tmp/yolov12-main/datasets/twins/images/val/11.jpg")
results[0].show()

#%% 新增：将预测box保存为nii.gz格式
import numpy as np
import nibabel as nib
from PIL import Image

# 读取原始图片，获取尺寸
img_path = "/root/autodl-tmp/yolov12-main/datasets/twins/images/val/11.jpg"
img = Image.open(img_path)
img_w, img_h = img.size

# 创建空白mask
mask = np.zeros((img_h, img_w), dtype=np.uint8)

# 遍历预测结果中的每个box
for box in results[0].boxes.xyxy.cpu().numpy():
    x1, y1, x2, y2 = map(int, box[:4])
    mask[y1:y2, x1:x2] = 1  # 可根据类别区分填不同值

# 保存为nii.gz
nii_img = nib.Nifti1Image(mask, affine=np.eye(4))
nib.save(nii_img, img_path.replace('.jpg', '_pred.nii.gz'))
print(f"已保存预测mask为: {img_path.replace('.jpg', '_pred.nii.gz')}")

#%% 测试集预测
from ultralytics import YOLO

model = YOLO('yolov12n.pt')
model.predict()

#%%  xml格式标签转yolo格式txt文件
import os
import xml.etree.ElementTree as ET

def parse_xml_and_save_to_txt(xml_file_path, output_txt_path):
    """
    解析XML文件，提取'zhong'和'cai'对象的边界框坐标，并保存到txt文件
    
    参数:
        xml_file_path: XML文件路径
        output_txt_path: 输出的txt文件路径
    """
    try:
        # 解析XML文件
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        
        # 打开输出文件
        with open(output_txt_path, 'w') as f:
            # 遍历所有object元素
            for obj in root.findall('.//object'):
                # 获取name元素的文本
                name = obj.find('name').text
                
                # 只处理'zhong'和'cai'对象
                if name in ['zhong', 'cai']:
                    # 获取边界框坐标
                    bbox = obj.find('.//bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    
                    # 获取图片宽高
                    size = root.find('size')
                    img_width = float(size.find('width').text)
                    img_height = float(size.find('height').text)
                    
                    # VOC转YOLO格式
                    x_center = (xmin + xmax) / 2.0 / img_width
                    y_center = (ymin + ymax) / 2.0 / img_height
                    width = (xmax - xmin) / img_width
                    height = (ymax - ymin) / img_height
                    
                    # 将'zhong'替换为1，'cai'替换为2
                    class_id = '1' if name == 'zhong' else '2'
                    
                    # 写入txt文件，格式为: class_id x_center y_center width height
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        print(f"成功将'{xml_file_path}'中的数据保存到'{output_txt_path}'")
        return True
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def process_all_xml_files(xml_dir, output_dir):
    """
    处理目录中的所有XML文件
    
    参数:
        xml_dir: 包含XML文件的目录
        output_dir: 输出txt文件的目录
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有XML文件
    xml_files = [f for f in os.listdir(xml_dir) if f.endswith('.xml')]
    
    if not xml_files:
        print(f"在'{xml_dir}'中没有找到XML文件")
        return
    
    success_count = 0
    for xml_file in xml_files:
        xml_path = os.path.join(xml_dir, xml_file)
        # 创建相同名称的txt文件
        txt_file = os.path.splitext(xml_file)[0] + '.txt'
        txt_path = os.path.join(output_dir, txt_file)
        
        if parse_xml_and_save_to_txt(xml_path, txt_path):
            success_count += 1
    
    print(f"处理完成: 成功转换{success_count}/{len(xml_files)}个文件")

# 主函数，直接使用指定路径进行批量转换
if __name__ == "__main__":
    # 设置固定的输入和输出路径
    xml_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/anno'
    output_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/label'
    
    print(f"开始处理XML文件，从'{xml_dir}'转换到'{output_dir}'")
    process_all_xml_files(xml_dir, output_dir)