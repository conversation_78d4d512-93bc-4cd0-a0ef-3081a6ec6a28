#!/usr/bin/env python
# coding: utf-8

# ## 7. Registration with multiple metrics and with multi-spectral images.

# ### Multimetric Registration

# In[2]:


import itk


# In[3]:


# Import Images
fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)
moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)

# Import Multimetric Parameter Map (see elastix documentation, 
# KNNGraphAlphaMutualInformation is not supported yet by ITKElastix)
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile('data/parameters_Bspline_Multimetric.txt')
print(parameter_object)


# Registration can either be done in one line with the registration function...

# In[4]:


# Call registration function
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image,
    parameter_object=parameter_object,
    log_to_console=True)


# .. or by initiating an elastix image filter object.

# In[5]:


# Load Elastix Image Filter Object
elastix_object = itk.ElastixRegistrationMethod.New(fixed_image,moving_image)
elastix_object.SetParameterObject(parameter_object)

# Set additional options
elastix_object.SetLogToConsole(False)

# Update filter object (required)
elastix_object.UpdateLargestPossibleRegion()

# Results of Registration
result_image = elastix_object.GetOutput()
result_transform_parameters = elastix_object.GetTransformParameterObject()


# ### Multi-spectral image registration

# In[6]:


# Import Images of multiple spectra
# Images of actual different spectra should be used here, for now same images are used.
fixed_image_spectrum1 = itk.imread('data/CT_2D_head_fixed.mha', itk.F)
moving_image_spectrum1 = itk.imread('data/CT_2D_head_moving.mha', itk.F)
fixed_image_spectrum2 = itk.imread('data/CT_2D_head_fixed.mha', itk.F)
moving_image_spectrum2 = itk.imread('data/CT_2D_head_moving.mha', itk.F)

# Import Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile('data/parameters_BSpline.txt')


# Multi-spectral registration can only be done with the object-oriented method in ITKElastix

# In[7]:


# Load Elastix Image Filter Object
elastix_object = itk.ElastixRegistrationMethod.New(fixed_image_spectrum1, moving_image_spectrum1)
# elastix_object.SetFixedImage(fixed_image_spectrum1)
elastix_object.AddFixedImage(fixed_image_spectrum2)

# elastix_object.SetMovingImage(moving_image_spectrum1)
elastix_object.AddMovingImage(moving_image_spectrum2)

elastix_object.SetParameterObject(parameter_object)

# Set additional options
elastix_object.SetLogToConsole(False)

# Update filter object (required)
elastix_object.UpdateLargestPossibleRegion()

# Results of Registration
result_image = elastix_object.GetOutput()
result_transform_parameters = elastix_object.GetTransformParameterObject()

