(Transform "BSplineTransform")
(NumberOfParameters 1682)
(TransformParameters -0.000626 0.005453 0.012955 0.018906 0.015188 0.025636 0.069053 0.099557 0.106513 0.120715 0.213697 0.279055 0.273507 0.201369 0.099780 -0.046192 -0.236193 -0.354464 -0.368594 -0.290897 -0.199898 -0.116461 -0.051901 -0.012077 -0.002669 -0.013091 -0.026412 -0.025115 -0.009699 0.013082 0.065977 0.118203 0.185883 0.236415 0.582370 1.351950 2.379034 3.170390 3.469164 2.671405 1.877538 1.477596 1.097645 0.410318 -0.408430 -1.037204 -1.551344 -2.084808 -2.329377 -2.064058 -1.552002 -0.995498 -0.543578 -0.263880 -0.170812 -0.140783 -0.071322 -0.008146 0.026375 0.120835 0.150717 0.254273 0.402170 0.950829 2.019437 3.507735 4.769436 5.206784 3.493531 1.957198 1.483016 1.413376 0.410035 -0.764348 -1.064789 -1.468066 -2.349089 -3.045198 -2.964069 -2.347578 -1.649802 -1.088743 -0.674526 -0.362127 -0.223182 -0.098458 -0.001058 0.039186 0.162432 0.181412 0.354149 0.646989 1.282575 2.482611 3.931830 5.321549 5.805223 3.093248 0.845312 0.861117 1.346445 -0.455092 -1.579837 -0.658245 -0.648453 -1.523057 -3.024702 -3.460005 -3.041312 -2.454721 -1.975292 -1.403025 -0.680039 -0.337144 -0.165904 0.005977 0.047994 0.231397 0.338353 0.643670 1.042409 1.424390 2.067252 2.465607 2.635768 3.016893 1.465136 -1.154316 -1.394104 0.762472 -0.605902 -1.497538 -0.310125 1.039884 0.798983 -0.737038 -2.037198 -2.813166 -3.326302 -3.231228 -2.300544 -1.128018 -0.465374 -0.143071 0.029077 0.047843 0.286365 0.457693 0.788950 1.235636 1.392837 1.162668 0.134256 -1.358727 -1.927170 -1.944363 -2.044212 -1.438127 0.753401 -0.863721 0.105196 -0.951068 1.534247 3.827947 3.056411 0.751983 -2.027707 -3.925180 -4.365317 -3.189042 -1.571500 -0.606968 -0.147569 0.046572 0.034834 0.286362 0.461152 0.700304 1.016008 0.953438 0.244475 -1.494715 -3.879253 -5.068467 -0.919792 2.567487 0.236467 -1.268927 -5.103567 -3.582101 -1.928287 2.770129 3.526331 3.701153 2.410318 -1.265583 -3.518153 -4.443800 -3.332837 -1.743336 -0.756361 -0.261733 0.034056 0.010117 0.295996 0.407933 0.661968 1.018217 0.805985 -0.621776 -3.546572 -6.061825 -2.264053 0.511951 0.952780 1.460067 -1.572953 -4.754098 -0.734989 0.904078 4.206925 3.578944 1.096588 2.626965 1.826241 -1.231912 -2.936075 -2.606661 -1.591672 -0.833881 -0.385761 0.018573 -0.028323 0.362493 0.510272 0.841316 1.308978 0.844609 -1.503814 -5.216787 -5.490667 0.939266 2.202536 1.089082 2.766421 -1.019371 -1.097882 4.290098 4.940987 2.251754 -2.085355 -2.221267 0.715489 3.444004 1.458536 -0.866632 -1.548235 -1.311232 -0.989977 -0.535802 0.008531 -0.083426 0.531619 0.805053 1.163606 1.741648 0.914095 -2.456227 -6.323343 -4.892525 2.480454 5.178934 4.969367 4.269963 1.044519 2.566600 5.186438 3.655733 -3.045445 -5.219395 -2.747850 -0.153075 2.903571 3.144478 0.398397 -0.802075 -1.270263 -1.233659 -0.798907 -0.004023 -0.160799 0.757610 1.184263 1.407752 1.594063 0.394293 -2.930243 -6.402380 -2.994086 3.247782 5.190374 4.170015 2.276749 -0.796139 3.190470 6.912004 2.385127 -2.598024 -2.164281 -2.540652 1.343150 2.831917 2.525378 -1.360073 -2.265313 -2.324859 -1.886418 -1.252423 -0.020369 -0.234671 0.928566 1.507020 1.925153 1.404097 -0.081982 -3.162216 -3.820769 0.118027 2.570539 4.385414 2.439809 -0.071655 -1.450072 0.085937 2.895355 1.179056 -0.707536 -0.335798 0.010333 3.223584 3.091499 2.199912 -2.788386 -3.698281 -3.158827 -2.569547 -1.714201 -0.016927 -0.288887 1.027026 1.843870 2.687165 1.677012 -0.208497 -1.858230 0.690971 1.465994 1.023396 1.726096 0.411447 -1.687823 -2.419137 -2.780334 -3.130523 -0.498019 0.813308 1.175111 2.690235 3.428996 4.516800 3.478002 -3.123745 -3.663299 -3.455434 -3.002300 -1.976416 0.035723 -0.329057 0.910247 1.944636 3.232060 2.760880 1.033450 0.418104 2.052217 -1.426904 -1.264989 -0.440383 -0.949178 -1.677408 -3.528705 -4.280760 -3.698126 0.401683 2.986162 3.852865 4.878897 3.223356 4.281347 6.216658 0.055065 -2.288642 -3.916729 -3.725177 -2.315790 0.130619 -0.349162 0.612094 1.756513 3.247582 3.923089 2.738834 1.177756 0.278969 -3.612342 -1.142515 -0.473292 -0.896005 -2.747240 -2.628613 -2.411851 -2.529141 0.346693 2.441008 3.313465 3.036853 2.502392 3.702402 6.328334 0.541313 -3.547117 -5.630582 -4.816431 -2.712550 0.263213 -0.345465 0.223609 1.269753 2.535603 3.464771 3.299760 -1.381348 0.226175 -0.627218 0.729809 -0.370322 -0.544272 0.882747 1.798932 1.517415 -0.185339 -0.535155 1.946058 1.151787 -0.191116 0.970442 2.811544 5.086668 -0.554708 -6.165253 -7.598267 -5.771956 -2.968251 0.363912 -0.330113 -0.287177 0.369601 1.171827 0.575852 -1.029567 -1.694286 2.391273 1.888441 0.609916 0.018231 1.787657 3.913738 5.113384 3.298210 -1.010000 -0.911788 2.189486 0.407568 -1.451007 0.705640 2.241844 2.775674 -3.750924 -7.592129 -8.337355 -5.984810 -2.958759 0.368405 -0.312785 -0.609834 -0.244045 0.220645 -0.835337 -2.103678 0.516600 3.832819 0.152753 -3.507452 0.121485 3.713490 4.440199 2.377140 -0.090616 -1.797275 0.691360 3.727544 2.600798 -1.064335 1.516876 0.113078 2.412086 -1.964963 -6.169037 -7.396728 -5.159636 -2.482791 0.343050 -0.294343 -0.554720 -0.187286 0.270480 -0.482022 -0.801333 0.151823 3.143591 0.217785 -6.965564 -2.192809 2.186745 3.092786 -0.338372 -1.981187 -2.506249 0.519757 2.939734 1.712023 -0.401445 -0.033605 -1.607206 -1.171777 -3.506680 -5.188954 -5.495565 -3.566976 -1.635960 0.303402 -0.278432 -0.236329 0.306086 1.039512 0.997488 0.832625 -0.262472 1.965481 0.918863 -3.543801 -4.312907 -0.602407 0.308497 -1.458856 -0.531310 -1.350041 -0.149339 -0.163880 -1.702495 -1.877269 -1.401912 -3.494514 -3.280657 -4.815525 -5.385345 -3.863087 -2.040128 -0.766614 0.270491 -0.277697 -0.180814 0.432126 1.473110 2.832577 3.843530 3.493295 2.207453 1.304959 -2.591908 -4.367081 -0.225063 0.012510 2.301487 2.088398 -3.675018 -2.780542 -2.272021 -2.499399 0.002302 -0.135763 0.922390 -3.068657 -5.968108 -5.252075 -3.087966 -1.366165 -0.399165 0.245719 -0.310250 -0.341338 0.223827 1.492907 3.586379 5.319483 4.892131 2.455927 0.593397 0.318056 0.067578 1.444714 0.165459 5.117534 2.860976 -5.439772 -3.442104 -1.846191 -1.346374 0.898090 3.333974 3.711123 -0.982572 -4.816995 -4.533201 -2.555397 -1.100744 -0.319675 0.207283 -0.365970 -0.652212 -0.237291 1.090256 3.130910 4.768801 4.418452 3.030549 2.113816 1.729795 3.003325 2.393781 2.239812 4.977913 3.233873 -1.798894 -1.425770 -1.530330 -2.325404 0.348835 3.414760 2.092852 -0.744529 -3.364205 -3.262968 -1.982157 -0.892937 -0.326229 0.154081 -0.416278 -0.890772 -0.633298 0.397982 1.996918 3.453204 3.500712 2.620779 1.732917 0.692766 1.926639 0.825799 -0.232880 0.089202 -1.009106 -2.243062 -2.302872 -3.062162 -2.018668 -0.828655 -1.281930 -1.076484 -1.803199 -2.026539 -1.790993 -1.286043 -0.728292 -0.375953 0.098472 -0.433234 -0.997283 -0.857133 -0.091088 1.095358 2.210185 2.412519 2.001466 1.443633 0.804283 0.420577 -1.622154 -2.733924 -1.340992 -2.106241 -2.853708 -2.478549 -2.294605 -2.703355 -3.757995 -4.170306 -4.085719 -3.326148 -1.983779 -1.231577 -0.779142 -0.377814 -0.166386 0.070830 -0.410401 -0.986483 -0.938461 -0.314870 0.489617 1.215234 1.466716 1.385049 1.134013 0.625524 -1.100705 -2.575934 -2.850681 -1.664533 -1.986293 -2.607827 -2.065365 -2.123227 -3.513049 -5.515470 -6.074237 -5.462841 -3.964483 -2.126186 -1.051839 -0.470680 -0.100983 -0.002548 0.043953 -0.339661 -0.839949 -0.821107 -0.428961 0.031817 0.406904 0.601996 0.700986 0.509331 0.095342 -0.976362 -1.663711 -1.649780 -1.205674 -1.173944 -1.498417 -1.475867 -2.163013 -3.569605 -5.121253 -5.480406 -4.775190 -3.411400 -1.766174 -0.813542 -0.271541 0.037667 0.043280 0.016962 -0.249840 -0.578214 -0.576082 -0.390916 -0.153004 0.067641 0.201720 0.270707 0.168621 -0.082750 -0.562403 -0.822564 -0.783563 -0.621217 -0.576293 -0.839239 -1.148761 -1.900530 -2.961455 -3.940846 -4.025050 -3.386772 -2.340521 -1.176203 -0.527496 -0.189030 0.023912 0.046438 0.005643 -0.071830 -0.150387 -0.155941 -0.138947 -0.111791 -0.088364 -0.070234 -0.051990 -0.030228 0.005387 0.083562 0.134015 0.131527 0.097096 0.058815 -0.031108 -0.181545 -0.297243 -0.339820 -0.308646 -0.236280 -0.153187 -0.082045 -0.031677 -0.010788 -0.003338 0.001495 -0.000549 -0.005129 -0.001388 0.013814 0.029733 0.047289 0.055089 0.063350 0.091891 0.128758 0.182061 0.298655 0.606588 0.810121 0.843115 0.831084 0.798307 0.751551 0.685092 0.681424 0.614936 0.449988 0.302218 0.172047 0.035745 -0.100441 -0.173923 -0.189798 -0.157711 -0.106872 -0.022306 -0.010767 0.016549 0.092524 0.193279 0.324124 0.760403 1.842201 3.227594 4.390528 4.965989 4.411930 4.000186 4.117169 4.113705 4.169346 4.215411 4.179809 4.031266 4.138750 4.238078 3.551279 2.592112 1.444673 0.408824 -0.147040 -0.371306 -0.333901 -0.217287 -0.049409 -0.014464 -0.007133 0.082740 0.210644 0.443039 1.150407 2.844138 4.969938 6.804428 7.624170 5.694732 4.480646 4.807120 4.991230 5.132599 5.259618 5.036514 4.554870 4.919583 5.699754 5.100238 3.862738 2.327263 1.063528 0.183884 -0.286954 -0.260710 -0.184649 -0.054675 -0.013843 0.007361 0.124676 0.333962 0.755652 1.748110 3.899560 6.426192 8.765344 9.892907 5.589724 3.223749 4.746513 3.727079 1.789584 2.513272 3.104259 3.720409 4.583395 6.489133 6.106929 4.850669 3.396772 1.926306 0.878286 0.213972 -0.044912 -0.119979 -0.054660 -0.012011 0.010550 0.124917 0.415558 1.030904 2.185372 4.048304 6.199978 8.282376 9.428884 4.322971 -0.759526 -2.149970 -1.226527 0.315377 0.069784 -1.717401 -0.844272 2.375950 5.088033 5.447917 5.071065 4.362798 3.075821 1.792684 0.798755 0.199522 -0.026160 -0.045862 -0.009193 0.030254 0.182614 0.572591 1.255520 2.316017 3.798149 5.067208 5.652448 5.263268 -0.009303 -4.507449 -2.374321 -1.188912 -0.825407 -0.927958 -0.078875 0.028413 1.287879 2.286034 3.195236 4.284287 4.847639 4.171909 2.687844 1.196632 0.353193 0.066898 -0.031545 -0.001611 0.079465 0.248886 0.649408 1.355963 2.348016 3.456399 3.779783 2.052203 -1.793600 -1.251608 2.209421 0.946785 -3.363527 -4.259154 -5.619663 -2.031605 2.443311 5.375753 1.733732 1.637359 3.370074 4.234231 3.961775 2.722853 1.279529 0.404214 0.094142 -0.023603 0.007398 0.100385 0.243608 0.624957 1.280153 2.115092 2.592573 1.410815 -1.547377 -1.591269 0.525047 0.589652 0.556168 -1.847634 -3.619134 -1.036675 2.300070 3.757199 2.280426 1.238390 1.210061 1.995706 2.969674 3.146027 2.269629 1.049324 0.330695 0.062275 -0.014033 0.017472 0.146301 0.270727 0.602382 1.183872 1.597184 1.310260 -1.021153 -3.030765 -0.306240 0.955583 2.207309 2.605737 1.621152 -1.488949 1.973014 6.416763 3.974374 -1.416717 -2.552059 -0.104068 1.602943 2.121401 2.269009 1.490492 0.515754 0.079864 0.000398 -0.003864 0.023399 0.138845 0.225222 0.479480 1.054071 1.255338 0.171331 -2.557880 -3.234944 2.483846 4.179885 5.536589 5.624561 4.448396 3.142995 4.994265 4.567609 -1.803043 -4.218159 -3.930172 -0.401395 1.495765 1.399382 1.466062 0.609400 0.055718 -0.069228 -0.065734 0.004463 0.032342 0.149811 0.140575 0.265283 0.754676 0.884591 -0.026703 -2.449005 -1.906554 3.118784 5.221857 4.079993 2.239029 1.127353 2.895272 5.415694 2.319777 -1.613681 -3.463714 -2.316051 1.096758 2.401285 1.260833 0.432449 -0.119401 -0.159176 -0.065424 -0.046326 0.017082 0.030405 0.151454 0.138946 0.249578 0.693600 0.992858 0.465602 -1.183032 -0.668166 2.186867 3.873504 1.059886 -1.383874 -1.633377 2.393444 2.322285 0.013192 0.394054 -0.381272 -0.403653 2.837410 2.939395 1.143128 -0.191091 -0.531921 -0.334769 -0.137210 -0.039380 0.025637 0.018971 0.186051 0.275285 0.386337 0.701484 0.780164 0.305227 -0.000310 0.435833 1.489165 1.846573 -0.398346 -1.794421 -3.236265 -3.413722 -2.264009 -1.963403 0.112190 2.369993 3.087699 3.949113 3.750700 0.794834 -0.527739 -0.827715 -0.523031 -0.279920 -0.128367 0.022317 0.005140 0.144828 0.226327 0.427434 0.521464 0.253497 -0.434939 0.026556 -0.911549 -1.005204 -0.653059 -0.917759 -1.936030 -3.515456 -5.275944 -5.127585 -2.504924 -0.062583 2.853997 4.711323 4.218523 3.032347 0.437951 -0.674331 -0.860410 -0.576817 -0.317827 -0.128824 0.017345 0.000028 0.023340 0.014638 -0.021101 -0.192094 -0.959564 -2.028733 -2.019847 -1.640107 -0.738581 -1.040335 -0.846441 -1.225803 -1.505466 -2.215642 -3.016763 -0.805358 1.076415 2.235417 1.887789 2.537860 1.602307 -0.369770 -1.188569 -0.915623 -0.346408 -0.039020 0.048873 0.019586 0.006549 -0.235881 -0.509875 -0.982442 -1.615707 -2.247155 -2.705002 -0.482359 0.711568 1.713375 0.088455 -0.643298 0.921413 3.420053 3.047013 -0.469383 -0.091622 1.881422 1.548744 -1.028839 3.151166 3.081868 1.288303 0.590194 0.505844 0.652121 0.601691 0.410804 0.026024 0.024765 -0.472392 -1.072585 -2.033358 -3.345453 -4.041213 -2.728614 2.187834 2.810200 0.933586 0.301872 1.025427 3.103765 5.236386 3.076659 -0.918070 0.180350 1.680310 1.371482 -2.937354 -0.104499 1.623335 3.648454 4.588433 3.465753 2.085548 1.232881 0.738341 0.025649 0.038109 -0.564665 -1.294158 -2.475621 -3.771085 -4.072885 -2.022350 1.190439 1.705390 0.134495 0.615913 2.952386 4.766323 2.605222 -0.671026 -2.334401 -0.521084 3.471983 3.196901 -0.632737 1.537252 1.232452 2.832618 4.085520 3.535854 2.399433 1.346278 0.803097 0.024516 0.045677 -0.471179 -1.047216 -1.886313 -2.473582 -2.005918 0.623926 2.220948 0.342629 -0.674743 0.440277 1.490165 2.336846 0.828831 -2.141434 -2.146441 0.000502 3.244495 1.925441 -1.695661 0.124269 -0.835056 -0.265056 0.194029 1.017971 0.995386 0.616128 0.436019 0.026294 0.049941 -0.239245 -0.575251 -0.937305 -0.729460 0.457434 2.939085 1.584986 -0.850263 -0.909486 -0.629518 0.418786 -1.186904 -2.382524 -1.040087 -1.415349 -1.101112 0.744447 -2.197277 -5.279088 -1.256398 -1.878694 -2.140593 -2.324502 -2.158993 -1.118036 -0.422658 -0.121654 0.029267 0.053596 -0.064685 -0.318065 -0.635901 -0.897019 -0.267256 1.742263 2.323999 0.009230 -2.686062 0.591097 3.140192 2.278414 1.039390 1.534760 -1.209183 -4.528884 -1.524388 -0.410692 -0.165795 -0.374517 -0.206425 -2.960875 -4.308755 -3.609206 -2.098945 -1.104840 -0.507023 0.017150 0.055696 0.075289 -0.133355 -0.571689 -1.362519 -1.506641 0.317021 1.943693 2.084799 -0.792512 -0.248127 3.207909 5.235058 3.512145 0.292083 -2.060018 -3.620104 -2.552662 0.115186 -0.810623 -0.322234 -0.575465 -3.040628 -4.548420 -3.750703 -2.231835 -1.246701 -0.649445 0.004907 0.049536 0.081063 -0.093550 -0.563937 -1.591151 -2.148164 -0.794996 0.734449 1.894240 3.448909 3.004435 1.258166 2.029609 3.346657 1.148203 -1.146038 -1.081779 -1.759043 -2.573947 -3.121591 -1.846203 -3.570327 -4.509861 -4.133141 -2.940580 -1.772011 -0.999823 -0.537042 0.002849 0.035100 0.024087 -0.141441 -0.566790 -1.473150 -2.044189 -1.341485 -0.298421 0.342606 1.766548 2.077680 2.094505 -0.342398 0.272910 3.188601 2.280596 -1.102566 -1.995660 -2.784464 -5.194155 -7.630930 -7.146115 -6.092335 -3.439727 -1.935413 -1.174911 -0.613399 -0.322824 0.003955 0.016890 -0.007156 -0.151032 -0.465618 -0.988879 -1.420521 -1.173455 -0.579789 -0.654521 -1.224256 -1.117594 0.392165 -1.640323 -0.791138 3.279810 4.662583 2.255297 0.464797 -2.682527 -7.284359 -9.274021 -9.066629 -6.851930 -3.466007 -1.776616 -1.066088 -0.553753 -0.253282 -0.002931 0.001524 -0.029570 -0.093298 -0.244696 -0.538216 -0.795559 -0.579994 -0.336617 -0.965274 -2.398062 -2.652926 -3.536742 -5.149359 -4.217123 0.087990 2.950356 1.808681 0.035397 -3.205655 -7.380801 -9.506065 -9.119802 -6.749897 -3.609087 -1.760620 -1.037122 -0.499711 -0.199526 -0.009876 -0.006576 -0.047188 -0.106687 -0.196385 -0.245438 -0.143453 0.095967 0.022907 -0.495039 -1.842278 -3.742803 -5.629556 -6.632745 -5.920178 -3.414529 -0.836549 -0.351293 -1.469705 -3.828876 -6.429018 -7.776795 -7.338118 -5.374093 -2.853569 -1.376559 -0.752712 -0.306858 -0.106214 -0.011896 -0.009627 -0.044715 -0.098697 -0.106890 -0.065104 0.061002 0.248327 0.209855 -0.199319 -1.238559 -3.138620 -4.911637 -5.892311 -5.732654 -4.495409 -2.785341 -1.897388 -2.331651 -3.701545 -5.207991 -5.666149 -5.011257 -3.549685 -1.851344 -0.903314 -0.445765 -0.205407 -0.077169 -0.017191 -0.006009 -0.013854 -0.012064 0.010801 0.056596 0.113025 0.155812 0.138927 0.021714 -0.245481 -0.749493 -1.269049 -1.658618 -1.861953 -1.899483 -1.759792 -1.496556 -1.237278 -1.033133 -0.855372 -0.587087 -0.341828 -0.168532 -0.072941 -0.025089 -0.009319 -0.016778 -0.019966 -0.013573)
(InitialTransformParametersFileName "exampleoutput/TransformParameters.0.txt")
(UseBinaryFormatForTransformationParameters "false")
(HowToCombineTransforms "Compose")

// Image specific
(FixedImageDimension 2)
(MovingImageDimension 2)
(FixedInternalImagePixelType "float")
(MovingInternalImagePixelType "float")
(Size 256 256)
(Index 0 0)
(Spacing 1.0000000000 1.0000000000)
(Origin 0.0000000000 0.0000000000)
(Direction 1.0000000000 0.0000000000 0.0000000000 1.0000000000)
(UseDirectionCosines "true")

// BSplineTransform specific
(GridSize 29 29)
(GridIndex 0 0)
(GridSpacing 10.0101989248 10.0101989248)
(GridOrigin -13.5973710259 -12.6485393505)
(GridDirection 1.0000000000 0.0000000000 0.0000000000 1.0000000000)
(BSplineTransformSplineOrder 3)
(UseCyclicTransform "false")

// ResampleInterpolator specific
(ResampleInterpolator "FinalBSplineInterpolator")
(FinalBSplineInterpolationOrder 3)

// Resampler specific
(Resampler "DefaultResampler")
(DefaultPixelValue 0.000000)
(ResultImageFormat "nii")
(ResultImagePixelType "float")
(CompressResultImage "false")
