# HCC肝脏多序列MRI配准工具

基于uniGradICON的HCC数据集肝脏多序列MRI图像配准工具，支持ap（动脉期）、pp（门静脉期）、hbp（肝胆期）三个序列的自动配准。

## 数据集结构

HCC数据集结构：
```
/root/autodl-tmp/120HCC/image/
├── ap/                    # 动脉期序列
│   ├── baizhengqiang-ap.nii.gz
│   ├── baoxiuying-ap.nii.gz
│   └── ...
├── pp/                    # 门静脉期序列
│   ├── baizhengqiang-pp.nii.gz
│   ├── baoxiuying-pp.nii.gz
│   └── ...
└── hbp/                   # 肝胆期序列
    ├── baizhengqiang-hbp.nii.gz
    ├── baoxiuying-hbp.nii.gz
    └── ...
```

## 序列说明

- **AP (Arterial Phase)**: 动脉期 - 对比剂注射后早期，动脉强化明显
- **PP (Portal Phase)**: 门静脉期 - 对比剂注射后中期，门静脉和肝实质强化
- **HBP (Hepatobiliary Phase)**: 肝胆期 - 对比剂注射后晚期，肝细胞和胆道系统强化

## 快速开始

### 1. 测试安装和单个患者配准

```bash
cd "/root/autodl-tmp/3d MRI配准"
python test_single_patient.py
```

### 2. 单个患者配准

```bash
# 配准指定患者（以ap为参考序列）
python liver_hcc_registration.py --patient_name baizhengqiang

# 使用pp作为参考序列
python liver_hcc_registration.py --patient_name baizhengqiang --reference_sequence pp
```

### 3. 批量配准（测试模式）

```bash
# 配准前5个患者进行测试
python liver_hcc_registration.py --max_patients 5

# 配准前10个患者
python liver_hcc_registration.py --max_patients 10
```

### 4. 批量配准所有患者

```bash
# 使用默认参数批量配准
python batch_hcc_registration.py

# 自定义参数批量配准
python batch_hcc_registration.py \
    --reference_sequence ap \
    --max_workers 2 \
    --chunk_size 10 \
    --model unigradicon \
    --io_iterations 50
```

## 参数说明

### 主要参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--data_root` | `/root/autodl-tmp/120HCC/image` | 120HCC数据根目录 |
| `--output_dir` | `/root/autodl-tmp/3d MRI配准/results` | 输出目录 |
| `--reference_sequence` | `ap` | 参考序列（ap/pp/hbp） |
| `--model` | `unigradicon` | 配准模型 |
| `--io_iterations` | `50` | 实例优化迭代次数 |
| `--similarity` | `lncc2` | 相似性度量 |

### 批量处理参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--max_workers` | `2` | 并行进程数 |
| `--chunk_size` | `10` | 每批次患者数 |
| `--skip_existing` | `True` | 跳过已处理的患者 |

## 输出结果

### 单个患者输出结构
```
results/
└── baizhengqiang/
    ├── transform_pp_to_ap.hdf5           # pp到ap的变换
    ├── warped_baizhengqiang_pp_to_ap.nii.gz  # 配准后的pp图像
    ├── transform_hbp_to_ap.hdf5          # hbp到ap的变换
    ├── warped_baizhengqiang_hbp_to_ap.nii.gz # 配准后的hbp图像
    └── baizhengqiang_registration_results.json  # 配准结果报告
```

### 批量处理输出
```
batch_results/
├── patient1/
│   ├── transform_*.hdf5
│   ├── warped_*.nii.gz
│   └── *_registration_results.json
├── patient2/
│   └── ...
└── batch_hcc_results.json              # 批量处理总结
```

## 配准策略

### 推荐配准顺序

1. **以AP为参考序列**（推荐）
   - AP → PP: 动脉期到门静脉期
   - AP → HBP: 动脉期到肝胆期
   - 优势：动脉期血管结构清晰，适合作为参考

2. **以PP为参考序列**
   - PP → AP: 门静脉期到动脉期
   - PP → HBP: 门静脉期到肝胆期
   - 优势：门静脉期肝实质对比度好

3. **以HBP为参考序列**
   - HBP → AP: 肝胆期到动脉期
   - HBP → PP: 肝胆期到门静脉期
   - 优势：肝胆期肝细胞特异性强化

### 配准参数优化

#### 相似性度量选择
- **lncc2**: 适用于同模态MRI序列，对比度变化较小
- **lncc**: 标准局部归一化互相关
- **mind**: 适用于对比度差异较大的序列

#### 迭代次数建议
- **快速测试**: 20-30次迭代
- **标准配准**: 50次迭代（默认）
- **高精度配准**: 100-200次迭代

## 性能优化

### 内存管理
- 建议并行进程数不超过2个
- 每批次处理10个患者
- 监控系统内存使用情况

### 计算资源
- **CPU**: 多核处理器推荐
- **内存**: 至少16GB
- **存储**: 每个患者约需要1-2GB输出空间

## 质量控制

### 配准质量评估
1. **视觉检查**: 使用ITK-SNAP或3D Slicer查看配准结果
2. **解剖标志点**: 检查血管、肝脏边界对齐情况
3. **定量指标**: 查看配准结果JSON文件中的统计信息

### 常见问题处理
1. **配准失败**: 检查图像质量、对比度
2. **内存不足**: 减少并行进程数
3. **处理缓慢**: 降低迭代次数或使用更快的相似性度量

## 使用示例

### 示例1: 测试单个患者
```bash
# 测试第一个患者
python test_single_patient.py

# 如果测试成功，配准指定患者
python liver_hcc_registration.py --patient_name baizhengqiang
```

### 示例2: 小批量测试
```bash
# 配准前3个患者进行测试
python liver_hcc_registration.py --max_patients 3 --reference_sequence ap
```

### 示例3: 生产环境批量处理
```bash
# 使用优化参数批量处理所有患者
python batch_120hcc_registration.py \
    --reference_sequence ap \
    --max_workers 2 \
    --chunk_size 5 \
    --model unigradicon \
    --io_iterations 50 \
    --similarity lncc2
```

### 示例4: 高精度配准
```bash
# 使用更多迭代次数获得更高精度
python liver_120hcc_registration.py \
    --patient_name baizhengqiang \
    --io_iterations 100 \
    --similarity mind
```

## 结果分析

### 配准成功率统计
- 查看 `batch_hcc_results.json` 获取整体统计
- 查看各患者的 `*_registration_results.json` 获取详细信息

### 处理时间分析
- 单个患者平均处理时间：2-5分钟
- 120个患者总处理时间：4-10小时（取决于硬件和参数）

## 故障排除

### 常见错误
1. **找不到患者数据**: 检查数据路径和文件命名
2. **uniGradICON未安装**: 运行测试脚本自动安装
3. **内存不足**: 减少并行进程数和批次大小
4. **配准失败**: 尝试不同的相似性度量和参数

### 日志分析
- 查看控制台输出获取实时进度
- 检查错误信息定位问题
- 查看JSON结果文件获取详细状态

## 技术支持

如有问题，请检查：
1. 数据路径是否正确
2. uniGradICON是否正确安装
3. 系统资源是否充足
4. 参数设置是否合理

## 引用

如果使用本工具，请引用uniGradICON原始论文：

```bibtex
@inproceedings{tian2024unigradicon,
  title={unigradicon: A foundation model for medical image registration},
  author={Tian, Lin and Greer, Hastings and Kwitt, Roland and others},
  booktitle={MICCAI},
  year={2024}
}
```
