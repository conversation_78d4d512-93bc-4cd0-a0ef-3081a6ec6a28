#!/usr/bin/env python
# coding: utf-8
#%% 2d目标检测FastRCNNPredictor训练
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils import data
import numpy as np
import matplotlib.pyplot as plt
# get_ipython().run_line_magic('matplotlib', 'inline')
import torchvision
import os
from lxml import etree  # 安装 lxml ： conda install lxml
import glob
from PIL import Image
import utils
from engine import train_one_epoch, evaluate

# # 使用预训练模型对于自行标注数据训练

# In[2]:
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor

# In[3]:

model = torchvision.models.detection.fasterrcnn_resnet50_fpn(pretrained=True)
model
# In[4]:
# 类别数 + background
num_classes = 3

# In[5]:
# get number of input features for the classifier
in_features = model.roi_heads.box_predictor.cls_score.in_features

# In[6]:
# replace the pre-trained head with a new one
model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
print(model)

#%%数据读取和预处理
xml = open('/root/autodl-tmp/yolov12-main/datasets/twins/anno/1.xml').read()
print(xml)
sel = etree.HTML(xml)
# In[14]:

label_dict = {'zhong': 1, 'cai': 2}

## 创建输入

# In[15]:
class My_dataset(data.Dataset):
    def __init__(self, img_xml_pairs):
        self.img_xml_pairs = img_xml_pairs
        
    def __getitem__(self, index):
        img_path, xml_path = self.img_xml_pairs[index]
        pil_img = Image.open(img_path)
        pil_img = np.array(pil_img)
        tensor_img = torch.from_numpy(pil_img/255).permute(2, 0, 1).type(torch.float32)
        
        xml = open(xml_path).read()
        sel = etree.HTML(xml)
        objects_num = len(sel.xpath('//object'))
        label_names = sel.xpath('//object/name/text()')
        xmin = sel.xpath('//object/bndbox/xmin/text()')
        ymin = sel.xpath('//object/bndbox/ymin/text()')
        xmax = sel.xpath('//object/bndbox/xmax/text()')
        ymax = sel.xpath('//object/bndbox/ymax/text()')
        boxes = []
        label_idxs = []
        areas = []
        iscrowd = []  # 新增
        for i in range(objects_num):
            x1, y1, x2, y2 = int(xmin[i]), int(ymin[i]), int(xmax[i]), int(ymax[i])
            boxes.append([x1, y1, x2, y2])
            label_idxs.append(label_dict.get(label_names[i]))
            areas.append((x2 - x1) * (y2 - y1))
            iscrowd.append(0)  # 全部设为0，表示不是crowd
        boxes = torch.as_tensor(boxes, dtype=torch.float32)
        label_idxs = torch.as_tensor(label_idxs, dtype=torch.int64)
        areas = torch.as_tensor(areas, dtype=torch.float32)
        iscrowd = torch.as_tensor(iscrowd, dtype=torch.int64)  # 新增

        target = {}
        target["boxes"] = boxes
        target["labels"] = label_idxs
        target["image_id"] = torch.tensor(index)
        target["area"] = areas
        target["iscrowd"] = iscrowd  # 新增

        return tensor_img, target
    
    def __len__(self):
        return len(self.img_xml_pairs)

# In[16]:
# 加载训练集图像和标注
train_images = glob.glob(r'/root/autodl-tmp/yolov12-main/datasets/twins/images/train/*')
print(f"训练集图像数量: {len(train_images)}")

# 加载验证集图像
val_images = glob.glob(r'/root/autodl-tmp/yolov12-main/datasets/twins/images/val/*')
print(f"验证集图像数量: {len(val_images)}")

# 加载标注文件
xmls = glob.glob(r'/root/autodl-tmp/yolov12-main/datasets/twins/anno/*.xml')
print(f"标注文件数量: {len(xmls)}")

# 创建图像和标注的映射关系
train_img_xml_pairs = []
val_img_xml_pairs = []

for img_path in train_images:
    img_name = os.path.basename(img_path).split('.')[0]
    xml_path = None
    for xml in xmls:
        if os.path.basename(xml).split('.')[0] == img_name:
            xml_path = xml
            break
    if xml_path:
        train_img_xml_pairs.append((img_path, xml_path))

for img_path in val_images:
    img_name = os.path.basename(img_path).split('.')[0]
    xml_path = None
    for xml in xmls:
        if os.path.basename(xml).split('.')[0] == img_name:
            xml_path = xml
            break
    if xml_path:
        val_img_xml_pairs.append((img_path, xml_path))

print(f"匹配到的训练图像和标注对: {len(train_img_xml_pairs)}")
print(f"匹配到的验证图像和标注对: {len(val_img_xml_pairs)}")

# 创建训练集和验证集
train_dataset = My_dataset(train_img_xml_pairs)
val_dataset = My_dataset(val_img_xml_pairs)


# In[23]:

BATCH_SIZE = 2

# In[24]:

# 创建训练集和验证集的数据加载器
train_loader = data.DataLoader(
    train_dataset,
    batch_size=BATCH_SIZE,
    shuffle=True,
    collate_fn=utils.collate_fn
)

val_loader = data.DataLoader(
    val_dataset,
    batch_size=BATCH_SIZE,
    shuffle=False,  # 验证集不需要打乱顺序
    collate_fn=utils.collate_fn
)

# In[25]:

(imgs_batch, labels_batch) = next(iter(train_loader))

# In[26]:

imgs_batch = list(image for image in imgs_batch)

# In[27]:

len(imgs_batch)

# In[28]:

imgs_batch[0].shape, imgs_batch[1].shape

# In[29]:

labels_batch = [{k: v for k, v in t.items()} for t in labels_batch]

# In[30]:

labels_batch

# # 创建定位模型

# In[31]:

model = torchvision.models.detection.fasterrcnn_resnet50_fpn(pretrained=True)
# 类别数 + background
num_classes = 3
# get number of input features for the classifier
in_features = model.roi_heads.box_predictor.cls_score.in_features
# replace the pre-trained head with a new one
model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
model
# In[32]:
device = "cuda" if torch.cuda.is_available() else "cpu"
print("Using {} device".format(device))

# In[33]:

model = model.to(device)

# In[34]:
params = [p for p in model.parameters() if p.requires_grad]

# In[35]:
optimizer = torch.optim.SGD(params, lr=0.005, momentum=0.9, weight_decay=0.0005)

#optimizer = torch.optim.Adam(params, lr=0.005)
# and a learning rate scheduler
lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer,
                                                step_size=3,
                                                gamma=0.1)

# In[36]:
num_epochs = 50

# In[37]:
# 创建字典来存储训练和验证指标
metrics_history = {
    'train_loss': [],
    'val_mAP': [],
    'val_AP50': [],
    'val_AP75': []
}

print("开始训练和评估...")
for epoch in range(num_epochs):
    # 训练一个周期，每10次迭代打印一次
    print(f"\n训练周期 {epoch+1}/{num_epochs}")
    train_metrics = train_one_epoch(model, optimizer, train_loader, device, epoch, print_freq=10)
    metrics_history['train_loss'].append(train_metrics.meters['loss'].global_avg)
    
    # 更新学习率
    lr_scheduler.step()
    
    # 在验证集上评估模型
    print(f"\n在验证集上评估模型...")
    coco_evaluator = evaluate(model, val_loader, device=device)
    
    # 提取并保存评估指标
    if 'bbox' in coco_evaluator.coco_eval:
        # 获取mAP和AP50、AP75指标
        bbox_eval = coco_evaluator.coco_eval['bbox']
        metrics_history['val_mAP'].append(bbox_eval.stats[0])  # mAP @ IoU=0.5:0.95
        metrics_history['val_AP50'].append(bbox_eval.stats[1])  # AP @ IoU=0.5
        metrics_history['val_AP75'].append(bbox_eval.stats[2])  # AP @ IoU=0.75
        
        print(f"验证集结果 - mAP: {bbox_eval.stats[0]:.4f}, AP50: {bbox_eval.stats[1]:.4f}, AP75: {bbox_eval.stats[2]:.4f}")
    
    # 保存模型（可选）
    if (epoch + 1) % 5 == 0 or epoch == num_epochs - 1:
        torch.save(model.state_dict(), f"model_epoch_{epoch+1}.pth")
        print(f"模型已保存: model_epoch_{epoch+1}.pth")

# 训练完成后绘制指标图表
plt.figure(figsize=(12, 8))

# 绘制训练损失
plt.subplot(2, 1, 1)
plt.plot(metrics_history['train_loss'], label='训练损失')
plt.title('训练损失随时间变化')
plt.xlabel('周期')
plt.ylabel('损失')
plt.legend()

# 绘制验证指标
plt.subplot(2, 1, 2)
plt.plot(metrics_history['val_mAP'], label='mAP@0.5:0.95')
plt.plot(metrics_history['val_AP50'], label='AP@0.5')
plt.plot(metrics_history['val_AP75'], label='AP@0.75')
plt.title('验证集评估指标')
plt.xlabel('周期')
plt.ylabel('AP')
plt.legend()

plt.tight_layout()
plt.savefig('training_metrics.png')
plt.show()

print("训练和评估完成！")
print(f"最终模型性能 - mAP: {metrics_history['val_mAP'][-1]:.4f}, AP50: {metrics_history['val_AP50'][-1]:.4f}")

#%% 模型测试In[38]:
names = {1: 'zhong', 2: 'cai'}

# In[39]:
# 务必要设置为预测模式
model.cpu().eval()

# In[45]:
img_path = '/root/autodl-tmp/yolov12-main/datasets/twins/images/val/1.jpg'
img_path

# In[52]:
threshold = 0.5
pil_img = Image.open(img_path)
pil_img = np.array(pil_img)
tensor_img = torch.from_numpy(pil_img/255).permute(2, 0, 1).type(torch.float32)
pred = model([tensor_img])
boxes = pred[0]['boxes']
labels = pred[0]['labels']
scores = pred[0]['scores']
pred_index = scores > threshold
boxes = boxes[pred_index]
labels = labels[pred_index]
print(labels)
labels = [names.get(idx.item()) for idx in labels]
pil_img = torch.from_numpy(pil_img).permute(2, 0, 1)
pil_img = torchvision.utils.draw_bounding_boxes(pil_img, boxes, labels, width=3, font_size=300)
pil_img = Image.fromarray(pil_img.permute(1,2,0).numpy())
pil_img

#%%预测函数，预测单张图
import torch
import torchvision
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
from PIL import Image
import numpy as np
import pandas as pd
from torchvision.utils import draw_bounding_boxes

# 定义预测函数
def predict_and_save_to_excel(model, img_path, threshold, class_names, output_excel, device):
    # 读取图片
    pil_img = Image.open(img_path)
    pil_img_array = np.array(pil_img)
    tensor_img = torch.from_numpy(pil_img_array / 255.0).permute(2, 0, 1).type(torch.float32).to(device)
    model= model.to(device)
    # 设置为评估模式
    model.eval()
    
    # 进行预测
    with torch.no_grad():
        pred = model([tensor_img])
    
    # 获取预测结果
    boxes = pred[0]['boxes']
    labels = pred[0]['labels']
    scores = pred[0]['scores']
    
    # 过滤得分低于阈值的预测结果
    pred_index = scores > threshold
    boxes = boxes[pred_index].cpu()
    labels = labels[pred_index].cpu()
    scores = scores[pred_index].cpu()
    
    # 转换标签为类别名称
    labels = [class_names.get(idx.item(), 'Unknown') for idx in labels]
    
    # 将结果保存到Excel
    df = pd.DataFrame({
        'box_x1': boxes[:, 0].tolist(),
        'box_y1': boxes[:, 1].tolist(),
        'box_x2': boxes[:, 2].tolist(),
        'box_y2': boxes[:, 3].tolist(),
        'label': labels,
        'score': scores.tolist()
    })
    df.to_excel(output_excel, index=False)
    
    # 可视化预测结果
    pil_img = torch.from_numpy(pil_img_array).permute(2, 0, 1)
    pil_img = draw_bounding_boxes(pil_img, boxes, labels, width=3, font_size=300)
    pil_img = Image.fromarray(pil_img.permute(1, 2, 0).numpy())
    return pil_img

# 模型设置和训练代码（假设已经执行过）
# model = torchvision.models.detection.fasterrcnn_resnet50_fpn(pretrained=True)
# num_classes = 3  # 类别数 + background
# in_features = model.roi_heads.box_predictor.cls_score.in_features
# model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
# device = "cuda" if torch.cuda.is_available() else "cpu"
# model = model.to(device)
# ... (训练代码)

# 预测和保存结果
img_path =  '/root/autodl-tmp/yolov12-main/datasets/twins/images/val/1.jpg'
output_excel = 'prediction_results3.xlsx'
class_names = {1: 'zhong', 2: 'cai'}
threshold = 0.5

# 运行预测函数
predicted_image = predict_and_save_to_excel(model, img_path, threshold, class_names, output_excel, device)
predicted_image.show()  # 显示预测结果


# %%预测函数，同时预测多张图片
import torch
import torchvision
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
from PIL import Image
import numpy as np
import pandas as pd
from torchvision.utils import draw_bounding_boxes

# 定义预测函数
def predict_and_collect_results(model, img_path, threshold, class_names, device):
    # 读取图片
    pil_img = Image.open(img_path)
    pil_img_array = np.array(pil_img)
    tensor_img = torch.from_numpy(pil_img_array / 255.0).permute(2, 0, 1).type(torch.float32).to(device)
    
    # 设置为评估模式
    model.eval()
    
    # 进行预测
    with torch.no_grad():
        pred = model([tensor_img])
    
    # 获取预测结果
    boxes = pred[0]['boxes']
    labels = pred[0]['labels']
    scores = pred[0]['scores']
    
    # 过滤得分低于阈值的预测结果
    pred_index = scores > threshold
    boxes = boxes[pred_index].cpu()
    labels = labels[pred_index].cpu()
    scores = scores[pred_index].cpu()
    
    # 转换标签为类别名称
    labels = [class_names.get(idx.item(), 'Unknown') for idx in labels]
    
    # 收集结果
    results = []
    for box, label, score in zip(boxes, labels, scores):
        results.append({
            'image': img_path,
            'box_x1': box[0].item(),
            'box_y1': box[1].item(),
            'box_x2': box[2].item(),
            'box_y2': box[3].item(),
            'label': label,
            'score': score.item()
        })
    
    return results

# 模型设置和训练代码（假设已经执行过）
# model = torchvision.models.detection.fasterrcnn_resnet50_fpn(pretrained=True)
# num_classes = 3  # 类别数 + background
# in_features = model.roi_heads.box_predictor.cls_score.in_features
# model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
# device = "cuda" if torch.cuda.is_available() else "cpu"
# model = model.to(device)
# ... (训练代码)

# 预测和保存结果
img_paths  = glob.glob(r'/root/autodl-tmp/yolov12-main/datasets/twins/images/val/*')  # 替换为你的图片路径列表

class_names = {1: 'zhong', 2: 'cai'}
threshold = 0.5
all_results = []

# 对多个图片进行预测并收集结果
for img_path in img_paths:
    print(img_path)
    results = predict_and_collect_results(model, img_path, threshold, class_names, device)
    all_results.extend(results)

# 将所有结果保存到同一个Excel文件中
df = pd.DataFrame(all_results)
output_excel = 'all_predictions3.xlsx'
df.to_excel(output_excel, index=False)

# %% 预测函数，同时预测多张图片的boxes，并转为mask
# import torch
# import torchvision
# from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
# from PIL import Image
# import numpy as np
# import pandas as pd
# import nibabel as nib
# from torchvision.utils import draw_bounding_boxes
# import os

# # 定义预测函数
# def predict_and_collect_results(model, img_path, threshold, class_names, device):
#     # 读取图片
#     pil_img = Image.open(img_path)
#     pil_img_array = np.array(pil_img)
#     tensor_img = torch.from_numpy(pil_img_array / 255.0).permute(2, 0, 1).type(torch.float32).to(device)
    
#     # 设置为评估模式
#     model.eval()
    
#     # 进行预测
#     with torch.no_grad():
#         pred = model([tensor_img])
    
#     # 获取预测结果
#     boxes = pred[0]['boxes']
#     labels = pred[0]['labels']
#     scores = pred[0]['scores']
    
#     # 过滤得分低于阈值的预测结果
#     pred_index = scores > threshold
#     boxes = boxes[pred_index].cpu()
#     labels = labels[pred_index].cpu()
#     scores = scores[pred_index].cpu()
    
#     # 转换标签为类别名称
#     labels = [class_names.get(idx.item(), 'Unknown') for idx in labels]
    
#     # 收集结果
#     results = []
#     for box, label, score in zip(boxes, labels, scores):
#         results.append({
#             'image': img_path,
#             'box_x1': box[0].item(),
#             'box_y1': box[1].item(),
#             'box_x2': box[2].item(),
#             'box_y2': box[3].item(),
#             'label': label,
#             'score': score.item()
#         })
    
#     return results, boxes

# def boxes_to_mask(boxes, img_size):
#     """
#     将边界框转换为二值掩码
#     boxes: 边界框 (x1, y1, x2, y2)
#     img_size: 图像尺寸 (width, height)
#     """
#     mask = np.zeros(img_size, dtype=np.uint8)
#     for box in boxes:
#         x1, y1, x2, y2 = box
#         mask[int(y1):int(y2), int(x1):int(x2)] = 1
#     return mask

# def save_mask_as_nii(mask, output_path):
#     """
#     将掩码保存为 NIfTI 文件
#     mask: 二值掩码
#     output_path: 保存路径
#     """
#     nii_img = nib.Nifti1Image(mask, np.eye(4))
#     nib.save(nii_img, output_path)

# # 预测和保存结果
# img_paths  = glob.glob(r'/root/autodl-tmp/yolov12-main/datasets/twins/images/val/*')
# class_names = {1: 'zhong', 2: 'cai'}
# threshold = 0.6
# all_results = []

# output_mask_dir = 'masks/'  # 保存mask的文件夹
# os.makedirs(output_mask_dir, exist_ok=True)

# # 对多个图片进行预测并收集结果
# for img_path in img_paths:
#     results, boxes = predict_and_collect_results(model, img_path, threshold, class_names, device)
#     all_results.extend(results)

#     # 生成二值掩码
#     img_size = Image.open(img_path).size[::-1]  # (height, width)
#     mask = boxes_to_mask(boxes, img_size)

#     # 保存为 NIfTI 文件
#     img_name = os.path.basename(img_path).replace('.jpg', '.nii.gz')
#     output_nii_path = os.path.join(output_mask_dir, img_name)
#     save_mask_as_nii(mask, output_nii_path)

# # 将所有结果保存到同一个Excel文件中
# df = pd.DataFrame(all_results)
# output_excel = 'all_predictions2.xlsx'
# df.to_excel(output_excel, index=False)

# print(f"所有图片的预测结果已保存到 {output_excel}")
# print(f"所有mask已保存到 {output_mask_dir}")

#%%