import os
import numpy as np
import nibabel as nib
from torch.utils.data import Dataset, DataLoader
import torch
import torch.nn.functional as F
from torchvision import transforms
from pathlib import Path
import random
from scipy.ndimage import rotate, map_coordinates
try:
    from scipy.ndimage.filters import gaussian_filter
except ImportError:
    from scipy.ndimage import gaussian_filter

class MedicalAugmentation:
    """医学图像数据增强类"""
    
    @staticmethod
    def random_rotation(image, max_angle=15):
        """随机旋转"""
        if random.random() > 0.5:
            angle = random.uniform(-max_angle, max_angle)
            rotated = rotate(image, angle, reshape=False, mode='nearest')
            return np.ascontiguousarray(rotated)
        return image
    
    @staticmethod
    def random_horizontal_flip(image, prob=0.5):
        """随机水平翻转"""
        if random.random() < prob:
            return np.ascontiguousarray(np.fliplr(image))
        return image

    @staticmethod
    def random_vertical_flip(image, prob=0.3):
        """随机垂直翻转"""
        if random.random() < prob:
            return np.ascontiguousarray(np.flipud(image))
        return image
    
    @staticmethod
    def random_elastic_transform(image, alpha=30, sigma=5):
        """随机弹性变形"""
        if random.random() > 0.5:
            shape = image.shape
            dx = gaussian_filter((np.random.rand(*shape) * 2 - 1), sigma) * alpha
            dy = gaussian_filter((np.random.rand(*shape) * 2 - 1), sigma) * alpha

            x, y = np.meshgrid(np.arange(shape[1]), np.arange(shape[0]))
            indices = np.reshape(y+dy, (-1, 1)), np.reshape(x+dx, (-1, 1))

            transformed = map_coordinates(image, indices, order=1, mode='nearest').reshape(shape)
            return np.ascontiguousarray(transformed)
        return image
    
    @staticmethod
    def random_noise(image, noise_factor=0.1):
        """随机噪声"""
        if random.random() > 0.5:
            noise = np.random.normal(0, noise_factor, image.shape)
            return np.clip(image + noise, -1, 1)
        return image

class MedicalImageDataset(Dataset):
    def __init__(self, ap_dir, hbp_dir, image_size=(256, 256), slice_range=None, transform=None):
        """
        医学图像数据集
        Args:
            ap_dir: AP序列图像目录
            hbp_dir: HBP序列图像目录
            image_size: 图像尺寸
            slice_range: 切片范围 (start, end)
            transform: 数据变换
        """
        self.ap_dir = Path(ap_dir)
        self.hbp_dir = Path(hbp_dir)
        self.image_size = image_size
        self.slice_range = slice_range
        self.transform = transform
        
        # 获取配对的文件列表
        self.file_pairs = self._get_file_pairs()
        
    def _get_file_pairs(self):
        """获取AP和HBP的配对文件"""
        ap_files = sorted([f for f in self.ap_dir.glob("*.nii.gz")])
        hbp_files = sorted([f for f in self.hbp_dir.glob("*.nii.gz")])

        # 根据患者姓名配对
        pairs = []
        for ap_file in ap_files:
            # 提取患者姓名（去掉-ap/-AP后缀）
            ap_stem = ap_file.stem.replace('.nii', '')
            if ap_stem.lower().endswith('-ap'):
                patient_name = ap_stem[:-3]  # 去掉最后的'-ap'或'-AP'
            else:
                continue  # 跳过不符合命名规范的文件

            # 寻找对应的HBP文件
            hbp_file = None
            for hbp in hbp_files:
                hbp_stem = hbp.stem.replace('.nii', '')
                if hbp_stem.lower().endswith('-hbp'):
                    hbp_patient_name = hbp_stem[:-4]  # 去掉最后的'-hbp'或'-HBP'
                    if patient_name == hbp_patient_name:
                        hbp_file = hbp
                        break

            if hbp_file:
                pairs.append((ap_file, hbp_file))

        return pairs
    
    def _load_nii_image(self, file_path):
        """加载NII图像"""
        try:
            img = nib.load(file_path)
            data = img.get_fdata()
            return data
        except Exception as e:
            print(f"加载图像失败: {file_path}, 错误: {e}")
            return None
    
    def _preprocess_image(self, image):
        """预处理图像"""
        if image is None:
            return None

        # 检查异常值
        if np.any(np.isnan(image)) or np.any(np.isinf(image)):
            print("发现NaN或Inf值，跳过此图像")
            return None

        # 标准化到[-1, 1]范围，这对diffusion模型更合适
        # 首先进行z-score标准化
        mean = np.mean(image)
        std = np.std(image)
        if std > 1e-8:
            image = (image - mean) / std
        else:
            # 如果标准差太小，说明图像可能有问题
            print("图像标准差过小，可能存在问题")
            return None

        # 限制值范围到[-3, 3]
        image = np.clip(image, -3, 3)

        # 转换为[-1, 1]范围
        image = image / 3.0

        return image
    
    def _extract_slices(self, image):
        """提取切片"""
        if len(image.shape) == 3:
            # 3D图像，提取中间切片
            if self.slice_range:
                start, end = self.slice_range
                start = max(0, start)
                end = min(image.shape[2], end)
                slices = image[:, :, start:end]
            else:
                # 提取中间部分切片
                mid = image.shape[2] // 2
                start = max(0, mid - 10)
                end = min(image.shape[2], mid + 10)
                slices = image[:, :, start:end]
            
            return slices
        else:
            return image
    
    def _resize_image(self, image, target_size):
        """调整图像大小"""
        if len(image.shape) == 2:
            # 2D图像
            image = torch.from_numpy(image).float().unsqueeze(0).unsqueeze(0)
            image = F.interpolate(image, size=target_size, mode='bilinear', align_corners=False)
            return image.squeeze(0).squeeze(0).numpy()
        elif len(image.shape) == 3:
            # 3D图像，逐切片处理
            resized_slices = []
            for i in range(image.shape[2]):
                slice_2d = image[:, :, i]
                slice_tensor = torch.from_numpy(slice_2d).float().unsqueeze(0).unsqueeze(0)
                resized_slice = F.interpolate(slice_tensor, size=target_size, mode='bilinear', align_corners=False)
                resized_slices.append(resized_slice.squeeze(0).squeeze(0).numpy())
            return np.stack(resized_slices, axis=2)
        
        return image
    
    def __len__(self):
        return len(self.file_pairs)
    
    def __getitem__(self, idx):
        ap_file, hbp_file = self.file_pairs[idx]
        
        # 加载图像
        ap_image = self._load_nii_image(ap_file)
        hbp_image = self._load_nii_image(hbp_file)
        
        if ap_image is None or hbp_image is None:
            # 如果加载失败，返回随机索引
            return self.__getitem__(random.randint(0, len(self.file_pairs) - 1))
        
        # 提取切片
        ap_slices = self._extract_slices(ap_image)
        hbp_slices = self._extract_slices(hbp_image)
        
        # 随机选择一个切片
        if len(ap_slices.shape) == 3:
            slice_idx = random.randint(0, ap_slices.shape[2] - 1)
            ap_slice = ap_slices[:, :, slice_idx]
            hbp_slice = hbp_slices[:, :, slice_idx]
        else:
            ap_slice = ap_slices
            hbp_slice = hbp_slices
        
        # 预处理
        ap_slice = self._preprocess_image(ap_slice)
        hbp_slice = self._preprocess_image(hbp_slice)
        
        if ap_slice is None or hbp_slice is None:
            # 如果预处理失败，返回随机索引
            return self.__getitem__(random.randint(0, len(self.file_pairs) - 1))
        
        # 调整大小
        ap_slice = self._resize_image(ap_slice, self.image_size)
        hbp_slice = self._resize_image(hbp_slice, self.image_size)
        
        # 确保形状一致
        if ap_slice.shape != self.image_size or hbp_slice.shape != self.image_size:
            # 如果形状不一致，强制resize
            ap_slice = np.resize(ap_slice, self.image_size)
            hbp_slice = np.resize(hbp_slice, self.image_size)
        
        # 应用数据增强（对AP和HBP使用相同的变换以保持配对关系）
        if random.random() > 0.3:  # 70%概率应用增强
            # 生成相同的随机种子以确保AP和HBP使用相同变换
            seed = random.randint(0, 10000)
            
            # 保存当前random状态
            current_state = random.getstate()
            
            # 对AP图像应用增强
            random.seed(seed)
            ap_slice = MedicalAugmentation.random_rotation(ap_slice, 15)
            ap_slice = MedicalAugmentation.random_horizontal_flip(ap_slice, 0.5)
            ap_slice = MedicalAugmentation.random_vertical_flip(ap_slice, 0.3)
            ap_slice = MedicalAugmentation.random_elastic_transform(ap_slice)
            ap_slice = MedicalAugmentation.random_noise(ap_slice, 0.1)
            
            # 对HBP图像应用相同增强
            random.seed(seed)
            hbp_slice = MedicalAugmentation.random_rotation(hbp_slice, 15)
            hbp_slice = MedicalAugmentation.random_horizontal_flip(hbp_slice, 0.5)
            hbp_slice = MedicalAugmentation.random_vertical_flip(hbp_slice, 0.3)
            hbp_slice = MedicalAugmentation.random_elastic_transform(hbp_slice)
            hbp_slice = MedicalAugmentation.random_noise(hbp_slice, 0.1)
            
            # 恢复random状态
            random.setstate(current_state)
        
        # 转换为张量 - 修复负步长问题
        # 确保数组是连续的，避免负步长问题
        ap_slice = np.ascontiguousarray(ap_slice)
        hbp_slice = np.ascontiguousarray(hbp_slice)

        ap_tensor = torch.from_numpy(ap_slice).float().unsqueeze(0)  # 添加通道维度
        hbp_tensor = torch.from_numpy(hbp_slice).float().unsqueeze(0)
        
        # 确保张量形状一致
        ap_tensor = ap_tensor.view(1, self.image_size[0], self.image_size[1])
        hbp_tensor = hbp_tensor.view(1, self.image_size[0], self.image_size[1])
        
        # 应用变换
        if self.transform:
            ap_tensor = self.transform(ap_tensor)
            hbp_tensor = self.transform(hbp_tensor)
        
        return {
            'ap': ap_tensor,
            'hbp': hbp_tensor,
            'ap_file': str(ap_file),
            'hbp_file': str(hbp_file)
        }

def get_data_loaders(ap_dir, hbp_dir, batch_size=8, image_size=(256, 256), 
                     train_ratio=0.8, num_workers=4):
    """获取训练和验证数据加载器"""
    
    # 创建数据集
    dataset = MedicalImageDataset(ap_dir, hbp_dir, image_size=image_size)
    
    # 划分训练和验证集
    train_size = int(train_ratio * len(dataset))
    val_size = len(dataset) - train_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=0,  # 避免multiprocessing问题
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=0,  # 避免multiprocessing问题
        pin_memory=True
    )
    
    return train_loader, val_loader

if __name__ == "__main__":
    # 测试数据加载器
    ap_dir = "path/to/ap/images"
    hbp_dir = "path/to/hbp/images"
    
    train_loader, val_loader = get_data_loaders(ap_dir, hbp_dir, batch_size=4)
    
    print(f"训练集大小: {len(train_loader.dataset)}")
    print(f"验证集大小: {len(val_loader.dataset)}")
    
    # 测试一个批次
    for batch in train_loader:
        print(f"AP图像形状: {batch['ap'].shape}")
        print(f"HBP图像形状: {batch['hbp'].shape}")
        break