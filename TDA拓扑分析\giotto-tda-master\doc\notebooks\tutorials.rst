#########
Tutorials
#########

This page contains tutorials to help you get started with ``giotto-tda``. Additionally, the `post <https://towardsdatascience.com/getting-started-with-giotto-learn-a-python-library-for-topological-machine-learning-451d88d2c4bc>`_
by <PERSON> provides a friendly introduction to the philosophy of ``giotto-tda`` and to some of its main features.

.. toctree::
   :maxdepth: 1

   vietoris_rips_quickstart
   plotting_api
   mapper_quickstart
   topology_time_series
   time_series_forecasting
   persistent_homology_graphs
   local_homology

..
   include:: vietoris_rips_quickstart.rst
   :end-before: Import libraries

    Try it on `github <https://github.com/giotto-ai/giotto-tda/blob/master/examples/vietoris_rips_quickstart.ipynb>`__ for full interactivity,
    or check `the static version <vietoris_rips_quickstart.html>`__.

    .. include:: time_series_forecasting.rst
       :end-before: Import libraries

    Try it on `github <https://github.com/giotto-ai/giotto-tda/blob/master/examples/mapper_quickstart.ipynb>`__ for full interactivity,
    or check `the static version <mapper_quickstart.html>`__.
