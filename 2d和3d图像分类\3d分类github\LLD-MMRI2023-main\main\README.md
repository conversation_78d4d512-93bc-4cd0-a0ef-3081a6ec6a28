# An official implementation of training and prediction using the LLD-MMRI dataset

## Usage

First, clone the repository locally:
```
$ git clone https://github.com/LMMMEng/LLD-MMRI2023.git
$ cd main
```
We highly recommend you install the provided dependencies:
```
$ pip install -r requirements.txt
```

## Data Preparation
### 1. Download and extract dataset
**Note**: *Registered participants will receive the download link via email within 3 working days. If you do not receive it for a long time, please check the spam email or contact <NAME_EMAIL>.*   


Download and extract training dataset: 
- Linux

```
$ cat lld_mmri2023_part0* > lld_mmri2023.zip
$ unzip lld_mmri2023.zip
```
- Windows
```
$ type lld_mmri2023_part0* > lld_mmri2023.zip
$ unzip lld_mmri2023.zip

```


The data are stored in the following structure:   
```
data directory structure：
├── images
    ├── MR-a
        ├── a-seriesID
            ├── aa.nii.gz
            ├── ab.nii.gz
            ├── ac.nii.gz
    ├── MR-b
        ├── b-seriesID
            ├── ba.nii.gz
            ├── bb.nii.gz
            ├── bc.nii.gz
├── labels
    ├── Annotation.json
├── classification_dataset
```
Descriptions: 

**images**: There are train and validation data in the ```images``` directory. Each folder with MR at the beginning represents a patient case, and each case contains eight whole MRI volumes, each of which represents a single scanning phase and is saved as a nii.gz file. **You need to diagnose the category of liver lesions for a patient based on the corresponding 8 volumes**.  

**labels**: The ```Annotation.json``` contains the true volume spacing information, bounding box information, and the category information of the lesions. The corresponding labels of liver lesions in each category are as follows:
```
"Hepatic_hemangioma": 0,
"Intrahepatic_cholangiocarcinoma": 1,
"Hepatic_abscess": 2,
"Hepatic_metastasis": 3,
"Hepatic_cyst": 4,
"FOCAL_NODULAR_HYPERPLASIA": 5,
"Hepatocellular_carcinoma": 6,
"Benign": [0, 2, 4, 5],
"Malignant": [1, 3, 6],
"Inaccessible": -1
```
**Note**: **-1** indicates that category labels have not been provided for this data, on which you need to make predictions and submissions. In other words, a case with the label **-1** means the data belongs to the validation set.

**classification_dataset**:  Directory with lesion-centered 3D ROIs, please refer to [Data preprocessing](#2-data-preprocessingdata-preprocessing)

### 2. Data preprocessing
We provided lesion-centered 3D ROIs in the directory ```data/classification_dataset```, you can choose directly using our preprocessed dataset or customize your own data preprocessing.

#### 2.1 Directly using our preprocessed dataset
We provided data consisting of lesion-centered 3D ROIs. The directory structure is as follows: 
```
├── classification_dataset
    ├── images
        ├── MR-a
            ├── T2WI.nii.gz
            ├── In Phase.nii.gz
            ├── Out Phase.nii.gz
            ├── C+Delay.nii.gz
            ├── C+V.nii.gz
            ├── C-pre.nii.gz
            ├── C+A.nii.gz
            ├── DWI.nii.gz
        ├── MR-b
            ├── T2WI.nii.gz
            ├── In Phase.nii.gz
            ├── Out Phase.nii.gz
            ├── C+Delay.nii.gz
            ├── C+V.nii.gz
            ├── C-pre.nii.gz
            ├── C+A.nii.gz
            ├── DWI.nii.gz
    ├── labels
        ├── labels.txt
        ├── labels_val_inaccessible.txt
```
Descriptions: 

**images**: In the ```images``` directory, each folder with MR at the beginning represents a case, and each case contains 8 cropped lesion volumes.   
 
The specific volume information represented by each nii.gz file from a case is as follows:
```
'T2WI.nii.gz': T2-weighted imaging,
'In Phase.nii.gz': T1 in phase, 
'Out Phase.nii.gz': T1 out of phase,
'C+Delay.nii.gz': Delay phase,
'C+V.nii.gz': Venous phase,
'C-pre.nii.gz': Non-contrast phase,
'C+A.nii.gz': Arterial phase, 
'DWI.nii.gz': Diffusion-weighted imaging,
```
**labels**: 
```labels.txt``` recorded the liver lesion category of each case in training set.   
```labels_val_inaccessible.txt``` is the list of the validation set. Since labels are currently confidential, each sample has a label of -1. You need to make predictions on these data and submit results.

Those preprocessed 3D ROIs can be generated by running:
```
$ python3 preprocess/crop_roi.py --data_dir data/images/ --anno-path data/labels/Annotation.json --save-dir data/classification_dataset/images/
```

#### 2.2 Customize your own data preprocessing

In addition to using the preprocessed data we provided, you can choose to customize your own data preprocessing process. The contents of ```Annotation.json``` and [preprocess/crop_roi.py](```preprocess/crop_roi.py```) could be as references. Keep in mind, that the goal is to diagnose the liver lesion category of each case.

#### 2.3 Data division
We recommend using 5-fold cross-validation on the accessible dataset to evaluate your own algorithm, we have provided a cross-validation label file which you can refer to:
```
├── classification_dataset
    ├── labels
        ├── train_fold1.txt
        ...
        ├── train_fold5.txt
        ├── val_fold1.txt
        ...
        ├── val_fold5.txt
```

Also, you can generate n-fold cross-validated dataset yourself with the following command:
```
$ python3 preprocess/gene_cross_val.py --lab-path data/classification_dataset/labels/labels.txt --save-dir data/classification_dataset/labels/ --num-folds 5 --seed 66
```
This will produce the corresponding file under ```save-dir```

## Training
We use a 3D implementation of the [UniFormer-S](https://github.com/Sense-X/UniFormer/tree/main/image_classification) as the baseline model, and the multi-phase images are treated as the input channels of the model. The details can be found in [models/uniformer.py](models/uniformer.py).

To train the baseline model on LLD-MMRI dataset on a single node with 2 GPUs for 300 epochs, please run the following command:

```
$ python3 -m torch.distributed.launch --master_port=$((RANDOM+10000)) --nproc_per_node=2 train.py --data_dir data/classification_dataset/images/ --train_anno_file data/classification_dataset/labels/train_fold1.txt --val_anno_file data/classification_dataset/labels/val_fold1.txt --batch-size 4 --model uniformer_small_IL --lr 1e-4 --warmup-epochs 5 --epochs 300 --output output/
```

## Prediction
We accept submissions in the form of a Json file containing the predicted results at the specified time, the details will be notified to the registered participants via email.  

You can download the [trained model weights](https://github.com/LMMMEng/LLD-MMRI2023/releases/download/release-v1/best_f1_checkpoint-216.pth.tar) and use the following command to make predictions:

```
$ python3 predict.py --data_dir data/classification_dataset/images --val_anno_file data/classification_dataset/labels/labels_val_inaccessible.txt --model uniformer_small_IL --batch-size 8 --checkpoint best_f1_checkpoint-216.pth.tar --results-dir output/20230411-192839-uniformer_small_IL/ --team_name LLDBaseline
```

You can also retrain the baseline model, once you have a satisfactory model, please generate a prediction file on the validation set by running the following command:
```
$ python3 predict.py --data_dir data/classification_dataset/images --val_anno_file data/classification_dataset/labels/labels_val_inaccessible.txt --model uniformer_small_IL --batch-size 8 --checkpoint path-to-model-checkpoint --results-dir path-to-results-dir --team_name your_team_name
```
This will generate a ```your_team_name.json``` under ```results-dir```.    

**Important**: You may have custom data processing, model design, and training pipeline. Therefore, the provided prediction code may not be applicable. We provide a prediction result file format [here](output/LLDBaseline.json), please strictly follow this format to generate predictions. In addition, the submitted Json file must be named by your registered team name. Otherwise we can't make an evaluation.
