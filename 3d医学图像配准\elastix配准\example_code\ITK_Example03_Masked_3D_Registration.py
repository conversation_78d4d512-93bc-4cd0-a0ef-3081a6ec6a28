#!/usr/bin/env python
# coding: utf-8

# ## 3. 3D Image Registration with Masks

# ### Registration

# Often, some content in the images may not correspond. For example, there may be background content or noisy areas. A mask defined on the fixed and/or moving image can be used to exclude these regions at a pixel level from the similarity metric computations. This improves the robustness of registration.
# 
# A mask is a binary image with 1 meaning that a pixel is include in elastix' computation and a 0 meaning it's not.
# 
# For more information, see Section 5.4, "Masks" of the [Elastix Manual](https://elastix.lumc.nl/download/elastix-5.0.1-manual.pdf).

# In[1]:


import itk
from itkwidgets import compare, checkerboard, view


# The function calls in the 3D case to import and register the images is similar to the 2D case. Masks, usually binary images, are import with the itk library similar to the images.  

# In[7]:


# Import Images
fixed_image = itk.imread('data/CT_3D_lung_fixed.mha', itk.F)
moving_image = itk.imread('data/CT_3D_lung_moving.mha', itk.F)

# Import Custom Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile('data/parameters.3D.NC.affine.ASGD.001.txt')

# "WriteResultImage" needs to be set to "true" so that the image is resampled at the end of the registration
# and the result_image is populated properly
parameter_object.SetParameter(0, "WriteResultImage", "true")

# Import Mask Images
fixed_mask = itk.imread('data/CT_3D_lung_fixed_mask.mha', itk.UC)
moving_mask = itk.imread('data/CT_3D_lung_moving_mask.mha', itk.UC)

# Or Optionally Create Masks from scratch

# MaskImageType = itk.Image[itk.UC, 2]
# fixed_mask = itk.binary_threshold_image_filter(fixed,
#                                               lower_threshold=80.0,
#                                               inside_value=1,
#                                               ttype=(type(fixed), MaskImageType))
# moving_mask = itk.binary_threshold_image_filter(moving,
#                                                lower_threshold=80.0,
#                                                inside_value=1,
#                                                ttype=(type(moving), MaskImageType))


# ### Input Visualization
# The images and their masks can be visualized with the itkwidget's view function. This can be useful to visually inspect the quality of the masks.

# In[3]:


view(fixed_image, label_image=fixed_mask)


# In[4]:


view(moving_image, label_image = moving_mask)


# ### Registration

# Registration can either be done in one line with the registration function...

# In[8]:


# Call registration function
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image,
    parameter_object=parameter_object,
    fixed_mask=fixed_mask, moving_mask=moving_mask,
    log_to_console=False)


# .. or by initiating an elastix image filter object.

# In[6]:


# Load Elastix Image Filter Object
# Fixed and moving image should be given to the Elastix method to ensure that
# the correct 3D class is initialized.
elastix_object = itk.ElastixRegistrationMethod.New(fixed_image, moving_image)
elastix_object.SetFixedMask(fixed_mask)
elastix_object.SetMovingMask(moving_mask)
elastix_object.SetParameterObject(parameter_object)

# Set additional options
elastix_object.SetLogToConsole(False)

# Update filter object (required)
elastix_object.UpdateLargestPossibleRegion()

# Results of Registration
result_image = elastix_object.GetOutput()
result_transform_parameters = elastix_object.GetTransformParameterObject()


# ### Output Visualization
# The results of the 3D image registration can also be visualized with widgets from the itkwidget library such as the checkerboard and compare widgets.

# In[9]:


checkerboard(fixed_image, result_image,pattern=5)


# In[10]:


compare(fixed_image, result_image, label_image= [fixed_image, result_image],link_cmap=True)


# In[ ]:




