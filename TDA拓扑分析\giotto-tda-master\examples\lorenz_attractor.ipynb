{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# The Lorenz attractor\n", "\n", "This notebook contains a full TDA pipeline to analyse the transitions of the Lorenz system to a chaotic regime from the stable one and viceversa.\n", "\n", "If you are looking at a static version of this notebook and would like to run its contents, head over to [GitHub](https://github.com/giotto-ai/giotto-tda/blob/master/examples/lorenz_attractor.ipynb) and download the source.\n", "\n", "## See also\n", "\n", "- [Topology of time series](https://giotto-ai.github.io/gtda-docs/latest/notebooks/topology_time_series.html), in which the *Takens embedding* technique used here is explained in detail and illustrated via simple examples.\n", "- [Gravitational waves detection](https://giotto-ai.github.io/gtda-docs/latest/notebooks/gravitational_waves_detection.html), where,following [arXiv:1910.08245](https://arxiv.org/abs/1910.08245), the Takens embedding technique is shown to be effective for the detection of gravitational waves signals buried in background noise.\n", "- [Topological feature extraction using VietorisRipsPersistence and PersistenceEntropy](https://giotto-ai.github.io/gtda-docs/latest/notebooks/vietoris_rips_quickstart.html) for a quick introduction to general topological feature extraction in ``giotto-tda``.\n", "\n", "**License: AGPLv3**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import libraries\n", "The first step consists in importing relevant *gtda* components and other useful libraries or modules."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the gtda modules\n", "from gtda.time_series import <PERSON><PERSON><PERSON><PERSON>, SlidingWindow, takens_embedding_optimal_parameters, \\\n", "    TakensEmbedding, PermutationEntropy\n", "from gtda.homology import WeakAlphaPersistence, VietorisRipsPersistence\n", "from gtda.diagrams import Scaler, Filtering, PersistenceEntropy, BettiCurve, PairwiseDistance\n", "from gtda.graphs import KNeighborsGraph, GraphGeodesicDistance\n", "\n", "from gtda.pipeline import Pipeline\n", "\n", "import numpy as np\n", "from sklearn.metrics import pairwise_distances\n", "\n", "import plotly.express as px\n", "from plotly.offline import init_notebook_mode, iplot\n", "init_notebook_mode(connected=True)\n", "\n", "# gtda plotting functions\n", "from gtda.plotting import plot_heatmap\n", "\n", "# Import data from openml\n", "import openml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plotting functions\n", "from gtda.plotting import plot_point_cloud"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting up the Lorenz attractor simulation\n", "\n", "In the next block we set up all the parameters of the <PERSON><PERSON><PERSON> system and we define also the instants at which the regime (stable VS chaotic) changes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plotting the trajectories of the Lorenz system\n", "from openml.datasets.functions import get_dataset\n", "\n", "point_cloud = get_dataset(42182).get_data(dataset_format='array')[0]\n", "plot_point_cloud(point_cloud)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Selecting the z-axis and the label rho\n", "X = point_cloud[:, 2]\n", "y = point_cloud[:, 3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = px.line(title='Trajectory of the Lorenz solution, projected along the z-axis')\n", "fig.add_scatter(y=X, name='X')\n", "fig.add_scatter(y=y, name='y')\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Resampling the time series\n", "\n", "It is important to find the correct time scale at which key signals take place. Here we propose one possible resampling period: *10h*. Recall that the unit time is *1h*. The resampler method is used to perform the resampling."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["period = 10\n", "periodicSampler = Resampler(period=period)\n", "\n", "X_sampled, y_sampled = periodicSampler.fit_transform_resample(X, y)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = px.line(title='Trajectory of the Lorenz solution, projected along the z-axis and resampled every 10h')\n", "fig.add_scatter(y=X_sampled.flatten(), name='X_sampled')\n", "fig.add_scatter(y=y_sampled, name='y_sampled')\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Takens Embedding\n", "\n", "In order to obtain meaningful topological features from a time series, we use a *time-delay embedding* technique named after <PERSON><PERSON> who used it in the 1960s in his foundational work on dynamical systems.\n", "\n", "The idea is simple: given a time series $X(t)$, one can extract a sequence of vectors of the form $X_i := [(X(t_i)), X(t_i + 2 \\tau), ..., X(t_i + M \\tau)]$. The difference between $t_i$ and $t_{i-1}$ is called *stride*.\n", "\n", "$M$ and $\\tau$ are optimized automatically in this example according to known heuristics implemented in ``giotto-tda`` in the ``takens_embedding_optimal_parameters`` function. They can also be set by hand if preferred."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["max_time_delay = 3\n", "max_embedding_dimension = 10\n", "stride = 1\n", "optimal_time_delay, optimal_embedding_dimension = takens_embedding_optimal_parameters(\n", "    X_sampled, max_time_delay, max_embedding_dimension, stride=stride\n", "    )\n", "\n", "print(f\"Optimal embedding time delay based on mutual information: {optimal_time_delay}\")\n", "print(f\"Optimal embedding dimension based on false nearest neighbors: {optimal_embedding_dimension}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Having computed reasonable values for the parameters by looking at the whole time series, we can now perform the embedding procedure (which transforms a single time series into a single point cloud) on local sliding windows over the data. The result of this will be a \"time series of point clouds\" with possibly interesting topologies, which we will be able to feed directly to our homology transformers.\n", "\n", "We first construct sliding windows using ``SlidingWindow`` transformer-resampler, and then use the ``TakensEmbedding`` transformer to perform the embedding in parallel on each window, using the parameters ``optimal_time_delay`` and ``optimal_embedding_dimension`` found above."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["window_size = 41\n", "window_stride = 5\n", "SW = SlidingWindow(size=window_size, stride=window_stride)\n", "\n", "X_windows, y_windows = SW.fit_transform_resample(X_sampled, y_sampled)\n", "\n", "TE = TakensEmbedding(time_delay=optimal_time_delay, dimension=optimal_embedding_dimension, stride=stride)\n", "X_embedded = TE.fit_transform(X_windows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can plot the Takens embedding of a specific window either by using ``plot_point_cloud``, or by using the ``plot`` method of ``SlidingWindow``, as shown below.\n", "\n", "*Note*: only the first three coordinates are plotted!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["window_number = 3\n", "TE.plot(X_embedded, sample=window_number)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For comparison, here is the portion of time series containing the data which originates this point cloud. Notice the quasi-periodicity, corresponding to the loop in the point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embedded_begin, embedded_end = SW.slice_windows(X_windows)[window_number]\n", "window_indices = np.arange(embedded_begin, embedded_end + optimal_time_delay * (optimal_embedding_dimension - 1))\n", "fig = px.line(title=f\"Resampled Lorenz solution over sliding window {window_number}\")\n", "fig.add_scatter(x=window_indices, y=X_sampled[window_indices], name=\"X_sampled\")\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Persistence diagram\n", "The topological information in the embedding is synthesised via the persistence diagram. The horizontal axis corresponds to the moment in which a homological generator is born, while the vertical axis corresponds to the moments in which a homological generator dies.\n", "The generators of the homology groups (at given rank) are colored differently."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["homology_dimensions = (0, 1, 2)\n", "WA = WeakAlphaPersistence(homology_dimensions=homology_dimensions)\n", "\n", "X_diagrams = WA.fit_transform(X_embedded)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can plot the persistence diagram for the embedding of the same sliding window as before:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WA.plot(X_diagrams, sample=window_number)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scikit-learn–style pipeline\n", "\n", "One of the advantages of ``giotto-tda`` is the compatibility with ``scikit-learn``. It is possible to set up and run a full pipeline such as the one above in a few lines:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Steps of the Pipeline\n", "steps = [('sampling', periodicSampler),\n", "         ('window', SW),\n", "         ('embedding', TE),\n", "         ('diagrams', WA)]\n", "\n", "# Define the Pipeline\n", "pipeline = Pipeline(steps)\n", "\n", "# Run the pipeline\n", "X_diagrams = pipeline.fit_transform(X)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The final result is the same as before:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipeline[-1].plot(X_diagrams, sample=window_number)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rescaling the diagram\n", "\n", "By default, rescaling a diagram via ``Scaler`` means normalizing points such that the maximum \"bottleneck distance\" from the *empty diagram* (across all homology dimensions) is equal to 1. Notice that this means the birth and death scales are modified. We can do this as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diagramScaler = Scaler()\n", "\n", "X_scaled = diagramScaler.fit_transform(X_diagrams)\n", "\n", "diagramScaler.plot(X_scaled, sample=window_number)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filtering diagrams\n", "\n", "Filtering a diagram means eliminating the homology generators whose lifespan is considered too short to be significant. We can use ``Filtering`` as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diagramFiltering = Filtering(epsilon=0.1, homology_dimensions=(1, 2))\n", "\n", "X_filtered = diagramFiltering.fit_transform(X_scaled)\n", "\n", "diagramFiltering.plot(X_filtered, sample=window_number)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can add the steps above to our pipeline:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["steps_new = [\n", "    ('scaler', diagramScaler),\n", "    ('filtering', diagramFiltering)\n", "]\n", "\n", "pipeline_filter = Pipeline(steps + steps_new)\n", "\n", "X_filtered = pipeline_filter.fit_transform(X)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Persistence entropy\n", "\n", "The *entropy* of persistence diagrams can be calculated via ``PersistenceEntropy``:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PE = PersistenceEntropy()\n", "\n", "X_persistence_entropy = PE.fit_transform(X_scaled)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = px.line(title='Persistence entropies, indexed by sliding window number')\n", "for dim in range(X_persistence_entropy.shape[1]):\n", "    fig.add_scatter(y=X_persistence_entropy[:, dim], name=f\"PE in homology dimension {dim}\")\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>\n", "\n", "The Betti curves of a persistence diagram can be computed and plotted using ``BettiCurve``:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BC = BettiCurve()\n", "\n", "X_betti_curves = BC.fit_transform(X_scaled)\n", "\n", "BC.plot(X_betti_curves, sample=window_number)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Distances among diagrams\n", "\n", "In this section we show how to compute several notions of distances among persistence diagrams.\n", "\n", "In each case, we will obtain distance matrices whose i-th row encodes the distance of the i-th diagram from all the others.\n", "\n", "We start with the so-called \"landscape $L^2$ distance\": when ``order`` is ``None``, the output is one distance matrix per sample and homology dimension."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_L = 2\n", "n_layers = 5\n", "PD = PairwiseDistance(metric='landscape',\n", "                      metric_params={'p': p_L, 'n_layers': n_layers, 'n_bins': 1000},\n", "                      order=None)\n", "\n", "X_distance_L = PD.fit_transform(X_diagrams)\n", "X_distance_L.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is what distances in homology dimension 0 look like:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_heatmap(X_distance_L[:, :, 0], colorscale='blues')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We now change metric and compute the \"$2$-Was<PERSON>stein distances\" between the diagrams. This one takes longer!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_W = 2\n", "PD = PairwiseDistance(metric='wasserstein',\n", "                      metric_params={'p': p_W, 'delta': 0.1},\n", "                      order=None)\n", "\n", "X_distance_W = PD.fit_transform(X_diagrams)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And again this is what distances in homology dimension 0 look like:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_heatmap(X_distance_W[:, :, 0], colorscale='blues')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Notice that how dramatically things can change when the metrics are modified."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## New distances in the embedding space: kNN graphs and geodesic distances\n", "\n", "We propose here a new way to compute distances between points in the embedding space. Instead of considering the Euclidean distance in the Takens space, we propose to build a $k$-nearest neighbors graph and then use the geodesic distance on such graph."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_neighbors = 2\n", "kNN = KNeighborsGraph(n_neighbors=n_neighbors)\n", "\n", "X_kNN = kNN.fit_transform(X_embedded)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Given the graph embedding, the natural notion of distance between vertices corresponds to the lengths of the shortest path connecting two vertices. This is also known as *graph geodesic distance*. We compute it and plot it for our chosen window number."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GGD = GraphGeodesicDistance()\n", "\n", "GGD.fit_transform_plot(X_kNN, sample=window_number);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For comparison, this is what the ordinary pairwise Euclidean distance matrix looks like for the same window:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_heatmap(pairwise_distances(X_embedded[window_number]), colorscale='blues')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is what the first few steps (before scaling and filtering) of the pipeline described above would be if you'd like persistence diagrams to be obtained using this new distance instead.\n", "\n", "*Note*: ``WeakAlphaPersistence`` cannot be used now as it needs point cloud input. We can use instead an instance of ``VietorisRipsPersistence``, but we have to take care to pass ``metric='precomputed'`` to the constructor!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Steps of the Pipeline\n", "steps = [\n", "    ('sampling', periodicSampler),\n", "    ('window', SW),\n", "    ('embedding', TE),\n", "    ('kNN_graph', kNN),\n", "    ('graph_geo_distance', GGD),\n", "    ('diagrams', <PERSON><PERSON>orisRipsPersistence(metric='precomputed',\n", "                                         homology_dimensions=homology_dimensions))\n", "    ]\n", "\n", "# Define the Pipeline\n", "pipeline = Pipeline(steps)\n", "\n", "# Run the pipeline\n", "X_diagrams = pipeline.fit_transform(X)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's look at the persistence diagrams obtained with this new distance:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipeline[-1].plot(X_diagrams, sample=window_number)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}}, "nbformat": 4, "nbformat_minor": 4}