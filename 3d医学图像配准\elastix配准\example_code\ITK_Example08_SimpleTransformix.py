#!/usr/bin/env python
# coding: utf-8

# ## 8. Simple Transformix

# After image registrations it is often useful to apply the transformation as found by the registration to another image. Maybe you want to apply the transformation to an original (larger) image to gain resolution. Or maybe you need the transformation to apply it to a label image (segmentation). Transformix is an image filter that was developed together with elastix, that can be used to do these transformations.
# 
# A spatial transformation is defined as a mapping from the fixed image domain to the moving image domain. More information on the precise definition of the transform can be found in the [elastix manual](https://elastix.lumc.nl/download/elastix-5.0.1-manual.pdf). Transformix can be used to apply this mapping not only to images, but also to masks (binary images) and point sets see [example 9](ITK_Example09_PointSetAndMaskTransformation.ipynb#section_id10).
# 
# As an alternative, see the [itk resampling example](ITK_Example13_ITKResampling.ipynb).

# ### Elastix

# In[1]:


import itk
from itkwidgets import compare, checkerboard


# In[2]:


# Import Images
fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)
moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)

# Import Default Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_map_rigid = parameter_object.GetDefaultParameterMap('rigid')
parameter_object.AddParameterMap(parameter_map_rigid)


# Registration with the registration function...

# In[3]:


# Call registration function
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image,
    parameter_object=parameter_object)


# ### Transformix
# The transform parameters that elastix outputs can be given to transformix as input for the transformations. The output transform parameters from elastix are mappings from the fixed image to the moving image domain. Transformix therefore uses a backwards mapping to obtain a registered version of the moving image (moving -> fixed domain). 

# In[4]:


# Import Image to transform
# In this example the same moving image is used, that was used for elastix,
# this however will result in the same image as was already given by the 
# first elastix registration.
moving_image_transformix = itk.imread('data/CT_2D_head_moving.mha', itk.F)


# Transformation can either be done in one line with the transformix function...

# In[5]:


result_image_transformix = itk.transformix_filter(
    moving_image,
    result_transform_parameters)


# .. or by initiating an transformix image filter object similar to the elastix algorithm.

# In[6]:


# Load Transformix Object
transformix_object = itk.TransformixFilter.New(moving_image_transformix)
transformix_object.SetTransformParameterObject(result_transform_parameters)

# Update object (required)
transformix_object.UpdateLargestPossibleRegion()

# Results of Transformation
result_image_transformix = transformix_object.GetOutput()


# ### Visualization
# The results of the image transform can be visualized with widgets from the itkwidget library such as the checkerboard and compare widgets.

# In[7]:


checkerboard(fixed_image, result_image_transformix, pattern=5)


# In[8]:


compare(fixed_image, result_image_transformix, link_cmap=True)


# In[ ]:




