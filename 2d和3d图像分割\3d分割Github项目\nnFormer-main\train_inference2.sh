#!/bin/bash


while getopts 'c:n:t:r:p' OPT; do
    case $OPT in
        c) cuda=$OPTARG;;
        n) name=$OPTARG;;
		t) task=$OPTARG;;
        r) train="true";;
        p) predict="true";;
        
    esac
done
echo $name	


if ${train}
then
    # 设置环境变量
    export CUDA_VISIBLE_DEVICES=0
    export CUDA_LAUNCH_BLOCKING=1
    export CUDA_VISIBLE_DEVICES=${cuda}      
    
    # 清理GPU内存
    nvidia-smi -r
    
    # 设置多进程参数
    export OMP_NUM_THREADS=1
    export OPENBLAS_NUM_THREADS=1
    export MKL_NUM_THREADS=1
    export VECLIB_MAXIMUM_THREADS=1
    export NUMEXPR_NUM_THREADS=1
	
	cd /root/autodl-tmp/nnFormerFrame/nnFormer/nnformer/
	CUDA_VISIBLE_DEVICES=${cuda} nnFormer_train 3d_fullres nnFormerTrainerV2 ${task} 0   
fi

# if ${predict}
# then


# 	cd /home/<USER>/new_transformer/nnFormerFrame/DATASET/nnFormer_raw/nnFormer_raw_data/Task001_ACDC/
# 	CUDA_VISIBLE_DEVICES=${cuda} nnFormer_predict -i imagesTs -o inferTs/${name} -m 3d_fullres -t ${task} -f 0 -chk model_best -tr nnFormerTrainerV2_${name}
# 	python inference_acdc.py ${name}
# fi


