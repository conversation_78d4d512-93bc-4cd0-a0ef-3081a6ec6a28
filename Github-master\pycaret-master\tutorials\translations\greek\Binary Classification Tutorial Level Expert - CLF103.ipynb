{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#  <span style=\"color:orange\">Μάθημα Δυαδικής <PERSON>α<PERSON>ινόμησης (CLF103) - Ε<PERSON><PERSON><PERSON>εδο Προχωρημένων</span>\n", "\n", "**Ημερομηνία Τελευταίας Τροποποίησης: 20 Αυγούστου 2022**\n", "\n", "# Έργο εν εξελίξει\n", "Αυτή τη στιγμή εργαζόμαστε πάνω σε αυτό το μάθημα. Παρακαλούμε να ανατρέξετε εδώ σύντομα!\n", "\n", "# Στο ενδιάμεσο, μπορείτε να δείτε: \n", "- __[Μάθημα Δυαδικής Ταξινόμησης (CLF101) - Επίπεδο Αρχάριων](https://github.com/pycaret/pycaret/blob/master/tutorials/Greek/Binary%20Classification%20Tutorial%20Level%20Beginner%20-%20%20CLF101.ipynb)__\n", "- __[Μάθημα Δυαδικής Ταξινόμησης (CLF102) - Μέσ<PERSON> Επίπεδο](https://github.com/pycaret/pycaret/blob/master/tutorials/Greek/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}