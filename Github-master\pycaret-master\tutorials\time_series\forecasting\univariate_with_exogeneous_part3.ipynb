{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Now that we have gone through a manual process of modeling our dataset, let's see if we can replicate this using an Automated workflow. As a reminder, our plan of action was as follows:\n", "\n", "1. Perform EDA on the dataset to extract valuable insight about the process generating the time series **(COMPLETED)**.\n", "2. Build a baseline model (univariable model without exogenous variables) for benchmarking purposes **(COMPLETED)**.\n", "3. Build a univariate model with all exogenous variables to check best possible performance **(COMPLETED)**.\n", "4. Evaluate the model with exogenous variables and discuss any potential issues **(COMPLETED)**.\n", "5. Overcome issues identified above **(COMPLETED)**.\n", "6. Make future predictions with the best model **(COMPLETED)**.\n", "7. Replicate flow with Automated Time Series Modeling (AutoML) **(Covered in this notebook)**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Only enable critical logging (Optional)\n", "import os\n", "os.environ[\"PYCARET_CUSTOM_LOGGING_LEVEL\"] = \"CRITICAL\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "System:\n", "    python: 3.9.16 (main, Jan 11 2023, 16:16:36) [MSC v.1916 64 bit (AMD64)]\n", "executable: C:\\Users\\<USER>\\.conda\\envs\\pycaret_dev_sktime_16p1\\python.exe\n", "   machine: Windows-10-10.0.19044-SP0\n", "\n", "PyCaret required dependencies:\n", "                 pip: 22.3.1\n", "          setuptools: 65.6.3\n", "             pycaret: 3.0.0rc9\n", "             IPython: 8.10.0\n", "          ipywidgets: 8.0.4\n", "                tqdm: 4.64.1\n", "               numpy: 1.23.5\n", "              pandas: 1.5.3\n", "              jinja2: 3.1.2\n", "               scipy: 1.10.0\n", "              joblib: 1.2.0\n", "             sklearn: 1.2.1\n", "                pyod: 1.0.8\n", "            imblearn: 0.10.1\n", "   category_encoders: 2.6.0\n", "            lightgbm: 3.3.5\n", "               numba: 0.56.4\n", "            requests: 2.28.2\n", "          matplotlib: 3.7.0\n", "          scikitplot: 0.3.7\n", "         yellowbrick: 1.5\n", "              plotly: 5.13.0\n", "             kaleido: 0.2.1\n", "         statsmodels: 0.13.5\n", "              sktime: 0.16.1\n", "               tbats: 1.1.2\n", "            pmdarima: 2.0.2\n", "              psutil: 5.9.4\n", "\n", "PyCaret optional dependencies:\n", "                shap: 0.41.0\n", "           interpret: Not installed\n", "                umap: Not installed\n", "    pandas_profiling: Not installed\n", "  explainerdashboard: Not installed\n", "             autoviz: Not installed\n", "           fairlearn: Not installed\n", "             xgboost: Not installed\n", "            catboost: Not installed\n", "              kmodes: Not installed\n", "             mlxtend: Not installed\n", "       statsforecast: Not installed\n", "        tune_sklearn: Not installed\n", "                 ray: Not installed\n", "            hyperopt: Not installed\n", "              optuna: Not installed\n", "               skopt: Not installed\n", "              mlflow: 2.1.1\n", "              gradio: Not installed\n", "             fastapi: Not installed\n", "             uvicorn: Not installed\n", "              m2cgen: Not installed\n", "           evidently: Not installed\n", "               fugue: 0.8.0\n", "           streamlit: Not installed\n", "             prophet: 1.1.2\n"]}], "source": ["def what_is_installed():\n", "    from pycaret import show_versions\n", "    show_versions()\n", "\n", "try:\n", "    what_is_installed()\n", "except ModuleNotFoundError:\n", "    !pip install pycaret\n", "    what_is_installed()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from pycaret.datasets import get_data\n", "from pycaret.time_series import TSForecastingExperiment"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Global Figure Settings for notebook ----\n", "# Depending on whether you are using jupyter notebook, jupyter lab, Google Colab, you may have to set the renderer appropriately\n", "# NOTE: Setting to a static renderer here so that the notebook saved size is reduced.\n", "global_fig_settings = {\n", "    # \"renderer\": \"notebook\",\n", "    \"renderer\": \"png\",\n", "    \"width\": 1000,\n", "    \"height\": 600,\n", "}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CO(GT)</th>\n", "      <th>NOx(GT)</th>\n", "      <th>PT08.S3(NOx)</th>\n", "      <th>RH</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2005-03-05 15:00:00</th>\n", "      <td>1.5</td>\n", "      <td>180.0</td>\n", "      <td>820.0</td>\n", "      <td>28.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-03-05 16:00:00</th>\n", "      <td>1.8</td>\n", "      <td>255.0</td>\n", "      <td>751.0</td>\n", "      <td>29.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-03-05 17:00:00</th>\n", "      <td>2.0</td>\n", "      <td>251.0</td>\n", "      <td>721.0</td>\n", "      <td>38.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-03-05 18:00:00</th>\n", "      <td>1.9</td>\n", "      <td>258.0</td>\n", "      <td>695.0</td>\n", "      <td>56.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-03-05 19:00:00</th>\n", "      <td>2.5</td>\n", "      <td>344.0</td>\n", "      <td>654.0</td>\n", "      <td>57.9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     CO(GT)  NOx(GT)  PT08.S3(NOx)    RH\n", "index                                                   \n", "2005-03-05 15:00:00     1.5    180.0         820.0  28.3\n", "2005-03-05 16:00:00     1.8    255.0         751.0  29.7\n", "2005-03-05 17:00:00     2.0    251.0         721.0  38.7\n", "2005-03-05 18:00:00     1.9    258.0         695.0  56.3\n", "2005-03-05 19:00:00     2.5    344.0         654.0  57.9"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data = get_data(\"airquality\", verbose=False)\n", "\n", "# Limiting the data for demonstration purposes.\n", "data = data.iloc[-720:]\n", "data[\"index\"] = pd.to_datetime(data[\"Date\"] + \" \" + data[\"Time\"])\n", "data.drop(columns=[\"Date\", \"Time\"], inplace=True)\n", "data.replace(-200, np.nan, inplace=True)\n", "data.set_index(\"index\", inplace=True)\n", "\n", "target = \"CO(GT)\"\n", "exog_vars = ['NOx(GT)', 'PT08.S3(NOx)', 'RH']\n", "include = [target] + exog_vars\n", "data = data[include]\n", "data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 7: AutoML"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["FH=48\n", "metric = \"mase\"\n", "exclude = [\"auto_arima\", \"bats\", \"tbats\", \"lar_cds_dt\", \"par_cds_dt\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 7A: Univariate AutoML with and without Exogenous Variables"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_92158_row25_col1, #T_92158_row30_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_92158\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_92158_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_92158_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_92158_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_92158_row0_col1\" class=\"data row0 col1\" >42</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_92158_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_92158_row1_col1\" class=\"data row1 col1\" >CO(GT)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_92158_row2_col0\" class=\"data row2 col0\" >Approach</td>\n", "      <td id=\"T_92158_row2_col1\" class=\"data row2 col1\" >Univariate</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_92158_row3_col0\" class=\"data row3 col0\" >Exogenous Variables</td>\n", "      <td id=\"T_92158_row3_col1\" class=\"data row3 col1\" >Present</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_92158_row4_col0\" class=\"data row4 col0\" >Original data shape</td>\n", "      <td id=\"T_92158_row4_col1\" class=\"data row4 col1\" >(720, 4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_92158_row5_col0\" class=\"data row5 col0\" >Transformed data shape</td>\n", "      <td id=\"T_92158_row5_col1\" class=\"data row5 col1\" >(720, 4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_92158_row6_col0\" class=\"data row6 col0\" >Transformed train set shape</td>\n", "      <td id=\"T_92158_row6_col1\" class=\"data row6 col1\" >(672, 4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_92158_row7_col0\" class=\"data row7 col0\" >Transformed test set shape</td>\n", "      <td id=\"T_92158_row7_col1\" class=\"data row7 col1\" >(48, 4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_92158_row8_col0\" class=\"data row8 col0\" >Rows with missing values</td>\n", "      <td id=\"T_92158_row8_col1\" class=\"data row8 col1\" >3.8%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_92158_row9_col0\" class=\"data row9 col0\" >Fold Generator</td>\n", "      <td id=\"T_92158_row9_col1\" class=\"data row9 col1\" >ExpandingWindowSplitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_92158_row10_col0\" class=\"data row10 col0\" >Fold Number</td>\n", "      <td id=\"T_92158_row10_col1\" class=\"data row10 col1\" >3</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_92158_row11_col0\" class=\"data row11 col0\" >Enforce Prediction Interval</td>\n", "      <td id=\"T_92158_row11_col1\" class=\"data row11 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_92158_row12_col0\" class=\"data row12 col0\" >Splits used for hyperparameters</td>\n", "      <td id=\"T_92158_row12_col1\" class=\"data row12 col1\" >all</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_92158_row13_col0\" class=\"data row13 col0\" >User Defined Seasonal Period(s)</td>\n", "      <td id=\"T_92158_row13_col1\" class=\"data row13 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_92158_row14_col0\" class=\"data row14 col0\" >Ignore Seasonality Test</td>\n", "      <td id=\"T_92158_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_92158_row15_col0\" class=\"data row15 col0\" >Seasonality Detection Algo</td>\n", "      <td id=\"T_92158_row15_col1\" class=\"data row15 col1\" >auto</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_92158_row16_col0\" class=\"data row16 col0\" >Max Period to Consider</td>\n", "      <td id=\"T_92158_row16_col1\" class=\"data row16 col1\" >60</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_92158_row17_col0\" class=\"data row17 col0\" >Seasonal Period(s) Tested</td>\n", "      <td id=\"T_92158_row17_col1\" class=\"data row17 col1\" >[24, 23, 25, 2, 48, 22, 47, 49, 26, 3, 12, 11, 21, 13, 46, 10, 50]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_92158_row18_col0\" class=\"data row18 col0\" >Significant Seasonal Period(s)</td>\n", "      <td id=\"T_92158_row18_col1\" class=\"data row18 col1\" >[24, 23, 25, 2, 48, 22, 47, 49, 26, 3, 12, 11, 21, 13, 46, 10, 50]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_92158_row19_col0\" class=\"data row19 col0\" >Significant Seasonal Period(s) without Harmonics</td>\n", "      <td id=\"T_92158_row19_col1\" class=\"data row19 col1\" >[48, 46, 50, 22, 47, 49, 26, 21]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_92158_row20_col0\" class=\"data row20 col0\" >Remove Harmonics</td>\n", "      <td id=\"T_92158_row20_col1\" class=\"data row20 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_92158_row21_col0\" class=\"data row21 col0\" >Harmonics Order Method</td>\n", "      <td id=\"T_92158_row21_col1\" class=\"data row21 col1\" >harmonic_max</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_92158_row22_col0\" class=\"data row22 col0\" >Num Seasonalities to Use</td>\n", "      <td id=\"T_92158_row22_col1\" class=\"data row22 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_92158_row23_col0\" class=\"data row23 col0\" >All Seasonalities to Use</td>\n", "      <td id=\"T_92158_row23_col1\" class=\"data row23 col1\" >[24]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_92158_row24_col0\" class=\"data row24 col0\" >Primary Seasonality</td>\n", "      <td id=\"T_92158_row24_col1\" class=\"data row24 col1\" >24</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_92158_row25_col0\" class=\"data row25 col0\" >Seasonality Present</td>\n", "      <td id=\"T_92158_row25_col1\" class=\"data row25 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_92158_row26_col0\" class=\"data row26 col0\" >Target Strictly Positive</td>\n", "      <td id=\"T_92158_row26_col1\" class=\"data row26 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_92158_row27_col0\" class=\"data row27 col0\" >Target White Noise</td>\n", "      <td id=\"T_92158_row27_col1\" class=\"data row27 col1\" >No</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_92158_row28_col0\" class=\"data row28 col0\" >Recommended d</td>\n", "      <td id=\"T_92158_row28_col1\" class=\"data row28 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_92158_row29_col0\" class=\"data row29 col0\" >Recommended Seasonal D</td>\n", "      <td id=\"T_92158_row29_col1\" class=\"data row29 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_92158_row30_col0\" class=\"data row30 col0\" >Preprocess</td>\n", "      <td id=\"T_92158_row30_col1\" class=\"data row30 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_92158_row31_col0\" class=\"data row31 col0\" >Numerical Imputation (Target)</td>\n", "      <td id=\"T_92158_row31_col1\" class=\"data row31 col1\" >ffill</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_92158_row32_col0\" class=\"data row32 col0\" >Transformation (Target)</td>\n", "      <td id=\"T_92158_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_92158_row33_col0\" class=\"data row33 col0\" >Scaling (Target)</td>\n", "      <td id=\"T_92158_row33_col1\" class=\"data row33 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_92158_row34_col0\" class=\"data row34 col0\" >Feature Engineering (Target) - Reduced Regression</td>\n", "      <td id=\"T_92158_row34_col1\" class=\"data row34 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_92158_row35_col0\" class=\"data row35 col0\" >Numerical Imputation (Exogenous)</td>\n", "      <td id=\"T_92158_row35_col1\" class=\"data row35 col1\" >ffill</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_92158_row36_col0\" class=\"data row36 col0\" >Transformation (Exogenous)</td>\n", "      <td id=\"T_92158_row36_col1\" class=\"data row36 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_92158_row37_col0\" class=\"data row37 col0\" ><PERSON>aling (Exogenous)</td>\n", "      <td id=\"T_92158_row37_col1\" class=\"data row37 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_92158_row38_col0\" class=\"data row38 col0\" >CPU Jobs</td>\n", "      <td id=\"T_92158_row38_col1\" class=\"data row38 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_92158_row39_col0\" class=\"data row39 col0\" >Use GPU</td>\n", "      <td id=\"T_92158_row39_col1\" class=\"data row39 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "      <td id=\"T_92158_row40_col0\" class=\"data row40 col0\" >Log Experiment</td>\n", "      <td id=\"T_92158_row40_col1\" class=\"data row40 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "      <td id=\"T_92158_row41_col0\" class=\"data row41 col0\" >Experiment Name</td>\n", "      <td id=\"T_92158_row41_col1\" class=\"data row41 col1\" >ts-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_92158_level0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "      <td id=\"T_92158_row42_col0\" class=\"data row42 col0\" >USI</td>\n", "      <td id=\"T_92158_row42_col1\" class=\"data row42 col1\" >d7b9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x2784940f0d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<pycaret.time_series.forecasting.oop.TSForecastingExperiment at 0x27849347c40>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["exp_auto = TSForecastingExperiment()\n", "\n", "# enforce_exogenous=False --> Use multivariate forecasting when model supports it, else use univariate forecasting\n", "exp_auto.setup(\n", "    data=data, target=target, fh=FH, enforce_exogenous=False,\n", "    numeric_imputation_target=\"ffill\", numeric_imputation_exogenous=\"ffill\",\n", "    fig_kwargs=global_fig_settings, session_id=42\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# # Check available models ----\n", "# exp_auto_noexo.models()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_a17f9 th {\n", "  text-align: left;\n", "}\n", "#T_a17f9_row0_col0, #T_a17f9_row0_col7, #T_a17f9_row1_col0, #T_a17f9_row1_col1, #T_a17f9_row1_col2, #T_a17f9_row1_col3, #T_a17f9_row1_col4, #T_a17f9_row1_col5, #T_a17f9_row1_col6, #T_a17f9_row2_col0, #T_a17f9_row2_col1, #T_a17f9_row2_col2, #T_a17f9_row2_col3, #T_a17f9_row2_col4, #T_a17f9_row2_col5, #T_a17f9_row2_col6, #T_a17f9_row2_col7, #T_a17f9_row3_col0, #T_a17f9_row3_col1, #T_a17f9_row3_col2, #T_a17f9_row3_col3, #T_a17f9_row3_col4, #T_a17f9_row3_col5, #T_a17f9_row3_col6, #T_a17f9_row3_col7, #T_a17f9_row4_col0, #T_a17f9_row4_col1, #T_a17f9_row4_col2, #T_a17f9_row4_col3, #T_a17f9_row4_col4, #T_a17f9_row4_col5, #T_a17f9_row4_col6, #T_a17f9_row4_col7, #T_a17f9_row5_col0, #T_a17f9_row5_col1, #T_a17f9_row5_col2, #T_a17f9_row5_col3, #T_a17f9_row5_col4, #T_a17f9_row5_col5, #T_a17f9_row5_col6, #T_a17f9_row5_col7, #T_a17f9_row6_col0, #T_a17f9_row6_col1, #T_a17f9_row6_col2, #T_a17f9_row6_col3, #T_a17f9_row6_col4, #T_a17f9_row6_col5, #T_a17f9_row6_col6, #T_a17f9_row6_col7, #T_a17f9_row7_col0, #T_a17f9_row7_col1, #T_a17f9_row7_col2, #T_a17f9_row7_col3, #T_a17f9_row7_col4, #T_a17f9_row7_col5, #T_a17f9_row7_col6, #T_a17f9_row7_col7, #T_a17f9_row8_col0, #T_a17f9_row8_col1, #T_a17f9_row8_col2, #T_a17f9_row8_col3, #T_a17f9_row8_col4, #T_a17f9_row8_col5, #T_a17f9_row8_col6, #T_a17f9_row8_col7, #T_a17f9_row9_col0, #T_a17f9_row9_col1, #T_a17f9_row9_col2, #T_a17f9_row9_col3, #T_a17f9_row9_col4, #T_a17f9_row9_col5, #T_a17f9_row9_col6, #T_a17f9_row9_col7, #T_a17f9_row10_col0, #T_a17f9_row10_col1, #T_a17f9_row10_col2, #T_a17f9_row10_col3, #T_a17f9_row10_col4, #T_a17f9_row10_col5, #T_a17f9_row10_col6, #T_a17f9_row10_col7, #T_a17f9_row11_col0, #T_a17f9_row11_col1, #T_a17f9_row11_col2, #T_a17f9_row11_col3, #T_a17f9_row11_col4, #T_a17f9_row11_col5, #T_a17f9_row11_col6, #T_a17f9_row11_col7, #T_a17f9_row12_col0, #T_a17f9_row12_col1, #T_a17f9_row12_col2, #T_a17f9_row12_col3, #T_a17f9_row12_col4, #T_a17f9_row12_col5, #T_a17f9_row12_col6, #T_a17f9_row12_col7, #T_a17f9_row13_col0, #T_a17f9_row13_col1, #T_a17f9_row13_col2, #T_a17f9_row13_col3, #T_a17f9_row13_col4, #T_a17f9_row13_col5, #T_a17f9_row13_col6, #T_a17f9_row13_col7, #T_a17f9_row14_col0, #T_a17f9_row14_col1, #T_a17f9_row14_col2, #T_a17f9_row14_col3, #T_a17f9_row14_col4, #T_a17f9_row14_col5, #T_a17f9_row14_col6, #T_a17f9_row14_col7, #T_a17f9_row15_col0, #T_a17f9_row15_col1, #T_a17f9_row15_col2, #T_a17f9_row15_col3, #T_a17f9_row15_col4, #T_a17f9_row15_col5, #T_a17f9_row15_col6, #T_a17f9_row15_col7, #T_a17f9_row16_col0, #T_a17f9_row16_col1, #T_a17f9_row16_col2, #T_a17f9_row16_col3, #T_a17f9_row16_col4, #T_a17f9_row16_col5, #T_a17f9_row16_col6, #T_a17f9_row16_col7, #T_a17f9_row17_col0, #T_a17f9_row17_col1, #T_a17f9_row17_col2, #T_a17f9_row17_col3, #T_a17f9_row17_col4, #T_a17f9_row17_col5, #T_a17f9_row17_col6, #T_a17f9_row17_col7, #T_a17f9_row18_col0, #T_a17f9_row18_col1, #T_a17f9_row18_col2, #T_a17f9_row18_col3, #T_a17f9_row18_col4, #T_a17f9_row18_col5, #T_a17f9_row18_col6, #T_a17f9_row18_col7, #T_a17f9_row19_col0, #T_a17f9_row19_col1, #T_a17f9_row19_col2, #T_a17f9_row19_col3, #T_a17f9_row19_col4, #T_a17f9_row19_col5, #T_a17f9_row19_col6, #T_a17f9_row19_col7, #T_a17f9_row20_col0, #T_a17f9_row20_col1, #T_a17f9_row20_col2, #T_a17f9_row20_col3, #T_a17f9_row20_col4, #T_a17f9_row20_col5, #T_a17f9_row20_col6, #T_a17f9_row20_col7, #T_a17f9_row21_col0, #T_a17f9_row21_col1, #T_a17f9_row21_col2, #T_a17f9_row21_col3, #T_a17f9_row21_col4, #T_a17f9_row21_col5, #T_a17f9_row21_col6, #T_a17f9_row21_col7, #T_a17f9_row22_col0, #T_a17f9_row22_col1, #T_a17f9_row22_col2, #T_a17f9_row22_col3, #T_a17f9_row22_col4, #T_a17f9_row22_col5, #T_a17f9_row22_col6, #T_a17f9_row22_col7, #T_a17f9_row23_col0, #T_a17f9_row23_col1, #T_a17f9_row23_col2, #T_a17f9_row23_col3, #T_a17f9_row23_col4, #T_a17f9_row23_col5, #T_a17f9_row23_col6, #T_a17f9_row23_col7, #T_a17f9_row24_col0, #T_a17f9_row24_col1, #T_a17f9_row24_col2, #T_a17f9_row24_col3, #T_a17f9_row24_col4, #T_a17f9_row24_col5, #T_a17f9_row24_col6, #T_a17f9_row24_col7 {\n", "  text-align: left;\n", "}\n", "#T_a17f9_row0_col1, #T_a17f9_row0_col2, #T_a17f9_row0_col3, #T_a17f9_row0_col4, #T_a17f9_row0_col5, #T_a17f9_row0_col6, #T_a17f9_row1_col7 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_a17f9_row0_col8, #T_a17f9_row1_col8, #T_a17f9_row2_col8, #T_a17f9_row3_col8, #T_a17f9_row4_col8, #T_a17f9_row5_col8, #T_a17f9_row6_col8, #T_a17f9_row7_col8, #T_a17f9_row8_col8, #T_a17f9_row9_col8, #T_a17f9_row10_col8, #T_a17f9_row11_col8, #T_a17f9_row12_col8, #T_a17f9_row13_col8, #T_a17f9_row14_col8, #T_a17f9_row16_col8, #T_a17f9_row17_col8, #T_a17f9_row18_col8, #T_a17f9_row19_col8, #T_a17f9_row20_col8, #T_a17f9_row21_col8, #T_a17f9_row23_col8, #T_a17f9_row24_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_a17f9_row15_col8, #T_a17f9_row22_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_a17f9\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_a17f9_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_a17f9_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_a17f9_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_a17f9_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_a17f9_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_a17f9_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_a17f9_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_a17f9_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "      <th id=\"T_a17f9_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row0\" class=\"row_heading level0 row0\" >arima</th>\n", "      <td id=\"T_a17f9_row0_col0\" class=\"data row0 col0\" >ARIMA</td>\n", "      <td id=\"T_a17f9_row0_col1\" class=\"data row0 col1\" >0.2509</td>\n", "      <td id=\"T_a17f9_row0_col2\" class=\"data row0 col2\" >0.2302</td>\n", "      <td id=\"T_a17f9_row0_col3\" class=\"data row0 col3\" >0.1810</td>\n", "      <td id=\"T_a17f9_row0_col4\" class=\"data row0 col4\" >0.2449</td>\n", "      <td id=\"T_a17f9_row0_col5\" class=\"data row0 col5\" >0.1443</td>\n", "      <td id=\"T_a17f9_row0_col6\" class=\"data row0 col6\" >0.1523</td>\n", "      <td id=\"T_a17f9_row0_col7\" class=\"data row0 col7\" >0.8499</td>\n", "      <td id=\"T_a17f9_row0_col8\" class=\"data row0 col8\" >0.9367</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row1\" class=\"row_heading level0 row1\" >prophet</th>\n", "      <td id=\"T_a17f9_row1_col0\" class=\"data row1 col0\" >Prophet</td>\n", "      <td id=\"T_a17f9_row1_col1\" class=\"data row1 col1\" >0.3079</td>\n", "      <td id=\"T_a17f9_row1_col2\" class=\"data row1 col2\" >0.2647</td>\n", "      <td id=\"T_a17f9_row1_col3\" class=\"data row1 col3\" >0.2226</td>\n", "      <td id=\"T_a17f9_row1_col4\" class=\"data row1 col4\" >0.2823</td>\n", "      <td id=\"T_a17f9_row1_col5\" class=\"data row1 col5\" >0.1943</td>\n", "      <td id=\"T_a17f9_row1_col6\" class=\"data row1 col6\" >0.2027</td>\n", "      <td id=\"T_a17f9_row1_col7\" class=\"data row1 col7\" >0.8501</td>\n", "      <td id=\"T_a17f9_row1_col8\" class=\"data row1 col8\" >0.5800</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row2\" class=\"row_heading level0 row2\" >br_cds_dt</th>\n", "      <td id=\"T_a17f9_row2_col0\" class=\"data row2 col0\" >Bayesian Ridge w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row2_col1\" class=\"data row2 col1\" >0.8609</td>\n", "      <td id=\"T_a17f9_row2_col2\" class=\"data row2 col2\" >0.8199</td>\n", "      <td id=\"T_a17f9_row2_col3\" class=\"data row2 col3\" >0.6211</td>\n", "      <td id=\"T_a17f9_row2_col4\" class=\"data row2 col4\" >0.8730</td>\n", "      <td id=\"T_a17f9_row2_col5\" class=\"data row2 col5\" >0.4675</td>\n", "      <td id=\"T_a17f9_row2_col6\" class=\"data row2 col6\" >0.4450</td>\n", "      <td id=\"T_a17f9_row2_col7\" class=\"data row2 col7\" >-0.7053</td>\n", "      <td id=\"T_a17f9_row2_col8\" class=\"data row2 col8\" >1.1967</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row3\" class=\"row_heading level0 row3\" >ridge_cds_dt</th>\n", "      <td id=\"T_a17f9_row3_col0\" class=\"data row3 col0\" >Ridge w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row3_col1\" class=\"data row3 col1\" >0.8647</td>\n", "      <td id=\"T_a17f9_row3_col2\" class=\"data row3 col2\" >0.8218</td>\n", "      <td id=\"T_a17f9_row3_col3\" class=\"data row3 col3\" >0.6238</td>\n", "      <td id=\"T_a17f9_row3_col4\" class=\"data row3 col4\" >0.8750</td>\n", "      <td id=\"T_a17f9_row3_col5\" class=\"data row3 col5\" >0.4714</td>\n", "      <td id=\"T_a17f9_row3_col6\" class=\"data row3 col6\" >0.4491</td>\n", "      <td id=\"T_a17f9_row3_col7\" class=\"data row3 col7\" >-0.7106</td>\n", "      <td id=\"T_a17f9_row3_col8\" class=\"data row3 col8\" >1.2200</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row4\" class=\"row_heading level0 row4\" >lr_cds_dt</th>\n", "      <td id=\"T_a17f9_row4_col0\" class=\"data row4 col0\" >Linear w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row4_col1\" class=\"data row4 col1\" >0.8651</td>\n", "      <td id=\"T_a17f9_row4_col2\" class=\"data row4 col2\" >0.8220</td>\n", "      <td id=\"T_a17f9_row4_col3\" class=\"data row4 col3\" >0.6240</td>\n", "      <td id=\"T_a17f9_row4_col4\" class=\"data row4 col4\" >0.8752</td>\n", "      <td id=\"T_a17f9_row4_col5\" class=\"data row4 col5\" >0.4718</td>\n", "      <td id=\"T_a17f9_row4_col6\" class=\"data row4 col6\" >0.4494</td>\n", "      <td id=\"T_a17f9_row4_col7\" class=\"data row4 col7\" >-0.7111</td>\n", "      <td id=\"T_a17f9_row4_col8\" class=\"data row4 col8\" >1.4067</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row5\" class=\"row_heading level0 row5\" >snaive</th>\n", "      <td id=\"T_a17f9_row5_col0\" class=\"data row5 col0\" >Seasonal Naive Forecaster</td>\n", "      <td id=\"T_a17f9_row5_col1\" class=\"data row5 col1\" >0.9672</td>\n", "      <td id=\"T_a17f9_row5_col2\" class=\"data row5 col2\" >0.9659</td>\n", "      <td id=\"T_a17f9_row5_col3\" class=\"data row5 col3\" >0.6972</td>\n", "      <td id=\"T_a17f9_row5_col4\" class=\"data row5 col4\" >1.0275</td>\n", "      <td id=\"T_a17f9_row5_col5\" class=\"data row5 col5\" >0.4645</td>\n", "      <td id=\"T_a17f9_row5_col6\" class=\"data row5 col6\" >0.3643</td>\n", "      <td id=\"T_a17f9_row5_col7\" class=\"data row5 col7\" >-1.8616</td>\n", "      <td id=\"T_a17f9_row5_col8\" class=\"data row5 col8\" >1.3867</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row6\" class=\"row_heading level0 row6\" >theta</th>\n", "      <td id=\"T_a17f9_row6_col0\" class=\"data row6 col0\" >Theta Forecaster</td>\n", "      <td id=\"T_a17f9_row6_col1\" class=\"data row6 col1\" >0.9871</td>\n", "      <td id=\"T_a17f9_row6_col2\" class=\"data row6 col2\" >0.8962</td>\n", "      <td id=\"T_a17f9_row6_col3\" class=\"data row6 col3\" >0.7146</td>\n", "      <td id=\"T_a17f9_row6_col4\" class=\"data row6 col4\" >0.9574</td>\n", "      <td id=\"T_a17f9_row6_col5\" class=\"data row6 col5\" >0.4639</td>\n", "      <td id=\"T_a17f9_row6_col6\" class=\"data row6 col6\" >0.4412</td>\n", "      <td id=\"T_a17f9_row6_col7\" class=\"data row6 col7\" >-0.2349</td>\n", "      <td id=\"T_a17f9_row6_col8\" class=\"data row6 col8\" >0.0600</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row7\" class=\"row_heading level0 row7\" >en_cds_dt</th>\n", "      <td id=\"T_a17f9_row7_col0\" class=\"data row7 col0\" >Elastic Net w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row7_col1\" class=\"data row7 col1\" >0.9948</td>\n", "      <td id=\"T_a17f9_row7_col2\" class=\"data row7 col2\" >0.9519</td>\n", "      <td id=\"T_a17f9_row7_col3\" class=\"data row7 col3\" >0.7181</td>\n", "      <td id=\"T_a17f9_row7_col4\" class=\"data row7 col4\" >1.0130</td>\n", "      <td id=\"T_a17f9_row7_col5\" class=\"data row7 col5\" >0.5099</td>\n", "      <td id=\"T_a17f9_row7_col6\" class=\"data row7 col6\" >0.3702</td>\n", "      <td id=\"T_a17f9_row7_col7\" class=\"data row7 col7\" >-1.7375</td>\n", "      <td id=\"T_a17f9_row7_col8\" class=\"data row7 col8\" >1.3767</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row8\" class=\"row_heading level0 row8\" >lasso_cds_dt</th>\n", "      <td id=\"T_a17f9_row8_col0\" class=\"data row8 col0\" >Lasso w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row8_col1\" class=\"data row8 col1\" >1.0081</td>\n", "      <td id=\"T_a17f9_row8_col2\" class=\"data row8 col2\" >0.9598</td>\n", "      <td id=\"T_a17f9_row8_col3\" class=\"data row8 col3\" >0.7275</td>\n", "      <td id=\"T_a17f9_row8_col4\" class=\"data row8 col4\" >1.0213</td>\n", "      <td id=\"T_a17f9_row8_col5\" class=\"data row8 col5\" >0.5178</td>\n", "      <td id=\"T_a17f9_row8_col6\" class=\"data row8 col6\" >0.3741</td>\n", "      <td id=\"T_a17f9_row8_col7\" class=\"data row8 col7\" >-1.8353</td>\n", "      <td id=\"T_a17f9_row8_col8\" class=\"data row8 col8\" >1.1533</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row9\" class=\"row_heading level0 row9\" >llar_cds_dt</th>\n", "      <td id=\"T_a17f9_row9_col0\" class=\"data row9 col0\" >Lasso Least Angular Regressor w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row9_col1\" class=\"data row9 col1\" >1.0081</td>\n", "      <td id=\"T_a17f9_row9_col2\" class=\"data row9 col2\" >0.9598</td>\n", "      <td id=\"T_a17f9_row9_col3\" class=\"data row9 col3\" >0.7275</td>\n", "      <td id=\"T_a17f9_row9_col4\" class=\"data row9 col4\" >1.0213</td>\n", "      <td id=\"T_a17f9_row9_col5\" class=\"data row9 col5\" >0.5178</td>\n", "      <td id=\"T_a17f9_row9_col6\" class=\"data row9 col6\" >0.3741</td>\n", "      <td id=\"T_a17f9_row9_col7\" class=\"data row9 col7\" >-1.8354</td>\n", "      <td id=\"T_a17f9_row9_col8\" class=\"data row9 col8\" >1.1967</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row10\" class=\"row_heading level0 row10\" >omp_cds_dt</th>\n", "      <td id=\"T_a17f9_row10_col0\" class=\"data row10 col0\" >Orthogonal Matching Pursuit w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row10_col1\" class=\"data row10 col1\" >1.0119</td>\n", "      <td id=\"T_a17f9_row10_col2\" class=\"data row10 col2\" >0.9608</td>\n", "      <td id=\"T_a17f9_row10_col3\" class=\"data row10 col3\" >0.7303</td>\n", "      <td id=\"T_a17f9_row10_col4\" class=\"data row10 col4\" >1.0223</td>\n", "      <td id=\"T_a17f9_row10_col5\" class=\"data row10 col5\" >0.5209</td>\n", "      <td id=\"T_a17f9_row10_col6\" class=\"data row10 col6\" >0.3738</td>\n", "      <td id=\"T_a17f9_row10_col7\" class=\"data row10 col7\" >-1.8667</td>\n", "      <td id=\"T_a17f9_row10_col8\" class=\"data row10 col8\" >1.1067</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row11\" class=\"row_heading level0 row11\" >huber_cds_dt</th>\n", "      <td id=\"T_a17f9_row11_col0\" class=\"data row11 col0\" >Huber w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row11_col1\" class=\"data row11 col1\" >1.0141</td>\n", "      <td id=\"T_a17f9_row11_col2\" class=\"data row11 col2\" >0.9402</td>\n", "      <td id=\"T_a17f9_row11_col3\" class=\"data row11 col3\" >0.7314</td>\n", "      <td id=\"T_a17f9_row11_col4\" class=\"data row11 col4\" >1.0009</td>\n", "      <td id=\"T_a17f9_row11_col5\" class=\"data row11 col5\" >0.5600</td>\n", "      <td id=\"T_a17f9_row11_col6\" class=\"data row11 col6\" >0.4724</td>\n", "      <td id=\"T_a17f9_row11_col7\" class=\"data row11 col7\" >-1.3001</td>\n", "      <td id=\"T_a17f9_row11_col8\" class=\"data row11 col8\" >1.1867</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row12\" class=\"row_heading level0 row12\" >knn_cds_dt</th>\n", "      <td id=\"T_a17f9_row12_col0\" class=\"data row12 col0\" >K Neighbors w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row12_col1\" class=\"data row12 col1\" >1.0397</td>\n", "      <td id=\"T_a17f9_row12_col2\" class=\"data row12 col2\" >0.9920</td>\n", "      <td id=\"T_a17f9_row12_col3\" class=\"data row12 col3\" >0.7505</td>\n", "      <td id=\"T_a17f9_row12_col4\" class=\"data row12 col4\" >1.0557</td>\n", "      <td id=\"T_a17f9_row12_col5\" class=\"data row12 col5\" >0.5770</td>\n", "      <td id=\"T_a17f9_row12_col6\" class=\"data row12 col6\" >0.4194</td>\n", "      <td id=\"T_a17f9_row12_col7\" class=\"data row12 col7\" >-1.9501</td>\n", "      <td id=\"T_a17f9_row12_col8\" class=\"data row12 col8\" >1.1100</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row13\" class=\"row_heading level0 row13\" >gbr_cds_dt</th>\n", "      <td id=\"T_a17f9_row13_col0\" class=\"data row13 col0\" >Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row13_col1\" class=\"data row13 col1\" >1.0614</td>\n", "      <td id=\"T_a17f9_row13_col2\" class=\"data row13 col2\" >0.8726</td>\n", "      <td id=\"T_a17f9_row13_col3\" class=\"data row13 col3\" >0.7667</td>\n", "      <td id=\"T_a17f9_row13_col4\" class=\"data row13 col4\" >0.9294</td>\n", "      <td id=\"T_a17f9_row13_col5\" class=\"data row13 col5\" >0.6470</td>\n", "      <td id=\"T_a17f9_row13_col6\" class=\"data row13 col6\" >0.6835</td>\n", "      <td id=\"T_a17f9_row13_col7\" class=\"data row13 col7\" >-0.7997</td>\n", "      <td id=\"T_a17f9_row13_col8\" class=\"data row13 col8\" >1.5100</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row14\" class=\"row_heading level0 row14\" >naive</th>\n", "      <td id=\"T_a17f9_row14_col0\" class=\"data row14 col0\" >Naive Forecaster</td>\n", "      <td id=\"T_a17f9_row14_col1\" class=\"data row14 col1\" >1.0848</td>\n", "      <td id=\"T_a17f9_row14_col2\" class=\"data row14 col2\" >0.9259</td>\n", "      <td id=\"T_a17f9_row14_col3\" class=\"data row14 col3\" >0.7861</td>\n", "      <td id=\"T_a17f9_row14_col4\" class=\"data row14 col4\" >0.9895</td>\n", "      <td id=\"T_a17f9_row14_col5\" class=\"data row14 col5\" >0.6125</td>\n", "      <td id=\"T_a17f9_row14_col6\" class=\"data row14 col6\" >0.5160</td>\n", "      <td id=\"T_a17f9_row14_col7\" class=\"data row14 col7\" >-0.3784</td>\n", "      <td id=\"T_a17f9_row14_col8\" class=\"data row14 col8\" >2.0967</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row15\" class=\"row_heading level0 row15\" >croston</th>\n", "      <td id=\"T_a17f9_row15_col0\" class=\"data row15 col0\" ><PERSON><PERSON><PERSON></td>\n", "      <td id=\"T_a17f9_row15_col1\" class=\"data row15 col1\" >1.1033</td>\n", "      <td id=\"T_a17f9_row15_col2\" class=\"data row15 col2\" >0.9167</td>\n", "      <td id=\"T_a17f9_row15_col3\" class=\"data row15 col3\" >0.7966</td>\n", "      <td id=\"T_a17f9_row15_col4\" class=\"data row15 col4\" >0.9775</td>\n", "      <td id=\"T_a17f9_row15_col5\" class=\"data row15 col5\" >0.7744</td>\n", "      <td id=\"T_a17f9_row15_col6\" class=\"data row15 col6\" >0.5053</td>\n", "      <td id=\"T_a17f9_row15_col7\" class=\"data row15 col7\" >-0.6474</td>\n", "      <td id=\"T_a17f9_row15_col8\" class=\"data row15 col8\" >0.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row16\" class=\"row_heading level0 row16\" >et_cds_dt</th>\n", "      <td id=\"T_a17f9_row16_col0\" class=\"data row16 col0\" >Extra Trees w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row16_col1\" class=\"data row16 col1\" >1.1264</td>\n", "      <td id=\"T_a17f9_row16_col2\" class=\"data row16 col2\" >0.9404</td>\n", "      <td id=\"T_a17f9_row16_col3\" class=\"data row16 col3\" >0.8140</td>\n", "      <td id=\"T_a17f9_row16_col4\" class=\"data row16 col4\" >1.0019</td>\n", "      <td id=\"T_a17f9_row16_col5\" class=\"data row16 col5\" >0.6625</td>\n", "      <td id=\"T_a17f9_row16_col6\" class=\"data row16 col6\" >0.7238</td>\n", "      <td id=\"T_a17f9_row16_col7\" class=\"data row16 col7\" >-1.0762</td>\n", "      <td id=\"T_a17f9_row16_col8\" class=\"data row16 col8\" >1.6300</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row17\" class=\"row_heading level0 row17\" >rf_cds_dt</th>\n", "      <td id=\"T_a17f9_row17_col0\" class=\"data row17 col0\" >Random Forest w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row17_col1\" class=\"data row17 col1\" >1.1411</td>\n", "      <td id=\"T_a17f9_row17_col2\" class=\"data row17 col2\" >0.9626</td>\n", "      <td id=\"T_a17f9_row17_col3\" class=\"data row17 col3\" >0.8240</td>\n", "      <td id=\"T_a17f9_row17_col4\" class=\"data row17 col4\" >1.0255</td>\n", "      <td id=\"T_a17f9_row17_col5\" class=\"data row17 col5\" >0.6866</td>\n", "      <td id=\"T_a17f9_row17_col6\" class=\"data row17 col6\" >0.6809</td>\n", "      <td id=\"T_a17f9_row17_col7\" class=\"data row17 col7\" >-1.1643</td>\n", "      <td id=\"T_a17f9_row17_col8\" class=\"data row17 col8\" >1.9033</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row18\" class=\"row_heading level0 row18\" >ada_cds_dt</th>\n", "      <td id=\"T_a17f9_row18_col0\" class=\"data row18 col0\" >AdaBoost w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row18_col1\" class=\"data row18 col1\" >1.1815</td>\n", "      <td id=\"T_a17f9_row18_col2\" class=\"data row18 col2\" >0.9690</td>\n", "      <td id=\"T_a17f9_row18_col3\" class=\"data row18 col3\" >0.8545</td>\n", "      <td id=\"T_a17f9_row18_col4\" class=\"data row18 col4\" >1.0328</td>\n", "      <td id=\"T_a17f9_row18_col5\" class=\"data row18 col5\" >0.7225</td>\n", "      <td id=\"T_a17f9_row18_col6\" class=\"data row18 col6\" >0.7716</td>\n", "      <td id=\"T_a17f9_row18_col7\" class=\"data row18 col7\" >-1.0861</td>\n", "      <td id=\"T_a17f9_row18_col8\" class=\"data row18 col8\" >1.3300</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row19\" class=\"row_heading level0 row19\" >lightgbm_cds_dt</th>\n", "      <td id=\"T_a17f9_row19_col0\" class=\"data row19 col0\" >Light Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row19_col1\" class=\"data row19 col1\" >1.1975</td>\n", "      <td id=\"T_a17f9_row19_col2\" class=\"data row19 col2\" >0.9889</td>\n", "      <td id=\"T_a17f9_row19_col3\" class=\"data row19 col3\" >0.8647</td>\n", "      <td id=\"T_a17f9_row19_col4\" class=\"data row19 col4\" >1.0531</td>\n", "      <td id=\"T_a17f9_row19_col5\" class=\"data row19 col5\" >0.7274</td>\n", "      <td id=\"T_a17f9_row19_col6\" class=\"data row19 col6\" >0.7735</td>\n", "      <td id=\"T_a17f9_row19_col7\" class=\"data row19 col7\" >-1.4445</td>\n", "      <td id=\"T_a17f9_row19_col8\" class=\"data row19 col8\" >1.3067</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row20\" class=\"row_heading level0 row20\" >grand_means</th>\n", "      <td id=\"T_a17f9_row20_col0\" class=\"data row20 col0\" >Grand Means Forecaster</td>\n", "      <td id=\"T_a17f9_row20_col1\" class=\"data row20 col1\" >1.3017</td>\n", "      <td id=\"T_a17f9_row20_col2\" class=\"data row20 col2\" >1.0074</td>\n", "      <td id=\"T_a17f9_row20_col3\" class=\"data row20 col3\" >0.9392</td>\n", "      <td id=\"T_a17f9_row20_col4\" class=\"data row20 col4\" >1.0725</td>\n", "      <td id=\"T_a17f9_row20_col5\" class=\"data row20 col5\" >0.9892</td>\n", "      <td id=\"T_a17f9_row20_col6\" class=\"data row20 col6\" >0.5728</td>\n", "      <td id=\"T_a17f9_row20_col7\" class=\"data row20 col7\" >-1.6494</td>\n", "      <td id=\"T_a17f9_row20_col8\" class=\"data row20 col8\" >1.4100</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row21\" class=\"row_heading level0 row21\" >dt_cds_dt</th>\n", "      <td id=\"T_a17f9_row21_col0\" class=\"data row21 col0\" >Decision Tree w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_a17f9_row21_col1\" class=\"data row21 col1\" >1.3115</td>\n", "      <td id=\"T_a17f9_row21_col2\" class=\"data row21 col2\" >1.1741</td>\n", "      <td id=\"T_a17f9_row21_col3\" class=\"data row21 col3\" >0.9486</td>\n", "      <td id=\"T_a17f9_row21_col4\" class=\"data row21 col4\" >1.2521</td>\n", "      <td id=\"T_a17f9_row21_col5\" class=\"data row21 col5\" >0.7326</td>\n", "      <td id=\"T_a17f9_row21_col6\" class=\"data row21 col6\" >0.7303</td>\n", "      <td id=\"T_a17f9_row21_col7\" class=\"data row21 col7\" >-2.1020</td>\n", "      <td id=\"T_a17f9_row21_col8\" class=\"data row21 col8\" >1.1233</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row22\" class=\"row_heading level0 row22\" >polytrend</th>\n", "      <td id=\"T_a17f9_row22_col0\" class=\"data row22 col0\" >Polynomial Trend Forecaster</td>\n", "      <td id=\"T_a17f9_row22_col1\" class=\"data row22 col1\" >1.4885</td>\n", "      <td id=\"T_a17f9_row22_col2\" class=\"data row22 col2\" >1.1214</td>\n", "      <td id=\"T_a17f9_row22_col3\" class=\"data row22 col3\" >1.0745</td>\n", "      <td id=\"T_a17f9_row22_col4\" class=\"data row22 col4\" >1.1939</td>\n", "      <td id=\"T_a17f9_row22_col5\" class=\"data row22 col5\" >1.1644</td>\n", "      <td id=\"T_a17f9_row22_col6\" class=\"data row22 col6\" >0.6238</td>\n", "      <td id=\"T_a17f9_row22_col7\" class=\"data row22 col7\" >-2.3456</td>\n", "      <td id=\"T_a17f9_row22_col8\" class=\"data row22 col8\" >0.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row23\" class=\"row_heading level0 row23\" >exp_smooth</th>\n", "      <td id=\"T_a17f9_row23_col0\" class=\"data row23 col0\" >Exponential Smoothing</td>\n", "      <td id=\"T_a17f9_row23_col1\" class=\"data row23 col1\" >1.7959</td>\n", "      <td id=\"T_a17f9_row23_col2\" class=\"data row23 col2\" >1.6246</td>\n", "      <td id=\"T_a17f9_row23_col3\" class=\"data row23 col3\" >1.2987</td>\n", "      <td id=\"T_a17f9_row23_col4\" class=\"data row23 col4\" >1.7354</td>\n", "      <td id=\"T_a17f9_row23_col5\" class=\"data row23 col5\" >0.8998</td>\n", "      <td id=\"T_a17f9_row23_col6\" class=\"data row23 col6\" >0.5140</td>\n", "      <td id=\"T_a17f9_row23_col7\" class=\"data row23 col7\" >-3.1090</td>\n", "      <td id=\"T_a17f9_row23_col8\" class=\"data row23 col8\" >0.1700</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a17f9_level0_row24\" class=\"row_heading level0 row24\" >ets</th>\n", "      <td id=\"T_a17f9_row24_col0\" class=\"data row24 col0\" >ETS</td>\n", "      <td id=\"T_a17f9_row24_col1\" class=\"data row24 col1\" >2.3059</td>\n", "      <td id=\"T_a17f9_row24_col2\" class=\"data row24 col2\" >2.1201</td>\n", "      <td id=\"T_a17f9_row24_col3\" class=\"data row24 col3\" >1.6615</td>\n", "      <td id=\"T_a17f9_row24_col4\" class=\"data row24 col4\" >2.2557</td>\n", "      <td id=\"T_a17f9_row24_col5\" class=\"data row24 col5\" >1.2981</td>\n", "      <td id=\"T_a17f9_row24_col6\" class=\"data row24 col6\" >0.6474</td>\n", "      <td id=\"T_a17f9_row24_col7\" class=\"data row24 col7\" >-10.2278</td>\n", "      <td id=\"T_a17f9_row24_col8\" class=\"data row24 col8\" >1.7233</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27849ae9460>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Include slower models like <PERSON> (turbo=False), but exclude some specific models ----\n", "best = exp_auto.compare_models(sort=metric, turbo=False, exclude=exclude)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "source": ["exp_auto.plot_model(best)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["final_auto_model = exp_auto.finalize_model(best)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def safe_predict(exp, model):\n", "    \"\"\"Prediction wrapper for demo purposes.\"\"\"\n", "    try: \n", "        future_preds = exp.predict_model(model)\n", "    except ValueError as exception:\n", "        print(exception)\n", "        exo_vars = exp.exogenous_variables\n", "        print(f\"{len(exo_vars)} exogenous variables (X) needed in order to make future predictions:\\n{exo_vars}\")\n", "        \n", "        \n", "        exog_exps = []\n", "        exog_models = []\n", "        for exog_var in exog_vars:\n", "            exog_exp = TSForecastingExperiment()\n", "            exog_exp.setup(\n", "                data=data[exog_var], fh=FH,\n", "                numeric_imputation_target=\"ffill\", numeric_imputation_exogenous=\"ffill\",\n", "                fig_kwargs=global_fig_settings, session_id=42\n", "            )\n", "\n", "            # Users can customize how to model future exogenous variables i.e. add\n", "            # more steps and models to potentially get better models at the expense\n", "            # of higher modeling time.\n", "            best = exog_exp.compare_models(\n", "                sort=metric, include=[\"arima\", \"ets\", \"exp_smooth\", \"theta\", \"lightgbm_cds_dt\",]        \n", "            )\n", "            final_exog_model = exog_exp.finalize_model(best)\n", "\n", "            exog_exps.append(exog_exp)\n", "            exog_models.append(final_exog_model)\n", "\n", "        # Step 2: Get future predictions for exog variables ----\n", "        future_exog = [\n", "            exog_exp.predict_model(exog_model)\n", "            for exog_exp, exog_model in zip(exog_exps, exog_models)\n", "        ]\n", "        future_exog = pd.concat(future_exog, axis=1)\n", "        future_exog.columns = exog_vars\n", "        \n", "        future_preds = exp.predict_model(model, X=future_exog)\n", "    \n", "    return future_preds      "]}, {"cell_type": "code", "execution_count": 13, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model was trained with exogenous variables but you have not passed any for predictions. Please pass exogenous variables to make predictions.\n", "3 exogenous variables (X) needed in order to make future predictions:\n", "['NOx(GT)', 'PT08.S3(NOx)', 'RH']\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_e9a31_row25_col1, #T_e9a31_row30_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_e9a31\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_e9a31_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_e9a31_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_e9a31_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_e9a31_row0_col1\" class=\"data row0 col1\" >42</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_e9a31_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_e9a31_row1_col1\" class=\"data row1 col1\" >NOx(GT)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_e9a31_row2_col0\" class=\"data row2 col0\" >Approach</td>\n", "      <td id=\"T_e9a31_row2_col1\" class=\"data row2 col1\" >Univariate</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_e9a31_row3_col0\" class=\"data row3 col0\" >Exogenous Variables</td>\n", "      <td id=\"T_e9a31_row3_col1\" class=\"data row3 col1\" >Not Present</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_e9a31_row4_col0\" class=\"data row4 col0\" >Original data shape</td>\n", "      <td id=\"T_e9a31_row4_col1\" class=\"data row4 col1\" >(720, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_e9a31_row5_col0\" class=\"data row5 col0\" >Transformed data shape</td>\n", "      <td id=\"T_e9a31_row5_col1\" class=\"data row5 col1\" >(720, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_e9a31_row6_col0\" class=\"data row6 col0\" >Transformed train set shape</td>\n", "      <td id=\"T_e9a31_row6_col1\" class=\"data row6 col1\" >(672, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_e9a31_row7_col0\" class=\"data row7 col0\" >Transformed test set shape</td>\n", "      <td id=\"T_e9a31_row7_col1\" class=\"data row7 col1\" >(48, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_e9a31_row8_col0\" class=\"data row8 col0\" >Rows with missing values</td>\n", "      <td id=\"T_e9a31_row8_col1\" class=\"data row8 col1\" >0.8%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_e9a31_row9_col0\" class=\"data row9 col0\" >Fold Generator</td>\n", "      <td id=\"T_e9a31_row9_col1\" class=\"data row9 col1\" >ExpandingWindowSplitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_e9a31_row10_col0\" class=\"data row10 col0\" >Fold Number</td>\n", "      <td id=\"T_e9a31_row10_col1\" class=\"data row10 col1\" >3</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_e9a31_row11_col0\" class=\"data row11 col0\" >Enforce Prediction Interval</td>\n", "      <td id=\"T_e9a31_row11_col1\" class=\"data row11 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_e9a31_row12_col0\" class=\"data row12 col0\" >Splits used for hyperparameters</td>\n", "      <td id=\"T_e9a31_row12_col1\" class=\"data row12 col1\" >all</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_e9a31_row13_col0\" class=\"data row13 col0\" >User Defined Seasonal Period(s)</td>\n", "      <td id=\"T_e9a31_row13_col1\" class=\"data row13 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_e9a31_row14_col0\" class=\"data row14 col0\" >Ignore Seasonality Test</td>\n", "      <td id=\"T_e9a31_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_e9a31_row15_col0\" class=\"data row15 col0\" >Seasonality Detection Algo</td>\n", "      <td id=\"T_e9a31_row15_col1\" class=\"data row15 col1\" >auto</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_e9a31_row16_col0\" class=\"data row16 col0\" >Max Period to Consider</td>\n", "      <td id=\"T_e9a31_row16_col1\" class=\"data row16 col1\" >60</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_e9a31_row17_col0\" class=\"data row17 col0\" >Seasonal Period(s) Tested</td>\n", "      <td id=\"T_e9a31_row17_col1\" class=\"data row17 col1\" >[24, 48, 23, 25, 47, 49, 13, 12, 36, 11, 35, 60]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_e9a31_row18_col0\" class=\"data row18 col0\" >Significant Seasonal Period(s)</td>\n", "      <td id=\"T_e9a31_row18_col1\" class=\"data row18 col1\" >[24, 48, 23, 25, 47, 49, 13, 12, 36, 11, 35, 60]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_e9a31_row19_col0\" class=\"data row19 col0\" >Significant Seasonal Period(s) without Harmonics</td>\n", "      <td id=\"T_e9a31_row19_col1\" class=\"data row19 col1\" >[48, 23, 25, 47, 49, 13, 60, 36, 11, 35]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_e9a31_row20_col0\" class=\"data row20 col0\" >Remove Harmonics</td>\n", "      <td id=\"T_e9a31_row20_col1\" class=\"data row20 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_e9a31_row21_col0\" class=\"data row21 col0\" >Harmonics Order Method</td>\n", "      <td id=\"T_e9a31_row21_col1\" class=\"data row21 col1\" >harmonic_max</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_e9a31_row22_col0\" class=\"data row22 col0\" >Num Seasonalities to Use</td>\n", "      <td id=\"T_e9a31_row22_col1\" class=\"data row22 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_e9a31_row23_col0\" class=\"data row23 col0\" >All Seasonalities to Use</td>\n", "      <td id=\"T_e9a31_row23_col1\" class=\"data row23 col1\" >[24]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_e9a31_row24_col0\" class=\"data row24 col0\" >Primary Seasonality</td>\n", "      <td id=\"T_e9a31_row24_col1\" class=\"data row24 col1\" >24</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_e9a31_row25_col0\" class=\"data row25 col0\" >Seasonality Present</td>\n", "      <td id=\"T_e9a31_row25_col1\" class=\"data row25 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_e9a31_row26_col0\" class=\"data row26 col0\" >Target Strictly Positive</td>\n", "      <td id=\"T_e9a31_row26_col1\" class=\"data row26 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_e9a31_row27_col0\" class=\"data row27 col0\" >Target White Noise</td>\n", "      <td id=\"T_e9a31_row27_col1\" class=\"data row27 col1\" >No</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_e9a31_row28_col0\" class=\"data row28 col0\" >Recommended d</td>\n", "      <td id=\"T_e9a31_row28_col1\" class=\"data row28 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_e9a31_row29_col0\" class=\"data row29 col0\" >Recommended Seasonal D</td>\n", "      <td id=\"T_e9a31_row29_col1\" class=\"data row29 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_e9a31_row30_col0\" class=\"data row30 col0\" >Preprocess</td>\n", "      <td id=\"T_e9a31_row30_col1\" class=\"data row30 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_e9a31_row31_col0\" class=\"data row31 col0\" >Numerical Imputation (Target)</td>\n", "      <td id=\"T_e9a31_row31_col1\" class=\"data row31 col1\" >ffill</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_e9a31_row32_col0\" class=\"data row32 col0\" >Transformation (Target)</td>\n", "      <td id=\"T_e9a31_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_e9a31_row33_col0\" class=\"data row33 col0\" >Scaling (Target)</td>\n", "      <td id=\"T_e9a31_row33_col1\" class=\"data row33 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_e9a31_row34_col0\" class=\"data row34 col0\" >Feature Engineering (Target) - Reduced Regression</td>\n", "      <td id=\"T_e9a31_row34_col1\" class=\"data row34 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_e9a31_row35_col0\" class=\"data row35 col0\" >CPU Jobs</td>\n", "      <td id=\"T_e9a31_row35_col1\" class=\"data row35 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_e9a31_row36_col0\" class=\"data row36 col0\" >Use GPU</td>\n", "      <td id=\"T_e9a31_row36_col1\" class=\"data row36 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_e9a31_row37_col0\" class=\"data row37 col0\" >Log Experiment</td>\n", "      <td id=\"T_e9a31_row37_col1\" class=\"data row37 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_e9a31_row38_col0\" class=\"data row38 col0\" >Experiment Name</td>\n", "      <td id=\"T_e9a31_row38_col1\" class=\"data row38 col1\" >ts-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9a31_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_e9a31_row39_col0\" class=\"data row39 col0\" >USI</td>\n", "      <td id=\"T_e9a31_row39_col1\" class=\"data row39 col1\" >95be</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x2784a0cddc0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_2ce5c th {\n", "  text-align: left;\n", "}\n", "#T_2ce5c_row0_col0, #T_2ce5c_row0_col2, #T_2ce5c_row0_col4, #T_2ce5c_row0_col7, #T_2ce5c_row1_col0, #T_2ce5c_row1_col1, #T_2ce5c_row1_col3, #T_2ce5c_row1_col5, #T_2ce5c_row1_col6, #T_2ce5c_row1_col7, #T_2ce5c_row2_col0, #T_2ce5c_row2_col1, #T_2ce5c_row2_col2, #T_2ce5c_row2_col3, #T_2ce5c_row2_col4, #T_2ce5c_row2_col5, #T_2ce5c_row2_col6, #T_2ce5c_row3_col0, #T_2ce5c_row3_col1, #T_2ce5c_row3_col2, #T_2ce5c_row3_col3, #T_2ce5c_row3_col4, #T_2ce5c_row3_col5, #T_2ce5c_row3_col6, #T_2ce5c_row3_col7, #T_2ce5c_row4_col0, #T_2ce5c_row4_col1, #T_2ce5c_row4_col2, #T_2ce5c_row4_col3, #T_2ce5c_row4_col4, #T_2ce5c_row4_col5, #T_2ce5c_row4_col6, #T_2ce5c_row4_col7 {\n", "  text-align: left;\n", "}\n", "#T_2ce5c_row0_col1, #T_2ce5c_row0_col3, #T_2ce5c_row0_col5, #T_2ce5c_row0_col6, #T_2ce5c_row1_col2, #T_2ce5c_row1_col4, #T_2ce5c_row2_col7 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_2ce5c_row0_col8, #T_2ce5c_row1_col8, #T_2ce5c_row3_col8, #T_2ce5c_row4_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_2ce5c_row2_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_2ce5c\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_2ce5c_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_2ce5c_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_2ce5c_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_2ce5c_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_2ce5c_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_2ce5c_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_2ce5c_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_2ce5c_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "      <th id=\"T_2ce5c_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_2ce5c_level0_row0\" class=\"row_heading level0 row0\" >arima</th>\n", "      <td id=\"T_2ce5c_row0_col0\" class=\"data row0 col0\" >ARIMA</td>\n", "      <td id=\"T_2ce5c_row0_col1\" class=\"data row0 col1\" >0.8406</td>\n", "      <td id=\"T_2ce5c_row0_col2\" class=\"data row0 col2\" >0.9158</td>\n", "      <td id=\"T_2ce5c_row0_col3\" class=\"data row0 col3\" >87.3689</td>\n", "      <td id=\"T_2ce5c_row0_col4\" class=\"data row0 col4\" >133.0642</td>\n", "      <td id=\"T_2ce5c_row0_col5\" class=\"data row0 col5\" >0.4273</td>\n", "      <td id=\"T_2ce5c_row0_col6\" class=\"data row0 col6\" >0.3443</td>\n", "      <td id=\"T_2ce5c_row0_col7\" class=\"data row0 col7\" >-1.3072</td>\n", "      <td id=\"T_2ce5c_row0_col8\" class=\"data row0 col8\" >0.3200</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2ce5c_level0_row1\" class=\"row_heading level0 row1\" >exp_smooth</th>\n", "      <td id=\"T_2ce5c_row1_col0\" class=\"data row1 col0\" >Exponential Smoothing</td>\n", "      <td id=\"T_2ce5c_row1_col1\" class=\"data row1 col1\" >0.8954</td>\n", "      <td id=\"T_2ce5c_row1_col2\" class=\"data row1 col2\" >0.8400</td>\n", "      <td id=\"T_2ce5c_row1_col3\" class=\"data row1 col3\" >93.0132</td>\n", "      <td id=\"T_2ce5c_row1_col4\" class=\"data row1 col4\" >121.9760</td>\n", "      <td id=\"T_2ce5c_row1_col5\" class=\"data row1 col5\" >0.4828</td>\n", "      <td id=\"T_2ce5c_row1_col6\" class=\"data row1 col6\" >0.5917</td>\n", "      <td id=\"T_2ce5c_row1_col7\" class=\"data row1 col7\" >-0.9311</td>\n", "      <td id=\"T_2ce5c_row1_col8\" class=\"data row1 col8\" >0.1533</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2ce5c_level0_row2\" class=\"row_heading level0 row2\" >theta</th>\n", "      <td id=\"T_2ce5c_row2_col0\" class=\"data row2 col0\" >Theta Forecaster</td>\n", "      <td id=\"T_2ce5c_row2_col1\" class=\"data row2 col1\" >1.0279</td>\n", "      <td id=\"T_2ce5c_row2_col2\" class=\"data row2 col2\" >0.9437</td>\n", "      <td id=\"T_2ce5c_row2_col3\" class=\"data row2 col3\" >107.4620</td>\n", "      <td id=\"T_2ce5c_row2_col4\" class=\"data row2 col4\" >137.6886</td>\n", "      <td id=\"T_2ce5c_row2_col5\" class=\"data row2 col5\" >0.5192</td>\n", "      <td id=\"T_2ce5c_row2_col6\" class=\"data row2 col6\" >0.4990</td>\n", "      <td id=\"T_2ce5c_row2_col7\" class=\"data row2 col7\" >-0.4072</td>\n", "      <td id=\"T_2ce5c_row2_col8\" class=\"data row2 col8\" >0.0600</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2ce5c_level0_row3\" class=\"row_heading level0 row3\" >lightgbm_cds_dt</th>\n", "      <td id=\"T_2ce5c_row3_col0\" class=\"data row3 col0\" >Light Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_2ce5c_row3_col1\" class=\"data row3 col1\" >1.2021</td>\n", "      <td id=\"T_2ce5c_row3_col2\" class=\"data row3 col2\" >1.1033</td>\n", "      <td id=\"T_2ce5c_row3_col3\" class=\"data row3 col3\" >124.6215</td>\n", "      <td id=\"T_2ce5c_row3_col4\" class=\"data row3 col4\" >160.1069</td>\n", "      <td id=\"T_2ce5c_row3_col5\" class=\"data row3 col5\" >0.6995</td>\n", "      <td id=\"T_2ce5c_row3_col6\" class=\"data row3 col6\" >0.5078</td>\n", "      <td id=\"T_2ce5c_row3_col7\" class=\"data row3 col7\" >-2.5717</td>\n", "      <td id=\"T_2ce5c_row3_col8\" class=\"data row3 col8\" >1.1700</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2ce5c_level0_row4\" class=\"row_heading level0 row4\" >ets</th>\n", "      <td id=\"T_2ce5c_row4_col0\" class=\"data row4 col0\" >ETS</td>\n", "      <td id=\"T_2ce5c_row4_col1\" class=\"data row4 col1\" >1.6466</td>\n", "      <td id=\"T_2ce5c_row4_col2\" class=\"data row4 col2\" >1.5514</td>\n", "      <td id=\"T_2ce5c_row4_col3\" class=\"data row4 col3\" >171.0757</td>\n", "      <td id=\"T_2ce5c_row4_col4\" class=\"data row4 col4\" >225.4193</td>\n", "      <td id=\"T_2ce5c_row4_col5\" class=\"data row4 col5\" >0.9284</td>\n", "      <td id=\"T_2ce5c_row4_col6\" class=\"data row4 col6\" >0.5548</td>\n", "      <td id=\"T_2ce5c_row4_col7\" class=\"data row4 col7\" >-4.3206</td>\n", "      <td id=\"T_2ce5c_row4_col8\" class=\"data row4 col8\" >1.7100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x2784a037580>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_a4d5f_row25_col1, #T_a4d5f_row30_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_a4d5f\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_a4d5f_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_a4d5f_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_a4d5f_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_a4d5f_row0_col1\" class=\"data row0 col1\" >42</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_a4d5f_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_a4d5f_row1_col1\" class=\"data row1 col1\" >PT08.S3(NOx)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_a4d5f_row2_col0\" class=\"data row2 col0\" >Approach</td>\n", "      <td id=\"T_a4d5f_row2_col1\" class=\"data row2 col1\" >Univariate</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_a4d5f_row3_col0\" class=\"data row3 col0\" >Exogenous Variables</td>\n", "      <td id=\"T_a4d5f_row3_col1\" class=\"data row3 col1\" >Not Present</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_a4d5f_row4_col0\" class=\"data row4 col0\" >Original data shape</td>\n", "      <td id=\"T_a4d5f_row4_col1\" class=\"data row4 col1\" >(720, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_a4d5f_row5_col0\" class=\"data row5 col0\" >Transformed data shape</td>\n", "      <td id=\"T_a4d5f_row5_col1\" class=\"data row5 col1\" >(720, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_a4d5f_row6_col0\" class=\"data row6 col0\" >Transformed train set shape</td>\n", "      <td id=\"T_a4d5f_row6_col1\" class=\"data row6 col1\" >(672, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_a4d5f_row7_col0\" class=\"data row7 col0\" >Transformed test set shape</td>\n", "      <td id=\"T_a4d5f_row7_col1\" class=\"data row7 col1\" >(48, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_a4d5f_row8_col0\" class=\"data row8 col0\" >Rows with missing values</td>\n", "      <td id=\"T_a4d5f_row8_col1\" class=\"data row8 col1\" >0.1%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_a4d5f_row9_col0\" class=\"data row9 col0\" >Fold Generator</td>\n", "      <td id=\"T_a4d5f_row9_col1\" class=\"data row9 col1\" >ExpandingWindowSplitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_a4d5f_row10_col0\" class=\"data row10 col0\" >Fold Number</td>\n", "      <td id=\"T_a4d5f_row10_col1\" class=\"data row10 col1\" >3</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_a4d5f_row11_col0\" class=\"data row11 col0\" >Enforce Prediction Interval</td>\n", "      <td id=\"T_a4d5f_row11_col1\" class=\"data row11 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_a4d5f_row12_col0\" class=\"data row12 col0\" >Splits used for hyperparameters</td>\n", "      <td id=\"T_a4d5f_row12_col1\" class=\"data row12 col1\" >all</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_a4d5f_row13_col0\" class=\"data row13 col0\" >User Defined Seasonal Period(s)</td>\n", "      <td id=\"T_a4d5f_row13_col1\" class=\"data row13 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_a4d5f_row14_col0\" class=\"data row14 col0\" >Ignore Seasonality Test</td>\n", "      <td id=\"T_a4d5f_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_a4d5f_row15_col0\" class=\"data row15 col0\" >Seasonality Detection Algo</td>\n", "      <td id=\"T_a4d5f_row15_col1\" class=\"data row15 col1\" >auto</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_a4d5f_row16_col0\" class=\"data row16 col0\" >Max Period to Consider</td>\n", "      <td id=\"T_a4d5f_row16_col1\" class=\"data row16 col1\" >60</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_a4d5f_row17_col0\" class=\"data row17 col0\" >Seasonal Period(s) Tested</td>\n", "      <td id=\"T_a4d5f_row17_col1\" class=\"data row17 col1\" >[24, 48, 25, 23, 47, 49, 12, 36, 11]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_a4d5f_row18_col0\" class=\"data row18 col0\" >Significant Seasonal Period(s)</td>\n", "      <td id=\"T_a4d5f_row18_col1\" class=\"data row18 col1\" >[24, 48, 25, 23, 47, 49, 12, 36, 11]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_a4d5f_row19_col0\" class=\"data row19 col0\" >Significant Seasonal Period(s) without Harmonics</td>\n", "      <td id=\"T_a4d5f_row19_col1\" class=\"data row19 col1\" >[48, 25, 23, 47, 49, 36, 11]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_a4d5f_row20_col0\" class=\"data row20 col0\" >Remove Harmonics</td>\n", "      <td id=\"T_a4d5f_row20_col1\" class=\"data row20 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_a4d5f_row21_col0\" class=\"data row21 col0\" >Harmonics Order Method</td>\n", "      <td id=\"T_a4d5f_row21_col1\" class=\"data row21 col1\" >harmonic_max</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_a4d5f_row22_col0\" class=\"data row22 col0\" >Num Seasonalities to Use</td>\n", "      <td id=\"T_a4d5f_row22_col1\" class=\"data row22 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_a4d5f_row23_col0\" class=\"data row23 col0\" >All Seasonalities to Use</td>\n", "      <td id=\"T_a4d5f_row23_col1\" class=\"data row23 col1\" >[24]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_a4d5f_row24_col0\" class=\"data row24 col0\" >Primary Seasonality</td>\n", "      <td id=\"T_a4d5f_row24_col1\" class=\"data row24 col1\" >24</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_a4d5f_row25_col0\" class=\"data row25 col0\" >Seasonality Present</td>\n", "      <td id=\"T_a4d5f_row25_col1\" class=\"data row25 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_a4d5f_row26_col0\" class=\"data row26 col0\" >Target Strictly Positive</td>\n", "      <td id=\"T_a4d5f_row26_col1\" class=\"data row26 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_a4d5f_row27_col0\" class=\"data row27 col0\" >Target White Noise</td>\n", "      <td id=\"T_a4d5f_row27_col1\" class=\"data row27 col1\" >No</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_a4d5f_row28_col0\" class=\"data row28 col0\" >Recommended d</td>\n", "      <td id=\"T_a4d5f_row28_col1\" class=\"data row28 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_a4d5f_row29_col0\" class=\"data row29 col0\" >Recommended Seasonal D</td>\n", "      <td id=\"T_a4d5f_row29_col1\" class=\"data row29 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_a4d5f_row30_col0\" class=\"data row30 col0\" >Preprocess</td>\n", "      <td id=\"T_a4d5f_row30_col1\" class=\"data row30 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_a4d5f_row31_col0\" class=\"data row31 col0\" >Numerical Imputation (Target)</td>\n", "      <td id=\"T_a4d5f_row31_col1\" class=\"data row31 col1\" >ffill</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_a4d5f_row32_col0\" class=\"data row32 col0\" >Transformation (Target)</td>\n", "      <td id=\"T_a4d5f_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_a4d5f_row33_col0\" class=\"data row33 col0\" >Scaling (Target)</td>\n", "      <td id=\"T_a4d5f_row33_col1\" class=\"data row33 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_a4d5f_row34_col0\" class=\"data row34 col0\" >Feature Engineering (Target) - Reduced Regression</td>\n", "      <td id=\"T_a4d5f_row34_col1\" class=\"data row34 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_a4d5f_row35_col0\" class=\"data row35 col0\" >CPU Jobs</td>\n", "      <td id=\"T_a4d5f_row35_col1\" class=\"data row35 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_a4d5f_row36_col0\" class=\"data row36 col0\" >Use GPU</td>\n", "      <td id=\"T_a4d5f_row36_col1\" class=\"data row36 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_a4d5f_row37_col0\" class=\"data row37 col0\" >Log Experiment</td>\n", "      <td id=\"T_a4d5f_row37_col1\" class=\"data row37 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_a4d5f_row38_col0\" class=\"data row38 col0\" >Experiment Name</td>\n", "      <td id=\"T_a4d5f_row38_col1\" class=\"data row38 col1\" >ts-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a4d5f_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_a4d5f_row39_col0\" class=\"data row39 col0\" >USI</td>\n", "      <td id=\"T_a4d5f_row39_col1\" class=\"data row39 col1\" >7c62</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x278532bf6d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_4f62c th {\n", "  text-align: left;\n", "}\n", "#T_4f62c_row0_col0, #T_4f62c_row1_col0, #T_4f62c_row1_col1, #T_4f62c_row1_col2, #T_4f62c_row1_col3, #T_4f62c_row1_col4, #T_4f62c_row1_col5, #T_4f62c_row1_col6, #T_4f62c_row1_col7, #T_4f62c_row2_col0, #T_4f62c_row2_col1, #T_4f62c_row2_col2, #T_4f62c_row2_col3, #T_4f62c_row2_col4, #T_4f62c_row2_col5, #T_4f62c_row2_col6, #T_4f62c_row2_col7, #T_4f62c_row3_col0, #T_4f62c_row3_col1, #T_4f62c_row3_col2, #T_4f62c_row3_col3, #T_4f62c_row3_col4, #T_4f62c_row3_col5, #T_4f62c_row3_col6, #T_4f62c_row3_col7, #T_4f62c_row4_col0, #T_4f62c_row4_col1, #T_4f62c_row4_col2, #T_4f62c_row4_col3, #T_4f62c_row4_col4, #T_4f62c_row4_col5, #T_4f62c_row4_col6, #T_4f62c_row4_col7 {\n", "  text-align: left;\n", "}\n", "#T_4f62c_row0_col1, #T_4f62c_row0_col2, #T_4f62c_row0_col3, #T_4f62c_row0_col4, #T_4f62c_row0_col5, #T_4f62c_row0_col6, #T_4f62c_row0_col7 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_4f62c_row0_col8, #T_4f62c_row1_col8, #T_4f62c_row3_col8, #T_4f62c_row4_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_4f62c_row2_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_4f62c\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_4f62c_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_4f62c_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_4f62c_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_4f62c_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_4f62c_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_4f62c_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_4f62c_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_4f62c_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "      <th id=\"T_4f62c_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_4f62c_level0_row0\" class=\"row_heading level0 row0\" >exp_smooth</th>\n", "      <td id=\"T_4f62c_row0_col0\" class=\"data row0 col0\" >Exponential Smoothing</td>\n", "      <td id=\"T_4f62c_row0_col1\" class=\"data row0 col1\" >1.2435</td>\n", "      <td id=\"T_4f62c_row0_col2\" class=\"data row0 col2\" >1.2056</td>\n", "      <td id=\"T_4f62c_row0_col3\" class=\"data row0 col3\" >126.5383</td>\n", "      <td id=\"T_4f62c_row0_col4\" class=\"data row0 col4\" >158.9241</td>\n", "      <td id=\"T_4f62c_row0_col5\" class=\"data row0 col5\" >0.1738</td>\n", "      <td id=\"T_4f62c_row0_col6\" class=\"data row0 col6\" >0.1695</td>\n", "      <td id=\"T_4f62c_row0_col7\" class=\"data row0 col7\" >-0.0211</td>\n", "      <td id=\"T_4f62c_row0_col8\" class=\"data row0 col8\" >0.1600</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f62c_level0_row1\" class=\"row_heading level0 row1\" >ets</th>\n", "      <td id=\"T_4f62c_row1_col0\" class=\"data row1 col0\" >ETS</td>\n", "      <td id=\"T_4f62c_row1_col1\" class=\"data row1 col1\" >1.3630</td>\n", "      <td id=\"T_4f62c_row1_col2\" class=\"data row1 col2\" >1.3140</td>\n", "      <td id=\"T_4f62c_row1_col3\" class=\"data row1 col3\" >138.7259</td>\n", "      <td id=\"T_4f62c_row1_col4\" class=\"data row1 col4\" >173.2545</td>\n", "      <td id=\"T_4f62c_row1_col5\" class=\"data row1 col5\" >0.1906</td>\n", "      <td id=\"T_4f62c_row1_col6\" class=\"data row1 col6\" >0.1879</td>\n", "      <td id=\"T_4f62c_row1_col7\" class=\"data row1 col7\" >-0.2091</td>\n", "      <td id=\"T_4f62c_row1_col8\" class=\"data row1 col8\" >0.7067</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f62c_level0_row2\" class=\"row_heading level0 row2\" >theta</th>\n", "      <td id=\"T_4f62c_row2_col0\" class=\"data row2 col0\" >Theta Forecaster</td>\n", "      <td id=\"T_4f62c_row2_col1\" class=\"data row2 col1\" >1.3716</td>\n", "      <td id=\"T_4f62c_row2_col2\" class=\"data row2 col2\" >1.3079</td>\n", "      <td id=\"T_4f62c_row2_col3\" class=\"data row2 col3\" >139.5929</td>\n", "      <td id=\"T_4f62c_row2_col4\" class=\"data row2 col4\" >172.4272</td>\n", "      <td id=\"T_4f62c_row2_col5\" class=\"data row2 col5\" >0.1909</td>\n", "      <td id=\"T_4f62c_row2_col6\" class=\"data row2 col6\" >0.1878</td>\n", "      <td id=\"T_4f62c_row2_col7\" class=\"data row2 col7\" >-0.1963</td>\n", "      <td id=\"T_4f62c_row2_col8\" class=\"data row2 col8\" >0.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f62c_level0_row3\" class=\"row_heading level0 row3\" >arima</th>\n", "      <td id=\"T_4f62c_row3_col0\" class=\"data row3 col0\" >ARIMA</td>\n", "      <td id=\"T_4f62c_row3_col1\" class=\"data row3 col1\" >1.3929</td>\n", "      <td id=\"T_4f62c_row3_col2\" class=\"data row3 col2\" >1.3245</td>\n", "      <td id=\"T_4f62c_row3_col3\" class=\"data row3 col3\" >141.6775</td>\n", "      <td id=\"T_4f62c_row3_col4\" class=\"data row3 col4\" >174.6211</td>\n", "      <td id=\"T_4f62c_row3_col5\" class=\"data row3 col5\" >0.1792</td>\n", "      <td id=\"T_4f62c_row3_col6\" class=\"data row3 col6\" >0.1953</td>\n", "      <td id=\"T_4f62c_row3_col7\" class=\"data row3 col7\" >-0.3985</td>\n", "      <td id=\"T_4f62c_row3_col8\" class=\"data row3 col8\" >0.4100</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f62c_level0_row4\" class=\"row_heading level0 row4\" >lightgbm_cds_dt</th>\n", "      <td id=\"T_4f62c_row4_col0\" class=\"data row4 col0\" >Light Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_4f62c_row4_col1\" class=\"data row4 col1\" >1.6778</td>\n", "      <td id=\"T_4f62c_row4_col2\" class=\"data row4 col2\" >1.5491</td>\n", "      <td id=\"T_4f62c_row4_col3\" class=\"data row4 col3\" >170.7442</td>\n", "      <td id=\"T_4f62c_row4_col4\" class=\"data row4 col4\" >204.2588</td>\n", "      <td id=\"T_4f62c_row4_col5\" class=\"data row4 col5\" >0.2197</td>\n", "      <td id=\"T_4f62c_row4_col6\" class=\"data row4 col6\" >0.2541</td>\n", "      <td id=\"T_4f62c_row4_col7\" class=\"data row4 col7\" >-0.7666</td>\n", "      <td id=\"T_4f62c_row4_col8\" class=\"data row4 col8\" >1.1667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x27849bc07f0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_c1f41_row25_col1, #T_c1f41_row30_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_c1f41\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_c1f41_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_c1f41_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_c1f41_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_c1f41_row0_col1\" class=\"data row0 col1\" >42</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_c1f41_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_c1f41_row1_col1\" class=\"data row1 col1\" >RH</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_c1f41_row2_col0\" class=\"data row2 col0\" >Approach</td>\n", "      <td id=\"T_c1f41_row2_col1\" class=\"data row2 col1\" >Univariate</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_c1f41_row3_col0\" class=\"data row3 col0\" >Exogenous Variables</td>\n", "      <td id=\"T_c1f41_row3_col1\" class=\"data row3 col1\" >Not Present</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_c1f41_row4_col0\" class=\"data row4 col0\" >Original data shape</td>\n", "      <td id=\"T_c1f41_row4_col1\" class=\"data row4 col1\" >(720, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_c1f41_row5_col0\" class=\"data row5 col0\" >Transformed data shape</td>\n", "      <td id=\"T_c1f41_row5_col1\" class=\"data row5 col1\" >(720, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_c1f41_row6_col0\" class=\"data row6 col0\" >Transformed train set shape</td>\n", "      <td id=\"T_c1f41_row6_col1\" class=\"data row6 col1\" >(672, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_c1f41_row7_col0\" class=\"data row7 col0\" >Transformed test set shape</td>\n", "      <td id=\"T_c1f41_row7_col1\" class=\"data row7 col1\" >(48, 1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_c1f41_row8_col0\" class=\"data row8 col0\" >Rows with missing values</td>\n", "      <td id=\"T_c1f41_row8_col1\" class=\"data row8 col1\" >0.1%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_c1f41_row9_col0\" class=\"data row9 col0\" >Fold Generator</td>\n", "      <td id=\"T_c1f41_row9_col1\" class=\"data row9 col1\" >ExpandingWindowSplitter</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_c1f41_row10_col0\" class=\"data row10 col0\" >Fold Number</td>\n", "      <td id=\"T_c1f41_row10_col1\" class=\"data row10 col1\" >3</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_c1f41_row11_col0\" class=\"data row11 col0\" >Enforce Prediction Interval</td>\n", "      <td id=\"T_c1f41_row11_col1\" class=\"data row11 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_c1f41_row12_col0\" class=\"data row12 col0\" >Splits used for hyperparameters</td>\n", "      <td id=\"T_c1f41_row12_col1\" class=\"data row12 col1\" >all</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_c1f41_row13_col0\" class=\"data row13 col0\" >User Defined Seasonal Period(s)</td>\n", "      <td id=\"T_c1f41_row13_col1\" class=\"data row13 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_c1f41_row14_col0\" class=\"data row14 col0\" >Ignore Seasonality Test</td>\n", "      <td id=\"T_c1f41_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_c1f41_row15_col0\" class=\"data row15 col0\" >Seasonality Detection Algo</td>\n", "      <td id=\"T_c1f41_row15_col1\" class=\"data row15 col1\" >auto</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_c1f41_row16_col0\" class=\"data row16 col0\" >Max Period to Consider</td>\n", "      <td id=\"T_c1f41_row16_col1\" class=\"data row16 col1\" >60</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_c1f41_row17_col0\" class=\"data row17 col0\" >Seasonal Period(s) Tested</td>\n", "      <td id=\"T_c1f41_row17_col1\" class=\"data row17 col1\" >[2, 3, 24, 23, 25, 22, 4, 26, 21, 48, 47, 49, 46, 5, 27, 50, 20, 45, 51, 28, 19, 6, 44, 52]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_c1f41_row18_col0\" class=\"data row18 col0\" >Significant Seasonal Period(s)</td>\n", "      <td id=\"T_c1f41_row18_col1\" class=\"data row18 col1\" >[2, 3, 24, 23, 25, 22, 4, 26, 21, 48, 47, 49, 46, 5, 27, 50, 20, 45, 51, 28, 19, 6, 44, 52]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_c1f41_row19_col0\" class=\"data row19 col0\" >Significant Seasonal Period(s) without Harmonics</td>\n", "      <td id=\"T_c1f41_row19_col1\" class=\"data row19 col1\" >[52, 51, 48, 46, 50, 44, 21, 47, 49, 27, 20, 45, 28, 19]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_c1f41_row20_col0\" class=\"data row20 col0\" >Remove Harmonics</td>\n", "      <td id=\"T_c1f41_row20_col1\" class=\"data row20 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_c1f41_row21_col0\" class=\"data row21 col0\" >Harmonics Order Method</td>\n", "      <td id=\"T_c1f41_row21_col1\" class=\"data row21 col1\" >harmonic_max</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_c1f41_row22_col0\" class=\"data row22 col0\" >Num Seasonalities to Use</td>\n", "      <td id=\"T_c1f41_row22_col1\" class=\"data row22 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_c1f41_row23_col0\" class=\"data row23 col0\" >All Seasonalities to Use</td>\n", "      <td id=\"T_c1f41_row23_col1\" class=\"data row23 col1\" >[2]</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_c1f41_row24_col0\" class=\"data row24 col0\" >Primary Seasonality</td>\n", "      <td id=\"T_c1f41_row24_col1\" class=\"data row24 col1\" >2</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_c1f41_row25_col0\" class=\"data row25 col0\" >Seasonality Present</td>\n", "      <td id=\"T_c1f41_row25_col1\" class=\"data row25 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_c1f41_row26_col0\" class=\"data row26 col0\" >Target Strictly Positive</td>\n", "      <td id=\"T_c1f41_row26_col1\" class=\"data row26 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_c1f41_row27_col0\" class=\"data row27 col0\" >Target White Noise</td>\n", "      <td id=\"T_c1f41_row27_col1\" class=\"data row27 col1\" >No</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_c1f41_row28_col0\" class=\"data row28 col0\" >Recommended d</td>\n", "      <td id=\"T_c1f41_row28_col1\" class=\"data row28 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_c1f41_row29_col0\" class=\"data row29 col0\" >Recommended Seasonal D</td>\n", "      <td id=\"T_c1f41_row29_col1\" class=\"data row29 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_c1f41_row30_col0\" class=\"data row30 col0\" >Preprocess</td>\n", "      <td id=\"T_c1f41_row30_col1\" class=\"data row30 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_c1f41_row31_col0\" class=\"data row31 col0\" >Numerical Imputation (Target)</td>\n", "      <td id=\"T_c1f41_row31_col1\" class=\"data row31 col1\" >ffill</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_c1f41_row32_col0\" class=\"data row32 col0\" >Transformation (Target)</td>\n", "      <td id=\"T_c1f41_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_c1f41_row33_col0\" class=\"data row33 col0\" >Scaling (Target)</td>\n", "      <td id=\"T_c1f41_row33_col1\" class=\"data row33 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_c1f41_row34_col0\" class=\"data row34 col0\" >Feature Engineering (Target) - Reduced Regression</td>\n", "      <td id=\"T_c1f41_row34_col1\" class=\"data row34 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_c1f41_row35_col0\" class=\"data row35 col0\" >CPU Jobs</td>\n", "      <td id=\"T_c1f41_row35_col1\" class=\"data row35 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_c1f41_row36_col0\" class=\"data row36 col0\" >Use GPU</td>\n", "      <td id=\"T_c1f41_row36_col1\" class=\"data row36 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_c1f41_row37_col0\" class=\"data row37 col0\" >Log Experiment</td>\n", "      <td id=\"T_c1f41_row37_col1\" class=\"data row37 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_c1f41_row38_col0\" class=\"data row38 col0\" >Experiment Name</td>\n", "      <td id=\"T_c1f41_row38_col1\" class=\"data row38 col1\" >ts-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c1f41_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_c1f41_row39_col0\" class=\"data row39 col0\" >USI</td>\n", "      <td id=\"T_c1f41_row39_col1\" class=\"data row39 col1\" >9d33</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x2785831afa0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_eb9df th {\n", "  text-align: left;\n", "}\n", "#T_eb9df_row0_col0, #T_eb9df_row1_col0, #T_eb9df_row1_col1, #T_eb9df_row1_col2, #T_eb9df_row1_col3, #T_eb9df_row1_col4, #T_eb9df_row1_col5, #T_eb9df_row1_col6, #T_eb9df_row1_col7, #T_eb9df_row2_col0, #T_eb9df_row2_col1, #T_eb9df_row2_col2, #T_eb9df_row2_col3, #T_eb9df_row2_col4, #T_eb9df_row2_col5, #T_eb9df_row2_col6, #T_eb9df_row2_col7, #T_eb9df_row3_col0, #T_eb9df_row3_col1, #T_eb9df_row3_col2, #T_eb9df_row3_col3, #T_eb9df_row3_col4, #T_eb9df_row3_col5, #T_eb9df_row3_col6, #T_eb9df_row3_col7, #T_eb9df_row4_col0, #T_eb9df_row4_col1, #T_eb9df_row4_col2, #T_eb9df_row4_col3, #T_eb9df_row4_col4, #T_eb9df_row4_col5, #T_eb9df_row4_col6, #T_eb9df_row4_col7 {\n", "  text-align: left;\n", "}\n", "#T_eb9df_row0_col1, #T_eb9df_row0_col2, #T_eb9df_row0_col3, #T_eb9df_row0_col4, #T_eb9df_row0_col5, #T_eb9df_row0_col6, #T_eb9df_row0_col7 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_eb9df_row0_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "#T_eb9df_row1_col8, #T_eb9df_row2_col8, #T_eb9df_row3_col8, #T_eb9df_row4_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_eb9df\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_eb9df_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_eb9df_level0_col1\" class=\"col_heading level0 col1\" >MASE</th>\n", "      <th id=\"T_eb9df_level0_col2\" class=\"col_heading level0 col2\" >RMSSE</th>\n", "      <th id=\"T_eb9df_level0_col3\" class=\"col_heading level0 col3\" >MAE</th>\n", "      <th id=\"T_eb9df_level0_col4\" class=\"col_heading level0 col4\" >RMSE</th>\n", "      <th id=\"T_eb9df_level0_col5\" class=\"col_heading level0 col5\" >MAPE</th>\n", "      <th id=\"T_eb9df_level0_col6\" class=\"col_heading level0 col6\" >SMAPE</th>\n", "      <th id=\"T_eb9df_level0_col7\" class=\"col_heading level0 col7\" >R2</th>\n", "      <th id=\"T_eb9df_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_eb9df_level0_row0\" class=\"row_heading level0 row0\" >theta</th>\n", "      <td id=\"T_eb9df_row0_col0\" class=\"data row0 col0\" >Theta Forecaster</td>\n", "      <td id=\"T_eb9df_row0_col1\" class=\"data row0 col1\" >1.6218</td>\n", "      <td id=\"T_eb9df_row0_col2\" class=\"data row0 col2\" >1.4765</td>\n", "      <td id=\"T_eb9df_row0_col3\" class=\"data row0 col3\" >11.3749</td>\n", "      <td id=\"T_eb9df_row0_col4\" class=\"data row0 col4\" >13.1578</td>\n", "      <td id=\"T_eb9df_row0_col5\" class=\"data row0 col5\" >0.2481</td>\n", "      <td id=\"T_eb9df_row0_col6\" class=\"data row0 col6\" >0.2286</td>\n", "      <td id=\"T_eb9df_row0_col7\" class=\"data row0 col7\" >-0.0585</td>\n", "      <td id=\"T_eb9df_row0_col8\" class=\"data row0 col8\" >0.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_eb9df_level0_row1\" class=\"row_heading level0 row1\" >arima</th>\n", "      <td id=\"T_eb9df_row1_col0\" class=\"data row1 col0\" >ARIMA</td>\n", "      <td id=\"T_eb9df_row1_col1\" class=\"data row1 col1\" >1.8001</td>\n", "      <td id=\"T_eb9df_row1_col2\" class=\"data row1 col2\" >1.6165</td>\n", "      <td id=\"T_eb9df_row1_col3\" class=\"data row1 col3\" >12.6310</td>\n", "      <td id=\"T_eb9df_row1_col4\" class=\"data row1 col4\" >14.4108</td>\n", "      <td id=\"T_eb9df_row1_col5\" class=\"data row1 col5\" >0.2548</td>\n", "      <td id=\"T_eb9df_row1_col6\" class=\"data row1 col6\" >0.2523</td>\n", "      <td id=\"T_eb9df_row1_col7\" class=\"data row1 col7\" >-0.2695</td>\n", "      <td id=\"T_eb9df_row1_col8\" class=\"data row1 col8\" >0.0800</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_eb9df_level0_row2\" class=\"row_heading level0 row2\" >lightgbm_cds_dt</th>\n", "      <td id=\"T_eb9df_row2_col0\" class=\"data row2 col0\" >Light Gradient Boosting w/ Cond. Deseasonalize & Detrending</td>\n", "      <td id=\"T_eb9df_row2_col1\" class=\"data row2 col1\" >2.7797</td>\n", "      <td id=\"T_eb9df_row2_col2\" class=\"data row2 col2\" >2.6115</td>\n", "      <td id=\"T_eb9df_row2_col3\" class=\"data row2 col3\" >19.4667</td>\n", "      <td id=\"T_eb9df_row2_col4\" class=\"data row2 col4\" >23.2519</td>\n", "      <td id=\"T_eb9df_row2_col5\" class=\"data row2 col5\" >0.5241</td>\n", "      <td id=\"T_eb9df_row2_col6\" class=\"data row2 col6\" >0.3609</td>\n", "      <td id=\"T_eb9df_row2_col7\" class=\"data row2 col7\" >-4.8711</td>\n", "      <td id=\"T_eb9df_row2_col8\" class=\"data row2 col8\" >0.5633</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_eb9df_level0_row3\" class=\"row_heading level0 row3\" >exp_smooth</th>\n", "      <td id=\"T_eb9df_row3_col0\" class=\"data row3 col0\" >Exponential Smoothing</td>\n", "      <td id=\"T_eb9df_row3_col1\" class=\"data row3 col1\" >5.2972</td>\n", "      <td id=\"T_eb9df_row3_col2\" class=\"data row3 col2\" >4.7592</td>\n", "      <td id=\"T_eb9df_row3_col3\" class=\"data row3 col3\" >37.2423</td>\n", "      <td id=\"T_eb9df_row3_col4\" class=\"data row3 col4\" >42.4918</td>\n", "      <td id=\"T_eb9df_row3_col5\" class=\"data row3 col5\" >0.7188</td>\n", "      <td id=\"T_eb9df_row3_col6\" class=\"data row3 col6\" >0.9298</td>\n", "      <td id=\"T_eb9df_row3_col7\" class=\"data row3 col7\" >-10.5261</td>\n", "      <td id=\"T_eb9df_row3_col8\" class=\"data row3 col8\" >0.1400</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_eb9df_level0_row4\" class=\"row_heading level0 row4\" >ets</th>\n", "      <td id=\"T_eb9df_row4_col0\" class=\"data row4 col0\" >ETS</td>\n", "      <td id=\"T_eb9df_row4_col1\" class=\"data row4 col1\" >5.3235</td>\n", "      <td id=\"T_eb9df_row4_col2\" class=\"data row4 col2\" >4.7812</td>\n", "      <td id=\"T_eb9df_row4_col3\" class=\"data row4 col3\" >37.4259</td>\n", "      <td id=\"T_eb9df_row4_col4\" class=\"data row4 col4\" >42.6872</td>\n", "      <td id=\"T_eb9df_row4_col5\" class=\"data row4 col5\" >0.7228</td>\n", "      <td id=\"T_eb9df_row4_col6\" class=\"data row4 col6\" >0.9349</td>\n", "      <td id=\"T_eb9df_row4_col7\" class=\"data row4 col7\" >-10.5911</td>\n", "      <td id=\"T_eb9df_row4_col8\" class=\"data row4 col8\" >0.1233</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x278582265b0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Axes: >"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["future_preds = safe_predict(exp_auto, final_auto_model)\n", "future_preds.plot()"]}], "metadata": {"interpreter": {"hash": "c161a91f6f4623a54f30c5492a42e7cf0592610fb90c8abd312086f09f8fbe0f"}, "kernelspec": {"display_name": "pycaret_dev_sktime_16p1", "language": "python", "name": "pycaret_dev_sktime_16p1"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}