#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D-TransUNet 数据预处理和模型训练脚本，最好4090显卡
支持3D MRI图像分割任务的完整训练流程
模型参数太大，数据量要大一些   
"""
# https://github.com/Beckschen/3D-TransUNet

import os
import sys
import subprocess
import importlib

def install_dependencies():
    """自动安装必要的依赖包"""
    required_packages = [
        'ml_collections',
        'batchgenerators',
        'medpy',
        'einops',
        'fvcore',
        'tensorboardX',
        'termcolor',
        'tabulate',
        'iopath',
        'portalocker',
        'omegaconf',
        'hydra-core',
        'scipy'
    ]

    print("检查并安装必要的依赖包...")

    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败，请手动安装")

    print("依赖包检查完成！")

# 安装依赖
install_dependencies()

import argparse
import pickle
import yaml
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import autocast, GradScaler
from torch.utils.tensorboard import SummaryWriter
import SimpleITK as sitk
from tqdm import tqdm
from collections import OrderedDict
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.append('/root/autodl-tmp/3D-TransUNet')

from nn_transunet.networks.transunet3d_model import Generic_TransUNet_max_ppbp, InitWeights_He
from nn_transunet.trainer.loss_functions import DC_and_CE_loss, SoftDiceLoss


class Config:
    """配置类 - 优化版本，包含内存优化选项"""
    def __init__(self):
        # 数据路径配置
        self.image_dir = "/root/autodl-tmp/320HCC/image/ap"  # 图像目录
        self.mask_dir = "/root/autodl-tmp/320HCC/mask/ap"   # mask目录
        self.preprocessed_data_dir = "/root/autodl-tmp/data/preprocessed"  # 预处理数据目录
        self.output_dir = "/root/autodl-tmp/output"  # 输出目录

        # 内存优化配置
        self.use_compression = True  # 使用压缩存储格式
        self.use_float32 = True      # 使用float32而不是float64
        self.use_memory_mapping = False  # 使用内存映射加载大文件
        self.gradient_accumulation_steps = 4  # 梯度累积，模拟更大的batch_size
        self.mixed_precision = True  # 使用混合精度训练

        # 模型配置
        self.model_name = "Generic_TransUNet_max_ppbp"
        self.num_classes = 2  # 包括背景类，根据实际任务调整
        self.input_channels = 1  # MRI通常是单通道
        self.base_num_features = 32
        
        # 训练配置 - 内存优化版本
        self.batch_size = 1  # 减少batch_size以节省GPU内存
        self.num_epochs = 50  # 减少训练轮数：从100改为50
        self.initial_lr = 0.0001  # 使用更保守的学习率：从0.0005改为0.0001
        self.weight_decay = 1e-3  # 增强正则化：从1e-4改为1e-3
        self.momentum = 0.99

        # 数据增强配置 - 保持原始patch_size但减少batch_size
        self.patch_size = [64, 160, 160]  # [D, H, W] - 保持原始尺寸以避免维度问题
        self.do_data_augmentation = True

        # 其他配置
        self.mixed_precision = True
        self.deep_supervision = True
        self.save_interval = 50  # 减少保存频率以节省空间
        self.validation_interval = 1  # 增加验证频率：每个epoch验证一次

        # 早停配置
        self.early_stopping_patience = 10  # 减少早停patience：从15改为10
        self.early_stopping_min_delta = 0.001  # 最小改进阈值

        # TransUNet特定配置 - 恢复原始配置以避免尺寸问题
        self.vit_depth = 12  # 恢复原始ViT深度
        self.vit_hidden_size = 768  # 恢复原始隐藏维度
        self.max_hidden_dim = 192  # 恢复原始最大隐藏维度
        self.num_queries = 20  # 恢复原始查询数量
        self.is_max_hungarian = True
        self.is_max_cls = True
        self.use_pretrained_vit = False  # 禁用预训练权重


def setup_directories(config):
    """创建必要的目录"""
    os.makedirs(config.preprocessed_data_dir, exist_ok=True)
    os.makedirs(config.output_dir, exist_ok=True)
    os.makedirs(os.path.join(config.output_dir, "checkpoints"), exist_ok=True)
    os.makedirs(os.path.join(config.output_dir, "logs"), exist_ok=True)
    os.makedirs(os.path.join(config.output_dir, "predictions"), exist_ok=True)


def load_nifti_image(filepath):
    """加载NIfTI图像"""
    sitk_image = sitk.ReadImage(filepath)
    image_array = sitk.GetArrayFromImage(sitk_image)
    spacing = sitk_image.GetSpacing()
    origin = sitk_image.GetOrigin()
    direction = sitk_image.GetDirection()
    
    return {
        'data': image_array,
        'spacing': spacing,
        'origin': origin,
        'direction': direction,
        'sitk_image': sitk_image
    }


def save_nifti_image(image_array, reference_image, output_path):
    """保存NIfTI图像"""
    output_image = sitk.GetImageFromArray(image_array.astype(np.uint8))
    output_image.CopyInformation(reference_image)
    sitk.WriteImage(output_image, output_path)


def preprocess_image(image_data, target_spacing=[1.0, 1.0, 1.0], is_label=False, use_compression=True):
    """
    预处理单个图像或标签
    包括重采样、归一化等步骤
    优化版本：使用float32减少内存占用
    """
    data = image_data['data']
    spacing = image_data['spacing']

    # 数据类型转换 - 强制使用float32以减少内存占用
    if not is_label:
        data = data.astype(np.float32)  # 图像数据使用float32
    else:
        data = data.astype(np.uint8)   # 标签数据使用uint8

    # 重采样到目标spacing
    if spacing != target_spacing:
        # 使用SimpleITK进行重采样
        original_image = image_data['sitk_image']

        # 计算新的尺寸
        original_size = original_image.GetSize()
        original_spacing = original_image.GetSpacing()

        new_size = [
            int(round(original_size[i] * original_spacing[i] / target_spacing[i]))
            for i in range(3)
        ]

        # 重采样
        resampler = sitk.ResampleImageFilter()
        resampler.SetOutputSpacing(target_spacing)
        resampler.SetSize(new_size)
        resampler.SetOutputDirection(original_image.GetDirection())
        resampler.SetOutputOrigin(original_image.GetOrigin())
        resampler.SetTransform(sitk.Transform())
        resampler.SetDefaultPixelValue(0)

        # 对标签使用最近邻插值，对图像使用线性插值
        if is_label:
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)
        else:
            resampler.SetInterpolator(sitk.sitkLinear)

        resampled_image = resampler.Execute(original_image)
        data = sitk.GetArrayFromImage(resampled_image)

    # 只对图像进行强度归一化，不对标签进行
    if not is_label:
        # 强度归一化
        # 裁剪异常值
        percentile_99_5 = np.percentile(data, 99.5)
        percentile_00_5 = np.percentile(data, 0.5)
        data = np.clip(data, percentile_00_5, percentile_99_5)

        # Z-score归一化
        mean = data.mean()
        std = data.std()
        if std > 0:
            data = (data - mean) / std

    return data


def create_data_splits(image_dir, mask_dir, train_ratio=0.8, val_ratio=0.2, test_ratio=0.0, quick_test=False):
    """创建数据集划分 - 支持分离的图像和mask目录"""
    print(f"图像目录: {image_dir}")
    print(f"Mask目录: {mask_dir}")

    # 获取所有图像文件
    image_files = []
    for file in os.listdir(image_dir):
        if file.endswith('.nii.gz'):
            image_files.append(file)

    # 获取所有mask文件
    mask_files = []
    for file in os.listdir(mask_dir):
        if file.endswith('.nii.gz'):
            mask_files.append(file)

    image_files.sort()
    mask_files.sort()

    print(f"找到 {len(image_files)} 个图像文件")
    print(f"找到 {len(mask_files)} 个mask文件")

    # 快速测试模式：只使用前10个文件
    if quick_test:
        image_files = image_files[:10]
        mask_files = mask_files[:10]
        print(f"快速测试模式：只使用前 {len(image_files)} 个文件")

    # 匹配图像和mask文件
    data_pairs = []
    for image_file in image_files:
        # 尝试找到对应的mask文件
        base_name = image_file.replace('.nii.gz', '')

        # 可能的mask文件名模式
        possible_mask_names = [
            f"{base_name}-mask.nii.gz",
            f"{base_name}_mask.nii.gz",
            f"{base_name}-seg.nii.gz",
            f"{base_name}_seg.nii.gz",
            f"{base_name}-label.nii.gz",
            f"{base_name}_label.nii.gz",
            f"{base_name}.nii.gz"  # 同名文件
        ]

        mask_file = None
        for possible_name in possible_mask_names:
            if possible_name in mask_files:
                mask_file = possible_name
                break

        if mask_file:
            data_pairs.append((image_file, mask_file))
            print(f"匹配: {image_file} -> {mask_file}")
        else:
            print(f"警告: 未找到 {image_file} 对应的mask文件")

    print(f"成功匹配 {len(data_pairs)} 对文件")
    assert len(data_pairs) > 0, "没有找到匹配的图像-mask对"

    # 随机打乱
    np.random.shuffle(data_pairs)

    # 划分数据集
    n_total = len(data_pairs)
    n_train = int(n_total * train_ratio)
    n_val = int(n_total * val_ratio)

    train_pairs = data_pairs[:n_train]
    val_pairs = data_pairs[n_train:n_train + n_val]
    test_pairs = data_pairs[n_train + n_val:]

    print(f"数据划分: 训练集 {len(train_pairs)}, 验证集 {len(val_pairs)}, 测试集 {len(test_pairs)}")

    return {
        'train': train_pairs,
        'val': val_pairs,
        'test': test_pairs
    }


def process_single_case(args):
    """处理单个样本的函数 - 用于并行处理"""
    image_file, label_file, config, split_dir = args

    try:
        # 加载图像和标签
        image_path = os.path.join(config.image_dir, image_file)
        label_path = os.path.join(config.mask_dir, label_file)

        image_data = load_nifti_image(image_path)
        label_data = load_nifti_image(label_path)

        # 预处理图像和标签到相同的spacing
        processed_image = preprocess_image(image_data, is_label=False)
        processed_label = preprocess_image(label_data, is_label=True)

        # 使用原始文件名作为基础名称
        base_name = image_file.replace('.nii.gz', '')

        # 保存预处理后的数据，使用压缩格式减少存储空间
        np.savez_compressed(os.path.join(split_dir, f"{base_name}_image.npz"),
                          data=processed_image.astype(np.float32))
        np.savez_compressed(os.path.join(split_dir, f"{base_name}_label.npz"),
                          data=processed_label.astype(np.uint8))

        # 保存元数据
        metadata = {
            'original_spacing': image_data['spacing'],
            'original_origin': image_data['origin'],
            'original_direction': image_data['direction'],
            'original_shape': image_data['data'].shape,
            'processed_shape': processed_image.shape,
            'image_file': image_file,
            'label_file': label_file,
            'image_path': image_path,
            'label_path': label_path,
            'base_name': base_name
        }

        with open(os.path.join(split_dir, f"{base_name}_metadata.pkl"), 'wb') as f:
            pickle.dump(metadata, f)

        return f"成功处理: {base_name}"

    except Exception as e:
        return f"处理失败 {image_file}: {str(e)}"


def preprocess_dataset(config, use_parallel=True, num_workers=4):
    """预处理整个数据集 - 支持并行处理"""
    print("开始数据预处理...")

    # 创建数据划分
    splits = create_data_splits(config.image_dir, config.mask_dir, quick_test=getattr(config, 'quick_test', False))

    # 保存数据划分信息
    with open(os.path.join(config.preprocessed_data_dir, 'splits.pkl'), 'wb') as f:
        pickle.dump(splits, f)

    # 预处理每个数据集
    for split_name, pairs in splits.items():
        split_dir = os.path.join(config.preprocessed_data_dir, split_name)
        os.makedirs(split_dir, exist_ok=True)

        print(f"预处理 {split_name} 数据集 ({len(pairs)} 个样本)...")

        if use_parallel and len(pairs) > 1:
            # 并行处理
            from multiprocessing import Pool

            # 准备参数
            args_list = [(image_file, label_file, config, split_dir)
                        for image_file, label_file in pairs]

            # 使用进程池并行处理
            with Pool(processes=num_workers) as pool:
                results = list(tqdm(
                    pool.imap(process_single_case, args_list),
                    total=len(args_list),
                    desc=f"并行处理{split_name}"
                ))

            # 打印结果
            for result in results:
                if "失败" in result:
                    print(result)
        else:
            # 串行处理（原始方法）
            for i, (image_file, label_file) in enumerate(tqdm(pairs, desc=f"串行处理{split_name}")):
                result = process_single_case((image_file, label_file, config, split_dir))
                if "失败" in result:
                    print(result)

    print("数据预处理完成!")
    return splits


class CustomDataset(torch.utils.data.Dataset):
    """自定义数据集类 - 优化版本，支持压缩文件格式"""
    def __init__(self, data_dir, patch_size, do_augmentation=True):
        self.data_dir = data_dir
        self.patch_size = patch_size
        self.do_augmentation = do_augmentation

        # 获取所有数据文件，支持.npy和.npz格式
        self.data_files = []
        for file in os.listdir(data_dir):
            if file.endswith('_image.npy'):
                case_name = file.replace('_image.npy', '')
                self.data_files.append(case_name)
            elif file.endswith('_image.npz'):
                case_name = file.replace('_image.npz', '')
                self.data_files.append(case_name)

        self.data_files.sort()
        print(f"数据集包含 {len(self.data_files)} 个样本")

    def __len__(self):
        return len(self.data_files)

    def __getitem__(self, idx):
        case_name = self.data_files[idx]

        # 智能加载图像和标签，支持.npy和.npz格式
        # 优先使用压缩格式以节省内存
        image_npz_path = os.path.join(self.data_dir, f"{case_name}_image.npz")
        image_npy_path = os.path.join(self.data_dir, f"{case_name}_image.npy")
        label_npz_path = os.path.join(self.data_dir, f"{case_name}_label.npz")
        label_npy_path = os.path.join(self.data_dir, f"{case_name}_label.npy")

        # 加载图像数据
        if os.path.exists(image_npz_path):
            image = np.load(image_npz_path)['data'].astype(np.float32)
        elif os.path.exists(image_npy_path):
            image = np.load(image_npy_path).astype(np.float32)
        else:
            raise FileNotFoundError(f"找不到图像文件: {case_name}")

        # 加载标签数据
        if os.path.exists(label_npz_path):
            label = np.load(label_npz_path)['data'].astype(np.uint8)
        elif os.path.exists(label_npy_path):
            label = np.load(label_npy_path).astype(np.uint8)
        else:
            raise FileNotFoundError(f"找不到标签文件: {case_name}")

        # 添加通道维度
        if len(image.shape) == 3:
            image = image[np.newaxis, ...]  # [1, D, H, W]

        # 随机裁剪到patch_size
        image, label = self._random_crop(image, label)

        # 数据增强
        if self.do_augmentation:
            image, label = self._augment(image, label)

        # 转换为tensor
        image = torch.from_numpy(image.astype(np.float32))
        label = torch.from_numpy(label.astype(np.long))

        # 为标签添加通道维度以匹配损失函数期望的格式
        # 损失函数期望 [B, 1, D, H, W] 格式，所以我们需要添加通道维度
        label = label.unsqueeze(0)  # [D, H, W] -> [1, D, H, W]

        return {
            'data': image,
            'target': label,
            'case_name': case_name
        }

    def _random_crop(self, image, label):
        """随机裁剪到指定大小"""
        # image: [C, D, H, W], label: [D, H, W]
        _, d, h, w = image.shape
        target_d, target_h, target_w = self.patch_size

        # 如果图像小于目标大小，进行padding
        pad_d = max(0, target_d - d)
        pad_h = max(0, target_h - h)
        pad_w = max(0, target_w - w)

        if pad_d > 0 or pad_h > 0 or pad_w > 0:
            image = np.pad(image, ((0, 0), (0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            label = np.pad(label, ((0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            d, h, w = image.shape[1:]

        # 随机选择起始位置
        start_d = np.random.randint(0, max(1, d - target_d + 1))
        start_h = np.random.randint(0, max(1, h - target_h + 1))
        start_w = np.random.randint(0, max(1, w - target_w + 1))

        # 裁剪
        image = image[:, start_d:start_d + target_d, start_h:start_h + target_h, start_w:start_w + target_w]
        label = label[start_d:start_d + target_d, start_h:start_h + target_h, start_w:start_w + target_w]

        return image, label

    def _augment(self, image, label):
        """增强的数据增强 - 恢复更强的增强强度"""
        # 随机翻转 - 恢复50%概率
        if np.random.random() > 0.5:
            image = np.flip(image, axis=1).copy()
            label = np.flip(label, axis=0).copy()

        if np.random.random() > 0.5:
            image = np.flip(image, axis=2).copy()
            label = np.flip(label, axis=1).copy()

        if np.random.random() > 0.5:
            image = np.flip(image, axis=3).copy()
            label = np.flip(label, axis=2).copy()

        # 随机旋转（简单的90度旋转） - 恢复50%概率
        if np.random.random() > 0.5:
            k = np.random.randint(1, 4)
            image = np.rot90(image, k, axes=(2, 3)).copy()
            label = np.rot90(label, k, axes=(1, 2)).copy()

        # 随机强度变化 - 恢复更大的变化范围
        if np.random.random() > 0.5:
            factor = np.random.uniform(0.8, 1.2)
            image = image * factor

        # 添加高斯噪声
        if np.random.random() > 0.5:
            noise = np.random.normal(0, 0.1, image.shape)
            image = image + noise

        # 随机对比度调整
        if np.random.random() > 0.5:
            mean_val = np.mean(image)
            contrast_factor = np.random.uniform(0.8, 1.2)
            image = (image - mean_val) * contrast_factor + mean_val

        return image, label


def create_model(config):
    """创建3D-TransUNet模型"""
    print("创建模型...")

    # 模型参数
    model_params = {
        'is_masked_attn': True,
        'max_dec_layers': 3,
        'is_max_bottleneck_transformer': True,
        'vit_depth': config.vit_depth,
        'max_msda': '',
        'is_max_ms': True,
        'max_ms_idxs': [-4, -3, -2],
        'max_hidden_dim': config.max_hidden_dim,
        'mw': 1.0,
        'is_max_ds': config.deep_supervision,
        'is_masking': True,
        'is_max_hungarian': config.is_max_hungarian,
        'num_queries': config.num_queries,
        'is_max_cls': config.is_max_cls,
        'is_mhsa_float32': True,
        'is_vit_pretrain': config.use_pretrained_vit,  # 使用配置中的设置
        'vit_layer_scale': True,
        'decoder_layer_scale': True
    }

    # 网络参数
    norm_op_kwargs = {'eps': 1e-5, 'affine': True}
    dropout_op_kwargs = {'p': 0.2, 'inplace': True}  # 增加dropout：从0.1改为0.2
    net_nonlin = nn.LeakyReLU
    net_nonlin_kwargs = {'negative_slope': 1e-2, 'inplace': True}

    # 池化和卷积核大小（根据patch_size调整）
    num_pool = 5
    pool_op_kernel_sizes = [[2, 2, 2]] * num_pool
    conv_kernel_sizes = [[3, 3, 3]] * (num_pool + 1)

    # 创建模型
    model = Generic_TransUNet_max_ppbp(
        input_channels=config.input_channels,
        base_num_features=config.base_num_features,
        num_classes=config.num_classes,
        num_pool=num_pool,
        num_conv_per_stage=2,
        feat_map_mul_on_downscale=2,
        conv_op=nn.Conv3d,
        norm_op=nn.InstanceNorm3d,
        norm_op_kwargs=norm_op_kwargs,
        dropout_op=nn.Dropout3d,
        dropout_op_kwargs=dropout_op_kwargs,
        nonlin=net_nonlin,
        nonlin_kwargs=net_nonlin_kwargs,
        deep_supervision=config.deep_supervision,
        dropout_in_localization=False,
        final_nonlin=lambda x: x,  # 不使用softmax，在损失函数中处理
        weightInitializer=InitWeights_He(1e-2),
        pool_op_kernel_sizes=pool_op_kernel_sizes,
        conv_kernel_sizes=conv_kernel_sizes,
        upscale_logits=False,
        convolutional_pooling=False,
        convolutional_upsampling=True,
        max_num_features=320,
        patch_size=config.patch_size,
        **model_params
    )

    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"模型总参数数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")

    return model


def calculate_dice_score(pred, target, num_classes):
    """计算Dice系数"""
    dice_scores = []

    for class_idx in range(1, num_classes):  # 跳过背景类
        pred_class = (pred == class_idx).float()
        target_class = (target == class_idx).float()

        intersection = (pred_class * target_class).sum()
        union = pred_class.sum() + target_class.sum()

        if union == 0:
            dice = 1.0  # 如果该类别在预测和真实标签中都不存在
        else:
            dice = (2.0 * intersection) / union

        dice_scores.append(dice.item() if hasattr(dice, 'item') else dice)

    return dice_scores


def validate_model(model, val_loader, criterion, device, num_classes):
    """验证模型"""
    model.eval()
    total_loss = 0.0
    all_dice_scores = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(val_loader, desc="验证中")):
            data = batch['data'].to(device)
            target = batch['target'].to(device)

            # 前向传播
            with autocast():
                output = model(data)

                # 处理3D-TransUNet的输出格式
                if isinstance(output, (list, tuple)) and len(output) > 0 and isinstance(output[0], dict):
                    # 3D-TransUNet输出：[dict, tensor1, tensor2, ...]
                    main_dict = output[0]
                    main_output = main_dict['pred_masks']

                    # 计算主要损失
                    loss = criterion(main_output, target)

                    # 添加深度监督损失
                    for i, ds_output in enumerate(output[1:], 1):
                        # 需要将深度监督输出上采样到主输出大小
                        # main_output shape: [B, C, D, H, W], ds_output shape: [B, C, D', H', W']
                        main_spatial = main_output.shape[2:]  # [D, H, W]
                        if ds_output.shape[2:] != main_spatial:
                            ds_output = torch.nn.functional.interpolate(
                                ds_output, size=main_spatial, mode='trilinear', align_corners=False
                            )
                        loss += criterion(ds_output, target) * (0.5 ** i)

                    output = main_output  # 用于评估
                elif isinstance(output, dict):
                    # 纯字典输出
                    main_output = output['pred_masks']
                    loss = criterion(main_output, target)
                    output = main_output
                elif isinstance(output, (list, tuple)):
                    # 纯张量列表输出
                    loss = 0
                    for i, o in enumerate(output):
                        loss += criterion(o, target) * (0.5 ** i)
                    output = output[0]
                else:
                    # 单个张量输出
                    loss = criterion(output, target)

            total_loss += loss.item()

            # 计算Dice系数
            pred = torch.argmax(output, dim=1)
            for i in range(pred.shape[0]):
                dice_scores = calculate_dice_score(pred[i], target[i], num_classes)
                all_dice_scores.append(dice_scores)

    # 计算平均指标
    avg_loss = total_loss / len(val_loader)

    if all_dice_scores:
        all_dice_scores = np.array(all_dice_scores)
        mean_dice_scores = np.mean(all_dice_scores, axis=0)
        overall_dice = np.mean(mean_dice_scores)
    else:
        mean_dice_scores = [0.0] * (num_classes - 1)
        overall_dice = 0.0

    return avg_loss, overall_dice, mean_dice_scores


def train_epoch(model, train_loader, criterion, optimizer, scaler, device, epoch, num_classes, config):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    all_dice_scores = []

    pbar = tqdm(train_loader, desc=f"Epoch {epoch}")
    for batch_idx, batch in enumerate(pbar):
        data = batch['data'].to(device)
        target = batch['target'].to(device)

        # 梯度累积：只在第一个batch或累积完成后清零梯度
        if batch_idx % config.gradient_accumulation_steps == 0:
            optimizer.zero_grad()

        # 前向传播
        with autocast():
            output = model(data)

            # 处理3D-TransUNet的输出格式
            if isinstance(output, (list, tuple)) and len(output) > 0 and isinstance(output[0], dict):
                # 3D-TransUNet输出：[dict, tensor1, tensor2, ...]
                main_dict = output[0]
                main_output = main_dict['pred_masks']  # [B, num_queries, D, H, W]

                # 对于简化，我们将pred_masks转换为类别预测
                # 这里我们假设前num_classes个查询对应不同的类别
                if main_output.shape[1] >= config.num_classes:
                    # 取前num_classes个查询作为类别预测
                    class_output = main_output[:, :config.num_classes, :, :, :]  # [B, num_classes, D, H, W]
                else:
                    # 如果查询数少于类别数，进行填充
                    padding = torch.zeros(
                        main_output.shape[0], config.num_classes - main_output.shape[1],
                        *main_output.shape[2:], device=main_output.device
                    )
                    class_output = torch.cat([main_output, padding], dim=1)

                # 计算主要损失
                loss = criterion(class_output, target)

                # 添加深度监督损失
                for i, ds_output in enumerate(output[1:], 1):
                    # 需要将深度监督输出上采样到class_output大小
                    class_spatial = class_output.shape[2:]  # [D, H, W]
                    if ds_output.shape[2:] != class_spatial:
                        ds_output = torch.nn.functional.interpolate(
                            ds_output, size=class_spatial, mode='trilinear', align_corners=False
                        )
                    loss += criterion(ds_output, target) * (0.5 ** i)

                output = class_output  # 用于评估
            elif isinstance(output, dict):
                # 纯字典输出
                main_output = output['pred_masks']
                loss = criterion(main_output, target)
                output = main_output
            elif isinstance(output, (list, tuple)):
                # 纯张量列表输出
                loss = 0
                for i, o in enumerate(output):
                    loss += criterion(o, target) * (0.5 ** i)
                output = output[0]
            else:
                # 单个张量输出
                loss = criterion(output, target)

        # 反向传播（支持梯度累积）
        loss = loss / config.gradient_accumulation_steps  # 缩放损失
        scaler.scale(loss).backward()

        # 每gradient_accumulation_steps步或最后一个batch时更新参数
        if (batch_idx + 1) % config.gradient_accumulation_steps == 0 or batch_idx == len(train_loader) - 1:
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()
        else:
            # 不清零梯度，继续累积
            pass

        total_loss += loss.item()

        # 计算Dice系数
        with torch.no_grad():
            pred = torch.argmax(output, dim=1)
            for i in range(pred.shape[0]):
                dice_scores = calculate_dice_score(pred[i], target[i], num_classes)
                all_dice_scores.append(dice_scores)

        # 更新进度条
        if batch_idx % 10 == 0:
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Avg Loss': f'{total_loss / (batch_idx + 1):.4f}'
            })

    # 计算平均指标
    avg_loss = total_loss / len(train_loader)

    if all_dice_scores:
        all_dice_scores = np.array(all_dice_scores)
        mean_dice_scores = np.mean(all_dice_scores, axis=0)
        overall_dice = np.mean(mean_dice_scores)
    else:
        mean_dice_scores = [0.0] * (num_classes - 1)
        overall_dice = 0.0

    return avg_loss, overall_dice, mean_dice_scores


def save_checkpoint(model, optimizer, scaler, epoch, loss, dice, config, filename):
    """保存模型检查点 - 优化版本，减少存储空间"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scaler_state_dict': scaler.state_dict(),
        'loss': loss,
        'dice': dice,
        'config': config.__dict__
    }

    checkpoint_path = os.path.join(config.output_dir, "checkpoints", filename)

    # 如果是保存latest.pth，先删除旧的以节省空间
    if filename == "latest.pth" and os.path.exists(checkpoint_path):
        os.remove(checkpoint_path)

    torch.save(checkpoint, checkpoint_path)
    print(f"模型已保存到: {checkpoint_path}")


def load_checkpoint(model, optimizer, scaler, checkpoint_path):
    """加载模型检查点"""
    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    scaler.load_state_dict(checkpoint['scaler_state_dict'])

    return checkpoint['epoch'], checkpoint['loss'], checkpoint['dice']


class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=15, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None

    def __call__(self, val_score, model):
        if self.best_score is None:
            self.best_score = val_score
            self.save_checkpoint(model)
        elif val_score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                if self.restore_best_weights:
                    model.load_state_dict(self.best_weights)
                return True
        else:
            self.best_score = val_score
            self.counter = 0
            self.save_checkpoint(model)
        return False

    def save_checkpoint(self, model):
        """保存最佳模型权重"""
        self.best_weights = model.state_dict().copy()


def save_class_metrics(config, epoch, train_dice_per_class, val_dice_per_class=None):
    """保存每个类别的Dice分数"""
    class_metrics_file = os.path.join(config.output_dir, "class_metrics.csv")

    # 如果文件不存在，创建并写入表头
    if not os.path.exists(class_metrics_file):
        with open(class_metrics_file, 'w') as f:
            header = "epoch"
            for i in range(1, config.num_classes):  # 跳过背景类
                header += f",train_dice_class{i}"
            if val_dice_per_class is not None:
                for i in range(1, config.num_classes):
                    header += f",val_dice_class{i}"
            f.write(header + "\n")

    # 写入当前epoch的数据
    with open(class_metrics_file, 'a') as f:
        line = f"{epoch+1}"
        for dice in train_dice_per_class:
            line += f",{dice:.6f}"
        if val_dice_per_class is not None:
            for dice in val_dice_per_class:
                line += f",{dice:.6f}"
        f.write(line + "\n")


def train_model(config):
    """主训练函数"""
    print("开始训练...")

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 创建数据加载器
    train_dataset = CustomDataset(
        os.path.join(config.preprocessed_data_dir, 'train'),
        config.patch_size,
        do_augmentation=config.do_data_augmentation
    )

    val_dataset = CustomDataset(
        os.path.join(config.preprocessed_data_dir, 'val'),
        config.patch_size,
        do_augmentation=False
    )

    # 创建CSV文件保存训练指标
    metrics_file = os.path.join(config.output_dir, "training_metrics.csv")
    with open(metrics_file, 'w') as f:
        f.write("epoch,train_loss,train_dice,val_loss,val_dice,learning_rate\n")

    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=8,
        pin_memory=True
    )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # 创建模型
    model = create_model(config)
    model = model.to(device)

    # 创建损失函数
    criterion = DC_and_CE_loss(
        soft_dice_kwargs={'batch_dice': True, 'smooth': 1e-5, 'do_bg': False},
        ce_kwargs={'weight': None},
        aggregate="sum"
    )

    # 创建优化器 - 使用AdamW，符合原始3D-TransUNet配置
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config.initial_lr,
        weight_decay=config.weight_decay,
        betas=(0.9, 0.999),
        eps=1e-8
    )

    # 学习率调度器 - 使用StepLR更保守的策略
    from torch.optim.lr_scheduler import StepLR

    scheduler = StepLR(
        optimizer,
        step_size=15,  # 每15个epoch降低学习率
        gamma=0.5      # 学习率衰减因子
    )

    # 混合精度训练
    scaler = GradScaler()

    # TensorBoard日志
    writer = SummaryWriter(os.path.join(config.output_dir, "logs"))

    # 训练历史
    train_losses = []
    train_dice_scores = []
    val_losses = []
    val_dice_scores = []

    best_dice = 0.0
    start_epoch = 0

    # 初始化早停机制
    early_stopping = EarlyStopping(
        patience=config.early_stopping_patience,
        min_delta=config.early_stopping_min_delta,
        restore_best_weights=True
    )

    # 检查是否有预训练模型
    latest_checkpoint = os.path.join(config.output_dir, "checkpoints", "latest.pth")
    if os.path.exists(latest_checkpoint):
        print("发现预训练模型，继续训练...")
        start_epoch, _, best_dice = load_checkpoint(model, optimizer, scaler, latest_checkpoint)
        start_epoch += 1

    # 训练循环
    for epoch in range(start_epoch, config.num_epochs):
        print(f"\nEpoch {epoch + 1}/{config.num_epochs}")
        print("-" * 50)

        # 训练
        train_loss, train_dice, train_dice_per_class = train_epoch(
            model, train_loader, criterion, optimizer, scaler, device, epoch + 1, config.num_classes, config
        )

        train_losses.append(train_loss)
        train_dice_scores.append(train_dice)

        # 验证
        if (epoch + 1) % config.validation_interval == 0:
            val_loss, val_dice, val_dice_per_class = validate_model(
                model, val_loader, criterion, device, config.num_classes
            )

            val_losses.append(val_loss)
            val_dice_scores.append(val_dice)

            print(f"训练 - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}")
            print(f"验证 - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}")

            # 记录到TensorBoard
            writer.add_scalar('Loss/Train', train_loss, epoch)
            writer.add_scalar('Loss/Val', val_loss, epoch)
            writer.add_scalar('Dice/Train', train_dice, epoch)
            writer.add_scalar('Dice/Val', val_dice, epoch)
            writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], epoch)

            # 保存指标到CSV文件
            with open(os.path.join(config.output_dir, "training_metrics.csv"), 'a') as f:
                f.write(f"{epoch+1},{train_loss:.6f},{train_dice:.6f},{val_loss:.6f},{val_dice:.6f},{optimizer.param_groups[0]['lr']:.8f}\n")

            # 保存每个类别的Dice分数
            save_class_metrics(config, epoch, train_dice_per_class, val_dice_per_class)

            # 保存最佳模型
            if val_dice > best_dice:
                best_dice = val_dice
                save_checkpoint(
                    model, optimizer, scaler, epoch, val_loss, val_dice, config, "best.pth"
                )
                print(f"新的最佳模型! Dice: {best_dice:.4f}")

            # 早停检查
            if early_stopping(val_dice, model):
                print(f"早停触发！在第 {epoch + 1} 个epoch停止训练")
                print(f"最佳验证Dice: {early_stopping.best_score:.4f}")
                break

        else:
            print(f"训练 - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}")
            writer.add_scalar('Loss/Train', train_loss, epoch)
            writer.add_scalar('Dice/Train', train_dice, epoch)
            writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], epoch)

            # 保存指标到CSV文件（无验证数据）
            with open(os.path.join(config.output_dir, "training_metrics.csv"), 'a') as f:
                f.write(f"{epoch+1},{train_loss:.6f},{train_dice:.6f},,,{optimizer.param_groups[0]['lr']:.8f}\n")

            # 仅保存训练的每个类别Dice分数
            save_class_metrics(config, epoch, train_dice_per_class)

        # 更新学习率
        scheduler.step()

        # 不再定期保存模型，只保存最佳和最新模型以节省空间
        # if (epoch + 1) % config.save_interval == 0:
        #     save_checkpoint(
        #         model, optimizer, scaler, epoch, train_loss, train_dice, config, f"epoch_{epoch + 1}.pth"
        #     )

        # 保存最新模型
        save_checkpoint(
            model, optimizer, scaler, epoch, train_loss, train_dice, config, "latest.pth"
        )

    # 不保存最终模型，因为latest.pth已经是最新的
    # save_checkpoint(
    #     model, optimizer, scaler, config.num_epochs - 1, train_loss, train_dice, config, "final.pth"
    # )

    # 关闭TensorBoard
    writer.close()

    # 绘制训练曲线
    plot_training_curves(train_losses, train_dice_scores, val_losses, val_dice_scores, config)

    # 保存训练摘要
    summary_file = os.path.join(config.output_dir, "training_summary.txt")
    with open(summary_file, 'w') as f:
        f.write(f"训练完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总训练轮数: {config.num_epochs}\n")
        f.write(f"最佳Dice系数: {best_dice:.6f}\n")
        f.write(f"最终训练Loss: {train_loss:.6f}\n")
        f.write(f"最终训练Dice: {train_dice:.6f}\n")
        if val_losses:
            f.write(f"最终验证Loss: {val_losses[-1]:.6f}\n")
            f.write(f"最终验证Dice: {val_dice_scores[-1]:.6f}\n")
        f.write("\n模型配置:\n")
        for key, value in config.__dict__.items():
            f.write(f"{key}: {value}\n")

    print("训练完成!")
    print(f"最佳Dice系数: {best_dice:.4f}")
    print(f"训练指标已保存到: {metrics_file}")
    print(f"训练摘要已保存到: {summary_file}")

    return model


def plot_training_curves(train_losses, train_dice_scores, val_losses, val_dice_scores, config):
    """绘制训练曲线"""
    plt.figure(figsize=(15, 5))

    # 损失曲线
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='训练损失', color='blue')
    if val_losses:
        val_epochs = [i * config.validation_interval for i in range(len(val_losses))]
        plt.plot(val_epochs, val_losses, label='验证损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('训练和验证损失')
    plt.legend()
    plt.grid(True)

    # Dice系数曲线
    plt.subplot(1, 3, 2)
    plt.plot(train_dice_scores, label='训练Dice', color='blue')
    if val_dice_scores:
        val_epochs = [i * config.validation_interval for i in range(len(val_dice_scores))]
        plt.plot(val_epochs, val_dice_scores, label='验证Dice', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Dice Score')
    plt.title('训练和验证Dice系数')
    plt.legend()
    plt.grid(True)

    # 学习率曲线（warmup_cosine调度估算）
    plt.subplot(1, 3, 3)
    epochs = range(len(train_losses))
    warmup_epochs = 5
    lrs = []
    for epoch in epochs:
        if epoch < warmup_epochs:
            # 线性预热阶段
            lr = config.initial_lr * epoch / warmup_epochs
        else:
            # 余弦退火阶段
            import math
            progress = (epoch - warmup_epochs) / (config.num_epochs - warmup_epochs)
            lr = 1e-6 + 0.5 * (config.initial_lr - 1e-6) * (1 + math.cos(math.pi * progress))
        lrs.append(lr)
    plt.plot(epochs, lrs, label='学习率 (Warmup+Cosine)', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('Learning Rate')
    plt.title('学习率变化 (Warmup+Cosine)')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(config.output_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"训练曲线已保存到: {os.path.join(config.output_dir, 'training_curves.png')}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='3D-TransUNet 训练脚本')
    parser.add_argument('--image_dir', type=str, default='/root/autodl-tmp/320HCC/image/ap',
                        help='图像目录')
    parser.add_argument('--mask_dir', type=str, default='/root/autodl-tmp/320HCC/mask/ap',
                        help='mask目录')
    parser.add_argument('--output_dir', type=str, default='/root/autodl-tmp/output',
                        help='输出目录')
    parser.add_argument('--num_classes', type=int, default=2,
                        help='类别数量（包括背景）')
    parser.add_argument('--batch_size', type=int, default=2,
                        help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=100,
                        help='训练轮数')
    parser.add_argument('--initial_lr', type=float, default=0.0001,
                        help='初始学习率')
    parser.add_argument('--patch_size', nargs=3, type=int, default=[64, 160, 160],
                        help='补丁大小 [D, H, W]')
    parser.add_argument('--preprocess_only', action='store_true',
                        help='仅进行数据预处理')
    parser.add_argument('--resume_training', action='store_true',
                        help='继续训练')
    parser.add_argument('--validation_interval', type=int, default=2,
                        help='验证间隔（每N个epoch验证一次）')
    parser.add_argument('--quick_test', action='store_true',
                        help='快速测试模式，只处理少量数据')
    parser.add_argument('--parallel_workers', type=int, default=8,
                        help='并行处理的进程数')

    args = parser.parse_args()

    # 创建配置
    config = Config()
    config.image_dir = args.image_dir
    config.mask_dir = args.mask_dir
    config.output_dir = args.output_dir
    config.num_classes = args.num_classes
    config.batch_size = args.batch_size
    config.num_epochs = args.num_epochs
    config.initial_lr = args.initial_lr
    config.patch_size = args.patch_size

    # 创建输出目录
    setup_directories(config)

    print("3D-TransUNet 训练脚本")
    print("=" * 50)
    print(f"图像目录: {config.image_dir}")
    print(f"Mask目录: {config.mask_dir}")
    print(f"输出目录: {config.output_dir}")
    print(f"类别数量: {config.num_classes}")
    print(f"批次大小: {config.batch_size}")
    print(f"训练轮数: {config.num_epochs}")
    print(f"补丁大小: {config.patch_size}")
    print("=" * 50)

    # 检查数据目录
    if not os.path.exists(config.image_dir):
        print(f"错误: 图像目录不存在: {config.image_dir}")
        print("请确保图像目录存在并包含.nii.gz文件")
        return

    if not os.path.exists(config.mask_dir):
        print(f"错误: Mask目录不存在: {config.mask_dir}")
        print("请确保mask目录存在并包含.nii.gz文件")
        return

    # 数据预处理
    if not os.path.exists(os.path.join(config.preprocessed_data_dir, 'splits.pkl')) or args.quick_test:
        print("开始数据预处理...")
        config.quick_test = args.quick_test
        preprocess_dataset(config, use_parallel=True, num_workers=args.parallel_workers)
    else:
        print("发现预处理数据，跳过预处理步骤")

    if args.preprocess_only:
        print("仅预处理模式，程序结束")
        return

    # 开始训练
    try:
        model = train_model(config)
        print("训练成功完成!")

    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"训练过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()


# python 3d_transunet_train.py \
#     --raw_data_dir /path/to/raw/data \
#     --output_dir /path/to/output \
#     --num_classes 2 \
#     --batch_size 2 \
#     --num_epochs 200