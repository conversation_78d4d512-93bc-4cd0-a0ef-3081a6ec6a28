#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nnUNet数据预处理和模型训练脚本
基于nnUNet v2项目代码整合

功能:
1. 数据集准备和格式转换
2. 环境变量设置
3. 数据预处理和实验规划
4. 模型训练
5. 数据验证和错误处理

作者: 基于nnUNet项目代码整合
"""
# 确保已安装nnUNet: pip install nnunetv2
# 确保已安装依赖: pip install SimpleITK

import os
import sys
import json
import shutil
import subprocess
import logging
from pathlib import Path
from typing import Union, List, Dict, Optional, Tuple
import SimpleITK as sitk
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nnunet_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class nnUNetPreprocessingTraining:
    """nnUNet数据预处理和训练类"""
    
    def __init__(self, 
                 base_dir: str = "./nnUNet_workspace",
                 dataset_id: int = 100,
                 dataset_name: str = "CustomDataset"):
        """
        初始化nnUNet预处理和训练环境
        
        参数:
            base_dir: nnUNet工作目录
            dataset_id: 数据集ID (3位数字)
            dataset_name: 数据集名称
        """
        self.base_dir = Path(base_dir)
        self.dataset_id = dataset_id
        self.dataset_name = dataset_name
        self.dataset_folder_name = f"Dataset{dataset_id:03d}_{dataset_name}"
        
        # 创建nnUNet目录结构
        self.nnunet_raw = self.base_dir / "nnUNet_raw"
        self.nnunet_preprocessed = self.base_dir / "nnUNet_preprocessed"
        self.nnunet_results = self.base_dir / "nnUNet_results"
        
        self.dataset_path = self.nnunet_raw / self.dataset_folder_name
        
        self._setup_directories()
        self._setup_environment()
    
    def _setup_directories(self):
        """创建nnUNet目录结构"""
        directories = [
            self.nnunet_raw,
            self.nnunet_preprocessed,
            self.nnunet_results,
            self.dataset_path,
            self.dataset_path / "imagesTr",
            self.dataset_path / "labelsTr",
            self.dataset_path / "imagesTs",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
    
    def _setup_environment(self):
        """设置nnUNet环境变量"""
        env_vars = {
            'nnUNet_raw': str(self.nnunet_raw),
            'nnUNet_preprocessed': str(self.nnunet_preprocessed),
            'nnUNet_results': str(self.nnunet_results)
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"设置环境变量: {key}={value}")
    
    def prepare_data_from_folders(self,
                                  images_folder: str,
                                  masks_folder: str,
                                  test_images_folder: Optional[str] = None,
                                  train_ratio: float = 0.8,#使用全部数据集训练
                                  channel_names: Dict[str, str] = None,
                                  labels: Dict[str, int] = None):
        """
        从文件夹准备nnUNet格式的数据
        
        参数:
            images_folder: 原始图像文件夹路径
            masks_folder: 标签文件夹路径
            test_images_folder: 测试图像文件夹路径(可选)
            train_ratio: 训练集比例
            channel_names: 通道名称字典
            labels: 标签字典
        """
        logger.info("开始准备数据...")
        
        # 默认参数
        if channel_names is None:
            channel_names = {"0": "image"}
        if labels is None:
            labels = {"background": 0, "foreground": 1}
        
        # 获取所有图像文件
        image_files = self._get_nii_files(images_folder)
        mask_files = self._get_nii_files(masks_folder)
        
        logger.info(f"找到 {len(image_files)} 个图像文件")
        logger.info(f"找到 {len(mask_files)} 个标签文件")
        
        # 匹配图像和标签文件
        matched_pairs = self._match_image_mask_pairs(image_files, mask_files)
        logger.info(f"匹配到 {len(matched_pairs)} 对图像-标签文件")
        
        # 划分训练集和测试集
        if test_images_folder is None:
            train_pairs, test_pairs = self._split_train_test(matched_pairs, train_ratio)
        else:
            train_pairs = matched_pairs
            test_image_files = self._get_nii_files(test_images_folder)
            test_pairs = [(f, None) for f in test_image_files]
        
        # 复制训练数据
        self._copy_training_data(train_pairs)
        
        # 复制测试数据
        if test_pairs:
            self._copy_test_data(test_pairs)
        
        # 创建dataset.json
        self._create_dataset_json(len(train_pairs), channel_names, labels)
        
        logger.info("数据准备完成!")
    
    def _get_nii_files(self, folder: str) -> List[str]:
        """获取文件夹中的所有.nii.gz文件"""
        folder_path = Path(folder)
        return sorted([str(f) for f in folder_path.glob("*.nii.gz")])
    
    def _match_image_mask_pairs(self, image_files: List[str], mask_files: List[str]) -> List[Tuple[str, str]]:
        """匹配图像和标签文件对，支持HCC数据集命名模式"""
        pairs = []

        # 创建标签文件的映射字典，便于快速查找
        mask_dict = {}
        for mask_file in mask_files:
            mask_path = Path(mask_file)
            mask_name = mask_path.stem.replace('.nii', '')

            # 去掉可能的mask后缀，得到基础名称
            base_name = mask_name
            if base_name.endswith('-mask'):
                base_name = base_name[:-5]
            elif base_name.endswith('_mask'):
                base_name = base_name[:-5]

            mask_dict[base_name] = mask_file

        for img_file in image_files:
            img_path = Path(img_file)
            img_name = img_path.stem.replace('.nii', '')

            # 查找对应的标签文件
            mask_file = None

            # 直接匹配基础名称
            if img_name in mask_dict:
                mask_file = mask_dict[img_name]
            else:
                # 尝试其他匹配模式
                possible_base_names = [
                    img_name,
                    img_name.replace('-ap', '').replace('-pp', '').replace('-hbp', ''),  # 去掉序列后缀
                ]

                for base_name in possible_base_names:
                    if base_name in mask_dict:
                        mask_file = mask_dict[base_name]
                        break

            if mask_file:
                pairs.append((img_file, mask_file))
                logger.info(f"匹配成功: {img_path.name} <-> {Path(mask_file).name}")
            else:
                logger.warning(f"未找到图像 {img_path.name} 对应的标签文件")
                logger.info(f"可用的标签文件基础名称: {list(mask_dict.keys())}")

        return pairs
    
    def _split_train_test(self, pairs: List[Tuple[str, str]], train_ratio: float) -> Tuple[List, List]:
        """划分训练集和测试集"""
        import random
        random.seed(42)
        
        shuffled_pairs = pairs.copy()
        random.shuffle(shuffled_pairs)
        
        split_idx = int(len(shuffled_pairs) * train_ratio)
        train_pairs = shuffled_pairs[:split_idx]
        test_pairs = shuffled_pairs[split_idx:]
        
        return train_pairs, test_pairs
    
    def _copy_training_data(self, train_pairs: List[Tuple[str, str]]):
        """复制训练数据到nnUNet格式，保持原始文件名"""
        logger.info("复制训练数据...")

        for i, (img_file, mask_file) in enumerate(train_pairs):
            # 保持原始文件名，只去掉路径
            img_path = Path(img_file)
            mask_path = Path(mask_file)

            # 获取原始文件名（不包含扩展名）
            img_base_name = img_path.stem.replace('.nii', '')  # 去掉.nii.gz中的.nii
            mask_base_name = mask_path.stem.replace('.nii', '')

            # 去掉标签文件名中的-mask后缀
            if mask_base_name.endswith('-mask'):
                mask_base_name = mask_base_name[:-5]  # 去掉'-mask'
            elif mask_base_name.endswith('_mask'):
                mask_base_name = mask_base_name[:-5]  # 去掉'_mask'

            # 复制图像文件 (添加_0000后缀)
            img_dst = self.dataset_path / "imagesTr" / f"{img_base_name}_0000.nii.gz"
            shutil.copy2(img_file, img_dst)

            # 复制标签文件（不添加_0000后缀）
            mask_dst = self.dataset_path / "labelsTr" / f"{mask_base_name}.nii.gz"
            shutil.copy2(mask_file, mask_dst)

            logger.info(f"复制训练数据 {i+1}/{len(train_pairs)}: {img_base_name} -> {mask_base_name}")

    def _copy_test_data(self, test_pairs: List[Tuple[str, Optional[str]]]):
        """复制测试数据到nnUNet格式，保持原始文件名"""
        logger.info("复制测试数据...")

        for i, (img_file, _) in enumerate(test_pairs):
            img_path = Path(img_file)
            # 获取原始文件名（不包含扩展名）
            img_base_name = img_path.stem.replace('.nii', '')

            img_dst = self.dataset_path / "imagesTs" / f"{img_base_name}_0000.nii.gz"
            shutil.copy2(img_file, img_dst)

            logger.info(f"复制测试数据 {i+1}/{len(test_pairs)}: {img_base_name}")
    
    def _create_dataset_json(self, num_training: int, channel_names: Dict, labels: Dict):
        """创建dataset.json文件"""
        dataset_json = {
            "channel_names": channel_names,
            "labels": labels,
            "numTraining": num_training,
            "file_ending": ".nii.gz",
            "overwrite_image_reader_writer": "SimpleITKIO"
        }
        
        json_path = self.dataset_path / "dataset.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_json, f, indent=4, ensure_ascii=False)
        
        logger.info(f"创建dataset.json: {json_path}")
        logger.info(f"数据集信息: {dataset_json}")

    def validate_and_fix_data(self):
        """验证和修复数据集中的图像和标签"""
        logger.info("验证和修复数据集...")

        images_dir = self.dataset_path / "imagesTr"
        labels_dir = self.dataset_path / "labelsTr"

        image_files = sorted(list(images_dir.glob("*.nii.gz")))
        label_files = sorted(list(labels_dir.glob("*.nii.gz")))

        for img_file, label_file in zip(image_files, label_files):
            try:
                # 检查文件大小
                if img_file.stat().st_size == 0 or label_file.stat().st_size == 0:
                    logger.warning(f"文件大小为0: {img_file} 或 {label_file}")
                    continue

                # 读取图像和标签
                image = sitk.ReadImage(str(img_file))
                label = sitk.ReadImage(str(label_file))

                # 检查方向、原点和间距是否一致
                if (image.GetDirection() != label.GetDirection() or
                    image.GetOrigin() != label.GetOrigin() or
                    image.GetSpacing() != label.GetSpacing()):

                    logger.info(f"修复标签文件: {label_file.name}")

                    # 使用图像作为参考对标签进行重采样
                    transform = sitk.Transform()
                    resampled_label = sitk.Resample(
                        label,
                        image,
                        transform,
                        sitk.sitkNearestNeighbor,
                        0,
                        label.GetPixelID()
                    )

                    # 保存重采样后的标签
                    sitk.WriteImage(resampled_label, str(label_file))

                logger.info(f"验证通过: {img_file.name} - 形状: {image.GetSize()}")

            except Exception as e:
                logger.error(f"处理文件时出错 {img_file}: {e}")
                continue

    def run_preprocessing(self, verify_dataset_integrity: bool = True, num_processes: int = 8):
        """运行nnUNet预处理"""
        logger.info("开始nnUNet预处理...")

        try:
            # 构建命令 - 使用正确的参数格式
            cmd = [
                "nnUNetv2_plan_and_preprocess",
                "-d", str(self.dataset_id),
                "-c", "3d_fullres"  # 只预处理3d_fullres配置 2d, 3d_fullres, 3d_lowres
            ]

            if verify_dataset_integrity:
                cmd.append("--verify_dataset_integrity")

            # 只为3d_fullres配置设置进程数
            cmd.extend(["-np", str(num_processes)])

            # 获取配置信息用于显示
            config_info = "3d_fullres"  # 当前只处理3d_fullres配置

            logger.info(f"执行命令: {' '.join(cmd)}")
            logger.info(f"预处理配置: {config_info}")
            logger.info("预处理过程中将显示实时进度...")

            # 执行命令并实时显示输出
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 实时读取并显示输出
            last_progress_line = ""
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_line = output.strip()
                    if output_line:
                        # 检查是否是进度条
                        if '%|' in output_line and '/it]' in output_line:
                            # 实时显示进度条，包含配置信息
                            print(f"\r[{config_info}预处理] {output_line}", end='', flush=True)
                            last_progress_line = output_line
                        # 显示其他重要信息（非进度条）
                        elif any(keyword in output_line.lower() for keyword in ['done', 'finished', 'completed', 'error', 'warning']):
                            # 如果之前有进度条，先换行
                            if last_progress_line:
                                print()  # 换行
                            print(f"[{config_info}预处理] {output_line}")
                            logger.info(f"预处理信息: {output_line}")

            # 确保最后的进度信息换行显示
            if last_progress_line:
                print()  # 换行
                logger.info(f"{config_info}预处理进度: {last_progress_line}")

            # 等待进程完成
            return_code = process.poll()
            if return_code != 0:
                raise subprocess.CalledProcessError(return_code, cmd)

            logger.info("预处理完成!")

        except subprocess.CalledProcessError as e:
            logger.error(f"预处理失败: {e}")
            raise
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_plan_and_preprocess命令，请确保已正确安装nnUNet")
            raise

    def train_model(self,
                    configuration: str = "3d_fullres",
                    fold: int = 0,
                    trainer_class: str = "nnUNetTrainer",
                    continue_training: bool = False,
                    num_gpus: int = 1,
                    num_epochs: Optional[int] = None):
        """训练nnUNet模型"""
        logger.info("开始模型训练...")

        try:
            # 根据训练轮数选择训练器
            if num_epochs is not None:
                epoch_trainers = {
                    1: "nnUNetTrainer_1epoch",
                    5: "nnUNetTrainer_5epochs",
                    10: "nnUNetTrainer_10epochs",
                    20: "nnUNetTrainer_20epochs",
                    50: "nnUNetTrainer_50epochs",
                    100: "nnUNetTrainer_100epochs",
                    250: "nnUNetTrainer_250epochs",
                    1000: "nnUNetTrainer_1000epochs"
                }
                trainer_class = epoch_trainers.get(num_epochs, trainer_class)
                logger.info(f"使用训练器: {trainer_class} (训练轮数: {num_epochs})")

            # 构建训练命令
            cmd = [
                "nnUNetv2_train",
                str(self.dataset_id),
                configuration,
                str(fold)
            ]

            # 添加可选参数
            if continue_training:
                cmd.append("--c")

            if trainer_class != "nnUNetTrainer":
                cmd.extend(["-tr", trainer_class])

            if num_gpus > 1:
                cmd.extend(["--num_gpus", str(num_gpus)])

            logger.info(f"执行训练命令: {' '.join(cmd)}")
            logger.info("训练过程中将显示详细参数...")

            # 设置环境变量强制输出到终端
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'  # 强制Python不缓冲输出
            env['TQDM_DISABLE'] = '0'      # 启用进度条

            # 启动训练进程 - 不重定向输出，让其直接显示到终端
            process = subprocess.Popen(
                cmd,
                env=env,
                bufsize=0,  # 无缓冲
                universal_newlines=True
            )

            print("� [训练启动] 训练已开始，训练参数将实时显示...")
            print("💡 [提示] 如果看不到训练参数，请等待模型编译完成（首次运行需要几分钟）")

            # 等待训练完成
            return_code = process.wait()
            if return_code != 0:
                raise subprocess.CalledProcessError(return_code, cmd)

            logger.info("模型训练完成!")

        except subprocess.CalledProcessError as e:
            logger.error(f"训练失败: {e}")
            raise
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_train命令，请确保已正确安装nnUNet")
            raise

    def find_best_configuration(self):
        """寻找最佳配置"""
        logger.info("寻找最佳配置...")

        try:
            cmd = ["nnUNetv2_find_best_configuration", str(self.dataset_id)]
            logger.info(f"执行命令: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("最佳配置搜索完成!")
            logger.info(f"结果: {result.stdout}")

        except subprocess.CalledProcessError as e:
            logger.error(f"寻找最佳配置失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_find_best_configuration命令")

    def run_full_pipeline(self,
                          images_folder: str,
                          masks_folder: str,
                          test_images_folder: Optional[str] = None,
                          configuration: str = "3d_fullres",
                          num_epochs: int = 100,
                          channel_names: Dict[str, str] = None,
                          labels: Dict[str, int] = None):
        """运行完整的预处理和训练流程"""
        logger.info("开始完整的nnUNet流程...")

        try:
            # 1. 准备数据
            self.prepare_data_from_folders(
                images_folder=images_folder,
                masks_folder=masks_folder,
                test_images_folder=test_images_folder,
                channel_names=channel_names,
                labels=labels
            )

            # 2. 验证和修复数据
            self.validate_and_fix_data()

            # 3. 运行预处理
            self.run_preprocessing()

            # 4. 训练模型
            self.train_model(
                configuration=configuration,
                num_epochs=num_epochs
            )

            # 5. 寻找最佳配置
            self.find_best_configuration()

            logger.info("完整流程执行完成!")

        except Exception as e:
            logger.error(f"流程执行失败: {e}")
            raise


def main():
    """主函数 - 演示nnUNet预处理和训练流程"""

    # 配置参数 - 请根据您的数据修改这些路径
    config = {
        # 数据路径配置
        "images_folder": "/root/autodl-tmp/120HCC/image/ap",
        "masks_folder": "/root/autodl-tmp/120HCC/mask/ap",
        "test_images_folder": None,  # 如果有单独的测试集，请指定路径

        # nnUNet配置
        "base_dir": "./nnUNet_workspace",
        "dataset_id": 120,
        "dataset_name": "HCC_Dataset",

        # 训练配置
        "configuration": "3d_fullres",  # 可选: "2d", "3d_fullres", "3d_lowres"
        "num_epochs": 100,  # 减少训练轮数用于测试
        "num_gpus": 1,

        # 数据集配置
        "channel_names": {"0": "ap"},  # 动脉期图像
        "labels": {"background": 0, "HCC": 1},
        "train_ratio": 0.8,

        # 控制选项
        "run_preprocessing": True,
        "run_training": True,
        "run_evaluation": True
    }

    try:
        # 检查CUDA可用性
        import torch
        if torch.cuda.is_available():
            logger.info(f"CUDA可用，GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            logger.warning("CUDA不可用，将使用CPU训练（速度较慢）")

        # 创建nnUNet训练实例
        trainer = nnUNetPreprocessingTraining(
            base_dir=config["base_dir"],
            dataset_id=config["dataset_id"],
            dataset_name=config["dataset_name"]
        )

        # 分步执行流程
        if config["run_preprocessing"]:
            logger.info("步骤1: 数据准备和预处理")
            # 1. 准备数据
            trainer.prepare_data_from_folders(
                images_folder=config["images_folder"],
                masks_folder=config["masks_folder"],
                test_images_folder=config["test_images_folder"],
                channel_names=config["channel_names"],
                labels=config["labels"]
            )

            # 2. 验证和修复数据
            trainer.validate_and_fix_data()

            # 3. 运行预处理
            trainer.run_preprocessing()
            logger.info("预处理完成!")

        if config["run_training"]:
            logger.info("步骤2: 模型训练")
            # 4. 训练模型
            trainer.train_model(
                configuration=config["configuration"],
                num_epochs=config["num_epochs"]
            )
            logger.info("训练完成!")

        # if config["run_evaluation"]:
        #     logger.info("步骤3: 寻找最佳配置")
        #     # 5. 寻找最佳配置
        #     trainer.find_best_configuration()
        #     logger.info("评估完成!")

        logger.info("=" * 50)
        logger.info("nnUNet预处理和训练完成!")
        logger.info("=" * 50)
        logger.info(f"模型保存位置: {trainer.nnunet_results}")
        logger.info(f"数据集ID: {config['dataset_id']}")
        logger.info("您现在可以使用训练好的模型进行预测")

    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

    return 0


if __name__ == "__main__":
    """
    使用说明:
    1. 修改main()函数中的config字典，设置您的数据路径
    2. 确保已安装nnUNet: pip install nnunetv2
    3. 确保已安装依赖: pip install SimpleITK
    4. 运行脚本: python nnunet_preprocessing_training.py

    数据格式要求:
    - 图像和标签都应该是.nii.gz格式
    - 图像和标签文件名应该能够匹配（支持-mask后缀）
    - 图像应该是3D医学图像格式
    """

    # 设置环境变量
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['OPENBLAS_NUM_THREADS'] = '1'

    exit_code = main()
    sys.exit(exit_code)
