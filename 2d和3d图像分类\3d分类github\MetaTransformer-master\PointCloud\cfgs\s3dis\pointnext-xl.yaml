# Model Information
# GFLOPs  Params.(M)
#  84.81    41.576
# Throughput (ins./s): 46.06739325463371 

model:
  NAME: BaseSeg
  encoder_args:
    NAME: PointNextEncoder
    blocks: [1, 4, 7, 4, 4] 
    strides: [1, 4, 4, 4, 4]
    sa_layers: 1
    sa_use_res: False 
    width: 64 
    in_channels: 4
    expansion: 4
    radius: 0.1
    nsample: 32 
    aggr_args:
      feature_type: 'dp_fj'
      reduction: 'max'
    group_args:
      NAME: 'ballquery'
      normalize_dp: True
    conv_args:
      order: conv-norm-act
    act_args:
      act: 'relu'
    norm_args:
      norm: 'bn'
  decoder_args:
    NAME: PointNextDecoder
  cls_args:
    NAME: SegHead
    num_classes: 13
    in_channels: null
    norm_args:
      norm: 'bn'

batch_size: 8