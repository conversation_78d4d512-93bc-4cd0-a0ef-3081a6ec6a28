{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# 3D classification example based on DenseNet\n", "\n", "This tutorial shows an example of 3D classification task based on DenseNet and array format transforms.\n", "\n", "Here, the task is given to classify MR images into male/female.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_classification/torch/densenet_training_array.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, tqdm]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 0.7.0+431.g1ba06454\n", "Numpy version: 1.21.2\n", "Pytorch version: 1.10.2\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False\n", "MONAI rev id: 1ba0645481afb95b10a7194f8fed82ce05b921bc\n", "MONAI __file__: /home/<USER>/workspace/monai/MONAI_mine/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.8\n", "Nibabel version: 3.2.1\n", "scikit-image version: 0.18.3\n", "Pillow version: 8.4.0\n", "Tensorboard version: 2.6.0\n", "gdown version: 4.2.1\n", "TorchVision version: 0.11.3\n", "tqdm version: 4.62.3\n", "lmdb version: 1.2.1\n", "psutil version: 5.8.0\n", "pandas version: 1.3.5\n", "einops version: 0.4.0\n", "transformers version: 4.14.1\n", "mlflow version: 1.23.1\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import logging\n", "import os\n", "import sys\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "import torch\n", "from torch.utils.tensorboard import SummaryWriter\n", "import numpy as np\n", "\n", "import monai\n", "from monai.apps import download_and_extract\n", "from monai.config import print_config\n", "from monai.data import DataLoader, ImageDataset\n", "from monai.transforms import (\n", "    EnsureChannelFirst,\n", "    <PERSON><PERSON><PERSON>,\n", "    RandRotate90,\n", "    Resize,\n", "    ScaleIntensity,\n", ")\n", "\n", "pin_memory = torch.cuda.is_available()\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "logging.basicConfig(stream=sys.stdout, level=logging.INFO)\n", "print_config()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/data/LargeArchiveStorage/IXI\n"]}], "source": ["# Set data directory\n", "directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# IXI dataset as a demo, downloadable from https://brain-development.org/ixi-dataset/\n", "images = [\n", "    os.sep.join([root_dir, \"ixi\", \"IXI314-IOP-0889-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI249-Guys-1072-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI609-HH-2600-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI173-HH-1590-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI020-Guys-0700-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI342-Guys-0909-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI134-Guys-0780-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI577-HH-2661-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI066-Guys-0731-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI130-HH-1528-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI607-Guys-1097-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI175-HH-1570-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI385-HH-2078-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI344-Guys-0905-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI409-Guys-0960-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI584-Guys-1129-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI253-HH-1694-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI092-HH-1436-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI574-IOP-1156-T1.nii.gz\"]),\n", "    os.sep.join([root_dir, \"ixi\", \"IXI585-Guys-1130-T1.nii.gz\"]),\n", "]\n", "\n", "# 2 binary labels for gender classification: man or woman\n", "labels = np.array([0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0])\n", "\n", "# Represent labels in one-hot format for binary classifier training,\n", "# BCEWithLogitsLoss requires target to have same shape as input\n", "labels = torch.nn.functional.one_hot(torch.as_tensor(labels)).float()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ixi.tar: 100%|██████████| 4.51G/4.51G [08:19<00:00, 9.70MB/s]   \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2022-05-04 12:23:06,530 - INFO - Downloaded: /mnt/data/rbrown/Documents/Data/MONAI/ixi.tar\n", "2022-05-04 12:23:13,734 - INFO - Verified 'ixi.tar', md5: 34901a0593b41dd19c1a1f746eac2d58.\n", "2022-05-04 12:23:13,735 - INFO - Writing into directory: /mnt/data/rbrown/Documents/Data/MONAI/ixi.\n"]}], "source": ["if not os.path.isfile(images[0]):\n", "    resource = \"http://biomedic.doc.ic.ac.uk/brain-development/downloads/IXI/IXI-T1.tar\"\n", "    md5 = \"34901a0593b41dd19c1a1f746eac2d58\"\n", "\n", "    dataset_dir = os.path.join(root_dir, \"ixi\")\n", "    tarfile_name = f\"{dataset_dir}.tar\"\n", "\n", "    download_and_extract(resource, tarfile_name, dataset_dir, md5)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'monai.data.meta_tensor.MetaTensor'> (3, 1, 96, 96, 96) tensor([[1., 0.],\n", "        [1., 0.],\n", "        [1., 0.]]) torch.<PERSON><PERSON>([3, 2])\n"]}], "source": ["# Define transforms\n", "train_transforms = Compose([ScaleIntensity(), EnsureChannelFirst(), Resize((96, 96, 96)), RandRotate90()])\n", "\n", "val_transforms = Compose([ScaleIntensity(), EnsureChannelFirst(), Resize((96, 96, 96))])\n", "\n", "# Define nifti dataset, data loader\n", "check_ds = ImageDataset(image_files=images, labels=labels, transform=train_transforms)\n", "check_loader = DataLoader(check_ds, batch_size=3, num_workers=2, pin_memory=pin_memory)\n", "\n", "im, label = monai.utils.misc.first(check_loader)\n", "print(type(im), im.shape, label, label.shape)\n", "\n", "# create a training data loader\n", "train_ds = ImageDataset(image_files=images[:10], labels=labels[:10], transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=2, pin_memory=pin_memory)\n", "\n", "# create a validation data loader\n", "val_ds = ImageDataset(image_files=images[-10:], labels=labels[-10:], transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=2, num_workers=2, pin_memory=pin_memory)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------\n", "epoch 1/5\n", "1/5, train_loss: 0.7230\n", "2/5, train_loss: 0.6376\n", "3/5, train_loss: 0.2891\n", "4/5, train_loss: 1.5737\n", "5/5, train_loss: 0.6099\n", "epoch 1 average loss: 0.7667\n", "----------\n", "epoch 2/5\n", "1/5, train_loss: 1.3489\n", "2/5, train_loss: 0.6097\n", "3/5, train_loss: 0.2268\n", "4/5, train_loss: 1.0190\n", "5/5, train_loss: 0.5579\n", "epoch 2 average loss: 0.7525\n", "saved new best metric model\n", "Current epoch: 2 current accuracy: 0.4000 \n", "Best accuracy: 0.4000 at epoch 2\n", "----------\n", "epoch 3/5\n", "1/5, train_loss: 0.4963\n", "2/5, train_loss: 0.4916\n", "3/5, train_loss: 0.4534\n", "4/5, train_loss: 0.6815\n", "5/5, train_loss: 0.8401\n", "epoch 3 average loss: 0.5926\n", "----------\n", "epoch 4/5\n", "1/5, train_loss: 0.4132\n", "2/5, train_loss: 0.8116\n", "3/5, train_loss: 0.4805\n", "4/5, train_loss: 0.5007\n", "5/5, train_loss: 0.3900\n", "epoch 4 average loss: 0.5192\n", "saved new best metric model\n", "Current epoch: 4 current accuracy: 0.7000 \n", "Best accuracy: 0.7000 at epoch 4\n", "----------\n", "epoch 5/5\n", "1/5, train_loss: 0.5056\n", "2/5, train_loss: 0.3694\n", "3/5, train_loss: 0.4057\n", "4/5, train_loss: 0.7161\n", "5/5, train_loss: 0.3352\n", "epoch 5 average loss: 0.4664\n", "Training completed, best_metric: 0.7000 at epoch: 4\n"]}], "source": ["# Create DenseNet121, CrossEntropyLoss and Adam optimizer\n", "model = monai.networks.nets.DenseNet121(spatial_dims=3, in_channels=1, out_channels=2).to(device)\n", "\n", "loss_function = torch.nn.CrossEntropyLoss()\n", "# loss_function = torch.nn.BCEWithLogitsLoss()  # also works with this data\n", "\n", "optimizer = torch.optim.Adam(model.parameters(), 1e-4)\n", "\n", "# start a typical PyTorch training\n", "val_interval = 2\n", "best_metric = -1\n", "best_metric_epoch = -1\n", "epoch_loss_values = []\n", "metric_values = []\n", "writer = SummaryWriter()\n", "max_epochs = 5\n", "\n", "for epoch in range(max_epochs):\n", "    print(\"-\" * 10)\n", "    print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "\n", "    for batch_data in train_loader:\n", "        step += 1\n", "        inputs, labels = batch_data[0].to(device), batch_data[1].to(device)\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = loss_function(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "        epoch_len = len(train_ds) // train_loader.batch_size\n", "        print(f\"{step}/{epoch_len}, train_loss: {loss.item():.4f}\")\n", "        writer.add_scalar(\"train_loss\", loss.item(), epoch_len * epoch + step)\n", "\n", "    epoch_loss /= step\n", "    epoch_loss_values.append(epoch_loss)\n", "    print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "\n", "        num_correct = 0.0\n", "        metric_count = 0\n", "        for val_data in val_loader:\n", "            val_images, val_labels = val_data[0].to(device), val_data[1].to(device)\n", "            with torch.no_grad():\n", "                val_outputs = model(val_images)\n", "                value = torch.eq(val_outputs.argmax(dim=1), val_labels.argmax(dim=1))\n", "                metric_count += len(value)\n", "                num_correct += value.sum().item()\n", "\n", "        metric = num_correct / metric_count\n", "        metric_values.append(metric)\n", "\n", "        if metric > best_metric:\n", "            best_metric = metric\n", "            best_metric_epoch = epoch + 1\n", "            torch.save(model.state_dict(), \"best_metric_model_classification3d_array.pth\")\n", "            print(\"saved new best metric model\")\n", "\n", "        print(f\"Current epoch: {epoch+1} current accuracy: {metric:.4f} \")\n", "        print(f\"Best accuracy: {best_metric:.4f} at epoch {best_metric_epoch}\")\n", "        writer.add_scalar(\"val_accuracy\", metric, epoch + 1)\n", "\n", "print(f\"Training completed, best_metric: {best_metric:.4f} at epoch: {best_metric_epoch}\")\n", "writer.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Occlusion sensitivity\n", "One method for trying to visualise why the network made a given prediction is occlusion sensitivity. We occlude part of the image, and see how the probability of a given prediction changes. We then iterate over the image, moving the occluded portion as we go, and in doing so we build up a sensitivity map detailing which areas were the most important in making the decision.\n", "\n", "#### Bounds\n", "If we were to test the occlusion centred on all voxels in our image, we would have to do `torch.prod(im.shape) = 96^3 = ~1e6` predictions. We can use the bounding box to only to the estimations in a region of interest, for example over one slice.\n", "\n", "To do this, we simply give the bounding box as `(minC,maxC,minD,maxD,minH,maxH,minW,maxW)`. We can use `-1` for any value to use its full extent (`0` and `im.shape-1` for min's and max's, respectively).\n", "\n", "#### Output\n", "The output image in this example will look fairly bad, since our network hasn't been trained for very long. Training for longer should improve the quality of the occlusion map."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# create a validation data loader\n", "test_ds = ImageDataset(image_files=images[-10:], labels=labels[-10:], transform=val_transforms)\n", "test_loader = DataLoader(val_ds, batch_size=1, num_workers=2, pin_memory=torch.cuda.is_available())\n", "itera = iter(test_loader)\n", "\n", "\n", "def get_next_im():\n", "    test_data = next(itera)\n", "    return test_data[0].to(device), test_data[1].unsqueeze(0).to(device)\n", "\n", "\n", "def plot_occlusion_heatmap(im, heatmap):\n", "    plt.subplots(1, 2)\n", "    plt.subplot(1, 2, 1)\n", "    plt.imshow(np.squeeze(im.cpu()))\n", "    plt.colorbar()\n", "    plt.subplot(1, 2, 2)\n", "    plt.imshow(heatmap)\n", "    plt.colorbar()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Get a random image and its corresponding label\n", "img, label = get_next_im()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Computing occlusion sensitivity: 100%|██████████| 64/64 [00:00<00:00, 100.49it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1080 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get the occlusion sensitivity map\n", "occ_sens = monai.visualize.OcclusionSensitivity(nn_module=model, mask_size=12, n_batch=10)\n", "# Only get a single slice to save time.\n", "# For the other dimensions (channel, width, height), use\n", "# -1 to use 0 and img.shape[x]-1 for min and max, respectively\n", "depth_slice = img.shape[2] // 2\n", "occ_sens_b_box = [depth_slice - 1, depth_slice, -1, -1, -1, -1]\n", "\n", "occ_result, _ = occ_sens(x=img, b_box=occ_sens_b_box)\n", "occ_result = occ_result[0, label.argmax().item()][None]\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(25, 15), facecolor=\"white\")\n", "\n", "for i, im in enumerate([img[:, :, depth_slice, ...], occ_result]):\n", "    cmap = \"gray\" if i == 0 else \"jet\"\n", "    ax = axes[i]\n", "    im_show = ax.imshow(np.squeeze(im[0][0].detach().cpu()), cmap=cmap)\n", "    ax.axis(\"off\")\n", "    fig.colorbar(im_show, ax=ax)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}