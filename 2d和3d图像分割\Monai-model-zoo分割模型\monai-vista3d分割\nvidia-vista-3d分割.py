
#%% Monai调用VISTA-3D分割
# https://github.com/Project-MONAI/VISTA
pip install monai
pip install "monai[fire]"
python -m monai.bundle download "vista3d" --bundle_dir "bundles/"

# #肝脏分割 教程原始代码有错
# export CUDA_VISIBLE_DEVICES=0; python -m scripts.infer --config_file 'configs/infer.yaml' - infer --image_file 'example-1.nii.gz' --label_prompt "[1]" --save_mask true
# #所有器官分割
# export CUDA_VISIBLE_DEVICES=0; python -m scripts.infer --config_file 'configs/infer.yaml' - infer_everything --image_file 'example-1.nii.gz'

# cd /root/autodl-tmp/bundles/vista3d   
# 修改inference.json中image_file路径和标签
"output_dir": "$@bundle_root + '/eval'",
"input_files": "$sorted(glob.glob('/root/autodl-tmp/bundles/vista3d/image/*.nii.gz'))",
"input_dicts": "$[{'image': f, 'label_prompt': [1]} for f in @input_files]",

 #肝脏分割
export CUDA_VISIBLE_DEVICES=0; python -m monai.bundle run --config_file configs/inference.json


#%% VISTA-3D分割，调用API， 需要申请API，很慢
# https://github.com/Project-MONAI/VISTA
import io
import os
import requests
import shutil
import tempfile
import zipfile

invoke_url = "https://health.api.nvidia.com/v1/medicalimaging/nvidia/vista-3d"

headers = {
    "Authorization": "Bearer $API_KEY_REQUIRED_IF_EXECUTING_OUTSIDE_NGC",
}

sample = "example-1"
payload = {
    "image": f"https://assets.ngc.nvidia.com/products/api-catalog/vista3d/{sample}.nii.gz",
    "prompts": {
        "classes": ["liver", "spleen"]
    }
}

# re-use connections
session = requests.Session()
response = session.post(invoke_url, headers=headers, json=payload)

response.raise_for_status()
with tempfile.TemporaryDirectory() as temp_dir:
    z = zipfile.ZipFile(io.BytesIO(response.content))
    z.extractall(temp_dir)
    file_list = os.listdir(temp_dir)
    for filename in file_list:
        filepath = os.path.join(temp_dir, filename)
        if os.path.isfile(filepath) and filename.endswith(".nrrd"):
            shutil.move(filepath, f"{sample}_seg.nrrd")

print("---------------------------------------------------------------")
print(f"Input Image: {payload['image']}")
print(f"Class Prompts: {payload.get('prompts', {}).get('classes')}")
print(f"Response mask: {sample}_seg.nrrd")
print("")

#%% 器官标签
{
    "liver": 1,
    "spleen": 3,
    "pancreas": 4,
    "right kidney": 5,
    "aorta": 6,
    "inferior vena cava": 7,
    "right adrenal gland": 8,
    "left adrenal gland": 9,
    "gallbladder": 10,
    "esophagus": 11,
    "stomach": 12,
    "duodenum": 13,
    "left kidney": 14,
    "bladder": 15,
    "portal vein and splenic vein": 17,
    "small bowel": 19,
    "brain": 22,
    "lung tumor": 23,
    "pancreatic tumor": 24,
    "hepatic vessel": 25,
    "hepatic tumor": 26,
    "colon cancer primaries": 27,
    "left lung upper lobe": 28,
    "left lung lower lobe": 29,
    "right lung upper lobe": 30,
    "right lung middle lobe": 31,
    "right lung lower lobe": 32,
    "vertebrae L5": 33,
    "vertebrae L4": 34,
    "vertebrae L3": 35,
    "vertebrae L2": 36,
    "vertebrae L1": 37,
    "vertebrae T12": 38,
    "vertebrae T11": 39,
    "vertebrae T10": 40,
    "vertebrae T9": 41,
    "vertebrae T8": 42,
    "vertebrae T7": 43,
    "vertebrae T6": 44,
    "vertebrae T5": 45,
    "vertebrae T4": 46,
    "vertebrae T3": 47,
    "vertebrae T2": 48,
    "vertebrae T1": 49,
    "vertebrae C7": 50,
    "vertebrae C6": 51,
    "vertebrae C5": 52,
    "vertebrae C4": 53,
    "vertebrae C3": 54,
    "vertebrae C2": 55,
    "vertebrae C1": 56,
    "trachea": 57,
    "left iliac artery": 58,
    "right iliac artery": 59,
    "left iliac vena": 60,
    "right iliac vena": 61,
    "colon": 62,
    "left rib 1": 63,
    "left rib 2": 64,
    "left rib 3": 65,
    "left rib 4": 66,
    "left rib 5": 67,
    "left rib 6": 68,
    "left rib 7": 69,
    "left rib 8": 70,
    "left rib 9": 71,
    "left rib 10": 72,
    "left rib 11": 73,
    "left rib 12": 74,
    "right rib 1": 75,
    "right rib 2": 76,
    "right rib 3": 77,
    "right rib 4": 78,
    "right rib 5": 79,
    "right rib 6": 80,
    "right rib 7": 81,
    "right rib 8": 82,
    "right rib 9": 83,
    "right rib 10": 84,
    "right rib 11": 85,
    "right rib 12": 86,
    "left humerus": 87,
    "right humerus": 88,
    "left scapula": 89,
    "right scapula": 90,
    "left clavicula": 91,
    "right clavicula": 92,
    "left femur": 93,
    "right femur": 94,
    "left hip": 95,
    "right hip": 96,
    "sacrum": 97,
    "left gluteus maximus": 98,
    "right gluteus maximus": 99,
    "left gluteus medius": 100,
    "right gluteus medius": 101,
    "left gluteus minimus": 102,
    "right gluteus minimus": 103,
    "left autochthon": 104,
    "right autochthon": 105,
    "left iliopsoas": 106,
    "right iliopsoas": 107,
    "left atrial appendage": 108,
    "brachiocephalic trunk": 109,
    "left brachiocephalic vein": 110,
    "right brachiocephalic vein": 111,
    "left common carotid artery": 112,
    "right common carotid artery": 113,
    "costal cartilages": 114,
    "heart": 115,
    "left kidney cyst": 116,
    "right kidney cyst": 117,
    "prostate": 118,
    "pulmonary vein": 119,
    "skull": 120,
    "spinal cord": 121,
    "sternum": 122,
    "left subclavian artery": 123,
    "right subclavian artery": 124,
    "superior vena cava": 125,
    "thyroid gland": 126,
    "vertebrae S1": 127,
    "bone lesion": 128,
    "airway": 132
}