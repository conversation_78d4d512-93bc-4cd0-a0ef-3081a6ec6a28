# automated_hybrid_IDH
This is public repository for "Fully Automated Hybrid Network to Predict IDH Mutation Status of Glioma via Deep Learning and Radiomics" by <PERSON> et al.
 The automated hybrid model consists of UNet-based Model1 for tumor segmentation, ResNet-based Model 2 for IDH status prediction, and automated processing pipeline inbetween. Model 2 integrates 2D MR images, radiomic features of 3D tumor shape & loci, and age in one CNN. The code to test a sample is avaialble as a jupyter notebook in 'Model_testing.ipynb'.
 
