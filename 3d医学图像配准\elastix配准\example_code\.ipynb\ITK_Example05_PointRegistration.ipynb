{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 5. Point Registration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Point-based registration allows us to help the registration via pre-defined sets of corresponding points. The 'CorrespondingPointsEuclideanDistanceMetric' minimises the distance of between a points on the fixed image and corresponding points on the moving image. The metric can be used to help in a difficult registration task by taking into account positions are known to correspond. Think of it as a way of embedding expert knowledge in the registration procedure. We can manually or automatically select points via centroids of segmentations for example. Anything is possible.\n", "Point sets should always be given to elastix with their corresponding image."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Registration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "from itkwidgets import compare, checkerboard, view"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In order for 3D registration to work with a point set, the 'CorrespondingPointsEuclideanDistanceMetric', should be set as metric. For the 3D case, this means that the metric should be a multimetric with the first metric of type AdvancedImageToImageMetric and the second the 'CorrespondingPointsEuclideanDistanceMetric'. The Registration parameter should therefore be set to 'MultiMetricMultiResolutionRegistration', to allow a multimetric parameter."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Import Images\n", "fixed_image = itk.imread('data/CT_3D_lung_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_3D_lung_moving.mha', itk.F)\n", "\n", "fixed_point_set = np.loadtxt('data/CT_3D_lung_fixed_point_set_corrected.txt', skiprows=2, delimiter=' ')\n", "moving_point_set = np.loadtxt('data/CT_3D_lung_moving_point_set_corrected.txt', skiprows=2, delimiter=' ')\n", "\n", "# Import and adjust Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_map_rigid = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_map_rigid['Registration'] = [\n", "    'MultiMetricMultiResolutionRegistration']\n", "original_metric = parameter_map_rigid['Metric']\n", "parameter_map_rigid['Metric'] = [original_metric[0],\n", "                                 'CorrespondingPointsEuclideanDistanceMetric']\n", "parameter_object.AddParameterMap(parameter_map_rigid)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The point sets can be visualized and analysed with the itkwidgets package. Currently the point set visualization layer is put on top of the 3D image, which makes the slice view show all 'passed' points as well, this will change in future versions of the itkwidgets. (Changing the point set size and the gradient opacity of the image aids the visualization of the point sets in the 3D image)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4b85bb6d559348c5affec2dcdfcf6a3b", "version_major": 2, "version_minor": 0}, "text/plain": ["Viewer(geometries=[], gradient_opacity=0.22, point_set_colors=array([[0.8392157, 0.       , 0.       ]], dtype…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["view(fixed_image, point_sets=[fixed_point_set])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bf24816ea3ec41b693d371fee1841a63", "version_major": 2, "version_minor": 0}, "text/plain": ["Viewer(geometries=[], gradient_opacity=0.22, point_set_colors=array([[0.8392157, 0.       , 0.       ]], dtype…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["view(moving_image, point_sets=[moving_point_set])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With regards to the elastix function, the point sets do not need to be initialized first, so their file name + path can be given directly to elastix. Future version of ITKElastix will support initialization of point sets and passing these to elastix, just like the point set was initialized for the visualization with the itkwidgets.\n", "\n", "Registration can either be done in one line with the registration function..."]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    fixed_point_set_file_name='data/CT_3D_lung_fixed_point_set_corrected.txt',\n", "    moving_point_set_file_name='data/CT_3D_lung_moving_point_set_corrected.txt',\n", "    log_to_console=False,\n", "    parameter_object=parameter_object)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an elastix image filter object."]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["# Load Elastix Image Filter Object\n", "# Fixed and moving image should be given to the Elastix method to ensure that\n", "# the correct 3D class is initialized.\n", "elastix_object = itk.ElastixRegistrationMethod.New(fixed_image,moving_image)\n", "elastix_object.SetFixedPointSetFileName('data/CT_3D_lung_fixed_point_set_corrected.txt')\n", "elastix_object.SetMovingPointSetFileName(\n", "    'data/CT_3D_lung_moving_point_set_corrected.txt')\n", "elastix_object.SetParameterObject(parameter_object)\n", "elastix_object.SetLogToConsole(False)\n", "\n", "# Update filter object (required)\n", "elastix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Registration\n", "result_image = elastix_object.GetOutput()\n", "result_transform_parameters = elastix_object.GetTransformParameterObject()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}