#%%elastix-napari配准软件版，可看配准效果，成功
# https://github.com/SuperElastix/elastix-napari
# pip install elastix-napari
napari     #终端调用

#%% elastix配准 最终版
# The current SimpleElastix repository (https://github.com/SuperElastix/SimpleElastix),
# SimpleElastix has now become a part of SimpleITK! 
# Instead of SimpleElastix, you may also consider ITKElastix, a new Python Elastix wrapper,
# based on the ITK Python package. It is available via pip install itk-elastix.

## 多个nii图像批量elastix配准，只配准image，成功
#根据ITKElastix包https://github.com/InsightSoftwareConsortium/ITKElastix里面的examples的ITK_Example03_Masked_3D_Registration.ipynb修改的
#安装itk-elastix程序包 
# pip install itk-elastix
import os
#报错时加上下面代码OMP: Error #15: Initializing libiomp5md.dll, but found libiomp5md.dll already initialized.
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE' 
import glob
import itk

fixed_path = r"H:\1.HCC-dataset\850HCC\all-HCC\234HCC-nantong\image\hbp"
moving_path = r"H:\1.HCC-dataset\850HCC\all-HCC\234HCC-nantong\image\ap"
output_image_path = r"H:\1.HCC-dataset\850HCC\all-HCC\234HCC-nantong\image\ap4"

if not os.path.exists(output_image_path):    
    os.makedirs(output_image_path)

# 获取固定和移动路径下的所有 NIfTI 文件
fixed_files = glob.glob(os.path.join(fixed_path, "*.nii.gz"))
moving_files = glob.glob(os.path.join(moving_path, "*.nii.gz"))
print(fixed_files[:5])

# 导入自定义参数文件，可换多个比较
#Bspline变形太严重，Affine和Rigid效果一般，3D.NC.affine.ASGD.001相对好点
parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterFile(r'D:\anaconda3\envs\ITKElastix-main\examples\data\parameters.3D.NC.affine.ASGD.001.txt')
# parameter_object.AddParameterFile(r'D:\anaconda3\envs\ITKElastix-main\examples\data\parameters_Affine.txt')


# 设置结果图像写入参数
parameter_object.SetParameter(0, "WriteResultImage", "true")

# 遍历每对固定和移动图像
for fixed_file, moving_file in zip(fixed_files, moving_files):
    try:
        # 导入图像
        fixed_image = itk.imread(fixed_file, itk.F)
        moving_image = itk.imread(moving_file, itk.F)
        print(f"处理固定图像: {fixed_file} 和移动图像: {moving_file}")

        # 调用registration函数
        result_image, result_transform_parameters = itk.elastix_registration_method(
            fixed_image, moving_image,
            parameter_object=parameter_object,
            log_to_console=False)

        # 保存结果图像
        output_file = os.path.join(output_image_path, os.path.basename(moving_file))
        itk.imwrite(result_image, output_file)
        print(f"结果图像已保存: {output_file}")

    except Exception as e:
        print(f"处理 {fixed_file} 和 {moving_file} 时出错: {e}")


#%%使用elastix进行配准，配准不同序列的image和mask，效果一般，成功了
#参考Radiology_HCC_MVI-main代码
import shutil
import subprocess
import sys
print(sys.path)
from os import listdir
import os
import logging
from os.path import join, exists

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('registration.log'),
        logging.StreamHandler()
    ]
)

# 检查文件是否为图像文件（以 .nii.gz 结尾）
def is_image_file(filename):
    return any(filename.endswith(extension) for extension in [".nii.gz"])

# 使用 Elastix 进行仿射配准
def AffBsp(mov_aff, out_aff, move_mask, fix, fixmask):
    try:
        # 检查输入文件是否存在
        for file_path in [mov_aff, move_mask, fix, fixmask]:
            if not exists(file_path):
                raise FileNotFoundError(f"找不到输入文件: {file_path}")

        # 创建输出目录
        if not exists(out_aff):
            os.makedirs(out_aff)
        
        # 构建命令行指令(路径包含引号以处理空格)
        cmd = f'"{sys.path[0]}\\elastix\\elastix.exe" -f "{fix}" -fMask "{fixmask}" ' + \
              f'-m "{mov_aff}" -mMask "{move_mask}" -out "{out_aff}" ' + \
              f'-p "{sys.path[0]}\\elastix\\Par0057Bspline.txt"'
        
        # 检查elastix程序是否存在
        elastix_path = os.path.join(sys.path[0], "elastix", "elastix.exe")       
        param_path = os.path.join(sys.path[0], "elastix", "Par0057Bspline.txt")
               
        # 执行命令并获取输出
        logging.info(f"执行配准命令: {cmd}")
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        # 检查执行结果
        if process.returncode != 0:
            error_msg = stderr.decode() if stderr else "无错误信息"
            raise RuntimeError(f"配准失败,退出代码 {process.returncode}. 错误: {error_msg}")
        
        # 检查输出文件
        result_file = os.path.join(out_aff, 'result.0.nii')        
            
    except Exception as e:
        logging.error(f"AffBsp错误: {str(e)}")
        raise

if __name__ == '__main__':
    # 定义输入图像的路径,input_time3_dir为固定图像
    # input_time0_dir = 'input_data/aAif'
    input_time1_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap"
    input_time2_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\pp"
    input_time3_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\hbp"
    
    # 定义输入mask的路径
    # input_mask0_dir = 'input_data_mask/aAif_mask'
    input_mask1_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap"
    input_mask2_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\pp"
    input_mask3_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp"
    
    # 定义输出结果的路径
    # output_time0_dir = 'output_data/aRes'
    output_time1_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\ap"
    output_time2_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\registered\pp"
    
    # 定义临时路径
    # tmp_time0_dir = 'tmp/a'
    tmp_time1_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\ap"
    tmp_time2_dir = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\tmp\pp"   
   

    # 获取时间点3的文件名
    time3_filenames = [x for x in listdir(input_time3_dir) if is_image_file(x)]
    # 获取基础文件名（去掉-hbp.nii.gz后缀）
    base_filenames = [x.replace('-hbp.nii.gz', '') for x in time3_filenames]

    # 文件数量
    num = len(time3_filenames)
    logging.info(f"Found {num} files to process")

    # 遍历每个文件进行配准
    for i in range(num):
        try:
            base_name = base_filenames[i]
            current_hbp = f"{base_name}-hbp.nii.gz"
            current_ap = f"{base_name}-ap.nii.gz"
            current_pp = f"{base_name}-pp.nii.gz"
            
            # Update mask file names to include -mask in the pattern
            current_hbp_mask = f"{base_name}-hbp-mask.nii.gz"
            current_ap_mask = f"{base_name}-ap-mask.nii.gz"
            current_pp_mask = f"{base_name}-pp-mask.nii.gz"
            
            logging.info(f"Processing files for patient {base_name}")
            
            # 获取固定图像和mask的路径
            fix = join(input_time3_dir, current_hbp)
            fixmask = join(input_mask3_dir, current_hbp_mask)  
            
            # 配准时间1图像 (AP)
            mov_aff = join(input_time1_dir, current_ap)
            out_aff = join(tmp_time1_dir, f"{base_name}_aff")
            move_mask = join(input_mask1_dir, current_ap_mask) 
            
            AffBsp(mov_aff, out_aff, move_mask, fix, fixmask)
            result_file = join(out_aff, 'result.0.nii')
            output_file = join(output_time1_dir, current_ap)
            shutil.move(result_file, output_file)
            logging.info(f"Successfully registered and saved AP: {output_file}")
            
            # 配准时间2图像 (PP)
            mov_aff = join(input_time2_dir, current_pp)
            out_aff = join(tmp_time2_dir, f"{base_name}_aff")
            move_mask = join(input_mask2_dir, current_pp_mask)  
            
            AffBsp(mov_aff, out_aff, move_mask, fix, fixmask)
            result_file = join(out_aff, 'result.0.nii')
            output_file = join(output_time2_dir, current_pp)
            shutil.move(result_file, output_file)
            logging.info(f"Successfully registered and saved PP: {output_file}")
            
        except Exception as e:
            logging.error(f"Error processing patient {base_name}: {str(e)}")
            continue

#%%MRI图像配准
##单个病例MRI图像配准
import SimpleITK as sitk
 # 读取T1和T2图像
t1_image = sitk.ReadImage(r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\hbp_data\baohanqing.nii')
t2_image = sitk.ReadImage(r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\dwi_data\baohanqing_dwi.nii')
 # 配准参数设置
registration_method = sitk.ImageRegistrationMethod()
 # 配准方法：多分辨率贪婪随机样本一致性算法（Multi-resolution Greedy Algorithm with Random Sampling (MGRAS)）
registration_method.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
registration_method.SetMetricSamplingPercentage(0.01)
registration_method.SetInterpolator(sitk.sitkLinear)
registration_method.SetOptimizerAsGradientDescent(learningRate=1.0, numberOfIterations=100, estimateLearningRate=registration_method.EachIteration)
registration_method.SetOptimizerScalesFromPhysicalShift()

 # 初始变换设置
initial_transform = sitk.CenteredTransformInitializer(t1_image, t2_image, sitk.Euler3DTransform(), sitk.CenteredTransformInitializerFilter.GEOMETRY)
registration_method.SetInitialTransform(initial_transform)

 # 开始配准
final_transform = registration_method.Execute(sitk.Cast(t1_image, sitk.sitkFloat32), sitk.Cast(t2_image, sitk.sitkFloat32))
 # 应用变换
t2_transformed = sitk.Resample(t2_image, t1_image, final_transform, sitk.sitkLinear, 0.0, t2_image.GetPixelID())

 # 保存配准后的图像
sitk.WriteImage(t2_transformed, r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\dwi_registered.nii')

#%%批量MRI图像配准最新版 文件夹内为.nii文件  成功
import os
import SimpleITK as sitk

 # Set the input folder paths
fixed_image_path = r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\hbp_data' # 根据hbp图像进行配准
moving_image_path = r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data'    # 需要配准处理的图像
output_folder = r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap-registered'

fixed_image_files = os.listdir(fixed_image_path)  # 根据hbp图像进行配准
moving_image_files = os.listdir(moving_image_path)  # 需要配准处理的图像

# Sort the file lists based on file names
fixed_image_files.sort()
moving_image_files.sort()

 # Registration parameter settings
registration_method = sitk.ImageRegistrationMethod()
registration_method.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
registration_method.SetMetricSamplingPercentage(0.01)
registration_method.SetInterpolator(sitk.sitkLinear)
registration_method.SetOptimizerAsGradientDescent(learningRate=1.0, numberOfIterations=100,
                                                  estimateLearningRate=registration_method.EachIteration)
registration_method.SetOptimizerScalesFromPhysicalShift()
 # Register each pair of images based on file names
for fixed_file, moving_file in zip(fixed_image_files, moving_image_files):
    try:
        # Load the images
        fixed_image = sitk.ReadImage(os.path.join(fixed_image_path, fixed_file))
        moving_image = sitk.ReadImage(os.path.join(moving_image_path, moving_file))
        print(f"Processing files: {fixed_file}, {moving_file}")
        # 重抽样 Resample the moving image to match the size and dimensions of the fixed image
        resampler = sitk.ResampleImageFilter()
        resampler.SetSize(fixed_image.GetSize())
        resampler.SetOutputSpacing(fixed_image.GetSpacing())
        resampler.SetOutputDirection(fixed_image.GetDirection())
        resampler.SetOutputOrigin(fixed_image.GetOrigin())
        resampler.SetTransform(sitk.Transform())
        resampler.SetDefaultPixelValue(0)
        moving_image = resampler.Execute(moving_image)
        # Convert image types and dimensions
        fixed_image = sitk.Cast(fixed_image, moving_image.GetPixelID())
        fixed_image = sitk.Resample(fixed_image, moving_image, sitk.Euler3DTransform(), sitk.sitkLinear, 0.0, fixed_image.GetPixelID())
        # Initial transform setting
        initial_transform = sitk.CenteredTransformInitializer(fixed_image, moving_image, sitk.Euler3DTransform(), sitk.CenteredTransformInitializerFilter.GEOMETRY)
        registration_method.SetInitialTransform(initial_transform)
        # Start registration
        final_transform = registration_method.Execute(sitk.Cast(fixed_image, sitk.sitkFloat32), sitk.Cast(moving_image, sitk.sitkFloat32))
        # Apply transformation
        moving_image_registered = sitk.Resample(moving_image, fixed_image, final_transform, sitk.sitkLinear, 0.0, moving_image.GetPixelID())
        # Save the registered moving image
        output_file_name = os.path.splitext(moving_file)[0] + "_registered.nii"
        output_file_path = os.path.join(output_folder, output_file_name)
        sitk.WriteImage(moving_image_registered, output_file_path)
    except Exception as e:
        print(f"Error processing file: {moving_file}")
        print(f"Error message: {str(e)}")
        continue
print("Done!")

#%%建议检查一下  dp_roi  和  ap_pp_roi  的像素类型和维度是否相同。
# 如果不同，需要将其中一个图像的像素类型或维度调整为与另一个图像相同。
# 可以参考以下代码进行像素类型和维度的调整，把image2的像素类型和维度调整与image1一致
import SimpleITK as sitk
 # 读取图像
image1 = sitk.ReadImage(r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data\wangjinhai.nii")
image2 = sitk.ReadImage(r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data\wanghua.nii") #需要调整的图像
  # 获取像素类型和维度
pixel_type1 = image1.GetPixelID()
pixel_type2 = image2.GetPixelID()
pixel_type2
dimension1 = image1.GetDimension()
dimension2 = image2.GetDimension()
dimension2
  # 设置新的像素类型和维度
new_pixel_type = pixel_type1
new_dimension = dimension1
  # 如果像素类型或维度不同，则进行调整
if pixel_type2 != new_pixel_type or dimension2 != new_dimension:
    new_spacing = (1.0,) * new_dimension
    new_size = (int(round(image2.GetSize()[0] * image2.GetSpacing()[0] / new_spacing[0])),
                int(round(image2.GetSize()[1] * image2.GetSpacing()[1] / new_spacing[1])),
                int(round(image2.GetSize()[2] * image2.GetSpacing()[2] / new_spacing[2])))
    new_origin = tuple((0.5 * float(new_size[i] - 1) * new_spacing[i]) for i in range(new_dimension))
    new_direction = image2.GetDirection()
      # 执行像素类型和维度的调整
    image2 = sitk.Resample(image2, new_size, sitk.Transform(), sitk.sitkLinear, new_origin, new_spacing, new_direction, 0.0, new_pixel_type)
    # 保存调整后的图像到原路径
    sitk.WriteImage(image2, r"I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data\wanghua.nii")

 # 输出调整后的像素类型和维度
print(image2.GetPixelID())
print(image2.GetDimension())

## 刚体变换配准  未成功
import SimpleITK as sitk
import os
 # Function to read all nii.gz files in a folder
def read_all_images_in_folder(folder_path):
    image_list = []
    for file_name in os.listdir(folder_path):
        if file_name.endswith('.nii'):
            image_path = os.path.join(folder_path, file_name)
            # Read image and get metadata
            reader = sitk.ImageFileReader()
            reader.SetFileName(image_path)
            image = reader.Execute()
            image.SetMetaData('Modality', os.path.splitext(os.path.basename(image_path))[0])
            image.SetMetaData('FileName', image_path)
            image_list.append(image)
    return image_list


 # 定义函数，设置文件路径和配准参数
fixed_folder = "I:/1.HCC-VETC/datasets/all-HCC-nantong/all-HCC/image/hbp_data"
moving_folder = "I:/1.HCC-VETC/datasets/all-HCC-nantong/all-HCC/ap_data"
output_folder = "I:/1.HCC-VETC/datasets/all-HCC-nantong/all-HCC/image/gangxing-register"

number_of_bins = 50
sampling_percentage = 0.01
learning_rate = 1.0
number_of_iterations = 100
convergence_minimum_value = 1e-6
convergence_window_size = 10
shrink_factors = [4, 2, 1]
smoothing_sigmas = [2, 1, 0]

# 读取固定和移动文件夹中的所有图像
fixed_images = read_all_images_in_folder(fixed_folder)
moving_images = read_all_images_in_folder(moving_folder)

# 添加元数据到每个图像
for fixed_image in fixed_images:
    fixed_image.SetMetaData("Modality", "CE-T1WI")
for moving_image in moving_images:
    moving_image.SetMetaData("Modality", "T1WI")
 # 将每个移动图像对准到对应的固定图像
for i, moving_image in enumerate(moving_images):
    print(f"Aligning moving image {i+1}/{len(moving_images)}")
    fixed_image = fixed_images[i]
    print(moving_image)
    # 初始对齐
    initial_transform = sitk.CenteredTransformInitializer(fixed_image, moving_image, sitk.Euler3DTransform(), sitk.CenteredTransformInitializerFilter.GEOMETRY)
    registration_method = sitk.ImageRegistrationMethod()
    registration_method.SetMetricAsMattesMutualInformation(number_of_bins)
    registration_method.SetMetricSamplingStrategy(registration_method.RANDOM)
    registration_method.SetMetricSamplingPercentage(sampling_percentage)
    registration_method.SetInterpolator(sitk.sitkLinear)
    # registration_method.SetOptimizerAsGradientDescent(learning_rate=learning_rate, numberOfIterations=number_of_iterations, convergenceMinimumValue=convergence_minimum_value, convergenceWindowSize=convergence_window_size)
    registration_method.SetOptimizerAsGradientDescent(learning_rate, number_of_iterations)
    registration_method.SetOptimizerConvergenceMinimumValue(convergence_minimum_value)
    registration_method.SetOptimizerConvergenceWindowSize(convergence_window_size)
    registration_method.SetOptimizerScalesFromPhysicalShift()
    registration_method.SetInitialTransform(initial_transform, inPlace=False)
    # 多分辨率刚性配准
    registration_method.SetShrinkFactorsPerLevel(shrink_factors)
    registration_method.SetSmoothingSigmasPerLevel(smoothing_sigmas)
    registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
    registration_method.SetInitialTransform(initial_transform)
    final_transform = registration_method.Execute(fixed_image, moving_image)
    print("Rigid Registration Done!")
    # 根据对齐参数对移动图像进行转换
    moving_resampled = sitk.Resample(moving_image, fixed_image, final_transform, sitk.sitkLinear, 0.0, moving_image.GetPixelID())
    # 将对齐后的图像和变换保存到输出文件夹
    moving_file_name = os.path.basename(moving_image.GetMetaData('FileName'))
    output_file_name = os.path.join(output_folder, moving_file_name)
    sitk.WriteImage(moving_image, output_file_name)
    transform_file_name = os.path.splitext(output_file_name)[0] + ".tfm"
    sitk.WriteTransform(final_transform, transform_file_name)
print("All Done!")

#%%非刚性配准，成功
import os
import SimpleITK as sitk
 # Define file paths
fixed_image_path = r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\hbp_data'
moving_image_path = r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\ap_data'
output_folder = r'I:\1.HCC-VETC\datasets\all-HCC-nantong\all-HCC\image\feigangxing-registered'
 # Get the list of filenames in fixed_image_path and moving_image_path
fixed_image_files = sorted(os.listdir(fixed_image_path))
moving_image_files = sorted(os.listdir(moving_image_path))
 # Iterate over the files and perform registration
for fixed_file, moving_file in zip(fixed_image_files, moving_image_files):
    try:
        # Read the fixed and moving images
        fixed_image = sitk.ReadImage(os.path.join(fixed_image_path, fixed_file), sitk.sitkFloat32)
        moving_image = sitk.ReadImage(os.path.join(moving_image_path, moving_file), sitk.sitkFloat32)
        print(f"Processing files: {fixed_file}, {moving_file}")
         # Resample the moving image to match the size and dimensions of the fixed image
        resampler = sitk.ResampleImageFilter()
        resampler.SetSize(fixed_image.GetSize())
        resampler.SetOutputSpacing(fixed_image.GetSpacing())
        resampler.SetOutputDirection(fixed_image.GetDirection())
        resampler.SetOutputOrigin(fixed_image.GetOrigin())
        resampler.SetTransform(sitk.Transform())
        resampler.SetDefaultPixelValue(0)
        moving_image = resampler.Execute(moving_image)
        # Convert image types and dimensions
        fixed_image = sitk.Cast(fixed_image, moving_image.GetPixelID())
        fixed_image = sitk.Resample(fixed_image, moving_image, sitk.Euler3DTransform(), sitk.sitkLinear, 0.0, fixed_image.GetPixelID())
         # Create BSpline transform grid
        mesh_size = [5, 5, 5]  # Adjust the size of the grid as needed
        spline_order = 3  # Interpolation order, 3 for cubic interpolation
        bspline_transform = sitk.BSplineTransformInitializer(fixed_image, mesh_size, spline_order)
         # 非刚性配准 Perform non-rigid registration using an optimizer
        registration = sitk.ImageRegistrationMethod()
        registration.SetMetricAsMattesMutualInformation()
        registration.SetOptimizerAsLBFGSB(gradientConvergenceTolerance=1e-5, numberOfIterations=100)
        registration.SetInitialTransform(sitk.TranslationTransform(fixed_image.GetDimension()))
        registration.SetInterpolator(sitk.sitkLinear)
        registration.AddCommand(sitk.sitkIterationEvent,lambda: None)
        final_transform = registration.Execute(fixed_image, moving_image)
         # Apply the final transform to the moving image
        moving_image_registered = sitk.Resample(moving_image, fixed_image, final_transform, sitk.sitkLinear, 0.0,
                                                moving_image.GetPixelID())
         # Save the registered image
        output_file = os.path.join(output_folder, f'{os.path.splitext(fixed_file)[0]}_registered.nii')
        sitk.WriteImage(moving_image_registered, output_file)
    except Exception as e:
        print(f'Error processing file: {fixed_file} - {moving_file}')
        print(f'Error message: {str(e)}')
        continue
