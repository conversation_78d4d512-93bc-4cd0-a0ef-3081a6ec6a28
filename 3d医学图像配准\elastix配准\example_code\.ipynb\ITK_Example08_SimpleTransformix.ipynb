{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 8. Simple Transformix"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After image registrations it is often useful to apply the transformation as found by the registration to another image. Maybe you want to apply the transformation to an original (larger) image to gain resolution. Or maybe you need the transformation to apply it to a label image (segmentation). Transformix is an image filter that was developed together with elastix, that can be used to do these transformations.\n", "\n", "A spatial transformation is defined as a mapping from the fixed image domain to the moving image domain. More information on the precise definition of the transform can be found in the [elastix manual](https://elastix.lumc.nl/download/elastix-5.0.1-manual.pdf). Transformix can be used to apply this mapping not only to images, but also to masks (binary images) and point sets see [example 9](ITK_Example09_PointSetAndMaskTransformation.ipynb#section_id10).\n", "\n", "As an alternative, see the [itk resampling example](ITK_Example13_ITKResampling.ipynb)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Elastix"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "from itkwidgets import compare, checkerboard"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import Images\n", "fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_map_rigid = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(parameter_map_rigid)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Registration with the registration function..."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transformix\n", "The transform parameters that elastix outputs can be given to transformix as input for the transformations. The output transform parameters from elastix are mappings from the fixed image to the moving image domain. Transformix therefore uses a backwards mapping to obtain a registered version of the moving image (moving -> fixed domain). "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Import Image to transform\n", "# In this example the same moving image is used, that was used for elastix,\n", "# this however will result in the same image as was already given by the \n", "# first elastix registration.\n", "moving_image_transformix = itk.imread('data/CT_2D_head_moving.mha', itk.F)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Transformation can either be done in one line with the transformix function..."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["result_image_transformix = itk.transformix_filter(\n", "    moving_image,\n", "    result_transform_parameters)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an transformix image filter object similar to the elastix algorithm."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Load Transformix Object\n", "transformix_object = itk.TransformixFilter.New(moving_image_transformix)\n", "transformix_object.SetTransformParameterObject(result_transform_parameters)\n", "\n", "# Update object (required)\n", "transformix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Transformation\n", "result_image_transformix = transformix_object.GetOutput()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization\n", "The results of the image transform can be visualized with widgets from the itkwidget library such as the checkerboard and compare widgets."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d857fe306c1848a8802af23ebfb415e8", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Viewer(annotations=False, interpolation=False, rendered_image=<itk.itkImagePython.itkImageF2; p…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["checkerboard(fixed_image, result_image_transformix, pattern=5)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "039f95f8fd05433986ca7cd0e952e1fc", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=True, description='cmap'), Checkbox(va…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compare(fixed_image, result_image_transformix, link_cmap=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}