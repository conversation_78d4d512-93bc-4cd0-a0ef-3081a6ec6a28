{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# Brain tumor 3D segmentation with MONAI\n", "\n", "This tutorial shows how to construct a training workflow of multi-labels segmentation task.\n", "\n", "And it contains below features:\n", "1. Transforms for dictionary format data.\n", "1. Define a new transform according to MONAI transform API.\n", "1. Load Nifti image with metadata, load a list of images and stack them.\n", "1. Randomly adjust intensity for data augmentation.\n", "1. <PERSON><PERSON> and transforms to accelerate training and validation.\n", "1. 3D SegResNet model, Dice loss function, Mean Dice metric for 3D segmentation task.\n", "1. Deterministic training for reproducibility.\n", "\n", "The dataset comes from http://medicaldecathlon.com/.  \n", "Target: Gliomas segmentation necrotic/active tumour and oedema  \n", "Modality: Multimodal multisite MRI data (FLAIR, T1w, T1gd,T2w)  \n", "Size: 750 4D volumes (484 Training + 266 Testing)  \n", "Source: BRATS 2016 and 2017 datasets.  \n", "Challenge: Complex and heterogeneously-located targets\n", "\n", "Below figure shows image patches with the tumor sub-regions that are annotated in the different modalities (top left) and the final labels for the whole dataset (right).\n", "(Figure taken from the [BraTS IEEE TMI paper](https://ieeexplore.ieee.org/document/6975210/))\n", "\n", "![image](../figures/brats_tasks.png)\n", "\n", "The image patches show from left to right:\n", "1. the whole tumor (yellow) visible in T2-FLAIR (Fig.A).\n", "1. the tumor core (red) visible in T2 (Fig.B).\n", "1. the enhancing tumor structures (light blue) visible in T1Gd, surrounding the cystic/necrotic components of the core (green) (Fig. C).\n", "1. The segmentations are combined to generate the final labels of the tumor sub-regions (Fig.D): edema (yellow), non-enhancing solid core (red), necrotic/cystic core (green), enhancing core (blue).\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/brats_segmentation_3d.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": []}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "!python -c \"import onnxruntime\" || pip install -q onnxruntime\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 0.9.1\n", "Numpy version: 1.22.4\n", "Pytorch version: 1.13.0a0+340c412\n", "MONAI flags: HAS_EXT = True, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 356d2d2f41b473f588899d705bbc682308cee52c\n", "MONAI __file__: /opt/monai/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.9\n", "Nibabel version: 4.0.1\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.0.1\n", "Tensorboard version: 2.9.1\n", "gdown version: 4.5.1\n", "TorchVision version: 0.13.0a0\n", "tqdm version: 4.64.0\n", "lmdb version: 1.3.0\n", "psutil version: 5.9.1\n", "pandas version: 1.3.5\n", "einops version: 0.4.1\n", "transformers version: 4.20.1\n", "mlflow version: 1.27.0\n", "pynrrd version: 0.4.3\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import time\n", "import matplotlib.pyplot as plt\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader, decollate_batch\n", "from monai.handlers.utils import from_engine\n", "from monai.losses import DiceLoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.metrics import DiceMetric\n", "from monai.networks.nets import SegResNet\n", "from monai.transforms import (\n", "    Activations,\n", "    Activationsd,\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON>,\n", "    Invertd,\n", "    LoadImaged,\n", "    MapTransform,\n", "    NormalizeIntensityd,\n", "    Orientationd,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    RandScaleIntensityd,\n", "    RandShiftIntensityd,\n", "    RandSpatialCropd,\n", "    Spacingd,\n", "    EnsureTyped,\n", "    EnsureChannelFirstd,\n", ")\n", "from monai.utils import set_determinism\n", "import onnxruntime\n", "from tqdm import tqdm\n", "\n", "import torch\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/workspace/data/medical\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["set_determinism(seed=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define a new transform to convert brain tumor labels\n", "\n", "Here we convert the multi-classes labels into multi-labels segmentation task in One-Hot format."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class ConvertToMultiChannelBasedOnBratsClassesd(MapTransform):\n", "    \"\"\"\n", "    Convert labels to multi channels based on brats classes:\n", "    label 1 is the peritumoral edema\n", "    label 2 is the GD-enhancing tumor\n", "    label 3 is the necrotic and non-enhancing tumor core\n", "    The possible classes are TC (Tumor core), WT (Whole tumor)\n", "    and ET (Enhancing tumor).\n", "\n", "    \"\"\"\n", "\n", "    def __call__(self, data):\n", "        d = dict(data)\n", "        for key in self.keys:\n", "            result = []\n", "            # merge label 2 and label 3 to construct TC\n", "            result.append(torch.logical_or(d[key] == 2, d[key] == 3))\n", "            # merge labels 1, 2 and 3 to construct WT\n", "            result.append(torch.logical_or(torch.logical_or(d[key] == 2, d[key] == 3), d[key] == 1))\n", "            # label 2 is ET\n", "            result.append(d[key] == 2)\n", "            d[key] = torch.stack(result, axis=0).float()\n", "        return d"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms for training and validation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["train_transform = Compose(\n", "    [\n", "        # load 4 Nifti images and stack them together\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=\"image\"),\n", "        EnsureTyped(keys=[\"image\", \"label\"]),\n", "        ConvertToMultiChannelBasedOnBratsClassesd(keys=\"label\"),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.0, 1.0, 1.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        RandSpatialCropd(keys=[\"image\", \"label\"], roi_size=[224, 224, 144], random_size=False),\n", "        RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=0),\n", "        RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=1),\n", "        RandFlipd(keys=[\"image\", \"label\"], prob=0.5, spatial_axis=2),\n", "        NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n", "        RandScaleIntensityd(keys=\"image\", factors=0.1, prob=1.0),\n", "        RandShiftIntensityd(keys=\"image\", offsets=0.1, prob=1.0),\n", "    ]\n", ")\n", "val_transform = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=\"image\"),\n", "        EnsureTyped(keys=[\"image\", \"label\"]),\n", "        ConvertToMultiChannelBasedOnBratsClassesd(keys=\"label\"),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.0, 1.0, 1.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quickly load data with DecathlonDataset\n", "\n", "Here we use `DecathlonDataset` to automatically download and extract the dataset.\n", "It inherits MONAI `CacheDataset`, if you want to use less memory, you can set `cache_num=N` to cache N items for training and use the default args to cache all the items for validation, it depends on your memory size."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "File exists: /workspace/data/medical/Task01_BrainTumour.tar, skipped downloading.\n", "Non-empty folder exists in /workspace/data/medical/Task01_BrainTumour, skipped extracting.\n"]}], "source": ["# here we don't cache any data in case out of memory issue\n", "train_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    transform=train_transform,\n", "    section=\"training\",\n", "    download=True,\n", "    cache_rate=0.0,\n", "    num_workers=4,\n", ")\n", "train_loader = DataLoader(train_ds, batch_size=1, shuffle=True, num_workers=4)\n", "val_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    transform=val_transform,\n", "    section=\"validation\",\n", "    download=False,\n", "    cache_rate=0.0,\n", "    num_workers=4,\n", ")\n", "val_loader = DataLoader(val_ds, batch_size=1, shuffle=False, num_workers=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check data shape and visualize"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image shape: torch.<PERSON><PERSON>([4, 240, 240, 155])\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1728x432 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["label shape: torch.Size([3, 240, 240, 155])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# pick one image from DecathlonDataset to visualize and check the 4 channels\n", "val_data_example = val_ds[2]\n", "print(f\"image shape: {val_data_example['image'].shape}\")\n", "plt.figure(\"image\", (24, 6))\n", "for i in range(4):\n", "    plt.subplot(1, 4, i + 1)\n", "    plt.title(f\"image channel {i}\")\n", "    plt.imshow(val_data_example[\"image\"][i, :, :, 60].detach().cpu(), cmap=\"gray\")\n", "plt.show()\n", "# also visualize the 3 channels label corresponding to this image\n", "print(f\"label shape: {val_data_example['label'].shape}\")\n", "plt.figure(\"label\", (18, 6))\n", "for i in range(3):\n", "    plt.subplot(1, 3, i + 1)\n", "    plt.title(f\"label channel {i}\")\n", "    plt.imshow(val_data_example[\"label\"][i, :, :, 60].detach().cpu())\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Model, Loss, Optimizer"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["max_epochs = 300\n", "val_interval = 1\n", "VAL_AMP = True\n", "\n", "# standard PyTorch program style: create SegResNet, DiceLoss and Adam optimizer\n", "device = torch.device(\"cuda:0\")\n", "model = SegResNet(\n", "    blocks_down=[1, 2, 2, 4],\n", "    blocks_up=[1, 1, 1],\n", "    init_filters=16,\n", "    in_channels=4,\n", "    out_channels=3,\n", "    dropout_prob=0.2,\n", ").to(device)\n", "loss_function = DiceLoss(smooth_nr=0, smooth_dr=1e-5, squared_pred=True, to_onehot_y=False, sigmoid=True)\n", "optimizer = torch.optim.Adam(model.parameters(), 1e-4, weight_decay=1e-5)\n", "lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=max_epochs)\n", "\n", "dice_metric = DiceMetric(include_background=True, reduction=\"mean\")\n", "dice_metric_batch = DiceMetric(include_background=True, reduction=\"mean_batch\")\n", "\n", "post_trans = Compose([Activations(sigmoid=True), AsDiscrete(threshold=0.5)])\n", "\n", "\n", "# define inference method\n", "def inference(input):\n", "    def _compute(input):\n", "        return sliding_window_inference(\n", "            inputs=input,\n", "            roi_size=(240, 240, 160),\n", "            sw_batch_size=1,\n", "            predictor=model,\n", "            overlap=0.5,\n", "        )\n", "\n", "    if VAL_AMP:\n", "        with torch.cuda.amp.autocast():\n", "            return _compute(input)\n", "    else:\n", "        return _compute(input)\n", "\n", "\n", "# use amp to accelerate training\n", "scaler = torch.cuda.amp.GradScaler()\n", "# enable cuDNN benchmark\n", "torch.backends.cudnn.benchmark = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute a typical PyTorch training process"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["best_metric = -1\n", "best_metric_epoch = -1\n", "best_metrics_epochs_and_time = [[], [], []]\n", "epoch_loss_values = []\n", "metric_values = []\n", "metric_values_tc = []\n", "metric_values_wt = []\n", "metric_values_et = []\n", "\n", "total_start = time.time()\n", "for epoch in range(max_epochs):\n", "    epoch_start = time.time()\n", "    print(\"-\" * 10)\n", "    print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    for batch_data in train_loader:\n", "        step_start = time.time()\n", "        step += 1\n", "        inputs, labels = (\n", "            batch_data[\"image\"].to(device),\n", "            batch_data[\"label\"].to(device),\n", "        )\n", "        optimizer.zero_grad()\n", "        with torch.cuda.amp.autocast():\n", "            outputs = model(inputs)\n", "            loss = loss_function(outputs, labels)\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "        epoch_loss += loss.item()\n", "        print(\n", "            f\"{step}/{len(train_ds) // train_loader.batch_size}\"\n", "            f\", train_loss: {loss.item():.4f}\"\n", "            f\", step time: {(time.time() - step_start):.4f}\"\n", "        )\n", "    lr_scheduler.step()\n", "    epoch_loss /= step\n", "    epoch_loss_values.append(epoch_loss)\n", "    print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        with torch.no_grad():\n", "            for val_data in val_loader:\n", "                val_inputs, val_labels = (\n", "                    val_data[\"image\"].to(device),\n", "                    val_data[\"label\"].to(device),\n", "                )\n", "                val_outputs = inference(val_inputs)\n", "                val_outputs = [post_trans(i) for i in decollate_batch(val_outputs)]\n", "                dice_metric(y_pred=val_outputs, y=val_labels)\n", "                dice_metric_batch(y_pred=val_outputs, y=val_labels)\n", "\n", "            metric = dice_metric.aggregate().item()\n", "            metric_values.append(metric)\n", "            metric_batch = dice_metric_batch.aggregate()\n", "            metric_tc = metric_batch[0].item()\n", "            metric_values_tc.append(metric_tc)\n", "            metric_wt = metric_batch[1].item()\n", "            metric_values_wt.append(metric_wt)\n", "            metric_et = metric_batch[2].item()\n", "            metric_values_et.append(metric_et)\n", "            dice_metric.reset()\n", "            dice_metric_batch.reset()\n", "\n", "            if metric > best_metric:\n", "                best_metric = metric\n", "                best_metric_epoch = epoch + 1\n", "                best_metrics_epochs_and_time[0].append(best_metric)\n", "                best_metrics_epochs_and_time[1].append(best_metric_epoch)\n", "                best_metrics_epochs_and_time[2].append(time.time() - total_start)\n", "                torch.save(\n", "                    model.state_dict(),\n", "                    os.path.join(root_dir, \"best_metric_model.pth\"),\n", "                )\n", "                print(\"saved new best metric model\")\n", "            print(\n", "                f\"current epoch: {epoch + 1} current mean dice: {metric:.4f}\"\n", "                f\" tc: {metric_tc:.4f} wt: {metric_wt:.4f} et: {metric_et:.4f}\"\n", "                f\"\\nbest mean dice: {best_metric:.4f}\"\n", "                f\" at epoch: {best_metric_epoch}\"\n", "            )\n", "    print(f\"time consuming of epoch {epoch + 1} is: {(time.time() - epoch_start):.4f}\")\n", "total_time = time.time() - total_start"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train completed, best_metric: 0.7914 at epoch: 279, total time: 90155.70936012268.\n"]}], "source": ["print(f\"train completed, best_metric: {best_metric:.4f} at epoch: {best_metric_epoch}, total time: {total_time}.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot the loss and metric"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y, color=\"red\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val Mean Dice\")\n", "x = [val_interval * (i + 1) for i in range(len(metric_values))]\n", "y = metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y, color=\"green\")\n", "plt.show()\n", "\n", "plt.figure(\"train\", (18, 6))\n", "plt.subplot(1, 3, 1)\n", "plt.title(\"Val Mean Dice TC\")\n", "x = [val_interval * (i + 1) for i in range(len(metric_values_tc))]\n", "y = metric_values_tc\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y, color=\"blue\")\n", "plt.subplot(1, 3, 2)\n", "plt.title(\"Val Mean Dice WT\")\n", "x = [val_interval * (i + 1) for i in range(len(metric_values_wt))]\n", "y = metric_values_wt\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y, color=\"brown\")\n", "plt.subplot(1, 3, 3)\n", "plt.title(\"Val Mean Dice ET\")\n", "x = [val_interval * (i + 1) for i in range(len(metric_values_et))]\n", "y = metric_values_et\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y, color=\"purple\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check best pytorch model output with the input image and label"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABWMAAAFSCAYAAACXPc1rAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8rg+JYAAAACXBIWXMAAAsTAAALEwEAmpwYAAEAAElEQVR4nOz9abClWXYdhq1z53l688vMyqGquqq6eoIbbIABECSiwQEIwSBDMEjKtEkESTAo00PYCosOUyJDlkK0gxGyHLJF0RQJSgpJHExAoEwQIEADBBrdAKqH6u4qdE1ZVTm8fPO783zv5x8v17nrO3mzuobMl1n59oq48TLv8E33fuvss/ba+7goimAwGAwGg8FgMBgMBoPBYDAYDIaHi8SjPgCDwWAwGAwGg8FgMBgMBoPBYDgPMDHWYDAYDAaDwWAwGAwGg8FgMBjOACbGGgwGg8FgMBgMBoPBYDAYDAbDGcDEWIPBYDAYDAaDwWAwGAwGg8FgOAOYGGswGAwGg8FgMBgMBoPBYDAYDGcAE2MNBoPBYDAYDAaDwWAwGAwGg+EMYGKsAQDgnHvFOfeHHvVxfFg4595xzv3Ioz4OhXPuinMucs6lHvWxGAyGxwvGuQ8exrkGg+F+MM598DDONRgM7wXj3QcP490nCybGGgAAURS9GEXRrz3q4zhPcM41nHM/55zrOefedc79W4/6mAwGw9nAOPfs4Zz7K865l5xzI+fczz7q4zEYDGcH49yzhXMu65z7L+/Gtx3n3Deccz/6qI/LYDCcHYx3zx7Ouf/GOXfHOdd2zr3unPsLj/qYDPeHKeoGw6PD/xPAGMAGgM8B+P86516OouiVR3pUBoPB8GRiB8B/COCPAsg/4mMxGAyGJxkpADcB/EEANwD8GIB/5Jz7dBRF7zzKAzMYDIYnGP8xgD8fRdHIOfc8gF9zzn09iqKvPuoDM9wLc8YaAMRt+M65v+Gc+8d3Mysd59y3nHOfcM79n5xz+865m865PyKf/Wnn3O/dfe9159xfCrb9f7ybodlxzv2Fu9b6Z+6+lnXO/S3n3A3n3J5z7m875+47SXbO/UXZ16vOuf+JvPw559w3nXMt59w/dM7l7n6m7pz7H51zB865k7v/vijb/DXn3P/FOfelu9v9Zefc6t3XWArwZ+8e46Fz7v8sn0045/6qc+4t59yRc+4fOeca7+N6FwH8mwD+vSiKulEU/SaAXwDwv/hunzUYDB9/GOeeLecCQBRF/zSKop8HcPR+3m8wGJ4cGOeeLedGUdSLouhvRFH0ThRF8yiK/kcAbwP4/Hf7rMFgeDJgvPtIYt1Xoiga8b93H0+/n88azh4mxhruhx8H8F8DqAP4OoBfwunv5QKA/wDAfyHv3QfwbwCoAPhpAP8JScw598cA/O8B/AiAZwD8oWA/fxPAJ3DqDH3m7vb//WUH5Jz7nwH4GwD+l3f39T9FfFL9UwD+GICrAD4D4M/dfT4B4O8DuAzgKQADAP9ZsPl/6+6xrwPIAPh3gtd/EMBzAL4I4N93zr1w9/n/NYA/jtPM/zaAE5w6Xr8bPgFgGkXR6/LcywBefB+fNRgMTx6Mc+N40JxrMBgMCuPcOB4q5zrnNnB6Haz6y2A4vzDejeOh8K5z7v/lnOsD+A6AOwD++fv9rOGMEUWRPewBAO8A+JG7//4bAP6lvPbjALoAknf/X8ZplqV2n239PID/7d1//z0A/7G89szdzz4DwAHoAXhaXv/9AN6+z3Z/idu9z/H/Gfn//w3A377Pez8H4ET+/2sA/pr8/98G8C/u/vvK3eO9KK//DoA/dfffvwfgi/LaFoAJTsuz+NnUkmP4AwB2g+f+IoBfe9S/BXvYwx4P/2Gce7acGxzPfwjgZx/1b8Ae9rDH2T2Mcx8p56YB/AqA/+JR/w7sYQ97nN3DePeR8m4Sp2LvXwOQftS/BXssf1jPWMP9sCf/HgA4jKJoJv8HgBKApjttyP/XcZqBSgAoAPjW3fdsA3hJtnVT/r12971fdc7xOYdT8liGSwDeeo9j3pV/9+/uG865AoD/BKdZrfrd18vOuaScU/jZ0nfZNl+/DODnnHNzeX2G0z6w74UuTrNvigqAznf5nMFgeDJhnPve2/6onGswGAwK49z33vYD4VznXAKnTrgxgL/yfj5jMBieWBjvvve2H1ise/cYftM592cA/GUA/4/3+1nD2cHaFBg+EpxzWQD/HwB/C8BGFEU1nFrhyX53AFyUj1ySfx/ilHhfjKKodvdRjaIoJCriJj5cz5P/A05LAL4viqIKgB/i4X+IbS07ph+V469FUZSLouj2d/nc6wBSzrln5bnPwsq3DAbDe8A490NzrsFgMHxgGOd+eM51p0rIf4lTAeHfjKJo8gCOx2AwPOEw3n2gsW4K1jP2sYWJsYaPigyALIADANO7Waw/Iq//IwA/7Zx74W4G6d/jC1EUzQH8v3HaA2YdAJxzF5xzf/Q++/q7AP4d59zn3Smecc5dfh/HWMYpKTfvNr/+6x/wHN8LfxvAf8TjcM6tOed+4rt9KIqiHoB/CuA/cM4VnXM/AOAncOoeMBgMhvvBOPdDcO7d96buLryQBJB0zuWcc1YhZDAY3gvGuR+ScwH85wBeAPDjURQNvtubDQaD4S6Mdz8E7zrn1p1zf8o5V3LOJe+e858G8KsP8NgMDxAmxho+EqIo6gD43+CUFE9w2qj6F+T1X8SpLf7/B+BNAF+5+xJX+ft3+bxzro3TnlLP3Wdf/xjAfwTgv8VpOf/PA3g/Kwv+3wHkcZop+wqAf/E+T+/94D/F6fn+snOuc3f73/c+P/tv3z2ufQD/HYC/HEWROWMNBsN9YZz7kTj3r+E0cP6rAP7M3X//tQd4bAaD4QmDce6H49y7IsJfwmkfxV3nXPfu43/+AI/NYDA8gTDe/dCxboTTlgS3cHrd/haA/10URb/wnp8yPDK46LTBr8FwJri7SuC3AWSjKJo+6uMxGAyGJxnGuQaDwXB2MM41GAyGs4XxruHjCnPGGh46nHN/wjmXdc7VAfxfAfwzI0qDwWB4ODDONRgMhrODca7BYDCcLYx3DU8CTIw1nAX+Ek5L8d/C6UqAf/nRHo7BYDA80TDONRgMhrODca7BYDCcLYx3DR97PLQ2Bc65P4bTfhdJAH83iqK/+VB2ZDAYDAbjXIPBYDhDGOcaDAbD2cJ412AwPEl4KGKscy4J4HUAfxinDYR/F8CfjqLo1Qe+M4PBYDjnMM41GAyGs4NxrsFgMJwtjHcNBsOThofVpuALAN6Mouh6FEVjAP89gJ94SPsyGAyG8w7jXIPBYDg7GOcaDAbD2cJ412AwPFFIPaTtXgBwU/5/C8D36Ruccz8D4GcAIJ1Of351dfUhHYrBYDC8PzSbTfT7ffeoj+ND4LtyLnAv766srJzN0RkMBsN90Gq1Po68+4E5N5VKfb5Wq53JwRkMBsP90Ol0MBwOP26cC3xAfcE412AwPA54L859WGLsd0UURX8HwN8BgO3t7egv/IW/8KgOxWAwGAAAf/fv/t1HfQgPFcq7W1tb0U//9E8/4iMyGAznHX//7//9R30IDw3KuWtra9Gf+BN/4hEfkcFgOO/4uZ/7uUd9CA8NIef+8T/+xx/tARkMhnOPn//5n7/vaw+rTcFtAJfk/xfvPmcwGAyGBw/jXIPBYDg7GOcaDAbD2cJ412AwPFF4WGLs7wJ41jl31TmXAfCnAPzCQ9qXwWAwnHcY5xoMBsPZwTjXYDAYzhbGuwaD4YnCQ2lTEEXR1Dn3VwD8EoAkgL8XRdErD2NfBoPBcN5hnGswGAxnB+Ncg8FgOFsY7xoMhicND61nbBRF/xzAP39Y2zcYDAbDAsa5BoPBcHYwzjUYDIazhfGuwWB4kvCw2hQYDAaDwWAwGAwGg8FgMBgMBoNBYGKswWAwGAwGg8FgMBgMBoPBYDCcAUyMNRgMBoPBYDAYDAaDwWAwGAyGM4CJsQaDwWAwGAwGg8FgMBgMBoPBcAYwMdZgMBgMBoPBYDAYDAaDwWAwGM4AJsYaDAaDwWAwGAwGg8FgMBgMBsMZwMRYg8FgMBgMBoPBYDAYDAaDwWA4A5gYazAYDAaDwWAwGAwGg8FgMBgMZwATYw0Gg8FgMBgMBoPBYDAYDAaD4QxgYqzBYDAYDAaDwWAwGAwGg8FgMJwBTIw1GAwGg8FgMBgMBoPBYDAYDIYzgImxBoPBYDAYDAaDwWAwGAwGg8FwBjAx1mAwGAwGg8FgMBgMBoPBYDAYzgAmxhoMBoPBYDAYDAaDwWAwGAwGwxnAxFiDwWAwGAwGg8FgMBgMBoPBYDgDmBhrMBgMBoPBYDAYDAaDwWAwGAxnABNjDQaDwWAwGAwGg8FgMBgMBoPhDGBirMFgMBgMBoPBYDAYDAaDwWAwnAFMjDUYDAaDwWAwGAwGg8FgMBgMhjOAibEGg8FgMBgMBoPBYDAYDAaDwXAGMDHWYDAYDAaDwWAwGAwGg8FgMBjOACbGGgwGg8FgMBgMBoPBYDAYDAbDGcDEWIPBYDAYDAaDwWAwGAwGg8FgOAOYGGswGAwGg8FgMBgMBoPBYDAYDGcAE2MNBoPBYDAYDAaDwWAwGAwGg+EMYGKswWAwGAwGg8FgMBgMBoPBYDCcAUyMNRgMBoPBYDAYDAaDwWAwGAyGM4CJsQaDwWAwGAwGg8FgMBgMBoPBcAYwMdZgMBgMBoPBYDAYDAaDwWAwGM4AJsYaDAaDwWAwGAwGg8FgMBgMBsMZwMRYg8FgMBgMBoPBYDAYDAaDwWA4A5gYazAYDAaDwWAwGAwGg8FgMBgMZwATYw0Gg8FgMBgMBoPBYDAYDAaD4QxgYqzBYDAYDAaDwWAwGAwGg8FgMJwBTIw1GAwGg8FgMBgMBoPBYDAYDIYzgImxBoPBYDAYDAaDwWAwGAwGg8FwBjAx1mAwGAwGg8FgMBgMBoPBYDAYzgAmxhoMBoPBYDAYDAaDwWAwGAwGwxnAxFiDwWAwGAwGg8FgMBgMBoPBYDgDmBhrMBgMBoPBYDAYDAaDwWAwGAxnABNjDQaDwWAwGAwGg8FgMBgMBoPhDGBirMFgMBgMBoPBYDAYDAaDwWAwnAFMjDUYDAaDwWAwGAwGg8FgMBgMhjOAibEGg8FgMBgMBoPBYDAYDAaDwXAGMDHWYDAYDAaDwWAwGAwGg8FgMBjOACbGGgwGg8FgMBgMBoPBYDAYDAbDGcDEWIPBYDAYDAaDwWAwGAwGg8FgOAOYGGswGAwGg8FgMBgMBoPBYDAYDGcAE2MNBoPBYDAYDAaDwWAwGAwGg+EMYGKswWAwGAwGg8FgMBgMBoPBYDCcAUyMNRgMBoPBYDAYDAaDwWAwGAyGM0Dqo3zYOfcOgA6AGYBpFEXf65xrAPiHAK4AeAfAT0VRdPLRDtNgMBgMgPGuwWAwnCWMcw0Gg+HsYJxrMBjOCx6EM/aHoyj6XBRF33v3/38VwK9GUfQsgF+9+3+DwWAwPDgY7xoMBsPZwTjXYDAYzg7GuQaD4YnHw2hT8BMA/sHdf/8DAH/8IezDYDAYDAsY7xoMBsPZwTjXYDAYzg7GuQaD4YnDRxVjIwC/7Jz7qnPuZ+4+txFF0Z27/94FsPER92EwGAyGBYx3DQaD4exgnGswGAxnB+Ncg8FwLvCResYC+MEoim4759YB/Evn3Hf0xSiKIudctOyDd8n1ZwCgWq1+xMMwGAyGc4MHwruVSuXhH6nBYDB8/PFAOLdUKj38IzUYDIaPP4xzDQbDucBHcsZGUXT77t99AD8H4AsA9pxzWwBw9+/+fT77d6Io+t4oir63UCh8lMMwGAyGcwPjXYPBYDg7PCjOzeVyZ3XIBoPB8LGFca7BYDgv+NBirHOu6Jwr898A/giAbwP4BQB/9u7b/iyA/+GjHqTh/OCNN97Al7/8Zbz00kv49re/jShamvg0GM4ljHcNDwPHx8e4ffs2dnd3cXh4+KgPx2B4bGCca3gY6HQ6aDabGAwGmE6nj/pwDIbHBsa5hoeB3d1dvPHGG3j77bdx+/Zt0xcMjw0+SpuCDQA/55zjdv7bKIr+hXPudwH8I+fcnwfwLoCf+uiHaTgP2N3dxbe//W3cunUL6XQaxWIRxWIRiUQCtVrN2lkYDMa7hgeM/f193Lx5E91uF5lMBplMBtPpFKlUCsViEfl8/lEfosHwKGGca3igODw8xMHBASaTCUqlEjKZDLLZLJLJJAqFAszNZzjnMM41PFAcHh7i+vXrODk5QTqdRqFQQDqd9pxrca7hUeJDi7FRFF0H8Nklzx8B+OJHOSjD4wNmju4Oivd9D983n88BAKnUB/9p/fIv/zJarRZmsxmGwyE6nQ7+8T/+x5jP5/jBH/xBfOELX4hlstLpNBKJj7oGncHw8YHx7vkAefS9+G02m8X40DmHZDL5gff167/+6+h2u0gkEn4bb731FtLpNF544QU8/fTT/r1RFCGdTr/neGAwPEkwzj0feL+xrv4F3puj74ff/u3fRqfT8XxL7i0UCnj66adx+fJl/16+xzjXcF5gnHs+MJvNAOA941a+R/Fh4twvf/nLaLfbiKIIzjlkMhkcHR0hmUzimWeeicW5xrmGs8ZHXcDL8ITj9u3bmM/neOqpp5a+HkUR7ty5g36/j8FggNFohHQ6jc9+9p5x9LuiWq168ouiCKlUCrPZDNPpFK+++ipefvlljMdjjMdjzOdz/ORP/iSeffbZj3R+BoPB8Ljh3XffxWw2wzPPPLP09fl8jt/93d9Fv9/HaDRCFEUoFov4g3/wD37gfTUaDUynU8/fAJDL5ZDJZPD1r38dX//61zGfzzGZTDCfz/EjP/Ij9x0PDAaD4eOIw8NDzOdzbGwsX6A9iiIcHR1hMBh4PkwkErh27doH3tf6+joymQxGoxEmkwmiKEIul8N0OsUrr7yCb37zm3DOYTabIZvN4gtf+MJ9j8tgMBg+jnjnnXcwn8/vO4+fzWb41re+hfl87h+ZTAaf+9znPvC+crkcJpMJZrOZF3iHwyGm0ym+9rWv4eWXX0YikUAqlUImk8H3fu/3Gucazgwmxp5THBwc4Ctf+QrW19dRrVZRLBZRrVbvWWG9Wq1iMBhgZ2cHu7u7aLVa3t4/n8/R6XTQbrcxGo38RD6ZTOLVV19FqVTCZDJBt9tFv9/3JVmrq6soFot+wg+c9s8CgEwmg/F4jNFohGaziUwmg2QyiXQ6jXw+j0wmg+FwiEQigZdeegkvvfQSptMpptMp/tyf+3OWyTIYDI8tdnd38Ru/8RuoVCpoNBqoVqtYXV1Fo9GIvW9tbQ2j0QiHh4e+r+BsNosFpZPJBKlUCoVCAZlMBul0Gm+88YZ3ak0mEx98ptNpZLNZ5PN55HI5ZLNZJBIJjMdjPP3001hZWfFcTj4fDoeeT+fzOaIoQjabxVe/+lV861vfgnMOURThx37sx4x3DQbDY4nDw0N8/etfx/b2NrLZLEqlkn8oqtUqMpkM6vU6Dg4O0O124ZyDcw7z+Rzj8RiDwQDtdhvAqSM2kUjgxo0bcM4hl8thMBig1+thOByi0Wj4yX06nfYVBePxGLVaDdlsFpPJBOPxGN1uF4PBwDtu0+k0UqkU5vM5ptMpXnrpJS/OJhIJ41yDwfDY4uDgAF/+8peRzWZRLBaRyWSwtbWFS5cuxd5XKpXQ6XTw1ltv4eTkBMCiqpacOxwOkc1mvQaQzWZx48YNr0H0ej3M53PUajUAcVcrHbTD4RAXLlxAr9fDYDDAcDhEr9fDdDpFOp32x0Pz12g0wm/+5m/6qoVUKoUf/dEfNc41PDSYGHuO8Lu/+7tIp9OoVqu+BJUBonMO7XYbzWbTT+YrlQrS6TRqtRoSiQSKxSLu3LnjhQAuOrCxseGz+olEwvdhyWQy6PV62N/fR7fbRS6XwwsvvICVlZVYeRYA7/BiMNtut7G3t4dWq4XxeIx+v+9LZIFFaVgymUQymcRsNsOXv/xlfM/3fI/1fjEYDI8Nfuu3fgulUgm1Wg2ZTAa/7/f9PhQKBRSLRR/87e7uotvtYjqdIooi34LAOYfJZOKDQAaGuVwO+XzeT/I5eadAQI4eDocYj8eejzOZDFKplH9vPp9HsVjEysqK595Wq4V2u43BYIDxeIzpdIrhcIh+v4/ZbIZOp4NEIuHb0/yrf/Wv8P3f//0oFouP+EobDAYD8K1vfQvlchnFYhHOOTz//POo1+uYTCZIJpOYz+dotVpwzvlEFTm5XC57nmWin7FtPp9HtVpFFEVIJBJIJpOej3O5HDqdDlqtFiaTCdbW1gAsYlXnnOfmYrGIyWTiXx8MBrhz5w5OTk58LDwejz3HTqfTWLz8jW98Ay+88IL1ljUYDI8FXnrpJe8qnc/nWF9f97EmAD+XJ49ms1nkcjnfM3tzc9OLrtoiYD6fez6mAWA6nSKbzWI0GqHf73tOpVGBbV+I6XSKRqPheZVtEJl0o0NWOZqJMnL9r/3ar+ELX/iCxbmGhwITY59g0E2VzWaRSqU8+ZEgGXRq5p/vTyaT3knFz5fLZZ+JYraq0WhgdXUVuVzOEyEAL/L2+31UKhWMx2NUq1U888wzyGazXgCm8MDSr9FohF6vF3PFtlotAAunVxRFmM/n3nHA/9MVViqVvEDBxWim0ynm87kvUfjUpz71KL8ag8HwhKLdbmMymXgeJU8555BKpVCr1RBFEcbjMXq9Hvr9Pvr9vnf8z+dz/zl1FhDJZNI7uxigAogFvsBpAEoxldukWMtkWDabBXDqUKAbtlqtot1u++Oik4ABaxRFPlCdTqe4ceMGarUaCoWCP0ceB0VlCsv3a7tgMBgMHxbj8RjOOe/4r1QqvgogmUwil8v55BcfjCEpxPL9OvGnMxWAF1wTiUSs1yHFBRoFUqmU51ZuQ8HjVNFgNBohmUyiXC6j2+36arLhcOirG3gcwGl5byKRQKFQ8PtjLDydTv05ftg2CgaDwfBeaLfbPtFPxz/n4pyHk5uA06RTJpNBoVBApVJBLpfzVV3kSVZ4qehK3tNkFFtzsZ0ht03u4+f54Ov5fN4/Nx6PUSwW0ev1YpzL2Far0ObzOW7duoV6vY5isRjrL06e5Xknk8lY/1mD4f3AxNgnBCQMEmMikcBoNPIZ9WQyieeee86X+ZN8uKrgfD73/65UKigUCl4QUKt+o9Hw5VXJZNKX2qqgy8CYfbCq1aoPHCuVyj1ZfooQFH3V7TqZTJBOp70YQJcs98FgeTqdotfr4bd+67fQaDR8lqxQKMSE2Ol0islkgitXrnjXhMFgMHwYjMdjTCYTH0RGUYRms4nhcOgz/p/4xCeQyWT8woTHx8de4Oz3+54P6bACTif0dK0yYFUBgNsmrwEL5xWhx6R9svjeTCaDXC7neZTJK4oT/X4fvV4PvV7Pu3Pptu33+z54ZV8v8vd8Pkc+n/fvV5fvxsYGSqWS8a7BYPhQYKxLbmNMS5drIpHA888/77lxMpmg0+lgMpkgl8t5AYGcWyqVkM/nfQKJ3KyTcq1GIF/O53PPuTopp8NWF0UEEBMImFwjNwNAvV73xoV+v49Op4Nms4lut+uTduwx65zDyy+/jGq16nshFotFJBKJmIDrnMPm5qY/boPBYPigoMjJytfpdIqTkxN0Oh3Pm41GA+l0OpY4SqfTPomVy+VQqVS8oEkHbBRF3rFKk9ZsNovN9Sm6MkalHjAajXwSjcfIigMKw/wLLCrLMpmMj6HZu7vZbGJ/fx/NZtO36tI1amazGV555RUUCgUkEgl0u11fPaHcmk6nsbKygnK5/KEWGjOcT5gY+4Sg3W6j2+1idXXVu1kvXbrkMzYnJydotVoYDod+ckyC6Xa7vgVAqVTyRMeME8krl8uh0WjgwoULvpyAfWNJ1Hye2Si6pyiC9no9/zkA3mXFMjD2i6ULdmVlBdVqFQD8sTIjR/GXogFFkc9//vPI5XL4Z//snyGKImxubmJ9fd23L5hMJvjyl7+MH/zBH7SWBgaD4UPj5s2buHXrFi5fvowrV65gOBxia2sLAPyiWIPBAM1m03MgXVB0zpKPOZFnoiuTyaBWq3mnPwVNCglhFYI6XhmE8sEJP52x3D4XSSR/A/AiA91WnOxr2Rj5mYm9ZrOJF154wZdzjcdjX4mRz+c9t//rf/2v8cM//MMoFAqP4NsyGAwfd/R6PYxGI2xubiKKImxvb6NcLvse2oPBAK1WC6PRyHMkTQqMe2lUoICbTqd9uSpNBLVaLVZ5ACy4kdvithmLkqMHgwEA+PcSiUTCmwOAhWDAx3A4BAAUCgWUy2Wsra351jDD4dD3rO12uzg5OcELL7yATCaDX/mVX0GhUMDq6qp3y9LB+41vfAOf//znfRWEwWAwfBAcHR3h4OAAtVoNFy9exOHhIQqFAnK5HLrdLjqdDqbTaSxpxfZX5XLZJ7vIhay6Go/HABZ9YiniqskAOOVRVnqRI/m5+XyOwWDgF5jVCgIAnqcZ93L70+kUuVwOV69eRaVSQSqVwng8xsnJCa5fv46DgwMcHx/j+PjYr6UwHo/x4osvolAo4Etf+pI3T5D36eb9+Z//efzkT/7kPX3JDYb7wcTYJwCDwQDFYhEXLlzApUuXkM/n8fLLL+P1119Hq9XygifbAbCJda/XQ7Va9e0GcrkchsMhms2mD0aZdeKqgnRFkXzpXGXZlRIfJ+u6Wmw2m40FoCwXy2azXpQATsm3UqlgdXXVlxiQLOkYoCuNRJjJZLC3t4d33nkHh4eHXqDY2dnBzs6O76sIACsrK/it3/otfPrTn8b6+vqj+eIMBsPHFsPhENeuXcP3fu/3Ym1tDc1mE6+88gqazaZfaZsTfSacWOZPvmJpFRdA7PV6AE77dddqNTQaDV8CqwIrsAgutf2A9hgE4kEiy2PZRoAiAh0FXMwgmUz642XrAT5HvqXAy+CYQka328XW1hYODw+9o4sB+Gw2Q7lcxm//9m/jU5/6lO+paDAYDO8HdNevrq7iypUr+PSnP41/8k/+CX7v934PrVbLt7Zigon8CMCLBcCi5HU+n+Pw8NCLo2xbwCSVTuw1NiUHqtgLwLcqIP8zyaXlstw2twfAjwPaA1YrIbhuQyqV8vH8YDBAv99Hq9XC5cuXsb+/7xe20XLfQqGAb37zm3jmmWdQr9cfyvdiMBieTOzu7gIALl++jEKhgNlshnfeeQftdtu3I1hdXcXW1hbW1tawubmJ1dVVlEolXw3GRL8mw9T1T0MY3fx87zIjAXmVfE4uTyaTsbYGys/8POPufD7v9Y2joyO/Fk4ul8Pq6io+85nPYD6fo9/vo91uYzgc4uTkBMfHxzg8PMQbb7zhq+ByuZyPmzkO1Go1fOlLX8JnP/tZbG5untl3Zfj4wsTYjyFIUJPJBOVyGevr6z5A3N3dxWuvvYavfOUr6Pf73s3KyTSzUblcDs8++yyef/551Go13wel2+1id3cXOzs7mEwm2NraQiqVipULAPAkx55c7BXI44uiKNbegBN6ZsL0fSxBYPaLZQpsn8ByV/ZkqVarXkggOXPf9Xodly9fRrfbxdHREXZ2drC3t+dbHAyHQ9/39uDgAJcuXTIx1mAwfFdw1etqtYp6vY6VlRXPxbdu3cLe3h6Ojo68i5Q8xjIsBo+1Ws1XMJDHGOzt7Oz4/ty1Ws33+OYkn9vV8ictg+W4wMCT44L+H4gHquRuLd1VgZgJMybFAMQcCCz5Ir82Gg00Gg0fxNINPBgM0Ol00G638dRTT5kYazAY3hOM7wD4xWfJQdevX8dbb72Fl156Ce12Oyaokuu0NzZ5SheHSSQS3qWVy+WQyWS82MnXlTfZUgZYiLLcJ7le+VK5mVAO5ntVhNAKB7anUa5mEi+Xy6Fer2NjYwNPPfUU+v0+dnd3cXh4iE6ng8FggOl0im63i+PjY6yvr5sYazAY3hNcdNs5Fyv/Z4zabDaRSqXw4osvetF1ZWUFly5dwsrKim8ruL+/j4ODA28wYBtExrX5fB7NZtOvQQMszAMAvPmApip1tjJmZVzMz7KljMbeytMKtqFR9Pt97OzseO1CTQqbm5vY2try8frx8bEfg7hOhBotlrl0DYb7wcTYjxmY7dam0exvwizOq6++6lsWsMzfOedbFOTzeTQaDVy5cgUrKys+206HaiaTQafTQbfb9aWmFEO1OfZ79aDS/rXqjCKhLiubJfky+GRwzHJeni+PU8t2KcY651AqlVCtVr27LJ/PY29vD91u1/cDYwmxlkIYDAbDMtCZT14bDoe+FJYu0l6v50VNnVjTqcQ+sJVKxS+GCMD3PwSAg4MDpNNp32qG/MheiPrgfjVBxvYt3DeA2MrfWpGgQkGYOMtms8hms75nli58A8ALzhRw2eaGIkGpVPIVGBR1tU+uLkhmMBgMIdjyCoBvY8Ve1XSGkoN1Fe5MJhOrSmAMGU7egUX7Ak64KYTqJF4n8uRUfU0XwVXhFIDnWT7C6gbGu1rlEL7OfWisTAMDAL8oDhN8tVrNJ70ODw99Qm2ZIGEwGAwEF88ejUa+pQorqcgzW1tbKJVKfn5OzslmsxgMBr4l4uHhoV9TAICPAwHgqaeewmw284vGhrwZagu6MBi5l7oAX9de3HxOY1o+GKNT09B9hiYvrf7S7SQSCZ/ceuqpp3Djxg3cuXMHR0dHGAwGPmY2MdbwfmFi7McIJB6SGLNVzWYTrVYLzWYTh4eHuHXrFp5++mk8++yzWFtbQy6XiwV9pVLJl8Cy/x9JMpFIoFwuY2VlBYPB4B7HqpZlkRg1QCToaA2baDOY5fYY9FJIoLgBwLcVoNhKcqRQy23oSrIUbCkmVCqVewhfif/OnTtoNBq+DYPBYDAomAAjdwyHQ9+3j30J1SFFMYC8SB5j4FosFpFKpbxblgEjV/SmiMDyVm5DOReA/zwAL5SORqP7urHI5doblm5XfZ77UTFDBVktAyN04UiKzoVCwfcfHwwG6Ha7/vOskjBR1mAwhNBVuJlAiqLI94tlTMcWXeSqZDLpS/0p0JJ7gfiiLuQrTXQBcccroS0KCAqc2ppAHbWavOK+dPKv7bp0xXE9Rj0OxrrhgmD8XC6XQ7lc9v24J5OJd8myGo5txQwGg0HR6/VwcnISW0Mmk8n4Ki1WPNGtT6GV+sDJyYlfFyZsUaXts8iDrFYNuU6FU+DeBBi5movPEmxLAywqGLSKjMkrisuj0eieSgYmucjBoTai1RdckJzrOtRqNezu7mJ/f9+vZdNsNtFsNlGr1R7yt2f4uMPE2Mcc6ipNp9O+X2un00EURRiPxzg6OsLx8TGOjo7QbDYxm83w6U9/GteuXUO5XI4txKVBJxe+4vbVQVWtVnFwcODLVekc0CCWbi9O8rWPFjNKfJDc6dDVRWJYWkAnFbNxoejA57hPBr7sE8Zj1AVpcrkcqtWqX5iM4gaP63d+53fQarXwR//oH7VFZQwGQwxM7HQ6HRwfH6PZbMI55/tVA4iJpiy/Yq9tFTq1pxSDPnUspdNp1Go1vy0GqRRjNbBlv26Km/p/hQqvFCsoLNNRpkJq2NqAQoX249KWBlygkfvShBudvVEU+ZVv0+k0NjY2MBqNcHx8jLW1NVtx1mAwxMpTs9ksptMp+v2+X9CQk2O67SeTCYrFou+nygVk8/l8LNEfclootDJWJC+q6QBYtA0gtL2WxuW6YBjPJ3R8MQ4mh1OI5bmpMKwOLa2iCNsc8HnG8sViERsbG77qjdfrzTff9NUb71XVZjAYzg9YXcXETRRFyOfzWFtbw8bGBi5evIgLFy5ge3sb9Xodb7zxBr71rW+h1+thNpshlUr5Mn1gsfgWOZfbVw7jXJ8cRw2A8S45iq/rmgkAfJytHM92YNyXJq20vYxWgYVVA5o8U45W4xlND3QB12o11Ot11Ot1XLlyBTdv3sRoNMLNmzexs7OD6XSK7/me77E41/CeMDH2MQfFx4ODAy9A0m00HA6xs7ODZrPpm/mPx2N8+tOfxmc+85kYWdEZyr4mJCNO8pmh4sqEzIhR6GQPFG2YTQIbj8e+pIGiQLfb9W5XblvdXsDC/cDeNKPRCO12GwBQLpcxGo1Qr9djwgRJkiKAOgUo0mppVzKZRKfTQb1e9/2zDg4OcOPGDdy+fRuDwQBvvPEGDg8P8Rf/4l98NF+ywWB4LMG+03RXsQSULihdlGttbQ2rq6uoVqt+9VhOssPFtujUymQyfluj0QiFQiFWNcAJt4qs5OjxeOwn/BwPAPjAFVi0gdEVvCkQMPBVR4G2RdCyWg1Gtce3LpbD1W55TShysEIBgD9PCrT9fh/Xrl07s+/TYDA8nnDudLGpw8NDFItF39+UjqvDw0OfBCPHrK6uolKp+MSPQs0BTCJls9mYSKrrG4StB4CFsKCuLXW0kouPjo7864SKD0yUqZuLjism2yjS3u/aqAhL0wFdrhQJyuVyrIz3E5/4hF9T4jvf+Q5efvllvPbaa7bKt8FgwGg0wttvv41+v49KpYLNzU1sbm7i6aefxtNPP41areb5lfz02muv+R7ddMgyvtUKsnC9AQCx+bsKr8RsNvMcTTjnvJGABrL19fVYBRe3q4ktdd3qthiD1ut1jMfjmCmCsTew4G0myTTxxW2FCzfm83lcuHABURShUCjg6OgI4/EYN27cwNWrVx/iN2n4uMPE2McQw+HQ29pp95/NZuj1ep7w0um0JxK6ljjJZfDFyXmhUPCTegoEQLyUSoPOkHjU+aQkqgsNqLOVoi1dDAxuSWoUEegkmM/nWF1d9fuYTCbodDro9XqYz+c+ww8gdv4MwFU4oOjAY6QQwPc753yvMYoB3W4X/X4fr732GlqtFj71qU+hUCjg3XffxT/9p/80Vi6XTqdRLpextbWFP/yH//CZ/SYMBsPDhYqf5LxSqYRyueyz97qIofaP4nt1gRnglHsZxGpJKRfJChdwYdKL4H6ZiOJx8rMMLlkWxX0Ci6CRLWR4/EzIZbNZf65srcB9cPt6XSgm8/O62rdWVuRyOc/FTJil02kUi0UMBgM0m03ft3s0GuFrX/saEokEnnvuOeTzebTbbXznO9/xrXem06lfsffSpUu4ePHiQ/j2DQbDWYOTVmBR5v+Zz3wGuVwOvV4PvV7PJ+ppDEilUn7hFYqY5FBya9iKICx7Ja9S2GXMymSTVqRxm8BCbGV1VyKR8O1quPgXeZD8pyIxx5VcLufb1WQyGbRaLS+QhrzNY+D1optWRQYmwWi6ODo6wo0bN/Ctb33LCxeTyQTHx8cYDofY3d3FbDbDlStXkM1m0W638dprr6Hb7aLZbPqEW7FYxObmJl588cWH8wMwGAxnirfeeiu2ZsB0OsWFCxdw8eJFbyaoVCqx9n/a3k/XTWCSSRP6AGKJfu5LW3cxOabJfzprgXtjymQyiUKh4Hl4MplgZ2fH966lbkBtgQYxVqeRi4vFotcAmLwil2srRXX0AgsDA8ExhWNWu92OGRBarRYmkwlWVlawsrKCixcvYmVlBcPhEO+++y6GwyGOjo7w8ssv+x69wKmhjAsz/sAP/IBVMJwjmBj7mIBlo6VSCZlMBv1+H8PhEJ1Ox/+b/QQphObzeU8mzWYTOzs7ePvtt/Hmm2/ii1/8IorFIqIo8outhMGpEiODUxU0Gehq+UDouNKgk5NrBsosL8tkMp5sSZp00EZR5AUPCrvc/3w+R6fT8eeqLl4SOLNa2k9WMZlMUC6Xkc1mAZySaqlU8gvkcEAhkbZaLaTTad+Hd3V1Fa1WC9ls1pP94eGhD1pXV1fxwgsvYHNz82x/MAaD4SNjMpmg2WyiUCj4lgIAvIsfWCTEyKMsjWXARp7m5F4dAslkEvl8HgBi4iWw6ENIrtNJvyaUtIJBn+cEnCVZFHK1FY0Gk6yM4HkQHAfodiUfLusvG7p1yZth4MzxRMcGJgMZQOuYMZvN8O677/rxJJVKeRGcybLr169jZ2cH+/v7uHjxIiqViu8PaTAYPl4oFosxXqjVarh69Sq2trbw9ttv4/j4GCcnJ36xVcaH5LfZbIZut4tarRZbfCtsRcB/h84nALGYc5loqxytC88ql5PzNeHGWFRLZenMVdGX8Tn7Jy5brJbHErpyQxetrjROjmYcqwv+JpNJlMtldDod3Llzx485LLVdXV1Fu91Gu91Gs9lEt9tFu932ZcvlcvkB/goMBsPDxnA4xP7+/j0xYC6Xw9bWll+AazqdotVq+daHXGy2VqthbW0NtVoNKysraDabAOJcE7aFYbsZch85jHxTKBTQ7XZ9Yl7n/QDuSaoBp3F5pVKJtV6kaEqDgVbc0kDGal8eE/ddLBZ9LMr9qgjL8YHb1/NlTBzqDvP5HOVyGZVKxcflbGMDnFb9rq6u4uLFi7h27Rpu3ryJ119/Hfv7+55rB4MBvvSlL6FUKuHSpUtYWVk5k9+J4dHBxNhHDF2VmhNuXWVwPB6j3W4jmUx6YmTwyF4os9kMjUYD1WoVuVwOb731Fl555RW8+OKLqNVqPuMU9tHS4FGt9zweBp+cHDO4VFeUtgXQAJEiKcVYkrD2OaRQsbKy4t0NdLDyeHq9nidrtiEYj8d+u3wOwD0BNY9fnQk8f54fXcP8LvSc19fX8alPfQo3b97E4eGhvz7j8di7NthWodVqoVwuY3t7+6H+XgwGw0eHrnbKzD25hBNlOui5UAH5qlqt+kQXy7TS6bTvcchgUJ1X2i82dDUBC0F02aI1FB90Yh9OzFXIZSAaBo4qslIYyGaznkfDElstLQv3qW4JLfnV9/KY9P90XPG4WDLGvxRm2d6A14aVF+12G2+++Sam06nvYZbL5awfl8HwmENjOIKcwFW6G40GDg4OcHh4iOFw6JPu5BbyDI0CbM/FRbzIK2GMqxUGGq9SRNBYWMFthCBXp9NpDIdDz1XcB8cL5UTGrDq5Z7WCVl3QcKEJLl4rbTXD41Pe1pYKALzYq23CcrmcF33Dlcy5UA97njebTfR6Pdy5c8evuTCdTv3rBoPh8QV7ubJ1oLYwobBK7iyVSn4ezliWDs5isYijoyNcvnwZ6+vr2Nvbu0e0BOANV8BinZsw8c85tC7MHXK0Jt4AxOI7xpLKP/wMtQbGlGyJMJ1O/ULmTEhRS8hkMr6aTWNb7kvb2oQaivJ2qJ1oBcZgMPDb41jBBGS5XEa1WsXNmzdx584d7O/vo91u4+bNm75tWbfbRT6fx/r6+kP5nRgePUyMfYSIosiXN9HlyQkzAE8QuVzO93OhGMtJOIVKzV7t7+/ja1/7GorFIj7xiU/E+hCGGRzui3/VFcCs/3g89q0LlJgoMpBMGQhmMhmfEQtJlCTJEig2v+bCXXSoAfCE2e12MR6PMRqNfCAbCqwUBTQDB8AvGjMajTw5aisGHQwYQCeTSdRqNX/dc7kcOp2OL+dlWRcdyW+++SYODw+xsbGBXC6HRqPxsH4yBoPhAUAXFqQoSWGSvNpsNrG/v++dREyG0dkJnHIoy6QODw99Bp6cQkEhFD21SkGdrNpDixyv1QxAvC8hEQaBwGLyzioEXfCQi4zxvDlRZ0IrdHrp8YTurFCs4DGrmBzyMt83mUyQzWYxGo1i50ehhMKELszTarVw8+ZNf25c4dfEAYPh8QQNBEC8jzXF2UqlglKphF6vh+985zvo9Xq+JRT5RltxkVN2d3fR7/fvmfyqSzR0yzLW4/EQoStWY2VtMaOczF6BOplXgZfb5XvDpBF5lGYHiqx6bOoMI/g+5VQtI+a5hKXAGr8nEqe9y4fDIQaDgRdbGcPmcjkUCgU/DrbbbRweHsI5h1qt5sVog8Hw+IFCKh2Z8/nci5WNRgNPPfUUtra2/H2v1aO8r7k2wdHREfb39zEcDnHp0iWsrq4iiiJfMaYLyZL7GHeG5f2Mi5lEpyhKA5caDMiXjJtpntA4NxRu1cymbRUYV1Iw5uKGwKnGou1utFpYE2dh/EpNg9DWMjQehGYKjYspsD7//PNYWVnB+vo63n33Xbz99tvY29vz7Q9OTk5Qr9dRLBZRLBYfyu/F8GhhYuwjBEVJZq8TiQSOj4/9xJ89Ua9evYqVlRWfVdegs9frodvtotvtIpVKYWtrC8ViEb/wC7+AX//1X0er1cIP/MAPoFQq+QyRLgjDv+qe5fNsLcCgGVgEsiSUfr/vFyVgyT9bCmiGCzgldk6+V1ZWfPsAZsroOmMvQwBeNKADot/v+1IDHi+PmddLy245qGj/QxWgmT0jYXOSwNW/E4kENjY2cO3aNRweHvrrxFYGvV7P92u8desWDg4O8OM//uOxvpEGg+HxgpaCMjPOzHm73catW7dw584dFItFXLhwIeYgUPGRARVwGvz2ej0fNLIXIqGlVQz6KKAy4Eun07FFXnRfPGYmxVQgYHCnE3ANLBkkc4JNFwQrEchXbIGQTCZjK80qb3L84Gv3E27D0rVl7RN4vHTDKn9zO3QTZDIZFAoFZLNZdLtd7Ozs+J7fqVQKzz77rDlkDYbHEEykqJueSRZOLm/fvu0X6crlcsjlcjEXLVt5kcsymQzW19fRbrd9v2zt7w0sTAXL3P3cplZFqJtVhVcgHkNykq+va2UYeVrbaYWtB4CFwzaXy2E8HntzhVZEhFy6zClLhNUMOi6ElRR6HSnSMqGovRhpmtjd3UUmk/HtD6bTKTY2NpYmBw0Gw6MBeerWrVt+3Rit1tra2sIzzzyD1dVV3+6qWCyi3W77+Sx5hvNocu9rr72GZDKJCxcuoFar4eTkBEdHR2i1Wt6Zqovb0owQOmh7vZ7/fyaT8evHAPBuU+1rC8RjRuecX5+GvbrJh/1+34uYmogCFhVxek0oKDMhOBwO/Tkr95LPw9hb5xKsNGA8zRibHEuuZ2JuMpng4OAA1WoVjUYDjUYDV69exdraGr7xjW/gzp07fmHz+XyO3/u938PnPve52LhoeDJg3+gZg8JdLpfD2toaoijC8fExms0mcrkcNjc3MZvNfMamVCp5qz2AWEn+bDbz22k0Gn7bL7zwAur1On7lV34Fv/mbv4nvfOc7+OIXv4jNzU0/0efEmlkdzeYAiC14pYElF4AhqXGF8fl8jnw+70uxKHJSvBgOh95qT8GVpamJRAKDwSDmNuMxafkBBYEoirwbis9pWYGSIx23DMhJwNpfjIMFCY8DD/vf7uzsoNvtolAooFAoIJfL4fDwELlcDkdHR76kg8f/yiuv4Nlnn7UVaw2GxwjM/nPCTQ6s1WqoVqu+Hyn75CWTSTzzzDPY2tryHMhWJgzuyMP5fB5ra2uxBFk+n0ej0UClUvFBaTjx1+QauZOTek2AAYtAlvsPhQPtucXzI4c555DP52McFvYwXFaixf2rQ0CFARVNKQRQoGCwqUGsBrM8L21XQDcDF15gf3AeTyaTwdNPP43BYIDj42N0Oh0Mh0Osrq5iZ2cHGxsblggzGB4D6D3OqgDer6VSCVeuXMFgMMBoNEKn0/Ex7srKiv+cToY5qSb3RlGEWq2GVCqFw8NDtFotHBwcYHV11SfOgLiIqs5XxqbkKX2vVl6F7iqKyureVy5Tdxd5LDwOvUbkWy7ewmsym828wYDHwXGB/K5mBAoThC46prGzno+Og3SqDQYDJBIJFAoFFItFnJyc+Eox9u9OpVJ49913fYsxg8Hw6MGqIa0SYlVBpVLB5z73OVy7ds07RGmOYjsutkyhQYyfB+Dj0+vXr8M5h42NDayurno3561bt3wcybhP5+M6316WrNfEljpRdSFExoqh25+cxvaBHEu0QiLsS0thtFAoYDKZ+P7kNAeEQjBbSqpQrbyqiTJWemnVrnPOC7F0+ubzefT7fayurvq1KRKJBH7oh34IpVIJr776qjcdDIdD3Lp1C1EU4YUXXjB94QmDibFnCDp4Ll68iPF4jJOTE8xmM5+JYY8m7bvC1gWVSiXmLCIxkSQYrDJg2tjYwA/90A/hd37nd/C1r30NL7/8MorFYsyhQAIJSZGO0tFo5AO6Xq/ng1YKEBQ3NMtOIZk9qpjtp+iby+V8GQEJjcEsywR0te6wtBWAPzaSI68dM1Ba6sum5EBc3AVwD7FTmODqhswqFotFZLPZWMBNNwfbKNDpMRgMcP36dTSbTS/E8BgYyOpx62q+zjk/2bDsl8HwYKBZdk6I5/O5b4mSTqfx2muv4Y033gAA704l95AjGFgymAp7vLIPF4NCJne4X97fdHpqwMmqBeU68hp5ScVYtkvRFis8VnIoE2ecpFOE5fEzEKULQINVJgCVO7WFAa8FxxItq6UDQ90K6lTjMXEcYeKL++f+eFw67nH729vbWF9fR6fTwdHRETqdDpxzfvV14FQoZ1BO5zMXU9jf34+t3uucw+rqKp555pnYvgwGwwcH2281Gg1/HzLuYV9SLoIatmwhD7J1ivKMJpvIeUx6HR8f+8VPmHRjTKuOee4vXCwxTBaFjledbHM7AGItwMhtjCdZNUEu5Pij22ALB5bq0tTAJBrPg6Dgqm1ouE/+n9cxfA/BNl0URZxzKBQKcM7h+PjY87ImDnlcFBfS6bQXZLlmRSqV8hUiNEJQNMnlcv768JrqopkGg+HDY29vD91uF6VSyWsNwClfrKys4LOf/Syef/55zGYznJyc+NiIa6CwFaDGRWo8YAzY7/exs7ODbDaL1dVV1Ot1nJyceBOWzr+VNxnDamUtAG+WIq9prEuuoJlBE/Nh6T8X0tUYViu3+LzqHZy3syqu1WrFuJ+fGwwGPrbUeTnPQc0JoeFCBVxeS42ZR6MRBoOBfz/bxjz33HMYDofIZDI4ODhAs9lEp9NBt9vFm2++GUu88fzVCMFYW79T7uMLX/iC6QuPGezbOCPohJ42+k6nE8uM0zVQLpe9K4g9CDVg0YBwOp3e4xbgYjKbm5t47rnncOfOHdy6dcv3f9FyV36GIIFpXxV9Ply9O5vNerGDAR5LrYBTAZoBGd0K6lRlEEvRQV1YJBV9aIksg2ANdNlXltcrk8nEettShNAsmw4Kx8fHvjeOZrIajYZv28AMFnuVsQUERXUKuYPBwAfaPDYOePP53A86bO+gPXdff/11L36n02k899xzD+unaTA8seD9pq1JeM8yWGGpFXunqphIkY9CHisLyHcUSulkovPfudPepr1eD+VyGblcLiZAcvKsE2tOijlOcAKbz+e9G4rvZbCnQW3oNGWiRyfDmqzi+WqSSifp2v+Kr4UCrLomuB0NeAkVpSm6sk0Mk3XqRlABW5NnHJtmsxnK5bJPMHIyoMfI73symfjVbDkOaD90Vm3s7u7i4ODAr7BbLpdx4cKFh/0TNRieKDD5w1ZSw+HQL3LIFla9Xg/NZjPWn5D8GbZuUad+KJaq44hxGBP9ajBYJvjphDsUPXXfGofyc+QZxncUT5c5eNXxBSzi91AcVe6jk1jfH/5bncfAwgmrwrPyM7Co5FC3lsbCPDduKywfplEjiiLU63WMRiN0u10vnFN81iQhOZ6xrrZh0JJoXmerbDAYPhh2d3d9uT3jPi5adfHiRXziE5/Ac889F2sByBhURUP+1ThL+TORSGA0GuH4+NjPjTc2NlCv12NtDTTODaupKJBquy2N/TTpRh4aDAa+7ZfGzTQNUNDkPhXa9opco3oG98ljZyzKWJXzeXKWHnOY5ON5kCc1ocj4VBOMvMasNlBOzuVyuHDhgr8O2tN3MBh4rYeVbblczn/vXJhMxxR+djqd4tvf/rb/XD6fx9WrVx/wL9LwQWFi7BmAWSreuO12G81mE81mM0Y8wMKFyYl2v99HFJ02ymaAq0EaCYFEShJg79bt7W0899xz+OpXv+oJEri3v5X+n2TDm5mkpZ8lOTAYJcFyW3RV9ft9f8NruQL3w4BaWxSoi4pQV9qy9zCQDMvLQjeClkiQNHm8JycnODg48NecwTTFm2Qy6XvW6kClgSsJmS0jAPiAkxkqDZDL5TIA+M9wYHn33Xf94EPHdK1W+3A/QIPhHIKBFMVY3vfZbDbmBB0OhygUCqhUKl4E4H1K1yqwmLRz4sxJMwD/Pq5KG0WR7/Wk5aEaLHLSzuCMfKICKhfY0sUNlAt5nupCVccTAD8mcILPoJCTauUkdZFRANZAXN1kWt5LAVu5NQRf44SBD+0DqRyuwa9WTJDX0+k0isUiGo2G/z44QVAXNBNhdG3QEaxOheFwiHa7jXa77cXYarWK2WyGp5566kH+LA2GJxZMHnOix0kyS9/n80ULqMFg4HlI4x+NvfgZbSel7igKoalUypeb0t1EruTEm9sC4lVSKkKoIMC/WmLLGJlCLHkm5CUVFPUY1MFKkEtVCFE+Vf4NeTWMpXlsum0VtPV5XmutYmBcz2vLcZLv1bY65XLZu18Z50dR5KstOA8h9xaLRQyHw1iliiYlVbww15bB8P5ALYHg/ZrP53Hp0iV88pOfxLVr11CpVNDr9WKl9eSKQqHg5/K6loouKE5OAk7F0aOjI28+4MLi5AblIAqbyl/KycqPfD//zf3pfBtAbM7NsUTn1zRqaeJLY0IeA/fFcQSA7/tKDYOPkKPDagpuU68BRVhqN5rUIzcC8PzJa00eXFlZiVXT6hipiT/G6ox1uUAaEO8tTt7f398HcDpuFItFFAoFbGxsfLgfoOGBwEa8h4woitBsNrG+vu4FuX6/j06ng2aziX6/74mjXq/jqaee8s4q3lR0qYYrYGuGhhN4ZqU48S2Xy3jhhRfw7W9/2x8PyVFt/HwNWLic1InKyTChGfKQ5PheEjmDMyAeNCromCVp6rlpQMlj1qwQM0wkPZ3Mh4RMN5Q6iqfTKY6OjvDmm2+i1+t5QZzXgSVnWmqnwSZfj6II3W435mqmKMMVw9kDMZPJoFwu+wUQdBLQ7/f99SyVSnDOeXHWXAMGw/sDRQBmuhkkMeghB5bLZZTLZS8KaFZfxVMGOgyUyJHkNpYJkTdGo5FvOwPEe68Ci1XF6XLlMeqknhN7zeZTPGCmXCf5GkCTp8iLnGhriwMNCMlDDCSVb7lP7kN5Wl1W3A65UaFJOvIwvxu9HsqF6sJVgZw9uQCgUqmgUqkgiqJYSwd+l71eL5bAA+ATjfl8HsViEaVSCTs7O9jd3fUlccfHxzg6OsL6+rofbw0Gw/0xGo2Qz+c9t5BfyCtaBVQsFpFMJlEoFO5xy5OLybUqhAKIxXX8m81mUalUfO8/jauW3buhI1YrsoBFj1e+RxNW/KtxMuM6Vh+oq4znFrpxuZ8oivw4wLhXQV5UYZbb577C8yFPa/sXvX4URgHEnMzkTl4Pfid8ne/hmg3c/3A49O0MUqkUisUiptMpWq1WLJGmvcG1NQOr4+bzeWzOYDAYliOKIuzs7ABYuNHJM9vb2/j9v//34+mnn0ahUPALTgOLtWKY9KpUKj5RTo2C/9cKVGDBf+12G0dHR6hWq9je3o5VHSlf6eeVhzUZFkINCqpx8DVgUZo/GAw8l5HraFKjHsKqKG6P2yBvc3vz+dy3WdSKOnK7tiXjOTDm13heeZoVIeqW1QpZagPkc9UeSqUSarWab5k4n89xeHjok53qolU9JzSKhEm34XDo3b6TyQSvvvoqSqWSb1VjOHvYaPeQ4Zzzze9ZojMej7G7u4t33nnH33AbGxu4ePEiVlZWYhNnltSznPL4+BipVMpPQMvlshcN2ONJRVmKvM8++6x/npNnYGHz578pIqgQGm6fJEfSCYNBvjadTlGr1WIrs/L4NLAGECuFVcEEQKxPbRhUqmBSKBS8CAog1sR8MBj47SeTSS+4Hhwc4M0338TLL7+M4+NjrK6uYnV11Wfqy+Uy6vU6ZrMZer1e7LrpSpXr6+tYW1vDG2+8gXw+70tEuCiCChl67tVq1TcNz+fzGI/HvvcPW1k457wo+/zzz5sgazC8D7CHFEU6Os3p0qIIS/FAnVS8z8ltTBTxEQqH5D3e4+wfe3BwEHM+aQDFiTdfZ8WBCqL8jCbYtI0AKwX0nLkIA8eQQqEQS+SpWMDkkC4MyW3znMKgmcfMhya4+N7QncB9MejkZ9g2h/0ENfjWBwAv8HK8YrUIzyWVSsWCXB4zJ/gqyIfuZJZPp1IptNttH8wnk0l89atfxec///nYeGIwGO5FuVyOCbF0SuqK0IxfKbjlcjnfY5bxKfsYUtwL21sBC6e+VhpR0KOQq4kqnWSGwqY6hygSkv9UrOVknNxAd36/3/fHryJxaHTgvvV5dbSqsyosuSUvcjwgz2tSLORqFQ3It+Px2AurPGYeNyu6dPVxOsUA+Ao3cm2hUMB8PvdJQ4o55FLOYfj96XXgOMA+iESpVEKpVMKlS5dMkDUY3gPkNmAR+yUSCXzf930f/uSf/JNYX19Ht9vFwcEB2u22n7NOp1N/b5IDOO+nIYg8qAtXkZ84X2+1Wrhz5w7W1tZiIqQaBsilyr/k5zBZFkVRbMFDcgbHBAC+LzXHFE3w6Vig1cjUDfQ9Gm/z/+SvcJ5OLUB5OxQ4OR7wwVib17dYLMI55014zjmsrKwgl8uh1+thNBr5djtcAJjP1Wo1/51p60WOTcVi0XO8JhW1FQSvc6VSAQAcHh6i0+lgMpmg1+vhN37jN/AH/sAf8OvhGM4WNtI9RFDYHI/HePvtt7G3t4fd3V0cHh761WS73a4viaRdnAs+kZjK5TJOTk5iLlZmtlQsYHY9DOCiKMKlS5d8wKjkxqBZSUbJSl1LpVLJCxkMTvk+FYAJFTq0xQGPmw8VW5VEeYw6IS8UCp5YGEBSXL158ya+/e1vYzAYIJ/Pe4GbC2mRlJiZ/+Y3v4nr16/j4OAA0+kUV65cgXPOTxoSiQTa7TZu3rzpM4rqNEilUnjuueewtbWFer3uB5Fms+n7GVKIpjuEn+c5dLtddDodLybQ4cvVE+n0Ojw89G0trl69imq1+kB/qwbDk4LZbIZ2u43JZOIdryzxOT4+RjabRbVaRbVa9XwILMo7OcHVyTQDXuA0MKNLXZ2hDMCYhS8Wi1hZWfHOzGVBpbputf0MRdgQPC5N7KjLlxUSPD4mhCjIqqtXuVpLnrgfFV11Pwxatc0D+zQqd/Oh7gWtyKBQqtda98PtqtOV5Vdra2uoVCp+QYXZbLa0KoKJLA3WtV0C/3Iiw+CYCwEB8M7YsLzYYDCcghNBLd3nfdbtdn0ynEmhTCbjK7c4EWV8S7cQgNgkl/wQupFCVz4XUdSKAi3314n/Mret8r+KCNqGSysQ7lfCqrG0VhaEbVy0VQ3F0rCNgZbuOufQ7XZxeHiIROJ0LYZ6ve65Vw0KNFFwHsJjbrfbKJVKKJfLvs0AxyfyNxetYesdCt0cUzgOaHUIhYh8Po9er+dNCXTiDgaD2GIy2WwWg8HAL4IbRaeVhFzod3Nz01YNNxiWYDQa4caNG75VSLlcxrPPPovPf/7z+JEf+RHf3o6xDGOcTqcD4DTmZEuXdDrt5/ZM9ugcXysRgEUCazAY4Pj4GJ1OB4VCwW+P76Egu6yqgA/dFvmFfOWc82Iwt8F4r9Vq+XVwVGRd5sp1zvkxSA0Culi4jgc0UWk1nWonFFc19leTGuPafD7vY+/5fI5bt25hPB6jXq+jVCphPp+j2+3COYdqteoTmXr91PjBtR/I13yOYxZjehrC1NnL7885h7W1Nb9t6grD4RBf//rX8fzzz2N1dfUh/WoN94OJsQ8Rk8kEOzs7OD4+BgA/kdza2vL/Bxa9DbWsXjPybJLNEngGNvl83rc5IAEkEonYQiUUavP5vO+HSqIBFoKABplaVhX2I9RgW8tqSWLc32g08gFi6DbTElySDwlWA172RuF5sf+KOtXoUN3Z2cGXv/xlnJyceKIk0XJCz0l3t9vF3t4efu/3fs83uq5UKuh2u/eUqrHnI4+byOVyWF1dxVNPPYVSqeTPZXNzE81m0wed/X4flUrFv6ff78ccYQxWtU9PqVTyQaou/FYoFHBycoL5fI719XVcvHjxDH7FBsPHCwzUmAwZjUZ+wZjJZILV1dVYsMUSTXWeqjNSkyfkLn5eJ8p0YmkQVCwW/aRUJ/H6ubBKQPmYCHuqMqnDCTb/UpSk4BFm6SlOaIJL+VtFBD1mAJ6ftGyMIvfR0ZEvl6KDlK4qdevy/HUMmM/nfjVfbpcVDlygiw6qVCqFCxcuYG1tLRZcEioqcwzVsUyFa90XsBiPudAkF2m8efMmZrMZarWaT7IZDIYFmGRixRbvffISHfaa4GGSjDzCCavGv6ELXwVZ8reWwFPI7Xa7GI/HnqcZsxKclJPngIXzlI4pPbewsonbY9zGyi8VG0Khl9yt8aXyqbZh0aSSJpEYF9+8eRPNZhPFYtFfMybbdBzTsUG3qyItj4G8SSFWF63hgoZahlsoFHB0dOQ5FjiNi4vFIsrlshcMGHsznqXQwvGNgjyPk+fZarW8MGTrJRgMcUwmExwcHKBUKuGZZ57BtWvXcPXqVVy5csWLa4y5yAnj8Rjdbte3EFGTEFuGUABlMpqcxPuSLf5437Nit1qtYnd3NyYQ6ns11g1FUXKNLuJNruRYoOYCji/KF4TGgzx+crPqFKpHsMKKYxI1B91eLpfD/v6+b8XDRcs4Puh7nXM+VkwkEuj3+9jb20M6ncba2lpMQF1bWwMAHyvP53OsrKxgMpmg2Wyi0+mg3W6j1+vFdCGOP2xrAMQTjPxuqfFw/GQ1SqPR8MfKpNt0OsWtW7cwmUy8TmU4G5gY+5DQ6XRwdHSEZrPpy26AxY0/m82wu7uL27dvx5xFDOxIiiRBbf7MoCksB1UXE7DoRZXJZFAqlbC7u+tLkHhDM2OvJMm/PFbN6vPGDstYOcklOXISDSwIQh2t6vzSBQ/UWcAsmYoew+EQvV4P3W7Xu0cHgwFu3bqF69eve2G61Wrh+PgYu7u7sYXGKDCwfw7LrkjumUzGi7IAvHOYDlUOCvP53K+STiIE4AcLFYzpKgYWq7urEKCCN7e/sbGBTqfjg2IKLVwMjsKBwWBYoN/vo91uAzh1MlGI7XQ6vuSHTh0tMaLLnvel9uDWiTm5UHmP/Mt7lxNTJsEY5JCPySncBo+VUD4GFmJp+Jw6nTS4ZNDKAJPHS77V4FUTZer612oLHU/4HhV+B4MBms2m514KE5zw87y1xJjHMZvNvAjDSTorF4bDIarVqq+e4HFQZFYRISxtU8EHWFRRMOnH9/J74KSfiy5q+e5gMMDbb7+NWq2Gra0tE2MNBgF5olwuo1ar+Yks70veR7y31OGkSS7eg2HPQY0JVcRc1naEHMyYUcVd7lu3pX/DYwkn+GHSTPlH+Vs/E5oVdFuh0KxOWuViVhTwuX6/7/mWosl8Pvelr8uSe3RzTadTHz/z3EM3M8cVCiHqDlZnmMa+yreFQsG7XWmECB12mlAE4GN0tj/gb6HX6/n2QgaD4RStVguHh4doNBq4evUqtre3/UKmx8fHGAwGPnYCFn2gucgVY1YKdoybCOUBxkuMSyk+khPG4zGazSa2t7f9eikA/L3Mz6qeodoFHxqXa6Utxw9tO0ATmnI2j5ucHCbG+H/ytSbQOFZxOyp6stKMx7KysoK1tTXUajV/PXRRQq7TkM/n4ZxDv9/HyckJRqNRbIF26jhs5cDvhsaJfr+PbrfrBVpNXHI8476oCSifh+es4jL1lVqtBudOF77VcYRjteHsYGLsQwADJrYg2N7eRrVajfUTmc1mPsNzcnJyj7DKm1UdSsCijYCKCcAi082bTwO8ZDKJUqnkJ85shcDth3+BuEWeE3oAXtTUMq7QFQvAlwfzvQQdZMx6cZv8qwTKhtdsKM7Vro+Pj72YyuvM/icaOPJv2GuMg08ul/OuK/aDnU6nvoyDgjcFZpIkvxMSou4zkUj4UmV+TxzMuOojnWEUAzTQ5Xmzd3Cv1/PlYswaKhkbDIZT0IlO9z9Ln+i6YcabIoCWcpKXNAADEOspRTCwYaBKUYDb0N6iLO1UwVQTPNweA13lffJjOJlX52r4IP9QsFQxVkUAdR/wswy+NWAPj4PvY/DJflfs3UVXBEtd9Zppn/H5fLEgZLlc9gtMhOeSSCz6pbM1j04agEX5MIVuvq5OD21/Q4GW4gG5nOfJCYRzp4sgnJyceFc1E4wGgyE+aa1Wq37SyskthVFymsax2geWzwH3LvJKbgyFxlDg4+s0CzCe42uc3KsTNoRO4jUuVoSJMcazYVWVbkuPObwOWjHAMYWuNY5pfB97urKiC1g4qih6ks94/XV/rA7TKgAeB8enwWAQa/ui/K/nQS5VIZnvOTw89L23WTKrCctEIuHX0OBvgNUM2sM2vPYGw3kHDUiJRAJPP/00Ll++7OOSVquFdrvttYVyuexFQsasjPnIJ4Q66jW+VJeqJu9VAG21Wtje3vZr47C6gPsEFguMKecxLiY/c9+MifXfHCeUE3lMPC4dK4gw6UR+0cVx1RWrAi9jwGq1ipOTEzQaDVy+fBmbm5uxMYacqecBwJtBeLzD4dC3wtIqAx4vF6GkAYE6gs45gLg+EorIes5hUpGv0bFMM1o2m/ULX4bzDcPZwBSdh4B+v49kMolLly6hWq2iXq97cnLO+SzI1tYWyuUydnZ2cHJygk6n463jYaaHJKqZcwoMAO5xc2kJLB1a9XodvV7PT365IEpYbstj1EBanbdhVowl9yxLSyaT/pwpSKrArMdHDAYD32pBRdDbt2/7PrsHBwdoNpveFct9a4NxYBFkK7GTuKIo8t/PbHa6KBfJnRl9504ba9PlcXR0hOPj41jASZcqsFiRUp3IdDpTfJ9Op/7YAfiyaGbQuPK7Di7ZbNYLNuyDC5yufEkHnomyBsPpPd/pdDCdTlEoFJBIJHwvLQoDzJgr79Btr0GNJot0QhiWLakLk8+peMuArFgs+ok1+TwsmdVJJ3k+fA/PUwNl7VuoLQp0UksO0m1x+1woh2PSaDTy2XhuU91kKsQy+CV38Xg5BvB1YDGRV7Gan6vVal4UUN5j4u/k5MQ7IegYUKctzy0sV1anF51crNjQfr8cWylkMwHGMXBjYwOpVMpfFxVCDIbzCjqGeE9FURRbiVuTL3Q9cXLJ8spOpxMTUsPJKbDgPuXu0OWjJgQVHjiJZ/wdfkbHAnVJqatWRV7uQ00RYeUEEK90IPgZFUeUgzqdjp+4s2IKWLRP4bhBHuTiWRyDKIIwGUlnHPuxau9VXnv2NuT2lx23OtD0PFT05nF0Oh0cHx/jzp07PnnGcYPfAeN/fodqUGBVB2PmSqVilQgGw11EUYSjoyNUKhU8//zzqFar91QDkSd2dnZQr9f9ui0089BFqUkWxsfUC7g9chXfx3tWk/nz+dxzFyuyGHOxClhFXm5LKwuYKOPif8qj6qZnvB62UCBHhwkwTc5ptRiTiOEiWxwXVDegsSGRSOATn/iEb3OmSXu9LjR3aRsFtiu7ffs2rl275k1gjFnT6TQajQYKhQKq1aqvXND5hyYX+ZxWS1MfYGuaYrHoNSKdS2h7Ml47rr/Q7Xa9nqLtFwwPH6bkPGCwrytdAul0GgcHBz7jwckoG2aXy2Vcu3YNx8fHeOedd/wkUG9EOmk1AKS9naX1mu1msMNMM4lkfX0dr7zyim/IXygUfB8SJVoG0sygqegLxDNcJJLBYIB+v49EIuEHCHU6aMZKyYCPTCaDRqOB2WyG4+NjvP3223jttdfw6quv+gCe+2Z2iPtXAYQZJXVPcaLAMq4LFy7gs5/9rCffnZ0dv/CXcw7f//3fjxdffBHb29uIogiDwQBvvfUWXnrpJXQ6Hf997O3t+euvpV75fN4HxsCpOHF4eIjr16/7612v17G5uYnhcOiFBp4TJygatGezWWxsbPjB6N1338WNGzfw/d///Q/tt2wwfFywt7fnXaij0ciLshT0KBIw8aLtT3iPk1PJmeQpddSHiSkNhDXQ43vY95SBF0UITRQRmoAjZwKIZau1jYK6e8kRHFsoLmtpF4+XDn0mf1h10Gq1vAuUvQR53iru6gIF2lKAgWK9XsfTTz8N55wP0jk2hb3Mx+Mxbt265bmzVCr5CXgymcTe3l4sY0+xgUILv5vQZadlXdPp6YI1zp2uOruxseEDfi2f43YoCPC4ydeTyQRvv/02bty4gZ/6qZ96qL9ng+FxBxdBVBePTiI1QRW2bFEXui7ySnFP3UzkaZ0YkvvIsyoWcs0AHgewcPXTBaXbUR5R0TVM3BGhY3aZg1a3E4KmADr+yb2sDNDPaVJQS1IpfDNu5ByA15aVByqEr66uet4tlUrelFAsFmPtyCimkG+ZEON2+ODcRBdC5Fh7fHzs38O1Msbj8X3dZ7xmdGxxvQk+ljmUDYbzhqOjI3zyk59EpVIBAG8iAuCFNFYTsRK2Wq36Xt7UDpjIBxZxIRPqvIeZZFJdQPlQWxhEUeTXa9GWMEC8conbYysSfpaxoCbBtKKBcSx73qqQq8khNayRM7mIFnWQbDaLQqHgr5sazhKJhK+YpX7C411ZWfGLwWqbwOl0im9+85sYDocolUre+cptFItFrK6uotvt4ld/9Vf9gpVMuqXTaaysrKBarXqjQKVSwcrKCo6Pj2Oxddh+h6Y1VtrR4ctWie122+sLHCvYeiscnzjvce60h+zrr7+OF1544aP/aA3vCybGPiBEUYRWq4V8Po+VlRXk83nMZjM0m02kUil/o+lEVHunVqtVXL582WeQ2EaAQRt72+lks9Pp+AwIAE+0DGoZmJGgarWaz8pwG5ywhpkuFSC0vyuPh64pZoLYIiCfz3vxIZlMehu8BmDz+dwTI8sA5vM5Xn/9dbz66qt4++23vdCp7icGo41GA41Gw68GW6lUUCgU/EJeN2/exM7ODg4PD714mUqlsLm5ieeffx4//MM/jO3tbX8eOzs7WFtbw5e+9CU888wz+OIXv+idvePx2BN3Op3Ga6+9hps3b/rrxe83n897gUJFEhV4Ll26hMPDQxweHuLtt9/GcDjE6uqqJ0Vev9XVVe/O0t42k8kkJvZrObTBcB4RRREODg5QLpfR6XR8MLqysoJKpeLvf7Yh6XQ6aLVacM75RAonx+RXdXRxHxqEqtNfnaOEthoYj8fIZrMolUqx0lHye7gPBoTkeXWgqguX++dfzfYz46/jC7Bok8LerAyADw4OsL+/7wM/BsfcDoPZYrGIQqHgJ+7sZ8iFBen65+qxKgbQPcdjp/BKPuTClrVaLSaUV6tVbG1tIZ1Oo9VqYT6f+wCTvdX5nXDBBI6vdCjwOxkMBt5le/HiRdRqtVgbA1YrqIMtiiLvVgvbIxgM5xGJRAKVSsUvqkQnDp2Q6hQKE0ucICeTSTSbzVh/bPKmboNcqIkxdYCFzh3Go0wYUUzkazwGQvlUz48xmfaq5v7p6lSRgQKpxrj6V91b0+nUr2nQarW8uKBjD3k8TKrpeMEWPOR0GgUYF9MQwvkBezhOp1McHx/7PpOML3nuTGh2Oh1vDGDVBKsQ2HaADx4738vWBHSGURxQoUUThxQ3WBVBYYgVDYzxDYbziCiK0G638dnPfhYAfCKqWq366iY1bLF6SQXOer2Oer3u41/ykVa1atxDAVCT8jwWfZDjuJgY+VYT7qofaDtDjW+5fcag1AaoFdC4pIuOaWJIkzbatzWfzyOXy6FWq/kWAEwYqfhaKpW8e5bnrG27SqWSPxfOvfv9Pt5++23cunULtVoNtVoNiUQC3W7XjxW8PtVqFd/3fd8HANjf34+1vur1etja2sLa2pqPa1dWVrC3txdLVPJ75dxhOp36dpjaA5dzE213SFFXdSKOj/ytkI+5YKPh7GBi7AMAs9AUW5m1ZlBIMggz55yoU4TL5XJ499130Wq1vMtLJ/paMkVSY/CpQS8XqlFnFQlke3vbb4POKG1poG4hzWylUim/H2Ah0vKG5s3LElkVBRjcUvTVUjCuCPmNb3wDb775Jlqtli9z3d7eRr1e96uzlstllEol1Go13++EgSqzV1tbW7hy5Qpu376NN998E7dv30a/38fm5iZefPFFfPrTn0alUvF9YROJBBqNBp5//nm89dZb+NSnPoVGo+HdHKVSyZ/r008/7Qe6mzdvotfr4Z133gEA1Go19Hq92ACnwfRsNkO1WvXOrxs3buCtt97CyckJNjY2fBYtiiLk83mUy2VfXjafz707mItkUBQxGM4ryF3lchnNZhMnJyc+WUN3JSd0wKkLq91u+z5OWr5ORxADPxVENYMcJqk4CQ8DQ5bwhv1PyQfqptf9aeDL7arTgO/lg4ksZrUpEKizAFgIsdreha7WZrOJVqvls+V0hpKHKMIyIaZlXMAp95HrKXy/9dZb/lwpzmqvVp7ncDhEp9PxSTVd/IsBe7Va9d9Tq9Xyq8wyaCX3a/sB/Y6AxYJcs9kMJycnvqyXrWhC9zODeZaiJRIJlMtlbG5uYnt7+4H/lg2GjwOYYF9bW/NuSPIJK6g0AQTEHaZ6f7G3Yfg8/08wnmLsuMwVxc/ofRy6THks+h5uT5/j+5hMDwUKunSX9brl/5e5ZJkYbLVaODo68mMQxwquvK2JNE2oqQCSSqV8HMuxj+3HCoWCj5XV3cbrWK1W0el0UK/XUSqVYu5bjiW8fvxemXjTa8M5DrBYX0HPn2Mvt8HvQivvuOAXhQL+xgD4pKXFuobzDAplzz//vJ+XMt6jWEhBDViUrqvQxnlko9GIVUXxeYp9jM1CHgw5U4Vf7jPcJp3yfB9f4zmFgioQ7+PN4yfvUDAmZ4cxLo+b/EkTAOM2Fa7pYq1UKrHYlIvRMk5mHMoWYKlUyouuXAsim83i+eef93MHiqxatTCbzVCv15FIJHB0dOTn9olEwrcw5PXm8bBCADgVhdn+kNeG50yRluvKMMkFwI9fWqXA34tWJnMuQt2pWCx697XhbGBi7AOCBjG0jc/ncx9MUGjTgEQz7LoN2v818xSKnwoSGANWddCSTJxzsV5+2g9QLe8aWKqbgZ9ToZHb53tLpZIndJ3QMrDjcQDwJVqtVgvXr1/Hm2++iX6/j1Kp5AXXRqPh+wkWi0UvEBQKBU+OHBh47Cyb4/nkcjm0221cvXoV165dw8rKih/A1J1bq9WwurqKjY2NWCCtgxBJvdls4ujoCJ1OBzdv3vR9XUle2qtMy2ApnqysrHhH38nJCQCgXq/7Rc8oerD5uorpWgahfWAMhvMGTers7e1hPB6jXq/77DZdRjp5JD/xvubzvV4vxhtMkDHg4/40SaVlQ+RA7eHETD0DUz7HYEknyhpUcl96jro/YDHB1wVzyLc8JoLCI4MwCrPk4zARx8RapVJBqVTyCTa+R8UPOtC0UoKtdhgYMtEYliDzs8Vi0S8wqWMOz5fVFfwe6diikEPnF6+hJgLpZuCkgNUFrVbLb69SqcSqGsLfF78vChmbm5sP8mdsMHxsQH5IpVIxHmG8S75dNrkOBU91DvF9Gn+GYqlOQEPBk58hD4bVDLpNQuNrdW6Fx8sYjpNr8pIeW3i+3CYFCMacvV4PzWbTt8Qiv1JEXVaZQXAs0e+CfWN1nlAqlfyq3foZ/qX7VA0bOs5xcs6qCIpB7NOoVQTqWNbvXcc0igBMmqlQBMBzqxpDmBxlxZn25TUYzhNYtl4ul31rEdUMeB9qKXsqlYqVp/PepxOezk29h4F4P2gAnkv1dTUKAIjNv8lFup6B8qzyMjk8dNyGjnvyD3UR5ZhlSTuNK2u1GlZWVrC5uemTYVEUxeLkXq/nj5ki7HA4jOkjqmkwSVWtVr2Zi8YGViloe0fyHK8J22yF14WJNbaUYKu14XDov6/wepHzeY1C/YZjhP5WRqOR15/0u+W4QxOFrjNhePiwEe4jgjcYby5a/kmKfNCVwx+/9gNl1peLmfCm1s+FN6AKBMAig0Vnk0LFgJBY+f9lwZ8GQEqQapNnSwWKiJzwLiv15edms9PFr9rtNvb29nDjxg2kUik8/fTTaDQaqNfrvj8LJ+J6DDxuko9mmLi/QqGACxcuoFAoYDwe49KlS2g0GkgkErGFdDiApdNpbGxsoFKp+OvLjBOPHzhd+fvChQvY29tDq9XC4eGhP/9yuey3yUBXs3j83ulQZruC/f19P7DRBcCSAXXXssyiVqvBOYdWq/Uhf7UGw5MBZqKPjo5iK5Ty3tPS18lk4ld7ZVkPk1QMdtSpyvtOnyM4YVW3EjlQJ6J0HmmgzIlmyGsAYsEoXyMoSGqCh8k/JuG4XZZyAadjFBdJmUwmGAwGvt0JBU11BpfLZayvr6NcLvs+VAzeCN0/Az+2ZGF2n+4BXhM9H/Kd9i8PncJ8KH9yvB0MBjg6OoqVK5PXNaFFIZg9vFlGd3R0hFar5ScrXBRRXWx0izDJyGoMg+E8Qu9xuh21TykR8lnoXGVMxD6iKiYq1CDAWJPb5F9NVmksDCxaEKiAoKIu30MsE3DDbYf7VL7W81BRg+6ufr+PTqcTG39YHquJQx1n1Lmm+2Osy8k2PzMcDn2FAGPd8NiZ4Aq5Vb83jn8UYyk2AKfzglKpdM9cgmMik5B8bzKZ9OMfY3YVOVSw4bboimVi1WA4j3DO+bks+/hrq5DQ3EXwXtN2AInE6aK2hULBcy/jKc7LNfZxznlXp3JwKIDO53O/qKou/hT2lNW/yj06N+Y5a4VXWFXL93A76uZVx32xWES9Xsfq6ioKhQJ2dnbQ7/e9RjIajXB0dOSPk8e8DMpZJycnXnCluN3v9307K76P58r4m7qOJiC5X1YQtNttHB0deaGYwrJWhqgRDoi3wlEe5YKNrJ7TlhH8TvWa63UPtSDDw4eJsR8RzWYTx8fHvgRAe26QEEejkV/JUMmIYNCYzWaxtbWFO3fuoNPpYD4/7fPCAIdg4KeirBJAmF2iVZ0gCTJQ4qRTy8vUYaDbzuVysYwOHVFaXsZMtgrEvPkp2ALwn11dXcXa2lqs/UC1WkW5XPaZHC2H0GNTZy7dosxIpdNpVKtVL+xygs7JOYmJ1+Hq1atYW1uLESWPPyT6p556CsfHxzg5OcHt27e9yMqVFnmdOGAy46TO5itXrmAymeD69evodrv+98IyZn6vURShWCzi6tWr3vnLNgsGw3mECgB7e3v+eToEVLikyMdS0Ol06rPmXORgf38fnU4n1rNP262oSKgTcv6lCBi6a/VeDp1eIXfzPQTfQ+5c5gTg8eo2KU7ys2xPEEWnfccYTK6urqLdbsd6V9VqNS/Ehk4n/lVO1PGGQnAmk/Euf4U6hTV4LhQKPuDU603eZMCYy+XQaDR8awMuWDEej717dTabxVrDUOxQkXVtbQ2FQgE3b970PWRXV1d9PzH+jlSM5fdrwoDhvIJ9nykKcIJJLiD/MPmisR/FQQCel+io15iY9zzjL41JNXkF4J6JJBDv1RpWOClvhcKjblc5TTma5oKQ9wjlNu4ndMey9RV7/msVmx6nihfhv5Vv1dwxm8280MJtMtlG8ZbnQ8FWBVV9j1ZacMzsdru+LzhF0lAM4XFqvE4+Z//ZUHTn70nbMbASrlAoIJVKxRbMMRjOCyjGAaf8wjmxCrHh/8mL4SJdURRhb28PGxsbvnJM9QGdz+u2yTl8Xg1G+v9+vw8gXkGmWoRWdoVVDbz/lZcVIWdowof71H1rC4L5fI47d+7g6OjInwfNYPrZcH8al2usT06Logh37tzx4yA5WKuRuf0oOl17gP3B9Zw16Tgej73Jqlwue1OF9tdmkk0TghTbNeZXDqc2wu9IE2NE2PrRcLawK/4RwZt6dXXVZyI4QSaRMpDgzUlBkCKtOo8mkwmKxaJ3MmnwR5cmy4QoUupEnllo3oQUCLR3i4qLSraaeeFrYUsCYBFMA6dBGZ23SrpAPDhl8Ki9bdj3ij1aAfjgj+VXzOKH4rIS3nw+95MCljPwOKvVKmq1mg+EnXPeuUxi47mvrKzEziWROO3lRbLjSq8saaWYQYGZi4atr69ja2vLB9vaGF3Pv1ar4cKFC35hGa6m65zzzbNZaru6uupd1Pv7++aKNZxr8L5st9vY3NxEpVLxWWlmqOnKJNgyhgt3sacUnUnHx8e+fJSJG4qF5EV1uwNxR0IoipKbNMDUyW7o9tLnmGTSCT33S6FBxeLQ0ZlIJPyKrScnJz4rf/v2bbTbbUynU3Q6HbTbbV9qVa/XUavVvPuTx6AOYV31lsfNaxGKD2F1CMc5DcTJwSqe8/pQVFcHgTpfud9+v++FV/bU4mtcFZjJMHI4x9/bt2+j1Wr5leA5HvMz5XIZzrmY4GswnEdoH2U6yjXBrMmqZS4n3jvKYVoGr4KeTn4ZG9LlpcKrcjFf4zFwX/wbVpiFrxE6OQ4NDuF2AMQ+q8egk3fyjy5mqBUb6nYLz4MJKe6XcTEAX1ZLnmY8ruOHxs08B46NKmjwe6QrS68vF9WiwYDmAe35y2vH70z3zXMlBzMODtvu8DgLhQKq1aqP7Q2G84j19XWsr68jlUr5Xs9sDaPcxHue9zO1gtBZf3Bw4HvzZ7NZ9Ho9L+5pSwPGasuc9YxVgXgbLSAumnK+H4qdjCOVj5THNdnFcwwXT11mPlOzVDabRb/f971Y1dg1HA5jnKKxpcbgjPVpNtNxp9/v+0VkVR8JF9nVB3vVql6j+g6vN1sZsJ0NE3dcMFO3QW1HrzO3xQoMnZMsa2NGPYrnSEHWcLYwMfYjoN/v48KFC/jEJz6BSqWCRCIRa8TsnPPCIBs98wbn5FQDEwalpVIJzWYTw+EQ7XYb9Xrdb4ukyuBVMyvMVDGTppNKTl71ASxWM6RwG5JtKBoo0YQTbLXfM4gk8XKbPE+Wt9FJxUbbdLBSfFay5jlyMsDX2FuF5M/rxOBUiZ2ES7JWkTSZTPrMEUUAYLGiLsmMAWi5XMa1a9d8uRkHAuecz/bzGvMa6rnQyXDhwgUkk0kcHx+j2+36hYc088geXcvKAg2G8wSWwtNxtb6+jn6/7wOsdDrty7HUcUkxgaXrzACz3AgAdnd3PYeyHFddBCo4hG7WUKjTCaYGrfwbNtHXz+n9Te7RxBG5KHwPj4cBGRchmM/naLfbPpCjoLmxseEFaQaW8/k8tsiYOpaiKIot1sKWNBRPOSZpcBc6elnuzLGBgbkmMbkfrbrgNSoWi7G2QORk9p1VYUC3xecpKJfLZTQaDd8rDFgsGsMSWfZKVPeawXDeUCgU/KKILMnUSTD/TXcVXZOh6Ml7kLEv7zHGhXr/8jM6MVZo38TQqaqT3PAYQpcVEO8/qK4v/j9MOunx8fPLtqvxaCKR8EIsP6PHGgrLKjho7EiBlxzIOJfXn8evnKWCBz8DxA0W5EvtQa7HxwXGGJ9rokuFCu5X5wdcYFErPWgaofGDQmytVvOVhcuuqcFwHsD7hQYjTTKRZ4EFP2kCjL2ZgbhhIJ1Oo9lsolaroVQqeS5XZ62aBbhwlG5LjyM0X2k5Pee4WilGDiK3hHEut6N/lyGMvcOkE4+VYwzdq9p7ltvQ2JDQJBH/Ml5llQTHPS7EqEKsXgs+WJ22bBzh96nr2fB7G41GMe0hk8mg2+36cVMNDLr9+XyObrfrWz0yrmesreM3n1s2zhrOBibGfkhMp1NsbW35vnoUynjTUUDLZrPeTRC2AeBDs9C8obLZLCaTCVqtlp8wAovFBHgj0aWpIiqz43qTkhjv5wzQ8lwKkxq8qXgcZq00e6XnocIrBY5UKoVOp+NdvbTGs0WBBpC01vMceN15XCqYKvFRKOYApcImr1HY7JyEHPa0opiu3426vtbW1lCpVHzGn9+3OgN47Gr9p9iczWZRqVR8DzbtZVgul33Q3Wq1PKEy62kwnDeom4b8B5zeT5zA8Z4Oy4U4kVQ3Jh/z+dxnhLnIVegqIJYJjEQoXmoCiMcZloBxm8tEV36GXMJ9MODTz2pyLiz/Jbgwly7gwkoL5SxeS4LXQRNOfF5bquh7tKpCz0OvEx8UQSnshskoBo0AYsEkXVS6SBi3r8k4HlMqlfKLLLA/Ll3Dg8EgJoDw2o3HY2xsbJggazh3oAinCZtwAZDwPgfiC3GFz/F5jWOBRWsBrcjia8oH99uHTnyXTeRVbND/h/zK/emxhcnv75YM57lx8s9zIgfpGKKu4rBSTccOjWv5eSYmQxFVE4d8XoVdXgd1K+s56cI95GB1TGl1nQohej31Oqn4QhcefzPh3IZjQHhMBsN5Aqu8GOfM53O/jgpfZ5VSGF9qb1AVaalPcM6azWZjpfNhTKsx4LL/qzkgNHJpPKt8p7wSirFhTPzdsEzMZfsbxvEAvIkpjMd5HBrjhzwLwIui1Ep43hRhQ8Gb/w4Fcp0ThGMfQYcs5x/9ft/zOMVafhfhmKbXjryvZjKN3fmdUPtZdk0NZwcTYz8EnDvtP6glnbxRnXMYDAaxXnh0WPHmUIIKMzy8wekQ6PV6sZuJ7w0nqLoNNmwmKGCEAiuw6IXFQJHv4c0f3uz8PAlMA0cGVToI6Pu4LV3RkD398vm8DxRJbgz8VcSmE4vXSq+BHi/Pjdc97MvFybhO3knC6gKmYMr9aQZrNpuh0Wj43pMsN+N2wn4wywJ+LckaDAY4Pj72pK39gijWUlQ3GM4j9P6moEruoQir97q6TskV5FZ1ndPNlc/nfRmYlkZpUoX71qBmWWmSciMQXwRA+ZR/NXjl+8Nt6Oc0oAsdAnrc5KJ8Po9isegFaV2wSscHjgXKMzxunTCH14THDCA2jujnKapQPBiPxz4Zpq44FRM4joTfh36fFDn4veg58rj4/ig6XbmWAgOvCVtU6DFQrFCh12A4T+DidrznGaeRd3mfLHMDhXyk0FiLCLdxv8/q66Fop7Hme+3/vfBex79MYA7FX+UtnivFU25DzzOEJhBVoFTxljGiJv3V0QoshAEVZpWX1QGmZg2dAwCL1mvkaE266din10TFFZ4ruZ6CrI4rPF+dB5gwYDiPoFtc+ylHUeQrQGez0/6fGqdqwkXnrYQKtYy7qBWEHKfx5v24UO9zIB7L8vnQFavHuOyz4b+X4X774ziUSJy26FK3qrYlWCZihia5MH7XVgA69pHjQ3GV79GqYbZHDMfH+yUOefw0X/GYqIno9dDrqddPzVvUO5RTOYfiX11s13C2MDH2Q2J7exuz2Qy9Xi9GBhQVE4mEF2ydc7GepmpxVyKkE5MiXTqd9quvMhgOrfUa7KmoqmRAsmbZg35Gs+QqeoYEQfLhNtUZoUEXz4VExe1yGywVyGQyvvSNTbY5WScBcRVCnpuWR/AY+Zq6i1UwJnlqucR4PEa/3/e9IPngxJsBLl2xuVzOC+I8Rvb01bJnJWYtuVWRWINhDoQUZAuFgv9euf8oirx7NlzwwmA4b1CnTyJx2nOQyRFy37JSV4JuAPIZHbHT6RS9Xg+FQsF/hpNTnQhrIMiHco8KAWEQq69xG2GGXgVm8lvo1tR9a+ad1yd0drL6gGKAlpyps0GFVp4/t6H7BBbigvYd5PloUitM/LEHGScY/Lf2sgpdaXQ4kI+1J3joRKYYEbYEUicXeZvXgC0ZBoOBf8455ysd+JuxINVwHlGpVPxCeYyltF+/CqqsSFD+UiiH0aSgPfqBhQBwPyerQifH/H84IQ25bBk0jn0vhKIjz/F+CXLnXGwBR8Z8Wrml/BgKq3oNdBwJr2cikfDVDPyczi3UcQws2kToa9xv2P6BPKoTdY6J6l7mvjm2crvkU+5DndSaLCVPU/hY1m/SYDgP4JowUXTqJOfCeaVSCY1Gw88POVcO54Tj8fie8nsVFfv9PvL5fKy1Vpj4B+IVpwrlIfKgGhSAONeqxqHPqTjI2E2rA1RXeK+klVa4jsdjv6AWhWluf1liTflWuZ3zCsab5DzVaZTjuL1wPON3SMOHahNqeguFYG2jqOcYagqa9OO1UlOH8jzj2tDUQi5nSwPD2cPE2A8JlgeQ0HjD1ut1HB4eotPp4ODgAOvr6xiPx77Xljbo543L4ETdQrPZDKVSCYVCAeVyObZ6HrAgBk6I9bXhcOgDPxUF2Xs2dHcC8JkkzcZokMWAk72pKPyGIqUG6xRZeXMPh0OMRiPU63UvDmhQRpFEe4eRYIFFpo3kpo2sdX86KaewoG7a+Xx+j9NJg10SJs+fIqiWIqRSKaytrcUGPG5bHyocceKgGTuSvvayqdVq3i1L5y0XIjs4OLinmbnBcF7A4Eqbziv/MttPPtQVnzVACu9TVjAUi8XYYoJaSq8BJTkGiAeEGjCGQZ66YvW9mswLhddlLloNXjXg02uk557P5315kyanUqmUX0BHJ/0awGqyi+OMlpUCiGXuuX9NfvG10G2hQir3wXFVhR8emybrNPgPXb0UV9Uhpt9fMnna01Z7rqlYy6C0VqthZWUFpVLJhFjDuQUnhYyJyLXkXU5OGTepaYAgJ2gChuWWmggHFuKgThIZJ3FboQCgk2yKf/dLWuuxhUkufl6d/RrDhkJBOAkOk09aAsrnNdkWCrDKZaHDSZNeWp4cJqX0/MjN6lLTaxpeNz1PjhnLegkmEgkvOoQxsAq2OubxeMjhXGBGz42taur1ekyoNhjOE9rttjcBcc7IFn9ra2t+XstYKBTcgMVaJ0C8qlPNYcqh5DZNjuhfIN6rNYyJ6NjVxH1oOAvF0NBNGhoZeEzq2g+3o/8ejUbodDoxUVJfZ0y/LLmljlflUK2gpcjLcY9xrCbAoujUwazjm/bw1XFAzRN6POEiuTpWsGe3fn+6bx0b1WCh2oMmTPlvW5z20cLE2A+IVCqFRqOBfr+PYrHob7Rer+cnvxcuXMCtW7dwcHCAd955B2tra/cIAQC8sAkssifJZBK1Ws0HWZz8DgYD38CZ5KDZaW6XARUnzpygDodD3Lhxw4uuo9EoZuVnBkhXyuWklo9CoeAdndy+9qbRwJbHpz2zeM48J2ZpKBKow4B9eDUzpWWtPCbFfD73Fv75/LQ0QfvhcNs8DxKkCgIAfI9fBtPJZBLlchmDwcA7lROJhF/dkO0J1EGrQgK/e05UGKQyW0Zy10VpNEAFYCvKGs415vM5Wq0WyuXyPYInhTWKqnSYa7ARiq8MWjgpTKfTqFar9wi7bDWj7Q90mzwGdeNqcKX9pfhedYARKqqGx0t+CkVdnWBrcEYhlH1Yyas6EdakUpjcmUwm3hHKcyJX6vEzeNTFADSYVUcxxynN6tMdF7qk6P5nb0omHKMoQrvdjlWLOHfa01Kvrz70+uj4pteECzDQUZvJZFCr1bC6uopyuYx8Ph8Ljg2G84BkMulXtFcxVBM3umgqFzZVLmCVlwqa4eucOKrBQGNldbWquADgntW+VUxQp5fyoooOOpaEyS3lXnKJuvZDfgmFVX5eJ/z8d2g00PFFz4HnF4qx4bHz+2GyTc99mSOM79VYU1v9AAtxliI7eZnb0oW79C+5XscjTZRRYNKYNpPJoFwueyNCNpvFYDAwMdZwrkBeYAwKLETK2WyGk5MTHBwcoFAooNFo+PVXyLNqoNKYRQVaioSs7hwOh+j3+74FoFYSKXfpva+vawsTclrYNio0EjCpp8l1XWyWugGAWHyqiS69ZvxLPUL3q9UCWvbP7ZEjdcxRbgvFUeor/G40tub8fTQa3WO84/bJq2EbCV5H51ysL6xWUfD4dI6g58H9cSzh96CfC8VY/T7L5fKSX6XhLGBi7IcAJ4fqvNFeSsViERcvXkSpVPKkwpuFRMcV8lSsAxY3EomKn2OpJCfJJDog7vJKJBIYDAZ+dXHta6KZHwbQOrHXm5hCKTNBFCTz+XyMeCmA8LMaMDP7pkRBkVez+cuyOlz9kOfH60iHMEmE5M/AV1dP1/IwTsL5/fHcNeOmQilJK5FIYDgcIplM4vj4GO12G8453zaAk/xOp+PPi9sgwatjWFtBZDIZDIdDv4CM9sNVhxeD4aOjo/uWwxkMTzLm89PFDMrlciwA5P1WLpf9/aVuJOVRCrfq3lFeZJUDuYjbCVvCaICq7WEYPJLH+BlNeGn7mFA0pGNTM9Sha4D8GiZ6gIWgG5bNhgk7LQWjQxZAbFxQAYLcrk4Jfl45S8ewMHNP8Nh43qGbi8/xu+W147FwrOF3OxwO0ev1/LHpeet4xqQf2+QApy6KXq/nxViO4RxjKaSYEGs4r9A4SONIJlXUgb7MraRuLBUxAcR4QzkwFFeVr8m76gbicWocqW20+LlQpFSEgilj7LBVjbq8dHJN3lGxIJyEkwvD67RM9Aivo8bWzjmfIArft6xlQyimaIyuLlY1N3Cs4rXgSuDcDs/vfseq6zRQXNI5Bcchzol0wcdsNuvXlTAYzhOiKLqntyiNO7wfjo+P/f1FA5BqDMovynfUJwaDgY9pZrOZN2VpMowCoMZ6mkRijMSEfy6X8yYIcqc6/DWhpMkfraRlbEbBlIuqUtwM+VuTgpq0Iy+FMaueQ2go0M/zeBR8j8aF+pqOUbzuuk9NCJJXacIKv3/G4EB8oURNSuo4ys/pPICPVCqFUqmE0WiEfr/vRV7G4UyoXr58OaZNGM4eJsZ+QHACyh5yOuHUmyybzWJlZQX9fh/JZNKX/fMHr2XqBG+4sC8oyU3Lb3ksGhAyO9NsNmOCJp2wnGyrQKykozcwXQ4EyRFY9PvSxU14PdQZxesR2uhJMMsCWyVriqDMTDGIU2ImUUVRFHPNUXjRQC/M4qtrjcetjlYKMtPpFKlUCq1WC8PhENlsFrVaLda/ZTKZ+IGDzlt+b/y89kvUAenw8BDHx8d+URkN4jl45nK5WPBtMJw3hCKsthCgc1LvH34GQIzXeA9q8MnPUuxTIXLZRJcZbH1Ne5rS9U7Bjy1gOMllUEQO5yS0UCigWCz66gNWQ4SOgNAVpee5TGxQXlwmIoQuJr5XAzR9nuIG3aa8JprM436V9zVQVO4OxW2Od+Q/jhWZTAaVSsVzozpcVTDi900HBpNenHgA8EIsk36hM06vjcFw3qCJ+3Q6HXPUqOMo5En9u8xpytdCrtbPKtQsoFBXlVYY8d7V2DYUAoCFwBkmtJTvyIcUpPW9eswhN2psq6KBXpfwnJSP3wsUMtVxqsk/3Vd4Xe93zcm7Ok4ACx5kZYQ6gZe5ynQfep35PVFgIefqeKNJSjMdGM4jqC/oui/kLI1baNDSuaomuQkVU8m/GnuGFV7kpnQ6jdFoFHuN8WipVPKLbnPun8/nkc/nfXJOuULbspCXVY/gc5zja3uURCLhXbsqxmpMpgk54N51E3gdwoSecrT2qF4mxIbl/iF0+xqDaryunMgxi6Y8/d74vegYoVqEHhf3obys50gxNpFI+O89PKdkMukXojc8OpgY+wGgwakuOsUfvjoI6GwkGQLx4JQBR2j35w1LkSGKopiTVIMedSf1+330ej30+30cHR1hNBr5wKff76Pb7XpRVt1G6jqi8yiXy6FSqaBYLMayWxRCeQ24CjUdu7w+PCaepx4vgNgkl0QSDhoUM5gB1Oxfr9eLnT/3wWPQ65tIJHxvSX6HwMJRqw4KTty5n9DFQEcI+yyORiMvSFOQDQc0fXAwZXDLAJWlJ8PhMNZAezabeUdCsVj0ZQ3mGDCcJ3DBPbreyRcU7SjG6kQYiCeXeE8qhzIrrOVdbL+i9766Z1VQBOK9uNgTnDxM3mWVgvZrYmCkpaHk0nK57EXZcrmMarXqxwDl6rAHVZjYIjQIDAVkLT3WyTPHpbDaQccCHgvLSUOhgoGzitjaPofBtnI4xzxOBlg9wOPkophsgbNMpFEOBhaleRTDuRhYu932Qm/YS4xjGROA+Xx+qVBkMDypCBPr5C7eozqJDEWAkIOWxSzLXJU6mQzFTBUxGW9pgktjRnKyToZDhxd5VF2ZFBz4nK7VQGdUmPBSrlFeVNGSnBlOngleYxUZeI31mqgzmW3NdNKvAoPGvPr9qDjCbXM80VJbjgMqVISCBj9LIZexve6DXM+kGJ15+h2Hv51MJuMXMDIYzgN4/7DlgCaPdE4NLErd1TwUipGMD5eZxlSIDXlcjQ7kGhoZyuUy6vW61wdyuVxMkNVKMY1XNU6mcUlNCzQraOsEHj/bGOp4wOPm/5XjKEqH8bqW+2urLmAxf+e1DWNq1QtCrgpjXxWD7zeuaZJLq734npCfGYuHRr1l42x43jRz9Pv9ez7H92gfeMOjgYmx7xPqyFxbW/MEEvZ54sRuPp970VAnzgQFABUtGdTMZrPYas98HVg4h/hat9tFp9NBp9NBu91Gs9nE0dERDg4O0Gw2vfOHk87QyaCBJZ9jMFar1ZDL5VAsFlGv15FMJr3DiIJBrVZDqVSKBcJRFPnVHDVzx/NgSaiSNINVkiLbI7BkQx1NAHyvstC1RFcyg79+v490Oo1yuey3X6vV/MChDi9eBwokKvwkEglsbGz4ALnX66HVankim8/nvt8gxR917vH6ao+w4XDog1hOKkqlUkzgHQwGmE6nODo6wsWLF82lZThXmM1mODg4wM2bN/GZz3zGc5S2ItHgT7lEJ5QatNIdq25ZrTzQACecfOrrKlSMRiPPvWxn0m63vZBIftQJtmbhycmc8GcyGRSLRZRKJdTrdayvr6NSqSCXy/nWJVwEMZxU87z0uOlu4zVVYUKDz2UZdg30tcVB6BTQcS50AJBf2daAvMfqAfIu96HPs0ojTK5p6xlt4aOCuwrpPJfhcIjj42P0ej1Mp1MUCgU/vhYKBd8qYjqdYnd3F/1+H88++6wFqoZzB3UqMn5koiRMvhAhf5KDl72X0EmqJtT4vLazYkKFCS/tvc8JLnlHRVPdF7CIudmehCYECgwUeTlGqHEiPG7lTG4buNdwkEgklsatvFY6CQ/dXXrsBLfF+QLHF409aRK5n7AZJpnowuL1mc1m/vzDYw0dsRr7ausImjqW/QY49ubzeRQKBWQyGWxtbeHmzZv3uNQMhicVdEoSOicG4CtsObdWPtFYjv9fdu9oLKQI72Pe6xRg2c95dXUVpVIJxWLRi7Gc9+qCt+SAcN9hzEyjBR/sKU2+1/YJKmxyG+Rd1TJ4DjzXMO7lNdTKXU3mhYYO4NQQQlFT21PydSbytOdrGHcyVtfElbpedV6QSqUwGAxi6zGEYwr3z3Ne5hamhuCc89XLHPPUBHJ8fIxEIoFGo3HP92Y4G5gY+z7x6quvYjqd4sqVK7HFu7TPCEXDMHPCHzdFO83eaN9DvelY2s4Fr7SfEye1dME2m00cHx/j4OAAu7u72N3d9X1lGDxzW6HwSoKgq5MEP5vN0Gq1sL+/j1QqhWq1ipWVlVjP2Pl8jkKh4EmMJEExmaSuriglGyVSzfxxINGVEbngGIBYcK/lA5qBZ2lwsVhEq9WK9Q48PDzEyspKLLh2znlHBKFiLsuFeazM3KdSKfT7/ZgbVreVSCR8iwqeF38f/E63traQTqc9+fK3Va1WY+LL7u4uVlZW3nNSYzA8SfjKV76CVquF7e1tHzDpAoaaNAmTTKEwFyaf+Jy6/4H4ZF2dCeoKHQwGPlnU6XRweHiI3d1d7O/v+4QVSzHJe3yEgSWwSLqR65xzaDabflyo1Wq4dOkSNjc3Ua/X/WJjYQBKHiQfkyMpmnL/TFjpgioqoIZjRChEakBI8ZIZfo6FOmGnKEBRVkVaFdT5GoNEishhgKg9HHmNWTZHUT0U4YnhcIiTkxNkMhmsrKzEFnSkEMPrTpezwXCeQP5g6yedvGp8u8wNq73yQqgTkryrMaI6O/k+/l8Xnx2Px+h0Ot5lqS0U7lfirsejE251XnGM4WR4Op3GKjJ0Uq3b0X3eb3whaFJY5jLVbYaTbl4f7o8VWnydZayMM6PotA966KZSaOWecqwmPRmLKnerAzhMpOlnAcSSkKwS4/4SiQRKpRLK5bJvzwPAx/0Gw3mAth7gfF/vJTU6sWInNPpwbqminbZX4Xs0nlJO571O/qpWq1hbW0Oj0UC1WkWtVkO9XkexWEQ+n/ciLOe86oYF7uVBPkfnr8ZXWkHGa0EhVrlSBU7lPgCx8+c14TWgIMm4lNeZVVK8vmHbxtBEBsQXjeT58nx4bmoM4Ta0PQSf11iZLQv4/bNFF/fDcw/XUdC5QxRFsTkRK6Sp29DsASxiaP5WhsMh6vX6g/pJGz4gTIx9n2A/v2q16l2xQJxwOCkNnUJhpiWcsGomSSeCmuGmyEhC5uT35OQEb7/9NnZ3d3F4eIijoyMMBgMv4Ck5MZBUEtDJrJIyVyhnRmV7exvFYhE3b97EpUuXsLW1hQsXLmA8HqPb7aJUKsV62nJ/Ojgw8Mtms/dkdrTkiu9jjz9Opnkd8vm8fz9LHXK5HI6Ojvx3wP3zM7wmvLadTuceh5eKDrxGJEB1SdD13Ov1UCgUUCgUfB9DkqEG0yRNHQDn8zna7TbS6TS2traQz+fRbDYxm828OKDXsdfrwTmHg4MDL9IaDE86dJVlTrh5v2o2OXQRhe5IYOEaVZGVCANI5U3e/9p7m4Ls0dGRT4DRCUvO1+1x/wQn1MuEWXKwOh+azWaMr7UsLDxWBmzhdaBbSTPwfD6c/Ct3qZOVLixyI4CYKKBcreJFmDjT7+Hk5MT3tiJHk4sLhQK63a4fB8ihdL9pAJrL5fwibOrwVeGXQk4ul0Oj0fBVEnRMqIhPzGYz3LlzBysrKygWix/6t2wwfBzA+4+VX+Hip0zeaxkssLw3ajhpJVQw4P95L6vziM8Ph0O02230+33Pw/x36PRXflNnKnlJ+Vhf52doXuj3+8jlcqjVaqhUKmg2m16QDHtdhwKtHr+eN8URfZ8mi+4nYoc8Si5kOTO5SzGbzdDr9WLJQD2mcJxaJniQOzlnYTWKvj/cbnhd+Bty7nThMYoOFF45vvA6jcdjnJycxEQLg+FJRqVS8XNOJslZocl7hWKexq0ax+l9F7okea8yNiKoETCGUp2j0Wig0Wh4Z2ylUkG9XvcVWQTnt3oMGluG1cP6ORWAE4kEyuWyH3u4VgoT89pLV/fJbfGv8i0Q7yXL66NtbshxjGlDzYYxK3mW26KpQatHnHOxxdn1GFV7UXMWr426VSn48ndA48ey+QkAf1wc87S9BOcv/P2oySKsDn733XexsbGBUqm0/IdqeGgwMfZ9gi5IEgidmprtZdZJJ7bsjRS6BRgY6oMkHDq1uA8GL8yM0RV7cnLiF4HicWqgzMm2BlcUdRn4MljWgIznQvH5woUL2NnZ8aW3OrnXbItzzgfrYQYrDMqVnLQ0iu0Roui014n2xZnNZn5hNJ4TXcfs8ZdOp70TWFs+aFDLa6mkqNBgkNeT3wsbjXMQ4UP3EQrw+t1TXKAQ0Ol0/GCovyeeF0XaYrEYm6wYDE8yVldXMZvN0Gg0PJep2xOIC4fKYcopQDwY1ESMipiEBjW6siv72I3HY98O5uDgwAuxoQtTeYC8EB6nTsr1c7pwI6sEuGotnQqEirBa1kY3BIB7xGvuR8Vsjgl0WXG802utJah0DDBg5HY5odeAW8cYDZ7JhxqMUmggH9IBx8/wGBTqsA2FnzAQLhQKMeFWxwQVl5iM1LZElggzPMkgB3Bizl7YBPkrFDeBe1et1vtc/yoH8b06WQ7dQ+RergjNxV7IAVrBoAKrQrevHK2fJzfkcjkMh0Mfi+XzebTb7ZhzNzzmcLvha3ps/LyOPWFijRN8va66H62mo5kijDt5TfTfKrTqhJyg8KBcrQsQc9sqoujvIvwt6XdKM4VW4HGM5XkxJmf1hrq/DIYnERQZabgKRVPeq5xnq0Oe0LhFOZX3Je/f8H7lvJOtBwuFAsrlMhqNhnfCsmUW55/hfa7bI8gxmnhTXgtF0Nls5lseagxYKBR87KUtte6XqFExmP8P30fOo7FCr8WypCGAe3QMHo86TLm/sEVOeMz6GiszVIhPJBZrMXB/ajLT49bjCM9RuZvHQc1Gr6UK3KxKKJfL91xbw8ODqTrvA3t7e75PC8mDWRXeRBRmS6WSv4H4WkgEGqhy4huW3GrAxxuUk3P2VG21Wuh0Ouh2u97ur4KFEoGKouokUKJWMg3JgwHedDpFp9NBq9VCu9322SxeGxKHBsbqzAqzY4SeO4BYv8Bms+nLCcKFcIjBYBArhU2n06hUKn4BBg3s+dBgOgzS1cmqYqxeN/bMoYjA7YfOMP5fHRhRFHmBncI3xQeKyerM1eukoj3LGAyGJw3NZhPlctn3T+V9oa5Y4N7gC4gnft4rMAsFWhUaGPSw1IccFEWnzvqTkxM0m02fLCLC8ikdB8LEk76X+wXgHUhctE8nrsPhMFY6H+6b7lxtFcNz1WuyzFXBh/KfJgXDa8xrxgw+z0kF4fA70utBbmWFA9/PQJKTj0Qi4UWC0AXNcwknMLpfPW+Otfobougajnc6RtNhsKxtgsHwJEDvL07uGG+Rv/iaJrbD2DF0mwJY+h7lIY2XVLRkX0HGfnywrdUyV6nuX9/zXqADjX0QNaGkE//7OUKXtSoIJ+T6CIUUXm/lbN3Xsuf4fo0v+VwotC4TLzTu14m/nqtWNCwTc5aJsOHx6W+B3Kvnqb0kNeal6GwwPKnodrt+/qcVs5oQmc/n3pEOIOao1FhKxT69N7lNFVL5PHtl0/XKR7VavWehLm0ntSzxouCxMC7U49J5eDjmsH90Pp/383oVSwm2MAh5dxlUFOZ10PFGRU3G6ITyoS7oyPcwRg1jRz2eZRqQvkbTFTlYxzfVgzgnCdcaCr9rbVfG2DU8Do1x+Zoa2pxz5pA9Q9go9z7wzjvv+AwNAB8M8uaja4oTZhVPgXg2Qm8YEjBt+OrOUSJmywFtPUB3bL/fR6/X8/2teIxKDCpCEnrThgF1eIOTAE9OTjw5tlotHBwcIJvNYmNjI5a14ufVyaQihw44AGLBmTqj6M6az+f3rEjO68prNZlMvHDBhbEA+MBaJ9R0tS5rDB4GoRpEasYrl8uhWq3Get+ytEAHHWDRTzi87toTiJMACqyaoVMxdzgcxhaZMTHW8KTizp07uHDhQswBSs7U5Ii6IcOHCncahPIvOVLdVPpZcr1uh878brfr3VlhgKv7CSfjOgkllGd5v9MFS64i71AM1uNmEDedTn2fa+6T58XALEz4heKmcqCesx6rOk15XOl02l8r7i8UtwkKtiroaOKM+9exktukY03HLf42wiA6PGa6QMKAG1iMQwx4eZy89iqGW7sCw5MIcp662VkeqeIpsOCJkI8VYdJEn9MktwqCWhHEZBgXotVJ6zIOVVFR96P3sR4LsEgqMTlOYwGAexLtvCYhH+r2NNnE/2tsqWKEJt/CKo7we1mWSNR9arKN50XzRvgdcJ/cjp6DjmEax6oQGz6v48z9vhd+ju/VKhd1+qlwwuQXF58xGJ40tFotv3Agq3XUEa8Jaq1Q0Pkv3x/GuJpg4ZyToPO0Uqmg0WigUqkgm8364+CDi+rpfPl+wqv+n9BYK0xGaVyoegAXDtOqjFCPYMWSVo8q9/HahGIjz0EXPVSzRDin1uNj3EodwDnnxz+NdUOuCnUBXisVgPld0R1NLtWe29rfVuNf/bd+z+PxGPl8fqnGQWj8zbGC+zAx9uxgYux3QRRFqNfrSKfTqNfrqNVqfsJJ0oyiyN+Q7CnIIIJOWb151DXLcn/eROqA0puMFnXtWwjAl2vpxDd0ewILItNSVTp6VcxIJE77oPDzdIOxPCyRSODy5ctYX19HFEVot9vI5/P3uFUpkFJsBuD7lujiVnwvBWYOBCQili5kMhkMBgPM56clrTqJDx+cbHc6HbTbbRSLRVSrVZRKJS8akBBTqcUiaXSbMUjk90SRQQNsls9qOQJLWUulkheigXv7iXFA0RXGu90uut2uv1Z0tnE7FJUpxMxmM7+gGLdrMDwpiKLIL5yi5ZS6AAh5Q4MPvRe1DApYTHjDsiK9N3XSqwGeinRh79gwGA4DMV20L5z06vmSF9QhwBYFXJyKgejx8TG63S4qlUosOaUuX124hdeR0ITZMjcG+TFM0IXXTQUZBowUAJggDEUHvW4AYolC7akYtvYhF9NBq6Iyg+tQYOU4zZ6KvK7M/qtgw+uvgob+pvS3YWKs4UlELpdDr9fDdDpFsVj0MYomyunQCifSywREQifxGqMqXzLRwtiUE0K2+mJczf79YaXVMk4H7uVkNRvw87qAy2AwQLvdRi6X8wsmMi4Pk0q63TBpo8eyTJDUZD1NHeQ1TZTpcavYwESkc85/T/pZNYEoB+t29HzUmcvj4DbCtjDcl55/OO4pb2sMHCb/9LuYzU773HLNCv7mJpMJKpUKDIYnDY1Gw8e6wEL0U+MB9QIuFg0s7iPGNcDintNWUmEVD2NgJvsLhULM3cmH8hjjb+5DNYYwEaWJO/JIaGbgMau4TKdrr9fz6wTw3tc4nlxCgRhArI0Brw2hcaxeSx3b9BhULNbry0XLGb9q/1jVNFRsDced0I0aisc8T5oGqA8AiwW/CeVunpcayDh3KRQKSKVSvmcs5yO6EDK1hn6/H4vLw+tjeHgwMfY9MJ1O8S//5b8EAKysrPg+pPP5HLlczouZAFCr1WIiAQNGFVX5GomKE0t1YfIG1yzUdDrF8fGxD0QHg4HvU6jOMDonOVEM3a7am4SvA4u2BZp1opMgm82iWCxidXXVu7FarRZSqRQqlQry+bxvjxC6yijEalAYEgqvCR29OllnjzAN5Eqlkg/SQuggoNe70+n468aVIEkw/X4f/X4/Ntnm9ed3oqIn90OhmlCnF8V5fv9hrxteewrMbDXB42Ngns1mfduCwWCAfr/vz5/fGVe15OruBsPHHdPpFL/4i7+IT37ykz7rzXuwUCjEAlcgPrmlk4oudQ2oGOCw3Iqf1W3w3/o5FQcYsDAhp5yxrKeVYplrgQiDRxVHycUMuCaTCXq9HprNJorFoudb8g978qn7gH3IKGpwmxw/9Hi0xYoGseExMSBm32sNbplA5PMadIetGrT0X90HrD5hoMtjp0uK154cq4lNHivHaR4DJwfqyuXvgoGplv3xWvOYyO35fB4vvvgibt++jeFw+FF/8gbDI0UURdjb2/P96orFonflh5NtdWupkyZs+7FsH4wR1UUZinbqFNLJZlgtpUkbxTKXrsbdNDXwmLTlQqlUwsbGhp/csy2Xcp7uOxRmyRs6CVdnE1/XyTo5k/tg0lFLTRWaPOR1Z5zNbYTiN3kxHGf0+msiTF/X+YomOkMxnp9NJBJ+WypOk+u1L6Z+7/y3VqPw95XL5dDtdgEAa2tr/rMGw8cVURTh+PgYV65c8WsiMHblPafJfs5TNTbVOIvv472mcRu5k3EWq0UZG4YxHt9PrlTRUJNgKghrHE7u0bL+0OSgrUrG43GsgpXH1uv1UKlUfAWazs9pDtOKJiDeJiwUKzURB+CeJFYURd5cploJ5+ChLhNFkV9cW/UP5UPlXEKTZioi6/igi4txTBuNRj7+Vc2F4zTPIYoiHzdTM1DjBL9rvd6lUgknJyc+3k2lUt4g9uyzz5oB4SHju4qxzrm/B+DfALAfRdGn7j7XAPAPAVwB8A6An4qi6MSd/rr+UwA/BqAP4M9FUfS1h3PoZwNmyDVboll7ThZzuVzM6UiC4sSXJfQA/M1DcgTiq2vTuUmnAB96w+ZyOaysrPgMEgklmUx6RwMXibpfJpr7ZQDM/zOQ4sS0WCziueeew+HhoQ/EgNNVtOv1OtrtNmq1WkwUUFGX58xgTnuhqBOJBKTHF2bW8vm8FwDC1XTDjCFFBr5O10UicdrnVgNUfpcqavC49XMEBwB+H8DpAFEsFv1gyO+P0GPi76fT6WBnZ8c3zebCZTq4zWanCxzQPcvykX6/j3w+j1Kp5B1y6sg1fHxxnnnXOedXrmegxmSNBjzAYkIKLBbGo3OeEzuCwaX23tKAkoGkJnUoGvJ9OhaQq3WV77D8XYNVRTgp1n2rGKkTXu29lclkYj0TVShghQb/rRN1FZwZzGrpGs9JoUGljhPcHh2pIRczaEwmk77FDINzvaZhcK/7GwwGcM55Z7BzDu12O5bsDIUGcmfoGFYRIxRWKH4sEy20ZzCD2pOTE/T7fTzzzDNIp9PodDrf/YdteKxxnjkXWLRHYRIYgE9khK4fxng60VaBTSfl4UNfJ4foa9wveZNxMnmGwoEKheF2CZ2cAgvXmSabyAWpVArlchmbm5vodrs4PDz0hgdNCIVQMVlFi1CYUCFFt6PXRstlgUX8q8K0JsX4vWn/RI2flcvC49QxR49VuU/j9tCQsMwtGzrTdNvz+Wm7MV2gh2OEjqnkeGCxSrg64w4PD1GtVn1MYPh447zyrnMOtVoNlUoF1WoVzjmfcAi5gvGOxqrKISooAvHWeNwXqy1VhKP7lomPcA7M/VJPUPe+zt2Vi0L+ZdxK3qLDFFgkbrrdLqIo8vc1jU7dbteLtNqmQef6YUJMoTypYrAmipSjAMTiWfIjP88xTo0XbA3G/S0TYbXVRAh+V+Q4PRZqMTwOTbzxOui1JUdS6KZWoIvN08Ch+5nP52g0Gmi32z6Op36RyWRw8+ZNbGxsoF6v3/tDNjwQvB9n7M8C+M8A/Ffy3F8F8KtRFP1N59xfvfv/fxfAjwJ49u7j+wD853f/fiwRRaf9RZ1zOD4+9qRTLBa9y5Q/+uFwiNFoFAtQeIMpceoNwImkEgrJUhcp4I3GfjLc59raGjY3N3HlyhXcuXMHu7u7ODo6wmAw8OVmXIWagTb/rYGjTtJVPNBAjW0ISMIMjguFQszJwJuY58broDZ8ALFJL8lfSZHXgNtQwmEJk5a2UrBUVwSzOyqazmYz33dVA2YOEDyvcLAIA+swA8lz4SCipK9ZKS3ddc55hxvLAimuhtlEEi2/z8FggEwmg42NDQyHQ99+oVgsIp1Oo9lsPtibwXDW+FmcU95NJBLY2toCcBqMkXNZSQAsKgc04cKgUblI3ZrKseSTUKhTzlPnFRfq40qzpVIJ1WoV3W4X7XYbJycnODk5Qbfb9cfA7ekEeVmgFk6GNZGlSSl+jqKwnp8G6uoS4PaUk8NAPmxxw88xkCWP8Xrp98Rj0vNVkSYMThkUqmDA1+kI4f74nVEMoiiiLtjwGMJEI6+JOrm4Tbphw4cG7fz+6FjhpKTZbHqnxPb2Np577jlcu3YNv/iLv/jBfuyGxwk/i3PKucAp3zD+pBOLfBvyhT7H+5T3isaWyinAvSKdiowUHfg5/qUQygQU70FNvPG9GmcrVCBgzK3iJz8zHo/R6/V8YpyLxgLxRQTDbXN7y+Jn5VQVJ5SHNY5UkYBCK4+bx6nxKferianwuuvkOxS+9T3qtAu/G31P+HmdQ2g8rGMDe/6ywpAiS3jOHHe0N6NWJZB3AcRKlQ0fW/wsziHvOufQaDRQKBS88YBrz/A3rTELP8MYS/lC4+BliZ7QBanuSGARe7FMnfdaJpNBpVJBqVTyj0Kh4Pev2wNwz5xfuUKPSzlY3zcajbz5jAlwCsYUCe9XPh9yqO5L5/p6Xfk58o8ej2oUaoRT17/O8YfDoedPjqM6junx3s8kx+cojKvzN5FIxJKk4TgExOdFNIfp96vmvzDpxqoQbbXJ9zBJ0Ov1kMlksL6+fs++DR8N33UUi6LoXzvnrgRP/wSAP3T33/8AwK/hlCh/AsB/FZ3+wr7inKs557aiKLrzwI74jDAej7G7u+tvfgpmURSh0Wh4AmJ/Uk7A8/k8gMVNwZstFO74HCeVJAqKuZw8641VLpc9iSaTSZ9V29jYwIULF7C3t4fbt2/j1q1bmM9Py1XVWUthWcvpgYVATHLh8wyMR6OR71FI0Y9Ew+NVp1F4o3N7KqwyEOb7wqDfuVP3cCaTiW1fj5eBMbfHib5eXxV3OfBwcq0Dhg4OYSCrogywIDJtMcHtUgDWUlx+hr8r9kDj5CKdTqNcLqNSqfhG7iqw6CSJkwNmOSmGs79MNpv1gkVI9oaPD84r70bRqWNzfX0dBwcH6HQ6vpF8uVz2k2/2DeXEkbymPKSiKLAIYNjvOQx4la9DYVJ7MjnnvCjLRRRrtRry+TwODw/9wl5aThtOhPm8/uW+lIuBeGkYJ9z5fN4noDSI08+E21NxVzlfk2y8TuQPIjxOBXkPwD2TBA1+J5NJrM+Wjj8q/KoIweMjX4cuBnU4KGeHY5k+x3Gc46puK7z+3J+6FnguzWYT77zzDjKZDJ566ilsb29je3sbe3t7sd+d4eOB88q5wCI+Jc9o8kHjotAJqeIcOVjjKn1vKJTqBJdxjiaG9KFcr71kWRmm8aOKoJpY0wSLngvjtNls5h2xs9npWgVcQIbtACgKLIOeI89fF7ENBVqNaTXuXCYU6HvCSbQKu+F10+2ErrFQJNFj0PgxvLb8PnieypMqOITbH4/HXogN5xEqjnPOw9ZbPG5tC8b9sk2XVh4aPl44j7zLe6hWq/m+rWzBxN8859baYkljQRUJNZbk9rUClSKhVnRq/Acs9AjyPmMkVmuWSiXUajXUajWvRfDBOSf3oTGccgWwcPKrSEyHPEVYrlEDwK+dQP0CuLcCgucflvzzPJfFrsqNfL+ayNRpTH7iefH/4XUMq5RV79HxT3WSUDDXtjS6HZ6nxqJ6/vyrjudMJuMXG6aBcFkyjb/H1dVVNJtNdLvdWIX0fD73lbnUGOjmNjwYfNiU4oaQ3y6Ajbv/vgDgprzv1t3nPlZECZy2J/jKV77i+wsyCGTZEntq8XkSHMspSYAqwulqrSQ+IN44n4TEzJRmRLScgPt17rQcdH19HbVazU8Iv/71r/us1mQyQavVQrvdRqFQQL/f9w7NUDDQm5xk0W63sbu7i3Q6jZWVFU+EoQhLqOjKberzwIL4taVBOHmfTqe+sbSWHs/nCweqfpZiJckmnU57V0O4wA9X5NZ+sNy2luWR4LV/l14f/Usip4CsA41zLuZK1u/32rVrflCOosVCDirujkYj5HI5/93x+ty8eRPVahW5XM6vAsmsFYPkMCtn+Njiieddtu7Y3NzEu+++i3a77cVY3kfMGiuPhkKetgxQHk6lUj6BocFhOFldFkTxfiZXJpNJFAoFVKtVNBoNVKtVVKtV7O/v4+TkBO122ydO+HluT5NAGpzxfmVATI7SxSIpxnKirFylSS49PxVq9RyVm5X/VbwNr2EoYuhEXF0ImgwjD7LCQgWD0NHKz/Ca6IIJuro5z42TEhXXw/Izbp/PM5GqnwlFpFDY57a4wvB0OkWr1cLrr7+OXq+Hk5MTfM/3fA9+5Vd+xZ+j4WOPJ55zeZ+wPZKWxDJuDd9LsU6doppgAe6dzGr8F/ZB1YTOsnuR9yywWMjEOYdiseh77rOVV3iv6zExftPxgPf1eDxGq9XC4eEhCoWCL8lUQZoLyuhEn8enXEmoUMn3a9n/MkGW/1ZO5jmFIrd+J8uuNfe/bAJ+P+i+1HSg1y1sUbAsxlQe5Xur1arncEU4zrAKRkUKJgmcc941R5fsxsbGPeKH4WONJ5p3yQN0iFPgYnJdF8RmubxyD3kh5GaN+zRm0TZMmmBmUiiKoljiSZPRrVYLwKJCbH19HZcvX0aj0fDrEyQSCb/4Nvej9zjnz4zrNC6k2MuKT1YEc/7PqjTOq5XrNBEVull1XFrGCfxMGBeHbRWWrS+xjFepS1DEDq+BjnmhUExQNOf2+F7uk4tr8vvQZFZ4bBR2aRDR/u4h//L3RPdzPp/3vw+Ot9RhKJh/+tOf9i01l7VfMHwwfOT6jiiKIufcB7bfOed+BsDPAKcD9OOGXC6HZ555BplMBmtra/cEY/yRckGYTCbjyYOgUMqbmmU6nNDTIcmbRm9Qkg8zZVEU+WwFBYhutxtzXQGnN+gnP/lJPPPMM3jzzTfx1ltvYX9/HxcuXEChUEC320Umk0GxWPRuWQabHABC59lsNkOr1cLzzz/vJ6KNRgO1Wm1pSa1O0tUer1k/FSB4fTQTw8GCE2sN6DQY1oCvVCr55tMUULnvdDrtM1e81jx29qEtlUo+4ON5MFjWzBT/Oue8A479r/j7UFerEix/DxRb1tfXsb6+7s9ZBxt+Byrgs/ci22JQgD08PPTX4ejoCKPRCKVSyZcqlMvlB36PGB4dHgTvPo6rE6fTaWxtbfnf+Xx+Wrq1v7+P+XyOcrns3TDAwkkbTu7JJWzNwv7ZhUIh1nIlvK/DSS25mg/lOH6GQWqhUMDKygouXryIo6Mj7O7u4tatW2i1WvckRch/KkqqeMqyJ44t/AwTc+yXp0EngzPyD4+dHMReueROBt664Bevi3KzCsbcjwqmvB4qeoSTYvI6r79ujzwZOhV4HXju3DcDfgXdAOocCMUdBsec/JAfKRRzITLdPycGdHywjUy9Xken08GdO3f8d/3Nb34Tv/RLv4S1tTU/Mcnlct/lF2/4uOBBcG6pVHrgx/VRwaTSxsYG0uk0Wq2Wn5gz7lD3qz70ngfibUkY/zKe4n2ovMCJo8ZZWuLJyTKdsJz4kbtLpZIXNejcGY/HSKVSfjthYglYLFCrHDMYDNBsNlGr1VAqlbxzTSe6TMgva9/A66HJIBUsdXFe5VLtARiaOMLEED+vi2mpsEF+DwXaUBBWUUSf4/nptdJ96bny+1OBRxN1ivn8tLUO41AVNsLrCMCLU3TD0U1LsYrfAU0VFGWZGDU8OfgwvPu4c24UnbZE2dnZ8UYpio38jfN+0ipZch9f18W1AMT4hvELsIgNCeVpimvhIlmcI/N9nD+fnJzg5s2b2Nzc9OavfD7v28fQPMDYalnsBSwWkgJOuXFvbw+9Xg/9fh/Hx8fodDpeV1HhUXk25EoVLvU68Hz0+odJLU0+8XM8dm0Lxuup1zuMGVXI1WutBi++PxR57xdTa+UIxWqNe/UaMz6+fv26b9+ocT23xe+Ix8HvLp1O+/YZPHaeC9fH+PrXv45UKoXNzU089dRTy37mhg+ADyvG7rm7pQHOuS0A+3efvw3gkrzv4t3n7kEURX8HwN8BgO3t7ceqlvrWrVs4OTnB1atXUSwWUa1W0e/3fbk4AxYKYmxyzF6yQNwlwN5WR0dHcM5hfX0dKysrfn8aOE0mExQKhVj5ZDKZRK/X81ZzignAomcSgyuKcc45PPXUU9ja2sLBwQG+853voFgsolgsot/vo9PpeJKh8EHBVPuf8CZPpVKo1+tIpVK+PYJzDru7u1hfX/d9asOee8DCccqBQ0uLlXCYTSLpcWCikMLPUpxQ5xID0UKh4AcXJUX2AQPgHVYUUklwdLHpsWvJbkio3Da/A3WvhU5cThD4HIDYSocccHQRmfAashdsFEVeSOC1uH37thevgdNgttvteqLO5XJ49tlnH+BdYngEeKC8u7W19Vjx7nA4xHw+971YNzY2sLKy4gOHfr+P4XDohbNCoeBdNjrppBDHpBnvVQ3+gEVAyvtRs90a+AGLrLa627XMkv9nf63V1VVsb29jfX0d169fx8HBgXe08x4ndymHKX+qsMjKAAa56rAPqxm0OiAM8FSkJk9RpNU2DwQDMP4bWHCiViVw26HjjNvUIJYCOTmdbVlUSCbCJCGDee2dzmPgeyjYqBgLwAeY/D1wvEqn074fuy6apiIx22QUCgXkcjkv1rTbbXS7XXQ6HZycnODg4ADvvvuuF2ELhQJ+4Ad+4CPdF4ZHigfKuWtra48V5zLWbLfbiKII29vbPq7l/ablrqGTh/ebupJ4v+sKzzr55LZ4j/F1boMtl1Q45Wuh85UTXt6bjUbD9/FWPlKO5r5V+OAxTSYTdDodLxioWNDv930yXSfn4YQ65B0Vp3nc4SSe5x8+H4qU5Dn9Lsj/4Vimn9fvidDkH/epomo4Lup4qN+bHiu5nd8bsHAy5/P5mHiionCYlOT5cMxhjMzfgY7HXIndudMFkI6OjizW/fjjI/Hu48y5jF1oqiGP6MJRGucBiwQ4q26ZRIqiyM8/2SKx0+l4cw5fU45j7EnxVPk+TKpoZRfvb77vnXfewe7uLt566y1sb2/j8uXLyGaz6Pf7sXYkGk8Ph0OUSiW/7gsTKsPhEN1u1+sc3W4Xs9kMKysr3pkLLCoGlH80blfXKGNf1VeU4+7n7tf5t8bDaopSztdt8DpxvkJxk9deDQiE/pv8x7Fo2XihY406iPXz8/ncJyY5RjC2jaLIJ9hUY6D+w+8NgJ9jkZOHwyGazabXThKJBPb399Fut/GpT33qA9wFhhAfVoz9BQB/FsDfvPv3f5Dn/4pz7r/HaVPtVvQx6+UCAI1Gw/dHoVCn5YwMGDUYUqdr6PRhgJdOp33Wna5H2vT541enACeyANBqtTx5az+Xfr/vnU3AYvLMzyeTSayvryOTyWB3dxcHBweeKAeDAY6Pj2MkCyzcZpyo8xgpkKho2ul0vFuNn1WSCifMGswpofJ68Pz5V68Br2UodHJAYmDM7wdALMDl9VZiC4NgCsU8/jAzpUKqlixTMOF3ELZeYEkJXVYUK3iefD+dABrUcgClu5oOM4oCJH6SL89dyxESiQRarZa/7levXsXGxgYMHys80byr7vBKpYLV1VXPAZr4okOcXMAJnU6+md0lt2qfOraeCd2bKiqQi7RcV0vsC4WCn9zrhJKBHIU+3qs7OzvY29vDycmJb0MTiq88Bg24ptOpL02iCK0LDPIzwIJTNWuv/MO/mvziBJniKLcXTr41QFdXgrYMUGcCn9NElm6fASO/X3WI6b6U/3n+oQjOYJUTBwaz5HKOQ/ob0ySaOqj5vfC70fGZjmyOV8AiQOd++Tn2Et7Z2cH+/r7f14svvoiLFy8+iNvFcDZ4ojmX9wGNBJo0UTExLAEPRTRODIF4mwE1MGhySGMyFfTCRIpyicZVfC/Be5rbS6fT6PV6vmoiFB75eW5TnUyMi3ncALwzdzZbrAuhuJ94Go4v+lCEsaZyJL+D8PqreEC+5rUL9xPuU2Nxfpfh9eY+9bdA6LGF50cRSY+d46QK84pw/CMH6/9V1OAcKJFI+N8tW/dQzOHv8NKlS1hdXb1nn4bHGk8s706nUwwGA7RaLVSrVS/GaoKbC1eRt5bdk+QrzicZA1HI5P2hXKCuSHIaq3+4fXVy6r55H/L9FOeoF7RaLVy4cAHVatW3AmPlFe9ljacpntI4xVhQYyu2VWQ8rxW4usCUis08VhU+VYTl66EYyc+HiSxypS4QTpB7OV7p98U5Cfenx6LXNoyZCeVn1Qo0qab70X0wpmeLQ/4eQlFav29WD3Ne4JzzcSt/Z1xI+fj4GL1ez/+em80mvva1r/lrd/nyZaytrd374zfcF99VjHXO/Xc4baS96py7BeCv45Qg/5Fz7s8DeBfAT919+z8H8GMA3gTQB/DTD+GYHzrK5fI9gZBmS3gjqKgWRYten8w4MXPFstJarYZqtepdi/yhk1h5s2lmmQEgy+/pHFLyCcvuQwJOJBIolUpYW1tDJpPxGahWqwXnHDqdjnelcbulUsmTPkvlw/2oCMiJb+jIAu5dZGCZY4Db5kNdudyfbldLu7hfkjwJT4UaIhS7OYiNRiMvsBM6IIUZtHC/6lRQkYVBaegIUOFZy054bkB8ZUoG2RRl+JsiUZZKJT+w8rcCwAs3/N3wnG01xMcb55F3taSHfYsYwLG6gIEb+YK/af72GUCw1xSz8HyOYqzyOaFZ6VA8YIlWv9+PBU4UPyniaSDLnt2ZTAb5fB7FYhF7e3s4Pj72zi1yCfdJ6PFxPMjn8770PeyTq+eiXKKBpTok+D5dOVuvQQgVefU55XAVPFRs0Wus4we/W3UzkOf02MmnFNMZhOv58DPhsev4pUk88rSKFAz01aXHwJQ8ywevr04YtAyZv9Vms4lWq+X3ffHiRRNjH1OcR85luw5yqbpNdTKq/BT+G4iLnJy4aZJCuVQnpDoZDCephMbd94sp9Th10sj7kpP9EBpH8xi4eG82m/UL1fCzvV7P8wI/pzGlHguPk//XeBy411EVXkcVZHU74fVfNsleltwLrxP/rcJseKxh/KzX7X5OLxXXQ04Oqyn0d6HnomKTJsb0unDuo3HxbHa6CFuz2fSiTqPRMDH2McZ5413Gk4wpWcnE3zv/UqANkyahYDqfz31syiQwxVKdWzLO4nyQwqfGTry39K8KjXovq9hI/WMymWB1dRX1eh21Wi3G2zx+9oZlsm40GqHX6/m4jo5QtmkMK0+5PzUbcB/qmg1jwZDHQk4MeTBMiOnnQ/FUP6sxMblN3fvLxoT7jU3hMavpQ/lS5/j8zXCs1UUkuR/On/Q7pUuZsbW2C+Kx0lDI/dIgMxwOY/1sTYj94PiuYmwURX/6Pi99ccl7IwD/q496UI8aJIB2u+0nbOqyIYmQMBgUsPxdWxU4d7rgx9raGiqVSqyUneJASFZsAk2ypLirgSz3yUbLvDnoAuJ2o2jRt5WrNlKIZZk83QC8kVRgBhY9ESk0cz9sB9But2POMxK4Dh5KQCqmhkGjCrUqFijodNDntcSWwbEGzHy/lpBx4h/27lVngR6zfqfclwbYuqKkZiF5DbShOrfLbfL/JFkA3gmg15O9KbmAHFGr1fx2d3d3/WBbKpUwm828iDQej5HL5Xygang8cR55V5MYFFA5ecvn8xiNRr7R/2Qy8ZUCbCHDHocsRy+Xy34yzW0zkaHCXBi4LeMlYFEyPxqNYgGM9oNWQTaKFgtFUbSr1WrY3d3FzZs3cXx8HLsPyRtAPKFG7uF9n8vl0G63l5bZhsHl/bLpQLzvlb5Hk0U8bwWDszBIDwN1noueF6GTae17yIlIKMRqiVin0wEA/xvRoDQ89/DYNLBUISX8vlUA0CoYHh+vGVscFAoFH5jyXHlubH/AsdzweOI8ci4FRya7whhQ+Q+4VzwNOSWKIt9GRDkMuDdJEwqwOnnV+5Fl6ctK+zXe0+3wGNiOqtfrwbn4CuNhkp+Yz+c4PDz03MLqAeecb0lGrn+vybSet16fcNKu1zac8PM89bmQS/UaUzgOk3A6R9BthU6tcNu6LX0POVfHHD2n8LqQ05WrVVhW8wLPmfMYfg+cd/E8WTHD68s5GH/Hg8HAx7yGxxfnkXc1jiAn8T7g714d+8oZTFAwfkomkz75S3FXq43IK9QltL0K4xitfKDewf1zTOB8XI0Qaigaj8e4ffs2+v2+F4Tr9bpPWmu8xfNlDBsuKp5IJHx8x2oobRGgiXu9TsotPOZw7h5yWsjPYSJNxzh15obb0bhSeVrjaOV8FdlDTeB+HB+Oqbo/XhuNy+mKDavw9DqyzQGrDrXPsJ43f1O5XA61Wg2z2QwnJycAFuIuNY5lCTzDe+MjL+D1JEJvBCUAdeuocErQQaOLeHEixx6rFFXL5bIX0HRyS6Lk5C3MbACLm+Pw8BDb29v+5mOGqdFoxCaiFIRbrRam0ynq9To2NzfR6XRw/fp1TxDM1NFdCiyCVgqtDHo6nQ6Oj49xcnKCZDKJcrnsz5E3fT6f9+QVZtt4w6o7QMlKyVQJlaRPkuFzfA/FW30waNeJhorV3A7L/DkZd875yXQo1vD70ICc567EqRn9drsd+x5V/OXnEokEBoOBn0wAp4NnLpfDysoKLly4gHq97he94+DL93Pif/v2bd9jmOfPBvEcKA2Gxw3MlDMQI58y4GR7AnXEMtCjMErRUlea5b3KIJMcpBNCIN4+RR+8l9PpNE5OTvxndVJOruHx6iQ6k8l4x8DGxgbq9Tpu3LiBw8PDWEY5DLgYRNHdw0XXmJVmOxny37JSqvsJABQn1JnK81gm5DIRqa/NZrPYYmp6Drye3LaOBXyffhfhhF2/hyiKfD+xXq93Ty8ujtFakqzcrufP72lZ0M3zp6CjPWzVpaKBLXsF8/oz8cXvRft2LRNtDIZHBd6D5Ix+vx9L7jD24W+X/KhtmlR042s6kSMva+ys8aBuNxTn+OB9yPYwfJ6xH2NnAD7pQeGiUql49zpbF/C9Gn8uS45zjNFjZDsUfeh2wiQPH3qdlHNURFg2WQ//rdvTsULHMr2u4ffN72nZ88tEBP23XhvyZMhpHLf0OPXYVUDXcUGFArbO0N8g278wlgUWCa/5fO5d0GxfxO/PONfwOIG/ZSaYOe/WBDTb2jHm5LyX90YYW/K3z/kk54xhj2zVGcI5dgiuJ0NOYeI55ERW7haLRUwmE+zt7eHo6P/P3p/ESJpl6dnYa2Y+2GzmY3ikZ0RkVlZ2DV1dRbKbQjdIgj83BBcCBC0IUhstJOjnQoI2WkkbCRD+nYaNAAG/IIHQghIErkRRgABxI4CgILLZzSab3V1ZWZmVMflo8+TuNmjh/Vx7v+MWNXVVRlTmd4BAuJubfcP97L73nPe859xrvX79Wqenpzo9PVW9Xn+QNJfWxDB+E34cm/2x+Vqv10vKW1TD4AcGsbgJk9wPlbL45dcV+R1+jgItT867OMx5BvxMF3BFjI8CCo7tggTHT4Rszm9wfPfl4/tjojP6yMvlUsPhMFV1Mbb+HeG7RYLs8PAwI8rjuP5dzu3nt5yM3WAEU8vlMm3U4Qqe1WqVyl4jmQp556pWFIwEaJSYHx4eZibwYrHQdDpNOwoyyV155aVk9XpdZ2dnajabKXtPRrhcLmeAY7VaqdFoaDgcajgcajKZqF6v6wc/+IEGg4Gur681GAwSqDsBynFwOG9vbzUajfT69eukIOD16XSaNoSBSICQdCDEgfKfUSHgoAHS7tg6uLj6guyPE+f+M5vEkCn385Jp5H65/mKxmHoEOoEAgMY2CN5ywoMXzulBDuNC0OClt5Tixl4vENzNZlOtVkvFYjEpuOkxRI/DarWq//gf/2M6L2OEqi6Cdm65vW0DM3u9XoYU5TvN99s33iPRMJ1Ok7Pm/eRwHLxXbCRcSbhFsswdKPAJJxElAokUaY1rKGFRULmzy3XU63U9ffpUnU5HvV5PvV5Po9FIs9ksnZ/PVatVvffee2q1WioUCklFEMdBetiTKo6vYxQOnH+GMXEiFqxijXICwMlO1jHe7w6ZE9okuTCSVTFgoLwY0tmDEdYJEqJeLucEhzvcTpLwvH399rUF3Ce55eQH9zGfz1NvwkKhkKoQrq6uVCrd71B/d3enq6urTNVIbrm9K/bq1St1Oh299957CeNcKe9JfWldmgouSFnMKJfLmcDXExz+mrQmHZzI9eDUk0Cl0v1meSScwQpPiDO/nMBlDQD7t7a2UtLdyzpjywAnQEgA4stzTXzeg/9NSSxfH/x9/N37MErZNhCsV/Ec/O8JLu+36MSBl/b6Z2OpqwsKIlHhyUKPd9w2kdKci59dteWEuj9vfOtGo/GAGPbNJiWpVqtpPp/r+vo6c7xI8OaW27ti/n2ES8Anchzgu+wiI1fS4p8guvHqUDZiciESc9wxAmznc7wurTc8HY1Gya/DpxmNRon8Yz0Yj8dJsAVB9/r1a/3kJz/RycmJ2u12EqzFKlb347hfNk8Ht2mVCMkZ43Jpvdmj47D7+14ZG/1TT5htShxuIhh9HYyJH8dIF2tgTnbil7sAhDF3HGOdc36D+/b78LFwDPdYhnHguKhcPTHnyVXWWfzyw8PDVG0t3VfksndPbr+45WTsBlut7vufPH/+XB988IH29vYyjopntQm2IWIhBdloBdIUktb7oLhTw+dvbm5SCaqXGXimebm87w9Sq9WS0pFmzbu7u6mptk9iQIxyyru7u9TjlobbKM+Gw2FSIEFsQASyecFkMlG/30/H6fV6CVwKhfsNbubzeVoQpOxO3958myDYSVj65eCYRSdWym7ABQns7RwkJXCAMPaAn7H3QJzjUc7MYuZAKSk54iwSlCPznLkHANHVWDzr1WqVzuFOI8/NF83b21vV63U9e/Ys096CbCWKudlspsvLS1UqFU0mE71+/TqRMMPhUKvVSv/wH/5DnZyc/HonUW65/RKGMhbyDeUn2ElQTeBJywCCMy/FwgHjs54QihlijsFrsZ8o6k/pviUIVQbuYKGCnE6nCQ+q1WpqBeMBeLPZVK1W0/7+flIFkChD4QCG1Wo1NRqN1A+aZJq0JjdiGW90JqWHm3B5f20PsrkXd8oIDMBtT/A4xmF+LE+O+frJebgWnikECJu2uQqC54hCDbKD7wjXGwl3Xt9EasT75DXW8ajq5R7q9Xoi5El61ev11DpDUvIBbm9v9Y/+0T/Se++99wvMhtxy+/Uac4XvLBuvODknZZWc3r8vqmjxP6V1gBiV+n488NpJ1UigFQqFlIBx/MGfdALYVVzSmjz2hHmr1UqfRbzgCRdfIyjdvLm5SSSuX3+stHBc5Hrc3A+M5LMnCv3e47PYpOhy8twxzj/v5opmf68To06WxP0i/L4i0bHpOfsawnljS7C4YZGLLSRlCCXUW5A2EFL8nc1l/v7f//t5v9jc3jnb2trS3t5eii/BKZL8TqC6GCmSaPhLTm6Cl55A528QeV5N5QSl4+719bUODg5SLM/f9vb2tFqtEnmKvwYmcS6wrtfrpWqmer2uRqORaRUmrYUMXG+z2UyEMmX0LuzyJJdb9EN5DR5BWscGmPt3jlcuOvDxcQ6B4/F+941j0gry3DmdN6mSJSWuJeI+ZDZ/415Yi/jb7u5u+pvjvPvscFeMk48NYi/nSzyBNxqNVKvV9PjxY1UqFY1GIw0GA/2dv/N3tL+/v/Gecnuz5WTsG8ydJSYqX0oINFd8uhooSs93d3dT+aiTsSgfcQoBNVceOMEZsy6QjDiokG40wPYMkV8TQLZcLhNIFgqFRLaya7WXaS6X6zJ3gn9AEjUvWS/pvsQBUPKsOGDi2RPfXMUBJyooPAvn5pkewM4DewDElRBStvcYn2ODHJ4RYx2z93FHc9R5nA9nn4VvU9lWoVBIBC7BDMQrAT7XhrLXnX2eZ6PRSMB9e3ur7e1t9ft9ffjhh1oul7q8vNRkMtHu7q6m06mq1WqmlUZuub0rxhxkczowEOfGyTcnV5vNZlIYgTOQsOAL2WYP8CRlnEd3WKS1EojAECeK8hxUU55h9iw+CZtarfYg882xIG2bzWbq++WVA6iBxuOxBoNBUsL7PAbvfF77eHl2n+tkLFy5tSnIx/n0SgBXtjLmkjJrlpuTId53nLXEN2bjXpxQdyWEP6dNigK/x6hKi+TGprXEj+8OqI8na22j0UjXOZ1OVSgU1Gg00tqzs7Ojo6MjjUajtJlbbrm9SxYD64gDMVj0330e4Zfw2ajo8ff7z07q8rr/z1yEdHVyUMqqe/wY4DTz05VU4Am4haLLj+kVB7xGCynvOR7FAo4fiA88oI4KfR9rv+9NajXeG4kCH6eY7Ip/j+tfTJxJSuuPB/N+/RwnEh/+HPxvfN7J2Eg6+31DtPqx/LOQSsQ6VDAigmCdqVarmXZFueX2LhhJIUQH+BleVRsT4vzveBcTV/iUxKex4st9Z2lNzPGa94Odz+ep/QDvv7u7U7vdzlSSQvJ6ciX6eSS0EHGVy+V0XPfr8EmJsSVlqq0QsZGk9zGI2BrH2zHPfdSIsVFtG6u5wCpv78X5/Xq8iox74hq9J6/7qP7/m9Zd983hj/zafd32Ndm/PwgxHN9jMg185Vn7Ws4zQLiGUOyzzz7LfF9y+/ktH7E3GIDiih5ed/LVg11pnZ3A3KnwYBYyFcfSnWEPfH1yO/ASEHJN0no3vNFo9KBvomf7+RnA9V0bWRAajUY6B2NQKNxvYOBBc6lUShlpyhdQcdLfy8kHAMLHyJtL+0ITwc3H37N5Tt7ipDkoe8Z9sbjvcegBByBPlp3+q1yTb8gFyPHd4BlAnkIyuxoMwsUDgvgdYYxwKHkObl7C6+DvpXvb29va39/XYrHQyclJAl2en7dLyC23d82KxWIqffE+yO5MxUCUOUliA8xCSctcdhWAKzT9M5jjrGMWx6rVaumzTspKSskscKtcLqc2MlJWgeQ4xPV6IAzGQvRRiXB3d5fU8JFA5BzgbXTq/T7BzfjZSCo4acBxuHbfMMJx1pUEYBXH9koSkpCOVT4u/szdUfXvBQ6/42okKpwoiKSBO7f+/ngdMUFKUOHkca1Wy2zCWa/Xkzo6t9zeNXNcJNkdCTvpIbEXcZjEirR5U5JNxwSXo3+7iYSALHA/yFU9Pvcdaz049c9wfhLvjrlO0jpe+wY5HG8TYR39du6B44NZfq3uB7pFwiX+3V/f9Pk4Jp5si+eR1nFBxL9IqkfS4E3kbHyf+8YRjxlrV2fx/XACnOMgPiDZSXUfFQq5r5vbu2il0n3J/2q1yihbIwb43HMCNr4uPawKcKzEnL/wv4Fh/vpqtUr9sWu1WiJH2fSRVln4OdyD+52xbQD+69bWViax5edGyMU/8NK5DJL3XDv+peMG9+BrgfMfm/DPcZlz8f7YTsvHjdcZC97Pc4TjcG7BYwvHU78Wfz784/hchyfI/Pvh52ZMHJ9jwi1idxxb1lxXx/K5ra2tJDrI/dxfzvJR+ym2Wt1LsX03ZXcYPKvjmZkInqizcHIwV2uRCXNSwJ0rsh9+DrJNnu3odDo6OzvTZDJJmz0h8Ucp4MDnvfZckUT7Ba6FDEyxWMyUlFJmhiqNsuJms5l6PsUFgIyKEwWeOQLgUIZ6Ly2exWKxyJSlurrWFVVkCSMpzLNxYGSRcVJBWm8a5qAVnXYIdcbBx8yddyd2JSVVHddbLBaTAgRFNc/Y1W3+PUCxwSJaLpdVLpfVbDb1wQcfJKXv+fm52u127qDm9s5asVhMqnySGJ5FdoIQPCA5Ag4y//3zUtaxcIfMN3yJDos7g5ir1WmLwuYwOK6ONZTbczwCy0h6usPGuuBOOiofnNrlcpl29wbruCfMr3sTaeuOpX8WjI3kqisi4ud9oxUwCweOzzLW9J0C33jdr9kdSleJeR9g1sFNvRL5nN9XVEE4WexrYySDYjKW51MulzM4j7PaarW0vb2tm5ubVG6Xq2JzexcNQkDKKhDBQ8x9KPyPiC9+TJ9z7jvzGZLgHvz7/HSS0v/nGCRybm9vM21kHMOZr7zmCSLO4euDV6XhV9J2wQlET8RLD0UYnC8q86VsX8OYEHLs4fqdrObYmwyy2q/Bn6lfyyaShmuLIg9/Fj7++KRRZMBaGtfRN/U6hHz1Y8akmo+V35sT2rTPYA2ldDq33N41gxcgriRGdH8vYiJzwMk39x89nnUiNuLGpjnhxCW+Db4xbbdouUW1z3A41GAw0HK5TLEv2MF8dLWspEy8THWRx9/8zz3T43u1WmWqXvGpwTIwgmpdxzf3Zd0vdwzy8eX++SxYDTZzPt7npKuTlZDM/I0xjFjq2OfXJ2Wr9rhe1mrMhQoci896XOHEMX6rf/ciXjvnRRLTOQzGhQ06K5WKTk5OcjL2l7R81N5gi8UibSTTbDYzpKL/7BOLneudZORLC4gwWShPAFgWi4UqlUpSPTlYFIvFFMxL2c1SIGEpKTg/P9eLFy+0WCzSRk+e3Wei+iRzchTiN/YgBDDr9bqGw2E6znA4VKvVSjvxoaplwaB8nvvkWnxHSMaCYDWSFAAh1ziZTB6Q0p5l4/jSfb9FevbSjweg3dnZSbs8Qi7HLKNngOj/J63LV51UYTHxvpVOWjuZe3t7m8Z1e3s7fQfi5j93d3dpMzfGxgOLxWKh0WiUcV4nk4kKhYJqtVoiZQ8PD/Wv//W/fuDo5pbbu2oeXPm8dByFcPWe0E44Mjf5nXJUn4uQg555d8fXHSGuC4eMPok4hV6NQEIF7OF6nYyIQSdJG+6VQLdarero6Cj1jB2NRur1eioWi2q326mtA3jox/T7iAG44zzjxLjH4Nd/d+Ibg5T0MeX4OHKsA97agbXMS9RiwssDBfq4MsYxUeX3tymw8c/432LwEklrJwDcYWbnb0+KtlqttA7yvctxN7d30e7u7jQcDlWpVDLBpAeR4K37j5u+zxEneE3Klqh79QJJ+piEicSpYzq+l6TkSzG3ed37IlJ1hoG9joHgM0ke/HA2TJxOp5LWiXdveeMiDClLHLsf70mduA7xfwzWMcdcb+Plx4tVY042+5j6Z/weXBjgY72JQOazjqNRYOHH8sScky18nnvjHJHQ9Xti7fV+wZA1W1tbKdbIiYHc3kWLGAap5e2pnKgjVsc8GcR33n0gOAf3d7a27jfCpZ89tilpQ4LEcWm5XKpSqTwgh4lXJWV8c67BNyTzpJPjAZhaqVRUr9dTkr5QKOizzz5LZJ/76CTxIZ89puZ8/M/nnJDGL41EsXMOcZ3gGI6hiCc8+cZYx6paOISIazE55tfo1+RkPe9xfOUfr4PxXhHoxDTCBm91iTnW83m4DD7HNbLvhY97br+Y5SvVG6xcLutb3/qW9vf3UxkiG8ugyiHYjJkUJwrIShC0E6w5MQhwjcdjjUYjNRoNSet+Lu6kODkAcYpjuFqtdHx8rMePH6f7oCdsuVzWdDrNKH/ceSWA9p5LXAP3UiqVNB6PE+m8vb2t6XSqg4MDffLJJ6mH3v7+vobDoS4vL/X48WPt7++r0WioXq8nRzw6X/RE5b5cSesOG2rZer2eCQy2t7dT2S4G4LXbba1Wq0RkMOb0NuFfoVBIG5HFlhK+4PkiE5UIXB+/Q46g3ACIeQ6MO0YWkD6S9GPZ2dlJu1qy8DnZQfsBVyjwvdre3tbe3p7+7t/9u/r000/17//9v9fHH3+cbyaT2ztn4IO0TmZEwtKdBjLTzDUvI/f56ckxJ97AQCdA/VoI4J1kcLU6x2GegktghOMImEkpZbFYTGuJqxn8ejyh12q1dHJyoslkol6vl1rCdLvdtL7Q81pS2vSGlgpRRREx3skCDwb4mfF0AtXf59d+d3eXkpmMEY7peDxOSn7UcfHePQjwZ0hv3RiMgKUxacVYo1r1AMIV174eukqAceW75k6wf6d2dnZSVcVwOExrMs+hWCzq4uJC7XY7PY/ccnsX7Pb2VmdnZ3r69GkKLD3IcwGCJ7fYxMsDS0+Sg4/4IGCat8aiH3YMGB2HXRHkSWjHj0iIej9xzkmA7XsBMEdj8ok5jSjj/Pxcq9VK7XY7kQDg5XQ6TdVHkLneY9xx0u8nBs685uRzJDP9f/+clN09241rYPy4bhc5cF2O0+AbyUevPomkMcb9ONHM65Arce2IJDHvd7/YiQknqEiE4ntzv6VSSaPRSH/2Z3+mJ0+e5Jt45fZOGdjT6XQyrf8QBHm/62jRH/b5HPHC41da8CEWYt74fHJ/E5HD1taW6vV6ImKn02kSRHEfnnCTlDCQ3716inker5k9D0jMswH1YrHQhx9+mNYK3wR8OBymJBl+rCtXwSLnZaIgwRM2HN85D/x23u8E6fb2ttrtdvq7r0tU5bnvHCslGD9Pfvmzi60EPCnmOLxpfXYSmnv2arfov3L+TeQusRj/fOM4T7KygfHh4WHisXL7+SwnY99gEIsomwAo6d4pZGdXvqxMdBweb6KNgwv48GWGSJ3NZppOpxoOh4kIJdvkn3HykS8//QpxrMge0b/0+vpat7e3evbs2QMlEMQwZLIHoK4AxfmdTqe6vLzUeDxWoXDfNP/o6EiSUg+XwWCgfr+v3d1dXV5e6vnz52q32zo+PtazZ88SSKMiZfLj6DHB3VHlnvmfDcf4u7caiI4si407yb7g+IZpzWZT+/v7GdUVC41n/BgvPitt3pGb17lmxtHJZleI8N3iH+CN8kq6V/pC8LKYxkzmcrlMalqI9tvbWzUaDR0cHKSA4vLyMj2/3HJ7F2y5vN9UsNVqqVQqpVJDx19+9zKbWBrkwT3m89KdHHdo/DPuOPEeKgnAV4hFKgJ8t1Swx5Wy/E5wyj1A+Hl2nWvgusB3VJds9sUY4QDitEIQHhwcJMWub1TD9XmQ70G0O2aOOa7uYizdSSRRNZ1O03PAoXWlBj3OHacidjIGKKCptPBAxddWd2KpFoE8Yb3gexXbYDjJ49gKEcFxvTTWv2uFQiGVoUEAe2KhUChoOBymqpXccnsXbHd3V0+ePHnQo35T8n/T36SHpfMxqMOXYd7f3t6qVqtpMpk8UNJ40sRf8wQaOAMu0dvQ1Uj+Xicp/ThgU8R6SSmxAklA261IkuAf0pIE5T5j5mPkCR1ec38zEsJRQcXr7sv7+ujX7j4h1wzB6ZVpnJvjeUUK4+jiCK6fsXcCICYSGVO/bvfR+T74ffp9R9LC4wDWbNY7nq1v7LNcLnV9fa27u7uMSCW33N6mEZtvwj8pq0rku+4xufsykRdgXkpr1az3efXEsyexPHGOL01rLMjYm5ubJApyjsPbFWKO2TGB5Ofn+r1KAjy9vr7WycmJHj16lGJoFLQHBwcajUbqdDo6Pz/Xq1evMurYiCFYJGSltZ/tY+KJel9DvJKD1gMkJtmMDXFGo9FIiaLRaJRZMz1GiT/ze+Rr4Eo8TogEbvwe+TEdu6Noi2NtEr/4GkLMRTWvE+18ty8uLjSbzXJ+4RewnIx9gzEZRqNRIifJxHpfUH8vahgmablcTl9sd7gkZZyj2Wz2gDwDjNxcnUUwD5FLkE9vOg8uOUfsU+hBbDwPDhgO5GQy0XA41HA4TAFuoVDQ3t5eUpNCkgKY/X5f19fXqZfi/v6+ms1m2lWa97pEPgISIOgOp4MhgbqXFHDvENWDwSD1SPF7Azz9WUaVBU4pUn4HSt88BnOn1cltL9fwZ8kxoiLPlWau6vJnyDWgluU4TlR7QFIoFHRwcJBKCtjdslqt/pKzJLfcfrVGYA1ekbgCg9252JQx9oCNJEwkCvyzHsz6fJEeOm3ulLBZIZl5kmAk0dwx5tq5Np/DrBeOBTHI5m+MQ61WU6PR0MXFRcIUsJrqDbLijEGhUEjKTXfeXVXqY8PfGQf/m2MLGMp5PNnI2uLPg2Qbx4c0RT3AeHOdOPwEFSTsfHx4/q7UAONYV11d6wk27smVte7outLBvyNe6uUkMoS4J8H4vnCs29vbtEtxbrm9bdve3lar1cr0bZayySvHhah4lNZBtifyMZLX8TUpq9SByIMYiFglKYMf5XI5U30W8clx3AkNx35P3rlvJyn5sCRPGo2GBoPBxuDelZ6o2mKw7OMV17FIPsexx3xsIzHpQgZX+jqhzvolKaNKjuuPj5t/JxxnN6miPVnn9xcVu46dfAd8PfdzcD1OGHs8wDNEycw6yO8keC8uLnR8fKzccnvbRowc/S1iR8xjW3wW/7vPEeYvcxi/iRgZkjOqWJlPUbxQLN5XItEyj/PTQ9uPxXmcQPSkWlwTnOjEIDSpGob0bTabGdER47e7u5uEbtPpVBcXFxkfm/f5P+5XyiaHnJSMRCzv5VzSOrlO1THn8vvztimMj7fo8qSUJ+58PfA4wn1MX9v8nqK4wu/d1zavcnAClnXUiX5v7cY6B+EfRWDY3d2dBoOBCoVCXpXwc1pOxr7BVqtVUu00m800oQA4sq+8F5UUQambZ5jdgfCAs1KpqFqtJhDwL75PlphZktZBLq9BzEnrUl8IR1c/OHB71g3DUZpOp+r3++r1eprP56l/1nJ5v3P01dVVZsHgnnHumfAQGN6Xy8kKJyr8Hj0LzmfpW+KbxnB+B6HZbJYITQcOVxxzDieBfCHz8fdSWs7PPaJ09ecPSb5JJRBBz3/3neFZAH0TsUhaOPDz/DkG7+N58TNlHih3c8vtbRtYNhgM0hx0Vbq0VrU6VoFz0sNeeFJ2AyZ3ZDyoc8crkrJSNoAmQTUajXR3d5cUkVFVVChk+2PjLHL9zEPOg5PkhKefn7nabDaTcwcOMVZ+nShUUYBG8teJw+iwbiJdIBQjGcu5GBtJiZjG0YeU9EoTd66jU+oOMmPgmzk6GRtJgdvb2wxmemDgzq0HEyRA3dn18fBrcTJ9E0HAOksSj57h3laGsc8tt7dppVJJzWZTg8Eg098v+oVOhkUSkX/4YRFnOQZz3gNbT0B50O7HdMwBZ7zCyst68cF8Prr/6IQk5wWznCRhU0bavZTL5aRuiv48n3WcxDYRLn7Pfu+biF63TcQsx6JCzrHVz+PHo9TX1yfGCX/WiRUwO8Ydm9aLTbiMzxyJBt6/6Ti+jsX1xwlkHwuO57ET35nLy8u0CVFuub1tA3P4bnuySFoLe7zdVZxnHutKa2Wq+8vue7lq3LHVzf0wfBl8OOclYgKJ93sCxeNbv1fuK2LFZDLRzs5OUuAuFgu1Wq0HSR3fuHcymSQRGq9RaYqvFTHNccnXGd7jeOh46vwCwgg2YmPtcXwD7yC2uXfieq4nJp18PN3f5Fi+nvAZX5e49/hcsejf8iyczGa8GWuug2ul7Sb3He/97u5OV1dXKpfLaVPj3N5seSTwU2y1Wmk0GqVNnjxw8y+878bNF9fJO3b+q9friXBFRUrWAXCC7GRy+yTzskx+9l1kcaTpW8gkQRnL+bgPgk8HbqxQKCRVab/fV7/fTyUOk8kkOX6oM3u9Xjq+b3S1t7en09NTHR0d6e7uTj/5yU80Ho9TOVej0dAHH3ygb3zjG5n75bgocF3ZwOJQqVTS4sP9jsdjdbvdlFVbLBYJBN3ZpdcUm10RHNDzlYABUIkLDePlyjsvf/XzMZZcp5uX+PJ9ofk35Xzexxenm2cdd0KkXLrRaKharWaU2jilvIfek5PJRE+ePMmJgdzeGbu6ukrEZrlczjiuzJnoTDqhGB0qKaskcmfT3xOJhk3BMcEjlQmSEnEcnVOy965CoOesB5bcn6vU/dzuBFM21mw208aHXoXB9XiyyvuSsT6Vy+XUisedTnfWPaPuygwPlKfTacIkkpJbW1v68MMPtb+/n6oS+v2+xuNx+hzrpaQMQcsYgrfgryfGeEZULfiY++Zh/jz8+wFhznfHK0gKhXvFgzu9/v0B8xnLWGrmyl9PAnA8AgQ2v4yqkdxy+zKtVCqlpPqmUnDp4aaqjqHR/DVwJPakw9dwwgwjCPRjSGv1FX4MrQ6cWFitVsmn9T7/YJkH/5IS8cq13d7eJjXscDhMm7ySbKtWq0l0EQnoYrGY6dk9Ho9TKSfXWK1WMxvR+JhI2RYNTiYzLj72Tizc3t6q1+upXq+ntcDJC08wQQaAn07Cuvnz3fQ3D/6dHNhEAkQyJn5f/HsWFXWsfdxn9HcXi0VSwXINvO5ryXK51KeffqrvfOc7mTUjt9y+bHM/VcrGilhMbnkCGUyj4sbnHb7JcDjMnI+/uWjAY1r/mWuj/Ysnw9jvgOsh2bxYLDK9Uzkf2OO+NWpb5yRevnypyWSiw8PDxDOglHWSWVKGnJbu/Uf2VqE3LkKH0Wik0WiUfFS/DrAlYrm03nQVPIGj4b5o40APXY4f/TnnM4gHEAvQvgfBnBPh7u/GRFZMdPJ9iOSuV3DFtQQj/vDrdrEhz9XXJGIbxs+fue9FcXt7qx/96Ef67ne/+6Ave25Zy9mXN1ixWFS9Xtd4PNZ4PM4oj3D2isVi6kdHj1mUmDiCOI3e59QDXAJslFbu5ABsEHIAIgDn8ncmH0EeQOek8WAw0Hg8zigIIBPofVKtVrVcLvXq1Sudn59nNoYiWMV5v7m5Ua/XU7lcVr1eTyRivV5Pm2a1220tl0v9+Z//uV68eKHXr1/rBz/4gX7/939fp6eniZy+urrSZDLJlPo2Go3MfUtrJSgbfjH+/X5fn376qZ4/fy5JSQm2Wq305MkTNRoNtVotFQqFtMEKQQFg6yUFy+Uy3SuA6WV8Lut3pZ7v2ggIsph4qa2k1HvSHWU+A4mOw0wfMgIQiGsAvdfrpftio5i9vT01Go1Uzgcx7d+B5XKpdrutzz//XKenp6knY265vQ0rl8t68uSJPv30Uw0GA+3t7SV85Lve7/cl3ROO4AcOA46PKy2lrCKJee/Z/ag8AB/dYXZnjew7SnN6ssbyJt9c0Z0iae2seNDs/WZjlQTXwNp0enqaFAFg1ng8TjjEvVDyNZlMksPn6wfjCFHAud15chI6lqey0QIbinl2vV6vq1arqdlsqlKpJHLC2wbg4Ht1h5MMru5nHB1n467Z/sw9+HeylECG11yhy/PwjX78fvEDYpDkSjLvY+xOMc+X87x48ULHx8d5ZUJub80mk4n+4i/+IvkqkHl8f8EMrwriex2rAKR1CSc+LHjgyaVIynn/QfdNpOw8JqCNwaVjOXgiKYOh+LJgCPfI+0ejUar4Wq1WSUUJMTCZTNJmXb5PgY8BMcFwOEz7Nbiqi8QZqnh+J3HmSUaCa8deYoP5fK7RaJSED6yPxWIx+XuebAInwU3WJB8f37jGg3Yna7gXryhzksDvlc9zHmIjT4RxfU4Ku8/tCTTe59fF+OJHs8Z68tOvVZL++I//WN/5znfyDWZye+tG5Qz9/x1jwQefK8xbT+Iz/zGfL8w/KZsocXyNpB+veQwMnjjWSWvclu6TT64Q9XM4xt3d3aW9DIrFoobDoZ4/f67Xr1/ru9/9rtrtdkatPxgM9JOf/CTTCxxMBAfb7baePXumk5MTffzxx2nD8uvra71+/VqfffaZzs/PNRqNUtwNXkZiUlIGB3kWtVpNR0dHarfbKek1n8/16aefZvYHcIx2MRx+Jokjx3avaPO1FFEYhLXHAqx5HIvnw2vgrR/X/ffY3se/dzFheXNzk+GdfE3iu4Uf7p/lu/Knf/qn+vjjj/OqhJ9iORn7M2xnZ0etViuz+QgACgiVSiXV6/WkUpKU+WICKjhMDmYEhHzJp9NpmhBMEojN8XicCcrn83mm3ItyKoJyB9sYbDq5Qf9QJ/i63a5KpVIK0jepiFarlU5OTvTkyRPV6/W0cVi/39dsNtPz5881Ho91dnamy8tLSVKj0dBv//Zv61vf+pbK5bI6nY6++OILDQaDtPN3pVJJBIErhN3hLpVKqR/gcrlMmSmcPYJzFL2ffPKJ2u12agZeq9V0d3eXSPLd3V01m800pq68oi0C2URpreTyUjAWsLgwQtawoQ3P7/b2VtVqNTndPCvaBzgxzHeL7wnXx/U0Go3U/5Z2C6hey+WyqtVqxvn0QIKxi4qG3HJ7W8Z33YNu+oDSLgVVJdgjZVU4Tih6kOiOhAeXBJxSdnMmjovzi+NDiRLn4XUv24GU26SiYl7zOd5H4oXfuU4Pkufzud5///00f3GYSNhMJpPkIIJHTvz6mgNe+nV5Mon7j9l4cNmVodzzfD7XZ599pk6no/39fT169EgnJyfa3d1VrVbLrGM4etI6yObYXD9OozuSEAiLxSLTt8vHy5OR/jcP0sFtAgtIJu6T8/g48Dl3Znnu/jn/G8fYRDjlltvbNFSbrpKX1v6Lz1dJqRWA464TYJjPVcwx1X1hglg/vwerkhKhiY+MstX/xYAR3IBUXS6Xyd9rNpv64osvdHZ2lnC8UqmkeXt+fp7utVar6dGjRylByD0sFgtdXV1JuickSHxx72ApibHZbKZKpZKURVjEWydd/FzgDyW9YBb+33Q6Va1WS4peCHEXeURs9LWPQD6upTwjV885yc7fwER/v5PjnrTy9c1VV3EsiL/47vEZhDERS706j3uK61duub1Nw9dj06nY55Q5yVx1taP7Fu6bgZP4IY7FEWu5Bo9XnSuQ7ucNFbEkeuIxVqtVSqT59SBEk5Qq3CAWT05OVCgUdHZ2ppcvX2owGOhv/I2/oXa7nTAMEQFJona7rVarpVqtljb4LZXWe9GA6Y1GQ71eT9fX1+r3+6lKq91uq9vtJlEavqrzCJiLIRBDPX78OCNYWq1Wevz4sZ4+fao//dM/1eeff67z8/Pke+NP8gypwiWGYLypqOB74AlG1iQX9/FcebYe44CvjDu8g/v+nqxyXzmqjz0e8O8sz9kFBsQ/saWcxzE57v50y8nYn2KLxUJ//ud/rm9+85s6OTlJoBj70eGs9nq9TAmSZ3VR4riDgcKG32PgCKjxRf5pm594lr1QKKTrQ0pPgE+WhnN5sE6LhPF4rGq1qlqtliYj/bOYgK1WK4FjvV5Pk3MymajT6aQm3KiEAIxyuaw/+qM/0tXVVeppulwudXp6mjJigNFoNErEqAevgEkEhVqtpvfeey+Vyna7XV1cXCQ1bKVS0cnJib75zW/qt3/7t7VYLPTixQsNBgNtb2/ryZMnKhQKarVaqbQYQOr3+8kpRHkAQQSpzTP01hEQvU7EuCKgUqkkBayrPyB5PMDgOxMDeYIFVxbs7OxkSn9ZdHxjHa7Hz51bbu+C4czRY5qeQ5QtMRf5XkvKzJfocLjD6cSfZ4RdQeBkgBOBrljnnDFr7cd1lULMfHv23LPMTixy3nhNqE5pIYMqAQWS929ywgT8IlhvNBoPqgLAFu+75esROIPSolKpaG9vTzs7OxqPx2mjx263m9S4VI7gtKMEQZWGotdJWP/fVRlgmxMArAuRCNr0HmldnuwqO19L/HvjSlawl896WZ7/LZIq7ox6oOKKiNxye5tGUC1lN0vyv/NdJhnm/qkHkp4Ac9xw2/ReT257EOgY6dgLNvv5IVOldS/xnZ2dFJQyn3u9nq6urtTtdiWt24+Mx2N1Op00DogEms2m9vf3U/UYaloS3y5SYHM+r2YDX9n0BfzwYNYD5k244EpUYgDGA4Xd1tZW8v8lqdlsarFYpBYxviGxrytcL8Y6E4nYTbjqzxoc5zOscb5pL9ccg32eKzhMss2/k+4Dew9gx1In5z05yncpx9zc3ratVisNBoM0v/DVmBP4OlQ/LZfLlHzg88xd98miedLdSV3Ha87vxyFmB2NR/KPWdJ+JuJL56HiPSAzCtNfr6dWrVyoUCknd/+TJE5XLZV1eXibsgkNAkVqv17W3t6d2u61Go5FwAX4DzHQCeGtrS/1+X69evdLl5aX6/X6mQsyxysfDsYYYud/vq1KpqN1upySXJLXbbX37299Ws9nUy5cv9fr1a3U6ncS97OzspLXCq7sYY1qT0dbG+QC+E151F9dV3uf/r1arFEe4SMT5KOdVeJ/zFN431r8j3hrSSf0oMojfzbwN4k+3fHR+ihWLRTWbTX322WdaLpc6PDxUtVpNwEZ/EJyDwWCQyS64mpTjYT4B+J9sin+xl8tl2kTAAzo/L58DbHEQmTir1SqV9pJ1QqXjQTEZG8hAlASoJlFy1mo1HRwcaH9/P1Pe685oq9VK43B0dJR+dpKlWCymUrBHjx4l4hUD4CEbfTxZqBwA6E1I39bZbKZ6vZ6UVywonU5Hr169Spmy8/PzRCzs7Oxkyr/8+BAZONKMD+DpGS2MZ+DONWON88/fXQXhpce8N7ZIWC7XqhP+d0KX8eO9mxaf+XyedqGMTnFuub0NKxQKajabmk6nafdqvp9kyyHnCNa8zNGDdWmNsR7sOzHG3OZnd0icqOPvOHw3NzeZ84BtMUDd1GcVXIgJplqtllEY8A/1k/+NwN5LZn0MPTHDGNFPutFoqFarJdIABxFn0XsEuhPo/9zBItGGum5ra0vD4VDlclmtVis54pJSVcZ0Ok1lZu6o0j/L+2hRnuwVE359XIM7h2Cujz/j50o/D0IwV3d4yZ0fh/WCZ893LfZXlNb9xzgmpLUnWHPL7W0aRKX0cAMvx0wP4jywBDtjMmKTbXrdCVg/BgYeSOv5hJqIgBy/DGz2EnVvO0P1kLRWhqLCInAm8N7b29Pe3p729/dT5ZmvB5wfjKUfIhjnROzu7m5qA+F9+CKpzDh6FRTGWHvZsXSvgnKSgZZq4NebqjUgZF3Z7/41f/NgXtKDdcq/N/ydc29SRm0iYrlursP9UgQtxDnxO+LX5kk1Py74m5Oxub0LxvcRws3njPtznoyOWOC/b0qwUE2A+CnGp9LDzfL4F6tqia19QyxPTnvyRbrHaSqLST4hoACnwblut6vhcJhJrMA1nJyc6PDwMEOEcu0kmpbLdSua2Wym4XCoTqej169f68WLF+r1eon7iLiF7wtOMf7uP4O10+k0KXBpp4jAAX+c+J3KYtYH7k1Spq0j54O38Aornmf0bfkbx/KEJef2BFRso+DxCePtlXLb29uZilm+Wx5v+Di6eMJjmnK5nCrCc3uz5WTsT7Fisajvfe97+mf/7J+lCfLkyZPk+ElrMBsOhxoMBqmlQJzofDljZkNaOwrT6TQplTxoxAEl8HUwRY3qCh1aAuD03d7e6vnz5xnSlQCf3WH9npnEg8FAg8Eglcijbjo4ONDe3p52d3fTZi0OAo1GQycnJ6ksntYDi8VCnU5HxWJRh4eHOjg4ULPZTKo3b7Ad1RaAJQ4hk9yVAnx+Pp8nouHk5CSV6xeLxUT4vn79Ou1IzjPpdDoZALu5uUk9g/f29tRqtRLYupMMsQLZEHv/xtJkFgPGxBcB+tl42UAk8X0TNklJxQsJ4AkBJ0D4G9+nu7s79Xo9ffLJJw8Ijtxye5u2v7+vL774QsPhMLNBwM3Njfr9fsIjkiaRPHVnFPIA3IgtAHA2wNpCoZCOj7nj6RUEnAeHzYlbkj/eXsaNhJekVAoF5nuAzHudBAAj2QRxtVqlxNru7m66Fsbo9vY2KQso9SIAcGfMSVnwhN8ZBx9Xz96TWGNzsX6/n9aCR48eqVKppI0VnSCgrYK3ZMFp9qQiGOt9zlFdgMeuCGa94FxeTSBlN6VxnMY80IDc8e8LrYri980dUp4tx5vP5xqPx7q+vtZwOMw4ybnl9raMueNVTATj+JtgG3PW+8t74gNzPHZ/WMpu8BIJyIjhvEbPfpIyq9UqKbVQr7bbbQ2Hw9SyCnUVileSaPSHbbfbqtVqSSWLn1gul3V0dKT33ntP+/v7KeCmxJV7LRaLCfNIzkA6XF1dqVarZaqW8NUijkIY+/37zx6Ie1LdiVGI6Pl8noQDjlf+jPxZ+FrFPe3u7qbvgq8F8Xl7kpFj8Vz5rqxW670UNh0DH9/vxdcj7sEVs74WxnXRP+ftKkgCxu9ibrm9Ldve3tb5+bkODg5S7Iki3AUGzEMSES7i8WSNv4c5wWvEgs47uGggionAfzDaFfz4tO4vQoJC4G1vb2t/f1/7+/vpHIiXmMfgMaILKkJrtZqq1WryV/f29jJtaWjJtbOzkwhceIqzszNNJhMNBgN1u11dX19rMBg88LV8jPC1WXMgdYmX3ffs9/upVYK3d7y+vk7ncu4HdTPXD8dBxd/u7m5ao/DJ8Xsd//z63fd2nzWSqrzXhRO+9oLhtKLge4KgQlLCTOdhPJnoRKxjuccNMUGQ20PLydifwwaDQSICLi4udHp6qpOTk9QftdvtJpLxW9/6VtohmaBdUlIbuaRfWm/ONZlMdH19rXK5nFodeI9A72sC8NAPVVrvns3xmQxM9k8//TQ5rUxS+rVCaKAE2N3dTY2ui8Vi2giqUqno4OAgOXkoew4ODhJQMCEnk4nOzs4ymfj9/X0dHBzot3/7txO4cj/j8ThNcFcuEXg7yYFj7o5fpVJRvV7PtDWQlMoU6B8zGAw0Go0Sof7kyRO1223N5/e9c5vNpqrVaiplYFMsxszLaYvFYnLmeVYQCFE9ISnjFPtCyj3zWUrNtre3UylxoXDfagGwg+RZrVaZPj5OKjjhjyPL+4fDoS4vL/Xpp5/q/Pw8Q3Lkltu7YuCuB544cGALZJyrAnCCJGV6p4IV7piAT5Bj9DWlzAfcdnUkuFAqlVKbBIhPKdvyhfkMRuOseJLNFalgOpiyKYsPHtFuADxYLBYpkHb8qVarevTokZrNZqZclLI3Jz+c0OR8ECuunAK/3IlfLpdqNps6Pj6WpNTTEKeWe/bqApQDkO705waPeHb0MucztMphLeV9kXBl3PgbgY2rrwguSGC6ksrXNojiQuG+hxkOvAdHYLknx3DCUXb3er0UWOTK2NzeFVssFnr06JGKxaI6nU7a5ARsurm5ScE2BKh/76V1ua20xk5el7LtP8BEAmuwxolU8JU2LFQUdLvdhMGXl5cql8t6/PixlstlIl5RoXc6nSRaILHDpq6o07e2tlIrgtPTU33jG99IlQrT6VSvXr3Sf/gP/0GSdHh4mEha9pBgjaAv7GKxSEkvaU1yxIQbhEksMY4Y7EpcxszXBVRW3obMFW7EApCs4ButtlAGs/cD6xL+ql+fi0ucNI/KWGldWeHK6RjAg+H8DfUdOIwPznfDxSrSwwQY8Q/nxVegyiy33N4V++EPf6jj42M9fvw4kbJgR7vdTjEmSWV8B2JQ/DlXNaL+l9YELfukuKgATCeZzXGocNrd3c20DCCJzEaPxMvD4VAXFxfJV4XUe/Lkifb29hJHQrs/Nr9CbSvdCzDoc83GuNPpVM+fP9cnn3yS8B//8v3331ez2VShcN/u4PXr11qtVqnSCl/TyWopG4fHvzFejreSUmxeLpc1Go10eXmZ1MDHx8eq1+sppvaEDyKvra0tVSqV1LbACfLlcpkqrnd3d1WtVtMagj/Ms4qxiJQlVWmFSWsEX1Njha63ZPD2lWA9G4VzXrAaot3XGLiFarWa+R2R3Gw2S+0Yctts+ar0c9g/+Sf/RJJ0fn6uTz75RH/4h3+YnDyC7fl8rmq1qhcvXqjZbCZQcceUCUbAv7e3l1FLOulLhgT1j5dbrVbrZv0AGeqi+XyudrstaU3gsus3WR3p3nmBSHZi9O7uTufn52q1WnrvvffUbDZTacHt7a1evnz5IMP04sWL1CcW6/V6Oj8/Tw4Yza+/8Y1vaGtrS+PxOENMlMvlDGnCGLHguEoLAMXhhfxdLpcZxVylUtFwONRyuUzn575Z2CSlwBy1BaBUrVb1+PHj5GBDkuCE1mq1lOmbTCYPdsiV1moojglI3d3d6fLy8gFASkolFr1eT5LSzun7+/u6vr5Ou0IWi0Wdnp6mgMhLmT2TBmECCXt3d6cvvvgibbDWbDYzzy633N4Fe/r0qaT7ZFin00m9qkieUBVAFtsV6Dg7npyS1uU8OBOOqwT9zCHa0LgKyTPP9Mom408izVuq4OiCGX6NXjo7n9+X6xOYoq7yvl2e0QY7cdZwQD1xxHGazaaOjo60t7eXgn8wwZ06rgXs880KwWF3tHH4XN3v4wdue6/xmJVHWbazs5Na4vjacnt7mxJo0+lUlUpFH330kb773e8mpe0XX3yhy8vLB+1iOA4VFAThvkEj2EvAMZlMMs+b9WS1ulce9/t9DYfD9GxIhOF4OgkOpvP8J5NJaiFEoBCVZbnl9jZtPp/r+fPn2tvb05MnT3Rzc6Ner6fhcJg2p2JenZ2dpbZNzDFPgjl5Bl7hl0AkersA/CYwknnZ7/czASoJ6/F4rO985zv68MMP09z60z/9U83nc52dnWk8Hie/+tWrV2lPAQLUm5sbvXr1SqVSSaenp6rX66pUKknFNBqNdH19ncjc58+f64svvtD777+f/LvBYJB8Y4hO8BNRBnhEEO1YKWUrn6Ss6skJWSc/nUBgfYAcqFQqmcQUWIs/jerKzw+RQ4IoJja9Dy1EMqW53ouS9RFsZawhvMFXT+hFVSv3hN/MmlCr1VIViL8fccomla4nEIjR+F7mltu7YH/v7/09SfebTVOliGpyPB5rMBhk9iFBUOV+kgu1XMgEgVYoFNTpdBL28l73j/mctwAbDAaJfOUc9Mz2RMrNzY0ajUaqimo0Gtra2tKnn36q6+tr3dzcpHaE7XZbV1dXiXAmoQVBOxgM9Pr1a41Go9TnemdnR/V6XcfHx3r27JmePXumg4OD9Dmqwn70ox8lZar7gTc3N5k4Gwxyf9Rjc/cB3Wd3AcXOzo6urq70xRdf6NGjR6nijIS9cxW3t7fqdDoZAhVx3P7+flrPUPM7+Qru0oLGxRG+TsCFcF/ct6S0MSWV155gcxEdz4NN2/kueOUCschoNMpgLmsFYw52c/2x2jC3rOUj83MYjsvh4aF2dnb0x3/8x2o2m3r27Fki4MjkDwaDBByUNknr/qcoMlEEeFacTcJ8h9hCoZDZLdzLura3tzWdTtO5Ud/QCBogR4nj5IUTddPpNLNhlaRMWwFAGgfWnRl3oLa2tjSdTtXtdhPJ+61vfUvvv/++Tk9P9fTp06Qm8qwVpAf3wpjzs2evPMPuGR9pna0HjMiOkSlEOUBpgysICoVC2oQGgG40GprP5+p2u0llASlMQM+iSR9GSoK9zHg+n6tSqSTgwwBsrh2io9FoJLKeQODf/Jt/k0gXgpd6vZ5aY6DgYAGOmxrhlHc6neSQ851kJ8fccnsXDfy7urpK881LFZlDBI3eegRnADUlakzfNRzHhLJbHBDvSyUpE3DisEWCFkNdwP9eAsXnvEUA98JnWB9cVeUBrLRWoHl/a5y1SqWSqgXa7bbq9bqk7GZc4Dg45SphAmknoXnNg2h36ry8FgzFIfSyO5JfnM+Ddy/p5dkfHBykdgff/e53Va1WdXNzo7OzM/V6vdRqh1YOrEk4f1H14T0HcSZRgKCQBae5blcy8z1Bfeffjel0+oBwiaVxkLjz+VydTicnY3N7p2y1ut9jYLFY6Lvf/a7G43HyX1BXQUCCt5KSP8vGtqgQHWdc+Qo2gJ9gjc//SqWi9957T9K9z8Vmq5VKRYeHhynwJyB3cQJJIHw2Wm4dHR0lYnJra0uPHj1SqVRKZPNqtUpJdhJK+HTf/OY3k3iByqm7u7tEIhMPQJiQ8HMiwH3cuL7wd7DKy/N5fyzX51h83gkGH3d8WI7pn2edwg9HTVutVtVsNlWpVNJO5Dx3lLaxDYsnojxWYY1w7I3KWj5P9YFXuCHi4PtCpQL3xZh5i69SqZQ2GZ7NZmo0Gnr9+vUDNVxuub0t4/sPgfnjH/84/c4GtrzPfRdpHZNTgcTcceWiJ1a8zJ3Pe9978KVYLKY2Ae7zspE3QjD8VhS5iJ5qtZoGg0Gac2A0Jfio8PEDOQdrB/jTbrd1dHSk4+PjVJEMoUtFba/X0+Xlpc7Pz9Xtdh/gIPiL8XdwhJ/5G+uFK1BJ0Pn78V13d3dTe8F6vZ565Lo62ROTnojs9/vqdDq6urrS1dVVEop4BbP7/tHX5zpjdQXPkme8WCxSZTDtGUlgeUXucDjU+fl58lH5LOdg3Z7NZqnvL+uMdN+u04V9nKPT6aSq6tw2W07G/gJ2fX2tH//4x2q32/rwww9TD1Fp3UtuOp0mgtDJTZQxSLy9FDL2ZfFSGoI3z4K48qvRaGScocVikbJZnpVgh2tAg7/hDJMxduAnm+RBf71ef7DbN8dnkk6nUz169Ei/+7u/q29/+9t69OhR6gtLWZkvKBzDHSRXjXkZHMFzdG65lkjiupKAzBKgRqDNvxg08zx9AUIRRx+bVquVHHAA0xdMd4g9QOcaADlIVFQHACY9dcvlsn74wx+qVqtpb28vbVaxWq30xRdfpGcK+NE7EuUW4Mz3jeuBoP7BD36QFNW55fYuGar/QqGQye5Kysx1ad0ChOATZSpOJ86Mk52uaAd7UaZ6SSSOSOwZLT3sfcj84rg4hk4WepsWnLtisZjZZduxHYLZM9CUCqPILRaLibykfyHEM9fJeEUSm39OEriCyNWl7pwxTh5go9LnmPwdpxGiwY/j2X6eJfh0fHys9957T61WS59//rlevnypq6srdTqd1EfS1xXH3/hMwOSoTGY9iC0vcFjZoIzXuB/uIyq7IE1YV1hj+TedTvXs2bPUWz233N4Vq9VqOjw8VLfbTbhEaxHHLEg2LwmPJfdRteNzkYoo5mpUzvAaiSqwFBxkcxAv1727u8uUSzLvBoOBJKVgeTQaaTgc6sWLF5m2KXd3dxoOh8nHoj/gyclJZj4z7/HDvSrCcdOFFZj/7FUP/O7vickaxx7GiP/x9TgfOOZBOsfgd08ycpxS6X6zR3YvB8fZW8DxmmuMai3HfF/P4rn5O98BTwr6eodQRVKm5QvXw/ru6ysJSeKqw8PDtF7mltu7ZMPhUGdnZ2lTKL630logAPHnFURglFfQ4l+5QMnjUSnbVsRxwiu4vHJzuVyqXq+nGNP5BTCcNifsK0NlU7/f1+3tbWqxcHFxkfH9HMvAYtomHh0d6dGjRzo6OkqtYRaLhV68eJES8v1+X/1+P+HRJrW9tE4ObUrGxPc6nsHVcI2SUgIIcpTKWf6GIID3l0rr3tusoQisBoNBundvLRCv3Ulj5z94hjx/rpPxYN3kfN5ah+uET/D2EfG+GQ+vLOYauEfuk+/p1taWTk9PcyL2Z1hOxv6c9vr1a/3kJz9Rp9PR7/7u76YvlwfLHiTj3JB99/IhjAkprSc4x8DJ9SzymwDTs844JagdmbBMeAiK2WyWdhekp6FPdklJ0UlJKv0J6efE8afTaSpZLRQKajQa+p3f+R394Ac/yBCxqIa8ZyHX520I3Dw455r4rC807thF5QHjxwLGAoeDR2maNxYns4NTSPYQYuDo6Ej1ej3tRk4wz/Pkufk1+j25WsDVDxwHwokFsdlsJtD0ssDRaJSalfd6vdRHlgU5EjGo21wZuLOzo+9+97t5CUFu75yRgZ1Op2kDKhQEHmC684W5M+Sl/152xMZTHogzb/jdqwikbM9Wfuc8/j5PIPn1OT6xfoA3bu7oOYZ4YI+yHQe2VCqljRE5thOS/C4p09bEx9KdLHfoHBt9nFmnvGQJAoUMO2sG9+73646xVzmwznEsNsn8j//xP6a2BIwB+Mz4RMKCscbBdsWel+eBq6yrJBAh9Z2MptIEZYGbr9MYuI26eLVa6Rvf+MaDNS+33N6m1Wq11Fsa5bZ/R11RGZNiBJS8FlVJmBNsHC+qJHkfx2LOxtcQQDjOocQiKEQlP5/f94WmOgsiF7yXlHxUfDDve436luvd2lrviO0J+U0+n//v9xdJAMYxKmk3vS8S3E6G+jg7lkrZ3bXBSYQfrG9U90HygLFO+oC1nCsSqE7C+3viOsBaxTOK94Z5OTaqWFfKerKRRCjr+2KxSOtiHMfccnubRvu5fr+vp0+fqtVqJTyR1rEqPhtxoqQkugEbYxsmj5EjzoLR4APvixWVXAM9TVG4IlaClKU6mF6yYACJE6qkaCvIcSWle0AVik/r+AThe319rU8++UQXFxepHQ1Ysyke2DTfY6zg/r1fm58/JhmlrPIYH9kVv34sMBSchlzf2dlJytFCoZDWJT8G1xFFY37tkMAuPMDfRTntvJDfAxsIO3fFuPJc+P7Rg5hr8/Y1XDdWLN5XfeetYX665ezLz2n/9t/+W43HY/3u7/6uvvnNb2q1WqX+q54x8FItMra1Wi0zoR0UYxDMe1CQOkHqiizOKynTKw8Apo8or1PWRTZrMpmo0Wikxty8RiYDR5dm2tvb9zsjttvtBKYOOvV6PWXF2u22/tbf+ltqNptpkpIV95JRn7SMhQfmvgkExr171s8BwJ3DqDjgej377qqG4XCYFonl8n53WloGjMdjdbtd1Wo1nZ6e6vj4ON2b75DIYhADEECVe3Bw53sDYT0cDlMmC0dzb29Pv//7v69icd1/CyK13W4ntVi3203ncXIYcmE2m2UUDYvFQldXV7lzmts7aZQ6VSoVnZyc6PHjxw8qBVy96OSppMzOoK5aYm7d3Nwk54+2JBzTy3g4Jsm1mBQjMef4BRaBz+6YSWv8JgDu9/sJn/mME4oeRPraAWHgbQlcwQlW+LzHMYwqKmlNbkcVmwfdXE9UGDi249RDcKJm8o1ZcAwjTntgQBnX3d2der2eut1u2vQLotdJZK7BMRnDMfVz8DrkLA41Qc5sNssktXBO+ZwrRLy1BIkz2moQULGmu1OdW27vih0dHalYLOrly5cPWpm4bUoiQJh665ZIska1T/y7ixqkrHrTg0h8W3DFk1v1el2FQiH1taNcnaT8cDhM/vuzZ890fHycNkGkIslVoLR+ms1mya+VlJJ5zHknULwiIJoTBvijngyPRMIm0jbimqvcnJgF00ha0f/VFU+QBL6Boe8V4a3TNj0/X6P8e+LrDdfC68QYfNbHztcWF6TwfogGhAXcH8fdpNy6vb1Vt9vNJFJzy+1dsD/7sz/TeDzWkydPUn9sVzlK676fq9W6xz3+rFdW8jcp26oEtaPPFWldJeUVkz73PMEhKbUocVEDCbjxeKyzszP1+301Go0kFPKWTvQkBWNYY2i1x33TbpGNGiFoF4uF/vzP/1yfffZZJtkuZTfycz/W/Wg3x1gXUEVlbfwsa5ZzGFE0N5/PU6KQhB7iLY7v/X8PDg4SOXt9fZ18y6ggdmWzcw3+fXFVL4kr+By+J+A8f/cqbbga/pGgrdfriYPgnCQ1aSHD371FXC44+NmWk7E/p7Gz6ve///2klEFpxZeXJsU4HShg6LfFF9/L0aU1kEAmOKFHaYKXN8bPeem5O3goUHlPsVhMu1bTy6Xf7+vi4kKlUik15i6V7vtnQWRQpnV8fJwcplarlRye29tbHR4eqtPpSLrfFZENwxqNRrqum5ubB8AurVXE0THzLB5O5s7OTtq8insFaHAYXcnAYuKgTTuF1WqViAJ6tLILuCs3UJx1u920gQXHALC9lYL3X42Osf/zrBsguFwu1W630/1SFjgej9PfHXTpDfn9739fjx490tnZWTo/ARRlJBAWgDmkOrtQ5pbbu2iNRkOnp6d6//33k+J/Op0mIo82Hk4+EkRDwEKGudpcWgd9lOISTBKwu7LLg31prVbwBAnHJvlRLpfT/HZH2UkIb1tzc3Oj4XCoer2e+ryCL97+xANb5i5lXV5mv4kw5TO3t7fp/Iwbn2EzGi9J9Ub+jIVXGYBxjs2sgTit5XI5JQClda8uX/N8bO7u7lKfbNYINtCJJXRgI0QQxGu0OB5OuLNuOM4yHoybk/mu/MVx9lY/rjLz9f/29jb1p8wtt3fJPvnkk5TQp5yRhJb0sNepzzOCSRLolMvGgAwM84SEK7icbPXPORYz9/mdeQ2e4QOj4CFoRjzAJru/9Vu/pcViobOzs4QBbM7oBAXVbaenp+r1ehoMBql6jPPG9idch9+PCyykbCsY798I+ez+Pvcbg3CucRPR6Ak8F42gOvP3cB8o73xd4xh+HNYmJ2k3Je78OXnyzPGU9RoS2M+xSWHG/g9e7cB3CnUzPi7imX6/r7/+1/96XgWW2ztlx8fHWiwWOjk5eRDjeULLlZH4Ku6HYfF15x1iogyOwrkFKTt3nfibz+dpkywwjTm9t7eX/Juzs7OknuWz1WpVjx49SlWc7LfCuakwAnuGw6GKxWLyF2ezmWq1ml69epVw1lWomxJ5saqN17lHREtcI3GFE7LRL10ul0kEwechNN1npP92pVLJxAb45YvFInEy8CfF4n0fYOIQfFNw0OMAF0tsIj1dIMZxKpWK+v1+apvJ9SIm8HtnnfD+6Lu7u6nFZRRt8Nx8c/Xcfj7LV6Sfw/7Fv/gX+oM/+AN9+OGHWq1WGgwGGbWrlA3w+CJL2ey174jn2RMHR5webz1AEAqgMvkgEFyxw66u3qOJUirOQdsAHKbJZJIyWJ1ORxcXF/rggw/0/vvvp1J8djEdDocPmuYXi/d9CskEAWBMbO9xQ2bFFbGQxpQoARAsOlExIK3bAkhrxZgvVlwDJDQLF2Drm1AAKu12O1Mu5WQAIAhhjeMYM5JOKjhRy88ANyQv5/Z74xz0kWHTN87pfbGk9UZgtVpNJycnifTGyY2kivfRnE6nqZwkt9zeJXv58mX6Tp+cnGh3d1ej0Ujdbje1PKGFCqQnuECCynugQoyBjcwlyDR6Q4NXJKSccGSeMOc98w6mEVDSAgXlgGfLHSM8Q809MT8JNj3T7Rl7T+yhFmXeE+hCBhDYs66wnnA/UXkkIQX1qAAA09ZJREFUrUkBJ4z5R3mp99xlbSKA9yCfJBltFbi3fr+fwUHGAnJhb28v3SsJNx9nT0JCdHMcJwHcGfa2Btwn3xs2puE43lYAIxngx6vVakkxwneKtRfiwwn7SGrlltvbNvwz/AcwBDyVssl/n1/Svd+KutQT75uIWF53YpVEjuPHJuIyJjE8wOX9BIO1Wi1tokhSOu5SPZvNtL29rcPDwzT3Sda7P49QAWz2Cgyuy6uRpGzZK/fqwoMYRHvSL6qyXDnqrznJ4ONO/ODqYz4DdjoOQQL42sPv/O/rkCe+IunshDHX5s+ZhJ8TrgT7bITL+HEdYC7jvVwukyrPSYlCoZBiHo8VcsvtXbM/+ZM/0cHBgVqtliSlDUkp22deUb3qcSBtBJ1oZb4Te/vrkjL+JYQiSlUn+aLFBFmsZkKIhC9K5S0+m7d08f0a+CyVB64C9XMPh0NdX18n0hNhFO/zNcN9RF5zbMTf9c97zO7j4ASs+5ZwCC4+AN/wD51IZR31TWtjBZy/7jyI++mc38nYWHUnKaNy9XUWHOfeeZacJz57jyEizhOz8De+j1SUEUd5TJHbZsvJ2J9iy+VSn3zyiX7wgx/o8PAwOQOoEUejUXJqpGxZaqGw3nmacnjP7MbA1tVO7lT4ZzyYlpTJgkfwYUJxPf5+PwZOrKu5Go2Gnj17lshUCExk6Cg2vcyV+6T/LFkuJ0chEf2amdA4pw7C7sT62AIefixAwUudnDDgZ5xTgJTnDAHj5IiTMD6OfCYSv/F6PaM4mUweqNQgDhg7nh+vMyYECn4OrhPyhu8h7Q1c4cV1ogZ2hWCpdN9jMidjc3uXbDQapWb9lJySnSXbzIZKZJ3dOYAgA4sI4HyjK3CYz1Km6iWcvBdnxeeJl3l6YArx5ruteu9Bd2ZwYJfL+80RvJKC+/FzuNO1WCyS0wcmu+MpPdyB1x0td9D8Z1dkeMDuRCLYyz2A7ZzPk2GMA8bayDUUi0UNBoOEyWA74+0KYm8BEFWsrENgKOPqKi2IUhJhXANqLEganhffj7jW0u7FMZ73cl1OxhaLxcxGRSTPcjI2t3fBCoX7PqvHx8ept19MykjrwMwTPE7mRTLQ//H3+BlpHRyD1XzezXFTWvu6rmDicxAWrqQFt3wjVzakLZVKqYTUk1weIIP3rgiiKsP9e7eYPIsJMcdkkv/+WVeH+rPaROA6Oc5r/uzi845/Bwdd7eTr1KZn7r62n5Ox9vHj8/6+YrGYCBz8YOIlf36ufJPWbbcgbzku4+rfQff1weLccnvbtlqt9OrVK3300UepAtL3VWFzbkhVvuP4OPhDcd4TK7u/GPkBfBQI0agsZZ55IswxHDzldf6G7+Z+qyeiUXqCmeCx+8GxqkBSUswWCvel+1QVcb1cEySjYxS4ExN5fp2Os5uS5H4ef7/fpz8DJ1l93Dxpxz/WvoilLhDgPE7K4m/6Of1anU/ivvCNi8X71pXea9cxGfPYBpz2++OfE8ecE5/ZE7O5vdlyMvanWKFQ0MHBgR49epRKd/xvBISSEknojiAOBp+LBKxnyTEnUpnMHvx6VorAGQAEkDyj7P/cEcTpBbx4P2QsWS1pXdJKcAwAABBOoKIQAgjc8YQI5fPRmfMsEoASHXs+59mY+DeuyRUSMYvn5VcorujTg0PqZbnulG5SNLiz6eDnC6NfK4G5E0O+aEpKGU+u30veopPs5JKTTFwf3yHIW44DWdDr9dRsNvPyrdzeCaMXMsHuarXSZDLJ9OUkKYZ6CcKUpMzOzk4K6iBiPSHkZOF8ft8T0JVBOL1O8krKOBoeLOIwMuc8Y+3lQDhk7oQxF53Ik/SAEInOnZMmUQXrmOjOkJMCrn7z4/OZWPbq9+ZqgYjDrlCKjjXXy9oJlvlutLzOuHEtBB/87uuLtA42eHZOqrDmMcbuvHJvm9YbX/MwroN1n8oFzss/kpY+fmA9pWls1pFbbm/L8HXdH3GLr0U/B99uk7ImkgW8xnFiQsP/zrnBX1cgeXWY479jTyQWwB3OQyI7+qLuS/k9eeKHdYB75zXOH8diE7EabRN2R6KFc2x6hrzn53nNj8m9RaLdE//xO8E48Vm/Zv/Hcf3Z+b3gm3JfXnESvwfuY0vrfumsFf68XQXt358XL17o0aNHaR3OLbe3ZdVqVc1mMyXWwRF8V0lJKOSVoNLar3OVJz/73MW8GsyTHR6Pun/nyRQ3EiF+DvxXSQ/OHbEAYVMkIGNCLeIcx/e9UBwfN8XncZ3hfe7nYk5ierwez+MY7byC/51rQADGeRGHxbXUOQaO68+S8/t4+HUSa/gz8uuIXAW//zS/M3IeCLqkNenu60RUGjveD4fDVDWW22bLI4A3GE7Cxx9/rFKplMoD3DmjDxylTpRpOjkmZVUFUrbHkwOGqwqiw+fkK+CJ2saBScpmqelp5yQd5/YST1RVfo23t7eZAJ/J6yQDk5RS1kqlor29PUlZR9edoUgscz7PJvk4Onhuys5H4PaFib95domS5K2traQqKxQK6TUyhbSH8EXElWmMpZMavuhgTky72oLriuOwCbDI7keyfrlcJoUyY+qLrBMNLMZxAd7a2tL19XVSDeaW29u0xWKhRqOhZrOZKe8Hg1HD0ueqVCpldlaWsthHK4JKpaJCoZBR7HvLEidiCfI8cQPxiTPrOA8Ox/JRJ1bBPQgPfvYqCN7vG+dsIlKZ+zh7sSLAyQgnFqR1o39XQm1KNvm9OGZ5EO2ErF8j53clbXR8HWu8p3V0anktErB+rJh4Ygx9XeE74apcCB1faz2gx/z4fAdcfUBrGWndy43NZSD0ee6+HnU6HVWr1Rx3c3trhkK00WhoPB5nCDLmvSuwIukmrZPpm5RacS69yaJP7MFuxCfmEcpISDze7wkQv85icd0Wi/N54j7iCuaYRJLHqyrw6aNaLCqL/W/x2D/r75sIhJg8igotf/+mn+Nn/D1gL2PxpoSe+/j+bPxzftz4bKnw8ioWFIJ+r179UiisqysY4xgbeCLOyYLnz5+r3W7nZGxub82YFwcHB8m39E2U2APBN0LCv4yxHP6NtyNwvONchcK6ojUmLSKHIK3jaJ+3fkz3zbyqAV/Lsdt9Ud7vbQ55n3MDkpJ/6jG2i+IkZea2tFaPcp9RTBH9zPhc4noVE2Tug/t7nAx1EpVz+hqJX+xrXSSqN/EczsVw3ngdjIGvmXxv4BCiAM+xO8YBjEs8F+fhsyQLNrVeYI+lnIx9s+URwBsMtQt9jdwRRY0lKWWsPOilzIDNSgBbJgRlq9LDHfA8qwX4ugPCJJPuwcaPK91P+u3t7cwOzj4BnLDEefRAkWOxe6FP5nK5nIgJSgpYNG5ubtKGDygvaWDti4U7aygUCJidHGBiU1Lm/wi0eU6RwAbQfGylNaHJ39mZUlJa+Pw1aU2kerYegsbHhjHhWE48Mxaxx68/f1+kIKAmk0nmnqIygefof4d451pYpKKT6lkryOlNi1NuuX2ZtlgsNJlMdHJykr7vlI7f3NykPllgM84o/aaceHNlIuQC2V1vVcJcYa7TKoDNwnCScUy9/QF4end3l3qGSuvyrFarlcFhnDMPIJ149USZKxi49k1OG2ovd5acKEaNi5rB8Zb7jhl0xoLrjeowHE/fDRdnmc+D3Y5hnM/HAdLc1zZXfMTMPEQna1XM8nPfW1tbmYoCT9hRquV4zHF8Ix7Gz9dYcJadaRkrSFpIAq6TY3r/Nx/n3HJ7m1ar1fSd73xHr169kqTkv0GIOekYidiYGPa//7TveSRrvYSUQN+VXsw/T6T4BrUuLIgBJj5aLOudz+dJjc9cj/fj1RL0wiuXy5kNHzlGDOQ96eakQFRuMW7eD5C/YzE4dmLBj+Ml+z6+cdzdD3eC1n1Rnh3PYtOzj98DF1D4eDo57QoqSAmuCayuVCqZyjwSZTHxBzHu3zm/T//uuVAht9zepoF59IH1zZ/AGMQC+FH4PLQWYK47Vjpx6YSftPY18ZldnOTv4Xc/nvtUW1tbqd+2tOYtvGSf/x3Pmbvx502xLf47FUz4wK6o9fv22J7Pc1wEF1LWd3XzWJ1j+7VID5NkvOaJdI4Tq3ElJS7EN16Lgoi4fnI9nphi7DiPk66RVHff2ZOFkjIVgJivw3wezsf5Ld4TNzuLJDfrmq97uW22nIx9g+GIdDqdFPTjpLLTKiBKcMaX0DcGWK1WibAkIGaCzWYzFYvF9EUlI+zqHyYDk96DTyaO9xjESfXgmM+TCXbilh31MMAastEJWogOFLAAXb/f13A4TEQJBALjw7nIftOE3513duzz8WeDBFfn+sLkzmsM9FlQCLoBJ0hTyE537gEtNvUCNLnXmNl3wjyW5TqBDphCcPNcJKXvjYMrPWMdEGMfWRZlFnTugWvkO8B40c8Xkt6DJ3rJ5mCZ29u23d1d7e/vJ+KQYJjKg1qtlohScGo8Hms4HKaG/q42BTt8DpHIYX4yf13d7mQp/5Ng801upHUSiJYfTgbjUPmuz8w7D0R5n3+W+3BFvv88nU4Txkb1qJ8njoOrDEhaYe54Ou46XnAcnF/GwzPhHItrdYfMqzT4n+Qmr/EsfRy5Xs+6+4ZsTvD657gWjuHJ0mq1qpubGw2HQ43HY0nKkOMQ+tHh3t3d1eHhYUq4smZs2iCONcA3ZZCU+nvn5EBub9Mmk4n+4i/+QhcXF3r8+HHyMaR1UOa+jLSep745a9zsSsoSZP49Bxs4Fq95EtlJtO3t7dTLmc3xlsulms2mBoNBuj76MDuG+32A1U4scC2enJvNZur3+2mu0y6HjaVikt2vk7nuY8W1+Aa3XBd46vfLNfH3SGx6oswtkpXgcsTBTefza3GBiJPKPn6IFDgOa5UnBDf55ly7B+2s9/i/HC+OVSTZOZ5fB98fT6BBCnjpb265vS27u7tLm2HTDxY/kUoZJ2jxRZyQJSaXsvMyVvc4ZyCt+69K2dgSTIzzmcpLrmNnZ0ej0SgdG6LO43EwGOyJQgL3SXm/C4e4Nq8U5RocEzb5pZ5El9aJKzeuBT/Z+QEXFGBcb1yXXBDB+HK+6NsxDmyyjlCE+3bVsxOrjC+46vfv5s/Yf/bYnvGKmM7vKLDjGMeNEzclCv076BwT1We5/XTLydgN5jsUlkr3u8/TcBoHgSyHtC6L5AuMYsZLmXAkIGYlJadDypYN+Wf8n6uBAAVAW1o7g04y8lmfeA5WPin9Wur1uqR1eRXOaqPRSOAxm800nU6TYsADYBRBEVi4RgdoFpnFYpHAQLp3XAEVAGJ7ezvjDHN/y+UyEbTubMasE/fsfaRYRBxsuV+IGy/J5Tie9cG2t7cfkD6ol8k2QjJw75RNs2je3NxkCAhpvQPmfD7PPFsCoOl0mmny7osLY1ir1dL4OUk8nU717W9/+8EO47nl9mXa7u6u6vW6Wq1Wymajdi0U7nfqrFarSf3N3PV2G6jm3SF1xxBMwnnkb/66O5DuCHO8+Xye6RnLP9qcsE7gcHsLGM+6ewsD5r+XakpKST3PuIO9/X4/rUPgFoSBJ4lIJuLUeWsHSRmFmaSEtaxPHlzzz4NsVzi4Ix6z6BzLg3Z+JlkZncioJIOQdQfTzxPVXpzTr8uftz8TLxvz40Cuck0EQzwz1hHUcjxvHwPvh+6BzXvvvZe3KMjtrdnTp0+1v7+vzz//XKenp5mgz/1CD+CiL0eCBBzxQBd/WXrYriAShX5O94P9GrwqyysmnFDkXPH6PUAnKOb8JLTA19vbW1Wr1QxOOIkR+86yIaAnvSIOSdlS+3itfv3u177J/PhR4eR4688jjr0LDPxzXvHAODJG+Lz49puukfWTn7lHx2zO5YE661Ucn0iMMIauivW/s747+Yr//Hu/93sp+Zdbbl+23dzc6ObmJuEmMalv8kpSyKt6wCGSD9G3iu2ZIvkZY0p+dgwBVzE+76rISqWSYnPeA75Gc5LOz8e1co3cj+O/tE4G+UaLEMRwGY6D3CvrBD4d/m5UaG7CaMaC6/exehMue0zvHIEn1rgfrpN1LLYx47ye9PJ4nTVkE+46Ae7XAd4y1nxffNzAS8zv0wUt+L98LgrP/Pp5rh988MGD/Sdyy1oeBbzByAC58yBlsx6umnLikfJIgIey87iBDEEz55PWfVYxn9RMKndCvHUA13p5ean9/X1Vq9WM6sAnLxOFa3ZiwietTzAcNJ+gkITNZjPtdD6bzVSr1dJk90VluVw+2NEU4gRn34HdnTPA10kSKSvb97GM72GhIRsZQdjJWh8HB82YrfOMlmfTcFZ9/P375EDlPQs9eGGMMcpSeM1VYq524xp9geNauHcCjtlslgiHTYtSbrl9WYYCaXd3N1UXgEFemuVBf6lUSmoCcIU5jPPmjokntdxp3fTdJzj3xA9zy/EJjEQZKT3crXqTE+jKAF9LcM68T3SxeK/kH4/HSRFLaxE/thOPTkxzPM7lhCnn9Wvm3D4uJNgwJ7w96HXiO7Z3kbLKieVymVoP8Cwxxo0kUyRYPGm26R54T3weHNsJI1e/xuDECQmM3101wvh4+aCT6HH9xbHOLbe3Zfha+/v7KpVKGo/HmaDZA0osquyp+IrJDIx5HuelY0UkBZ2gJNBj92fH4Ol0miFto3oqznl88+gDMh/5Jynhp2MTbaxYQ3ycvJ2N44OvP+Cyj4GP0yYVl8ca3EdMLPnYOdkSA/Y43k7axjWI50asQc90PhcDd74bEef8HuMzZjzi2ui+tF/jm+7dfWiu2//5Wp77urm9TWNe+vceARivIUKIOLyJgCMG9dJ3BEecT1rjhiev+Tvxa8SFyIF41e6biN1IyG1KwoC3jo2x8pfft7e3U/WVX1NsYRiTfsT7cd3xMXSiknXGx8s/w3viuPl7nHx2nIqtr1ardcsF5xCcg4nX6r5sJGP9WURs20Q+RxHZpoSdf+Znref+jD0J4KLEHHN/uuVk7AbzBZ2JxGSUso4H/0MCeACHoar1YJbgDcDlNc9euRMTAZAvtpO7OByoqSKx60DMcdzpcVKTCeggDXnMeEA4+rVxDogSvw+Os6mMlv89wAeQAd9I1PIs+KzfF+bg6MoHP54vUpyf8SwUCpln5KAVwYi/+4LGOPA9iYSp/92fM8/LAxLO6+DqRIQvcNyjf5cARoIGvif7+/s5UOb21o32J2CglysSFPv8dPIu9ueTlMhZny8+3yl5jMSZlHUootPjx+CYnPdNhCufdVyNBER0LMEriNhut6vBYKDxeJwcOceKSO5uUvpyna62iMGyXxOGksFVXxzHe1BzH2CnvzcSD+BaVHgUi8XUM43j4Hj7tbnqP65V/nokZSLJxHfBFaruEMex8Gfn4+0qBndEUW87Hkv3OynnltvbNL6f1Wo1+Tlgoiea4vyRlHkfPkycL5vmj9umucWxHReZs94bkWRaJCMjAeuvIQxwVVoMNiWlfRcODg7SmkOLp9vb24yfCjFAmzFXDnsVRkziOe653/emMXPM2fT3GJ9E3zveo2OcnxdMc1xfrVaposrP8abA30kN/9k/636xf5brjWup35P7+r7e8B4XI/gac3Bw8GAccsvtyzSwjMRQoVBQtVpNsTxtXzy5jbmPiuF3uK8JVsb5yvs91o8kKu8nceFJF0lpI13mlPs17m/6cd1f9niW351IdtEFx46b7W3yy7EowHA+wa8v4nP06SO+uEU885jelafwDNFP5XPcB++LsUE8Pr/H70HkPd6EuTwLzsu5fJ3wY/C9co4ifh8jqevVE8vlfSuhHHN/tuVk7AbjyzedThMBALAAoF52GgNASZnfARd/793dXabPbKFQSBJwJ/8wJxwlpSDay1BxMBuNhqQ1EUn5JNftTjRkJ+9ngrJ5jAetOzs7urq6ypSrAQyDwUDz+Typ2zYF7YyZlzf5RMbBRsnlJLeDiDtvm5RtvG+1WiUlbMxscQ6yZ2w8FjP7Dlze4kHKBuI8Iy8JccDjfv28gKDvMuif8WcvKdNTl/OjnGN8ULQ5uPP9ms/nGo/HGcXzzs6O3n///Z9jVuSW26/XXM1KwOxEbHQ4pXW5KP28pbWzAm67U4iT4DizXC6T+nITIevmwSpBH5hCry8v8QT7PSvuxEXELi/p4tyz2UxnZ2fqdDoajUap5zX/vMSNe/IyWtYTcFC6x1Z6L3pJqCeQXKnga9umtYmxcbJayvYj9FImfy/3TRkwz5zWN9vb26kNi2MgeMdnNhGxfi6ukeODmVGlEgN/vx931n0NY12CrKEtgW8Q558plUpqt9ubpkFuuX1p5oklEjzMQ2+3EedQDKYxL6v1ZHv8/KZElJT1qSLueIDn7ydR40pV9xd5X0yuUW3h9wRG7e7uam9vT9/61reSCKFWq2k2m2kwGGi1WrfFmU6n6na76bj4XuA/6xnKUic9+d3Xh7jmuPmYRIIhEpKbPuvP3Z/Bm/xnJyNon+XfCR/buJ69iUSKPnMkdiNR/SZ/HJ+c17wChOovVw0Wi0U9e/bsjWObW25fhrnPBk6wLwlErGOr+5wRG/CJXaTA52ghE4lE4tM3JWt8zoNxXo07mUwyGA/eOV5EgndTcoV/rurd2dlRq9XK9M0djUYpUc76NBwOM2pLjuXtD3wcwRwnFV1g4MQo5vGAx9N+zEiSuv+MbbpvTzp6BSvfjagkdTyP59tkjrURh514dq4hcjasSYgI43cvjlnkdHi2p6enG68xt6zlZOwG44tcKpVUq9W0XK5LEAnovfzcAQFAcZUsQBInGxMdIgAnhDJdSSnIJoDDwXBw4m/SfeB+eHiYJrdnqwgSCRzr9Xoq82ICEaRXq9V0r1zH1dWVrq+v0/UQ5EPmsrkOPXP5HP20fILHsiIsltbjBPJ+7scJct8ZnPIzdsCdz+eZTRM8IyYpQyzTC9LbC0Bg4EjzWUgW/y44UczzdQLXj+FZSy+FBtjo++sLLNflijG+H/1+P23mw/cBUpzeavP5XL1eT/P5XM+ePdPx8fFfbaLkltuv0MBOTzx5v1iwIjqXOHM4B5sywtIan2MCDeLMydo3OVzR+XNndG9vLyXfwBF+5jo5r+OtO3uSUusQcOfs7EyffPJJGhccbzAw9skFo5yQmM/nCRMlJbIzqgU8meQ9pZyAcQLVE3V83rGaMeezXGtMFI5Go7SBFucm0KjX66pUKrq9vdVgMMiQnZ7UAsfd0eS6nbhw5YGv9X4cHw8ce94nrXt++XfEk2ZOxno5dLVaTf3Yc8vtbZsnB/iOurqH3poR98AdKUukej96SZn5yOfcD/PX3ffz+el4I60xczabqV6vpw1IPdmDzyOtk3wY6wVlweAuGLy7u6tKpaKjoyMdHh6m+6IdDu2dSqWSqtVq8oV9h3E2f3XM4Twoy5ygdJGEJ+TcwCV+dkID7AP3/Pk6WeprH2Pm4821OAmxWq1S0ssJlE0kTySH4/rrz90FEr7+uY/s5KuLSfyzXAfrnIsvlsulHj9+rMPDwzfMgNxy+3LNv7f4WuCG74fiVa/uS8VKSl7f5Ne4/+rYIWUTG5HYlJTwnWPjz7gP5IIsaZ34x5ePoiAMH497RJx2cnKSNki/vb1NWF+r1TSfz1MrRPAVHOY8+MHgGteIn+ljEEVhMSnkOOnPzvHU/WGOwzPlNSfSI2HuCmm4GcQW3mM1+pebnrWvux6vOEZiUYDHc3b+w7Ee8/WEhBfn5+fb21s9fvxYBwcHyu3nt5yM3WB8Qdk5lYkLaECsRSdG2tyvCWDAIFNRY04mEw2Hw/S6Z2Bw9HZ3dzdmyzwTgY1GIzUajRScM5G9f8rOzo7q9XqaUE6ASlK3203E6s7OjqbTqX7yk5/o5OQkAWelUtH19bUmk4lOT09T37HFYpFaMDDBfWykdS9YrgtS2BccCATG3AGe47kzD+g4OEprFTHl+Wxixd8AH54p6jxXP0O8MlaTySSjMOZ6HJj82bCoxMbqbCrAtRA0cD9Sdkd3VxVwf+fn5xoOh4moqVQqKpfLCdjZaA312e/8zu/khEBu76Th8K1W640QIUk9y+uBvbRWt+IMMZ+jutMJSuZ3tVpNKgKSFgR1HsByLFfo4jRvbW2l5AeY4qVXvhYw7x3jwQDOy9wdDAbqdDrpGpn7fuy4AZf3P2VsSFLhTBUKhYStfi9gkBPNUY3k5gk2dwSjos7Hz9ctd+Aw1KQ4pOVyOREku7u7mk6nmk6nSeXPWho3rQRno5o6vgfn2e+P6/YEHOsR31HWORxYVBtUJ3h/8vl8rsePH+ebGOT2Ttl0OlW/31ej0dB0OtVwOMxsROWBtAd77vNgji0+t3wexcDWA1QnBDw5wrkdx8GP29tb1Wq1NMf47Gq1SjhGwEvSyJNMXOft7W3a/HC1Wmk4HOrDDz/Uf/7P/1n1el2Hh4eZvRlardaDxGGhUMiQvhC74BJYx/g6SQ12OM76eufj6WPNz451YJgnnvyZcK2eTGN88VG9xB/z/thOYvvz5D45P9eGP+/3IGVbzTjxS89LL9fmddoXcb0cj/HjPPP5XL/1W7+lSqXyM+dBbrl9WXZ3d6fRaKT5fJ4hXH0j7EgQSmsiMJJvnoDxOe/+YmxBgxFT48c5aSut/WrOP5vNMol8FxMUCoW0GbW09nWpWnPimHPif+7v7+vDDz9Mx1+tVgmraVlFEqvZbGp7e1uDwSAjTHAsAe9KpfXGXdyPJ3LYiCyKBxgLnoP7rI6JHr8znn7vHrvH2CGupxDe+JckEzclJ1nDnFzmGWBO+HMOT3y6b8s4Ow5zPm+zhZHw8nEh/vrWt76VY+4vYTkZu8HcecEBQAFJkB8zHtKaNPMgHmBxAsCdKEhNz1JDImDT6TSTbZayEnonKKrVagIYn8A4oq4oBaxwIL3UdX9/Pym92Djm8ePHevTokXq9XrqHZrOpSqWik5OTDGhKa8KAsYFooISX64P8JCBGTUT/Le7TyRcHnQiUPvaUL3mJLtcCGEP8OMARbLPQefbRyQ3/vkSCyK+TRcJfx0GFCHJFVeydy4JDqS7nn81mmQWS/sROTvmi8ju/8zuq1Wq/9NzILbdfp+Gw8HOcB1gk3JgL0lrt7lUAMdvrQSlkWgx+wXg+72VNXv2Ac+kqMg8wuRdfB3z9cAKWhBnBOdhPrzvuyTch8VIrXyf8Xr0/Nccfj8eJiAT7UfK6Y+eEMffM2HsPR8ZNWmfZPUnGtfrxqAphTfDxKRTWvWK5Z2ndDoLEH2oI7oXvCdfGtbpDLCmNta893EvEcdYlMNodUyetXMXF+29vb/Xee+/lRGxu75zN53ONRiPd3d2p1+up0+mkfqkkeQg2CaAjvnhQCI7GxM2mz0TykKS3Kxsj8eDHLpXuW4l5qwHwnGS/J9JI6tAHkY0QSfgxh1erlU5PT/WNb3wjJYAqlYouLi5UrVZ1cHCgvb09LRYLXVxcJPzydcqT7fiVYKcH2Y6F0T90X9bXFF8LneB0n1jKErZ+XN4HZkZ/1QkLnilrVHzGPFuMZ+zX7t+BKCTgfxcv8DlP2EVxA8fj++nENtfx8ccfZ8jx3HJ7V8zxwAlAJ+mcHPV56ngpPdw0z5PhlUol/ex96/mcC7BQv2Puy2DgV0x8u8/m9+Ib2uITevubZrOpdruto6MjbW1t6fz8PHEQTtguFovUfhFy0H12xxrnFuBU4ALwx4jx8Wkd/91HdhLZx8F/xl+OIjG/Drge538cv5xUheB2bMP89U2JTz8f7/fvlZPLkN08F+7D13i4jnjvjttgtKQcc/8KlpOxwY6Pj1M2xb/IOHieJYgEoROkHgQ66eYycHfCyuVyyl55pmW1WmWI2Ugk8BrGZPZeekwkB0H+5lmRSqWSetewcyygRx8XbwkAadtqtRLBR0aLa3HDCfZyWldd+CKBWoHfeQ/XTuaF5+G9+QAHB2kcwdgyIqqm/JrcmXWHk/fNZrMHGa6oNHPA9HtwsgMSwVs7eDmKfycgNm5ubtJGPh488R2bTCbpPA7UuSI2t3fRGo1GakXgxBxzMM4ldwIhvpgjTnqCv5ClXi3gc9AJSSdQfS6iwARHXMnP+1x5EINLV/qDR06W8g9Sz0k/MFHK9q11Ypfg3pVJYEh01iGwnYjkPDGI9+Mzvj72OKqOcx54exLRVQnepmDTdTIGHIM1FSK8Wq2m9dGTi7yf+4mVIwQrkBHcWyyL9Wv3AIjnxvfBn72vz65wYE3MLbd3xfAF6MtHtY/3m2ZOgD/RD8IiXjDPwSlel7LtWqSHm7BGddYmklFSJrnhpZTMd08wVatVVSqVhL9gq6vRqCjY3t7Wxx9/nBJ5rC23t7c6OTnJ9CPleASyXlHFnhOeTMenHo/HKbHDP/eFWQecZPQ1MPrWm4w1zkUETnTGsX3TmPt6u8kfdv/Sn108R3zerI1gpRM6kXT174jfO7i7KYGWq7Nye9cMf5GqUJ9n7nO4DxrJOI9pmT9RoerVV74BObjnSRf3m/1avN1UtCgK8887v8B1eUKMpBhtX2q1WqosgqPgffjukKlc63K5VLVaTYIjxmC5zKr2uT73rYndeS/jzHXjjzpH40p9x7NIBseY3jmGuI7FMfOYQFImFoh4i3E/Tkw7zhOnODfFOucVIn5s/154FZgTuv5d9O8p7S1z+8UtJ2ODHRwcaD6fq9vtZjIcfKE96HdHKZKofKHpe+JOKWDJxgDSPXDhLFLizzE8kHOHxsHEJyLHd1KQgBpn01VepVJJjUZD9Xo9Bbgxo1OpVDSfz/Xy5ct0zHK5rHq9rlqt9oAAxgn07JoTHg46y+V6sx0+L2V70jhxQSBOtg9gjSCCuTILkOa+WRRc5ebKLwebCIpcuwcqtJTw5xTB2kGbe4UUYZy4Fxag5XKZFM84rpJSea6PKdfq30O+x7nl9i5avV5PCifmIfPNg8BNJBntR3z+OwEItvgc5xju3BIsgwuuxvTEVVQwuKKKc8bkDAmU2WyWHEgcZT4bSzI9Ix3LRh1fvM0JSSxwhPd4sBoxslAoZBJfvgEZxr1ucsJ9ffFr8nXEr8EJTycMYoLTMZAxAX/BbZ6nkwB+LhzSGKzzugc1rsDy925SLvB9cafWMRfcvr291XQ6/UWnQ265/dqNfqeDwUDD4VBbW1sPdvV2H1J62BoK2xTU8bonveLfIgEQEyd+bF8LnGR0Hx1zQUG1WlWj0VC1WlWn00l+FCTA7u5upidquVzWwcGBXr58qfF4nMhXztfv9xMeQ6pyTo5BtRNjyfW470rVg6QM/uLXE4y/yXf0sYmCjDcRJNGvJU7gZ7+HeB7GORJE8fyOw/wfvzNxLXKsRw0XiaBImPi9bxqb3HJ71ww/1OPd2M4qJoPj/5vIwE2JcI/9iC3xD7F4Lo7DZ/26fS5GAtfnHn6t+5DssUJlQrVaTf3zIToRCy2XyyQMm8/n6efRaJThCvCdvZ0J549CACdYwVTv5e+4EZ8HfjQCvU3r1ybyPLYr25RU9MSS/93Vzh5XuCjDfWcXHbhgwK+T80HGOknr3zuvRoCI9TiJa46K6bgm5PaLWU7GBsM5oUfrfD5XuVxOuznPZrNMc+boAPikdECMCkpJKTvmsnEvdcVJYWK4w+EEAo6kZ6I9qCZIhwwFBNlwq1AoJHDc2dlJ5fe3t7eqVqtpXGazWcpWcRxaGQBaXJeX27tKolqtZsjL+XyelEMeHDtBAfAy7gA6vWY5FxsouIO3SbEBMBN8QJ46sHpA7lmySN5EAHJnluP4IsDfPCuFo8/nWZz4zpGZ6/f7ktYLeq1WU7lc3rhxhH+f+Z5GIjm33N4Vi8rISPxBxnkjfk/AQFS6E7Fa3fcNBJe8JCkqbMAFHD93uvibE3GbEiwxCSSt+59SFkvvZuZwpVJJhC/3AmHrhKb3RIzZfD8niRgnJ8Fbd6qiQ0kJlxPLEAKuGKUvnysdqCRxLOT9PEfuDeeOawZXYxsZnjuON842z42knldNSGvilPuHbPH2AnEddRUr5mSBjy3Pw9XSnijw7wyKQ0/s5pbbu2TM4+VyqcPDQ+3s7KTWR4PBIPXni4pzT2JIa4z0Pqrxve4POwnInHbyIB7f1f7MP8dfab0PAcdDRODJ8UqlkhQ8tBrY3t5Wt9vNKFW73a4+/fRTbW9vazqd6vb2VoeHhxoOh7q4uEgtHNzfjOPD2iOtA3PeV6/XMypa8McD6Jhkisk/Xys5txMiHsx74sv94qiw9WNG4UK8Bn/ubu4j+3HxifndSYZNya9NWM1xOS/H8XU5Ekm55faumAt6wETv1xmFRdE28Qj+3XcDlyJJyrzz5JfjjLTGH36OOO9l8sx/xzpUr+xfQlUacS+4zD4L3s6ReBdf1vvyE3fjh3JfjpW8z306j8EdL52b8HXDCV33yV0cwPtZP/3Zcjyvntr0PONrm4hwr37z5+Hrn2+chl+KX84zcRUt5+J8HhsQS/EvJlPx6fmOxBgkt1/OcjI2mIMdak8m/mq1SmVGmGccpHUQ6n38PKh0Z8TJAY4F+RnVjS6zx+HwLIlnTsh+cBxI2dVqlUjYarWqZrOpvb29FNSSXZlOp2q32+nYNzc3GgwGarfbmR5+pVJJrVYr01jfwZLesVzX3d2dKpWKJpNJZkEAsJzs9KDX2yiQ1RmPx0nRMZ1OU3/FTZlAgNpJgXq9nsCJa97a2koqJlcB8LwhfSF5dnZ2Mmq2mH3juXq234mh2H6C7wDkKv3MpHXJHQuh9/DiOUtKG3bxvYEsGY/HGg6HOSmQ2ztpnjSKLTnAQuZmLK/hO+9krM9Hx3SOx6YCTsgRmHuvaylLBMSSHylLaEprUhXykX/eosCPR1DJWsM/1hV6RfMZzgVu+TV4gMr1OCnK2OFIOkkNtrAJlZO4rFsQBo7Nnnl3R9FLbZ0YoE8s180z82vjOfv48z0BIyF/wD5/7k7U+vPytQGcZD3lb5G4YIxdweXYDcHM8+XYvV5Pr1+/Vrfb/UWnQ265/dqNubNcLtVut/X+++9rNBqlShtPvsdgzIkv91FiUMp8pEIMHxNM4nh+bCfm/GdX/0hrwQMEMIkXMLNWq6W5SyBdq9XUaDTUbrdVr9d1dnaW/HNpvWHNixcvtFqtkn88Ho/TBrVbW1vq9XqS7vc72N/f1/b2dnqN8x0cHGg4HKb7Au8ZX8e6ZrOZ/D1PNmJOuGzy3yKJHRNjYJZXq0WLhPim9dHHnefv7SIiQR+vOfr4vv65j+5rnN8HY+GJPdYNT5Y5YZJbbu+SOadAGxUERqvVSqPRSFJWESllFZ/+Gn6vJ2pi7Ml7l8tlirW96jJiBueXsqpX8MArax3btre31W63tb+/r3a7ndrASOvNaRFfcc9c1+3trXZ3d3Vzc6PJZJLEb6vVKvXtdmxxQpBEuyfF+OfcDO+nKvn29jbjy1K5wLHAJQQVmyq6eN0FYMTrjp9vSnD5/cTxLxQKmZ7oHitEP5sWD/69cR8d8+9EJF43rQ2xGoJnQLzGteX2V7OcjA1GpobyQgK/crmcdrRmx1nvp0V2QsqW2QNcnoWIWV3PdONccjwPlOOu2ZLSbrKeBSsWi0n+D9EJ4Lfb7aSIRVU5HA4z5bKLxULlclmj0Sg5TO12Ox23Wq2mUthms/lALVwqlZIqYDgcZq6dzBOOFMH9crnUZDJJZIhngcjmkEkbj8caj8epzxnPwjNmXDfgAngRpNMvjazXbDZLpcnSGixRmnoA7uSrtM46xWyRlO1dCAkN6Pv7GE8Wt8lkknaP3NraSsQB/c8wxhLjfDs7O+p0OhoOh4m0ztsU5PauGhgEtoAZk8kk4QvzIJKsNOV3lYyXCIGB9MOWHhKrUjbIw8GJpfrM8xhUSkrXwedx0vgd7HcHhuAfJ5XPe+YdRw2MltYVHB7kcg++nrB++MYJXJOTt+5Ug3dgNHjHGMZ2DWC+k6dRacE1c3/SwxI8zB1tT5bxM+Vq3p7FiVhfO1wdweuMrZcX8yxctctY8DdXFNAv0okD1pFut6vLy0tdXV2p0+lkkma55fauGN/vnZ0dNZtNSUpJH4LoqNKKpBqBpydr4nuldRJFUgZ3Y+DneMQxmP+Qpu4P45dzTWDR48ePE6ZyTb1eT9fX14n4lKTRaKTj4+OEd9J9DMA6gt+F33tzc6PhcJg2sQUrP/roI33xxRfqdrup/QAtx6rVaiK1fQNW/EvWtEqlkiq9HMs2BbqRaIxJQg+e40ZdXq2G+drk/zyp6D4+x3EC1IUhJLz8er39lotSnMD3pJnjP887ksIINe7u7jSZTNIzzHsX5vYumldIkvBG7EQ1Lu9x1bjPB/dJmaf4S05WRqI0JjEcA8AaJzAjFrhAyfuO4ltWKpW0uaHzD16ZRHUAbaZcfAHpSsztRO7u7m5qL7Ozs6Pb21sNh8NUWYaIgGQcm3+TmGFMpOyaQiLJ2zcyRo7V4/E4+c2MF5Vt8EQcB6xywhqjss/XuIiBUXnqJDLfIX/WLrjDHDudi+D75UI+x3nffB58jVUR0eCqWCtz++UsJ2OD9ft9lctlrVarTNAIWejZXP+SA26bFDmQbK6a9ADQjUnqk4Vgk9e4BmldmsVxCR5brVY6H1ka79EiKTmWAJF0TyhwD37tlM5zTJrjc3wACGBFms9146Atl2u1prQmJBxMGEtAh/fO5/c7/15dXanX62kwGKQSstlsllHFxawYx/DrdHIC4iSqwVyZ586l9xhz1YIHHd5ewlUGfC5mwvgMz8ZJDhY8B3bODXB7iwK+I4VCIamK6/V67qDm9k7a+fm5FouFjo+PJa0dM1Slk8kkYZ2X6WNgLVhFkMZmAdGpcBKWOREV9I4ZOI0xGPSM+M3NTXLgnID1XlMecEpKgbeTlH5N4ISTBa7k5ByYO/J+f05Wx/5amwiWiE+Oxe5EbkrwuCqD46AcRT3q543KC1//XG3AcyO56Lu7byIF+F5w/b5+cg4fO+8/joPryTInEmKPYsjZwWCg6+tr9fv9tEFavnlXbu+i+eYnxWLxwaafntSJCZNorsrcFLSBE062+e/Seq66f+wkBMfxTfNQuMbEVLvdVrfbTfcI9kAAeN/um5sbTafT1Aaq1Wqp0+loNBrp5uZGzWZTjUYjiTJI/LtfWCjct/pCfEBrM98cjLZkJBwhviEonMR0UsXjCY8pHHudfPXX3vT3TRUePEewlM95UpNretNz9Ovmmjc9R/+eueLMRQ1gNOfge+ExFMfy0mbGP7fc3jXziiZiVe8rT3JsU3WBx5G87qRqxNxI0DrBx9yPMWn8J63nnSfvqAzl593dXTUaDTWbzfQ7ifvIlzSbzcxmiczfGOP6Zzi3Y4KvN1wfoi9PzqNqdR8T8/uCO0GgFltGOJ/Acfxa+Jm9JzZdo+OtJ6A4npPg8bN8RlrzBPi1xEYRg53XgCvw74rjL+uU+/mFQuFBEg4jtiFGc5FYbr+45WRssIuLi0w/J2ntaOAEebmPlG1u7+0JALtNgWZ0dJkgfOE9A+aAxPtcnQTZ6r29UDp41qRSqaTAEGdwsVjvYAgIkWmOSiOCaHaP9Y1xHKg8WwcByzhQFhDHguP44hJB2QPewWCQnGVef9Pujz6mft0us/fz+CIIAb1J0eagtkkN6w4t7+FenQzxa/aWCP6sHfQdnCUl4Oc9tGzAQa1UKpmSvdxye9es2+0mdRCqdTCYLLWUbb2xKdADU3ASUeg7genBrdumRIU7tK7+wrg2HBNKsXAGydjHY3qp0HK5TNl1sMaxyAliLOKGk4sxII4OeSRMPEnox/SfIVW55tgj0a+Nc4BhOIU4bhzHjx0JGI7tx2VNoKrDy6/iWvqme2AsfG3i+l1FS/LRE2so7Lg3iAPK6dglne8qG1weHBzkSbDc3jljLkrZXel5zYOwGBg72Sbpge+DbQrsI25vSp44JqBY4n+IgGKxmPY5cH+IpD0YvFjcb7RFyetwOEzz2PdEmEwmKhaLajQa6vf7Gg6Hmkwm2tnZUb1e12g0SrjD+sPcHwwGmUQO90ul1nJ53yIBTPG9Hlw9FnF8E264/+dBe3y/kzfxOcXnF48fj+HjG9/jz85jpfi+eA08fyc73HxNkJSJb5ycWC6XGaLGewTnltu7ZCR1+Z6SzEcdXygUHmzgHXmGmABxBS0W49FYzclc2eSL4SO7wMv/+f4ycA87Ozup4hbfG/EPYiv4BoRcYDrn4Jo9OSUpEbX4nIyRtxrEH+PeXJnPPbnfzuuemOd33xAsKogjrrDG8Le4lwHjH/3YN8UejqF+ff45f41zsja6GI3YJZr75E7Eeos2x/yI/ZKS38u1bm9va39/P8fdv4LlZGywi4uL1DO1UqkkB25TFsQl5ICIT6pIpvG69FCNFR0pJxb8HJwbcpXSW0CAc7BRAef3DbC8rNfVlNPpVN1uV3t7e8mx5NpZFNjgi/thx1gPVAGEm5ubdB+xT4mDHYQLFslpdyghPyjjd/WZqxQYYwdCV1XgeDtB49kr7i9m7GNw7wAJYYz5sSFa3Pl0MobX/fqdlGZx8YWL6/A2FbPZTP1+P6lcyuWyms1maieRW27vok2nU/V6veTQRZXmarVK6tioHnK1JriMQ0gCAhzwJFsk7iLhGR1dn+PMVYJxV0tGVcOm8+AIbW9vZ1oBxEDVMcdLgF3d4KoBd0D9fLzG/ySkwCUfF1QEfl2sAU7C8nscY64RTGYt8PYPbh7w+ziDna4KgbjwTRd9fYzOM+sR74mJUq+E4Ln4Jhc3NzfJyeWftCYzvM+v93Vvt9upb+XHH3/8C8+H3HL7dZvvWUBiwqsCopoyJnEkZeZVJFaxGFT669HnjQkm5jpJOlqllMvlTLDsPf0IvMGd6XSqwWCQEiqQsZLUaDTSRjOOTdPpVKPRSMViMbNRKqp85v3NzU0iFIbDYTr/zc2NKpVKZm8DCIzFYqFarZbGgPukFNbHLAa3MW7wZ+PPjfc6brvAA+KXZ+gWiRnM1zWeT4x34rH8eL6WxePESg5X43FP3sLMSV+v1OP74vtV5Jbbu2KIkYi7UcUSu+G7ekuOGC+6+Zxz3yb2CfX56XwFn3GlZUyAOeEK/nobAnxHjre9vZ2EEKvVKrVkWS7vq8eoMnDCEtGQpFQ5wHum02niGMBHNt52ktmrHcCN1WqV2Sjd8XVrayuD39JDRT+v+drn2MU5uYdNrQkiqe6fd580+q78nc9wb/4ZMD6KAN2PdlUs1+N7WSyXy9S/l83LIvHv9+/rLRzD9vZ2qmrM7ZeznIwNNhgMUm+T4+PjDBEp6cEk20TO8Tf+eZkik8p7+fFZz1S5UslBxrMXEBcOAN7HlkmF8rVcLiflzmq1Sr2xmFjuWEtKATTHBmBjYE7G3zPcBLIue+c9vqO2qxd8oYiBvQfnUlaZxY7lcWHxzLorKubzuXq9Xhp7V+C5epi/+YIHKKGQgHAGtCCXV6tsr2A+izmgevkACwMLE8eUlAIByBQCEr4jvV5Po9EoLfaoYYvFYk7E5vZOG5nzXq+ndrstaU3m4aA6tjjx5UQihJ07g05gev88xxdpneVmLhPco3QEg1BBosj3EvZI9DFXY5sA6WHZqeOfO11+jbzXVQIxo+/lX5zDk27cH+9n3DiWE46Oe54A4m9s2ugGrrmz52W4/kw8QPesPtjN2uNrXHQ63YFlvHw9c6UyziRKClcxuKpqsVhkyGOedVRKuyO+Wq2SM/vBBx+k6pTccnsXzZWZktTr9ZJvSTmqt5mSsj4uOMHnpSxBJykzn38aUct7/XUSL/V6XfV6Xa1WK20+S3BOOSmtbKbTqWq1mg4PD3V2dqazszN1Op10ryT0uK5er6dWq6WTk5OEA5PJRJVKRZ1OR4vFQr1eL+HZxcWFTk9P05jQ03x3dzf1MCwUCrq8vNTR0VEiCSaTiUajUSJy2RgMvMK3B2PANB9bDFxjvBkvrxbx5xHHl/XR1yTHz0hE8FlPjnmyNKpsnQgAI52s9/M50RTvifFx0icmTCGItra2UkIsJ2Jze1fNyVjfcIl/zBX6ohJr40fFiipiffclmZcRO+IcY36C8S5IgnSl3zWqdHwkrh1xE3Mbvxvlv/uiXA+JLe6HY0Iic4xKpZLIUvxDHx9vCbZc3lceDAaD5K9jTo46v+ECpzhO8R9+INjrfjzEt5OxLlAAZ7nXmMTyqivOv4k34Hvjgi6+M+Ct34uLwPg8nyORKK17f7MGOUnuyT18YpKbW1tbOjw8zFvC/IosJ2ODHR8fazQapV1WDw4OVK/XU0AnrScxmRl3Drz0kQnKhPMJiAMCmHmwGdsT0Izag1OfdB5o8zeIOhwon3xMbo4LEQkJSD9VMlRce71eT31zHYwAVgiT+XyeANlLUwEdsl6MEyrf6XSaCGBXsbkSFOUS58K8TMF3aGQMIFhx2Ai4ozIEchXzYCEqn+fzeepBxj8WFW/0DSDTs4zFyQkbnvtisdBwOMwohfmOzWYzdTqdRICwSN3e3qbxZqOJ3HL7TbKTkxNNp1NNJhM9f/5cR0dHyfGhlxSOBCXv4CEKRA9qIdliMOpkXSwxcmWVk5JgL58bjUZp3noplW/qCJHgGxdyLIwyn8Vike6He/Z1xUkK3s/Gh76+cH9glK8VnuX2oJhroh0LGAce8bsnrBx3cTq5Hp4V9+mtYzxZBtHu98C652QqGXt6jBWLxQcbtvFZL3mDhEW57Cpf1qOYpGONYDy4RvqSczyerxPmtVpNe3t7v5rJkFtuX4Lh/5FYlu6VoqihmGuOWcwfxxVelzaXpW8iW52M89fAnEqlokajoXa7rb29veRzLxaL1AuWHq4Eh5AXJMs9aVWv1zPrBhgzHo+1vb2twWCgWq2mQuF+g8C9vT1dXFwkBWyxeN8S4cWLFzo8PEx40ev1UnsSfLGtrS01m031ej0dHh6q1Wol3KxUKjo6OlKj0dBkMtFwOEyb8MYKCQ+mnQTg75Hk3ETGemIQ/xs/1fHcBRGsVR6nsK76c2JjQk/aOZkbk50xAeoJt/jd8bXA4wF8dnxovgdRsZZbbu+iHR0dJT+R+B9fk7lNcsfLxT2e9fnpvqn7I8wl3/jUhQuxLB9fyDdI9DZfnqDzmBxfC+GVx+yVSiUjnmo2mwlrwD9p7XsjMCOhzVzf2trS5eWlpDWJXK1WMzH/YrHeQNUrbzFwxv1GxAGMpSfZo48cq1vdX41+9nK5zLS0oYduFJL4NZJ48yQY/XOj2AFhFWsFGIlQzr8PMZnGuHI+55Ig16NYwn1fSTo4OEiCmdx+dZaTsRsMJeHZ2ZmWy6WePn36IBvi2emYlfJdRj1rwmsul+d8AJ2X4XBsJgzA5O0GmLAEr0ym8XicwJvgEufV+2wBHpRzMenYyAsnmGPPZrNMaS299bi+7e1tzWYzTSYT1ev1pMLlXvgdB3O5XCanDgISkpGg19ViOMV7e3vqdrvpGAARDiPHpbSMMWLR29THz3fl9WcKWeAZQO7fs/UsSgBuVLh5v1zIX8bUQZXvhC+M4/FYr1+/1mw2U7VaVa1WyxC5jJsranPL7TfJ2Ozk+vo6JRY8UGP+OHnpyklX0XjFQQzUPHkSFa2eSPMSWCeC3Vl27ITQjFltNmfwc7gTxDliCwXegyPlSnvwZlNbFP87+OnBdnQyY/IJnHPFKZ8liHBi1fuduTIjjn1UZUSC2MdDWpeu4ZxLyqgh/L49sQb5gLqE9S/eeyTCXRULhrOTricGOAfOabvdzlWwuf1GWrVaVaPR0M3Njfb29hIZC655aTrz25VCTv5FNaWU3UgUYx6SFHG1ECXn+/v7ajabqbpnPp+r1WppPp/r/Pxcr1+/1mQyUavV0s3NTdowr1AoqFaraTab6fLyMimmSqVSmq+bqtwuLi50cHCgarWarol2Od4Dem9vLynAqEIYj8d6+fKlWq1WwpJms5lUWtVqVYVCQZPJJLX1IrnIxmKsAY7vEQ8d13394HfH3bhO8DdpXQGyqa1aVIlF0sbXR1dlRXKdZ+zrEZ9z0thVWE4WVavV1PfXr4NEK7jt37PccvtNMHwmRE/sUUM8zf/VajUT+zPXfM55RZjPZScH8Yn9Zzf4Ct97ht+pwCT5hOI1xp6oYL2CwuN2cLFSqWi1WiVM9rWF83n/U/anGY1G6Ty8n8oN/H0wBkGXE9fuB0c/UMruCxQr8SLuYhCbPC+uvVAoJJ4En51x8YQbY+h4yDV67O/Xx3l8XebvJAL9MxwbTsn9ZrCW78nd3Z263W4m+cX3Fd4lF3r9+iwnYzcYi32n09FyudTp6WkiSyUlUo/skDs/nrkGRD1glrKllEww34QA1Y70sO+oB9Q4x2RYPDuOohVAwxkkc1SpVLRYLDIEo7RWPpDpkrKBMpkx/8xsNkskIPeMo+Rlqa4641wEvNyrO5ORjCCTiHrX79Gz5h70AzZeToqSCWACfAF3LyN2oAKUOH5UvUEYxQAEYONeuDaeTXRy2cwBsITERq1MgIL6jut05zq33H7TjO/ueDzWcDhUu93O4CkbC3qfaM8Ee6ba52V0vPw1cAkMkdYBJRjjvan5rBO1bJhHcoVzu5IzntOVlZ6x9vYDPv95nzuNnuxx/I4k56ZjOCHsmOGfjepcPutjyBi5qtSfha9z4J/jfBxPxggi1kt5Gev4Xo7nyTjpHte9HNfJZT8npLY7047fONC8vrW1pXa7ndYU3wE4t9x+kwzcQpXkPqj7O47DGPPOiTgnBzwIjf6xk3+OgdVqNfWGRZXO/EM9Sqkkmx763Ibs7Ha76vV6KYEHGeuqTK7Lj0mv8cVikamIu7m50Xg81s7OjiaTSSIT8B0RHzAOs9lMrVYrKfpXq1XyL1HRQkCUy+W0xoBJjtG+drmvSTKQ3/35eFLMiW5JSfXvxGnEdT9uTFhyHY7j0e/cRI463ke/2eMlfHzH+NVqlRRhb9qoN7fcfhOMyqFut6tyuZwSYPgc4A34IGWJP0+wSNk9ATwx43OYY0Uykfez4RZ4xd+9csgrd12EBPcwnU4zPWYhQ92XwmckwU68zT1FoRq+/O7ubqpahV+gKoJjchwXIhAf+DnAzdjOkH/ck4sf8P82YZekTPUW9+GiAcdir/LwZ8Dz5Jq5fnDbfWvHa85H0jCOt78W/W6PRVAXO662Wq10LgQouf16LCdjNxiTgHJxmvGTkYBw8wy1A6NnezB/zTPUvD9mrQmgIeogBH0i0VOF81Li7s6zKzhpA+COFYDgih9IQs++M1kjCLkj5w4mQOdlrFyHB+dkwACZ6LD5cQApHFicc+7HAwgPznd2djSdTjOvFwqFlKXjOrzlQHRGeca830s1AHDPQKF8dZLce6t4yayT664EdCK5UCikzSa8fMH72+aW22+6Ecj2+309fvw4s/hT9kOATqa3WCymbD0YxRzZFPj7a07QOV6D+wT97vAWi8WEuxCE3js1VkPwfldkxQA4qqCcTI1BtTuc0VyR4M6WHy+StTHYj85aJGKd0HQyxD/jpALBs/e7imPux+f50X+R4ARVbDwvn2PN5HdXdvAa1+LOsJe1kWwE311J4MFJXoGQ21fBmFOlUimj7KSSyhNFTrpiMYEeibmIL/66+8I+50kwU8ZKUoaNbrz6aDQaZdp8kRBjYxxwmoCcOR+TcZJSYrxWq6WqM3zF5XKZfMher5d28kYY4EkpznV0dJQJ1ovFYto8DOUUfh5rjOOiq2M3YX0kaqWHG61EgtyfnfdB9M870QnR4X/juXMOr+bwZ8trfh/xdycIXDyBMhD89/Zofr255fabZsSN/X4/0/rQfZnZbJb8R7AjEo7glsfOMS6PSRkpuzkVx6/Vamq328nHjokSV2l6la+fxzf9drEX5+G+UXByfK7X/WL3eyVlYt5isajJZKJms5lJ+vneAJF89LF1X9tx1e8PLoDxiskoxtyrlSNR7W0YojiCnyP2Oq8Qj80aw7P3eIPqNK/IdfNnuOlvkd/x9Ti3L8dyMvYNRubk9vZWo9EoqQZwCvwLHSdbLB31viTxHPTj8D6nDnKU9ZNlcYeF6+E44/FYvV5PxWJRrVYro7YtFArq9/u6vr5OKiCCTSa+T2IyLd6rz3uteibLAU9SZmMpgAlwLZVKGdB2ZSfBtPe2IgNGUMyxdnZ21Gg0MuBSKq173wI+jEFUDt/d3anVaj1QSEWCmOeG44zFHr3eyoHFhsXBARZyAvCEMPZr4HnxHeE6CFQ8i5pbbl81WywWGo/HGo1GqtfrCWco75TWm3vhBErrgLtYLKbdqrFN6h3/G/Mbkg5FLKWlkjIE3WQySe9nA5lNCStXTvmGOTiNXJM7u75OeFaeYzJGkcAFuxzDcVY96PbPOUHM5yNxy8+RyPDgOxK0XlLnDruX23F8sJHXSqVSpv3OdDrVeDxOu+tCFrlyt1AoJHUZ2F2pVNRsNlMSjEDAP+sqipgkpOc5v3svy9xy+6rY3d2d+v2+Dg8PdXNzk/woyFifm9J6k1InYt0cv9zv87/xP3jiCiyIWIjU5TK7OQttX7yigXlZq9V0c3OjbrebqQqLKrIYaBeLxcxeBuPxOLP5rKTki89mM9Xr9dSeBAyhJRbtdlqtlgaDgSQlX1dSJsD1GANM8nXAxz2OMX/ze3B/nFjES2W5b1d9ueHjcw6/pkhq4IO6ko1ju/+8aV1zRVZUWxED4eeuVutWP7nl9lUwYlsEQfg1VFERO3rpvpNmXvYu6YF/KK05DBcGOQnL/zs7O2mTRMQ+xLckvMAFj5M5P35Tt9vVcDhM5C5+F5/l98lkkpn3+KaOA+6bLZfLjYkxb0XmZKzzGY5hEfudAI5js0kUFTcp572x6rVarSaOgWP42LHeuSjAsdIFAfzuY85Y4atSWeE9dD2e8N/BZn+fv4f1lw3Ac/vyLCdj32D1el1/7a/9NUnSYDDQ8fFxUjgxaSHU3CHFEWOCLxaL1PvF+7UwMdkRlp4rEI04WF66RBYLQs7fM5vN1O129fnnn2u5XOr4+DjtdMfknUwm+uyzz3R1daXhcJh2fIbMQEWK8ocSNgCBdgTj8TgTUAOyjEWxWExqMcCOTAsLjAf1ZNG8pQFOL/eHosAdODatou8ZC4cH2gQNOKaUaDWbzQcZfK4XcgBzB5QyaRYAnOlCoZAIBHdcCfyl9QLrbS4YH5xXfvdyOc+SQlKzQOeW21fJqtWqvve970m6b1fQbrdTlcJsNlO73U5OK8Sa7woqZXeKjQGglC3dl9YZaW/rwj+UmJBzW1tbGg6Haedxjsvc9WNShVCtVjUYDDJO4Xw+V7VazZAUzGuwFWfN/76J6IgEIo4oDp3jix8LB5T78h5XOJ6e7Ue9Bfa5o+vqKTdPNjGOKF6djPXrctyEZB2Px5rP52o0Gum8TrzyzL19jaSUlGQ8UFbHkjYnL9zhxjFlV+HYby233H7TbWvrfsOps7MznZycZJIys9ks077AxQIekHtLFccpDwidWPS5DiZUq1VNJpMHSezZbKarqytNp1NVKpWEVf1+PyVddnZ21G6300aQjUZDn3/+ucbjccIwJ2VdfICPxT2Q1KHCy4N3/LbhcKh6va5Hjx5psVikPQwghLn37e1t3dzcaDQaablcqtFopGQTrRF6vZ4Gg0Fm40MfJycEfE2L/ivxhAfuCCqcSOW9cW2JRA7vZfd01lzMBQ5+nE3X52SDr5Gb/O1CoZDOQ7lzbrl9laxer+v3fu/3JEmj0SgRcK6yx3dDYco//FJ8VY91Has8noRHYD5CuJZKJTUajYSrLmTi72ySN51Okx/Ofg6LxX0rmUKhoMFgkIQJzFkqCFw8RNwO9nPf3KdjneOdr0nL5VL9fj/13sZP8/eCKd7WxONvMB8xG/fs9+8CLW+R5YrWarWa/EJ4iclkku7Ryd74Wf8bZCg8CZwCnBICECeLpSym+rPG9+Y9rG9gKtfhcRHvz/3cL99+5ogXCoX/k6T/pqSL1Wr1vb987X8h6X8g6fIv3/Y/W61W/8+//Nv/VNJ/X9JC0v94tVr9v34N1/2l2p/92Z/p+PhYjx49UrPZzASKcTI5eRrVPw50rtZEhQoZ6gpOP4Y7v9La0cJBhAxEYVAoFFKvq1KppIuLC7169Urz+VwvX77U69ev9bf/9t9OQTJ9YaR1b1h+n0wmKQCWsr2rnBQE8FBXkLGiYXmhUEjOqjfZHg6H6V7YAIxx2tQLBiB3xRLjwG6VXOtoNNL+/n66zvl8nshmiBwadkOA4rgD5F4iALCdn5+r0+moWCxqf39fT548yTiabKgFKe/kB4H/YDBI35nd3d20EQw7sTNmKG6ldePt3L66luOu1Ol0dHx8nFpx9Hq9RIqRAPI+oq5c9JYjUlaJ5Wr9aDgz3lYF/PU2KMzN6FwRaOOo3tzcqN1up01lSHzRM8v/+XrgQThzPTqmrCfeLgWs8FIysMuDWt7rJC5ErPdj5G/gEckoH6+bm5vkYPtxGTNvAwD+uZMKBpO1572VSiVTkUFfb67Nr4Hr4Bmw8VckW50cIrDxpCZj7u/3vrVOQuX21bIcc6Wrqyu999572tnZSQkQ5izzxEm8SAZK2eDQk+7+Pl4n8Uyyp9vtZiq6qICSlKojWAO++OKLjAJpsVgkFRc+KPhxc3OjWq32gBwkKeaCCsQG9Xo9gxGcq9FoJBxkA56joyM9ffo0VU50Oh31er3Uc288Hqd2Z+PxOG2a1u/31e12H5ClmG9M42Psaw5/Ywx4D4E32BYTZTG5F4lanj0Jz8FgkPFrWUsjQcy5uR7+d7/a4wcXMXBfrnrL7atrOeZKP/zhD/Wd73xHe3t7mUQ3viLz2MU7rkh1dSbGPIZoxG/hvfh21WpV7XZbjUZDrVYrEZB8Ft8a9f7V1VWK0zk2Ca/lcqlut6tOp6OLiwsNh0Pt7e0l9a8rQRE7uO9KJUZMhktKG3nhu0v3cT0+9c3NTcYnZkPv5XKZkvkkk9jgC+NcXkmB34vvS3yOwMKTklQ8cF+Xl5epiiImupwg5znx3PCx+Q6wPwziE+eVpLWi2H3YuC5IayyFz6ESO44znIRXx+X25dnPQ3//U0n/O0n/5/D6/3a1Wv2v/IVCofBdSf9Y0m9Lek/S/7tQKPzWarX6jU5tfvDBB2nC7O/v6/z8XNLDUnImhH+5cSw9IMTi53FScVD5m5e0EgjjxDSbTXU6nQRQ5XI5KUWbzWYCmcFgoMFgkJzFXq+nH/3oR/re976no6Oj5LDiGPmmWgTMnnmTssSrO9euIgVcpbVjzj36romQJ5THQU7jDHL/qFc3OXeFQiGpzQjMcR7ZCZLr8N4yfm08B8ad6/DNZLgOFjl2GYSM57yu2vMSZRYl3s+Ccnd3p8PDw4xKOirHfPHK7Stt/1Rfc9w9OjpKakzUSIPBIJFtBOaxHMfJy+iceG+u2BpEyiqSnHjzDfwgfVFY+vvBCLBxOp2q1WqpXq8nzJhOp4kYICOPeskJU3CC+4otBZxcdGeX9/M7Kgre7yVQJOHAWh8Xad3X/O7uLl2jE61+rZ6J97YNHIP3e/beMZe/gefcm6slnOB1BTF47v22cMY5rt+7O8Hc82q1SlUO3DPJwrhTbW5fSfun+ppjLr4eihz8Fr73Tpp5gM/8dT/X/WAPSDHmJq2qqBDz9lM7OztqtVpqNBp69OhRUjNJ0uHhoRaLhdrttp4+faqDgwMVi0VdX1/r+vpa29vbarfburu7S5UJbvjnHI/rZCMv1GbgueOvJ7tQ03rVxt3dncbjcfLlvIUCQoV+v69er5dIlxioOzkaxR0xycTYuqIJ7IoEL39z4sOPATZjq9UqqYXBV8bKx839aff7XUDB8fie4duzJ4evS7e3t0mJl9tX1v6pvuaY+9FHH6WYko2l8SvxQ3xDZ+Ys89r9XUmZmJZj8D7HLqpKvdLWyd3xeJwwzP3KGJMuFovUVgZfrN/v6/nz5ynRxLVDcm5aI1gHfK3wMn58RUhVzuniBe7TN/r1+BpuYbVaZTYod//ZBQCMhRO9Xh3N/WKsCYxnFOb5+zw+8TXUnyPXwjnwr/FTi8Xig1Y8fgyOTTvKSqWSKro3GbxMXnX75dvPJGNXq9X/p1AofPBzHu+/Jen/ulqtbiR9VigUfiTpvyHp3/zyl/j2rVKppKb9z549U7fbTUScZ/mZmO4gxX4iPkE94OS9UnYXaz7DZHMnqVwupwASgNnf388EnRCKvV5Pd3d3eu+99zQajZKCoNvtqtlsZlSY8Ro8WPUWCgT6BOlOxnK/nklyUKL8l+OXSqUMaEpKpWI+ZoxVHBd3EBlLiF6O4QAdgwauDRLB79lLF3iWlMwVCgU1m03V6/WN5M8m9YC/zt/oi8j3jEUYAJWUNojIVbFffctxV2lX7N3dXR0cHGi1Wmk0GqlarWZKz1FMRgcO86AxYpG/x/EJbAYvnDyMmA0m+FrghO9oNEqVCxAcODtRjeTlXPGa3GK23f/O8cmaezUDAa871bzGZ9ksyzGS+4rlS9EBxHFHqeBErDu7jJ+Tux7I+z/wmzHy9cCVt5ueiz8bzNcJVxegtvZx5/iVSiXH3a+45Zh7/92/urpSs9lUu91Oftom4tUJ2ohP0k9XNUbMwk+NCRVK5Hd3d1Wr1XRxcZFwEl/28PBQjx49UqvVSthTq9X09OnTlPwqlUoaDAYPMI1NohyzSaq5fxnbFfiasLu7q36/nyoDptNpwsHhcKhGo5GUWtPpVNVqVaPRSMPhMG3yAg452b0pbtg01sQe/t645r3J3G9+0xrI+1DncZ9OgkR8fdN65diNshilFs+dard+v58q6nL7alqOuVKtVtPV1ZUWi4X29/cTAQrm4Od4T1Ap2xYwJrw2+TzuH+EL0otVWifJXYDlVabE/56Ik5TI2NVqpWq1qtlslpJrXs2G/xYxaZPv7GKDmIgiiba1taXxeJzhYbhn3xARDMG3Zf2IWOvXE7kcxoiKtzdhknMHPvZu7jPHJCbPgf/hT/x4vscMf4vnxW9FeEBMUKvVkj8dk2qc02OQ3L48+6s0hvgfFQqF/66kfyfpf7JarbqSTiX9f+09L/7ytQdWKBT+S0n/pXTf1+1dt36/r/39fZXLZdVqtTQhXaW4iWyLhKO0niixvNRVOw5a7shwHiYi2WUyzPv7+wmcFov7Xljj8VjX19darVZ69OiR6vW6rq6uNJ/PdXV1pVarpf39/aQo5bNkXzDu1ye8k5buuEprNZerq6LSjLEi4wUZS3bLM0icx/8eCYnYJkJaA6gvNpAhkRzn2JgDE+DN/ddqNVUqFdXrdVUqlXR83ut9ZLx8DBKE70ixWExE7KtXr/Txxx+n8YbE8WxZDpRfa/uV4S4tMd5lw7EDL+ld5U3ucSycKNxEvvn8jO+LySLmJkE1WBLVnGCFB8DxHMPhMCl4y+VymtdOSOAs0r7AzyOt1Q6sCZFk5NwoszyJBYFMkOtrliuJUemzSQ73x3hQwRDLmLzPLMnB2WyWyE3GddP6yM/cmyeu3BF2ZQQWSdlIsG9ydON1OKlCT9j4fZlMJmq1WjkZ+/W1Xxnm1uv1X/Ol/tWt3++rUCjo6OhI5XI5IzxwIvNNFomASBI6VjnZ4Al3qpm8DyB9DWezmXZ3d/Xs2TP1ej3t7e2p3W6nMtLd3V0dHh7q4OAgqStXq1Xa3NbvoVwup1JcrjHiq5OxTkDzer1e16tXr9LGLRxvZ2dHw+Ew3Zv7exCxJJFiIB6VU5ivB3E8XXnKvzeRt5HU8XPFZJakRNrEeOdNwfsmHHbfOcZPKIY9Ucj45Pa1tK8V5t7c3KRq2EqlkmL6KNCKSkrfWBpc87nH61I2Do2KT8dgjk8llxPDJE+cYyDhVSzeb5yLsGqxWOjq6iqV2jtZ6LgRiWRPBHkC3q8bkdJoNMqMB+fxDXOJ+d2XZ1xpvQVxybXxPxwE2OW8jo9VTEB5vO+JLiei/ViRQ8LfxlyM4H12+U7Ec/MZfGLey5q6qR3Bpu9Obl+e/bJk7P9e0v9S0uov//9fS/rv/SIHWK1W/7Wk/1qS3nvvvXf+G0DD/cvLSx0cHKQsu4MG5KUHvACQk67uPOGMMSkpG5XWDgugjIPq5ZLeK2W1WqXefXx2OByq0+no+vo69a8qlUpqt9va2tpK5Vz1ej3Tl/Tm5kaTySRdD44l18I1R2KZRQMScTqdprKC2WymarWaCawZC0oiaM4trckBV0oAUhE4FotFIiA4nwf1EAaAJuSvO66FQiGRJq6S4Bk5gUHPLMacsiopWyrm6gYPZhyUy+Wy9vb2NJlM9Omnn+rk5CQ9H75XbPLgfX1z+9rZrxR3Hz9+/M7j7tHRkaR7VXij0VC329VoNNJqtUqKJ+l+DtFzyZWyzCGcRncCfZ67ExQVB64a9WPgzJIEwoH27D3BPhn5YrGYNqEaDAaq1+upPAtc20Q84ty5w+jXCB5CiuKI8j762EZlm5MKbNRAHzCSVr4esbmj94dloxtUFPS38g2vHEejSkN6uPFXxHZ3MHmu/O6OqifuIFl5Rq4U9uOzXpRKJdXr9eTozmYz9fv9tIFnjrtfS/uVYu7R0dE7j7n1ej3hVrValZRNQmFvCtycuHUi1slXD7zdVqtV6rMNhpMMqdfr2tvbSz0Knz59qm9/+9tpPtOD9fz8PG2UBVb3er00r2PwjB/oWMTGOE5WEMTTl7xWq2m1WiVfDXwhgC+Xy5lNbPEVUYyxVnhQ70SAj2UcK8bWiQpXSPl4ehzivmhMTHENkVjgdaotOI5/H9w/dqKCc7HmurqXFl4INUjg7e7upj7BOUHwtbSvHeaenp5qtVrp8vJSR0dHmT1W3lQx6wmVmPwCax3XHJf5jB/b/SLibNqtrFar1NYAIQTzfDKZpLWCagZi9RcvXqTrYk5DIMcqMO4HlTyxr1dNcG2LxUK1Wk29Xi/dC37laDTK+HlcJ3jr/EWj0dBwOEwYGcUZjI2/xjF8ncAn9TaEjof42FEw4v13/XlFrHTje+Et1zZhJD6vr8XL5f2+OPj3zks5X5Lbl2+/VHSxWq3O+blQKPwfJP0//vLXl5Ke2Fvf/8vXvhL2+vVrXV1d6R/8g3+g09NTff7555mMfqVSSYQckwOH0ie7q4JcLRo3OXEH2LPcTETeUygUkkqT/kqQD8ViUe12W0dHR9rd3VWv10ufWa1W6nQ6uru7U6vV0snJSVIfUHblitbV6r6EjdIuznF3d5d2PYeEgOyU1iVO/OzZOS+H8MzbaDRSt9uVtFZebW1taW9vT8ViMbP7LYBCA3COVS6XNZ1OJSmT5aMvrjcFp78jhE4ETyfQcdIhfvk74LypQTZksX8HUHFwb8ViUVdXV/qX//Jf6vDwUHt7e4nQuLu7U7PZ1OXlZXo9t6+XfV1xF0ex1Wqp3W5rPB6n3rGSMmQjZCHlOOBvVHZ5EsZVRK4YAMfADvDZFQVeTjsej5Oi1J1DnFCUo+AS2AvGsA5EFTznZQ1B5epqYHcQZ7NZSto4Pvp1cX9gn7dhcJKiVqvp8PBQlUpF5+fniXBwdQGkh2Obk8uMK9UIrClStoUEv0Po+gZe0UnknNyP76oL8UsiD8c+qg8INhhvVNh8X1hHKpWKPvnkEz158kTtdvtX+t3O7d22ryvm9no9TSYT/c2/+TfTXKBk0oP4SNx5UOgEZgz2YsLbPwOByXtIlOAnUfZfLBaTMOLy8lK9Xk+9Xk9ffPGFPvroI0n3c7rb7er6+jrjq5No8hYnER+ktSKU+yRxTjsw1gbGx3GOKrpWq6VisZgECWdnZ5k+qD4W+KX43PinTqCAwdKaKIiqLu4DPHRVGpVivmbwXCKRwDWhYsVYQ6KajmtyoojvjFetlUolVavVtLEZvj8lzbSaQMyR29fHvq6Y2+l01Ol01G63k2gJHxRzH8crOr1ilXkc1Y/MS/888aqkBzhIb1GqPpfLZUqYgJvgKH1n8U251rOzMzWbzcy1QcZ63Iv/7cQkxwJr2FixUqloOByq1WolAZlX2kI0cs8keqQsRuEft1qtRCiDxV7CzxpEP1+ul/XQN3jk/lww5wlIvyfey327UMHjFN+XIhKlxACRd+L1GEtMp9PkE+P3OwE9Ho9VqVRSP93cvjz7pcjYQqHweLVavf7LX//bkv7TX/78f5f0zwqFwv9G9w22P5b0//srX+U7YkywP/qjP9L3v/99HR8fazgcpknpzh3l/q50dWcEIPAslsvoUWh6WRAg5sBGSQCqHsjHwWCQrqter+v73/++Vqv7RvydTkeDwUCTySTtmtvv93V4eJgy/pPJJAW5BL70qN3Z2cmUGUGEeEbcS2K5z9iHy0scANO7uztNJhP1ej2Nx+NEOvKe6XSqZ8+eJVKE8SEYhxApFosPNvfxDcFoXA7QskELfwcQIUucsOBY3m+MdhHb29saj8cPyor5WcqWogGeo9FINzc3evbsWSofR+FXLpdTKwQU0hDZuX197OuKu+DH+fm5Hj16lKoUXEkK3ngSyp0RzLPIThRGtSSYApbzfif3pHUJVMR5gnM2oMFZ5brJ7IPn7mQ5LuDIOY7xNzDXHWJJmWqCqGDi+N6eAEzmtTcF+OA+mMlYcMxIyvimZyTkSCw5Eer3Q9DuVSZ+TMdSXx99Mxjeg5PqfQ5dJUEp29bWlprNZkbxS5KwUqlosVhoMBjoxYsX6vV6+uCDD37GNza3r4p9XTEXX/f8/Fztdlu9Xi+DkbGiy7HTEzVRJenJLwJET+5MJpNEwLXb7VT2Kt2TFaPRKJGxg8FAktIu3/RafPLkSSJAnz9/rqurK/X7fUnK+NK0G4uEJQE5/Wi9r6JXvIFB29vbOjg4UKVSSW1NxuNx2lAMgQBVapPJJKNcI3nEeuFKMDZ09SoGTyY61m1vb2cqMFxd5aovD/xdgRvJGMYLvPR1k7UnPnfiGdYsJ+A9acd3gmfJGupEwHw+T2IEeuvm9tW3ryvmtttt1et1ffLJJ/r2t7+dmXfMUydS8ZWkbGm6t6ZyxSXzmzlI7M38k9a4z/x10lRSqn4aDoeJiETNTlXu9fV1qqqdTCZ6/vy5Dg4OJN239mN+OzG5KaHDtePb0QeWdYffeT9VwhyHcaLygfif80LkFgqFtO40m81Mop8xxScl/uZvTs4uFvdtISuVSvIboyrVK3glpc1zufZYUe1CBBefOCnvfrLjNLxFuVxOohSvQHYfmvXXOSZPvuX267efScYWCoX/i6T/QtJhoVB4Iel/Lum/KBQKf033ZQSfS/onkrRarf60UCj83yT9Z0lzSf/D1W/4TofR7u7u9Mknn6QeVWzi5Bl2d0RdHcoE473SujeWpJR5oveVqzKZjA4oOzs7yfnCsQLoOB9ltYAt2WhXfLVarYxqiV3CHZB9wjrgAXKAKM6bK7h8R15XoHE+gnLvWejHdbXS9fW1Hj9+rGq1+uAanRz19g4A+Pb2dtpABzUxTb0hUiFgPDu1WCzSLumuroCkcaKWBXI8Hj9QQXtWkrEslUrq9/upr9nx8bEeP36cMnd83ks3uMfcvrqW427WVqv7zbt2dnaSep5sN5ujOEkKdoBrKABwQpjPrjjiPI7TzDOwzBXxzE8cPvCDv4Nb3rpAUsrkc91YDJo9246T5kE4zps7bgS0ruaKqlhP8EUcYV1pNBoJl9k0Z7FYpASX431UazBuft/e1kFaq1Idwz0x6Ykr/9k3NHAiAeWyb4wGLnsg4+0kSGKiYCOYQC3MegHZTXIyt6+m5ZibtdvbW/3Jn/yJ/uAP/iAliOnR57girUkADzz9Zydoeb+fh7ZW+Ir4bo1GQ/V6PSmjSJyB41RR0Usc5Rbka7fbTcQB6wF+8s7OTipp9WQVPh9KKQ+QwdYoOnj69Gmq1lgs7jdtBJvZnHU4HGo4HEpakw7gUMQqCA/203BSm7GXlMFc/sb/YC7PAR8a/5bkGp9hLfPAn2PHNYn4BX+X9cjXhZjoZFxdnezVdOVyORE7rryLCdXcvjqWY+7a8DueP3+uw8PDRAy6Ut59HuZp3DuFecX82zR38ZnG43ESJblP6ckTFzfg37rfh1rVsZJ/+Nnj8ViTySRt9h2TPp7I9wQ9/ICrT8Ewknbcr8fHPhZOinI/khIRTaswv2YEXZ5wisToJsGHxwZOMm8SjuEn47NG8YMLMhzz4VSiHx5FHax5iD5ms5lGo1GKPzgnx8c3j2tAbl+O/UwydrVa/Xc2vPx//Cnv/68k/Vd/lYt61w3APD09TQqbOBHcmbm5uXmwe7ZPclf/ADS+g7SU7eWE07O9vZ2cY47r5fHu6NHH79GjR4nQAFzoz+VOlmfW3fHyfwAx18z9ugLYA+C4OLjDxbgCXt4b1Z222Wymy8tLnZycpPErlUq6vb1NStJ6vZ5RWzhxwn145oefXe2AAXL+vDwThjPqKj2uk/uAbPexk9bET7fb1Ww2S2oNnGRIWP655U7qV9ty3H1opVIplXGRRAHzwBl32NyhgIhFtb5cLjO9r3AwmcOx/NIdE/7mCZrJZJIy0I5xVEvErD1KUU8egZdRZerOnyu5XLmA01osFlOC0J24+HsM5n1dIvFDD1mcOO4b0pKxcLWF/4v9c9059gSWO8tekhud3Ej2+rU7aev35sfw9cb72vrmNAQVUcEQ8Te3r57lmJs1kh+dTkenp6cpeQ0RCga5PwR+8XknD9yvcpLWCYRGo5HxpcFJKrjwM7e2tlStVhOuTyYTDQaDRMjSI7bX62XajzhZAPnhWCtlN+fyYLpYLCbs84B5OBym9jmSUqucra0tTadTjUajVLHlhLWPEbjsCrebm5tUHRbXKfdlwV+uMVY8xIRjVGh5nOE/+3Pydji+vhYK696vEKp+Lr9Pad17OK5p4O/u7m6KTeIxcvvqWY65WSsWi6rX63rx4oU++OCDpBxnQywn0TzhFYk7JycdKzxJDaHJho2SMvEmcTIYRTwN1hPX8/tisUgVYPhUjUYjbWzo/h736nPccReMczIWYw2CjHW/P+IquMO9ePWxk6UkgkjUuUjM8ZpjY5GMdfLXhQX4j5GE5vjEDRwrCjhcweqf595ivIDh01L96xu+8z5X5PrY5fblWr4jxS9oxWJRe3t7Oj8/T2VObGxVq9UyATeTCyl8zFoDIgAer9HX1ANnMu4Owq7G5XdKdSGJp9NpOsdkMkmK3sPDQ9VqNQ2HQ+3v72ea83vJqxO07uQxYQlqvRk/6lpJiQimdJZWB06aYICY76JLFsdB7Pnz5yqVSmo2m5kgnzKCarWa1Gru7Pk5GCfArVQqpTIxnFSurVgsJtBnHDYpe+mls7u7m9nNkQDD1VwsjLPZTOfn59rZ2UmZUHrjoO6j7IHPoDrjenLL7atuxeJ979UXL16keeEKJXDK+yu5I4XSCTUkx3Rn1p0cJzdJjnAuad37qlS6bxNDuXtM2njrBOa0O7uSMg6tlCX+IoHgwbPjAeSnq/89cH+TA8x9RrzjXmhFgzLNr4tEFGuSBwTcM+PkTjLrlgf+rGMcm9dwQMF4J0jAUwhhfx/HcSffvxNOIHj7HP9uRdWyJyZzy+2rbltbWzo6OtLr16/TfgKU0DuexABz01xEIelKTXwx5nWhUFC9Xk9+rLROoKAGmk6nCeOYx/P5XIPBIBGvlMeWy+WkfPXrcuKT5FNMVoFX+JBgKgk3EoHz+VyvX7/W/v5++oz7cv1+PyW1PAD2sUIogOLXq9DYKBjlViS9GXcnC6JYwHGZe3QRxKbAm+cbE2IxjnExwmq1SiIKx9uYSJPWsURsAeYKLV/3fO3JLbevqm1vb+ujjz7SH/3RH6lcLqtaraYesv1+P7PHgIuEfG7jr/k8Yp763gf83uv1UpsTcMUFDmAI1WXgLkku4uPVaqW9vT3V6/WES81mU9vb26miwIVbMenjnIaLp1zJyXXQCoZrct88+ppcP+/z+7u7u0tkdL1el6RUXRYFHl6d5upRXy+8Ig0sQ1QVe7+6H+5txXi2XAvvJ5bwRFgk2zmer7vlcjmNoZPoxC88e+eAcvvyLSdjf0lbre53w242mzo8PEylSJByrr6klMCJVbL6OCgop5bL+76ngClOGE6hN7P3XrGA3Hg8VqPRULFY1HQ61c3NjZrNZiKOKcuEsHzy5EkiG1arVeoji8UWBNw7IEA225VdAD8BOUDthIMH1bxfWvcNhMj2ALhUKqlWq+n6+lrPnz/XRx99lNnkBaD1a6Uvju8mjkrD+1Btb2+n7CPPz8uvuAYPMAhOuG6I5sVikcoyIKVdhcdzLZVKmf46PFMWAhTMqLe4Tu8Lk1tuXyfb2rrfXJDNvLa3tzWZTFIiBYcPctKDSldhEgB6xYGTeJ4198Qa2MGcdDwAv1HtSkpYLmUVtWAFGOPZeieGHWu5NhyoiLkeqHuSCXPn0ZUD0ZHHuFf6fKGkcGcNZ51xZL2LLWScEAW7XAmL2o7xdiz3Z+fXG51Ux/pisZjpu+4JT+8V5s+b93MsCBfGNi/byu3raNPpVBcXF3r8+LEODw+1tbWl169fJ79OyvqJP01JxN+ldRDKe6gOQplPsHh3d6fRaJQq0PCDIVrv7u40GAw0Go3SZ/r9fiIePHkOSeuCAfDDcQy/jwRavV5Xv99PG0zRhgofjntB/FCpVFJ/PggD34iM+yfZh4IJjIH4Xa3ulbeRkOXzPp5SVnnqPrR/zsUH+MR+DIQNmMcdHsPwnNlwC7K8Xq9nkmtcI8/KK0qc0L29vdVkMsmQSTz/3HL7OlmxWNTr1/dtc+/u7nRycvKgzZ6r1SENXWhAXO+YBk/APIxiKOLtmLznffP5XDs7O5rNZmmTbzY1LRaLiWug1zdzvdFopJjXMcSFAtLDDcrA8Ol0mvw4qn1ZD/DzfTMsb/sC9kjrHtW+f0Ov10utbPB1qWjjOji3++KsEZviA7DRKxYkpbgenCZm8DGX1iSsJ9x4PqXS/aZjd3d3aX1xgZ6kzIbpfIdo6cC5KpVKatHFdeQVYG/PcjL2l7RGo5GcpP39fR0dHSVwimVCZPU9APbed7H3H04gvVu9FyoAxURHhUn/l8FgoMFgoE6nkybf3d2der1euqbBYKDb21vt7u6msnjp3um+vr5OQC6tdx1kMxQPUDmetyXwzAr36SQw1zoYDDSbzTJjwn3jjLGBCucBcPf39zUej3V9fa2trS212+20ILFwQHDzPzuMUz7m4MX9snsrzb09U4TDLK2d2XK5rC+++ELz+Txt/sJmaCxaOJP1ej2jhgaUR6NRKmujH8/29nZafNidncweJR959iq3r6OdnJzo8vIy9XTa29vTarVKiSkcVOYx5UckNgaDQXIswdBaraZqtZqcOs9a0zcQxwh8InkDru/v76vRaCTM4XWCbLCNoFNSalOAefDqGCs97CMFznlQS/9xHD031iVw3YmKmGGXlLDWlWt7e3spWUZyESPZxbV58i2Ws3Gvfi84xjjC3Bvn9nXFlQ+YO5sck7W3WCxmKgm4JgKHarWayO3r6+uM0svVDRDslOLmltvXwSqVikajkYbDocrlsh4/fpwUm66A9WROxBRPaEvrIN8D2+VyqX6/r0ajofl8rsvLS41GIzWbzaS4QvTQ7XYTzr148UKvXr3S7e2tms1mKuv1oFxaJ8R9o0MwFv/VKxBI9NfrdZXLZQ2HQz1+/DiVfbIW7O/vq1AopE1mIZNfvnyZcNZJ4ag2RaHl7V9Qau3s7KjT6SShh+/U7YktLx929Za3feG4rBPEL27ui3N9fC5Wkvl9+ea40Tx55mSMV4pJ6yo61grau+VkbG5fJysUCvrBD36gTz/9VL1eLyWoms2misVi6jstrXuRMkd8PrG3gvtjThoyd6V1ewR6aYPrcVNy+IDt7W01Gg1J6wTQ3t5eIkN3dnZ0fHycSv9Jlm1tbaXqB68UAF+cL+C+4Az8d/fLpTU/wDGomHBfvtlsJv91NpslgVu73dZsNkubnj9+/DiRr4vFfUWxr3OeQOT8LvSC1+C64GtIauH3RzGA+65wFF6hxXEWi/tq7IijrpblmXtrIa9oQXDGMZy8deFDbl+e5WTsX8G2trY0HA41n891eHiYlDVeeuOKI3deUQFAGEA+4rBAHnggybE29R9xhRQKz+VyqV6vl8rMnj17loDn5uZG5XJZvV5Pk8lErVYrZcQHg4FarVam16lncBwkcKABacAnKmO5d3pneRk/IOaOPKUEBOYcw53ZTqeTIRhqtVpyqL0MwYGFRck3++H5bG9v6+rqSsvl/UYNgKZvBsTzlfSg3w5jT3YfIpn7gjBicWo2m+p2u4kUYIwha/r9vi4uLrRcLtM9EmxQDub9hnPL7etg+/v7Gg6HGo/HWq1WKVnlCaKbm5uUkWe+0OpjOp0m5yWqUJ1YAHu8/7Sr9cFrx3AcUS8J83InaZ319lJXLytypZLfl7Qu3fQgm3tAce/47MfBeXVy1FVsfo2cS1r3r6pUKpnMOiVzELM+Pu5ou7qCc0SSNbZVwDn3JB+fc0Udn/XN3EiMcW5/ZiQtCT68NztrFRUNfIdqtZq2t7dTu535fK4PPvjgV/mVzi23d9pGo5HOzs60XC71/vvvp9ZLUaXJz07Mev9PTyK7T4wx32mLQpn/o0ePVK/XdXNzo+FwqOvr6xRwv3jxIuFnt9vN4KOTvsxx6WErFNYG8AdlPaTD1tZW8jfZdZsKCDbcJRlUKNyX8aLSB4/93qPP634puIggACUveAuORyIV39uVWCT18R89IQZmOgkN+eLXyLmoPkH44FUKvtZsui/wn7YwrtSDwEGg4qW/HLdSqSQlc265fdWtUCjo6dOn6nQ66vf7qlQqajQaqtVqSYXufACxNr5OxOCYuHfhlFeLSmvSzxXs0tpvJbk1nU4zBOazZ89SOT4iKdp4SeuNrmPFkbTGLjAdvEZNH/+RGIdgBP8Zh1g96u1ToirVMX+xWKjf72tvby+R35HLiSSlYyAxA1W3cd1zURkCNydS+Qx+Z7/fz+A5uMjnfONaxtnXu0ajkWnz4Ak6BBqQxC6uiCR/br9+y5mcv4LxpUc6zxefLLWrl6RsltknNQDpZfv+jwnIsaLqCCCgrMkDy9FolMhSV2RK96B7dXWl/f39DIHpDlvMuPt18neCZmld4gQh7H1SHDg3OfGuZPLyKS8P5T4hmguFQuqr42PpwTuEirQuJXYAdMLF+8q4YtkVZ5TF9Xo9vXz5MoE298NOwE7yohKOZXnD4TCV/nFeFhyI6+VyqXa7nXq/MJa0qPC2Ernl9lU3b/OC47YJpyDpUCuxOQi9rlxB7+VG7vi4wwhGerLLA1CwjiQSASvEJIE1AToO5SYVbHTMnFRAAexkMq+Bx+7AYTjS3p+P1+Pa5Pfl9051A+QAOOfOrzvs7vyCbRzXiV1fR0jsSWtyxh1gyADUsJwLIjYmq/w8lLxxP07aOMnMuhq/S/1+X/1+X+PxWN/97ncfECK55fZVtMXivvVSp9NRrVbT/v5+JijGJ9mURPLkEu/Fl3VlPu+BMPWEOdVJe3t7iYzodrtpR2wSYIPBIP1eLpczBB4BJ1gCNpN4d8yRlNm3YXd3NxHQ0+k0+Zu0bRmPx2ldkJRpceU47HjKWMSkmqvSODfqMFRp/pk4rlS9UWnlVQV+TpJTTo6Cxf4M5/N5Uo55P0jMieVY3eGJNBKVPHdJiYD1mIO+wIy/tPb7IXJyy+2rbjs7O2q1WqkKtNFo6PT0VLu7uw98FMgzfBxwICbko3ISP5C4lfdJ61iZuNx9ZHwnn9c7Ozva39+XtK6KZeNX5rn7gK6Yl5SEU94ixQlY8Nr3oPFxAL+cR8F8QzL8Ra+CYBwhuxGo0QJyOBwmstPNx9IFHC6q8vehOsZf92peF6E1m81EdheLxVSNxRhAWntSzHkL1K7lclmdTiczRmAvaxliCgjiKGzJBV9fjuWj/Fc0HE2Url4q61nmTcEujhkTxUuMnDCMain+DunKhOGzTPjVapU2raFkARIPqft4PE79aJiMXDvnBMT856g0BZS9TMAdLK7Vs3Z8PioGWFggQuN40/+WnXO3trZUq9UyAbWXY5RKpZSpwvF0ghcww5nk+nHSWXR80Xj+/Lmurq70+vVrlUqlpJSIxLk7uRBHvD4YDDLlwGzcRtbLx6JaraayOWmdAaT9Qm65fZ2MeUnWl15aUjaIROFIUsT7/UHS4dA55klvVnvFxJm0Ln2POA8e4ux46xInRMF5xzt3pJn3vA+85xiuMnOy1q/fMRp8iYkpzuNKLpxx7hOHmsCd3Wd9PDgGjrSkhNvSwz6wKFm5RifCGRPukecK+UGgTj9HiBUfayehWTN8fZOUIc15jSCD0rPZbKbr62udn5/r29/+dmYdyS23r7LN53ONRiNdXFyoXC6rXq9n/MEYZLv/IikzV1x1iTkZe3t7mxJXOzs7iYzc3t7W0dGRdnd3ValUNBgMJN3jCS2dDg8PtVqtVK/X1el0EtaMx+MHvpIrhPgbBDOvs0ZUKpUM2eGCCEpnvRWKYzvjwu+bXsN4Dbzj/iFF4waQfB6cJNkIgRKfC3iOT+7VXv48WBtubm7U7XZTCzN6P7rQhP+99Y4rZ736g9cQf7haGVKoUCgkf5fPUT2YW25fF0Oxf319rbOzMzUajeRHeik+Pp/7VZ4sZ85v8tMwL6fHt/Q4VFJG+EUJvLd+qVarSfBwcXGR8NnJPk8O4d/xP/4ivxOLuxLUf/d9IcDjTZwBqtiIby7OkpR8WTiTcrmsVqulUqmUNojkmE6wgvfe2jBWh0lKf3c/nvtnnCFjLy4uHiTR6MvtVXeS0rrjgg0IXAhXvw5pTXT7PXhCzUn53H79lo/yr8AgBofDod5///0MqQkB6aVaDgiQd5FE9J58OIUADWDrDbhdRdpoNNKklu4n097eXlL3QNKOx2PVarWkzGQC7+7upgbRTG7PonEN7qzhlHvgS78nJ0fIbLmT5kSoZ5pw5D0rh0oVZ7DT6Wg0GqnRaOjk5CRzzSwkZIcYU5qLs2hA2OLg+wJAiRqbbJVKJY3HY/3oRz/S2dmZ7u7udHp6mu4PYOM5u0KC13muFxcXaQdHJwEYO0jYQqGQes6yyQMLRu6c5vZ1NfCKUh5X4TDH6DEIhlSr1RTYg7OuAHCHjZ89KUQwGjc0wXkE4zwYJ4B2x4l5G8lLJ0dZD3hfVBL453jffD5PLRM8+PZ1w9WonAu8iUomsAtsQm3h90Hyzx1HdtBlExs2JiRhFisXnOzFsYybGNCGYTQapZ7j29vbScFAWS/v85Ja1h2wnWoGxo1nRh91d6JJ5rF2UrWQW25fN7u5udHZ2Zmm06m+9a1vpddQbYInjpe+QZaUTVRFjMO3GY1G2tnZUbVaValUUqfTSUmWZrOpo6MjHRwcaDAY6OLiQq9fv9ZgMFCj0dBHH32kwWCg4+Nj/eQnP0n9AReLRUrao76vVqsp2QLJKCn5elRCTCaThPNxd+pCofAgOYQP6uXE7tfGv4Hp+MZOYuP7RjzG/NiSMomnVquVMMxLbRFtDIfDhLNRGQz5MRwO9erVq1T952uHJ++8soRzcU30eER0sFgsUgKtUCik9RC/ne9GVNfmltvXzcrlsk5OTvTFF1/os88+09OnTxMGOLnKPHcy1pM2Hsu7uTiMti0cF4ECn3Hc3NraUqVSSZsigom0JoCIJXnkLUpItvn8dsUn1z+ZTNImVcS8o9Eok/Ryv9N9S9YTsNIVsZvEUk7msuY0m00dHBxob28vJeP92JLS+kFCj8TgJoMTcb/XKyjgIXZ3d9Xr9dRsNlMlHffOOsP144f7JuXFYjFVanuS0EUOvv44MRwrOcDo3H69lpOxv0Lb2trS+fl5UmABAjSA9i80Ex5wcqcUBQABPJPP+4vM5/NEzHkmCNBj0xhKuj788ENdX19rZ2dH0+lUOzs7Oj09VbvdVqVS0dnZmSSlPi+AsituB4NBJrjnvF566gvBarVKO976YuE9ZWOphSu6WHBubm5UrVYlrXt8NRoNPXnyRKVSSS9fvtS/+lf/SsfHxzo4OEjXB+jO5/NECEAekKkDhCEPGo1GWhS63a5KpZIODg50eHioWq2mL774Qn/4h3+of/fv/p16vZ5+//d/X81mM90X5C/3HMunpfvgpd/v6+XLl/rBD36QUajhpLJJBA4t5QoOlFH9lltuXzcrFApqt9vq9XoJc5nzBLzj8TiRdq1WS8PhMLMTuDsmKGnBBYJMz7jjRHqJJeSlZ5E9eUUizJXtOG4etLrSyANyVxV5YIoT5aoDxsUDdJwwT+hJ60DfcaRYLCbHmiQSx2ctgoBgMwnWMXf0SW5tb2+nHWvZgNA3g4Qo9167TnYsl0s1m03t7++r2WzqP/2n/5TIk1arlRxW7ptnhoKWY00mE41Go1TJwPVCRtPjm3F23K7Vamq326l0jEqL3HL7utlyuVS329WPf/xjffTRR2o0GhoMBinp4e/Dt3PlTlThO7YVCgWVy+W02Yt0r7aiXRPJ8Vqtpvfee0/f/OY3dXd3pz/8wz/Uj3/849S2YDweJ/zZ2dlJytbV6n7fAse/mPTf2dlJlU6UreIz4+N5iSgVTR7Mc4+xnBgxgZOVUa0EHpEgI5GFv4d/zloH5i6Xy7RhJRVxXu7PNUtKMQptFlwVx7p3e3urXq+nH//4x7q4uNCzZ8/UbDYzrQZ4Rh67eLKO62LzWTAXbK5UKklhTXyD7x73icirEHL7ulqpVNKHH36oH/7wh6ldC+IefDLmoiv5XYUZSVj3ccAGfCcIWchcNlYFKxuNhiaTSfIb2+122s8EH89bDvI5rzaAmwALUXB6he1oNEoYh8+Ib04CTFLGr46K3lKppNFolLCTf56Y4v9+v69qtZrGA7/1+9//vk5OTtIm6aPRSJISfjmfg/DNuRs3bzNRr9eTsGO1ut8EjfMcHR2lvsEXFxcajUapxRa+PsfBR+dZo1C+uLhIZKwT0F5ZB8lNgo7x9Cre3H79lpOxv2JbrVY6Pz/X0dGRjo+PVSwW9fLly0wGQlq3JiCTJK3LkhwwcMAAIGldkvXixQu9//77md5KgJYTjoDho0ePEsDiVAKa5XI5ZVPq9bpWq1UKcHHSvKzIFa2QvrFMl91QuT+ICYCQLJA7cChLOa4TAjhn3suVDcc+++wz/fN//s/1j//xP1a73U5y/kqlooODA718+VIvX77U559/rn6/r+9973vpWATqXBuL2N3dnc7Pz9XtdjWfz/Xq1Sv98Ic/1F/8xV9oNpup1WrpyZMnarfbCcQgOhh/Hw9JycHtdDp68uSJ6vV6UlnhOLN5havCIPZ9QaLnjCu5csvt62j01lqtVmkzPCnbzgMHhd1ccZJQ9qPcBBNcLRRVq/TOQ3HFuZi/zPvt7e2Ep5JSwAk+SJt3ZfXse3SmPbHlKiocadYMT5jhXMdjcN2eAOPaXJGKswrxy7FRvnKvBM6z2UxnZ2fa39/X8fGxTk5O9Nlnn+n6+jptxgI57WsUJESxWNRkMlGv19NisVC1WtXe3p729vY0n8/VbDZTAIATSlKN8R+Px5ke2ziljUYj86ydUPD79SCFtaJSqaSNOnPL7etsV1dX2t7e1rNnz/T06VOVy2VdXFxkNjwBt3y+gG0ot+LrJNHYvGt3d1fVajWDF/P5XK1WS71eT///9t4sNrI8ve48l0swdgaDTDI3VmZW1tZdXaitu1FoWS0JA2msloD2vAw8D2NhYEAvGsAG/KIZv8zjvIwHMDBjwAMZkgfGGAPYA+vBgEaLhZFa7W51t1Rd1dndteXOfYl9Ixl3Hsjz57k3mb1UZQaTjPMDAiRjuXHvDcZ3v//5tkKhkGiBtbm5iVarFXzacrmMRqMRsmwBBIGB5bb6GPtOMwijgqzaAg28tdvtIDBq1pIGlNLHzv3Q6wq3T9tfLBaDsMLzEkUROp1Owg6paEkhli1a6Juq/04bSWGYQghF5e3tbTSbzSBGzM/P49KlS6hUKsHv1ApAwn3QzGfguN2LXn9U/NDAJ/eHA9C0F7j2YTRm3Lh582bwiS5cuBB8SwY46APpzBZm/6dL2zUjlrYz3U8VQCIpS1/DbbASiX5zq9XCYDDA7OxsEDY5SJBZnnt7e2g2m4/44fTTmFDBY9CWDLQtFCK5H7Sj6vPSl6Qd5OuB4yARK+zYBoaaDJM7JiYmQqXF5OQkNjc3w75TwKZ/vbe3h3w+j2q1GgY7qu2nnsGkg06ng4WFBczNzWF+fh7lchkHBwdYWVnBq6++GgTYg4OD0LKCyQq8rvLawPPLVl1ra2thP1VX0ooDftZakUa7zm2a0WAx9ilAwW1iYgKXLl3C/Pw8Njc3E18KdViZzarlrmpQ9DEuKPn7gwcPMDc3h0KhEJwuGiZmGczOziKOY5TL5bBwZo8QOk+MapXL5cRiVLNxdchVOlrFyApwnL2l2WV0vmhEVFDQ9HjNJI2iKES++DzeH0URCoUC5ufn0el0sLu7iwcPHuBP/uRP8OUvfxk3btwIGau3b9/G6uoq1tfXQ1ZEJpPBwsJCyE47ODjA3NxcyCxg6V2v18OdO3dw7969YNyYATc7OxuEWIoJmmnLCCWNP7NzGRGrVqshuqiRR+C4eTqNZCaTCc3Qee7pQF+5cmWk/9/GPGuoYKnBKDp1XExPT0+HXlccREU7p33z6GQCx31SuT0gWX6brhZIO8bsNcgeTQykpDNc+VPfR48tXRLKfVQxU7Np6aSr86nZEem2DrS9WhWh5yPtyPPaROeQdlGdcva0XlhYQBRFWF9fR6lUCgElZmjxfbgfdILpEGezWTQaDWxtbYXsN+0zyyCeZgwwI5ZON3CYZcfsLp5vnicVnZlJQBvOzGYuctLZDsaMG8PhEFtbW8EeVSqVUEp5Upao+rQqigLJ1l1chHPxyoBPekgXB7myNyz93e3t7SBIFAoFLC0thd6xOkFae8VSNOB+sKUB/UOKpxQUuY/cz1arlRiqomgQTH9qcgUX2Wqrtd8235d2jcGsXC6HmZmZRGYqRQXguN+fJjBon0lWcdGGDwYDtNvtYLvpe2r1gV4fNaCoAg3PD3BcrZeG1zBWGfAz0SGRbDHR7XZDdZwx44q2TJqYmMDFixdDMAM4rrjlel2z6Ck4qoCpmbD0TblmB5IDnAqFQgh887UUYemPMfCtgR0g2X+b/rZWhPJGny3tc1HUTLcy4HFwX9PJT5rUwL9pb/k7K90ABN+V55E2juv4arUarmtcu+fzeVy4cCFcp3Z3d7G8vBw+J22xQFtXKBQQx3HiOe12O/QGZ1bsrVu3wmev50mvn1wf0P/l2kbXN0CylSV/V5utmdDAcYa1k71Gg8XYp4CWQuVyOVSr1RDp5RdDnT7NcEr3LtFMHX451Fmt1+tBmGWvERUR2GOLDhH3aTgchl6LAIJTp6WmNCI03lxAayYYHT86f2knM/23Hrsugin0ph1Xzb6l+MES5KmpKZRKJVy+fDmclx/+8Ic4ODjAzs4O5ufnMTk5iffeew/37t0LQurOzg7ef//9sCingbp27VoQSGu1GhqNBrrdLtbW1vDw4UPUarXQ34XCAUVUiiUareMx0BAz4sfXULDl62kkVcClIEvRhX1u6TDzHNtYmnGH5ai6iOUil32fWbaqZf2PK5dVsZXb0ps6NWozNZjErB7aftpbRqPT26TAyO+zlgpp2ZVWTqTFWN0fRQVeLux1/7nfup/ch3R1Bo9fHXA6khMTE6GvV6/XC9cfXqeA42xcOuR8fw1AqXjC8rDNzU10Oh1cvnw5iCLpbFYVFrhNCrtaBcLj1gCXniveTyGf/cvcp9uYQ/r9Pra3twEAzz//PObn58N3SbOC0gEmtYPqI55kh5gtSX+ULQxoU5gdRpvKYI22PqGtYeCbi1dWRLC0lLai2WyG7VGs1IUwRU364gzSqCjJ49Sg2ElCNNG1AG/am5yCCvenXq+HfaD9a7fb4f014KjVVRRKtLUC34sJCLrgp+itQUHNdE4L6rTDPF6uR3htJOlrJu2xljpzzUG/Op2Ja8y4wWrKra0tTE1N4fLly2FNT0GNWoJmmGrbAH5n9TuqwSW1IY1GI/zNbHzaA67L6UuzVyrX9ZrxDhy3EVBRmDaTtlsFWQZjeC3hY9yWQvuklV60o7RZaotVX+Ex00dlmwRud2dnB6VSCZVKJfThbjQaoSo4k8kk1uQ3b94MFRs8lk6nE9q/UDjVdoRMXOBwTK0I0Gq0k9b7PB4K1ZqIwM9eX5NO9mBChGpAPAfz8/Of9V/W/Az4yvYUYFRqMBhgfX0dlUolDGrSZsp8rhoMOo40oPwCqSCp0XIuUjudDjKZTCirKhQKQThkb1k6YxRlaRy4QFUxmCIGgEccUuA4wkRDw6wCLblKG0Q1nuqApx11NZY0sNPT0yEynu4nxmEtExMT+Na3voVbt25hZWUlZL7evn07nAs65/fu3Qvnnw56v9/H5cuXQ3ZHp9MJIjqdd73I6AUlnc7PxyhINJvNIKbqxVKjdlquof3I+F4sR2Dvw2aziUql8in/S405X2hWJFsPpJ3OQqEQ+u9RuORjughWofCkyHs6q58BFLVz2jO13+8Hh0iFQI1sa3aqZsESvQZwUa9CrJaSqcB5kvPGfaaowfv0OHVfeT9RMTaKokQvVdooXlN0wBrL1CgQ6Htxv+lMatbyzs4OarUa1tbWQusZLti531rSptkNmmmr5cX8rCj0aJYAt6v2u91uo9lsot/vh0xfY8YdZoXmcjm8/PLLwWfh4hA4zvxX+3mSoJe2t7QTmqnOx7WPNVvTcNGe7pXK91BRlHaT/aBVjAUQvvva35oBLM2a14WvclKJp153uE9qR2gT+Xpt4cVeufS1d3Z2Qnst7hMFAtpbCtga6NNFvYqg9PlZ+srXp7PpeF3Qz47nRsVmPVbadD0nmpjCdQe3Q8F8cnIyCLIumTUGmJubAwBsb29jZWUF8/PzyGaziWA517kqxtIH0uA/cNweMW2TaVOYWKaBJg1eM+OV9oLJDvRj9XH6fsx2B44HXdPO0vdjq0FNsOBj2p4lvb/8XZ/Dm2aJxnGcyPSlfVThV4VNCqKFQiEM1C4UCvj2t78djomvZ0YpX8PzcnBwENqpRVEUhmzp+mR2dhbAYZVDuVxGqVQKn4F+fjx3/D2dgKDXs/Rnq+eB2gavmepDn5TUYZ4OFmOfEjRGm5ubmJycxPPPPx8E2U6nExbouvBTY6FfMjpfukDml0cFUS4sNaOAZViaEcQvZbFYDGW72vMkig5Lhyj60hnTElot36Sh1cymOI7DdFousnls6UUvRd6THFGKrOzVx75RNGw0UDpk686dO1hdXcX777+P2dlZ7O/vo1qthuhPo9FAsVgMmVw0vB988EEwujMzM6hUKqhWq+j3+ygWi6HVBBcBOzs7WFtbC4KwOpT9fj+ULVCEYESNhpufJ51lfpYs8yN8zv7+Pu7evYutrS3s7e2hXC7jzTfffNr/ysacGWgXe71eEN60/JSVA1woprOPVHDlfVzY0t6oDdasUW0doItJ2lSWamrWLt8jLe4Cx+Ir7SRFRc2kZ39t2ns9BrXF6rzRKdZhMXweX6+ZUdzndCYpncder4coOizXqlQquHz5MhqNRmICer/fx+TkJBYXF9FoNIJD+jhxhovynZ0dNBqNcO2gSNDtdlEqlQAgiD4qHlCQ0fOjIghtvma8qaihonC9Xg990Ciq/+qv/uqT/Lc15sxCse3DDz/E/Pw8lpaWgh2heEr/Vu2SJiIAx6JAHMeh/QntDf1BDfTTXlGQBBCmdBOWuHJQCv1Y2kv2o6Wdou1YXFzE2tpasD209zwe9X07nU5iCAtJ217g0VYxwLGIoBVXGpjjdvW6Uy6Xg28LHAoAHPSysLAQ+mKrAKIZahRdeR3MZDIolUphhgNtKT8X4LhsVcVbHhNFFAbgaHf5upMSMmhjeQ4oAuzu7oZp7SSd2WXMOMMBXh9++CE+/vhjvPLKK4m+18PhMJTEa2IXbZz6QsCxOKffaeC4ddTe3h5qtVqo8NSWIbRJbB1DH3Y4PGwho71Yaf+Z7ER7xCxY9eHYU1UzabWKiT6xwsc1aMQkKhWruQ1W03FNMDExEdou8vhZjcFWhZxXwMGSf/RHf4Tl5WV87nOfw/Xr1zEzM4OtrS00Go3Q55U2MJfLod/vh6B+FB22W7x48WLQYwaDATY2NnD58mXMz8+j1WqFFofA8VwJ/ZyYgKKfIW0+dRi+Jr3O4f/CzMxM0DtqtRq63S6uXbv2Wf5Nzc+BxdinCBfgDx8+BAAsLi6iVCohk8mExspa2kPRkdGgdrv9yOJ+eno6PAYcDq5hv1ZdcHOAwfT0NNrtdphQS8eMi+lMJpOYCg4cR85VhKCAHMfJqYcaqdLoGhfDKrzSweRkWhoFNaDapuHg4HgQFjNK2XaBpROaicsJhIuLizg4OECj0cCtW7dCBrAKw+netTz3S0tLKBaLyOfzKBQKmJmZQafTwY0bN1Cr1UKPl+3tbXQ6HfzoRz9CFEW4fPnyIxliwGHm7vz8fBCIgOOSMwrgzO5QQT1djnVwcIDvfve7WFtbw8TEBG7evIl33nnnKf8HG3P2iKIo9GnVPsy9Xg87OzuhVx7FTc3sPymTVEu4+Dztl8Wm+doPlq+nk0kRADh0jrPZbOjBp+JAWgjWSDhLS7WvU3p4Y3qxf9J9aov1py7O+RptU8NrEKP42lYll8vh+vXrePXVV/HFL34Rd+7cwR//8R9jdXU1ESDULAxeG1gJwPPJ49EqEj6Wy+Wwvb2Nzc3NRF9ZrTbhNjKZTNhnzRjmZ0JhgTcKLBSR6DTX6/UgTCwtLeGVV175bP+gxpxDhsMhvvnNb+LNN9/E1atXkc/ncfv27ZAZr21f1Nehj0t7MhwOg5inZalaTcTvODNWdRv0MXd2dkILAQAhE4rDpHK5HCqVCubm5hLVWM1mMwx5ZPAcQMic0uCMZp6ls7TUv+dPHfioogJwfJ3hsVAI0ewmbkv73FLI5bDIXC4XZkC02210u93wuM5E2N3dDX1xWTU2NzcX7DFLa2lrOciW22byhvalVZ+f10zNatWkDX72eo3hOWIyyMzMDEqlUsgGNMYcUigU8Nprr+Fv/uZvAADPPfdcWLfye0UbRDGTSUj04fg8FUr5d7FYTOgFTFzgUCnadPqk2WwWpVIp+NaaXMR1M1s+6ftRR+B1QWcQpJMgNGlAExUAJI4pnbBAERo4tkFMOstms4kqsHw+H64tfH+KtWxV9fDhw1AJUK1WcenSJVSr1VCttbCwAODQVrPtYavVAnCYDMbrH4CgGRSLxbAmYNZwtVoNgTKuXYDjQJ9mwapewwQwFd11bZNOTuA5fvDgQUiisBA7WizGjgAOFeh0OkHoozEBjp2YOI6DA8U+h4w4dzqdYHQ0ExZAyMpkZIl9ERlBoZHRITVcrOZyuSD6siyAz9PWAzROWpZFJ4uGSo05hVMVOSjQUkyg08ZIml5AKHboeaIRrlarAI4n2HLfVZSemprC/Pw8vvSlL2FqagqNRiM4zxcvXgwGkFlaFEv6/T7K5XJwpjVDTCdSsv9kt9vF6uoqhsNhyNRiSwUtU+axaw8tOv7pyCXPA/t3TU5O4qWXXsLCwkK4oOgUYmPMo0xOTqLZbAI4Hrg1MXHcz5ROyczMTMiAUlHgpBJSZroyu4DPr1QqjwSqKCLwe0+Hj/afggO3CxxnLPG5asOZ9avbYtSdtkrtPfBoixg9Jto4zRbTxzUDDTjOnqAowtvU1OEww8nJSTx8+BDb29t48OABNjY2Ej20WZ3BbATa+X6/j1arFd6PzjKHbQEITjBteLfbRbPZDI4/7SbPpwYwNbinlSR8Hy1F40KEwc4XXnghkc3lUlljHk8cx7h161Zo+XTz5k2srKxgY2Mj2Bh+n+jraoasCnkMelGQ1BJ4DSLRd9Rg0v7+fqLElXaKgXD2JZ2amgoCg9rtVquFVquVCOJQpKQdUpuiASYNgGm1G8+P/g0cB8f4O20Sj0OzoGibaS9ZbRHHMW7cuIF+vx/adtGfZM9X4LjagmIzfVEOl2FVmJ7jg4PDXo6c4s5rDJMluF1m8uqx6zHpudEAYXp9sLi4GO7Xc2OMSTI5OYnXXnsNt2/fRq/Xw9LSEhYXF1GpVEJAWb9zOshLE5nSGacTExMYDAahrVQ6s1SHc3Htn64q43wWDgPkjbZNqws0+1XL7YHjoWTam5a2TzN4gWS/atrRXq8X5rPo89W31UAXn6vJFwCCvsBrBANTb7zxBiYnD+cacDbNlStXMDU1hXw+H2xqq9VCPp8PPiyvH+qPMuGiWCwGPYbXCwbHVPtJ++gqVPNc8bPTtYy2nclms6hWqyEhrVwuuwrhFLAYOwLUMNTrddTrdczNzWFubi6k4dO5U6dGMwjomPJLwuh2q9UK/UvY+mBvby8YAYqyFO+YSUUnR0W/fr8fUt0p/mrkhSX3NJrMYNDIGMuN0oaaxoAZXjQmNIp0ePW1PB+6yOb50ZIxLR/lcQMIZRLZbBblcjnRQ4XnipE6ZkoVi8XgyPLcMDJFZ344POyjs7OzExz3SqWCxcXFcG7oCFMMUEdds69UFIjjOAjqLDMpFotYXFxM9Do0xvxsTE9Po9FohEUqewumo+n8rmuGVdrRoY2l46rltrRr3KZm/PN1fC9+j3WyN19HaLO1v+1JmQC8Pmi2FVEbqwv5tKjMXn66iNYsXc3yVRGWz6fzR4G00+mEATgqbDP4p+ea78O2EpopxeAkbbO2EdAAHMuSWY6m+6wZzdx/BtTSiwCWevV6PRSLRSwsLITgoTHmZ6Pf7+PBgwcYDAa4dOkSlpaWQuCZ/T/Vn01nPOmCOW2PdBHNllK6kFZBgNvhYpg2j69hxRcX7LQD/Ds9qI82iL56ujJLSWfEqlirz1UhmqhwyvfRx+jvcoYA72fQX48/iqLQHo3+9PT0dOhVqOeawUMVl1lFwmotXtdYkUC7z8+T18O0SKKZx3ruuF8HBwdBCLCfa8zPzszMDC5evBiC4P1+H88991xYy/J7DSDxfeb6V+0PfUUGXJgkpraB2fDpdirdbhf7+/shAYKt+phJq31INfFKRViKk2rz0tVbhP60Bnq0IoF2dn9/P1GZmhZk+VoNFPJ9dc4DbaDex3PD46KQvLq6Gs4frzfMfqV9pZ3T6g2+L+1hoVAI7Rw0QSTt5+q50aAlh5HxnKjdjeMY5XI56Efp15rR4qveiNAFOr98OlAKOF7sM+KjgqCWbtFIaHRLs6nU2aUYq1kBwHGEvlaroV6vJzKDmCEKHPd14rRsGq+0QVOHl/uQLtllFpeKmjR+NMqPExA000szzvgaZlIVi8VgJLU8tlQqBaPLPrk8B2wmziwNdRiBY0PHjN9MJoPZ2dmQXctsYV0MqHHU7FpuczgcJkQGPdd0uLPZLObm5jyky5hPCUVMBl207yBRp1Cz8XUBrRlZ6fJT2kNmY3JbfI1mvNPGaxaXOlQq2rKfoZYUneRYnZRlpL/zuSp+6LGps67OaVoISb8PzyP3n0ImnUeeC3U+WTXAACBfy/3gNUKHGtChTYux/Cz6/X7CEeUxqHjCbVJkUYFWM+IGgwFKpRJmZ2dtd435FMRxHNpwDYdDLC8vY3FxEZOTk2g0GgkfTgPxbHeifhBwPKiLfllavFTbqNsjtDtqw4FkhRnfXyvRVGilf60+vNredObRSWKs2tJ0ZpPaVg3O0c/n8TEAxfOiIjWvE3p9oPiiIou2NGDwX8ULrdyK4zisH9j2jP5+Pp8/seKCn4l+RhQFNAim6wQGSnO53Kf9tzNmbKlUKqG//draGjKZTCibT7dRYbKRJgLRNp1UlaXfbdpV9W+B45kC2rpF56Yw2MXvvdpN3s+2AHwPzYrnfdpGi+jz9XcNyHPdTtuptk59aeoGtHuKahE8Pwzo0d7RT+VMBD2PXO+rBpPO6E1rOgCCNqFtFmjb+RytIObn2Ov1wppHzzWfUy6XQwWvOX0sxo4QGoV8Po9Wq4V6vZ5oJ8BIs35ZtW+KGk5+8eloabkp34uLWX5pGeXionkwGODBgwdhAqsaMy5wtYSTxoU/6QQznR5AKENlL0aiUSo18nwvLoa57fTraIDoKOvFYDAYBOeShp7najg8LC/TLDTN+tIMXvYkTGd/cb81u4oltCxnbbfbWF9fD84qG3dz33WhwMUI+45p9rNmiCwvL4fJisaYTwez2uv1Onq9HlqtVuhFTfvQ7XYTwZeTHFEAiUV4uh0AX6slUXycdmFiYiL0LEz3bdIqABUhtVJAg0+0mxrh12NIR9F1P9OObvo5tEXcp3RwTLehQbXp6WlUKpWQkaH7SLhA4PWF+6D7R4des4K1dI3ONRcAdJ55vri/FFqZ7aZDFXkOeT3c399HPp/HK6+84h6FxnwGhsPDyp4HDx6g2+3inXfeSbQVSAd71Iao30v7w4UvgIRfxufrT/U1VTBQnzAdtGIpPm9pQZMipwqIajcpmJ6UpavZryo86yAWtY96XaEIzfen/Wc1gF539vb2Er4tF+d8rYoLw+EwVB1wvzTbLZ3sQVE3k8mEWQoTExNhNgL3RYN9ul5geW36usjzNjc3lxi8Zoz5+VhcXEQmk8H9+/dx584dxHGM+fn5RMUQcJzEBCDhH2rSgNo1tWVcx9J+aRUTv//M3O92u6jX62g2m4nMVU14UptAm6q+qyZ3aVBfBdR0hiyPBTi27WzdyOzbtH1S35nnRlvAUEPQY+XzeNwM7ml1M3CsXcRxHKpwCY+HPrHu+/7+Pra3t8PgMOoeTFTQmwbQuE/NZhPNZvORllyTk5PI5/NYWloKrRXN6WMxdsTwCwsAq6ur6HQ6oYy2XC7jypUrYTFLY8GFITNq6SxevXoVhUIh9EAEDr+MuVwuCH784s7MzIQIVb1eD+0R2DeVr+HilCVdhJF37alIp42ZR+12Ozh97DtCkZaRHxouGkQ6fWwboNleURSFiYN0xHUiLg3+wcFBGMLAnotaYsVjSYu03J4+pg4t33NiYiJMy6XDyGxaTpVst9t47733kM/nceHChdCMm20ceMFhlKvZbGJraytMm+QFiM72b/7mb7o81pgnRC6XC1k3q6uriXYFzWYTOzs7Ibufw0nSmax0ZtTRUyeOtofBKD7OgA3L9BmNTosHhJURFIpPclqZScAsJYoOtLEUKklafOBP2jjaKjrj+lptp6LZvhr4U7vKoJlmsgHHAg3b2qT7Z2lWBs8pH2P5F68Zc3NzmJ2dDUMV2N+Rnx17eLFPGQWJTqeD7e3tcI3QbLjhcIhf+7Vfs9015glA+7W6uopvfvObof/zYDBAo9EIgat0e5KZmZkgEtKW0D6cVKrK96IflRZIuQ36epqNSvtHO6HvkxZf076rCr26MOd+aeUA95N+azorKh3U08QInkfeCoUCCoXCI+W8KnZzn2nL9LgponAoogo1tOUMBNKfZ9ZypVIJvv2PfvSjEACj3S4UCsHO85zt7e1hZWUlXF+0Smw4HOKFF15IfKbGmE9HpVIJFT3f+c53QtLQ3NwclpeXsbS0hFarFdbz+Xw+JF5pRRfFStph4DhIxMfZl5v+lrY+oE9Vr9cTIix/1+Qr4LgqWKsQNCBFX41CJyvHut1uIvijwqzaVFYhp68dWnVA6Odq0Egr4lRYTj+Xx8btaaVCp9NJ7IOeE02+4DVsZmYGn3zyCT766CNsbW2F49rf3w/ty/ge6nMDh3Z8a2srXNP0fExMTOArX/mK28E8Y/jTOCWiKEKlUsH8/Dy63W6IgHzve9/D5z73Obz99ttYWloK0WdtM0CHJ45jbG9vB8PALzrFW4qhrVYrTPNjphKHFnDCH8vKaBwZFfpJzq9miQFIDGvh4p4RcRpJZiBw23wdRWY6iPreaoR4ceA5Y9SLFwPumy7wuV90RLVcl4aMF4Zer5d4X+Awklgul7G/fzi4q9vtYmtrC7u7u6HBOaOBW1tb2N7eDiXGmUwmkXmnEcQoihIlZEtLS3jrrbcAwIbSmKcERUiKmJxMzb9pDxilVyePdpboYph/qzM7OTmJ+fn54DzqcBU6bRqAoqigJUuaMcb7KSzQSeSimBUJdA65D3rswLEAkM6c1SxdBsrYooY2VB3mdL+vKIpC1q8OJtDna2aADnlUAVh7V2kFSL/fR6VSwfXr15HL5dBoNLCxsYHBYIBCoRAyXOM4RqvVQrvdDlkNBwcH4ZpXrVbDtWZxcRGvvfZaeF9jzJMjjmNsbm7i4cOHITjNEtZCoRCytyYmJsKUbv5NMY++nAoDtJdcxAPHPVVVOGUwR4fZpAVI2lL9/qdtAd8jLa5yH5i8oFVoPH7NCFMRguh1hnZekwb4mmw2i0KhkMhepU+pi3weC8UVfWw4PO7fOxwetz/Tdj21Wi3R5oX9afnejUYDq6urqFQqYfuTk5Oo1Wpot9vodDphUKUGMHmOyuVyGNSVDkYaYz47DHavrKzgzp07ePfdd/HlL38ZX/jCF5DNZkMWK4BgBzTwpBUBwHE1EoBEEJvreZbE0x/UKi7VEWjX0uIoWzZqshcf18oA2hBqItq6httOJyIMh8PgI59UgctrAn1bXne0ei09n4Hvr4kQuv/05YHj/ra9Xi9RDa3JHmwXEMeHvVw3Nzfx3e9+Fzs7O4kKLrZj5Pnj7KD0DASK7DyupaUlvP322+F8mmcLKz6nRBzHaDQaKBQKKJfLqFarIaNyY2MDf/3Xf43r16/jhRdeQKFQCKWVmq2VLqXiYpzOLnuUqGHUTCsKjzQSmtXFhTMNLg2SihEUEtJGm8O+dNvcBo0Zy680+r+3t5foiwsgRN/o1PHiwZ627DVFw8eFPg2zZrppeRsNK40sF/Fzc3OJctdmsxmyOPb29lCr1bC+vo6HDx8+YvwmJydRrVYTUat8Po+vfOUriciboqKrh3QZ8/ShI6qCnzqlwHFvKi1H0pYv/Jl2AtOlS2x9og4YcNz2hDZJs0+B4+Fb/FszFyhy0lnk49xHirjqBGt2qtpCOsCaWUZBmsevNk7tvYod6ojSkaWN5rWJNl9FWkLxV88h90MDf/l8HouLi2FCOPt2s6UB9zGbzeLGjRuJrAB+Rrz+8Lzo/4Ex5snDoDntKoe9DgaDUFFFgfHg4CCR2a9+kVYrPS6LHzgeQkW7ztel22HR5uhsAxUQFA0qAcnKgrTQoPZT0ddodixfk058YEYXfVUO4D2pBRjtnJbq0u6lM2BVkNakCAq06SAeW4HRlg+HwyAecF0xPT2Ny5cvP3IsGgDUwKZFWGOeHjdu3MDDhw/DWrfX6+Gb3/wmVlZWcP369RCQpp1M2zX9vhIt69dAEJAcFkXbRVuh2gX9Ok1M4nWBWbbqzwLJgayahMB9SicvqJ3m76wO1gQE/q6BM9pXXnOYcaqZsEQDdCp8prfN7afbJdIvZqIBK/h2d3fxF3/xF7h9+3bCJtNvZnuYYrGI119/PSFI8zjSorRW0JlnDys/p8j+/j5arVYoF5iensalS5cwMXHYfHllZQUHBwe4du0aKpVKYgGrhoYOErOu6FRpvzxtrM37+eVlxoCWB9CpZdSGr2UWqkbtVRROl3XpsC4a0XRkitF3jYZp3xVeHHS72j+Qf6uB5/0a5dP3U0GBUDBOO9nsFdNoNLC9vY2dnZ2QdaWlGxSxacQvXLiA5eVl9x805hmCNoaBKtoDls8Ch/aV2UC0lxQFdLiXOqdKFEUhk0AzZU/qAa79qNV508WyColpofYkB0uz79POLx1XOs1qA9ML5XTGrg4gSAsT/MnzOhgMHukPnh4Oo3aW1wluh9dHDvqanZ1FLpcL2R7MfuW1jEHA2dlZzM/PuwehMc8IuvCmPSJcYGu7AkX9UrVrutg8aXFNu6V2VH3ok+y22jr6vnp/ujcf35PHpfY9LcZyX/S1ut/p+4gmQvDc0Xanzy/Pp16f0sIoz69WOHD/6PfzmsNrU6fTQblcDlnGrVYrCDzD4RCFQiG0VzPGnD5aMcrvJX2nvb09XLhwARcuXEC1Wg1rXC3zT/d3Vt8wbdeI2hnacs3KV9+Zz+O24zhOiJX6funrBX3YdCKE+sbpNgDqv6d9Xw2K0cZrlQK3pz4q/05XmOm200E6bbOQDrxx4O3a2ho+/vhj/PCHP0y0lFDR+eDgAAsLC7h69aqHHp4TLMaeMiwBBQ4NZaVSQTabxdbWFprNJu7evYt+v4833ngj0UtQ0+q56H9c2r4u5AmNC9sWqOiq/QG5TRoAzSBIR45oNNPRft7f6/UwMzMTRAdN/6dDNzMzkzBYaXGV22VZLMth1QDSCPIYNeNLs740kqUOKfdJLxStVgtra2tYX1/H7u4u2u02ut1uOPeaLVetVgEAV65cwdWrV5/2v5Ax5lNAGzIcHvbZ7nQ6YYo2bag6OrSTJzmf6ggCySxaza5V506dN82W0sd4n75OUfEhXUarZV58H8001euELvb1Nbo/FGPTATMeP98/3Q9MKyDS2QoqlPA6QYeT7V3i+LA8N5/PI4oibGxsoNfrJQbOAAitDObm5jA/P/8z/x8YY54+aZGVgWvaFgbvgUcHWqXtBJ+Tzv45KTuI/l4600ufq++Z9mMVDoHRY+B21abRV+b7pQNs6azQk4JaaRGC95/U1gVIir08Z8z8PSnAx9eofdcKApa/ttttNBoNZDKZ0P+c1Qi5XA7D4RDFYjH0qjTGPBuwgoj2aG5uDvV6HRsbG2GY6cTEBCqVSiI4oz5bOmAOHAuzaiN1TkJamFSBVYNyQDIoRh1CfViu8fncdGsvINk2UYN1mmGroiyPR31Q3Q8ea1pLUYFVz8VJvi6TFtL7o5mxmkjR7Xaxu7uLu3fv4gc/+AF2dnZCMpjub7VaxXA4xNWrV60vnCMsxp4y6WyAUqkUehjWajWsrq7i+9//Pqanp/Hqq6+GqXvdbjfRwBk4Nprs1ZTJZBI99NQIMgrT7XaRzWYfyYDVvic0cCy35UKbQrBG01XY1AwptgIAjo0eBYy9vT3k8/lgZGjMWUKbFjD4WoqsGgXjcdD55mAwRuW0vFYz0jSjgf1h2d5ha2sLH3/8MW7fvo1arRbKBtIZaRR13nnnHbcbMOYZRhe5w+EQpVIJ2WwWjUYDrVYr/Lx06RKKxWJwuNjmRcuhuA1G1bXkis/RLNp0xoE6gekSKuBYrE0PIVTBVxfm2rqAtk5b1Wgbg3QLmrRgwf2jM8lsV/Zb1euX7hcHUKpDr2KyOq1anszsVtrkTCYTSpp7vR7W1tawubmJUqmEcrkcJtFOTEzg5s2bj5QWG2OeDbTEVAdEaQUXWxjw+bSb6RYpwMlloHydPkf78rH9Ae0gn6PPpX+pC28+pgJEOrOK/ia3nbb72lNW35evV/FAj0+FCJ4H4DhxQDNzdRsUu3l+T8pQY5KE+v3dbjcMReRwy+FwGIbVAocDazOZDJ5//nnbXGOeUS5duoTt7W3s7u4Ge1GpVNDr9dBqtbCzs4N2u4233347CLfD4TAxzJXBJ207oMF8XWtrn1iu3XUoVTpYlvY56TemhU9NVtBkK21Lo+KpJgnwuZzbwvc8yW6pzVbbqu+hz1V/nMfFa0Q6USJ97eK1Looi1Go1fPDBB7h9+zbW1tbCOaetZoVdJpPBl770JbcbOIdYMXoG6Ha7aDabYaGbyWRQKBQwOzuL5eVlNBoN/OhHP0Kj0cDy8jIuXboURNuJiQk0m81QRkvDyH59FDWnpqaCAdahKmknSzO/dIE9NTUVDAeNILNo+bgKvdwOxVgaZg5tGA6HYdo1hzhwGJb2M5yYmAiGSRf36nhq/79SqZTI8NVInTqvJ5UdM4uAmbCbm5tYX1/HnTt38PDhQ7Tb7fC6arWKbDaL/f19lMvlUBr7uc99bsT/PcaYT4M6ZL1eD9lsNlQmMPN9c3MTg8EgNMwHkKgmUBvCCDbwqPOokXzN5Fe7mc6Ypc2i/UxnmKqjpv2g1FGj2MFWNFrexf3lNjQrTZ1eVmTw9cCjvWfZr5UL+nK5HOw9M5DTYiyPn39rn3Oen729Payvr4fBMNPT07hx4wYWFxfDdW4wGOCll156Cv8hxpgnCb/jBwcHuHjxYliAMxOedimfzycGd+nMAdoOirRAsmRU4cKcw/40+KS2RwNrHDKm2aIa2EpnzKpgoI/RFtKOcVvcnrYLoE3m31pNoY9RwODj2gaG5+ak3t+EdphtwbidXq+HRqOBTqeDzc1NNBqNRNucGzduYH5+PlyrqtVqYt1gjHk24dr6k08+CX5ePp8P69eVlRUMh0PcvHkz9OOnz8Z1v7a1+kkiJu0N7Qp70qZfR1+Wtor7lW6BqP5yOoGCvao1qD8cDoNfS3+0UCig1Wo94nOmoa2jDsB9UVuq9ldbMepjtPknVZn1+33MzMwE+//w4UO8++67+OSTT0LfXl6vCoUCstlsCCYWi0W88cYbn/n/wTybWIx9RuCCvtPpYHFxEdPT02i322g2m1hYWMDrr7+Oe/fuYX19He12G3Nzc0EQpPOmU6qZEcobozI0jjRUFy9eRLFYDAIDYQYD2wbQ2NTr9UdKAfg4jSmAMLSL7wMcD2np9XrBIZ+cnMTs7GwQNzRzlQZcSxcODg7Q6XRCJhWzhPP5fIjya6kYBQsgWbamIokOw9nd3cW9e/ewtbWF3d1dbG5uYmdnB4PBAKVSCQsLCygWi8hms+HvxcVFlEqlRxYCxphnm4mJiVAZoBn4HPy3t7eH7e1ttFqtEGSi45gumeJAk3TkXUtZyeOcWq0qoBOqNpTbZ6Sc0fL08D9m/LMqgY6jlpzRWczlcsFBpEOpLRw0u4FBQxUweP5UdG61WsH+p51xFZWBQ4GmVquFaby9Xg/NZhO7u7vodrshOKlDJ7rdLubn57G0tHRiObEx5tmEC+K1tbUQxAYOsy0ZwInjwxYxFALouzEgxuDUSf4d7ZUG3Ok7amls2h6mqx8o9tJWasVAWljgcfEn7WEmkwk2E0AIbBHdpvrT6kty/gMFD/r26ufSbmvWq2a16XUqiiIUi0VMT09jbW0NjUYDu7u7WFtbC4kduVwOc3NzKBaLKBQKuHDhQhAGThK9jTHPLoVCAa+88go++OAD5HK5YGsODg5w5coV7O7u4t1338Xly5dx9epVLCwshCAN1/U6dDvdd5riKxO9aItZKQAks1fTFWMaIOPzuM10exsgWWmlWaj0UxlIop/c6/US7Q/VH9dglyaQ6bwHtdk8Zl4feCw8R4/zd6m/0NZubGxge3sbnU4nZCzTD2e18OTkJJ5//vmQfGDOLxZjnyEokq6srCCbzYZJ3OpAsSRzd3cXGxsb2NnZwdWrV3H58mVUq9UQLaKBmZw8HA5G46iLexrLTqeTSIunM6nlBWxp0Gq1kM/nE4O51BCle8zyvbgvcRyH5v8UEoBD8YHlaVoOphlZ6YFkWsaVLjHje+t+qrirJQQUeLe2tnD//n3cu3cvOOQUrC9cuBCMZSaTwfz8fCjX1awyY8zZgw4fMy353Z6cnAwZW51OBwcHByFKrdmwGtDRTFktDQWSYiuQLH1SR1VtHZ1cjerTRmtbBC2nYvYTF/IUIfhcdTTpQGpGgmZiAQiDIDT7S1u/EP4+GAxCL3Q+RysuKBAPBgPs7u5idXU1MVV9YmICc3NzeOmll0KbCACoVqtBWKHjaow5e7D1SKvVStiyvb29EIjJ5/OhJcnm5iaazSay2SxyuVyw2bpYV5/u4OAgPPekPquazZRuGaA/TwqmnbQt9TN5H5MSgOQ063Rv3JP8RwoTXKzz+SoEpIN06UAXgEcytIbDIWq1Gvb393Hr1q1wLer3+yiXy1haWkKlUkE+n0cmkwnXPL6vMeZsQX/x2rVrmJqaCu32aBsKhUJoydfr9VCv14OPy2pTIGmvNBlBy/q15QrtVboiStfhvKl9pG2jP6jiL4CE6Mv76cvS3vLYGFBL2/W0PU+3WFDbqpVkWoWhYrH64PoeFLXr9Tra7TYePnyI3d1d9Ho9xHGM2dlZVCqV8PqZmRlcuXIlnAsGzsz5xgrSMwQdyVarhY2NjSD6cXjJYDAI2VdcaNfr9URkvVwuBwFXo0c0jmwfoItslm6le6XwuRQoer1eQqQlFELZjwtAQlDQ1gKdTieIpNlsNpSDcX9pULlfauB1n5mJy9fScPJ4VczIZDKPRLlYutvpdNBoNLCxsYHV1VVsbGyg0WiEKNbs7Cxee+21MJkyk8kgl8uhVCqN5p/CGPPUoY2ifSoUCoksKjqUvV4vDPmKoiiUEukgGs0eoM1hyxWNqtMOq9On4isFS3X2KPZSCE0v4ilU8vXaOoA3ZvdqX3CtGuAxa4RfHet0dvBgMEg4tjpwgedWt0+hlsNhms1mGNbV7/cxPT2NhYUFLC8vhwAjz6EnxxpzPuAie2NjI7QmYO9uBsI7nU5YpBcKBdRqtURlli5+02X+fA/anLSAqr21Cf3PdDZWOoP1p8GsVPVpWX2RznxN+7jpfdd2B8BxO4T0PqazxPg7s8RoY5l8cenSJeRyuWDDM5kMFhcX8fzzzyeuNTqwzBhzNomiKKxbNzY2QtXR9PR0qAIbDAZotVrY39/H7OxsEAEpzLJVDLenIm06qKVBek3c4nM0a5Z+KfBoCyzdBt9P+33zpgkD9G1VMD4py5X7rm3EKLIyWW1mZgb9fj8xMFaPI50Fq3Nu+v0+2u026vU6Njc3EUVR6F3L12WzWRSLxTC/5+LFi1hcXPzMn7c5W1iMfUbZ3NwEcGgoKpUKCoVCiF5NTEyEcvmDgwPU63U8ePAAjUYD165dw6VLl4KQSuNAUZLZRFww0wim0+vpNDKyzyb/HJrC7dIIUqSl88dUfx4Df7JcgD1ruKBnhiuQHKRAx1hLtNinhcfIfmNAslcjjTqzVycmJtDv98NQgl6vh+3tbaytrYWm2dPT0yiXy2EfSqUSXn755ZDBMTMzYyHWmHMKI+502mh3aD8ymUyIcB8cHISe17Ozs8FW8UZnjbZM27AAx5NhubimMMvglw5K1L6uXCRrxpMu4jUQxm1rkIv7oc6piqnal5HXAz6XLQlYuUDRlzZZM3eZ5aZtDfb29tBoNFCr1VCv19FqtZDJZLC0tIQoisKgiWw2G6419Xo99PM1xpwvarUaAGB2dhZRFOHChQsol8tBNOT3f25uLrG4TourKmICx5mlFCHTgq2KBRro12QDFUhV/E2TzhilwDkcDsPgW72mpDO6mMGl+6JZvtxXJhLoPlHo4DWH+0i/ny3BGo0G6vU66vU6MpkM3nzzTVQqFdy9excPHjwIGXLz8/Mhc4uVa8aY8wN9QyZF6byBg4OD0FqFJfPVahXlcjkIqPQ9+ZNtVNIBfNo6DULR9mrCFUVhVnKdNKgwPexcbZ22R1TRVtsPqL3Ua8dPui7Q1waOK7xOel8AIcGr1+uFRK96vY5arYbd3V00m01cvXo19NtuNpshsYPvUSwWcfHixSf9cZszgMXYZ5Rr166F3+/evQsAeOGFF0LUu1QqYXZ2FgsLC9jZ2cHt27fxwQcf4K/+6q9QrVbxxhtvhC9+sVjE3t4e6vU6ACR6x1JABY4jUhQyoygKGUwHBweoVCrIZDKhNy2jRtPT0yFzlAtuzU7gor3T6WBvby8xDEczWhWKFIx2TUwc969lPxWifbjUqFJ8LhaL6HQ6WFlZwf3793H79m3cuXMnMUwim83i1VdfxS/8wi9gMBjgo48+wocffoh79+7hG9/4Bt555x1cuHDhyX7IxphnikuXLoXfaZs4uK9YLGJ2djZkaPX7/VBin8lkEv2kKZyypQoDPekWLBQWNNuJAibtHrdH55eZqcwsSwsEfD/th0hnmIE3bYlw0uI/Pb2cDiMDgMze1X5gOmiMcDhlu91Gv99Hq9XC1tZWGCQ5MzODV199FW+//TY++eQT/PjHP8b6+jpu376NBw8e4Dd+4zewvLz8VD9zY8zpsbCwEH5vtVpotVpYWFgIdm9/fx+1Wg3T09NhEjgDYvTzdAirigXakktnHwDHi3wgme0axzG63W5CXFDRk/abUGCl3wsc2j0AmJmZSSQcpNFgF99bfWceEwUTZl1pey4+Xysu2Ad2Z2cHOzs74TrEc/P888/jxRdfDO9bq9WwtraGlZUVXLx4MQT/jDHnj5s3bz5y39ra2iO28uDgAB999FFoI3jlyhVcvXoVc3NzibYo2hOVSVK0TSeJp9rOgFUR6rOmX8sWhrR9WqlFe9zv90PCFlFRloEs2lzdPivNgOP5PfRzqXPQN04fx3A4RL1ex/b2NnZ2dlCr1cIARArLcRwjn88HPYbi7N7eHjY2NvBLv/RLoerZjCcWY88Q3/72t5HJZFAqldBoNJDJZJDNZlGtVlGpVLC8vIz3338fd+/exZ/92Z8hig6bQj/33HO4fv06Ll++jEKhkNgmF9X8HTge7kLjNTExgVKphFKphHa7HQzezMxMEEiBZA8u4FgQ5YTs4XCIarWaEBP0vbiI5wVBs6rSfWlzuVwQHXRQg07B3d/fx8bGBv7yL/8SH374ITY3N4MIAAD5fB5LS0uYnZ3F7OwsnnvuudA64Y033sDrr78eztPPUppmjDl/fPLJJ5iamkKlUsHc3BwKhQKmp6dDi4Lt7W2sr69jc3MztDApFAoolUohK582KZ3NRadQM76YtaB2j7ZSe8RSSKUzqu0JVHzljbZNnVfaZRVmmU2m2+X2ZmZmgs1VUbbVaiVsf6/XC4MKarVaEE54HmjDy+UyhsMh3n//fSwvL+NXfuVXEufedteY8ePevXuhGoyiIBev2kKrVquFRTV941KplLC39Ff5HLVruhjXoBiAkBlGoRdAwi6n+7ASbptJB+nH0pUIaWGX70FxQEUPVrjpvAW2z+l2u1hbWwsDJ9vtNtrtNgaDAQqFAorFIkqlEorFIiYnJ/Hnf/7nYZjPiy++iBdeeAGAba4x48ju7m6ofspkMhgMBmFQNQeqvvvuu3j33XdRqVSwtLSEpaWlRJWuBvYBJERZQtu9t7eXSCpgAEvRBAIdtMX3yGQyiUG1+Xw+aBbMYk0LvFqdpvMSWPmmbcqoTTABLZ/Ph202m000Gg3cv38/tH2g+Er/mclgPMZ79+7hq1/96iNiuG2usRh7hiiXyyEi1Gw2g3Gp1+uhtIpZRvV6Hffu3QuDvjY3N3HlyhVUq9VQasqoPQd8TUxMhIxTLqY17V97vaYjTunFvGa8suxWjRv739KwaZsBGkct6+LzKLKqAWXGwO3bt7G+vo5WqxV6Y62srKBWqwUxgUPQVEyh83z58uWEsbeBNMa8+OKLicARnbtarYapqanglLIElBlJnEJNZ5L2D0hm/muLl06nExw/HXy1t7eX+Js9ENPCArNV01lYapOB45YxHCKgpb8AgqPM53KoGTOy2DoGQCjJYi/YnZ0drK2thSGIWgLMCgcdcvbSSy8lBjoYY8Yb9jLtdDqhxQBnFuRyOczMzKBSqYThXPV6Hb1eD7VaDXt7e7h27VpYpKvoSn+WDIfDR2Yd0M+dmZl5pGKL9laDaNwOew1S9OXwK+DYl9R2B9oTUVt08bms3OJjmqHF0lc+h9lYW1tbiUo3zj3g3AkG5a5fvx4yyDQL1xgznlAg1Gz7KIqwtbUVbO7c3FyoVLh9+zbu3r2LfD6PK1eu4Nq1a8jlcok2VhQ4acO0Sov+pVbTqk2ljWUyA5AMZrEagUKsZuTyOHQoJLUEohUPrBbje1B74Hsx2HX//n30ej202200Gg00Gg10u91ExYJWHNP/LRQK+OVf/mUACAEwYxSLsWcIHdaiTiL7F7I/FbM8C4UCms1mKHednp7G+vo6pqamQkYSDWWj0QCAINTWajVsb2+jUCggn8+j1+sl+hTqJETg2KHjflFgoAhLY07xk8IGo1S6YFdnUhtip4VSihKtVgurq6v44IMPsLq6Gobw0JDOzs6GwS/clziOg7NdLpexvLwcxAZjjCEskUqTyWTQ7/extbUVnlMqlUIWFm0gnTnNDNWFOCsFMpkMGo1GsOPpCaoUTYGkw0wnUvvDpoeF6ePcBxVjmU0LHPe4pd2lGKwtCXjrdrtYXV3F1tYWms0mms0m6vU6BoMB8vl8CO5plUU2m0U2m8X8/DyuXr362PNrjBlP1AckOgCQGU6sROCQFbacWl5exsrKSlho5/P5kFDA6qg4PpyQzUwm7ReofV31/VV8ZcYq91dFTfrHtKVEBybq6xjcoi3Wdi8UGXq9Hvb399Fut7G5uYn19fVQpcAKBbbm4vnjdWVmZiZkx5bLZbcgMMYkeJxNmJ2dxc7ODtrtdljDT0xMBNtDG3f37l1MT0+HFooUZalbMFOUw8E4cyEtlGo/Vh3Cle7tOj09nQhUcT+4HZ2tkLbPOlAMOPat6ZsDx+2+GOza3t4O7QcGg0EQYVVY1X64zLqdm5vD9evXUSwWn+jnZc4XFmPPAdqX8ODgALVaDdVqNZSkTk5OolKphBLTKIqCIEmntNVqBYcvn8+j2+0iig6nL+bzeXQ6nUTza51OqBEhwsc4HZsOIQVPzeJinxkVGFTc1WmuzEyr1WrhtrOzgzt37mBlZQWdTidRmkbnk8McGKUqFovBmM/NzeHy5csj/9yMMWcXBrJYXpXJZJDP5zEzMxMW0rR3mtnf7XZDGZO2FGDQS/vDqqgKHGfUpstk05xUQksnk/uh1wwu9AEEIThddsbn0xntdDrY2trCvXv3UKvVQlYv+4tNTk6GIY0Ufy9evBjO0fz8fKJHrzHG/CS0jQvtI7OfaGOLxSKuXr2KZrOZyKaN4zgEgjg4JZvNotlshmE22ldWB8g8bl8AJERZbSfDbWr7A+03y4QCbUuggoAGzthyoNVqhaEwtVot+OEcgJPP5xMtwDKZTJiEns/ng+9rjDE/C7lcDoVCIQS6JiYmUCwWEyIpEwlarVZiYDh95CiKQkAsn8/j4sWLaLVaqNVqYW5LOqOVv6st1h7bKrqqv0vtIT18V9uEaVBMK3oZCOPAbtrcRqMRBGmSHixGIXZmZgaLi4vhejI/P++5B+anYjH2nEDDBwBbW1vY2tpKPFYoFHDjxo1w397eHh4+fBjS+8vlcjCIk5OTWFhYQKFQQLVaRRRF2N7eDpmkmiml/azUudQMgvTkboq+/X4fw+EQxWIRuVwuLNo5lZB9VrTp9t7eHra3t3Hnzp3Qq4UlWplMJgzY4VCHqampUFbAfjj5fB5vvfXWaD8gY8y5g30NFZ2wur+/j3w+H2wbBUuWRbXb7ZDJzyoECgYsnz0pQ0BLXTWjVcutgGPhlQt7ihm64Gfkn0NrKpVKWMBzW/v7+6FHFocUsAVOo9EIWWa6+Kdjm81mUSgUUKlU8Iu/+Isj+FSMMecV+nXA4aAs7TVI0ZEZs/QZ6/U6Go0Gbt68GXq5sscgq6ToP3e73eCnatIAkBRg+bcu7rXP60mtXegLM9ilLbqYDcvb3t4eer0ednZ2gp/barUSrcPYBzyfz4dMYfq5DIRZCDDGfBYqlQoqlcpjHz84OMD8/DzW1tawt7cXBtwWCoWQTcsg0KVLlzA/Pw/g0H9ut9tBhwCOK4BVhE1nsk5NTYWhuGqLmSGrSV30Y7WPOIVfnUvDxC9WHtDe0mdXsZi+NhO/2H6LFQjvvPPOE/4EzHnHYuwYMBgMcOvWLdy6dQsAcP369WBYB4MB6vV6YpGfz+dx4cIFLC8vhwyny5cvI5vNhimAHP6S7kOo9Pv9kBmrpQcUXjVqxubhNIgsAWDJa6fTQaPRwObmJtbW1kJbBWYDvPjii6EVAYAgfHQ6HWQyGZTL5cSQAmOMGQWdTgfA8SRYVhtQEGWGQL1ex+zsbFiIa5CKWVXMrEoPgtGqBG1how5t+jV8n0ajgY2NDbRaLQCHE80LhUIiuDYYDLC1tYXNzc3QJ4vlZcw8o51n+4Lp6WksLS3h+eefx8WLF0dyro0x48v+/j5WVlbwe7/3ewCAq1evolqtotPpYH19PfiNtIP0defn51Eul0Pbgm63i93d3RCgYlUAYTBMg2IKExsYZKtUKqEHLiu2ONiQ/jSrCjjMhtcEDi4DEGYd5HI5FIvF4J9T6OXxFAqFRFszY4x5WjAB4eLFi2i326FSivaNPiuHgd+7dw/Xr18PLVM4V4aip1ZAqG+rVbVMQNNkMO3/yvYz/X4/BNq0urff72N3dzfoBNxv7nt6ACSDgNr6gFm609PTuHbtGp577rlTOf/m7OOr9Rhy79493L9//5EeLJqt9fDhQ7z33nvB+Zubm8PMzAy63S4ymUyIugMI7Q8II0ccMqZlBXt7e2HAjZZbcVgYb+zhRSeT+5nL5ZDP53Hp0qVEXzGW3TJKduHCBbzxxhuJKNhPKus1xpinSXo4CwAsLCwgm81ibm4Ok5OTqNVq2NzcxMrKShgEU6lUkM/nQ9Y/ewLSCWVknv1pNTNAKxEY5Wergb29vZCZ22630ev1MBwOsbOzE7IU2KNRe8yyNyPLY7n/2WwWi4uLePnll8Mx/7R2CsYY87RYWVnB6upqsIUsM2Vga3NzE1tbW6G8tFQqYXFxEVevXkU+nw9DsACEft7sfa2ZVpy/QJiR1ev1QhsELv6ZNUZ73O/3g3DA5Aa13xzARSGAZcPsSc4sYKLXGWOMGSWs8EoH4Hd3d7G2toaNjQ2srq6GdlWFQgGlUilk+bOFIP1ZttIi7BdbLBbD2l9bylDUZRIXq74YUKMvS1FXKxLYA5aDyrkfbHVA21qtVvHSSy8l9sl+rvksWIwdQ35SDyyNXrEEYHJyEp1OJ0wKZyuBXq8XMg0Y0dLm1ZyWzdYGHJ5Qr9fDhFw6jRR9OeCLi30gOYGWJV98f4rANJ6vv/566JvlzABjzLNMFEUJgbVcLocsKfar2t7extraWmixwrYHwHEGlg4oyOfzoV93t9sNkf9ut/tIv1nNNND2BdqjloGudKZANptFuVzG/Pw8nnvuudBaIT14zBhjToO0r6uzCBjI0gV/v98PCQIURulzNhqNEJCiOKCZUbOzs0EI4DyChw8fhufzvdkyi/sXRVEQfHkdYL/tg4ODUKXA99PkCM3QMsaYZwEdhkgqlUro3R3HMe7fv49msxmqAAh7sPJ3iqW0ubSNrC6jyKqVXtQGGODSqjBNQqMvzMQCvjeFV9p2Vqe98MILwe5aXzBPEv83mYAaKRog3s/FOo0XDVgulwt9YXXYDB1MDkTgMANGtbjIp6HkcC/tlcXhNumJuhRgl5aWQr9Gtj1YXFz0pFhjzJmBQwaAQ7s7MzODXC4XSlGZycrnsS8WcLyYB47tt/bV4uKewa4oilCv11Gv1xMTwWmLNctAy3A5gHFmZgYLCwvI5/OhRUyxWES1Wn3E+TbGmGeRdFWY9sYeDAbY3NwEgEQGbFpUVUGVZa9s3cJ2A+z73W63Q7msLu7pN7PfrJbDFgqF4BvncrlgX2l3HfQyxpwVtNc3cFi9Sn9ThyACxwlYbDsAHPu69EfV36Sd1mFdu7u7aLVaieQC1Se0GlgTyMjCwgKKxWKi0qxSqdjumqeCxVjzWLQfLHBoyJ7kJNZerxdKF+jwslyr1+uh0+lgbm7ukX5cNMSvvPIKFhYWntj+GGPMacPqgmw2+1S2v7a2ho8//hgbGxvBMdVBXxMTEyiXy4lJ5eyTVSgU8NxzzyXKYo0x5iyTXqyzjYxCO8gs1jRc9DOwNjU1hUuXLgEA6vU6tre30e/3E21mtP83K7pYFsv3mZycDL0VjTHmPHDhwoWnuv21tTVMT09jd3c3iL3avoXtBnSejT6+vLyM2dnZp7qPxhCLsebUYB+sb33rW5icnAzDvDKZTOgn89WvftXlAMYY84S4ePEipqen8eGHHwZxIZvNhjYxmUwGX/7yl136aowxT4DZ2VlEURR8XVaJsaKrWCzilVdeca9XY4x5Aly8eBG5XA7f+MY3EnNndI7NW2+95You80xglcucKtPT07hw4UIopZ2ZmcHly5fxhS984bR3zRhjziXZbBYvv/xyGJ5QKBTCzRhjzJNleno6CAQMfOVyuadWAWGMMeMMA15sYQAcirSf//znT3nPjEliMdacKuVyGV/72tfC3yc1/jbGGPPkyOfzePvttx/poWWMMebJk81mE9mvtLvGGGOePKVSCV/96lcT91lfMM8iFmPNqTIxMYFcLnfau2GMMWMD28EYY4x5+rD6yxhjzNMniiJXHpgzgZvCGWOMMcYYY4wxxhhjzAiwGGuMMcYYY4wxxhhjjDEjwGKsMcYYY4wxxhhjjDHGjACLscYYY4wxxhhjjDHGGDMCLMYaY4wxxhhjjDHGGGPMCLAYa4wxxhhjjDHGGGOMMSPAYqwxxhhjjDHGGGOMMcaMgJ8qxkZRtBxF0X+KouhWFEU/iKLoHx3dX42i6I+jKPrw6Ofc0f1RFEX/PIqij6Io+n4URW897YMwxpjzgm2uMcaMFttdY4wZHba5xhjzs2XG7gP4J3Ecfx7AOwB+J4qizwP4XQB/GsfxiwD+9OhvAPh1AC8e3X4bwL944nttjDHnF9tcY4wZLba7xhgzOmxzjTFjz08VY+M4Xo3j+HtHvzcB/BDAFQBfB/AHR0/7AwB/7+j3rwP41/Eh/xlAJYqiS096x40x5jxim2uMMaPFdtcYY0aHba4xxvycPWOjKLoO4E0A3wKwFMfx6tFDawCWjn6/AuC+vOzB0X3GGGN+DmxzjTFmtNjuGmPM6LDNNcaMKz+zGBtFURHAvwPwj+M4buhjcRzHAOKf542jKPrtKIq+E0XRdzqdzs/zUmOMOfc8aZt7tE3bXWOMeQxP09ft9XpPcE+NMebsY5trjBlnfiYxNoqiaRwayn8Tx/G/P7p7neUBRz83ju5/CGBZXn716L4EcRz/yziOvxjH8Rfz+fyn3X9jjDl3PA2bC9juGmPM43javm42m316O2+MMWcM21xjzLjzU8XYKIoiAL8H4IdxHP8zeegPAfzW0e+/BeA/yP3/4Gjq4TsA6lJuYIwx5idgm2uMMaPFdtcYY0aHba4xxgBTP8NzfgHAfwvgvSiK/vbovv8RwP8M4P+OougfArgL4L8+euw/AvgagI8AdAD8d09yh40x5pxjm2uMMaPFdtcYY0aHba4xZuz5qWJsHMd/CSB6zMP/xQnPjwH8zmfcL2OMGUtsc40xZrTY7hpjzOiwzTXGmJ9jgJcxxhhjjDHGGGOMMcaYT4/FWGOMMcYYY4wxxhhjjBkBFmONMcYYY4wxxhhjjDFmBFiMNcYYY4wxxhhjjDHGmBFgMdYYY4wxxhhjjDHGGGNGgMVYY4wxxhhjjDHGGGOMGQEWY40xxhhjjDHGGGOMMWYEWIw1xhhjjDHGGGOMMcaYEWAx1hhjjDHGGGOMMcYYY0aAxVhjjDHGGGOMMcYYY4wZARZjjTHGGGOMMcYYY4wxZgRYjDXGGGOMMcYYY4wxxpgRYDHWGGOMMcYYY4wxxhhjRoDFWGOMMcYYY4wxxhhjjBkBFmONMcYYY4wxxhhjjDFmBFiMNcYYY4wxxhhjjDHGmBFgMdYYY4wxxhhjjDHGGGNGgMVYY4wxxhhjjDHGGGOMGQEWY40xxhhjjDHGGGOMMWYEWIw1xhhjjDHGGGOMMcaYEWAx1hhjjDHGGGOMMcYYY0aAxVhjjDHGGGOMMcYYY4wZARZjjTHGGGOMMcYYY4wxZgRYjDXGGGOMMcYYY4wxxpgRYDHWGGOMMcYYY4wxxhhjRoDFWGOMMcYYY4wxxhhjjBkBFmONMcYYY4wxxhhjjDFmBFiMNcYYY4wxxhhjjDHGmBFgMdYYY4wxxhhjjDHGGGNGgMVYY4wxxhhjjDHGGGOMGQEWY40xxhhjjDHGGGOMMWYEWIw1xhhjjDHGGGOMMcaYEWAx1hhjjDHGGGOMMcYYY0aAxVhjjDHGGGOMMcYYY4wZARZjjTHGGGOMMcYYY4wxZgRYjDXGGGOMMcYYY4wxxpgRYDHWGGOMMcYYY4wxxhhjRoDFWGOMMcYYY4wxxhhjjBkBFmONMcYYY4wxxhhjjDFmBFiMNcYYY4wxxhhjjDHGmBFgMdYYY4wxxhhjjDHGGGNGgMVYY4wxxhhjjDHGGGOMGQEWY40xxhhjjDHGGGOMMWYERHEcn/Y+IIqiTQBtAFunvS8jZAHjdbyAj3kcOOvHey2O4wunvROjIIqiJoAfn/Z+jJiz/v/58zJuxwv4mM8iY2F37euODeN2zON2vMDZP2bb3PPNWf///HkZt+MFxu+Yz/rxPtbmPhNiLABEUfSdOI6/eNr7MSrG7XgBH/M4MG7He5YZx89q3I553I4X8DGbZ5tx+6zG7XiB8TvmcTteYDyP+awyjp/VuB3zuB0vMH7HfJ6P120KjDHGGGOMMcYYY4wxZgRYjDXGGGOMMcYYY4wxxpgR8CyJsf/ytHdgxIzb8QI+5nFg3I73LDOOn9W4HfO4HS/gYzbPNuP2WY3b8QLjd8zjdrzAeB7zWWUcP6txO+ZxO15g/I753B7vM9Mz1hhjjDHGGGOMMcYYY84zz1JmrDHGGGOMMcYYY4wxxpxbTl2MjaLo70ZR9OMoij6Kouh3T3t/nhZRFN2Joui9KIr+Noqi7xzdV42i6I+jKPrw6Ofcae/nZyGKon8VRdFGFEXvy30nHmN0yD8/+ty/H0XRW6e355+Oxxzv/xRF0cOjz/lvoyj6mjz2Pxwd74+jKPovT2evPxtRFC1HUfSfoii6FUXRD6Io+kdH95/bz/k8Mg521zb3fH4Xx83u2uaeD8bB5gK2u+fx+2iba5t7FrHNtc09q9/HcbO5wHjb3VMVY6MomgTwvwH4dQCfB/DfRFH0+dPcp6fMr8Rx/EYcx188+vt3AfxpHMcvAvjTo7/PMr8P4O+m7nvcMf46gBePbr8N4F+MaB+fJL+PR48XAP7Xo8/5jTiO/yMAHP1f/30Arx695n8/+v8/a+wD+CdxHH8ewDsAfufo2M7z53yuGDO7a5t7/r6Lv4/xsru2uWecMbO5gO3uefs+/j5sc21zzxC2uba5ONvfx9/HeNlcYIzt7mlnxn4ZwEdxHH8Sx/EAwL8F8PVT3qdR8nUAf3D0+x8A+HuntyufnTiO/z8AO6m7H3eMXwfwr+ND/jOAShRFl0ayo0+Ixxzv4/g6gH8bx3E/juPbAD7C4f//mSKO49U4jr939HsTwA8BXME5/pzPIeNsd21zz/h3cdzsrm3uuWCcbS5gu3umv4+2uba5ZxDbXNvcM/t9HDebC4y33T1tMfYKgPvy94Oj+84jMYD/N4qi70ZR9NtH9y3Fcbx69PsagKXT2bWnyuOO8Tx/9v/9Ucr8v5LSkHN3vFEUXQfwJoBvYTw/57PKuHwmtrnj9V0893bXNvfMMk6fie3u+HwfbXMPOVfHfE4Yp8/ENnd8vo/n3uYC42d3T1uMHSf+ThzHb+Ewrfp3oij6qj4Yx3GMQ4N6bhmHY8RhmvxNAG8AWAXwv5zq3jwloigqAvh3AP5xHMcNfWxMPmfz7GObOwbHeMS5t7u2ueaMYLs7BscI29xx+IzN2cA2dwyOEWNgc4HxtLunLcY+BLAsf189uu/cEcfxw6OfGwD+HxymkK8zpfro58bp7eFT43HHeC4/+ziO1+M4PojjeAjg/8BxqcC5Od4oiqZxaCj/TRzH//7o7rH6nM84Y/GZ2OaOz3fxvNtd29wzz9h8Jra74/F9tM09/5/xGWdsPhPb3PH4Pp53mwuMr909bTH2rwG8GEXRjSiKMjhsQPyHp7xPT5woigpRFJX4O4BfA/A+Do/1t46e9lsA/sPp7OFT5XHH+IcA/sHRNLx3ANQlDf3MkupX8l/h8HMGDo/370dRNBNF0Q0cNpz+9qj377MSRVEE4PcA/DCO438mD43V53zGOfd21zZ3vL6L59nu2uaeC869zQVsdzFG30fb3PP/GZ9xbHNtc8/V9/E821xgzO1uHMenegPwNQAfAPgYwD897f15Ssf4PIB3j24/4HECmMfhZLgPAfwJgOpp7+tnPM7/C4ep83s47N3xDx93jAAiHE66/BjAewC+eNr7/4SO9/88Op7v49BQXJLn/9Oj4/0xgF8/7f3/lMf8d3BYIvB9AH97dPvaef6cz+PtvNtd29zz+10cN7trm3s+bufd5h4do+3uOfw+2uba5p7Fm22ube5Z/T6Om809OoaxtbvR0QEZY4wxxhhjjDHGGGOMeYqcdpsCY4wxxhhjjDHGGGOMGQssxhpjjDHGGGOMMcYYY8wIsBhrjDHGGGOMMcYYY4wxI8BirDHGGGOMMcYYY4wxxowAi7HGGGOMMcYYY4wxxhgzAizGGmOMMcYYY4wxxhhjzAiwGGuMMcYYY4wxxhhjjDEjwGKsMcYYY4wxxhhjjDHGjID/HweMJsFbvkaBAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1728x432 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABBQAAAFWCAYAAADZt85cAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8rg+JYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAv90lEQVR4nO3deXhU5fnG8fuZyYJhkU2QTUAEBSyiIgJSN9zRghtqW0VFca1al9ZW+6tttbW22mqrWKkWqfu+tLYuWKiAyCIU2bewgyh7AoQk8/7+mAEDJCRvMpOTmfP9XFeuzJxl5nkd8nhy55z3mHNOAAAAAAAAPiJBFwAAAAAAANIPgQIAAAAAAPBGoAAAAAAAALwRKAAAAAAAAG8ECgAAAAAAwBuBAgAAAAAA8EaggP0ys6VmdloVt3Vmdlg138d7XzM72cxWVuf9UsnM7jOz54KuA0BmoA/7ow8DSDZ6sT96cTgQKAC1zMwGmNk8M9tmZv8xs/ZB1wQAYWFmOWb2WuKXA2dmJwddEwCEjZn1MbMPzWyDmX1lZq+aWaug64I/AgWgFplZc0lvSPqZpKaSpkp6OdCiACB8xkv6vqS1QRcCACHVRNJTkjpIai9pq6S/BVkQqodAAVVmZr3N7FMz22Rma8zsz2aWs9dm55jZEjP72sx+Z2aRMvtfbWZzzWyjmb1f1b/Mm1lTM/ubma1O7PvWXuvvMLN1iZquKrN8oJlNN7MtZrbCzO4rs65D4i9TQ81seaLee8qsv8/MXjGz0Wa21cxmm1mvMutbm9nriUQ138xuqeJ/xgskzXbOveqc2yHpPklHmdkRVdwfQIjRh2veh51zO51zf3TOjZdUWpV9AKAsenFSevG/EsfDW5xz2yT9WdIJVdkXdQuBAnyUSvqhpOaS+koaIOnGvbY5X1IvScdIGiTpakkys0GSfqr4L9QHSfpE0otVfN+/S8qT1F1SC0l/KLPuYEkHSmojaZikx82sSWJdoaQrJDWWNFDSDWY2eK/X7i/p8MRY/s/MupZZ9x1JLyX2f0fxRqfE/xDelfS/xPsOkHSbmZ1ZhbF0T+wnSXLOFUpanFgOAJWhD6vGfRgAaoperKT34hMlza7GfggYgQKqzDk3zTk3yTlX4pxbKukvkk7aa7PfOuc2OOeWS/qjpMsSy6+X9Bvn3FznXImkX0vqWVkia/Frqc6WdL1zbqNzrtg5N67MJsWSfplY/p6kAsWboZxzY51zXzjnYs65mYo3673r/YVzbrtz7n+KN8Ojyqwb75x7zzlXqngD37XuOEkHOed+mfhL1xJJIyVdur+xJDSQtHmvZZslNazCvgBCjj6clD4MADVCL05uLzazHpL+T9JdPvuhbsgKugCkDzPrIukRxdPWPMX//Uzba7MVZR4vk9Q68bi9pEfN7OGyL6l4mrlsP2/bTtIG59zGCtavTzTjXbYp/ku7zOx4SQ9KOlJSjqRcSa/utf/a8vatYF09M8tKjKW1mW0qsz6qeMJcmQJJjfZa1kjx68YAYL/ow0npwwBQI/Ti5PVii9/R4l+SbnXO0cPTEGcowMcISfMkdXbONVL8dC3ba5t2ZR4fIml14vEKSdc55xqX+TrAOTexkvdcIampmTWuRr0vKH5aVjvn3IGSniyn3upYISl/r7E0dM6dU4V9Z6tM4mtm9SV1Eqd4Aaga+vA3NVW3DwNATdGLv6mp2r04cVbGR5J+5Zz7exLqQQAIFOCjoaQtkgosPongDeVsc5eZNTGzdpJu1Td3MHhS0k/MrLskmdmBZnZxZW/onFujeGr5ROJ1s83sRI96NzjndphZb0nfreJ+lZksaauZ/djMDjCzqJkdaWbHVWHfNyUdaWYXmlk9xU/vmumcm5ek2gBkNvpwXE36sMwsN9GDJSnHzOqZWTIOrgGEA704rtq92MzaSPpY0p+dc08mqR4EgEABPu5UvAFtVfz6qPJud/i24qd8zZD0T0lPS5Jz7k1Jv5X0kpltkTRL8evAquJyxa8LmydpnaTbqrjfjZJ+aWZbFf/F/ZUq7rdfievHzpXUU1K+pK8l/VXxiXAq2/crSRdKekDSRknHi2t+AVQdfVg168MJ8yVtV/wU4/cTj6s0yzoAiF4sqca9+BpJh0q6z8wKdn0loy7ULnPOBV0DAAAAAABIM5yhAAAAAAAAvBEoAAAAAAAAbykLFMzsLDObb2aLzOzuVL0PAKB89GEACB69GEAmS8kcCmYWlbRA0umSVkqaIuky59ycpL8ZAGAf9GEACB69GECmy0rR6/aWtMg5t0SSzOwlSYMklds8cyzX1VP9FJUCANW3VRu/ds4dFHQd1eDVhyV6MYC6aYcKtdMVpettPTkmBpARKjomTlWg0EbSijLPVyp+e7zdzGy4pOGSVE95Ot4GpKgUAKi+j9xry4KuoZoq7cMSvRhA3feZGxN0CTXBMTGAjFDRMXFgkzI6555yzvVyzvXKVm5QZQBAqNGLASBY9GEA6SxVgcIqSe3KPG+bWAYAqB30YQAIHr0YQEZLVaAwRVJnM+toZjmSLpX0ToreCwCwL/owAASPXgwgo6VkDgXnXImZ3SzpfUlRSc8452an4r0AAPuiDwNA8OjFADJdqiZllHPuPUnvper1AQD7Rx8GgODRiwFkssAmZQQAAAAAAOmLQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgLesmuxsZkslbZVUKqnEOdfLzJpKellSB0lLJQ1xzm2sWZkAgIrQiwEgWPRhAGGVjDMUTnHO9XTO9Uo8v1vSGOdcZ0ljEs8BAKlFLwaAYNGHAYROKi55GCTp2cTjZyUNTsF7AAD2j14MAMGiDwPIeDUNFJykD8xsmpkNTyxr6Zxbk3i8VlLLGr4HAGD/6MUAECz6MIBQqtEcCpL6O+dWmVkLSR+a2byyK51zzsxceTsmmu1wSaqnvBqWAQChRi8GgGDRhwGEUo3OUHDOrUp8XyfpTUm9JX1pZq0kKfF9XQX7PuWc6+Wc65Wt3JqUAQChRi8GgGDRhwGEVbUDBTOrb2YNdz2WdIakWZLekTQ0sdlQSW/XtEgAQPnoxQAQLPowgDCrySUPLSW9aWa7XucF59y/zWyKpFfMbJikZZKG1LxMAEAF6MUAECz6MIDQqnag4JxbIumocpavlzSgJkUBAKqGXgwAwaIPAwizVNw2EgAAAAAAZDgCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACAt0oDBTN7xszWmdmsMsuamtmHZrYw8b1JYrmZ2WNmtsjMZprZMaksHgDCgl4MAMGiDwPAvqpyhsIoSWfttexuSWOcc50ljUk8l6SzJXVOfA2XNCI5ZQJA6I0SvRgAgjRK9GEA2EOlgYJz7r+SNuy1eJCkZxOPn5U0uMzy0S5ukqTGZtYqSbUCQGjRiwEgWPRhANhXdedQaOmcW5N4vFZSy8TjNpJWlNluZWLZPsxsuJlNNbOpxSqqZhkAEGr0YgAIFn0YQKjVeFJG55yT5Kqx31POuV7OuV7Zyq1pGQAQavRiAAgWfRhAGFU3UPhy12lbie/rEstXSWpXZru2iWUAgOSjFwNAsOjDAEKtuoHCO5KGJh4PlfR2meVXJGa27SNpc5nTwAAAyUUvBoBg0YcBhFpWZRuY2YuSTpbU3MxWSvq5pAclvWJmwyQtkzQksfl7ks6RtEjSNklXpaBmAAgdejEABIs+DAD7qjRQcM5dVsGqAeVs6yTdVNOiAAB7ohcDQLDowwCwrxpPyggAAAAAAMKHQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3rKCLgCZoWjgcVrXM1vRYqn1QxODLgcAQiGrbRvlD20vSeo4aqkKe7ahFwNAgDgmRtgQKKBaInl5ct077X7+5ZU7NPeEkVpeUqBrx94gc4ntNhaodFF+QFUCQOaxY7tLkfgJhl/2aKA5Nz0hSeq96gZtHVhQbi+WJCsuVWzGnCBKBoDQWH1Clm4f/LaWFzXTtIc4GRyZz5xzlW+VYo2sqTveBgRdBqoiEpVFTMUnHqUxzz1d6ea9p1+sJuctkWKltVAckHwfudemOed6BV1HbaAX12FmsmhUsoieWvyxDslq4P0SY7dH9ODhx8iVlKSgQCB1PnNjtMVtsKDrqA30YQB1VUXHxMRm8NJwXBM9lz9Ob4z+c5W2n9DzJQ2btyjFVQFAZlt7a189lz9Ozy35T7XCBEk6+YCYnssfp6yDWya5OgAAEFZc8oCqMdNBEw7U7Be6qHfvH2jJac9Uabdsi+qcvC/12bROFW4z66YjpUkzk1UpAGSM6H9aq2ujtbqqwYtqHq1f49drHq2vo/+9WttLs8td/+ELfdTqYa75BQAAVUOggN2s15Fa+P34X74O/8V8lW7cKEmKtmyheT89VK8c8gf1O6WNeh+8xut1G0Tq6eFWn1e4/vAfHqmSNX2Utyqi1r/jQBZAMJY+0Fcd3i0MLOBc/n/9tLNpbI9l4zr9Xm2reUZCRe5v8UWF6666uL6mRvrRiwGEXrRRI83/Zbc9jokB7ItAIcwiURWdeYyUuCpxdf8sLR4yQpJ0/LQbdMD6+HW2Ba2ztOTiEZIO0Oy+zye9jPnfHi1JemTDoXp35qnKeX9q0t8DACoz8rIRGpZ1gw7b3lWx/82tlfeMNmuqbcfHz+D6wxVP66y8or22SG6YUJm/HfKJHhm6Su/Mjl/DfcC4OYoVFtZqDQBQJ+Tm6uwTpiu/XpOgKwHqNCZlDCHLzpHVy1WkUUO9NfldZVs06JJ2W1daqCt7nKvSTZukOvBvE2BSxvC4Z8kMnVhPOmL85Wo/pOK/4idDJC9PikZVcHo3ffLnv6T0vWrirIHfk5s+O+gyEHJMyggAwavomJgzFEJo4UPHaN6QxyWpToUJktQiWl9vz/pI5517BQexADLW4Z/s1O8O/kwRjRPzIwMAgHTFUUxYRKI6bdZWDZm7Vq8PflTZFq1zYcIu2RbVtS+/q69u6Bt0KQBC5DeDL9UFi05P2etHehyhIXPXasjctbq3xThlW1RRq9v/G7725Xc1ZO5aLX7+6D2WLxx17O6x7PoqPu3YgKoEAABB4QyFDLbi3n7a2XV7/Ik5vdNkpHItW1JuoHVVxYUNtujfV87UxMb91PY3TA4GIPViM+dp7eN9dNDO5F5utasXNzmwUMMOXJtYWvM7NtSGCxtskbRFzXq/qjufv3j38l/3ekOXNvxmkrKuT92oQ5esVkkANQIAgOAQKGSYSF6eNlx4lCTpgos+2Ws27/JvE1ZXjWw3Qaef1lT6TdCVAAiLhi9P2uP5lsv6KJZlajKvQG5K+fMqWHaONl1yjJq8PVslxxymLYfU22P9vr04/QyuX6DBp/yt3HWlLqaOj85VCbOgAwAQOgQKGSDr4JZSNH75QvEhB+mz344IuKLkqZ+1U8WtDlbJmrWVbwwASRDJy1OkSWMpEtGrv43ftvHQD4ap6z2ty90+1qSRJj30pE5fcZU2/6hAk48u/xfvTFTsSjV9Z0xysco3BoB0Yqas1q1UsnoNE4UD+0GgkAGu/WSCBtcvCLqMlHir8/uaMbFIP+54fNClAAiJVTf01Mw7nkg8i9+2cckZT0tn7H+/D18MT5Cwy5uFTfV0l46SNgddCgAkVVbrVvrnlPc08IRBKslfFnQ5QJ1FoJCmdpzXW7969ClJUt/cUkl1c4JFAEgXp35RqL71F6pNdLx2BQnYv0H1v1aLxVv10LHfVukmQgUA6adkwLG6bsRrevqIw9T5sywNafaZJClH0yRF9fOPX9MOl62hH1+jLtdM3WPfFff005PDntDWWD39qWsPueKdAYwACBaBQppZMPI4tW63Xt2azNaJuy/TJUwAgOqKNmuqzc831vDGz6lJNE+ECVWXa9n6dr0SPVTH71YBABUpzY1oUP2v9dh7A/SjlqN1SNau/wfEj69758bnIMttVLTPviX1nU6sJxXENulPEavwPVb8rJ9iWVL7nzPRODIPRwDpIhLV2lv7acQpozWhxxsa2W5C0BUBQEawevU0occbiTABAJCpogcdpLW39ZNlxf+mWnryMfryuGz1mjxU43u8USZM2Ne32y/R+mv3vKV5s1lOA+Z8R9kW1apbjtXaH/aT63vUvu97zCadd96n+ur6+P7rbuqnaOdDZcd9Sxuu5jbpSG8ECmkgUq+e7Ogj9Oldf9RZefumowAABKmk6yGK1E+PW2ECCK9Yh4M1+a5HZbnxW6jnD87RkAvGqfVvoip2pfvdd2S7Cbr/x88o2v1wyeJnIzR6YZLs/ubKtWx98cMn9L+7ntCyc8oPp3938HT97q745cqDh49VQffmWtu3oTpevSCJIwRqH4FCGiju003/fvd55UVygi4lEKWq+BQyAECwohbRB689q4Izjgy6FACoVOled2z4xUGz9f5bf1e2VX4J8Vl5RXr3gxcVbdy44o1M8cDBvjl+da7MsayZJh6VowPemqyWj03U5v7rPUcA1C0ECqjTzl1wtn529OlBlwEAqMTrjz2ihY/2CboMAKiQmzZHF33rTMUKC1P2HpOGPqyRyz7RjxbN3L2s3eVLddTvbtTJ9Yr15NJP4rd8BzIEgUIdt+7GfurzxylBlxGYotIsZg4HkFKl677WiTcO18ydO4IuJa21iNbXnwaOUv5LPYIuBQDKFytV6caNKX2LJtE8HZLVQB2yvjl+jRUWqu1ry3XKTddr6K23q3R9amsAalOlgYKZPWNm68xsVpll95nZKjObkfg6p8y6n5jZIjObb2ZnpqrwsChs63R/iy+CLiMQFyw6XevebRd0GUCdQC9OHVe8Uwe8NVkXvPpDPb354KDLqZOe39pMHd8Zvs/XYWOv3GO7gXk7NOK455X/677K/3VfZbVrG0zBQArQh4OX1b6dlv6qrxQJ7g5nUYtowT2HK//XfbV9cO8Kt2saiSj/N30Vbd5MklSyYqUOeGuyDnhrMreXREapym0jR0n6s6TRey3/g3Pu92UXmFk3SZdK6i6ptaSPzKyLc5XMcoJy2dHdVdwqnA3nTxvba/nfD9PBI7m9DpAwSvTilOp016d65lv9NKzHG0GXUqf8c1s93TvhfHW5fvI+66KHddQvXu+2+3n/BvM14IBSLbhyhCTp2GU36OB/m0qWrai1eoEUGiX6cGCy2rfT6oHtNO2qR3TR/SfJFQX3n3LRd5+UJHVsOlydXip/mwaRXH3vnHGaPLKL9DXzJCBzVXqGgnPuv5I2VPH1Bkl6yTlX5JzLl7RIUsXRHfar1Yjlyj/z6aDLCMQ/v99fzUZ+GnQZQJ1BL0YQCmI79OORV6vL1VPLXV+6KF8Tj8rZ/XXjC8O1LfZNEP7Jzx7Vkqs40wyZgT4crIXXtdX0e59I6mtaabzPFcSSf8nb8pLtmnhUjkqWLE36awN1SU3mULjZzGYmTv9qkljWRlLZP0OsTCwDAKQGvRgpc+EF16rN7z6r8vYdfj5Z5155w+7n3/n+9Trkl1XfH0hT9OE01emuKbqo00ka0nNgpbeNBFC+6gYKIyR1ktRT0hpJD/u+gJkNN7OpZja1WEXVLCOzrb2urTr+65qgy6hVG0u36fTLrpLNXRx0KUA6oBcjJXb14sjMhVLM4yA7Vqrczxbo9CFX6vQhVyp7yny//YH0Qx9OZ7FSuaIila7foHMuGaZ3CvO8dn/5zMfl7v1aA74/bI+zs4AwqVag4Jz70jlX6pyLSRqpb07hWiWp7LmNbRPLynuNp5xzvZxzvbKVW50yMl7sf3PV8UWnju+FI1R4q7CB+v31TkX+O0OxHcy2DlSGXpx8JS+0VO/pFwddRuCK5RT5ZGa1enFs61ZFxs9QZPyMlN6aDagL6MO1p+3HO1N2TGw5OVp0ebY6Z3/ttV/v3GydeNAiZU+YpZhiu5eP2tJC5438UbLLBOqkagUKZtaqzNPzJe2a7fYdSZeaWa6ZdZTUWdK+szihyrI/mqZOz8Uq3zDNvVJwoG6fcIkO+cVEybmgywHSAr04+RqP/lTR55rpltXHBV1KYBYUF+rqxRdJLvP/3wPUFH249uw6Jo4ooi2Dj1a08YFJe23LydGCc59U1xy/MxQq8u/1R6rd/UwsjnCo9C4PZvaipJMlNTezlZJ+LulkM+spyUlaKuk6SXLOzTazVyTNkVQi6SZms605K4lpcXGBOmU3CLqUlFhZUqB7X79BnX/CJIxARejFtafRi5O08IsjpA+mBF1KrdtYuk0/XT5IxSevCboUoM6hDwfPSmJaU7pTE//wpM64+ErZhBk1f82sLEWaNal8wyraWLpNX21voKwqz98JpLdKAwXn3GXlLK7w1gPOuQckPVCTorCnyPgZuvnw0/TqorFqEKkXdDlJd8U1t6njB4QJwP7Qi1EbjnvldnW6Y1LQZQB1En04eGWPiZNlxxlHa+xfR0qKJuX1+oy6Qx1+xnEtwqMmd3lALXJcBgAAtcbNX6JzTr9EK0sKgi4FAFBGbMcODTn9ckWmzg26FEnSHc0+142zvsjIP/oBVVHpGQpAKozdHtFtj14vSWoze6lKAq4HAMqKHtxCy++PqmkkJ+hSAAB7KZ270HufgouP15bvbt1necemK8rZuuoaROrpzLzN6vH7WxUpkTpMJohGuBAoIBDzilqp5WPxyWoIEwDURfVzuQUYAGSCHef11vqLt2ne8S+k5PWLXalaPz5NrojbfiJ8uOQhjXy8vWlG3ON2TUmBpmzpGHQZAFChkhUrdeDAxfo6A3ouAIRdwztWaF7/vyf9dZeXFGjM9qg+2t5cinF5MsKJQCFNuKIiPd65ix7f1DXoUmrshLG3aGUfTgcDAABA+jrpvdv1UKdvaUTnw+SKCaARTlzyAABASI3a0kKvnnLM7uedt3yhWID1AACA9EKgkGb+cfcAvX3zURrf442gSwEApLEz556rHX9srXprJgddCgCk1PZftVbflteXu27jEaZ514yo5YqAzEGgkGbq/WOyFnznOKlH0JVU3ZqSAp0w9pbdz5uMzw2wGgDAmXPP1dcvt1Pzd7lXOoDMlzVmmhpVsK7xkUfosMOu1PyTnlHU9n81eH5xgQaMu0UzT31C5865VAdNiia/WCDNECikoeyvs/TkpjbKtlINO3Bt0OVUak7xgep8xedBlwEgpKLdD5dtLlDJylVBl1JnbHm6rZq/QJgAALFZ89T5uoaKzXOqKB74YFu2luxsoY83HKHOQ6frwenHKfJQMzX5iD4KMCljGur400/1ZreD9Pq3uwddSqVKXUzFjtwKQHC6jF6s+T9sJ0X4S1KRK1aRK5aYjBxAhrGsLFlWlmRWrf2LXHGF63764DV6s9tB2tx/veScpvSMKvujadUtFcgoBApIqf4zL9ajPY4NugwAITb/hCy5qNRyQv2gSwnU2O0RDe5ysgZ3OVmNXp4SdDkAkDTRZk01On+cnssfp3U39vXeP7Z1qy4+fIBGb2meguqAzMafjtNYbNNmnXLVNZKk+j9ZpX90+VfAFe2p0yvXq8uoLYpt2xZ0KQBCLLZjhw5/4ivNmt9dR02apdV9CyS3/z/RZx3aQUe8ulytogfUUpWpNWTJAG24t72ihVx+BiDzxDZv0SXX3CpJ2vzdHTr16ohmbWotDVhZ9dcoLFQs8bfW61b21YKffXMmcMuZ+SpJbslAxuAMhTTmSkqU8/5U5bw/Vcs2NpEUnyzmsBev17rSwkBr6/jeNTr0tR2KzZgTaB0AIEmlCxbr4DHrNH7skVXaPpZXTw+3+lzZlv6XSfSfeYGWjOqi6NiahQnRRo206I99tOiPfaQ+aTQzMICMV/aYuH6jHXq41ef6UYf4H9ryH+wrO7pqlwk/9PeL9NMve2jJ1ua7Xy/n/akqWVP35ywDgsIZChli++JGurnt8Zq7uaU63TFJ1/S6UL9t/4a65uTVWg0Ligv12LpTJUndHvhKJfnLau29AaAypQsW69C7FwddRq26d923tGHCwTpk+uYaT5tgDRto8ZAnJUm9Zt2gZpNqXh8AJFvZY+IsLdcRffO19KtD1bTVccouLFFk3PQK9233wES9nneCdjYrVRetqMWqgfRFoJAhOt05SQslZWm5JKnopLW6a+yFtXIZxNelhYpJum3JxSo9ZXViKWECgPRlsZjWlRaqRTT95l0odqXaGNuhFtH6mnrNUcr92QZt7lesxkMa7t4mVrhNipV6v/aus9/Mf1cAqBXlHRMfMtb0jzv+pSc3tdGb3Q7a7/4d7qnenRsiDRsqVlD5JXVApiFQQI1d9t2bFZkwU3Jrgi4FAJKidM4CXdHxJD21ZKwOyWoQdDleLl96urZckKX3pn+gd99+dvdlG8XzvkkBTrzzJjV60e8Ug5JVq3V5+xMlSU1j3CoNAHax3Fy9MOd9XXLpjbIJM4IuB6hVBAoZLHbtATqr0fckSS4a0Uuv/0VNojW7BKLnb27UwRM377Esa/Zcxarxly4AqGsWjjpW9xz/T43Z0FXr+2/avbzLf6/Qge/X15QHRgRX3F521fSfX/1BF140XFYc78ORrTuk0k2StMccEGUf/+JXT+vGY4ep052e1y3Q6wGkoV3HxJEdJVJkkU6buUlNo4V6+LkL1O6BiTV+fVdUpMu+c62ic+cploR6gXRCoJDBShcu+eaJmU78052675rndGGDLZXu22XUDcreuu99fDv8c7VKlizdYxkndgHIBAue7K3sNVE9MvoC5WyVWriJGvjYjxTLlmLNY8oasi7oEiVJXZ+6UZEiqc30YuXNWq4+LW5Xm8mfySV+2Y8d9y0tvX//4fH1Ey5Xu7Ec9gIIh13HxKWSZKbRo8/Ufdc8p8EXjdfreSdU+zKHstz02RwTI5QIFMLCObV+aKLu7HyJnjq08ksTOj+2RCVrv9xnObfMAZCpGrQsULN36iv3vSm7l7X+ffwvV+uv6asj+6d2lu8rlp2oL7c12u82MZk6PPyFYlu3Sor35Da/XbXHNgXt8zSv/1/2+zot389RvX8wqyKAECpzTNzl0DXa2Ywzr4CaIFAImS7XTql8IxEcAAif1udXfJvbZn/9VMvm9tSM5z5Wz9zclLz/uhvbSdNn73ebiFTp6bSRYqeZO3eoR069pNUGAJlm1zFxF60MuBIgvUWCLgAAgHRgE2boJ11PUkFsR9Cl7NcBb0/W3b0GBl0GAAAIAc5QAACgCly/o3Tz6Jc15NTvaeBbk3VT4/g9yvOLC3Tzyd/TDz78t87KK5IkTdgR068HnK/f/+dFdc35Zj6DIles88/4vmxbOaHEivlJq7V0/QYNPGHQ7ufLLmmjWbc8IUk6a9DlavzFDCYOAwAANUagAABAFWQtXqP7fn2Vmi6cpBd+fo7eun61Puz6rlpGc7RpRFQ//tMw3VsYn5Ira7vTgfmTVJw4EfA7C8/SwjGHasZ1j8otXanSwsLUFuucSvKX7X6as6m1Zu/crqH3367mMz9XrKgote8PAABCgUABAIAqKP1ynZo+E7/TQ4NXP9NXzfqq22nf372+42tLVbJqtSJHHqHVpzfVttv7qWlkfHz7rCJFSqQe44epY0nyzkSoqsZLinXuRz9Ql79+yizkAAAgaQgUAACohlZv50vv56pk6XJFuh+uWJNGijRppGXnN9WcG55IbNVAkvRA23f143Mi2tx/fSC/0Gd/MFVdPgjgjQEAQEYjUAAAoBrm3NdOx3VfooLBTfXu+y/sXh61fec7HjDuFnW+4vPaLA8AACDlCBQAAKiGI344W1ujUcW2btV5Rw7YvXzVlV31v7ue2GPbmac+oQdnHKcpPaO1XSYAAEDKECgAAFANsW3bdj8u3bhx9+Po9m8uajj+7hvU/9bPNG39IYo81EzZmlarNQIAAKTSvudlAgCAams2e4c6jblKkvT10U6H563V8rVNlf0RYQIAAMgsnKEAAEASRT6ZriOWttUv/tFNV5z6X63Z2VhZq3KDLgsAACDpCBQAAEiykhUrNfGonN3PO+rTAKsBAABIDS55AAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgLdKAwUza2dm/zGzOWY228xuTSxvamYfmtnCxPcmieVmZo+Z2SIzm2lmx6R6EACQ6ejFABAs+jAA7KsqZyiUSLrDOddNUh9JN5lZN0l3SxrjnOssaUziuSSdLalz4mu4pBFJrxoAwodeDADBog8DwF4qDRScc2ucc58nHm+VNFdSG0mDJD2b2OxZSYMTjwdJGu3iJklqbGatkl04AIQJvRgAgkUfBoB9ec2hYGYdJB0t6TNJLZ1zaxKr1kpqmXjcRtKKMrutTCzb+7WGm9lUM5tarCLfugEgtOjFABAs+jAAxFU5UDCzBpJel3Sbc25L2XXOOSfJ+byxc+4p51wv51yvbOX67AoAoUUvBoBg0YcB4BtVChTMLFvxxvm8c+6NxOIvd522lfi+LrF8laR2ZXZvm1gGAKgBejEABIs+DAB7qspdHkzS05LmOuceKbPqHUlDE4+HSnq7zPIrEjPb9pG0ucxpYACAaqAXA0Cw6MMAsK+sKmxzgqTLJX1hZjMSy34q6UFJr5jZMEnLJA1JrHtP0jmSFknaJumqZBYMACFFLwaAYNGHAWAvlQYKzrnxkqyC1QPK2d5JuqmGdQEAyqAXA0Cw6MMAsC+vuzwAAAAAAABIBAoAAAAAAKAaCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDcCBQAAAAAA4I1AAQAAAAAAeCNQAAAAAAAA3ggUAAAAAACANwIFAAAAAADgjUABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN4IFAAAAAAAgDdzzgVdg8zsK0mFkr4OupZa1lzhG7MUznGHccxSZoy7vXPuoKCLqA1mtlXS/KDrCEAm/Dv1FcYxS+EcdyaMOUx9mGPicAnjuMM4Zikzxl1uL64TgYIkmdlU51yvoOuoTWEcsxTOcYdxzFJ4x52uwvp5hXHcYRyzFM5xh3HM6S6Mn1kYxyyFc9xhHLOU2ePmkgcAAAAAAOCNQAEAAAAAAHirS4HCU0EXEIAwjlkK57jDOGYpvONOV2H9vMI47jCOWQrnuMM45nQXxs8sjGOWwjnuMI5ZyuBx15k5FAAAAAAAQPqoS2coAAAAAACANEGgAAAAAAAAvAUeKJjZWWY238wWmdndQdeTSma21My+MLMZZjY1saypmX1oZgsT35sEXWdNmdkzZrbOzGaVWVbuOC3uscTnP9PMjgmu8uqrYMz3mdmqxOc9w8zOKbPuJ4kxzzezM4OpumbMrJ2Z/cfM5pjZbDO7NbE8oz/rTBWWXkwfzuyfTXoxvTidhaUPS/TiTP7ZpA+Hrw8HGiiYWVTS45LOltRN0mVm1i3ImmrBKc65nmXuQ3q3pDHOuc6SxiSep7tRks7aa1lF4zxbUufE13BJI2qpxmQbpX3HLEl/SHzePZ1z70lS4t/4pZK6J/Z5IvGzkG5KJN3hnOsmqY+kmxJjy/TPOuOEsBfThzP3Z3OU6MX04jQUwj4s0Ysz9WdzlOjDoerDQZ+h0FvSIufcEufcTkkvSRoUcE21bZCkZxOPn5U0OLhSksM5919JG/ZaXNE4B0ka7eImSWpsZq1qpdAkqmDMFRkk6SXnXJFzLl/SIsV/FtKKc26Nc+7zxOOtkuZKaqMM/6wzVNh7MX04Q3426cX04jQW9j4s0Ysz4meTPhy+Phx0oNBG0ooyz1cmlmUqJ+kDM5tmZsMTy1o659YkHq+V1DKY0lKuonFm+r+BmxOnMj1T5tS9jBuzmXWQdLSkzxTezzqdhemzoQ/Hhe1nk14crs87HYXtc6EXx4XpZ5M+nKGfddCBQtj0d84do/hpLjeZ2YllV7r4PTwz/j6eYRmn4qcvdZLUU9IaSQ8HWk2KmFkDSa9Lus05t6XsuhB91kgf9GGFZ5wJ9OJwfd5ID/RihWecog9n9GcddKCwSlK7Ms/bJpZlJOfcqsT3dZLeVPyUni93neKS+L4uuApTqqJxZuy/Aefcl865UudcTNJIfXMKV8aM2cyyFW+czzvn3kgsDt1nnQFC89nQh8P3s0kvDtfnncZC9bnQi8P1s0kfzuzPOuhAYYqkzmbW0cxyFJ+U452Aa0oJM6tvZg13PZZ0hqRZio93aGKzoZLeDqbClKtonO9IuiIx22kfSZvLnBqU1va6Fup8xT9vKT7mS80s18w6Kj4hy+Tarq+mzMwkPS1prnPukTKrQvdZZ4BQ9GL6cDh/NunFkkL0eaexUPRhiV6sEP5s0oclZfJn7ZwL9EvSOZIWSFos6Z6g60nhOA+V9L/E1+xdY5XUTPFZPxdK+khS06BrTcJYX1T8dKZixa8JGlbROCWZ4rMaL5b0haReQdefxDH/PTGmmYo3jlZltr8nMeb5ks4Ouv5qjrm/4qduzZQ0I/F1TqZ/1pn6FYZeTB/O/J9NejG9OJ2/wtCHE+OkF2fwzyZ9OHx92BKDAgAAAAAAqLKgL3kAAAAAAABpiEABAAAAAAB4I1AAAAAAAADeCBQAAAAAAIA3AgUAAAAAAOCNQAEAAAAAAHgjUAAAAAAAAN7+H8TTN0rSZi/dAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "with torch.no_grad():\n", "    # select one image to evaluate and visualize the model output\n", "    val_input = val_ds[6][\"image\"].unsqueeze(0).to(device)\n", "    roi_size = (128, 128, 64)\n", "    sw_batch_size = 4\n", "    val_output = inference(val_input)\n", "    val_output = post_trans(val_output[0])\n", "    plt.figure(\"image\", (24, 6))\n", "    for i in range(4):\n", "        plt.subplot(1, 4, i + 1)\n", "        plt.title(f\"image channel {i}\")\n", "        plt.imshow(val_ds[6][\"image\"][i, :, :, 70].detach().cpu(), cmap=\"gray\")\n", "    plt.show()\n", "    # visualize the 3 channels label corresponding to this image\n", "    plt.figure(\"label\", (18, 6))\n", "    for i in range(3):\n", "        plt.subplot(1, 3, i + 1)\n", "        plt.title(f\"label channel {i}\")\n", "        plt.imshow(val_ds[6][\"label\"][i, :, :, 70].detach().cpu())\n", "    plt.show()\n", "    # visualize the 3 channels model output corresponding to this image\n", "    plt.figure(\"output\", (18, 6))\n", "    for i in range(3):\n", "        plt.subplot(1, 3, i + 1)\n", "        plt.title(f\"output channel {i}\")\n", "        plt.imshow(val_output[i, :, :, 70].detach().cpu())\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation on original image spacings"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["val_org_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\"]),\n", "        ConvertToMultiChannelBasedOnBratsClassesd(keys=\"label\"),\n", "        Orientationd(keys=[\"image\"], axcodes=\"RAS\"),\n", "        Spacingd(keys=[\"image\"], pixdim=(1.0, 1.0, 1.0), mode=\"bilinear\"),\n", "        NormalizeIntensityd(keys=\"image\", nonzero=True, channel_wise=True),\n", "    ]\n", ")\n", "\n", "val_org_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    transform=val_org_transforms,\n", "    section=\"validation\",\n", "    download=False,\n", "    num_workers=4,\n", "    cache_num=0,\n", ")\n", "val_org_loader = DataLoader(val_org_ds, batch_size=1, shuffle=False, num_workers=4)\n", "\n", "post_transforms = Compose(\n", "    [\n", "        Invertd(\n", "            keys=\"pred\",\n", "            transform=val_org_transforms,\n", "            orig_keys=\"image\",\n", "            meta_keys=\"pred_meta_dict\",\n", "            orig_meta_keys=\"image_meta_dict\",\n", "            meta_key_postfix=\"meta_dict\",\n", "            nearest_interp=False,\n", "            to_tensor=True,\n", "            device=\"cpu\",\n", "        ),\n", "        Activationsd(keys=\"pred\", sigmoid=True),\n", "        AsDiscreted(keys=\"pred\", threshold=0.5),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metric on original image spacing:  0.7912478446960449\n", "metric_tc: 0.8422\n", "metric_wt: 0.9129\n", "metric_et: 0.6187\n"]}], "source": ["model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "\n", "with torch.no_grad():\n", "    for val_data in val_org_loader:\n", "        val_inputs = val_data[\"image\"].to(device)\n", "        val_data[\"pred\"] = inference(val_inputs)\n", "        val_data = [post_transforms(i) for i in decollate_batch(val_data)]\n", "        val_outputs, val_labels = from_engine([\"pred\", \"label\"])(val_data)\n", "        dice_metric(y_pred=val_outputs, y=val_labels)\n", "        dice_metric_batch(y_pred=val_outputs, y=val_labels)\n", "\n", "    metric_org = dice_metric.aggregate().item()\n", "    metric_batch_org = dice_metric_batch.aggregate()\n", "\n", "    dice_metric.reset()\n", "    dice_metric_batch.reset()\n", "\n", "metric_tc, metric_wt, metric_et = metric_batch_org[0].item(), metric_batch_org[1].item(), metric_batch_org[2].item()\n", "\n", "print(\"Metric on original image spacing: \", metric_org)\n", "print(f\"metric_tc: {metric_tc:.4f}\")\n", "print(f\"metric_wt: {metric_wt:.4f}\")\n", "print(f\"metric_et: {metric_et:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Convert torch to onnx model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dummy_input = torch.randn(1, 4, 240, 240, 160).to(device)\n", "onnx_path = os.path.join(root_dir, \"best_metric_model.onnx\")\n", "torch.onnx.export(model, dummy_input, onnx_path, verbose=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inference onnx model\n", "Here we change the model used by predictor to onnx_infer, both of which are used to obtain a tensor after the input has been reasoned by the neural network.\n", "\n", "Note: If the warning `pthread_setaffinity_np failed` appears when executing this cell, this is a known problem with the onnxruntime and does not affect the execution result. If you want to disable the warning, you can cancel the following comment to solve the problem."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using the following program snippet will not affect the execution time.\n", "# options = ort.SessionOptions()\n", "# options.intra_op_num_threads = 1\n", "# options.inter_op_num_threads = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def onnx_infer(inputs):\n", "    ort_inputs = {ort_session.get_inputs()[0].name: inputs.cpu().numpy()}\n", "    ort_outs = ort_session.run(None, ort_inputs)\n", "    return torch.Tensor(ort_outs[0]).to(inputs.device)\n", "\n", "\n", "def predict(input):\n", "    def _compute(input):\n", "        return sliding_window_inference(\n", "            inputs=input,\n", "            roi_size=(240, 240, 160),\n", "            sw_batch_size=1,\n", "            predictor=onnx_infer,\n", "            overlap=0.5,\n", "        )\n", "\n", "    if VAL_AMP:\n", "        with torch.cuda.amp.autocast():\n", "            return _compute(input)\n", "    else:\n", "        return _compute(input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["onnx_model_path = os.path.join(root_dir, \"best_metric_model.onnx\")\n", "ort_session = onnxruntime.InferenceSession(onnx_model_path)\n", "\n", "for val_data in tqdm(val_loader, desc=\"Onnxruntime Inference Progress\"):\n", "    val_inputs, val_labels = (\n", "        val_data[\"image\"].to(device),\n", "        val_data[\"label\"].to(device),\n", "    )\n", "\n", "    ort_outs = predict(val_inputs)\n", "    val_outputs = post_trans(torch.Tensor(ort_outs[0]).to(device)).unsqueeze(0)\n", "\n", "    dice_metric(y_pred=val_outputs, y=val_labels)\n", "    dice_metric_batch(y_pred=val_outputs, y=val_labels)\n", "onnx_metric = dice_metric.aggregate().item()\n", "onnx_metric_batch = dice_metric_batch.aggregate()\n", "onnx_metric_tc = onnx_metric_batch[0].item()\n", "onnx_metric_wt = onnx_metric_batch[1].item()\n", "onnx_metric_et = onnx_metric_batch[2].item()\n", "\n", "print(f\"onnx metric: {onnx_metric}\")\n", "print(f\"onnx_metric_tc: {onnx_metric_tc:.4f}\")\n", "print(f\"onnx_metric_wt: {onnx_metric_wt:.4f}\")\n", "print(f\"onnx_metric_et: {onnx_metric_et:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check best onnx model output with the input image and label"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["onnx_model_path = os.path.join(root_dir, \"best_metric_model.onnx\")\n", "ort_session = onnxruntime.InferenceSession(onnx_model_path)\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "\n", "with torch.no_grad():\n", "    # select one image to evaluate and visualize the model output\n", "    val_input = val_ds[6][\"image\"].unsqueeze(0).to(device)\n", "    val_output = inference(val_input)\n", "    val_output = post_trans(val_output[0])\n", "    ort_output = predict(val_input)\n", "    ort_output = post_trans(torch.Tensor(ort_output[0]).to(device)).unsqueeze(0)\n", "    plt.figure(\"image\", (24, 6))\n", "    for i in range(4):\n", "        plt.subplot(1, 4, i + 1)\n", "        plt.title(f\"image channel {i}\")\n", "        plt.imshow(val_ds[6][\"image\"][i, :, :, 70].detach().cpu(), cmap=\"gray\")\n", "    plt.show()\n", "    # visualize the 3 channels label corresponding to this image\n", "    plt.figure(\"label\", (18, 6))\n", "    for i in range(3):\n", "        plt.subplot(1, 3, i + 1)\n", "        plt.title(f\"label channel {i}\")\n", "        plt.imshow(val_ds[6][\"label\"][i, :, :, 70].detach().cpu())\n", "    plt.show()\n", "    # visualize the 3 channels model output corresponding to this image\n", "    plt.figure(\"output\", (18, 6))\n", "    for i in range(3):\n", "        plt.subplot(1, 3, i + 1)\n", "        plt.title(f\"pth output channel {i}\")\n", "        plt.imshow(val_output[i, :, :, 70].detach().cpu())\n", "    plt.show()\n", "    plt.figure(\"output\", (18, 6))\n", "    for i in range(3):\n", "        plt.subplot(1, 3, i + 1)\n", "        plt.title(f\"onnx output channel {i}\")\n", "        plt.imshow(ort_output[0, i, :, :, 70].detach().cpu())\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}