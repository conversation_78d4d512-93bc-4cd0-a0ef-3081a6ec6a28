#%%
import os
import pandas as pd
import shutil

def replace_pinyin_with_chinese_names(file_path, excel_path, name_column='姓名', pinyin_column='拼音'):
    """
    根据Excel表格中的拼音姓名对照，将文件名中的拼音替换为中文名字
    
    参数:
    file_path: 文件所在路径
    excel_path: Excel表格路径
    name_column: Excel中中文姓名列的列名
    pinyin_column: Excel中拼音列的列名（如果有的话）
    """
    
    # 检查路径是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件路径不存在 - {file_path}")
        return
    
    if not os.path.exists(excel_path):
        print(f"错误：Excel文件不存在 - {excel_path}")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        print(f"Excel文件列名: {list(df.columns)}")
        
        # 检查是否有拼音列，如果没有则从中文名生成拼音
        if pinyin_column not in df.columns:
            print(f"未找到拼音列 '{pinyin_column}'，将从中文名生成拼音...")
            try:
                from pypinyin import lazy_pinyin
                df[pinyin_column] = df[name_column].apply(lambda x: ''.join(lazy_pinyin(x)) if pd.notna(x) else '')
            except ImportError:
                print("需要安装pypinyin库: pip install pypinyin")
                return
        
        # 创建拼音到中文名的映射字典
        pinyin_to_chinese = {}
        for _, row in df.iterrows():
            if pd.notna(row[name_column]) and pd.notna(row[pinyin_column]):
                chinese_name = str(row[name_column]).strip()
                pinyin_name = str(row[pinyin_column]).strip().lower()
                pinyin_to_chinese[pinyin_name] = chinese_name
        
        print(f"创建了 {len(pinyin_to_chinese)} 个拼音-中文名映射")
        
        # 获取文件夹中的所有文件
        files = os.listdir(file_path)
        renamed_count = 0
        
        for file_name in files:
            if os.path.isfile(os.path.join(file_path, file_name)):
                # 提取文件名中第一个"-"前面的部分
                if '-' in file_name:
                    name_prefix = file_name.split('-')[0].lower()
                    
                    # 检查是否在映射字典中
                    if name_prefix in pinyin_to_chinese:
                        chinese_name = pinyin_to_chinese[name_prefix]
                        
                        # 构建新文件名
                        file_parts = file_name.split('-')
                        file_parts[0] = chinese_name
                        new_file_name = '-'.join(file_parts)
                        
                        # 重命名文件
                        old_file_path = os.path.join(file_path, file_name)
                        new_file_path = os.path.join(file_path, new_file_name)
                        
                        # 检查新文件名是否已存在
                        if not os.path.exists(new_file_path):
                            try:
                                os.rename(old_file_path, new_file_path)
                                print(f"重命名: {file_name} -> {new_file_name}")
                                renamed_count += 1
                            except Exception as e:
                                print(f"重命名失败 {file_name}: {e}")
                        else:
                            print(f"目标文件已存在，跳过: {new_file_name}")
                    else:
                        print(f"未找到匹配的中文名: {name_prefix}")
        
        print(f"\n完成！共重命名了 {renamed_count} 个文件")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")

def batch_replace_multiple_folders(base_path, excel_path, subfolders=None):
    """
    批量处理多个子文件夹
    
    参数:
    base_path: 基础路径
    excel_path: Excel表格路径
    subfolders: 子文件夹列表，如果为None则处理所有子文件夹
    """
    
    if subfolders is None:
        # 获取所有子文件夹
        subfolders = [f for f in os.listdir(base_path) 
                     if os.path.isdir(os.path.join(base_path, f))]
    
    for subfolder in subfolders:
        folder_path = os.path.join(base_path, subfolder)
        if os.path.exists(folder_path):
            print(f"\n处理文件夹: {subfolder}")
            replace_pinyin_with_chinese_names(folder_path, excel_path)
        else:
            print(f"文件夹不存在: {folder_path}")

# 使用示例
if __name__ == "__main__":
    # 方式1: 处理单个文件夹
    file_path = r"K:\肝脏MRI数据集\HCC新增待整理\HCC补-吴凯莹\nii_output"
    excel_path = r"K:\肝脏MRI数据集\HCC新增待整理\需要补充的图像资料-吴.xlsx"  # 请替换为实际的Excel文件路径
    # excel_path =  r"M:\​新建文件夹\HCC数据集\HCC新增待整理\HCC2024-2025\2024.8-2025.5.30HCC临床资料.xlsx"


    # 如果Excel中有现成的拼音列，请指定列名
    replace_pinyin_with_chinese_names(
        file_path=file_path,
        excel_path=excel_path,
        name_column='姓名',  # Excel中中文姓名列的列名
        pinyin_column='拼音'  # Excel中拼音列的列名，如果没有会自动生成
    )
    
    # 方式2: 批量处理多个序列文件夹（如ap, pp, hbp等）
    # base_path = r"M:\新建文件夹\HCC数据集\HCC新增待整理\小吴20HCC\hcc20\image"
    # excel_path = r"M:\新建文件夹\HCC数据集\HCC新增待整理\小吴20HCC\姓名拼音对照表.xlsx"
    # subfolders = ['ap', 'pp', 'hbp']  # 指定要处理的子文件夹
    # 
    # batch_replace_multiple_folders(base_path, excel_path, subfolders)

# %%
