{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(-114.775390625, -305.775390625, -262.75)\n", "(0.44921875, 0.44921875, 10.0)\n", "(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0)\n", "(-149.70703125, -295.70703125, -372.25)\n", "(0.5859375, 0.5859375, 10.0)\n", "(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0)\n", "(-117.2705078125, -301.7705078125, 226.25)\n", "(0.458984375, 0.458984375, 8.0)\n", "(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0)\n", "(-117.2705078125, -263.2705078125, 150.75)\n", "(0.458984375, 0.458984375, 8.0)\n", "(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0)\n", "(-114.775390625, -248.275390625, -244.75)\n", "(0.44921875, 0.44921875, 10.0)\n", "(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0)\n"]}], "source": ["import SimpleITK as sitk\n", "import os\n", "\n", "def dcm2nii(dcms_path, nii_path):\n", "\t# 1.构建dicom序列文件阅读器，并执行（即将dicom序列文件“打包整合”）\n", "    reader = sitk.ImageSeriesReader()\n", "    dicom_names = reader.GetGDCMSeriesFileNames(dcms_path)\n", "    reader.SetFileNames(dicom_names)\n", "    image2 = reader.Execute()\n", "\t# 2.将整合后的数据转为array，并获取dicom文件基本信息\n", "    image_array = sitk.GetArrayFromImage(image2)  # z, y, x\n", "    origin = image2.Get<PERSON><PERSON>in()  # x, y, z\n", "    print(origin)\n", "    spacing = image2.GetSpacing()  # x, y, z\n", "    print(spacing)\n", "    direction = image2.GetDirection()  # x, y, z\n", "    print(direction)\n", "\n", "    # 3.将array转为img，并保存为.nii.gz\n", "    image3 = sitk.GetImageFromArray(image_array)\n", "    image3.SetSpacing(spacing)\n", "    image3.SetDirection(direction)\n", "    image3.<PERSON><PERSON><PERSON><PERSON>(origin)\n", "    sitk.WriteImage(image3, nii_path)\n", "\n", "\n", "if __name__ == '__main__':\n", "    dcms_folder = r'F:\\sth\\23Fall\\fcpro\\brain_image2\\image_dcm'  # dicom序列文件所在路径\n", "    nii_folder = r'F:\\sth\\23Fall\\fcpro\\brain_image2\\image'  # 所需.nii.gz文件保存路径\n", "    # 遍历文件夹下所有文件\n", "    for files in os.listdir(dcms_folder):\n", "        # print(files)\n", "        dcm2nii(os.path.join(dcms_folder, files), os.path.join(nii_folder, files + '.nii.gz'))\n", "\n", "\n", "\n", "\n", "    # 逐个将文件夹内dicom序列文件整合为一个.nii.gz文件\n", "\n", "\n", "    # dcm2nii(dcms_path, nii_path)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["mask_folder = r'F:\\sth\\23Fall\\fcpro\\brain_image2\\mask'\n", "for mask in os.listdir(mask_folder):\n", "    file_path = os.path.join(mask_folder, mask)\n", "    if '_0000.nii.gz' in mask:\n", "        new_file_name = mask.replace('_0000.nii.gz', '.nii.gz')\n", "        \n", "        # 获取新的文件路径\n", "        new_file_path = os.path.join(mask_folder, new_file_name)\n", "        os.rename(file_path, new_file_path)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import SimpleITK as sitk\n", "import os\n", "\n", "num = 333\n", "nii_folder = r'F:\\sth\\23Fall\\fcpro\\brain_image5\\image'\n", "mask_folder = r'F:\\sth\\23Fall\\fcpro\\brain_image5\\mask'\n", "for mask in os.listdir(mask_folder):\n", "    # 重命名\n", "    if len(mask) < 17:\n", "        mask_image_path = os.path.join(mask_folder, mask)\n", "        mask_image = sitk.ReadImage(mask_image_path)\n", "        mask_array = sitk.GetArrayFromImage(mask_image)\n", "\n", "        os.rename(os.path.join(nii_folder, mask), os.path.join(nii_folder, \"brain_\" + str(num) + '_0000.nii.gz'))\n", "        os.rename(os.path.join(mask_folder, mask), os.path.join(mask_folder, \"brain_\" + str(num) + '_0000.nii.gz'))\n", "        \n", "        \n", "        if len(np.unique(mask_array)) != 5:\n", "            print(mask_image_path[-15:-12])\n", "            print(np.unique(mask_array))\n", "        \n", "        num += 1\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "path = r'F:\\sth\\23Fall\\fcpro\\brain_image2\\labelsTr'\n", "for i in os.listdir(path):\n", "    if len(i) > 17:\n", "        os.rename(os.path.join(path, i), os.path.join(path, i[:9]+'.nii.gz'))"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}