# ==================== 火山图示例数据生成 ====================
# 生成符合真实RNA-seq差异表达分析的示例数据
# 包含基因名、fold change、p值等信息

# 清理环境
rm(list = ls())

# 设置随机种子
set.seed(42)

# ==================== 生成真实风格的基因数据 ====================

# 基因数量
n_genes <- 2000

# 生成基因名称（模拟真实基因名）
gene_symbols <- c(
  # 一些真实的基因名作为示例
  "TP53", "BRCA1", "BRCA2", "EGFR", "MYC", "RAS", "APC", "PTEN", 
  "PIK3CA", "KRAS", "BRAF", "IDH1", "IDH2", "CDKN2A", "RB1",
  "VHL", "MLH1", "MSH2", "MSH6", "PMS2", "ATM", "CHEK2",
  "PALB2", "CDH1", "STK11", "SMAD4", "DPC4", "FHIT", "WWOX",
  "PARK2", "LATS1", "LATS2", "NF1", "NF2", "TSC1", "TSC2",
  paste0("ENSG", sprintf("%011d", sample(1:99999999999, n_genes - 36)))
)

# 确保基因名唯一
gene_symbols <- make.unique(gene_symbols[1:n_genes])

# ==================== 生成表达数据 ====================

# 基础表达值（模拟正常分布）
base_expression <- rnorm(n_genes, mean = 8, sd = 2)

# 处理组表达值
treatment_expression <- base_expression + rnorm(n_genes, mean = 0, sd = 0.5)

# 为一些基因添加真实的差异表达
# 上调基因（约5%）
n_upregulated <- round(n_genes * 0.05)
upregulated_indices <- sample(1:n_genes, n_upregulated)
treatment_expression[upregulated_indices] <- treatment_expression[upregulated_indices] + 
  runif(n_upregulated, 1.5, 4)

# 下调基因（约5%）
n_downregulated <- round(n_genes * 0.05)
downregulated_indices <- sample(setdiff(1:n_genes, upregulated_indices), n_downregulated)
treatment_expression[downregulated_indices] <- treatment_expression[downregulated_indices] - 
  runif(n_downregulated, 1.5, 4)

# 计算fold change
fold_change <- treatment_expression - base_expression
log2_fold_change <- fold_change / log(2)

# ==================== 生成统计检验结果 ====================

# 模拟t检验的t统计量
t_statistic <- log2_fold_change / (runif(n_genes, 0.1, 0.5))

# 计算p值（基于t分布）
df <- 6  # 自由度（假设每组4个样本）
p_values <- 2 * pt(-abs(t_statistic), df)

# 确保p值在合理范围内
p_values <- pmax(p_values, 1e-10)
p_values <- pmin(p_values, 0.99)

# 计算调整后的p值（FDR校正）
adjusted_p_values <- p.adjust(p_values, method = "fdr")

# ==================== 生成其他统计信息 ====================

# 平均表达量
average_expression <- (base_expression + treatment_expression) / 2

# 标准误
standard_error <- runif(n_genes, 0.1, 0.8)

# 置信区间
ci_lower <- log2_fold_change - 1.96 * standard_error
ci_upper <- log2_fold_change + 1.96 * standard_error

# ==================== 创建完整数据框 ====================

volcano_dataset <- data.frame(
  Gene_Symbol = gene_symbols,
  Gene_ID = paste0("ENSG", sprintf("%011d", 1:n_genes)),
  Base_Expression = round(base_expression, 3),
  Treatment_Expression = round(treatment_expression, 3),
  Log2_Fold_Change = round(log2_fold_change, 4),
  Fold_Change = round(2^log2_fold_change, 4),
  P_Value = format(p_values, scientific = TRUE, digits = 4),
  Adjusted_P_Value = format(adjusted_p_values, scientific = TRUE, digits = 4),
  T_Statistic = round(t_statistic, 4),
  Standard_Error = round(standard_error, 4),
  CI_Lower = round(ci_lower, 4),
  CI_Upper = round(ci_upper, 4),
  Average_Expression = round(average_expression, 3),
  stringsAsFactors = FALSE
)

# 转换p值为数值型（用于后续分析）
volcano_dataset$P_Value_Numeric <- as.numeric(volcano_dataset$P_Value)
volcano_dataset$Adjusted_P_Value_Numeric <- as.numeric(volcano_dataset$Adjusted_P_Value)

# ==================== 添加分类标签 ====================

# 设置阈值
fc_threshold <- 1.0  # log2 fold change阈值
p_threshold <- 0.05  # p值阈值

# 分类基因
volcano_dataset$Regulation <- "Not Significant"
volcano_dataset$Regulation[volcano_dataset$Log2_Fold_Change > fc_threshold & 
                          volcano_dataset$P_Value_Numeric < p_threshold] <- "Up-regulated"
volcano_dataset$Regulation[volcano_dataset$Log2_Fold_Change < -fc_threshold & 
                          volcano_dataset$P_Value_Numeric < p_threshold] <- "Down-regulated"

# 添加显著性标记
volcano_dataset$Significant <- volcano_dataset$P_Value_Numeric < p_threshold
volcano_dataset$Highly_Significant <- volcano_dataset$P_Value_Numeric < 0.01

# ==================== 保存数据 ====================

# 创建输出目录
if (!dir.exists("volcano_data")) {
  dir.create("volcano_data")
}

# 保存完整数据集
write.csv(volcano_dataset, "volcano_data/complete_volcano_dataset.csv", row.names = FALSE)

# 保存简化版本（仅包含火山图必需的列）
simple_dataset <- volcano_dataset[, c("Gene_Symbol", "Log2_Fold_Change", 
                                     "P_Value_Numeric", "Regulation")]
colnames(simple_dataset) <- c("Gene", "log2FC", "p_value", "significance")
write.csv(simple_dataset, "volcano_data/simple_volcano_data.csv", row.names = FALSE)

# 保存显著差异基因
significant_genes <- volcano_dataset[volcano_dataset$Regulation != "Not Significant", ]
write.csv(significant_genes, "volcano_data/significant_genes.csv", row.names = FALSE)

# ==================== 数据摘要 ====================

cat("==================== 数据生成摘要 ====================\n")
cat("总基因数:", nrow(volcano_dataset), "\n")
cat("上调基因数:", sum(volcano_dataset$Regulation == "Up-regulated"), "\n")
cat("下调基因数:", sum(volcano_dataset$Regulation == "Down-regulated"), "\n")
cat("无显著差异:", sum(volcano_dataset$Regulation == "Not Significant"), "\n")
cat("显著基因比例:", round(mean(volcano_dataset$Significant) * 100, 2), "%\n")
cat("高度显著基因比例:", round(mean(volcano_dataset$Highly_Significant) * 100, 2), "%\n")

cat("\nLog2 Fold Change 统计:\n")
cat("范围:", round(range(volcano_dataset$Log2_Fold_Change), 3), "\n")
cat("均值:", round(mean(volcano_dataset$Log2_Fold_Change), 3), "\n")
cat("标准差:", round(sd(volcano_dataset$Log2_Fold_Change), 3), "\n")

cat("\nP值统计:\n")
cat("最小p值:", format(min(volcano_dataset$P_Value_Numeric), scientific = TRUE), "\n")
cat("最大p值:", format(max(volcano_dataset$P_Value_Numeric), scientific = TRUE), "\n")
cat("中位数p值:", format(median(volcano_dataset$P_Value_Numeric), scientific = TRUE), "\n")

# 显示前几个显著基因
cat("\n前10个最显著的上调基因:\n")
top_up <- volcano_dataset[volcano_dataset$Regulation == "Up-regulated", ]
top_up <- top_up[order(top_up$P_Value_Numeric), ]
print(head(top_up[, c("Gene_Symbol", "Log2_Fold_Change", "P_Value")], 10))

cat("\n前10个最显著的下调基因:\n")
top_down <- volcano_dataset[volcano_dataset$Regulation == "Down-regulated", ]
top_down <- top_down[order(top_down$P_Value_Numeric), ]
print(head(top_down[, c("Gene_Symbol", "Log2_Fold_Change", "P_Value")], 10))

cat("\n数据文件已保存到 volcano_data/ 目录\n")
cat("- complete_volcano_dataset.csv: 完整数据集\n")
cat("- simple_volcano_data.csv: 简化版火山图数据\n")
cat("- significant_genes.csv: 显著差异基因\n")

# ==================== 生成数据说明文档 ====================

readme_content <- "
# 火山图示例数据说明

## 数据文件描述

### 1. complete_volcano_dataset.csv
完整的差异表达分析数据集，包含以下列：
- Gene_Symbol: 基因符号
- Gene_ID: 基因ID (ENSEMBL格式)
- Base_Expression: 对照组表达量
- Treatment_Expression: 处理组表达量
- Log2_Fold_Change: log2转换的倍数变化
- Fold_Change: 倍数变化
- P_Value: p值
- Adjusted_P_Value: 校正后p值 (FDR)
- T_Statistic: t统计量
- Standard_Error: 标准误
- CI_Lower: 置信区间下限
- CI_Upper: 置信区间上限
- Average_Expression: 平均表达量
- Regulation: 调控类型 (Up-regulated/Down-regulated/Not Significant)
- Significant: 是否显著 (p < 0.05)
- Highly_Significant: 是否高度显著 (p < 0.01)

### 2. simple_volcano_data.csv
简化版火山图数据，包含绘制火山图的必需列：
- Gene: 基因名
- log2FC: log2 fold change
- p_value: p值
- significance: 显著性分类

### 3. significant_genes.csv
仅包含显著差异表达的基因

## 数据特点
- 总基因数: 2000
- 约5%基因上调，5%基因下调
- 模拟真实RNA-seq差异表达分析结果
- 包含统计检验的完整信息

## 使用方法
可以直接使用这些数据运行火山图绘制代码，或作为自己数据分析的参考格式。
"

writeLines(readme_content, "volcano_data/README.md")

cat("数据说明文档已生成: volcano_data/README.md\n")
cat("数据生成完成！\n")
