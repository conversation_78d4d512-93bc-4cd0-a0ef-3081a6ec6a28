{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Y57RMM1LEQmR"}, "source": ["#  <span style=\"color:orange\">Tutorial de classificação binária (CLF101) - Nível iniciante</span>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "GM-nQ7LqEQma"}, "source": ["**<PERSON><PERSON><PERSON> usando: PyCaret 2.3.10** <br />\n", "**Data de atualização versão em inglês: 11 de novembro de 2020**<br />\n", "**Data de atualização versão em português: 01 de junho de 2022**\n", "\n", "- __[Link do repositório pycaret no github](https://github.com/pycaret/pycaret)__\n", "- __[<PERSON> da versão em inglês no github](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Beginner%20-%20%20CLF101.ipynb)__\n", "\n", "\n", "# 1.0 Objetivo do tutorial\n", "Bem-vindo ao Tutorial de Classificação Binária (CLF101) - Nível Iniciante. Este tutorial assume que você é novo no PyCaret e quer começar com Classificação Binária usando o módulo `pycaret.classification`.\n", "\n", "Neste tutorial vamos aprender:\n", "\n", "\n", "* **Obtendo dados:** como importar dados do repositório PyCaret\n", "* **Configurando o ambiente:** como configurar um experimento no PyCaret e começar a criar modelos de classificação\n", "* **Criar modelo:** como criar um modelo, realizar validação cruzada estratificada e avaliar métricas de classificação\n", "* **Ajustar modelo:** como ajustar automaticamente os hiperparâmetros de um modelo de classificação\n", "* **Plotagem do modelo:** como analisar o desempenho do modelo usando vários gráficos\n", "* **Finalizar o modelo:** como finalizar o melhor modelo ao final do experimento\n", "* **Predict usando o modelo:** como fazer previsões em dados novos/não vistos\n", "* **<PERSON><PERSON>/carregar modelo:** como salvar/carregar um modelo para uso futuro\n", "\n", "Tempo de leitura: Aprox. 30 minutos\n", "\n", "\n", "# 1.1 Instalando o PyCaret\n", "O primeiro passo para começar a usar o PyCaret é instalar o pycaret. A instalação é fácil e levará apenas alguns minutos. Siga as instruções abaixo:\n", "\n", "# Instalando o PyCaret no Jupyter Notebook Local\n", "`pip install pycaret` <br />\n", "\n", "# Instalando o PyCaret no Google Colab ou no Azure Notebooks\n", "`!pip install pycaret`\n", "\n", "\n", "# 1.2 Pré-requisitos\n", "- Python 3.6 ou superior\n", "- PyCaret 2.0 ou superior\n", "- Conexão com a Internet para carregar dados do repositório do pycaret\n", "- Conhecimentos Básicos de Classificação Binária\n", "\n", "# 1.3 Para usuários do Google colab:\n", "Se você estiver executando este notebook no Google colab, execute o código a seguir na parte superior do notebook para exibir recursos visuais interativos.<br/>\n", "<br/>\n", "`from pycaret.utils import enable_colab` <br/>\n", "`enable_colab()`\n", "\n", "\n", "# 1.4 <PERSON><PERSON><PERSON>:\n", "- __[Tutorial de classificação binária (CLF102) - Nível intermediário - Inglês](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__\n", "- __[Tutorial de classificação binária (CLF103) - Nível de especialista - Inglês](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Expert%20-%20CLF103.ipynb)__"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "2DJaOwC_EQme"}, "source": ["# 2.0 O que é Classificação Binária?\n", "A classificação binária é uma técnica de aprendizado de máquina supervisionada em que o objetivo é prever rótulos de classe categórica que são discretos e não ordenados, como Aprovado/Reprovado, Positivo/Negativo, Padrão/Não Padrão etc. Alguns casos de uso do mundo real para classificação estão listados abaixo :\n", "\n", "- Exames médicos para determinar se um paciente tem uma determinada doença ou não - a propriedade de classificação é a presença da doença.\n", "- Um método de teste \"aprovado ou reprovado\" ou controle de qualidade em fábricas, ou seja, decidindo se uma especificação foi ou não atendida - uma classificação de aprovação/reprovação.\n", "- Recuperação de informação, nomeadamente decidir se uma página ou um artigo deve ou não estar no conjunto de resultados de uma pesquisa – a propriedade de classificação é a relevância do artigo, ou a utilidade para o utilizador.\n", "\n", "__[Saiba mais sobre classificação binária](https://medium.com/@categitau/in-one-of-my-previous-posts-i-introduced-machine-learning-and-talked-about-the-two-most-common-c1ac6e18df16)__"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XC3kSuueEQmh"}, "source": ["# 3.0 Visão geral do módulo de classificação no PyCaret\n", "O módulo de classificação do PyCaret (`pycaret.classification`) é um módulo de aprendizado de máquina supervisionado que é usado para classificar os elementos em um grupo binário baseado em várias técnicas e algoritmos. Alguns casos de uso comuns de problemas de classificação incluem a previsão de inadimplência do cliente (sim ou não), rotatividade de clientes (o cliente sairá ou ficará), doen<PERSON> encontrada (positiva ou negativa).\n", "\n", "O módulo de classificação PyCaret pode ser usado para problemas de classificação binária ou multiclasse. Possui mais de 18 algoritmos e 14 gráficos para analisar o desempenho dos modelos. Seja ajuste de hiperparâmetros, ensembling ou técnicas avançadas como empilhamento, o módulo de classificação do PyCaret tem tudo."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "aAKRo-EbEQml"}, "source": ["# 4.0 Conjunto de dados para o tutorial"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "VLKxlFjrEQmq"}, "source": ["Para este tutorial, usaremos um conjunto de dados da UCI chamado **Default of Credit Card Clients Dataset**. Este conjunto de dados contém informações sobre pagamentos padrão, fatores demográficos, dados de crédito, histórico de pagamentos e extratos de cobrança de clientes de cartão de crédito em Taiwan de abril de 2005 a setembro de 2005. Existem 24.000 amostras e 25 recursos. As descrições curtas de cada coluna são as seguintes:\n", "\n", "- **ID:** ID de cada cliente\n", "- **LIMIT_BAL:** Valor do crédito concedido em NEW TAIWAN dólares (inclui crédito individual e familiar/complementar)\n", "- **SEX:** <PERSON><PERSON><PERSON><PERSON> (1=masculino, 2=feminino)\n", "- **EDUCATION:** (1=graduação, 2=universidade, 3=ensino médio, 4=outros, 5=desconhecido, 6=desconhecido)\n", "- **MARRIAGE:** Estado civil (1=casado, 2=solteiro, 3=outros)\n", "- **AGE:** <PERSON><PERSON> em anos\n", "- **PAY_0 a PAY_6:** Status de pagamento há n meses (PAY_0 = último mês ... PAY_6 = 6 meses atrás) (Rótulos: -1=pagamento devido, 1=atraso no pagamento por um mês, 2=atraso no pagamento por dois meses, ... 8=atraso de pagamento por oito meses, 9=atraso de pagamento por nove meses e acima)\n", "- **BILL_AMT1 a BILL_AMT6:** Valor do extrato da conta há n meses ( BILL_AMT1 = last_month .. BILL_AMT6 = 6 meses atrás)\n", "- **PAY_AMT1 a PAY_AMT6:** Valor do pagamento até n meses atrás ( BILL_AMT1 = last_month .. BILL_AMT6 = 6 meses atrás)\n", "- **DEFAULT:** default no pagamento (1=sim, 0=não) `Coluna target (resposta)`\n", "\n", "# Fonte do conjunto de dados:\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013). Repositório de aprendizado de máquina UCI. Irvine, CA: Universidade da Califórnia, Escola de Informação e Ciência da Computação.\n", "\n", "O conjunto de dados original e o dicionário de dados podem ser __[encontrados aqui.](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)__"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Ui_rALqYEQmv"}, "source": ["# 5.0 Obtendo os dados"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "BfqIMeJNEQmz"}, "source": ["Você pode baixar os dados da fonte original __[encontrado aqui](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)__ e carregá-los usando pandas __[( Saiba como)](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html)__ ou você pode usar o repositório de dados do PyCaret para carregar os dados usando o `get_data()` função (Isso exigirá uma conexão com a Internet)."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211}, "colab_type": "code", "id": "lUvE187JEQm3", "outputId": "8741262c-0e33-4ec0-b54d-3c8fb41e52c0"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>689.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331.0</td>\n", "      <td>14948.0</td>\n", "      <td>15549.0</td>\n", "      <td>1518.0</td>\n", "      <td>1500.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>5000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314.0</td>\n", "      <td>28959.0</td>\n", "      <td>29547.0</td>\n", "      <td>2000.0</td>\n", "      <td>2019.0</td>\n", "      <td>1200.0</td>\n", "      <td>1100.0</td>\n", "      <td>1069.0</td>\n", "      <td>1000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940.0</td>\n", "      <td>19146.0</td>\n", "      <td>19131.0</td>\n", "      <td>2000.0</td>\n", "      <td>36681.0</td>\n", "      <td>10000.0</td>\n", "      <td>9000.0</td>\n", "      <td>689.0</td>\n", "      <td>679.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>19394.0</td>\n", "      <td>19619.0</td>\n", "      <td>20024.0</td>\n", "      <td>2500.0</td>\n", "      <td>1815.0</td>\n", "      <td>657.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>800.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0      20000    2          2         1   24      2      2     -1     -1   \n", "1      90000    2          2         2   34      0      0      0      0   \n", "2      50000    2          2         1   37      0      0      0      0   \n", "3      50000    1          2         1   57     -1      0     -1      0   \n", "4      50000    1          1         2   37      0      0      0      0   \n", "\n", "   PAY_5  ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0     -2  ...        0.0        0.0        0.0       0.0     689.0       0.0   \n", "1      0  ...    14331.0    14948.0    15549.0    1518.0    1500.0    1000.0   \n", "2      0  ...    28314.0    28959.0    29547.0    2000.0    2019.0    1200.0   \n", "3      0  ...    20940.0    19146.0    19131.0    2000.0   36681.0   10000.0   \n", "4      0  ...    19394.0    19619.0    20024.0    2500.0    1815.0     657.0   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  default  \n", "0       0.0       0.0       0.0        1  \n", "1    1000.0    1000.0    5000.0        0  \n", "2    1100.0    1069.0    1000.0        0  \n", "3    9000.0     689.0     679.0        0  \n", "4    1000.0    1000.0     800.0        0  \n", "\n", "[5 rows x 24 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pycaret.datasets import get_data\n", "dataset = get_data('credit')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 33}, "colab_type": "code", "id": "kMqDGBkJEQnN", "outputId": "b2015b7a-4c1a-4377-d9cf-3e9ac5ce3ea2"}, "outputs": [{"data": {"text/plain": ["(24000, 24)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["#verifica a dimensão do dataframe\n", "dataset.shape"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "LyGFryEhEQne"}, "source": ["Para demonstrar a função `predict_model()` em dados não vistos, uma amostra de 1.200 registros foi retida do conjunto de dados original para ser usada para previsões. Isso não deve ser confundido com uma divisão de treinamento/teste, pois essa divisão específica é realizada para simular um cenário da vida real. Outra maneira de pensar sobre isso é que esses 1.200 registros não estão disponíveis no momento em que o experimento de aprendizado de máquina foi realizado."]}, {"cell_type": "code", "execution_count": 51, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "hXmaL1xFEQnj", "outputId": "f1f62a7d-5d3d-4832-ee00-a4d20ee39c41"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Conjunto de dados para modelagem (treino e teste): (22800, 24)\n", "Conjunto de dados não usados no treino/teste, apenas como validação: (1200, 24)\n"]}], "source": ["data = dataset.sample(frac=0.95, random_state=786)\n", "data_unseen = dataset.drop(data.index)\n", "data.reset_index(inplace=True, drop=True)\n", "data_unseen.reset_index(inplace=True, drop=True)\n", "print('Conjunto de dados para modelagem (treino e teste): ' + str(data.shape))\n", "print('Conjunto de dados não usados no treino/teste, apenas como validação: ' + str(data_unseen.shape))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "y9s9wNcjEQn0"}, "source": ["# 6.0 Configurando o ambiente no PyCaret"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZlA01j6NEQn7"}, "source": ["A função `setup()` inicializa o ambiente em pycaret e cria o pipeline de transformação para preparar os dados para modelagem e implantação. `setup()` deve ser chamado antes de executar qualquer outra função em pycaret. São necessários dois parâmetros obrigatórios: um dataframe de pandas e o nome da coluna de destino. Todos os outros parâmetros são opcionais e são usados para personalizar o pipeline de pré-processamento (veremos em tutoriais posteriores).\n", "\n", "Quando `setup()` é executado, o algoritmo de inferência do PyCaret inferirá automaticamente os tipos de dados para todos os recursos com base em determinadas propriedades. O tipo de dados deve ser inferido corretamente, mas isso nem sempre é o caso. Para explicar isso, o PyCaret exibe uma tabela contendo os recursos e seus tipos de dados inferidos após a execução de `setup()`. Se todos os tipos de dados forem identificados corretamente, pode-se pressionar \"enter\" para continuar ou digitar \"quit\" para encerrar o experimento. Garantir que os tipos de dados estejam corretos é de fundamental importância no PyCaret, pois ele executa automaticamente algumas tarefas de pré-processamento que são imperativas para qualquer experimento de aprendizado de máquina. Essas tarefas são executadas de forma diferente para cada tipo de dados, o que significa que é muito importante que elas sejam configuradas corretamente.\n", "\n", "Em tutoriais posteriores, aprenderemos como sobrescrever o tipo de dados inferido do PyCaret usando os parâmetros `numeric_features` e `categorical_features` em `setup()`."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {}, "colab_type": "code", "id": "BOmRR0deEQoA"}, "outputs": [], "source": ["from pycaret.classification import *"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 803}, "colab_type": "code", "id": "k2IuvfDHEQoO", "outputId": "c7754ae9-b060-4218-b6f0-de65a815aa3a", "scrolled": false}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_0de8b_row44_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_0de8b\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_0de8b_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_0de8b_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_0de8b_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_0de8b_row0_col1\" class=\"data row0 col1\" >123</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_0de8b_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_0de8b_row1_col1\" class=\"data row1 col1\" >default</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_0de8b_row2_col0\" class=\"data row2 col0\" >Target Type</td>\n", "      <td id=\"T_0de8b_row2_col1\" class=\"data row2 col1\" >Binary</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_0de8b_row3_col0\" class=\"data row3 col0\" >Label Encoded</td>\n", "      <td id=\"T_0de8b_row3_col1\" class=\"data row3 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_0de8b_row4_col0\" class=\"data row4 col0\" >Original Data</td>\n", "      <td id=\"T_0de8b_row4_col1\" class=\"data row4 col1\" >(22800, 24)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_0de8b_row5_col0\" class=\"data row5 col0\" >Missing Values</td>\n", "      <td id=\"T_0de8b_row5_col1\" class=\"data row5 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_0de8b_row6_col0\" class=\"data row6 col0\" >Numeric Features</td>\n", "      <td id=\"T_0de8b_row6_col1\" class=\"data row6 col1\" >14</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_0de8b_row7_col0\" class=\"data row7 col0\" >Categorical Features</td>\n", "      <td id=\"T_0de8b_row7_col1\" class=\"data row7 col1\" >9</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_0de8b_row8_col0\" class=\"data row8 col0\" >Ordinal Features</td>\n", "      <td id=\"T_0de8b_row8_col1\" class=\"data row8 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_0de8b_row9_col0\" class=\"data row9 col0\" >High Cardinality Features</td>\n", "      <td id=\"T_0de8b_row9_col1\" class=\"data row9 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_0de8b_row10_col0\" class=\"data row10 col0\" >High Cardinality Method</td>\n", "      <td id=\"T_0de8b_row10_col1\" class=\"data row10 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_0de8b_row11_col0\" class=\"data row11 col0\" >Transformed Train Set</td>\n", "      <td id=\"T_0de8b_row11_col1\" class=\"data row11 col1\" >(15959, 88)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_0de8b_row12_col0\" class=\"data row12 col0\" >Transformed Test Set</td>\n", "      <td id=\"T_0de8b_row12_col1\" class=\"data row12 col1\" >(6841, 88)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_0de8b_row13_col0\" class=\"data row13 col0\" >Shuffle Train-Test</td>\n", "      <td id=\"T_0de8b_row13_col1\" class=\"data row13 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_0de8b_row14_col0\" class=\"data row14 col0\" >Stratify Train-Test</td>\n", "      <td id=\"T_0de8b_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_0de8b_row15_col0\" class=\"data row15 col0\" >Fold Generator</td>\n", "      <td id=\"T_0de8b_row15_col1\" class=\"data row15 col1\" >StratifiedKFold</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_0de8b_row16_col0\" class=\"data row16 col0\" >Fold Number</td>\n", "      <td id=\"T_0de8b_row16_col1\" class=\"data row16 col1\" >10</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_0de8b_row17_col0\" class=\"data row17 col0\" >CPU Jobs</td>\n", "      <td id=\"T_0de8b_row17_col1\" class=\"data row17 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_0de8b_row18_col0\" class=\"data row18 col0\" >Use GPU</td>\n", "      <td id=\"T_0de8b_row18_col1\" class=\"data row18 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_0de8b_row19_col0\" class=\"data row19 col0\" >Log Experiment</td>\n", "      <td id=\"T_0de8b_row19_col1\" class=\"data row19 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_0de8b_row20_col0\" class=\"data row20 col0\" >Experiment Name</td>\n", "      <td id=\"T_0de8b_row20_col1\" class=\"data row20 col1\" >clf-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_0de8b_row21_col0\" class=\"data row21 col0\" >USI</td>\n", "      <td id=\"T_0de8b_row21_col1\" class=\"data row21 col1\" >7277</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_0de8b_row22_col0\" class=\"data row22 col0\" >Imputation Type</td>\n", "      <td id=\"T_0de8b_row22_col1\" class=\"data row22 col1\" >simple</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_0de8b_row23_col0\" class=\"data row23 col0\" >Iterative Imputation Iteration</td>\n", "      <td id=\"T_0de8b_row23_col1\" class=\"data row23 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_0de8b_row24_col0\" class=\"data row24 col0\" >Numeric Imputer</td>\n", "      <td id=\"T_0de8b_row24_col1\" class=\"data row24 col1\" >mean</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_0de8b_row25_col0\" class=\"data row25 col0\" >Iterative Imputation Numeric Model</td>\n", "      <td id=\"T_0de8b_row25_col1\" class=\"data row25 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_0de8b_row26_col0\" class=\"data row26 col0\" >Categorical Imputer</td>\n", "      <td id=\"T_0de8b_row26_col1\" class=\"data row26 col1\" >constant</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_0de8b_row27_col0\" class=\"data row27 col0\" >Iterative Imputation Categorical Model</td>\n", "      <td id=\"T_0de8b_row27_col1\" class=\"data row27 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_0de8b_row28_col0\" class=\"data row28 col0\" >Unknown Categoricals Handling</td>\n", "      <td id=\"T_0de8b_row28_col1\" class=\"data row28 col1\" >least_frequent</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_0de8b_row29_col0\" class=\"data row29 col0\" >Normalize</td>\n", "      <td id=\"T_0de8b_row29_col1\" class=\"data row29 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_0de8b_row30_col0\" class=\"data row30 col0\" >Normalize Method</td>\n", "      <td id=\"T_0de8b_row30_col1\" class=\"data row30 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_0de8b_row31_col0\" class=\"data row31 col0\" >Transformation</td>\n", "      <td id=\"T_0de8b_row31_col1\" class=\"data row31 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_0de8b_row32_col0\" class=\"data row32 col0\" >Transformation Method</td>\n", "      <td id=\"T_0de8b_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_0de8b_row33_col0\" class=\"data row33 col0\" >PCA</td>\n", "      <td id=\"T_0de8b_row33_col1\" class=\"data row33 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_0de8b_row34_col0\" class=\"data row34 col0\" >PCA Method</td>\n", "      <td id=\"T_0de8b_row34_col1\" class=\"data row34 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_0de8b_row35_col0\" class=\"data row35 col0\" >PCA Components</td>\n", "      <td id=\"T_0de8b_row35_col1\" class=\"data row35 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_0de8b_row36_col0\" class=\"data row36 col0\" >Ignore Low Variance</td>\n", "      <td id=\"T_0de8b_row36_col1\" class=\"data row36 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_0de8b_row37_col0\" class=\"data row37 col0\" >Combine Rare Levels</td>\n", "      <td id=\"T_0de8b_row37_col1\" class=\"data row37 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_0de8b_row38_col0\" class=\"data row38 col0\" >Rare Level Threshold</td>\n", "      <td id=\"T_0de8b_row38_col1\" class=\"data row38 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_0de8b_row39_col0\" class=\"data row39 col0\" >Numeric Binning</td>\n", "      <td id=\"T_0de8b_row39_col1\" class=\"data row39 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "      <td id=\"T_0de8b_row40_col0\" class=\"data row40 col0\" >Remove Outliers</td>\n", "      <td id=\"T_0de8b_row40_col1\" class=\"data row40 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "      <td id=\"T_0de8b_row41_col0\" class=\"data row41 col0\" >Outliers Threshold</td>\n", "      <td id=\"T_0de8b_row41_col1\" class=\"data row41 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "      <td id=\"T_0de8b_row42_col0\" class=\"data row42 col0\" >Remove Multicollinearity</td>\n", "      <td id=\"T_0de8b_row42_col1\" class=\"data row42 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row43\" class=\"row_heading level0 row43\" >43</th>\n", "      <td id=\"T_0de8b_row43_col0\" class=\"data row43 col0\" >Multicollinearity Threshold</td>\n", "      <td id=\"T_0de8b_row43_col1\" class=\"data row43 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row44\" class=\"row_heading level0 row44\" >44</th>\n", "      <td id=\"T_0de8b_row44_col0\" class=\"data row44 col0\" >Remove Perfect Collinearity</td>\n", "      <td id=\"T_0de8b_row44_col1\" class=\"data row44 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row45\" class=\"row_heading level0 row45\" >45</th>\n", "      <td id=\"T_0de8b_row45_col0\" class=\"data row45 col0\" >Clustering</td>\n", "      <td id=\"T_0de8b_row45_col1\" class=\"data row45 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row46\" class=\"row_heading level0 row46\" >46</th>\n", "      <td id=\"T_0de8b_row46_col0\" class=\"data row46 col0\" >Clustering Iteration</td>\n", "      <td id=\"T_0de8b_row46_col1\" class=\"data row46 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row47\" class=\"row_heading level0 row47\" >47</th>\n", "      <td id=\"T_0de8b_row47_col0\" class=\"data row47 col0\" >Polynomial Features</td>\n", "      <td id=\"T_0de8b_row47_col1\" class=\"data row47 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row48\" class=\"row_heading level0 row48\" >48</th>\n", "      <td id=\"T_0de8b_row48_col0\" class=\"data row48 col0\" >Polynomial Degree</td>\n", "      <td id=\"T_0de8b_row48_col1\" class=\"data row48 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row49\" class=\"row_heading level0 row49\" >49</th>\n", "      <td id=\"T_0de8b_row49_col0\" class=\"data row49 col0\" >Trignometry Features</td>\n", "      <td id=\"T_0de8b_row49_col1\" class=\"data row49 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row50\" class=\"row_heading level0 row50\" >50</th>\n", "      <td id=\"T_0de8b_row50_col0\" class=\"data row50 col0\" >Polynomial Threshold</td>\n", "      <td id=\"T_0de8b_row50_col1\" class=\"data row50 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row51\" class=\"row_heading level0 row51\" >51</th>\n", "      <td id=\"T_0de8b_row51_col0\" class=\"data row51 col0\" >Group Features</td>\n", "      <td id=\"T_0de8b_row51_col1\" class=\"data row51 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row52\" class=\"row_heading level0 row52\" >52</th>\n", "      <td id=\"T_0de8b_row52_col0\" class=\"data row52 col0\" >Feature Selection</td>\n", "      <td id=\"T_0de8b_row52_col1\" class=\"data row52 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row53\" class=\"row_heading level0 row53\" >53</th>\n", "      <td id=\"T_0de8b_row53_col0\" class=\"data row53 col0\" >Feature Selection Method</td>\n", "      <td id=\"T_0de8b_row53_col1\" class=\"data row53 col1\" >classic</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row54\" class=\"row_heading level0 row54\" >54</th>\n", "      <td id=\"T_0de8b_row54_col0\" class=\"data row54 col0\" >Features Selection Threshold</td>\n", "      <td id=\"T_0de8b_row54_col1\" class=\"data row54 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row55\" class=\"row_heading level0 row55\" >55</th>\n", "      <td id=\"T_0de8b_row55_col0\" class=\"data row55 col0\" >Feature Interaction</td>\n", "      <td id=\"T_0de8b_row55_col1\" class=\"data row55 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row56\" class=\"row_heading level0 row56\" >56</th>\n", "      <td id=\"T_0de8b_row56_col0\" class=\"data row56 col0\" >Feature Ratio</td>\n", "      <td id=\"T_0de8b_row56_col1\" class=\"data row56 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row57\" class=\"row_heading level0 row57\" >57</th>\n", "      <td id=\"T_0de8b_row57_col0\" class=\"data row57 col0\" >Interaction Threshold</td>\n", "      <td id=\"T_0de8b_row57_col1\" class=\"data row57 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row58\" class=\"row_heading level0 row58\" >58</th>\n", "      <td id=\"T_0de8b_row58_col0\" class=\"data row58 col0\" >Fix Imbalance</td>\n", "      <td id=\"T_0de8b_row58_col1\" class=\"data row58 col1\" >0</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0de8b_level0_row59\" class=\"row_heading level0 row59\" >59</th>\n", "      <td id=\"T_0de8b_row59_col0\" class=\"data row59 col0\" >Fix Imbalance Method</td>\n", "      <td id=\"T_0de8b_row59_col1\" class=\"data row59 col1\" >SMOTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6f9da0f10>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["exp_clf101 = setup(data = data, target = 'default', session_id=123) "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "JJSOhIOxEQoY"}, "source": ["Uma vez que a configuração tenha sido executada com sucesso, ele imprime a grade de informações que contém várias informações importantes. A maioria das informações está relacionada ao pipeline de pré-processamento que é construído quando o `setup()` é executado. A maioria desses recursos está fora do escopo deste tutorial, no entanto, algumas coisas importantes a serem observadas neste estágio incluem:\n", "\n", "- **session_id :** Um número pseudo-aleatório distribuído como semente em todas as funções para posterior reprodutibilidade. Se nenhum `session_id` for passado, um número aleatório é gerado automaticamente e é distribuído para todas as funções. Neste experimento, o `session_id` é definido como `123` para reprodutibilidade posterior.<br/>\n", "<br/>\n", "- **Target Type:** Binário ou Multiclasse. O tipo de alvo é detectado e exibido automaticamente. Não há diferença em como o experimento é realizado para problemas Binários ou Multiclasse. Todas as funcionalidades são idênticas.<br/>\n", "<br/>\n", "- **Label Encoded :** Quando a variável Target é do tipo string (ou seja, 'Yes' ou 'No') em vez de 1 ou 0, ela codifica automaticamente o rótulo em 1 e 0 e exibe o mapeamento (0 : No, 1: Sim) para referência. Neste experimento, nenhuma codificação de rótulo é necessária, pois a variável de destino é do tipo numérico. <br/>\n", "<br/>\n", "- **Original Data:** Exibe a forma original do conjunto de dados. Neste experimento (22.800, 24) significa 22.800 amostras e 24 recursos, incluindo a coluna de destino. <br/>\n", "<br/>\n", "- **Missing values:** quando houver valores ausentes nos dados originais, isso será exibido como Verdadeiro. Para este experimento, não há valores ausentes no conjunto de dados.\n", "<br/>\n", "<br/>\n", "- **Numeric features:** o número de features inferidos como numéricos. Nesse conjunto de dados, 14 dos 24 features são inferidos como numéricos. <br/>\n", "<br/>\n", "- **Categorical features:** o número de features inferidos como categóricos. Neste conjunto de dados, 9 dos 24 features são inferidos como categóricos. <br/>\n", "<br/>\n", "- **Transformed Train Set:** exibe a forma do conjunto de treinamento transformado. Observe que a forma original de (22800, 24) é transformada em (15959, 91) para o conjunto de treino transformado e o número de features aumentou de 24 para 91 devido à codificação categórica <br/>\n", "<br/>\n", "- **Transformed Test Set:** exibe a forma do conjunto de teste/hold-out transformado. Existem 6841 amostras no conjunto de teste/hold-out. Essa divisão é baseada no valor padrão de 70/30 que pode ser alterado usando o parâmetro `train_size` na configuração. <br/>\n", "\n", "Observe como algumas tarefas que são indispensáveis para realizar a modelagem são tratadas automaticamente, como imputação de valores ausentes (neste caso, não há valores ausentes nos dados de treinamento, mas ainda precisamos de imputadores para dados não vistos), codificação categórica etc. parâmetros em `setup()` são opcionais e usados para personalizar o pipeline de pré-processamento. Esses parâmetros estão fora do escopo deste tutorial, mas à medida que você avança para os níveis intermediário e especialista, nós os abordaremos com muito mais detalhes."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "it_nJo1IEQob"}, "source": ["# 7.0 Comparando todos os modelos"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "apb_B9bBEQof"}, "source": ["Comparar todos os modelos para avaliar o desempenho é o ponto de partida recomendado para a modelagem assim que a configuração estiver concluída (a menos que você saiba exatamente que tipo de modelo você precisa, o que geralmente não é o caso). Essa função treina todos os modelos na biblioteca de modelos e os pontua usando validação cruzada estratificada para avaliação de métricas. A saída imprime uma grade de pontuação que mostra a Precisão média, AUC, Recall, Precision, F1, Kappa e MCC (Matthews correlation coefficient) em folds (10 por padrão) junto com a quantidade de vezes de treinamento."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {}, "colab_type": "code", "id": "AsG0b1NIEQoj", "outputId": "a6e3a510-45a1-4782-8ffe-0ec138a64eed", "scrolled": false}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_c3c5d th {\n", "  text-align: left;\n", "}\n", "#T_c3c5d_row0_col0, #T_c3c5d_row0_col2, #T_c3c5d_row0_col3, #T_c3c5d_row0_col5, #T_c3c5d_row0_col6, #T_c3c5d_row0_col7, #T_c3c5d_row1_col0, #T_c3c5d_row1_col1, #T_c3c5d_row1_col2, #T_c3c5d_row1_col3, #T_c3c5d_row1_col4, #T_c3c5d_row2_col0, #T_c3c5d_row2_col1, #T_c3c5d_row2_col3, #T_c3c5d_row2_col4, #T_c3c5d_row2_col5, #T_c3c5d_row2_col6, #T_c3c5d_row2_col7, #T_c3c5d_row3_col0, #T_c3c5d_row3_col1, #T_c3c5d_row3_col2, #T_c3c5d_row3_col3, #T_c3c5d_row3_col4, #T_c3c5d_row3_col5, #T_c3c5d_row3_col6, #T_c3c5d_row3_col7, #T_c3c5d_row4_col0, #T_c3c5d_row4_col1, #T_c3c5d_row4_col2, #T_c3c5d_row4_col3, #T_c3c5d_row4_col4, #T_c3c5d_row4_col5, #T_c3c5d_row4_col6, #T_c3c5d_row4_col7, #T_c3c5d_row5_col0, #T_c3c5d_row5_col1, #T_c3c5d_row5_col2, #T_c3c5d_row5_col3, #T_c3c5d_row5_col4, #T_c3c5d_row5_col5, #T_c3c5d_row5_col6, #T_c3c5d_row5_col7, #T_c3c5d_row6_col0, #T_c3c5d_row6_col1, #T_c3c5d_row6_col2, #T_c3c5d_row6_col3, #T_c3c5d_row6_col4, #T_c3c5d_row6_col5, #T_c3c5d_row6_col6, #T_c3c5d_row6_col7, #T_c3c5d_row7_col0, #T_c3c5d_row7_col1, #T_c3c5d_row7_col2, #T_c3c5d_row7_col3, #T_c3c5d_row7_col4, #T_c3c5d_row7_col5, #T_c3c5d_row7_col6, #T_c3c5d_row7_col7, #T_c3c5d_row8_col0, #T_c3c5d_row8_col1, #T_c3c5d_row8_col2, #T_c3c5d_row8_col3, #T_c3c5d_row8_col4, #T_c3c5d_row8_col5, #T_c3c5d_row8_col6, #T_c3c5d_row8_col7, #T_c3c5d_row9_col0, #T_c3c5d_row9_col1, #T_c3c5d_row9_col2, #T_c3c5d_row9_col3, #T_c3c5d_row9_col4, #T_c3c5d_row9_col5, #T_c3c5d_row9_col6, #T_c3c5d_row9_col7, #T_c3c5d_row10_col0, #T_c3c5d_row10_col1, #T_c3c5d_row10_col2, #T_c3c5d_row10_col3, #T_c3c5d_row10_col4, #T_c3c5d_row10_col5, #T_c3c5d_row10_col6, #T_c3c5d_row10_col7, #T_c3c5d_row11_col0, #T_c3c5d_row11_col1, #T_c3c5d_row11_col2, #T_c3c5d_row11_col3, #T_c3c5d_row11_col4, #T_c3c5d_row11_col5, #T_c3c5d_row11_col6, #T_c3c5d_row11_col7, #T_c3c5d_row12_col0, #T_c3c5d_row12_col1, #T_c3c5d_row12_col2, #T_c3c5d_row12_col3, #T_c3c5d_row12_col4, #T_c3c5d_row12_col5, #T_c3c5d_row12_col6, #T_c3c5d_row12_col7, #T_c3c5d_row13_col0, #T_c3c5d_row13_col1, #T_c3c5d_row13_col2, #T_c3c5d_row13_col3, #T_c3c5d_row13_col4, #T_c3c5d_row13_col5, #T_c3c5d_row13_col6, #T_c3c5d_row13_col7, #T_c3c5d_row14_col0, #T_c3c5d_row14_col1, #T_c3c5d_row14_col2, #T_c3c5d_row14_col4, #T_c3c5d_row14_col5, #T_c3c5d_row14_col6, #T_c3c5d_row14_col7 {\n", "  text-align: left;\n", "}\n", "#T_c3c5d_row0_col1, #T_c3c5d_row0_col4, #T_c3c5d_row1_col5, #T_c3c5d_row1_col6, #T_c3c5d_row1_col7, #T_c3c5d_row2_col2, #T_c3c5d_row14_col3 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_c3c5d_row0_col8, #T_c3c5d_row1_col8, #T_c3c5d_row2_col8, #T_c3c5d_row3_col8, #T_c3c5d_row4_col8, #T_c3c5d_row5_col8, #T_c3c5d_row6_col8, #T_c3c5d_row7_col8, #T_c3c5d_row8_col8, #T_c3c5d_row10_col8, #T_c3c5d_row11_col8, #T_c3c5d_row12_col8, #T_c3c5d_row13_col8, #T_c3c5d_row14_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_c3c5d_row9_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_c3c5d\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_c3c5d_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_c3c5d_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_c3c5d_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_c3c5d_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_c3c5d_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_c3c5d_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_c3c5d_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_c3c5d_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "      <th id=\"T_c3c5d_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row0\" class=\"row_heading level0 row0\" >ridge</th>\n", "      <td id=\"T_c3c5d_row0_col0\" class=\"data row0 col0\" >Ridge Classifier</td>\n", "      <td id=\"T_c3c5d_row0_col1\" class=\"data row0 col1\" >0.8254</td>\n", "      <td id=\"T_c3c5d_row0_col2\" class=\"data row0 col2\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row0_col3\" class=\"data row0 col3\" >0.3637</td>\n", "      <td id=\"T_c3c5d_row0_col4\" class=\"data row0 col4\" >0.6913</td>\n", "      <td id=\"T_c3c5d_row0_col5\" class=\"data row0 col5\" >0.4764</td>\n", "      <td id=\"T_c3c5d_row0_col6\" class=\"data row0 col6\" >0.3836</td>\n", "      <td id=\"T_c3c5d_row0_col7\" class=\"data row0 col7\" >0.4122</td>\n", "      <td id=\"T_c3c5d_row0_col8\" class=\"data row0 col8\" >0.0600</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row1\" class=\"row_heading level0 row1\" >lda</th>\n", "      <td id=\"T_c3c5d_row1_col0\" class=\"data row1 col0\" >Linear Discriminant Analysis</td>\n", "      <td id=\"T_c3c5d_row1_col1\" class=\"data row1 col1\" >0.8247</td>\n", "      <td id=\"T_c3c5d_row1_col2\" class=\"data row1 col2\" >0.7634</td>\n", "      <td id=\"T_c3c5d_row1_col3\" class=\"data row1 col3\" >0.3755</td>\n", "      <td id=\"T_c3c5d_row1_col4\" class=\"data row1 col4\" >0.6794</td>\n", "      <td id=\"T_c3c5d_row1_col5\" class=\"data row1 col5\" >0.4835</td>\n", "      <td id=\"T_c3c5d_row1_col6\" class=\"data row1 col6\" >0.3884</td>\n", "      <td id=\"T_c3c5d_row1_col7\" class=\"data row1 col7\" >0.4132</td>\n", "      <td id=\"T_c3c5d_row1_col8\" class=\"data row1 col8\" >0.1310</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row2\" class=\"row_heading level0 row2\" >gbc</th>\n", "      <td id=\"T_c3c5d_row2_col0\" class=\"data row2 col0\" >Gradient Boosting Classifier</td>\n", "      <td id=\"T_c3c5d_row2_col1\" class=\"data row2 col1\" >0.8226</td>\n", "      <td id=\"T_c3c5d_row2_col2\" class=\"data row2 col2\" >0.7789</td>\n", "      <td id=\"T_c3c5d_row2_col3\" class=\"data row2 col3\" >0.3551</td>\n", "      <td id=\"T_c3c5d_row2_col4\" class=\"data row2 col4\" >0.6806</td>\n", "      <td id=\"T_c3c5d_row2_col5\" class=\"data row2 col5\" >0.4664</td>\n", "      <td id=\"T_c3c5d_row2_col6\" class=\"data row2 col6\" >0.3725</td>\n", "      <td id=\"T_c3c5d_row2_col7\" class=\"data row2 col7\" >0.4010</td>\n", "      <td id=\"T_c3c5d_row2_col8\" class=\"data row2 col8\" >1.7320</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row3\" class=\"row_heading level0 row3\" >ada</th>\n", "      <td id=\"T_c3c5d_row3_col0\" class=\"data row3 col0\" >Ada Boost Classifier</td>\n", "      <td id=\"T_c3c5d_row3_col1\" class=\"data row3 col1\" >0.8221</td>\n", "      <td id=\"T_c3c5d_row3_col2\" class=\"data row3 col2\" >0.7697</td>\n", "      <td id=\"T_c3c5d_row3_col3\" class=\"data row3 col3\" >0.3505</td>\n", "      <td id=\"T_c3c5d_row3_col4\" class=\"data row3 col4\" >0.6811</td>\n", "      <td id=\"T_c3c5d_row3_col5\" class=\"data row3 col5\" >0.4626</td>\n", "      <td id=\"T_c3c5d_row3_col6\" class=\"data row3 col6\" >0.3690</td>\n", "      <td id=\"T_c3c5d_row3_col7\" class=\"data row3 col7\" >0.3983</td>\n", "      <td id=\"T_c3c5d_row3_col8\" class=\"data row3 col8\" >0.4260</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row4\" class=\"row_heading level0 row4\" >lightgbm</th>\n", "      <td id=\"T_c3c5d_row4_col0\" class=\"data row4 col0\" >Light Gradient Boosting Machine</td>\n", "      <td id=\"T_c3c5d_row4_col1\" class=\"data row4 col1\" >0.8210</td>\n", "      <td id=\"T_c3c5d_row4_col2\" class=\"data row4 col2\" >0.7750</td>\n", "      <td id=\"T_c3c5d_row4_col3\" class=\"data row4 col3\" >0.3609</td>\n", "      <td id=\"T_c3c5d_row4_col4\" class=\"data row4 col4\" >0.6679</td>\n", "      <td id=\"T_c3c5d_row4_col5\" class=\"data row4 col5\" >0.4683</td>\n", "      <td id=\"T_c3c5d_row4_col6\" class=\"data row4 col6\" >0.3721</td>\n", "      <td id=\"T_c3c5d_row4_col7\" class=\"data row4 col7\" >0.3977</td>\n", "      <td id=\"T_c3c5d_row4_col8\" class=\"data row4 col8\" >0.3660</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row5\" class=\"row_heading level0 row5\" >rf</th>\n", "      <td id=\"T_c3c5d_row5_col0\" class=\"data row5 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_c3c5d_row5_col1\" class=\"data row5 col1\" >0.8199</td>\n", "      <td id=\"T_c3c5d_row5_col2\" class=\"data row5 col2\" >0.7598</td>\n", "      <td id=\"T_c3c5d_row5_col3\" class=\"data row5 col3\" >0.3663</td>\n", "      <td id=\"T_c3c5d_row5_col4\" class=\"data row5 col4\" >0.6601</td>\n", "      <td id=\"T_c3c5d_row5_col5\" class=\"data row5 col5\" >0.4707</td>\n", "      <td id=\"T_c3c5d_row5_col6\" class=\"data row5 col6\" >0.3727</td>\n", "      <td id=\"T_c3c5d_row5_col7\" class=\"data row5 col7\" >0.3965</td>\n", "      <td id=\"T_c3c5d_row5_col8\" class=\"data row5 col8\" >0.7960</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row6\" class=\"row_heading level0 row6\" >xgboost</th>\n", "      <td id=\"T_c3c5d_row6_col0\" class=\"data row6 col0\" >Extreme Gradient Boosting</td>\n", "      <td id=\"T_c3c5d_row6_col1\" class=\"data row6 col1\" >0.8160</td>\n", "      <td id=\"T_c3c5d_row6_col2\" class=\"data row6 col2\" >0.7561</td>\n", "      <td id=\"T_c3c5d_row6_col3\" class=\"data row6 col3\" >0.3629</td>\n", "      <td id=\"T_c3c5d_row6_col4\" class=\"data row6 col4\" >0.6391</td>\n", "      <td id=\"T_c3c5d_row6_col5\" class=\"data row6 col5\" >0.4626</td>\n", "      <td id=\"T_c3c5d_row6_col6\" class=\"data row6 col6\" >0.3617</td>\n", "      <td id=\"T_c3c5d_row6_col7\" class=\"data row6 col7\" >0.3829</td>\n", "      <td id=\"T_c3c5d_row6_col8\" class=\"data row6 col8\" >2.4770</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row7\" class=\"row_heading level0 row7\" >et</th>\n", "      <td id=\"T_c3c5d_row7_col0\" class=\"data row7 col0\" >Extra Trees Classifier</td>\n", "      <td id=\"T_c3c5d_row7_col1\" class=\"data row7 col1\" >0.8092</td>\n", "      <td id=\"T_c3c5d_row7_col2\" class=\"data row7 col2\" >0.7377</td>\n", "      <td id=\"T_c3c5d_row7_col3\" class=\"data row7 col3\" >0.3677</td>\n", "      <td id=\"T_c3c5d_row7_col4\" class=\"data row7 col4\" >0.6047</td>\n", "      <td id=\"T_c3c5d_row7_col5\" class=\"data row7 col5\" >0.4571</td>\n", "      <td id=\"T_c3c5d_row7_col6\" class=\"data row7 col6\" >0.3497</td>\n", "      <td id=\"T_c3c5d_row7_col7\" class=\"data row7 col7\" >0.3657</td>\n", "      <td id=\"T_c3c5d_row7_col8\" class=\"data row7 col8\" >0.8920</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row8\" class=\"row_heading level0 row8\" >lr</th>\n", "      <td id=\"T_c3c5d_row8_col0\" class=\"data row8 col0\" >Logistic Regression</td>\n", "      <td id=\"T_c3c5d_row8_col1\" class=\"data row8 col1\" >0.7814</td>\n", "      <td id=\"T_c3c5d_row8_col2\" class=\"data row8 col2\" >0.6410</td>\n", "      <td id=\"T_c3c5d_row8_col3\" class=\"data row8 col3\" >0.0003</td>\n", "      <td id=\"T_c3c5d_row8_col4\" class=\"data row8 col4\" >0.1000</td>\n", "      <td id=\"T_c3c5d_row8_col5\" class=\"data row8 col5\" >0.0006</td>\n", "      <td id=\"T_c3c5d_row8_col6\" class=\"data row8 col6\" >0.0003</td>\n", "      <td id=\"T_c3c5d_row8_col7\" class=\"data row8 col7\" >0.0034</td>\n", "      <td id=\"T_c3c5d_row8_col8\" class=\"data row8 col8\" >1.1840</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row9\" class=\"row_heading level0 row9\" >dummy</th>\n", "      <td id=\"T_c3c5d_row9_col0\" class=\"data row9 col0\" >Dummy Classifier</td>\n", "      <td id=\"T_c3c5d_row9_col1\" class=\"data row9 col1\" >0.7814</td>\n", "      <td id=\"T_c3c5d_row9_col2\" class=\"data row9 col2\" >0.5000</td>\n", "      <td id=\"T_c3c5d_row9_col3\" class=\"data row9 col3\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row9_col4\" class=\"data row9 col4\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row9_col5\" class=\"data row9 col5\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row9_col6\" class=\"data row9 col6\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row9_col7\" class=\"data row9 col7\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row9_col8\" class=\"data row9 col8\" >0.0230</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row10\" class=\"row_heading level0 row10\" >knn</th>\n", "      <td id=\"T_c3c5d_row10_col0\" class=\"data row10 col0\" >K Neighbors Classifier</td>\n", "      <td id=\"T_c3c5d_row10_col1\" class=\"data row10 col1\" >0.7547</td>\n", "      <td id=\"T_c3c5d_row10_col2\" class=\"data row10 col2\" >0.5939</td>\n", "      <td id=\"T_c3c5d_row10_col3\" class=\"data row10 col3\" >0.1763</td>\n", "      <td id=\"T_c3c5d_row10_col4\" class=\"data row10 col4\" >0.3719</td>\n", "      <td id=\"T_c3c5d_row10_col5\" class=\"data row10 col5\" >0.2388</td>\n", "      <td id=\"T_c3c5d_row10_col6\" class=\"data row10 col6\" >0.1145</td>\n", "      <td id=\"T_c3c5d_row10_col7\" class=\"data row10 col7\" >0.1259</td>\n", "      <td id=\"T_c3c5d_row10_col8\" class=\"data row10 col8\" >0.3150</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row11\" class=\"row_heading level0 row11\" >dt</th>\n", "      <td id=\"T_c3c5d_row11_col0\" class=\"data row11 col0\" >Decision Tree Classifier</td>\n", "      <td id=\"T_c3c5d_row11_col1\" class=\"data row11 col1\" >0.7293</td>\n", "      <td id=\"T_c3c5d_row11_col2\" class=\"data row11 col2\" >0.6147</td>\n", "      <td id=\"T_c3c5d_row11_col3\" class=\"data row11 col3\" >0.4104</td>\n", "      <td id=\"T_c3c5d_row11_col4\" class=\"data row11 col4\" >0.3878</td>\n", "      <td id=\"T_c3c5d_row11_col5\" class=\"data row11 col5\" >0.3986</td>\n", "      <td id=\"T_c3c5d_row11_col6\" class=\"data row11 col6\" >0.2242</td>\n", "      <td id=\"T_c3c5d_row11_col7\" class=\"data row11 col7\" >0.2245</td>\n", "      <td id=\"T_c3c5d_row11_col8\" class=\"data row11 col8\" >0.1220</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row12\" class=\"row_heading level0 row12\" >svm</th>\n", "      <td id=\"T_c3c5d_row12_col0\" class=\"data row12 col0\" >SVM - Linear Kernel</td>\n", "      <td id=\"T_c3c5d_row12_col1\" class=\"data row12 col1\" >0.7277</td>\n", "      <td id=\"T_c3c5d_row12_col2\" class=\"data row12 col2\" >0.0000</td>\n", "      <td id=\"T_c3c5d_row12_col3\" class=\"data row12 col3\" >0.1017</td>\n", "      <td id=\"T_c3c5d_row12_col4\" class=\"data row12 col4\" >0.1671</td>\n", "      <td id=\"T_c3c5d_row12_col5\" class=\"data row12 col5\" >0.0984</td>\n", "      <td id=\"T_c3c5d_row12_col6\" class=\"data row12 col6\" >0.0067</td>\n", "      <td id=\"T_c3c5d_row12_col7\" class=\"data row12 col7\" >0.0075</td>\n", "      <td id=\"T_c3c5d_row12_col8\" class=\"data row12 col8\" >0.1810</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row13\" class=\"row_heading level0 row13\" >qda</th>\n", "      <td id=\"T_c3c5d_row13_col0\" class=\"data row13 col0\" >Quadratic Discriminant Analysis</td>\n", "      <td id=\"T_c3c5d_row13_col1\" class=\"data row13 col1\" >0.5451</td>\n", "      <td id=\"T_c3c5d_row13_col2\" class=\"data row13 col2\" >0.5689</td>\n", "      <td id=\"T_c3c5d_row13_col3\" class=\"data row13 col3\" >0.6117</td>\n", "      <td id=\"T_c3c5d_row13_col4\" class=\"data row13 col4\" >0.2886</td>\n", "      <td id=\"T_c3c5d_row13_col5\" class=\"data row13 col5\" >0.3719</td>\n", "      <td id=\"T_c3c5d_row13_col6\" class=\"data row13 col6\" >0.1115</td>\n", "      <td id=\"T_c3c5d_row13_col7\" class=\"data row13 col7\" >0.1235</td>\n", "      <td id=\"T_c3c5d_row13_col8\" class=\"data row13 col8\" >0.2000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_c3c5d_level0_row14\" class=\"row_heading level0 row14\" >nb</th>\n", "      <td id=\"T_c3c5d_row14_col0\" class=\"data row14 col0\" >Naive <PERSON></td>\n", "      <td id=\"T_c3c5d_row14_col1\" class=\"data row14 col1\" >0.3760</td>\n", "      <td id=\"T_c3c5d_row14_col2\" class=\"data row14 col2\" >0.6442</td>\n", "      <td id=\"T_c3c5d_row14_col3\" class=\"data row14 col3\" >0.8845</td>\n", "      <td id=\"T_c3c5d_row14_col4\" class=\"data row14 col4\" >0.2441</td>\n", "      <td id=\"T_c3c5d_row14_col5\" class=\"data row14 col5\" >0.3826</td>\n", "      <td id=\"T_c3c5d_row14_col6\" class=\"data row14 col6\" >0.0608</td>\n", "      <td id=\"T_c3c5d_row14_col7\" class=\"data row14 col7\" >0.1207</td>\n", "      <td id=\"T_c3c5d_row14_col8\" class=\"data row14 col8\" >0.0330</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6f9bb60d0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["best_model = compare_models(fold=10)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nZAUhQGLEQoz"}, "source": ["Duas palavras simples de código ***(nem mesmo uma linha)*** treinaram e avaliaram mais de 15 modelos usando validação cruzada. A grade de pontuação impressa acima destaca a métrica de melhor desempenho apenas para fins de comparação. A grade por padrão é classificada usando Precisão ('Accuracy') (do maior para o menor) que pode ser alterado passando o parâmetro `sort`. Por exemplo, `compare_models(sort = 'Recall')` classificará a grade por Recall em vez de Accuracy. Se você quiser alterar o parâmetro fold do valor padrão de `10` para um valor diferente, então você pode usar o parâmetro `fold`. Por exemplo, `compare_models(fold = 5)` irá comparar todos os modelos na validação cruzada de 5 vezes. Reduzir o número de folds melhorará o tempo de treinamento. <PERSON>r padr<PERSON>, `compare_models` retorna o modelo com melhor desempenho com base na ordem de classificação padrão, mas pode ser usado para retornar uma lista dos N principais modelos usando o parâmetro `n_select`."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RidgeClassifier(alpha=1.0, class_weight=None, copy_X=True, fit_intercept=True,\n", "                max_iter=None, normalize=False, random_state=123, solver='auto',\n", "                tol=0.001)\n"]}], "source": ["print(best_model)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "P5m2pciOEQo4"}, "source": ["# 8.0 C<PERSON>do um modelo"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "u_6cIilfEQo7"}, "source": ["`create_model` é a função mais granular no PyCaret e geralmente é a base por trás da maioria das funcionalidades do PyCaret. Como o nome sugere, esta função treina e avalia um modelo usando validação cruzada que pode ser definida com o parâmetro `fold`. A saída imprime uma grade de pontuação que mostra Precisão, AUC, Recall, Precisão, F1, Kappa e MCC por dobra.\n", "\n", "Para a parte restante deste tutorial, trabalharemos com os modelos abaixo como nossos modelos candidatos. As seleções são apenas para fins ilustrativos e não significam necessariamente que são as de melhor desempenho ou ideais para esse tipo de dados.\n", "\n", "- Classificador de Árvore de Decisão ('dt') - decision tree\n", "- Classificador <PERSON> ('knn')\n", "- Classificador de Floresta Aleatória ('rf') - random forest\n", "\n", "Existem 18 classificadores disponíveis na biblioteca de modelos do PyCaret. Para ver a lista de todos os classificadores, verifique a `docstring` ou use a função `models` para ver a lista de modelos."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Reference</th>\n", "      <th>Turbo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lr</th>\n", "      <td>Logistic Regression</td>\n", "      <td>sklearn.linear_model._logistic.LogisticRegression</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>knn</th>\n", "      <td>K Neighbors Classifier</td>\n", "      <td>sklearn.neighbors._classification.KNeighborsCl...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>nb</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>sklearn.naive_bayes.GaussianNB</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <td>Decision Tree Classifier</td>\n", "      <td>sklearn.tree._classes.DecisionTreeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>svm</th>\n", "      <td>SVM - Linear Kernel</td>\n", "      <td>sklearn.linear_model._stochastic_gradient.SGDC...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rbfsvm</th>\n", "      <td>SVM - Radial <PERSON></td>\n", "      <td>sklearn.svm._classes.SVC</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gpc</th>\n", "      <td>Gaussian Process Classifier</td>\n", "      <td>sklearn.gaussian_process._gpc.GaussianProcessC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mlp</th>\n", "      <td>MLP Classifier</td>\n", "      <td>sklearn.neural_network._multilayer_perceptron....</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ridge</th>\n", "      <td>Ridge Classifier</td>\n", "      <td>sklearn.linear_model._ridge.RidgeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rf</th>\n", "      <td>Random Forest Classifier</td>\n", "      <td>sklearn.ensemble._forest.RandomForestClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>qda</th>\n", "      <td>Quadratic Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.QuadraticDiscrim...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ada</th>\n", "      <td>Ada Boost Classifier</td>\n", "      <td>sklearn.ensemble._weight_boosting.AdaBoostClas...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gbc</th>\n", "      <td>Gradient Boosting Classifier</td>\n", "      <td>sklearn.ensemble._gb.GradientBoostingClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lda</th>\n", "      <td>Linear Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.LinearDiscrimina...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>et</th>\n", "      <td>Extra Trees Classifier</td>\n", "      <td>sklearn.ensemble._forest.ExtraTreesClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>xgboost</th>\n", "      <td>Extreme Gradient Boosting</td>\n", "      <td>xgboost.sklearn.XGBClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lightgbm</th>\n", "      <td>Light Gradient Boosting Machine</td>\n", "      <td>lightgbm.sklearn.LGBMClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dummy</th>\n", "      <td>Dummy Classifier</td>\n", "      <td>sklearn.dummy.DummyClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     Name  \\\n", "ID                                          \n", "lr                    Logistic Regression   \n", "knn                K Neighbors Classifier   \n", "nb                            <PERSON><PERSON>   \n", "dt               Decision Tree Classifier   \n", "svm                   SVM - Linear Kernel   \n", "rbfsvm                SVM - Radial Kernel   \n", "gpc           Gaussian Process Classifier   \n", "mlp                        MLP Classifier   \n", "ridge                    Ridge Classifier   \n", "rf               Random Forest Classifier   \n", "qda       Quadratic Discriminant Analysis   \n", "ada                  Ada Boost Classifier   \n", "gbc          Gradient Boosting Classifier   \n", "lda          Linear Discriminant Analysis   \n", "et                 Extra Trees Classifier   \n", "xgboost         Extreme Gradient Boosting   \n", "lightgbm  Light Gradient Boosting Machine   \n", "dummy                    Dummy Classifier   \n", "\n", "                                                  Reference  Turbo  \n", "ID                                                                  \n", "lr        sklearn.linear_model._logistic.LogisticRegression   True  \n", "knn       sklearn.neighbors._classification.KNeighborsCl...   True  \n", "nb                           sklearn.naive_bayes.GaussianNB   True  \n", "dt             sklearn.tree._classes.DecisionTreeClassifier   True  \n", "svm       sklearn.linear_model._stochastic_gradient.SGDC...   True  \n", "rbfsvm                             sklearn.svm._classes.SVC  False  \n", "gpc       sklearn.gaussian_process._gpc.GaussianProcessC...  False  \n", "mlp       sklearn.neural_network._multilayer_perceptron....  False  \n", "ridge           sklearn.linear_model._ridge.RidgeClassifier   True  \n", "rf          sklearn.ensemble._forest.RandomForestClassifier   True  \n", "qda       sklearn.discriminant_analysis.QuadraticDiscrim...   True  \n", "ada       sklearn.ensemble._weight_boosting.AdaBoostClas...   True  \n", "gbc         sklearn.ensemble._gb.GradientBoostingClassifier   True  \n", "lda       sklearn.discriminant_analysis.LinearDiscrimina...   True  \n", "et            sklearn.ensemble._forest.ExtraTreesClassifier   True  \n", "xgboost                       xgboost.sklearn.XGBClassifier   True  \n", "lightgbm                    lightgbm.sklearn.LGBMClassifier   True  \n", "dummy                         sklearn.dummy.DummyClassifier   True  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["models()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "UWMSeyNhEQo-"}, "source": ["# 8.1 Decision Tree Classifier"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "LP896uSIEQpD", "outputId": "d6d31562-feb5-4052-ee23-0a444fecaacf"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_fb898_row10_col0, #T_fb898_row10_col1, #T_fb898_row10_col2, #T_fb898_row10_col3, #T_fb898_row10_col4, #T_fb898_row10_col5, #T_fb898_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_fb898\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_fb898_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_fb898_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_fb898_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_fb898_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_fb898_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_fb898_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_fb898_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_fb898_row0_col0\" class=\"data row0 col0\" >0.7343</td>\n", "      <td id=\"T_fb898_row0_col1\" class=\"data row0 col1\" >0.6257</td>\n", "      <td id=\"T_fb898_row0_col2\" class=\"data row0 col2\" >0.4327</td>\n", "      <td id=\"T_fb898_row0_col3\" class=\"data row0 col3\" >0.4005</td>\n", "      <td id=\"T_fb898_row0_col4\" class=\"data row0 col4\" >0.4160</td>\n", "      <td id=\"T_fb898_row0_col5\" class=\"data row0 col5\" >0.2444</td>\n", "      <td id=\"T_fb898_row0_col6\" class=\"data row0 col6\" >0.2447</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_fb898_row1_col0\" class=\"data row1 col0\" >0.7325</td>\n", "      <td id=\"T_fb898_row1_col1\" class=\"data row1 col1\" >0.6277</td>\n", "      <td id=\"T_fb898_row1_col2\" class=\"data row1 col2\" >0.4384</td>\n", "      <td id=\"T_fb898_row1_col3\" class=\"data row1 col3\" >0.3984</td>\n", "      <td id=\"T_fb898_row1_col4\" class=\"data row1 col4\" >0.4175</td>\n", "      <td id=\"T_fb898_row1_col5\" class=\"data row1 col5\" >0.2443</td>\n", "      <td id=\"T_fb898_row1_col6\" class=\"data row1 col6\" >0.2448</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_fb898_row2_col0\" class=\"data row2 col0\" >0.7431</td>\n", "      <td id=\"T_fb898_row2_col1\" class=\"data row2 col1\" >0.6282</td>\n", "      <td id=\"T_fb898_row2_col2\" class=\"data row2 col2\" >0.4241</td>\n", "      <td id=\"T_fb898_row2_col3\" class=\"data row2 col3\" >0.4146</td>\n", "      <td id=\"T_fb898_row2_col4\" class=\"data row2 col4\" >0.4193</td>\n", "      <td id=\"T_fb898_row2_col5\" class=\"data row2 col5\" >0.2544</td>\n", "      <td id=\"T_fb898_row2_col6\" class=\"data row2 col6\" >0.2544</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_fb898_row3_col0\" class=\"data row3 col0\" >0.7274</td>\n", "      <td id=\"T_fb898_row3_col1\" class=\"data row3 col1\" >0.6151</td>\n", "      <td id=\"T_fb898_row3_col2\" class=\"data row3 col2\" >0.4155</td>\n", "      <td id=\"T_fb898_row3_col3\" class=\"data row3 col3\" >0.3856</td>\n", "      <td id=\"T_fb898_row3_col4\" class=\"data row3 col4\" >0.4000</td>\n", "      <td id=\"T_fb898_row3_col5\" class=\"data row3 col5\" >0.2240</td>\n", "      <td id=\"T_fb898_row3_col6\" class=\"data row3 col6\" >0.2242</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_fb898_row4_col0\" class=\"data row4 col0\" >0.7187</td>\n", "      <td id=\"T_fb898_row4_col1\" class=\"data row4 col1\" >0.6054</td>\n", "      <td id=\"T_fb898_row4_col2\" class=\"data row4 col2\" >0.4040</td>\n", "      <td id=\"T_fb898_row4_col3\" class=\"data row4 col3\" >0.3691</td>\n", "      <td id=\"T_fb898_row4_col4\" class=\"data row4 col4\" >0.3858</td>\n", "      <td id=\"T_fb898_row4_col5\" class=\"data row4 col5\" >0.2038</td>\n", "      <td id=\"T_fb898_row4_col6\" class=\"data row4 col6\" >0.2042</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_fb898_row5_col0\" class=\"data row5 col0\" >0.7187</td>\n", "      <td id=\"T_fb898_row5_col1\" class=\"data row5 col1\" >0.6014</td>\n", "      <td id=\"T_fb898_row5_col2\" class=\"data row5 col2\" >0.3897</td>\n", "      <td id=\"T_fb898_row5_col3\" class=\"data row5 col3\" >0.3656</td>\n", "      <td id=\"T_fb898_row5_col4\" class=\"data row5 col4\" >0.3773</td>\n", "      <td id=\"T_fb898_row5_col5\" class=\"data row5 col5\" >0.1958</td>\n", "      <td id=\"T_fb898_row5_col6\" class=\"data row5 col6\" >0.1960</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_fb898_row6_col0\" class=\"data row6 col0\" >0.7206</td>\n", "      <td id=\"T_fb898_row6_col1\" class=\"data row6 col1\" >0.6128</td>\n", "      <td id=\"T_fb898_row6_col2\" class=\"data row6 col2\" >0.4212</td>\n", "      <td id=\"T_fb898_row6_col3\" class=\"data row6 col3\" >0.3760</td>\n", "      <td id=\"T_fb898_row6_col4\" class=\"data row6 col4\" >0.3973</td>\n", "      <td id=\"T_fb898_row6_col5\" class=\"data row6 col5\" >0.2162</td>\n", "      <td id=\"T_fb898_row6_col6\" class=\"data row6 col6\" >0.2168</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_fb898_row7_col0\" class=\"data row7 col0\" >0.7331</td>\n", "      <td id=\"T_fb898_row7_col1\" class=\"data row7 col1\" >0.5986</td>\n", "      <td id=\"T_fb898_row7_col2\" class=\"data row7 col2\" >0.3610</td>\n", "      <td id=\"T_fb898_row7_col3\" class=\"data row7 col3\" >0.3830</td>\n", "      <td id=\"T_fb898_row7_col4\" class=\"data row7 col4\" >0.3717</td>\n", "      <td id=\"T_fb898_row7_col5\" class=\"data row7 col5\" >0.2024</td>\n", "      <td id=\"T_fb898_row7_col6\" class=\"data row7 col6\" >0.2026</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_fb898_row8_col0\" class=\"data row8 col0\" >0.7206</td>\n", "      <td id=\"T_fb898_row8_col1\" class=\"data row8 col1\" >0.6045</td>\n", "      <td id=\"T_fb898_row8_col2\" class=\"data row8 col2\" >0.3983</td>\n", "      <td id=\"T_fb898_row8_col3\" class=\"data row8 col3\" >0.3707</td>\n", "      <td id=\"T_fb898_row8_col4\" class=\"data row8 col4\" >0.3840</td>\n", "      <td id=\"T_fb898_row8_col5\" class=\"data row8 col5\" >0.2036</td>\n", "      <td id=\"T_fb898_row8_col6\" class=\"data row8 col6\" >0.2038</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_fb898_row9_col0\" class=\"data row9 col0\" >0.7442</td>\n", "      <td id=\"T_fb898_row9_col1\" class=\"data row9 col1\" >0.6272</td>\n", "      <td id=\"T_fb898_row9_col2\" class=\"data row9 col2\" >0.4195</td>\n", "      <td id=\"T_fb898_row9_col3\" class=\"data row9 col3\" >0.4148</td>\n", "      <td id=\"T_fb898_row9_col4\" class=\"data row9 col4\" >0.4171</td>\n", "      <td id=\"T_fb898_row9_col5\" class=\"data row9 col5\" >0.2533</td>\n", "      <td id=\"T_fb898_row9_col6\" class=\"data row9 col6\" >0.2533</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_fb898_row10_col0\" class=\"data row10 col0\" >0.7293</td>\n", "      <td id=\"T_fb898_row10_col1\" class=\"data row10 col1\" >0.6147</td>\n", "      <td id=\"T_fb898_row10_col2\" class=\"data row10 col2\" >0.4104</td>\n", "      <td id=\"T_fb898_row10_col3\" class=\"data row10 col3\" >0.3878</td>\n", "      <td id=\"T_fb898_row10_col4\" class=\"data row10 col4\" >0.3986</td>\n", "      <td id=\"T_fb898_row10_col5\" class=\"data row10 col5\" >0.2242</td>\n", "      <td id=\"T_fb898_row10_col6\" class=\"data row10 col6\" >0.2245</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fb898_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_fb898_row11_col0\" class=\"data row11 col0\" >0.0092</td>\n", "      <td id=\"T_fb898_row11_col1\" class=\"data row11 col1\" >0.0112</td>\n", "      <td id=\"T_fb898_row11_col2\" class=\"data row11 col2\" >0.0218</td>\n", "      <td id=\"T_fb898_row11_col3\" class=\"data row11 col3\" >0.0174</td>\n", "      <td id=\"T_fb898_row11_col4\" class=\"data row11 col4\" >0.0173</td>\n", "      <td id=\"T_fb898_row11_col5\" class=\"data row11 col5\" >0.0218</td>\n", "      <td id=\"T_fb898_row11_col6\" class=\"data row11 col6\" >0.0218</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6817012e0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dt = create_model('dt')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {}, "colab_type": "code", "id": "FRat05yGEQpQ", "outputId": "c8e6a190-8bec-4646-d2c8-8a92b129c484"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='gini',\n", "                       max_depth=None, max_features=None, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.0, min_impurity_split=None,\n", "                       min_samples_leaf=1, min_samples_split=2,\n", "                       min_weight_fraction_leaf=0.0, presort='deprecated',\n", "                       random_state=123, splitter='best')\n"]}], "source": ["#O modelo treinado é guardado na variável 'dt'. \n", "print(dt)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "rWUojqBCEQpb"}, "source": ["# 8.2 K <PERSON>eighbors Classifier"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "2uonD20gEQpe", "outputId": "560e3cb6-41d5-4293-b1c5-2bd1cf3bc63b"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_bce43_row10_col0, #T_bce43_row10_col1, #T_bce43_row10_col2, #T_bce43_row10_col3, #T_bce43_row10_col4, #T_bce43_row10_col5, #T_bce43_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_bce43\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_bce43_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_bce43_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_bce43_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_bce43_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_bce43_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_bce43_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_bce43_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_bce43_row0_col0\" class=\"data row0 col0\" >0.7469</td>\n", "      <td id=\"T_bce43_row0_col1\" class=\"data row0 col1\" >0.6020</td>\n", "      <td id=\"T_bce43_row0_col2\" class=\"data row0 col2\" >0.1920</td>\n", "      <td id=\"T_bce43_row0_col3\" class=\"data row0 col3\" >0.3545</td>\n", "      <td id=\"T_bce43_row0_col4\" class=\"data row0 col4\" >0.2491</td>\n", "      <td id=\"T_bce43_row0_col5\" class=\"data row0 col5\" >0.1128</td>\n", "      <td id=\"T_bce43_row0_col6\" class=\"data row0 col6\" >0.1204</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_bce43_row1_col0\" class=\"data row1 col0\" >0.7550</td>\n", "      <td id=\"T_bce43_row1_col1\" class=\"data row1 col1\" >0.5894</td>\n", "      <td id=\"T_bce43_row1_col2\" class=\"data row1 col2\" >0.2092</td>\n", "      <td id=\"T_bce43_row1_col3\" class=\"data row1 col3\" >0.3883</td>\n", "      <td id=\"T_bce43_row1_col4\" class=\"data row1 col4\" >0.2719</td>\n", "      <td id=\"T_bce43_row1_col5\" class=\"data row1 col5\" >0.1402</td>\n", "      <td id=\"T_bce43_row1_col6\" class=\"data row1 col6\" >0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_bce43_row2_col0\" class=\"data row2 col0\" >0.7506</td>\n", "      <td id=\"T_bce43_row2_col1\" class=\"data row2 col1\" >0.5883</td>\n", "      <td id=\"T_bce43_row2_col2\" class=\"data row2 col2\" >0.1576</td>\n", "      <td id=\"T_bce43_row2_col3\" class=\"data row2 col3\" >0.3459</td>\n", "      <td id=\"T_bce43_row2_col4\" class=\"data row2 col4\" >0.2165</td>\n", "      <td id=\"T_bce43_row2_col5\" class=\"data row2 col5\" >0.0923</td>\n", "      <td id=\"T_bce43_row2_col6\" class=\"data row2 col6\" >0.1024</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_bce43_row3_col0\" class=\"data row3 col0\" >0.7419</td>\n", "      <td id=\"T_bce43_row3_col1\" class=\"data row3 col1\" >0.5818</td>\n", "      <td id=\"T_bce43_row3_col2\" class=\"data row3 col2\" >0.1519</td>\n", "      <td id=\"T_bce43_row3_col3\" class=\"data row3 col3\" >0.3136</td>\n", "      <td id=\"T_bce43_row3_col4\" class=\"data row3 col4\" >0.2046</td>\n", "      <td id=\"T_bce43_row3_col5\" class=\"data row3 col5\" >0.0723</td>\n", "      <td id=\"T_bce43_row3_col6\" class=\"data row3 col6\" >0.0790</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_bce43_row4_col0\" class=\"data row4 col0\" >0.7563</td>\n", "      <td id=\"T_bce43_row4_col1\" class=\"data row4 col1\" >0.5908</td>\n", "      <td id=\"T_bce43_row4_col2\" class=\"data row4 col2\" >0.1490</td>\n", "      <td id=\"T_bce43_row4_col3\" class=\"data row4 col3\" >0.3611</td>\n", "      <td id=\"T_bce43_row4_col4\" class=\"data row4 col4\" >0.2110</td>\n", "      <td id=\"T_bce43_row4_col5\" class=\"data row4 col5\" >0.0954</td>\n", "      <td id=\"T_bce43_row4_col6\" class=\"data row4 col6\" >0.1085</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_bce43_row5_col0\" class=\"data row5 col0\" >0.7550</td>\n", "      <td id=\"T_bce43_row5_col1\" class=\"data row5 col1\" >0.5997</td>\n", "      <td id=\"T_bce43_row5_col2\" class=\"data row5 col2\" >0.1748</td>\n", "      <td id=\"T_bce43_row5_col3\" class=\"data row5 col3\" >0.3720</td>\n", "      <td id=\"T_bce43_row5_col4\" class=\"data row5 col4\" >0.2378</td>\n", "      <td id=\"T_bce43_row5_col5\" class=\"data row5 col5\" >0.1139</td>\n", "      <td id=\"T_bce43_row5_col6\" class=\"data row5 col6\" >0.1255</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_bce43_row6_col0\" class=\"data row6 col0\" >0.7638</td>\n", "      <td id=\"T_bce43_row6_col1\" class=\"data row6 col1\" >0.5890</td>\n", "      <td id=\"T_bce43_row6_col2\" class=\"data row6 col2\" >0.1891</td>\n", "      <td id=\"T_bce43_row6_col3\" class=\"data row6 col3\" >0.4125</td>\n", "      <td id=\"T_bce43_row6_col4\" class=\"data row6 col4\" >0.2593</td>\n", "      <td id=\"T_bce43_row6_col5\" class=\"data row6 col5\" >0.1413</td>\n", "      <td id=\"T_bce43_row6_col6\" class=\"data row6 col6\" >0.1565</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_bce43_row7_col0\" class=\"data row7 col0\" >0.7613</td>\n", "      <td id=\"T_bce43_row7_col1\" class=\"data row7 col1\" >0.6240</td>\n", "      <td id=\"T_bce43_row7_col2\" class=\"data row7 col2\" >0.1633</td>\n", "      <td id=\"T_bce43_row7_col3\" class=\"data row7 col3\" >0.3904</td>\n", "      <td id=\"T_bce43_row7_col4\" class=\"data row7 col4\" >0.2303</td>\n", "      <td id=\"T_bce43_row7_col5\" class=\"data row7 col5\" >0.1163</td>\n", "      <td id=\"T_bce43_row7_col6\" class=\"data row7 col6\" >0.1318</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_bce43_row8_col0\" class=\"data row8 col0\" >0.7619</td>\n", "      <td id=\"T_bce43_row8_col1\" class=\"data row8 col1\" >0.5988</td>\n", "      <td id=\"T_bce43_row8_col2\" class=\"data row8 col2\" >0.1862</td>\n", "      <td id=\"T_bce43_row8_col3\" class=\"data row8 col3\" >0.4037</td>\n", "      <td id=\"T_bce43_row8_col4\" class=\"data row8 col4\" >0.2549</td>\n", "      <td id=\"T_bce43_row8_col5\" class=\"data row8 col5\" >0.1356</td>\n", "      <td id=\"T_bce43_row8_col6\" class=\"data row8 col6\" >0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_bce43_row9_col0\" class=\"data row9 col0\" >0.7549</td>\n", "      <td id=\"T_bce43_row9_col1\" class=\"data row9 col1\" >0.5756</td>\n", "      <td id=\"T_bce43_row9_col2\" class=\"data row9 col2\" >0.1897</td>\n", "      <td id=\"T_bce43_row9_col3\" class=\"data row9 col3\" >0.3771</td>\n", "      <td id=\"T_bce43_row9_col4\" class=\"data row9 col4\" >0.2524</td>\n", "      <td id=\"T_bce43_row9_col5\" class=\"data row9 col5\" >0.1246</td>\n", "      <td id=\"T_bce43_row9_col6\" class=\"data row9 col6\" >0.1351</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_bce43_row10_col0\" class=\"data row10 col0\" >0.7547</td>\n", "      <td id=\"T_bce43_row10_col1\" class=\"data row10 col1\" >0.5939</td>\n", "      <td id=\"T_bce43_row10_col2\" class=\"data row10 col2\" >0.1763</td>\n", "      <td id=\"T_bce43_row10_col3\" class=\"data row10 col3\" >0.3719</td>\n", "      <td id=\"T_bce43_row10_col4\" class=\"data row10 col4\" >0.2388</td>\n", "      <td id=\"T_bce43_row10_col5\" class=\"data row10 col5\" >0.1145</td>\n", "      <td id=\"T_bce43_row10_col6\" class=\"data row10 col6\" >0.1259</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bce43_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_bce43_row11_col0\" class=\"data row11 col0\" >0.0065</td>\n", "      <td id=\"T_bce43_row11_col1\" class=\"data row11 col1\" >0.0126</td>\n", "      <td id=\"T_bce43_row11_col2\" class=\"data row11 col2\" >0.0191</td>\n", "      <td id=\"T_bce43_row11_col3\" class=\"data row11 col3\" >0.0279</td>\n", "      <td id=\"T_bce43_row11_col4\" class=\"data row11 col4\" >0.0214</td>\n", "      <td id=\"T_bce43_row11_col5\" class=\"data row11 col5\" >0.0214</td>\n", "      <td id=\"T_bce43_row11_col6\" class=\"data row11 col6\" >0.0230</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e682286e80>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["knn = create_model('knn')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nSg3OUjuEQpu"}, "source": ["# 8.3 Random Forest Classifier"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "FGCoUiQpEQpz", "outputId": "212cb736-6dcb-4b77-e45b-14ad895bff43"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_81e0f_row10_col0, #T_81e0f_row10_col1, #T_81e0f_row10_col2, #T_81e0f_row10_col3, #T_81e0f_row10_col4, #T_81e0f_row10_col5, #T_81e0f_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_81e0f\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_81e0f_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_81e0f_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_81e0f_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_81e0f_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_81e0f_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_81e0f_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_81e0f_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_81e0f_row0_col0\" class=\"data row0 col0\" >0.8133</td>\n", "      <td id=\"T_81e0f_row0_col1\" class=\"data row0 col1\" >0.7673</td>\n", "      <td id=\"T_81e0f_row0_col2\" class=\"data row0 col2\" >0.3610</td>\n", "      <td id=\"T_81e0f_row0_col3\" class=\"data row0 col3\" >0.6269</td>\n", "      <td id=\"T_81e0f_row0_col4\" class=\"data row0 col4\" >0.4582</td>\n", "      <td id=\"T_81e0f_row0_col5\" class=\"data row0 col5\" >0.3551</td>\n", "      <td id=\"T_81e0f_row0_col6\" class=\"data row0 col6\" >0.3749</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_81e0f_row1_col0\" class=\"data row1 col0\" >0.8239</td>\n", "      <td id=\"T_81e0f_row1_col1\" class=\"data row1 col1\" >0.7615</td>\n", "      <td id=\"T_81e0f_row1_col2\" class=\"data row1 col2\" >0.3782</td>\n", "      <td id=\"T_81e0f_row1_col3\" class=\"data row1 col3\" >0.6735</td>\n", "      <td id=\"T_81e0f_row1_col4\" class=\"data row1 col4\" >0.4844</td>\n", "      <td id=\"T_81e0f_row1_col5\" class=\"data row1 col5\" >0.3882</td>\n", "      <td id=\"T_81e0f_row1_col6\" class=\"data row1 col6\" >0.4117</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_81e0f_row2_col0\" class=\"data row2 col0\" >0.8258</td>\n", "      <td id=\"T_81e0f_row2_col1\" class=\"data row2 col1\" >0.7708</td>\n", "      <td id=\"T_81e0f_row2_col2\" class=\"data row2 col2\" >0.3467</td>\n", "      <td id=\"T_81e0f_row2_col3\" class=\"data row2 col3\" >0.7076</td>\n", "      <td id=\"T_81e0f_row2_col4\" class=\"data row2 col4\" >0.4654</td>\n", "      <td id=\"T_81e0f_row2_col5\" class=\"data row2 col5\" >0.3756</td>\n", "      <td id=\"T_81e0f_row2_col6\" class=\"data row2 col6\" >0.4098</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_81e0f_row3_col0\" class=\"data row3 col0\" >0.8177</td>\n", "      <td id=\"T_81e0f_row3_col1\" class=\"data row3 col1\" >0.7605</td>\n", "      <td id=\"T_81e0f_row3_col2\" class=\"data row3 col2\" >0.3725</td>\n", "      <td id=\"T_81e0f_row3_col3\" class=\"data row3 col3\" >0.6436</td>\n", "      <td id=\"T_81e0f_row3_col4\" class=\"data row3 col4\" >0.4719</td>\n", "      <td id=\"T_81e0f_row3_col5\" class=\"data row3 col5\" >0.3710</td>\n", "      <td id=\"T_81e0f_row3_col6\" class=\"data row3 col6\" >0.3913</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_81e0f_row4_col0\" class=\"data row4 col0\" >0.8208</td>\n", "      <td id=\"T_81e0f_row4_col1\" class=\"data row4 col1\" >0.7642</td>\n", "      <td id=\"T_81e0f_row4_col2\" class=\"data row4 col2\" >0.3725</td>\n", "      <td id=\"T_81e0f_row4_col3\" class=\"data row4 col3\" >0.6599</td>\n", "      <td id=\"T_81e0f_row4_col4\" class=\"data row4 col4\" >0.4762</td>\n", "      <td id=\"T_81e0f_row4_col5\" class=\"data row4 col5\" >0.3780</td>\n", "      <td id=\"T_81e0f_row4_col6\" class=\"data row4 col6\" >0.4006</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_81e0f_row5_col0\" class=\"data row5 col0\" >0.8283</td>\n", "      <td id=\"T_81e0f_row5_col1\" class=\"data row5 col1\" >0.7638</td>\n", "      <td id=\"T_81e0f_row5_col2\" class=\"data row5 col2\" >0.3954</td>\n", "      <td id=\"T_81e0f_row5_col3\" class=\"data row5 col3\" >0.6866</td>\n", "      <td id=\"T_81e0f_row5_col4\" class=\"data row5 col4\" >0.5018</td>\n", "      <td id=\"T_81e0f_row5_col5\" class=\"data row5 col5\" >0.4070</td>\n", "      <td id=\"T_81e0f_row5_col6\" class=\"data row5 col6\" >0.4297</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_81e0f_row6_col0\" class=\"data row6 col0\" >0.8127</td>\n", "      <td id=\"T_81e0f_row6_col1\" class=\"data row6 col1\" >0.7647</td>\n", "      <td id=\"T_81e0f_row6_col2\" class=\"data row6 col2\" >0.3582</td>\n", "      <td id=\"T_81e0f_row6_col3\" class=\"data row6 col3\" >0.6250</td>\n", "      <td id=\"T_81e0f_row6_col4\" class=\"data row6 col4\" >0.4554</td>\n", "      <td id=\"T_81e0f_row6_col5\" class=\"data row6 col5\" >0.3522</td>\n", "      <td id=\"T_81e0f_row6_col6\" class=\"data row6 col6\" >0.3721</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_81e0f_row7_col0\" class=\"data row7 col0\" >0.8283</td>\n", "      <td id=\"T_81e0f_row7_col1\" class=\"data row7 col1\" >0.7390</td>\n", "      <td id=\"T_81e0f_row7_col2\" class=\"data row7 col2\" >0.3553</td>\n", "      <td id=\"T_81e0f_row7_col3\" class=\"data row7 col3\" >0.7168</td>\n", "      <td id=\"T_81e0f_row7_col4\" class=\"data row7 col4\" >0.4751</td>\n", "      <td id=\"T_81e0f_row7_col5\" class=\"data row7 col5\" >0.3861</td>\n", "      <td id=\"T_81e0f_row7_col6\" class=\"data row7 col6\" >0.4202</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_81e0f_row8_col0\" class=\"data row8 col0\" >0.8108</td>\n", "      <td id=\"T_81e0f_row8_col1\" class=\"data row8 col1\" >0.7496</td>\n", "      <td id=\"T_81e0f_row8_col2\" class=\"data row8 col2\" >0.3610</td>\n", "      <td id=\"T_81e0f_row8_col3\" class=\"data row8 col3\" >0.6146</td>\n", "      <td id=\"T_81e0f_row8_col4\" class=\"data row8 col4\" >0.4549</td>\n", "      <td id=\"T_81e0f_row8_col5\" class=\"data row8 col5\" >0.3496</td>\n", "      <td id=\"T_81e0f_row8_col6\" class=\"data row8 col6\" >0.3678</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_81e0f_row9_col0\" class=\"data row9 col0\" >0.8176</td>\n", "      <td id=\"T_81e0f_row9_col1\" class=\"data row9 col1\" >0.7565</td>\n", "      <td id=\"T_81e0f_row9_col2\" class=\"data row9 col2\" >0.3621</td>\n", "      <td id=\"T_81e0f_row9_col3\" class=\"data row9 col3\" >0.6462</td>\n", "      <td id=\"T_81e0f_row9_col4\" class=\"data row9 col4\" >0.4641</td>\n", "      <td id=\"T_81e0f_row9_col5\" class=\"data row9 col5\" >0.3645</td>\n", "      <td id=\"T_81e0f_row9_col6\" class=\"data row9 col6\" >0.3867</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_81e0f_row10_col0\" class=\"data row10 col0\" >0.8199</td>\n", "      <td id=\"T_81e0f_row10_col1\" class=\"data row10 col1\" >0.7598</td>\n", "      <td id=\"T_81e0f_row10_col2\" class=\"data row10 col2\" >0.3663</td>\n", "      <td id=\"T_81e0f_row10_col3\" class=\"data row10 col3\" >0.6601</td>\n", "      <td id=\"T_81e0f_row10_col4\" class=\"data row10 col4\" >0.4707</td>\n", "      <td id=\"T_81e0f_row10_col5\" class=\"data row10 col5\" >0.3727</td>\n", "      <td id=\"T_81e0f_row10_col6\" class=\"data row10 col6\" >0.3965</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_81e0f_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_81e0f_row11_col0\" class=\"data row11 col0\" >0.0062</td>\n", "      <td id=\"T_81e0f_row11_col1\" class=\"data row11 col1\" >0.0089</td>\n", "      <td id=\"T_81e0f_row11_col2\" class=\"data row11 col2\" >0.0131</td>\n", "      <td id=\"T_81e0f_row11_col3\" class=\"data row11 col3\" >0.0335</td>\n", "      <td id=\"T_81e0f_row11_col4\" class=\"data row11 col4\" >0.0139</td>\n", "      <td id=\"T_81e0f_row11_col5\" class=\"data row11 col5\" >0.0172</td>\n", "      <td id=\"T_81e0f_row11_col6\" class=\"data row11 col6\" >0.0202</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e68220ac70>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rf = create_model('rf')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "z6F3Fk7TEQp8"}, "source": ["Observe que a pontuação média de todos os modelos corresponde à pontuação impressa em `compare_models()`. <PERSON><PERSON> ocorre porque as métricas impressas na grade de pontuação `compare_models()` s<PERSON> as pontuações médias em todos os folds do CV (cross-validation). Semelhante a `compare_models()`, se você quiser alterar o parâmetro fold do valor padrão de 10 para um valor diferente, então você pode usar o parâmetro `fold`. Por exemplo: `create_model('dt', fold = 5)` criará um classificador de árvore de decisão usando CV estratificado de 5 vezes."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XvpjzbGQEQqB"}, "source": ["# 9.0 Model tunning (Hyperparameter Tunning)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nc_GgksHEQqE"}, "source": ["Quando um modelo é criado usando a função `create_model()`, ele usa os hiperparâmetros padrão para treinar o modelo. Para ajustar os hiperparâmetros, a função `tune_model()` é usada. Esta função ajusta automaticamente os hiperparâmetros de um modelo usando `Random Grid Search` em um espaço de busca pré-definido. A saída imprime uma grade de pontuação que mostra Precisão, AUC, Recall, Precisão, F1, Kappa e MCC por dobra para o melhor modelo. Para usar a grade de pesquisa personalizada, você pode passar o parâmetro `custom_grid` na função `tune_model` (veja 9.2 Ajuste de KNN abaixo). <br/>\n", "<br/>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "BQlMCxrUEQqG"}, "source": ["# 9.1 Classificação usando Decision Tree"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "of46aj6vEQqJ", "outputId": "26f7f708-739a-489b-bb76-b33e0a800362"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_93950_row10_col0, #T_93950_row10_col1, #T_93950_row10_col2, #T_93950_row10_col3, #T_93950_row10_col4, #T_93950_row10_col5, #T_93950_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_93950\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_93950_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_93950_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_93950_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_93950_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_93950_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_93950_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_93950_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_93950_row0_col0\" class=\"data row0 col0\" >0.8177</td>\n", "      <td id=\"T_93950_row0_col1\" class=\"data row0 col1\" >0.7475</td>\n", "      <td id=\"T_93950_row0_col2\" class=\"data row0 col2\" >0.3095</td>\n", "      <td id=\"T_93950_row0_col3\" class=\"data row0 col3\" >0.6835</td>\n", "      <td id=\"T_93950_row0_col4\" class=\"data row0 col4\" >0.4260</td>\n", "      <td id=\"T_93950_row0_col5\" class=\"data row0 col5\" >0.3355</td>\n", "      <td id=\"T_93950_row0_col6\" class=\"data row0 col6\" >0.3728</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_93950_row1_col0\" class=\"data row1 col0\" >0.8289</td>\n", "      <td id=\"T_93950_row1_col1\" class=\"data row1 col1\" >0.7711</td>\n", "      <td id=\"T_93950_row1_col2\" class=\"data row1 col2\" >0.3381</td>\n", "      <td id=\"T_93950_row1_col3\" class=\"data row1 col3\" >0.7375</td>\n", "      <td id=\"T_93950_row1_col4\" class=\"data row1 col4\" >0.4637</td>\n", "      <td id=\"T_93950_row1_col5\" class=\"data row1 col5\" >0.3782</td>\n", "      <td id=\"T_93950_row1_col6\" class=\"data row1 col6\" >0.4190</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_93950_row2_col0\" class=\"data row2 col0\" >0.8208</td>\n", "      <td id=\"T_93950_row2_col1\" class=\"data row2 col1\" >0.7377</td>\n", "      <td id=\"T_93950_row2_col2\" class=\"data row2 col2\" >0.2894</td>\n", "      <td id=\"T_93950_row2_col3\" class=\"data row2 col3\" >0.7266</td>\n", "      <td id=\"T_93950_row2_col4\" class=\"data row2 col4\" >0.4139</td>\n", "      <td id=\"T_93950_row2_col5\" class=\"data row2 col5\" >0.3305</td>\n", "      <td id=\"T_93950_row2_col6\" class=\"data row2 col6\" >0.3796</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_93950_row3_col0\" class=\"data row3 col0\" >0.8252</td>\n", "      <td id=\"T_93950_row3_col1\" class=\"data row3 col1\" >0.7580</td>\n", "      <td id=\"T_93950_row3_col2\" class=\"data row3 col2\" >0.3152</td>\n", "      <td id=\"T_93950_row3_col3\" class=\"data row3 col3\" >0.7333</td>\n", "      <td id=\"T_93950_row3_col4\" class=\"data row3 col4\" >0.4409</td>\n", "      <td id=\"T_93950_row3_col5\" class=\"data row3 col5\" >0.3563</td>\n", "      <td id=\"T_93950_row3_col6\" class=\"data row3 col6\" >0.4010</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_93950_row4_col0\" class=\"data row4 col0\" >0.8195</td>\n", "      <td id=\"T_93950_row4_col1\" class=\"data row4 col1\" >0.7545</td>\n", "      <td id=\"T_93950_row4_col2\" class=\"data row4 col2\" >0.3095</td>\n", "      <td id=\"T_93950_row4_col3\" class=\"data row4 col3\" >0.6968</td>\n", "      <td id=\"T_93950_row4_col4\" class=\"data row4 col4\" >0.4286</td>\n", "      <td id=\"T_93950_row4_col5\" class=\"data row4 col5\" >0.3398</td>\n", "      <td id=\"T_93950_row4_col6\" class=\"data row4 col6\" >0.3794</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_93950_row5_col0\" class=\"data row5 col0\" >0.8271</td>\n", "      <td id=\"T_93950_row5_col1\" class=\"data row5 col1\" >0.7509</td>\n", "      <td id=\"T_93950_row5_col2\" class=\"data row5 col2\" >0.3438</td>\n", "      <td id=\"T_93950_row5_col3\" class=\"data row5 col3\" >0.7186</td>\n", "      <td id=\"T_93950_row5_col4\" class=\"data row5 col4\" >0.4651</td>\n", "      <td id=\"T_93950_row5_col5\" class=\"data row5 col5\" >0.3769</td>\n", "      <td id=\"T_93950_row5_col6\" class=\"data row5 col6\" >0.4134</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_93950_row6_col0\" class=\"data row6 col0\" >0.8195</td>\n", "      <td id=\"T_93950_row6_col1\" class=\"data row6 col1\" >0.7488</td>\n", "      <td id=\"T_93950_row6_col2\" class=\"data row6 col2\" >0.3123</td>\n", "      <td id=\"T_93950_row6_col3\" class=\"data row6 col3\" >0.6943</td>\n", "      <td id=\"T_93950_row6_col4\" class=\"data row6 col4\" >0.4308</td>\n", "      <td id=\"T_93950_row6_col5\" class=\"data row6 col5\" >0.3415</td>\n", "      <td id=\"T_93950_row6_col6\" class=\"data row6 col6\" >0.3801</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_93950_row7_col0\" class=\"data row7 col0\" >0.8246</td>\n", "      <td id=\"T_93950_row7_col1\" class=\"data row7 col1\" >0.7529</td>\n", "      <td id=\"T_93950_row7_col2\" class=\"data row7 col2\" >0.2980</td>\n", "      <td id=\"T_93950_row7_col3\" class=\"data row7 col3\" >0.7482</td>\n", "      <td id=\"T_93950_row7_col4\" class=\"data row7 col4\" >0.4262</td>\n", "      <td id=\"T_93950_row7_col5\" class=\"data row7 col5\" >0.3446</td>\n", "      <td id=\"T_93950_row7_col6\" class=\"data row7 col6\" >0.3957</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_93950_row8_col0\" class=\"data row8 col0\" >0.8195</td>\n", "      <td id=\"T_93950_row8_col1\" class=\"data row8 col1\" >0.7241</td>\n", "      <td id=\"T_93950_row8_col2\" class=\"data row8 col2\" >0.3123</td>\n", "      <td id=\"T_93950_row8_col3\" class=\"data row8 col3\" >0.6943</td>\n", "      <td id=\"T_93950_row8_col4\" class=\"data row8 col4\" >0.4308</td>\n", "      <td id=\"T_93950_row8_col5\" class=\"data row8 col5\" >0.3415</td>\n", "      <td id=\"T_93950_row8_col6\" class=\"data row8 col6\" >0.3801</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_93950_row9_col0\" class=\"data row9 col0\" >0.8188</td>\n", "      <td id=\"T_93950_row9_col1\" class=\"data row9 col1\" >0.7378</td>\n", "      <td id=\"T_93950_row9_col2\" class=\"data row9 col2\" >0.3075</td>\n", "      <td id=\"T_93950_row9_col3\" class=\"data row9 col3\" >0.6903</td>\n", "      <td id=\"T_93950_row9_col4\" class=\"data row9 col4\" >0.4254</td>\n", "      <td id=\"T_93950_row9_col5\" class=\"data row9 col5\" >0.3362</td>\n", "      <td id=\"T_93950_row9_col6\" class=\"data row9 col6\" >0.3751</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_93950_row10_col0\" class=\"data row10 col0\" >0.8222</td>\n", "      <td id=\"T_93950_row10_col1\" class=\"data row10 col1\" >0.7483</td>\n", "      <td id=\"T_93950_row10_col2\" class=\"data row10 col2\" >0.3136</td>\n", "      <td id=\"T_93950_row10_col3\" class=\"data row10 col3\" >0.7123</td>\n", "      <td id=\"T_93950_row10_col4\" class=\"data row10 col4\" >0.4352</td>\n", "      <td id=\"T_93950_row10_col5\" class=\"data row10 col5\" >0.3481</td>\n", "      <td id=\"T_93950_row10_col6\" class=\"data row10 col6\" >0.3896</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_93950_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_93950_row11_col0\" class=\"data row11 col0\" >0.0037</td>\n", "      <td id=\"T_93950_row11_col1\" class=\"data row11 col1\" >0.0122</td>\n", "      <td id=\"T_93950_row11_col2\" class=\"data row11 col2\" >0.0156</td>\n", "      <td id=\"T_93950_row11_col3\" class=\"data row11 col3\" >0.0219</td>\n", "      <td id=\"T_93950_row11_col4\" class=\"data row11 col4\" >0.0159</td>\n", "      <td id=\"T_93950_row11_col5\" class=\"data row11 col5\" >0.0161</td>\n", "      <td id=\"T_93950_row11_col6\" class=\"data row11 col6\" >0.0158</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6867e03a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_dt = tune_model(dt)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {}, "colab_type": "code", "id": "__anDkttEQqV", "outputId": "7cf46ace-012a-4131-b8b8-370f9d4a63cb"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='entropy',\n", "                       max_depth=6, max_features=1.0, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.002, min_impurity_split=None,\n", "                       min_samples_leaf=5, min_samples_split=5,\n", "                       min_weight_fraction_leaf=0.0, presort='deprecated',\n", "                       random_state=123, splitter='best')\n"]}], "source": ["#modelo \"tunado\" está guardado na variável 'tuned_dt'. \n", "print(tuned_dt)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "CD-f0delEQqq"}, "source": ["# 9.2 Classificação usando K Neighbors"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "xN1nYwFXEQqv", "outputId": "e4ab669d-bee0-4a9d-f5c7-2ed07ec613b9"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_4f50d_row10_col0, #T_4f50d_row10_col1, #T_4f50d_row10_col2, #T_4f50d_row10_col3, #T_4f50d_row10_col4, #T_4f50d_row10_col5, #T_4f50d_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_4f50d\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_4f50d_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_4f50d_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_4f50d_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_4f50d_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_4f50d_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_4f50d_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_4f50d_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_4f50d_row0_col0\" class=\"data row0 col0\" >0.7813</td>\n", "      <td id=\"T_4f50d_row0_col1\" class=\"data row0 col1\" >0.6482</td>\n", "      <td id=\"T_4f50d_row0_col2\" class=\"data row0 col2\" >0.0372</td>\n", "      <td id=\"T_4f50d_row0_col3\" class=\"data row0 col3\" >0.5000</td>\n", "      <td id=\"T_4f50d_row0_col4\" class=\"data row0 col4\" >0.0693</td>\n", "      <td id=\"T_4f50d_row0_col5\" class=\"data row0 col5\" >0.0402</td>\n", "      <td id=\"T_4f50d_row0_col6\" class=\"data row0 col6\" >0.0876</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_4f50d_row1_col0\" class=\"data row1 col0\" >0.7807</td>\n", "      <td id=\"T_4f50d_row1_col1\" class=\"data row1 col1\" >0.6436</td>\n", "      <td id=\"T_4f50d_row1_col2\" class=\"data row1 col2\" >0.0315</td>\n", "      <td id=\"T_4f50d_row1_col3\" class=\"data row1 col3\" >0.4783</td>\n", "      <td id=\"T_4f50d_row1_col4\" class=\"data row1 col4\" >0.0591</td>\n", "      <td id=\"T_4f50d_row1_col5\" class=\"data row1 col5\" >0.0330</td>\n", "      <td id=\"T_4f50d_row1_col6\" class=\"data row1 col6\" >0.0759</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_4f50d_row2_col0\" class=\"data row2 col0\" >0.7744</td>\n", "      <td id=\"T_4f50d_row2_col1\" class=\"data row2 col1\" >0.6563</td>\n", "      <td id=\"T_4f50d_row2_col2\" class=\"data row2 col2\" >0.0315</td>\n", "      <td id=\"T_4f50d_row2_col3\" class=\"data row2 col3\" >0.3333</td>\n", "      <td id=\"T_4f50d_row2_col4\" class=\"data row2 col4\" >0.0576</td>\n", "      <td id=\"T_4f50d_row2_col5\" class=\"data row2 col5\" >0.0206</td>\n", "      <td id=\"T_4f50d_row2_col6\" class=\"data row2 col6\" >0.0403</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_4f50d_row3_col0\" class=\"data row3 col0\" >0.7845</td>\n", "      <td id=\"T_4f50d_row3_col1\" class=\"data row3 col1\" >0.6589</td>\n", "      <td id=\"T_4f50d_row3_col2\" class=\"data row3 col2\" >0.0659</td>\n", "      <td id=\"T_4f50d_row3_col3\" class=\"data row3 col3\" >0.5610</td>\n", "      <td id=\"T_4f50d_row3_col4\" class=\"data row3 col4\" >0.1179</td>\n", "      <td id=\"T_4f50d_row3_col5\" class=\"data row3 col5\" >0.0754</td>\n", "      <td id=\"T_4f50d_row3_col6\" class=\"data row3 col6\" >0.1345</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_4f50d_row4_col0\" class=\"data row4 col0\" >0.7826</td>\n", "      <td id=\"T_4f50d_row4_col1\" class=\"data row4 col1\" >0.6645</td>\n", "      <td id=\"T_4f50d_row4_col2\" class=\"data row4 col2\" >0.0315</td>\n", "      <td id=\"T_4f50d_row4_col3\" class=\"data row4 col3\" >0.5500</td>\n", "      <td id=\"T_4f50d_row4_col4\" class=\"data row4 col4\" >0.0596</td>\n", "      <td id=\"T_4f50d_row4_col5\" class=\"data row4 col5\" >0.0368</td>\n", "      <td id=\"T_4f50d_row4_col6\" class=\"data row4 col6\" >0.0903</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_4f50d_row5_col0\" class=\"data row5 col0\" >0.7794</td>\n", "      <td id=\"T_4f50d_row5_col1\" class=\"data row5 col1\" >0.6477</td>\n", "      <td id=\"T_4f50d_row5_col2\" class=\"data row5 col2\" >0.0544</td>\n", "      <td id=\"T_4f50d_row5_col3\" class=\"data row5 col3\" >0.4634</td>\n", "      <td id=\"T_4f50d_row5_col4\" class=\"data row5 col4\" >0.0974</td>\n", "      <td id=\"T_4f50d_row5_col5\" class=\"data row5 col5\" >0.0539</td>\n", "      <td id=\"T_4f50d_row5_col6\" class=\"data row5 col6\" >0.0961</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_4f50d_row6_col0\" class=\"data row6 col0\" >0.7826</td>\n", "      <td id=\"T_4f50d_row6_col1\" class=\"data row6 col1\" >0.6278</td>\n", "      <td id=\"T_4f50d_row6_col2\" class=\"data row6 col2\" >0.0630</td>\n", "      <td id=\"T_4f50d_row6_col3\" class=\"data row6 col3\" >0.5238</td>\n", "      <td id=\"T_4f50d_row6_col4\" class=\"data row6 col4\" >0.1125</td>\n", "      <td id=\"T_4f50d_row6_col5\" class=\"data row6 col5\" >0.0688</td>\n", "      <td id=\"T_4f50d_row6_col6\" class=\"data row6 col6\" >0.1214</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_4f50d_row7_col0\" class=\"data row7 col0\" >0.7751</td>\n", "      <td id=\"T_4f50d_row7_col1\" class=\"data row7 col1\" >0.6702</td>\n", "      <td id=\"T_4f50d_row7_col2\" class=\"data row7 col2\" >0.0372</td>\n", "      <td id=\"T_4f50d_row7_col3\" class=\"data row7 col3\" >0.3611</td>\n", "      <td id=\"T_4f50d_row7_col4\" class=\"data row7 col4\" >0.0675</td>\n", "      <td id=\"T_4f50d_row7_col5\" class=\"data row7 col5\" >0.0278</td>\n", "      <td id=\"T_4f50d_row7_col6\" class=\"data row7 col6\" >0.0523</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_4f50d_row8_col0\" class=\"data row8 col0\" >0.7813</td>\n", "      <td id=\"T_4f50d_row8_col1\" class=\"data row8 col1\" >0.6409</td>\n", "      <td id=\"T_4f50d_row8_col2\" class=\"data row8 col2\" >0.0630</td>\n", "      <td id=\"T_4f50d_row8_col3\" class=\"data row8 col3\" >0.5000</td>\n", "      <td id=\"T_4f50d_row8_col4\" class=\"data row8 col4\" >0.1120</td>\n", "      <td id=\"T_4f50d_row8_col5\" class=\"data row8 col5\" >0.0662</td>\n", "      <td id=\"T_4f50d_row8_col6\" class=\"data row8 col6\" >0.1146</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_4f50d_row9_col0\" class=\"data row9 col0\" >0.7881</td>\n", "      <td id=\"T_4f50d_row9_col1\" class=\"data row9 col1\" >0.6426</td>\n", "      <td id=\"T_4f50d_row9_col2\" class=\"data row9 col2\" >0.0661</td>\n", "      <td id=\"T_4f50d_row9_col3\" class=\"data row9 col3\" >0.6389</td>\n", "      <td id=\"T_4f50d_row9_col4\" class=\"data row9 col4\" >0.1198</td>\n", "      <td id=\"T_4f50d_row9_col5\" class=\"data row9 col5\" >0.0822</td>\n", "      <td id=\"T_4f50d_row9_col6\" class=\"data row9 col6\" >0.1548</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_4f50d_row10_col0\" class=\"data row10 col0\" >0.7810</td>\n", "      <td id=\"T_4f50d_row10_col1\" class=\"data row10 col1\" >0.6501</td>\n", "      <td id=\"T_4f50d_row10_col2\" class=\"data row10 col2\" >0.0482</td>\n", "      <td id=\"T_4f50d_row10_col3\" class=\"data row10 col3\" >0.4910</td>\n", "      <td id=\"T_4f50d_row10_col4\" class=\"data row10 col4\" >0.0873</td>\n", "      <td id=\"T_4f50d_row10_col5\" class=\"data row10 col5\" >0.0505</td>\n", "      <td id=\"T_4f50d_row10_col6\" class=\"data row10 col6\" >0.0968</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4f50d_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_4f50d_row11_col0\" class=\"data row11 col0\" >0.0039</td>\n", "      <td id=\"T_4f50d_row11_col1\" class=\"data row11 col1\" >0.0119</td>\n", "      <td id=\"T_4f50d_row11_col2\" class=\"data row11 col2\" >0.0148</td>\n", "      <td id=\"T_4f50d_row11_col3\" class=\"data row11 col3\" >0.0861</td>\n", "      <td id=\"T_4f50d_row11_col4\" class=\"data row11 col4\" >0.0255</td>\n", "      <td id=\"T_4f50d_row11_col5\" class=\"data row11 col5\" >0.0206</td>\n", "      <td id=\"T_4f50d_row11_col6\" class=\"data row11 col6\" >0.0338</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e68170de20>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "tuned_knn = tune_model(knn, custom_grid = {'n_neighbors' : np.arange(0,50,1)})"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KNeighborsClassifier(algorithm='auto', leaf_size=30, metric='minkowski',\n", "                     metric_params=None, n_jobs=-1, n_neighbors=42, p=2,\n", "                     weights='uniform')\n"]}], "source": ["print(tuned_knn)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "KO3zIfs-EQrA"}, "source": ["# 9.3 Classificação usando Random Forest"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "gmaIfnBMEQrE", "outputId": "a59cebfa-f81e-477c-f83c-e9443fd80b0f"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_d3808_row10_col0, #T_d3808_row10_col1, #T_d3808_row10_col2, #T_d3808_row10_col3, #T_d3808_row10_col4, #T_d3808_row10_col5, #T_d3808_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_d3808\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_d3808_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_d3808_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_d3808_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_d3808_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_d3808_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_d3808_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_d3808_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_d3808_row0_col0\" class=\"data row0 col0\" >0.7876</td>\n", "      <td id=\"T_d3808_row0_col1\" class=\"data row0 col1\" >0.7692</td>\n", "      <td id=\"T_d3808_row0_col2\" class=\"data row0 col2\" >0.5731</td>\n", "      <td id=\"T_d3808_row0_col3\" class=\"data row0 col3\" >0.5128</td>\n", "      <td id=\"T_d3808_row0_col4\" class=\"data row0 col4\" >0.5413</td>\n", "      <td id=\"T_d3808_row0_col5\" class=\"data row0 col5\" >0.4036</td>\n", "      <td id=\"T_d3808_row0_col6\" class=\"data row0 col6\" >0.4047</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_d3808_row1_col0\" class=\"data row1 col0\" >0.7989</td>\n", "      <td id=\"T_d3808_row1_col1\" class=\"data row1 col1\" >0.7687</td>\n", "      <td id=\"T_d3808_row1_col2\" class=\"data row1 col2\" >0.5845</td>\n", "      <td id=\"T_d3808_row1_col3\" class=\"data row1 col3\" >0.5368</td>\n", "      <td id=\"T_d3808_row1_col4\" class=\"data row1 col4\" >0.5597</td>\n", "      <td id=\"T_d3808_row1_col5\" class=\"data row1 col5\" >0.4296</td>\n", "      <td id=\"T_d3808_row1_col6\" class=\"data row1 col6\" >0.4303</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_d3808_row2_col0\" class=\"data row2 col0\" >0.7838</td>\n", "      <td id=\"T_d3808_row2_col1\" class=\"data row2 col1\" >0.7664</td>\n", "      <td id=\"T_d3808_row2_col2\" class=\"data row2 col2\" >0.5387</td>\n", "      <td id=\"T_d3808_row2_col3\" class=\"data row2 col3\" >0.5054</td>\n", "      <td id=\"T_d3808_row2_col4\" class=\"data row2 col4\" >0.5215</td>\n", "      <td id=\"T_d3808_row2_col5\" class=\"data row2 col5\" >0.3821</td>\n", "      <td id=\"T_d3808_row2_col6\" class=\"data row2 col6\" >0.3824</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_d3808_row3_col0\" class=\"data row3 col0\" >0.7832</td>\n", "      <td id=\"T_d3808_row3_col1\" class=\"data row3 col1\" >0.7792</td>\n", "      <td id=\"T_d3808_row3_col2\" class=\"data row3 col2\" >0.5903</td>\n", "      <td id=\"T_d3808_row3_col3\" class=\"data row3 col3\" >0.5037</td>\n", "      <td id=\"T_d3808_row3_col4\" class=\"data row3 col4\" >0.5435</td>\n", "      <td id=\"T_d3808_row3_col5\" class=\"data row3 col5\" >0.4025</td>\n", "      <td id=\"T_d3808_row3_col6\" class=\"data row3 col6\" >0.4047</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_d3808_row4_col0\" class=\"data row4 col0\" >0.7945</td>\n", "      <td id=\"T_d3808_row4_col1\" class=\"data row4 col1\" >0.7902</td>\n", "      <td id=\"T_d3808_row4_col2\" class=\"data row4 col2\" >0.5903</td>\n", "      <td id=\"T_d3808_row4_col3\" class=\"data row4 col3\" >0.5269</td>\n", "      <td id=\"T_d3808_row4_col4\" class=\"data row4 col4\" >0.5568</td>\n", "      <td id=\"T_d3808_row4_col5\" class=\"data row4 col5\" >0.4235</td>\n", "      <td id=\"T_d3808_row4_col6\" class=\"data row4 col6\" >0.4247</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_d3808_row5_col0\" class=\"data row5 col0\" >0.7945</td>\n", "      <td id=\"T_d3808_row5_col1\" class=\"data row5 col1\" >0.7803</td>\n", "      <td id=\"T_d3808_row5_col2\" class=\"data row5 col2\" >0.5845</td>\n", "      <td id=\"T_d3808_row5_col3\" class=\"data row5 col3\" >0.5271</td>\n", "      <td id=\"T_d3808_row5_col4\" class=\"data row5 col4\" >0.5543</td>\n", "      <td id=\"T_d3808_row5_col5\" class=\"data row5 col5\" >0.4213</td>\n", "      <td id=\"T_d3808_row5_col6\" class=\"data row5 col6\" >0.4222</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_d3808_row6_col0\" class=\"data row6 col0\" >0.7713</td>\n", "      <td id=\"T_d3808_row6_col1\" class=\"data row6 col1\" >0.7701</td>\n", "      <td id=\"T_d3808_row6_col2\" class=\"data row6 col2\" >0.6304</td>\n", "      <td id=\"T_d3808_row6_col3\" class=\"data row6 col3\" >0.4825</td>\n", "      <td id=\"T_d3808_row6_col4\" class=\"data row6 col4\" >0.5466</td>\n", "      <td id=\"T_d3808_row6_col5\" class=\"data row6 col5\" >0.3973</td>\n", "      <td id=\"T_d3808_row6_col6\" class=\"data row6 col6\" >0.4036</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_d3808_row7_col0\" class=\"data row7 col0\" >0.7763</td>\n", "      <td id=\"T_d3808_row7_col1\" class=\"data row7 col1\" >0.7464</td>\n", "      <td id=\"T_d3808_row7_col2\" class=\"data row7 col2\" >0.5301</td>\n", "      <td id=\"T_d3808_row7_col3\" class=\"data row7 col3\" >0.4894</td>\n", "      <td id=\"T_d3808_row7_col4\" class=\"data row7 col4\" >0.5089</td>\n", "      <td id=\"T_d3808_row7_col5\" class=\"data row7 col5\" >0.3644</td>\n", "      <td id=\"T_d3808_row7_col6\" class=\"data row7 col6\" >0.3649</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_d3808_row8_col0\" class=\"data row8 col0\" >0.7607</td>\n", "      <td id=\"T_d3808_row8_col1\" class=\"data row8 col1\" >0.7394</td>\n", "      <td id=\"T_d3808_row8_col2\" class=\"data row8 col2\" >0.5731</td>\n", "      <td id=\"T_d3808_row8_col3\" class=\"data row8 col3\" >0.4619</td>\n", "      <td id=\"T_d3808_row8_col4\" class=\"data row8 col4\" >0.5115</td>\n", "      <td id=\"T_d3808_row8_col5\" class=\"data row8 col5\" >0.3554</td>\n", "      <td id=\"T_d3808_row8_col6\" class=\"data row8 col6\" >0.3590</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_d3808_row9_col0\" class=\"data row9 col0\" >0.7730</td>\n", "      <td id=\"T_d3808_row9_col1\" class=\"data row9 col1\" >0.7526</td>\n", "      <td id=\"T_d3808_row9_col2\" class=\"data row9 col2\" >0.5546</td>\n", "      <td id=\"T_d3808_row9_col3\" class=\"data row9 col3\" >0.4825</td>\n", "      <td id=\"T_d3808_row9_col4\" class=\"data row9 col4\" >0.5160</td>\n", "      <td id=\"T_d3808_row9_col5\" class=\"data row9 col5\" >0.3687</td>\n", "      <td id=\"T_d3808_row9_col6\" class=\"data row9 col6\" >0.3703</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_d3808_row10_col0\" class=\"data row10 col0\" >0.7824</td>\n", "      <td id=\"T_d3808_row10_col1\" class=\"data row10 col1\" >0.7662</td>\n", "      <td id=\"T_d3808_row10_col2\" class=\"data row10 col2\" >0.5749</td>\n", "      <td id=\"T_d3808_row10_col3\" class=\"data row10 col3\" >0.5029</td>\n", "      <td id=\"T_d3808_row10_col4\" class=\"data row10 col4\" >0.5360</td>\n", "      <td id=\"T_d3808_row10_col5\" class=\"data row10 col5\" >0.3949</td>\n", "      <td id=\"T_d3808_row10_col6\" class=\"data row10 col6\" >0.3967</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d3808_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_d3808_row11_col0\" class=\"data row11 col0\" >0.0115</td>\n", "      <td id=\"T_d3808_row11_col1\" class=\"data row11 col1\" >0.0151</td>\n", "      <td id=\"T_d3808_row11_col2\" class=\"data row11 col2\" >0.0273</td>\n", "      <td id=\"T_d3808_row11_col3\" class=\"data row11 col3\" >0.0226</td>\n", "      <td id=\"T_d3808_row11_col4\" class=\"data row11 col4\" >0.0186</td>\n", "      <td id=\"T_d3808_row11_col5\" class=\"data row11 col5\" >0.0249</td>\n", "      <td id=\"T_d3808_row11_col6\" class=\"data row11 col6\" >0.0246</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6f9b89f40>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_rf = tune_model(rf, optimize='F1')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "IqxEZRi1EQrO"}, "source": ["Por pad<PERSON><PERSON>, `tune_model` otimiza `Accuracy`, mas isso pode ser alterado usando o parâmetro `optimize`. Por exemplo: `tune_model(dt, optimize = 'AUC')` irá procurar os hiperparâmetros de um classificador de árvore de decisão que resulta no maior `AUC` em vez de `Accuracy`. Para os propósitos deste exemplo, usamos a métrica padrão \"Precisão\" apenas para simplificar. Geralmente, quando o conjunto de dados está desequilibrado (como o conjunto de dados de crédito com o qual estamos trabalhando), \"Precisão\" não é uma boa métrica a ser considerada. A metodologia por trás da seleção da métrica certa para avaliar um classificador está além do escopo deste tutorial, mas se você quiser saber mais sobre isso, você pode __[clique aqui](https://medium.com/@MohammedS/performance-metrics-for-classification-problems-in-machine-learning-part-i-b085d432082b)__ para ler um artigo sobre como escolher a métrica de avaliação correta.\n", "\n", "Métricas por si só não são os únicos critérios que você deve considerar ao finalizar o melhor modelo para produção. Outros fatores a serem considerados incluem tempo de treinamento, desvio padrão de kfolds, etc. À medida que você avança na série de tutoriais, discutiremos esses fatores em detalhes nos níveis intermediário e avançado. <PERSON>r enquanto, vamos avançar considerando o Tuned Random Forest Classifier `tuned_rf`, como nosso melhor modelo para o restante deste tutorial."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "w_P46O0jEQrT"}, "source": ["# 10.0 Plot a Model"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "FGM9GOtjEQrV"}, "source": ["Antes da finalização do modelo, a função `plot_model()` pode ser usada para analisar o desempenho em diferentes aspectos, como AUC, Matriz de confusão, limite de decisão, etc. Essa função pega um objeto de modelo treinado e retorna um gráfico com base no conjunto de teste/hold-out.\n", "\n", "Existem 15 gráficos diferentes disponíveis, por favor veja a docstring `plot_model()` para a lista de gráficos disponíveis."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "euqkQYJaEQrY"}, "source": ["# 10.1 AUC Plot"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {}, "colab_type": "code", "id": "RLbLqvkHEQra", "outputId": "fe40b5e3-6375-43e8-e97d-1d487e02eb2d"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'auc')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "bwyoTUDQEQrm"}, "source": ["# 10.2 Precision-Recall Plot"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {}, "colab_type": "code", "id": "4IvchQoiEQrr", "outputId": "fdff2076-86fc-42f5-beee-f0051ea30dd4"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'pr')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "_r9rwEw7EQrz"}, "source": ["# 10.3 Importân<PERSON> das variáveis (Feature Importance) Plot"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {}, "colab_type": "code", "id": "nVScSxJ-EQr2", "outputId": "f44f4b08-b749-4d0e-dcc9-d7e3dc6240c8"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot='feature')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "FfWC3NEhEQr9"}, "source": ["# 10.4 <PERSON><PERSON> (Confusion matrix)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {}, "colab_type": "code", "id": "OAB5mes-EQsA", "outputId": "bd82130d-2cc3-4b63-df5d-03b7aa54bf52"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'confusion_matrix')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "deClKJrbEQsJ"}, "source": [" *Outra* maneira de analizar a performance dos modelos é usar a função `evaluate_model()`, a qual mostra uma interface do usuário para todos os gráficos disponíveis de um determinado modelo. Internamente, ela usa a função `plot_model()`."]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 436, "referenced_widgets": ["42d5400d235d40b78190016ef0dabe11", "41031579127f4a53b58957e601465083", "12bf8b3c6ae8444a900474912589fdf1", "9bb3600d38c04691b444ff375ad5e3f5", "8886001bc7c1463ba58a8453f5c55073", "0a06fb091bd94ce6b6ab892e2c6faadf", "3cc1e83b91f34b289c7d52003f20a97a", "8d709ec9ec484944b1f9773748857f84", "8399e21b17634116861a5abaa9c0ccf7", "d5b6fce1763b4b54898ff3397b0f5bb0", "57b94ac505d142769b79de2f1e5c1166", "2a81017413ca4fe789c2272a5831a069", "02771b4dc3284414ab05df1906f4556b", "9e338844e75b4e17be8483529f5f38fd", "22588a12c0db4067982e62ebbe7e6930"]}, "colab_type": "code", "id": "OcLV1Ln6EQsN", "outputId": "7b5b8b4e-8d4a-4371-9a4f-cabb0a96265a"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9275c00c18724e17bf4ae7f906a546ef", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(ToggleButtons(description='Plot Type:', icons=('',), options=(('Hyperparameters', 'param…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["evaluate_model(tuned_rf)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "RX5pYUJJEQsV"}, "source": ["# 11.0 Predict na amostra de test / hold-out"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "mFSvRYiaEQsd"}, "source": ["Antes de finalizar o modelo, é aconselhável realizar uma verificação final prevendo o conjunto de teste/hold-out e revisando as métricas de avaliação. Se você observar a grade de informações na Seção 6 acima, verá que 30% (6.841 amostras) dos dados foram separados como amostra de teste/resistência. Todas as métricas de avaliação que vimos acima são resultados de validação cruzada com base apenas no conjunto de treinamento (70%). Agora, usando nosso modelo final treinado armazenado na variável `tuned_rf`, faremos previsões em relação à amostra de retenção e avaliaremos as métricas para ver se elas são materialmente diferentes dos resultados do CV."]}, {"cell_type": "code", "execution_count": 39, "metadata": {"colab": {}, "colab_type": "code", "id": "nwaZk6oTEQsi", "outputId": "d30c8533-d347-4fa6-f18e-5b2abc937bec"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_2a50b\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_2a50b_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_2a50b_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_2a50b_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_2a50b_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_2a50b_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_2a50b_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_2a50b_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_2a50b_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_2a50b_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_2a50b_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_2a50b_row0_col1\" class=\"data row0 col1\" >0.7835</td>\n", "      <td id=\"T_2a50b_row0_col2\" class=\"data row0 col2\" >0.7694</td>\n", "      <td id=\"T_2a50b_row0_col3\" class=\"data row0 col3\" >0.5695</td>\n", "      <td id=\"T_2a50b_row0_col4\" class=\"data row0 col4\" >0.5215</td>\n", "      <td id=\"T_2a50b_row0_col5\" class=\"data row0 col5\" >0.5444</td>\n", "      <td id=\"T_2a50b_row0_col6\" class=\"data row0 col6\" >0.4028</td>\n", "      <td id=\"T_2a50b_row0_col7\" class=\"data row0 col7\" >0.4035</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6869453a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(tuned_rf);"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "E-fHsX2AEQsx"}, "source": ["A precisão no conjunto de teste/hold-out é **`0.7835`** comparada a **`0.7824`** alcançada nos resultados do CV `tuned_rf` (na seção 9.3 acima). Esta não é uma diferença significativa. Se houver uma grande variação entre os resultados do teste/hold-out e do CV, isso normalmente indicaria um ajuste excessivo, mas também pode ser devido a vários outros fatores e exigiria investigação adicional. Nesse caso, avançaremos com a finalização do modelo e a previsão de dados não vistos (os 5% que separamos no início e nunca expusemos ao PyCaret).\n", "\n", "(DICA: É sempre bom observar o desvio padrão dos resultados do CV ao usar `create_model()`.)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "r79BGjIfEQs1"}, "source": ["# 12.0 Finalizando o modelo para deploy"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "B-6xJ9kQEQs7"}, "source": ["A finalização do modelo é a última etapa do experimento. Um fluxo de trabalho normal de aprendizado de máquina no PyCaret começa com `setup()`, seguido pela comparação de todos os modelos usando `compare_models()` e listando alguns modelos candidatos (com base na métrica de interesse) para executar várias técnicas de modelagem, como ajuste de hiperparâmetros, ensembling, empilhamento, etc. Esse fluxo de trabalho acabará por levá-lo ao melhor modelo para uso em fazer previsões sobre dados novos e não vistos. A função `finalize_model()` ajusta o modelo ao conjunto de dados completo, incluindo a amostra de teste/hold-out (30% neste caso). O objetivo dessa função é treinar o modelo no conjunto de dados completo antes de ser implantado na produção."]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {}, "colab_type": "code", "id": "_--tO4KGEQs-"}, "outputs": [], "source": ["final_rf = finalize_model(tuned_rf)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 147}, "colab_type": "code", "id": "U9W6kXsSEQtQ", "outputId": "794b24a4-9c95-4730-eddd-f82e4925b866"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RandomForestClassifier(bootstrap=True, ccp_alpha=0.0,\n", "                       class_weight='balanced_subsample', criterion='entropy',\n", "                       max_depth=4, max_features='log2', max_leaf_nodes=None,\n", "                       max_samples=None, min_impurity_decrease=0.0002,\n", "                       min_impurity_split=None, min_samples_leaf=5,\n", "                       min_samples_split=9, min_weight_fraction_leaf=0.0,\n", "                       n_estimators=130, n_jobs=-1, oob_score=False,\n", "                       random_state=123, verbose=0, warm_start=False)\n"]}], "source": ["#Parâmetros finais do modelo Random Forest para deploy\n", "print(final_rf)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "kgdOjxypEQtd"}, "source": ["**Cuidado:** mais uma coisa para prestar atenção. Depois que o modelo é finalizado usando `finalize_model()`, todo o conjunto de dados, incluindo o conjunto de teste/hold-out, é usado para treinamento. Como tal, se o modelo for usado para previsões no conjunto de retenção após o uso de `finalize_model()`, as informações impressas poderá confundir, pois você está predizendo nos mesmos dados que foram usados para modelagem (treino). Para demonstrar apenas este ponto, usaremos `final_rf` em `predict_model()` para comparar a tabela de informações com a acima na seção 11."]}, {"cell_type": "code", "execution_count": 43, "metadata": {"colab": {}, "colab_type": "code", "id": "NJDk3I-EEQtg", "outputId": "4d75663a-e86f-4826-c8e4-c9aa722648df"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_230d1\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_230d1_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_230d1_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_230d1_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_230d1_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_230d1_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_230d1_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_230d1_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_230d1_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_230d1_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_230d1_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_230d1_row0_col1\" class=\"data row0 col1\" >0.7781</td>\n", "      <td id=\"T_230d1_row0_col2\" class=\"data row0 col2\" >0.7713</td>\n", "      <td id=\"T_230d1_row0_col3\" class=\"data row0 col3\" >0.5978</td>\n", "      <td id=\"T_230d1_row0_col4\" class=\"data row0 col4\" >0.5099</td>\n", "      <td id=\"T_230d1_row0_col5\" class=\"data row0 col5\" >0.5504</td>\n", "      <td id=\"T_230d1_row0_col6\" class=\"data row0 col6\" >0.4043</td>\n", "      <td id=\"T_230d1_row0_col7\" class=\"data row0 col7\" >0.4065</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e686939af0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(final_rf);"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "V77JC5JVEQtp"}, "source": ["Observe como o AUC em `final_rf` aumentou para **`0.7713`** de **`0.7694`**, mesmo que o modelo seja o mesmo. Isso ocorre porque a variável `final_rf` foi treinada no conjunto de dados completo, incluindo o conjunto de teste/hold-out."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "hUzc6tXNEQtr"}, "source": ["# 13.0 Predizendo o conjunto de dados que não foi usado no treino"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "dx5vXjChEQtt"}, "source": ["A função `predict_model()` também é usada para predizer no conjunto de dados não visto. A única diferença da seção 11 acima é que desta vez passaremos o parâmetro `data_unseen`. `data_unseen` é a variável criada no início do tutorial e contém 5% (1200 amostras) do conjunto de dados original que nunca foi exposto ao PyCaret. (veja a seção 5 para explicação)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211}, "colab_type": "code", "id": "0y5KWLC6EQtx", "outputId": "30771f87-7847-43ce-e984-9963cff7d043"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_43896\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_43896_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_43896_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_43896_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_43896_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_43896_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_43896_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_43896_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_43896_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_43896_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_43896_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_43896_row0_col1\" class=\"data row0 col1\" >0.7842</td>\n", "      <td id=\"T_43896_row0_col2\" class=\"data row0 col2\" >0.7745</td>\n", "      <td id=\"T_43896_row0_col3\" class=\"data row0 col3\" >0.6122</td>\n", "      <td id=\"T_43896_row0_col4\" class=\"data row0 col4\" >0.5063</td>\n", "      <td id=\"T_43896_row0_col5\" class=\"data row0 col5\" >0.5542</td>\n", "      <td id=\"T_43896_row0_col6\" class=\"data row0 col6\" >0.4135</td>\n", "      <td id=\"T_43896_row0_col7\" class=\"data row0 col7\" >0.4168</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e6f9da0bb0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>Label</th>\n", "      <th>Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.5441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.6662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.5524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.8320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.5353</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  \\\n", "0      0  ...      567.0     380.0     601.0       0.0     581.0    1687.0   \n", "1     -1  ...    11873.0   21540.0   15138.0   24677.0   11851.0   11875.0   \n", "2      2  ...     3151.0    5818.0      15.0    9102.0      17.0    3165.0   \n", "3      2  ...   149531.0    6300.0    5500.0    5500.0    5500.0    5000.0   \n", "4      0  ...     1737.0    2622.0    3301.0       0.0     360.0    1737.0   \n", "\n", "   PAY_AMT6  default  Label   Score  \n", "0    1542.0        0      0  0.5441  \n", "1    8251.0        0      0  0.6662  \n", "2    1395.0        0      0  0.5524  \n", "3    5000.0        1      1  0.8320  \n", "4     924.0        0      0  0.5353  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["unseen_predictions = predict_model(final_rf, data=data_unseen)\n", "unseen_predictions.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "oPYmVpugEQt5"}, "source": ["As colunas `Label` e `Score` são adicionadas ao conjunto `data_unseen`. Rótulo é a previsão e pontuação é a probabilidade da previsão. Observe que os resultados previstos são concatenados ao conjunto de dados original enquanto todas as transformações são executadas automaticamente em segundo plano. Você também pode verificar as métricas sobre isso, pois tem a coluna de destino real `default` disponível. Para fazer isso, usaremos o módulo `pycaret.utils`. Veja exemplo abaixo:"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7842"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(unseen_predictions['default'], unseen_predictions['Label'], metric = 'Accuracy')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "L__po3sUEQt7"}, "source": ["# 14.0 Salvando o modelo final"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "1sQPT7jrEQt-"}, "source": ["Agora terminamos o experimento finalizando o modelo `tuned_rf` que agora está armazenado na variável `final_rf`. Também usamos o modelo armazenado em `final_rf` para prever `data_unseen`. Isso nos leva ao final de nosso experimento, mas uma pergunta ainda deve ser feita: o que acontece quando você tem mais dados novos para predizer? Você tem que passar por todo o experimento novamente? A resposta é não, a função interna do PyCaret `save_model()` permite que você salve o modelo junto com todo o pipeline de transformação para uso posterior."]}, {"cell_type": "code", "execution_count": 46, "metadata": {"colab": {}, "colab_type": "code", "id": "ln1YWIXTEQuA", "outputId": "d3cb0652-f72e-44e8-9455-824b12740bff"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Saved\n"]}, {"data": {"text/plain": ["(Pipeline(memory=None,\n", "          steps=[('dtypes',\n", "                  DataTypes_Auto_infer(categorical_features=[],\n", "                                       display_types=True, features_todrop=[],\n", "                                       id_columns=[],\n", "                                       ml_usecase='classification',\n", "                                       numerical_features=[], target='default',\n", "                                       time_features=[])),\n", "                 ('imputer',\n", "                  Simple_Imputer(categorical_strategy='not_available',\n", "                                 fill_value_categorical=None,\n", "                                 fill_value_numerical=None,\n", "                                 numeric_stra...\n", "                  RandomForestClassifier(bootstrap=True, ccp_alpha=0.0,\n", "                                         class_weight='balanced_subsample',\n", "                                         criterion='entropy', max_depth=4,\n", "                                         max_features='log2',\n", "                                         max_leaf_nodes=None, max_samples=None,\n", "                                         min_impurity_decrease=0.0002,\n", "                                         min_impurity_split=None,\n", "                                         min_samples_leaf=5, min_samples_split=9,\n", "                                         min_weight_fraction_leaf=0.0,\n", "                                         n_estimators=130, n_jobs=-1,\n", "                                         oob_score=False, random_state=123,\n", "                                         verbose=0, warm_start=False)]],\n", "          verbose=False),\n", " 'Final RF Model 01Jun2022.pkl')"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["save_model(final_rf,'Final RF Model 01Jun2022')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "WE6f48AYEQuR"}, "source": ["(DICA: É sempre bom usar data no nome do arquivo ao salvar modelos, é bom para controle de versão.)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Z8OBesfkEQuU"}, "source": ["# 15.0 Carregando o modelo salvo"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "V2K_WLaaEQuW"}, "source": ["Para carregar um modelo salvo em uma data futura no mesmo ambiente ou em um ambiente alternativo, usaríamos a função `load_model()` do PyCaret e, em seguida, aplicaríamos facilmente o modelo salvo em novos dados não vistos para previsão."]}, {"cell_type": "code", "execution_count": 47, "metadata": {"colab": {}, "colab_type": "code", "id": "Siw_2EIUEQub", "outputId": "5da8b7c9-01f7-469c-f0c9-b19c8ce11bcc"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Loaded\n"]}], "source": ["saved_final_rf = load_model('Final RF Model 01Jun2022')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "1<PERSON>i6-<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, "source": ["Uma vez que o modelo é carregado no ambiente, você pode simplesmente usá-lo para prever quaisquer novos dados usando a mesma função `predict_model()`. Abaixo, aplicamos o modelo carregado para prever o mesmo `data_unseen` que usamos na seção 13 acima."]}, {"cell_type": "code", "execution_count": 48, "metadata": {"colab": {}, "colab_type": "code", "id": "HMPO1ka9EQut"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_b97a8\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_b97a8_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_b97a8_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_b97a8_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_b97a8_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_b97a8_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_b97a8_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_b97a8_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_b97a8_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_b97a8_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_b97a8_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_b97a8_row0_col1\" class=\"data row0 col1\" >0.7842</td>\n", "      <td id=\"T_b97a8_row0_col2\" class=\"data row0 col2\" >0.7745</td>\n", "      <td id=\"T_b97a8_row0_col3\" class=\"data row0 col3\" >0.6122</td>\n", "      <td id=\"T_b97a8_row0_col4\" class=\"data row0 col4\" >0.5063</td>\n", "      <td id=\"T_b97a8_row0_col5\" class=\"data row0 col5\" >0.5542</td>\n", "      <td id=\"T_b97a8_row0_col6\" class=\"data row0 col6\" >0.4135</td>\n", "      <td id=\"T_b97a8_row0_col7\" class=\"data row0 col7\" >0.4168</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1e683adae80>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["new_prediction = predict_model(saved_final_rf, data=data_unseen)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"colab": {}, "colab_type": "code", "id": "7wyDQQSzEQu8", "outputId": "23065436-42e3-4441-ed58-a8863f8971f9"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>Label</th>\n", "      <th>Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.5441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.6662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.5524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.8320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.5353</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  \\\n", "0      0  ...      567.0     380.0     601.0       0.0     581.0    1687.0   \n", "1     -1  ...    11873.0   21540.0   15138.0   24677.0   11851.0   11875.0   \n", "2      2  ...     3151.0    5818.0      15.0    9102.0      17.0    3165.0   \n", "3      2  ...   149531.0    6300.0    5500.0    5500.0    5500.0    5000.0   \n", "4      0  ...     1737.0    2622.0    3301.0       0.0     360.0    1737.0   \n", "\n", "   PAY_AMT6  default  Label   Score  \n", "0    1542.0        0      0  0.5441  \n", "1    8251.0        0      0  0.6662  \n", "2    1395.0        0      0  0.5524  \n", "3    5000.0        1      1  0.8320  \n", "4     924.0        0      0  0.5353  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["new_prediction.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "bf8I1uqcEQvD"}, "source": ["Observe que os resultados de `unseen_predictions` e `new_prediction` s<PERSON> idênticos."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7842"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(new_prediction['default'], new_prediction['Label'], metric = 'Accuracy')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "_HeOs8BhEQvF"}, "source": ["# 16.0 Resumo / Próximos passos?"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "VqG1NnwXEQvK"}, "source": ["Este tutorial cobriu todo o pipeline de aprendizado de máquina desde a ingestão de dados, pré-processamento, treinamento do modelo, ajuste de hiperparâmetros, previsão e salvamento do modelo para uso posterior. Concluímos todas essas etapas em menos de 10 comandos que são naturalmente construídos e muito intuitivos de lembrar, como `create_model()`, `tune_model()`, `compare_models()`. Recriar todo o experimento sem o PyCaret levaria mais de 100 linhas de código na maioria das bibliotecas.\n", "\n", "Nós cobrimos apenas o básico de `pycaret.classification`. Nos tutoriais a seguir, aprofundaremos o pré-processamento avançado, o ensembling, o empilhamento generalizado e outras técnicas que permitem personalizar totalmente seu pipeline de aprendizado de máquina e devem ser conhecidas por qualquer cientista de dados.\n", "\n", "Nos vemos no próximo tutorial. Siga o link para __[Tutorial de classificação binária (CLF102) - Nível intermediário - Inglês](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__"]}], "metadata": {"colab": {"collapsed_sections": ["Ui_rALqYEQmv", "y9s9wNcjEQn0", "it_nJo1IEQob", "P5m2pciOEQo4", "UWMSeyNhEQo-", "rWUojqBCEQpb", "nSg3OUjuEQpu", "XvpjzbGQEQqB", "BQlMCxrUEQqG", "CD-f0delEQqq", "KO3zIfs-EQrA", "w_P46O0jEQrT", "euqkQYJaEQrY", "bwyoTUDQEQrm", "_r9rwEw7EQrz", "FfWC3NEhEQr9", "RX5pYUJJEQsV", "r79BGjIfEQs1", "hUzc6tXNEQtr", "L__po3sUEQt7", "Z8OBesfkEQuU", "_HeOs8BhEQvF"], "name": "Binary Classification Tutorial (CLF101) - Level Beginner (ACN_EDITS).ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"02771b4dc3284414ab05df1906f4556b": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_22588a12c0db4067982e62ebbe7e6930", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9e338844e75b4e17be8483529f5f38fd", "value": 5}}, "0a06fb091bd94ce6b6ab892e2c6faadf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12bf8b3c6ae8444a900474912589fdf1": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsModel", "_options_labels": ["Hyperparameters", "AUC", "Confusion Matrix", "<PERSON><PERSON><PERSON><PERSON>", "Precision Recall", "Error", "Class Report", "Feature Selection", "Learning Curve", "Manifold Learning", "Calibration Curve", "Validation Curve", "Dimensions", "Feature Importance", "Decision Boundary"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonsView", "button_style": "", "description": "Plot Type:", "description_tooltip": null, "disabled": false, "icons": [""], "index": 2, "layout": "IPY_MODEL_0a06fb091bd94ce6b6ab892e2c6faadf", "style": "IPY_MODEL_8886001bc7c1463ba58a8453f5c55073", "tooltips": []}}, "22588a12c0db4067982e62ebbe7e6930": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2a81017413ca4fe789c2272a5831a069": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3cc1e83b91f34b289c7d52003f20a97a": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_8399e21b17634116861a5abaa9c0ccf7", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8d709ec9ec484944b1f9773748857f84", "value": 2}}, "41031579127f4a53b58957e601465083": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "42d5400d235d40b78190016ef0dabe11": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "state": {"_dom_classes": ["widget-interact"], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_12bf8b3c6ae8444a900474912589fdf1", "IPY_MODEL_9bb3600d38c04691b444ff375ad5e3f5"], "layout": "IPY_MODEL_41031579127f4a53b58957e601465083"}}, "57b94ac505d142769b79de2f1e5c1166": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8399e21b17634116861a5abaa9c0ccf7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8886001bc7c1463ba58a8453f5c55073": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_width": "", "description_width": "", "font_weight": ""}}, "8d709ec9ec484944b1f9773748857f84": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9bb3600d38c04691b444ff375ad5e3f5": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_4f8f81ab97b041a58a53c85a1ab97bd4", "msg_id": "", "outputs": [{"image/png": "iVBORw0KGgoAAAANSUhEUgAAAeoAAAFlCAYAAAAki6s3AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAm9n9QR+M8QEgGtgAAAABJRU5ErkJggg==\n", "metadata": {"tags": []}, "output_type": "display_data", "text/plain": "<Figure size 576x396 with 1 Axes>"}]}}, "9e338844e75b4e17be8483529f5f38fd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d5b6fce1763b4b54898ff3397b0f5bb0": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_2a81017413ca4fe789c2272a5831a069", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_57b94ac505d142769b79de2f1e5c1166", "value": 5}}}}}, "nbformat": 4, "nbformat_minor": 1}