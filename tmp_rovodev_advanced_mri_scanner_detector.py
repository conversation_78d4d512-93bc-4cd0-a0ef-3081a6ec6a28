#!/usr/bin/env python3
"""
高级MRI扫描仪信息提取器 - 从NII.gz文件中提取扫描仪信息
Advanced MRI Scanner Information Extractor from NII.gz files

支持多种方法提取扫描仪信息：
1. NII header扩展信息
2. JSON sidecar文件（BIDS格式）
3. 文件名模式识别
4. 目录结构分析
5. 残留DICOM信息
"""

import nibabel as nib
import json
import os
import re
import pandas as pd
from pathlib import Path
import numpy as np
from datetime import datetime

class MRIScannerDetector:
    def __init__(self):
        # 扫描仪厂商数据库
        self.manufacturer_db = {
            'siemens': {
                'keywords': ['siemens', 'sie', 'skyra', 'avanto', 'trio', 'prisma', 'vida', 'verio', 'symphony', 'sonata'],
                'models': ['skyra', 'avanto', 'trio', 'prisma', 'vida', 'verio', 'symphony', 'sonata', 'espree', 'aera'],
                'software_patterns': ['syngo', 'vb', 'vc', 'vd', 've']
            },
            'ge': {
                'keywords': ['ge', 'general_electric', 'signa', 'discovery', 'optima', 'excite', 'genesis'],
                'models': ['signa', 'discovery', 'optima', 'excite', 'genesis', 'architect', 'pioneer'],
                'software_patterns': ['dx', 'lx', 'hdx', 'hdxt', 'mr750', 'mr450']
            },
            'philips': {
                'keywords': ['philips', 'achieva', 'ingenia', 'intera', 'gyroscan', 'panorama'],
                'models': ['achieva', 'ingenia', 'intera', 'gyroscan', 'panorama', 'multiva'],
                'software_patterns': ['release', 'r1', 'r2', 'r3', 'r4', 'r5']
            },
            'toshiba': {
                'keywords': ['toshiba', 'canon', 'vantage', 'titan', 'excelart'],
                'models': ['vantage', 'titan', 'excelart', 'atlas'],
                'software_patterns': ['v2', 'v3', 'v4', 'v5']
            },
            'hitachi': {
                'keywords': ['hitachi', 'echelon', 'oasis', 'airis'],
                'models': ['echelon', 'oasis', 'airis'],
                'software_patterns': ['v1', 'v2', 'v3']
            }
        }
        
        # 磁场强度模式
        self.field_strength_patterns = {
            '0.2T': ['0.2t', '0.2tesla', '200mt'],
            '0.3T': ['0.3t', '0.3tesla', '300mt'],
            '0.5T': ['0.5t', '0.5tesla', '500mt'],
            '1.0T': ['1t', '1.0t', '1tesla', '1000mt'],
            '1.5T': ['1.5t', '1.5tesla', '1500mt'],
            '3.0T': ['3t', '3.0t', '3tesla', '3000mt'],
            '7.0T': ['7t', '7.0t', '7tesla', '7000mt'],
            '9.4T': ['9.4t', '9.4tesla', '9400mt']
        }
        
        # 序列类型
        self.sequence_types = {
            'T1': ['t1', 't1w', 't1_weighted', 'mprage', 'spgr', 'fspgr'],
            'T2': ['t2', 't2w', 't2_weighted', 'tse', 'fse'],
            'FLAIR': ['flair', 'fluid_attenuated'],
            'DWI': ['dwi', 'diffusion', 'adc', 'fa', 'dti'],
            'SWI': ['swi', 'susceptibility'],
            'BOLD': ['bold', 'fmri', 'functional'],
            'TOF': ['tof', 'time_of_flight'],
            'PHASE': ['phase', 'ph'],
            'MAG': ['mag', 'magnitude']
        }

    def extract_from_nii_header(self, nii_file_path):
        """从NII header中提取信息"""
        try:
            img = nib.load(nii_file_path)
            header = img.header
            
            info = {
                'data_type': str(header.get_data_dtype()),
                'image_shape': header.get_data_shape(),
                'voxel_size': header.get_zooms(),
                'orientation': nib.aff2axcodes(img.affine)
            }
            
            # 检查扩展信息
            if hasattr(header, 'extensions') and header.extensions:
                info['extensions'] = []
                for ext in header.extensions:
                    ext_info = {
                        'code': ext.get_code(),
                        'size': len(ext.get_content())
                    }
                    
                    # 尝试解析内容
                    try:
                        content = ext.get_content()
                        if isinstance(content, bytes):
                            content_str = content.decode('utf-8', errors='ignore')
                            
                            # 查找扫描仪相关信息
                            scanner_info = self._parse_extension_content(content_str)
                            if scanner_info:
                                ext_info.update(scanner_info)
                                
                    except Exception as e:
                        ext_info['parse_error'] = str(e)
                    
                    info['extensions'].append(ext_info)
            
            return info
            
        except Exception as e:
            return {'error': str(e)}

    def _parse_extension_content(self, content):
        """解析扩展内容中的扫描仪信息"""
        scanner_info = {}
        content_lower = content.lower()
        
        # 查找厂商信息
        for manufacturer, data in self.manufacturer_db.items():
            for keyword in data['keywords']:
                if keyword in content_lower:
                    scanner_info['manufacturer'] = manufacturer
                    scanner_info['manufacturer_keyword'] = keyword
                    break
            if 'manufacturer' in scanner_info:
                break
        
        # 查找型号信息
        if 'manufacturer' in scanner_info:
            manufacturer = scanner_info['manufacturer']
            for model in self.manufacturer_db[manufacturer]['models']:
                if model in content_lower:
                    scanner_info['model'] = model
                    break
        
        # 查找磁场强度
        for strength, patterns in self.field_strength_patterns.items():
            for pattern in patterns:
                if pattern in content_lower:
                    scanner_info['field_strength'] = strength
                    break
            if 'field_strength' in scanner_info:
                break
        
        # 使用正则表达式查找更多信息
        patterns = {
            'software_version': r'software[_\s]*version[:\s]*([^\s\n]+)',
            'station_name': r'station[_\s]*name[:\s]*([^\s\n]+)',
            'institution': r'institution[:\s]*([^\n]+)',
            'protocol_name': r'protocol[_\s]*name[:\s]*([^\n]+)',
            'sequence_name': r'sequence[_\s]*name[:\s]*([^\n]+)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content_lower)
            if match:
                scanner_info[key] = match.group(1).strip()
        
        return scanner_info

    def check_json_sidecar(self, nii_file_path):
        """检查BIDS格式的JSON sidecar文件"""
        nii_path = Path(nii_file_path)
        json_path = nii_path.with_suffix('.json')
        
        if not json_path.exists():
            # 尝试其他可能的JSON文件名
            possible_names = [
                nii_path.stem + '.json',
                nii_path.stem.replace('.nii', '') + '.json'
            ]
            
            for name in possible_names:
                test_path = nii_path.parent / name
                if test_path.exists():
                    json_path = test_path
                    break
            else:
                return None
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 提取扫描仪相关信息
            scanner_fields = [
                'Manufacturer', 'ManufacturerModelName', 'MagneticFieldStrength',
                'SoftwareVersions', 'DeviceSerialNumber', 'StationName',
                'InstitutionName', 'InstitutionalDepartmentName',
                'SequenceName', 'ScanningSequence', 'SequenceVariant',
                'MRAcquisitionType', 'SliceThickness', 'RepetitionTime',
                'EchoTime', 'FlipAngle', 'ImagingFrequency'
            ]
            
            scanner_info = {}
            for field in scanner_fields:
                if field in metadata:
                    scanner_info[field] = metadata[field]
            
            return scanner_info if scanner_info else None
            
        except Exception as e:
            return {'json_error': str(e)}

    def analyze_filename_pattern(self, file_path):
        """分析文件名模式"""
        filename = Path(file_path).name.lower()
        
        analysis = {}
        
        # 检查厂商
        for manufacturer, data in self.manufacturer_db.items():
            for keyword in data['keywords']:
                if keyword in filename:
                    analysis['manufacturer'] = manufacturer
                    analysis['manufacturer_keyword'] = keyword
                    break
            if 'manufacturer' in analysis:
                break
        
        # 检查磁场强度
        for strength, patterns in self.field_strength_patterns.items():
            for pattern in patterns:
                if pattern in filename:
                    analysis['field_strength'] = strength
                    break
            if 'field_strength' in analysis:
                break
        
        # 检查序列类型
        found_sequences = []
        for seq_type, patterns in self.sequence_types.items():
            for pattern in patterns:
                if pattern in filename:
                    found_sequences.append(seq_type)
                    break
        
        if found_sequences:
            analysis['sequences'] = found_sequences
        
        # 检查日期模式
        date_patterns = [
            r'(\d{4})[-_]?(\d{2})[-_]?(\d{2})',  # YYYY-MM-DD
            r'(\d{2})[-_]?(\d{2})[-_]?(\d{4})',  # DD-MM-YYYY
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                analysis['potential_date'] = match.group(0)
                break
        
        return analysis if analysis else None

    def analyze_directory_structure(self, file_path):
        """分析目录结构获取信息"""
        path_parts = Path(file_path).parts
        
        structure_info = {}
        
        # 查找可能包含扫描仪信息的目录名
        for part in path_parts:
            part_lower = part.lower()
            
            # 检查厂商信息
            for manufacturer, data in self.manufacturer_db.items():
                for keyword in data['keywords']:
                    if keyword in part_lower:
                        structure_info['directory_manufacturer'] = manufacturer
                        structure_info['directory_keyword'] = keyword
                        break
                if 'directory_manufacturer' in structure_info:
                    break
            
            # 检查磁场强度
            for strength, patterns in self.field_strength_patterns.items():
                for pattern in patterns:
                    if pattern in part_lower:
                        structure_info['directory_field_strength'] = strength
                        break
                if 'directory_field_strength' in structure_info:
                    break
        
        return structure_info if structure_info else None

    def comprehensive_analysis(self, nii_file_path):
        """综合分析单个NII文件"""
        results = {
            'file_path': str(nii_file_path),
            'file_name': Path(nii_file_path).name,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        # 1. NII header分析
        header_info = self.extract_from_nii_header(nii_file_path)
        if header_info:
            results['nii_header'] = header_info
        
        # 2. JSON sidecar分析
        json_info = self.check_json_sidecar(nii_file_path)
        if json_info:
            results['json_metadata'] = json_info
        
        # 3. 文件名分析
        filename_info = self.analyze_filename_pattern(nii_file_path)
        if filename_info:
            results['filename_analysis'] = filename_info
        
        # 4. 目录结构分析
        directory_info = self.analyze_directory_structure(nii_file_path)
        if directory_info:
            results['directory_analysis'] = directory_info
        
        # 5. 综合判断
        final_assessment = self._make_final_assessment(results)
        results['final_assessment'] = final_assessment
        
        return results

    def _make_final_assessment(self, results):
        """基于所有分析结果做出最终判断"""
        assessment = {
            'confidence_level': 'unknown',
            'manufacturer': None,
            'model': None,
            'field_strength': None,
            'evidence_sources': []
        }
        
        # 收集所有证据
        evidence = {}
        
        # JSON metadata (最可靠)
        if 'json_metadata' in results:
            json_data = results['json_metadata']
            if 'Manufacturer' in json_data:
                evidence['manufacturer'] = ('json', json_data['Manufacturer'], 'high')
            if 'ManufacturerModelName' in json_data:
                evidence['model'] = ('json', json_data['ManufacturerModelName'], 'high')
            if 'MagneticFieldStrength' in json_data:
                evidence['field_strength'] = ('json', f"{json_data['MagneticFieldStrength']}T", 'high')
        
        # NII header extensions
        if 'nii_header' in results and 'extensions' in results['nii_header']:
            for ext in results['nii_header']['extensions']:
                if 'manufacturer' in ext:
                    if 'manufacturer' not in evidence:
                        evidence['manufacturer'] = ('nii_extension', ext['manufacturer'], 'medium')
                if 'model' in ext:
                    if 'model' not in evidence:
                        evidence['model'] = ('nii_extension', ext['model'], 'medium')
                if 'field_strength' in ext:
                    if 'field_strength' not in evidence:
                        evidence['field_strength'] = ('nii_extension', ext['field_strength'], 'medium')
        
        # 文件名分析 (可靠性较低)
        if 'filename_analysis' in results:
            filename_data = results['filename_analysis']
            if 'manufacturer' in filename_data:
                if 'manufacturer' not in evidence:
                    evidence['manufacturer'] = ('filename', filename_data['manufacturer'], 'low')
            if 'field_strength' in filename_data:
                if 'field_strength' not in evidence:
                    evidence['field_strength'] = ('filename', filename_data['field_strength'], 'low')
        
        # 目录结构分析 (可靠性最低)
        if 'directory_analysis' in results:
            dir_data = results['directory_analysis']
            if 'directory_manufacturer' in dir_data:
                if 'manufacturer' not in evidence:
                    evidence['manufacturer'] = ('directory', dir_data['directory_manufacturer'], 'very_low')
        
        # 设置最终评估
        for key, (source, value, confidence) in evidence.items():
            assessment[key] = value
            assessment['evidence_sources'].append({
                'field': key,
                'source': source,
                'value': value,
                'confidence': confidence
            })
        
        # 确定总体置信度
        if any(conf == 'high' for _, _, conf in evidence.values()):
            assessment['confidence_level'] = 'high'
        elif any(conf == 'medium' for _, _, conf in evidence.values()):
            assessment['confidence_level'] = 'medium'
        elif any(conf == 'low' for _, _, conf in evidence.values()):
            assessment['confidence_level'] = 'low'
        else:
            assessment['confidence_level'] = 'very_low'
        
        return assessment

    def batch_analysis(self, input_directory, output_file=None):
        """批量分析目录下的所有NII文件"""
        input_path = Path(input_directory)
        nii_files = list(input_path.rglob("*.nii")) + list(input_path.rglob("*.nii.gz"))
        
        if not nii_files:
            print(f"在目录 {input_directory} 中未找到NII文件")
            return None
        
        print(f"找到 {len(nii_files)} 个NII文件，开始分析...")
        
        results = []
        for i, nii_file in enumerate(nii_files, 1):
            print(f"分析文件 {i}/{len(nii_files)}: {nii_file.name}")
            
            analysis = self.comprehensive_analysis(nii_file)
            results.append(analysis)
        
        # 转换为DataFrame
        df = pd.json_normalize(results)
        
        if output_file:
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"结果已保存到: {output_file}")
        
        # 生成统计报告
        self._generate_summary_report(results)
        
        return results

    def _generate_summary_report(self, results):
        """生成统计报告"""
        print("\n" + "="*60)
        print("📊 扫描仪信息分析报告")
        print("="*60)
        
        total_files = len(results)
        print(f"总文件数: {total_files}")
        
        # 统计置信度分布
        confidence_counts = {}
        manufacturer_counts = {}
        field_strength_counts = {}
        
        for result in results:
            if 'final_assessment' in result:
                assessment = result['final_assessment']
                
                # 置信度统计
                confidence = assessment.get('confidence_level', 'unknown')
                confidence_counts[confidence] = confidence_counts.get(confidence, 0) + 1
                
                # 厂商统计
                manufacturer = assessment.get('manufacturer')
                if manufacturer:
                    manufacturer_counts[manufacturer] = manufacturer_counts.get(manufacturer, 0) + 1
                
                # 磁场强度统计
                field_strength = assessment.get('field_strength')
                if field_strength:
                    field_strength_counts[field_strength] = field_strength_counts.get(field_strength, 0) + 1
        
        print(f"\n🎯 置信度分布:")
        for confidence, count in sorted(confidence_counts.items()):
            percentage = (count / total_files) * 100
            print(f"  {confidence}: {count} ({percentage:.1f}%)")
        
        if manufacturer_counts:
            print(f"\n🏭 检测到的厂商:")
            for manufacturer, count in sorted(manufacturer_counts.items()):
                percentage = (count / total_files) * 100
                print(f"  {manufacturer}: {count} ({percentage:.1f}%)")
        
        if field_strength_counts:
            print(f"\n🧲 检测到的磁场强度:")
            for field_strength, count in sorted(field_strength_counts.items()):
                percentage = (count / total_files) * 100
                print(f"  {field_strength}: {count} ({percentage:.1f}%)")

# 使用示例
if __name__ == "__main__":
    detector = MRIScannerDetector()
    
    # 示例：分析单个文件
    # result = detector.comprehensive_analysis("example.nii.gz")
    # print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 示例：批量分析
    # results = detector.batch_analysis("/path/to/nii/files", "scanner_analysis_results.csv")
    
    print("MRI扫描仪检测器已准备就绪！")
    print("\n使用方法:")
    print("1. 单个文件分析:")
    print("   detector = MRIScannerDetector()")
    print("   result = detector.comprehensive_analysis('file.nii.gz')")
    print("\n2. 批量分析:")
    print("   results = detector.batch_analysis('/path/to/files', 'output.csv')")