# List of available models

| Models family             | Code link                              | All supported models                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
|---------------------------|----------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Resnet                    | [Code](timm_3d/models/resnet.py)       | 'resnet10t.c3_in1k', 'resnet14t.c3_in1k', 'resnet18.a1_in1k', 'resnet18.a2_in1k', 'resnet18.a3_in1k', 'resnet18d.ra2_in1k', 'resnet34.a1_in1k', 'resnet34.a2_in1k', 'resnet34.a3_in1k', 'resnet34.bt_in1k', 'resnet34d.ra2_in1k', 'resnet26.bt_in1k', 'resnet26d.bt_in1k', 'resnet26t.ra2_in1k', 'resnet50.a1_in1k', 'resnet50.a1h_in1k', 'resnet50.a2_in1k', 'resnet50.a3_in1k', 'resnet50.b1k_in1k', 'resnet50.b2k_in1k', 'resnet50.c1_in1k', 'resnet50.c2_in1k', 'resnet50.d_in1k', 'resnet50.ram_in1k', 'resnet50.am_in1k', 'resnet50.ra_in1k', 'resnet50.bt_in1k', 'resnet50d.ra2_in1k', 'resnet50d.a1_in1k', 'resnet50d.a2_in1k', 'resnet50d.a3_in1k', 'resnet50t.untrained', 'resnet101.a1_in1k', 'resnet101.a2_in1k', 'resnet101.a3_in1k', 'resnet101d.ra2_in1k', 'resnet152.a1h_in1k', 'resnet152.a1_in1k',  'resnet152.a2_in1k', 'resnet152.a3_in1k', 'resnet152d.ra2_in1k', 'resnet200.untrained', 'resnet200d.ra2_in1k', 'wide_resnet50_2.racm_in1k', 'resnet18.tv_in1k', 'resnet34.tv_in1k', 'resnet50.tv_in1k', 'resnet50.tv2_in1k', 'resnet101.tv_in1k', 'resnet101.tv2_in1k', 'resnet152.tv_in1k', 'resnet152.tv2_in1k', 'wide_resnet50_2.tv_in1k', 'wide_resnet50_2.tv2_in1k', 'wide_resnet101_2.tv_in1k', 'wide_resnet101_2.tv2_in1k', 'resnet50_gn.a1h_in1k', 'resnext50_32x4d.a1h_in1k', 'resnext50_32x4d.a1_in1k', 'resnext50_32x4d.a2_in1k', 'resnext50_32x4d.a3_in1k', 'resnext50_32x4d.ra_in1k', 'resnext50d_32x4d.bt_in1k', 'resnext101_32x4d.untrained', 'resnext101_64x4d.c1_in1k', 'resnext50_32x4d.tv_in1k', 'resnext101_32x8d.tv_in1k', 'resnext101_64x4d.tv_in1k', 'resnext50_32x4d.tv2_in1k', 'resnext101_32x8d.tv2_in1k', 'resnext101_32x8d.fb_wsl_ig1b_ft_in1k', 'resnext101_32x16d.fb_wsl_ig1b_ft_in1k', 'resnext101_32x32d.fb_wsl_ig1b_ft_in1k', 'resnet18.fb_ssl_yfcc100m_ft_in1k', 'resnet50.fb_ssl_yfcc100m_ft_in1k', 'resnext50_32x4d.fb_ssl_yfcc100m_ft_in1k', 'resnext101_32x4d.fb_ssl_yfcc100m_ft_in1k', 'resnext101_32x8d.fb_ssl_yfcc100m_ft_in1k', 'resnext101_32x16d.fb_ssl_yfcc100m_ft_in1k', 'resnet18.fb_swsl_ig1b_ft_in1k', 'resnet50.fb_swsl_ig1b_ft_in1k', 'resnext50_32x4d.fb_swsl_ig1b_ft_in1k', 'resnext101_32x4d.fb_swsl_ig1b_ft_in1k', 'resnext101_32x8d.fb_swsl_ig1b_ft_in1k', 'resnext101_32x16d.fb_swsl_ig1b_ft_in1k', 'ecaresnet26t.ra2_in1k', 'ecaresnetlight.miil_in1k', 'ecaresnet50d.miil_in1k', 'ecaresnet50t.a1_in1k', 'ecaresnet50t.a2_in1k', 'ecaresnet50t.a3_in1k', 'ecaresnet101d.miil_in1k', 'ecaresnet200d.untrained', 'ecaresnet269d.ra2_in1k', 'ecaresnext26t_32x4d.untrained', 'ecaresnext50t_32x4d.untrained', 'seresnet18.untrained', 'seresnet34.untrained', 'seresnet50.a1_in1k', 'seresnet50.a2_in1k', 'seresnet50.a3_in1k', 'seresnet50.ra2_in1k', 'seresnet50t.untrained', 'seresnet101.untrained', 'seresnet152.untrained', 'seresnet152d.ra2_in1k', 'seresnet200d.untrained', 'seresnet269d.untrained', 'seresnext26d_32x4d.bt_in1k', 'seresnext26t_32x4d.bt_in1k', 'seresnext50_32x4d.racm_in1k', 'seresnext101_32x4d.untrained', 'seresnext101_32x8d.ah_in1k', 'seresnext101d_32x8d.ah_in1k', 'resnetblur18.untrained', 'resnetblur50.bt_in1k', 'resnetblur50d.untrained', 'resnetblur101d.untrained', 'resnetrs50.tf_in1k', 'resnetrs101.tf_in1k', 'resnetrs152.tf_in1k', 'resnetrs200.tf_in1k', 'resnetrs270.tf_in1k', 'resnetrs350.tf_in1k', 'resnetrs420.tf_in1k', 'resnet18.gluon_in1k', 'resnet34.gluon_in1k', 'resnet50.gluon_in1k', 'resnet101.gluon_in1k', 'resnet152.gluon_in1k', 'resnet50c.gluon_in1k', 'resnet101c.gluon_in1k', 'resnet152c.gluon_in1k', 'resnet50d.gluon_in1k', 'resnet101d.gluon_in1k', 'resnet152d.gluon_in1k', 'resnet50s.gluon_in1k', 'resnet101s.gluon_in1k', 'resnet152s.gluon_in1k', 'resnext50_32x4d.gluon_in1k', 'resnext101_32x4d.gluon_in1k', 'resnext101_64x4d.gluon_in1k', 'seresnext50_32x4d.gluon_in1k', 'seresnext101_32x4d.gluon_in1k', 'seresnext101_64x4d.gluon_in1k', 'senet154.gluon_in1k'                                                                                                                                                                                                |
| Efficientnet family       | [Code](timm_3d/models/efficientnet.py) | 'mnasnet_100.rmsp_in1k', 'mnasnet_050.untrained', 'mnasnet_075.untrained', 'mnasnet_100.rmsp_in1k', 'mnasnet_140.untrained', 'semnasnet_050.untrained', 'semnasnet_075.rmsp_in1k', 'semnasnet_100.rmsp_in1k', 'semnasnet_140.untrained', 'mnasnet_small.lamb_in1k', 'mobilenetv2_035.untrained', 'mobilenetv2_050.lamb_in1k', 'mobilenetv2_075.untrained', 'mobilenetv2_100.ra_in1k', 'mobilenetv2_110d.ra_in1k', 'mobilenetv2_120d.ra_in1k', 'mobilenetv2_140.ra_in1k', 'fbnetc_100.rmsp_in1k', 'spnasnet_100.rmsp_in1k', 'efficientnet_b0.ra_in1k', 'efficientnet_b1.ft_in1k', 'efficientnet_b2.ra_in1k', 'efficientnet_b3.ra2_in1k', 'efficientnet_b4.ra2_in1k', 'efficientnet_b5.sw_in12k_ft_in1k', 'efficientnet_b5.sw_in12k', 'efficientnet_b6.untrained', 'efficientnet_b7.untrained', 'efficientnet_b8.untrained', 'efficientnet_l2.untrained', 'efficientnet_b0_gn.untrained', 'efficientnet_b0_g8_gn.untrained', 'efficientnet_b0_g16_evos.untrained', 'efficientnet_b3_gn.untrained', 'efficientnet_b3_g8_gn.untrained', 'efficientnet_es.ra_in1k', 'efficientnet_em.ra2_in1k', 'efficientnet_el.ra_in1k', 'efficientnet_es_pruned.in1k', 'efficientnet_el_pruned.in1k', 'efficientnet_cc_b0_4e.untrained', 'efficientnet_cc_b0_8e.untrained', 'efficientnet_cc_b1_8e.untrained', 'efficientnet_lite0.ra_in1k', 'efficientnet_lite1.untrained', 'efficientnet_lite2.untrained', 'efficientnet_lite3.untrained', 'efficientnet_lite4.untrained', 'efficientnet_b1_pruned.in1k', 'efficientnet_b2_pruned.in1k', 'efficientnet_b3_pruned.in1k', 'efficientnetv2_rw_t.ra2_in1k', 'gc_efficientnetv2_rw_t.agc_in1k', 'efficientnetv2_rw_s.ra2_in1k', 'efficientnetv2_rw_m.agc_in1k', 'efficientnetv2_s.untrained', 'efficientnetv2_m.untrained', 'efficientnetv2_l.untrained', 'efficientnetv2_xl.untrained', 'tf_efficientnet_b0.ns_jft_in1k', 'tf_efficientnet_b1.ns_jft_in1k', 'tf_efficientnet_b2.ns_jft_in1k', 'tf_efficientnet_b3.ns_jft_in1k', 'tf_efficientnet_b4.ns_jft_in1k', 'tf_efficientnet_b5.ns_jft_in1k', 'tf_efficientnet_b6.ns_jft_in1k', 'tf_efficientnet_b7.ns_jft_in1k', 'tf_efficientnet_l2.ns_jft_in1k_475', 'tf_efficientnet_l2.ns_jft_in1k', 'tf_efficientnet_b0.ap_in1k', 'tf_efficientnet_b1.ap_in1k', 'tf_efficientnet_b2.ap_in1k', 'tf_efficientnet_b3.ap_in1k', 'tf_efficientnet_b4.ap_in1k', 'tf_efficientnet_b5.ap_in1k', 'tf_efficientnet_b6.ap_in1k', 'tf_efficientnet_b7.ap_in1k', 'tf_efficientnet_b8.ap_in1k', 'tf_efficientnet_b5.ra_in1k', 'tf_efficientnet_b7.ra_in1k', 'tf_efficientnet_b8.ra_in1k', 'tf_efficientnet_b0.aa_in1k', 'tf_efficientnet_b1.aa_in1k', 'tf_efficientnet_b2.aa_in1k', 'tf_efficientnet_b3.aa_in1k', 'tf_efficientnet_b4.aa_in1k', 'tf_efficientnet_b5.aa_in1k', 'tf_efficientnet_b6.aa_in1k', 'tf_efficientnet_b7.aa_in1k', 'tf_efficientnet_b0.in1k', 'tf_efficientnet_b1.in1k', 'tf_efficientnet_b2.in1k', 'tf_efficientnet_b3.in1k', 'tf_efficientnet_b4.in1k', 'tf_efficientnet_b5.in1k', 'tf_efficientnet_es.in1k', 'tf_efficientnet_em.in1k', 'tf_efficientnet_el.in1k', 'tf_efficientnet_cc_b0_4e.in1k', 'tf_efficientnet_cc_b0_8e.in1k', 'tf_efficientnet_cc_b1_8e.in1k', 'tf_efficientnet_lite0.in1k', 'tf_efficientnet_lite1.in1k', 'tf_efficientnet_lite2.in1k', 'tf_efficientnet_lite3.in1k', 'tf_efficientnet_lite4.in1k', 'tf_efficientnetv2_s.in21k_ft_in1k', 'tf_efficientnetv2_m.in21k_ft_in1k', 'tf_efficientnetv2_l.in21k_ft_in1k', 'tf_efficientnetv2_xl.in21k_ft_in1k', 'tf_efficientnetv2_s.in1k', 'tf_efficientnetv2_m.in1k', 'tf_efficientnetv2_l.in1k', 'tf_efficientnetv2_s.in21k', 'tf_efficientnetv2_m.in21k', 'tf_efficientnetv2_l.in21k', 'tf_efficientnetv2_xl.in21k', 'tf_efficientnetv2_b0.in1k', 'tf_efficientnetv2_b1.in1k', 'tf_efficientnetv2_b2.in1k', 'tf_efficientnetv2_b3.in21k_ft_in1k', 'tf_efficientnetv2_b3.in1k', 'tf_efficientnetv2_b3.in21k', 'mixnet_s.ft_in1k', 'mixnet_m.ft_in1k', 'mixnet_l.ft_in1k', 'mixnet_xl.ra_in1k', 'mixnet_xxl.untrained', 'tf_mixnet_s.in1k', 'tf_mixnet_m.in1k', 'tf_mixnet_l.in1k', "tinynet_a.in1k", "tinynet_b.in1k", "tinynet_c.in1k", "tinynet_d.in1k", "tinynet_e.in1k" |
| MaxVit and CoAtNet family | [Code](timm_3d/models/maxxvit.py)      | 'coatnet_nano_rw_224.sw_in1k', 'coatnet_0_rw_224.sw_in1k', 'coatnet_1_rw_224.sw_in1k', 'coatnet_2_rw_224.sw_in12k_ft_in1k', 'coatnet_rmlp_1_rw2_224.sw_in12k_ft_in1k', 'coatnet_rmlp_2_rw_224.sw_in12k_ft_in1k', 'coatnet_rmlp_2_rw_384.sw_in12k_ft_in1k', 'coatnet_bn_0_rw_224.sw_in1k', 'coatnet_rmlp_nano_rw_224.sw_in1k', 'coatnet_rmlp_0_rw_224.untrained', 'coatnet_rmlp_1_rw_224.sw_in1k', 'coatnet_rmlp_2_rw_224.sw_in1k', 'coatnet_rmlp_3_rw_224.untrained', 'coatnet_nano_cc_224.untrained', 'coatnext_nano_rw_224.sw_in1k', 'coatnet_2_rw_224.sw_in12k', 'coatnet_3_rw_224.sw_in12k', 'coatnet_rmlp_2_rw_224.sw_in12k', 'coatnet_0_224.untrained', 'coatnet_1_224.untrained', 'coatnet_2_224.untrained', 'coatnet_3_224.untrained', 'coatnet_4_224.untrained', 'coatnet_5_224.untrained', 'maxvit_tiny_tf_224.in1k', 'maxvit_tiny_tf_384.in1k', 'maxvit_tiny_tf_512.in1k', 'maxvit_small_tf_224.in1k', 'maxvit_small_tf_384.in1k', 'maxvit_small_tf_512.in1k', 'maxvit_base_tf_224.in1k', 'maxvit_base_tf_384.in1k', 'maxvit_base_tf_512.in1k', 'maxvit_large_tf_224.in1k', 'maxvit_large_tf_384.in1k', 'maxvit_large_tf_512.in1k', 'maxvit_base_tf_224.in21k', 'maxvit_base_tf_384.in21k_ft_in1k', 'maxvit_base_tf_512.in21k_ft_in1k', 'maxvit_large_tf_224.in21k', 'maxvit_large_tf_384.in21k_ft_in1k', 'maxvit_large_tf_512.in21k_ft_in1k', 'maxvit_xlarge_tf_224.in21k', 'maxvit_xlarge_tf_384.in21k_ft_in1k', 'maxvit_xlarge_tf_512.in21k_ft_in1k'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ConvNext family           | [Code](timm_3d/models/convnext.py)     | 'convnext_tiny', 'convnext_small', 'convnext_atto', 'convnext_atto_ols', 'convnext_femto', 'convnext_femto_ols', 'convnext_pico', 'convnext_pico_ols', 'convnext_nano', 'convnext_nano_ols', 'convnext_tiny_hnf', 'convnext_base', 'convnext_large', 'convnext_xlarge', 'convnextv2_nano', 'convnextv2_tiny', 'convnextv2_base', 'convnextv2_large', 'convnextv2_huge', 'convnextv2_atto', 'convnextv2_femto', 'convnextv2_pico', 'convnextv2_small', 'convnext_large_mlp', 'convnext_xxlarge'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| DenseNet family           | [Code](timm_3d/models/densenet.py)     | 'densenet121', 'densenetblur121d', 'densenet264d', 'densenet169', 'densenet201', 'densenet161'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| VGG family                | [Code](timm_3d/models/vgg.py)          | 'vgg11', 'vgg13', 'vgg16', 'vgg19', 'vgg11_bn', 'vgg13_bn', 'vgg16_bn', 'vgg19_bn'                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
