name: build

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2.2.2
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install poetry
      run: make poetry-download

    - name: Set up cache
      uses: actions/cache@v4
      with:
        path: .venv
        key: venv-${{ matrix.python-version }}-${{ hashFiles('pyproject.toml') }}-${{ hashFiles('poetry.lock') }}
    - name: Install dependencies
      run: |
        poetry config virtualenvs.in-project true
        poetry install

    - name: Run style checks
      run: |
        make check-codestyle

    - name: Run tests
      run: |
        make test

    - name: Run safety checks
      run: |
        make check-safety
