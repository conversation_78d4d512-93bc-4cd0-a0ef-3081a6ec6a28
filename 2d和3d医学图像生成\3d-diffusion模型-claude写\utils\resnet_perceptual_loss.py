import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import resnet50

class ResNetPerceptualLoss(nn.Module):
    """基于ResNet50的感知损失"""
    def __init__(self, feature_layers=None):
        super().__init__()
        
        if feature_layers is None:
            # ResNet50的关键层
            feature_layers = ['layer1', 'layer2', 'layer3']
        
        self.feature_layers = feature_layers
        
        # 加载预训练ResNet50
        resnet = resnet50(pretrained=True)
        
        # 提取特征层
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool
        self.layer1 = resnet.layer1
        self.layer2 = resnet.layer2
        self.layer3 = resnet.layer3
        self.layer4 = resnet.layer4
        
        # 冻结参数
        for param in self.parameters():
            param.requires_grad = False
        
    def forward(self, predicted, target):
        # 转换为3通道
        if predicted.size(1) == 1:
            predicted = predicted.repeat(1, 3, 1, 1)
        if target.size(1) == 1:
            target = target.repeat(1, 3, 1, 1)
        
        # 标准化
        predicted = torch.clamp(predicted, 0, 1)
        target = torch.clamp(target, 0, 1)
        
        # ImageNet标准化
        mean = torch.tensor([0.485, 0.456, 0.406]).to(predicted.device)
        std = torch.tensor([0.229, 0.224, 0.225]).to(predicted.device)
        
        predicted = (predicted - mean.view(1, 3, 1, 1)) / std.view(1, 3, 1, 1)
        target = (target - mean.view(1, 3, 1, 1)) / std.view(1, 3, 1, 1)
        
        # 提取特征
        pred_features = self.extract_features(predicted)
        target_features = self.extract_features(target)
        
        # 计算损失
        loss = 0
        for layer in self.feature_layers:
            pred_feat = pred_features[layer]
            target_feat = target_features[layer]
            loss += F.mse_loss(pred_feat, target_feat)
        
        return loss / len(self.feature_layers)
    
    def extract_features(self, x):
        features = {}
        
        # 初始卷积层
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        # ResNet层
        x = self.layer1(x)
        features['layer1'] = x
        
        x = self.layer2(x)
        features['layer2'] = x
        
        x = self.layer3(x)
        features['layer3'] = x
        
        x = self.layer4(x)
        features['layer4'] = x
        
        return features

# 对比测试
if __name__ == "__main__":
    from loss_functions import PerceptualLoss
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 测试数据
    predicted = torch.randn(2, 1, 256, 256).to(device)
    target = torch.randn(2, 1, 256, 256).to(device)
    
    # VGG19感知损失
    vgg_loss = PerceptualLoss()
    vgg_loss_value = vgg_loss(predicted, target)
    
    # ResNet50感知损失
    resnet_loss = ResNetPerceptualLoss()
    resnet_loss_value = resnet_loss(predicted, target)
    
    print(f"VGG19感知损失: {vgg_loss_value.item():.4f}")
    print(f"ResNet50感知损失: {resnet_loss_value.item():.4f}")
    
    # 参数量对比
    vgg_params = sum(p.numel() for p in vgg_loss.parameters())
    resnet_params = sum(p.numel() for p in resnet_loss.parameters())
    
    print(f"VGG19参数量: {vgg_params:,}")
    print(f"ResNet50参数量: {resnet_params:,}")