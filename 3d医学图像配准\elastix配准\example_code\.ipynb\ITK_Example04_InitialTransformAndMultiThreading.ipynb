{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 4. Image Registration with initial transform and/or multiple threads"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this notebook 2 other options of the elastix algorithm are shown: initial transformation and multithreading.\n", "They're shown together just to reduce the number of example notebooks and \n", "thus can be used independently as well as in combination with whichever other functionality\n", "of the elastix algorithm. \n", "\n", "Initial transforms are transformations that are done on the moving image before the registration is started.\n", "\n", "Multithreading spreaks for itself and can be used in similar fashion in the transformix algorithm.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Registration"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import itk"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Import Images\n", "fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_map_rigid = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(parameter_map_rigid)\n", "parameter_object.AddParameterMap(parameter_map_rigid)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Registration can either be done in one line with the registration function..."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Call registration function with initial transfrom and number of threads\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object,\n", "    initial_transform_parameter_file_name='data/TransformParameters.0.txt',\n", "    number_of_threads=4,\n", "    log_to_console=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an elastix image filter object."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Load Elastix Image Filter Object with initial transfrom and number of threads\n", "elastix_object = itk.ElastixRegistrationMethod.New(fixed_image,moving_image)\n", "# elastix_object.SetFixedImage(fixed_image)\n", "# elastix_object.SetMovingImage(moving_image)\n", "elastix_object.SetParameterObject(parameter_object)\n", "elastix_object.SetInitialTransformParameterFileName(\n", "    'data/TransformParameters.0.txt')\n", "elastix_object.SetNumberOfThreads(4)\n", "\n", "# Set additional options\n", "elastix_object.SetLogToConsole(False)\n", "\n", "# Update filter object (required)\n", "elastix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Registration\n", "result_image = elastix_object.GetOutput()\n", "result_transform_parameters = elastix_object.GetTransformParameterObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pointing from a transform parameter file to the path of a second initial transform parameter file is supported from the 0.7.0 release of ITKElastix."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}