name: Run detailed tests
on:
  push:
    paths-ignore:
      - '**.md'
jobs:

  run-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]  # save action minutes; not relevant: for open source unlimited minutes
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{matrix.python-version}}
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python-version}}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest Cython fury xgboost
          pip install torch==2.4.0 -f https://download.pytorch.org/whl/cpu
          pip install .

      - name: Run test script
        run: ./tests/tests.sh
        shell: bash

  # upload-dockerhub:
  #   runs-on: ubuntu-latest
  #   # needs: run-tests
  #   # if: success() && github.ref == 'refs/heads/master'
  #   strategy:
  #     matrix:
  #       python-version: ["3.8"]
  #   steps:
  #     - uses: actions/checkout@v4
  #     - name: Check disk space
  #       run: df -h
  #     - name: Login to Docker Hub
  #       uses: docker/login-action@v1
  #       with:
  #         username: ${{ secrets.DOCKER_HUB_USERNAME }}
  #         password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}
  #     - name: Set up Docker Buildx
  #       uses: docker/setup-buildx-action@v1
  #     - name: Build and push
  #       uses: docker/build-push-action@v2
  #       with:
  #         context: .
  #         file: ./Dockerfile
  #         push: true
  #         tags: ${{ secrets.DOCKER_HUB_USERNAME }}/totalsegmentator_container:master