DATASET: 'ONCEDataset'
DATA_PATH: '../data/once'
# OSS_PATH: 's3://${PATH_TO_DATASET}/once'

POINT_CLOUD_RANGE: [-75.2, -75.2, -5.0, 75.2, 75.2, 3.0]

INFO_PATH: {
    'train': [once_infos_train_vehicle.pkl],
    'val': [once_infos_val_vehicle.pkl],
    'test': [once_infos_test.pkl],
}

DATA_SPLIT: {
    'train': train,
    'test': val
}

DATA_AUGMENTOR:
    DISABLE_AUG_LIST: ['placeholder']
    AUG_CONFIG_LIST:
        - NAME: gt_sampling
          USE_ROAD_PLANE: False
          DB_INFO_PATH:
              - once_dbinfos_train_vehicle.pkl #once_dbinfos_train.pkl
          PREPARE: {
             filter_by_min_points: ['Vehicle:5', 'Pedestrian:5', 'Cyclist:5'],
          }

          SAMPLE_GROUPS: ['Vehicle:15', 'Pedestrian:10', 'Cyclist:10']
          NUM_POINT_FEATURES: 4
          REMOVE_EXTRA_WIDTH: [0.0, 0.0, 0.0]
          LIMIT_WHOLE_SCENE: True

        - NAME: random_world_flip
          ALONG_AXIS_LIST: ['x', 'y']

        - NAME: random_world_rotation
          WORLD_ROT_ANGLE: [-0.78539816, 0.78539816]

        - NAME: random_world_scaling
          WORLD_SCALE_RANGE: [0.95, 1.05]


POINT_FEATURE_ENCODING: {
    encoding_type: absolute_coordinates_encoding,
    used_feature_list: ['x', 'y', 'z', 'intensity'],
    src_feature_list: ['x', 'y', 'z', 'intensity'],
}


DATA_PROCESSOR:
    - NAME: mask_points_and_boxes_outside_range
      REMOVE_OUTSIDE_BOXES: True

    - NAME: shuffle_points
      SHUFFLE_ENABLED: {
        'train': True,
        'test': False
      }

    - NAME: transform_points_to_voxels
      VOXEL_SIZE: [0.1, 0.1, 0.2]
      MAX_POINTS_PER_VOXEL: 5
      MAX_NUMBER_OF_VOXELS: {
        'train': 60000,
        'test': 60000
      }
