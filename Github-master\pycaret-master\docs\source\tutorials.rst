Tutorials
===================================

Classification
**************
Learn how to prepare the data for modeling, create a classification model, tune hyperparameters of a model, analyze the performance, and consume the model for predictions.

- `Binary Classification Tutorial <https://github.com/pycaret/pycaret/blob/master/tutorials/Tutorial%20-%20Binary%20Classification.ipynb>`_

- `Multiclass Classification Tutorial <https://github.com/pycaret/pycaret/blob/master/tutorials/Tutorial%20-%20Multiclass%20Classification.ipynb>`_

Regression
**************
Learn how to prepare the data for modeling, create a regression model, tune hyperparameters of a model, evaluate model errors, and consume the model for predictions.

- `Regression Tutorial <https://github.com/pycaret/pycaret/blob/master/tutorials/Tutorial%20-%20Regression.ipynb>`_

Clustering
**************
Learn how to prepare the data for modeling, create a K-Means clustering model, assign the labels, analyze results, and consume a trained model for predictions on unseen data.

- `Clustering Tutorial <https://github.com/pycaret/pycaret/blob/master/tutorials/Tutorial%20-%20Clustering.ipynb>`_


Anomaly Detection
*****************
Learn how to prepare the data for modeling, create an unsupervised anomaly detector, evaluate the results of the trained model, and consume the model for predictions on unseen data.

- `Anomaly Detection Tutorial <https://github.com/pycaret/pycaret/blob/master/tutorials/Tutorial%20-%20Anomaly%20Detection.ipynb>`_


Time Series Forecasting
***********************
Learn how to prepare the data for modeling, create and compare a time series model, analyze it, use it to make predictions, and save the model.


- `Time Series Forecasting Tutorial <https://github.com/pycaret/pycaret/blob/master/tutorials/Tutorial%20-%20Time%20Series%20Forecasting.ipynb>`_
