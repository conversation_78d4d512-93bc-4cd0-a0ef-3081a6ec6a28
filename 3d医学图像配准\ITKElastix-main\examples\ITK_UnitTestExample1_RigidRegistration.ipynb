{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def image_generator(x1, x2, y1, y2):\n", "    image = np.zeros([100, 100], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    image = itk.image_view_from_array(image)\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rigid Test"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Create rigid transformed test images\n", "fixed_image = image_generator(25,75,25,75)\n", "moving_image = image_generator(1,51,10,60)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(default_rigid_parameter_map)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object,\n", "    log_to_console=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization Rigid Test"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 2160x2160 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(1,3, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0].imshow(result_image)\n", "axs[0].set_title('Result', fontsize=30)\n", "axs[1].imshow(fixed_image)\n", "axs[1].set_title('Fixed', fontsize=30)\n", "axs[2].imshow(moving_image)\n", "axs[2].set_title('Moving', fontsize=30)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}