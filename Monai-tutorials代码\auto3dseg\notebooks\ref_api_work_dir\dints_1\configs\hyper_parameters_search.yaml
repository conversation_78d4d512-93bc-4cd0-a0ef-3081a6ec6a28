_meta_: {}
searching:
  amp: true
  arch_optimizer_a:
    _target_: torch.optim.Adam
    betas: [0.5, 0.999]
    lr: '@searching#learning_rate_arch'
    weight_decay: 0
  arch_optimizer_c:
    _target_: torch.optim.Adam
    betas: [0.5, 0.999]
    lr: '@searching#learning_rate_arch'
    weight_decay: 0
  arch_path: $@bundle_root + '/arch_ram' + str(@searching#ram_cost_factor) + '_fold'
    + str(@fold)
  cache_rate: 1
  determ: false
  input_channels: 1
  learning_rate: 0.025
  learning_rate_arch: 0.001
  log_output_file: $@bundle_root + '/arch_ram' + str(@searching#ram_cost_factor) +
    '_fold' + str(@fold) + '/searching.log'
  loss: {_target_: DiceFocalLoss, batch: true, include_background: true, sigmoid: $not
      @searching#softmax, smooth_dr: 1.0e-05, smooth_nr: 1.0e-05, softmax: '@searching#softmax',
    squared_pred: true, to_onehot_y: '@searching#softmax'}
  lr_scheduler: {_target_: torch.optim.lr_scheduler.StepLR, gamma: 0.5, step_size: '$max(int(float(@searching#num_epochs
      - @searching#num_warmup_epochs) * 0.4), 1)'}
  num_cache_workers: 8
  num_crops_per_image: 1
  num_epochs: 1000
  num_epochs_per_validation: 20
  num_images_per_batch: 2
  num_sw_batch_size: 2
  num_warmup_epochs: 500
  num_workers: 6
  optimizer: {_target_: torch.optim.SGD, lr: '@searching#learning_rate', momentum: 0.9,
    weight_decay: 4.0e-05}
  output_classes: 3
  overlap_ratio: 0.625
  ram_cost_factor: 0.8
  resample_resolution: [1.0, 1.0, 1.0]
  roi_size: [32, 32, 32]
  roi_size_valid: [32, 32, 32]
  softmax: true
  sw_input_on_cpu: false
  train_cache_rate: '@searching#cache_rate'
  transforms: {resample_resolution: '@searching#resample_resolution'}
  validate_cache_rate: '@searching#cache_rate'
stats_summary:
  image_stats:
    shape:
      mean: [35.4, 50.666666666666664, 36.06666666666667]
  n_cases: 30
