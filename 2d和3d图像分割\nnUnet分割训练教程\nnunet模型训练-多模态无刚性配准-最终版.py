#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nnUNet数据预处理和模型训练脚本
基于nnUNet v2项目代码整合

功能:
1. 数据集准备和格式转换
2. 环境变量设置
3. 数据预处理和实验规划
4. 模型训练
5. 数据验证和错误处理

作者: 基于nnUNet项目代码整合
"""
# 确保已安装nnUNet: pip install nnunetv2
# 确保已安装依赖: pip install SimpleITK
#安装 pip install batchgenerators==0.25.1

import os
import sys
import json
import shutil
import subprocess
import logging
from pathlib import Path
from typing import Union, List, Dict, Optional, Tuple
import SimpleITK as sitk
import numpy as np
import re

# 创建自定义trainer类以支持不同的epoch数量
def create_custom_trainer(num_epochs: int, trainer_name: str = None):
    """
    创建自定义的nnUNet trainer类，支持指定epoch数量

    参数:
        num_epochs: 训练轮数
        trainer_name: 自定义trainer名称
    """
    if trainer_name is None:
        trainer_name = f"nnUNetTrainer_{num_epochs}epochs"

    # 创建自定义trainer文件
    trainer_code = f'''
from nnunetv2.training.nnUNetTrainer.nnUNetTrainer import nnUNetTrainer

class {trainer_name}(nnUNetTrainer):
    def __init__(self, plans: dict, configuration: str, fold: int, dataset_json: dict, unpack_dataset: bool = True,
                 device: str = 'cuda'):
        super().__init__(plans, configuration, fold, dataset_json, unpack_dataset, device)
        self.num_epochs = {num_epochs}
'''

    # 确保nnunetv2训练器目录存在
    try:
        import nnunetv2
        trainer_dir = Path(nnunetv2.__file__).parent / "training" / "nnUNetTrainer"
        trainer_file = trainer_dir / f"{trainer_name}.py"

        # 写入自定义trainer文件
        with open(trainer_file, 'w', encoding='utf-8') as f:
            f.write(trainer_code)

        logger.info(f"创建自定义trainer: {trainer_name} (epochs: {num_epochs})")
        return trainer_name

    except Exception as e:
        logger.warning(f"无法创建自定义trainer: {e}")
        return "nnUNetTrainer"

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nnunet_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class nnUNetPreprocessingTraining:
    """nnUNet数据预处理和训练类"""
    
    def __init__(self, 
                 base_dir: str = "./nnUNet_workspace",
                 dataset_id: int = 100,
                 dataset_name: str = "CustomDataset"):
        """
        初始化nnUNet预处理和训练环境
        
        参数:
            base_dir: nnUNet工作目录
            dataset_id: 数据集ID (3位数字)
            dataset_name: 数据集名称
        """
        self.base_dir = Path(base_dir)
        self.dataset_id = dataset_id
        self.dataset_name = dataset_name
        self.dataset_folder_name = f"Dataset{dataset_id:03d}_{dataset_name}"
        
        # 创建nnUNet目录结构
        self.nnunet_raw = self.base_dir / "nnUNet_raw"
        self.nnunet_preprocessed = self.base_dir / "nnUNet_preprocessed"
        self.nnunet_results = self.base_dir / "nnUNet_results"
        
        self.dataset_path = self.nnunet_raw / self.dataset_folder_name
        
        self._setup_directories()
        self._setup_environment()
    
    def _setup_directories(self):
        """创建nnUNet目录结构"""
        directories = [
            self.nnunet_raw,
            self.nnunet_preprocessed,
            self.nnunet_results,
            self.dataset_path,
            self.dataset_path / "imagesTr",
            self.dataset_path / "labelsTr",
            self.dataset_path / "imagesTs",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
    
    def _setup_environment(self):
        """设置nnUNet环境变量"""
        env_vars = {
            'nnUNet_raw': str(self.nnunet_raw),
            'nnUNet_preprocessed': str(self.nnunet_preprocessed),
            'nnUNet_results': str(self.nnunet_results)
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"设置环境变量: {key}={value}")
    
    def prepare_data_from_folders(self,
                                  images_folders: Dict[str, str],
                                  masks_folder: str,
                                  test_images_folders: Optional[Dict[str, str]] = None,
                                  train_ratio: float = 1.0,#使用全部数据集训练
                                  channel_names: Dict[str, str] = None,
                                  labels: Dict[str, int] = None):
        """
        从文件夹准备nnUNet格式的多序列数据

        参数:
            images_folders: 多序列图像文件夹路径字典，格式: {"序列名": "路径"}
            masks_folder: 标签文件夹路径
            test_images_folders: 测试图像文件夹路径字典(可选)
            train_ratio: 训练集比例
            channel_names: 通道名称字典
            labels: 标签字典
        """
        logger.info("开始准备多序列数据...")

        # 默认参数
        if channel_names is None:
            # 为3个序列设置默认通道名称
            channel_names = {"0": "ap", "1": "pp", "2": "hbp"}
        if labels is None:
            labels = {"background": 0, "HCC": 1}

        # 获取所有序列的图像文件
        all_sequence_files = {}
        for sequence_name, folder_path in images_folders.items():
            sequence_files = self._get_nii_files(folder_path)
            all_sequence_files[sequence_name] = sequence_files
            logger.info(f"找到 {len(sequence_files)} 个{sequence_name}序列图像文件")

        # 获取标签文件
        mask_files = self._get_nii_files(masks_folder)
        logger.info(f"找到 {len(mask_files)} 个标签文件")

        # 匹配多序列图像和标签文件
        matched_cases = self._match_multisequence_cases(all_sequence_files, mask_files)
        logger.info(f"匹配到 {len(matched_cases)} 个完整的多序列病例")

        # 划分训练集和测试集
        if test_images_folders is None:
            train_cases, test_cases = self._split_train_test_multisequence(matched_cases, train_ratio)
        else:
            train_cases = matched_cases
            # 处理测试集的多序列数据
            test_sequence_files = {}
            for sequence_name, folder_path in test_images_folders.items():
                test_sequence_files[sequence_name] = self._get_nii_files(folder_path)
            test_cases = self._prepare_test_multisequence_cases(test_sequence_files)

        # 复制训练数据
        self._copy_multisequence_training_data(train_cases, list(images_folders.keys()))

        # 复制测试数据
        if test_cases:
            self._copy_multisequence_test_data(test_cases, list(images_folders.keys()))

        # 创建dataset.json
        self._create_dataset_json(len(train_cases), channel_names, labels)

        logger.info("多序列数据准备完成!")
    
    def _get_nii_files(self, folder: str) -> List[str]:
        """获取文件夹中的所有.nii.gz文件"""
        folder_path = Path(folder)
        return sorted([str(f) for f in folder_path.glob("*.nii.gz")])
    
    def _match_image_mask_pairs(self, image_files: List[str], mask_files: List[str]) -> List[Tuple[str, str]]:
        """匹配图像和标签文件对，支持HCC数据集命名模式"""
        pairs = []

        # 创建标签文件的映射字典，便于快速查找
        mask_dict = {}
        for mask_file in mask_files:
            mask_path = Path(mask_file)
            mask_name = mask_path.stem.replace('.nii', '')

            # 去掉可能的mask后缀，得到基础名称
            base_name = mask_name
            if base_name.endswith('-mask'):
                base_name = base_name[:-5]
            elif base_name.endswith('_mask'):
                base_name = base_name[:-5]

            mask_dict[base_name] = mask_file

        for img_file in image_files:
            img_path = Path(img_file)
            img_name = img_path.stem.replace('.nii', '')

            # 查找对应的标签文件
            mask_file = None

            # 直接匹配基础名称
            if img_name in mask_dict:
                mask_file = mask_dict[img_name]
            else:
                # 尝试其他匹配模式
                possible_base_names = [
                    img_name,
                    img_name.replace('-ap', '').replace('-pp', '').replace('-hbp', ''),  # 去掉序列后缀
                ]

                for base_name in possible_base_names:
                    if base_name in mask_dict:
                        mask_file = mask_dict[base_name]
                        break

            if mask_file:
                pairs.append((img_file, mask_file))
                logger.info(f"匹配成功: {img_path.name} <-> {Path(mask_file).name}")
            else:
                logger.warning(f"未找到图像 {img_path.name} 对应的标签文件")
                logger.info(f"可用的标签文件基础名称: {list(mask_dict.keys())}")

        return pairs

    def _match_multisequence_cases(self, all_sequence_files: Dict[str, List[str]], mask_files: List[str]) -> List[Dict]:
        """匹配多序列图像和标签文件"""
        cases = []

        # 创建标签文件的映射字典
        mask_dict = {}
        for mask_file in mask_files:
            mask_path = Path(mask_file)
            mask_name = mask_path.stem.replace('.nii', '')

            # 去掉可能的mask后缀，得到基础名称
            base_name = mask_name
            # 处理 病例名-序列-mask 格式
            if '-ap-mask' in base_name:
                base_name = base_name.replace('-ap-mask', '')
            elif '-pp-mask' in base_name:
                base_name = base_name.replace('-pp-mask', '')
            elif '-hbp-mask' in base_name:
                base_name = base_name.replace('-hbp-mask', '')
            elif base_name.endswith('-mask'):
                base_name = base_name[:-5]
            elif base_name.endswith('_mask'):
                base_name = base_name[:-5]

            mask_dict[base_name] = mask_file

        # 获取第一个序列作为参考，找出所有病例的基础名称
        first_sequence = list(all_sequence_files.keys())[0]
        reference_files = all_sequence_files[first_sequence]

        for ref_file in reference_files:
            ref_path = Path(ref_file)
            ref_name = ref_path.stem.replace('.nii', '')

            # 提取基础病例名称（去掉序列后缀）
            base_case_name = ref_name
            for seq_suffix in ['-ap', '-pp', '-hbp', '_ap', '_pp', '_hbp']:
                if base_case_name.endswith(seq_suffix):
                    base_case_name = base_case_name[:-len(seq_suffix)]
                    break

            # 检查是否所有序列都存在
            case_files = {}
            all_sequences_found = True

            for sequence_name, sequence_files in all_sequence_files.items():
                # 查找该序列对应的文件
                sequence_file = None
                for seq_file in sequence_files:
                    seq_path = Path(seq_file)
                    seq_name = seq_path.stem.replace('.nii', '')
                    if seq_name.startswith(base_case_name):
                        sequence_file = seq_file
                        break

                if sequence_file:
                    case_files[sequence_name] = sequence_file
                else:
                    all_sequences_found = False
                    break

            # 查找对应的标签文件
            mask_file = mask_dict.get(base_case_name)

            if all_sequences_found and mask_file:
                case_info = {
                    'case_name': base_case_name,
                    'sequences': case_files,
                    'mask': mask_file
                }
                cases.append(case_info)
                logger.info(f"匹配完整病例: {base_case_name}")
            else:
                missing_info = []
                if not all_sequences_found:
                    missing_info.append("缺少序列")
                if not mask_file:
                    missing_info.append("缺少标签")
                logger.warning(f"病例 {base_case_name} 不完整: {', '.join(missing_info)}")

        return cases

    def _split_train_test_multisequence(self, cases: List[Dict], train_ratio: float) -> Tuple[List, List]:
        """划分多序列训练集和测试集"""
        import random
        random.seed(42)

        shuffled_cases = cases.copy()
        random.shuffle(shuffled_cases)

        split_idx = int(len(shuffled_cases) * train_ratio)
        train_cases = shuffled_cases[:split_idx]
        test_cases = shuffled_cases[split_idx:]

        return train_cases, test_cases

    def _prepare_test_multisequence_cases(self, test_sequence_files: Dict[str, List[str]]) -> List[Dict]:
        """准备测试集的多序列数据"""
        test_cases = []

        # 获取第一个序列作为参考
        first_sequence = list(test_sequence_files.keys())[0]
        reference_files = test_sequence_files[first_sequence]

        for ref_file in reference_files:
            ref_path = Path(ref_file)
            ref_name = ref_path.stem.replace('.nii', '')

            # 提取基础病例名称
            base_case_name = ref_name
            for seq_suffix in ['-ap', '-pp', '-hbp', '_ap', '_pp', '_hbp']:
                if base_case_name.endswith(seq_suffix):
                    base_case_name = base_case_name[:-len(seq_suffix)]
                    break

            # 收集所有序列文件
            case_files = {}
            for sequence_name, sequence_files in test_sequence_files.items():
                for seq_file in sequence_files:
                    seq_path = Path(seq_file)
                    seq_name = seq_path.stem.replace('.nii', '')
                    if seq_name.startswith(base_case_name):
                        case_files[sequence_name] = seq_file
                        break

            if len(case_files) == len(test_sequence_files):
                case_info = {
                    'case_name': base_case_name,
                    'sequences': case_files,
                    'mask': None  # 测试集没有标签
                }
                test_cases.append(case_info)

        return test_cases

    def _split_train_test(self, pairs: List[Tuple[str, str]], train_ratio: float) -> Tuple[List, List]:
        """划分训练集和测试集"""
        import random
        random.seed(42)
        
        shuffled_pairs = pairs.copy()
        random.shuffle(shuffled_pairs)
        
        split_idx = int(len(shuffled_pairs) * train_ratio)
        train_pairs = shuffled_pairs[:split_idx]
        test_pairs = shuffled_pairs[split_idx:]
        
        return train_pairs, test_pairs
    
    def _copy_training_data(self, train_pairs: List[Tuple[str, str]]):
        """复制训练数据到nnUNet格式，保持原始文件名"""
        logger.info("复制训练数据...")

        for i, (img_file, mask_file) in enumerate(train_pairs):
            # 保持原始文件名，只去掉路径
            img_path = Path(img_file)
            mask_path = Path(mask_file)

            # 获取原始文件名（不包含扩展名）
            img_base_name = img_path.stem.replace('.nii', '')  # 去掉.nii.gz中的.nii
            mask_base_name = mask_path.stem.replace('.nii', '')

            # 去掉标签文件名中的-mask后缀
            if mask_base_name.endswith('-mask'):
                mask_base_name = mask_base_name[:-5]  # 去掉'-mask'
            elif mask_base_name.endswith('_mask'):
                mask_base_name = mask_base_name[:-5]  # 去掉'_mask'

            # 复制图像文件 (添加_0000后缀)
            img_dst = self.dataset_path / "imagesTr" / f"{img_base_name}_0000.nii.gz"
            shutil.copy2(img_file, img_dst)

            # 复制标签文件（不添加_0000后缀）
            mask_dst = self.dataset_path / "labelsTr" / f"{mask_base_name}.nii.gz"
            shutil.copy2(mask_file, mask_dst)

            logger.info(f"复制训练数据 {i+1}/{len(train_pairs)}: {img_base_name} -> {mask_base_name}")

    def _resample_image_to_reference(self, image_path: str, reference_path: str, output_path: str):
        """将图像重采样到参考图像的空间"""
        import SimpleITK as sitk

        # 读取图像
        image = sitk.ReadImage(image_path)
        reference = sitk.ReadImage(reference_path)

        # 设置重采样参数
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(reference)
        resampler.SetInterpolator(sitk.sitkLinear)
        resampler.SetDefaultPixelValue(0)

        # 执行重采样
        resampled_image = resampler.Execute(image)

        # 保存重采样后的图像
        sitk.WriteImage(resampled_image, output_path)

        return output_path

    def _resample_mask_to_reference(self, mask_path: str, reference_path: str, output_path: str):
        """将标签图像重采样到参考图像的空间，使用最近邻插值"""
        import SimpleITK as sitk

        # 读取图像
        mask = sitk.ReadImage(mask_path)
        reference = sitk.ReadImage(reference_path)

        # 设置重采样参数（标签使用最近邻插值）
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(reference)
        resampler.SetInterpolator(sitk.sitkNearestNeighbor)  # 标签使用最近邻插值
        resampler.SetDefaultPixelValue(0)

        # 执行重采样
        resampled_mask = resampler.Execute(mask)

        # 保存重采样后的标签
        sitk.WriteImage(resampled_mask, output_path)

        return output_path

    def _copy_multisequence_training_data(self, train_cases: List[Dict], sequence_names: List[str]):
        """复制多序列训练数据到nnUNet格式，并进行空间配准"""
        logger.info("复制多序列训练数据...")

        for i, case_info in enumerate(train_cases):
            case_name = case_info['case_name']
            sequences = case_info['sequences']
            mask_file = case_info['mask']

            # 使用第一个序列作为参考空间
            reference_sequence = sequence_names[0]  # 使用ap作为参考
            reference_file = sequences[reference_sequence]

            # 复制每个序列的图像文件
            for j, sequence_name in enumerate(sequence_names):
                if sequence_name in sequences:
                    img_file = sequences[sequence_name]
                    img_dst = self.dataset_path / "imagesTr" / f"{case_name}_{j:04d}.nii.gz"

                    if sequence_name == reference_sequence:
                        # 参考序列直接复制
                        shutil.copy2(img_file, img_dst)
                    else:
                        # 其他序列需要配准到参考空间
                        try:
                            self._resample_image_to_reference(img_file, reference_file, str(img_dst))
                            logger.debug(f"配准 {sequence_name} 序列到 {reference_sequence} 空间: {Path(img_file).name}")
                        except Exception as e:
                            logger.warning(f"配准失败，使用原始文件: {e}")
                            shutil.copy2(img_file, img_dst)

            # 复制并配准标签文件到参考空间
            mask_dst = self.dataset_path / "labelsTr" / f"{case_name}.nii.gz"
            try:
                # 使用最近邻插值配准标签文件
                self._resample_mask_to_reference(mask_file, reference_file, str(mask_dst))
                logger.info(f"✅ 配准标签文件: {case_name}")

                # 验证配准结果
                import SimpleITK as sitk
                ref_img = sitk.ReadImage(reference_file)
                mask_img = sitk.ReadImage(str(mask_dst))

                if (ref_img.GetSize() == mask_img.GetSize() and
                    abs(ref_img.GetSpacing()[0] - mask_img.GetSpacing()[0]) < 1e-6):
                    logger.debug(f"配准验证成功: {case_name}")
                else:
                    logger.error(f"配准验证失败: {case_name}")
                    raise Exception("配准验证失败")

            except Exception as e:
                logger.error(f"❌ 标签配准失败: {case_name} - {e}")
                # 配准失败时，强制使用参考图像的空间重新配准
                try:
                    logger.info(f"尝试强制配准: {case_name}")
                    import SimpleITK as sitk
                    mask = sitk.ReadImage(mask_file)
                    reference = sitk.ReadImage(reference_file)

                    resampler = sitk.ResampleImageFilter()
                    resampler.SetReferenceImage(reference)
                    resampler.SetInterpolator(sitk.sitkNearestNeighbor)
                    resampler.SetDefaultPixelValue(0)

                    resampled_mask = resampler.Execute(mask)
                    sitk.WriteImage(resampled_mask, str(mask_dst))
                    logger.info(f"✅ 强制配准成功: {case_name}")
                except Exception as e2:
                    logger.error(f"❌ 强制配准也失败: {case_name} - {e2}")
                    shutil.copy2(mask_file, mask_dst)

            logger.info(f"复制多序列训练数据 {i+1}/{len(train_cases)}: {case_name}")

    def _copy_multisequence_test_data(self, test_cases: List[Dict], sequence_names: List[str]):
        """复制多序列测试数据到nnUNet格式，并进行空间配准"""
        logger.info("复制多序列测试数据...")

        for i, case_info in enumerate(test_cases):
            case_name = case_info['case_name']
            sequences = case_info['sequences']

            # 使用第一个序列作为参考空间
            reference_sequence = sequence_names[0]  # 使用ap作为参考
            reference_file = sequences[reference_sequence]

            # 复制每个序列的图像文件
            for j, sequence_name in enumerate(sequence_names):
                if sequence_name in sequences:
                    img_file = sequences[sequence_name]
                    img_dst = self.dataset_path / "imagesTs" / f"{case_name}_{j:04d}.nii.gz"

                    if sequence_name == reference_sequence:
                        # 参考序列直接复制
                        shutil.copy2(img_file, img_dst)
                    else:
                        # 其他序列需要配准到参考空间
                        try:
                            self._resample_image_to_reference(img_file, reference_file, str(img_dst))
                            logger.debug(f"配准测试 {sequence_name} 序列到 {reference_sequence} 空间: {Path(img_file).name}")
                        except Exception as e:
                            logger.warning(f"测试数据配准失败，使用原始文件: {e}")
                            shutil.copy2(img_file, img_dst)

            logger.info(f"复制多序列测试数据 {i+1}/{len(test_cases)}: {case_name}")

    def _copy_test_data(self, test_pairs: List[Tuple[str, Optional[str]]]):
        """复制测试数据到nnUNet格式，保持原始文件名"""
        logger.info("复制测试数据...")

        for i, (img_file, _) in enumerate(test_pairs):
            img_path = Path(img_file)
            # 获取原始文件名（不包含扩展名）
            img_base_name = img_path.stem.replace('.nii', '')

            img_dst = self.dataset_path / "imagesTs" / f"{img_base_name}_0000.nii.gz"
            shutil.copy2(img_file, img_dst)

            logger.info(f"复制测试数据 {i+1}/{len(test_pairs)}: {img_base_name}")
    
    def _create_dataset_json(self, num_training: int, channel_names: Dict, labels: Dict):
        """创建dataset.json文件"""
        dataset_json = {
            "channel_names": channel_names,
            "labels": labels,
            "numTraining": num_training,
            "file_ending": ".nii.gz",
            "overwrite_image_reader_writer": "SimpleITKIO"
        }
        
        json_path = self.dataset_path / "dataset.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_json, f, indent=4, ensure_ascii=False)
        
        logger.info(f"创建dataset.json: {json_path}")
        logger.info(f"数据集信息: {dataset_json}")

    def validate_and_fix_data(self):
        """验证并修复 imagesTr 与 labelsTr 中文件间的几何一致性
        1. 对每个病例（依据文件名前缀）收集所有通道图像以及其标签。
        2. 选择 _0000 通道作为参考，若不存在则选择列表中的第一个图像。
        3. 检查所有通道与参考图像的 size/spacing/origin/direction，若任一不一致则重采样到参考空间。
        4. 检查标签与参考图像的几何信息，若不一致同样重采样（使用最近邻插值）。
        这样可以最大程度地避免 nnUNet verify_dataset_integrity 阶段出现 Shape/Spacing/Origin/Direction mismatch 错误。
        """
        logger.info("开始几何一致性检查与修复 …")

        images_dir = self.dataset_path / "imagesTr"
        labels_dir = self.dataset_path / "labelsTr"

        image_paths = list(images_dir.glob("*.nii.gz"))
        if not image_paths:
            logger.warning("imagesTr 目录为空，跳过验证")
            return

        # 1. 依据文件名前缀分组病例
        case_dict = {}
        pattern = re.compile(r"(.*)_\d{4}\.nii\.gz$")
        for img_p in image_paths:
            m = pattern.match(img_p.name)
            if not m:
                logger.warning(f"无法解析文件名: {img_p.name}，跳过")
                continue
            case_name = m.group(1)
            case_dict.setdefault(case_name, []).append(img_p)

        for case_name, imgs in case_dict.items():
            try:
                # 2. 选取参考图像（优先 _0000）
                reference_img_path = None
                for p in imgs:
                    if p.name.endswith("_0000.nii.gz"):
                        reference_img_path = p
                        break
                if reference_img_path is None:
                    reference_img_path = imgs[0]
                reference_img = sitk.ReadImage(str(reference_img_path))

                # 3. 检查并修复每个通道
                for img_p in imgs:
                    if img_p == reference_img_path:
                        continue  # 参考图像跳过
                    current = sitk.ReadImage(str(img_p))
                    if (current.GetSize() != reference_img.GetSize() or
                        current.GetSpacing() != reference_img.GetSpacing() or
                        current.GetOrigin()  != reference_img.GetOrigin()  or
                        current.GetDirection() != reference_img.GetDirection()):
                        logger.info(f"[{case_name}] 图像 {img_p.name} 与参考不一致，执行重采样 …")
                        self._resample_image_to_reference(str(img_p), str(reference_img_path), str(img_p))

                # 4. 检查并修复标签
                label_path = labels_dir / f"{case_name}.nii.gz"
                if not label_path.exists():
                    logger.warning(f"[{case_name}] 未找到对应标签 {label_path.name}，跳过标签验证")
                    continue

                label_img = sitk.ReadImage(str(label_path))
                if (label_img.GetSize() != reference_img.GetSize() or
                    label_img.GetSpacing() != reference_img.GetSpacing() or
                    label_img.GetOrigin()  != reference_img.GetOrigin()  or
                    label_img.GetDirection() != reference_img.GetDirection()):
                    logger.info(f"[{case_name}] 标签与参考图像几何信息不一致，执行重采样 …")
                    self._resample_mask_to_reference(str(label_path), str(reference_img_path), str(label_path))

                logger.debug(f"[{case_name}] 几何一致性检查通过")
            except Exception as e:
                logger.error(f"[{case_name}] 几何一致性检查/修复失败: {e}")
                continue

        logger.info("几何一致性检查与修复完成 ✅")

    def run_preprocessing(self, verify_dataset_integrity: bool = False, num_processes: int = 8):
        """运行nnUNet预处理"""
        logger.info("开始nnUNet预处理...")

        try:
            # 构建命令 - 使用正确的参数格式
            cmd = [
                "nnUNetv2_plan_and_preprocess",
                "-d", str(self.dataset_id),
                "-c", "3d_fullres"  # 只预处理3d_fullres配置 2d, 3d_fullres, 3d_lowres
            ]

            # 移除严格的数据集完整性检查，因为它对空间属性检查过于严格
            if verify_dataset_integrity:
                cmd.append("--verify_dataset_integrity")
                logger.info("启用数据集完整性检查（可能对空间属性要求严格）")
            else:
                logger.info("跳过数据集完整性检查（推荐用于多序列配准数据）")

            # 只为3d_fullres配置设置进程数
            cmd.extend(["-np", str(num_processes)])

            # 获取配置信息用于显示
            config_info = "3d_fullres"  # 当前只处理3d_fullres配置

            logger.info(f"执行命令: {' '.join(cmd)}")
            logger.info(f"预处理配置: {config_info}")
            logger.info("预处理过程中将显示实时进度...")

            # 设置nnUNet环境变量
            env = os.environ.copy()
            env['nnUNet_raw'] = self.nnunet_raw
            env['nnUNet_preprocessed'] = self.nnunet_preprocessed
            env['nnUNet_results'] = self.nnunet_results

            # 执行命令并实时显示输出
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=env
            )

            # 实时读取并显示输出
            last_progress_line = ""
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_line = output.strip()
                    if output_line:
                        # 检查是否是进度条
                        if '%|' in output_line and '/it]' in output_line:
                            # 实时显示进度条，包含配置信息
                            print(f"\r[{config_info}预处理] {output_line}", end='', flush=True)
                            last_progress_line = output_line
                        # 显示其他重要信息（非进度条）
                        elif any(keyword in output_line.lower() for keyword in ['done', 'finished', 'completed', 'error', 'warning']):
                            # 如果之前有进度条，先换行
                            if last_progress_line:
                                print()  # 换行
                            print(f"[{config_info}预处理] {output_line}")
                            logger.info(f"预处理信息: {output_line}")

            # 确保最后的进度信息换行显示
            if last_progress_line:
                print()  # 换行
                logger.info(f"{config_info}预处理进度: {last_progress_line}")

            # 等待进程完成
            return_code = process.poll()
            if return_code != 0:
                raise subprocess.CalledProcessError(return_code, cmd)

            logger.info("预处理完成!")

        except subprocess.CalledProcessError as e:
            logger.error(f"预处理失败: {e}")
            raise
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_plan_and_preprocess命令，请确保已正确安装nnUNet")
            raise

    def check_existing_training(self, configuration: str = "3d_fullres", fold: int = 0, trainer_class: str = "nnUNetTrainer"):
        """检查是否存在已有的训练权重"""
        results_folder = self.nnunet_results / f"Dataset{self.dataset_id:03d}_{self.dataset_name}" / f"{trainer_class}__nnUNetPlans__{configuration}" / f"fold_{fold}"

        logger.info(f"检查训练权重路径: {results_folder}")

        checkpoint_final = results_folder / "checkpoint_final.pth"
        checkpoint_latest = results_folder / "checkpoint_latest.pth"
        checkpoint_best = results_folder / "checkpoint_best.pth"

        existing_checkpoints = []
        if checkpoint_final.exists():
            existing_checkpoints.append(("final", str(checkpoint_final)))
            logger.info(f"找到 final 检查点: {checkpoint_final}")
        if checkpoint_latest.exists():
            existing_checkpoints.append(("latest", str(checkpoint_latest)))
            logger.info(f"找到 latest 检查点: {checkpoint_latest}")
        if checkpoint_best.exists():
            existing_checkpoints.append(("best", str(checkpoint_best)))
            logger.info(f"找到 best 检查点: {checkpoint_best}")

        if not existing_checkpoints:
            logger.warning(f"在路径 {results_folder} 中未找到任何检查点文件")

        return existing_checkpoints, results_folder

    def train_model(self,
                    configuration: str = "3d_fullres",
                    fold: int = 0,
                    trainer_class: str = "nnUNetTrainer",
                    continue_training: bool = False,
                    checkpoint_name: str = "latest",
                    num_gpus: int = 1,
                    num_epochs: Optional[int] = None):
        """训练nnUNet模型

        参数:
            configuration: 训练配置 (3d_fullres, 2d, 3d_lowres)
            fold: 交叉验证折数
            trainer_class: 训练器类名
            continue_training: 是否继续训练
            checkpoint_name: 继续训练时使用的检查点 ("latest", "best", "final")
            num_gpus: GPU数量
            num_epochs: 训练轮数
        """
        logger.info("开始模型训练...")

        # 检查是否存在已有训练
        existing_checkpoints, results_folder = self.check_existing_training(configuration, fold, trainer_class)

        if continue_training:
            if not existing_checkpoints:
                logger.warning("未找到已有的训练权重，将开始新的训练")
                continue_training = False
            else:
                logger.info("找到以下已有的训练权重:")
                for name, path in existing_checkpoints:
                    logger.info(f"  - {name}: {path}")

                # 检查指定的检查点是否存在
                checkpoint_exists = any(name == checkpoint_name for name, _ in existing_checkpoints)
                if not checkpoint_exists:
                    logger.warning(f"指定的检查点 '{checkpoint_name}' 不存在，使用 'latest' 检查点")
                    checkpoint_name = "latest"
                    if not any(name == "latest" for name, _ in existing_checkpoints):
                        checkpoint_name = existing_checkpoints[0][0]  # 使用第一个可用的检查点

                logger.info(f"将从 '{checkpoint_name}' 检查点继续训练")
        else:
            if existing_checkpoints:
                logger.info("检测到已有训练权重:")
                for name, path in existing_checkpoints:
                    logger.info(f"  - {name}: {path}")
                logger.info("如需继续训练，请设置 continue_training=True")

        try:
            # 构建训练命令
            cmd = [
                "nnUNetv2_train",
                str(self.dataset_id),
                configuration,
                str(fold)
            ]

            # 添加可选参数
            if continue_training:
                cmd.append("--c")
                logger.info(f"继续训练模式: 从 {checkpoint_name} 检查点恢复")

            # 使用标准训练器（暂时不使用自定义trainer避免兼容性问题）
            if trainer_class != "nnUNetTrainer":
                cmd.extend(["-tr", trainer_class])

            # 注意：nnUNet默认训练1000轮，如需修改请使用自定义trainer
            if num_epochs is not None and num_epochs != 1000:
                logger.warning(f"注意：nnUNet默认训练1000轮，当前设置{num_epochs}轮将被忽略")
                logger.warning("如需自定义轮数，请手动创建自定义trainer或修改nnUNet源码")

            if num_gpus > 1:
                cmd.extend(["--num_gpus", str(num_gpus)])

            logger.info(f"执行训练命令: {' '.join(cmd)}")
            if num_epochs is not None:
                logger.info(f"训练轮数: {num_epochs}")
            if continue_training:
                logger.info(f"继续训练: 从 {checkpoint_name} 检查点开始")
            logger.info("训练过程中将显示详细参数...")

            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'  # 强制Python不缓冲输出
            env['TQDM_DISABLE'] = '0'      # 启用进度条

            # 设置nnUNet必需的环境变量
            env['nnUNet_raw'] = str(self.nnunet_raw)
            env['nnUNet_preprocessed'] = str(self.nnunet_preprocessed)
            env['nnUNet_results'] = str(self.nnunet_results)

            # 启动训练进程 - 不重定向输出，让其直接显示到终端
            process = subprocess.Popen(
                cmd,
                env=env,
                bufsize=0,  # 无缓冲
                universal_newlines=True
            )

            if continue_training:
                print(f" [继续训练] 从 {checkpoint_name} 检查点恢复训练...")
            else:
                print(" [新训练] 训练已开始，训练参数将实时显示...")
            print("💡 [提示] 如果看不到训练参数，请等待模型编译完成（首次运行需要几分钟）")

            # 等待训练完成
            return_code = process.wait()
            if return_code != 0:
                raise subprocess.CalledProcessError(return_code, cmd)

            logger.info("模型训练完成!")

        except subprocess.CalledProcessError as e:
            logger.error(f"训练失败: {e}")
            raise
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_train命令，请确保已正确安装nnUNet")
            raise

    def find_best_configuration(self):
        """寻找最佳配置"""
        logger.info("寻找最佳配置...")

        try:
            cmd = ["nnUNetv2_find_best_configuration", str(self.dataset_id)]
            logger.info(f"执行命令: {' '.join(cmd)}")

            # 设置nnUNet环境变量
            env = os.environ.copy()
            env['nnUNet_raw'] = self.nnunet_raw
            env['nnUNet_preprocessed'] = self.nnunet_preprocessed
            env['nnUNet_results'] = self.nnunet_results

            result = subprocess.run(cmd, capture_output=True, text=True, check=True, env=env)
            logger.info("最佳配置搜索完成!")
            logger.info(f"结果: {result.stdout}")

        except subprocess.CalledProcessError as e:
            logger.error(f"寻找最佳配置失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_find_best_configuration命令")

    def run_full_pipeline(self,
                          images_folders: Dict[str, str],
                          masks_folder: str,
                          test_images_folders: Optional[Dict[str, str]] = None,
                          configuration: str = "3d_fullres",
                          num_epochs: int = 100,
                          channel_names: Dict[str, str] = None,
                          labels: Dict[str, int] = None):
        """运行完整的多序列预处理和训练流程"""
        logger.info("开始完整的多序列nnUNet流程...")

        try:
            # 1. 准备数据
            self.prepare_data_from_folders(
                images_folders=images_folders,
                masks_folder=masks_folder,
                test_images_folders=test_images_folders,
                channel_names=channel_names,
                labels=labels
            )

            # 2. 验证和修复数据
            self.validate_and_fix_data()

            # 3. 运行预处理
            self.run_preprocessing()

            # 4. 训练模型
            self.train_model(
                configuration=configuration,
                num_epochs=num_epochs
            )

            # 5. 寻找最佳配置
            self.find_best_configuration()

            logger.info("完整流程执行完成!")

        except Exception as e:
            logger.error(f"流程执行失败: {e}")
            raise


def main():
    """主函数 - 演示nnUNet预处理和训练流程"""

    print("=" * 60)
    print("🚀 nnUNet 多序列训练脚本")
    print("=" * 60)
    print("💡 快速使用提示:")
    print("   - 如果已有预处理数据，直接运行即可开始训练")
    print("   - 如果需要重新预处理，请修改下面的控制选项")
    print("=" * 60)

    # 配置参数 - 请根据您的数据修改这些路径
    config = {
        # 多序列数据路径配置
        "images_folders": {
            "ap": "/root/autodl-tmp/120HCC/image/ap",    # 动脉期
            "pp": "/root/autodl-tmp/120HCC/image/pp",    # 门静脉期
            "hbp": "/root/autodl-tmp/120HCC/image/hbp"   # 肝胆期
        },
        "masks_folder": "/root/autodl-tmp/120HCC/mask/ap",  # 标签文件夹（通常在一个序列文件夹中）
        "test_images_folders": None,  # 如果有单独的测试集，请指定多序列路径字典

        # nnUNet配置
        "base_dir": "./nnUNet_workspace",
        "dataset_id": 120,
        "dataset_name": "HCC_MultiSequence",

        # 训练配置
        "configuration": "3d_fullres",  # 可选: "2d", "3d_fullres", "3d_lowres"
        "num_epochs": 500,  # 训练轮数 (可修改: 10, 50, 100, 250, 500, 1000等)
        "num_gpus": 1,

        # 继续训练配置
        # "continue_training": False,     # 是否从已有权重继续训练
        # "checkpoint_name": "latest",    # 继续训练时使用的检查点: "latest", "best", "final"

        # 数据集配置 - 3个序列作为3个通道
        "channel_names": {"0": "ap", "1": "pp", "2": "hbp"},  # 三个序列
        "labels": {"background": 0, "HCC": 1},
        "train_ratio": 1.0,

        # 控制选项 - 根据需要修改这些设置
        # "skip_data_preparation": False,   # 如果已有nnUNet格式数据，设为True跳过数据准备
        # "run_preprocessing": True,      # 是否运行nnUNet预处理
        # "run_training": True,           # 是否运行训练
        # "run_evaluation": False,        # 是否运行评估

        # 完整流程模式配置（从头开始）:
        # "skip_data_preparation": False,  # 重新准备数据
        # "run_preprocessing": True,       # 运行预处理
        # "run_training": True,           # 运行训练
        # "run_evaluation": True          # 运行评估

        # 继续训练模式配置（从已有权重继续）:
        "skip_data_preparation": True,   # 跳过数据准备
        "run_preprocessing": False,      # 跳过预处理
        "run_training": True,           # 运行训练
        "continue_training": True,       # 启用继续训练
        "checkpoint_name": "latest",     # 使用最新检查点
    }

    try:
        # 检查CUDA可用性
        import torch
        if torch.cuda.is_available():
            logger.info(f"CUDA可用，GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            logger.warning("CUDA不可用，将使用CPU训练（速度较慢）")

        # 创建nnUNet训练实例
        trainer = nnUNetPreprocessingTraining(
            base_dir=config["base_dir"],
            dataset_id=config["dataset_id"],
            dataset_name=config["dataset_name"]
        )

        # 分步执行流程
        print("\n🔄 执行流程:")

        if config["run_preprocessing"]:
            if not config["skip_data_preparation"]:
                print("📁 步骤1: 多序列数据准备")
                logger.info("步骤1: 多序列数据准备和预处理")
                # 1. 准备数据
                trainer.prepare_data_from_folders(
                    images_folders=config["images_folders"],
                    masks_folder=config["masks_folder"],
                    test_images_folders=config["test_images_folders"],
                    channel_names=config["channel_names"],
                    labels=config["labels"]
                )

                # 2. 验证和修复数据
                trainer.validate_and_fix_data()
            else:
                print("⏭️  跳过数据准备步骤 - 使用现有数据")
                logger.info("跳过数据准备步骤 - 使用现有数据")

            # 3. 运行预处理
            print("⚙️  步骤2: nnUNet预处理")
            logger.info("步骤2: nnUNet预处理")
            trainer.run_preprocessing()
            logger.info("预处理完成!")
        else:
            print("⏭️  跳过预处理步骤 - 使用现有预处理数据")

        if config["run_training"]:
            if config["continue_training"]:
                print(f"🔄 步骤3: 继续训练 (从 {config['checkpoint_name']} 检查点, epochs: {config['num_epochs']})")
                logger.info("步骤3: 继续模型训练")
            else:
                print(f"🎯 步骤3: 新模型训练 (epochs: {config['num_epochs']})")
                logger.info("步骤3: 新模型训练")

            # 4. 训练模型
            trainer.train_model(
                configuration=config["configuration"],
                continue_training=config["continue_training"],
                checkpoint_name=config["checkpoint_name"],
                num_epochs=config["num_epochs"]
            )
            logger.info("训练完成!")
        else:
            print("⏭️  跳过训练步骤")

        # if config["run_evaluation"]:
        #     logger.info("步骤3: 寻找最佳配置")
        #     # 5. 寻找最佳配置
        #     trainer.find_best_configuration()
        #     logger.info("评估完成!")

        logger.info("=" * 50)
        logger.info("nnUNet预处理和训练完成!")
        logger.info("=" * 50)
        logger.info(f"模型保存位置: {trainer.nnunet_results}")
        logger.info(f"数据集ID: {config['dataset_id']}")
        logger.info("您现在可以使用训练好的模型进行预测")

    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

    return 0


if __name__ == "__main__":
    """
    多序列nnUNet训练脚本使用说明:

    === 🚀 快速训练（推荐 - 已有预处理数据）===
    1. 确保 nnUNet_workspace 目录存在且包含预处理数据
    2. 默认配置已设置为跳过数据准备和预处理
    3. 直接运行: python nnunet模型训练python代码最终版-多模态.py
    4. 脚本会自动检测并使用现有的预处理数据开始训练

    === 📁 完整流程（从原始数据开始）===
    1. 修改main()函数中的config字典，设置您的多序列数据路径
    2. 设置控制选项:
       - skip_data_preparation=False  # 重新准备数据
       - run_preprocessing=True       # 运行预处理
       - run_training=True           # 运行训练
    3. 运行脚本: python nnunet模型训练python代码最终版-多模态.py

    === ⚙️ 仅预处理（准备数据）===
    1. 设置 skip_data_preparation=False, run_preprocessing=True, run_training=False
    2. 运行脚本仅进行数据预处理

    === 🔄 继续训练（从已有权重恢复）===
    1. 设置继续训练选项:
       - continue_training=True          # 启用继续训练
       - checkpoint_name="latest"        # 选择检查点: "latest", "best", "final"
    2. 脚本会自动检测已有的训练权重并从指定检查点继续
    3. 支持的检查点类型:
       - "latest": 最新的训练检查点（推荐，包含完整训练状态）
       - "best": 验证集上表现最好的检查点
       - "final": 训练完成时的最终检查点

    === 🎯 修改训练轮数 ===
    方法1: 直接修改config中的"num_epochs"参数 (推荐)
    - 在main()函数中修改 "num_epochs": 100  # 改为您想要的轮数

    方法2: 使用自定义trainer (高级用户)
    - 运行: python create_custom_trainer.py 100  # 创建100轮的trainer
    - 然后在训练时指定: trainer_class="nnUNetTrainer_100epochs"

    === 📋 控制选项说明 ===
    - skip_data_preparation: True=跳过数据准备, False=重新准备数据
    - run_preprocessing: True=运行预处理, False=跳过预处理
    - run_training: True=运行训练, False=跳过训练
    - run_evaluation: True=运行评估, False=跳过评估
    - continue_training: True=继续训练, False=新训练
    - checkpoint_name: 继续训练时使用的检查点类型

    环境要求:
    - pip install nnunetv2
    - pip install SimpleITK

    多序列数据格式要求:
    - 所有序列的图像和标签都应该是.nii.gz格式
    - 每个病例在所有序列文件夹中都应该有对应的文件
    - 文件命名格式: 病例名-序列名.nii.gz (如: patient001-ap.nii.gz)
    - 标签文件命名格式: 病例名-mask.nii.gz 或 病例名_mask.nii.gz
    - 所有序列的图像应该是配准后的3D医学图像格式
    - 支持的序列: ap(动脉期), pp(门静脉期), hbp(肝胆期)
    """

    # 设置环境变量
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['OPENBLAS_NUM_THREADS'] = '1'

    exit_code = main()
    sys.exit(exit_code)
