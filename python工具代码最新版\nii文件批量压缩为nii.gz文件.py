import os, gzip, shutil, sys, time
from concurrent.futures import ProcessPoolExecutor, as_completed
from pathlib import Path

def compress_one(path_in):
    try:
        path_in = Path(path_in)
        path_out = path_in.with_suffix(path_in.suffix + ".gz")
        
        # 如果压缩文件已存在，跳过
        if path_out.exists():
            return f"⚠️ {path_in.name} 已存在压缩版本，跳过"
        
        # 记录原始文件大小
        original_size = path_in.stat().st_size
        
        # 压缩
        with open(path_in, "rb") as f_in, gzip.open(path_out, "wb") as f_out:
            shutil.copyfileobj(f_in, f_out)
        
        # 验证压缩文件完整性
        if not path_out.exists():
            return f"❌ {path_in.name} 压缩失败：压缩文件未生成"
        
        compressed_size = path_out.stat().st_size
        if compressed_size == 0:
            path_out.unlink()  # 删除空文件
            return f"❌ {path_in.name} 压缩失败：压缩文件为空"
        
        # 测试解压缩（验证完整性）
        try:
            with gzip.open(path_out, "rb") as test_file:
                test_file.read(1024)  # 读取前1KB测试
        except Exception as e:
            path_out.unlink()  # 删除损坏的压缩文件
            return f"❌ {path_in.name} 压缩失败：文件损坏 {e}"
        
        # 验证成功后才删除原文件
        path_in.unlink()
        
        # 计算压缩比
        compression_ratio = (1 - compressed_size / original_size) * 100
        return f"✅ {path_in.name} (压缩率: {compression_ratio:.1f}%)"
        
    except Exception as e:
        # 清理可能的残留文件
        if 'path_out' in locals() and path_out.exists():
            try:
                path_out.unlink()
            except:
                pass
        return f"❌ {path_in.name} 失败: {e}"

def compress_nii_dir(folder, max_workers=None):
    folder = Path(folder)
    
    if not folder.exists():
        print(f"❗ 目录不存在: {folder}")
        return
    
    nii_files = [p for p in folder.iterdir() if p.suffix == ".nii" and p.is_file()]
    
    if not nii_files:
        print("❗ 该目录下没有 .nii 文件")
        return
    
    print(f"共找到 {len(nii_files)} 个 .nii 文件，开始压缩...")
    start_time = time.time()
    
    # 限制并发数，避免过度占用系统资源
    if max_workers is None:
        max_workers = min(os.cpu_count(), 4)
    
    success_count = 0
    skip_count = 0
    error_count = 0
    
    with ProcessPoolExecutor(max_workers=max_workers) as pool:
        futures = {pool.submit(compress_one, str(p)): p for p in nii_files}
        
        for fut in as_completed(futures):
            result = fut.result()
            print(result)
            
            if result.startswith("✅"):
                success_count += 1
            elif result.startswith("⚠️"):
                skip_count += 1
            else:
                error_count += 1
    
    # 最终统计
    elapsed_time = time.time() - start_time
    print(f"\n📊 压缩完成统计:")
    print(f"   成功: {success_count}")
    print(f"   跳过: {skip_count}")
    print(f"   失败: {error_count}")
    print(f"   耗时: {elapsed_time:.1f}秒")
    
    # 检查残留文件
    remains = list(folder.glob("*.nii"))
    if remains:
        print(f"\n⚠️ 以下 {len(remains)} 个文件未被压缩:")
        for f in remains:
            print(f"   {f.name}")
    else:
        print("\n🎉 目录内所有 .nii 文件已成功压缩")

if __name__ == "__main__":
    # 使用 Path 对象，更好的跨平台兼容性
    path = r"K:\test\LITS-Challenge-Test-Data"
    compress_nii_dir(path, max_workers=4)