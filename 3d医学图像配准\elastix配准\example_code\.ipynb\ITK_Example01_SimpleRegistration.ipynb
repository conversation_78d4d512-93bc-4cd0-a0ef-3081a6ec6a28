{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 1. Simple Elastix"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Registration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "from itkwidgets import compare, checkerboard\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Image registration** finds the *spatial transformation that aligns images in the presence of noise*."]}, {"cell_type": "markdown", "metadata": {}, "source": ["In image registration, we typically identify the two images as the fixed and moving image. Our goal is to find the spatial transformation that makes the moving image align with the fixed image.\n", "\n", "First, let's load our **fixed image** and the image we will align to our fixed image, the **moving image**."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Load images with itk floats (itk.F). Necessary for elastix\n", "fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For now we will use a default parametermap of elastix, for more info about parametermaps, see [example2](ITK_Example02_CustomOrMultipleParameterMaps.ipynb#section_id2)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["parameter_object = itk.ParameterObject.New()\n", "default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(default_rigid_parameter_map)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Registration can either be done in one line with the registration function..."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object,\n", "    log_to_console=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an elastix image filter object. The result of these to methods should only differ due to the stochastic nature of elastix. \n", "\n", "The object oriented method below can be used when more explicit function calls are preferred."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Load Elastix Image Filter Object\n", "elastix_object = itk.ElastixRegistrationMethod.New(fixed_image, moving_image)\n", "# elastix_object.SetFixedImage(fixed_image)\n", "# elastix_object.SetMovingImage(moving_image)\n", "elastix_object.SetParameterObject(parameter_object)\n", "\n", "# Set additional options\n", "elastix_object.SetLogToConsole(False)\n", "\n", "# Update filter object (required)\n", "elastix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Registration\n", "result_image = elastix_object.GetOutput()\n", "result_transform_parameters = elastix_object.GetTransformParameterObject()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Save image with itk\n", "itk.imwrite(result_image,'exampleoutput/result_image.mha')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The output of the elastix algorithm is the registered (transformed) version of the moving image. The parameters of this transformation can also be obtained after registation."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}