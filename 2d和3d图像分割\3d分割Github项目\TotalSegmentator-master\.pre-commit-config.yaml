repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: "v4.4.0"
  hooks:
  - id: check-added-large-files
    args: ['--maxkb=4096']
  - id: check-ast
  - id: check-case-conflict
  - id: check-merge-conflict
  - id: check-symlinks

- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: v0.1.7
  hooks:
    # Run the linter.
    - id: ruff
      args: [ --fix ]

- repo: https://github.com/asottile/pyupgrade
  rev: v3.15.0
  hooks:
  -   id: pyupgrade
      args: [--py39-plus]

- repo: https://github.com/codespell-project/codespell
  rev: v2.2.6
  hooks:
    - id: codespell
      args: [
        "--write-changes"
        ]
