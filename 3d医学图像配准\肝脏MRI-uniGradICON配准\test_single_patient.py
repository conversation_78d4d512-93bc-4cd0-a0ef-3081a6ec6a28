#!/usr/bin/env python3
"""
单个患者配准测试脚本
用于测试HCC数据集的配准功能
"""

import sys
import logging
from pathlib import Path
from liver_hcc_registration import LiverHCCRegistration

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_patient():
    """测试单个患者的配准"""
    
    # 数据路径
    data_root = "/root/autodl-tmp/120HCC/image"
    output_dir = "/root/autodl-tmp/3d MRI配准/test_results"
    
    # 创建配准器
    registrator = LiverHCCRegistration(data_root, output_dir)
    
    # 获取患者列表
    patients = registrator.get_patient_list()
    
    if not patients:
        print("❌ 未找到患者数据")
        return False
    
    print(f"📋 找到 {len(patients)} 个患者")
    print(f"📋 前10个患者: {patients[:10]}")
    
    # 选择第一个患者进行测试
    test_patient = patients[0]
    print(f"\n🧪 测试患者: {test_patient}")
    
    # 检查患者的序列文件
    sequences = registrator.get_patient_sequences(test_patient)
    print(f"📁 患者序列: {list(sequences.keys())}")
    
    for seq_name, seq_path in sequences.items():
        file_exists = Path(seq_path).exists()
        print(f"   {seq_name}: {'✅' if file_exists else '❌'} {seq_path}")
    
    if len(sequences) < 2:
        print(f"❌ 患者 {test_patient} 序列数量不足: {len(sequences)}")
        return False
    
    # 验证图像文件
    print(f"\n🔍 验证图像文件...")
    for seq_name, seq_path in sequences.items():
        is_valid = registrator.validate_image(seq_path)
        print(f"   {seq_name}: {'✅' if is_valid else '❌'}")
        if not is_valid:
            print(f"❌ 图像验证失败: {seq_name}")
            return False
    
    # 执行配准
    print(f"\n🚀 开始配准患者: {test_patient}")
    print(f"📌 参考序列: ap (动脉期)")
    
    try:
        result = registrator.register_patient(test_patient, reference_sequence='ap')
        
        print(f"\n📊 配准结果:")
        print(f"   状态: {result['status']}")
        print(f"   序列: {result['sequences']}")
        print(f"   参考序列: {result['reference_sequence']}")
        
        if 'success_rate' in result:
            print(f"   成功率: {result['success_rate']:.1%}")
        
        if 'processing_time' in result:
            print(f"   处理时间: {result['processing_time']:.1f} 秒")
        
        # 显示详细配准结果
        if 'registrations' in result:
            print(f"\n📋 详细配准结果:")
            for reg in result['registrations']:
                status_icon = "✅" if reg['status'] == 'success' else "❌"
                print(f"   {status_icon} {reg['moving_sequence']} -> {reg['fixed_sequence']}")
                if reg['status'] == 'success':
                    print(f"      变换文件: {reg['transform']}")
                    print(f"      配准图像: {reg['warped_image']}")
                else:
                    print(f"      错误: {reg.get('error', '未知错误')}")
        
        if result['status'] in ['success', 'partial']:
            print(f"\n✅ 患者 {test_patient} 配准成功!")
            return True
        else:
            print(f"\n❌ 患者 {test_patient} 配准失败: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"\n❌ 配准过程中出现异常: {e}")
        return False

def test_installation():
    """测试uniGradICON安装"""
    print("🔧 检查uniGradICON安装...")
    
    registrator = LiverHCCRegistration()
    
    if registrator.check_unigradicon_installation():
        print("✅ uniGradICON已安装")
        return True
    else:
        print("❌ uniGradICON未安装")
        print("🔧 正在安装uniGradICON...")
        try:
            registrator.install_unigradicon()
            print("✅ uniGradICON安装成功")
            return True
        except Exception as e:
            print(f"❌ uniGradICON安装失败: {e}")
            return False

def main():
    """主函数"""
    print("🧪 HCC肝脏多序列MRI配准测试")
    print("=" * 50)
    
    # 测试安装
    if not test_installation():
        print("❌ 安装测试失败，退出")
        sys.exit(1)
    
    print()
    
    # 测试单个患者配准
    if test_single_patient():
        print("\n🎉 测试成功! 可以开始批量处理")
        print("\n📝 使用说明:")
        print("1. 单个患者配准:")
        print("   python liver_hcc_registration.py --patient_name baizhengqiang")
        print()
        print("2. 批量配准前5个患者:")
        print("   python liver_hcc_registration.py --max_patients 5")
        print()
        print("3. 批量配准所有患者:")
        print("   python batch_hcc_registration.py")
        print()
        print("4. 使用不同参考序列:")
        print("   python liver_hcc_registration.py --reference_sequence pp")
    else:
        print("\n❌ 测试失败，请检查数据和配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
