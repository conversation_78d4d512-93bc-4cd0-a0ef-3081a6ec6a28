{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["\n", "<h2 style=\"color:red\"> This file is deprecated. Please look at ActivityNet-Release1.3.Proposals.ipynb notebook</h2>\n", "\n", "\n", "# ActivityNet Challenge Agnostic Temporal Proposals\n", "This notebook is intended as demo on how to use the temporal proposals provided for the ActivityNet Challenge CVPR 2016. Additionally, recall performance is reported."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import json\n", "\n", "import h5py\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load annotations for validation subset in a Pandas Data Frame."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["# Modify this paths\n", "ANNOTATION_FILE = 'activity_net.v1-3.min.json'\n", "PROPOSALS_FILENAME = 'activitynet_v1-3_proposals.hdf5'\n", "SUBSET = 'validation'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Read json file containing the annotations\n", "with open(ANNOTATION_FILE, 'r') as fobj:\n", "    data = json.load(fobj)['database']\n", "\n", "# Parsing and looking for agnostic segments of an intended subset.\n", "video_id_fmt = 'v_{}'\n", "gt_s_init, gt_s_end, video_id = [], [], []\n", "for vid, vitem in data.iteritems():\n", "    if vitem['subset'] != SUBSET:\n", "        continue\n", "    for ann in vitem['annotations']:\n", "        gt_s_init.append(ann['segment'][0])\n", "        gt_s_end.append(ann['segment'][1])\n", "        video_id.append(video_id_fmt.format(vid))\n", "# Creates ground truth data frame.\n", "ground_truth_df = pd.DataFrame({'s-init': gt_s_init, \n", "                                's-end': gt_s_end,\n", "                                'video-id': video_id})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load temporal proposals in a Pandas Data Frame."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average number of proposals: 89.6140885099\n"]}], "source": ["# Looking for videos in the subset\n", "intended_videos = []\n", "for vid, vitem in data.iteritems():\n", "    if vitem['subset'] == SUBSET:\n", "        intended_videos.append('v_{}'.format(vid))\n", "\n", "# Reading proposals from HDF5 file.\n", "s_init, s_end, score, video_id = [], [], [], []\n", "fobj = h5py.File(PROPOSALS_FILENAME, 'r')\n", "for vid in fobj.keys():\n", "    if vid not in intended_videos:\n", "        continue\n", "    s_init.extend(fobj[vid]['segment-init'].value.tolist())\n", "    s_end.extend(fobj[vid]['segment-end'].value.tolist())\n", "    score.extend(fobj[vid]['score'].value.tolist())\n", "    video_id.extend(np.repeat(vid, fobj[vid]['segment-init'].value.size).tolist())\n", "fobj.close()\n", "proposals_df = pd.DataFrame({'s-init': s_init, \n", "                             's-end': s_end, \n", "                             'score': score,\n", "                             'video-id': video_id})\n", "print 'Average number of proposals: {}'.format(proposals_df.shape[0] / float(len(intended_videos)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Subrutines to compute temporal IoU and wrapper for recall"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["def segment_iou(target_segments, test_segments):\n", "    \"\"\"Compute intersection over union btw segments\n", "    Parameters\n", "    ----------\n", "    target_segments : ndarray\n", "        2-dim array in format [m x 2:=[init, end]]\n", "    test_segments : ndarray\n", "        2-dim array in format [n x 2:=[init, end]]\n", "    Outputs\n", "    -------\n", "    iou : n<PERSON><PERSON>\n", "        2-dim array [m x n] with IOU ratio.\n", "    Note: It assumes that target-segments are more scarce that test-segments\n", "    \"\"\"\n", "    if target_segments.ndim != 2 or test_segments.ndim != 2:\n", "        raise ValueError('Dimension of arguments is incorrect')\n", "\n", "    m, n = target_segments.shape[0], test_segments.shape[0]\n", "    iou = np.empty((m, n))\n", "    for i in xrange(m):\n", "        tt1 = np.maximum(target_segments[i, 0], test_segments[:, 0])\n", "        tt2 = np.minimum(target_segments[i, 1], test_segments[:, 1])\n", "\n", "        # Non-negative overlap score\n", "        intersection = (tt2 - tt1).clip(0)\n", "        union = ((test_segments[:, 1] - test_segments[:, 0]) +\n", "                 (target_segments[i, 1] - target_segments[i, 0]) -\n", "                 intersection)\n", "        # Compute overlap as the ratio of the intersection\n", "        # over union of two segments at the frame level.\n", "        iou[i, :] = intersection / union\n", "    return iou\n", "\n", "def recall_vs_iou_thresholds(proposal_df, df, iou_threshold=np.array([0.5])):\n", "    vds = proposal_df['video-id'].unique()\n", "    score_lst = []\n", "    # Compute iou score\n", "    for i, v in enumerate(vds):\n", "        # Proposals\n", "        idx = proposal_df['video-id'] == v\n", "        this_df = proposal_df.loc[idx]\n", "        proposals = np.stack((this_df['s-init'], \n", "                              this_df['s-end']), axis=-1)\n", "\n", "        # Sort proposals\n", "        idx = this_df['score'].argsort()[::-1]\n", "        proposals = proposals[idx, :]\n", "\n", "        # Annotations\n", "        jdx = df['video-id'] == v\n", "        ann_df = df.loc[jdx]\n", "        annotations = np.stack((ann_df['s-init'],\n", "                                ann_df['s-end']), axis=-1)\n", "        if proposals.ndim == 1:\n", "            proposals = proposals[np.newaxis, :]\n", "        score_lst.append(segment_iou(annotations, proposals))\n", "        if not (i+1)%500:\n", "            print 'Scored videos: {}'.format(i+1)\n", "    matches = np.zeros((vds.shape[0], iou_threshold.shape[0]))\n", "    pos = np.zeros(vds.shape[0])\n", "    # Matching\n", "    recall = np.empty(iou_threshold.shape[0])\n", "    for cidx, this_iou in enumerate(iou_threshold):\n", "        # Score analysis per video.\n", "        for i, sc in enumerate(score_lst):\n", "            pos[i] = sc.shape[0] # Positives per video.\n", "            lmt = int(sc.shape[1])\n", "            matches[i, cidx] = ((sc[:, :lmt] >= this_iou).sum(axis=1) > 0).sum()\n", "        this_recall = matches[:, cidx].sum() / pos.sum()\n", "        recall[cidx] = this_recall\n", "    return recall"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["Lets compute the Recall at different Temporal IoU thresholds"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scored videos: 500\n", "Scored videos: 1000\n", "Scored videos: 1500\n", "Scored videos: 2000\n", "Scored videos: 2500\n", "Scored videos: 3000\n", "Scored videos: 3500\n", "Scored videos: 4000\n", "Scored videos: 4500\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/lib/python2.7/site-packages/ipykernel/__main__.py:47: DeprecationWarning: using a non-integer number instead of an integer will result in an error in the future\n"]}], "source": ["iou_thrs = np.arange(0.1, 0.6, 0.1)\n", "recall = recall_vs_iou_thresholds(proposals_df, ground_truth_df, iou_threshold=iou_thrs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Recall vs Temporal IoU curve"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnkAAAFRCAYAAAAIHrtnAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAIABJREFUeJzs3Xl0Ved97//395yjWUcDSEhilMxoYzMZOxiMje3YiZ3B\nadLGGV2nTlp3bn+/pr33pvfmd7vamw63bXrb1aZNm7ZxRqfJjdPMHoIHhhgwYPAAGJCZhECg4WjW\nOef5/SERSzqPQEI68+e1lhacvZ999NVnbeBhP9+ztznnEBEREZHcEkh3ASIiIiIy8zTJExEREclB\nmuSJiIiI5CBN8kRERERykCZ5IiIiIjlIkzwRERGRHKRJnoiIiEgOSvkkz8w2m9njZnbKzOJm9uAk\njrnezLaaWa+ZnTSz/56KWkVERESyVTqu5JUDB4DfAnqvNNjMwsATQAtwI/DbwCfN7HeTWaSIiIhI\nNrN0PvHCzCLArzvnvniZMb8KfAaY45wbHNn2KeAR59yC1FQqIiIikl2yoSdvA/DcpQneiB8Bc81s\nUZpqEhEREclo2TDJqwdax21rBWxkn4iIiIiMkw2TPBERERGZolC6C5iEs0DduG11gBvZN4aZpa/J\nUERERGSKnHOWjPfNhit5O4DNZlY4ats9wBnn3Bu+A5xz+hr39elPfzrtNWTil3JRLspEuSgX5ZLO\nr2RKx33yysxstZmtGfn+C0deLxjZ/xkze3LUIV9h+FYr/2ZmK83svcAfAH+Z6tqzWXNzc7pLyEjK\nxU+5JFImfsrFT7n4KZfUSseVvPXAXmAPUAz8T+DFkV9h+MMUTZcGO+e6gLuBucAu4G+Bv3DOfTaF\nNYuIiIhklZT35DnnnuEyk0vn3Mc8214Gtkz2ezxx8h8oCVVQGqpM/DUYJhgouKras9lDDz2U7hIy\nknLxUy6JlImfcvFTLn7KJbXSejPkZDAz953jf37ZMUWBUkpClaMmfxUjrysoCVUQChRe9ngRERGR\nmWBmuCR98CIbPl074wbivQwM9tIx2OLdXxgoGTPpGz8ZLAgUpbji6du6dStbtmxJdxkZR7n4KZdE\nysRPufgpFz/lklp5Ocm7ksF4H4ODfXQOJtyhBYCCQPGbk77g+Mng8CTQLCmTchEREZFJycnl2u7B\ni/RGO+mLdtEb7aIv2klvtIveaCf9sW6Gb7GXPCEr9F4BvPS6MFCiSaCIiIgkdbk2Jyd5l/uZ4i5G\nfzQyMvnrojd2aTI4/GtfNIIjntQag1bg6QWspDQ4fEWwKFimSaCIiEge0CRvCq40ybsS5+L0x7rH\nXAEc/WtfNEKc2AxWnChAcIJPBw9PBouD5VOeBKoPwk+5+CmXRMrET7n4KRc/5ZJIH7xIIbMAJSM9\ndjA/Yb9zjoFYz6jl4FG/xoaXh+MuOq0a4sToibbTE23310jA+6ngS5PB4mCYgGXDw0xEREQkWXQl\nb4Y55xiM905wJXB4MhhzQ0mtwTCKg+EJbhFTSUkoTMCCSa1BRERErkzLtVOQ7knelTjnGIr3e64E\nXpoMdhJ1g0mvozgY/tkEcPyycEmwgmBAF3lFRESSTZO8Kcj0Sd5kDMX66Y1NfCVwKN4/5fc8sPMo\nN2xYPOnxRcGycR8IGTsRDOXIU0PUH+KnXBIpEz/l4qdc/JRLIvXk5ZmCYDGVwWIqC+d490fjg5e5\nEtjFYLx32jUMxHoYiPXQPjDRDaNLPTeLfvNXPTVEREQkvXQlLwdF40Mjt4NJvEVMb7STgVhP0mt4\n84bRb078SkeWgktDlRQEi5Neg4iISKbTcu0UaJJ3ZbF4lL5YxHuLmN5oV8puGJ3QCzjq97phtIiI\n5ANN8qZAkzy/qfRBjL5htO8WMf3RLlySJ4FBKxj36eCxVwULA6UzMglUf4ifckmkTPyUi59y8VMu\nidSTJykVsCClBVWUFlR598dHbhg99ibRY/sDp/vUkJgbIjLURmSobYIaQyNLv4kTwNJQpZ4aIiIi\neU9X8mTGOedGJoFd/g+IxKZ/w+grGX5qSHjccnDlqNvElGO6YbSIiKSZlmunQJO8zPfmDaMTJ4CX\nfp/8G0YHKA6FqSioYUH59dSVLtFTQkREJOU0yZsCTfL8sqkPYngS2DdmAjj+quBM3TD60v0Di4Nh\nGsNrWBheRVGwdEbeO5tl0/mSKsrET7n4KRc/5ZJIPXmSV8yMomApRcFSqorqvWOGYv1jPgwy/qrg\nVG8Y3R+L8FrHcxzu2M7cshU0Vayb8HuLiIhkA13Jk5zkv2H0m8vCk7lhdFVhA00Va2koW07Q9P8h\nERGZeVqunQJN8mQyovFBIkMXOBF5iVM9r1z2gyCFgVIWhVexKLyGklA4hVWKiEiuS+YkT53meWLr\n1q3pLiGjhAKFVBc10H6wiLvnP8J11VsoDVV6xw7GeznSuZOnTv0ju889TlvfCXL9PxI6XxIpEz/l\n4qdc/JRLamkNSvJeYbCExZU3cU3FjZzrO87xyF7O9x1PGOdwtPQepqX3MOGCGhor1jK/7Do9p1dE\nRDKSlmtFPLqH2mmO7OVk5MBlP8kbsiIWhK+nMbyW8oLqFFYoIiK5QD15U6BJnsykaHyQUz2v0Ny1\nd8Knb1xSW9JEU3gtc0qu0dM2RERkUtSTJ9OmPgi/K+USChTSGF7D7XMf4pa6B2goXYbh/7N4vu84\nL5z7Fk+f/jxHO3cxGJvabVwyic6XRMrET7n4KRc/5ZJa6skTmQQzo6ZkITUlC+mLdvFGZD9vRF7y\n3oqlN9rJK+1bea3jeeaXXUdjxVoqC+ekoWoREclnWq4VuUoxF6Wl5xDHu/bSMdhy2bGziubTVLGW\n+tKlBCyYogpFRCTTqSdvCjTJk3ToGGjheNdezvS8RpzYhOOKgmXDj08rX0VxqDyFFYqISCZST55M\nm/og/GYql6qiBtbW3sdbFzzCiqrNFAf9N00eiPVwqGMbT576R148/10u9p/OyHvu6XxJpEz8lIuf\ncvFTLqmlnjyRGVQULGVp1QYWV95Ma+/rNEf20tZ/ImGcI87pnlc53fMqlYV1NIbXMq9sBcFAQRqq\nFhGRXKTlWpEkiwy2Dd9zr/tlYm5ownEFgWIWlq+iMbyG0gL/0zdERCS3qCdvCjTJk0w1FB/gVPfL\nHO96kZ5o+2XH1pUsoaliLTXFi3TPPRGRHKaePJk29UH4pTKXgkARTRXruGPew7yl7uepK1k84djW\nvtfZ2foNfnL6CxzvepGh+EDK6gSdLz7KxE+5+CkXP+WSWurJE0kxM2NOSRNzSproHeqgObKPE90H\nGIon3jy5J3qRgxef4tX2Z1lQvpLG8FrChTVpqFpERLKNlmtFMkA0PsSZntc4HnmRrsFzlx1bU7yQ\nxvA66koXEzBdjBcRyWbqyZsCTfIkmznnaB84Q3NkL2d6DuGITzi2JFjBovAaFoZvoChYmsIqRURk\npqgnT6ZNfRB+mZaLmTGreB7rat/JW+f/CsuqNlIULPOO7Yt18VrHszx58nPsPf99OgbOzlgdmZZL\nJlAmfsrFT7n4KZfUUk+eSIYqDpWzvGoTSys30NJ7hOauF7k4cDphXJwYp3pe5lTPy1QVNtBUsZaG\nsuUETX+8RUTymZZrRbJI50ArzZF9nOp5hbiLTjiuMFDKovAqFoXXUBLyP31DRETSTz15U6BJnuSD\nwVgfJ7sP0hzZS2+0c8JxhlFfupTGinXMLpqve+6JiGQY9eTJtKkPwi9bcykMlrC48ibunPdxbp7z\nXmqLG73jHI6W3sPsOPs1njnzbzRH9hGND17x/bM1l2RSJn7KxU+5+CmX1FLTjkgWMwtQV7qYutLF\ndA9dpLlrHye7DxB1iRO5yFAbBy48wasXn2VB+Hoaw2spL6hOQ9UiIpIKWq4VyTHR+CCnul+hObKX\nyFDbZcfWljTRFF7HnJImLeWKiKSBevKmQJM8kWHOOS70n6Q5spezvUdwTPznojRUSWN4LQvKb6Aw\nWJzCKkVE8lvO9eSZ2a+Z2TEz6zOz3WZ26xXGv9/M9ppZj5kdN7PfS1WtuUJ9EH65nIuZUVOykPVz\n7ueu+b/MksoNFAZKvGN7o5280r6VJ079A/vbfsR3n/i/Ka428+XyuTIdysVPufgpl9RKeU+emT0A\nfBZ4BNgG/DrwAzO71jl3yjP+XuDLwG8APwKuBf7ZzHqdc3+fuspFsldJqIJrqzezrPIWzvQeorlr\nLx2DLQnj4i7Kie6XOHDhKNUt/TRVrKW+dCkBC6ahahERmY6UL9ea2U5gn3PukVHbDgPfcM59yjP+\ny0Cxc+59o7b9BvBJ59wiz3gt14pMQvtAC81deznT8xpxYhOOKw6Wsyi8moXlqygOlaewQhGR3JfM\n5dqUXskzswLgRuAvxu36MbBxgsOKgP5x2/qB+Wa20Dl3YmarFMkP1UUNVNc2cN2sLZyIvERzZB/9\nsUjCuP5YN4c6tnG4Ywdzy5bTGF5LddFcfVBDRCTDpbonrwYIAq3jtrcC9RMc8yPgfjO724YtA/6f\nkX0NySkz96gPwk+5QFGwlKVVG7hr/i+zvvZ+Zhcv5MDOownjHHFO97zKtrNf4bmWRzkROUAsPpSG\nitND54qfcvFTLn7KJbUy/j55zrnPm9k1wLeBQqAT+Bvg/wPiaSxNJKcELEBD2TIaypbRMbuY+eFS\nTnW/TMwlTuQ6B1vZf+GHvNK+lYXlq2gMr6G0oDINVYuIyERSPclrA2JA3bjtdcDZiQ5yzv1XM/tv\nDF/tOw+8dWTXMd/4hx56iMbGRgCqqqpYs2YNW7ZsAd78X4Re6/UlW7duzZh6MuX1fXffD8C5/Y5z\nfcepXx2kJ9r+syt8N2xYDMCL21/mRV7mhg0vUFeyhDP7BqgsrOOOO+7IqJ9nJl5v2bIlo+rJpNeX\nZEo9mfBa54vOl8v9/Fu3bqW5uZlky5QPXhxi+IMXfzjJ9/gicI1zLuHWK/rghcjMc85xvr+Z5q69\ntPYlLuWOVhaaRVPFWuaXr6QgUJSiCkVEslOu3Sfvr4CHzOxhM1thZn/DcG/d5wDM7DNm9uSlwWY2\n28weGRm7emT8+4DfTkPtWWv8/6BkmHLxG5+LmTGnpImb697LXfM+weKKmygI+G+a3BO9yMGLT/HE\nyX/gwIUniQxeSEHFyadzxU+5+CkXP+WSWinvyXPOPWZms4BPMTy5OwjcO+oeefVA07jDHgT+HDBg\nB3C7c25PikoWkVFKC6q4btYWllVt4kzPqxzvepGuofMJ42JuiObIXpoje6kpXkhjeB11pYsJWDr+\nbykikn/0WDMRmRbnHO0Dpzke2UtLz2HcZT4PVRKsYFF4DQvDN1AULE1hlSIimUnPrp0CTfJE0qc/\n2s0b3ft5I7KfgVjPhOMCBJlbtoKminVUFU109yQRkdyXaz15kgbqg/BTLn5Xm0txqJzlVZt46/xf\nYV3tu6gumucdFyfGqZ6Xea7lUZ478yVOdb9CzEWnUXHy6VzxUy5+ysVPuaRWxt8nT0SyT8CCzCtb\nwbyyFXQOtNIc2cupnleJeyZyHYMt7G37Hi9f/AmLwqtZFF5NSSichqpFRHKLlmtFJCUGY32c7D7A\n8cg++qKdE44zjPrSpTRWrGN20Xw9Pk1Ecpp68qZAkzyRzOZcnHN9xzne9SLn+5svOzZcUENjxVrm\nl11HKFCYmgJFRFJIPXkybeqD8FMufsnMxSxAXeliNtT/AnfMe5im8I2EzD+Biwy1ceDCEzxx8nMc\nvPg0PUPtSavrSnSu+CkXP+Xip1xSSz15IpI25QWzuH72nayovpVT3S9zPLKX7qHEmydH3QDHu/Zw\nvGsPc0qaaAyvY05Jk5ZyRUQuQ8u1IpIxnHNc6D/J8ciLnO19HZj4z3JpqJLG8FoWlN9AYdD/9A0R\nkUynnrwp0CRPJDf0Rrt4I7KfE5H9DMb7JhwXsBDzy66jqWItFYVzUlihiMj0qSdPpk19EH7KxS8T\ncikNVXBt9WbeOv8R1tTcR1Wh/6bJcRflRPdLPHPm39nW8lXO9Bwi7mIzXk8mZJKJlIufcvFTLqml\nnjwRyWjBQIgF5StZUL6S9oEWmrteHJ7IkTiRuzhwiovnT1EcLGdReDULy1dRHCpPQ9UiIumn5VoR\nyToDsR5ORA7QHNlHfywy4TgjwNyy5TSG11JdNFcf1BCRjKOevCnQJE8kf8RdnNbe1zke2cuF/hOX\nHVtZWEdjeC3zylYQDBSkqEIRkctTT55Mm/og/JSLX7bkErAADWXL2Fj/AFvmfoxF4TUEzT+B6xxs\nZf+FH/LEqc/xysVn6B2a+KkbPtmSSaopFz/l4qdcUks9eSKSE8KFNayafTfXVm3mZM/LNHftpSea\nePPkoXg/R7te4GjXC9SVLKGpYi01xYu0lCsiOUfLtSKSk5xznO9vprlrL619Ry87tiw0i6aKtcwv\nX0lBoChFFYqIqCdvSjTJE5HxeoY6eCOyjxPdBxiK9084LmgFLCi/nsbwWsKFs1NYoYjkK/XkybSp\nD8JPufjlWi5lBVVcN2sLb53/CKtnv42KglrvuJgbojmyl61nvsCOs1+npecIcRcHci+TmaJc/JSL\nn3JJLfXkiUjeCAUKWBhexYLyG7g4cJrmrr209B7GEU8Y29Z/grb+E5QEK1gUXsNgbOIrgCIimUjL\ntSKS1/qj3bwR2c8b3fsZiPVMOC5AkPnlK1ladQuloYoUVigiuUw9eVOgSZ6IXI24i9HSc5jjkb20\nD5yecFyAIAvDq1hauUFP0xCRaVNPnkyb+iD8lItfPuYSsCDzyq/l1oYPcVvDgywsv4GAvdnRcmDn\n8Cd048RojuzlqdOf5+WLP7ns1b98kI/nymQoFz/lklrqyRMRGaeyqI7VRW/n2urbOdl9gOORfQlj\n4i7Ksa7dvBHZT1PFOhZX3ERhsCQN1YqI+Gm5VkTkCuIuzqnulzncsZ2+WJd3TMgKuaZyPddUrNe9\n9kRk0tSTNwWa5IlIssRdjBORlzjSuZP+WLd3TEGgmCWVN9MYXksoUJjiCkUk26gnT6ZNfRB+ysVP\nuSTaunUrAQvSWLGWO+d9nJXVd1AYKE0YNxTv59X2Z3nq1Oc52rmbWHwoDdWmjs4VP+Xip1xSSz15\nIiJTFAwUcE3lehaGV9Ec2cvrnS8kPEljMN7LK+0/4VjXLpZWbmBheBUBC6apYhHJR1quFRGZpqH4\nAMe79nC0cxdRN+gdUxKsYFnVRuaXryRgWkQRkWHqyZsCTfJEJF0GY30c7drN8a49xJx/mbYsVMWy\nqo3MK7sW02RPJO+pJ0+mTX0QfsrFT7kkmkwmhcESrq3ezF3zP8E1FevH3Gfvkp5oB3vbvs/WM//G\nmZ5DZPt/SnWu+CkXP+WSWurJExGZYUXBMlbOuoPFFTdxpHMnb0T2Jzwft3voAnvOf4eKglqWV99K\nXclizJLyn3kRyVNarhURSbLeaBdHOnZwsvsADv/fT1WFDSyv3kRtcaMmeyJ5RD15U6BJnohkqp6h\ndg537OBUzyswwWRvVtE8lldvpqZ4QWqLE5G0UE+eTJv6IPyUi59ySTQTmZQVVLO29j62zP0Yc0tX\neMdcHDjNjrNfY8fZr3Ox//S0v2ey6VzxUy5+yiW1NMkTEUmxcOFsbpzzLm6f+4vUly7xjmnrP8G2\ns1/hp63fpGPgbIorFJFcoOVaEZE06xg4y6GO5znXd3zCMfWlS1letYmKwtoUViYiyaaevCnQJE9E\nstXF/tMc6nietv4TE46ZW7aC5VWbKC+YlcLKRCRZ1JMn06Y+CD/l4qdcEqUik1nF87il/gFuqXuA\n6qJ53jFnel7jJ6e/wN7z36dnqCPpNV2JzhU/5eKnXFJL98kTEckwNSUL2VT8Qc73NfNax/N0Do7v\nyXOc6nmZ0z2vsqD8epZV3UJJqCIttYpI5tJyrYhIBnPO0dr3Oofat9E1dN47JkCQReHVLKl8C8Wh\n8hRXKCLToZ68KdAkT0RykXOOlt5DHOrYRvfQRe+YgIVoCq9lceXNFAVLU1yhiFwN9eTJtKkPwk+5\n+CmXROnOxMyYW7aCLXM/xpqa+ygNVSWMibsoR7t28dSpf+K19ucYjPUnva5055KplIufckkt9eSJ\niGQRswALylcyr2wFJ7tf5nDHdvpjkTFjYm6II507Od61l8WV62mquJGCQFGaKhaRdNFyrYhIFou5\nKCciL3GkcycDsR7vmIJACUsqb6YxvJZQoCDFFYrI5agnbwo0yRORfBSLD9Ec2cfrnS8wGO/1jikK\nlLKkagOLylcTDGghRyQT5FxPnpn9mpkdM7M+M9ttZrdeYfzbzGy7mXWZ2Xkz+7aZLU1VvblAfRB+\nysVPuSTK9EyCgQIWV97EXfM/wYqqzRQEihPGDMR7efni0zx9+vM0d+0j7mLT/r6Znku6KBc/5ZJa\nKZ/kmdkDwGeBPwbWANuBH5jZ/AnGNwLfBp4ZGX8XUAx8LwXliohklVCgkKVVG7hr/i+zrHIjIStM\nGNMf6+bAxSd4+vS/cDJykLiLp6FSEUm2lC/XmtlOYJ9z7pFR2w4D33DOfcoz/n3A14DCS+uwZrYF\neAqodc5dHDdey7UiIiMGY30c7drF8a4Xibkh75iyUDXLqjYyr2wFZrrpgkgq5UxPnpkVAL3AB5xz\n3xy1/e+Alc65OzzHLAReA34b+BegDPhbYLlz7hbPeE3yRETGGYj18HrnT4eXafEv04YLalhetYn6\n0qWYJeXfHBEZJ5d68mqAINA6bnsrUO87wDl3ArgH+CNgAOgAVgLvSl6ZuUd9EH7KxU+5JMr2TIqC\nZaycdSd3zv8EjeE1mOev/8hQG7vPP86zLV+ktfcok/kPc7bnkizKxU+5pFbGf7zKzOoYvoL378BX\ngTDDE75vAAlX/gAeeughGhsbAaiqqmLNmjVs2bIFePMEy7fXl2RKPZnyet++fRlVT6a8viRT6tHr\nmX59N4srb+Yr3/tHzvcd5/oN1wBwYOdRAG7YAC+c+xbHd3ewMHwD99/zAGbmfb99+/ZlwM+j19ny\nWucLP/t9c3MzyZYNy7V/BLzDOXfjqG3zgJPArc657ePGa7lWRGSSeobaOdSxndM9r0w4ZlbRfFZU\n38rs4gUprEwkP+TMcq1zbgjYA9w9btfdwLYJDiuFhAaSSx8FS2n9IiK5pqygmnW172DL3I/RULrc\nO+biwCm2n/0aO84+RvvAmRRXKCJXKx2TpL8CHjKzh81shZn9DdAAfA7AzD5jZk+OGv89YJ2Z/Xcz\nW2Jm64B/BU4wPGGUSRh9mVjepFz8lEuiXM8kXFjD+jnv5raGB6krWeId09b/Bs+3fJkXWr9F58Bw\na3Wu53K1lIufckmtlPfkOeceM7NZwKcYntwdBO51zp0aGVIPNI0a/xMz+xDw+8AnGV7u3Qm83TnX\nl9LiRURyXGVRHTfX/RztAy0can+e8/3NCWNa+47S2neUhtJl9PrvyiIiGUCPNRMRkQld6D/Fofbn\nuTBwcsIx88quZVnVJsoLqlNYmUhuyJn75KWCJnkiIjPLOUdb/wkOdTw/YU+eYcwvX8myyo2UFlSm\nuEKR7JUzH7yQ9FEfhJ9y8VMuifI5EzOjtmQRm+o/xM1z3kdlYd3P9l267YrDcbL7IE+f/mdeuvBj\n+qKRdJWbEfL5fLkc5ZJaGX+fPBERyQxmRl3pNcwpaeJs7xEOdWwDjo4Z44jzRmQ/JyMHWRRew9Kq\nt1AULEtPwSJ5Tsu1IiJyVZxznOl5jUMd2+mJXvSOCVoBjeG1LKm8mcJgSYorFMl86smbAk3yRERS\nK+7inO55hcMd2+mNdnrHhKyQpoobWVyxnoJgcYorFMlcae3JM7P3TvYrGQXKzFAfhJ9y8VMuiZSJ\n39atWwlYgAXl13PHvIdZNfseioPhhHFRN8iRzh08dfqfONyxg2h8MA3Vpo7OFz/lklqT6cn7j0m+\nlwOC06hFRESyWMCCLAqvZn7ZSk50v8SRzp0MxHrGjBmKD3Co43mOd+1hSeVbWBReQyhQkKaKRXKb\nlmtFRCQpovEh3ojs5fXOFxiM++9dXxQsY2nlBhaGVxE0fRZQ8o968qZAkzwRkcwSjQ9yrGsPx7p2\nMRQf8I4pDoZZVnULC8qvJ2BaFJL8oZ48mTb1QfgpFz/lkkiZ+E0ml1CgkGVVt3DXvF9maeUtBC1x\nebY/FuGlCz/mJ6f/hZPdB3EunoRqU0fni59ySS315ImISEoUBItZUX0rTRXrONq5i+ORF4m76Jgx\nvdFO9rX9gCMdP2V51Ubmlq3ALCkXOURynpZrRUQkLfqj3bze+QJvRPYRJ+YdEy6oYXnVrdSXLtFk\nT3KSevKmQJM8EZHs0heNcKRzByciB3D4l2krC+tYXnUrc0qaNNmTnJJRz641s5CZbTSzD5jZg6O/\nklGgzAz1QfgpFz/lkkiZ+M1ELiWhMKtm38Od8x5mQfn1GIn/3nUOtvLCuW+y7exXaOt7Y9rfM9l0\nvvgpl9Sa0ufVzWwF8J9AE2BAbOQ9hoAB4IszXaCIiOSH0oIq1tTcy5LKt3C4Yzune15NGNM+cIYd\nrY8xu3gBy6tuZXbx/DRUKpIdprRca2Y/BDqAh4GzwBqgEvgH4A+dc08ko8ip0HKtiEhuiAy2cahj\nGy29hyccU1vcyIrqW6kqakhhZSIzJ2N68szsAnC7c+6gmXUCNzvnDpnZ7cDfOudWJaPIqdAkT0Qk\nt3QOtHKoYxutfUcnHFNXsoTl1ZuoLJyTwspEpi+TevIM6B35/Xlg3sjvTwFLZqoomXnqg/BTLn7K\nJZEy8UtFLpVFddxc915ubfgwNcWLvGNa+17n2TP/zu5z3yEyeCHpNV2Jzhc/5ZJaU32GzEFgNXAM\neAH4AzOLAZ8AXp/h2kRERH6mumgut9S/nwv9J3mt/XkuDpxKGNPSe4iW3sPMK7uW5VUbKSuoTkOl\nIplhqsu1bwPKnHPfMrNrgO8By4E24P3Oua1JqXIKtFwrIpL7nHO09b/Ba+3P0zHY4h1jGAvKr2dp\n1S2UhipTXKHI5GRMT573DcxmAe2ZMrPSJE9EJH845zjXd4zXOp6na/Ccd4wRYFF4FUsrb6E4VJ7i\nCkUuL2M1y3EUAAAgAElEQVR68sys3szGfF7dOXcRmGdmdTNamcwo9UH4KRc/5ZJImfilOxczo650\nMbc1PMj62vsJF9QkjHHEaY7s46nTn+fli08zEOtJel3pziVTKZfUmuoHL74EvM2z/W3Ao9MvR0RE\nZOrMjIayZdw+9xdZW/MOykKJvXhxF+VY1x6eOvV5Xm1/lsFYXxoqFUmdqfbkdTB825TD47YvA3Y6\n52bNcH1TpuVaERGJuzinu1/hUOd2+qKd3jEhK+SaivVcU7megkBRiisUGZYxPXlm1g1sdM69NG77\nKmCHc65shuubMk3yRETkkriLcSJygCOdO+iPdXvHFASKWVxxM00VawkFClNcoeS7jOnJA34K/Kpn\n+68Du6ZfjiSL+iD8lIufckmkTPwyPZeABWmsWMOd8z7Byll3UhQoTRgzFO/ntY5neerU5znauYtY\nfGja3zfTc0kX5ZJaU71P3qeAp0eu3D09su1OYC3w1pksTEREZKYEAyGuqbiRheU30BzZx+udLzAU\nH9uTNxjv5ZX2rRzt2sXSyg0sDK8iaFP9Z1Ikc0z5Fipmthr4fYafWwuwF/gL59z+Ga7tqmi5VkRE\nrmQoPsDxrj0c7dxN1A14x5QEK1hWdQvzy1cSsGCKK5R8kTE9edlAkzwREZmswVg/x7p2caxrDzHn\nX6YtDVWxvGoj88quxWyqXU4il5dJPXmYWZ2Z/Z6Z/b2Z1Yxs22RmTTNfnswU9UH4KRc/5ZJImfhl\ney6FwWJWVG/mrvm/zOKKmwh4lmd7ox3sbfs+W8/8G2d6DjGZCwnZnkuyKJfUmurNkG8EDgEfBj4O\nVIzsuhv4k5ktTUREJDWKgqVcN2sLd837BE3hdQRIXJ7tHrrAnvPf4dkz/87Z3tcnNdkTSaep3kLl\nJ8CzzrlPm1kEWO2cO2ZmtwBfc84tSlahk6XlWhERma6+aBeHO3Zwsvsgjrh3TFVhPcurb6W2uBGz\npKy2SR7ImJ48M+sC1oxM7EZP8hqB15xzxckocio0yRMRkZnSM9TB4Y7tnOp5BfD/21JdNI8VVbdS\nU7IwtcVJTsiknrw+IPFZMbAC8D8ZWjKC+iD8lIufckmkTPxyPZeygirW1t7HHfN+ibllK7xj2gdO\ns6P16+w4+3Uu9p8Gcj+Xq6VcUmuqNwB6HPi0mf3CyGs3chXvz4BvzmBdIiIiGaO8YBY31r6LpZUb\nONSxjbO9RxLGtPWfoO3sV5hT0kT3oD6FK+k31eXaCuD7wCqgDDgL1AHbgXudcz3JKHIqtFwrIiLJ\n1jFwlkMd2zjXd2zCMddU3Mi11bfrHntyWRnTk/ezg8zuBNYxvNz7onPuSTNb4Jw7OdMFTpUmeSIi\nkioX+09zqON52vpPePfPLlrAjXPeRVEw7Y92lwyVST15ADjnnnbO/W/n3J8DB83s74DDM1uazCT1\nQfgpFz/lkkiZ+OV7LrOK53FL/QPcUvcAs4rm/Wz7gZ1HAbgwcJJnz3yR9oEz6Soxo+T7+ZJqk5rk\nmVmVmX3ZzM6b2Rkz+y0b9mngGLAB+KWkVioiIpKhakoWsrH+g7yl7ucpDobH7OuPdbO95Wu8EcmI\np39KHpnUcq2Z/T3wTuAx4O3AtQz35pUB/9M590wyi5wKLdeKiEg6DcR62HP+u1zwLOEuLF/F9bPv\nIuh5sobkp7T35JnZG8DDI7131wCvA//HOfc7yShqOjTJExGRdIu7OK+1P8vRrl0J+6oKG1g/592U\nhCo8R0q+yYSevLnAKwDOuWNAP/D5ZBQkyaE+CD/l4qdcEikTP+Xi9+wzz3LdrC2sq30XQSsYs69j\nsIVnzzxKW5//wxq5TOdLak12khcAhka9jgG9M1+OiIhI7phXtoJbGz5MWahqzPbBeC87Wx/jaOdu\nPQNXkmayy7Vx4AlgYGTTvcAzjJvoOefePdMFTpWWa0VEJNMMxfrZ2/Z9WvuOJuybV3Ytq2bfQyhQ\nmIbKJN0yoSfvXyfzZs65j027omnSJE9ERDKRc47Dnds53LE9YV+4oIab5ryHsgLfk0Mll6W9J885\n97HJfE32m5rZr5nZMTPrM7PdZnbrZcZ+2sziZhYb+TU+6nXNZL9nvlMfhJ9y8VMuiZSJn3Lx8+Vi\nZiyv2sTNc95LyIrG7IsMtfFcy6O09k78BI1coPMltVL+cD0zewD4LPDHwBqGH4n2AzObP8EhfwHU\nAw0jv9YzvFT8E+dcW/IrFhERmTl1pYu5be5HCReMvU4xFB/ghXPf5HDHdvXpyYy4qseaTesbmu0E\n9jnnHhm17TDwDefcpyZx/ALgOPBh59zXPfu1XCsiIhkvGh9kf9uPONP7WsK+upLFrK19BwWBIs+R\nkkvSvlw7U8ysALiR4Q9xjPZjYOMk3+Zh4CLwrRksTUREJKVCgULW1b6T66q3YIz9N7617yjPnXmU\nyKAWrOTqpXq5tgYIAq3jtrcyvAx7WWYWAD4GfNE5N3Sl8fIm9UH4KRc/5ZJImfgpF7/J5mJmLK68\niQ1176cwUDpmX0+0nedavsSZnkNJqDA9dL6kVrY9V+VeYD5XuBHzQw89RGNjIwBVVVWsWbOGLVu2\nAG+eYPn2+pJMqSdTXu/bty+j6smU15dkSj16nbmv9+3bl1H1ZOvrmpKFcKSR4+3baFo//AnbAzuH\nb7cS2zBEx8BNnN0fJ2CBjKj3al/rfOFnv29ubibZUtqTN7Jc2wt8wDn3zVHb/w5Y6Zy74wrHfxuY\n7ZzbfJkx6skTEZGsFItHOXjxSU50H0jYV1O8kHW176IoWOo5UrJVzvTkjSyx7gHuHrfrbmDb5Y41\nswbgHcA/Jac6ERGR9AoGQqyueTurZt+Djfsnuq3/BM+deZSOgbNpqk6yTUoneSP+CnjIzB42sxVm\n9jcM3x7lcwBm9hkze9Jz3MNAN/CN1JWaO0ZfJpY3KRc/5ZJImfgpF7/p5rIovJpN9R+kOFg+Zntf\nrIttLV/hRCTxSl820PmSWimf5DnnHgN+B/gUsJfhT9Xe65w7NTKkHmjyHPpLwJecc/0pKVRERCSN\nqovnctvcB5lVNPY2snFi7L/wQ1668GPiLpam6iQbpPw+ecmmnjwREcklcRfjlfZnON61J2FfdVED\nN9beT0konIbKZCak/dm12USTPBERyUWnul9h/4UfEXfRMduLAqXcOOd+ZhdP9OAoyWQ588ELSR/1\nQfgpFz/lkkiZ+CkXv2TkMr/8OjY3fJjSUOWY7QPxXnac/TrHuvZk/OPQdL6kliZ5IiIiWaKicA6b\nGx5kTsnY1nVHnJcvPs3etu8TjetZATJMy7UiIiJZxrk4hzq2c6RzR8K+ioJa1s95D2UFVWmoTKZK\nPXlToEmeiIjki7O9r7P3/PeIusEx2wsCxayreSdzSn03q5BMop48mTb1QfgpFz/lkkiZ+CkXv1Tl\nUl+6hM1zP0p5wewx24fi/fz03H9wpGNnRvXp6XxJLU3yREREslh5wSw2N3yEhtJlCfte63iO3ee/\nzVB8IA2VSbppuVZERCQHOOc42vUCr7Y/B4z9d7AsNIub5ryHcOFs/8GSNurJmwJN8kREJJ+d72tm\nz/nvMhTvG7M9aAWsrbmPhrLEK36SPurJk2lTH4SfcvFTLomUiZ9y8UtnLrUljdzW8FEqC+vGbI+5\nIXaff5xX25/FuXhaatP5klqa5ImIiOSY0oJKNtV/kPllKxP2vd75U3a2/geDsT7PkZJLtFwrIiKS\no5xzvBHZx8GLT+MYe/WuJFjB+jnvoaqoboKjJRXUkzcFmuSJiIiMdbH/NLvPP85ArGfM9oCFWDX7\nbhaUX5+mykQ9eTJt6oPwUy5+yiWRMvFTLn6Zlsus4nnc1vAg1UXzxmyPuyj72n7AgQtPEnexpNeR\nabnkOk3yRERE8kBxqJyN9Q/QGF6bsK85spftZ79Of7Q7DZVJsmi5VkREJM+c7D7ISxeeIO6iY7YX\nBctYX/tuZhXPT1Nl+Uc9eVOgSZ6IiMiVdQ60suv84/RFO8dsNwKsnHUHjeG1mCVl7iGjqCdPpk19\nEH7KxU+5JFImfsrFLxtyqSyq47aGj1Jb3DhmuyPOwYtPsa/tB8TiQzP6PbMhl1yiSZ6IiEieKgyW\n8Ja697Gk8i0J+071vMzzZ79C71Cn50jJBlquFREREVp6DrO37fvE3NirdwWBEm6sfSe1JY3pKSzH\nqSdvCjTJExERuTqRwQvsOvdteqIXx+0xrq3ezOKKm9WnN8PUkyfTpj4IP+Xip1wSKRM/5eKXrbmE\nC2ezee5HqC9dMm6P49X2Z9lz/jtE44NX/f7Zmku20iRPREREfqYgUMT62vewompzwr6W3sM81/Il\nuofGX+mTTKTlWhEREfE613ecF89/l6F4/5jtIStkTc19NJQtTVNluUM9eVOgSZ6IiMjM6R3qYNf5\nx+kaPJewb2nlBpZXbcJMC4NXSz15Mm3qg/BTLn7KJZEy8VMufrmUS2lBFZvqP8S8susS9h3p3MlP\nz32LwVjfpN4rl3LJBprkiYiIyGWFAgWsrbmP62fdhY2bOpzvO85zLY/S6bnSJ+ml5VoRERGZtAv9\np9hz7nEG4r1jtgcsxOrZb2N+eeIVP5mYevKmQJM8ERGR5OqPdrP7/OO0D5xJ2NcUvpHrZt1OwIJp\nqCz7qCdPpk19EH7KxU+5JFImfsrFL9dzKQ6Vs7H+AywKr0nYdzyyhx1nH2Mg1pOwL9dzyTSa5ImI\niMiUBSzIqtl3s3r22wkw9qrdxYFTPHvmi7T3J17pk9TRcq2IiIhMS8fAWXafe5y+WNeY7UaA62ff\nxaLy1Xoc2gTUkzcFmuSJiIik3kCslxfP/ydt/ScS9i0sv4HrZ72VYCCUhsoym3ryZNrUB+GnXPyU\nSyJl4qdc/PIxl6JgKW+p+wUWV9ycsO9E9wG2nf0qP3zqe2moLH9pkiciIiIzImABrpt1OzfWvpug\nFYzZ1zl4lpfafkxbX+KVPkkOLdeKiIjIjIsMtrHr3LfpibaP22NcV30711SsV58e6smbEk3yRERE\nMsNQfIC9579Pa9/rCfvmli5ndc3bCQUK01BZ5lBPnkxbPvaHTIZy8VMuiZSJn3LxUy7DCgJF3DTn\nPSyvuhWAAzuP/mzfmd5DPN/yZbqHxl/pk5miSZ6IiIgkjZmxrOoWbp7zPkLj+vQiQ208d+ZRWnuP\nTnC0TIeWa0VERCQleoba2XXu20SG2hL2LavcyLKqjXnXp6eevCnQJE9ERCRzReODvHThx5zueTVh\n35ySa1hX8w4KgsVpqCw91JMn06b+ED/l4qdcEikTP+Xip1z8tm7dSihQyNqad7Cy+g6MsXObc33H\neLblUboGz6epwtyiSZ6IiIiklJlxTeV6NtQ/QGGgdMy+3mgHz7d8mdPdiVf6ZGq0XCsiIiJp0xeN\nsPvc43QMtiTsu6ZiPddW307AcveaVM4t15rZr5nZMTPrM7PdZnbrJI75HTN71cz6zey0mf2vVNQq\nIiIiyVMSCrOx4QMsLF+VsO9Y1252tj7GQKwnDZVlv5RP8szsAeCzwB8Da4DtwA/MbP5ljvkr4BHg\nk8AK4D7g2eRXmzvUH+KnXPyUSyJl4qdc/JSL30S5BC3E6pq3sWr2PQQIjtl3of8kz555lPaBxCt9\ncnnpuJL3u8AXnHNfcM4dcs79FtAC/KpvsJktB34DeLdz7rvOuWbn3H7n3A9TWLOIiIgk2aLwajY2\nfJDiYHjM9v5YhO0tX+VE5KU0VZadUtqTZ2YFQC/wAefcN0dt/ztgpXPuDs8xnwR+CfhH4DcZnpg+\nA3zSOZfw8Rv15ImIiGS3gVgPe879JxcGTibsW1i+iutn30XQQmmobOblUk9eDRAEWsdtbwXqJzjm\nGqAReAB4EPgIw0u230lOiSIiIpJORcEyNtS/n2sq1ifsO9H9EttbvkZfNJKGyrJLNnxcJQAUAh9x\nzm1zzm0DPgq8xcxuSm9p2UP9IX7KxU+5JFImfsrFT7n4TSWXgAVYOesO1tW8k+C4x6F1DLbw7Jkv\n0tafeKVP3pTqa51tQAyoG7e9Djg7wTEtQNQ597MH2znnjphZDFgI7Bp/wEMPPURjYyMAVVVVrFmz\nhi1btgBvnmD59vqSTKknU17v27cvo+rJlNeXZEo9ep25r/ft25dR9eh1Zr++2vMlXDibzz/+5wzE\nurlhw2IA9mw/wIsc5IF7H6ap4kaeeeaZtP98k3l96ffNzc0kW8rvk2dmO4F9zrlHRm07BHzDOfeH\nnvF3Az8Eljjnjo9sWwwcAW52zu0eN149eSIiIjlmMNbP3rbvca7vWMK+eWXXsmr2PYQChWmobHpy\n6tm1ZvZ+4IvArwPbGP5U7ceA65xzp8zsM8BNzrm3jow34AWgm+FP5hrw10CBc26T5/01yRMREclB\nzjkOd2zncOf2hH0VBbWsn3M/ZQXVaajs6uXSBy9wzj0G/A7wKWAvsBG41zl3amRIPdA0arwD3gmc\nY/hTtT8ATgDvSWHZWW/0ZWJ5k3LxUy6JlImfcvFTLn7TzcXMWF69iZvm/BwhG3vVrmvoPM+1PEpr\nb+KVvnyVls8fO+c+B3xugn0f82xrZfjTtSIiIpLn6kuXsHnug+w+920iQ20/2z4UH+CFc99kedUm\nllbewvBiYP7Ss2tFREQkK0Xjg+xv+yFneg8l7KsrWcLa2vsoCBSlobLJy6mevGTTJE9ERCR/OOc4\n1rWLV9qfBcb++18WquamOe8hXFiTnuImIad68iQ91B/ip1z8lEsiZeKnXPyUi18ycjEzFlfezC11\nv0BhoGTMvp5oO8+1fIkzPYlX+vKBJnkiIiKS9WpKFrF57oNUFo59gFbMDbHn/Hd45eIzxF08TdWl\nh5ZrRUREJGfE4lEOXHyCk90HE/bVFC9kXe27KAqWpqEyP/XkTYEmeSIiIvnNOccb3fs5eOEpHGOv\n3pUEK1g/536qiuonODq11JMn06b+ED/l4qdcEikTP+Xip1z8UpWLmdEYXsOm+g9SHCwfs68v1sW2\nlq9wMpJ4pS/XaJInIiIiOam6eC6bGz7KrKL5Y7bHibHvwg946cITxF0sTdUln5ZrRUREJKfFXYxX\nLj7D8ciehH3VRXNZX3s/xaFyz5HJp568KdAkT0RERHxOdb/M/gs/Ju6iY7YXBcu4sfbdzC6eP8GR\nyaOePJk29Yf4KRc/5ZJImfgpFz/l4pfuXOaXr+TW+g9RGqocs30g1sOOs1/neNeL5NKFIk3yRERE\nJG9UFtWxueGj1BY3jtnuiHPw4lPsa/s+0fhQeoqbYVquFRERkbzjXJxDHds40rkzYV9F4Rxuqr2f\n0oKqpNehnrwp0CRPREREJqul58jw1Ts3OGZ7QaCYdbXvZE5JU1K/v3ryZNrS3QeRqZSLn3JJpEz8\nlIufcvHLxFwaypayee5HKS+YPWb7ULyfn7b+B0c6dmZtn54meSIiIpLXygtmsbnhIzSULkvY91rH\nc+w+/zhD8YE0VDY9Wq4VERERYfhxaEe7XuDV9ueAsXOJ8oJZrK99D+HC2f6Dr5J68qZAkzwRERGZ\njvN9zew5/58MxfvHbA9ZIWtq7qWhLPGK39VST55MWyb2QWQC5eKnXBIpEz/l4qdc/LIll9qSRm5r\neJCKwjljtkfdILvPP86r7c/hXDxN1U2eJnkiIiIi45QWVHJr/YeYX7YyYd/rnTv5aes3GYz1paGy\nydNyrYiIiMgEnHO8EdnHwYtP4xh79a4kVMlNtfdTWVR31e+vnrwp0CRPREREZtrF/lPsPv8dBmI9\nY7YHLMTq2fcwvzzxit9kqCdPpi1b+iBSTbn4KZdEysRPufgpF79szmVW8Xxua3iQ6qJ5Y7bHXZS9\nbd/n4IWniLtYmqrz0yRPREREZBKKQ+VsrH+AxvDahH3HIy+y4+zX6Y92p6EyPy3XioiIiEzRye6D\nvHThCeIuOmZ7UbCM9bX3M6t43gRHjqWevCnQJE9ERERSoWOgld3nvk1frGvMdiPA9bPuZFF4DWaX\nn7+pJ0+mLZv7IJJJufgpl0TKxE+5+CkXv1zLpaqojtvmPkhN8aIx2x1xDlx8kv0XfkgsPpSm6jTJ\nExEREblqhcESNtT9PEsq35Kw72T3Qbad/Sq90c40VKblWhEREZEZcabnEPvafkDMjb16VxAo4cba\nd1Jb0phwjHrypkCTPBEREUmXyGAbu859m55o+7g9xrXVm1lccfOYPj315Mm05VofxExRLn7KJZEy\n8VMufsrFLx9yCRfWsHnuR6krWTJuj+PV9mfZc/47ROODKalFkzwRERGRGVQQKOKmOe9hedWtCfta\neg/zXMuX6B66mPQ6tFwrIiIikiTneo/zYtt3GYr3j9keskLW1r6DhrKlWq4VERERyTZzSpvY3PBR\nKgpqx2yPukF2nfu/Sf3emuTliXzog7gaysVPuSRSJn7KxU+5+OVrLmUFVWxq+DDzyq5N6ffVJE9E\nREQkyUKBAtbWvIOVs+7ESMrqbAL15ImIiIik0IX+k+w+9x0G4728u+n31ZMnIiIikgtmFy/gtrkP\nUl3UkNTvo0lensjXPogrUS5+yiWRMvFTLn7KxU+5vKkkFOaW+g8k9XtokiciIiKSBkELJfX91ZMn\nIiIikiZ6rJmIiIiITIkmeXlCfRB+ysVPuSRSJn7KxU+5+CmX1NIkT0RERCQHqSdPREREJE1yrifP\nzH7NzI6ZWZ+Z7TazWy8zdpGZxcd9xczsnlTWLCIiIpJNUj7JM7MHgM8CfwysAbYDPzCz+Zc5zAH3\nAPUjXw3A00kuNaeoD8JPufgpl0TKxE+5+CkXP+WSWum4kve7wBecc19wzh1yzv0W0AL86mWOMeCi\nc+7cqK9oSqrNEfv27Ut3CRlJufgpl0TKxE+5+CkXP+WSWimd5JlZAXAj8MS4XT8GNl7h8G+ZWauZ\nPW9m70tKgTmso6Mj3SVkJOXip1wSKRM/5eKnXPyUS2ql+kpeDRAEWsdtb2V4GdanG/h/gfcD9wJP\nAV83sw8lq0gRERGRbJfc52nMAOfcBeCvR2160cxmA78PfCU9VWWf5ubmdJeQkZSLn3JJpEz8lIuf\ncvFTLqmV0luojCzX9gIfcM59c9T2vwNWOufumOT7PAj8g3OuzLNP908RERGRrJGsW6ik9Eqec27I\nzPYAdwPfHLXrbuAbU3irtQx/WMP3PZISlIiIiEg2Scdy7V8BXzSzXcA2hj9V2wB8DsDMPgPc5Jx7\n68jrB4EhYC8QB949cszvp750ERERkeyQ8kmec+4xM5sFfIrhyd1B4F7n3KmRIfVA07jD/hBYCMSA\nw8DHnHNfTVHJIiIiIlkn5x5rJiIiIiJpeqzZVEzxEWhFZvavZrbfzAbNzPtUDDO7feS9+szsdTP7\nleT9BMkx07mMZOJ7fNyy5P4kM2eKmdxuZt82szNm1jOSzccmGJdP58oVc8mFcwWmnMu1Zva0mZ0d\nGX/UzP5k5MNko8fl2/lyxVxy4XyZSibjjltqZhEz6/Lsy6tzZdxx3lxy4VyB5Dy69arPF+dcxn4B\nDwCDwC8By4H/A0SA+ROMLwX+Hvg48C3gac+YRobvvffZkff8+Mj3+Ll0/7xpzuV2hpfDlwNzRn1Z\nun/eJGXyX4E/Am4ZOSceYbj38wN5fq5MJpesPleuMpfFwIPADcAC4J3AWeBP8/x8mUwuWX2+TDWT\nUccVALuB/wS6xu3Lu3Nlkrlk9blyNbkAi0Z+5reO+5lDM3G+pD2QK4S1E/jcuG2HgT+ZxLF/i38y\n82fAoXHbPg9sS/fPm+ZcLv3hmpXuny/VmYwa/3XgGzpXrphLVp8rM5jLX44+F3S+TJhLVp8vV5sJ\nw/d3/RfgFz2Tmbw9V66QS1afK1eTC8OTvDiw7jLvedXnS8Yu19r0HoF2ORtG3mO0HwHrzSw4jfdN\niSTmAsPPCN49slT3pJltmeb7pcQMZlIBtI96rXNl2PhcIEvPFZiZXMxsCfB2YOuozXl/vkyQC2Tp\n+XK1mZjZO4D7gN+cYEheniuTyAWy9FyBpD669arPl4yd5HF1j0CbjPoJ3jM08j0zXbJyaWF4ae59\nwM8Bh4CnzGzTNN4zVaadiZm9E7gT+MdRm/P+XJkgl2w+V2AauZjZNjPrY/hnfs4596lRu/P2fLlC\nLtl8vkw5EzObC/wT8GHnXO8E75t358okc8nmcwWS9+jWqz5fMv6xZpIazrnDDF9SvuSnZtYIfJLh\n+xnmrJG/QL4M/KZzbk+668kUE+WSz+cKw38Rh4HVwP82s//inPvTNNeUCSbMJQ/Pl0eBv3fO7R55\nrRv0D7tiLnl4ruCS/OjWTL6S18bw2nzduO11DDf2Xq2zE7xndOR7Zrpk5eLzU2DpDL9nMlx1JiOf\nevo+8IfOuX8atztvz5Ur5OKTLecKTCMX59xp59xrzrmvA/8F+LSZXfp7NG/Plyvk4pMt58vVZHIH\nwz//kJkNAf8MlNvwnQ0+PjImH8+VyeTiky3nCszcv88vMPZnvurzJWMnec65IeDSI9BGu5vpzeh3\neN7zHmC3cy42jfdNiSTm4jPh4+MyydVmYma3MTyR+R/Oub/1DMnLc2USufhkxbkCM/pnKDjqC/L0\nfPEYn4tPVpwvV5nJ9cAahq9qrgb+B8PPbF/Nm4/vzMdzZTK5+GTFuQIz+mdo/M989edLuj+JcoVP\nqbwf6AceBlYAfwN0MfJRZOAzwJPjjrmW4RPpqwzPhlcDq0ftb2T448x/PfKeHx/5Hu9J98+b5lx+\nG7gfWAJcN/IeMeD+dP+8ycgE2MJwL8SfMfw/oktfNfl8rkwyl6w+V64yl48AP8/w7QuaRo4/BXw5\nz8+XyeSS1efLVDPxHO/7FGnenSuTzCWrz5WryYXhWxB9cGTsMuD3Ro7/rZk4X9IeyCQCewQ4BvQB\nu4BNo/b9K3B03PjjIyfFpa84EBs3ZjPD9+npA44Cn0j3z5nuXBjueTgE9DB8+fcZ4G3p/jmTlcnI\n68QAU98AAAUVSURBVJjn61g+nyuTySUXzpWryOUDDP8PvXPkL+wDwB8ARXl+vlwxl1w4X6b69+24\nYxMmM/l4rkwml1w4V6aaC8OTvJcZnsR1MHwR5oMzdb7osWYiIiIiOShje/JERERE5OppkiciIiKS\ngzTJExEREclBmuSJiIiI5CBN8kRERERykCZ5IiIiIjlIkzwRERGRHKRJnohIiphZkZnFzey+dNci\nIrlPkzwRSamRSU5s5NfxXzEz+0K6a0wnM/uMme2awvgJJ45m9lUze2xmKxSRbBFKdwEiknfqR/3+\nXcA/jWyzkW19Ka9omsws5JyLzuBb6lFEIjJtupInIinlnDt36YvhZzXinDs/ansEwMwWmtljZtZu\nZhfM7HEza7z0PpeueJnZx83sDTOLmNk/mFnQzH7HzE6Z2Xkz+9PR39/MWszsv41c5eo2s9Nm9pvj\nxjSa2XdG3rNzpI46z/f+hJkdA/rNLGBm7zSz50dqbjOz75nZkulmZma/aWZHzWzAzA6Z2YPTfU8R\nyX2a5IlIxjGzcmArcAHYBGwE2oEnzKxg1NDlwF3A24D3M/zQ8++ObL8T+DXg98zs3nHf4pMMP+x7\nDfC/gL80s7ePfO8A8D0gDNwKvBVoAv5j3HusAN4NvAdY45yLA6XAnwPrRr7/APD4yHtebRYfBP4C\n/v927ibUqiqMw/jzJhZBUBA0aFCBFWoIgSAmSlRQAyeFSBMVSiycifYxaRJOpDAHUghS2CRqEORE\nRdCUQpskRWZX84uKxBDhdgsUw7fBWid3+3rVe73icZ/nB4e7P9da547+5117bdYBjwGbgI8i4pmJ\ntilpMDhdK6kfLQX+ysyVvQMRsYIS+p6jBDko05ovZeY5YCgidlGC28Iauo5ExGrgKWB7o/29mbm+\nbh+NiLnAamAHsBB4GHg6M0/XvpcChyJiXmbuq/dNAZZk5nCv0cz83/NvEbG8jvlx4MAE/xdrgM2Z\nubnub4iIOcCbwK4JtilpAFjJk9SPZgMz6nTpSESMAGcplbJpjeuO14DXcxoYqgGveey+VvvftPb3\nAzPr9nTgZC/gAWTmECWszWzcc6IZ8AAi4pGI+LROrQ4Dv1CC6ANX/8pjmg7sax37ujUWSRrFSp6k\nfnQbJYgt49KCjJ4zje0LrXM5xrHJ+kHbXBDx92XO7wB+ApYDp+qxQ8Dtk9T/qLFk5vmIOA/cfZlr\n7gF+uwF9S7oFWMmT1I8OAI8Cf2Tm8dbnz0lof25r/wlKOKP+fSgi/lsFHBEzgHuBH8dqMCLupzy7\ntzYz92Tm4XpPO6SO1xDlucSmBZTw2PMzpfrZHM9UYBZw+Dr7l3SLspInqR99DKwCvoiItynVqAeB\nF4D1mfnrdbb/ZESsAbYCz1IWbTxfz22jhKZP6jVTgfeBrzKzPc3b1Fst/GpEnKnjXQdcvMI91+Jd\nYEtEfA98SVnssYjybGLPe8DGiDgC7AbuojzLdwcw0O8dlAaZlTxJfae+RmU+8DvwOaVq9SFwJzB8\nhVuv1TvAHOA74C3g9czcXvu+SFl8MQLsBXYCx4DFVxnzP8CLtd0fKMHrDUaHvHG9Ay8zPwNeoyy0\nOAi8Arycmbsb12wBVgIrgG+5tDp4QWaeHU9/krojMn3npqTBERGnKFOqH9zssUjSjWQlT5IkqYMM\neZIGjdMXkgaC07WSJEkdZCVPkiSpgwx5kiRJHWTIkyRJ6iBDniRJUgcZ8iRJkjrIkCdJktRB/wIc\nzwwpDZ1rIwAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f91bac95d10>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fn_size = 14\n", "plt.figure(num=None, figsize=(10, 5))\n", "plt.plot(iou_thrs, recall, linewidth=4, color=np.array([178,223,138])/255.0)\n", "plt.xlabel('Temporal IoU', fontsize=fn_size)\n", "plt.ylabel('Recall', fontsize=fn_size)\n", "plt.grid(b=True, which=\"both\")\n", "plt.setp(plt.axes().get_xticklabels(), fontsize=fn_size)\n", "plt.setp(plt.axes().get_yticklabels(), fontsize=fn_size)\n", "plt.xlim([0.1, 0.5])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.13"}}, "nbformat": 4, "nbformat_minor": 0}