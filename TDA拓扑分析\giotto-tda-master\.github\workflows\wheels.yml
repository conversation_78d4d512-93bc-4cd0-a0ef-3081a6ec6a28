name: Build Wheels

# https://docs.github.com/en/actions/learn-github-actions/workflow-syntax-for-github-actions#onworkflow_dispatchinputs
on :
  workflow_dispatch:
    inputs:
      nightlyRelease:
        description: 'Generates wheels for the nightly release'
        type: boolean
        required: true
        default: false

jobs:

  build_wheels:
    name: Build wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            boost_install_dir: /home/<USER>/work
          - os: macos-latest
            boost_install_dir: /Users/<USER>/work
          - os: windows-latest
            boost_install_dir: D:\
    env:
      BOOST_VERSION: 1.76.0

    steps:
      - uses: actions/checkout@v2

      - name: Change name to giotto-tda-nightly
        if: ${{ github.event.inputs.nightlyRelease == true }}
        run: |
          set -e
          sed -i "s/'giotto-tda'/'giotto-tda-nightly'/1" setup.py
          sed -i 's/"giotto-tda"/"giotto-tda-nightly"/1' setup.py
          sed -i "s/__version__.*/__version__ = '$(Build.BuildNumber)'/1" gtda/_version.py
        shell: bash

      - name: Create directories boost
        run: |
          cd ${{ matrix.boost_install_dir }}
          mkdir -p boost/boost
          cd -

      - name: Cache boost ${{env.BOOST_VERSION}} in ${{ matrix.boost_install_dir }}
        # FIXME For an unknown reason on windows runner, when the cache is hit
        # The compilation fails because it doesn't find some headers
        # I don't know if the cache is corrupted, for the moment hard to debug
        if: ${{ runner.os == 'Linux' || runner.os == 'macOS' }}
        uses: actions/cache@v2
        id: cache-boost
        with:
          # Set the default path as the path to cache
          path: ${{ matrix.boost_install_dir }}/boost/boost
            # Use the version as the key to only cache the correct version
          key: 1-boost-${{env.BOOST_VERSION}}

      - name: Install boost ${{env.BOOST_VERSION}}
        uses: MarkusJx/install-boost@v2.3.1
        id: install-boost
        if: steps.cache-boost.outputs.cache-hit != 'true'
        with:
          boost_version: ${{env.BOOST_VERSION}}
          boost_install_dir: ${{ matrix.boost_install_dir }}

      - name: Build wheels
        uses: pypa/cibuildwheel@v2.18.1
        env:
          # Specify which Python versions to build wheels
          # https://cibuildwheel.readthedocs.io/en/stable/options/#build-skip
          CIBW_BUILD: "cp38-* cp39-* cp310-* cp311-* cp312-*"
          # Skip 32 bit architectures, musllinux, and i686, and macOS x86_64 wheels for CP3.8 -- CP3.12
          CIBW_SKIP: "*-win32 *-musllinux_x86_64 *_i686 cp38-macosx_x86_64 cp39-macosx_x86_64 cp310-macosx_x86_64 cp311-macosx_x86_64 cp312-macosx_x86_64"
          CIBW_BEFORE_BUILD_WINDOWS: python -m pip install --upgrade pip setuptools && sed -i $'s/\r$//' README.rst && python -m pip install delvewheel
          CIBW_BEFORE_BUILD_LINUX: python -m pip install --upgrade pip setuptools
          CIBW_BEFORE_BUILD_MACOS: python -m pip install --upgrade pip setuptools
          CIBW_TEST_COMMAND: python -m pytest --pyargs gtda
          # nbformat is needed by plotly: https://github.com/plotly/plotly.py/issues/2159
          CIBW_TEST_REQUIRES: pytest hypothesis pandas nbformat
          CIBW_ENVIRONMENT_WINDOWS: BOOST_ROOT='${{ matrix.boost_install_dir }}boost\boost'
          CIBW_ENVIRONMENT_MACOS: BOOST_ROOT='${{ matrix.boost_install_dir }}/boost'
          CIBW_ENVIRONMENT_LINUX: BOOST_ROOT=/host${{ matrix.boost_install_dir }}/boost
          CIBW_ENVIRONMENT_PASS_LINUX: BOOST_ROOT
          CIBW_REPAIR_WHEEL_COMMAND_WINDOWS: "delvewheel repair -vv -w {dest_dir} {wheel}"
          CIBW_MANYLINUX_X86_64_IMAGE: manylinux2014
          # Should generate universal2 wheels for CP3.8 -- CP3.12
          CIBW_ARCHS_MACOS: x86_64 universal2

      - name: Set-up python 3.10 for upload
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Publish
        env:
          TWINE_USERNAME: ${{ secrets.PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          pip install twine
          twine check ./wheelhouse/*.whl
          twine upload --skip-existing ./wheelhouse/*.whl
