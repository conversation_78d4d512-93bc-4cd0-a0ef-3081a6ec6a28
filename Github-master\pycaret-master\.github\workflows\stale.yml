name: Mark stale issues and pull requests

on:
  schedule:
  - cron: "30 1 * * *"

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
    - uses: actions/stale@v7
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'Stale issue message: This issue will be automatically closed by GitHub Actions in 1 week if there is no further activity.'
        stale-pr-message: 'Stale pull request message: This PR will be automatically closed by GitHub Actions in 1 week if there is no further activity.'
        stale-issue-label: 'no-issue-activity'
        stale-pr-label: 'no-pr-activity'
        exempt-issue-labels: 'bug,enhancement,help wanted'
