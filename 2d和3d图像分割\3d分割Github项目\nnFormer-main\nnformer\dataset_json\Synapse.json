{"labels": {"0": "background", "1": "spleen", "10": "portal vein and splenic vein", "11": "pancreas", "12": "right adrenal gland", "13": "left adrenal gland", "2": "right kidney", "3": "left kidney", "4": "gallbladder", "5": "esophagus", "6": "liver", "7": "stomach", "8": "aorta", "9": "inferior vena cava"}, "licence": "see challenge website", "modality": {"0": "CT"}, "name": "Synapse", "numTest": 12, "numTraining": 30, "description": "It seems that we use the whole data to train, but we will split the validation set from the training set", "reference": "see challenge website", "release": "0.0", "tensorImageSize": "4D", "test": ["./imagesTs/img0001.nii.gz", "./imagesTs/img0002.nii.gz", "./imagesTs/img0003.nii.gz", "./imagesTs/img0004.nii.gz", "./imagesTs/img0008.nii.gz", "./imagesTs/img0022.nii.gz", "./imagesTs/img0025.nii.gz", "./imagesTs/img0029.nii.gz", "./imagesTs/img0032.nii.gz", "./imagesTs/img0035.nii.gz", "./imagesTs/img0036.nii.gz", "./imagesTs/img0038.nii.gz"], "training": [{"image": "./imagesTr/img0001.nii.gz", "label": "./labelsTr/label0001.nii.gz"}, {"image": "./imagesTr/img0002.nii.gz", "label": "./labelsTr/label0002.nii.gz"}, {"image": "./imagesTr/img0003.nii.gz", "label": "./labelsTr/label0003.nii.gz"}, {"image": "./imagesTr/img0004.nii.gz", "label": "./labelsTr/label0004.nii.gz"}, {"image": "./imagesTr/img0005.nii.gz", "label": "./labelsTr/label0005.nii.gz"}, {"image": "./imagesTr/img0006.nii.gz", "label": "./labelsTr/label0006.nii.gz"}, {"image": "./imagesTr/img0007.nii.gz", "label": "./labelsTr/label0007.nii.gz"}, {"image": "./imagesTr/img0008.nii.gz", "label": "./labelsTr/label0008.nii.gz"}, {"image": "./imagesTr/img0009.nii.gz", "label": "./labelsTr/label0009.nii.gz"}, {"image": "./imagesTr/img0010.nii.gz", "label": "./labelsTr/label0010.nii.gz"}, {"image": "./imagesTr/img0021.nii.gz", "label": "./labelsTr/label0021.nii.gz"}, {"image": "./imagesTr/img0022.nii.gz", "label": "./labelsTr/label0022.nii.gz"}, {"image": "./imagesTr/img0023.nii.gz", "label": "./labelsTr/label0023.nii.gz"}, {"image": "./imagesTr/img0024.nii.gz", "label": "./labelsTr/label0024.nii.gz"}, {"image": "./imagesTr/img0025.nii.gz", "label": "./labelsTr/label0025.nii.gz"}, {"image": "./imagesTr/img0026.nii.gz", "label": "./labelsTr/label0026.nii.gz"}, {"image": "./imagesTr/img0027.nii.gz", "label": "./labelsTr/label0027.nii.gz"}, {"image": "./imagesTr/img0028.nii.gz", "label": "./labelsTr/label0028.nii.gz"}, {"image": "./imagesTr/img0029.nii.gz", "label": "./labelsTr/label0029.nii.gz"}, {"image": "./imagesTr/img0030.nii.gz", "label": "./labelsTr/label0030.nii.gz"}, {"image": "./imagesTr/img0031.nii.gz", "label": "./labelsTr/label0031.nii.gz"}, {"image": "./imagesTr/img0032.nii.gz", "label": "./labelsTr/label0032.nii.gz"}, {"image": "./imagesTr/img0033.nii.gz", "label": "./labelsTr/label0033.nii.gz"}, {"image": "./imagesTr/img0034.nii.gz", "label": "./labelsTr/label0034.nii.gz"}, {"image": "./imagesTr/img0035.nii.gz", "label": "./labelsTr/label0035.nii.gz"}, {"image": "./imagesTr/img0036.nii.gz", "label": "./labelsTr/label0036.nii.gz"}, {"image": "./imagesTr/img0037.nii.gz", "label": "./labelsTr/label0037.nii.gz"}, {"image": "./imagesTr/img0038.nii.gz", "label": "./labelsTr/label0038.nii.gz"}, {"image": "./imagesTr/img0039.nii.gz", "label": "./labelsTr/label0039.nii.gz"}, {"image": "./imagesTr/img0040.nii.gz", "label": "./labelsTr/label0040.nii.gz"}]}