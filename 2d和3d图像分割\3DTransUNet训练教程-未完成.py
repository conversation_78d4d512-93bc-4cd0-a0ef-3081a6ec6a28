conda create -n 3D-TransUNet python=3.8       #conda remove --name 3DTransUNet --all
# 使用autodl 4090D可以正常安装，3090会环境报错：
conda activate 3D-TransUNet
cd autodl-tmp
git clone https://github.com/Beckschen/3D-TransUNet.git
cd 3D-TransUNet
cd scripts
chmod +x install.sh
./install.sh
chmod +x train.sh
./train.sh
/inference.sh


conda update conda #conda init报错时用
# 对于 CUDA 11.8
# nvidia-smi 看一下NVDIA的cuda版本
# nvcc --version看一下系统安装的cuda版本与torch版本是否一致
# 不一致的话，请安装与nvcc --version版本一致的torch
nvidia-smi 
nvcc --version
python -c "import torch; print(torch.__version__)" 

conda uninstall pytorch torchvision torchaudio 
conda install pytorch==2.2.2 torchvision==0.17.2 torchaudio==2.2.2 pytorch-cuda=11.8 -c pytorch -c nvidia
