#!/usr/bin/env python3
"""
HCC数据集批量配准脚本
支持并行处理和进度监控
"""

import os
import json
import argparse
import logging
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict
import time

from liver_hcc_registration import LiverHCCRegistration

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchHCCRegistration:
    """HCC批量配准处理器"""
    
    def __init__(self, data_root: str, output_dir: str):
        """
        初始化批量处理器
        
        Args:
            data_root: 数据根目录
            output_dir: 输出目录
        """
        self.data_root = data_root
        self.output_dir = output_dir
        self.config = {
            "data_root": data_root,
            "output_dir": output_dir,
            "reference_sequence": "ap",
            "sequences": ["ap", "pp", "hbp"],
            "registration_params": {
                "model": "unigradicon",
                "io_iterations": 50,
                "io_sim": "lncc2"
            },
            "processing": {
                "max_workers": 2,  # 减少并行数以避免内存问题
                "skip_existing": True,
                "chunk_size": 10   # 分批处理
            }
        }
    
    def get_patient_chunks(self, patients: List[str], chunk_size: int) -> List[List[str]]:
        """将患者列表分成小批次"""
        chunks = []
        for i in range(0, len(patients), chunk_size):
            chunks.append(patients[i:i + chunk_size])
        return chunks
    
    def process_patient_chunk(self, patients: List[str], chunk_id: int) -> Dict:
        """处理一批患者"""
        logger.info(f"开始处理批次 {chunk_id}: {len(patients)} 个患者")
        
        # 为每个批次创建独立的配准器
        registrator = LiverHCCRegistration(self.data_root, self.output_dir)
        registrator.registration_params.update(self.config["registration_params"])
        
        chunk_results = {
            "chunk_id": chunk_id,
            "patients": patients,
            "results": [],
            "successful": 0,
            "failed": 0,
            "start_time": time.time()
        }
        
        for patient_name in patients:
            try:
                # 检查是否跳过已存在的结果
                if self.config["processing"]["skip_existing"]:
                    patient_output_dir = Path(self.output_dir) / patient_name
                    results_file = patient_output_dir / f'{patient_name}_registration_results.json'
                    if results_file.exists():
                        logger.info(f"跳过已处理的患者: {patient_name}")
                        chunk_results["results"].append({
                            "patient_name": patient_name,
                            "status": "skipped"
                        })
                        continue
                
                # 处理患者
                result = registrator.register_patient(
                    patient_name, 
                    self.config["reference_sequence"]
                )
                
                chunk_results["results"].append(result)
                
                if result["status"] in ["success", "partial"]:
                    chunk_results["successful"] += 1
                else:
                    chunk_results["failed"] += 1
                    
            except Exception as e:
                logger.error(f"患者 {patient_name} 处理异常: {e}")
                chunk_results["results"].append({
                    "patient_name": patient_name,
                    "status": "error",
                    "message": str(e)
                })
                chunk_results["failed"] += 1
        
        chunk_results["processing_time"] = time.time() - chunk_results["start_time"]
        logger.info(f"批次 {chunk_id} 完成: 成功 {chunk_results['successful']}, 失败 {chunk_results['failed']}")
        
        return chunk_results
    
    def run_parallel_processing(self) -> Dict:
        """运行并行批量处理"""
        # 获取患者列表
        registrator = LiverHCCRegistration(self.data_root, self.output_dir)
        patients = registrator.get_patient_list()
        
        if not patients:
            return {"status": "failed", "message": "未找到患者数据"}
        
        logger.info(f"找到 {len(patients)} 个患者，开始批量处理")
        
        # 创建输出目录
        output_dir = Path(self.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 分批处理
        chunk_size = self.config["processing"]["chunk_size"]
        patient_chunks = self.get_patient_chunks(patients, chunk_size)
        
        logger.info(f"分为 {len(patient_chunks)} 个批次，每批次 {chunk_size} 个患者")
        
        # 批量处理结果
        batch_results = {
            "total_patients": len(patients),
            "total_chunks": len(patient_chunks),
            "processed_patients": 0,
            "successful_patients": 0,
            "failed_patients": 0,
            "skipped_patients": 0,
            "chunk_results": [],
            "start_time": time.time(),
            "config": self.config
        }
        
        # 并行处理批次
        max_workers = self.config["processing"]["max_workers"]
        
        if max_workers > 1 and len(patient_chunks) > 1:
            logger.info(f"使用 {max_workers} 个进程并行处理")
            
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # 提交任务
                future_to_chunk = {
                    executor.submit(self.process_patient_chunk, chunk, i): i
                    for i, chunk in enumerate(patient_chunks)
                }
                
                # 收集结果
                for future in as_completed(future_to_chunk):
                    chunk_id = future_to_chunk[future]
                    try:
                        chunk_result = future.result()
                        batch_results["chunk_results"].append(chunk_result)
                        
                        # 更新统计
                        for result in chunk_result["results"]:
                            batch_results["processed_patients"] += 1
                            if result["status"] in ["success", "partial"]:
                                batch_results["successful_patients"] += 1
                            elif result["status"] == "skipped":
                                batch_results["skipped_patients"] += 1
                            else:
                                batch_results["failed_patients"] += 1
                        
                        logger.info(f"批次 {chunk_id} 完成，总进度: {batch_results['processed_patients']}/{batch_results['total_patients']}")
                        
                    except Exception as e:
                        logger.error(f"批次 {chunk_id} 处理异常: {e}")
        
        else:
            # 串行处理
            logger.info("串行处理模式")
            
            for i, chunk in enumerate(patient_chunks):
                chunk_result = self.process_patient_chunk(chunk, i)
                batch_results["chunk_results"].append(chunk_result)
                
                # 更新统计
                for result in chunk_result["results"]:
                    batch_results["processed_patients"] += 1
                    if result["status"] in ["success", "partial"]:
                        batch_results["successful_patients"] += 1
                    elif result["status"] == "skipped":
                        batch_results["skipped_patients"] += 1
                    else:
                        batch_results["failed_patients"] += 1
                
                logger.info(f"进度: {batch_results['processed_patients']}/{batch_results['total_patients']}")
        
        # 计算总处理时间
        batch_results["total_time"] = time.time() - batch_results["start_time"]
        
        # 保存批量处理结果
        batch_results_file = output_dir / "batch_hcc_results.json"
        with open(batch_results_file, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False)
        
        # 打印总结
        self.print_summary(batch_results)
        
        return batch_results
    
    def print_summary(self, batch_results: Dict):
        """打印处理总结"""
        print("\n" + "="*60)
        print("HCC批量配准完成!")
        print("="*60)
        print(f"总患者数: {batch_results['total_patients']}")
        print(f"成功处理: {batch_results['successful_patients']}")
        print(f"处理失败: {batch_results['failed_patients']}")
        print(f"跳过处理: {batch_results['skipped_patients']}")
        
        total_processed = batch_results['successful_patients'] + batch_results['failed_patients']
        if total_processed > 0:
            success_rate = batch_results['successful_patients'] / total_processed
            print(f"成功率: {success_rate:.1%}")
        
        print(f"总处理时间: {batch_results['total_time']:.1f} 秒")
        if batch_results['total_patients'] > 0:
            avg_time = batch_results['total_time'] / batch_results['total_patients']
            print(f"平均处理时间: {avg_time:.1f} 秒/患者")
        
        print(f"参考序列: {batch_results['config']['reference_sequence']}")
        print(f"输出目录: {batch_results['config']['output_dir']}")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='HCC批量肝脏多序列MRI配准')
    parser.add_argument('--data_root', type=str, 
                       default='/root/autodl-tmp/120HCC/image',
                       help='HCC数据根目录')
    parser.add_argument('--output_dir', type=str, 
                       default='/root/autodl-tmp/3d MRI配准/batch_results',
                       help='输出目录')
    parser.add_argument('--reference_sequence', type=str, default='ap',
                       choices=['ap', 'pp', 'hbp'],
                       help='参考序列')
    parser.add_argument('--max_workers', type=int, default=2,
                       help='最大并行工作进程数')
    parser.add_argument('--chunk_size', type=int, default=10,
                       help='每批次处理的患者数')
    parser.add_argument('--model', type=str, default='unigradicon',
                       choices=['unigradicon', 'multigradicon'],
                       help='使用的模型')
    parser.add_argument('--io_iterations', type=int, default=50,
                       help='实例优化迭代次数')
    parser.add_argument('--similarity', type=str, default='lncc2',
                       choices=['lncc', 'lncc2', 'mind'],
                       help='相似性度量')
    parser.add_argument('--skip_existing', action='store_true', default=True,
                       help='跳过已存在的结果')
    
    args = parser.parse_args()
    
    # 创建批量处理器
    batch_processor = BatchHCCRegistration(args.data_root, args.output_dir)
    
    # 更新配置
    batch_processor.config.update({
        "reference_sequence": args.reference_sequence,
        "registration_params": {
            "model": args.model,
            "io_iterations": args.io_iterations,
            "io_sim": args.similarity
        },
        "processing": {
            "max_workers": args.max_workers,
            "skip_existing": args.skip_existing,
            "chunk_size": args.chunk_size
        }
    })
    
    # 运行批量处理
    try:
        results = batch_processor.run_parallel_processing()
        
        if results["successful_patients"] > 0:
            print(f"\n✓ 批量处理完成，成功处理 {results['successful_patients']} 个患者")
        else:
            print("\n✗ 批量处理完成，但没有成功处理任何患者")
            
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise

if __name__ == "__main__":
    main()
