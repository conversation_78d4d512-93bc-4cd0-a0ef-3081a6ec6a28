model:
  NAME: BaseSeg
  encoder_args:
    NAME: PointNet2Encoder
    in_channels: 6
    width: null
    strides: [4, 4, 4, 4]
    layers: 3
    use_res: False
    mlps: [[[32, 32, 64]],  # stage 1: 96
        [[64, 64, 128]], # stage 2: 256
        [[128, 128, 256]], # stage 3: 512
        [[256, 256, 512]]] # stage 4: 1024
    radius: 0.1
    num_samples: 32
    sampler: fps
    aggr_args:
      NAME: 'convpool'
      feature_type: 'dp_fj'
      anisotropic: False
      reduction: 'max'
    group_args:
      NAME: 'ballquery'
      use_xyz: True
    conv_args: 
      order: conv-norm-act
    act_args:
      act: 'relu'
    norm_args:
      norm: 'bn'
  decoder_args:
    NAME: PointNet2Decoder
    fp_mlps: [[128, 128, 128], [256, 128], [256, 256], [256, 256]]
  cls_args:
    NAME: SegHead
    num_classes: 20
    in_channels: null

class_weights: null
criterion_args: 
  NAME: CrossEntropy
  label_smoothing: 0.0

# training receipe borrowed from: https://github.com/yanx27/Pointnet_Pointnet2_pytorch
# lr_scheduler:
sched: 'step'
decay_epochs: 10
decay_rate: 0.7
sched_on_epoch: True
warmup_epochs: 0
min_lr: 0

# Training parameters
batch_size: 32
lr: 0.002
optimizer:
 NAME: 'adam'
 weight_decay: 1.0e-4
 betas: [0.9, 0.999]
 eps: 1.0e-8
 momentum: 0.98

grad_norm_clip: 10


# data augmentation from
# https://github.com/yanx27/Pointnet_Pointnet2_pytorch/blob/e365b9f7b9c3d7d6444278d92e298e3f078794e1/train_semseg.py#L184
datatransforms:
  train: [PointsToTensor,  PointCloudXYZAlign, PointCloudRotation]
  vote: null
  val: [PointsToTensor, PointCloudXYZAlign]
  kwargs:
    angle: [0, 0, 1]
    gravity_dim: 2

