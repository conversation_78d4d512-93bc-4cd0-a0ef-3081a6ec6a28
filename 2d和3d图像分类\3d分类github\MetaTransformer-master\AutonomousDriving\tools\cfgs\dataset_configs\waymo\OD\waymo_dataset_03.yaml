DATASET: 'WaymoDataset'
DATA_PATH: '../data/waymo'
# OSS_PATH: 's3://${PATH_TO_DATASET}/waymo_0.5.0'
PROCESSED_DATA_TAG: 'waymo_processed_data_v0_5_0'

POINT_CLOUD_RANGE: [-75.2, -75.2, -2, 75.2, 75.2, 4]

DATA_SPLIT: {
    'train': train,
    'test': val
}

SAMPLED_INTERVAL: {
    'train': 32,
    'test': 5
}

FILTER_EMPTY_BOXES_FOR_TRAIN: True
DISABLE_NLZ_FLAG_ON_POINTS: True

USE_SHARED_MEMORY: False  # it will load the data to shared memory to speed up (DO NOT USE IT IF YOU DO NOT FULLY UNDERSTAND WHAT WILL HAPPEN)
SHARED_MEMORY_FILE_LIMIT: 35000  # set it based on the size of your shared memory

DATA_AUGMENTOR:
    DISABLE_AUG_LIST: ['placeholder']
    AUG_CONFIG_LIST:
        - NAME: gt_sampling
          USE_ROAD_PLANE: False
          DB_INFO_PATH:
              - waymo_processed_data_v0_5_0_waymo_dbinfos_train_sampled_1.pkl

          USE_SHARED_MEMORY: False  # set it to True to speed up (it costs about 15GB shared memory)
          DB_DATA_PATH:
              - waymo_processed_data_v0_5_0_gt_database_train_sampled_1_global.npy

          PREPARE: {
             filter_by_min_points: ['Vehicle:5', 'Pedestrian:5', 'Cyclist:5'],
             filter_by_difficulty: [-1],
          }

          SAMPLE_GROUPS: ['Vehicle:15', 'Pedestrian:10', 'Cyclist:10']
          NUM_POINT_FEATURES: 5
          REMOVE_EXTRA_WIDTH: [0.0, 0.0, 0.0]
          LIMIT_WHOLE_SCENE: True

        - NAME: random_world_flip
          ALONG_AXIS_LIST: ['x', 'y']

        - NAME: random_world_rotation
          WORLD_ROT_ANGLE: [-0.78539816, 0.78539816]

        - NAME: random_world_scaling
          WORLD_SCALE_RANGE: [0.95, 1.05]


POINT_FEATURE_ENCODING: {
    encoding_type: absolute_coordinates_encoding,
    used_feature_list: ['x', 'y', 'z', 'intensity', 'elongation'],
    src_feature_list: ['x', 'y', 'z', 'intensity', 'elongation'],
}


DATA_PROCESSOR:
    - NAME: mask_points_and_boxes_outside_range
      REMOVE_OUTSIDE_BOXES: True

    - NAME: shuffle_points
      SHUFFLE_ENABLED: {
        'train': True,
        'test': True
      }

    - NAME: transform_points_to_voxels
      VOXEL_SIZE: [0.1, 0.1, 0.15]
      MAX_POINTS_PER_VOXEL: 5
      MAX_NUMBER_OF_VOXELS: {
        'train': 150000,
        'test': 150000
      }