#!/usr/bin/env python
# coding: utf-8
#%% 2dCNN-Vit 多任务学习代码,成功
import torch
print(torch.__version__)
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
# get_ipython().run_line_magic('matplotlib', 'inline')
import torchvision
import glob
from torchvision import transforms
from torch.utils import data
from PIL import Image
import torchvision.models as models
import timm
import os
import pandas as pd

#%%
def get_file_list(directory):
    file_list = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.jpg'):
                file_list.append(os.path.join(root, file))
    file_list.sort()  # 按照文件名排序
    return file_list

def get_label_list(file_path):   
    label_file = pd.read_excel(file_path)    
    name_values = label_file['name'].tolist()# 从'name'和'VETC'列中获取数值
    label_values1 = label_file['MVI'].tolist()   
    label_values2 = label_file['VETC'].tolist()
    sorted_label_values1 = [label for _, label in sorted(zip(name_values, label_values1))] # 根据'name'列对数值进行排序 
    sorted_label_values2 = [label for _, label in sorted(zip(name_values, label_values2))]
    label_list1 = sorted_label_values1  # 将排序后的VETC数值存储在label_list中
    label_list2 = sorted_label_values2     
    return label_list1, label_list2

train_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\train"
test_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\test"
train_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\train30.xlsx"
test_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\test.xlsx"

train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5]);# 打印标签列表
print(len(train_image_list));print(len(test_image_list))

# 调用函数获取标签列表
train_label_list1 = get_label_list(train_file_path)[0]
train_label_list2 = get_label_list(train_file_path)[1]
test_label_list1 = get_label_list(test_file_path)[0]
test_label_list2 = get_label_list(test_file_path)[1]
print(train_label_list1[:5]);print(train_label_list2[:5])
print(len(train_label_list1));print(len(test_label_list1))

# 打印类别分布情况
print("Task 1 class distribution:", np.unique(train_label_list1, return_counts=True))
print("Task 2 class distribution:", np.unique(train_label_list2, return_counts=True))

# In[14]:

transforms = transforms.Compose([
    transforms.Resize((96, 96)),
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.RandomRotation(10),
    transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),
    transforms.ColorJitter(brightness=0.2, contrast=0.2),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# In[15]:
class Multi_output_dataset(data.Dataset):
    def __init__(self, imgs_path, label1, label2):
        self.imgs_path = imgs_path
        self.label1 = label1
        self.label2 = label2

    def __getitem__(self, index):
        img_path = self.imgs_path[index]
        pil_img = Image.open(img_path)
        pil_img = pil_img.convert("RGB")
        pil_img = transforms(pil_img)
        label_1 = self.label1[index]
        label_2 = self.label2[index]
        return pil_img, (label_1, label_2)
    
    def __len__(self):
        return len(self.imgs_path)

# In[16]:
train_dataset = Multi_output_dataset(train_image_list, train_label_list1, train_label_list2)
test_dataset = Multi_output_dataset(test_image_list, test_label_list1, test_label_list2)
print(len(train_dataset), len(test_dataset))

# In[18]:

BTACH_SIZE = 32

# In[19]:

train_dl = torch.utils.data.DataLoader(train_dataset,batch_size=BTACH_SIZE,shuffle=True,num_workers=0, pin_memory=True)

test_dl = torch.utils.data.DataLoader(test_dataset,batch_size=BTACH_SIZE,shuffle=False, num_workers=0, pin_memory=True)

imgs, labels = next(iter(train_dl))

# In[22]:

imgs.shape

labels

# In[24]:
im = imgs[0].permute(1, 2, 0).numpy()

labels[0][0]
labels[1][0]

plt.imshow(im)

# In[28]:

# class Net(nn.Module):
#     def __init__(self, num_classes1, num_classes2):
#         super(Net, self).__init__()
#         resnet50 = models.resnet50(pretrained=True)
#         # Remove the final fully connected layer of ResNet50
#         self.features = nn.Sequential(*list(resnet50.children())[:-1])
#         # Add custom fully connected layers
#         self.fc1 = nn.Linear(2048, num_classes1)
#         self.fc2 = nn.Linear(2048, num_classes2)

#     def forward(self, x):
#         x = self.features(x)
#         x = x.view(x.size(0), -1)
#         x1 = self.fc1(x)
#         x2 = self.fc2(x)
#         return x1, x2

# # Device and model instantiation
# device = "cuda" if torch.cuda.is_available() else "cpu"
# print("Using {} device".format(device))

# model = Net(num_classes1=3, num_classes2=4).to(device)

#%% torchvision系列模型
class CustomModel(nn.Module):
    def __init__(self, model, num_classes1, num_classes2):
        super(CustomModel, self).__init__()
        self.model = model
        self.remove_final_fc_layer()
        in_features = self.get_input_features()
        
        # 为每个任务添加独立的特征提取层
        self.task1_layers = nn.Sequential(
            nn.Linear(in_features, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, num_classes1)
        )
        
        self.task2_layers = nn.Sequential(
            nn.Linear(in_features, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, num_classes2)
        )
        
        # 添加任务权重参数
        self.task1_weight = nn.Parameter(torch.FloatTensor([1.0]))
        self.task2_weight = nn.Parameter(torch.FloatTensor([1.0]))

    def forward(self, x):
        x = self.model(x)
        x = x.view(x.size(0), -1)
        x1 = self.task1_layers(x)
        x2 = self.task2_layers(x)
        return x1, x2
    def remove_final_fc_layer(self):
        # Remove the final fully connected layer of the model
        if hasattr(self.model, 'fc'):
            self.model = nn.Sequential(*list(self.model.children())[:-1])
    def get_input_features(self):
        # Function to dynamically determine the number of input features
        with torch.no_grad():
            x = torch.randn(1, 3, 224, 224)  # Assumes input size 3x224x224, modify if necessary
            features = self.model(x)
            return features.view(features.size(0), -1).size(1)

# Device and model instantiation
device = "cuda" if torch.cuda.is_available() else "cpu"
print("Using {} device".format(device))

pretrained_model = models.resnet50(pretrained=True)
# pretrained_model = models.densenet121(pretrained=True)
# pretrained_model = models.swin_v2_b(pretrained=True)
# pretrained_model = models.vit_b_32(pretrained=True)#vit还有vit_b_32，vit_l_16，vit_l_32
# pretrained_model = models.vgg19 (pretrained=True)
# pretrained_model = models.efficientnet_b1(pretrained=True)

model = CustomModel(pretrained_model, num_classes1=2, num_classes2=2).to(device)

print(model)

#%%timm系列模型
# import timm
# from pprint import pprint
# model_names = timm.list_models(pretrained=True)
# pprint(model_names)

# #用于学术资源加速
# import subprocess
# import os
# result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
# output = result.stdout
# for line in output.splitlines():
#     if '=' in line:
#         var, value = line.split('=', 1)
#         os.environ[var] = value
# # unset http_proxy && unset https_proxy #取消学术加速

# pretrained_model = timm.create_model('convit_base', pretrained=True)
# # pretrained_model = timm.create_model('deit_base_distilled_patch16_224', pretrained=True)
# # pretrained_model = timm.create_model('twins_svt_small', pretrained=True)
# print(pretrained_model)

# # pretrained_model = timm.create_model('convit_base', pretrained=False)
# # checkpoint = torch.hub.load_state_dict_from_url(
# #     url="https://dl.fbaipublicfiles.com/convit/convit_base.pth",
# #     map_location="cpu", check_hash=True
# # )
# # pretrained_model.load_state_dict(checkpoint)

# class CustomModel(nn.Module):
#     def __init__(self, model, num_classes1, num_classes2):
#         super(CustomModel, self).__init__()
#         self.model = model
#         in_features = self.model.head.in_features
#         self.remove_final_fc_layer()        
#         self.fc1 = nn.Linear(in_features, num_classes1)
#         self.fc2 = nn.Linear(in_features, num_classes2)
    
#     def forward(self, x):
#         x = self.model(x)        
#         x = x.view(x.size(0), -1)
#         x1 = self.fc1(x)
#         x2 = self.fc2(x)
#         return x1, x2
    
#     def remove_final_fc_layer(self):
#         # Remove the final fully connected layer of the model
#         self.model.head = nn.Identity()

# # Device and model instantiation
# device = "cuda" if torch.cuda.is_available() else "cpu"
# print("Using {} device".format(device))

# model = CustomModel(pretrained_model, num_classes1=2, num_classes2=2).to(device)
# print(model)


#%%此处使用训练代码1

# loss_fn = nn.CrossEntropyLoss()
# optimizer = torch.optim.AdamW(model.parameters(), lr=0.00001, weight_decay=1e-5)  # weight_decay 为 L2 正则化参数
# scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)


# def train(dataloader, model, loss_fn, optimizer, scheduler):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     train_loss, correct1, correct2 = 0, 0, 0
#     model.train()
#     for X, y in dataloader:
#         y1 = y[0]
#         y2 = y[1]
#         X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
#         # Compute prediction error
#         pred = model(X)
#         loss1 = loss_fn(pred[0], y1)
#         loss2 = loss_fn(pred[1], y2)
#         loss = loss1 + loss2

#         # Backpropagation
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
        
#         with torch.no_grad():
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
#             train_loss += loss.item()
    
#     train_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
    
#     # 更新学习率
#     scheduler.step(train_loss)  # 使用训练损失来更新学习率
    
#     return train_loss, correct1, correct2

# # 测试代码
# def test(dataloader, model):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     model.eval()
#     test_loss, correct1, correct2 = 0, 0, 0
#     with torch.no_grad():
#         for X, y in dataloader:
#             y1 = y[0]
#             y2 = y[1]
#             X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
#             pred = model(X)
#             loss1 = loss_fn(pred[0], y1)
#             loss2 = loss_fn(pred[1], y2)
#             loss = loss1 + loss2
#             test_loss += loss.item()
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
    
#     test_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
#     return test_loss, correct1, correct2


#%%根据类别权重训练代码2
# 计算类别权重
# class_counts = [len(train_label_list1), len(train_label_list2)]  # 假设您有两个类别的样本数量
# class_weights = [sum(class_counts) / count for count in class_counts]
# class_weights = torch.FloatTensor(class_weights).to(device)

# # 在损失函数中使用类别权重
# loss_fn = nn.CrossEntropyLoss(weight=class_weights)
# optimizer = torch.optim.AdamW(model.parameters(), lr=0.00001, weight_decay=1e-5)  # weight_decay 为 L2 正则化参数
# scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)

# # 计算每个任务的类别权重
# def calculate_class_weights(labels):
#     # 统计每个类别的样本数量
#     unique_labels, counts = np.unique(labels, return_counts=True)
#     total_samples = sum(counts)
#     # 计算权重：样本数量越少，权重越大
#     weights = total_samples / (len(unique_labels) * counts)
#     # 归一化权重
#     weights = weights / weights.sum()
#     return torch.FloatTensor(weights).to(device)

# # 计算两个任务的类别权重
# weights1 = calculate_class_weights(train_label_list1)
# weights2 = calculate_class_weights(train_label_list2)

# # 创建两个带权重的损失函数
# loss_fn1 = nn.CrossEntropyLoss(weight=weights1)
# loss_fn2 = nn.CrossEntropyLoss(weight=weights2)

# # 在训练循环中使用
# def train(dataloader, model, loss_fn1, loss_fn2, optimizer, scheduler):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     train_loss, correct1, correct2 = 0, 0, 0
#     model.train()
#     for X, y in dataloader:
#         y1 = y[0]
#         y2 = y[1]
#         X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
        
#         # Compute prediction error
#         pred = model(X)
#         # 使用各自的权重损失函数
#         loss1 = loss_fn1(pred[0], y1)
#         loss2 = loss_fn2(pred[1], y2)
#         # 可以调整两个任务的权重
#         loss = loss1 + loss2  # 或者使用不同的权重，如: 0.5 * loss1 + 0.5 * loss2

#         # Backpropagation
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
        
#         with torch.no_grad():
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
#             train_loss += loss.item()
    
#     train_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
    
#     # 更新学习率
#     scheduler.step(train_loss)
    
#     return train_loss, correct1, correct2

# # 测试函数也需要相应修改
# def test(dataloader, model, loss_fn1, loss_fn2):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     model.eval()
#     test_loss, correct1, correct2 = 0, 0, 0
#     with torch.no_grad():
#         for X, y in dataloader:
#             y1 = y[0]
#             y2 = y[1]
#             X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
#             pred = model(X)
#             loss1 = loss_fn1(pred[0], y1)
#             loss2 = loss_fn2(pred[1], y2)
#             loss = loss1 + loss2
#             test_loss += loss.item()
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
    
#     test_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
#     return test_loss, correct1, correct2

# epochs = 100

# train_loss = []
# train_acc1 = []
# train_acc2 = []
# test_loss = []
# test_acc1 = []
# test_acc2 = []

# for epoch in range(epochs):
#     epoch_loss, epoch_acc1, epoch_acc2 = train(train_dl, model, loss_fn1, loss_fn2, optimizer, scheduler)
#     epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model,loss_fn1, loss_fn2)
#     train_loss.append(epoch_loss)
#     train_acc1.append(epoch_acc1)
#     train_acc2.append(epoch_acc2)
#     test_loss.append(epoch_test_loss)
#     test_acc1.append(epoch_test_acc1)
#     test_acc2.append(epoch_test_acc2)
    
#     template = ("epoch:{:2d}, train_loss: {:.5f}, train_acc1: {:.1f}% , train_acc2: {:.1f}% ," 
#                 "test_loss: {:.5f}, test_acc1: {:.1f}%, test_acc2: {:.1f}%")
#     print(template.format(
#           epoch, epoch_loss, epoch_acc1*100, epoch_acc2*100, 
#          epoch_test_loss, epoch_test_acc1*100, epoch_test_acc2*100))
    
# print("Done!")

#%%训练代码3
# 添加自动调参功能
class AutoMLConfig:
    def __init__(self, dataset_size, input_shape):
        self.dataset_size = dataset_size
        self.input_shape = input_shape
        
    def get_optimal_batch_size(self):
        # 根据数据集大小和GPU内存自动选择batch size
        if self.dataset_size < 1000:
            return 16
        elif self.dataset_size < 5000:
            return 32
        else:
            return 64
            
    def get_optimal_learning_rate(self):
        # 根据模型规模自动选择学习率范围
        return [1e-5, 1e-3]
        
    def get_optimal_augmentation(self):
        # 根据数据集特征自动选择增强策略
        return {
            'rotation_range': [-20, 20],
            'flip_prob': 0.5,
            'brightness_range': [0.8, 1.2]
        }

# 在训练前初始化自动调参
automl_config = AutoMLConfig(
    dataset_size=len(train_dataset),
    input_shape=(3, 96, 96)
)

# 使用自动调参配置
BATCH_SIZE = automl_config.get_optimal_batch_size()

# 添加学习率自动搜索
from torch.optim.lr_scheduler import CyclicLR
lr_range = automl_config.get_optimal_learning_rate()
scheduler = CyclicLR(
    optimizer,
    base_lr=lr_range[0],
    max_lr=lr_range[1],
    cycle_momentum=False
)

# 动态权重平衡
class DynamicWeightBalancer:
    def __init__(self, num_tasks):
        self.num_tasks = num_tasks
        self.task_weights = torch.ones(num_tasks) / num_tasks
        
    def update(self, losses):
        # 根据各任务的loss动态调整权重
        weights = F.softmax(torch.tensor(losses), dim=0)
        self.task_weights = 0.9 * self.task_weights + 0.1 * weights
        return self.task_weights

weight_balancer = DynamicWeightBalancer(num_tasks=2)

# 损失函数和优化器设置
loss_fn = nn.CrossEntropyLoss(reduction='none')  # 使用reduction='none'以便应用样本权重
optimizer = torch.optim.AdamW(model.parameters(), lr=0.000001, weight_decay=0.01)

# 在训练循环中使用动态权重
def train(dataloader, model, loss_fn, optimizer, scheduler):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    train_loss, correct1, correct2 = 0, 0, 0
    model.train()
    
    for X, y in dataloader:
        y1, y2 = y[0], y[1]
        X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
        
        # 计算预测和损失
        pred1, pred2 = model(X)
        
        # 计算每个样本的损失
        loss1 = loss_fn(pred1, y1)
        loss2 = loss_fn(pred2, y2)
        
        # 使用动态任务权重
        task1_weight = torch.exp(model.task1_weight)
        task2_weight = torch.exp(model.task2_weight)
        total_weight = task1_weight + task2_weight
        
        # 归一化权重
        task1_weight = task1_weight / total_weight
        task2_weight = task2_weight / total_weight
        
        # 组合损失
        loss = task1_weight * loss1.mean() + task2_weight * loss2.mean()
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 统计
        with torch.no_grad():
            correct1 += (pred1.argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred2.argmax(1) == y2).type(torch.float).sum().item()
            train_loss += loss.item()
    
    # 更新学习率
    scheduler.step(train_loss)
    
    return train_loss/num_batches, correct1/size, correct2/size

def test(dataloader, model, loss_fn):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss, correct1, correct2 = 0, 0, 0
    
    with torch.no_grad():
        for X, y in dataloader:
            y1, y2 = y[0], y[1]
            X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
            
            # 计算预测
            pred1, pred2 = model(X)
            
            # 计算每个样本的损失
            loss1 = loss_fn(pred1, y1)
            loss2 = loss_fn(pred2, y2)
            
            # 使用动态任务权重
            task1_weight = torch.exp(model.task1_weight)
            task2_weight = torch.exp(model.task2_weight)
            total_weight = task1_weight + task2_weight
            
            # 归一化权重
            task1_weight = task1_weight / total_weight
            task2_weight = task2_weight / total_weight
            
            # 组合损失
            loss = task1_weight * loss1.mean() + task2_weight * loss2.mean()
            
            # 统计
            test_loss += loss.item()
            correct1 += (pred1.argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred2.argmax(1) == y2).type(torch.float).sum().item()
    
    test_loss /= num_batches
    correct1 /= size
    correct2 /= size
    return test_loss, correct1, correct2

# In[36]:
epochs = 300

train_loss = []
train_acc1 = []
train_acc2 = []
test_loss = []
test_acc1 = []
test_acc2 = []

for epoch in range(epochs):
    epoch_loss, epoch_acc1, epoch_acc2 = train(train_dl, model, loss_fn, optimizer, scheduler)
    epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model,loss_fn)
    train_loss.append(epoch_loss)
    train_acc1.append(epoch_acc1)
    train_acc2.append(epoch_acc2)
    test_loss.append(epoch_test_loss)
    test_acc1.append(epoch_test_acc1)
    test_acc2.append(epoch_test_acc2)
    
    template = ("epoch:{:2d}, train_loss: {:.5f}, train_acc1: {:.1f}% , train_acc2: {:.1f}% ," 
                "test_loss: {:.5f}, test_acc1: {:.1f}%, test_acc2: {:.1f}%")
    print(template.format(
          epoch, epoch_loss, epoch_acc1*100, epoch_acc2*100, 
         epoch_test_loss, epoch_test_acc1*100, epoch_test_acc2*100))
    
print("Done!")

# In[38]:
plt.plot(range(1, epochs+1), train_loss, label='train_loss')
plt.plot(range(1, epochs+1), test_loss, label='test_loss')
plt.legend()

#%%绘制acc曲线
# plt.plot(range(1, len(train_loss)+1), train_acc1, label='train_VETC_acc')
# plt.plot(range(1, len(train_loss)+1), test_acc1 , label='test_VETC_acc')
plt.plot(range(1, len(train_loss)+1), train_acc2 , label='train_immune score_acc')
plt.plot(range(1, len(train_loss)+1), test_acc2 , label='test_immune score_acc')
plt.legend(loc='lower right', fontsize='small', markerscale=0.1) #x-small
plt.title("ConViT") #ResNet50,DenseNet121,Inception_v3,SqueezeNet_v1.0
plt.rcParams['font.size'] = 16
plt.show()

# In[39]:
# epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model)
# print(epoch_test_loss, epoch_test_acc1, epoch_test_acc2)

#%%保存模型权重
PATH = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\resnet50.pth"   
torch.save(model.state_dict(), PATH)

#%%保存图片路径、预测概率、真实的label、预测label
import torch
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix

new_train_dl = torch.utils.data.DataLoader(train_dataset, batch_size=16, shuffle=False)

train_image_paths = [train_dataset.dataset.imgs_path[i] for i in train_dataset.indices]
test_image_paths = [test_dataset.dataset.imgs_path[i] for i in test_dataset.indices]

def get_predictions(model, dataloader, imgs_path):
    model.eval()
    image_paths = []
    true_label1 = []
    true_label2 = []
    predict_label1 = []
    predict_label2 = []
    predict_prob1 = []  # 存储预测概率1
    predict_prob2 = []  # 存储预测概率2
    for x, (y1, y2) in dataloader:
        if torch.cuda.is_available():
            x = x.to('cuda')
            y1 = y1.to('cuda')
            y2 = y2.to('cuda')
        y_pred1, y_pred2 = model(x)
        _, y_pred_class1 = torch.max(y_pred1, dim=1)
        _, y_pred_class2 = torch.max(y_pred2, dim=1)
        # 获取预测概率
        y_pred_prob1 = torch.softmax(y_pred1, dim=1)
        y_pred_prob2 = torch.softmax(y_pred2, dim=1)
        # 清空image_paths列表
        image_paths.clear()
        image_paths.extend(imgs_path)
        true_label1.extend(y1.tolist())
        true_label2.extend(y2.tolist())
        predict_label1.extend(y_pred_class1.tolist())
        predict_label2.extend(y_pred_class2.tolist())
        predict_prob1.extend(y_pred_prob1.tolist())
        predict_prob2.extend(y_pred_prob2.tolist())
    return image_paths, true_label1, true_label2, predict_label1, predict_label2, predict_prob1, predict_prob2

train_image_paths, train_true_label1, train_true_label2, train_predict_label1, train_predict_label2, train_predict_prob1, train_predict_prob2 = get_predictions(model, new_train_dl, train_image_paths)
test_image_paths, test_true_label1, test_true_label2, test_predict_label1, test_predict_label2, test_predict_prob1, test_predict_prob2 = get_predictions(model, test_dl, test_image_paths)

train_data = {'Image Path': train_image_paths, 'True Label1': train_true_label1, 'Predict Label1': train_predict_label1, 'True Label2': train_true_label2, 'Predict Label2': train_predict_label2, 'Predict Prob1': train_predict_prob1, 'Predict Prob2': train_predict_prob2}
test_data = {'Image Path': test_image_paths, 'True Label1': test_true_label1, 'Predict Label1': test_predict_label1, 'True Label2': test_true_label2, 'Predict Label2': test_predict_label2, 'Predict Prob1': test_predict_prob1, 'Predict Prob2': test_predict_prob2}

train_results = pd.DataFrame(train_data)
test_results = pd.DataFrame(test_data)

train_results.to_excel(r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\train.xlsx", index=False)
test_results.to_excel(r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\test.xlsx", index=False)

