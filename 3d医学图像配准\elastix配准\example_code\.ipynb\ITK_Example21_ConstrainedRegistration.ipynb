{"cells": [{"cell_type": "markdown", "id": "9e396956", "metadata": {}, "source": ["This is an example notebook on how to contrain the registration in respect to specific tranformation parameters using `Scales`. The example involves an Euler Transform (=Rigid Transform) where the translation along one axis is constrained."]}, {"cell_type": "code", "execution_count": 1, "id": "00f5c930", "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "32b06b87", "metadata": {}, "source": ["### Create a synthetic pair of images"]}, {"cell_type": "code", "execution_count": 2, "id": "7960a301", "metadata": {}, "outputs": [], "source": ["fixed_image = np.zeros((100, 100), dtype=np.float32)\n", "fixed_image[25:75, 25:75] = 1\n", "fixed_image = itk.image_from_array(fixed_image)"]}, {"cell_type": "code", "execution_count": 3, "id": "4c782c2c", "metadata": {}, "outputs": [], "source": ["translation = [20, -15]\n", "transform = itk.Euler2DTransform.New()\n", "transform.SetOffset(translation)\n", "transform.SetAngleInDegrees(15)\n", "transform.SetCenter([50, 50])\n", "\n", "parameter_map = {\n", "                 \"Direction\": (\"1\", \"0\", \"0\", \"1\"),\n", "                 \"Index\": (\"0\", \"0\"),\n", "                 \"Origin\": (\"0\", \"0\"),\n", "                 \"Size\": (\"100\", \"100\"),\n", "                 \"Spacing\": (\"1\", \"1\"),\n", "                 \"FinalBSplineInterpolationOrder\": (\"0\")\n", "                }"]}, {"cell_type": "code", "execution_count": 4, "id": "b9c9ecdf", "metadata": {}, "outputs": [], "source": ["transform_parameter_object = itk.ParameterObject.New()\n", "transform_parameter_object.AddParameterMap(parameter_map)\n", "\n", "transformix_filter = itk.TransformixFilter.New(fixed_image)\n", "transformix_filter.SetMovingImage(fixed_image)\n", "transformix_filter.SetTransformParameterObject(transform_parameter_object)\n", "transformix_filter.SetTransform(transform)\n", "transformix_filter.Update()\n", "\n", "moving_image = transformix_filter.GetOutput()"]}, {"cell_type": "code", "execution_count": 5, "id": "d214ccd7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def show_images(images):\n", "    labels = ['fixed', 'moving', 'registered']\n", "    for i, image in enumerate(images):\n", "        plt.subplot(1, len(images), i+1)\n", "        plt.imshow(image, cmap='gray')\n", "        plt.title(labels[i])\n", "        plt.axis('off')\n", "    plt.show()\n", "\n", "show_images([fixed_image, moving_image])"]}, {"cell_type": "markdown", "id": "1b05a1b1", "metadata": {}, "source": ["### Registration 1: Unconstrained"]}, {"cell_type": "markdown", "id": "12b0533e", "metadata": {}, "source": ["This first registration is unconstrained, and serves the purpose of having a baseline to compare the contrained registration that follows."]}, {"cell_type": "code", "execution_count": 6, "id": "ee080861", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterObject (00000230A4E7FC00)\n", "  RTTI typeinfo:   class elastix::ParameterObject\n", "  Reference Count: 1\n", "  Modified Time: 875\n", "  Debug: Off\n", "  Object Name: \n", "  Observers: \n", "    none\n", "ParameterMap 0: \n", "  (AutomaticParameterEstimation \"true\")\n", "  (AutomaticScalesEstimation \"true\")\n", "  (CheckNumberOfSamples \"true\")\n", "  (DefaultPixelValue 0)\n", "  (FinalBSplineInterpolationOrder 0)\n", "  (FixedImagePyramid \"FixedSmoothingImagePyramid\")\n", "  (ImageSampler \"RandomCoordinate\")\n", "  (Interpolator \"LinearInterpolator\")\n", "  (MaximumNumberOfIterations 256)\n", "  (MaximumNumberOfSamplingAttempts 8)\n", "  (Metric \"AdvancedMattesMutualInformation\")\n", "  (MovingImagePyramid \"MovingSmoothingImagePyramid\")\n", "  (NewSamplesEveryIteration \"true\")\n", "  (NumberOfResolutions 4)\n", "  (NumberOfSamplesForExactGradient 4096)\n", "  (NumberOfSpatialSamples 2048)\n", "  (Optimizer \"AdaptiveStochasticGradientDescent\")\n", "  (Registration \"MultiResolutionRegistration\")\n", "  (ResampleInterpolator \"FinalBSplineInterpolator\")\n", "  (Resampler \"DefaultResampler\")\n", "  (ResultImageFormat \"nii\")\n", "  (Transform \"EulerTransform\")\n", "  (WriteIterationInfo \"false\")\n", "  (WriteResultImage \"true\")\n", "\n"]}], "source": ["rigid_parameter_object = itk.ParameterObject.New()\n", "rigid_parameter_map = rigid_parameter_object.GetDefaultParameterMap('rigid')\n", "rigid_parameter_map['FinalBSplineInterpolationOrder'] = ('0')\n", "rigid_parameter_object.AddParameterMap(rigid_parameter_map)\n", "\n", "print(rigid_parameter_object)"]}, {"cell_type": "code", "execution_count": 7, "id": "c9ffda6e", "metadata": {}, "outputs": [], "source": ["unconstrained_result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=rigid_parameter_object,\n", "    log_to_console=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "a9474430", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgQAAAC6CAYAAADRXVW6AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/d3fzzAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAal0lEQVR4nO3deVTVdf7H8ddlByEE3MAF1BpQj9toWOoUHMcidUrTNM0VTy5FZTmnmmxcEqvT4lidtFXMJtNmcrTMLMpl1CCzRQ9atqFmiZGUO7K9f3803J83qFCBLxeej3PuOd6P3+V14cOXF/d+v/e6zMwEAAAaNB+nAwAAAOdRCAAAAIUAAABQCAAAgCgEAABAFAIAACAKAQAAEIUAAACIQgAAAEQhkCStWLFCnTp1UnBwsFwulwYPHiyXy1XrOTZu3CiXy6WNGzfW+r6BX9q7d69cLpeWLFnidBTUc+c615YtW6YFCxbUSKbz5Y0/P35OB3Bafn6+xowZo5SUFC1cuFCBgYGKiYnR3Xff7XQ0wFHR0dHKyspS+/btnY6Ceu5c59qyZcuUk5OjadOm1UywBqbBF4LPP/9cxcXFGj16tC6//HL3eJs2bRxMBTgvMDBQl1xyidMxUMecPHlSISEh1brNujbXTp06peDgYKdj1LoG/ZLB+PHj1bdvX0nSiBEj5HK5lJSUpNmzZ3u8ZLBlyxb5+/vrr3/9q8f6S5Yskcvl0vPPP+8e++KLLzRq1Cg1a9ZMgYGB6tChg5588skK+/7ss8+UkpKikJAQNWnSRFOmTNGxY8dq6JHCW5TPvZ07d+q6665TeHi4IiMjdccdd6ikpER79uxRSkqKwsLCFBcXp4ceeshj/f3792v06NEe8+/RRx9VWVmZJKm4uFjNmjXTmDFjKuz7p59+UnBwsO644w5JlT/lWZ5v165dGjlypMLDw9W8eXOlpqbqyJEjFbY3ceJERUZGKjQ0VAMHDtTXX38tl8ul2bNnV+8XDjWi/Pv90UcfadiwYYqIiFD79u1lZlq4cKG6deum4OBgRUREaNiwYfr666891jcz3X///YqNjVVQUJB69uypzMxMJSUlKSkpyb1cZXMtPz9fkyZNUuvWrRUYGKimTZuqT58+eueddyRJSUlJeuONN7Rv3z65XC73rVxRUZHS09OVkJDgXn/ChAnKz8/3yBgXF6dBgwZp5cqV6t69u4KCgjRnzhxJUl5eniZPnqxWrVopICBAbdu21Zw5c1RSUuKxje+++07Dhw9XWFiYwsPDNWLECOXl5VXHt6B2WQP25Zdf2pNPPmmS7P7777esrCzbtWuXzZo1y375pXnwwQdNkq1evdrMzHJyciwkJMRGjx7tXmbXrl0WHh5unTt3tqVLl9rbb79t06dPNx8fH5s9e7Z7uby8PGvWrJm1bNnSMjIybO3atXbDDTdYmzZtTJJt2LChVh4/6p7yuRcfH29z5861zMxMu/POO02SpaWlWUJCgj3++OOWmZlpEyZMMEn26quvmpnZ999/by1btrSmTZvaU089ZevWrbO0tDSTZFOnTnXv4/bbb7fg4GA7cuSIx74XLlxokmznzp1mZpabm2uSLCMjo9J8M2fOtMzMTJs/f74FBgbahAkT3MuVlpZa3759LSgoyB588EF7++23bc6cOXbRRReZJJs1a1bNfRFRbcq/37GxsXbXXXdZZmamrVq1ym688Ubz9/e36dOn27p162zZsmWWkJBgzZs3t7y8PPf6f/vb30ySTZo0ydatW2fPPvustWnTxqKjo+3yyy93L1fZXLvyyiutadOm9swzz9jGjRtt1apVNnPmTFu+fLmZ/Xy87dOnj7Vo0cKysrLcN7Of519KSoo1atTI5syZY5mZmfbcc89Zy5YtrWPHjnby5En3fmJjYy06OtratWtnixcvtg0bNti2bdvs4MGD1rp1a4uNjbWnn37a3nnnHZs7d64FBgba+PHj3eufPHnSOnToYOHh4fbEE0/YW2+9Zbfeeqv7eH7mY6rrGnQhMDPbsGGDSbJ//etf7rHKCkFZWZkNGDDAGjdubDk5OdaxY0dLSEiw48ePu5e58sorrVWrVhUOtGlpaRYUFGQFBQVmZnbXXXeZy+WyTz75xGO5/v37UwgauPK59+ijj3qMd+vWzSTZypUr3WPFxcXWtGlTu/baa83M7O677zZJ9v7773usO3XqVHO5XLZnzx4zM9u5c6dJsmeeecZjucTEROvRo4f7/m8Vgoceeshj3ZtuusmCgoKsrKzMzMzeeOMNk2SLFi3yWO6BBx6gEHiR8u/3zJkz3WNZWVmVztFvvvnGgoOD7c477zQzs4KCAgsMDLQRI0Z4LFe+/u8VgtDQUJs2bdpv5hs4cKDFxsZWGH/55Zc9ynK5Dz74wCTZwoUL3WOxsbHm6+vr/vkoN3nyZAsNDbV9+/Z5jD/yyCMmyXbt2mVmZosWLfL4Y7HcjTfe6HWFoEG/ZHA2XC6Xli5dqrCwMPXs2VO5ubl65ZVX1KhRI0lSYWGh3n33XQ0ZMkQhISEqKSlx3wYMGKDCwkJlZ2dLkjZs2KBOnTqpa9euHvsYNWpUrT8u1E2DBg3yuN+hQwe5XC5dddVV7jE/Pz9deOGF2rdvnyRp/fr16tixoxITEz3WHT9+vMxM69evlyR17txZPXr0UEZGhnuZTz/9VNu2bVNqamqV8l199dUe97t06aLCwkJ9//33kqRNmzZJkoYPH+6x3MiRI6u0fdQtQ4cOdf97zZo1crlcGj16tMdxrkWLFuratav7Kqns7GydPn26why45JJLFBcX97v7TExM1JIlS5Senq7s7GwVFxdXOe+aNWvUuHFj/eUvf/HI2K1bN7Vo0aLClVxdunTRH/7whwrbSE5OVkxMjMc2yn8Gy+f4hg0bFBYWVuFnwhuP5xSCsxAVFaWrr75ahYWFSklJUefOnd3/d/jwYZWUlOiJJ56Qv7+/x23AgAGSpB9++MG9bIsWLSpsv7IxNEyRkZEe9wMCAhQSEqKgoKAK44WFhZJ+nlfR0dEVthUTE+P+/3KpqanKysrSZ599JknKyMhQYGBglX9hR0VFedwPDAyU9PPJWOX78vPzq/A4mjdvXqXto245c14dOnRIZqbmzZtXONZlZ2d7HOekyr/nVZkHK1as0Lhx4/Tcc8/p0ksvVWRkpMaOHVul1+YPHTqkn376SQEBARUy5uXluTNW9vjO3Mbrr79eYf1OnTpJ8jyeV/Z4vPF43uCvMjgbmZmZWrRokRITE/Wf//xHr776qrs5R0REyNfXV2PGjNHNN99c6fpt27aV9PPBtLJJ7ZUnoaDOiIqK0sGDByuMf/fdd5KkJk2auMdGjhypO+64Q0uWLNG8efP04osvavDgwYqIiKi2LCUlJSooKPAoBcxx73TmyXpNmjSRy+XS5s2b3UXwTOVj5aXx0KFDFZbJy8v73WcJmjRpogULFmjBggXav3+/XnvtNd199936/vvvtW7dut9dNyoq6leXCwsL+9XHd+Y2unTponnz5lW6jfKiHRUVpW3btlX4f2+c6zxDUEUHDx50X5r43nvv6eqrr9bEiROVm5srSQoJCVFycrI+/vhjdenSRT179qxwK/8BSU5O1q5du7Rjxw6PfSxbtqzWHxfqj379+mn37t366KOPPMaXLl0ql8ul5ORk91hERIQGDx6spUuXas2aNcrLy6vyywVVUX4J74oVKzzGly9fXm37gDMGDRokM9O3335b6XGu/JnTXr16KTAwsMIcyM7Odr/MVVVt2rRRWlqa+vfv7zG/AwMD3c9K/TLj4cOHVVpaWmnG+Pj4Kj3OnJwctW/fvtJtlBeC5ORkHTt2TK+99prH+t54POcZgiooLS3VyJEj5XK5tGzZMvn6+mrJkiXq1q2bRowYoS1btiggIECPPfaY+vbtqz/96U+aOnWq4uLidOzYMX355Zd6/fXX3a/hTps2TYsXL9bAgQOVnp6u5s2b66WXXnI/fQuci9tvv11Lly7VwIEDdd999yk2NlZvvPGGFi5cqKlTp1Z4jTQ1NVUrVqxQWlqaWrVqpT//+c/VliUlJUV9+vTR9OnTdfToUfXo0UNZWVlaunSpJMnHh79FvFWfPn00adIkTZgwQdu3b9dll12mRo0a6eDBg9qyZYs6d+6sqVOnui+XfeCBBxQREaEhQ4bowIEDmjNnjqKjo39zDhw5ckTJyckaNWqUEhISFBYWpg8++EDr1q3Ttdde616uc+fOWrlypRYtWqQePXrIx8dHPXv21PXXX6+XXnpJAwYM0G233abExET5+/vrwIED2rBhg6655hoNGTLkNx/nfffdp8zMTPXu3Vu33nqr4uPjVVhYqL1792rt2rV66qmn1KpVK40dO1b/+Mc/NHbsWM2bN08XXXSR1q5dq7feeqvavua1xuGTGh1XlasMZsyYYT4+Pvbuu+96rPvee++Zn5+f3Xbbbe6x3NxcS01NtZYtW5q/v781bdrUevfubenp6R7r7t692/r3729BQUEWGRlpEydOtNWrV3OVQQNXPvfy8/M9xseNG2eNGjWqsPzll19unTp1ct/ft2+fjRo1yqKioszf39/i4+Pt4YcfttLS0grrlpaWWuvWrU2SzZgxo8L//9ZVBr/Ml5GRYZIsNzfXPVZQUGATJkywxo0bW0hIiPXv39+ys7NNkj322GNV/ZLAQb/2/TYzW7x4sfXq1csaNWpkwcHB1r59exs7dqxt377dvUxZWZmlp6dbq1atLCAgwLp06WJr1qyxrl272pAhQ9zL/XKuFRYW2pQpU6xLly52wQUXWHBwsMXHx9usWbPsxIkT7vUKCgps2LBh1rhxY3O5XB7H7eLiYnvkkUesa9euFhQUZKGhoZaQkGCTJ0+2L774wr1cbGysDRw4sNLHn5+fb7feequ1bdvW/P39LTIy0nr06GEzZszwuMLswIEDNnToUAsNDbWwsDAbOnSovffee153lYHLzMyRJgKgwVm2bJluuOEGbd26Vb1793Y6DhyQm5urhIQEzZo1S/fcc4/TcXAGCgGAGvHyyy/r22+/VefOneXj46Ps7Gw9/PDD6t69u/uSLdRvO3bs0Msvv6zevXvrggsu0J49e/TQQw/p6NGjysnJ4aqTOoZzCADUiLCwMC1fvlzp6ek6ceKEoqOjNX78eKWnpzsdDbWkUaNG2r59u55//nn99NNPCg8PV1JSkubNm0cZqIN4hgAAAHDZIQAAoBAAAABRCAAAgCgEAABAZ3GVQWXv9QycLSfOYWXuojowd+Gtqjp3eYYAAABQCAAAAIUAAACIQgAAAEQhAAAAohAAAABRCAAAgCgEAABAFAIAACAKAQAAEIUAAACIQgAAAEQhAAAAohAAAABRCAAAgCgEAABAFAIAACAKAQAAEIUAAACIQgAAAEQhAAAAohAAAABRCAAAgCgEAABAFAIAACAKAQAAEIUAAACIQgAAAEQhAAAAohAAAABRCAAAgCgEAABAFAIAACAKAQDgHAQEBCg4ONjpGJVyuVwKCQmRr6+vx7ivr69CQkLkcrkcSla3UQgAAGdt3rx52rRpk3x86t6vkfj4eO3bt09JSUke4ykpKcrNzVXbtm2dCVbH+Tkd4FwlJydX+GbD0/r167Vp0yanYwCoJ8487iYnJ6tdu3aaM2eOysrKnA32C82aNVOTJk2Umpqqyy67zD3eoUMHNW3aVLfffrsKCgpqZN/efNz12kKQlJSkmTNnOh2jTistLfXaiQmg7qnsuHvvvfc6lOb3jRo1qtLxtLS0Gtnf4cOHdfToUa897nptIQAAoK44efKkunXrpry8PKejnLO69+IPAABeJiAgQHPnzlX//v2djnLOKAQAAJwnPz8/jR8/XhdffLHTUc4ZhQAAAFAIAAAAhQAAAIhCAAAARCEAAFRRRkaGhgwZotOnTzsdBTWAQgAAqBJfX18FBAQ4HQM1hEIAAKiSsWPHasWKFQoMDHQ6CmoAhQAAAFAIAAAAhQAAAIhCAAAARCEAAACiEAAAAFEIAACAKAQAAEAUAgD/ExYWpmbNmjkdA4BDKAQAJEkzZszQ1q1b5e/v73QUAA7wczoAgNrVp08fpaamVhhPTEzkLWmBBoxCANRzbdq0UXBwsPv+ZZddVmkhkKS8vDzFx8dr//79Onr0aG1FBFAHUAiAeu6ll15S79693fddLtevLtuiRQvt2LFD48eP14svvlgb8QDUERQCoJ645ZZb1L9//wrjnTp1ko9P1U8XOptlAdQfFALAy0RHRys2NrbC+BVXXKFBgwY5kAhAfUAhALzM9ddfr/nz5zsdA0A9QyEA6qhevXpV+os/Jiamxvc9c+ZMXXHFFRozZkyN7wtA3UAhAOqIHj16KDo62n3/j3/8o8fJgLXpwgsvVGFhoVwul8zMkQwAaheFAHBAZWf633PPPbr22msdSAMAFAKg1qWlpenmm2+uMN6yZUsH0gDAzygEQA2Ji4tTUlJShfH+/fsrISGh9gMBwG+gEADnyeVyVfr+/5deeqkyMjIcSFR9AgMDVVRUpLKyMqejAKhhFALgPHXs2FFvv/12hfEz3y7YGyUkJOirr77S6NGjtWHDBqfjAKhhFALgLPXr1089evRw34+JiamVSwFrm5+fn2JiYhQUFOR0FAC1gEIA/IqAgIBK/8ofPny4Jk2a5EAiAKg5FALgV9xwww1asGBBhXH+YgZQH1EI0OC1a9dON910U4Xxrl276oILLnAgEQDUPgoBGiQ/Pz81b95cLpdL3bt31/Tp052OVGc1adJETZs2VX5+vtNRANQgCgEapPj4eH344Yfy8fGp9F0D8f8WL16s//73v+rXr5/TUQDUIAoBGiSXy6WAgADKQBX4+fnJz49DBfBbioqKNG3aNG3dutXpKOfMx+kAAAB4u9LSUu3cuVMHDx50Oso5oxAAAHCegoODtXnzZk2ZMsXpKOeMQoAGae/evRowYIC2b9/udBQA9YTL5fLqlyEpBGiQjh8/rnXr1nHmfBWFh4erb9++Cg0NdToKgBpCIQDwu7p27arNmzerY8eOTkcBUEMoBAAAgEKAhi0rK0tbtmxxOgbgFT799FO9+eabKi0tdToKagCFAA3a3LlzNWPGDKdjeA3eyKlhW7FihVJTU1VUVOR0FNQACgGAKnvllVf0+OOPOx0DDrnlllu0detWPuCrnuLtxwBUWevWrdWyZUunY8AhUVFRateundMxUEN4hgAAAFAIAAAAhQDQ/v37lZ6eru+++87pKADgGAoBGry9e/fq73//uw4cOOB0FK/g7++vyMhI+fhw+ADqE36iAZyVq666Srm5uWrbtq3TUQBUIwoBgLPi6+ur0NBQ3o8AqGcoBAAAgEIAAAAoBIDbvffeq/nz5zsdAwAcQSEA/iczM5MPOjoLHTt2VKtWrZyOAaCaUAgAnDUfHx+tXr1a9957r9NRAFQTCgEAAKAQAAAACgGA88B7EQD1B4UAOMP69evVs2dP7d+/3+koXmHo0KHKyspSZGSk01EAnCc/pwMAdcmRI0f08ccf6/Tp005H8QpRUVEKCQmRv7+/01EAnCeeIQAAABQCAABAIQAqMDP985//1MaNG52OAgC1hkIA/IKZ6b777tPy5cudjuIVXC6XgoODOY+gATIznTx5UqWlpU5HqdTp06dVWFjodAyvQSEAcF6CgoL04Ycfavr06U5HQS3bu3ev2rZtqzfffNPpKJUaN26chg8f7nQMr+G1VxmsX7++zrbSuoKnvFFbIiMj1ahRI6djoIb98rhbUFCg/Px8LV26VB988IGDySr34YcfqqioSLNmzTrnbbRo0UJTp07VCy+8oK+//vp3l/fq465VkSRu3M775oRzzTp58mRH8nqruXPnOj6/mLvcqvvWqVMnO3TokPXt29fxLOd6qyqvfYYAAICatnv3bsXFxTWI9ybhHALgV2zevFm33Xabjh075nQUAA4xM506dUplZWVOR6lxFALgV+zevVvPPvusTp486XQUr9C4cWPFxcXx+QaAl3L973Wq31+QH3JUgypOt2p1PnM3ODhYubm5at68eTUmqp/MTD/88IPatWun48ePOx2n2nnb3AXKVXXu8gwBgGrhcrnk48MhBfBW/PQCAAAKAQAAoBAAv+n06dMaPHiwFi9e7HQUAKhRFALgN5SVlSk7O1u5ublOR/EKAQEB6tevn9q0aeN0FABniUIAoNqEhYVp1apVuuaaa5yOAuAsUQgAAACFAKiKnJwc/fvf/1ZJSYnTUQCgRlAIgCpYtWqVpkyZwmerV5Gvr6/8/PioFMCbUAgAVLuZM2dq06ZNlALAi1AIAFS7iIgIxcTEOB0DwFmgEAAAAAoBAACQeIEPqKITJ05o5syZuu6663TppZc6HafOKCkp0YMPPqgTJ054jB85cqRBfIY8UF/w8ceoVfXhI2Qff/xx3XLLLdW6TW9SWFioH3/80eN+r169lJ+f72Cqmlcf5i4apqrOXZ4hAHBW3nrrLV1//fXu+2am06dPO5gIQHWgEACo1IEDBzR79uwKT/vv3buX92MA6iEKAdDAlZWV6auvvqrwi//zzz/X888/71AqALWNQgA0cMeOHVNiYqKOHj3qMe7Ea+YAnEMhAM7SM888ox07dujpp5+Wr6+v03GqzMx0880368CBAx7jxcXFOn78OFcEAA0chQA4Szk5OTp+/Hid/wv6+PHjysnJcd8vKyvT2rVrtW/fPgdTAairKARAPZWTk8P7JQCoMgoB4OUOHz6sYcOGqaioyGP8l+cEAMBvoRAAXsLMtH79ep06dcpj/Mcff9TWrVtVXFzsUDIA9QGFADgHZlajJ+FVdn5CUVGRJkyYoG+++abG9gug4eKti1Gr6svbv/r7+6t9+/aaP3++rrrqqmrf/rhx47Rt27YK419++aVKSkqqfX/4ffVl7qLh4a2LgRpUXFyszz77rFpepz927JhWrlzp8UP7/vvva8+ePee9bQCoKgoBUIt+eeKfJH3zzTeaOHGiSktLHUgEAD+jEAC15NSpU7r44os9PilQ+vnjgykDAJxGIQDOw+rVq3Xq1CmNHz/eY/yFF17QoUOHPMaKioqUm5urkydP1mJCAKgaTipEraqPJ2Z169ZNH3/8scdY9+7d9cknn9ToflG76uPcRcNQ1blb5UIAAADqLx+nAwAAAOdRCAAAAIUAAABQCAAAgCgEAABAFAIAACAKAQAAEIUAAACIQgAAACT9H5yNk1WIAK/LAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_images([fixed_image, moving_image, unconstrained_result_image])"]}, {"cell_type": "markdown", "id": "0a5e3ffc", "metadata": {}, "source": ["### Registration 2: Constrained"]}, {"cell_type": "markdown", "id": "b637f250", "metadata": {}, "source": ["We will contrain the registration by setting an arbitarily high value for the `Scales` entry that corresponds to the transformation parameter (e.g. translation direction) that we want to penalize.\n", "\n", "NOTE: To do that we need to set manually also the rest of the `Scales` values. We can find the suitable values by first doing a 'dry-run' registration where `AutomaticScalesEstimation` is `true` and `MaximumNumberOfIterations` is `1`. We then find the calculated `Scales` by inspecting the log.\n", "\n", "Following the above-mentioned procdure we see that the calculated `Scales` are `[1666.5, 1, 1]`. Now, let's penalize one of the entries."]}, {"cell_type": "code", "execution_count": 9, "id": "c405b1af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterObject (00000230A4E7EAC0)\n", "  RTTI typeinfo:   class elastix::ParameterObject\n", "  Reference Count: 1\n", "  Modified Time: 35387\n", "  Debug: Off\n", "  Object Name: \n", "  Observers: \n", "    none\n", "ParameterMap 0: \n", "  (AutomaticParameterEstimation \"true\")\n", "  (AutomaticScalesEstimation \"false\")\n", "  (CheckNumberOfSamples \"true\")\n", "  (DefaultPixelValue 0)\n", "  (FinalBSplineInterpolationOrder 0)\n", "  (FixedImagePyramid \"FixedSmoothingImagePyramid\")\n", "  (ImageSampler \"RandomCoordinate\")\n", "  (Interpolator \"LinearInterpolator\")\n", "  (MaximumNumberOfIterations 256)\n", "  (MaximumNumberOfSamplingAttempts 8)\n", "  (Metric \"AdvancedMattesMutualInformation\")\n", "  (MovingImagePyramid \"MovingSmoothingImagePyramid\")\n", "  (NewSamplesEveryIteration \"true\")\n", "  (NumberOfResolutions 4)\n", "  (NumberOfSamplesForExactGradient 4096)\n", "  (NumberOfSpatialSamples 2048)\n", "  (Optimizer \"AdaptiveStochasticGradientDescent\")\n", "  (Registration \"MultiResolutionRegistration\")\n", "  (ResampleInterpolator \"FinalBSplineInterpolator\")\n", "  (Resampler \"DefaultResampler\")\n", "  (ResultImageFormat \"nii\")\n", "  (Scales 1666.5 1 3.40282e+38)\n", "  (Transform \"EulerTransform\")\n", "  (WriteIterationInfo \"false\")\n", "  (WriteResultImage \"true\")\n", "\n"]}], "source": ["constrained_rigid_parameter_object = itk.ParameterObject.New()\n", "constrained_rigid_parameter_map = constrained_rigid_parameter_object.GetDefaultParameterMap('rigid')\n", "constrained_rigid_parameter_map['FinalBSplineInterpolationOrder'] = ('0')\n", "constrained_rigid_parameter_map['AutomaticScalesEstimation'] = ('false',)\n", "constrained_rigid_parameter_map['Scales'] = ('1666.5', '1', str(np.finfo(fixed_image.dtype).max))\n", "constrained_rigid_parameter_object.AddParameterMap(constrained_rigid_parameter_map)\n", "\n", "print(constrained_rigid_parameter_object)"]}, {"cell_type": "code", "execution_count": 10, "id": "5cf28138", "metadata": {}, "outputs": [], "source": ["constrained_result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=constrained_rigid_parameter_object,\n", "    log_to_console=False)"]}, {"cell_type": "code", "execution_count": 11, "id": "aa9c8feb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_images([fixed_image, moving_image, constrained_result_image])"]}, {"cell_type": "code", "execution_count": null, "id": "10a04c5b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 5}