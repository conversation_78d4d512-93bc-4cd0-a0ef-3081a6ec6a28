{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def image_generator(x1, x2, y1, y2, upsampled=False):\n", "    if upsampled:\n", "        image = np.zeros([1000, 1000], np.float32)\n", "    else:\n", "        image = np.zeros([100, 100], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    image = itk.image_view_from_array(image)\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Size transformation test\n", "See example 12 for more explanation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create small images for registration\n", "fixed_image_small = image_generator(25,75,25,75)\n", "fixed_image_small.SetSpacing([10,10])\n", "moving_image_small = image_generator(0,50,10,60)\n", "moving_image_small.SetSpacing([10,10])\n", "\n", "# .. and a big moving image for transformation\n", "moving_image_large = image_generator(0,500,100,600, upsampled=True)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid',4)\n", "default_rigid_parameter_map['FinalBSplineInterpolationOrder'] = ['0']\n", "parameter_object.AddParameterMap(default_rigid_parameter_map)\n", "\n", "# Call elastix\n", "result_image_small, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image_small, moving_image_small,\n", "    parameter_object=parameter_object)\n", "\n", "# Adjust parameter file with spacing and size of large image.\n", "result_transform_parameters.SetParameter(\"Size\", ['1000', '1000'])\n", "result_transform_parameters.SetParameter(\"Spacing\", ['1', '1'])\n", "\n", "# Call transformix with procedural method\n", "result_image_large = itk.transformix_filter(\n", "    moving_image_large,\n", "    result_transform_parameters,\n", "    log_to_console=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Size transformation test visualization\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABqwAAAIoCAYAAAAcOsp8AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/d3fzzAAAACXBIWXMAAAsTAAALEwEAmpwYAAA8QklEQVR4nO3debi113w38O9PJhJTgqQxBlVDtaRN1TyFooSgahZKtTVT+qr2LR1c1VaVTlovbWKooaqkqKExtjTEVEOoIAghQRVBZFjvH+s+zs7JGfbzPOc5az/n+Xyua19nD2vfe51zJ9m/rO+616rWWgAAAAAAAGCUS4zuAAAAAAAAAHs3gRUAAAAAAABDCawAAAAAAAAYSmAFAAAAAADAUAIrAAAAAAAAhhJYAQAAAAAAMJTACvZSVfWMqmrT7baj+7O3qaojZv7+x6/R5viZNkdsbQ8BYHPMfJe9Y3RfNlNVPXTmd3vo6P5sB1V1+vT3PH2N1/**************************************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\n", "text/plain": ["<Figure size 2160x2160 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(1,3, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0].imshow(result_image_small)\n", "axs[0].set_title('Result Small', fontsize=30)\n", "axs[1].imshow(fixed_image_small)\n", "axs[1].set_title('Fixed Small', fontsize=30)\n", "axs[2].imshow(moving_image_small)\n", "axs[2].set_title('Moving Small', fontsize=30)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 2160x2160 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1,2, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0].imshow(result_image_large)\n", "axs[0].set_title('Result Large', fontsize=30)\n", "axs[1].imshow(moving_image_large)\n", "axs[1].set_title('Moving Large', fontsize=30)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}