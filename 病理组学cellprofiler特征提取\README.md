# 病理组学特征提取工具 - CellProfiler增强版

基于CellProfiler核心算法的病理组学特征提取工具，支持一键批量处理病理图像，提取细胞核、细胞质、细胞的全面特征用于后续分析。

## 🆕 版本说明

- **基础版**: 53个特征 (原始版本)
- **增强版**: 150+个特征 ⭐推荐 (新增细胞核、细胞质、细胞专门特征)

## 🚀 快速开始

### 方法1: 一键运行（推荐）
```bash
python 安装和运行.py
```

### 方法2: 直接运行
```bash
# 1. 安装依赖
pip install opencv-python scikit-image pandas numpy matplotlib seaborn pillow openpyxl scikit-learn

# 2. 运行特征提取
python 病理组学特征提取_CellProfiler_一键运行.py
```

## 📁 项目文件

| 文件名 | 描述 |
|--------|------|
| `病理组学特征提取_CellProfiler_一键运行.py` | 基础版主程序 (53特征) |
| `病理组学特征提取_增强版_细胞核细胞质细胞.py` | 增强版主程序 (150+特征) ⭐推荐 |
| `安装和运行.py` | 自动安装依赖和运行工具 |
| `测试病理组学特征提取.py` | 基础版测试脚本 |
| `测试增强版特征提取.py` | 增强版测试脚本 ⭐推荐 |
| `病理组学特征提取_使用说明.md` | 详细使用说明 |
| `README.md` | 项目说明文件 |

## ✨ 主要功能

### 基础版功能 (53特征)
- **🔬 细胞核分割**: 基于CellProfiler的IdentifyPrimaryObjects算法
- **📊 基础特征提取**: 形态学、纹理、强度特征
- **📈 基础可视化**: 生成特征分布图、相关性分析等

### 增强版功能 (150+特征) ⭐推荐
- **🧬 细胞核特征**: 25+个专门的细胞核形态学、强度特征
- **🔬 细胞质特征**: 15+个细胞质专门特征
- **🏠 细胞特征**: 20+个整个细胞的特征
- **⚖️ 核质比例**: 8+个核质比例和关系特征
- **📍 空间分布**: 10+个细胞空间分布特征
- **🎨 增强纹理**: 40+个多尺度、多方向纹理特征
- **📐 高级形态学**: 20+个Hu矩、凸包等高级特征
- **🌐 全局特征**: 15+个图像质量和全局统计特征

### 通用功能
- **📋 Excel输出**: 结构化的特征表格，按类型分工作表
- **🔄 批量处理**: 支持文件夹内所有图像的批量处理
- **📈 增强可视化**: 多维度分析图表和报告

## 🎯 提取的特征

### 基础版特征 (53个)
#### 形态学特征 (18个)
- 面积统计: 均值、标准差、最小值、最大值、中位数
- 周长统计: 均值、标准差
- 形状特征: 偏心率、实体度、圆形度
- 轴长特征: 长轴、短轴
- 细胞核: 数量、密度

#### 纹理特征 (25个)
- Haralick特征: 对比度、相异性、同质性、能量、相关性
- LBP特征: 局部二值模式直方图 (10个bin)
- 多尺度分析: 支持不同距离的纹理计算

#### 强度特征 (10个)
- 区域强度: 均值、标准差、最小值、最大值、中位数
- 全局强度: 整体图像的强度统计

### 增强版特征 (150+个) ⭐推荐

#### 细胞核特征 (25+个)
- **形态学**: 面积、周长、偏心率、实体度、圆形度、长短轴比
- **强度**: 平均强度、强度变异系数、强度分布
- **空间**: 细胞核间距离、分布均匀性、聚集指数
- **数量**: 细胞核计数、密度、边界效应

#### 细胞质特征 (15+个)
- **形态学**: 细胞质面积、周长、形状特征
- **强度**: 细胞质强度统计、变异性
- **纹理**: 细胞质专门的纹理特征

#### 细胞特征 (20+个)
- **整体形态**: 细胞总面积、周长、形状
- **强度分布**: 细胞整体强度特征
- **高级形态**: Hu矩、凸包面积、填充度、欧拉数

#### 核质比例特征 (8+个)
- **面积比**: 核质面积比、核细胞面积比
- **强度比**: 核质强度比、强度对比度
- **变异性**: 核质比的变异系数和分布

#### 空间分布特征 (10+个)
- **距离**: 最近邻距离、距离分布统计
- **分布**: 空间均匀性、聚集指数
- **边界**: 边界细胞比例、边界效应

#### 增强纹理特征 (40+个)
- **多尺度Haralick**: 4个距离 × 6个特征 = 24个
- **多半径LBP**: 3个半径 × 3个统计量 = 9个
- **梯度特征**: 梯度幅值、方向、统计量 = 7个

#### 全局图像特征 (15+个)
- **质量**: 对比度、清晰度、熵
- **统计**: 全局强度分布、四分位数、范围
- **纹理**: 全局纹理特征

## 📊 输出结果

### Excel文件结构
```
pathomics_features.xlsx
├── All_Features           # 完整特征表 (53个特征)
├── Morphological_Features # 形态学特征
├── Texture_Features       # 纹理特征
├── Intensity_Features     # 强度特征
└── Statistical_Summary    # 统计摘要
```

### 可视化图表
```
visualizations/
├── Morphological_features_distribution.png  # 形态学特征分布
├── Texture_features_distribution.png        # 纹理特征分布
├── Intensity_features_distribution.png      # 强度特征分布
├── correlation_matrix.png                   # 特征相关性热图
├── pca_variance_explained.png              # PCA方差解释
└── feature_importance.png                   # 特征重要性排序
```

## 🔧 核心算法

### 1. 图像预处理
- RGB到灰度转换
- 高斯滤波去噪 (σ=1.0)
- 强度归一化 (0-255)

### 2. 细胞核分割
```python
# Otsu阈值分割
thresh = threshold_otsu(image)
binary = image > thresh

# 形态学操作
binary = remove_small_objects(binary, min_size=50)
binary = binary_closing(binary, disk(3))

# 分水岭分割
distance = distance_transform_edt(binary)
markers = peak_local_maxima(distance)
labels = watershed(-distance, markers, mask=binary)
```

### 3. 特征计算
- **形态学**: 基于regionprops计算区域属性
- **纹理**: 灰度共生矩阵 + LBP
- **强度**: 像素强度统计分析

## 📋 使用示例

### 基础使用
```python
from 病理组学特征提取_CellProfiler_一键运行 import PathomicsFeatureExtractor

# 创建提取器
extractor = PathomicsFeatureExtractor(
    input_folder="./病理图像",
    output_folder="./结果"
)

# 批量处理
extractor.batch_process()

# 查看结果
print(f"提取了 {len(extractor.features_df)} 个图像的特征")
print(f"特征维度: {extractor.features_df.shape}")
```

### 单张图像处理
```python
# 处理单张图像
features = extractor.process_single_image("path/to/image.jpg")
print(f"提取特征: {len(features)} 个")
```

## 🧪 测试功能

运行测试脚本创建模拟病理图像：
```bash
python 测试病理组学特征提取.py
```

测试包括：
- 基础测试: 创建简单的模拟细胞核图像
- 高级测试: 创建不同病理类型的模拟图像

## 📖 技术细节

### 支持的图像格式
- JPG/JPEG
- PNG  
- TIF/TIFF
- BMP

### 系统要求
- Python 3.7+
- 内存: 建议4GB以上
- 磁盘空间: 根据图像数量而定

### 依赖包
```
opencv-python>=4.5.0
scikit-image>=0.18.0
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.3.0
seaborn>=0.11.0
pillow>=8.0.0
openpyxl>=3.0.0
scikit-learn>=1.0.0
scipy>=1.7.0
```

## 🔍 质量控制

### 图像质量检查
- 自动跳过无法读取的图像
- 处理过程中的异常捕获
- 详细的处理日志输出

### 特征验证
- 空值检查和处理
- 数值范围验证
- 统计摘要生成

## 📈 应用场景

- **病理学研究**: 组织形态学分析
- **癌症诊断**: 细胞核特征量化
- **药物筛选**: 细胞形态变化检测
- **机器学习**: 病理图像分类的特征工程

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

本项目基于MIT许可证开源。

## 🙏 致谢

本项目基于以下开源项目：
- [CellProfiler](https://cellprofiler.org/): 生物图像分析平台
- [scikit-image](https://scikit-image.org/): Python图像处理库
- [OpenCV](https://opencv.org/): 计算机视觉库

---

**作者**: AI Assistant  
**日期**: 2025-01-08  
**版本**: 1.0.0
