import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from tqdm import tqdm
import argparse
from pathlib import Path
import json
import time
from datetime import datetime
import matplotlib.pyplot as plt

from models.diffusion_model import ConditionalUNet, DiffusionModel
from utils.data_loader import get_data_loaders
from utils.loss_functions import DiffusionLoss, PerceptualLoss

class DiffusionTrainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config['system']['device'])
        
        # 创建保存目录
        self.save_dir = Path(config['paths']['save_dir'])
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存配置
        with open(self.save_dir / 'config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        # 创建模型
        self.model = self._create_model()
        
        # 添加权重初始化
        self._initialize_weights()
        
        # 创建优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config['training']['learning_rate'],
            weight_decay=config['training']['weight_decay']
        )
        
        # 学习率调度器 - 使用更安全的调度策略
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=50,  # 每50个epoch重启
            T_mult=2,  # 重启周期倍增
            eta_min=max(config['training']['learning_rate'] * 0.01, 1e-8)  # 防止负学习率
        )
        
        # 损失函数
        self.criterion = DiffusionLoss()
        if config['loss']['use_perceptual_loss']:
            self.perceptual_loss = PerceptualLoss().to(self.device)

        # 添加SSIM损失用于结构保持
        self.ssim_loss = self._create_ssim_loss()

        # 添加梯度损失用于边缘保持
        self.gradient_loss = self._create_gradient_loss()
        
        # 数据加载器
        self.train_loader, self.val_loader = get_data_loaders(
            config['data']['ap_dir'],
            config['data']['hbp_dir'],
            batch_size=config['data']['batch_size'],
            image_size=config['data']['image_size'],
            train_ratio=config['data']['train_ratio'],
            num_workers=config['data']['num_workers']
        )
        
        # 日志记录
        self.log_file = self.save_dir / 'training.log'
        self.loss_history = {'train': [], 'val': []}
        
        # 训练状态
        self.epoch = 0
        self.best_loss = float('inf')
        
        # 如果指定了checkpoint路径，加载checkpoint
        if config.get('checkpoint_path'):
            self.load_checkpoint(config['checkpoint_path'])
    
    def _create_model(self):
        """创建模型"""
        unet = ConditionalUNet(
            in_channels=self.config['model']['in_channels'],
            out_channels=self.config['model']['out_channels'],
            time_emb_dim=self.config['model']['time_emb_dim'],
            base_channels=self.config['model']['base_channels'],
            channel_multipliers=self.config['model']['channel_multipliers']
        )
        
        diffusion = DiffusionModel(
            model=unet,
            timesteps=self.config['diffusion']['timesteps'],
            beta_start=self.config['diffusion']['beta_start'],
            beta_end=self.config['diffusion']['beta_end'],
            device=self.device
        )
        
        return diffusion.to(self.device)
    
    def _initialize_weights(self):
        """初始化模型权重以提高稳定性"""
        for module in self.model.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.GroupNorm):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Linear):
                nn.init.normal_(module.weight, 0, 0.01)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def _create_ssim_loss(self):
        """创建SSIM损失函数"""
        def ssim_loss(pred, target):
            # 简化的SSIM损失实现
            mu1 = torch.mean(pred)
            mu2 = torch.mean(target)
            mu1_sq = mu1 ** 2
            mu2_sq = mu2 ** 2
            mu1_mu2 = mu1 * mu2

            sigma1_sq = torch.mean(pred ** 2) - mu1_sq
            sigma2_sq = torch.mean(target ** 2) - mu2_sq
            sigma12 = torch.mean(pred * target) - mu1_mu2

            c1 = 0.01 ** 2
            c2 = 0.03 ** 2

            ssim = ((2 * mu1_mu2 + c1) * (2 * sigma12 + c2)) / ((mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2))
            return 1 - ssim

        return ssim_loss

    def _create_gradient_loss(self):
        """创建梯度损失函数用于边缘保持"""
        def gradient_loss(pred, target):
            # 计算梯度
            pred_grad_x = torch.abs(pred[:, :, :, :-1] - pred[:, :, :, 1:])
            pred_grad_y = torch.abs(pred[:, :, :-1, :] - pred[:, :, 1:, :])

            target_grad_x = torch.abs(target[:, :, :, :-1] - target[:, :, :, 1:])
            target_grad_y = torch.abs(target[:, :, :-1, :] - target[:, :, 1:, :])

            # 计算梯度差异
            loss_x = torch.mean(torch.abs(pred_grad_x - target_grad_x))
            loss_y = torch.mean(torch.abs(pred_grad_y - target_grad_y))

            return loss_x + loss_y

        return gradient_loss

    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = len(self.train_loader)
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {self.epoch}')
        
        for batch_idx, batch in enumerate(pbar):
            ap_images = batch['ap'].to(self.device)
            hbp_images = batch['hbp'].to(self.device)
            
            # 采样时间步
            t = self.model.sample_timesteps(ap_images.shape[0])
            
            # 添加噪声
            noise = torch.randn_like(hbp_images)
            noisy_images, _ = self.model.add_noise(hbp_images, t, noise)
            
            # 预测噪声
            predicted_noise = self.model(noisy_images, t, condition=ap_images)
            
            # 计算基础扩散损失
            diffusion_loss = self.criterion(predicted_noise, noise)
            loss = diffusion_loss

            # 添加感知损失
            if self.config['loss']['use_perceptual_loss']:
                perceptual_loss = self.perceptual_loss(predicted_noise, noise)
                loss += self.config['loss']['perceptual_weight'] * perceptual_loss

            # 添加SSIM损失用于结构保持
            ssim_loss = self.ssim_loss(predicted_noise, noise)
            loss += 0.1 * ssim_loss

            # 添加梯度损失用于边缘保持
            gradient_loss = self.gradient_loss(predicted_noise, noise)
            loss += 0.05 * gradient_loss
            
            # 损失异常检测和处理
            if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 10.0:
                print(f"异常损失检测: {loss.item()}, 跳过此batch")
                continue
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 改进的梯度裁剪 - 更温和的裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Avg Loss': f'{total_loss / (batch_idx + 1):.4f}',
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
            })
            
            # 记录损失 - 每10个batch记录一次
            if batch_idx % 10 == 0:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"Epoch {self.epoch}, Batch {batch_idx}, Loss: {loss.item():.4f}, Avg Loss: {total_loss / (batch_idx + 1):.4f}, LR: {self.optimizer.param_groups[0]['lr']:.6f}\n")
        
        return total_loss / num_batches
    
    def validate(self):
        """验证"""
        self.model.eval()
        total_loss = 0
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validating'):
                ap_images = batch['ap'].to(self.device)
                hbp_images = batch['hbp'].to(self.device)
                
                # 采样时间步
                t = self.model.sample_timesteps(ap_images.shape[0])
                
                # 添加噪声
                noise = torch.randn_like(hbp_images)
                noisy_images, _ = self.model.add_noise(hbp_images, t, noise)
                
                # 预测噪声
                predicted_noise = self.model(noisy_images, t, condition=ap_images)
                
                # 计算损失
                loss = self.criterion(predicted_noise, noise)
                
                if self.config['loss']['use_perceptual_loss']:
                    perceptual_loss = self.perceptual_loss(predicted_noise, noise)
                    loss += self.config['loss']['perceptual_weight'] * perceptual_loss
                
                total_loss += loss.item()
        
        avg_loss = total_loss / num_batches
        self.loss_history['val'].append(avg_loss)
        
        return avg_loss
    
    def save_checkpoint(self, is_best=False):
        """保存checkpoint"""
        checkpoint = {
            'epoch': self.epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_loss': self.best_loss,
            'config': self.config
        }
        
        # 保存最新的checkpoint
        torch.save(checkpoint, self.save_dir / 'latest_checkpoint.pth')
        
        # 如果是最佳模型，保存最佳checkpoint
        if is_best:
            torch.save(checkpoint, self.save_dir / 'best_checkpoint.pth')
            print(f'保存最佳模型，验证损失: {self.best_loss:.4f}')
    
    def load_checkpoint(self, checkpoint_path):
        """加载checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.epoch = checkpoint['epoch']
        self.best_loss = checkpoint['best_loss']
        
        print(f'加载checkpoint from epoch {self.epoch}')
    
    def generate_samples(self, num_samples=4):
        """生成样本"""
        self.model.eval()
        
        with torch.no_grad():
            # 从验证集获取条件图像
            batch = next(iter(self.val_loader))
            ap_images = batch['ap'][:num_samples].to(self.device)
            hbp_images = batch['hbp'][:num_samples].to(self.device)
            
            # 生成样本
            generated = self.sample(ap_images)
            
            # 保存样本
            self.save_samples(
                ap_images.cpu(),
                hbp_images.cpu(),
                generated.cpu(),
                self.save_dir / f'samples_epoch_{self.epoch}.png'
            )
    
    def sample(self, condition, num_inference_steps=100):
        """改进的采样生成图像 - 使用DDIM采样"""
        self.model.eval()

        with torch.no_grad():
            # 从纯噪声开始
            shape = condition.shape
            x = torch.randn(shape, device=self.device)

            # 使用DDIM采样，更稳定
            timesteps = torch.linspace(self.model.timesteps - 1, 0, num_inference_steps, dtype=torch.long, device=self.device)
            eta = 0.0  # DDIM参数，0为确定性采样

            for i, t in enumerate(tqdm(timesteps, desc='Sampling')):
                t_batch = t.repeat(shape[0])

                # 预测噪声
                predicted_noise = self.model(x, t_batch, condition=condition)

                # 计算去噪参数
                alpha_cumprod_t = self.model.alphas_cumprod[t]

                # 计算前一个时间步的alpha_cumprod
                if i < len(timesteps) - 1:
                    alpha_cumprod_prev = self.model.alphas_cumprod[timesteps[i + 1]]
                else:
                    alpha_cumprod_prev = torch.tensor(1.0, device=self.device)

                # 计算x_0的预测
                pred_x0 = (x - torch.sqrt(1 - alpha_cumprod_t) * predicted_noise) / torch.sqrt(alpha_cumprod_t)

                # 更强的范围限制
                pred_x0 = torch.clamp(pred_x0, -1, 1)

                # DDIM采样公式
                if t > 0:
                    # 计算方向向量
                    direction = torch.sqrt(1 - alpha_cumprod_prev - eta**2 * (1 - alpha_cumprod_t)) * predicted_noise

                    # 计算噪声项
                    noise = eta * torch.sqrt(1 - alpha_cumprod_t) * torch.randn_like(x)

                    # 更新x
                    x = torch.sqrt(alpha_cumprod_prev) * pred_x0 + direction + noise
                else:
                    # 最后一步，直接使用预测的x_0
                    x = pred_x0

                # 每10步进行一次范围限制
                if i % 10 == 0:
                    x = torch.clamp(x, -2, 2)

        return x
    
    def save_samples(self, ap_images, hbp_real, hbp_generated, save_path):
        """保存生成的样本图像"""
        
        batch_size = ap_images.size(0)
        
        # 创建子图
        fig, axes = plt.subplots(batch_size, 3, figsize=(12, 4 * batch_size))
        if batch_size == 1:
            axes = axes.reshape(1, -1)
        
        for i in range(batch_size):
            # 转换为numpy并归一化
            ap_img = ap_images[i].squeeze().numpy()
            real_img = hbp_real[i].squeeze().numpy()
            gen_img = hbp_generated[i].squeeze().numpy()
            
            # 归一化到[0,1]
            ap_img = (ap_img - ap_img.min()) / (ap_img.max() - ap_img.min() + 1e-8)
            real_img = (real_img - real_img.min()) / (real_img.max() - real_img.min() + 1e-8)
            gen_img = (gen_img - gen_img.min()) / (gen_img.max() - gen_img.min() + 1e-8)
            
            # 绘制图像
            axes[i, 0].imshow(ap_img, cmap='gray')
            axes[i, 0].set_title('AP Input')
            axes[i, 0].axis('off')
            
            axes[i, 1].imshow(real_img, cmap='gray')
            axes[i, 1].set_title('HBP Real')
            axes[i, 1].axis('off')
            
            axes[i, 2].imshow(gen_img, cmap='gray')
            axes[i, 2].set_title('HBP Generated')
            axes[i, 2].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def train(self):
        """训练主循环"""
        print(f'开始训练，共{self.config["training"]["num_epochs"]}个epoch')
        print(f'训练集大小: {len(self.train_loader.dataset)}')
        print(f'验证集大小: {len(self.val_loader.dataset)}')
        
        for epoch in range(self.epoch, self.config['training']['num_epochs']):
            self.epoch = epoch
            
            # 动态降低学习率（如果损失波动过大）
            if epoch > 300 and epoch % 50 == 0:
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] *= 0.5
                print(f"学习率降低到: {self.optimizer.param_groups[0]['lr']:.8f}")
            
            # 训练
            train_loss = self.train_epoch()
            self.loss_history['train'].append(train_loss)
            
            # 验证
            val_loss = self.validate()
            
            # 更新学习率并检查负值
            self.scheduler.step()
            
            # 学习率保护机制：确保学习率不为负
            current_lr = self.optimizer.param_groups[0]['lr']
            if current_lr <= 0:
                print(f"警告：检测到负学习率 {current_lr:.8f}，重置为最小安全值")
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = 1e-8
            
            # 打印统计信息
            print(f'Epoch {epoch}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')
            
            # 保存checkpoint
            is_best = val_loss < self.best_loss
            if is_best:
                self.best_loss = val_loss
            
            # 早停检查：如果训练损失波动过大
            if epoch > 250 and len(self.loss_history['train']) >= 10:
                recent_losses = self.loss_history['train'][-10:]
                loss_std = np.std(recent_losses)
                if loss_std > 0.5:  # 如果最近10个epoch损失标准差过大
                    print(f"警告：训练不稳定，损失标准差: {loss_std:.4f}")
            
            self.save_checkpoint(is_best)
            
            # 生成样本
            if epoch % self.config['validation']['sample_interval'] == 0:
                self.generate_samples()
        
        print('训练完成!')
        
        # 保存最终损失历史
        with open(self.save_dir / 'loss_history.json', 'w') as f:
            json.dump(self.loss_history, f, indent=2)

def main(ap_dir=None, hbp_dir=None, output_dir=None, default_checkpoint=None):
    parser = argparse.ArgumentParser(description='Train Medical Image Diffusion Model')
    parser.add_argument('--config', type=str, help='Path to config file')
    parser.add_argument('--checkpoint', type=str, help='Path to checkpoint file')
    parser.add_argument('--ap_dir', type=str, help='Path to AP images directory')
    parser.add_argument('--hbp_dir', type=str, help='Path to HBP images directory')
    parser.add_argument('--batch_size', type=int, help='Batch size')
    parser.add_argument('--epochs', type=int, help='Number of epochs')
    parser.add_argument('--lr', type=float, help='Learning rate')
    parser.add_argument('--image_size', type=int, nargs=2, help='Image size [height width]')

    args = parser.parse_args()

    # 默认配置 - 使用传入的路径参数
    default_config = {
        "data": {
            "ap_dir": ap_dir,
            "hbp_dir": hbp_dir,
            "image_size": [256, 256],
            "train_ratio": 0.8,
            "batch_size": 4,
            "num_workers": 0
        },
        "model": {
            "in_channels": 1,
            "out_channels": 1,
            "time_emb_dim": 128,
            "base_channels": 64,
            "channel_multipliers": [1, 2, 4, 8]
        },
        "diffusion": {
            "timesteps": 1000,
            "beta_start": 1e-4,
            "beta_end": 0.02
        },
        "training": {
            "num_epochs": 800,
            "learning_rate": 2e-4,
            "weight_decay": 1e-4,
            "grad_clip": 1.0
        },
        "loss": {
            "use_perceptual_loss": True,
            "perceptual_weight": 0.1
        },
        "validation": {
            "sample_interval": 20
        },
        "paths": {
            "save_dir": output_dir
        },
        "system": {
            "device": "cuda" if torch.cuda.is_available() else "cpu"
        }
    }
    
    # 如果提供了配置文件，加载并合并
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            file_config = json.load(f)
        # 深度合并配置
        config = {**default_config, **file_config}
    else:
        config = default_config
    
    # 命令行参数覆盖配置
    if args.ap_dir:
        config['data']['ap_dir'] = args.ap_dir
    if args.hbp_dir:
        config['data']['hbp_dir'] = args.hbp_dir
    if args.batch_size:
        config['data']['batch_size'] = args.batch_size
    if args.epochs:
        config['training']['num_epochs'] = args.epochs
    if args.lr:
        config['training']['learning_rate'] = args.lr
    if args.image_size:
        config['data']['image_size'] = args.image_size
    
    # 设置checkpoint路径
    if args.checkpoint:
        config['checkpoint_path'] = args.checkpoint
    else:
        # 使用传入的默认checkpoint路径
        checkpoint_path = default_checkpoint 
        if Path(checkpoint_path).exists():
            config['checkpoint_path'] = checkpoint_path
            print(f"找到默认checkpoint: {checkpoint_path}")
        else:
            print("未找到默认checkpoint，将从头开始训练")
    
    # 打印配置信息
    print("训练配置:")
    print(f"  AP图像目录: {config['data']['ap_dir']}")
    print(f"  HBP图像目录: {config['data']['hbp_dir']}")
    print(f"  批次大小: {config['data']['batch_size']}")
    print(f"  训练轮数: {config['training']['num_epochs']}")
    print(f"  学习率: {config['training']['learning_rate']}")
    print(f"  图像尺寸: {config['data']['image_size']}")
    print(f"  设备: {config['system']['device']}")
    print(f"  输出目录: {config['paths']['save_dir']}")
    
    # 检查数据目录
    ap_dir = Path(config['data']['ap_dir'])
    hbp_dir = Path(config['data']['hbp_dir'])
    
    if not ap_dir.exists():
        print(f"警告: AP图像目录不存在: {ap_dir}")
    else:
        ap_files = list(ap_dir.glob("*.nii.gz"))
        print(f"发现 {len(ap_files)} 个AP图像文件")
    
    if not hbp_dir.exists():
        print(f"警告: HBP图像目录不存在: {hbp_dir}")
    else:
        hbp_files = list(hbp_dir.glob("*.nii.gz"))
        print(f"发现 {len(hbp_files)} 个HBP图像文件")
    
    # 创建训练器
    trainer = DiffusionTrainer(config)
    
    # 开始训练
    trainer.train()

if __name__ == "__main__":
    # ==================== 路径配置 ====================   
    # 数据路径
    AP_DIR = "/root/autodl-tmp/120HCC/image/ap"
    HBP_DIR = "/root/autodl-tmp/120HCC/image/hbp"

    # 输出路径
    OUTPUT_DIR = "outputs/medical_diffusion_experiment"

    # 默认checkpoint路径
    DEFAULT_CHECKPOINT = "/root/autodl-tmp/outputs/medical_diffusion_experiment/latest_checkpoint.pth"

    # ==================== 启动训练 ====================
    main(AP_DIR, HBP_DIR, OUTPUT_DIR, DEFAULT_CHECKPOINT)

# 使用示例:
# python train.py --batch_size 2 --epochs 100 --lr 1e-4
# python train.py --checkpoint "/root/autodl-tmp/outputs/medical_diffusion_experiment/latest_checkpoint.pth"
# python train.py --checkpoint "/root/autodl-tmp/outputs/medical_diffusion_experiment/best_checkpoint.pth"
# python train.py  # 自动加载默认checkpoint继续训练
