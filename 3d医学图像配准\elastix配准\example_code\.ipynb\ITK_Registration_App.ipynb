{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ffb4de889ec94753ad7690f684b4960a", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HBox(children=(VBox(children=(Label(value='Fixed image'), FileUpload(value={}, accept='.nrrd,.m…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5391c569d61842caaf94dd0cc36083cb", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "febeb275fbf04c018e25ed82dd1f38af", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(RadioButtons(description='Type:', options=('rigid', 'affine', 'bspline'), value='rigid'), Butto…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import itk\n", "from itkwidgets import view, compare, checkerboard\n", "from itkwidgets.widget_viewer import Viewer\n", "import ipywidgets\n", "from ipywidgets import Button, Label, HBox, VBox\n", "import os\n", "import base64\n", "\n", "\n", "# for use in viewers, from\n", "# https://itk.org/ITKExamples/src/Core/Common/CreateAnImage/Documentation.html\n", "Dimension = 3\n", "PixelType = itk.ctype('float')\n", "ImageType = itk.Image[PixelType, Dimension]\n", "\n", "placeholderImage = ImageType.New()\n", "\n", "ind = itk.Index[Dimension]()\n", "ind[0] = 0  # first index on X\n", "ind[1] = 0  # first index on Y\n", "ind[2] = 0  # first index on Z\n", "\n", "size1 = 4\n", "size = itk.Size[Dimension]()\n", "size[0] = size1  # size along X\n", "size[1] = size1  # size along Y\n", "size[2] = size1  # size along Z\n", "\n", "region = itk.ImageRegion[Dimension]()\n", "region.SetSize(size)\n", "region.SetIndex(ind)\n", "\n", "placeholderImage.SetRegions(region)\n", "placeholderImage.Allocate()\n", "\n", "# set voxel values\n", "for k in range(size1):\n", "    ind[2]=k\n", "    for j in range(size1):\n", "        ind[1]=j\n", "        for i in range(size1):\n", "            ind[0]=i\n", "            placeholderImage.SetPixel(ind, i+2*j+k*k)\n", "\n", "\n", "# from https://github.com/InsightSoftwareConsortium/itkwidgets/blob/cffd4583775af2d3fa2aaac67e34fc21b3a790b5/itkwidgets/widget_compare.py#L26-L34\n", "def link<PERSON><PERSON><PERSON><PERSON>(viewer1, viewer2):\n", "    ipywidgets.jslink((viewer1, 'mode'), (viewer2, 'mode'))\n", "    ipywidgets.jslink((viewer1, 'camera'), (viewer2, 'camera'))\n", "    ipywidgets.jslink((viewer1, 'roi'), (viewer2, 'roi'))\n", "    ipywidgets.jslink((viewer1, 'rotate'), (viewer2, 'rotate'))\n", "    ipywidgets.jslink((viewer1, 'annotations'), (viewer2, 'annotations'))\n", "    ipywidgets.jslink((viewer1, 'x_slice'), (viewer2, 'x_slice'))\n", "    ipywidgets.jslink((viewer1, 'y_slice'), (viewer2, 'y_slice'))\n", "    ipywidgets.jslink((viewer1, 'z_slice'), (viewer2, 'z_slice'))\n", "    ipywidgets.jslink((viewer1, 'slicing_planes'), (viewer2, 'slicing_planes'))\n", "    \n", "\n", "def readUploadedImage(uploader):   \n", "    # using FileUpload's syntax for ipywidgets <= 7\n", "    tempFilename=\"./uploaded_\"+ next(iter(uploader.value))\n", "    with open(tempFilename, \"wb\") as fp:\n", "        fp.write(uploader.data[0]) # write to temporary image file\n", "        \n", "    # I don't know how to read image from memory in Python\n", "    image = itk.imread(tempFilename, itk.F)\n", "    os.remove(tempFilename)\n", "    return image\n", "\n", "\n", "# these FileUpload widgets have maximum file size\n", "# controlled via jupyter_notebook_config.py\n", "# https://github.com/jupyter-widgets/ipywidgets/issues/2522\n", "# Start with:\n", "#  python -m jupyter notebook --config ./.jupyter/jupyter_notebook_config.py ./examples/ITK_Registration_App.ipynb\n", "# for <PERSON><PERSON><PERSON>, or\n", "#  python -m voila --Voila.tornado_settings '{\"websocket_max_message_size\":*********}' --theme=dark ./examples/ITK_Registration_App.ipynb\n", "# for Voilà on Linux. Command is slightly different on Windows:\n", "#  python -m voila \"--Voila.tornado_settings={\\\"websocket_max_message_size\\\":*********}\" --theme=dark ./examples/ITK_Registration_App.ipynb\n", "\n", "fixedUploader = ipywidgets.FileUpload(\n", "    accept='.nrrd,.mha,.nii,image/*',\n", "    multiple=False)\n", "movingUploader = ipywidgets.FileUpload(\n", "    accept='.nrrd,.mha,.nii,image/*',\n", "    multiple=False)\n", "\n", "# create viewers which we will link to have the same view etc\n", "# to change images as needed, they need to have an initial image\n", "fixedViewer = view(image=placeholderImage, ui_collapsed=True)\n", "movingViewer = view(image=placeholderImage, ui_collapsed=True)\n", "resultViewer = view(image=placeholderImage, ui_collapsed=True)\n", "linkViewers(fixedViewer, movingViewer)\n", "linkViewers(fixedViewer, resultViewer)\n", "\n", "vboxFixed = VBox([Label('Fixed image'), fixedUploader, fixedViewer])\n", "vboxMoving = VBox([Label('Moving image'), movingUploader, movingViewer])\n", "hboxFixedMoving = HBox([vboxFixed, vboxMoving])\n", "vboxResult = VBox([Label('Result image'), Label(' '), resultViewer])\n", "imageViews = VBox([hboxFixedMoving, vboxResult])\n", "display(imageViews)\n", "\n", "out = ipywidgets.Output()\n", "display(out) # for status messages\n", "\n", "regType=ipywidgets.RadioButtons(\n", "    options=['rigid', 'affine', 'bspline'],\n", "    value='rigid',\n", "    description='Type:',\n", "    disabled=False\n", ")\n", "registerButton = ipywidgets.Button(description=\"Register\")\n", "buttons = HBox([regType, registerButton])\n", "display(buttons)\n", "\n", "appState={'fixed':placeholderImage, 'moving':placeholderImage, 'result':None}\n", "\n", "\n", "def uploadedImage(b):\n", "    uploader=b['owner']\n", "    if (uploader is fixedUploader):\n", "        appState['fixed'] = readUploadedImage(fixedUploader)\n", "        fixedViewer.image=appState['fixed']\n", "        if (appState['moving'] is placeholderImage):\n", "            # make them close in physical space\n", "            appState['moving'].SetOrigin(appState['fixed'].GetOrigin())\n", "            movingViewer.image=appState['moving']\n", "    else:\n", "        appState['moving'] = readUploadedImage(movingUploader)\n", "        movingViewer.image=appState['moving']\n", "        if (appState['fixed'] is placeholderImage):\n", "            # make them close in physical space\n", "            appState['fixed'].SetOrigin(appState['moving'].GetOrigin())\n", "            fixedViewer.image=appState['fixed']\n", "    \n", "    resultViewer=checkerboard(appState['fixed'], appState['moving'])\n", "    linkViewers(fixedViewer, resultViewer.children[0])\n", "    \n", "    vboxResult=imageViews.children[1]\n", "    vboxResult.children=(Label('Fixed/moving'),\n", "                        Label('checkerboard'),\n", "                        resultViewer)\n", "    imageViews.children=(hboxFixedMoving,\n", "                        vboxResult)\n", "    out.clear_output(wait=True)\n", "\n", "fixedUploader.observe(uploadedImage, names='value')\n", "movingUploader.observe(uploadedImage, names='value')\n", "\n", "\n", "def registerImages(b):\n", "    out.clear_output(wait=True)\n", "    parameters = itk.ParameterObject.New()\n", "\n", "    resolutions = 3\n", "    default_rigid = parameters.GetDefaultParameterMap(\"rigid\", resolutions)\n", "    parameters.AddParameterMap(default_rigid)\n", "\n", "    if (regType.value!='rigid'):\n", "        resolutions = 2\n", "        default_affine = parameters.GetDefaultParameterMap(\"affine\", resolutions)\n", "        parameters.AddParameterMap(default_affine)\n", "\n", "    if (regType.value=='bspline'):\n", "        resolutions = 1\n", "        default_bspline = parameters.GetDefaultParameterMap(\"bspline\", resolutions)\n", "        parameters.AddParameterMap(default_bspline)\n", "\n", "    parameters.RemoveParameter(\"ResultImageFormat\")\n", "    with out:\n", "        print('Executing '+regType.value+' registration. Please wait...')\n", "    \n", "    appState['result'], params = itk.elastix_registration_method(appState['fixed'],\n", "                                                                 appState['moving'],\n", "                                                                 parameter_object=parameters,\n", "                                                                 log_to_file=True,\n", "                                                                 log_file_name=\"elx_HASI.log\",\n", "                                                                 output_directory='.')\n", "    \n", "    with out:\n", "        print('Compressing result image...')\n", "    itk.imwrite(appState['result'], \"./result.nrrd\", compression=True)\n", "    \n", "    resultViewer=checkerboard(appState['fixed'], appState['result'])\n", "    linkViewers(fixedViewer, resultViewer.children[0])\n", "    \n", "    vboxResult=imageViews.children[1]\n", "    vboxResult.children=(Label('Fixed/registered'),\n", "                        vboxResult.children[1],\n", "                        resultViewer)\n", "    \n", "    imageViews.children=(imageViews.children[0],\n", "                        vboxResult)\n", "    \n", "    # create a button which will contain base64-encoded file content\n", "    # that way server permission don't matter for downloading\n", "    with open(\"./result.nrrd\", mode='rb') as file:\n", "        fileContent = file.read()    \n", "    fileContent64 = base64.b64encode(fileContent)\n", "    html = '''<a download=\"MovingRegistered.nrrd\"\n", "        href=\"data:image/nrrd;base64,{payload}\"\n", "        target=\"_blank\">Download moving registered image</a>'''\n", "    html = html.format(payload=fileContent64.decode())\n", "    downloadLink=ipywidgets.HTML(html)\n", "    buttons.children=(buttons.children[0],\n", "                     buttons.children[1],\n", "                     downloadLink)\n", "\n", "    out.clear_output()\n", "\n", "registerButton.on_click(registerImages)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}