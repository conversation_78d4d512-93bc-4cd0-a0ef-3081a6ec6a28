# GMACs   Params.(M)
#  0.84    1.466
# Throughput (ins./s): 1632.3943616291886 (2080ti, B=48)
# Throughput (ins./s): 2062.428502574422

model:
  NAME: BaseCls
  encoder_args:
    NAME: PointNet2Encoder
    in_channels: 3
    width: null
    layers: 3
    use_res: False
    strides: [2, 4, 1]
    mlps: [[[64, 64, 128]],  # stage 1: 96
          [[128, 128, 256]],
          [[256, 512, 1024]]
          ] # stage 4: 1024
    radius: [0.2, 0.4, null]
    num_samples: [32, 64, null]
    sampler: fps
    aggr_args:
      NAME: 'convpool'
      feature_type: 'dp_fj'
      anisotropic: False
      reduction: 'max'
    group_args:
      NAME: 'ballquery'
      use_xyz: True
      normalize_dp: False
    conv_args: 
      order: conv-norm-act
    act_args:
      act: 'relu'
    norm_args:
      norm: 'bn'
  cls_args: 
    NAME: ClsHead
    num_classes: 40
    mlps: [512, 256]
    norm_args: 
      norm: 'bn1d'


# lr_scheduler:
sched: 'step'
decay_epochs: 20
decay_rate: 0.7
sched_on_epoch: True
warmup_epochs: 0
min_lr: 0

# Training parameters
lr: 0.002 # LR linear rule. 0.002 for 32 batches
optimizer:
 NAME: 'adam'
 weight_decay: 1.0e-4
 betas: [0.9, 0.999]
 eps: 1.0e-8
grad_norm_clip: 10

datatransforms:
  train: [PointsToTensor, PointCloudRotation, PointCloudScaleAndTranslate, PointCloudJitter]
  vote: [PointCloudRotation, PointCloudScaleAndTranslate, PointCloudJitter]
  val: [PointsToTensor]
  kwargs:
    angle: [0.0, 1.0, 0.0]
    gravity_dim: 1
    jitter_sigma: 0.01
    jitter_clip: 0.05
    scale: [0.8, 1.25]
    shift: [0.1, 0.1, 0.1]

criterion_args:
  NAME: CrossEntropy
  label_smoothing: 0.

