_meta_: {}
adapt_valid_mode: true
adapt_valid_num_epochs_per_validation: [5, 5, 5]
adapt_valid_progress_percentages: [10, 40, 70]
amp: true
auto_scale_allowed: true
auto_scale_max_epochs: 1000
bundle_root: ./ref_api_work_dir\swinunetr_1
cache_rate: 0
ckpt_path: $@bundle_root + '/model_fold' + str(@fold)
data_file_base_dir: C:\Users\<USER>\AppData\Local\Temp\tmp9lauk690\Task04_Hippocampus
data_list_file_path: "f:\\2. python\u6DF1\u5EA6\u5B66\u4E60\u8BFE\u7A0B\\1.\u65E5\u6708\
  \u5149\u534E-pyorch\u6DF1\u5EA6\u5B66\u4E60\u8BFE\u7A0B\\0.\u6DF1\u5EA6\u5B66\u4E60\
  \u548CR\u4EE3\u7801\u603B\u7ED3\\\u6DF1\u5EA6\u5B66\u4E60\u4EE3\u7801\u603B\u7ED3\
  \u6700\u65B0\u7248\\Monai-tutorials\\auto3dseg\\tasks\\msd\\Task04_Hippocampus\\\
  msd_task04_hippocampus_folds.json"
early_stop_delta: 0
early_stop_mode: true
early_stop_patience: 5
finetune: {activate: false, pretrained_ckpt_name: $@bundle_root + '/model_fold' +
    str(@fold) + '/best_metric_model.pt'}
fold: 1
infer: {ckpt_name: $@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt',
  data_list_key: testing, fast: false, log_output_file: $@bundle_root + '/model_fold'
    + str(@fold) + '/inference.log', output_path: $@bundle_root + '/prediction_' +
    @infer#data_list_key}
input_channels: 1
learning_rate: 0.0004
log_output_file: $@bundle_root + '/model_fold' + str(@fold) + '/training.log'
loss: {_target_: DiceCELoss, include_background: true, sigmoid: $not @softmax, smooth_dr: 1.0e-05,
  smooth_nr: 0, softmax: $@softmax, squared_pred: true, to_onehot_y: $@softmax}
lr_scheduler: {_target_: monai.optimizers.WarmupCosineSchedule, optimizer: $@optimizer,
  t_total: $@num_epochs // @num_epochs_per_validation + 1, warmup_multiplier: 0.1,
  warmup_steps: $@num_epochs//100}
mlflow_experiment_name: Auto3DSeg
mlflow_tracking_uri: $@bundle_root + '/model_fold' + str(@fold) + '/mlruns/'
n_cases: 30
num_cache_workers: 8
num_crops_per_image: 2
num_epochs: 1000
num_epochs_per_validation: 5
num_images_per_batch: 2
num_patches_per_iter: 1
num_sw_batch_size: $@num_patches_per_iter
num_workers: 8
num_workers_validation: 2
optimizer: {_target_: torch.optim.AdamW, lr: '@learning_rate', weight_decay: 1.0e-05}
output_classes: 3
overlap_ratio: 0.125
overlap_ratio_final: 0.625
pretrained_path: $@bundle_root + '/pretrained_model' + '/swin_unetr.base_5000ep_f48_lr2e-4_pretrained.pt'
random_seed: 0
resample_resolution: [1.0, 1.0, 1.0]
roi_size: [64, 64, 64]
roi_size_valid: [64, 64, 64]
show_cache_progress: false
softmax: true
sw_input_on_cpu: false
train_cache_rate: $@cache_rate
transforms: {lazy_resampling: false, resample_resolution: $@resample_resolution}
use_pretrain: true
valid_at_orig_resolution_at_last: true
valid_at_orig_resolution_only: false
validate: {ckpt_name: $@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt',
  log_output_file: $@bundle_root + '/model_fold' + str(@fold) + '/validation.log',
  output_path: $@bundle_root + '/prediction_fold' + str(@fold), save_mask: true}
validate_cache_rate: $@cache_rate
