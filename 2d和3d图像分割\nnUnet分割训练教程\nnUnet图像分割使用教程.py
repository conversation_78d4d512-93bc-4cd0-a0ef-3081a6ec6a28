
#%% Autodl的screen后台运行，守护进程，也可以复杂功能多的tmux后台运行
#linux终端安装screen：          
sudo apt-get update && sudo apt-get install -y screen
# 启动screen会话，终端输入    
screen 
# 再按
enter
        
#运行长时间任务（例如训练模型）：    
python train_model.py     
    
# 断开screen会话（按下 Ctrl + A 然后按 D）：     
Ctrl + A, 再按D

# 列出所有会话    
screen -ls 
# 重新连接到会话
screen -r 
# 重新连接到特定会话，如果同时有2个以上运行，根据离开会话的时间判断是哪一个
screen -r 44401 #如44401.pts-6.autodl-container-b49841aad4-7594a09d

# 强制终止所有 screen 会话
pkill screen
screen -ls

#%% nvidia-smi 是 NVIDIA 提供的一个命令行工具，可以显示 GPU 的状态和正在运行的 CUDA 程序
# nvidia-smi查看显卡的cuda版本，一般要安装低于显卡版本的cuda
# nvcc --version看一下系统安装的cuda版本与torch版本是否一致# 
# 不一致的话，请安装与nvcc --version版本一致的torch
nvidia-smi 
nvcc --version
python -c "import torch; print(torch.__version__)" 

#%% Autodl云服务器和本地图像快速传输方法
# （注意需要在您本地的机器的终端上执行）
# 复制您的ssh登录指令，指令格式为：ssh -p 29294 <EMAIL>

# （注意35394为端口号，region-1.autodl.com为远程地址，请更换为您的实例端口和地址）

# 那么scp远程拷贝文件的指令为：
# scp -rP 12603 "J:\nnUNet\nnUNet_results\Dataset503_ap.zip" <EMAIL>:/root/autodl-tmp 

# 如果是将实例中的数据拷贝到本地，那么scp远程拷贝指令为：
# scp -rP 35394 root@************:<实例中的文件/文件夹> <本地文件/文件夹>
# scp -rP 29294 <EMAIL>:/root/autodl-tmp/nnUNet/nnUNet_results/Dataset320 'j:\nnUNet\nnUNet_results'

#%% nnUnet使用教程,autodl云服务器已经跑成功
# 项目地址https://github.com/MIC-DKFZ/nnUNet
#方法1：创建虚拟环境
conda create -n nnUNet python=3.9
# 如果用镜像则直接运行：
conda activate nnUNet
cd autodl-tmp
git clone https://github.com/MIC-DKFZ/nnUNet.git
cd nnUNet
pip install -e .

#安装出现这个报错，不用处理：ERROR: No matching distribution found for batchgeneratorsv2>=0.2  
#报错：缺少blosc2；解决办法：pip uninstall acvl_utils；pip install acvl_utils==0.2


#方法2：
pip install nnunetv2
pip show nnunetv2

# 自己在nnUNet文件夹下创建3个文件夹nnUNet_raw/nnUNet_preprocessed/nnUNet_results
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw
mkdir /root/autodl-tmp/nnUNet/nnUNet_preprocessed
mkdir /root/autodl-tmp/nnUNet/nnUNet_results


# 数据集放在nnUNet_raw下面
# 如数据集Dataset001中存放的是训练集（imagesTr）、训练集标注(labelsTr)、测试集（imagesTs）、dataset.json文件
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320

mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTr
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/imagesTs
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/labelsTr   
mkdir /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset320/labelsTs   #（optional）
#labelsTr中的文件名跟image的名字要完全一样比如 dingdaoping-ap.nii.gz 这种格式

# windows设置环境变量
# 使用如下命令（路径替换为自己的）：
set nnUNet_raw=j:\nnUNet\nnUNet_raw
set nnUNet_preprocessed=j:\nnUNet\nnUNet_preprocessed
set nnUNet_results=j:\nnUNet\nnUNet_results


# linux设置环境变量
export nnUNet_raw="/root/autodl-tmp/nnUNet/nnUNet_raw"
export nnUNet_preprocessed="/root/autodl-tmp/nnUNet/nnUNet_preprocessed"
export nnUNet_results="/root/autodl-tmp/nnUNet/nnUNet_results"

# 数据集转换,image文件名后面加上_0000 暂时不成功，用下面的代码
# nnUNetv2_convert_raw_dataset_from_old_nnunet_format -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset503_pp -o Dataset503_pp #不覆盖原来数据集,id 5031

# 数据集重命名
# mv /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset100 /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset100_HCC

#%%3.终端运行,需要在nnUnet路径下训练
#2d数据集训练
# nnUNetv2_plan_and_preprocess -d 120 --verify_dataset_integrity #120为数据集的编号 Dataset120
# nnUNetv2_train 120 2d 0  
#  
#自己的3d数据集训练，需要在nnUnet路径下训练 ~/autodl-tmp/nnUNet#
nnUNetv2_plan_and_preprocess -d 506 --verify_dataset_integrity #数字598为数据集Dataset的编号 
#如果some image有错误，则使用重采样代码再运行下面代码
nnUNetv2_train 503 3d_fullres 0           #3d_fullres, 3d_lowres, 3d_cascade_lowres 

# 运行上面代码报错：ValueError: mmap length is greater than file size
# RuntimeError: One or more background workers are no longer alive. Exiting.
# 解决方法：删除nnUNet_preprocessed文件夹下的所有的npy文件，进入文件夹，然后执行rm *.npy
# 路径如下 nnUNet_preprocessed/Dataset503/nnUNetPlans_3d_fullres# rm *.npy
#如果还报同样的错，看一下是否空间不够

nnUNetv2_train 110 3d_fullres 0    #3d_fullres, 3d_lowres, 3d_cascade_lowres

nnUNet_train Dataset503 3d_fullres --fold 0 

#nnU-Net会每50个epochs存储一次checkpoint，如果需要继续之前的训练，请将训练命令中添加 --c 参数
nnUNetv2_train 503 3d_fullres 0 --c 

#%%模型预测和评估,成功了
# parser.add_argument('-chk', type=str, required=False, default='checkpoint_final.pth',
#                     help='Name of the checkpoint you want to use. Default: checkpoint_final.pth')
# 训练完成用Default: checkpoint_final.pth
# nnUNetv2_predict -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTs -o /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTs -d 108 -c 3d_fullres --save_probabilities

nnUNetv2_predict -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset503/imagesTs -o /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset503/labelsTs -d Dataset503 -c 3d_fullres -f 0 -chk checkpoint_latest.pth --save_probabilities
nnUNetv2_predict -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset503/imagesTs -o /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset503/labelsTs -d Dataset503 -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities
# nnUNetv2_predict -h 查看更多参数解析
# -i：测试数据地址
# -o：分割结果存放地址
# DATASET_NAME_OR_ID：数据集ID
# CONFIGURATION：使用的什么架构，2d or 3d_fullres or 3d_cascade_fullres等

# nnunet可以自己找寻最优模型并给出推理命令
nnUNetv2_find_best_configuration 4 -c 3d_fullres -f 0 
#nnunetv2会自动给你后续的推理代码#
# An ensemble won! What a surprise! Run the following commands to run predictions with the ensemble members:
nnUNetv2_predict -d Dataset001_Livertumor -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_raw/Dataset001_Livertumor/imagesTs -o E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict2d -f  0 1 2 3 4 -tr nnUNetTrainer -c 2d -p nnUNetPlans --save_probabilities
nnUNetv2_predict -d Dataset001_Livertumor -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_raw/Dataset001_Livertumor/imagesTs -o E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict3d_lowres -f  0 1 2 3 4 -tr nnUNetTrainer -c 3d_lowres -p nnUNetPlans --save_probabilities
nnUNetv2_ensemble -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict2d E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict3d_lowres -o E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/Livertumor_ensemble -np 8
nnUNetv2_apply_postprocessing -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/Livertumor_ensemble -o Livertumor_ensemble_pp -pp_pkl_file E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/ensembles/ensemble___nnUNetTrainer__nnUNetPlans__2d___nnUNetTrainer__nnUNetPlans__3d_lowres___0_1_2_3_4/postprocessing.pkl -np 8 -plans_json E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/ensembles/ensemble___nnUNetTrainer__nnUNetPlans__2d___nnUNetTrainer__nnUNetPlans__3d_lowres___0_1_2_3_4/plans.json
# 预测结果路径"E:\DeepLearning\nnUNet\Livertumor_ensemble_pp"

#模型评估
nnUNet evaluate folder -ref /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset503/labelsTs -pred autodl-tmp/nnUNet/nnUNet_raw/Dataset503/pred-labelsTs -l 1
#只有一个mask即为1  如果有2个mask则为0和1


#%%image和mask划分训练集和测试集
import os
import shutil
import random

# 设置种子确保结果可复现
random.seed(42)

# 指定源文件夹路径
source_dir  = '/root/autodl-tmp/nnUNet/nnUNet_raw/mask/ap'

# 获取所有文件列表
files = os.listdir(source_dir)

# 确保文件按名称排序（可选，根据需求）
files.sort()

# 计算训练集和测试集的划分比例
train_ratio = 0.9
num_train = int(len(files) * train_ratio)
num_test = len(files) - num_train

# 随机打乱文件顺序
random.shuffle(files)

# 划分训练集和测试集
train_files = files[:num_train]
test_files = files[num_train:]

# 创建image训练集和测试集的目标文件夹
# train_dir = os.path.join(source_dir, 'imagesTr')
# test_dir = os.path.join(source_dir, 'imagesTs') 

# 创建mask训练集和测试集的目标文件夹
train_dir = os.path.join(source_dir, 'labelsTr')
test_dir = os.path.join(source_dir, ' labelsTs') 

# 确保目标文件夹存在，若不存在则创建
os.makedirs(train_dir, exist_ok=True)
os.makedirs(test_dir, exist_ok=True)

# 将文件移动到对应的文件夹
for file in train_files:
    shutil.move(os.path.join(source_dir, file), os.path.join(train_dir, file))

for file in test_files:
    shutil.move(os.path.join(source_dir, file), os.path.join(test_dir, file))

print(f"Training set size: {len(train_files)}")
print(f"Testing set size: {len(test_files)}")

#%% imagesTr和imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
# 单序列数据格式如下
# nnUNet_raw/Dataset001/
# ├── dataset.json
# ├── imagesTr #_0000表示第一个序列，0001表示第2个序列，0002表示第3个序列
# │   ├── baohanqing-ap_0000.nii.gz
# │   ├── bianlianying-ap_0000.nii.gz
# │   ├── caomeifang-ap_0000.nii.gz
# │   ├── ...
# ├── imagesTs
# │   ├── caohan-ap_0000.nii.gz
# │   ├── zhagnwen-ap_0000.nii.gz
# │   ├── ...
# └── labelsTr
#     ├── baohanqing-ap.nii.gz
#     ├── bianlianying-ap.nii.gz
#     ├── caomeifang-ap.nii.gz
#     ├── ...

import os

target_dir = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr"

# 遍历目标目录下的所有文件
for filename in os.listdir(target_dir):
    # 获取文件路径
    file_path = os.path.join(target_dir, filename)
    
    # 检查文件是否是.nii.gz格式
    if filename.endswith('.nii.gz'):
        name = filename[:-7]  # 去掉.nii.gz
        ext = '.nii.gz'
    else:
        name, ext = os.path.splitext(filename)
    
    # 新文件名
    new_filename = f"{name}_0000{ext}"
    print(new_filename)
    
    # 新文件路径
    new_file_path = os.path.join(target_dir, new_filename)
    
    # 重命名文件
    os.rename(file_path, new_file_path)

#%% labelsTr重命名，去掉-mask，名字跟imagesTr的名字一样，不包括_0000
# labelsTr名字不包括_0000，否则报错np.str_('zongjinzhuan-pp'), np.str_('zouyudong-pp')]
import os

# 目标目录
target_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/labelsTr'

str = "-mask"

# 遍历目标目录下的所有文件
for filename in os.listdir(target_dir):
    # 检查文件名中是否包含'-mask'
    if str in filename:
        # 获取文件路径
        file_path = os.path.join(target_dir, filename)
        
        # 新文件名
        new_filename = filename.replace(str, '')
        
        # 新文件路径
        new_file_path = os.path.join(target_dir, new_filename)
        
        # 重命名文件
        os.rename(file_path, new_file_path)
        print(f"Renamed: {file_path} -> {new_file_path}")

#%%替换指定路径下文件名中的 "vp" 字符为 "pp"
import os

# 指定路径
path = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr"

# 遍历路径下的所有文件
for filename in os.listdir(path):
    if "-t" in filename:
        # 构造新的文件名
        new_filename = filename.replace("-t", "-t1")
        
        # 完整的文件路径
        src = os.path.join(path, filename)
        dst = os.path.join(path, new_filename)
        
        # 检查目标文件是否存在
        if not os.path.exists(dst):
            try:
                os.rename(src, dst)
                print(f"重命名: {src} -> {dst}")
            except Exception as e:
                print(f"无法重命名 {src}，错误: {e}")
        else:
            print(f"目标文件已存在，跳过重命名: {dst}")
#%% 统计train文件夹内文件数量
import os

target_dir = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/labelsTr"
file_count = len([name for name in os.listdir(target_dir) if os.path.isfile(os.path.join(target_dir, name))])

print(f"文件数量: {file_count}")

#%% dataset.json内容输入，保存为json文件即可
{ 
    "channel_names": {
        "0": "ap"
        "1": "pp"
        "2": "hbp"
    }, 
    "labels": {
        "background": 0,
        "HCC": 1
    }, 
    "numTraining": 144, #训练集imagesTr的图像数
    "file_ending": ".nii.gz",
    "overwrite_image_reader_writer": "SimpleITKIO"
}

#%% 生成数据集的json文件代码 成功
import json

# 定义 JSON 数据的结构
data = {
    "channel_names": {
        "0": "t1"
    },
    "labels": {
        "background": 0,
        "HCC": 1
    },
    "numTraining": 248,  # 替换为训练集 imagesTr 的图像数量
    "file_ending": ".nii.gz",
    "overwrite_image_reader_writer": "SimpleITKIO"
}

#多序列
# data = {
#     "channel_names": {
#         "0": "ap",
#         "1": "pp",
#         "2": "hbp"
#     },
#     "labels": {
#         "background": 0,
#         "HCC": 1
#     },
#     "numTraining":48,  # 替换为训练集 imagesTr 的图像数量
#     "file_ending": ".nii.gz",
#     "overwrite_image_reader_writer": "SimpleITKIO"
# }

# 保存 JSON 文件的路径
output_file = "/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/dataset.json"  # 替换为你的保存路径

# 写入 JSON 文件
with open(output_file, 'w') as f:
    json.dump(data, f, indent=4)

print(f"JSON 文件已保存到: {output_file}")

#%%图像报错处理：使用重抽样，使得image和mask的Direction、origin和spacing一致
import SimpleITK as sitk
import os

# 指定图像和标签路径
image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr'
label_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/labelsTr'

# 获取所有 .nii.gz 文件并排序
nii_images = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])
nii_labels = sorted([filename for filename in os.listdir(label_dir) if filename.endswith('.nii.gz')])

# 遍历图像和标签
for img_filename, label_filename in zip(nii_images, nii_labels):
    # 构建完整路径
    image_path = os.path.join(image_dir, img_filename)
    label_path = os.path.join(label_dir, label_filename)
    
    # 读取图像和标签
    image = sitk.ReadImage(image_path)
    label = sitk.ReadImage(label_path)
    
    # 检查方向、原点和间距是否一致
    if (image.GetDirection() != label.GetDirection() or
        image.GetOrigin() != label.GetOrigin() or
        image.GetSpacing() != label.GetSpacing()):
        
        # 创建一个默认的 Transform（Identity Transform）
        transform = sitk.Transform()
        
        # 使用图像作为参考对标签进行重采样
        resampled_label = sitk.Resample(
            label,  # 要重采样的图像
            image,  # 参考图像
            transform,  # 使用单位变换
            sitk.sitkNearestNeighbor,  # 插值方式
            0,  # 默认像素值
            label.GetPixelID()  # 输出像素类型
        )
        
        # 保存重采样后的标签
        resampled_label_path = os.path.join(label_dir, label_filename)
        sitk.WriteImage(resampled_label, resampled_label_path)
        print(f"Resampled label saved for {label_filename}")

    # 打印图像和标签的形状
    print(f"Image shape for {img_filename}: {image.GetSize()}, Label shape for {label_filename}: {label.GetSize()}")

#%%3.终端运行,需要在nnUnet路径下训练
#2d数据集训练
# nnUNetv2_plan_and_preprocess -d 120 --verify_dataset_integrity #120为数据集的编号 Dataset120
# nnUNetv2_train 120 2d 0  
#  
#自己的3d数据集训练，需要在nnUnet路径下训练 ~/autodl-tmp/nnUNet#
#数字001为数据集的编号Dataset001，id=001，只能是三位数
# DatasetXXX_Name (not Task) where XXX is a 3-digit number.
nnUNetv2_plan_and_preprocess -d 506 --verify_dataset_integrity #数字598为数据集的编号 Dataset001
nnUNetv2_train 506 3d_fullres 0    #3d_fullres, 3d_lowres, 3d_cascade_lowres

nnUNetv2_plan_and_preprocess -d 110 --verify_dataset_integrity #001为数据集的编号 Dataset001
nnUNetv2_train 110 3d_fullres 0    #3d_fullres, 3d_lowres, 3d_cascade_lowres

#nnU-Net会每50个epochs存储一次checkpoint，如果需要继续之前的训练，请将训练命令中添加 --c 参数
nnUNetv2_train 108 3d_fullres 0 --c

#%%模型预测和评估,成功了
# parser.add_argument('-chk', type=str, required=False, default='checkpoint_final.pth',
#                     help='Name of the checkpoint you want to use. Default: checkpoint_final.pth')
# 训练完成用Default: checkpoint_final.pth
# nnUNetv2_predict -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTs -o /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTs -d 108 -c 3d_fullres --save_probabilities

nnUNetv2_predict -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTs -o /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTs -d 108 -c 3d_fullres -f 0 -chk checkpoint_latest.pth --save_probabilities
nnUNetv2_predict -i /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/imagesTs -o /root/autodl-tmp/nnUNet/nnUNet_raw/Dataset108_hbp/labelsTs -d 108 -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities
# nnUNetv2_predict -h 查看更多参数解析
# -i：测试数据地址
# -o：分割结果存放地址
# DATASET_NAME_OR_ID：数据集ID
# CONFIGURATION：使用的什么架构，2d or 3d_fullres or 3d_cascade_fullres等

# nnunet可以自己找寻最优模型并给出推理命令
nnUNetv2_find_best_configuration 4 -c 3d_fullres -f 0 
#nnunetv2会自动给你后续的推理代码#
# An ensemble won! What a surprise! Run the following commands to run predictions with the ensemble members:
nnUNetv2_predict -d Dataset001_Livertumor -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_raw/Dataset001_Livertumor/imagesTs -o E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict2d -f  0 1 2 3 4 -tr nnUNetTrainer -c 2d -p nnUNetPlans --save_probabilities
nnUNetv2_predict -d Dataset001_Livertumor -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_raw/Dataset001_Livertumor/imagesTs -o E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict3d_lowres -f  0 1 2 3 4 -tr nnUNetTrainer -c 3d_lowres -p nnUNetPlans --save_probabilities
nnUNetv2_ensemble -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict2d E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/predict3d_lowres -o E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/Livertumor_ensemble -np 8
nnUNetv2_apply_postprocessing -i E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/Livertumor_ensemble -o Livertumor_ensemble_pp -pp_pkl_file E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/ensembles/ensemble___nnUNetTrainer__nnUNetPlans__2d___nnUNetTrainer__nnUNetPlans__3d_lowres___0_1_2_3_4/postprocessing.pkl -np 8 -plans_json E:/DeepLearning/nnUNet/nnUNetFrame/DATASET/nnUNet_results/Dataset001_Livertumor/ensembles/ensemble___nnUNetTrainer__nnUNetPlans__2d___nnUNetTrainer__nnUNetPlans__3d_lowres___0_1_2_3_4/plans.json
# 预测结果路径"E:\DeepLearning\nnUNet\Livertumor_ensemble_pp"

#模型评估
nnUNet evaluate folder -ref 金标准文件夹 -pred 预测结果文件夹 -l 1
#只有一个mask即为1  如果有2个mask则为0和1

#%% 要检查image和mask目录下的文件形状shape是否匹配
#解决报错ValueError: operands could not be broadcast together with shapes (4,) (3,)
import os
import SimpleITK as sitk

# 目标目录
# image_dir = r"H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\image\hbp"
# mask_dir = r"H:\1.HCC-dataset\734HCC\all-HCC\598HCC-suzhou\108HCC\mask\hbp"

image_dir =  '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr'
mask_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/labelsTr'


# 获取目录下的所有文件
image_files = sorted(os.listdir(image_dir))
mask_files = sorted(os.listdir(mask_dir))

# 检查文件形状是否匹配
for image_file, mask_file in zip(image_files, mask_files):
    image_path = os.path.join(image_dir, image_file)
    mask_path = os.path.join(mask_dir, mask_file)
    
    # 读取图像和掩码文件
    image = sitk.ReadImage(image_path)
    mask = sitk.ReadImage(mask_path)
    
    # 获取形状
    image_shape = image.GetSize()
    mask_shape = mask.GetSize()
    
    # 检查形状是否匹配
    if image_shape != mask_shape:
        print(f"Shape mismatch: {image_file} (image) vs {mask_file} (mask)")
        print(f"Image shape: {image_shape}, Mask shape: {mask_shape}")

#%%检查image和label的shape,保留前3个维度,并保存图像
import nibabel as nib
import os

# 指定图像路径
image_dir = '/root/autodl-tmp/nnUNet/nnUNet_raw/Dataset506/imagesTr'
# image_dir = r"J:\nnUNet\nnUNet_raw\Dataset506\imagesTr"
output_dir = image_dir   # 输出路径

# 确保输出目录存在
os.makedirs(output_dir, exist_ok=True)

# 获取所有 .nii.gz 文件并排序
nii_files = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])

# 遍历排序后的文件
for filename in nii_files:
    # 加载图像
    img_path = os.path.join(image_dir, filename)
    img = nib.load(img_path)
    
    # 获取图像数据
    image_data = img.get_fdata()    
    
    # 只保留前三个维度
    # if image_data.ndim > 3:       
    #     image_data = image_data[:, :, :, 0, 0]  # 维度=5时，直接去掉最后2个维度
        # image_data = image_data[:, :, :, 0]  # 维度=4时，直接去掉最后1个维度

    # 只保留前三个维度
    if image_data.ndim >= 4:
        image_data = image_data[:, :, :, 0]  # 维度=4或5时，保留前三个维度

    # 创建新的 NIfTI 图像对象
    new_img = nib.Nifti1Image(image_data, img.affine, img.header)
    
    # 保存新的图像
    new_filename = os.path.join(output_dir, filename)
    nib.save(new_img, new_filename)
    
    # 打印新的形状
    # print(f"Image shape for {filename}: {image_data.shape}")
    print(f"Image shape for {filename}: {image_data.shape}, Number of dimensions: {image_data.ndim}")

#%% nnUNet在windows本地电脑批量预测，已经成功啦！

#方法1：创建虚拟环境
conda create -n nnUNet python=3.9
# 如果用镜像则直接运行：
conda activate nnUNet

git clone https://github.com/MIC-DKFZ/nnUNet.git
cd nnUNet
pip install -e . #安装环境时，不能有3个下面的文件夹，否则报错
#安装出现这个报错，不用处理：ERROR: No matching distribution found for batchgeneratorsv2>=0.2  
#报错：缺少blosc2；解决办法：pip uninstall acvl_utils；pip install acvl_utils==0.2

# 方法2：pip install nnunetv2==2.5.1

# 自己在nnUNet文件夹下创建3个文件夹nnUNet_raw/nnUNet_preprocessed/nnUNet_results
mkdir j:\nnUNet\nnUNet_raw
mkdir j:\nnUNet\nnUNet_preprocessed
mkdir j:\nnUNet\nnUNet_results

# 设置环境变量
set nnUNet_raw=j:\nnUNet\nnUNet_raw
set nnUNet_preprocessed=j:\nnUNet\nnUNet_preprocessed
set nnUNet_results=j:\nnUNet\nnUNet_results

#%% imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
import os

target_dir = r"j:\nnUNet\nnUNet_raw\Dataset335\imagesTs"

# 遍历目标目录下的所有文件
for filename in os.listdir(target_dir):
    # 获取文件路径
    file_path = os.path.join(target_dir, filename)
    
    # 检查文件是否是.nii.gz格式
    if filename.endswith('.nii.gz'):
        name = filename[:-7]  # 去掉.nii.gz
        ext = '.nii.gz'
    else:
        name, ext = os.path.splitext(filename)
    
    # 新文件名
    new_filename = f"{name}_0000{ext}"
    print(new_filename)
    
    # 新文件路径
    new_file_path = os.path.join(target_dir, new_filename)
    
    # 重命名文件
    os.rename(file_path, new_file_path)

#%%在终端检查cuda是否可用
python
import torch
print(torch.cuda.is_available()) 
import torch
print(torch.__version__)  

nvcc --version #看一下安装的版本

# %% 报错解决和终端预测
#报错1：缺少blosc2  解决办法：pip uninstall acvl_utils；pip install acvl_utils==0.2
#不需要数据预处理，nnUNetv2_plan_and_preprocess不需要运行
#报错2：RuntimeError: Cannot access accelerator device when none is available
#gpu不可用的解决方法：先卸载torch：
pip uninstall torch torchvision torchaudio
#再安装带cuda的torch 2.5.1版本：
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
#报错3：# RuntimeError: Background workers died. Look for the error message further up! 
# If there is none then your RAM was full and the worker was killed by the OS. Use fewer workers or get more 
# 解决办法：数据维度不对，需要根据上面代码，把数据维度调整到3维

#在nnUNet路径下，预测代码，cpu或gpu预测，均成功了
#预测前不需要进行数据预处理，直接预测即可
nnUNetv2_predict -i "j:\nnUNet\nnUNet_raw\Dataset503_ap\imagesTs" -o "j:\nnUNet\nnUNet_raw\Dataset503_ap\labelsTs" -d Dataset503_ap -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities  #cuda预测
nnUNetv2_predict -i "j:\nnUNet\nnUNet_raw\Dataset503\imagesTs" -o "j:\nnUNet\nnUNet_raw\Dataset503\labelsTs" -d 503 -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities -device cpu
nnUNetv2_predict -i "./imagesTs" -o "./labelsTs" -d Dataset503_ap -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities

#%%nnUNet在windows本地批量预测代码,成功了
#方法1：切换到已经安装的nnUnet环境
#先利用上面代码进行imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
#预测前不需要进行数据预处理，直接预测即可
#方法2：不切换nnunet环境，直接安装nnUNetv2运行
# 需要安装nnUNetv2，不用对原始图像进行预处理
#代码适用于nnUNetv2==2.6.0  python3.12
# pip install nnunetv2
# pip show nnunetv2
# pip install nnunetv2==2.6.0   

import torch
print(torch.cuda.is_available()) 
import os
import subprocess

# 设置输入和输出路径
input_dir = r"J:\nnUNet\nnUNet_raw\Dataset504_pp\imagesTs" 
output_dir = r"J:\nnUNet\nnUNet_raw\Dataset504_pp\labelsTs"
checkpoint_path = r'J:\nnUNet\nnUNet_results\Dataset504_pp\nnUNetTrainer__nnUNetPlans__3d_fullres\fold_0\checkpoint_best.pth'

# 设置环境变量
os.environ["nnUNet_raw"] = r"j:\nnUNet\nnUNet_raw"
os.environ["nnUNet_preprocessed"] = r"j:\nnUNet\nnUNet_preprocessed"
os.environ["nnUNet_results"] = r"j:\nnUNet\nnUNet_results"

model_id = "Dataset504_pp" #多序列时Dataset506_ap报错，Dataset506成功
fold = 0

# 构建命令
command = [
    "nnUNetv2_predict",
    "-i", input_dir,
    "-o", output_dir,
    "-d", str(model_id),
    "-c", "3d_fullres",
    "-f", str(fold),  
    "-chk", checkpoint_path,
    "--save_probabilities"
]

# 执行命令并捕获输出
result = subprocess.run(command, capture_output=True, text=True)

# 打印标准输出和错误输出
print("标准输出:")
print(result.stdout)
print("错误输出:")
print(result.stderr)

#%%替换指定路径下文件名中的 "vp" 字符为 "pp"
import os

# 指定路径
path = r"h:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\hbp"

# 遍历路径下的所有文件
for filename in os.listdir(path):    
    # 构造新的文件名
    new_filename = filename.replace("HBP", "hbp")
    print(new_filename)
    # 重命名文件
    os.rename(os.path.join(path, filename), os.path.join(path, new_filename))

# %% 多序列一起训练，效果不如单序列，云端和本地均成功了
# 数据格式如下，多序列image要先进行配准，label只有一个序列的
nnUNet_raw/Dataset001/
├── dataset.json
├── imagesTr #_0000表示第一个序列，0001表示第2个序列，0002表示第3个序列
│   ├── baohanqing_0000.nii.gz
│   ├── baohanqing_0001.nii.gz
│   ├── baohanqing_0002.nii.gz
│   ├── bianlianying_0000.nii.gz
│   ├── bianlianying_0001.nii.gz
│   ├── bianlianying_0002.nii.gz
│   ├── ...
├── imagesTs
│   ├── caohan_0000.nii.gz
│   ├── caohan_0001.nii.gz
│   ├── caohan_0002.nii.gz
│   ├── ...
└── labelsTr
    ├── baohanqing.nii.gz
    ├── bianlianying.nii.gz
    ├── ...

#多序列训练代码同单序列的代码
#多序列本地预测时，注意是多通道的，所以预测单个序列时，需要复制图像2次，处理成3通道的，后面有_0000,0001,0002。

# %% 单序列数据格式如下
nnUNet_raw/Dataset001/
├── dataset.json
├── imagesTr #_0000表示第一个序列，0001表示第2个序列，0002表示第3个序列
│   ├── baohanqing-ap_0000.nii.gz
│   ├── bianlianying-ap_0000.nii.gz
│   ├── caomeifang-ap_0000.nii.gz
│   ├── ...
├── imagesTs
│   ├── caohan-ap_0000.nii.gz
│   ├── zhagnwen-ap_0000.nii.gz
│   ├── ...
└── labelsTr
    ├── baohanqing-ap.nii.gz
    ├── bianlianying-ap.nii.gz
    ├── caomeifang-ap.nii.gz
    ├── ...

#%%nUNetv2训练完成后，本地调用模型权重批量预测代码，成功版，很慢
# 需要安装nnUNetv2，不用对原始图像进行预处理
#代码适用于nnUNetv2==2.6.0  python3.12
# pip install nnunetv2
# pip show nnunetv2
# pip install nnunetv2==2.6.0    
# 激活现有环境
# conda activate nnunet
# conda install python=3.12
# pip install nnunetv2
# pip install --upgrade nnunetv2

from pathlib import Path
import glob
import torch
from nnunetv2.inference.predict_from_raw_data import nnUNetPredictor

def batch_predict_nnunet(input_folder, output_folder, model_folder, folds=(0,)):
    """
    使用nnUNet v2对输入文件夹中的所有3D图像进行批量预测    
    参数:
        input_folder (str): 包含输入图像的文件夹路径
        output_folder (str): 保存预测结果的文件夹路径
        model_folder (str): nnUNet模型权重所在的文件夹路径
        folds (tuple): 使用的模型折叠，默认为(0,)
    """
    # 创建输出文件夹（如果不存在）
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取输入文件夹中的所有.nii.gz文件
    input_files = sorted(glob.glob(os.path.join(input_folder, "*.nii.gz")))
    
    if len(input_files) == 0:
        print(f"在{input_folder}中未找到.nii.gz文件")
        return
    
    print(f"找到{len(input_files)}个文件进行预测")
    
    # 初始化nnUNet预测器 - 根据最新API
    predictor = nnUNetPredictor(
        tile_step_size=0.5,
        use_gaussian=True,
        use_mirroring=True,
        device=torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
        verbose=True,
        verbose_preprocessing=True,
        allow_tqdm=True
    )
    
    # 加载模型
    predictor.initialize_from_trained_model_folder(
        model_folder,
        use_folds=folds,
        checkpoint_name="checkpoint_final.pth",
    )
    
    # 对每个文件进行预测
    for input_file in input_files:
        # 获取文件名（不含路径和扩展名）
        filename = Path(input_file).stem
        if filename.endswith('.nii'):  # 处理.nii.gz文件的情况
            filename = filename[:-4]
            
        output_file = os.path.join(output_folder, f"{filename}_seg.nii.gz")
        
        print(f"处理: {input_file}")
        
        try:
            # 使用nnUNetPredictor进行预测
            predictor.predict_from_files(
                [input_file],  # 输入文件列表
                [output_file],  # 输出文件列表
                save_probabilities=False,
                overwrite=True,
            )
            
            print(f"预测结果已保存至: {output_file}")
                
        except Exception as e:
            print(f"处理{input_file}时出错: {str(e)}")
    
    print("批量预测完成!")

if __name__ == "__main__":
    # 设置参数
    input_folder = r"J:\nnUNet\nnUNet_raw\Dataset506\imagesTs" # 包含待预测图像的文件夹
    output_folder = r"J:\nnUNet\nnUNet_raw\Dataset506\labelsTs"  # 保存分割结果的文件夹
    model_folder = r'J:\nnUNet\nnUNet_results\Dataset504_pp\nnUNetTrainer__nnUNetPlans__3d_fullres'
    
    # 执行批量预测
    batch_predict_nnunet(input_folder, output_folder, model_folder)

#%% python调用命令行进行nnUNet预测，最终成功版，速度快
#先利用上面代码进行imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
#需要安装nnUNetv2，不用对原始图像进行预处理
#代码适用于nnUNetv2==2.6.0  python3.12
# pip install nnunetv2
# pip show nnunetv2
# pip install nnunetv2==2.6.0   

import subprocess
import os

# 使用示例
input_dir = r"J:\nnUNet\nnUNet_raw\Dataset505_hbp\imagesTs" 
output_dir = r"J:\nnUNet\nnUNet_raw\Dataset505_hbp\labelsTs"
model_folder = r'J:\nnUNet\nnUNet_results\Dataset505_hbp\nnUNetTrainer__nnUNetPlans__3d_fullres'

##imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
target_dir = input_dir

# 遍历目标目录下的所有文件
for filename in os.listdir(target_dir):
    # 获取文件路径
    file_path = os.path.join(target_dir, filename)   
    if filename.endswith('.nii.gz'):
        name = filename[:-7]  # 去掉.nii.gz
        ext = '.nii.gz'
    else:
        name, ext = os.path.splitext(filename)
    
    # 新文件名
    new_filename = f"{name}_0000{ext}"
    print(new_filename)       
    new_file_path = os.path.join(target_dir, new_filename) 
    os.rename(file_path, new_file_path)

#定义预测函数    
def nnunet_predict(input_dir, output_dir, model_folder, fold=0):
    """
    使用训练好的nnUNet模型进行预测    
    参数:
        input_dir: 输入图像目录
        output_dir: 预测结果输出目录 
        model_folder: 模型权重文件夹路径,包含checkpoint文件
        fold: 使用的模型fold,默认0
    """
    
    # 设置nnUNet环境变量
    os.environ["nnUNet_raw"] = r"J:\nnUNet\nnUNet_raw"
    os.environ["nnUNet_preprocessed"] = r"J:\nnUNet\nnUNet_preprocessed"
    os.environ["nnUNet_results"] = r"J:\nnUNet\nnUNet_results"

    # 获取模型checkpoint路径
    checkpoint_path = os.path.join(model_folder, f"fold_{fold}", "checkpoint_best.pth")

    # 构建预测命令
    cmd = [
        "nnUNetv2_predict",
        "-i", input_dir,
        "-o", output_dir,
        "-d", "Dataset505_hbp",  # 获取数据集ID
        "-c", "3d_fullres",
        "-f", str(fold),
        "-chk", checkpoint_path,
        "--save_probabilities"
    ]

    # 执行命令
    result = subprocess.run(cmd, capture_output=True, text=True)

    # 打印标准输出和错误输出
    print("标准输出:")
    print(result.stdout)
    print("错误输出:")
    print(result.stderr)

nnunet_predict(input_dir, output_dir, model_folder)

# %%命令行nnunet预测成功
#在nnUNet路径下，预测代码，cpu或gpu预测，均成功了
#预测前不需要进行数据预处理，直接预测即可
# pip install nnunetv2==2.6.0
nnUNetv2_predict -h
nnUNetv2_predict -i "j:\nnUNet\nnUNet_raw\Dataset503_ap\imagesTs" -o "j:\nnUNet\nnUNet_raw\Dataset503_ap\labelsTs" -d Dataset503_ap -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities  #cuda预测
nnUNetv2_predict -i "j:\nnUNet\nnUNet_raw\Dataset503\imagesTs" -o "j:\nnUNet\nnUNet_raw\Dataset503\labelsTs" -d 503 -c 3d_fullres -f 0 -chk checkpoint_best.pth --save_probabilities -device cpu
