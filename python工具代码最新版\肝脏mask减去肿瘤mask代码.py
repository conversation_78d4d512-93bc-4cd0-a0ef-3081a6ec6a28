import os
import nibabel as nib
import numpy as np
from pathlib import Path
import glob

def subtract_tumor_from_liver_mask(liver_mask_dir, tumor_mask_dir, output_dir):
    """
    从肝脏mask中减去对应的肿瘤mask，得到纯肝脏组织mask
    
    Args:
        liver_mask_dir: 肝脏mask文件夹路径
        tumor_mask_dir: 肿瘤mask文件夹路径  
        output_dir: 输出文件夹路径
    """
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查目录是否存在
    print(f"检查肝脏mask目录是否存在: {os.path.exists(liver_mask_dir)}")
    if os.path.exists(liver_mask_dir):
        all_files = os.listdir(liver_mask_dir)
        print(f"肝脏mask目录中的所有文件: {all_files[:10]}")  # 只显示前10个文件

    # 获取肝脏mask文件列表 (格式: 患者名-pp-liver.nii.gz)
    search_pattern = os.path.join(liver_mask_dir, "*-pp-liver.nii.gz")
    print(f"搜索模式: {search_pattern}")
    liver_mask_files = glob.glob(search_pattern)
    liver_mask_files.sort()

    print(f"找到 {len(liver_mask_files)} 个肝脏mask文件")
    if liver_mask_files:
        print(f"找到的文件示例: {liver_mask_files[:3]}")  # 显示前3个文件

    processed_count = 0
    skipped_count = 0

    for liver_mask_path in liver_mask_files:
        # 获取文件名（不含路径和扩展名）
        filename = os.path.basename(liver_mask_path)
        base_name = filename.replace('.nii.gz', '')

        # 从肝脏mask文件名提取患者名 (去掉-pp-liver部分)
        patient_name = base_name.replace('-pp-liver', '')

        # 构建对应的肿瘤mask文件路径 (格式: 患者名-pp-mask.nii.gz)
        tumor_filename = f"{patient_name}-pp-mask.nii.gz"
        tumor_mask_path = os.path.join(tumor_mask_dir, tumor_filename)

        # 构建输出文件路径
        output_path = os.path.join(output_dir, f"{patient_name}-pp-liver.nii.gz")
        
        # 检查输出文件是否已存在，如果存在则跳过
        if os.path.exists(output_path):
            print(f"跳过已存在的文件: {filename}")
            skipped_count += 1
            continue
        
        # 检查对应的肿瘤mask是否存在
        if not os.path.exists(tumor_mask_path):
            print(f"警告: 找不到对应的肿瘤mask文件: {tumor_filename}")
            print(f"患者: {patient_name}, 肝脏mask: {filename}")
            # 如果没有肿瘤mask，直接复制肝脏mask
            liver_img = nib.load(liver_mask_path)
            nib.save(liver_img, output_path)
            print(f"已复制肝脏mask (无对应肿瘤mask): {patient_name}")
            processed_count += 1
            continue
        
        try:
            # 加载肝脏mask
            liver_img = nib.load(liver_mask_path)
            liver_data = liver_img.get_fdata()
            
            # 加载肿瘤mask
            tumor_img = nib.load(tumor_mask_path)
            tumor_data = tumor_img.get_fdata()
            
            # 检查数据形状是否一致
            if liver_data.shape != tumor_data.shape:
                print(f"警告: 患者 {patient_name} 的肝脏mask和肿瘤mask形状不一致")
                print(f"肝脏mask形状: {liver_data.shape}, 肿瘤mask形状: {tumor_data.shape}")
                continue
            
            # 从肝脏mask中减去肿瘤mask
            # 将肿瘤区域设为0，保留其他肝脏区域
            liver_only_data = liver_data.copy()
            liver_only_data[tumor_data > 0] = 0
            
            # 创建新的NIfTI图像
            liver_only_img = nib.Nifti1Image(liver_only_data, liver_img.affine, liver_img.header)
            
            # 保存结果
            nib.save(liver_only_img, output_path)

            print(f"已处理患者: {patient_name}")
            processed_count += 1

        except Exception as e:
            print(f"处理患者 {patient_name} 时出错: {str(e)}")
            continue
    
    print(f"\n处理完成!")
    print(f"成功处理: {processed_count} 个文件")
    print(f"跳过文件: {skipped_count} 个文件")
    print(f"输出目录: {output_dir}")

def main():
    # 设置路径
    liver_mask_dir = r"K:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\masksub\maskliver"
    tumor_mask_dir = r"K:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\masksub\mask"
    output_dir = r"K:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\masksub\liver_only_masks"
    
    print("开始处理肝脏mask减去肿瘤mask...")
    print(f"肝脏mask目录: {liver_mask_dir}")
    print(f"肿瘤mask目录: {tumor_mask_dir}")
    print(f"输出目录: {output_dir}")
    print("-" * 50)
    
    # 检查输入目录是否存在
    if not os.path.exists(liver_mask_dir):
        print(f"错误: 肝脏mask目录不存在: {liver_mask_dir}")
        return
    
    if not os.path.exists(tumor_mask_dir):
        print(f"错误: 肿瘤mask目录不存在: {tumor_mask_dir}")
        return
    
    # 执行处理
    subtract_tumor_from_liver_mask(liver_mask_dir, tumor_mask_dir, output_dir)

if __name__ == "__main__":
    main()
