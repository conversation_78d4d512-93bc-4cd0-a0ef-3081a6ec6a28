{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "\n", "from monai.losses import DiceCELoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    EnsureChannelFirstd,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    RandCropByPosNegLabeld,\n", "    RandShiftIntensityd,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", "    SpatialPadd,\n", "    RandRotate90d,\n", "    CenterSpatialCropd,\n", "    ResizeWithPadOrCropd,\n", "    <PERSON>lip<PERSON>,\n", "    Rotate90d,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "from monai.config import print_config\n", "from monai.metrics import DiceMetric\n", "from monai.networks.nets import UNETR\n", "\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    load_decathlon_datalist,\n", "    decollate_batch,\n", "    pad_list_data_collate\n", ")\n", "\n", "import numpy as np\n", "import torch"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\anaconda3\\envs\\pytorch\\lib\\site-packages\\monai\\utils\\deprecate_utils.py:321: FutureWarning: monai.transforms.io.dictionary LoadImaged.__init__:image_only: Current default value of argument `image_only=False` has been deprecated since version 1.1. It will be changed to `image_only=True` in version 1.3.\n", "  warn_deprecated(argname, msg, warning_category)\n"]}], "source": ["train_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-50,\n", "            a_max=100,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        # Spacingd(keys=[\"image\", \"label\"], pixdim=(2.0, 2.0, 0.1), mode=(\"bilinear\", \"nearest\")),\n", "        # CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        # CenterSpatialCropd(keys=['image', 'label'], roi_size=(512,512,12)),\n", "        # Resize(spatial_size=(400, 400, 12)),\n", "        ResizeWithPadOrCropd(keys=[\"image\", \"label\"], spatial_size=(512, 512, 16)),\n", "        Rotate90d(keys=[\"image\", \"label\"], k=1),\n", "        RandCropByPosNegLabeld(\n", "            keys=[\"image\", \"label\"],\n", "            label_key=\"label\",\n", "            spatial_size=(96, 96, 16),\n", "            pos=1,\n", "            neg=1,\n", "            num_samples=8,\n", "            image_key=\"image\",\n", "            image_threshold=0,\n", "        ),\n", "        # RandAffined(\n", "        #     keys=[\"image\", \"label\"],\n", "        #     mode=(\"bilinear\", \"nearest\"),\n", "        #     prob=1.0,\n", "        #     spatial_size=(512, 512, 16),\n", "        #     translate_range=(40, 40, 2),\n", "        #     rotate_range=(np.pi / 36, np.pi / 36, np.pi / 4),\n", "        #     scale_range=(0.15, 0.15, 0.15),\n", "        #     padding_mode=\"border\",\n", "        # ),\n", "        # RandGaussianNoised(keys=[\"image\"], prob=0.10, std=0.1),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[0],\n", "            prob=0.10,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[1],\n", "            prob=0.10,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[2],\n", "            prob=0.10,\n", "        ),\n", "        RandRotate90d(\n", "            keys=[\"image\", \"label\"],\n", "            prob=0.10,\n", "            max_k=3,\n", "        ),\n", "        RandShiftIntensityd(\n", "            keys=[\"image\"],\n", "            offsets=0.10,\n", "            prob=0.50,\n", "        ),\n", "        # CenterSpatialCropd(keys=['image', 'label'], roi_size=(352,352,16))\n", "    ]\n", ")\n", "val_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        # Spacingd(keys=[\"image\", \"label\"], pixdim=(2.0, 2.0, 0.1), mode=(\"bilinear\", \"nearest\")),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-50,\n", "            a_max=100,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        # CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        # Resize(spatial_size=(400, 400, 12)),\n", "        ResizeWithPadOrCropd(keys=[\"image\", \"label\"], spatial_size=(512, 512, 16)),\n", "        Rotate90d(keys=[\"image\", \"label\"], k=1),\n", "        # CenterSpatialCropd(keys=['image', 'label'], roi_size=(352,352,16)),\n", "        # Flipd(keys=[\"image\", \"label\"], spatial_axis=[0]),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset:   0%|          | 0/8 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████| 8/8 [00:00<00:00,  8.29it/s]\n", "Loading dataset: 100%|██████████| 6/6 [00:00<00:00,  8.59it/s]\n"]}], "source": ["data_dir = r\"F:\\sth\\23Fall\\fcpro\\brain_image2\"\n", "split_json = \"./dataset.json\"\n", "\n", "datasets = data_dir + split_json\n", "datalist = load_decathlon_datalist(datasets, True, \"training\")\n", "val_files = load_decathlon_datalist(datasets, True, \"validation\")\n", "train_ds = CacheDataset(\n", "    data=datalist,\n", "    transform=train_transforms,\n", "    cache_num=8,\n", "    cache_rate=1.0,\n", "    num_workers=0,\n", ")\n", "device_ids = [i for i in range(torch.cuda.device_count())]\n", "train_loader = DataLoader(train_ds, batch_size=1 * len(device_ids), shuffle=True, num_workers=0, pin_memory=True,drop_last=True, collate_fn=pad_list_data_collate)\n", "val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_num=6, cache_rate=1.0, num_workers=0)\n", "val_loader = DataLoader(val_ds, batch_size=1* len(device_ids), shuffle=False, num_workers=0, pin_memory=True, collate_fn=pad_list_data_collate)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image shape: torch.Size([1, 512, 512, 16]), label shape: torch.Size([1, 512, 512, 16])\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABQEAAAIQCAYAAAAxV6jcAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOz9d5RdZ3k2/l+n1+m9aN<PERSON>tuciWLRtbGEyxg2kGftjLEJpNSCDEJgEHQpwXiE0Av+FNAm+CwZBvliF5QwisUFacGOJQAomNSVxwr5KsOkXS1FP2qb8/xPXMvbdG0ozajI6uz1qzrDlzzt7P3ucIxpfv+7lD9Xq9DhEREREREREREWlY4cVegIiIiIiIiIiIiJxYCgFFREREREREREQanEJAERERERERERGRBqcQUEREREREREREpMEpBBQREREREREREWlwCgFFREREREREREQanEJAERERERERERGRBqcQUEREREREREREpMEpBBQREREREREREWlwCgFFTjNf/epXEQqFsG3btsVeioiIiIiInEBH+7v/y1/+cpxzzjnHdS0rVqzA9ddff1yPKSILoxBQRERERERERESkwSkEFDnNvPOd70ShUMDy5csXeykiIiIiIiIicpJEF3sBInJyRSIRRCKRxV6GiIiIiIiIiJxEqgQUOc0E9wVZsWIFXv/61+MnP/kJLrzwQqRSKWzYsAE/+clPAADf/va3sWHDBiSTSWzatAkPPfSQ73iPPPIIrr/+eqxatQrJZBK9vb34jd/4Dezbt++gc/McyWQSq1evxpe//GXccsstCIVCBz33//2//4dNmzYhlUqhvb0db33rW7Fjx47jfj9ERERERE4X3/ve9/C6170O/f39SCQSWL16Nf7kT/4E1Wp1zuc/8MADePGLX4xUKoWVK1fijjvuOOg5nufhj//4j7FmzRokEgksW7YMf/AHfwDP80705YjIAqkSUETw3HPP4W1vexve97734R3veAf+7M/+DFdddRXuuOMO/NEf/RF+53d+BwBw22234dprr8XTTz+NcPjAf0O45557sGXLFrz73e9Gb28vHn/8cXzlK1/B448/jp///Ocu4HvooYfw6le/Gn19fbj11ltRrVbxyU9+El1dXQet59Of/jQ+/vGP49prr8Vv/uZvYmxsDH/1V3+Fyy67DA899BBaW1tP2r0REREREWkUX/3qV5HNZnHTTTchm83iRz/6ET7xiU9gamoK/+f//B/fc8fHx/Ha174W1157LX79138d3/zmN/H+978f8Xgcv/EbvwEAqNVqeMMb3oD//M//xHvf+16ceeaZePTRR/G5z30OzzzzDL773e8uwlWKyCHVReS0cuedd9YB1Ldu3Vqv1+v15cuX1wHU7733XvecH/zgB3UA9VQqVX/hhRfc41/+8pfrAOo//vGP3WP5fP6gc/zDP/xDHUD9pz/9qXvsqquuqqfT6fquXbvcY88++2w9Go3W7f8Ubdu2rR6JROqf/vSnfcd89NFH69Fo9KDHRURERERkbsHf/ef63f1973tfPZ1O14vFonvsZS97WR1A/c///M/dY57n1Tdu3Fjv7u6ul0qler1er//d3/1dPRwO13/2s5/5jnnHHXfUAdT/67/+yz22fPny+nXXXXccr05EFkrtwCKCs846C5s3b3bfX3zxxQCAV77ylRgaGjro8S1btrjHUqmU+3OxWMTevXtxySWXAAAefPBBAEC1WsW///u/401vehP6+/vd89esWYPXvOY1vrV8+9vfRq1Ww7XXXou9e/e6r97eXqxduxY//vGPj9dli4iIiIicVuzv7tPT09i7dy9e+tKXIp/P46mnnvI9NxqN4n3ve5/7Ph6P433vex9GR0fxwAMPAAC+9a1v4cwzz8T69et9v7u/8pWvBAD97i6yxKgdWER8QR8AtLS0AACWLVs25+Pj4+Pusf379+PWW2/FN77xDYyOjvqePzk5CQAYHR1FoVDAmjVrDjp38LFnn30W9Xoda9eunXOtsVhsPpckIiIiIiIBjz/+OD72sY/hRz/6Eaampnw/4+/u1N/fj0wm43vsjDPOAABs27YNl1xyCZ599lk8+eSTc27xA+Cgfz8QkcWlEFBEDjkt+FCP1+t19+drr70W9957Lz7ykY9g48aNyGazqNVqePWrX41arbbgtdRqNYRCIdx9991znj+bzS74mCIiIiIip7uJiQm87GUvQ3NzMz75yU9i9erVSCaTePDBB/HRj370qH9337BhA/7iL/5izp8HiwpEZHEpBBSRozY+Po4f/vCHuPXWW/GJT3zCPf7ss8/6ntfd3Y1kMonnnnvuoGMEH1u9ejXq9TpWrlzp/kujiIiIiIgcm5/85CfYt28fvv3tb+Oyyy5zj2/dunXO5+/evRu5XM5XDfjMM88AAFasWAHgwO/uv/zlL3H55Ze7gYAisnRpT0AROWqs1LOVgQDw+c9//qDnXXHFFfjud7+L3bt3u8efe+453H333b7nvvnNb0YkEsGtt9560HHr9Tr27dt3HK9AREREROT0MNfv7qVSCV/84hfnfH6lUsGXv/xl33O//OUvo6urC5s2bQJwoCto165d+Ou//uuDXl8oFJDL5Y7nJYjIMVIloIgctebmZlx22WX47Gc/i3K5jIGBAfzbv/3bnP818ZZbbsG//du/4dJLL8X73/9+VKtVfOELX8A555yDhx9+2D1v9erV+NSnPoWbb74Z27Ztw5ve9CY0NTVh69at+M53voP3vve9+PCHP3wSr1JERERE5NT34he/GG1tbbjuuuvwu7/7uwiFQvi7v/u7g/7DO/X39+NP//RPsW3bNpxxxhn4x3/8Rzz88MP4yle+4vbpfuc734lvfvOb+O3f/m38+Mc/xqWXXopqtYqnnnoK3/zmN/GDH/wAF1544cm8TBE5DIWAInJMvv71r+MDH/gAbr/9dtTrdbzqVa/C3Xff7ZsCDACbNm3C3XffjQ9/+MP4+Mc/jmXLluGTn/wknnzyyYMmkf3hH/4hzjjjDHzuc5/DrbfeCuDAfiKvetWr8IY3vOGkXZuIiIiISKPo6OjAXXfdhd///d/Hxz72MbS1teEd73gHLr/8clx55ZUHPb+trQ1f+9rX8IEPfAB//dd/jZ6eHnzhC1/Ab/3Wb7nnhMNhfPe738XnPvc5/O3f/i2+853vIJ1OY9WqVfi93/s9be8jssSE6oeK/UVEToI3velNePzxxw/aR1BEREREREREjh/tCSgiJ02hUPB9/+yzz+Jf//Vf8fKXv3xxFiQiIiIiIiJymlAloIicNH19fbj++uuxatUqvPDCC/jSl74Ez/Pw0EMPYe3atYu9PBEREREREZGGpT0BReSkefWrX41/+Id/wPDwMBKJBDZv3ozPfOYzCgBFRERERERETrBFbQe+/fbbsWLFCiSTSVx88cX4xS9+sZjLEZET7M4778S2bdtQLBYxOTmJ73//+7jgggsWe1kiIiJynOj3exERkaVr0ULAf/zHf8RNN92EP/7jP8aDDz6I8847D1deeSVGR0cXa0kiIiIiInKU9Pu9iIjI0rZoewJefPHFuOiii/CFL3wBAFCr1bBs2TJ84AMfwB/+4R8uxpJEREREROQo6fd7ERGRpW1R9gQslUp44IEHcPPNN7vHwuEwrrjiCtx3331HfH2tVsPu3bvR1NSEUCh0IpcqIiJyyqvX65ienkZ/fz/C4UXdCUREGtSx/n4P6Hd8ERGRhTia3/EXJQTcu3cvqtUqenp6fI/39PTgqaeeOuj5nufB8zz3/a5du3DWWWed8HWKiIg0kh07dmBwcHCxlyEiDWihv98D+h1fRETkeFjI7/inxHTg2267DbfeeutiL0NEROSU1tTUtNhLEBFxDvU7/kvwWkQRW4QViYiInDoqKOM/8a8L+h1/UULAzs5ORCIRjIyM+B4fGRlBb2/vQc+/+eabcdNNN7nvp6amsGzZshO+ThERkUai9joROVEW+vs9cOjf8aOIIRpSCCgiInJYv5rwsZDf8RdlY6B4PI5Nmzbhhz/8oXusVqvhhz/8ITZv3nzQ8xOJBJqbm31fIiIiIiKyNCz093tAv+OLiIicbIvWDnzTTTfhuuuuw4UXXogXvehF+PznP49cLod3v/vdi7UkERERERE5Svr9XkREZGlbtBDwLW95C8bGxvCJT3wCw8PD2LhxI77//e8ftJmwiIiIiIgsffr9XkREZGkL1ev1+mIvYqGmpqbQ0tKy2MsQERE5pUxOTqrdTkSWLP6O/3K8UXsCioiIHEGlXsZP8L0F/Y6/KHsCioiIiIiIiIiIyMmjEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGpxCQBERERERERERkQanEFBERERERERERKTBKQQUERERERERERFpcAoBRUREREREREREGtyCQ8Cf/vSnuOqqq9Df349QKITvfve7vp/X63V84hOfQF9fH1KpFK644go8++yzvufs378fb3/729Hc3IzW1la85z3vwczMzDFdiIiIiIiILJx+vxcRETk9LDgEzOVyOO+883D77bfP+fPPfvaz+Mu//EvccccduP/++5HJZHDllVeiWCy657z97W/H448/jnvuuQd33XUXfvrTn+K9733v0V+FiIiIiIgcFf1+LyIicnoI1ev1+lG/OBTCd77zHbzpTW8CcOC/Evb39+P3f//38eEPfxgAMDk5iZ6eHnz1q1/FW9/6Vjz55JM466yz8N///d+48MILAQDf//738drXvhY7d+5Ef3//Ec87NTWFlpaWo122iIjIaWlychLNzc2LvQwRWcIW6/d7YPZ3/JfjjYiGYifk+kRERBpFpV7GT/C9Bf2Of1z3BNy6dSuGh4dxxRVXuMdaWlpw8cUX47777gMA3HfffWhtbXW/IADAFVdcgXA4jPvvv3/O43qeh6mpKd+XiIiIiIicWCfq93tAv+OLiIicbMc1BBweHgYA9PT0+B7v6elxPxseHkZ3d7fv59FoFO3t7e45QbfddhtaWlrc17Jly47nskVEREREZA4n6vd7QL/ji4iInGynxHTgm2++GZOTk+5rx44di70kERERERE5BvodX0RE5OQ6riFgb28vAGBkZMT3+MjIiPtZb28vRkdHfT+vVCrYv3+/e05QIpFAc3Oz70tERERERE6sE/X7PaDf8UVERE624xoCrly5Er29vfjhD3/oHpuamsL999+PzZs3AwA2b96MiYkJPPDAA+45P/rRj1Cr1XDxxRcfz+WIiIiIiMgx0O/3IiIijSO60BfMzMzgueeec99v3boVDz/8MNrb2zE0NIQPfvCD+NSnPoW1a9di5cqV+PjHP47+/n43YezMM8/Eq1/9avzWb/0W7rjjDpTLZdx4441461vfOu/JYSIiIiIicnzo93sREZHTw4JDwP/5n//BK17xCvf9TTfdBAC47rrr8NWvfhV/8Ad/gFwuh/e+972YmJjAS17yEnz/+99HMpl0r/n7v/973Hjjjbj88ssRDodx9dVX4y//8i+Pw+WIiIiIiMhC6Pd7ERGR00OoXq/XF3sRCzU1NYWWlpbFXoaIiMgpZXJyUntuiciSxd/xX443IhqKLfZyRERElrRKvYyf4HsL+h3/lJgOLCIiIiIiIiIiIkdPIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCIiIiIiIiIiIg1OIaCIiIiIiIiIiEiDUwgoIiIiIiIiIiLS4BQCioiIiIiIiIiINDiFgCKniXA4jHg8jkgkglAotNjLEREREREREZGTKLrYCxCRo8cwLxwOI5vNoqWlBc3NzWhpaUFTUxNisRgAIBr1/1WfmJjA+Pg4SqUSSqUSyuUyarUaSqUSCoUC6vU6QqEQarUakskkMpkMPM9DrVYDAMzMzCCfz5/cixURERERERGRo6YQUOQUEIlE3FdLSwt6e3sxNDSE1atXY8WKFVi5ciUGBwexYsUKtLW1zfu4xWIR+XwexWIRk5OT2LVrF8bGxlAqlZDL5TA5OYlKpYJqtYrJyUnkcjnk83ns27cPU1NTyOVyGBsbw/j4OKrVKur1Omq1Gur1ujtHvV73fS8iIiIiIiIiJ59CQJEliK27mUwGPT09OPPMM7F+/Xqcc8452LRpE1atWoVIJHLM50kmk0gmkwCA/v5+nHnmmfN6ned5GBkZwcjICHbt2oXh4WHs3bsXY2NjGBsbw/T0NAqFAjzPw969e7Fv3z6USiVUKhUXEpZKJVdZKCIiIiIiIiInlkJAkSUiGo0inU6jvb0da9aswZlnnomNGzfixS9+MdavX7/Yy/NJJBIYGhrC0NAQLrroooN+XqvVMDw8jJGREQwPD2N0dBT79+/H/v37kcvlkMvl8OCDD2L37t2YmZlBoVBAuVxehCsREREREREROT0oBBRZJKz2a2pqQl9fH84991ysW7cO5557Ll70oheht7d3sZd41MLhMPr7+9Hf3z/nz8fHx/HYY49hy5Yt2LFjB55++mk89thj2LlzJ/bv368KQREREREREZHjTCGgyEkUCoWQSCTQ0tKC1atX4/zzz8emTZuwcuVKnHvuuWhvb1/sJZ4UbW1teOlLX4qXvvSlAIC9e/fiiSeewPPPP4+f/exneOihh/DEE0+gVCot8kpFREREREREGoNCQJGTJJvNYnBwEOeffz5e//rXY+3atVi2bBl6enrclN/TVWdnJy677DJcdtlleM1rXoNt27Zh27Zt+Kd/+if84he/wPbt2xd7iSIiIiIiIiKnNIWAIidYJpPBhg0b8LrXvQ6vfe1r0dXVhd7eXsRiscVe2pLU29uL3t5eXHLJJbj00kuxY8cOfPe738U//dM/YcuWLYu9PBEREREREZFTkkJAkROkqakJF154IX7jN34Dl112Gdrb25HNZhd7WaeUZcuWYXBwEBs2bMD111+Pb3zjG/jKV76CkZGRxV6aiIiIiIiIyCklVK/X64u9iIWamppCS0vLYi9DZE5dXV248MIL8a53vQtvfOMbEY/HEYlEFntZDaFcLmNsbAyf//zncfvttyOfzy/2kkROKZOTk2hubl7sZYiIzIm/478cb0Q0pI4JERGRw6nUy/gJvreg3/HDJ3hNIqeFSCSCrq4uvPrVr8aXvvQl/Mu//Ave+ta3IpVKKQA8jmKxGPr7+/HZz34WTz75JH79138dyWQS4bD+p0xERERERETkcNQOLHIMYrEYuru7cdlll+ENb3gD3vKWt5z2Qz5OlqGhIXz961/HT3/6U9x222249957MTMzg1qttthLExEREREREVlyFAKKHIVoNIre3l5cfPHFuOqqq3D11Vcv+f3+arUaQqFQw4WUl112Gc4//3x87Wtfw9///d/jkUceUZuwiIiIiIiISIBCQJEFCIVC6OrqwiWXXII3v/nNuPLKK9Hb23vSzl+v112IV6/XwS09y+UySqUSSqUSqtUqarUa6vU6wuEwQqEQqtUqPM9DJBJBKpVCNptFMpk8aes+0ZqamnDjjTfi8ssvx+233467774bL7zwAqrV6mIvTURERERERGRJUAgoMk+RSASbN2/GO97xDrzyla/E2rVrT9i5KpUKisWiC/Xq9Tqq1SpKpRIAIBwOo1KpuNbXXC6HqakpzMzMwPM8VKtVVKtVZLNZRKNRFItFFwomk0n09vaiq6sLnZ2dJ2zPwnq97tZfLpcBHGifTqVSJ6wa8cwzz8Stt96KV7ziFbjzzjvxs5/9DFNTUyfkXCIiIiIiIiKnEoWAIvOwatUqXH311bj22mtx/vnnn7DgbGJiAvl8Hvl8HtPT0y4IrNfrqNVqLuCr1+sol8sIh8MIh8MoFAool8sol8uoVCoA4ILDeDwOz/PcOXj8kZER9PX1YXBw8Kinhc7MzGBqagrFYhHFYtFVIVYqFVQqFZRKJfd9PB5HOp1GJpNBPB5HLBZDU1MTWltbj8etczo6OnD11VfjzDPPxF133YU777wTTz311HE9h4iIiIiIiMipRiGgyGG0tLTgzW9+M9773vdi3bp1aGtrOyHnmZmZwZ49ezAxMQHP85DP51EsFlGr1VAul1Gv1xGNRlGv1+F5HsrlMqrVKiKRCCKRCDzPc1WBDAxrtRoKhYILA22LcKlUQi6Xw8zMDCYmJjAwMIDly5fPe72lUgm7du3Crl27MDMzg3w+j5mZGVSrVVQqFRQKBbc+Tu9Np9NIJpMIhUKIxWKIRqPIZrNob29HJpNBa2srmpqajtuk37POOgvLli3DFVdcgc985jO45557VBUoIiIiIiIipy2FgCKHcMkll+CWW27BhRdeiI6OjhNyjlKphOHhYYyOjmL//v2oVCoIhUKusg+A29eOQz3YSss/B8M//plVgsCBVmZWEDIMZKXexMQE9u/fj+npaZxzzjnzWvOTTz6JkZERjI+Pu1CSrb+VSgUzMzOo1+uIx+OuBZnXwv0JAaBQKGB6ehqRSASJRALNzc1ob29HR0fHcRm00tTUhAsuuAB/9Vd/hTvvvBNf/OIXsWvXrmM+roiIiIiIiMipRiGgSEBTUxOuueYafPjDH8b69euPW2Va0MTEBLZs2YLJyUnkcjlUKhW3f144HHZDP0KhkBsCwhANOLAvYDQadWEgQ7hQKOQqBNm2zKEhNhhkYFev11EsFpHP51GpVLBx48ZDrrlareKBBx7Avn37kM/nXVjJycPxeByRSMSFjdFo1F0Ln0OVSgWRSAT5fB7VahXhcBhTU1MYHx/H7t27kU6n0dbWhsHBwWN+D/r6+vChD30IF198MT74wQ/iscceO6bjiYiIiIiIiJxqFAKKGAMDA7jlllvw7ne/+4Ts+8fAbevWrdi3bx8mJiZQKpXcPn6sqItGoy5Q4wAQ7g3I4SAMCWOxmAsCGbrxcWC2+q5SqbgQ0AaL5XLZ12Jcr9excePGg4Z31Go13HfffRgZGXHBH4O9RCLhnsPw0bYrc0Ix1851lctl3zr5c8/zUCgUUCgUMD4+ju7ubvT29h7TQJFUKoXLL78cX/3qV/Hxj38cP/3pT5HL5Y76eCIiIiIiIiKnEoWAIr/CAPA3f/M3j/uxK5UK8vk89uzZg5GREUxNTbmgy7bqRiIRxGIxlMtlX+svcKDyj48Ds+2/rObj63k8G/6x0i7YPsyQ0fM8hMNh7Nu3D48//jiampqwZs0ad27P8/Dzn/8ce/bscWvgnoWJRALJZNINLqnVakgmk75QMRwOu7XyzwB818kgNJFIoFqtun0RAbj9C3t7e5HJZI6pMnDTpk34i7/4C/zZn/0Zvv3tb2N8fPyojyUiIiIisqSFI4gu60flhR2LvRIRWQIUAooA6O7uxsc+9rHjGgCyam9mZgbbt2/HyMgICoWCm/hrKw0ZljEQ48+4LyAAV3nHdltWCDLg415/DNhCoRDC4bCb1BuPx10FoA0XY7EYUqmUmy7seR62bt2KlpYWxONxTE9P46GHHsL+/ftRLpfdgJJisYhIJOLWxVCR7b827LPVh9z3kI/ZxzkAhSEfh6JwQMrExAS6urrQ3d2NdDp91O/N+vXrccstt6Crqwt33nknRkZGjvpYIiIiIiJLVbSnC8NfSKHzqsVeiYgsBQoB5bTX3t6Oj370o/jt3/7t43bMcrmM/fv3Y8eOHdizZw8mJyfdnn0M4RKJBBKJhAvKgNl9+hiS2QpBhm3RaPSgYSDBgI1hnD0fA0NgNiAE4B63bcdPP/00ZmZmUC6XsWfPHpRKJaRSKcTjcV91nw0duY5QKOTahdmybENBXoNdpw03Pc9zfy6Xy4hEIiiVSpicnMT09DT27t2L0dFRrFixAm1tba6deKEGBwfxv/7X/0J3dze+8IUvYOvWrW69IiIiIiKNoLJnGJ1XDS/2MkRkiVAIKKe1rq4u/NEf/RE++MEPHpfjlctlTE5OYnJyElu2bMHY2BhmZmZQKpVcJR73/ANm98Vj+MQWXgZ4ANxAELb8ep7nwjcOBwFmKwX5GlYB1ut1t29gtVp1gRurAaPRqAvS2KI8NTWFdDqNfD6PiYkJxGIxxGIxF9ox7LPtxrYakC3JoVDIvS5Y7Qf4h55w/Tac5Ovtnoi8x3v37sWqVavQ29uL1tbWo3q/stksPvShD2FgYAC33XYbHn/8cV/1pYiIiIiIiEijUAgop62enh58+tOfxvXXX39cjjcxMYH9+/djdHQU09PTGB8fR6FQcHvdMUDjvngA3EAOG+bZqb4MAvlaACgWi24vvUQi4QI2VtdxuAiDRYZ2duIw/8zX2YpEBnpsDeZrbcWh/eJzAfj2JuTreF0cgGJ/xnvDY4TDYXft0WjUXVOlUnFhKasM2ea8d+9eDA4OYmBg4KirAq+99lq0t7fjs5/9LP7rv/4L+Xz+qI4jIiIiIiIislQd/e76IqeoUCiE3t5e/PEf/zHe/va3H5cpwKOjo9i2bRt27tyJ/fv3Y3JyEtVqFbFYDMlk0oV1kUgE0WjUtffaSjoGaFwjK+1sqy6DQIaD/DMr7Fjdx2Efnue5ijzAHzDayj2+xk70LZVKLkxkmGcr+kKhEOLxOIDZvQsZQjKktNWJbP+1jzPM8zwPxWLR165s90W0Q0YYRFarVUxNTWHbtm345S9/iYcffviY9va7/PLL8ZnPfAZXXnnlUYeJIiIiIiJLTeTsdXj+zy9x3xevehGivT2LuCIRWSyqBJTTSiwWQ39/P37nd34Hb3nLW5BMJo/5mDt27MDw8DAmJiZcxZqt1AuHwyiVSq6SzrbC2go/BnW2HdWGegzjGMixSi6fz/uGcgDwhWX2WAz3gsfmukOhEJLJJCKRiLsOno/BG4/PYHNmZsY9zufZtmS2N8fjcVelWKvVEIvFfG3RXJM9R6VSce3TDFDtnokMRycnJ5HP5zE+Po6+vj6sXLkS2Wx2Qe9jKBTCBRdcgI9//OOYmZnBPffcs6DXi4iIiIgsReW2FFZv3Om+nx6IIPt4ahFXJCKLRSGgnDai0SgGBgbw9re/HW9729vQ3t5+TMer1+vYtWsXduzYgampKRSLRRfW2cCOuBcfqwBZVWcn5XIvPdtCTHyMe/hx30DuEciWXg7esK2/PJ/93g72sNOC7fWFw2FXFceJxgzk+HgkEnFhHa+ZVY7ci9C2Dtvj23sVj8ddyMeKRLYA85y2opHVkjY8HR0dRalUQi6XQ29vL5YtW7agSs9wOIzzzjsPn/rUp1AoFPCf//mf836tiIiIiIiIyFKmdmA5LUQiEfT19eEVr3gF3vKWt2BwcPCYjlcsFvHss8/iueeew+TkJDzPc3ve8QuAC6+CYVq5XD7oOXaPPgAHDfmwE38ZLgbbdHlcPp5MJl0gaL/ITh9OJBKuVde2FnNvQLsmXgeDPq6ZlXm2BZjr4j2yx+BzGSra43DvQ147j2P3DeQ94Fqr1aobZrJ79248+eST7j7PVzgcxkUXXYRPfvKT2Lhx44JeKyIiIiKy1IVqR36OiDQmhYByWmhubsamTZvwzne+Exs2bDimY3meh61bt+Lxxx/H8PAw8vm8C7dYAcfAii22rN6zQR3bcgG4vfHYChysXgtWDHJ4B4M4PofThhkEck9Cuwdh8Ph2T0IGgAB8A0Hi8TiSyaRvT8K5gk6GcTwO18TgkxV+DAft/bJ7/fExG/gxPLSP2VCTVYie52FiYgLDw8PYtWsXHnzwQezcudMdcz5CoRBe8YpX4NZbb8X69evnrJQUERERETkVdX7lPlS2bFvsZYjIIlhQCHjbbbfhoosuQlNTE7q7u/GmN70JTz/9tO85xWIRN9xwAzo6OpDNZnH11VcftFn/9u3b8brXvQ7pdBrd3d34yEc+suBqHZH5SiaT2LBhA379138dr3jFK47pWJ7nYefOnXj++efdPnScAMzAjBhwMeCrVqtuLz/bFmyHf3ie5wvXGD4xHOPPggNFALhqOrbssqqOjzGYY5AXiUR8gz2CQRwr99iqa8M4PmYrB+3xuG8f/8wKQbJrZ0gaCoVcS7UNFoPhJL9n6Me18Dp5Pfl8HpOTkxgfH8e2bdvw6KOPYu/evb6hKkfyhje8ATfffDOWL1+uIFBERBqWfscXaWyRQhnP7uxe7GWIyBKwoBDwP/7jP3DDDTfg5z//Oe655x6Uy2W86lWvQi6Xc8/50Ic+hH/+53/Gt771LfzHf/wHdu/ejTe/+c3u59VqFa973etQKpVw77334mtf+xq++tWv4hOf+MTxuyqRXwmFQlizZg1e//rX48orrzymY83MzOD555/H9u3bUS6XXZttsVh0k3gB+Crg+MUgkO2+bA3mzxnC2X0Cg625tVrNVRfy+AzQ7Pe20o7DNWwAZ6vw+DMGiAwyeS4GdQzy7PRgW5kY3J/PVhbaAI+hHnE/Qh4nGPZxvfZ+sGKSex9y30GeKziV2PM8NzTkhRdewJ49e3zDV47kXe96F37/938fPT2aoCYiIo1Jv+OLNLb6A49j7XUPLvYyRGQJCNXn2x83h7GxMXR3d+M//uM/cNlll2FychJdXV34+te/jmuuuQYA8NRTT+HMM8/Efffdh0suuQR33303Xv/612P37t3uX6rvuOMOfPSjH8XY2JirJDqcqakptLS0HO2y5TTS3t6Oa665BjfddBPWrVt3VMeoVquYnJzE888/j3379rmAzPM8FAoFF8oBcBVpDKkYljEIjEajLqiyU3kZriWTSRfk8WcMDmu1mgsabVtvsM2XgRn3+WOwZ6sAbcDHddZqNUxMTKBUKiGRSAA4sO9fPB5HU1MTEokEqtUq4vG4Oyafx4CQId/09LQ7v93Lz7YMcy3RaNT9veeQEwaVrGrkc3kdrDTkMBGugfeAQ0wSiYTba5DfZ7NZ9Pb2oq+vz4Wx8/GFL3wBN998M2ZmZo7mYySyJExOTqK5uXmxlyEiS9xi/47/crwR0dD8/z9aRETkdFSpl/ETfG9Bv+Mf056Ak5OTAOCmrD7wwAMol8u44oor3HPWr1+PoaEh3HfffQCA++67Dxs2bPBV1Vx55ZWYmprC448/Pud5PM/D1NSU70vkSDKZDC6++GK8+93vPuoAcGZmBjt27MBTTz2F3bt3I5/Pu1ZcGzox1GP4x2o07tnHqjw7PISvZSUbg0K+1lYRMtSzlXS28o0VhDYMZOBm248B/1ReAL59/mzlXiwWQyqVQjKZdGEb9zVk6BdsreVjNuwjW9lnB32wEpJBIUNGW1kYfH0wGLSDVkKhEGKxmGthDofDKJVKKBaLKBaLmJmZwZ49e7Br164FVQTeeOON+MAHPjDv54uIiJyq9Du+iIhIYzrqELBWq+GDH/wgLr30UpxzzjkAgOHhYcTjcbS2tvqe29PTg+HhYfecYFsdv+dzgm677Ta0tLS4r2XLlh3tsuU0kUgkcM455+Daa6/FRRddtODX12o17Nu3D9u2bcPWrVuxf/9+V5VXKpXgeZ6vApCBVLFYRKlUckEZQ0Fgdl8/hmy2Oo/hWrFY9A3V4OtstZ9tj+VzuAcg18FqumBrsJ1AbKcB230LeT0cKpJKpRCLxXwVg9wD0e5VyDXaqj/bSmwnBttqwOAxbPVfcJqxHZBi9/+zU4dZJWj3ReTPqtUqisUiJiYmsHPnTkxMTMx7WAgA3HTTTXj7298+/w+SiIjIKUa/44uIiDSuow4Bb7jhBjz22GP4xje+cTzXM6ebb74Zk5OT7mvHjh0n/Jxy6gqHw1ixYgWuvPJKvOlNbzpo0u6RVCoVjI6OYvv27dizZw+mp6fdHoDhcNhN9rXDLWy1H/f+YxAW3BcPgKv8Y2BlK+eCwR4A137MY9vWXoZqdgAIK9wYWnK/QLKVgcGpxhzwwefwz2yrtWsLhUIol8vI5XLwPM+3hyGAgyoZg9fI8/M6uA4bXNrX2C+2R5dKJXd8Ps9OKbYhod1X0fM8jIyMLGjD8s7OTtxyyy3HPGBGRERkqdLv+CIiIo3rqELAG2+8EXfddRd+/OMfY3Bw0D3e29uLUqmEiYkJ3/NHRkbQ29vrnhOcJMbv+ZygRCKB5uZm35fIoTQ1NWH9+vW45pprDvov1kdSrVYxPDyMXbt2YWpqCsVi0Te4A4AL4NjSygo2BlG2TZdBXVAw8GLlXTQa9U3NtWGhrQRkmyu/+D0wO/XXViMGq91s6MdzxWIxVx3IANAGnAB8LbesCMzn876AkuvhfbPPt4NN7PpZuceKQxtc8jk8nt1HkcexoSHfIx6HAWCpVPK9rl6vI5fLYWRkZEHVgKtXr8af/umfYmhoaN6vERERORXod3wREZHGtqAQsF6v48Ybb8R3vvMd/OhHP8LKlSt9P9+0aRNisRh++MMfuseefvppbN++HZs3bwYAbN68GY8++ihGR0fdc+655x40NzfjrLPOOpZrEUEikcDAwADOOuusgz6f85HP57Fz505MTk76KsxCoRA8z3OBnK2YY4XgXJN6AbgBFsGqQNv2a6sG+TMAvufYSj7bHgzMVtxFIhGk02kkEglXGcd9+mxAZo/Jc0ejUdf+a9fC1/O62Q5tQzhbtWjXw9fZawnunciAz+6HWKlUXChq9w4MBn7xeNxdK3/GsHCuINa2HrOKcXR0dEF7A4ZCIZx33nn4xCc+cciQV0RE5FSi3/FFREROD9EjP2XWDTfcgK9//ev43ve+h6amJre/R0tLC1KpFFpaWvCe97wHN910E9rb29Hc3IwPfOAD2Lx5My655BIAwKte9SqcddZZeOc734nPfvazGB4exsc+9jHccMMNbtKoyNEIhULo7OzEhg0b8PrXvx6pVGpBr/c8D1u3bsX09DSKxeJBbcS1Wg3FYtHtccfgi1Vvdn87BlQMEBnqAfC1nwZDQOBA669tgZ1rGIgNACuViqvcs9V13LcvHo/7BnbYcJABHtdtJ+naPQf5ZzsMhNfPv7e8RoZxDPf4M7uXn92rz7ZRE8PVer3uqgz5uK1c5NRkTle2AWKtVnNhJn/OAS58z7gH41NPPYUNGzYcNMzkUOLxOC6//HJcc801+Na3vrWgSkIREZGlRr/ji4iInB4WFAJ+6UtfAgC8/OUv9z1+55134vrrrwcAfO5zn0M4HMbVV18Nz/Nw5ZVX4otf/KJ7biQSwV133YX3v//92Lx5MzKZDK677jp88pOfPLYrkdNeU1MT1qxZg4suughnnXXWgvYCrNfr2LNnD/bt24dCoQDP89wee3bohW3JZXsrq/54vkgk4vYM5MAPe5659sOzWFloW2WDr4tGo6691VYc1mo1FAoFd15WLAL+Vl6y1Yu2Yg8AYrGY+z4YsNmpwzweB4LYacd270L+OZFIuHDPBqC8Tjv4pFgswvM8tx5WSTIYZSgKzFYfMqz0PM+tww5SYWDKkJRVgRMTE2hra5v3Z2bFihW4/vrr8eCDD+K5556b9+tERESWGv2OLyIicnoI1U/BEpapqSm0tLQs9jJkCYlGo1i1ahUuv/xyXHfdddi0aZOvPfVIZmZm8PDDD2NiYgLFYtEFWolEAtVqFYVCwe1Tx73ygNnKPA7OYOCVz+ddOzGr5SKRCPL5PAqFApLJJIAD1Yd2Lzy73x+DuXw+72sHthWHDNpsIMcvu88gh2iwMg44EKrZFls7/bhWqyEejwM4EN7xPgBw62NFX6VSQaFQQLFYdJWFdm8/O4E4Ho8jlUqhXq+jUCigXC4jHo+7wJXnYws1h3rY6kHeAxvm2UrFYBUiqyEZrnJ9fD4ApNNpDAwMYN26dS78nI99+/bhIx/5CP72b//WV50pslRNTk5qzy0RWbL4O/7L8UZEQ/P//2MREZHTUaVexk/wvQX9jq8NreSUFwqF0NbWhjPOOAPnn38+1qxZs6AAEAC2bNmCXC7n9ovjAAt+scrM7ukXnILLisF6ve6q4ezgEGB2TzpW8HGdrOrj4Itqteoes69jwAfADfKw+9LZn9vHbKvxXNWHlt2XkGu0+wLa6woOK2EYGRycYvcjtBOO+Rjvob1+3iOGq2zDJh7L7jHIc9vJzcGpyAxEWWXIScHj4+PYvXv3vD8zANDR0YHNmzejs7NzQa8TEREREREROdkUAsopL5lMYuXKlTjvvPNw0UUXLbhKdM+ePdi1axfy+bwLyBgceZ7nKuMAuOo2BmQAXAUbW09ZLZhMJl2Fm606s+29DOds8Mhz2pCNQVmwBZdhm500zEo3VrTxtawgZGUcB27w3AzGGL5xbfafrKZjSGivNZlM+vby47F57/haXq8N6Oxehwxc+bgNB3lP7CRmPm5bn7le2wZsQ0feX7sf4eTkJHbv3o2pqakFfX4uv/xybNy4cUHt5yIiIiIiIiInm0JAOaWFw2H09/fj7LPPxiWXXILVq1cvuArw2WefxdjYGAqFAnK5nKvks6EVMNumalt2WW1m96djYGUDOR4nEom46j27xyAwG4SVSiWUSqWDhk3YajtWs9lhH7YCj4GUrVy0rbA2qCsWiygWi+5+2mpCvp6TgznsxO7zZ6+TgRuDQjsMxF7nkSosbQWlDWbt2mzoaqsqbQWjDUyDVZm2XZrry+Vy2L1794KmBa9atQoveclLkMlk5v0aERERERERkZNNIaCc0tra2nDWWWfh/PPPx/r165HNZhf0+u3bt2Pv3r1ur75isegL5rifHavGEokEUqmUC/FstZudhMv2VDv1F4AvxLMtv8EWXYaLdo872/7KoIvtsolEwh03eF4eh0M94vG4C9DK5TJyuRyKxaILPznYhPeCAScAt6cgA77gcA7eKxv8cS1cA19rq/9sCDhXu3EkEvFVGdoA1Q4isVWEfP/sWuz5+F5Eo1F3znK5jL1792JkZGRBn6OrrroKQ0ND854uLCIiIiIiInKyLaxkSmQJaW9vx9q1a3HOOefgRS96EVauXLmgEGZsbAzbt29HuVxGMpl0AZbnea6Sz1b7cfhHJBJxVXjA7FRchnCshGO7qg3sABwUhLGlNlhhyLDKvpbnqtfrriqPr2EbMdfCEI3rYHVbJBJxASDXwz+zEtEGoQz+7P6HFqsHOZGX1xIcVmL3COTPbfUfr4vHtNONeSxWD/JYDPe45uCeiLzPNky0rcSlUskFgQwXp6amsGvXLmQymXlPCz7nnHNw6aWX4plnnkGpVJrvR1BERERERETkpFEloJySuru7sX79emzYsAHnnnsu+vv7F7QnW6lUwtatWzE6OopwOIxsNotkMummzTI0y+fzruU2HA67Vl2GYnbvPbunna3us98Hh30A8FXF2YEjc1US8nVsVeY12/0GuRYGgvYYdtgJjxOPx90k4EKhcFCVnB3MYcPPYLWh53nuvjBYZEDJlmKGleVy2VUlMpCzVYAA3PsR3CuQa7JrsIGqHRZiKwNtyzD3BSwUCr7Al/doZGQE+/fvn/fnKRKJ4JprrsHQ0NC8XyMiIiIiIiJyMikElFNOa2sr+vv7sWbNGmzYsAHr16/HsmXL5v36er2O559/Hrt27UKxWHTVfcDB+84xvGOVYD6fh+d5viEZrBDk6xmI2YCpVquhWCyiUCj4hn4EK+DssAyGWDwuAzV+Mciye+LZtmDi62xQxsdjsRiSySSy2axrE2Y4x+NlMhmkUikA8FUusg2Xx+V1AXCBIO9rMOAD/HvyMSjkuvk4ABfq8Z7ZKkY7GCRY0cjzc308lq0YtJWXdg/CQqGA7du3LygIvOKKK/DSl77UBaoiIiIiIiIiS4lCQDmltLe3o7OzE21tbejt7cXKlSvR398/79fXajXs2rULw8PDLrBihVu5XHbBlw3IGEaxeo5Va3bPOhtesf20WCwil8uhUqm4gI0Bnh0ewpZeWw1ng0c+Zgdy8J82eGOAx3AtHo/79idkUMjWWg4p4RRjDv+wVXZ2/0IArmrQBoq8d1wjwzQGjzbotG3FNrRLJBLuuFwbA06u11b1MVzke0c8JwDfsBKGh1yrDWAZKtqgsl6vI5fLuUEx83XNNddg7dq12htQRERERERElhyFgHLKSCQSrm03mUyiu7sbAwMD6O7untfrq9Uqdu3ahW3btvlaZdkey4DKthUzvGL1GEM2hnjEoIjtqpwAbCvVbDDEYMoOqmBIxuczzOPx7TARhmz23BxsEQytOPiC67fXF6zoi8fjvsCO52RAxsEotl2Zx2GIZ9djKxltdWLw+LZdOBQKoVwuo1AoHNTmHAxNGeoFqwpthSLfJzuF2Aa6/HNwH8ZyuYwXXngBMzMz8/p8AcBrX/taXHbZZWhtbVUQKCIiIiIiIkuKBoPIKSOZTCIejyObzaKrqwuDg4MYHByc12trtRpGRkawZcsWTE9PA/DvxcfQiIEXQy9bGWbDMgY8DI/svnPhcBjxeNy9jiEU/2yHX/D4ttKPVW928AUr7QAgn8+jVqshm8260IthGc/JQMu2AtsBHjYoZKVjMJC0x7HXzwo6OzGYVYO8BwwkeT02ELQDQWwrMY/H15ZKJcRiMdf2DMC3P+Fc992+nwwJbXWgfT/tABee034WKpUK9u/fj3379iGTyRy0N+OhvOMd78Czzz6LX/7yl8jn88jn8wuqJhQRERERERE5ERQCyikhHo+jqakJ3d3dWLt2LS644AKsXbsWXV1dR3xtvV7H2NgYduzYgVwu51pQ7Z57ttotGMoxwGF4xbZaO+mWbMWbDRHt3oAMoLg2Po/nspNzGa7ZUI7VbaxgZDBmQ0cbDPJ6bYjFdXCddh22XZd/tsfjmhhW2utgIMjKPntfbOWirbizlZY8vg3n7Dp5bNvOW6lUUCwWkUgkDtp30J43ODnYBobBe2QrM7dt24aenh63L+KRXHLJJXjZy16GUqmE559/HsVi8aCJyiIiIiIiIiInm9qBZcmLRCJobm5Gf38/1q1bhxe96EXYuHEjzjjjjHm9fnp6Gtu2bcP4+Livos622B6KrdqzwRADKIZGdiotJwDbajd+2WCMx2fgZafn2jWxZZfBG9uhGXgFQ0EbktmwkYGXXbOt9GPwGRzUwXUfKpRjFR2nKNshJXwN18DrDQZ6NhTkz9gKHWwpZgtvMFzj48FAj+dkWzS/57FtazerDu2AlqmpKWzfvt3Xznw44XAYr3nNa9DR0eELmkVEREREREQWkyoBZclLJpPo6enB6tWrsXHjRpx99tkYHBxENps94mvr9Tqee+45TExMzLmPnm1ptRNlGWyxfZcVZwBc5RjDKjsgBJht3bUVgQwEuXdeMHxkMGb3+wvurWfbcrlGuzeerSC07a9sbZ1rnz87EIPP5xrZTszv4/G4uyd2Hz0AB+3FZysaAfjWz2MC8O2rGHy+DQ3t+8ngrlQquYEibBvmfWCYacNYO8wlOATE7kXI1/L+V6tV7Ny5E5FIBGvWrDniZw4AzjjjDHR0dPiuT0RERERERGQxqRJQlrRIJIK2tjYMDQ1h3bp1WLt2LXp6eua9F+Do6Cj27t3rJs3a4RCxWMxVm9m9+uxwDgZItsIM8Ff+sZWV+wAy+LHVZjwn9wtMJBKuMs1W8dlqOltpyAq1YrEIz/PcQAx7PQy/bIUjX8dwkdN02a4b3CvPriEej/umCdu99ezUYQDuXnINtmqP7JAV+5i957yvdt9Cu3ciJ/zaycq8T3YqMjDbBm2vzeK12AEtNnS0VZPT09PYvn07hoeH5/W5SyQSOO+889Dc3Dyv54uIiIiIiIicaKoElCUtFouht7cXq1evxqpVq9DZ2Ynm5mYX3h3J6OiorxLMTs9NJBIAgFKphEql4qrJbPUf971j9ZmtpqPgXnM2BLPVgwBcwMZAipV8wcpEnocVigy4GDwCcNN4Ga5xIrHd55ChXrlcdtV69Xod0WgUyWTSV3XIIMzzPLc2G6jZEM/uu8dKRFvhZ8NC3j8e005lZmUgwzbee95THs+2YnPNNkC0lZZ8z7hf4lz7NFYqFVftZ6cLA3DXkkwmXdVlqVTC9PQ0du/ejWw2e8Qq1Hg8jte//vX49re/jZ07d6oiUERERERERBadKgFlSUqlUkgkEujp6cHatWuxbt06DA0NobW1Fc3Nzb7Q7VDYHhqsJGOQxmo6W1XGYC24nx8AV7FnqwTJtvfaffX4Z4ZRrPizAZgNxlh5x+Ox2i4SibjWVzuFNzj5167F7pFnB22EQiG3xyCDMFZKVioV5HI5TE1NIZfLoVgs+ioO+ZpoNIpUKuXaofl6AL7KPMDfCmzXGfyzHaBiw1e+FwwIec/sEBA78Tc42IX3hEGcvec2aOQ6bCWonbIcCoUwNTWFnTt3zusz3NzcjE2bNqG7u3tezxcREREROSFCIez/jc345s77sONjL17s1YjIIlIIKEtONBpFPB5HT08P1q1bhzVr1mD58uVobW1FJpOZ95TW7du3o1Qq+arwbOUc22oZYjE8sgMtGBayUpCvDQ7zYIAGwP2MLbL8YvstA0Dbfmuv3T6XYRrXYoM4wD+4JNiyTHavPgC+qjhW8Xmeh1KpBM/z3ARlG+TxPrCSkYFkPB53QSCDQhu2cY020GSYatfGP3OgCO8r1xucqhysBOS18xr4HjHA43tnz2n3BuTxGPzZKce2CrFQKGBqagojIyNH/PxlMhm85CUvQW9v77xCaxERERGRE6F26Xm4+9Y/w/nf/iCWferexV6OiCwihYCy5KRSKbS0tGBoaAhr167F8uXL0dbWhkwm4yr15mN8fBzT09MuvLKDPtgSysCnVqu5qje7LxyDJRsMMVBjxZ0NFu2efrZ9tV6vuwEWdr89O92XYSAr/9iOyoEhDCrttGAArmotWNXIMDI4pReY3dPQ7rNXKBTgeR5isZirxLT749k2aFuFZ0O0Wq2GfD6PQqGAUqkEYLbyzoaCwfbnYODJ8M7eI+73ZwePBINCno/7GFr2ubZqkK/hfbGDUhi+2snPhUIB4+PjKBaLh/38JRIJnH322VizZo32BhQRERGRRRGKxbH3vDTe/sxbsPZ371/s5YjIIlMIKEtKJBJBNptFd3c3VqxYgRUrVqCvrw+tra1IJpPIZDK+abGHUigUXKjFKjEGbdwDkCEQA7Nyueyq/mzIxIo3YLZaje2hbK1l0GYDvbmGXNhwiedOJpO+kC4YtDHgs9Nzg9OLbaut3U+QGK7xmlg9x70EY7GYe002m0U6nXbViMGKPt4/DinhPQJmh2wwLGMoaaso7YTiYLBq23DtfoIMBYNTk3mfbAsy7xHDvlKp5JuCHGz3tu3DwcnQtgqQ5yuXy5iZmcHY2NgR9/prbW3FBRdcgI6ODlUDioiIiMhJF+nvwUP/64uLvQwRWSIUAsqSEYlEXBXgsmXLsHr1aixfvhwdHR1IpVJIp9MubDqSbdu2oVAoAPBP/rVtsQyA2N7KFtRgkBaPx10Qxio8fs+wyLaYMnSyQyrsvnh8nd1DD/BXyNnAy+5zxwCLFYqs9rMhGJ/HYIuhnA3deB67d6Ft8WWQxqo64roZ1tXrdRfwMay0lZT2um3wyGPxz3bPQBsS2gEqPL7d39AGhXaoir0+stcdrN60A1dsi7c9v33fi8Ui9u/fj+np6cN+DltaWrBx40b09/fPe5iNiIiIiIiIyImgEFCWhGg0inQ6jebmZnR2dmJoaAgrV65ET08P0um0G9oRbPGcy9jYGHbs2IGpqSnfXnS2tZX4Z7uHHyfn2um7dl++4NALWw1mA0Qei/vuVatVt+cfX8t/2hZVHt8Ov7DVewDcNFtW6wXbc237rm1vtd8zMLRhnA0TbcDIc/Ex23rMII0hHM8brHpklaQdqEJ27Xw9n8fz8x6wYrBQKPiGltjz2ZZpOzzFDkaxgaPd8zAYfAbx+YVCAblc7rCfxVgshmXLlmHt2rVqCRYREREREZFFdeh/0xU5SUKhEOLxODKZDHp7e7Fs2TIXADY3N7vKtPlWUu3cuROjo6Pu+0ql4sIghmCsiovFYq4KzFaK2ao6/pl73NkWXw7psPvXVatVVzVmp9AyWGKQxhDQtrcyKAzuJ8jgrVarufZh7mFowyyGnbYa0FYp2j0D7RAPW20YrHwMhnE2WCUbNPI9tVWXFvc8ZFBnr5XHYnsyn8vv2bJtQ0eyFZW2pdpORWbIGNzjkK/n54z3m/eF57P3olgsYnp6GsViEclk8pCfx/7+flx00UV46KGHMDk56YLcaDR6xHZiERERERERkeNFIaAsKrbiZjIZdHR0YHBwEKtWrcLAwIBvH8DW1tZ5tQKPjY1hdHTUtcuWSiXX6sqqPFb0cegGW3jt9F6GX6wMs4Ef233tYAq2ydqQyLaXspKxWCweFPQxELPVfLZ6jsez+wFWq1UUi8WDBn/w2rg2npsBpq1i5HFs9Rv3REwmk77qQztll6+1oV2wYpJhpF1/cDgIB58wFGM4Z6f42r0BAbgqQIah8Xjc135s93PkfY7H4we1Jc91L4IViLZVmZWdvH7eY05VPlwI2NLSgjPOOAMrV67Ec889567XToUWERERETlR/mayF5EPpKHfPkVEIaAsmlAohEQigebmZrS1taG7uxsDAwMYHBxEa2srMpkMkskkstksstnsEY9Xq9UwNjaG8fFxN7gin88jHA7D8zykUikA/nCHFXl2UjBDGlYgMqxhCMiwylaJMWSzbanBYM/uo2fDrGDARwzBgmwgxio/ns+GehxwAcCFlsFJvQy4bLswK+BsZSPX7XmeO7ddK0M0rsnuf2ifw9faoSc2tLTtxbYyc669/4KVhnyODQFtGMn3iGEyw0Ouw56TlZu2YtBWUDIotnsJHkooFEJ3dzeWLVuGbDaLXC7n27dQRERERORE2ltpQvWJZxZ7GSKyBCgElEUTiUSQTqeRyWTQ2dmJwcFBDA4Ooqury7UBp9NpF94dye7du/Hkk09i7969LvRKJBIuDCoWiwDgwi3bGsvAzu4RR9xbzu6Dx+PaYK9SqbhqObuvHjDbQsz96IDZveU8z/NNqrUDQQD4wjwejxVudp120AUDtWBlm92vkM8NDtJghaQ9LtupC4WCr82Yr+G1MDxkyGmr9DzP84WePL+9F7w/NnwF4Atq7V6IDPbssBcGdfZ6bTho920MYtBo3yfeA97fUqnkjlUoFDAzM4Pm5ubD7iM4MDCA9evXo7e3F2NjY+7abCWkiIiIiIiIyImkwSCyKEKhEJLJJOr1OtLpNLq6ujA4OIj+/n60t7cjlUohkUggnU4jkUgc8XiTk5N45plnsHfvXuTzeRSLRYRCIaRSKTQ1NSGTybjn2ooz7m3HfeBs4MWKL34x1LLDOxjGMfhjCGUrBW2VnZ0ya1uUOWiiVCr5qs64/x8DK1YjAvBV83FNwX357DAUhp3BVl57TbzvrCpkS7WtjmOoFgzRglOKeW7eR7s+uy7eO7tOhnF8PdcXi8WQTqfdXo6syLMtxHwNW6G5Xrsvo32PbEs225Z5nTa0rNVqKBQKLkzm52dyctKFkYfS3t6OwcFBLFu2zBcuKgAUERERkROqWsPjM32LvQoRWSJUCSiLol6vo1gsIpPJoL29HT09Pejp6UFbW5sLedgKfCTlchkvvPACtm/fjnK5jGQy6aryEomEbyAIALdHnA2xbLWYnaDLtlJgdm8/26Jq98hjdZrdxy5YHWarDBk42Wo5W7HG0MuGkcBsgMVqOp7ftskSqwFtVVyw3dZOCWZVInCgSq1UKrn9E23FIPcJ5J58XEOpVPKFofZ6WcEXDEb52mDbNI9xuH37GN7ZIM9WJQbfH75Hc+0NyHtuw1K7JvvZ5evL5TIKhcK8Bnz09PRg+fLl6Orqwo4dO9z7E4/HXZu1iIiIiMjxVNm5CyObF3sVIrJUKASURZNIJNDT04Pe3l50dHSgtbUV2WzWVcq1trYe8Ri1Wg07duzAI488gpmZGTfZ1vM8X7hjJ8wy/GIFGkMjBjkM4hgkMhzi8+xedfy53cuOIaCtGATg9hssFouu2pDhGjDbVmv3GmQIFhyOwX3tbNss/2lDT1uxaI/BnzM8A2aHanCYij2ODQ953Ta8ZFVcqVRyeynyuFwTA1G7HyLvHe8rQ0uu21blcX0MMJPJpG/gB88THNASDHR5vHK57EJBnovrsyEh18qfJZNJJBIJt9ZgteSh9Pf3Y+3atVi1ahV27drlrp9hq/YIFBERERERkRNJ7cCyKCKRCDo7OzEwMID+/n709PSgvb3dte52dnYeduIq7dixAw899BAKhcKcgZ8NiIIhjW0B5XPtPna2ddUGUfY5DMcYetk95RhkBSvX7F56DJPYbsz2YoZXdkiJ3buQ04GD++xx3fyywaXdp8+2KjPYi8fjrrXZVhDaYDM4/ReY3e/QtizX63UUCgXf/oTByj472INhn33/bIjIykdePx+z04ZtUGjPw/fQvs883lxr43FYfWmDWdtWbfcInJiYOOK0356eHvT396O3t9eFuAwj5/NZFxERERERETkWCgFlUWSzWfT29qK/vx99fX2uFTibzbo/H8n27dvx6KOPYnp6GvF43LX+2hCMgY+dZmur+PjFVlFbPVitVl0IZ6fT8rUMrdgay8d4jOCef2wftlVkwOw+dgyabNsqQ0wbPAEH2nHz+byb5AvAvSZ4bbayzYaAZPcEtCGkbZe218vn2wEeds8+npePc9iIrTjkc3n9vFesyGNwyupCvo82fJurpZsDYXgsXme1WoXnefA8z+27aKskg3tA2mpOhn+sHGUQbENhBp6HE4vF0N3djaGhIQwODrrH+dkREREREQGA3R958WIvQUQalNqB5aSLRqOuDZgBYGdnJ5qbm93XkezcuRNPP/00pqenfeEbAF9QxSAnFoshHo/7AiAALnCyr2dVnp0YzKq7RCLh9sFjK22wmo3H5752DPGA2WpEVuUx8EskEi5wtKEQQ75gUMhj2gnAtmXVhlc8F8O34EARtj7bANW2BTOMtNfH67WBXbAyzwaQc10P76ENaHkvWQXJacDBoStzDTjhGhkucv3BfQIZ1qVSKbcOG+rZYDD4mSJ7Dxait7cXa9aswbp167B7927k83kXUAaDVxERERE5PXU8puFxInJiqBJQTqpQKISmpiZ0dXWht7fXtQFns1k0Nzejq6vL18Y5l1KphOeffx5jY2MuaOOxg9NnWdFlB0fwOXYPPWA2xEskEkgkEmhqanITjIPDQSqVCorFogup7B54tn2WoZddmw2keH6GiwDmrFaz7b0M1fh8tuN6nue7HzYY5GsYqNkBJLwuW3nIsDCRSCCZTPoGk7BSkK2yNuyzVYc2tON6+Bzupce18RrsvQYOVM/Z6cE2iLOVh/ye7zHZoDBYqWnvqQ0yWbkY3N+PoWRwb0S7L+SRtLe3o7OzE4ODgxgaGvJdU3CIjIiIiIicnhJ3//diL0FEGpRCQDmpMpkMOjo6XBCybNkydHZ2IpVKoaOjA4lE4ojHeOKJJ7Bjxw7MzMzMOZWV4Rsr/Gxrrw2h2B4KwIU+4XAY8XjcDX9IJBKuijCZTCISiaBYLKJQKKBYLLpWVWB2aEQ8HvdVjjEwCw4asRV73I+QARWfywDMPs+GdGxTZkhn9yvkvbAtxdxb0AZgvBYbmDJg4x6B3IPQVg8yeLQt07zvwam/th2aVXzcL5Dhl91/j+9dMpl0lX32/Q1OPLbDPWxVYXDIh91zkPfN7vNnA83gBGE+zsByrmDySDo6OtDS0oL29nb09vb6Kg2PFH6LiIiIiIiIHAuVnshJEwqF0NraisHBQXR1daG1tdUNAmlpaTnicIR6vY6nn34aW7ZswcTEhDsmW2k57RWAC/Ps8IZyueym8TLIYUsuH7dDImzYY9tgWRFmB4NUq1WUSiU3udcOBwFm20btfng8HgO2UqmETCYzZ1WdZafqMgjkue112LCN1wzMVjPa0K5YLCIWiyGdTrt7FolEUC6X3fAR3kdO/+V7EtxjkaGdrcq0ezPyvJVKxYWCPCdDUAaLPKcNSHldwQpDHsvu3WgnM3NgiW2RZjAZHJpiKyUZylrBVuz5BnihUAiZTAZtbW3IZDJoamrC5OSk714caW9BERERETl95N98MaYHI+j5y3sXeyki0gAUAspJE4lE0N7ejq6uLvT396O9vR3xeBypVAqdnZ0HVXxZpVIJW7duxTPPPIPR0VEUCgVX1War+tg+ypAqOKnXBiz8M9tzgdnqMQaE3KstlUohkUi40Ccej7twjmESQ0gbEAGzbap8LgBfJRrDJzt4g+uy4ZndDy/YempbnG0rMYOlUCjk2m9tBVs4HEYymTxoX0Ebltmpv5bd1y84GMROT+b6bMsvn8vj2+o8vl+FQsEFntVq1e2faNfBkJBVjHzvbfDIz54dVmInBNvA1w5fsffATkS2nxHiOucjnU6jubkZ2WzWfS7shGeFgCIiInK6ivR0ozoyutjLWFLS374f6cVehIg0DPWfyQnH6rju7m709PRgYGAAPT09aG1tRWtrK7q7u5FOH/7/2vbt24ft27dj7969mJ6edqGf53koFosu0CmXy26Ih21XZZUcQx47gZf7+xWLxYMmw9qpwwzz2KbKx23LrQ32SqXSQa2iNkQCZoM+thszcOJx2BbLtlxWs/G+RqNRpFIpt3dhoVBAPp93+wraPQsZnjHwYqDIawHgO69dM1uhK5WKu0cUnHJs98sLhnA8P/cbZFjHwSWlUgnFYtGtsVwuHxSi2pZnhocMYXm9dp/GuaoDbUWhbZe2Q0hsOzTfZzu1mJOLue75DvXIZDJob29Hc3Mzuru73eOsZlRbsIiIiJyOwuesx+d/8R3gRRsWeylLUrSvF+Ej/DuTiMiR6N825YSKRqPo6urC+vXrcfbZZ2P16tVYvny5a4dsbW1FNps94nE8z8PExAQmJycxNTWFmZkZlEolFwIyHGLwxxZWVunZffiA2XZhts8ysLNBTnBIRHACbzA0spN7GToFAzEGTzZMYyDFaj07NdgGaQyiGIJxmAhDr1Ao5IIpVpbx2orFogu6WC0IwNe6ayfu2utgCMjAjnsrBvfMY6hKDFNtEBiPx31VlMGhIHydnUbM49sg0d5XhnWJRML3fBv62n0V7XsF+ENMu3+hDR95D+z7zc9cpVJBPp+fdwjY1taGtrY29PT0oK+vz7XBl0ol34AZERERkdPJG7/5M5wRy+Dv/+mOxV7KkjT8hpUY//+dqyBQRI6J2oHlhGALbUdHB9avX49Vq1ahra0Ng4ODGBgYQDqdRkdHBzo6Oo4YepTLZUxOTmJychK5XA6lUgmlUsmFO3aPO4Z91WoVhULhoH397CRhO4TDhkZsL56rxZOhD4/FvQLtpFwew7YFByvPGDqyddVWl9nr4vpt5V2wks1WLbK1F4C7Drtfng3fWGVoW5KDQ0X4HGJwZgd+8F4yULQhIQMzu3eeDRkZyrEN1u7hd6g2XN4z29abSqV8z+f9Ysu4nYhsW29tW68d8MJrs4NC7OeUnwNe18zMzLzbeNkKvGLFCrzwwgvo6urCjh07fG3pIiIiIqeL4lUvwv71UZyV/CI23P82FAtxZG9Io/t27YFndX75Pjzz/12IYvtG9N7xP6iXS4u9JBE5BSkElOMuFAohm82iq6sLfX19WL16NTZs2ICWlhY0Nzejo6MDmUwGy5YtQyqVOuLxZmZmsGfPHuTzeV/FHquvOMWXYQ+DG7b5MkjLZDK+Si4bSMXjcdTrdTctmJV6DGZYZWZbhYOVYnZAhr0XtnrNhllzhWSsTAw+l69ncGXXzvOzpZePsU01OEwjGLqyApGhJI9hwzeuzwZkPK8NXoHZ8NCGoly/nQDMa2OoaassbQgKwAV/xACRA0T4HBvK8j7azwVbw7lOfh7sXoU2nLTTl23gbCdLl0olxGIx7N+/3w12ORy2d3d1dWHlypV49NFHsWPHDgBAsVhEIpFwoayIiIhIo9vz9iKeuexvAQAzezNYd+MjeO5TF2Dyry5Gy1MRhYFG/w8iSL5vJ3ZkL8Tg/74P0O+Lx1WkrQ1TrzzDfd9y/05Udu5axBWJHH9qB5bjjlVhtl0ykUigtbUVmUwGmUwGPT09aGlpOeKxKpUKRkdHsXfvXrffWzKZdJN/LYY+NkBh4GQn0rKCi+zwCe6jx/CKw0HsnoEAXNWe3Z8vWJkYbIe1QzuCVWvcgy4YdjG042AShoD2ehimcbpvMLDktTGAs23NNvyz7bM2AOO5uHbeg1gs5lqSGaSx8pDvR7CF2lbzsTWY67PvCV9rqy0BuDCT187vPc9z98LeVx43eE/5mkKh4J5nQ0U7XITvfz6fd/tNslKU1Zbj4+PYsmXLvCv5otEoEokEent7MTg46FribZWpiIiIyOlm6+v+Grv/cRXWfvIxtD4RRqEbGPnAixd7WUtG9lv3Y2wmg01veAxbbrtksZfTcOqlEtJ7iu6rXigs9pJEjjtVAspxFw6HMTExgXw+j1gshvHxcczMzCASiSCbzaK5uRmdnZ3zOlY+n8fTTz+NiYkJVKtVJJNJV9HHgRoMyhjmVCoVpFIppFIpV52WTqfda2x7rn2MoZNtH2WIyACMAZdtR2bwx3CSYZLneb7WVVtdZ4MxG9Rxr0JWojHQtEGbrf6zgVuwys+2zgaHgdh97TgYxO4lyPXwfjJ047lY5cd12Ao8uwZbNcg9/mz1HtkQMDjZmcex1X88D+8Bg1fbHg3AnSscDvsCXns+W50YDEAtOxXYDgvhvRkfH5/3voDpdBqZTAb9/f1YuXIlnn32WTz99NMA4PZvFBERETkdrL6tjJd/7bfc963xENb+uIhtW2ew+nfHUG9vwfw2XTk9DNwSwv7PZ3DxZU/i3r+6GGs/cP9iL6lh1HI5hO79pftenztpRAoB5bhj8GKHXrBdNZVKYdmyZfMaflCpVPDcc8+5VmBW23FAQ6lUQiKRcAGenczL7+257d5uDMYAuDDMVuMFQxieG8BBVWasoONXPp/3Dfew+/DZakWGf1wXMBuo2Uo4Dvzg99x7z07F5f1msBkKhZBMJt2gEbtuXg/DMxv4kR1AYvf+s621DAJtO28ymXRTjhm62am8bPG1++nZIR12sAeDTa6X12hbjW0Qa59rJxnbtfC59jjBPQF57XwPeb2sXGRYyGvmY/V6HVu2bMH69euPWMmXTCaRTqdRr9exYsUK9Pf3Y8+ePZiamoLneUilUijovzyKiIjIaaD2yyeR+KV5IBTCc8+dgbZzMnj6zyNY/baHF2tpS1Lt4SdQqw4iFq7ikguewc8VBIrIAigElBMmk8mgt7cX/f396O3tRVNTE9asWTPv6aelUgnPP/+8C/tY2ceQi1NxbZhj977L5/MHBXG2as1OFA6FQojH4y6A42ATtpkyEGMFmJ32Gw6HXbssMFvBlkwmEYlEUCqVfPv4MVBjGBiPx93aeN22zdhWrXESLkMn7nNnh4Fw/Zw6a49pW1/tZGRWDXIfQZ6TlZb2ungPeB4bWrKt2P6cga2tILRt2sEJycFWbfu9PS7/zM8T27NtazbfVz6H71lwOnFwqjErMm0bMb+31ZL2vQuHwxgdHcW6deuOGAK2trZi3759SKVS6O/vR39/P55++mlMTU25dYqIiIiclup1VB9/Gk1PRtDyzwnMr8/i9BJ+wzgK/5ZAKlLGizc9jfs/uxmr/uC+Yz5upKcb9b5O1B5+4jisUkSWIoWAckLE43H09PRgYGAAQ0ND6OnpwdDQkC+YOpxarYYHHngA+XweiUQC9XodqVTKN1TCTlNlgBQcpsFQigGWHYJh9w3kP4MhEoMlhmM8Js8JzFansY2TgRrXaFt1GUAxjLOhpQ3lPM9z+x+yYi/YtsyWZBtK2am2AFxgWqvVUCgUfBV0xWLRBYLNzc2ueo4VlbaCz073ZfDFIJFr49p5z+3wDzuUwwZ4DNnssA6+plarufeKa7afL7t3XrBKks/l3nv8nu879yRk4Ek22OS9YthnB6QwXLbtyqVSCdPT03jooYdwwQUXHDbIy2az7j3q7OzE4OAguru7MTY25gLllpYWTE5Ozuvvi4iIiEjDqVVRy+cXexVLUi2Xw+Sv1ZH6UQSxcBXVVA2hRAL1Xw2uW6hQNIp6pYLqyCgwMnqcVysiS4kGg8hxEQw82Pa7Zs0arFy5Ej09PWhvb5/38YrFIvbv3++GT9jJtwxnWGkH4KBqslgs5vYO5H5xdv82YLaqjOEW95srlUooFArI5XIHDXqwAzIYlrEdN51Ou/NwejFbOu3+fbZt1YZT/N4GmmzvZQUgQ8NgW6zdE4/r5BALnsvuJwjAteTa4JHHY3ho25YTiQSSyaSr/LNhHe85w0kGfsGQjuGbfV8YCtr7ywpMAL7qQlZoptNpXxs1Ky9tlacNAO19BeDOb++z/SwE3xeey4Z+wX0dOWxk7969GB4ePuJnPJVKIRaLobm5GcuXL8fAwICbls29GkVERERE5mS6ZV520RPYcdOmeb80lEggnMkc+GpqQvmy807ECkVkCVIloBwzVk7ZSrm+vj6sXr0aa9asweDgIIaGhubdBlyr1fDwww/7KtpsoMQAjNVYdp87WzEHwO3lFqzUs1VtNthhUGaHh9gKsOCACuIE31Qq5dprWcVm9xIkBma8PgZXrBZMpVK+fe5sGMnWZLba2r3s5jofg0o+zudzDaFQCMVi0VU4svKOE57t84MDOGxrMd8Dew6uyQ4l4WeG+zLyvWRQys8Rj8nA11YR2uo9Gxbayc/8XNqg0laQMkDkeez187q5dlthaicQB4ezMLx87rnn0NnZ6Y49l46ODoyNjSEWi6G9vR0DAwNob2/HzMyMW6P2BhQRERGR+fA6aoj29aKy5/D/MTqcyaBy4Rkoth/o7gnV60h99xcnY4kisgSoElCOWSqV8gViyWQSa9aswapVqzA0NITBwUHXQjofo6OjbhowMDvQwgZ7tqXWVrfZVlMbVNmWTmC2LZRBYHAgiK1gAw60nyYSCTcEhOESqwY9z/NNoo3H40in024aLisQOajDVjIyoALga29mMMZ12NZZVtSxLZZViQDcz1gVVygUfEMu7MTf4PXafQN5zxnKMbTjHnrBoR02AJxr+Em1WoXneb7WZ64hGDQy4OQ+fHb/RgaLtpKQE3+LxaK7B6we5HltmGfbmxn+8Rqq1apvX0b72bOBn61eZAVjPp9HsVjE5OQkdu3addjPeUtLi68asLu7GwMDA64CsFgsznuKtoiIiIic3l76ksfx7O+tRHSgf86fh5NJRFcMobpxrQsAReT0o0pAOSbcr8+GSdzjjAFge3u7bz+3wymXy9i2bRvK5bLbo45BUnA/QLaNch22fZUhUaVSged5LlQ71D6CtqLQnoMBlm0R5fcceMEg0vM8JBIJV83Hfffs8A+uz1Yo2io9O2zEnts+Bvgr6fi9fb7d75DXxUDSTv0F4NpoOWCkUqm48IyPMSjlMYMtssEg0N4nW71n77V9b3lcrpP3gecoFouuJZk/Y8UiX8dgE5idnMzPCe8BPwf2/Awtg1WDdlKxvdZghagNTfP5PJLJJHK5HMbGxtDX1+da2YNCoRBaW1tRKBSQyWTQ19eHoaEhbN26FYVCAYVC4aAp1SIiIiIiAFCvVHD/P1+Ai6961D126csfw7Yfr0N8127fc0OJBGrnrsXMQPpkL7MhlF59EaqJX20rNFNF9IcPLPKKRI6eQkA5Jul0Grlczn0fi8WwfPly9Pb2YmBg4IgtkUHDw8OumguAC9hsSBZsObUBja0A5HNZQcZ1cCBHcFiEnVhrQzc+zufynwyK7H51rFxjNZkN6Rg02QpFO3zCDijhtQQrG+2egAyyGEjadmP72mAlHgeK8HU2pLPttLZakusOXn8wlGRIZoevsN2Xz+NaisWiCxjte2vvPSv8gnvx8R5Eo1EX8hJDTgaYwcEirMjkvoV2SAvXYQNPhrqsgOTz+Lni8YrFogscY7EYJiYmMDIygqGhoUN+3lOpFOLxOFKplJuk3dnZidHRUVQqFUxNTaklWEREREQOUq9UsPJrLwBXHfm54VQS0woAj1psqoRo7MC/S0TyFeg/08upTCGgHDUGT3Z/tq6uLixbtgwrVqxAX18fmpub5328Wq2G/fv3u3ZZVq3ZAQx20i7DMgZjdp83hkRsAS4WiygUCi6os1VfDOpYaWb3DrQhpJ2Sa0NHVoaxSo3XwuuwAVxwcq79ud2DLjjYgvfBttHOFU7y3HaKsH2fGIjx2Ay7bOUg187wLp1OuwrHYDDI1l0bqNo9+XhMDjXxPA+FQsGt34aC9n5wMjMA3z2yQSQrQavVqtsz0N4P3qdqteqq8ewAFAZ8wbCVwSrfS1Zt8lrscflesQrRDowZHx/H7t270draesi/B+FwGE1NTZiYmEBrayu6urrQ29uLp59+GpVKBTMzM2hublYIKCIiIiJHJRSLo3T+6sVexiktdO8vwX4nBYByqlMIKEctnU67MAU4EGgMDAxg2bJlWLVqFbq6uubdBgwA27Ztw8jICGZmZnzhDABf1RwrrVihNVd1mt3HjT+ba/Kr3V/QDspggBccasEgEJjdm5BBmA3qbFjHlmWeh1VxtnXX7msHwO2VVyqVXFszMDv8wgZsNogN/pnBJvcu5D1kqBdkA0C7Pp6H98gO3QBmq+9YLcfw1VY3svWZrdocfmL3M+R0XFuVyOpKfsbsP3ksWx1p9/iz+wYWi0X3ubCfk3A47MJW+9nidbM1nWEkQz8GggyV+f7x8+B5HsbGxjA6OnrIEDAUCiGTySCbzSKXy6GzsxP9/f3o6OjA7t27XTuzDThFREREROYtHILXOv/OLBFpbAoB5ajZtk8A6O3txbJly7By5Ur09PQsqAoQAMbHxzE5OQnP81yIZFsxbRgD+KvXGOowwOGgjlKp5H7OgQt2gAan4NoqPAAuzInFYi64YlhnJwczCLThmz0Ww6xYLObObysJ7b2Mx+Ou+jGVSrkAKtiGyuCK5w7uyWjvC9fLCji+hi3B6XT6oKpLBmt2Tz2GeYlEwlUA2gDV7u1n74GdyGtbnxlO8nmJRMK3tx8rJwH4QkB7zuBehDa0ZKsv3yNbaWnfN75nrPyzQ2KKxaL7DPFzYluW+RgnY9tglOeamZnBli1b0NTUhJ6enjk/9wxEY7EYurq6MDg4iO7ubgwPD6NWOzDVOth2LyIiIiIiIrJQCgHlqGSzWcRiMczMzAA4EM709fX5QsCF2LdvH6amplx4x/3qAPgCHwZU9nEGUsDscAn+mWFMLBZDIpHwTTEGZtuGbZBnJ8YyWLJTbW1rsA3f+GXbQu2EWRsY8dy2ktDuexcMFBks2Yq0IFv1VqlU3GAJttbac3DPQg4nYQu0bSNmZRzvM+8h3xeGs1xfLBbzDduw04iD4Rt5nufb65CvD1ZG8v20lYkM5WxAzOux1Ywc2sI12/vAddtKUbtvI+8fr8Hul5hKpXzBrg1q7X6RpVIJk5OT6Ojo8FUpUiQSQSaTQTKZRGtrK9ra2tDV1eX7PKgKUERERERERI7V/Hs1RX4lHA4jmUy6SikAaG5uRl9fH1asWIGBgQG0tLQs6JgjIyMYHR3FzMyMC35YEQbMTnW1e/jZUAaYDeJs6zAw2+LK6rBYLOaqy2y7qj0uAxiGknbKbDwed+2jttrNtg7bSbc2KAoOqghOprVDOoDZyb0MlWx4ZavTGDjF43G3Px7XxOvitdkqwmDoadul+XzuC2gHvNiQLHjfuZ+hDcT4fhL31Auuy74/bKtmKzODS37mOFCD72PwfeDnh9cXj8fd3n72vtt7X6vV4HmeCzd5X+06bZBsJyjb94/3kq8dHR3F1NTUXB99dy3pdBrJZBJNTU0uDOQ57b6GIiIiIqea6MrlwBz/EVuOTbWvfbGXICKnGIWAsmDZbBbNzc2uog2Aq15avnw5urq6FnS86elp7NmzB/v27XPtxZy4akM+VoaVSiW3TxoFB3zYUAg4EDoVCgVXEcbQa6799YJVYByiwQoxO2nWVgTaQSN2n0RW0LG9166b+/4xSAruY8jX20Em5XLZte8CcKEkAyeeI5PJIBaLueEZwfPyXiUSCcTjcXfd3POO043tEBTefwZlPEbwPWDgxqpFHpuv5fl5bQz0+MUKPwaLrIbj6xgYco32PWQYZ+9b8P5UKhV331hBaCtPg+3MDCftFyso7V6DrFC01aoMDvft23dQJSolEgk0NTUhlUqhs7MTnZ2daG9v991b7pcoIiIickp50Qb83j3/ikh722KvpKHUL92Ivr/attjLEJFTjEJAWRDum8YKKO6119/fj6GhISxbtgydnZ0LOubw8DBeeOEF7Nu3D7lczlWJMUxj6BacpGtbVm1lGQMihkMMYVjJZwM+YLaFeK4JwwB8e+DxOfY49nisJAtWM3L9rNTjee0U2+A0ZD7HVvzZykVesx1aYu8NKyd5bLYT20pDHjdYVWn3ByyVSpiZmXHrY/hr22r5Pa8zOICFnxu+H3w+gzNbccfHeO94fcHX2ZDStv/aPfvsGu167LUHh6DY1lv7WWDwyWDRhrf2s8f75nkePM9zz8vn877qR4v7M2YyGTccpLe31/dZAeAqGUVEREROBeUrNuENX/0JXpUuY/fb1i/2chpK52dfWOwliMgpSCGgLEgsFkNzc7PbIy+dTmNgYACDg4NYs2YNli9fvqDjlUol7N+/H+Pj4ygUCsjlcpiZmXFBGr9siMahIQxsSqUSisWiqxBkGydbXvlaAC5EtMe3oRYAF0jZAJHDQYDZVlg7oIJhEwOo4J9tZRqfa1ud7XPnGnphQzVWznEIil0Tg7NqtYpisegq8xjg2T0QK5WKC7bsNGVbfcYQMZ/Po1gs+oIwhmx2EEnwHDZQs0Eu3zfP89zzGPwyrE0mky44tGGjrUoMTkW2e0Taz4jdZ5H3jfsm2nZvWw3Ke87zs4rP7kto9wq018rr4JfneZienva1RAelUimk02mk02m0tLSgra3NDdfhuTKZzIImbouIiIgsFu91F+Flf34fbmjdAQB44A+/oJbg42TfezYjHJrfntHhNSuO+JzI2lXHuCIROVVoMIgsCIO4ffv2IRQKoaWlBQMDA1i+fDlWrFiBjo6OBR1vamoKe/bscRN0AfimzdrpswxkotGor1rQhlDBEJDtuwBcWMjWTlbK2WmutnqMwRGPZ4d+2MoxhmgMnngdDKSIa7WThvkV3E+QeyDanwGzYR+r2IDZCjyGlQyreH+I9weAa3Geaz89vpbvN6+Fx7KBJ39mW33tz2wwZgelBAex2J8xEOX7Y1tzGdDatmuGojZI5Nrtnog2bGW4W6vVfK3a/Jxx7XYvRr7WrtvuZcj7YMNQ20Jt9zOcSyQSQTKZRCKRcO3AnZ2dGB8fd6/lzwuFwmH/XomIiIgstrBXwze/8XJ8Ey93jw3W7128BTWQjrftQCx8cIfJz/7zbKx7ahfcvwGEQpg+o/Wwx6qHQsif0YHEs1uO+zpFZOlRCCgLEo/HkcvlkM/nAQDpdBo9PT1YvXo1BgcH55xaeyjVahUTExMoFApubzZb8cegjXsB2pCGjwXbTLmfnQ2zGOLwNcHptbbaj+vnaxi6Mfzh8ZPJJFKpFOr1OmZmZpDL5Vywxmmztp2VwZJtB2V4xGPbfQS5jx1fM9dAETsl2FaiMXxisGX3zONzuWdgOp327b/HPRPtsRiospXZhqRcL/9p9y7kz+37F6zgs/socs/F4F58PJcd8GErOe0ejXx/bDVmsLKP6+FnwF4vA8NKpeI7fzwed+tjlR9bjm1bMUNW3mP+me/hxMQEWlpaDlnNx6Eg3d3driV4165dLmDl54vTnEVERESWqti/P4DBf1/sVTSebZ/ajPOTT8/5s/bHQqi8sOMkr0hETiUKAWXeIpEI2traMDk56aqSstksOjs7MTg4iIGBgQUdz/M87Nu3z+2HZie0JpNJ17Jp98ljeBSNRl3VGafh8mesErT743H9DICA2UovVrsxqAq2urJ6K9juyeCK4VC5XHYDRGwASLayjY/bKjcGdwyebFswgz8bRHHtttqPYZ2tPmObq60yZCCWSCR867GVdzZkpGBrbjQadfeZLcq8nmBlHSc083ycAmzfV65hrgDQHsdWaPI19j0NVjLaPfsY2tn9BMPhsO9zZluKeXxb4WnfR95Tfn7s6yORCBKJBNLptAvQ7T0JYjVgU1MTenp60N3djfb2dkxMTKBer6NYLKKtrc1VtYqIiIjI6WPrZzZj42XPIBU5dHeJiMjhKASUeWP12NjYGIADE03b2tqwcuVK9PX1+Vpf56NcLmNqagrA7D5toVDIVaMB8O3fZ/d5s3vccf8+O6UXmB1uYUMXhkm2bZQtpgzW+PNgpR4A13bKYC2ZTLoAjMfnPne24pA/Y1AUj8dd5RiryRgCMqiyVXG20pEtsPZ+20CTlXLAbKjHc9vptbwXwf3sbCBnX8tAkcFfsJKS6w6Hw+4+2SnGDAEZjkWjUSSTSdTrdRQKBXeP7Z6DbN21FZwM3RKJhAsI7XsXrAada0iIvT4GgqVSyTfNmZ+J4Gct2MrNvRXte8AJx+Fw2LXwAkA+n/cNYJkLq0x7e3vR39+Pvr4+7Ny507eHYktLi28YjoiIiIg0vnJ3GZloabGXISKnMO0wL/M2ODiIXC6HXC4HAGhtbXXDQFavXr2gVuBKpYI9e/a4vc2CVWcMX+y+eqwsY5UaWzQZuDFcYcjFYJHBmp2sattE2TbLUMpWl0WjUaRSKdeuzEo/hlMM1WzlIqfWArOhY3B/QVaoscXV/twGcwyu7PAMPg7M7hFo71upVEIul3PDVuwei3aPP7bW2nZktr7yfjNQ43RltvTGYjEXiM0V0jLI43tBNkxNpVLuPWKoaYebcJ1sDbet4BwawiDSDksJ7kdoW4l5j4L3Pfj5sfeJXxwkwom/ts2b95YBom1rr9VqbqhKtVo94n5+2WwWTU1N6O7uxvLly9Hb24uWlhb386mpKdcCLyIiIiKnh22f2oyXnv3MIX/uVaMIHf6/NYuIqBJQ5icUCqG5uRnDw8MADgQlAwMDOOecc7By5UqkUqkFHa9cLmN0dNRVWNkqNtsayyDHBjb8uR3CYfd6YxDGEM9WlNnBHrbSjtfI8IfVZgyfEomEr9qPFWAMBhlaccAJ18kKMFZwMYxk0MfAKBgaMizifWXrsw2+bPWevQ5OBp6amkIul0MqlXJhoh1iwWEiNrica6gH/1kul1EsFl27s61WZOWc3YcwiEEdz82KQtvWa89n25R5vxjKArMVfsT3n/eHeN+DA1mCuC7ef1ZBsqqRa+JzGSbaSlRONmaQyPfFfnbGx8fR3Nx82NC8ubkZzc3N6OnpwcDAAHp6elwFbrFYRKFQQDqdxuTk5CGPISIiIiKNox4GIoeZCPzLu87E4N8e/eCVUCyOellVhiKNTiGgzAtDDLaZplIp9PT0oK+vD0NDQ74w5kjq9Tr27NmDqakpXzBmAze2V9rghq8FDgQhtVrNV93HqitWrVUqFRe42emuNtxi2MMKMla48ees/mKlH4MiVgbaIM62/9rQ0YZvdmIs2eDRDpiwAzTYcszr5TEYftq9ExnW8dhztf3y2niveP8YIAKz1ZJ2QEvwvvAcXAPPwSnNtjrQVsYB8AXAvC+sBuT3rDq1z+HrGAoysOU9tJWc9pqDwS4AX6Bar9fd59DuixisGGTFoW1PZiDKlmIbJtq/G3zsSNra2jA6Ooq2tja0t7ejvb0d6XTaDeTJ5XLIZDK+AS0iIiIiIkejGg+j9LINiP37A4u9FBE5wRQCyrysXLkSnue5UMZOimUYMV/VahW7d+9GLpdze8QxuLH7AQZbXdkqChyoSrNDKBjcMISzIR1DNTu5N7hPHjA79IKVbjZYKxaLSKVSvv39IpGIq/DjdTG8ZCUYq9mSyaRbt215ZRssW0m5Z6Dneb5glG21dpKvbcvl9fNaOaCCe+7xvvF9C14L729wnzk7UddOw+XzGeJWKhUXHjJUs0M1GIYysGJIyEpJOwSE33MPQl4f7wWvzbbi2kEq9j2218PKQ07WtZWPrOhjaGzvMc/FCdLhcBipVMq9/8Vi0e2VGJxqzPvDzwiv60i4/2ZTUxP6+vrQ09ODpqYmFwIWCgX3XszneCIiIiIiIiLaVErmpbOz0zdMA5gNchZq3759mJmZ8QVwtv03ONnXTnhltZUd/MCfcd81YLY6zVZreZ7nAiQ7LMJWGdoqQgZX2WzW185pqw15T2xlmQ2ybLh1qIm1DKvsHnP2nvBYwGy1oa36s+3Ndrovq9x4X23YyGm1tsLO7gvI89vz2snCdp89234dHM4SDMVsNZ99H7hWu38hzzFXuy3Pbduiee9sK3aw7dd+Duxnj1WKwXZpe/943yKRiAsXeT281mAb8lzVlwwhj6SjowPZbBbd3d3o6+tDW1ubL2wvFArIZDJHPI6IiIiInNpCF56D6rLiYi9DRBqAKgHliNra2pBOp32BFFtTOzs7fS2587Ft2zZ4nudCHFZM2bZKhnXA7DAJuz8bK7BsuMUgKxiQ2TAMmG0BZUUbAykGj2xJtpNuOfWXQY9dE/fA4/qDLbC8PoZ+PKZdtx34wYEaoVDIDeJgxWCwCs8OT7HhFcM6Vq9xoImdkgzA14bLe2sDSxtsMXxk+zQDTxvIcv0MyexAEhsG8v3le839Fe3a7FTkYFhs92Xka7kGO/WZ1Y5230k7XMa2BgeDaBuA2tZgu++fZQNUe9/JtlAzyDycdDqNpqYmdHZ2YnBwEMuWLcPu3bvdRO1qteoqA0VERESkcQ2/uBmXrX10sZchIg1AIaAc0bJly1AulzEzMwMArr21q6sLAwMDCwoBR0dHfXvJBQeAMHhjGMUwiYEJB3Hk83l4nud7nIEiMFsxN1dLKoMqhpmsLrSVbnZ9DJW4HrZBMyizwZS9JgaBwSnF9rwM+bhGhluJRAKhUAjFYtFNLuZz2PJqQ8VgYEWscAtWPZIN72wVHAO+4PcMSlkNZ8/DENO+pzZkC4aUtVrNTdtlazA/S7x3DFd5Thsa2+DUHtuGhwzquFa+73a6bzCE5X2x4TSPyxCUny07VMVOOmawyjCX18K24ZmZGbS1tR3x70tHRwcmJiawcuVKbNq0CdPT03jkkUdc+MdgWvsCioiIiIiIyJEoBJQj6ujowNTUFPbv3+8eC4fDaG1tXfBU4NHRUVdVZdthGfrZSrtgK60NeRKJhK/9lfui8dg28LLhTbDdNli5ZlthGQDZcA6AG0ZhJ+rakM+2DPNe2dZV2yYaHBzC9dqqOFasseqNQRarJm0lW7FYdGtiMMQQcK721OBee1yPbRUme2947cGBKXbfRgadtt3ZXqM9jr03rJDk9dv2cGKYxnvBa7HVlXytvU6Gczaota3Udg9KCk4ptkGvrQi1a4nFYq6alO8b37NarTbvEJDtxz09PTj33HPd+X75y1+6MF0BoIiIiIiIiMyHQkA5rP7+fiSTSezatcuFOxwG0t7e7gZezEexWHQtvMGQi62mHBRiqwttSynDsmw2CwC+SjHb4slQBoAvXLR7qtkAz4Y1DMAAuIETHOLBoMu2y9q97kqlEpqamly1og2wbJUhB4rwcYZDXBOfxyApONXYVtjZ1mAbZjKo5L1mCMWwygaW9nE7XMVeI8/N19kQ1e4rWC6XXbuzDQV5LB4nmUwiGo0il8v51mKvi+exey0G9xUkO1mYr2XAa+8TW5/ZLs7W8Hq97qtS5T/5OWL1oP088bixWAzJZNK1JvPa7HvPILdcLs+7jTcUCiGVSqFcLmNgYMA32OSRRx5x6xURERERmY+mZyZQaU2h0J048pNFpOEoBJTDGhoaQr1ed/uQAUAqlUJ/fz9Wr16NpqameR9rbGzMVYUx7GPAFxwqwSouVonZEIWBVTabhed5LmQB4Cb52qCRFYA2AORzGdQw0GIwxzZLtnTaCcA2AATgq4Dj1GLuwWcHiHAqbyKRcPfAtqByTWSHUrBV2e7XZ/fvs22qNgy0+9TZvfns0A3en0Qi4Ya92HbWcDjsKgxt8Mfj2/eNAWo8HneTayORiG8QCHAgsEsmk74KTwC+YO9Q+wTa9mk+nwEnQ0yu1VY+8voZ5nLPRF6DrfjjvQwGhMFwmNdiqy1tBSIFp1Gztf5IQqEQurq6UCgUXBjc3NyM/v5+7Nq1C7t27VIloIiIiEijqwPlWgSxcPXIzz2MUL0OlCtArY5QvY564N+PRKTxKQSUQwqHw0ilUpiZmUEul3OPR6NRtLa2oq+vzzf44Eimp6ddCGKHObACz+6rZ4MjVqjZ6jkGOvF43FVi1Wo15PN5F2QxiOOxbZstBzvY/eV4XgY8fO5cYQ4r1BhSsjXVTjBmMGXbmxnS2bCIwRTXwUCP11YsHpgElkql3LF4XZ7n+QJA7jvneZ4LI9k+TXxOKpVywarnecjn8y5sDFa82bZsBnu29bZcLruBJ8DsHozEakIboLKqzU7qtWGuDc3sfoO2ijEY5AWHxNiwme8v39PgcBFbhWrDVn4GeS+D1X0MAe1QkCAbMIZCId/fpyNJp9MuUOUEbHs8EREREWlsPX91L37RtxmXvvKxOX+eq8QRPcLw4FCtjszWKVSfeR4hALFXbkKpRXGAyOlGf+vlkIaGhhCPx7Fr1y5MT0+7x6PRKDo6Oha0HyBbgVmlx/DFTl4NTnhlNZptE7Xhkp2Ya6e/sn2TAZ/dL84OwLBDIQD4KtxYAcYQr1Ao+AZEMMi052U1WCQSQbFYdNVuoVDITcBlhR6r7WxLqQ23eB02bLJtvQwNAfjaqRmIcr8+/tmGefaecT38shWUtiIwlUqhXq+7NtpYLIZ0Ou1bhx26YUNe2+LLMJAtscHBH3YACq+b13io5/E95nUGA0W+t8G9/2ylKUM8295sz2+rRdnWzXvDISm2otXukcjPEz8zlUrFBbvzwRC3Uqkgl8thcnIS09PTKBQKqgIUERERETzwP2ux5nP3zvmzWK6KSiqM9PYcao88dZJXJiJLjUJAOaSenh6USiWMjIz4Hk+lUujr60Nzc/O8j7V3715f1ZcdzMCgCoAv7AuGXbFYzIVprBTkcWy7qZ1sC8AFfZwUawMp235rz8Owivv1sQqLba62ndYeh5VpDImIASCr6Gygx5/byjUAvlZnroPXw9dxLRyMwjWRHYzB58fjcV+wGgqFkE6nAcBNSbYhGSvsGIzZqb12krK9Dvt+Miyz+w3agR+8T3YyLzF0i8ViboqwDfPs+ljVyHVx2rANXRnI8RwMRG0FaHCvweC18f1gAMhKTDugpVAo+CYK23bnSCSCmZkZ3/07HO7BWavVsG/fPuzZswdjY2O+YJ7vp4iIiIg0vp88vg4vPesZREJH+A/C9TriP34E8XPXov7Q4ydncaepyJlrUX3qOUD/kV6WOIWAckihUAiFQsG3f1kkEkFraytWrFiB9vb2eR8rl8u5ENDzPHcsu2+bbRllaBePx12AZif/sv23WCy6qkIe01by1Wo1JBIJX2snwyNWtRWLRVfJx+DRBoi2Yo8/t9VdDOA4JCU4jZbnZOsur8GGoQw0gxOTiYM2+DPuu8fzs8IMmN2jjlV+wb3/yF4TQzOu0w5isWsKDr3ge8m1c23ct5HXYScZ2yEptvWYlYl8H+3943tgW3VtiGirEW0YFgwN7f6QNui1ewfa0JTvXbAlPDgQxlY4suXbfsbtmuPxODKZDCYmJub1d4jDQUKhEPL5PMbGxjAyMuJrO85kMr5QUEREREQaR+j8s1EZOPB790/vPRvrP/YIKvckEIlUjvBKoF4uAQ8oADyeIp0dKG1YjsTWvahs247QhefguaubsOqW7aj/6t+PRJaqI5ehyGkpm80iGo0eNH00Eomgvb0dnZ2drnpsPlgpZcMbAL597Wz1n90vzu7zZtt6LdvaG2zHZDslH2MlnG1Fta3JtiWWoaUNj2wLs20z5dps5RivDZjdV85W4TE0AmYHVwD+akXbuhwMuBg42Uo0Tqpl1SIDRHvPbGu0PR/fY/s6u5ed3YuvUqmgUCi4Sjd771khyXXatl22AtvJyKxe5J53xGN4nucGrPBY4XDYtVuzoo/3i/efLeg2cOWxGLx6nodcLueuhcezrej2/Q6+v9yD0rZc2/sEwLefX6lUQrlcxtjY2Lz//jB8ZdDMayI7TVtEREREGsvwS1vwsnXPAgDW3TGGWj6Px7955jEdM7l1L8JlVa0dlUgE5UwU9XgM9Refh21vaEI1qXsppwZVAsqcBgYGkMlksHPnTl/YEIvF0NfXh5aWlgUdj6ENQzhOsLVTchm0sIqMQY3dh42hoG1DZcUfK6NsCGQnuDLQYgtnsVh0AaGdIAscPHmXgREDPYY99vwAXFAVDCUZ1LGt1ladBfd1s+2xrIgLh8OuVZfnZphk9xnkvbP7+bGakmFbsNKR95b3mkET2VZTO9SF67ABVLDyjs8vlUouVLTVk8FhMHbQiK3AtOEfQ7hIJOLCXV4TQ0gbqPJzYdtzy+Wyq/zkIBU7fMS+z7Z9205IZqDI99uGrRxKU6/X3bAaG5LWajVMTk7O++8PpwK3t7eju7sbu3fvdkEq76OIiIiINJ76pRvhvfRAx8cD39qAgT2PAgD6v/wg8Ouxw730sCpbX0BofTcQ07C5haqOjCJ51yiqAPa/bDPKTQoA5dShEFDmxEEQMzMzvpAqFothcHBwQa3AwcEbbPllxZcdqMDwKjhNl6GVncxr97vj9Ntgaygfs9V0DNNslR3DG9uGaoMnz/Pc8ey+cwzPgm28/Jk9RrCKkINEgq2ofC7DKFsFyD0DbZsuQ7a5hljYyjSek8ezlY18DgNNew4el+tkJScDRv4MmN0/z7bE2oElDN4YUDIktGvkemxVpG2httfJ49oBIsGKTTt52a7TBskc7mGHmNiBNbwXNhwMTrUOnp8TnO3nzFYqLmQ4SDQaRVtbG1paWlwYODY25qvU5flEREREpHHk+hO4ZNkTAIDuB4qo/WoLmJrnYesnN2DsPXms++IYqoc7iJwwPXdtRaFrFYpd+o/ycmpQCCgHYRXT6Oiob5+xUCjk9gPs7e2d9/EYktnJvwy3GPDY/fBs5RzDJoZTdlov9wO0e+Ux6CJW/dmKM1aZsQKQbce2Ss6GjdFo1IVvDKnI7knH89vQ006gtYMnUqmUq9DzPM8NE+F9BuDWxPPw3rDdNxwOw/M8F64F9+VjyMj7ENzXkPeClWy83rnWEgzl7LANOxiD748dImLDL74PfJ4NQW1gyqpFvlf8GY/FNdl7HQz/uC47TZiBcnAICdcS3PvP/tNO6WWQbNuqbbs118bnpNNp39ToarWKfD4/779D4XAY6XTaTXy26+L5stksJiYm5n1MERERkZMt0tyM337gfw758ztefCmqC9gypdGFLjwHyfftAQD88qvnoPu+B+F+W63Xkfj+g1jxUBcqe4YXbY2nu8qeYSz/YgGIxVHVfoByCljQnoBf+tKXcO6556K5uRnNzc3YvHkz7r77bvfzYrGIG264AR0dHchms7j66qsPmiy7fft2vO51r0M6nUZ3dzc+8pGPqHpliWlra0NTUxPGx8d9wwfC4TB6e3vR19eHpqameR+PwZRtlWXgFtxnjVVibAXmfm35fB6e58HzPBQKBRde2VZdBlgMglhpZaflRqNRF6DZAGmu6j+2+mYyGWSzWd9U2WA7MCvc7LXw3BykAcxWnfF+cD85fm8n6fK5iUTC7QfHsI8hFK+dQRfDNq7JThouFApu3ztW6tmBIsT3hJV+NmzjfbL7OHLtDDP5PjPIs1VwfM9shaFtxWbgy6o2O6nYvjd2n0hgtk2Z5+e95PXb943PtXtB8jNpK/+Cg2p4XlYWsgWY98hWdPIYvI+ZTAbNzc3IZrO+PRCDreCHw88upwvbEDAWiy3o76SIiIil3/HlZKlOTeGOiy5yXx/653fhdekZ93XHA9/BHS/8J+544T+x4hcpIBQ68HWaqiajGMqOo1oPITFZP3joRK16TAFg4gcPIlRTK+uxqk5MKryWU8aCKgEHBwfxv//3/8batWtRr9fxta99DW984xvx0EMP4eyzz8aHPvQh/Mu//Au+9a1voaWlBTfeeCPe/OY347/+678AHAiDXve616G3txf33nsv9uzZg3e9612IxWL4zGc+c0IuUBaOLaPT09O+qjdOKU2lUgs6HkMX7p/G8IZBFoMvPtcGKclk0rdnIEM+Vpgx9Am2WTIQslN0uRcdwx/P81xIY6sJbRWcrUa0bcQM7mwYaFtNbdsqKwl5bxkCBltouWbbSmyry1jhODMz467Z8zwXdvK58XgciUTCVdKxcg04MPAlHo/7KhTj8bj7ua2IY0UegzIGjXZIC6+FgRuvixWYtj2b75kNbm3Vpb3nfG/s0BUb0rECj+vmZ8dW+PG9tUEb12P3QrQVjLbKkY/zGJVKxX1OQnP8MhrcrzASibj3Kbi/YyqVQiQSwcjIyLyrauPxODo6OtDU1OT7XAAHqnfb29uxY8eOeR1LRETE0u/4cjJVJ2b3RV5z08/x2psuOOg50VUr8Jc//n+objvwO9ebv/gRDPzpvQCASEc7EAqhunffyVnwIomuXI6O/7MFhWoMT/3jevT8w73H/yS1KsLlOmqaMSdy2lhQCHjVVVf5vv/0pz+NL33pS/j5z3+OwcFB/M3f/A2+/vWv45WvfCUA4M4778SZZ56Jn//857jkkkvwb//2b3jiiSfw7//+7+jp6cHGjRvxJ3/yJ/joRz+KW265RRMulwhWYdlKI+BA8JFOp9Hc3Lyg49nKPgY1wf3VGI7YYAo4EHww5GKIAsBVeiUSCRfu2eEhXDunsdrXsEqQwyo4RZdBD9do9+Hj3m7B6b28LwBcSMQQlc/jeuzehwyibHgZbCflcXgM22JqKxV5D+z7ZUNWVpAxxGUbdKFQcMfhce2ehPbcDCT5HK4tOBiE5+Q5arWaa3u2LdWsJmT4xvtjh4rwOvje23vPATEMaYNBon0+Q1xW8AGzg2JsS3osFvNdJ5/HkNQGjHYCMj8bdvKzfQ9taM1KTlZtTkxMzDsEjMViaG1tRWtrK1KplC+IrNfrSCaTaG1tVUuwiIgsmH7Hl6WmsmUbfmf5S9z3A5gNwM6+ZxxrUyP4pzO73WORzg7UB2a/rz/+LOqnciVqKITSUDsiof144L/WYdVfnoAA8FcS//rfKLzpRSfs+CKytCyoHdiqVqv4xje+gVwuh82bN+OBBx5AuVzGFVdc4Z6zfv16DA0N4b777gMA3HfffdiwYQN6enrcc6688kpMTU3h8ccfP+S5PM/D1NSU70tOnHA47KrNrFAohLa2tgUNBQHgC3TYhspqKNtKyeq8Wq2GZDLpqtkymYyroGOwwwmsLS0tbpqt3ZvP7llXLpfheR7y+TwKhcJBf2ZQyGCSoZ8Na+wAER7T7mXHFlQGcTZQ5Jp4nbwHrHAMDq2w+wvaSjSulddvQ0O7V54dbsHnMICy4aS9Z3bPvuCkZNvOy3DRDiixoa2tvuR7y/CW7wMAX6swjxcc5sGf28CVVYS853ZAC88ZjUaRSCR8U5/tPbNTkoNTmoMtycHBJXyebb/m93wNA2V+HngOtoan02m3tuDfscOJRqPIZrNoaWlBe3u7r+28UCggEomgo6Nj3scTERGZi37Hl6XukQvqvgAQAMauOgODX9nuviJ989+/fCkKZ7MY/NPnMFJoQmbn6dsOLSLH34IHgzz66KPYvHkzisUistksvvOd7+Css87Cww8/jHg8jtbWVt/ze3p6MDx8YJ+C4eFh3y8H/Dl/dii33XYbbr311oUuVY4Cg5TJyUnffoDAgWqs/v7+BQ0FAWYr5DhUIbhXm23PTCQSLlix03Ft1RODHw5JYLVbMMTiPxnmeJ7nwhpbiWYHajDwsZOAee2s+rLP43ns4A4bQtoWUB6PISPg3wePwZ9tmbWTbxnu8R4ylOI94loYVtoBI3YYh10DKwkZhPJ5NhRjkGfDtrnat/n5se3ENjTjcbke2zLMYTS29Ts4iZjHtC3cvI821OOa7X3i/bF7M9pBJVwPK0rtBGIbANpQj38/bEjKtfD+8nzA7KAYvqfVahWFQsGteT5SqRQ6OjrQ3NzsC4E9z3OVniIiIkdDv+PLqaz9zvuw/U77SG6xlnJcPfVCH9aewCpAqxYLI9rbg8rwyJGfLCKnrAVXAq5btw4PP/ww7r//frz//e/HddddhyeeeOJErM25+eabMTk56b6079WJk8lkkE6nMT09fdBmzslkEt3d3Qf9EngkrNiy1WGs5GK1HDDbesm93ji1NzhhllWArD5jIBSPx90QDjuUgVNVGVCxPbapqQnZbNYNCrH73AX3qgNm9/pjWGeDMVZ5sbKNIV5w7zvi3no2MKTgIA07/MIGZGSDNq59rgo/Bo42COT5OdTDrtO+xgZXvC5WVNr9DjOZjAtm7XXYENLeDxuwMTgLBp2WvX479ReAb4Kwfc/4ebKVg7wuvg9sMWbQan/Oe8H3gpWcttrSvt6+j1wzv2dFpN2rMZeb/y+p8XgcLS0tB7VVMYRmVayIiMhC6Xd8kaVj5/s2nNTzZbZMoZoMo7Sm76SeV0ROvgVXAsbjcaxZswYAsGnTJvz3f/83/u///b94y1veglKphImJCV9IZDe+7+3txS9+8Qvf8ThZ7HDVZWztkxOP4UKhUDjoZ+3t7QveDxCACztYGWXbVRn42H3pbHhi/8yf8fXcFy4cDsPzPLdXHfeKs8EYALd3HgMpO/2VwZIdqkHBSjxbsRcMeoDZ4SK2MjE4NdiGfADcmu3+gDb4DA4escEg/xms0uNxksmkG8piqyR5fIa9tpqS++TZik2+PwwOg9OVbfsw3/dghSUDQxu0ck9HBoHc347vra2ytMcPricSibiQGYCrruT+jwz2bADJakQb7Nl9/OznMPgecn22atEOrGHbMT/ftrXZVnXu378f2Wz2EH97/Bh222pF3mtWxIqIiBwN/Y4vskSEQjjvzU9g23Q7lv/DyWkFrj+5BVh9/kk5l4gsrqPeE5DYZrlp0ybEYjH88Ic/dD97+umnsX37dmzevBkAsHnzZjz66KMYHR11z7nnnnvQ3NyMs84661iXIscBB0ZwGAeFQiH09fWhra1twce0AyuCgxwYsNg977gnHFtEGZbY4KRYLLpgi1/cgy2RSLgpxuVyGfl83rfHnq3is5V4dm++4PReVoUFA0I7PITXyRZbG2DZ8NO2ljL4Y7UdAx5eJ18fDENtG67dr8/eV76WwaatQmOFJKv2eF8A/36BtpqRISOvPzgcw/M899xqtYpcLodCoeAmOAer5Ox12FDRDuzg9XE9c1Xu8T4FQzl733gcXmOpVHL7Qubzebd2z/MwMzPj2zOSATY/M/azzDXYoJjrsfsbBlvVbTiaz+fn/XfJVlNavJcKAUVE5HjR7/giiyccqmGykET8B/+z2EsRkQazoH9jvPnmm/Ga17wGQ0NDmJ6exte//nX85Cc/wQ9+8AO0tLTgPe95D2666SZXMfaBD3wAmzdvxiWXXAIAeNWrXoWzzjoL73znO/HZz34Ww8PD+NjHPoYbbrhB/xVwCWCFXC6XO2g/wFAohM7OzoP2e5kP2/LLijAGTAySgNmpr3PtkWYr4xiesHLMTswtlUou8GIFYr1eRyaTcccB4KtIAw6EK8Vi0bXGFotFtza2+fL+pFIpd1x7f4JtvAzcGJ6xKo1719kKM7vHoG3jtWElr4drs0EZwyEAvntrJ9naSckMxIJDWxim2YEtfF848CP4OnvMQ1XtcQ28d3aAil0PAzMGcnzfbcgV/OxwXdxzkse2w1e4BlY9Blt67T6G9nzB9zcY5DF4tJ/Xer3uqhBZgcgvu9ckK2ODbfeHw7VxgE5QMEAVERGZD/2OL7J0bPvGBiRKo+j73QJO9nzjUlsc6XPWo/bYUyf5zCJysiwoBBwdHcW73vUu7NmzBy0tLTj33HPxgx/8AL/2a78GAPjc5z6HcDiMq6++Gp7n4corr8QXv/hF9/pIJIK77roL73//+7F582ZkMhlcd911+OQnP3l8r0qOCsODXC53UKURAHR0dBx1JWCxWESlUkE8Hve1fzLkCQ62YPBkwz8GWmx5ZXDEoIzVWwyBWP3HwIdBDIMruwdhJBJBMpl0VYF2+i8A33nsgA3bSsyqPjvxlW2+fMwOGuGxotGoq7y0e/KxnTkej7tQjEEZqyF5XFuRx3UwtGJIyuDU3mNgdr87G8hyjbZlmcFbcBKwfcwO7kgmk75qPltJVy6X3b8UsPWY99VWtPE9sNdl227tIBdbWcigzFaQ8vpYnWcn//JzEnwvbfuwva+8RntNPJ5tA2YIyethFWa5XPa1Ls+XDUGDWK2YzWY1XVFERBZEv+OLLB1re8ZQqYVR3bb9pJ+7Fg2hnogc+YkicspaUAj4N3/zN4f9eTKZxO23347bb7/9kM9Zvnw5/vVf/3Uhp5WThO2ik5OTB/2so6MDvb29C54MDMy2KjIQsVVYrMSzk1aB2QmrweCFz4nH4772UFbHMfji43ZfNxuE8Xk2yGLLrw3OGLiwZdbuP8eBE/aY9hpYMWcDTV4vWzoZhvK+MBRkeMZKMh7HDrbgee0kYFvFxnMF9wLkfWXbsm0D5n3iuviYvXY7VIT7KNrqPIZbtjKS956VjHyPuSY7HIT3jiEk3yP7/gCzVW/A7ARqG3zyOPx8ca0MZYN7Ttq2cIZ5rDC0r2EAZ6sUbZjN1mc7pMV+8XMQ3L9xPnjM4J6AbONvaWlRCCgiIgum3/FFlobR763H2fFhjL1k+qSfO1ypoxYNAaFffc1RFCIipz5tICVOsEXU6u/vR0dHBzo7Oxd8XLahlstl5HI5F6IBs0MXgu2wNljh82yFIJ/HMMtWxAWrtmzYYwdBMOhj8GZDRAZYDH1Y0cd9CG1Lr62aZHhl231Zpcf1ce9DrjM45MNW/3GtNlBiO60NRW04yECL7ycfY5BnQ85QKORadGdmZlAqlXwBnN0Dj+uz+x8GPysM0Phe2eEswQo+VsXZQSIMz+xz+RUcrhGNRpFKpXx7CTJoDO69aKv2GNxSMKCzQbBtdeb7aKsV7eu4vyIrELnnIj8/dl9FXhMrOvP5PNLp9Lz+PgXfF15DsVh0xxcRERGRU0u0tweJWAXPjHehrTax4NeHk0kAQC2wt/t81MslZP7jKUxffiZyyzJoyq1B9clnF3wcEVn6FAKKY4dOBHV3d/smws2X53mYnp7GzMwMgNngzg4LsXux2f3TbIWareJjyGSDKLZCBl9j9wxkNRmDoXg87mvJZLBjwzi7jxvbpNnGymDN7lPItlbbmmsn0fL4pVLJt48fz2HDHTs0heEWA1UbijG44jXwdVwXQyGuhWHZoabfcl02qApOu7VVcPb+2vAzkUj49uTj+8zrte8X2ZCNLdsMeu17zuDWtuLaz5WtYgzurWjvq91/kfefLcM8XrFYdNduW495b3g+O92ZQa69l7btmYEgcKDaM5fLzTsEtO+9/aywalQhoIiIiMip58k/HcDL2p/F8K8BR7O7c+GVGxDxaoj+8IHjvjYRaRwKAcVhADKXbDaLbDa74GNOTU0hn8+7gI1hSrlcdlV1PLdtwbVDImzQw+AtHo+jVCr5hkwwKLNBn22/5PUxmLHTgtmibKcCzzWYg9OB7X56dk85OwiC62XlIkMh24LLIIdhpN3DzlY8MhSza2Sgl06nfQEX75dtxeXxbDDGcLFcLiMSiSCdTvtaWbl23mO2r9qAjfedVX28v3bCsa20s23KfA/sfn9s+bVThnkPbIAXDBBZgcfPDe+THdzB1mIb1tlKUT6XFZP2/tnzcO32GmzFqt2z0lZ52kpHBoMMFIODeI4kGAICcJOYNSFYRERE5BTzog3INC+8gu9EqTYlEU6nUcvnF3spInKc6d8WxWErIYMRi4MzForBTHNzsy/wC4YrDNEYHgVbWlk9ZcMhhm72Z6zMYiDFcIYBHPcPZPhkgz6+3rYh24myfD0DGNtWbKsMeVwew4ZOgL9qjGGRDavYssrz2z3ueD9sEGgHZ9gQ0LYm22EpdgIz21FZmcewkWuyeyIy4LSfDbY7M3zjZ4RrYOUgX8Nr5Gtt1SIfs3sJ8ty2ipAVjvx8BD9HvE7bRs3rYRBq32M7NISfNZ7HtiYHK2T5+bITefmcer2OfD5/0PAansP+HbDB83wFA0C2AxcKBU0IFhERETmF1F6yES/cWMOlfTtw3w82YEXxf476WONr4+h7vAeV4ZFjWlN+II00ViP8yLNH1V4sIkuXQkDxsSFN8PH5titadm8+DsewLYt2nzoGQhzeYEMgG+iVSiUUCgVX+RccysHn87k8ZiKR8FVm8bl8jq3Usq2tdkqu3QPQVrgxKORefsEWW57HVh3a/eEoOFyEAR6Pb0NLVnzZajsbFtogzYZpfE48HnchIcNa21rNSkyGrDbkCrbWBvfrs3s+8v1lqMhr5320VW3BPQJteBlsFef57NAVBqisaGSlHT8PtoLRBmV8L3g/bPhcLpfdMBOu0Q4EscNEPM9z67d7Edr3hPfFDl9ZCHtvrXK5jEqlgqamJmQyGUxPn/wNpUVERERk/uovPg8v3FDHpSu24r+/uwErb38UtXLpqI83ua6Ont4O4BhDQOBAENi0JQMoBBRpKAoBxWFoZfdeo0gk4vbCmy/P8zA1NYVCoXDQ8Ri62EEVDDFYIceqLdtOy3XaYCw4rde2rgZbRhkI2uCFVXd2Aq8NnHjdhULBt9eexUCI12YDLp6Dg0W4X5wdXGL3p7PVhcH7w/vAYI73g+Gi3RuRQZPneW7CLsMttlUnk0lEIhF4nudCs2Qy6QvKbEWjDTf5XtngkPje2fZme70A3OttZST/ad93W0nHa7X7E/L4wcDUtuXaSdFk9520ewTaYR98L+33fO/4ntpKSVam2qrZ4GfF7iVpW4nni9WEiUQC+TlaNNLpNFKplEJAERERkSVuamUKl658HADQ/9Mcavr9TUROMIWA4nOoQCKRSCx4T0DP81AoFADMDkpgmGUrumzVml2DrQRj4GKfw/35OH3VTpbN5/NIJBJIJBIu4GGVFgMUhkwMPbnXH4MgthQHQ79gNRmPXyqVXMWY3VvRBmm2TZahEyvGEomEqzDjcWxbsq0+Y4UaABdqFotFXyUjQ0d7rOBxgkMmgi3GrATkOlkhyKCNx6pUKojH476gjevknoF8n+x7bytA+Z6zWjBYTWlbe/k+8fNk28kBuHDThmwMJnkNvE6uiZ+vYFjIz5qtwLODSILtyfzc2ApM20Ju27Xtezxf/KwHKwir1Sqmp6fR1NS04GOKiIiIyEn2og2IvHN0sVchIqcZhYDi2ErAuX7W2dm54GPa0MgGSqwMsy2ZbL1lgJZMJl0LbTCY4XAQhl8MGBmQcOgIW0EjkQhmZmZQKpVceBgMjrhWz/N81XlsLQ1Wp9mBE7adOLgXIEMs3kfbysqf2+uzLbAcdGIDU1sVGBwgwnvDVmFWPtqwzrZaFwoFF97xvHZ9dupvcG8827JcLpcxPT3t2q75BcBdQzDQDFYGchKw3YORnwlbZci1sNKT77mtqrTtwXaoCqtMeb/sZ54BJO8Tz2/XUCqV3M/4GbI/5z1jqzrvdbCl2p57oYM85mrX5+dlenraBZsiIiIisjRF1q4C/nQfzkgdqPx74ovnoO0X/436EV53suUuWY30jwoaECLSQPRvinKQQ+1R1t7evuBj2ZZKhnI2LGNwwv3iGLLYcMcOcABm90JjpRjbV9mqCcwGenYvOLZrMjxkUGIrEW2Vl91vjkFOIpHwVX0Bs9VrtmqQQQ3XAcyGe7wXPB4A3z55tpWWrcq2Wo9BFlumGW4xaLLBpg0g8/m8r+XahrIMs4L79LGaLThFORKJuHMHAy5WwfH9sNWYDGh5rmA7sF2v3beR99hOjLb7PnKNvM+28pKBMCf38nNm7yM/A3YaMver9DzPvYeFQsGFrLZS0QbBhULB3T+Ggzw/P5MMMdlavJB9AbnWTCbj3lN+rnhdNjQVERERkaUj2tuDlq+OIxGpuMdi+RrqlcphXnUEL9qAnZfP7/e/8DnrEZ6YRmXnriM+txab+z8+i8ipSyGgOAxF5qoEtMHSfDFMs3u1Baei2j3sgo/b/ddsSy6DGhvk2cERtrKMU4AZ0gEHWpttSytDo0Qi4dvbzralxmIxZDIZpFIpFItFeJ7n1sigzFYssgLN7ttnAy+2/yaTSReeFYvFgyb9Bgdf8D7YikQOmAgGdzaAKhQK7p4xTOTedXxveT4OYeE57L21uAcj34d4PO5CWFYxMrCyLcYMw2w4ZoeY8HwMN1nJONc+iwy+uB5eV7CiNTiUhWvj63i99nhsH+f95NpsyzirSvl553FtYGs/83yerQCtLPAXPt6T5uZmDA8P+34WrNIUERERkSUkHEHHdwqIhGZr/saKWUS8Y68BrM9zN5jaY0+hfuE5iJTLqI7MtiPX63VESjVU4/4DhVJJQJWAIg1DIaA4oVAI6XQamUwGnuf5fsaAZCFYKcVhE6xQYshSLpd9rakAfC2VrAJk1RXDEzsEhJV2NmwDZvdN4znC4TCy2axv8ITdlw6AWwOr8+xzWc1mK/PsvnV8PYMzi2GV3ZPPtrgymCoWi26Qh51EzPCQVWue57l12Oo3hosAUCwWEY1GkUql3PPt3nS26pDrYsCVSqV8ewTa1mC+3h7Dti8Xi0VUKhXXDsz9CBns2veD1xMc6mHXwCDQ7u3H9eRyOXePbWsu3x9bjclwktdkAzleN4ej2D0mee7gcfn+BPcutMNrGBraykQGhZxUzZ8tZA+/4B6Flq0+FBEREZGlJXTeegA73PfbZ9qQu7MfLf/885O6jvr/PAbvlZuQDIVQ+dUk4dr0NDIPbMfU5hW+505fthbp7/0PUKvOcSQROdUoBBSHAchcgYQNOObLhli2zZPHs8GNbWm0e7Sx1ZOVWrYizQ4VYZVXsM3Y7jFnKwBZOcdwzVYt2n3jGKiw1bdQKPgCPdt2y/PwWHatNsRjQDQzM4NkMolKpYJisXhQlVpwcAfbVHnv7H6ANphjwMUgl+8dK/b4c4Z3DFEZWnHIB4ePcE8/+/7YARt2SEa1WkUqlfJ9Vnifg8NA7PvGMC0SiSCZTLr7bd9n7hnI5/JzwYCWz+c6bHs2v+z0X1YM2v0fWUHJcNW2HfO6GbbytbVaDclk0n227J5/9j3nvbYDZ+aqsJwPVrhabI1Pp9O+wTQiIiIisjSk/++orwpw5/0DWPH39y3KWuL78pi5aDmyD0Vda3C9WER8qoJS82xMkBzzEO3vnVf7sIgsfQoBxccGYJbdm2++bHVXoVBw+8fZUJBBlm1vDYZl3IOO67MBFIMq7qPHY/C5XDPDGtsya4MzAL5j2LZKBoF2WrENnGw1GYM7W71o9w6095OhE/fpsxWJvAcMm+zgkWAVGdtD7cALAG5aMFt1ef08pw0a2RJs266DnwOGbjZwtYEnz81QzQahds9BtnjbzwffN37+7HRiu+eeDarnCmx5Dt4rO9wjWM0aXLMNuoPVeQwDeW8YktrPhP2cMUxMJpNIJpO+4ySTSfceBveLnI9YLIZUKnXQ4zx2U1OTBoOIiIiILDFTb7sEbZFn3PdP7u9B10Pz3xf6cKKjk///9s48zM66vPvfs68zc2afTDKTBBIIARIkQIjgwqKUohdWfdvXUl9qta0K1q1eSmvd2gqvbbVqFVtri1oVRKEFFCGAhiUJIYGQjSxkm8kyWWYy+9nP8/6R9/vLfU4mMhOSmcnw/VzXXEzO8jy/5/ec6OHD975vND2XAAD4u/sxmqOWXnwJicK5GFg0HVXhEAo7dqHY24fozm7kFjYfO/bzWzF49QWISgIKMSXQvykKRz6fx9DQ0IgCwZZejhWbtKpMY1HW2ERU5SCIyuSZHd5AqWPLh4Hyybxcw0iTbm1Ky57fns+WvdqJs7Zc2Pbas+cl9s8UYbYc2JYB8zUsY+X5bW85ltXaycE25WgTcva9lGa8Dntv7ICUkUppKUn5fiv4KHQpGG0fQzsMxg51sVONbYqR5+G5KiUqU268Frvf/FxwzwOBAGKxWNl18flIJFImWClvKQvtZ8f2MeSxrRDNZDJO7PG9VmZXCmfgWCpwrOW74XB4RNFXOWBFCCGEEEJMDg7/2RJMv2knYoGj33939tcj/K91iP3Ps6fk+IVdHaje1XH09zG8r7hxC5K5szB4fhMSkRCKL2077jXe/LOQ2NoN74J5KG3YfErWK4SYOCQBhSOfz2NgYMCJC5uaGqnX3WjgMViKS4EHwCXQrLQBUCaybFLKlhRz0i+PzfdRrlnxxBJSPm8lIEtCKZhswowCjuejlLKyEECZwCI2rWbFGZNpPEY6ncbg4CAymYxLPIZCIZc0s/tk03As/eS0YsozXrPtlWjlG3BUorGcmOfl47Z3XSAQQCKRcMfl5FkrO23fRib32N/QliETe28qB53w/tpJwxR0vFdWvlLAVfbY46RgDl6xpds2UWk/T3ZStE08WtFn+/7Z3oqVPSntZ8imKm16tHLq9FjKd/l3hoNkiL3XY5k2LIQQQgghTh8Hb3k9mt7ZgfrI0VBFdzaB4JfrEPjNqold2P+nuG0Hkrk8Bi+choT/XCBd3ht+qC2BxIObEWhsGFXCUAgxuZEEFA47tKBSAo6lXJHY8k+KFAAYHh4u6xEHwPX7Y485O5DCJpsoTRKJhJtsa9Nllak8prp4bbaPoE0NUnJx4AJlHCWTlSt2b2wZbWX6yvYIpNS05a9+vx/Dw8MY/v/Ttnhu2y+RwsqKUb6Xr7ElshREPA5TdZRZ9tzczxOVT1NMURLa0mSeh8fn2kKhkEs1MkFo5ScHhdhz288W997KYpuU476z/DmTyTgxyQm+/BzZxCaTlbaE2SYrPc9zw2tswtMOceF9t/0OrXAbKYVny5Xz+bybJmxLt8fabzOdTqOrqwuHDx8u+zvKcmB+HoQQQgghxMTTd04JFyX6AADZYhD5j6QQWPf8BK+qnOK+Ayhd3Iqh2TXwF44fBpl/40J4T6yZgJUJIU41koDiOOz0VPvYyRAIBBCJRJDNZp2oCofDrmTUpq0oXoDy8tnK9BdFIGVRoVBANBotE0oUIcVi0QkjW2rL1Fg6nQYAJwGZ2LNikaKJKTE74MK+hylD29/PXhvPyeN5nudKgG3ZKAWOLYnmNdkeeZV969gDj+upHABipSHPaUtruV+Vx+b12lQchZvdb76eicJisYhsNuvuOfeYQo7w+rgnvG67du49E4qxWMytna+1ctcKTDukg/eM5+M6+f7K6dFWCvL67ORpCk9+1pkepYCkjK68Hn4WmCIcS/nuwMAAdu3a5T67lkrRLIQQQgghJo6uj70eF73uWIlt3x9Vo7Rz8pXUevkcqp7egYErz0IpdPzQunx1QOJAiCmC/i6LMpiIorizj48VO93XlqraAQssi6S0sWWvlE98L9fBcksAZdKF56R4onyykolyprJM1u/3o7q6Gn6/H7lczom6SCSCWCzmhCWlEpNwTDTaMtPKici2xJXr6e/vRzqddnvMJKTdOyb6KO+4Tpa+xmKxMmlY2Z+PsstOwbWl3hRqdlhHPp93yU3bWzCbzbqhLlYW8n2UZXb4SjqdRiaTQU1NDRKJhPs82Om7dk128i+AMjFqU56VCT/uE0uMbZrRCkz7+QuHw2Xl3va+cS9t6TOvLRKJuHPzs2ZThSx7t58N+5nlfaGU5OdhLEnAwcFBdHd3H5fO5TFzudyIglAIIYQQQowPud+5FPV/vRP1/pcQCRz9rp4tBlHaf2CCV3ZiiocOIf7fhxE4exYGLmw6/gU+H3AS/04ohJhcSAKKMmwPMysB7e+jgWk74OiUWqa77EAOAGVSzU4ErjwO01nZbLZMqtkSXTswg5NZmVyzk395TCvrKIYoHFkGDBwrQWaCjXvB8kvKsWw26wQkZZAVZFb4MUXGicl2AAZhXz+bwrRlwpRJtseiFWV2Ci/3jElGm4bk/vHYdmAJj8d7ZNOWVnTaMmzuBXvWUQqGw2Ekk0n3Xiux+L5wOFy2JptKtfePew2gbMgKJZ9N4lGa8nnKMptutINSKPQqh3pYKU1ZyT3mcSrTlbx2i+0tyfs00rTfE5FOp9Hb23tcOpefx6GhIfT394/6eEIIIYQQ4hTh86F0xUK0fX4LAr5jwmz/cDUCNxVRyhyZwMWNAs+Dr1CEv+ChFDz2Hdbz+ZC54VLEHlkLf00VSm0t8F7YOIELFUKcLJKAogw7hMEyODiIXC43psQSE1+UQraUlc8DcKXCVhzaScIss7Qij7KP/2Q/PSuRKPRsiowJNps243XbwRwUQhxwYSev2lJOO7yEpc8A3OOUmhRH2WwW6XT6uPJmWzJMicdBGBRUNtXGfn/AsUnCNi3IexgKhZywskM27KRb7iGFlk3Z8V5RbFb2W6SI5b2wSUMm5GyfQqYq+XorA7lnViraveZ9sv0dKUR5XZX9GgG4hKOVz3aNVtLZqcv8HNo0Ij/HPLftF8hrGKmknXLRJhX5+FhStp7nYWhoCOl0+rj3BQIBJJNJ9PX1YWBgYNTHFEIIIYQQpwZvyQLM+MftZY+91NOMuk8FUNi/ZYJWNTYKuzoQT8YxOLem7PFSyIfs1QsQfmQ1cLh7glYnhHi1SAKKMuwQBcvg4CCGhoZGLQEp/Hgs9j6zAmmkxJeVL5RH7P9WeRybTrS9/OzUWPbss3LITm/l75REtvyWj1kxCRwrjaZEopCy122TZbwOKy0p3bjnTDja6bRcM9NsvD7eI66DwyZsio1pRgpDm8C06TybmCR2zdxzylWum/eOWGFn++zZhCYlq/2M2TLiytJcUinbrKTlZymXyx2XALSfJQpHDpPheuxEZFsubnsBVvb1457wWvkcX2cHlNjXEyuArRQfDQMDAyOmAO15OGhGCCGEEEKMH7nrLkHbF7aWPbZ6fxum/UMIxY0vTtCqTg7/UBqBTBWK0fJKJc/vQ6C+Dt7QMBAKoaT/8CzEGYckoCiDPfwqk4CZTAYDAwOora0d9bEoX1g2STFly3MpzDiBd6TySVLZW89OdwXK+wXyPADKJtraY1OWVU4Ktn3+KGps3zk7kZiJPEo5ykuuh+k/pgGLxaJLR/K1lGN2+IQdLmGFWGUfQ4o9SileJ9fCJCOvt/IarLDk8fg4X2fTc5SFNnnItfCaK4/LaxweHnbijD35Kvebx7LJ0EoxbYeb2P2wspbHDIfDTi7bicGUkXa/uQabRmSptr3ntpzc9obkNXOvuKc8LtfFtfCej0UCptNpDAwMHNcP0D7f29s76uMJIYQQQohXz8AfXI5Zf3G0BHjPUAqdz8wAAEz/TQ6+5WfeVN3Czt2INFZjeHq87PFi1I/MorMQ7eyDr39IElCIMxBJQFEGyy1ramrQ3d3tSk7T6TT6+vrGdCyWtHJ4AiUIj0lJwh6AlQKNCT7bz47PW3HF/oUcDGHPX9nTzSbsrCQEUDZVlZKHyTkmCm16j8/Z/oK8Pls+mslkjht6Yfv3MX0YiUTc9dpJvhRTlFjcS5ZI2wm9PD+PwZJoW55MIcUUXC6XK0vuWcFECVYoFFx/QjtZuDIlaZN+9j5RLpdKJUSj0eN6EtrUHo/LXoe8JlsSbKWrlZOVaU1eA8u0KZ5topQizyYYKwWdTfpV9la0U4v5Xu6VfZ2dXmw/v5Wp29/G4OAgDhw44P4OVZLJZCQBhRBCCCHGke4/XYIZf7QDALD2Bxci2lvCzB+vmOBVvXqCe7tR1ZfG8JxaFMPHEoH5RACYXo3YEfWgFuJMRBJQHEcwGERVVRWCwWBZ37nBwcExHYfyKhQKuaQfhQ2nmHI6cCwWQzabdeezUom/U9LZHm223JjJQ1tWyvdRolHkWOFIIcRjM5lIIWjLUHkcCj0mGiuTbFyTHYBie8HZ9CGTcTaxZxNotk9hpewCUCZHrWSk7OIxKP/s4Av2HbTDLmyqrbJnH/eEa7aTie2abN88vs/KX+47/8x1Ms1oZWM0GnWvYz9ACmZCIchzc08oFfkc77XdB9sr0ZaNW/lsJw9bqWw/H7aU2PYGtClUe33BYBCxWAzxePl/Yf1tDA0Noaur67hBPT6fDzU1NfD5fCoHFkIIIYQYR6p35tD99Vno9oCm+5dP9HJOGYW9+wAAicFWDFwyA57f9NGuCgIXzEBoVjMCm3ahqKF0QpwxSAKK47BiixQKhTFPHGXSLRwOu/58FEG27LeypLey1JGijwKGSTtKJCbZbL/BQqGATCbjZJo9jy3htAlDluraybqUTRRFdo22L55NkdnEXTgcLku+VSbLmCykGLNSrbJEmMenUKvsR8c/2/55FFo2mcb3MGlnsQKS98sm9Wy5LAA3KIODSEqlEtLpdFkpLT8HTChms9myNdteh3Z4i73/vPe2TNr27LOpQP5OcWevgc/ZfeFrstmsS2DyOu1xmYa0/R6tXLTy2JaT21QihSdTnZFIxKUUR8Pw8DB6enrKJmjzuMlksmxNQgghhBDi9BN6bA1Cr/yyM5bC3n1IZjIYfMMceL5yEZivCqJqZxSQBBTijEESUBwHS0ytiEmn0+juHtsUKEopOwzDltzyNRyuYQUTh20wIVYoFJyYo8CyiSs7TZbSxZYA2z5ylISUe7bfnO3dZif/WknFc1qBxWuxiTuukeuu3AOWG1dOjGW/vEpJWSkKrRyzUpRrsvtAMci0JctXbeqP95zis1QqucQZz0mBxetnX79cLle2Z1bq8vdoNIp4PI5QKFQ2TZgSjklEXmvl4BCKMzv4xfbvs4KZ18295vm4X3wP1839O1Ha0vYF5OeDZcDAsV6SVkyGw2FXqm7TnOFw2E1uHms5MHtzjtQTkOXfQgghhBBCnEqK3T0nfG540UzElg2hNDQ0jisSQpwskoDiOJiKqhxIsW/fvjEdh+KJ5ZQAEIvFynrYUTSyD5yVdjb5ZXvfsQzUDmpgas321COUMhRC7JEXCoXcn0OhkBNv6XS6LM1lE35W1vEabRmrLQNlWozXk81mnaxkiS4FJKfWsjSaiUdSmSbjdfE6eG122qzPd3SqMPcuEAggEok4gcV1VaYKmaLz+/2IRqPueZt+4/msOKMEi8ViZfeS10jxxc+FHRwzUirQlm9znylPuQ+UuPZeUara+0WJaAfBBAIBZDIZNyU4FouV9VCsFOE8J8WhlYe8r5UJRtuf0ZYZU/SO9Jk6EYODg+jo6BhxOjDv1VhL9oUQQgghhBgNsQfWIJBMYPDqeWWJwGLEj+Grz0fsV2vh5Y+vNBJCTC4kAcVxZDKZsnQccFRodHV1jek4lB22JBc41tuNAogShL3eKE0om7gW23OPabtCoeDKgZn44jl5HjtIolLSUDpRuFVKGcpLDpeonBJbWRYKwB2Lcm6k6a9WiLE3H49lk5J232xazZ7PJv9s6SvFFK/DJuqsNKOcjEQi7jnKNIpRO2jE9h+0/RZzuZwTqEwKMoFnS2Ntqg8oH9ZBaWyTinwv039WEvO6Q6EQotGoE3FM6/H6uE/A0c8398UOa7Fy2u4dz22HeViJynsUiURQKBSQTqfLSsWj0ehxJe82zWnv8W9jaGgIu3fvHrHnHyXgwYMHR3UsIYQQQgghxkSpiGJ/P5LP7MTAlWeVPxX0YfiGixD/hUSgEJMdSUBxHAP/f9R7NBp1vwMYczmwndoaiUQQCAQwPDzsBI3tUQccK6m0QzvsIAqm+yi6stmsE1ic3GsnytqJrVZksYTVJrPYZ8+W1VZKKopL2++NiTY7bMKWu/IxOx0WOCYbKZeIlYpWMFUO/qiUmrYE1vbJq0zwMXHH91FW8Vq4BluCbMUaj2tlpB2ewf1LJBJlA0XsIA4rMXmNhULB9eQD4Eq12aeRctKm7zzPKxuQUdlvkZ8fnoP9C4FjopvP255/9t7wh2viOtjLkddgRSU/6xSF/AxxHVbYep436p6AQ0ND2L9//4i9HKurq+F53pjTukIIIYQQQoyJUhGhoSJKIV/Z1GDP50P6dy5C9MFVE7g4IcQrIQkoRsTn8yGRSKCnp8cJs7EmAZksY7LL9qijFGG5KktXKasqy0QBuFSd7ZPH8k0OtKDwAsrLRQG43nw2CUYqJ9hWyjYrbWwij8k4K5TC4bArswWO9WqzEpBr4X7wHFwzr4n/pKyj8GRqzvaoY5rOyk0rJVn6ylJUylaWpTI5x/fbISo2pcjHbfku1xaJRNykY0rDXC7nZC2FmhV2VsxaiWn7PNpBIHYfrYTlmrk3fD2TobxX9jNihSkTpfzsWjloP09Wolr4+bAS2QpCrosyMRwOu9Lv0TA8PIzOzs4yMU+qqqrc3wUhhBBCCCFOF8XuHoQe7UFwVjuG5jejGDEi0A8E6ut+aw9BIcTEIgkoTkgikXCJKM/zsGfPHvT396O6unpU72cJLYWOHW5RWbZaOeTClkwyoWaTVRQrkUjEJQopV1j+a2WNlWUs1+TrbaqOPdxs+TFwLMlV+VhlYo/nsj3p+EM5yH6LVm7agSI2ccfhF/a1LCWuFKxcu00H8oflutw73ldKRivyWJ5cKSZtWTblIEWjLd0FUHb9XLctjebx7HAQO7CD95Ty1JbnWmnKP/NnpM+gnWxMQVmZXLQykWu16UN7HZVl5VZU28d5vytfw9JdTo4eDYVCAYcPH8bBgwfL0o88dm1tLXp69GVLCCGEEEKMD4VdHUj4/SjWVyHdHEMp5IPn9yF96dkI/0rfS4WYrEgCihMSi8XK+uMdPHgQmzdvxmWXXTbqY1DUAccGgVDucGqqHVDBNJnt02Z7ANryVqC8fHakwQz2d8pByiYm4JgAsxN/KckAlEkkO6zDltHayb38M0UV18bEG4/F5KJN27GMtLL815aV2jJqrptCr1QquXNY8eh5nhNO9p7a3ymsrATkeYaHh+F5HpLJJCKRCLLZLDKZjFujFWi8B1bc2fJZXmdlao+CrbJUvHLIiJV4lf0ieW/tOSrvE4UhRWU0Gi0rbbY9Bbl29qjkZ4TnZ9rV9vsrFouIRqNlctOWj9vPi92X30Ymk8G2bdtGFH21tbWIRqN46aWXRnUsIYQQQgghTgWFHbuAHUBi7llAKIjBc1ITvSQhxCsgCShGJJ1OI5VKlUmiTCaDXbt2jUkCRiIRN/k2l8sdN8WX6T7KEJaNUu7YpJ6dSGsHQ7Asl/LMSjWbGuMxWQrLvnPAMaFnk2N24AdTa5XDI2zfw8qBGVaC2eeDwWBZko79DSORiBOmTMBVDlWhiMxms66UtDIBZ4WTTfnZBKAVXoRlxgDcfeL6uUam12zyza7B9tXj/QKOlSbz3nE/KBqZUqTktWW3Vo5Wylw7QdiWBNtrY3rSJiT5WpsGtX0PeTx7bVYq23VFIhG3Xzw+z2uToJUSMhQKoaqqqvKvzIik02ls3boVhw8fPu651tZW129TCCGEEEKI8aa4bQcAoGq4HSgUoQY1QkxeJAHFiBw8eBANDQ1lkqhQKKCjo2NMx2GKCjiWfrO94SidbFktS0T5nJ0iTGFke+IxfWaPTRHGXnA2xUUxRgnHvnT83fZVswk8yhuWilIqAscEEUUSoeSjEKMIovysLCvlcZku4z7YEmcrG/keW+7LJJ8tx6XErBzEwtfz2KFQCOl0GplMpqw0295DrotTbynE7MAKOyiDf+Y+2gnE9rrtflAMMzlqE312D/h+lk5b4WlTeFwj74UdaML9pii0SUQ7xMQmUa0czOfzZf0fgWOl6+zHaD+n/InFYqOeDJxOp/Hyyy+jv7//uOeSySSGhoZGdRwhhBBCCCFOF4VdY/t3RSHE+CMJKEYknU7D5zs6rIMJo0KhgK1bt47pOOFwGJFIxJVyssw0l8uhUCggFos5+VZZ+lopTSiqbKmwTQ2Gw2GXBrRlvzyfHeJROTSEpboUZVbS2UQYJZSVa5w6SxFpy2P53lAo5HrQASjrb2fLSe3j/DMlVeUaKMgouGzvPFs2TTHF81CgMS3Iqcq2/6BN4lWm+WxvQTs5mL0OKS0r984O+LD3kT0O+RljmrByijTTkRSrlMUcKsJEKNdmf2zycqSpvPa6rdC1aUrb+9EOAOGkYaZeeQ1cI/8e8Ljck9bW1lH9HSoWi+jo6EBXV9dxA218Ph+i0Sg2bdo0qmMJIYQQQgghhHjtIgkofiv19fXo6+tzImbr1q3o6OhAe3v7qN4fi8UQi8UQjUbddNpQKIR4PF4m0piUs4KPko+iBzgmnqw0s73jOHSBkrFQKLgSY+BYzz4mFG1vPQBliTce30ozJgYpvTiQhIlAK8Uqz2cHmFDAcd1WVtr+grlcDrFYzA1Ysf3quI7h4eGyQR4UZp7nuZ6AXAevjzKNwo57bwVqpVy0028px7LZrLs/FGCUZtx7W+5ty4Ztrz+fz+eSfNybYrHo3m+Hn9jPBxOQVnbaEm6eww4x4eusBAyFQmVJSq61srSXx7Kl1Ty3LTmmULZDXmzJOXsGjoZ8Po8NGzZg//79xz03ffp0BAKBEROCQgghhBBCCCGERRJQnJBSqVQ2vbRUKmHnzp3YvHnzqCWgxYqWWCwGABgYGDiu/JbyD8BxZZ8jiRgrWThYxPYc5NRcCq/KIRsAnIDjeSjl7FASW15rX0epRMlpp82yD54dAsJ1U5TZ9B+TgfF4vOyYlEtWMNp1sS8d10zhRPlmhRgFIPcxk8m4ibO2159dfywWQygUQiaTQbFYdOuzpbos3+U1ssyYx7V7zffwd+6PFZG8FitM+X47DIQJQf6Z+0+5aQe18LPAzwjXasuC7b3jZ5AJU95fXg8/A1wfJWUkEkEsFnPnrCwRb2pq+q1/Vywsw+/t7T3uuRkzZuDAgQOjPpYQQgghhBBCiNcukoDihORyOSd2yPDwMHbt2jWm44TD4bI+arlc7rhSVgBl6apcLodQKIRYLIZ8Po+hoaGyKa9W7lT2B6REYtKKiS+Kpsppuj6fr2wwCbHyzibNMpmMk0ZWrrHU1O/3IxKJlPWKs8lFWxbKfn52um6xWEQkEnFpMq6D/Q1zuZyTTpxOm0gk3Nps2Sz3lgm4bDbrJCuvh8di8jASibjkH2UbE5Y8Ft/DUmI7CMWmDpkW5LFs6bctu+V+29JdllGz3JfyLxKJlJX3UlhWlo9zrcRKSO4nJST3jJ9TKx0rP1tWKnKN/FydaPIy7zXvQ11d3ej+8vz/e7pz50709fWVPd7U1IREIoFnn3121McSQgghhBBCCPHaRRJQnJCuri7MmDGjTAJyQnCl8Pht1NTUoK+vD9lstkxMAccEoed5SKfTZRKNcs/287PTgoFj5bZMb9nyy0Kh4I7PXn8UfhQ/lDh2iIWVdJRaNmHHNJxNIlKW2oQd15nL5Y6bFAwcm8ZrS5xt7zsrjoBj8s0KNgo7K8RsT0MrSW3KkmvnvlYOu+D5ADiJxxJWrjESibhhLDwX99CWetv0YmWpLwWn7RXIa7Kl2jaVaO8NX8/SbJ6Pv/O6bY/GUqnkyq7t8zz2iQbD2Htm74ndM3v9nEpMocj119bWjurvDa9z8+bN2LNnT9njPp8PTU1Nrg+jEEIIIYQQQgjxSkgCihPS29uLmTNnOvEDHB0YsmnTJnR1dY16sAEHJlC4UMJR3gDHhk7YqapW6lEeRSIRJ+Q4SRjAca9l6THP4/f70d/f78pWgWODR4By4WclG19HycTjU0YyHcbUH9dGyWRFG3v0cWIs3wccK5e1ZdC2bJh9DcPhsEsO2tQaz8H7xMQlJVlliTDTlixVpSjkP3kM3gsKUwo87q0d2MJ7Upmks3LUpku5Jgo7W8rN/bH9GK3MKxQKyGQyiEQiLhnK6+O9YUlyZem1FYkjJQr5OIfa2PLlSunH4xBb2kypyb1g2fb06dOPO8aJKBaLWL169XFTuROJBFKp1JindQshhBBCCCGEeO0iCShOCIdYJBIJZDIZAEelxLZt27B58+ZRS0AATvxY+UEZMzw87FJTwLH+dZXTYyl1KI0oW2xfOtsfkCKI5wqHw0in067U2IoaK3hs6SqPy9dx0IadUEvBVykd7QAPrtNOOGYZLQAnofhPSje+n/CamKqMRCIumcfUGRN7lT0UK/vw2fJbyko7eZh7Yici8x7k83mk02mXjKvsgWivIRgMlpX4ch+4X7wXAFxfQ+4pP4c8P1CeyuPwER7bXjvfP9KgEPs5pGS1MpAykUIxGo2WlQzbZKEtJ2ey1KYF+VmNRqOoq6sbdYIWAHp6erB69erj+v7V19cjEomMOCxECCGEEEIIIYQYCUlA8Yo0NDSgu7vb/bmnpwc7d+4c0zFqa2tx8OBBJ1/sZFybEGNKjYk8isLKKb029cbnKnsGWrFI6cWEoX2dnbrLY1No8TFOM7bDIipLXCmv7LXZYRdMGBaLRQwPDzu5xZQkcGw6cTqdLisHptTieSm9WOZcOXRjpHUxwWelnh3eAaBMjtlhHrbU1w79YFKQz/OeVfbns5OR7bp5Tyg2+Zi9P5RtttehLbu26628DqYtK1OOlLmUrDYlSKHJz1flPtgkZ+VQE5Yac6gO95kiu76+vkzqvhJPPfUUVqxYUdarMh6Po7GxEXv37i27fiGEEEIIIYQQ4rchCSh+K4VC4bgeZr29vVi3bh16e3uRSqVGdRwKPibAKPeYhrPizMomih4rmliiWdkr0CYJLbaslBLQDo7gOSkeo9FoWV9BltCydJf7YgUMX0NpxfVzPXYyr+0XaEuPmciziTeuzSbxKOGYquM6bHKPa2IpbzqddmvlMbgGCj2uw07RtftHgRiNRst68tleg5Xnr0z18V5y/Uzb2X5+XAMTeVYA2xJpm2a0j9myav6zchAI7w3PwfQme0haycfPgcWK2ZGG3FRKbk5YttO2R8MzzzyD3bt3l52XA0G2bNlyXJ9JIYQQQgghhBDiREgCit/K4cOHMW/ePCeTgKPDQZ5//nls374dixYtGvMxS6WSSzbZtJXFCiIArkcbJwvbUl0ArhSWAovJrnw+70o1bVrOpggrz2f781nBU5k2pEiyfe2s7KPks8k4nisWizmpRqHJBJkdlsJ18XV2rYSSjRLPTnS2qUnuoS17tolGCjqbOGRa0PZDtIM8bMqvUnJyn2x/RN7naDTqBDAFny2pZcKO99AmNu0+8Mf26rPl4NwX9g6kOAZQNjDFik8rCvljJyjz88ayaPZVpORj8o+fM8rFxsZGd42jYfPmzdiwYUOZbI7FYqirq8ORI0fK0oFCCCGEEEIIIcQrMfrmVOI1SUdHB0Kh0HGJv87OzuMmlr4S06dPRywWQywWcz3ygGMCkP3UmMoqFApO2thBFLlcDul02r2eCUAreGxPN9tnzw7DsFLOTp61Us2WejJtSBFmxSOlGYeDUCRZyVWZvrP9BXmsXC7n+i/aycGEJbh8D4euRKNRdzy7nzwP5ZQVp3ZgBs9nB2nYnobhcBjxeBxVVVWu755NI7L/HwAnYCulHa+R0pBy0vZytJOZ7XAUrsnuKwC35/ZzVLnvfKzyx/ZgtLKR2N6BTEAGAgHXP9CmBW3ZNtfCexuLxRCPx5FIJMbUD/DXv/41li9f7v4cCATQ0NCAmpoa7N+/H7lcbtTHEkIIIYQQQgghlAQUvxWm9lpbW3Ho0CH3+KFDh7Blyxak02nEYrFRHSuZTKK+vr6sVJNlqpx4a+VNNpt10ouShpKLKSiKKMozyilO4QXgpJ8dTkEoaiqfs/LNps0q+/PZ6bK2x56VYCylBcpFlRWNFGT5fN7thZVbti+dHTphpx3zfUzeUdDZqbsUodFo1CXtOEGY+2HLjW35dCQScZOd7bV7nod4PI54PI58Po9sNluWorPDWijz0um0K+O26U17L7LZrLuPdlCJLTG298+mE23qsFAoYHBw0O0BPxe2lNneCztB2SY7bRKSa6bQZdLQDlmhfOXnfiylwMPDw3jqqacwNDTkHotEImhoaMDw8DCGh4dHfSwhhBBCCCGEEAJQElCMgp6eHrS0tJQNNBgeHsbKlSvR1dU1pmNNnz7dybJIJOKEFmWP53nIZDJlEoVllpRBhGkym3ijLKsccGHPZ/sHWsFG6WaTYTYxls/nMTg4iHQ6XSYW7URdSiW7JnudVuDxPDYpyD22ssum8/g6K9ZsOpDyjvuTyWTKym1tIpDH4/H5epYJ8xoBlCXvKOm4j6FQCPF43CURuUa7ZnvNXKedXGz3kftkB5vY3nyUb5UTfO1rbMl2ZeKxsoyYe0BJyPfaachWwnLvK6cmV94PHpMpSlvC/Uo88sgjeP7558sei0QiSCaT6OrqKpODQgghhBBCCCHEaFASULwiW7duxRVXXIHm5mbs27fPPd7Z2Yne3t4xHYuJMabVIpGIS09ZiVYoFMr61wHlE3mJTZhRGNkprRQyAMqOZcWNHUhhj2llDxNx2WzWSSjb362yLx0FmZWCoVCobMCIFX1WgjFdFo1GXTKQKTOWpbIfHeHarOCypct28AkHtFDocepuoVBwr+ExeX5K02w268pp7aRcvp59/ii82CvR9jW0vQ75PnsfKeKi0WjZFOjKvabko2S15di853bQByUlU6Tcb94XJiXZf9AOXaFE5D7wM2sFLT8rdmAI5fZYJgJnMhmsXLkSO3bscI8Fg0HU19ejUChgYGBg1McSQgghhBBCCCGIJKB4Rfr7+1EqlVBXV4euri4n4vbs2YPt27fjggsucHJkNLS1taG/v9+VYFrpRCHDxJedUGtTVpFIxEmjymnCdmBHpeSzyTsrpQA4aQcc6+lmB2LwnNlsFoVCwckxJudsrz/2M2RKzfbGi0ajTqTZ0lZOULaTjCkI7URdm4hk6Wkmkykb5MH+dXZ4BeWcFVzZbLZsQEc+n3eSsRKmBNnXzybxbJ8/mzKsnJhrpV5lv0PKUjtZlzKWKVEr2mzpNtfLISbESkVeO4Uf77cdGFPZb9Cew049rkw22l5/tp9hKBRCbW0tksnkK/ytOMaqVavwxBNPlF1HLBZDU1MTDhw4gJ6enlEfSwghhBBCCCGEIJKAYlQcOnQI9fX1SCQSLonU3d2Nhx56CG984xvR1NQ06mPV1NS4NKDtU1dZesqkHRNYlX3yKJWILe2lLLLJQKbgmBaj8KGos0MpKLcoyiqHh/B4FHIUYPl83vXXs6KQ4szKO3tOJuV4fJbxck+YUKNQpJCsxJ7PylDCY/C6eB3cf+7dSOW6dgIyS2aBY+WxtlS3MrXJRJ7dX1tuzHvHe57NZo+TbZXH4nlt2bGlcmIwE4/2uLxvVv7ytTZNyb2tnADN41NSAnCfs2g06oapjJa+vj4sXboUmzdvLjvetGnTAByd1i2EEEIIIYQQQpwM6gkoRsW2bdsQj8dRW1vrHsvn83j66aexY8eOEYXUifD7/Whvb0d1dbUTbZW95li66XkecrmcS6sBKEtn2YSd7W3H1/M5K/zsJF3bG86KLDsplik3HouJPSvBKH4oBFnOytdygi/XZ6cIU/7xzxwkYlOIxPa6swIqmUy687F/ohV13Dtepx1yYY9ve/HZRB6vm/eE57GTl215Mu8bRSP7DabT6eOSelyDLaEGULbn3DOWLNvzVE5P5p5GIhGXUOW9pehkMrJyQrDdYytCK8t5KS3tgBbbH5FpykQiMerBOQCwdu1aPProo25gDgDU1dVh1qxZGB4eHnP5vRBCCCGEEEIIQSQBxajgIIK6urqyx3ft2oUHHnjA9VkbLalUCqlUyokf2/fOTgdmIstOxo1EImUShpKpUvJQQAFwSTZbCmwHQPAxroVCJxKJOKlm+w4y9WeTZJFIxA2XoGxkss+m1qy4tEMkWK5qE4ZcD+GaKBNtbz6m0QqFQtn0ZAo5O/DDCjuuz/bGs5NzKct4fZWl3xRx7B8Yj8ddctMmGbkG/tPuB4WtLc2uTCPyeZYw8/g2IUmBDOC4kmtbomvLh3k9tsTZ9qjkPaeUte/h3tlhJkw6xuNxNDQ0jPrvRF9fH5555hls3769TNq2tbUBALZv3z7qYwkhhBBCCCGEEJVIAopRUSqVsHbtWlRXV5f1NysWi3j44YeRyWTGdLxgMIjGxkZEo1GXlrKTcynCKL1s30BKn8pEHctLKbNsWWxlYs0OymCKz/bd4zFtaSxwLIVon+d6mc6jpLN9CDOZjCvxtf3yQqGQm4Bse/7xz7ZEeCTxyOuwZbXcCytFrRi1g1NsOpI/Iw3ssMNQfD5fWTqT9ySbzbrjxWIxl4K0A1Zs8pIl0ryfFITsFcn7ztdGIhHEYrEyGWmPQcnHvaJErixNrkyt8p7w80bpyN6PtlS5st+kz+dDLBZDIpFwE6Q9z0MkEsGMGTPKhse8Elu3bsWjjz5alvarrq5GfX09MpmMBoIIIYQQQgghhHhVSAKKUXPkyBFEo9Hj0k3r16/Hz372szGVBANAJBJBIpFw8otDGxKJhJv4CqAswVcoFDA8POzEUWUSLhwOIx6POynD1/F5Sh6bUrNSLp/Pu7LLoaGhMrFEgWjTYLZUlGukNGQPQK45m806EWgn21rJWbkelgXzz0zCFQoFt0c25ViZkrP7w3NWyk6gXLoCOG5/uNd22ApwfFnsSOW4laW+dsALn+NrPc9DJpNxQpE/lHC2rJo/vC6mKSsFrZ2WTEnKPeE9L5VKZWuwstYmDe3e8PMai8XcsWOxGJLJJBobG8c0DKS/vx/PPPMMtm3bVtbnsqmpCcFgEC+++OKojyWEEEIIIYQQQoyEBoOIUVMoFHDgwAFMnz4d3d3dLplULBZx33334Y//+I/LpqS+EtFoFE1NTUin08hms04QsSx4aGgIuVwOAMr67wEoK2el2LE97KzEsc9TfDEJZ/vL2cnEnuchGo26x1lSWyqVnECzMovluZyUa+Uhz2F7F/IamDqzJah8P6cDc822jJXyi9OJKbJyuVzZgA3uHUWjHZrCXnVWiLGPn+1PyLXwWijA7LAO9r+zfRltjz07eIOpQduXkffWphgrBR3PaQfCWHkXDofL5CfPzetlcpLPca95LnvPwuGwu1+c8mzvEX8PBoPuc0FBWVVVhRkzZozyb8FRdu7ciV/84hfo6uoq+zxOnz4dwNFSYSGEEEIIIYQQ4tUgCShGTaFQwK5du/D6178ejY2NZeWJzz//PNatW4dFixaN6ZicoDowMOAGYgDHSmWZAqPAsn3dKKpGKovl66LRqCsdtvBYTMpR6oRCIaRSqbJ+g3w9j8skn+3rV9ljz/ar8/v9iMfjyOfzrvw4m826RB/Lf20fOh7X9pmzCTE7vZZlwkymAcfKk5kQtEk2e718L+VrZfkszzHShGOujfKToo3XXjnIhe+JRqNO5vn9/rLzM9nIa+D9t0LQCj++rvLzweuvnBBsS6TtlF/KVSuGbWqRr+M6KVZ9Pl9ZP8xgMIiWlpZRfvqPcuTIETz88MPYuHFjWZp25syZiEajWLFixZiOJ4QQQgghhBBCjITKgcWY4JTXurq6sqmnBw8exL/8y784MTZamJyySS0O9IjFYqiqqnICiok+pvGSyaTrv2f7+lHW8PFisYhMJuMEF6UY0102KceUF7HDSuzgDABl6TBbLktRVjnUwso8ro/lpLYk2q7ByjiKTmL7/NnefhRr6XS6TJry/CxD5nRflitXyjjuuU3vjSTWbB8+puJsmW6hUMDQ0BCGh4ddvzxeL49hS6V5L3iP7KRjm8Jj8pFCNZ1OI5PJuFQeh5PwPlb2I7S9BilTmQq06USbwLSTg3kNTACGQiFUVVWhqqpq1J//UqmEnTt34uGHH8b+/fvd47FYDDNnzsTAwIAmAgshhBBCCCGEOCUoCSjGxPDwMHbu3IkFCxZg//792Lt3r3tu6dKleOmllzB//vwxHTOZTKK+vv64xB6TeZRKFDJA+URWW2YbCASc/OJjNl1nS3TtoAs7zdYOmbC9+sLhsCv/tCWitmwWONYXj+ezJa28PoolCiSKQ5tWsxOFKSOtaLTpRJtmCwaDLoFo03sUXTb9xz3gdGU7BMX25GOvQVsmy8EqvBYO9bD3j1QOG+HxuQfFYhHpdNoJSqb6bAkuj2P3nffXpkCt7LPCEDjah9J+DipTgyyLptQd6Xj2nJwKnUgk0NjY6Mp3R0t/fz9+9rOfYcOGDQDgZHZrayv8fj9Wr149puMJIYQQQgghhBAnQhJQjAnP85xk4/ANyq99+/bhH//xH/Ef//EfYzpmMplEU1MTMpkMhoeHnQCqFGsAygZcsE8fX8N+eBRclD/BYLBMXtnhFuxLxwScTfVRjFGm8XU8BktwC4UCcrmcS4hV9p5j4o97RdkGoCwVWCmm7JThUqnkZB2FnBVwNgFJcWkn2g4NDbmSZaYKrVS0AzOIlaChUKhsQAsA17uPaUEen++xiUJKVZZA27JiXivvG3sScu38oWxl2hA4JnmZtuRrCoUC4vG4O799Dz9PtlcgpSLXxX200pJ7ZVOF4XAYkUgEqVQKLS0tY+qJWSqV8MILL+DJJ59ET08PAKC2thbTp09HXV0dDh06NOap20IIIYQQQgghxIlQObAYM319fejq6kJdXR3i8bh73PM8PP7441i5cuWYj5lMJpFMJpFIJFxppe0FxxLhypJQYie3smefTffxtRR2AI4TaLb/HV9P2cdSYibV4vF42SRZW/prJaMVcRR4lGBM/VGmWTnGdBlTcSP16gNQJg+5Xju4g9KOe0hxaafw2r2gAKsUmNFoFJFIxMlMWyKbyWQwODjohCvTdHYvuLeJRALhcNjJPJYBU3La1B5lou3hxxJgpg5tajMcDpcNG7Fl2ba34kiTj+1ng2nNyonJPC+f575UV1ejsbHRvW607NmzBz/+8Y+xbt0691g0GkVjYyOqq6uxY8eOMR1PCCGEEEIIIYT4bUgCijGTyWTQ29uL+vp6TJs2rayH3v79+/Fv//ZvYz5mMBjEtGnTkEqlEI/HnTiz8ocyiRKLaTFKOyupKH2AY0NCbJ86W/oJoCxpxkSbFYK5XM7JqlAohHg87nr5MWlICWSHjgDHphPbnntWElJm5XI5J/tseSuPyXVz7bYUGsBx77GSETg2KZdSk1LQikPuhZWqvAbuF/easjaXy2FoaKhsoAavy5ZK23vE54m91yyJreyhOJL85b5RAiaTSVRVVblBK9wD2wPSlojb4SlWGFsxzOMwycjeiOwBOG3aNCSTyTF/5n/+85/jV7/6VdmAHd6rbdu2YXBwcMzHFEIIIYQQQgghToQkoDgp9uzZg8OHD6OxsdH1WQOOSoxly5Zh2bJlYz5mLBZzffdsCS5LgAnTYBziYF/DRBmlHoUTcKy8lfLKDtugsKJUYlrQ9p8baSgH5ZRNjdlknu0tRzlZmdyrLLP1+XzI5XJlwzoq+9RxXVaO8nEO1qCMs+ezoo8DOqyws70MKbp4L+LxeFmJMs/H8udMJuNEpi3Dtj0G+bg9N/eBUpSDOijueL+Y9LPTiq2wI3bwB4CyEmqew76eJe42ZTlSWXQkEnF7y3XV1dWhurq6bI9Hw69+9SvcddddZT01Q6EQGhsb4Xkedu/eXSZ0hRBCCCGEEEKIV4skoDgpmIhqaGg4ToJ0dXXhwQcfPKnjNjc3O8FSVVXlUoHRaBThcNgJwEwmUybvmBgDjpVxWtlDsWcn1wJwyTtC4WN7A/KYFHj2cUolyj9bEmt7BPIYdsDHSINAKnv12ZQahSWTfHwunU47wWWTehRWPA5TZnaacjQaRTwed2Xd+XzeCVYr0riXFHsUlXYYCM/DxKRN0tlj5/P5slQn7wHPZ/eFe0sByHXw8xCJRFwi06YteWyug2XGNm3IY9vPSjQadXIVgLuX3Kfq6mrEYjHEYjE0NjaisbFxzALw4MGD+MlPfoKNGzeWpSFra2vR2NiIjRs3Ynh4eEzHFEIIIYQQ4nTR+devn+glCCFOEZKA4qTZvXs3hoaGUFdXV5acGh4exiOPPIKnn356zMdMJpNobGxEIpFALBZDJBIpGyBhh05YuQbAyTxbisvEnu3FR5EFHEt9WeljxRKFVSKRQDKZRDAYdEMoKNG4LpsspGgLBoOIxWKupx6PC8D1/YtGo06w+Xw+J9FsD0IrFW3vOwo0Kw5tj0E7JZnr5ERie/2UcrYPnx1eYq+LgpHlz0wlAkdFX29vL4aGhsrKmylOeU28LiYCbakzRR2PyfvGdCTvpZWQfr+/rFefPSav3fY1pOiz66F85h7wfVbqBgIBxGIx1NfXY/r06WMaBEJ+9KMf4Re/+EWZfI7FYmhra0MwGEQ6nS6Tg0IIIYQQQkwUnX/9emTrS9j1d0uAMf7HbyHE5EMSUJw0R44cQTqdRnt7O+rq6sqe2717N37961+fVEnjzJkzEYvFnAxiUssOqLBSxg73sGXEhEkuvqZyoAR/t5NnKWGY5GPqjKWv+XzeCTHKQtsTj2k2Cium2Wyqjee3co8Jt1Ao5I7Nf1Y+RqnIPoqUpbYXIEt6eUybzKOc43t4Pdw7m7SkzKTs5DEpyrg/fG86nUY6nS4rwwVQlkCkUOX5K6c38/UUd7Yvou3faBOGfA+lHofD2EQjJxDznmcymeNSira8m7KTn8Wqqiq0tbWNOQEIAC+++CIeeeQRHDlyxD3m8/lQU1ODxsZG7N69u6xHoBBCCCGEEBOJFwLgAwpxDx2fWwKfaQUlhDjzkAQUr4o9e/bA7/dj+vTpZVJkYGAAv/jFL/Dcc8+d1HHr6+udZOIQipF671ECWUFEqcb0GiUgS3Ht4A+bYGOSzpbhMknGx2z/PfsaJvPswAwOtkin0xgcHHRCDIArX6Uk5PE4eITpNEpHJvv4Gg4Y4TXY49l0G2UZRR73gOvgflAcMoUIwE0P5nlZJsw9ZyqPe2fLf/keXmsgECjrzWdLgW2aj2uiaLTXBqCsvBuA60dp18Y9sPeI77WfIYpIPkdBaHshUiSGQiHEYjFUV1dj2rRpZX0wR8vw8DB++ctfYsWKFWVyPBKJoLW1FT6fD/39/eoFKIQQQgghJg3hXiDS40fkiB++EnD4vRdP9JKEEK+C4Cu/RIgTs3v3brS1taG2thaxWKysl9lLL72EZcuW4cILL3Q950ZLQ0NDmczx+/1Ip9OuVx0l0kglwHa4BSWY7QNoZaF9D4/LJBqFH9NiNvnG1xI+VigUygaJ2MnAlce0YotpM5u+oyxjGrFygAiFII9NYcj1hEIhJ97y+XxZCSzllk0w8vfKJCWnI7NvH/fLilN7T7h2pjZtv0YKxMoJvSzX5b2005ZtqTCPz2vkXvMxW4ptS8Ir+zMGg0E3WIbl2JUDQbhXlJGxWAzTp09HU1PTmD7LwFEBeO+99+K+++7D0NCQe9zn87legLt370ZXV9eYjy2EEEIIIcTpouWfl0/0EoQQpxAlAcWrZvfu3UilUpg7d64TNwDQ39+Pe++9F5s2bRrzMX0+H5qbm1FVVeXEm+3dZuURsQM7KvvtUZJRLlH08fUUR1bQ2Um1XBPLWe1UXNu7z04mpvBjKpAijNcBoGxYhe3pZ+VmZYlysVh0g1ms3LKTj+06mcrjOexkZV4bk3W2pNom8bjfVory9cCxJGUgEEAymUR9fb1Lb/I8FILZbNZNEuY6mbaLRqNIJpPuHtlJw9xfliIzacm9j8fjrpyan4vKScH5fN6d236WiBWtdmhIMBhEKpU6KQHoeR5WrVqF//qv/8L69evLRG4kEkFbWxtCoRD6+/vHfGwhhBBCCCGEEGK0SAKKV01nZyc8z8PMmTOPkyQbN27Eo48+elJ9zoLBIBobGxGPx52QooyiIAJQJgiBY7KO8scOuACOlYVSpNmpuZQ+TNlRslFoMXFoy2OJlXScNMuhIBRy7AdIoQWgTNLZH56P67TDPygq7URgm5aj4OI5gGPJtkwmg3Q6XTall/vGhJ5NOXI/+ZhNMdrBKpzYTNnq9/vLpg1bIcfrAOCkpYVpQpYrZzIZl0SsXEtlArSyZJz7wnJhli5XDhCxpdx2KnAkEkF1dTVaWlrG/BkGgJ07d+IHP/gBVqxYgWw2W7avzc3NqKurQ0dHB/bs2XNSxxdCCCGEEEIIIUaDJKA4Jaxfvx51dXVoa2srK/1Np9O45557sG/fvjEf0+fzoaqqCg0NDQDKy19t/z4rrCh28vk80um06zdXKbasjKKwY1qNxwDgUmNWEFZKJ1vya9N+dqiEFYo2KWh7EDLRRmlYKQxtKs+WN/N6bLqQpb027UcJyPXx/FZk2sQhy3iZdOQ1cp8o2zjow66J4o99BnkvrAi1E48ry7B5TZUl2laYWqnIe2AFKR/nvnGvbd9Iyj6KXk6B5mTqaDSK6upqTJ8+HbFYbMyf4e7ubvzXf/0XfvGLX5SVAQNAU1MTzjvvPHieh507d4752EIIIYQQQgghxFiQBBSnhF27dsHn82HWrFlobW0tKwt+6aWXcO+995b1CxwtgUAA9fX1qKurc5NxbXKrsk+flVBMz9lkmJ0Sy/dSEtrj8DycHMuhH5RNFF9M69nSZA6/oPiyAys4bMMOx+DgD5/P5+QY10I5xSEWdrowfzj0hNIunU6783CiLdcWDoeRSCQQiUSc2OM1U6LxWm2SzkrDyh59gUAAsVgMNTU1LnnI11HicY+t1LRpSDvcxU4k5g/vfWXyzyZD7TARW+YNwPX+o8izwrZSCFsZnEgk0NraimQyOebPbiaTwSOPPIJ77rkHhw8fLnsulUrh7LPPRiKRwM6dOzURWAghhBBCCCHEaUcSUJwSisUili5diqqqKsybNw8NDQ1lk3d//vOfH5eEGi2RSMSVTVJOWUFGocTHKxNglFzsFWdFYCgUgud5ZZNwgfLhIRwykc/nnUyycsuWEzM5x95y/N3KM4q8WCzmRBsTeRR4LDfmMbgmK75s+TGTf0zCUXJRblKAUnBZOUbBxmuwIpUClOdjAtAO4mCJayQSQbFYdPeZMtHKW1vuzHVxHdwjvtbeI67Bpvqy2ayTlgCOSzDaPbBpT34mh4eHkc/n3WeFnynbd7ChoQHJZLIsRTpa9uzZg+9+97vYsmULSqWSE7aJRALt7e1oampCT08Ptm/fPuZjCyGEEEIIIYQQY0XTgcUpY9++fTh8+DBisRiampoQiUTQ3d2NgYEBrFu3Dt/+9rfx13/91044jYWamhrU1tair6/PiSFKOQBOplHIURAy6UdhZBNiTAla0WQHTdhSWL6ex2VZKgVhZeks5SJLV22ijX9mmhE4llxkMpDrsROGCa8ROFa+W5lwrBxuUjn5l/KSvfL4Wu4Vr4UpSuBYeS5wTDxyP7kWW7IdDofdvvC97BHIe8R/2t+Z2qQEtcm+cDiMXC7njsGUJMuOR1oTX8fj2D6Jdl95f5gYbGxsRCqVGutHFQAwODiIv/mbv8Hy5ctRLBZRU1ODWbNmOQE7Y8YM+P1+PPnkk2WDQoQQQgghhBBCiNOFkoDilFEqlfDLX/4ShUIBDQ0NaGhoQCKRcM/98Ic/PKmSYNLU1IS6ujo3rIGijcdn+o6CjmKMaTHbQ89O7bWTaQGUTc+1QzOsVLQ9/zj51+/3u1JblqUCcEKNa7MJPq7N5/O5c8ZisbLptzyfFX5MrPG6mPCz12bTflZUsuzVTgEGjkk+PsfUGktlKf5Yzsz9tkM6otEo4vG4m5LM67blw/ydx7SDXDgIxd4ne712mi/3h30brbDleyqHuPCecZowJS7vFxOar0YAZrNZ/MVf/AUeeOCBMuFZVVWF1tZWnH322YjH43j88cclAIUQQgghhBBCjBtKAopTSrFYRE9PD6qrq5HNZp1cKRQK2L59Oz72sY/he9/73kmVV0ajUbS2tgIABgYGygZrMPlm+7sRJu+YoKMM4++USpXTd21CjQLPTpm1gygo3fg8xReTf+l0ukyG8dx2UIWd9MtkIyUgpRaPEQwGy8QXy5gp6NgvkAlD2x/RyjmWqIZCITcpmfKTJbeUrbY02Q7x4N6HQiHEYjG3N5wQDMDdE5uS5D3iHrAM2IpDK1ntUBDKRqYAc7kcYrFYWZLSSkBOReY5rGTluROJBGpqatDY2HhSPQABYHh4GF/+8pfx05/+tEx4ZzIZ9Pb2Ytq0aUgkEli7di26u7tP6hxCCCGEEEIIIcTJoCSgOKUUCgWsWbMGnuchkUigqqqqrPzyvvvuw4YNG076+HV1dZgxYwaqqqoQj8cRj8ddCScTaDb1VyqVkMlkXFqMySzg2DAK9oCj0LP9BNkbjgKOwstO1aWwo2iqLM9lvzmKtcoptYFAwPUZLJVKyOVyxw3W4J95Hp6DciydTqO/vx+ZTKYscWjTkHbyL1/DPbHTiq0EzOVy7poAOIHGkln2COR77FReSjseq1gsuj6DtvegHbrCtVLo8vj8bNlUJUufmWZkUpO9/Zj6o4ysvD6eGzia1Kurq8OsWbNOWgAODAzgzjvvxLe//e3j+l/yfsdiMXR1dWHNmjUndQ4hhBBCCCGEEOJkkQQUp5xsNotdu3YhHo+joaEBVVVVTvD09fXhk5/8JHp7e0/6+KlUCtOmTUN1dXWZiLLYqbaUUpz4asteKQArZRv/bEtm7fOxWMwN9rDHsD+UTSw/tTKMIs8m7JgEtGXANqVIOWiTilaCVSYLbfms7QVoE4QAyuSgLX+2g1PS6bRLVFKycQ/tgBbbW5DpRx6T57DTd5kYtJOXrQS0Jc12QAlwVOIODg6WDS+h/GPPQzv5mSXA8Xjc3bdkMona2lpMmzYNM2fOPKmEKgD09/fj+9//Pv75n/8ZR44cKXsuGAyitbUVZ511Fvr6+vDoo4+qDFgIIYQQQgghxLijcmBxysnn89izZw+mT5+OWCyGRCKBvr4+ZDIZAMBTTz2Fn/70p/izP/uzkz5HfX09gsEgurq6kM1my4SRlUWlUslJQsorijTKIVvSCsCl6Cj+CCUef2c60E6ytcfhY3YAiC1P5VoBjCiFWOZrpw/bSbhM5VH8MbnH4RyUoPZ1dnIx94cSj+ez77XXZKcc22nDFH/2WiqnGnO/7CAW7odNSHJPuE7usZWzdhgLZaGd0mwFMO8vz8t+jewDmEqlUFdXh6amppP+LA4ODuKnP/0pvva1r2Hv3r1lzwWDQbS3t+OCCy5AOBzGihUrynoeCiGEEEIIIYQQ44WSgOK0MDAwgL179yIejyMWizlBBxxNCt55552vqiwYODoxmIlADnSgAASOiTX29aPcAlDWX47PMTlme/1RQFEU5vN592OPbwd72PQak3KUZpR3XANLgZna4w97+nFaL0uPKcP4OFN9LDVl6auVnpVlsjahaPsT2mEnPIYtnbWyz6YBrYCtTO7ZtKNdO+Uj12vLkrlfdi9tD0F7Tlv6bV9j5S1Tf5FIxCUj4/E46uvr0draiqamppNOABYKBTzyyCP4+te/jl27dh0ngKdNm4YFCxagtbUV27dvR1dXV9lrhBBCCCGEEEKI8eJVScA77rgDPp8PH/vYx9xjmUwGt9xyC+rr65FMJvGud70LBw4cKHtfR0cHbrjhBsTjcTQ1NeFTn/qUkxxialAsFtHR0YFDhw4hkUiUlet6noetW7fiRz/60as+T3V1Ndra2lBXV+f6wnEyrS0DZcksU2bsB2gTgXZiLtNm2WzW9RO0k3GZ6LPpuMr3216BNn0XiUSQTCbdOm05LSWdnQpsE3b2fMCxdCDlF/v1USBy+jDFE+UdpSn3xk76rSy75Zr4O6UdE21MCzKFSJnK/eZxbKkvz8e1W4Fqr8mmIJnuo4y0YpXXRonJdY40XTgajaKurg6tra2orq4+aQEIAMuXL8c//uM/YvPmzWXDaACgtrYW5557LqZNm4bNmzdj06ZN7nqEEEKIyYy+4wshhBBTk5OWgM899xz+9V//FQsWLCh7/OMf/zgefPBB3HvvvVi2bBn27duHd77zne75YrGIG264AblcDsuXL8f3v/993HXXXfjc5z538lchJiUDAwPo6upCTU0Npk+f7kpiASCdTuOBBx7AY4899qrPk0wm0d7ejpaWFiSTSTe0gsk4Jsso0yiQmBC0veQoDyuFHvsHVg6ZsKm2UCiEQqHgyp4p+WzKjmk028ePqUKbumNyjlKPoo3roiDjYxRrlJVM1FkhymuPRqOIRqMA4AZ/UHZSUjHtCKAsbUfxSSHJ/eN1VCYWWepLqWmFnT2unZAMwJXx2hSmvY82XchEJweJUABW9nvkeevq6tDW1oZkMvmqBOCLL76Iv/7rv8bq1auP+xecWCyGtrY2TJ8+HXv37sWaNWuQTqdP+lxCCCHEeKHv+EIIIcTU5aQk4ODgIG666SZ897vfRW1trXu8r68P3/ve9/DVr34VV199NRYtWoT//M//xPLly7Fy5UoAwKOPPopNmzbhv/7rv3DRRRfh+uuvx9/+7d/iW9/6VtnkVnHm43keduzYgXQ6jfPOOw+zZ892ItDzPOzatQtPPPHEKTlXNBrFzJkzMWPGDCfNmCajsKLUsolASrPKRB9Lh4PBoJOJgUDAlTYzDUhBSOHF81AM2kQaX2ulICWYFWhM6xGeGzg2ndcKTL6HYtGmCXntNkVHiTY8POzkH9dNsWiTiLxfwDEJWDlUZKShKJW9AvlaK2H5GK+H94jTnilmKweREFuKzSQif1j2G4/Hnfisq6tDe3u760l4snR2duK2227D8uXLR0w4NDc3o62tDZlMBhs2bEB/f/+rOp8QQggxHug7vhBCCDG1OSkJeMstt+CGG27AtddeW/b4mjVrkM/nyx6fN28e2tvbsWLFCgDAihUrcOGFF6K5udm95rrrrkN/fz82btx4MssRk5hSqYSXX34ZxWIR55xzDtra2txzw8PDuPfee/E///M/p+Rcfr8fra2taG5uRiwWAwBXwmtLUAGUJfTswA/b546/Vw4H4XXZIRk8RigUclODea5KEWb7DNp18FzsRZjP5530Y7qRUtFOJaZQ42PxeBw+nw/pdBq5XK6s3NkOQbFlxkwvAnCSz8o2XjvFHYWbnWxsz2H32/YXpMjLZrNl5c2VPQm5Pj5HEcpjVE5jtsKV57BlwMlkEtOnT8ecOXPK+lOeDN3d3bj11lvx8MMPH1cCDBztVTljxgzU1NRg9+7d2LFjx6s6nxBCCDFe6Du+EEIIMbUZ83Tgu+++G88//zyee+65457r6upCOBxGKpUqe7y5uRldXV3uNfbLAZ/ncyORzWbLJmoqVXNm0dXVhZdfftmlAXO5HDo7O+F5Hjo7O3Hfffdh4cKFmDVr1ik536xZsxCJRNDV1YX+/n732eE/RxrYYUs17bAQluGy1JTlp1Z4UUxRhtmedEzgsdyXKUPbf4+ii+f2PA+ZTMalEFlmzPJYmxqkRGSyLZ/Pu9+ZfuPQDZ6Two4C1E7yBY5NV7ayjNfO1J1N/1Ea2v6CvE4rEyORCMLh8HHTg60EtGXHNs1ImcghJ3yOEpEikGXAtuw5FAqhpaXluP/dORkOHz6MD37wg3jggQeOe87n86Gmpgbz5s3DnDlz0NnZ6dIRQgghxGRH3/GFEEKIqc+YIjGdnZ346Ec/ih/96Eeup9h4cPvtt6Ompsb92DSZODPYtGkTDh06hIaGBpx99tloamoCcPTL3y9/+Us8/vjjZV8CXy3Tpk3DzJkzUVdXV1YOGg6HAcBJLMoqW6paOTGXco9CD0BZ2W/l+6zUY889ijPKR5/P50qR7XtsD0L22ctms8jn864E2Q7hqPyhuOS1Vg4lsT0Ped22jJjls1wjr8kKU5uStHtCYcfr4VpsOS8AJymZ0LS9//i8Ldfm1OCRZCmTkkwKMjUZj8eRTCbR0NCAuXPnnhIB2NHRgfe///34+c9/ftxzfr8fjY2NuOCCC3DhhRfiwIEDePzxx1/1OYUQQojxQN/xhRBCiNcGY5KAa9aswcGDB3HxxRe78rtly5bhG9/4BoLBIJqbm5HL5dDb21v2vgMHDqClpQUA0NLSctwkMf6Zr6nktttuQ19fn/vp7Owcy7LFJGH58uU4cuQIUqkUpk+fjkQiAeBouuorX/kKHn/88VM6QY4DIBobG5FIJFwqjMKO5bOcGBuPx93EXCu3+B7C1zP5xnQgX8vSYNuPkEKMaTjbO69SqlF22WPZ8lauhek4mwLM5XKuDJiJQb4OKE/d8c/Asem7VrRxDwqFAoaHh5HL5cqm8tpSZl6nnfJrk4bcA/YdpNSz5+H+8BrZ5497zjJhTnzm/UskEmUTlYPBIKqqqtDU1IT29nbE4/FX/Vlat24d/vRP/xQPPvjgcc/5/X4nG+fOnYs9e/bg4YcfftXnFEIIIcYLfccXQgghXhuMSQJec801WL9+PdauXet+LrnkEtx0003u91AoVJaA2bJlCzo6OrBkyRIAwJIlS7B+/XocPHjQvWbp0qWorq7G/PnzRzxvJBJBdXV12Y848ygUCnjhhReQy+XQ3NyMadOmOYG1detW/MM//AO2bdvmJNSpIJVKYebMmWhtbUVtba3rrccpwravnp0kawd6WKFFEccfpu0oyVgWS8lGuWfLg5m8SyQSZROEKRNt6S+A4wSglWUAyo5v31cp+mzpL8Ug38djZTIZZDIZl6qjGMxms04CUmTyXLa0Oh6Pl+2p7dEHwKUbWSpc2QvQ9mfkuRKJhEsncn8oArluTl6Ox+NIpVJobW1FS0vLq+7/BwCrVq3CJz7xCTz++OPHfTZ9Ph9qa2sxd+5cnHXWWdi1axeWLl36qs8phBBCjCf6ji+EEEK8NhhTT8CqqipccMEFZY8lEgnU19e7x9///vfjE5/4BOrq6lBdXY2PfOQjWLJkCS6//HIAwFvf+lbMnz8f733ve/GVr3wFXV1d+OxnP4tbbrnFTY4VU5cDBw7gxRdfxMUXX+ympx44cAD5fB7PPPMM/vZv/xb/8R//cUpLUaLRKJqbm10ScHBw0PWgsYMyKKQotfhYpVjjD3BM0LFs1+/3o6qqygk5CrpKeUSJ6HmeG5JhE3i2JBY41qePf6agY1KP0o6vsYk/O5jDykz7fju8hNfJ5xOJhJsozNdxvaVSyUk/K/tYTsw+hJUJSe6NnW7MoSNW8vF1fL+9JjvFmXK3rq4OTU1NiMfjp0QALl++HF/84hfx1FNPlfVNJMlkEnPmzEF7ezt27tyJNWvWnNI0qxBCCDEe6Du+EEII8dpgzINBXomvfe1r8Pv9eNe73oVsNovrrrsO3/72t93zgUAADz30ED70oQ9hyZIlSCQSuPnmm/GlL33pVC9FTFL27t0Lv9+PhQsXYsaMGcjlcjh8+DDy+Tx+/vOfo66uDt/85jfLpvK+Wvx+P1KpFGKxGI4cOYJDhw4hEAggn887aWbTapRelFhMz9kSYj5GQZjL5ZzIoiijsGJJLACXQORgC1uyyxRiLBZzE375nJ28S2FoB2SwH14+ny/r8zdS3z0rA+01M+XHhKCdcMy94prtHvG1VqZWntsmGn0+H6LRqJuCzL2xqctIJOJSg1wHE382wRgMBpFMJtHY2Ija2lrX9/HVsnz5cnzuc5/D008/7cqSic/nQ1VVlRsCsnfvXrz44otlA2aEEEKIqYS+4wshhBBnPj7vVNZejhP9/f2oqamZ6GWIV4Hf78eMGTMwf/58dHV1YevWrRgeHgYAxGIx3HzzzbjzzjtPy7kpsvbu3Yvu7m6k02lXnprP55HJZNxrmUorFArIZrNOkLG/3fDwsJNgmUwG4XAYVVVVCAQCbiAIZaad+BsMBl1/PABl5bGVA0hKpZKTXaVSyZUd21LbQqHgehTaARtM0w0NDbljAyhL1bHU104OpmTjABLKzlgs5vaQfQ2ZqGTpM6UoX2f/bCcnc18zmYwTrNwbPp/P58te63meG15iewLOmDHjuN6NJ4vneXj22Wfx6U9/GitWrHD3yBIOhzF//nxccMEF6Ovrw4oVK9Dd3X1KS9mFOB309fWp3E4IMWnhd/w340YEfaGJXo4QQggxqSl4efwG/zOm7/inPAkoxGgolUrYt28fwuEwmpqaEA6HnQRMp9P4/ve/j3A4jK9//eun/NyUV7NmzcKMGTOwdetWZDIZl1gLhUIu9QYcS6fZFF1lrzu/3182VITizg7OsGWztuyVyTUKM5bN2iShnT5sp+wyMZjNZt17K3sBUliyzNgOLaE44/ORSMStjRKQQ0p4nZVlw5FIxMlK7i2TgsTzPDfd2JYO2/cyIWnXx16DTCnyutkrsKamBk1NTadE/vEerFixAp/85CexZs2aEUuAA4EA5syZg3nz5mFwcBBr1qyRABRCCCGEEEIIMemRBBQTRqFQQEdHBwqFAlKplBuuARwVgd/73vcQiURwxx13nDLJY/H7/QiHwzj//PNRKpWwe/du9Pf3OylGeWYn9RL2uUskEsjlcq402Cb7KMz4vB0KQmnHkmRbnmvLdCmhWPJbmbKjyLOlzQDKJhDzNRzKYcuY7XRiAE6AUlDavnss42XpsC1x5t7wuDy2HVZih69YsUlxynMVCgUnHe0UZSb/QqEQqqur0d7e7l53KiiVSnj88cfx53/+59i9e/eIUs/n82HOnDlYuHAhPM/D2rVrsW/fvlO2BiGEEEIIIYQQ4nQhCSgmlFwuh4MHD6K9vR1tbW3o6OhwIm1oaAj/9m//hkQigU9/+tOndFiIhSLsrLPOQjabxd69ezEwMODWZ3vsscSXcop9+NgLjkKNEpHiz5b7UghykAevl+W8LBO2/foo2eywETsshCk72y8wEokgFAohmUwil8thcHAQQ0NDZZLRDkLhPgDH0o+E18DS48rUIV/D6wPgxCJLh6PRqLtG+z6KQe43pStTkpSELP1NpVJoaGgoSxq+WkqlEp566in86Z/+KTo6OkZ8jc/nw/nnn4+LL74YALB69Wrs2rXrlK1BCCGEEEIIIYQ4nUgCiglneHgY3d3daGtrw7Rp09DZ2elEVl9fH77+9a8jEAjgox/9KKqqqk7rWiKRCM466ywMDg6ip6cHQ0NDTkxRBnqeV9YbkEMwOH2YsKSWco3ijWlH+xwTdHaYB18DHEsWRqPR41KDLJ2lLGSZMKVbIpFAIpEAcDRhaScZs0SYZbm2JyEFY+WAEK7HTu3l+Sn4+LudFmzTlDxmZcmzLZ1m6o+Tf5PJJBoaGlxfwlNFoVDAihUr8Md//McnFIChUAitra248MILEYvF8MILL2DTpk2ndB1CCCGEEEIIIcTpRBJQTAoOHTqEQqGA+vp6RCKRsuEcR44ccRPpPvzhDyOVSp329SSTSSSTSfT396O/vx+Dg4Nlfe8AOGlG+cZUoE3uAcfEGd8DoKzUlAKQoi0Wi7npwSwlZrIQQFkKj+dhOpCyLZlMun5/fF0sFkMsFnNpRiYFI5GISxPm83lXBsxjlkol16+RUNLZicVWZLIkmMex4pL7Z8uR+cP1xuNxN6m4qqoKtbW1p2WYQbFYxFNPPYWPfOQjJ0z1xeNxzJo1C+effz6qq6uxYcMGrFq16pSvRQghhBBCCCGEOJ1IAopJw5EjR5DJZBCNRhEIBDA0NOSe6+npwf/9v/8XAwMD+MxnPjNu06Grq6tRXV2N3t5eDA4OIpPJuHSgnf5LGUcRR3nGNCBlGVODpVLJiT72HrTiEEDZ0A4Arp8fj0kJ5/P5EI1G3TmYUgTgJvtyDVVVVa50mOvIZrPuNQBcOtAO+bB9BClCbR8/KzVtX0MrCXlOXjf3LRgMIhKJuH2LRqNIpVKIRqNIJBKnPPlnWbFiBf7qr/4KL7300ojPx+NxnHXWWTj//PORTCaxdu1aPPvss6dtPUIIIYQQQgghxOlCElBMKliuWl1djUAggP7+fvdcf38//umf/gmHDh3CX/zFX2DBggXjtq5UKoVUKoV8Pu/WmE6nXUrQTrNlGi8YDCKdTjsBxucpxoLBYFkZLQD3mB34wXQdJRzlGnsS2tLhUqnkhBqAsv57fr8fiUTCSTmugYlGTt/lGgGUyU2fz+ekp51UbNOGleXETAWyLyAloD0uy5fj8TgSiQSi0ehpL/sGgFWrVuGf/umf8OKLL5YNXCHhcBhtbW2YO3cuqqur8eyzz2LdunWnfV1CCCGEEEIIIcTpQBJQTDoKhQL6+vrcEIje3l73XD6fx/e+9z1s3LgRH/jAB/D+979/XNfGCbfA0VLS+vp65HI5DA0Noaenp0x6JRIJJJNJFAoFNw3YJuYonmyiLx6Pu7JcSrpQKOQeYwqPwzJsGa1NIbK3XqV8ZBqQpcYAXBqvMonIRKDt1cc+gPynnfLLxCD3yB6X123XZ3v9JZNJ1wOwch2nmnw+j2effRbf+ta3sGzZMjfUxRIIBNDU1ISzzjoLdXV1WLFihXoACiGEEEIIIYQ4o5EEFJOSYrGIgYEBxONx1NXVoaenp+z5lStXYufOnVi2bBl+8IMfTMgaA4GA67NXXV2NhoYGAHApu0KhgP7+flc2zL57TNOVSiVkMhk3HANAmVADUJYGZIKQA0Io4Zg6DAaDiMViThpW9hKkoIxEIqivr0c2m8XQ0JBLGjIRaPv3VQ4JsVOOrehjojCfz5cNSLFJQa4hEokgGo2irq7OlS7bvomnk8HBQdx333248847sWHDhrKSc0IBePbZZyOVSuGZZ57Btm3bRkwLCiGEEEIIIYQQZwqSgGLS4nkehoaGEAwG0dzcjMOHD7vhGQBw4MAB3H333di4cSO++MUv4m1ve9uErZWDQSqJx+MuBed5HgYGBtDX1+dKfqPRaNmwEJYHc8CITQkCQDQaRTAYRDKZdIM9mAi0/QAp7WxZMPv7sR9hOBx2gpHSL5vNliX4KkuQrfzjmlnOHI1GXVqQa2apbzKZRE1NjTuuLSkeLwYGBvBP//RPuPPOO9Hd3V32WSKBQAAzZ87E7NmzEY/H8dxzz2HHjh0SgEIIIYQQQgghzngkAcWkp6+vD9FoFG1tbdi3b58rYwWOlnY+//zz+MAHPoCbb74Zt9xyC9ra2k57SeloqRRdtbW1brpxqVTC4OAgBgYG3MCPfD6PQqFQVm7reR5isRgKhYJL+3GKMiWf53nI5/PI5/MubcjHKPGYOKSoSyQSLjWYy+UQDAadiGQvQa7fPmdLlIvFohOPHOoRDocRj8eRSqWQSCQAYMLvRzqdxqc+9Sn88Ic/PG7SMfH5fJg7dy7OO+88AMCLL76InTt3lpVwCyGEEEIIIYQQZyo+7wz8N9z+/v5xmw4rJg+zZs1CIpHA9u3bkclkRnxNbW0t/tf/+l/49Kc/jZaWFsRisQkXUJOJQqGATCbjpF0mk0F3dzcGBgZcn8BcLufKkJlMZILPpgg5KZhDPaLR6KT7e5nP59HT04N3v/vdePrpp0/4ukAggLlz52LevHnw+XxYv349Xn755XFcqRDjQ19fH6qrqyd6GUIIMSL8jv9m3IigLzTRyxFCCCEmNQUvj9/gf8b0HV8SUJxRnHPOOYjH4+js7ERfX58rpa0kFovhrW99Kz7zmc9gxowZqKmpGZeJs1OBQqHg0oScbmwTh+yDyInGk5XDhw/jhz/8Ie644w4cPHhwxNcEg0E0NDSgvb0dbW1t6O/vx4svvnjC1wtxpiMJKISYzEgCCiGEEKNHElC8Jmhvb0dt54TF+AAALJBJREFUbS0ymQz27t2LdDo9Yn834Gg57iWXXIK3vOUtePvb346amho0NDSgtrZ20ksscXL09vZi27Zt+NrXvob77rsP2Wz2uNf4fD7EYjHMmjULc+bMQXV1NTo6OvDUU0+p/FdMaSQBhRCTGUlAIYQQYvRIAorXDPF4HDNmzEB1dTX279+P7u7uE5YIA0fLPZubmzFnzhxcdtllWLx4MWbOnIm2tja0tLSM48rF6aJYLGLLli24++678YMf/AC7d+8e8XV+vx9VVVVoa2vDWWedBc/zcODAAaxatWqcVyzE+CMJKISYzEgCCiGEEKPnZCSgBoOIM5Lh4WFs3boVM2fOxLRp01BbW4t9+/ahv79/xBLhYrGIffv2Yd++fVi5ciUaGhrQ1taGBQsW4IILLkBdXR1aWlowa9YszJo1y5W/ijODgYEB3H333fjZz36GZcuWjZj+A4BQKITa2lpMmzYNdXV16OjowP79+1X+K4QQQgghhBBiyiPTIc5oOjo6MDQ0hJaWFsyfPx+9vb3o6upCT08PSqXSiO/J5XJOCD7//POIx+NIJpOor69Ha2srZs+ejTlz5uDSSy/FokWLEI/Hx/mqxGjxPA8PPPAAvv/972PFihXo6uo64Wvj8Tja2trQ2NiIfD6Pl19+GV1dXcjn8+O4YiGEEEIIIYQQYmKQBBRnNJ7n4fDhwxgYGEBbWxtmzpyJuro67NmzB7t27TqhCCT5fB59fX3o6+vD3r17sWHDBgSDQSSTSbS3t+Pcc8/FvHnz8MY3vhFXX331OF2VGA0bNmzAX/3VX2HdunUnLP0lkUgE7e3tmDFjBo4cOYItW7ZgeHj4FT8fQgghhBBCCCHEVEESUEwJstksdu7cib179+Lcc8/F2WefjVQqha6uLgwMDGBgYGBUxymVSsjlcujp6cGRI0ewceNGhMNh3HPPPVi0aBGuu+46vOMd79Ck4Qlk586d+Ku/+is888wz2Ldv3wmHwpB4PI7Zs2ejoaEBO3bsQGdnp9J/QgghhBBCCCFec2gwiJhy+P1+xGIxV8pbKBTQ2dmJwcFBeJ6HwcFBDA4OjikF5vP53ECJmTNn4vWvfz3e9ra34fWvfz1SqdTpuxgB4Gjic9u2bfi7v/s7/OIXv0BfX98ryr9EIoFp06ahoaEBpVIJa9asQalU0vRf8ZpGg0GEEJMZDQYRQgghRo+mAwtRQSwWw+LFixGLxZDP5zE8PIz+/n5kMhlkMhkMDw/D5/Ohp6cHAEYtiHw+H6qrq9HW1oZZs2Zh0aJF+N3f/V1ccMEFCAQC7sfn853Oy5uyFAoFFAoFZLNZLF++HJ/73OewZs2a33p/fD4fUqkUzjnnHKRSKQSDQZRKJWzZsgU7duwYx9ULMXmRBBRCTGYkAYUQQojRIwkoxAkIhUI499xz0draikAgAL/fj0KhgFwuhyNHjqBQKGBgYAA9PT3IZrMoFouvmDSrJJVKYcaMGZg7dy4uvfRSXHnllTjvvPMQjUYRj8fh9/tP09WdmXieh3w+j1wu5+5FLpdDPp/H/fffj4ceeggrV65EOp3+rcfx+/0Ih8OYPn065s+fj5kzZ6JQKGDPnj147LHHkMlkxumKhJj8SAIKISYzkoBCCCHE6JEEFOIV8Pv9mD59OmbOnIna2lokk0mEw2EMDg5ieHgYAwMDyGQy6O3txeHDh50QzOfzYyojDQQCaG5uxnnnnYc3vOENuPrqq9He3o66urrXTD/BfD6PwcFBpNNp91MqlZDP591AFg5w2bVrF15++WXs3LkThw4dGlWpNsu+GxsbMWPGDMyePRupVAoDAwPYsGED1q9fj2w2Ow5XKsSZgySgEGIyIwkohBBCjB5JQCHGQCqVcmIuFoshGo0iGo3C7/djeHgYvb29SKfTGBwcdMNFstmsS7CNdrhEIBDA9OnTcdFFF+Hiiy/GpZdeisbGRjQ0NKCxsRHJZPI0X+no8DwP/f39yGazCAQCSCQSiEajJ3x9Pp9Hf39/2f6k02kUi0Vks1kcOHAA27Ztw969e7F3714cOnQI6XQafX19bi9PhmAwiEgkglQqhblz56KlpQWpVArpdBo7d+7E5s2bcfDgwZPdBiGmNJKAQojJjCSgEEIIMXpORgJqOrB4zdLb24ve3l4AR3sHNjU1IZVKoaqqCi0tLWhra3OvLZVKGBgYQHd3t5NeR44cQTabdQm3E1EsFtHR0YGOjg488MADaGlpQXt7O9rb2zFnzhzMnDkT1dXVqK2tRVNTE6ZNm4ba2lrEYrHTev3ZbBZ79+7FwYMHcfjwYRw+fBg7duzAwMAAIpEIWlpa0NDQgHg8jnA4DL/fj1wuh3Q67STp3r17cfjwYRw6dMhNYs7n88hkMk4onor/zhAOh5FKpRCNRpFMJpFKpVBfX4+mpibE43Fs2LABu3fvVu8/IYQQQgghhBDiBCgJKEQFkUgEDQ0NSCaTaGxsRFtbGxoaGhCJRJDJZNDd3Y3+/n4cOXIEfX19OHLkCDKZjOttN5YedH6/H4lEAslkEtXV1aivr0dra6srVQ4EAq6nYCQSQXV1NVKpFOLxOOLxuEsw8p/BYBDFYhE+nw+BQAD5fN5Ju/7+fhw8eBAHDhzAkSNHcOTIEezatQt79+5FT08P+vv7MTw8jFKp5PrshcNhRKNRRCIR+Hw+5HI5N1Qlm826MulisTimacuvRCKRQCQSQTweh8/nQ1VVFdrb25FKpRCJROB5HtLpNHbv3o3+/n5s37591MlMIV7LKAkohJjMKAkohBBCjB4lAYU4BTAhBwC7d+/G5s2bEY1Gcd5556GpqQmBQADJZBLRaBT19fWuNHhoaMiVD7Ok1ufzYXBwEEeOHBlx0AgThgMDA9i/fz+Ao2LQ5/PB7/fD7/cjEAggGAwiEAggHA4jEokgFAohGAy6x4PBoHvO8zz3/qGhobJJuxSCFHj5fH7MA1BOBz6fD7FYDDU1Nairq0MikUA8Hi8ToVVVVQgGg+jt7cXq1avR29uLvr4+yT8hhBBCCCGEEGIUSAIK8Vtg6g0ADh06hFAoBL/fj2QyidbWVjQ2NqKmpsYl5QAgnU4jn8+7fnoHDx7E/v37MTQ0hOHhYZeiO9EEYibqTkbOcQ1ksgV9mVCMx+Oorq52IpN/rqqqQmNjIwKBAAC4EuTh4WFs2LABu3btckNcTmXyUAghhBBCCCGEmOpIAgoxSrLZrBtmMTAwgAMHDrjUXnNzM+bMmYPp06cjkUggHA6jqqoKgUAA9fX1aGlpcRJwaGjI/e73+1EoFDA4OOgSbYODg07eFQqFMcmuiZZ+8XgcwWDQrSORSAA4KjaDwSB8Pp8ra25paUF1dTWKxaKb0szXDQ0NoaurCzt37kRnZyc8z0OpVJoUqUUhhBBCCCGEEOJMRBJQiJOAffAopTo7O9HZ2emSeMlkEueccw7a2tpQVVWFUCiE+vp61NfXu/dR+hWLRQwPD2NwcND9ube3F4VCAX6/H8FgEKVSCcPDwy5lWCqVEAgEnET0+/2u1PdEInCkx30+H2praxEOh5HP55HNZhEKhRCLxVw5MnvyJZNJ7N+/H/39/ejv73drYC/CYDCI1tZWxGIxBINBhEIhV5YcDocRj8cRCoVQKBTcgI9QKOTSkXv27MG6devQ3d0Nz/MmXGgKIYQQQgghhBBTCUlAIU4hFFcDAwNYs2YN1qxZA+BoWWtra6sb+lFbW4tEIuEkXiKRQFNTk5OLHDISCoUQCARQKBRcCWypVEImk3GlyZSAuVwOXV1dyOfzruw2Go0im82iUCi4HoAcHgIcnYrc3NyMaDSKfD6PXC4Hz/OcyKPASyQSCIVCmD17Njo7O5FOp921sl9fJBJxQ0uCwaCbEsxBI57nIZPJYHBwEF1dXchkMjh06BB27949MTdLCCGEEEIIIYR4DSEJKMQ4UCqVsGfPHuzZs8c9xkEYqVQKqVTKibZYLOYGYwSDQZe0s8diT0Em8Hw+H/L5PGbOnIlsNgufz+d6EhYKBfdP9jdkGrFUKqGqqgqpVMpN/02n0/D5fAiHwy4NaJN99riFQgG5XM5NMAbgSps5kbivrw8DAwPo6urC0NDQuOy3EEIIIYQQQgghypEEFGKCSKfTSKfT6OrqKns8HA6jsbERsVjMlc1SvPl8Ptdzz/M8JwmZGPT5fE7g8T0Ufn6/H8CxYSeDg4MoFAoYGBhAb2+ve206nXYpRMpHJgvZnzAQCLiSZk4Y9jwPhw4dQkdHx3hsnxBCCCGEEEIIIcaAJKAQk4xcLoe9e/eO6rV+vx+BQMBJv2g0ikAg4OSgz+eD53llA0bS6TT6+/sxNDSkCbtCCCGEEEIIIcRrBElAIc5g2CMwn8+r1FYIIYQQQgghhBAnxD/RCxBCCCGEEEIIIYQQQpxeJAGFEEIIIYQQQgghhJjiSAIKIYQQQgghhBBCCDHFkQQUQgghhBBCCCGEEGKKIwkohBBCCCGEEEIIIcQURxJQCCGEEEIIIYQQQogpjiSgEEIIIYQQQgghhBBTHElAIYQQQgghhBBCCCGmOJKAQgghhBBCCCGEEEJMcSQBhRBCCCGEEEIIIYSY4kgCCiGEEEIIIYQQQggxxZEEFEIIIYQQQgghhBBiiiMJKIQQQgghhBBCCCHEFEcSUAghhBBCCCGEEEKIKY4koBBCCCGEEEIIIYQQUxxJQCGEEEIIIYQQQgghpjiSgEIIIYQQQgghhBBCTHEkAYUQQgghhBBCCCGEmOJIAgohhBBCCCGEEEIIMcWRBBRCCCGEEEIIIYQQYoojCSiEEEIIIYQQQgghxBRHElAIIYQQQgghhBBCiCmOJKAQQggxxfH79X/3QgghhBBCvNY5o/+tIBwOT/QShBBCiElLJBJBQ0MD3vnOd070UoQQQgghhBATzBktAW+66Sacc845qKmpmeilCCGEEJOG6upqnHvuufjDP/xD/OhHP8J3v/vdiV6SEEIIIYQQYoIJTvQCXg1f+cpXsHXrVjzxxBN44okn8OKLL6Knp2eilyWEEEJMCNXV1ViwYAHe9KY34e1vfzsWLlyIaDSK/v7+iV6aEEIIIYQQYoI5oyXg6tWrcfXVV+PSSy/FH/zBH+Cxxx7DihUr8OCDD6K3t3eilyeEEEKMC1VVVbj22mtx8cUX48Ybb8SsWbNQVVUFANiyZQt+8pOfTPAKhRBCCCGEEBONz/M8b6IXMVb6+/tRU1ODhQsXYvHixbj66qvxO7/zO6ipqUFXVxeefPJJPPPMM3j00UexefPmiV6uEEIIcVpob2/HDTfcgDe+8Y247LLLMG3aNMRiMWSzWWzatAn33HMPVq9ejSeeeAKe56Gvrw/V1dUTvWwhhBgRfsd/M25E0Bea6OUIIYQQk5qCl8dv8D9j+o5/RktAAAgEAmhtbUV7ezv+8A//EH/0R3+E6upqdHd3o6OjA6tXr8Z9992HX/3qVxO8aiGEEOLUcM011+DNb34z3vzmN+Pcc89FY2MjAGBwcBAPPvggfvKTn2Dnzp14+eWXkclk3PskAYUQkxlJQCGEEGL0vCYloCWVSqG+vh633HILPvjBD7o0xJEjR7Bp0yZ885vfxEMPPYRCoTABqxZCCCFOHr/fjyVLluCP//iP8aY3vQnTpk1DIpGAz+dDPp/Hj3/8Y/zt3/4t+vr60NPTg1KpdNwxJAGFEJMZSUAhhBBi9LzmJSCJRqOIRqO46qqr8Pd///c477zzUCqVkMlksGbNGnz3u9/Ffffdh6GhoXFctRBCCDF2gsEg3vCGN+DjH/84rrzySlRVVSEQCMDn82FwcBA/+9nP8PGPfxzZbBbpdPq3HksSUAgxmZEEFEIIIUaPJOAI+P1+nHPOOfjhD3+IBQsWIBgMwufzYceOHXjwwQfx2c9+FrlcDoVCAWfgVgghhJiCBINBBAIB3HbbbbjqqqtwxRVXIBAIwPM85PN5bNq0CZ/97Gfx2GOPIZfLjfr/vyQBhRCTGUlAIYQQYvRIAr4Cb3zjG/GFL3wB5513HmpqahCLxeB5Hu677z584xvfwKZNmzA4OFjWP0kIIYQYDwKBABKJBFpbW/GBD3wAV199NV73utcBAHK5HPr7+7FhwwZ8/vOfx5NPPnlS55AEFEJMZiQBhRBCiNEjCThKpk+fjg9/+MO46qqrMG/ePNTW1iKbzeKFF17APffcg4cffhj9/f04dOiQ+gcKIYQ4rYTDYbS2tuK8887Du9/9btx4442or68HAPT29mLz5s1Yv349vvOd7+D5559/VeeSBBRCTGYkAYUQQojRIwl4EnzqU5/C61//erS1teH888+H3+/H1q1bsXbtWjz44IPYvHkzNm3aJBkohBDilNLe3u7k380334wLL7wQdXV1KBaL6OjowPr167FixQrccccdp+yckoBCiMmMJKAQQggxeiQBT5JAIICLLroIv/u7v4sLLrgAV199NRoaGnDkyBFs3LgRjzzyCNatW4fVq1dj3759p+y8QgghXlvEYjFceumlmDt3Lq699losXLgQs2fPRjQaxcDAAJYvX44XX3wRq1atwoMPPohcLndKzy8JKISYzEgCCiGEEKPnZCRg8DSv6YygWCxizZo1WLNmDVpbW3HllVdi0aJF+P3f/31cccUVuPLKK7Fjxw6sXr0aq1atwsqVK/Hcc8+d8n85E0IIMTVpb2/H4sWLceWVV+KNb3wjZs+ejZqaGnieh7179+KJJ57AY489hueeew4vv/yy0udCCCGEEEKIU46SgCegpqYG559/PmpqavCRj3wE119/PUqlEvr6+rBnzx6sWLECK1euxAMPPIDu7u7TuhYhhBBnHsFgEBdddBFuvPFGvP71r8dZZ52F1tZWhMNhAMCTTz6JO+64AwMDA9i5cyf27t172tekJKAQYjKjJKAQQggxelQOfJpobW1FQ0MDzj//fHz2s5/F/Pnzkc1mMTAwgG3btmHZsmX43Oc+h3w+P25rEkIIMXn5zGc+g8svvxwXXXQR6uvrkUgk4PP50NHRge985zt4+OGH0d3djc7OznFdlySgEGIyIwkohBBCjB5JwNNMMBhEMpnE4sWLcdttt2Hx4sWIRCLI5XLo7+/Hv/zLv+BLX/rSuK9LCCHExPOWt7wF119/Pa699lrMmTMH4XAYgUAAnudh3bp1uO222/Dss89iaGgI2Wx2QtYoCSiEmMxIAgohhBCjRxJwnPD5fAgEAvD5fPjLv/xLfPjDH0ZzczMAoKOjA9/73vfwj//4jyiVSiiVSjgDt1gIIcQr4Pf70draine+8534vd/7PVxyySWIx+Pw+XwoFosYHh7GXXfdhS996Uvo6+tDsVic8P8/kAQUQkxmJAGFEEKI0SMJOIG0tbXhm9/8Jq644gpEo1Gk02ksW7YM3/rWt/Diiy9ieHh4wpIfQgghTh3RaBQtLS249dZbcd111+GCCy6A53nIZrMYHBzE+vXr8eUvfxmPPfbYRC/1OCQBhRCTGUlAIYQQYvRoOvAE0tnZiXe84x2IRqP4vd/7Pbzvfe/DxRdfjHvvvRd79uzBj370IyxduhR9fX04ePAghoeHJ3rJQgghRonf70dzczNqamrwnve8BzfddBPOPvtsZDIZ7Ny5E52dnXj88cfxta99DQMDAxO9XCGEEEIIIYQ4DiUBTyMXXXQR3va2t2HBggV43eteh9raWuzYsQM//elPsWrVKuzbtw8dHR3I5XITvVQhhBAj0NDQgJkzZ6K9vR033XQTFi9ejObmZnR2dmLTpk3YsWMHfvCDH2DNmjUTvdRRoSSgEGIyoySgEEIIMXpOJgnoH8sJvvCFL8Dn85X9zJs3zz2fyWRwyy23oL6+HslkEu9617tw4MCBsmN0dHTghhtuQDweR1NTEz71qU+hUCiMZRlnDGvXrsXf/d3f4fd///fx8Y9/HP/yL/+CNWvW4H3vex/+9V//FV/72tfw53/+57j++uvR3t4Ov39Mt0MIIcRpIBqN4oorrsAf/dEf4fOf/zy+973v4cc//jEWL16MX//61/jnf/5nfPKTn8Q73vEOfPSjHz1jBKAQQpwIfccXQgghXhuMuRz4/PPPL+tzFAweO8THP/5x/OIXv8C9996Lmpoa3HrrrXjnO9+JZ555BgBQLBZxww03oKWlBcuXL8f+/fvxf/7P/0EoFMKXv/zlU3A5k5eHHnoIDz30EPx+P97ylrdgxowZuOCCC/C+970PoVAI27Ztw8qVK7F161YsX74cBw8enOglCyHEa4qrrroK8+fPx5w5c/DGN74RZ599NvL5PB599FH8+Mc/xvbt2/HYY4+hr69vopcqhBCnHH3HF0IIIaY+YyoH/sIXvoD//u//xtq1a497rq+vD42Njfjxj3+Md7/73QCAzZs347zzzsOKFStw+eWX4+GHH8bb3vY27Nu3z03T/c53voNPf/rTOHToEMLh8KjWcaaUA78SqVQK559/PpqamvDud78b55xzDvx+P7Zv345NmzbhxRdfxLJly9DT0zPRSxVCiCnJlVdeiSuuuAJnn302lixZgpaWFnieh6effhoPPvgg9uzZg82bN2Pfvn0oFosTvdxXjcqBhRAjMdm+46scWAghhHhlxmUwyLZt29Da2opoNIolS5bg9ttvR3t7O9asWYN8Po9rr73WvXbevHlob293XxBWrFiBCy+80H05AIDrrrsOH/rQh7Bx40a87nWvG+tyzmh6e3vxzDPPwOfzYeXKlaipqUE0GsUnP/lJXHPNNbjhhhvwp3/6py598sQTT6jhvBBCvErOOuss/P7v/z7OPfdcXHLJJZgxYwYymQzWr1+Pz3zmM9izZw+6u7tx4MAB5PP5iV6uEEKMC/qOL4QQQkx9xiQBFy9ejLvuugvnnnsu9u/fjy9+8Yt4wxvegA0bNqCrqwvhcBipVKrsPc3Nzejq6gIAdHV1lX054PN87kRks1lks1n35/7+/rEse9LjeR7279+P/fv3AwBuvfVWhEIhtLa24i//8i9xySWX4PLLL8cHP/hBbNu2DStWrMD999+PTCYzwSsXQogzgzlz5uDmm2/Geeedh/POOw+zZs3CwMAANmzYgFtuuQVbt25FNptFf3//lEj8CSHEWNB3fCGEEOK1wZgk4PXXX+9+X7BgARYvXoyZM2fipz/9KWKx2ClfHLn99tvxxS9+8bQdf7LBflOHDx/Gn/zJn8Dv9+PNb34z3v3ud2PhwoW46KKLcOutt6K7uxu//vWv8cMf/hA9PT3wPA9n4LBnIYQ4pbCp/bXXXoslS5bg3HPPxUUXXYTZs2ejr68P69atw1e+8hXcf//9SKfTKBQK+t9OIcRrGn3HF0IIIV4bjLkc2JJKpXDOOefg5Zdfxlve8hbkcjn09vaW/ZfCAwcOoKWlBQDQ0tKCVatWlR2Dk8X4mpG47bbb8IlPfML9ub+/H21tba9m6WcMnKr26KOP4tFHHwUAXHbZZXjve9+LefPm4brrrsNb3/pWZDIZbNiwAffccw92796NYrGITCaDUqk0kcsXQojTTjAYRDAYRCQSwcKFC3HjjTdi3rx5mDNnDorFIvbt24cVK1bgq1/9Kv793/99opcrhBCTHn3HF0IIIaYmr0oCDg4OYvv27Xjve9+LRYsWIRQK4fHHH8e73vUuAMCWLVvQ0dGBJUuWAACWLFmCv//7v8fBgwfR1NQEAFi6dCmqq6sxf/78E54nEokgEom8mqVOKVatWoVVq1bB7/dj3rx5ePe73425c+diyZIluO6661AoFLBlyxZ8//vfx65du5DP55FOpzEwMKD+VkKIMx6fz4dkMun+v+Gyyy7D5Zdfjra2NsyYMQP5fB6HDx/GAw88gHvvvRerV6/WfxARQogxoO/4QgghxNRkTNOB//Iv/xJvf/vbMXPmTOzbtw+f//znsXbtWmzatAmNjY340Ic+hF/+8pe46667UF1djY985CMAgOXLlwMAisUiLrroIrS2tuIrX/kKurq68N73vhcf+MAH8OUvf3nUi54q04FPNQsWLMBVV12FOXPmoLGxEbNmzUI8HkdfXx/Wr1+P5cuXo6OjA8PDw+jp6UFHR4dLGgohxGQmHA6jqakJiUQCyWQSV111FebOnYu2tjaEQiF0dnZi//79OHDgAH7zm99g3bp1E73kSYmmAwshRmKyfcfXdGAhhBDilTnt04H37NmD97znPeju7kZjYyOuvPJKrFy5Eo2NjQCAr33ta/D7/XjXu96FbDaL6667Dt/+9rfd+wOBAB566CF86EMfwpIlS5BIJHDzzTfjS1/60liWIU7AunXr3L/4VlVV4dprr8U555yD6dOn4/zzz8dVV12F6upqDA4OYuvWrVi6dCmGh4exc+dObNq0yQ0mEUKIycCcOXPQ3NyMqqoqNDU14fLLL0dtbS2CwSD8fj+2b9+Ol19+GXv27MGTTz6JvXv3TvSShRDijETf8YUQQojXBmNKAk4WlAQcG+FwGAsWLMCsWbPQ0tKCxYsX44orrkB7ezsCgQA2b96M1atXY9euXeju7saKFSuwbt06pNPpiV66EOI1QiAQwHnnnYdFixahtrYWoVAIixYtQmtrKzzPQz6fx/79+7F69Wp0dHTgyJEjePHFF3HkyJGJXvoZhZKAQojJjJKAQgghxOg5mSSgJOBrkLa2NsyZMwetra249dZbcfnll7vnBgYGsHXrVuzevRv9/f144YUXcP/996Ozs3MCVyyEmGr4fD5cc801WLhwIc4++2zU1NRg1qxZmD17NgDg4MGD2LFjBx599FHs2LEDw8PDOHToEDo7OzE8PDzBqz9zkQQUQkxmJAGFEEKI0SMJKMaEz+fD3Llz0d7ejttvvx2XXHJJ2fOlUglHjhzBnj17MDQ0hMOHD2Pp0qX4+c9/rtJhIcSYufLKK3H11Vfj6quvRjgcRnNzM2pra5FMJjEwMICNGzdi5cqVePjhh3Ho0CEMDAygq6sL2Wx2opc+ZZAEFEJMZiQBhRBCiNEjCShOCp/Ph9mzZ+NjH/sY3ve+9yGZTI74umKxiKGhIfT396NQKGDDhg34j//4DzzyyCNK5gghjqOpqQkf/OAH8aY3vQlnnXUW4vE44vF42f/G7NmzB1//+tdxzz33IJvNIp1OY2hoSNN8TxOSgEKIyYwkoBBCCDF6XjMSsK+vD6lUaqKXMeUIBoO4+uqr8dWvfhVtbW2v+PpSqYR8Po98Pg8AeO655/DDH/4QP//5z0/3UoUQk5C6ujp88IMfxNvf/na0t7fD7/cjFAohGAzC5/OVvfbQoUP43//7f2Pjxo3I5XIoFosTtOrXFr29vfqPaEKISQu/41+J30UQkoBCCCHEb6OAPJ7GL8f0Hf+MlIB79uwZlaQSQgghxDE6OzsxY8aMiV6GEEKMyI4dO3D22WdP9DKEEEKIM4qxfMc/IyVgqVTCli1bMH/+fHR2dqq06TTS39+PtrY27fNpRvs8Pmifxwft8/gx2r32PA8DAwNobW2F3+8fxxUKIcTo6e3tRW1tLTo6OpRaPs3o/6vHB+3z+KB9Hh+0z+PH6fyOHzxVixxP/H4/pk+fDgCorq7WB3Ac0D6PD9rn8UH7PD5on8eP0ey1/oVaCDHZ4b/A1NTU6P8/xgn9f/X4oH0eH7TP44P2efw4Hd/xFQcQQgghhBBCCCGEEGKKIwkohBBCCCGEEEIIIcQU54yVgJFIBJ///OcRiUQmeilTGu3z+KB9Hh+0z+OD9nn80F4LIaYS+t+08UN7PT5on8cH7fP4oH0eP07nXp+Rg0GEEEIIIYQQQgghhBCj54xNAgohhBBCCCGEEEIIIUaHJKAQQgghhBBCCCGEEFMcSUAhhBBCCCGEEEIIIaY4koBCCCGEEEIIIYQQQkxxzkgJ+K1vfQuzZs1CNBrF4sWLsWrVqole0hnFk08+ibe//e1obW2Fz+fDf//3f5c973kePve5z2HatGmIxWK49tprsW3btrLX9PT04KabbkJ1dTVSqRTe//73Y3BwcByvYvJz++2349JLL0VVVRWamprwjne8A1u2bCl7TSaTwS233IL6+nokk0m8613vwoEDB8pe09HRgRtuuAHxeBxNTU341Kc+hUKhMJ6XMqm58847sWDBAlRXV6O6uhpLlizBww8/7J7XHp8e7rjjDvh8PnzsYx9zj2mvTw1f+MIX4PP5yn7mzZvnntc+CyGmKvqO/+rQd/zxQd/xxwd9x58Y9B3/9DFZvuOfcRLwnnvuwSc+8Ql8/vOfx/PPP4+FCxfiuuuuw8GDByd6aWcMQ0NDWLhwIb71rW+N+PxXvvIVfOMb38B3vvMdPPvss0gkErjuuuuQyWTca2666SZs3LgRS5cuxUMPPYQnn3wSf/ZnfzZel3BGsGzZMtxyyy1YuXIlli5dinw+j7e+9a0YGhpyr/n4xz+OBx98EPfeey+WLVuGffv24Z3vfKd7vlgs4oYbbkAul8Py5cvx/e9/H3fddRc+97nPTcQlTUpmzJiBO+64A2vWrMHq1atx9dVX48Ybb8TGjRsBaI9PB8899xz+9V//FQsWLCh7XHt96jj//POxf/9+9/P000+757TPQoipiL7jv3r0HX980Hf88UHf8ccffcc//UyK7/jeGcZll13m3XLLLe7PxWLRa21t9W6//fYJXNWZCwDv/vvvd38ulUpeS0uL9w//8A/usd7eXi8SiXg/+clPPM/zvE2bNnkAvOeee8695uGHH/Z8Pp+3d+/ecVv7mcbBgwc9AN6yZcs8zzu6r6FQyLv33nvda1566SUPgLdixQrP8zzvl7/8pef3+72uri73mjvvvNOrrq72stns+F7AGURtba337//+79rj08DAwIA3d+5cb+nSpd6b3vQm76Mf/ajnefo8n0o+//nPewsXLhzxOe2zEGKqou/4pxZ9xx8/9B1//NB3/NOHvuOffibLd/wzKgmYy+WwZs0aXHvtte4xv9+Pa6+9FitWrJjAlU0ddu7cia6urrI9rqmpweLFi90er1ixAqlUCpdccol7zbXXXgu/349nn3123Nd8ptDX1wcAqKurAwCsWbMG+Xy+bK/nzZuH9vb2sr2+8MIL0dzc7F5z3XXXob+/3/1XMHGMYrGIu+++G0NDQ1iyZIn2+DRwyy234IYbbijbU0Cf51PNtm3b0NrairPOOgs33XQTOjo6AGifhRBTE33HP/3oO/7pQ9/xTz/6jn/60Xf88WEyfMcPnqJrGRcOHz6MYrFYdtEA0NzcjM2bN0/QqqYWXV1dADDiHvO5rq4uNDU1lT0fDAZRV1fnXiPKKZVK+NjHPoYrrrgCF1xwAYCj+xgOh5FKpcpeW7nXI90LPieOsn79eixZsgSZTAbJZBL3338/5s+fj7Vr12qPTyF33303nn/+eTz33HPHPafP86lj8eLFuOuuu3Duuedi//79+OIXv4g3vOEN2LBhg/ZZCDEl0Xf804++458e9B3/9KLv+OODvuOPD5PlO/4ZJQGFOFO55ZZbsGHDhrKaf3HqOPfcc7F27Vr09fXhZz/7GW6++WYsW7Zsopc1pejs7MRHP/pRLF26FNFodKKXM6W5/vrr3e8LFizA4sWLMXPmTPz0pz9FLBabwJUJIYQQwqLv+KcXfcc//eg7/vgxWb7jn1HlwA0NDQgEAsdNSDlw4ABaWlomaFVTC+7jb9vjlpaW45o0FwoF9PT06D6MwK233oqHHnoIv/71rzFjxgz3eEtLC3K5HHp7e8teX7nXI90LPieOEg6HMWfOHCxatAi33347Fi5ciK9//eva41PImjVrcPDgQVx88cUIBoMIBoNYtmwZvvGNbyAYDKK5uVl7fZpIpVI455xz8PLLL+szLYSYkug7/ulH3/FPPfqOf/rRd/zTj77jTxwT9R3/jJKA4XAYixYtwuOPP+4eK5VKePzxx7FkyZIJXNnUYfbs2WhpaSnb4/7+fjz77LNuj5csWYLe3l6sWbPGveaJJ55AqVTC4sWLx33NkxXP83Drrbfi/vvvxxNPPIHZs2eXPb9o0SKEQqGyvd6yZQs6OjrK9nr9+vVlX8iWLl2K6upqzJ8/f3wu5AykVCohm81qj08h11xzDdavX4+1a9e6n0suuQQ33XST+117fXoYHBzE9u3bMW3aNH2mhRBTEn3HP/3oO/6pQ9/xJw59xz/16Dv+xDFh3/FPZqrJRHL33Xd7kUjEu+uuu7xNmzZ5f/Znf+alUqmyCSnitzMwMOC98MIL3gsvvOAB8L761a96L7zwgrd7927P8zzvjjvu8FKplPc///M/3rp167wbb7zRmz17tpdOp90xfud3fsd73ete5z377LPe008/7c2dO9d7z3veM1GXNCn50Ic+5NXU1Hi/+c1vvP3797uf4eFh95oPfvCDXnt7u/fEE094q1ev9pYsWeItWbLEPV8oFLwLLrjAe+tb3+qtXbvW+9WvfuU1NjZ6t91220Rc0qTkM5/5jLds2TJv586d3rp167zPfOYzns/n8x599FHP87THpxM7OczztNenik9+8pPeb37zG2/nzp3eM88841177bVeQ0ODd/DgQc/ztM9CiKmJvuO/evQdf3zQd/zxQd/xJw59xz89TJbv+GecBPQ8z/vmN7/ptbe3e+Fw2Lvsssu8lStXTvSSzih+/etfewCO+7n55ps9z/O8Uqnk/c3f/I3X3NzsRSIR75prrvG2bNlSdozu7m7vPe95j5dMJr3q6mrvfe97nzcwMDABVzN5GWmPAXj/+Z//6V6TTqe9D3/4w15tba0Xj8e93/u93/P2799fdpxdu3Z5119/vReLxbyGhgbvk5/8pJfP58f5aiYvf/Inf+LNnDnTC4fDXmNjo3fNNde4Lweepz0+nVR+QdBenxr+4A/+wJs2bZoXDoe96dOne3/wB3/gvfzyy+557bMQYqqi7/ivDn3HHx/0HX980Hf8iUPf8U8Pk+U7vs/zPG/MuUUhhBBCCCGEEEIIIcQZwxnVE1AIIYQQQgghhBBCCDF2JAGFEEIIIYQQQgghhJjiSAIKIYQQQgghhBBCCDHFkQQUQgghhBBCCCGEEGKKIwkohBBCCCGEEEIIIcQURxJQCCGEEEIIIYQQQogpjiSgEEIIIYQQQgghhBBTHElAIYQQQgghhBBCCCGmOJKAQgghhBBCCCGEEEJMcSQBhRBCCCGEEEIIIYSY4kgCCiGEEEIIIYQQQggxxZEEFEIIIYQQQgghhBBiivP/AEN2cPEY+nDnAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["slice_map = {\n", "    \"brain_272_0000.nii.gz\": 8,\n", "}\n", "case_num = 0\n", "# print(val_ds[case_num])\n", "img_name = os.path.split(val_ds[case_num]['image'].meta[\"filename_or_obj\"])[1]\n", "img = val_ds[case_num][\"image\"]\n", "label = val_ds[case_num][\"label\"]\n", "img_shape = img.shape\n", "label_shape = label.shape\n", "print(f\"image shape: {img_shape}, label shape: {label_shape}\")\n", "plt.figure(\"image\", (18, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"image\")\n", "plt.imshow(img[0, :, :, slice_map[img_name]].detach().cpu(), cmap=\"gray\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"label\")\n", "plt.imshow(label[0, :, :, slice_map[img_name]].detach().cpu())\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "model = UNETR(\n", "    in_channels=1,\n", "    out_channels=5,\n", "    img_size=(96, 96, 16),\n", "    feature_size=16,\n", "    hidden_size=768,\n", "    mlp_dim=3072,\n", "    num_heads=12,\n", "    pos_embed=\"perceptron\",\n", "    norm_name=\"instance\",\n", "    res_block=True,\n", "    dropout_rate=0.0,\n", ").to(device)\n", "\n", "root_dir = \"./run\"\n", "loss_function = DiceCELoss(to_onehot_y=True, softmax=True)\n", "torch.backends.cudnn.benchmark = True\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Training (171 / 30000 Steps) (loss=1.99244): 100%|██████████| 172/172 [02:48<00:00,  1.02it/s]\n", "Training (343 / 30000 Steps) (loss=1.83325): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Validate (344 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.25it/s]26<00:14,  1.08it/s]\n", "Training (500 / 30000 Steps) (loss=1.60646):  91%|█████████▏| 157/172 [02:59<02:44, 10.96s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.2055712193250656 Current Avg. Dice: 0.2055712193250656\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (515 / 30000 Steps) (loss=1.61719): 100%|██████████| 172/172 [03:14<00:00,  1.13s/it]\n", "Training (687 / 30000 Steps) (loss=1.49054): 100%|██████████| 172/172 [02:41<00:00,  1.07it/s]\n", "Training (859 / 30000 Steps) (loss=1.37557): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Validate (860 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]:11<00:29,  1.07it/s]\n", "Training (1000 / 30000 Steps) (loss=1.29215):  82%|████████▏ | 141/172 [02:44<05:30, 10.66s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.25200051069259644 Current Avg. Dice: 0.25200051069259644\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (1031 / 30000 Steps) (loss=1.25031): 100%|██████████| 172/172 [03:13<00:00,  1.12s/it]\n", "Training (1203 / 30000 Steps) (loss=1.15516): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Training (1375 / 30000 Steps) (loss=1.10787): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Validate (1376 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.27it/s]57<00:44,  1.08it/s]\n", "Training (1500 / 30000 Steps) (loss=1.03779):  73%|███████▎  | 125/172 [02:30<08:17, 10.58s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.26731759309768677 Current Avg. Dice: 0.26731759309768677\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (1547 / 30000 Steps) (loss=1.04179): 100%|██████████| 172/172 [03:14<00:00,  1.13s/it]\n", "Training (1719 / 30000 Steps) (loss=1.12873): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Training (1891 / 30000 Steps) (loss=0.97467): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Validate (1892 / 10 Steps): 100%|██████████| 40/40 [00:32<00:00,  1.24it/s]42<01:02,  1.03it/s]\n", "Training (2000 / 30000 Steps) (loss=0.93539):  63%|██████▎   | 109/172 [02:15<11:22, 10.84s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.2821037173271179 Current Avg. Dice: 0.2821037173271179\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (2063 / 30000 Steps) (loss=0.93124): 100%|██████████| 172/172 [03:15<00:00,  1.14s/it]\n", "Training (2235 / 30000 Steps) (loss=0.91781): 100%|██████████| 172/172 [02:42<00:00,  1.06it/s]\n", "Training (2407 / 30000 Steps) (loss=0.89687): 100%|██████████| 172/172 [02:39<00:00,  1.08it/s]\n", "Validate (2408 / 10 Steps): 100%|██████████| 40/40 [00:35<00:00,  1.14it/s]6<01:14,  1.07it/s]\n", "Training (2500 / 30000 Steps) (loss=0.89640):  54%|█████▍    | 93/172 [02:02<15:17, 11.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.298379123210907 Current Avg. Dice: 0.298379123210907\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (2579 / 30000 Steps) (loss=0.88324): 100%|██████████| 172/172 [03:20<00:00,  1.16s/it]\n", "Training (2751 / 30000 Steps) (loss=0.90908): 100%|██████████| 172/172 [02:53<00:00,  1.01s/it]\n", "Training (2923 / 30000 Steps) (loss=0.91350): 100%|██████████| 172/172 [02:41<00:00,  1.06it/s]\n", "Validate (2924 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.27it/s]2<01:32,  1.04it/s]\n", "Training (3000 / 30000 Steps) (loss=0.85086):  45%|████▍     | 77/172 [01:44<16:36, 10.49s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.40583372116088867 Current Avg. Dice: 0.40583372116088867\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (3095 / 30000 Steps) (loss=0.86618): 100%|██████████| 172/172 [03:17<00:00,  1.15s/it]\n", "Training (3267 / 30000 Steps) (loss=0.87076): 100%|██████████| 172/172 [02:40<00:00,  1.07it/s]\n", "Training (3439 / 30000 Steps) (loss=0.82346): 100%|██████████| 172/172 [02:39<00:00,  1.08it/s]\n", "Validate (3440 / 10 Steps): 100%|██████████| 40/40 [00:32<00:00,  1.24it/s]5<01:43,  1.08it/s]\n", "Training (3500 / 30000 Steps) (loss=0.95279):  35%|███▌      | 61/172 [01:28<19:49, 10.72s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.4296500086784363 Current Avg. Dice: 0.4296500086784363\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (3611 / 30000 Steps) (loss=0.82688): 100%|██████████| 172/172 [03:07<00:00,  1.09s/it]\n", "Training (3783 / 30000 Steps) (loss=0.78269): 100%|██████████| 172/172 [02:35<00:00,  1.10it/s]\n", "Training (3955 / 30000 Steps) (loss=0.78940): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (3956 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]9<01:53,  1.13it/s]\n", "Training (4000 / 30000 Steps) (loss=0.82641):  26%|██▌       | 45/172 [01:12<22:21, 10.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.43802985548973083 Current Avg. Dice: 0.43802985548973083\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (4127 / 30000 Steps) (loss=0.80608): 100%|██████████| 172/172 [03:07<00:00,  1.09s/it]\n", "Training (4299 / 30000 Steps) (loss=0.77139): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (4471 / 30000 Steps) (loss=0.85074): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (4472 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]6<02:10,  1.11it/s]\n", "Training (4500 / 30000 Steps) (loss=0.80460):  17%|█▋        | 29/172 [00:58<25:11, 10.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.4408009648323059 Current Avg. Dice: 0.4408009648323059\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (4643 / 30000 Steps) (loss=0.78773): 100%|██████████| 172/172 [03:06<00:00,  1.09s/it]\n", "Training (4815 / 30000 Steps) (loss=0.83285): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Training (4987 / 30000 Steps) (loss=0.73467): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (4988 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]1<02:24,  1.11it/s]\n", "Training (5000 / 30000 Steps) (loss=0.73300):   8%|▊         | 13/172 [00:43<28:15, 10.66s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.45060476660728455 Current Avg. Dice: 0.45060476660728455\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (5159 / 30000 Steps) (loss=0.81384): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (5331 / 30000 Steps) (loss=0.71791): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (5332 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]31<00:03,  1.12it/s]\n", "Training (5500 / 30000 Steps) (loss=0.78616):  98%|█████████▊| 169/172 [03:03<00:31, 10.50s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.49523982405662537 Current Avg. Dice: 0.49523982405662537\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (5503 / 30000 Steps) (loss=0.71762): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (5675 / 30000 Steps) (loss=0.78560): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Training (5847 / 30000 Steps) (loss=0.71628): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (5848 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]16<00:17,  1.13it/s]\n", "Training (6000 / 30000 Steps) (loss=0.67707):  89%|████████▉ | 153/172 [02:48<03:20, 10.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.5020974278450012 Current Avg. Dice: 0.5020974278450012\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (6019 / 30000 Steps) (loss=0.72719): 100%|██████████| 172/172 [03:05<00:00,  1.08s/it]\n", "Training (6191 / 30000 Steps) (loss=0.64607): 100%|██████████| 172/172 [02:35<00:00,  1.11it/s]\n", "Training (6363 / 30000 Steps) (loss=0.76783): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (6364 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]03<00:32,  1.11it/s]\n", "Training (6500 / 30000 Steps) (loss=0.72301):  80%|███████▉  | 137/172 [02:35<06:05, 10.45s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.5020974278450012 Current Avg. Dice: 0.5000311136245728\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (6535 / 30000 Steps) (loss=0.67929): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (6707 / 30000 Steps) (loss=1.00242): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (6879 / 30000 Steps) (loss=0.66663): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (6880 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]48<00:45,  1.14it/s]\n", "Training (7000 / 30000 Steps) (loss=0.75663):  70%|███████   | 121/172 [02:20<08:58, 10.55s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.5644749402999878 Current Avg. Dice: 0.5644749402999878\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (7051 / 30000 Steps) (loss=0.71705): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (7223 / 30000 Steps) (loss=0.71879): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Training (7395 / 30000 Steps) (loss=0.64361): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (7396 / 10 Steps): 100%|██████████| 40/40 [00:32<00:00,  1.24it/s]34<01:01,  1.10it/s]\n", "Training (7500 / 30000 Steps) (loss=0.68134):  61%|██████    | 105/172 [02:07<11:56, 10.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.5809929370880127 Current Avg. Dice: 0.5809929370880127\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (7567 / 30000 Steps) (loss=0.66275): 100%|██████████| 172/172 [03:08<00:00,  1.10s/it]\n", "Training (7739 / 30000 Steps) (loss=0.62971): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (7911 / 30000 Steps) (loss=0.71225): 100%|██████████| 172/172 [02:35<00:00,  1.11it/s]\n", "Validate (7912 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]9<01:12,  1.17it/s]\n", "Training (8000 / 30000 Steps) (loss=0.77485):  52%|█████▏    | 89/172 [01:51<14:33, 10.53s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.5842254757881165 Current Avg. Dice: 0.5842254757881165\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (8083 / 30000 Steps) (loss=0.67781): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (8255 / 30000 Steps) (loss=0.78311): 100%|██████████| 172/172 [02:35<00:00,  1.11it/s]\n", "Training (8427 / 30000 Steps) (loss=0.73114): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (8428 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]5<01:27,  1.15it/s]\n", "Training (8500 / 30000 Steps) (loss=0.69081):  42%|████▏     | 73/172 [01:37<17:21, 10.52s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.5932625532150269 Current Avg. Dice: 0.5932625532150269\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (8599 / 30000 Steps) (loss=0.62083): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (8771 / 30000 Steps) (loss=0.72137): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Training (8943 / 30000 Steps) (loss=0.64474): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (8944 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]1<01:42,  1.13it/s]\n", "Training (9000 / 30000 Steps) (loss=0.57412):  33%|███▎      | 57/172 [01:22<19:55, 10.40s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.5932625532150269 Current Avg. Dice: 0.5895284414291382\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (9115 / 30000 Steps) (loss=0.64590): 100%|██████████| 172/172 [03:05<00:00,  1.08s/it]\n", "Training (9287 / 30000 Steps) (loss=0.61281): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Training (9459 / 30000 Steps) (loss=0.53793): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (9460 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]6<01:58,  1.12it/s]\n", "Training (9500 / 30000 Steps) (loss=0.72689):  24%|██▍       | 41/172 [01:08<23:00, 10.54s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.5967512726783752 Current Avg. Dice: 0.5967512726783752\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (9631 / 30000 Steps) (loss=0.90124): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (9803 / 30000 Steps) (loss=0.64973): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Training (9975 / 30000 Steps) (loss=0.70721): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (9976 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]22<02:14,  1.10it/s]\n", "Training (10000 / 30000 Steps) (loss=0.72732):  15%|█▍        | 25/172 [00:54<25:52, 10.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.6037954688072205 Current Avg. Dice: 0.6037954688072205\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (10147 / 30000 Steps) (loss=0.69356): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (10319 / 30000 Steps) (loss=0.61455): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (10491 / 30000 Steps) (loss=0.69515): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (10492 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]<02:26,  1.12it/s]\n", "Training (10500 / 30000 Steps) (loss=0.56925):   5%|▌         | 9/172 [00:40<29:47, 10.96s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.6070736050605774 Current Avg. Dice: 0.6070736050605774\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (10663 / 30000 Steps) (loss=0.72369): 100%|██████████| 172/172 [03:07<00:00,  1.09s/it]\n", "Training (10835 / 30000 Steps) (loss=0.79407): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (10836 / 10 Steps): 100%|██████████| 40/40 [00:32<00:00,  1.25it/s]27<00:07,  1.11it/s]\n", "Training (11000 / 30000 Steps) (loss=0.62757):  96%|█████████▌| 165/172 [03:00<01:14, 10.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.6071916818618774 Current Avg. Dice: 0.6071916818618774\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (11007 / 30000 Steps) (loss=0.65035): 100%|██████████| 172/172 [03:06<00:00,  1.09s/it]\n", "Training (11179 / 30000 Steps) (loss=0.56871): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (11351 / 30000 Steps) (loss=0.65840): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (11352 / 10 Steps): 100%|██████████| 40/40 [00:32<00:00,  1.25it/s]13<00:21,  1.12it/s]\n", "Training (11500 / 30000 Steps) (loss=0.66526):  87%|████████▋ | 149/172 [02:45<04:02, 10.52s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6071916818618774 Current Avg. Dice: 0.6032554507255554\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (11523 / 30000 Steps) (loss=0.55857): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (11695 / 30000 Steps) (loss=0.62933): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (11867 / 30000 Steps) (loss=0.69360): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (11868 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]59<00:35,  1.14it/s]\n", "Training (12000 / 30000 Steps) (loss=0.65182):  77%|███████▋  | 133/172 [02:31<06:47, 10.44s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6071916818618774 Current Avg. Dice: 0.5974065661430359\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (12039 / 30000 Steps) (loss=0.89756): 100%|██████████| 172/172 [03:06<00:00,  1.09s/it]\n", "Training (12211 / 30000 Steps) (loss=0.72245): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (12383 / 30000 Steps) (loss=0.64037): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (12384 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.27it/s]43<00:50,  1.11it/s]\n", "Training (12500 / 30000 Steps) (loss=0.75756):  68%|██████▊   | 117/172 [02:15<09:27, 10.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6071916818618774 Current Avg. Dice: 0.5978001356124878\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (12555 / 30000 Steps) (loss=0.69223): 100%|██████████| 172/172 [03:04<00:00,  1.07s/it]\n", "Training (12727 / 30000 Steps) (loss=0.66124): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Training (12899 / 30000 Steps) (loss=0.70323): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (12900 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.28it/s]30<01:03,  1.13it/s]\n", "Training (13000 / 30000 Steps) (loss=0.76717):  59%|█████▊    | 101/172 [02:01<12:17, 10.39s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.6138178110122681 Current Avg. Dice: 0.6138178110122681\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (13071 / 30000 Steps) (loss=0.59970): 100%|██████████| 172/172 [03:04<00:00,  1.07s/it]\n", "Training (13243 / 30000 Steps) (loss=0.65051): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Training (13415 / 30000 Steps) (loss=0.68035): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (13416 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.25it/s]6<01:20,  1.10it/s]\n", "Training (13500 / 30000 Steps) (loss=0.62892):  49%|████▉     | 85/172 [01:48<15:11, 10.48s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6138178110122681 Current Avg. Dice: 0.6086055040359497\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (13587 / 30000 Steps) (loss=0.60916): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (13759 / 30000 Steps) (loss=0.67036): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (13931 / 30000 Steps) (loss=0.70648): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (13932 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]1<01:32,  1.12it/s]\n", "Training (14000 / 30000 Steps) (loss=0.67055):  40%|████      | 69/172 [01:33<17:56, 10.45s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6138178110122681 Current Avg. Dice: 0.6076064109802246\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (14103 / 30000 Steps) (loss=0.60756): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (14275 / 30000 Steps) (loss=0.65702): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (14447 / 30000 Steps) (loss=0.60529): 100%|██████████| 172/172 [02:34<00:00,  1.12it/s]\n", "Validate (14448 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.25it/s]7<01:47,  1.12it/s]\n", "Training (14500 / 30000 Steps) (loss=0.62222):  31%|███       | 53/172 [01:19<20:45, 10.46s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6138178110122681 Current Avg. Dice: 0.603232741355896\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (14619 / 30000 Steps) (loss=0.67003): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (14791 / 30000 Steps) (loss=0.64051): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Training (14963 / 30000 Steps) (loss=0.58687): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (14964 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.27it/s]3<02:02,  1.11it/s]\n", "Training (15000 / 30000 Steps) (loss=0.61560):  22%|██▏       | 37/172 [01:04<23:14, 10.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6138178110122681 Current Avg. Dice: 0.6121338605880737\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (15135 / 30000 Steps) (loss=0.70193): 100%|██████████| 172/172 [03:04<00:00,  1.07s/it]\n", "Training (15307 / 30000 Steps) (loss=0.57296): 100%|██████████| 172/172 [02:32<00:00,  1.13it/s]\n", "Training (15479 / 30000 Steps) (loss=1.81717): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Validate (15480 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.27it/s]8<02:16,  1.11it/s]\n", "Training (15500 / 30000 Steps) (loss=0.62000):  12%|█▏        | 21/172 [00:50<26:18, 10.46s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.6147109270095825 Current Avg. Dice: 0.6147109270095825\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (15651 / 30000 Steps) (loss=0.56724): 100%|██████████| 172/172 [03:04<00:00,  1.08s/it]\n", "Training (15823 / 30000 Steps) (loss=0.58166): 100%|██████████| 172/172 [02:33<00:00,  1.12it/s]\n", "Training (15995 / 30000 Steps) (loss=0.60718): 100%|██████████| 172/172 [02:32<00:00,  1.12it/s]\n", "Validate (15996 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.28it/s]<02:21,  1.19it/s]\n", "Training (16000 / 30000 Steps) (loss=0.76267):   3%|▎         | 5/172 [00:36<34:18, 12.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Saved ! Current Best Avg. Dice: 0.6177313923835754 Current Avg. Dice: 0.6177313923835754\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (16167 / 30000 Steps) (loss=0.72922): 100%|██████████| 172/172 [03:04<00:00,  1.07s/it]\n", "Training (16339 / 30000 Steps) (loss=1.67705): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (16340 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]25<00:10,  1.10it/s]\n", "Training (16500 / 30000 Steps) (loss=0.82198):  94%|█████████▎| 161/172 [02:56<01:54, 10.44s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6177313923835754 Current Avg. Dice: 0.6114506721496582\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (16511 / 30000 Steps) (loss=0.49448): 100%|██████████| 172/172 [03:06<00:00,  1.09s/it]\n", "Training (16683 / 30000 Steps) (loss=0.69398): 100%|██████████| 172/172 [02:35<00:00,  1.11it/s]\n", "Training (16855 / 30000 Steps) (loss=0.55408): 100%|██████████| 172/172 [02:35<00:00,  1.11it/s]\n", "Validate (16856 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]10<00:24,  1.12it/s]\n", "Training (17000 / 30000 Steps) (loss=0.55220):  84%|████████▍ | 145/172 [02:42<04:42, 10.45s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6177313923835754 Current Avg. Dice: 0.6113210916519165\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (17027 / 30000 Steps) (loss=0.70644): 100%|██████████| 172/172 [03:06<00:00,  1.09s/it]\n", "Training (17199 / 30000 Steps) (loss=0.64543): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Training (17371 / 30000 Steps) (loss=0.64768): 100%|██████████| 172/172 [02:34<00:00,  1.11it/s]\n", "Validate (17372 / 10 Steps): 100%|██████████| 40/40 [00:31<00:00,  1.26it/s]56<00:39,  1.10it/s]\n", "Training (17500 / 30000 Steps) (loss=0.65830):  75%|███████▌  | 129/172 [02:28<07:28, 10.43s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Model Was Not Saved ! Current Best Avg. Dice: 0.6177313923835754 Current Avg. Dice: 0.6089877486228943\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training (17543 / 30000 Steps) (loss=0.69295): 100%|██████████| 172/172 [03:06<00:00,  1.08s/it]\n", "Training (17624 / 30000 Steps) (loss=0.62517):  47%|████▋     | 81/172 [01:13<01:30,  1.00it/s]"]}], "source": ["def validation(epoch_iterator_val):\n", "    model.eval()\n", "    with torch.no_grad():\n", "        for batch in epoch_iterator_val:\n", "            val_inputs, val_labels = (batch[\"image\"].cuda(device_ids[0]), batch[\"label\"].cuda(device_ids[0]))\n", "            val_outputs = sliding_window_inference(val_inputs, (96, 96, 16), 8, model)\n", "            val_labels_list = decollate_batch(val_labels)\n", "            val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]\n", "            val_outputs_list = decollate_batch(val_outputs)\n", "            val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]\n", "            dice_metric(y_pred=val_output_convert, y=val_labels_convert)\n", "            epoch_iterator_val.set_description(\"Validate (%d / %d Steps)\" % (global_step, 10.0))\n", "        mean_dice_val = dice_metric.aggregate().item()\n", "        dice_metric.reset()\n", "    return mean_dice_val\n", "\n", "\n", "def train(global_step, train_loader, dice_val_best, global_step_best):\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    epoch_iterator = tqdm(train_loader, desc=\"Training (X / X Steps) (loss=X.X)\", dynamic_ncols=True)\n", "    for step, batch in enumerate(epoch_iterator):\n", "        step += 1\n", "        x, y = (batch[\"image\"].cuda(device_ids[0]), batch[\"label\"].cuda(device_ids[0]))\n", "        logit_map = model(x)\n", "        loss = loss_function(logit_map, y)\n", "        loss.backward()\n", "        epoch_loss += loss.item()\n", "        optimizer.step()\n", "        optimizer.zero_grad()\n", "        epoch_iterator.set_description(\"Training (%d / %d Steps) (loss=%2.5f)\" % (global_step, max_iterations, loss))\n", "        if (global_step % eval_num == 0 and global_step != 0) or global_step == max_iterations:\n", "            epoch_iterator_val = tqdm(val_loader, desc=\"Validate (X / X Steps) (dice=X.X)\", dynamic_ncols=True)\n", "            dice_val = validation(epoch_iterator_val)\n", "            epoch_loss /= step\n", "            epoch_loss_values.append(epoch_loss)\n", "            metric_values.append(dice_val)\n", "            if dice_val > dice_val_best:\n", "                dice_val_best = dice_val\n", "                global_step_best = global_step\n", "                torch.save(model.state_dict(), os.path.join(root_dir, \"best_metric_model.pth\"))\n", "                print(\n", "                    \"Model Was Saved ! Current Best Avg. Dice: {} Current Avg. Dice: {}\".format(dice_val_best, dice_val)\n", "                )\n", "            else:\n", "                print(\n", "                    \"Model Was Not Saved ! Current Best Avg. Dice: {} Current Avg. Dice: {}\".format(\n", "                        dice_val_best, dice_val\n", "                    )\n", "                )\n", "        global_step += 1\n", "    return global_step, dice_val_best, global_step_best\n", "\n", "\n", "max_iterations = 30000 # 2000\n", "eval_num = 500 # 50\n", "post_label = AsDiscrete(to_onehot=5)\n", "post_pred = AsDiscrete(argmax=True, to_onehot=5)\n", "dice_metric = DiceMetric(include_background=True, reduction=\"mean\", get_not_nans=False)\n", "global_step = 0\n", "dice_val_best = 0.0\n", "global_step_best = 0\n", "epoch_loss_values = []\n", "metric_values = []\n", "while global_step < max_iterations:\n", "    global_step, dice_val_best, global_step_best = train(global_step, train_loader, dice_val_best, global_step_best)\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.19595299661159515, 0.22811920940876007, 0.29186680912971497, 0.30148178339004517, 0.4097909927368164, 0.4458845257759094, 0.4761233329772949, 0.5563766360282898, 0.6065319776535034, 0.6107920408248901, 0.6280866861343384, 0.6354818940162659, 0.6494876146316528, 0.6396897435188293, 0.6542342305183411, 0.6503233909606934, 0.65642911195755, 0.6580517292022705, 0.6592726707458496, 0.6613168716430664, 0.6584131121635437, 0.66627037525177, 0.6648315191268921, 0.6659995317459106, 0.66603022813797, 0.6687301993370056, 0.6692132949829102, 0.6701905727386475, 0.6676666736602783, 0.6627170443534851, 0.6715517640113831, 0.6713098287582397, 0.6679916381835938, 0.6649607419967651, 0.6604234576225281, 0.6672571897506714, 0.6635393500328064, 0.6704288721084595, 0.6616403460502625, 0.6533085703849792, 0.668599009513855, 0.6667630076408386, 0.6474339365959167, 0.6666936874389648, 0.6608313322067261, 0.6596171259880066, 0.6581613421440125, 0.6595466732978821, 0.664583683013916, 0.6396064758300781, 0.664118766784668, 0.671792209148407, 0.6536137461662292, 0.6569033861160278, 0.655157208442688, 0.6592258214950562, 0.6567944288253784, 0.6280962228775024, 0.6582449674606323, 0.6437913775444031]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Iteration Average Loss\")\n", "x = [eval_num * (i + 1) for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val Mean Dice\")\n", "x = [eval_num * (i + 1) for i in range(len(metric_values))]\n", "print(metric_values)\n", "y = metric_values\n", "plt.grid(True)\n", "plt.xlabel(\"Iteration\")\n", "plt.yticks(np.arange(0.0, 0.8, 0.05))\n", "plt.plot(x, y)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************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********************************************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", "text/plain": ["<Figure size 1800x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["slice_map = {\n", "    \"brain_250_0000.nii.gz\": 8,\n", "    \"brain_231_0000.nii.gz\": 11,\n", "\n", "}\n", "case_num = 3\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "with torch.no_grad():\n", "    img_name = os.path.split(val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n", "    img = val_ds[case_num][\"image\"]\n", "    label = val_ds[case_num][\"label\"]\n", "    val_inputs = torch.unsqueeze(img, 1).cuda()\n", "    val_labels = torch.unsqueeze(label, 1).cuda()\n", "    val_outputs = sliding_window_inference(val_inputs, (96, 96, 16), 8, model, overlap=0.8)\n", "    plt.figure(\"check\", (18, 6))\n", "    plt.subplot(1, 3, 1)\n", "    plt.title(\"image\")\n", "    plt.imshow(val_inputs.cpu().numpy()[0, 0, :, :, slice_map[img_name]], cmap=\"gray\")\n", "    plt.subplot(1, 3, 2)\n", "    plt.title(\"label\")\n", "    plt.imshow(val_labels.cpu().numpy()[0, 0, :, :, slice_map[img_name]])\n", "    plt.subplot(1, 3, 3)\n", "    plt.title(\"output\")\n", "    plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, slice_map[img_name]])\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}