{"root_path": "/usr/home/<USER>/", "video_path": "/usr/home/<USER>/datasets/Kinetics", "annotation_path": "/usr/home/<USER>/Efficient-3DCNNs/annotation_Kinetics/kinetics.json", "result_path": "/usr/home/<USER>/Efficient-3DCNNs/results", "store_name": "kinetics_resnet_1.0x_RGB_16", "modality": "RGB", "dataset": "kinetics", "n_classes": 400, "n_finetune_classes": 600, "sample_size": 112, "sample_duration": 16, "downsample": 1, "initial_scale": 1.0, "n_scales": 5, "scale_step": 0.84089641525, "train_crop": "random", "learning_rate": 0.1, "lr_steps": [10, 30, 42, 70, 200, 250], "momentum": 0.9, "dampening": 0.9, "weight_decay": 0.001, "mean_dataset": "activitynet", "no_mean_norm": false, "std_norm": false, "nesterov": false, "optimizer": "sgd", "lr_patience": 10, "batch_size": 72, "n_epochs": 250, "begin_epoch": 1, "n_val_samples": 1, "resume_path": "/usr/home/<USER>/Efficient-3DCNNs/results/kinetics_resnet_1.0x_RGB_16_checkpoint.pth", "pretrain_path": "/usr/home/<USER>/Efficient-3DCNNs/results/resnet-50-kinetics.pth", "ft_portion": "complete", "no_train": false, "no_val": false, "test": false, "test_subset": "val", "scale_in_test": 1.0, "crop_position_in_test": "c", "no_softmax_in_test": false, "no_cuda": false, "n_threads": 32, "checkpoint": 1, "no_hflip": false, "norm_value": 1, "model": "resnet", "version": 1.1, "model_depth": 50, "resnet_shortcut": "B", "wide_resnet_k": 2, "resnext_cardinality": 32, "groups": 3, "width_mult": 1.0, "manual_seed": 1, "scales": [1.0, 0.84089641525, 0.7071067811803005, 0.5946035574934808, 0.****************], "arch": "resnet", "mean": [114.7748, 107.7354, 99.475], "std": [38.7568578, 37.88248729, 40.02898126]}