#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D-TransUNet 训练脚本 - 优化版本
基于nnUNet最佳实践进行优化，包括：
- 改进的错误处理和稳健性
- 优化的数据预处理流程
- 增强的训练循环和监控
- 更好的内存管理
- 完善的日志记录系统
- 自动化配置验证

优化特性:
- 参考nnUNet的数据预处理方法
- 自动裁剪到非零区域 (crop_to_nonzero)
- 模态特定的强度归一化方案
- 数据集指纹分析 (dataset fingerprinting)
- 自适应重采样策略
- 前景区域统计计算
- 强度属性分析
- 增强的检查点保存/加载机制
- 动态学习率调整
- 内存优化的数据加载

参考文献:
- nnUNet: https://github.com/MIC-DKFZ/nnUNet
- 3D-TransUNet: https://github.com/Beckschen/3D-TransUNet
"""
# 确保已安装nnUNet: pip install nnunetv2
# 确保已安装依赖: pip install SimpleITK scipy

import os
import sys
import subprocess
import importlib
import argparse
import pickle
import yaml
import time
import logging
import traceback
from pathlib import Path
from typing import Union, List, Dict, Optional, Tuple, Any
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.amp import autocast, GradScaler
from torch.utils.tensorboard import SummaryWriter
import SimpleITK as sitk
from tqdm import tqdm
from collections import OrderedDict
import matplotlib.pyplot as plt
from scipy.ndimage import binary_fill_holes
from copy import deepcopy
import json
import warnings
from torch.nn import functional as F

# 设置警告过滤
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# 设置日志系统 - 参考nnUNet的日志配置
def setup_logging(log_file: str = "transunet_training.log", level: int = logging.INFO) -> logging.Logger:
    """设置增强的日志系统"""
    # 创建日志目录
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )

    # 创建logger
    logger = logging.getLogger('TransUNet')
    logger.setLevel(level)

    # 避免重复添加handler
    if not logger.handlers:
        # 文件handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger

# 设置nnUNet环境变量 - 这是3D-TransUNet所需要的
def setup_nnunet_environment(base_dir: str = "/root/autodl-tmp") -> Dict[str, str]:
    """设置nnUNet环境变量，增加错误处理"""
    try:
        env_vars = {
            'nnUNet_N_proc_DA': '8',  # 数据增强进程数
            'nnUNet_codebase': os.path.join(base_dir, "3D-TransUNet"),  # 代码库路径
            'nnUNet_raw_data_base': os.path.join(base_dir, "nnUNet_raw_data_base"),  # 原始数据路径
            'nnUNet_preprocessed': os.path.join(base_dir, "nnUNet_preprocessed"),  # 预处理数据路径
            'RESULTS_FOLDER': os.path.join(base_dir, "nnUNet_results")  # 结果文件夹
        }

        # 设置环境变量
        for key, value in env_vars.items():
            os.environ[key] = value

        # 创建必要的目录
        directories_to_create = [
            env_vars['nnUNet_raw_data_base'],
            env_vars['nnUNet_preprocessed'],
            env_vars['RESULTS_FOLDER']
        ]

        for path in directories_to_create:
            try:
                os.makedirs(path, exist_ok=True)
            except PermissionError as e:
                print(f"警告: 无法创建目录 {path}: {e}")
            except Exception as e:
                print(f"错误: 创建目录 {path} 时发生未知错误: {e}")

        print("nnUNet环境变量设置完成:")
        for key, value in env_vars.items():
            print(f"  {key}: {value}")

        return env_vars

    except Exception as e:
        print(f"设置nnUNet环境变量时发生错误: {e}")
        raise

# 在导入其他模块之前设置环境变量
env_vars = setup_nnunet_environment()

# 添加项目路径 - 增加错误处理
transunet_path = '/root/autodl-tmp/3D-TransUNet'
if os.path.exists(transunet_path):
    sys.path.append(transunet_path)
else:
    print(f"警告: 3D-TransUNet路径不存在: {transunet_path}")
    print("请确保已正确下载和安装3D-TransUNet")

def install_dependencies(logger: Optional[logging.Logger] = None) -> bool:
    """自动安装必要的依赖包 - 增强版本"""
    required_packages = [
        'ml_collections',
        'batchgenerators',
        'medpy',
        'einops',
        'fvcore',
        'tensorboardX',
        'termcolor',
        'tabulate',
        'iopath',
        'portalocker',
        'omegaconf',
        'hydra-core',
        'scipy'
    ]

    if logger:
        logger.info("检查并安装必要的依赖包...")
    else:
        print("检查并安装必要的依赖包...")

    failed_packages = []

    for package in required_packages:
        try:
            importlib.import_module(package)
            msg = f"✅ {package} 已安装"
            if logger:
                logger.info(msg)
            else:
                print(msg)
        except ImportError:
            msg = f"📦 正在安装 {package}..."
            if logger:
                logger.info(msg)
            else:
                print(msg)

            try:
                # 使用更安全的安装方式
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )

                if result.returncode == 0:
                    msg = f"✅ {package} 安装成功"
                    if logger:
                        logger.info(msg)
                    else:
                        print(msg)
                else:
                    msg = f"❌ {package} 安装失败: {result.stderr}"
                    if logger:
                        logger.error(msg)
                    else:
                        print(msg)
                    failed_packages.append(package)

            except subprocess.TimeoutExpired:
                msg = f"❌ {package} 安装超时"
                if logger:
                    logger.error(msg)
                else:
                    print(msg)
                failed_packages.append(package)
            except Exception as e:
                msg = f"❌ {package} 安装时发生错误: {e}"
                if logger:
                    logger.error(msg)
                else:
                    print(msg)
                failed_packages.append(package)

    if failed_packages:
        msg = f"以下包安装失败，请手动安装: {', '.join(failed_packages)}"
        if logger:
            logger.warning(msg)
        else:
            print(f"警告: {msg}")
        return False

    return True

# 初始化日志系统
logger = setup_logging()

# 安装依赖
try:
    install_success = install_dependencies(logger)
    if not install_success:
        logger.warning("部分依赖包安装失败，可能影响程序运行")
except Exception as e:
    logger.error(f"安装依赖包时发生错误: {e}")
    print("继续运行，但可能遇到导入错误...")

# 尝试导入TransUNet模块 - 增加错误处理
try:
    from nn_transunet.networks.transunet3d_model import Generic_TransUNet_max_ppbp, InitWeights_He
    from nn_transunet.trainer.loss_functions import DC_and_CE_loss, SoftDiceLoss
    logger.info("成功导入TransUNet模块")
except ImportError as e:
    logger.error(f"导入TransUNet模块失败: {e}")
    logger.error("请确保3D-TransUNet已正确安装并且路径正确")
    raise
except Exception as e:
    logger.error(f"导入TransUNet模块时发生未知错误: {e}")
    raise


class nnUNetConfig:
    """nnUNet风格的配置类 - 优化版本"""
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

        # 数据路径配置 - 使用nnUNet标准目录结构
        self.image_dir = "/root/autodl-tmp/320HCC/image/ap"
        self.mask_dir = "/root/autodl-tmp/320HCC/mask/ap"
        self.preprocessed_data_dir = "/root/autodl-tmp/nnUNet_preprocessed"  # 使用nnUNet标准目录
        self.output_dir = "/root/autodl-tmp/nnUNet_results"  # 使用nnUNet标准结果目录
        
        # nnUNet预处理配置
        self.target_spacing = [1.0, 1.0, 1.0]  # 目标spacing
        self.normalization_scheme = "noNorm"  # CT, CT2, noNorm, MRI - 对于MRI数据推荐使用noNorm
        self.use_nonzero_mask = True  # 是否使用非零mask进行归一化
        self.transpose_forward = [0, 1, 2]  # 转置顺序
        self.crop_to_nonzero = True  # 是否裁剪到非零区域

        # 模型配置
        self.model_name = "Generic_TransUNet_max_ppbp"
        self.num_classes = 2
        self.input_channels = 1
        self.base_num_features = 32

        # 训练配置 - 优化版本
        self.batch_size = 4  # 更新为参数中的batch_size
        self.num_epochs = 200  # 增加训练轮数
        self.initial_lr = 1e-4  # 降低学习率
        self.weight_decay = 1e-3  # 增加正则化
        self.momentum = 0.99

        # 数据增强配置
        self.patch_size = [64, 192, 192]  # 更新为参数中的crop_size
        self.do_data_augmentation = True

        # 其他配置
        self.mixed_precision = True
        self.deep_supervision = True
        self.save_interval = 50
        self.validation_interval = 1

        # 早停配置 - 更宽松的早停
        self.early_stopping_patience = 30  # 增加patience，给模型更多时间学习
        self.early_stopping_min_delta = 0.001  # 降低min_delta，接受小的改进

        # 梯度累积配置
        self.gradient_accumulation_steps = 1

        # 内存优化配置 - 新增
        self.pin_memory = True
        self.num_workers = 8
        self.prefetch_factor = 2
        self.persistent_workers = True

        # 检查点配置 - 新增
        self.save_checkpoint_interval = 10  # 每N个epoch保存检查点
        self.keep_only_latest_checkpoints = True  # 只保留最新的检查点
        self.max_checkpoints_to_keep = 3  # 最多保留的检查点数量

        # TransUNet特定配置 - 根据提供的参数更新
        self.vit_depth = 12  # 参数中的vit_depth
        self.vit_hidden_size = 768
        self.max_hidden_dim = 192  # 参数中的max_hidden_dim
        self.num_queries = 20  # 参数中的num_queries
        self.is_max_hungarian = False  # 参数中的is_max_hungarian
        self.is_max_cls = True  # 参数中的is_max_cls
        self.is_vit_pretrain = False  # 不使用预训练权重，避免文件不存在错误
        self.is_max_bottleneck_transformer = True  # 参数中的is_max_bottleneck_transformer
        self.is_max = False  # 参数中的is_max
        self.is_masked_attn = True  # 参数中的is_masked_attn
        self.max_dec_layers = 3  # 参数中的max_dec_layers
        self.is_max_ms = True  # 参数中的is_max_ms
        self.max_ms_idxs = [-4, -3, -2]  # 参数中的max_ms_idxs
        self.mw = 0.0  # 参数中的mw
        self.is_max_ds = True  # 参数中的is_max_ds
        self.is_masking = True  # 参数中的is_masking
        self.is_mhsa_float32 = True  # 参数中的is_mhsa_float32
        self.vit_layer_scale = True  # 参数中的vit_layer_scale

    def validate_config(self) -> bool:
        """验证配置参数的有效性 - 新增功能"""
        try:
            # 检查路径是否存在
            if not os.path.exists(self.image_dir):
                self.logger.error(f"图像目录不存在: {self.image_dir}")
                return False

            if not os.path.exists(self.mask_dir):
                self.logger.error(f"标签目录不存在: {self.mask_dir}")
                return False

            # 检查数值参数
            if self.batch_size <= 0:
                self.logger.error(f"批次大小必须大于0: {self.batch_size}")
                return False

            if self.num_epochs <= 0:
                self.logger.error(f"训练轮数必须大于0: {self.num_epochs}")
                return False

            if self.initial_lr <= 0:
                self.logger.error(f"学习率必须大于0: {self.initial_lr}")
                return False

            # 检查patch_size
            if len(self.patch_size) != 3 or any(s <= 0 for s in self.patch_size):
                self.logger.error(f"patch_size必须是3个正整数: {self.patch_size}")
                return False

            # 检查GPU内存
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
                estimated_memory = self.batch_size * np.prod(self.patch_size) * 4 / 1024**3  # 粗略估计
                if estimated_memory > gpu_memory * 0.8:
                    self.logger.warning(f"估计内存使用 {estimated_memory:.1f}GB 可能超过GPU内存 {gpu_memory:.1f}GB")

            self.logger.info("配置验证通过")
            return True

        except Exception as e:
            self.logger.error(f"配置验证时发生错误: {e}")
            return False

    def print_config_summary(self):
        """打印配置摘要，便于验证参数 - 优化版本"""
        summary_lines = [
            "\n" + "="*60,
            "配置参数摘要 (与提供的参数对比)",
            "="*60,
            f"模型名称: {self.model_name}",
            f"输入通道数: {self.input_channels}",
            f"类别数: {self.num_classes}",
            f"批次大小: {self.batch_size}",
            f"训练轮数: {self.num_epochs}",
            f"初始学习率: {self.initial_lr}",
            f"补丁大小 (crop_size): {self.patch_size}",
            f"目标spacing: {self.target_spacing}",
            f"归一化方案: {self.normalization_scheme}",
            f"数据模态: 将在预处理时自动检测",
            f"深度监督: {self.deep_supervision}",
            f"混合精度: {self.mixed_precision}",
            f"内存优化: pin_memory={self.pin_memory}, num_workers={self.num_workers}",
            "\nTransUNet特定参数:",
            f"  vit_depth: {self.vit_depth}",
            f"  is_vit_pretrain: {self.is_vit_pretrain}",
            f"  vit_layer_scale: {self.vit_layer_scale}",
            f"  is_max_bottleneck_transformer: {self.is_max_bottleneck_transformer}",
            f"  is_max: {self.is_max}",
            f"  is_max_hungarian: {self.is_max_hungarian}",
            f"  is_masked_attn: {self.is_masked_attn}",
            f"  max_dec_layers: {self.max_dec_layers}",
            f"  is_max_ms: {self.is_max_ms}",
            f"  max_ms_idxs: {self.max_ms_idxs}",
            f"  max_hidden_dim: {self.max_hidden_dim}",
            f"  mw: {self.mw}",
            f"  is_max_ds: {self.is_max_ds}",
            f"  is_masking: {self.is_masking}",
            f"  num_queries: {self.num_queries}",
            f"  is_max_cls: {self.is_max_cls}",
            f"  is_mhsa_float32: {self.is_mhsa_float32}",
            "="*60
        ]

        for line in summary_lines:
            print(line)
            if self.logger:
                self.logger.info(line)

    def validate_against_reference_params(self):
        """验证当前配置是否与提供的参考参数匹配"""
        print("\n" + "="*60)
        print("参数验证 - 与提供的参考参数对比")
        print("="*60)

        # 参考参数
        reference_params = {
            'model': 'Generic_TransUNet_max_ppbp',
            'crop_size': [64, 192, 192],
            'batch_size': 4,
            'initial_lr': 3e-4,
            'max_num_epochs': 125,
            'warmup_epochs': 10,
            'lrschedule': 'warmup_cosine',
            'optim_name': 'adamw',
            'is_max': False,
            'is_max_hungarian': False,
            'is_max_bottleneck_transformer': True,
            'is_masked_attn': True,
            'max_dec_layers': 3,
            'is_max_ms': True,
            'max_ms_idxs': [-4, -3, -2],
            'max_hidden_dim': 192,
            'mw': 0.0,
            'is_max_ds': True,
            'is_masking': True,
            'num_queries': 20,
            'is_max_cls': True,
            'is_mhsa_float32': True,
            'vit_depth': 12,
            'is_vit_pretrain': False,  # 修改为False，不使用预训练权重
            'vit_layer_scale': True
        }

        # 当前配置
        current_params = {
            'model': self.model_name,
            'crop_size': self.patch_size,
            'batch_size': self.batch_size,
            'initial_lr': self.initial_lr,
            'max_num_epochs': self.num_epochs,
            'warmup_epochs': 10,  # 硬编码在调度器中
            'lrschedule': 'warmup_cosine',  # 硬编码在调度器中
            'optim_name': 'adamw',  # 硬编码在优化器中
            'is_max': self.is_max,
            'is_max_hungarian': self.is_max_hungarian,
            'is_max_bottleneck_transformer': self.is_max_bottleneck_transformer,
            'is_masked_attn': self.is_masked_attn,
            'max_dec_layers': self.max_dec_layers,
            'is_max_ms': self.is_max_ms,
            'max_ms_idxs': self.max_ms_idxs,
            'max_hidden_dim': self.max_hidden_dim,
            'mw': self.mw,
            'is_max_ds': self.is_max_ds,
            'is_masking': self.is_masking,
            'num_queries': self.num_queries,
            'is_max_cls': self.is_max_cls,
            'is_mhsa_float32': self.is_mhsa_float32,
            'vit_depth': self.vit_depth,
            'is_vit_pretrain': self.is_vit_pretrain,
            'vit_layer_scale': self.vit_layer_scale
        }

        # 对比参数
        all_match = True
        for key, ref_value in reference_params.items():
            current_value = current_params.get(key, 'MISSING')
            match = current_value == ref_value
            if not match:
                all_match = False
            status = "✅" if match else "❌"
            print(f"{status} {key:25} | 参考: {str(ref_value):15} | 当前: {str(current_value):15}")

        print("="*60)
        if all_match:
            print("🎉 所有参数都与参考配置匹配！")
        else:
            print("⚠️  部分参数与参考配置不匹配，请检查上述标记为❌的项目")
        print("="*60)

        return all_match


def setup_directories(config: nnUNetConfig) -> bool:
    """创建必要的目录 - 增强版本"""
    try:
        directories = [
            config.preprocessed_data_dir,
            config.output_dir,
            os.path.join(config.output_dir, "checkpoints"),
            os.path.join(config.output_dir, "logs"),
            os.path.join(config.output_dir, "predictions"),
            os.path.join(config.output_dir, "visualizations"),  # 新增可视化目录
            os.path.join(config.output_dir, "metrics"),  # 新增指标目录
        ]

        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                config.logger.info(f"创建/确认目录: {directory}")
            except PermissionError as e:
                config.logger.error(f"权限错误，无法创建目录 {directory}: {e}")
                return False
            except Exception as e:
                config.logger.error(f"创建目录 {directory} 时发生错误: {e}")
                return False

        return True

    except Exception as e:
        config.logger.error(f"设置目录时发生错误: {e}")
        return False


def load_nifti_image(filepath: str, logger: Optional[logging.Logger] = None) -> Dict[str, Any]:
    """加载NIfTI图像 - 增强版本"""
    try:
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"文件不存在: {filepath}")

        # 检查文件大小
        file_size = os.path.getsize(filepath)
        if file_size == 0:
            raise ValueError(f"文件为空: {filepath}")

        sitk_image = sitk.ReadImage(filepath)
        image_array = sitk.GetArrayFromImage(sitk_image)

        # 检查图像数据
        if image_array.size == 0:
            raise ValueError(f"图像数据为空: {filepath}")

        spacing = sitk_image.GetSpacing()
        origin = sitk_image.GetOrigin()
        direction = sitk_image.GetDirection()

        if logger:
            logger.debug(f"加载图像: {filepath}, 形状: {image_array.shape}, spacing: {spacing}")

        return {
            'data': image_array,
            'spacing': spacing,
            'origin': origin,
            'direction': direction,
            'sitk_image': sitk_image,
            'filepath': filepath,
            'file_size': file_size
        }

    except Exception as e:
        error_msg = f"加载NIfTI图像失败 {filepath}: {e}"
        if logger:
            logger.error(error_msg)
        raise RuntimeError(error_msg) from e


def save_nifti_image(image_array: np.ndarray, reference_image: sitk.Image,
                    output_path: str, logger: Optional[logging.Logger] = None) -> bool:
    """保存NIfTI图像 - 增强版本"""
    try:
        # 创建输出目录
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # 确保数据类型正确
        if image_array.dtype != np.uint8:
            image_array = image_array.astype(np.uint8)

        output_image = sitk.GetImageFromArray(image_array)
        output_image.CopyInformation(reference_image)
        sitk.WriteImage(output_image, output_path)

        if logger:
            logger.info(f"保存图像: {output_path}")

        return True

    except Exception as e:
        error_msg = f"保存NIfTI图像失败 {output_path}: {e}"
        if logger:
            logger.error(error_msg)
        return False


# nnUNet风格的预处理函数 - 优化版本
def create_nonzero_mask(data: np.ndarray, logger: Optional[logging.Logger] = None) -> np.ndarray:
    """创建非零mask - 来自nnUNet，增加错误处理"""
    try:
        assert len(data.shape) == 4 or len(data.shape) == 3, f"data must have shape (C, X, Y, Z) or shape (C, X, Y), got {data.shape}"

        nonzero_mask = np.zeros(data.shape[1:], dtype=bool)
        for c in range(data.shape[0]):
            this_mask = data[c] != 0
            nonzero_mask = nonzero_mask | this_mask

        # 使用形态学操作填充孔洞
        nonzero_mask = binary_fill_holes(nonzero_mask)

        if logger:
            nonzero_voxels = np.sum(nonzero_mask)
            total_voxels = nonzero_mask.size
            logger.debug(f"非零mask: {nonzero_voxels}/{total_voxels} ({100*nonzero_voxels/total_voxels:.1f}%)")

        return nonzero_mask

    except Exception as e:
        error_msg = f"创建非零mask失败: {e}"
        if logger:
            logger.error(error_msg)
        raise RuntimeError(error_msg) from e


def get_bbox_from_mask(mask: np.ndarray, outside_value: int = 0,
                      logger: Optional[logging.Logger] = None) -> List[List[int]]:
    """从mask获取边界框 - 来自nnUNet，增加错误处理"""
    try:
        mask_voxel_coords = np.where(mask != outside_value)

        if len(mask_voxel_coords[0]) == 0:
            raise ValueError("mask中没有找到非背景像素")

        minzidx = int(np.min(mask_voxel_coords[0]))
        maxzidx = int(np.max(mask_voxel_coords[0])) + 1
        minxidx = int(np.min(mask_voxel_coords[1]))
        maxxidx = int(np.max(mask_voxel_coords[1])) + 1
        minyidx = int(np.min(mask_voxel_coords[2]))
        maxyidx = int(np.max(mask_voxel_coords[2])) + 1

        bbox = [[minzidx, maxzidx], [minxidx, maxxidx], [minyidx, maxyidx]]

        if logger:
            original_shape = mask.shape
            cropped_shape = [maxzidx-minzidx, maxxidx-minxidx, maxyidx-minyidx]
            logger.debug(f"边界框: {bbox}, 原始形状: {original_shape}, 裁剪后形状: {cropped_shape}")

        return bbox

    except Exception as e:
        error_msg = f"获取边界框失败: {e}"
        if logger:
            logger.error(error_msg)
        raise RuntimeError(error_msg) from e


def crop_to_bbox(image: np.ndarray, bbox: List[List[int]],
                logger: Optional[logging.Logger] = None) -> np.ndarray:
    """裁剪到边界框 - 来自nnUNet，增加错误处理"""
    try:
        assert len(image.shape) == 3, f"only supports 3d images, got shape {image.shape}"

        # 验证边界框
        for i, (start, end) in enumerate(bbox):
            if start < 0 or end > image.shape[i] or start >= end:
                raise ValueError(f"无效的边界框维度 {i}: [{start}, {end}], 图像形状: {image.shape}")

        resizer = (slice(bbox[0][0], bbox[0][1]), slice(bbox[1][0], bbox[1][1]), slice(bbox[2][0], bbox[2][1]))
        cropped = image[resizer]

        if logger:
            logger.debug(f"裁剪: {image.shape} -> {cropped.shape}")

        return cropped

    except Exception as e:
        error_msg = f"裁剪到边界框失败: {e}"
        if logger:
            logger.error(error_msg)
        raise RuntimeError(error_msg) from e


def crop_to_nonzero(data: np.ndarray, seg: Optional[np.ndarray] = None,
                   nonzero_label: int = -1, logger: Optional[logging.Logger] = None) -> Tuple[np.ndarray, Optional[np.ndarray], List[List[int]]]:
    """裁剪到非零区域 - 来自nnUNet，增加错误处理"""
    try:
        nonzero_mask = create_nonzero_mask(data, logger)
        bbox = get_bbox_from_mask(nonzero_mask, 0, logger)

        cropped_data = []
        for c in range(data.shape[0]):
            cropped = crop_to_bbox(data[c], bbox, logger)
            cropped_data.append(cropped[None])
        data = np.vstack(cropped_data)

        if seg is not None:
            cropped_seg = []
            for c in range(seg.shape[0]):
                cropped = crop_to_bbox(seg[c], bbox, logger)
                cropped_seg.append(cropped[None])
            seg = np.vstack(cropped_seg)

        return data, seg, bbox

    except Exception as e:
        error_msg = f"裁剪到非零区域失败: {e}"
        if logger:
            logger.error(error_msg)
        raise RuntimeError(error_msg) from e


def resample_patient_sitk(data, seg, original_spacing, target_spacing, order_data=3, order_seg=0):
    """使用SimpleITK重采样患者数据 - 参考nnUNet"""
    # 转换为SimpleITK图像
    original_spacing = tuple(original_spacing)
    target_spacing = tuple(target_spacing)
    
    # 重采样数据
    resampled_data = []
    for c in range(data.shape[0]):
        sitk_image = sitk.GetImageFromArray(data[c])
        sitk_image.SetSpacing(original_spacing)
        
        # 计算新的尺寸
        original_size = sitk_image.GetSize()
        new_size = [
            int(round(original_size[i] * original_spacing[i] / target_spacing[i]))
            for i in range(3)
        ]
        
        # 重采样
        resampler = sitk.ResampleImageFilter()
        resampler.SetOutputSpacing(target_spacing)
        resampler.SetSize(new_size)
        resampler.SetOutputDirection(sitk_image.GetDirection())
        resampler.SetOutputOrigin(sitk_image.GetOrigin())
        resampler.SetTransform(sitk.Transform())
        resampler.SetDefaultPixelValue(0)
        
        if order_data == 3:
            resampler.SetInterpolator(sitk.sitkBSpline)
        elif order_data == 1:
            resampler.SetInterpolator(sitk.sitkLinear)
        else:
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)
        
        resampled_image = resampler.Execute(sitk_image)
        resampled_array = sitk.GetArrayFromImage(resampled_image)
        resampled_data.append(resampled_array[None])
    
    data = np.vstack(resampled_data)
    
    # 重采样分割
    if seg is not None:
        resampled_seg = []
        for c in range(seg.shape[0]):
            sitk_seg = sitk.GetImageFromArray(seg[c])
            sitk_seg.SetSpacing(original_spacing)
            
            resampler = sitk.ResampleImageFilter()
            resampler.SetOutputSpacing(target_spacing)
            resampler.SetSize(new_size)
            resampler.SetOutputDirection(sitk_seg.GetDirection())
            resampler.SetOutputOrigin(sitk_seg.GetOrigin())
            resampler.SetTransform(sitk.Transform())
            resampler.SetDefaultPixelValue(0)
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)
            
            resampled_seg_image = resampler.Execute(sitk_seg)
            resampled_seg_array = sitk.GetArrayFromImage(resampled_seg_image)
            resampled_seg.append(resampled_seg_array[None])
        
        seg = np.vstack(resampled_seg)
    
    return data, seg


def compute_intensity_properties(data, seg=None, use_nonzero_mask=True):
    """计算强度属性 - 参考nnUNet"""
    properties = {}

    for c in range(data.shape[0]):
        channel_data = data[c]

        if seg is not None and use_nonzero_mask:
            # 只在前景区域计算统计信息
            mask = seg[0] > 0  # 假设第一个通道是分割
            if np.sum(mask) > 0:
                foreground_data = channel_data[mask]
            else:
                foreground_data = channel_data[channel_data != 0]
        else:
            foreground_data = channel_data[channel_data != 0]

        if len(foreground_data) > 0:
            properties[c] = {
                'mean': float(np.mean(foreground_data)),
                'sd': float(np.std(foreground_data)),
                'percentile_00_5': float(np.percentile(foreground_data, 0.5)),
                'percentile_99_5': float(np.percentile(foreground_data, 99.5)),
                'min': float(np.min(foreground_data)),
                'max': float(np.max(foreground_data))
            }
        else:
            # 如果没有前景数据，使用全图像统计
            properties[c] = {
                'mean': float(np.mean(channel_data)),
                'sd': float(np.std(channel_data)),
                'percentile_00_5': float(np.percentile(channel_data, 0.5)),
                'percentile_99_5': float(np.percentile(channel_data, 99.5)),
                'min': float(np.min(channel_data)),
                'max': float(np.max(channel_data))
            }

    return properties


def normalize_intensity_nnunet_style(data, normalization_scheme, intensity_properties=None, use_nonzero_mask=True, seg=None):
    """nnUNet风格的强度归一化"""
    normalized_data = data.copy()

    for c in range(data.shape[0]):
        if normalization_scheme == "CT":
            # CT归一化：使用全局统计信息
            if intensity_properties is not None and c in intensity_properties:
                mean_intensity = intensity_properties[c]['mean']
                std_intensity = intensity_properties[c]['sd']
                lower_bound = intensity_properties[c]['percentile_00_5']
                upper_bound = intensity_properties[c]['percentile_99_5']

                normalized_data[c] = np.clip(normalized_data[c], lower_bound, upper_bound)
                normalized_data[c] = (normalized_data[c] - mean_intensity) / (std_intensity + 1e-8)

                if use_nonzero_mask and seg is not None:
                    normalized_data[c][seg[0] < 0] = 0
            else:
                # 如果没有预计算的属性，使用当前数据计算
                channel_data = normalized_data[c]
                if seg is not None and use_nonzero_mask:
                    mask = seg[0] > 0
                    if np.sum(mask) > 0:
                        foreground_data = channel_data[mask]
                        lower_bound = np.percentile(foreground_data, 0.5)
                        upper_bound = np.percentile(foreground_data, 99.5)
                        mean_intensity = np.mean(foreground_data)
                        std_intensity = np.std(foreground_data)
                    else:
                        lower_bound = np.percentile(channel_data, 0.5)
                        upper_bound = np.percentile(channel_data, 99.5)
                        mean_intensity = np.mean(channel_data)
                        std_intensity = np.std(channel_data)
                else:
                    lower_bound = np.percentile(channel_data, 0.5)
                    upper_bound = np.percentile(channel_data, 99.5)
                    mean_intensity = np.mean(channel_data)
                    std_intensity = np.std(channel_data)

                normalized_data[c] = np.clip(normalized_data[c], lower_bound, upper_bound)
                normalized_data[c] = (normalized_data[c] - mean_intensity) / (std_intensity + 1e-8)

        elif normalization_scheme == "CT2":
            # CT2归一化：使用局部统计信息
            if intensity_properties is not None and c in intensity_properties:
                lower_bound = intensity_properties[c]['percentile_00_5']
                upper_bound = intensity_properties[c]['percentile_99_5']
                mask = (normalized_data[c] > lower_bound) & (normalized_data[c] < upper_bound)
                normalized_data[c] = np.clip(normalized_data[c], lower_bound, upper_bound)
                if np.sum(mask) > 0:
                    mn = normalized_data[c][mask].mean()
                    sd = normalized_data[c][mask].std()
                    normalized_data[c] = (normalized_data[c] - mn) / (sd + 1e-8)

                if use_nonzero_mask and seg is not None:
                    normalized_data[c][seg[0] < 0] = 0
            else:
                # 使用当前数据的统计信息
                channel_data = normalized_data[c]
                lower_bound = np.percentile(channel_data, 0.5)
                upper_bound = np.percentile(channel_data, 99.5)
                mask = (channel_data > lower_bound) & (channel_data < upper_bound)
                normalized_data[c] = np.clip(normalized_data[c], lower_bound, upper_bound)
                if np.sum(mask) > 0:
                    mn = normalized_data[c][mask].mean()
                    sd = normalized_data[c][mask].std()
                    normalized_data[c] = (normalized_data[c] - mn) / (sd + 1e-8)

        elif normalization_scheme == "noNorm":
            # 不进行归一化 - 推荐用于MRI数据
            pass
        elif normalization_scheme == "MRI":
            # MRI特定归一化：基于前景区域的Z-score归一化
            if seg is not None and use_nonzero_mask:
                mask = seg[0] > 0
                if np.sum(mask) > 0:
                    # 使用前景区域计算统计量
                    foreground_data = normalized_data[c][mask]
                    # 使用更保守的百分位数去除异常值
                    lower_bound = np.percentile(foreground_data, 1.0)
                    upper_bound = np.percentile(foreground_data, 99.0)

                    # 裁剪异常值
                    clipped_data = np.clip(foreground_data, lower_bound, upper_bound)
                    mn = np.mean(clipped_data)
                    std = np.std(clipped_data)

                    # 应用归一化
                    normalized_data[c] = np.clip(normalized_data[c], lower_bound, upper_bound)
                    normalized_data[c] = (normalized_data[c] - mn) / (std + 1e-8)

                    # 保持背景为0
                    normalized_data[c][~mask] = 0
                else:
                    # 如果没有前景，使用全局统计
                    mn = normalized_data[c].mean()
                    std = normalized_data[c].std()
                    normalized_data[c] = (normalized_data[c] - mn) / (std + 1e-8)
            else:
                # 没有分割mask时，使用全局统计
                # 使用百分位数去除异常值
                lower_bound = np.percentile(normalized_data[c], 1.0)
                upper_bound = np.percentile(normalized_data[c], 99.0)

                clipped_data = np.clip(normalized_data[c], lower_bound, upper_bound)
                mn = np.mean(clipped_data)
                std = np.std(clipped_data)

                normalized_data[c] = np.clip(normalized_data[c], lower_bound, upper_bound)
                normalized_data[c] = (normalized_data[c] - mn) / (std + 1e-8)
        else:
            # 默认Z-score归一化
            if seg is not None and use_nonzero_mask:
                mask = seg[0] > 0
                if np.sum(mask) > 0:
                    mn = normalized_data[c][mask].mean()
                    std = normalized_data[c][mask].std()
                else:
                    mn = normalized_data[c].mean()
                    std = normalized_data[c].std()
            else:
                mn = normalized_data[c].mean()
                std = normalized_data[c].std()

            normalized_data[c] = (normalized_data[c] - mn) / (std + 1e-8)

    return normalized_data


def detect_modality_and_recommend_normalization(intensity_properties):
    """
    根据强度分布特征自动检测数据模态并推荐归一化方案
    """
    if not intensity_properties:
        return 'Unknown', 'noNorm'

    # 获取第一个通道的统计信息
    channel_0 = intensity_properties.get(0, {})

    min_val = channel_0.get('min', 0)
    max_val = channel_0.get('max', 1000)
    mean_val = channel_0.get('mean', 0)

    # CT数据特征：通常有负值（空气），范围较大，HU值
    # MRI数据特征：通常非负，范围相对较小，任意单位

    if min_val < -500 and max_val > 1000:
        # 典型的CT数据特征（HU值范围）
        modality = 'CT'
        recommended_norm = 'CT'
        reason = f"检测到CT数据特征: min={min_val:.1f}, max={max_val:.1f} (典型HU值范围)"
    elif min_val >= 0 and max_val < 4000 and mean_val > 0:
        # 典型的MRI数据特征
        modality = 'MRI'
        recommended_norm = 'noNorm'  # 对MRI推荐noNorm或MRI
        reason = f"检测到MRI数据特征: min={min_val:.1f}, max={max_val:.1f}, mean={mean_val:.1f}"
    elif min_val < 0 and max_val < 1000:
        # 可能是归一化后的数据
        modality = 'Normalized'
        recommended_norm = 'noNorm'
        reason = f"检测到已归一化数据: min={min_val:.1f}, max={max_val:.1f}"
    else:
        # 未知类型
        modality = 'Unknown'
        recommended_norm = 'noNorm'
        reason = f"未知数据类型: min={min_val:.1f}, max={max_val:.1f}, 建议使用noNorm"

    print(f"🔍 数据模态检测: {modality}")
    print(f"📊 {reason}")
    print(f"💡 推荐归一化方案: {recommended_norm}")

    return modality, recommended_norm


def analyze_dataset_fingerprint(image_dir, mask_dir, config):
    """分析数据集指纹 - 参考nnUNet的数据集分析"""
    print("分析数据集指纹...")

    # 获取所有文件
    image_files = [f for f in os.listdir(image_dir) if f.endswith('.nii.gz')]
    mask_files = [f for f in os.listdir(mask_dir) if f.endswith('.nii.gz')]

    image_files.sort()
    mask_files.sort()

    # 数据集统计信息
    dataset_properties = {
        'num_cases': len(image_files),
        'spacings': [],
        'sizes': [],
        'intensities': [],
        'modalities': ['Unknown'],  # 将通过分析确定
        'labels': {}
    }

    # 分析前几个样本来获取数据集特征
    num_samples_to_analyze = min(10, len(image_files))

    all_intensity_properties = []

    for i in range(num_samples_to_analyze):
        print(f"分析样本 {i+1}/{num_samples_to_analyze}: {image_files[i]}")

        # 加载图像和标签
        image_path = os.path.join(image_dir, image_files[i])
        mask_path = os.path.join(mask_dir, mask_files[i])

        image_data = load_nifti_image(image_path)
        mask_data = load_nifti_image(mask_path)

        # 记录spacing和尺寸
        dataset_properties['spacings'].append(image_data['spacing'])
        dataset_properties['sizes'].append(image_data['data'].shape)

        # 准备数据进行分析
        data = image_data['data'][None].astype(np.float32)  # 添加通道维度
        seg = mask_data['data'][None].astype(np.uint8)

        # 计算强度属性
        intensity_props = compute_intensity_properties(data, seg, use_nonzero_mask=True)
        all_intensity_properties.append(intensity_props)

        # 记录标签信息
        unique_labels = np.unique(seg)
        for label in unique_labels:
            label_key = int(label)  # 转换为Python int类型
            if label_key not in dataset_properties['labels']:
                dataset_properties['labels'][label_key] = 0
            dataset_properties['labels'][label_key] += int(np.sum(seg == label))

    # 计算全局强度属性
    global_intensity_properties = {}
    if all_intensity_properties:
        for c in all_intensity_properties[0].keys():
            global_intensity_properties[c] = {
                'mean': np.mean([props[c]['mean'] for props in all_intensity_properties]),
                'sd': np.mean([props[c]['sd'] for props in all_intensity_properties]),
                'percentile_00_5': np.mean([props[c]['percentile_00_5'] for props in all_intensity_properties]),
                'percentile_99_5': np.mean([props[c]['percentile_99_5'] for props in all_intensity_properties]),
                'min': np.min([props[c]['min'] for props in all_intensity_properties]),
                'max': np.max([props[c]['max'] for props in all_intensity_properties])
            }

    dataset_properties['intensity_properties'] = global_intensity_properties

    # 自动检测数据模态并推荐归一化方案
    detected_modality, recommended_norm = detect_modality_and_recommend_normalization(global_intensity_properties)
    dataset_properties['detected_modality'] = detected_modality
    dataset_properties['recommended_normalization'] = recommended_norm
    dataset_properties['modalities'] = [detected_modality]

    # 如果当前归一化方案是默认的，且检测到了更好的方案，给出建议
    if config.normalization_scheme == "noNorm" and recommended_norm != "noNorm":
        print(f"💡 建议: 当前使用 '{config.normalization_scheme}'，但检测到 '{detected_modality}' 数据")
        print(f"   推荐使用 '{recommended_norm}' 归一化方案以获得更好效果")
    elif config.normalization_scheme != recommended_norm:
        print(f"ℹ️  当前使用 '{config.normalization_scheme}' 归一化，检测推荐 '{recommended_norm}'")

    # 计算中位数spacing作为目标spacing
    if dataset_properties['spacings']:
        spacings_array = np.array(dataset_properties['spacings'])
        median_spacing = np.median(spacings_array, axis=0)
        dataset_properties['median_spacing'] = median_spacing.tolist()

        # 更新配置中的目标spacing
        config.target_spacing = median_spacing.tolist()

    # 保存数据集指纹
    fingerprint_path = os.path.join(config.preprocessed_data_dir, 'dataset_fingerprint.json')
    with open(fingerprint_path, 'w') as f:
        json.dump(dataset_properties, f, indent=2)

    print(f"数据集指纹已保存到: {fingerprint_path}")
    print(f"数据集包含 {dataset_properties['num_cases']} 个样本")
    print(f"中位数spacing: {dataset_properties.get('median_spacing', 'N/A')}")
    print(f"标签分布: {dataset_properties['labels']}")

    return dataset_properties


def preprocess_single_case_nnunet_style(image_file, mask_file, config, dataset_properties, split_dir):
    """nnUNet风格的单个样本预处理"""
    try:
        # 加载图像和标签
        image_path = os.path.join(config.image_dir, image_file)
        mask_path = os.path.join(config.mask_dir, mask_file)

        image_data = load_nifti_image(image_path)
        mask_data = load_nifti_image(mask_path)

        # 准备数据 - 添加通道维度
        data = image_data['data'][None].astype(np.float32)  # [1, D, H, W]
        seg = mask_data['data'][None].astype(np.uint8)      # [1, D, H, W]

        # 1. 裁剪到非零区域 (nnUNet的核心特性)
        if config.crop_to_nonzero:
            data, seg, bbox = crop_to_nonzero(data, seg)
            print(f"裁剪前: {image_data['data'].shape}, 裁剪后: {data[0].shape}")
        else:
            bbox = None

        # 2. 重采样到目标spacing
        original_spacing = image_data['spacing']
        target_spacing = config.target_spacing

        if original_spacing != target_spacing:
            data, seg = resample_patient_sitk(data, seg, original_spacing, target_spacing,
                                            order_data=3, order_seg=0)
            print(f"重采样: {original_spacing} -> {target_spacing}")

        # 3. nnUNet风格的强度归一化
        intensity_properties = dataset_properties.get('intensity_properties', None)
        data = normalize_intensity_nnunet_style(
            data,
            config.normalization_scheme,
            intensity_properties,
            config.use_nonzero_mask,
            seg
        )

        # 使用原始文件名作为基础名称
        base_name = image_file.replace('.nii.gz', '')

        # 保存预处理后的数据 - 兼容nnUNet格式
        # 选择保存格式：True=nnUNet兼容格式，False=简单npz格式
        use_nnunet_format = getattr(config, 'use_nnunet_format', False)

        if use_nnunet_format:
            # nnUNet兼容格式 - 使用batchgenerators格式
            try:
                from batchgenerators.utilities.file_and_folder_operations import save_pickle
                # 合并数据和分割 - nnUNet格式
                combined_data = np.vstack([data, seg])  # [channels+1, D, H, W]

                # 保存为.npz格式（简化版的nnUNet格式）
                np.savez_compressed(os.path.join(split_dir, f"{base_name}.npz"),
                                  data=combined_data.astype(np.float32))
                print(f"保存为nnUNet兼容格式: {base_name}.npz")
            except ImportError:
                print("警告: batchgenerators未安装，使用简化格式")
                use_nnunet_format = False

        if not use_nnunet_format:
            # 简化格式 - 分别保存图像和标签
            np.savez_compressed(os.path.join(split_dir, f"{base_name}_image.npz"),
                              data=data[0].astype(np.float32))  # 移除通道维度保存
            np.savez_compressed(os.path.join(split_dir, f"{base_name}_label.npz"),
                              data=seg[0].astype(np.uint8))     # 移除通道维度保存

        # 保存元数据
        metadata = {
            'original_spacing': original_spacing,
            'target_spacing': target_spacing,
            'original_origin': image_data['origin'],
            'original_direction': image_data['direction'],
            'original_shape': image_data['data'].shape,
            'processed_shape': data[0].shape,
            'crop_bbox': bbox,
            'normalization_scheme': config.normalization_scheme,
            'image_file': image_file,
            'mask_file': mask_file,
            'image_path': image_path,
            'mask_path': mask_path,
            'base_name': base_name
        }

        with open(os.path.join(split_dir, f"{base_name}_metadata.pkl"), 'wb') as f:
            pickle.dump(metadata, f)

        return f"成功处理: {base_name}"

    except Exception as e:
        return f"处理失败 {image_file}: {str(e)}"


def create_data_splits_nnunet_style(image_dir, mask_dir, train_ratio=0.8, val_ratio=0.2, test_ratio=0.0):
    """创建数据集划分 - nnUNet风格"""
    print(f"图像目录: {image_dir}")
    print(f"Mask目录: {mask_dir}")

    # 获取所有图像文件
    image_files = [f for f in os.listdir(image_dir) if f.endswith('.nii.gz')]
    mask_files = [f for f in os.listdir(mask_dir) if f.endswith('.nii.gz')]

    image_files.sort()
    mask_files.sort()

    print(f"找到 {len(image_files)} 个图像文件")
    print(f"找到 {len(mask_files)} 个mask文件")

    # 匹配图像和mask文件
    data_pairs = []
    for image_file in image_files:
        # 尝试找到对应的mask文件
        base_name = image_file.replace('.nii.gz', '')

        # 可能的mask文件名模式
        possible_mask_names = [
            f"{base_name}-mask.nii.gz",
            f"{base_name}_mask.nii.gz",            
            f"{base_name}-label.nii.gz",
            f"{base_name}_label.nii.gz",
            f"{base_name}.nii.gz"  # 同名文件
        ]

        mask_file = None
        for possible_name in possible_mask_names:
            if possible_name in mask_files:
                mask_file = possible_name
                break

        if mask_file:
            data_pairs.append((image_file, mask_file))
            print(f"匹配: {image_file} -> {mask_file}")
        else:
            print(f"警告: 未找到 {image_file} 对应的mask文件")

    print(f"成功匹配 {len(data_pairs)} 对文件")
    assert len(data_pairs) > 0, "没有找到匹配的图像-mask对"

    # 随机打乱
    np.random.shuffle(data_pairs)

    # 划分数据集
    n_total = len(data_pairs)
    n_train = int(n_total * train_ratio)
    n_val = int(n_total * val_ratio)

    train_pairs = data_pairs[:n_train]
    val_pairs = data_pairs[n_train:n_train + n_val]
    test_pairs = data_pairs[n_train + n_val:]

    print(f"数据划分: 训练集 {len(train_pairs)}, 验证集 {len(val_pairs)}, 测试集 {len(test_pairs)}")

    return {
        'train': train_pairs,
        'val': val_pairs,
        'test': test_pairs
    }


def preprocess_dataset_nnunet_style(config, use_parallel=True, num_workers=4):
    """nnUNet风格的数据集预处理"""
    print("开始nnUNet风格的数据预处理...")

    # 1. 分析数据集指纹
    dataset_properties = analyze_dataset_fingerprint(config.image_dir, config.mask_dir, config)

    # 2. 创建数据划分
    splits = create_data_splits_nnunet_style(config.image_dir, config.mask_dir)

    # 保存数据划分信息
    with open(os.path.join(config.preprocessed_data_dir, 'splits.pkl'), 'wb') as f:
        pickle.dump(splits, f)

    # 保存数据集属性
    with open(os.path.join(config.preprocessed_data_dir, 'dataset_properties.pkl'), 'wb') as f:
        pickle.dump(dataset_properties, f)

    # 3. 预处理每个数据集
    for split_name, pairs in splits.items():
        split_dir = os.path.join(config.preprocessed_data_dir, split_name)
        os.makedirs(split_dir, exist_ok=True)

        print(f"预处理 {split_name} 数据集 ({len(pairs)} 个样本)...")

        if use_parallel and len(pairs) > 1:
            # 并行处理
            from multiprocessing import Pool

            # 准备参数
            args_list = [(image_file, mask_file, config, dataset_properties, split_dir)
                        for image_file, mask_file in pairs]

            # 使用进程池并行处理
            with Pool(processes=num_workers) as pool:
                results = list(tqdm(
                    pool.starmap(preprocess_single_case_nnunet_style, args_list),
                    total=len(args_list),
                    desc=f"并行处理{split_name}"
                ))

            # 打印结果
            for result in results:
                if "失败" in result:
                    print(result)
        else:
            # 串行处理
            for i, (image_file, mask_file) in enumerate(tqdm(pairs, desc=f"串行处理{split_name}")):
                result = preprocess_single_case_nnunet_style(image_file, mask_file, config, dataset_properties, split_dir)
                if "失败" in result:
                    print(result)

    print("nnUNet风格数据预处理完成!")
    return splits, dataset_properties


class nnUNetDataset(torch.utils.data.Dataset):
    """nnUNet风格的数据集类 - 支持多种格式"""
    def __init__(self, data_dir, patch_size, do_augmentation=True):
        self.data_dir = data_dir
        self.patch_size = patch_size
        self.do_augmentation = do_augmentation

        # 获取所有数据文件 - 支持多种格式
        self.data_files = []
        self.data_format = None

        # 检测数据格式
        files = os.listdir(data_dir)

        # 格式1: nnUNet官方格式 (filename.npz)
        nnunet_files = [f for f in files if f.endswith('.npz') and not f.endswith('_image.npz') and not f.endswith('_label.npz')]

        # 格式2: 我们的格式 (filename_image.npz, filename_label.npz)
        image_files = [f for f in files if f.endswith('_image.npz')]

        if nnunet_files:
            # 使用nnUNet格式
            self.data_format = 'nnunet'
            for file in nnunet_files:
                case_name = file.replace('.npz', '')
                self.data_files.append(case_name)
            print(f"检测到nnUNet格式数据")
        elif image_files:
            # 使用我们的格式
            self.data_format = 'custom'
            for file in image_files:
                case_name = file.replace('_image.npz', '')
                self.data_files.append(case_name)
            print(f"检测到自定义格式数据")
        else:
            raise ValueError(f"在 {data_dir} 中未找到有效的数据文件")

        self.data_files.sort()
        print(f"nnUNet数据集包含 {len(self.data_files)} 个样本 (格式: {self.data_format})")

    def __len__(self):
        return len(self.data_files)

    def __getitem__(self, idx):
        case_name = self.data_files[idx]

        # 根据格式加载数据
        if self.data_format == 'nnunet':
            # nnUNet格式: 数据和标签在同一个文件中
            data_path = os.path.join(self.data_dir, f"{case_name}.npz")
            all_data = np.load(data_path)['data'].astype(np.float32)

            # 分离图像和标签 (最后一个通道是标签)
            image = all_data[:-1]  # [channels, D, H, W]
            label = all_data[-1].astype(np.uint8)  # [D, H, W]

            # 如果只有一个图像通道，移除通道维度然后重新添加
            if image.shape[0] == 1:
                image = image[0]  # [D, H, W]

        elif self.data_format == 'custom':
            # 自定义格式: 图像和标签分别存储
            image_path = os.path.join(self.data_dir, f"{case_name}_image.npz")
            label_path = os.path.join(self.data_dir, f"{case_name}_label.npz")

            image = np.load(image_path)['data'].astype(np.float32)
            label = np.load(label_path)['data'].astype(np.uint8)

        else:
            raise ValueError(f"未知的数据格式: {self.data_format}")

        # 添加通道维度
        if len(image.shape) == 3:
            image = image[np.newaxis, ...]  # [1, D, H, W]

        # 随机裁剪到patch_size
        image, label = self._random_crop(image, label)

        # 数据增强
        if self.do_augmentation:
            image, label = self._augment_nnunet_style(image, label)

        # 转换为tensor
        image = torch.from_numpy(image.astype(np.float32))
        label = torch.from_numpy(label.astype(np.long))

        # 损失函数期望target有通道维度 [B, 1, D, H, W]
        # 所以我们需要添加通道维度
        label = label.unsqueeze(0)  # [D, H, W] -> [1, D, H, W]

        return {
            'data': image,
            'target': label,  # [1, D, H, W]
            'case_name': case_name
        }

    def _random_crop(self, image, label):
        """随机裁剪到指定大小"""
        # image: [C, D, H, W], label: [D, H, W]
        _, d, h, w = image.shape
        target_d, target_h, target_w = self.patch_size

        # 如果图像小于目标大小，进行padding
        pad_d = max(0, target_d - d)
        pad_h = max(0, target_h - h)
        pad_w = max(0, target_w - w)

        if pad_d > 0 or pad_h > 0 or pad_w > 0:
            image = np.pad(image, ((0, 0), (0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            label = np.pad(label, ((0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            d, h, w = image.shape[1:]

        # 随机选择起始位置
        start_d = np.random.randint(0, max(1, d - target_d + 1))
        start_h = np.random.randint(0, max(1, h - target_h + 1))
        start_w = np.random.randint(0, max(1, w - target_w + 1))

        # 裁剪
        image = image[:, start_d:start_d + target_d, start_h:start_h + target_h, start_w:start_w + target_w]
        label = label[start_d:start_d + target_d, start_h:start_h + target_h, start_w:start_w + target_w]

        return image, label

    def _augment_nnunet_style(self, image, label):
        """nnUNet风格的数据增强（增强版）"""
        # 随机翻转
        if np.random.random() < 0.8:
            image = np.flip(image, axis=1).copy()
            label = np.flip(label, axis=0).copy()
        if np.random.random() < 0.8:
            image = np.flip(image, axis=2).copy()
            label = np.flip(label, axis=1).copy()
        if np.random.random() < 0.8:
            image = np.flip(image, axis=3).copy()
            label = np.flip(label, axis=2).copy()
        # 随机旋转（90度的倍数）
        if np.random.random() < 0.8:
            k = np.random.randint(1, 4)
            image = np.rot90(image, k, axes=(2, 3)).copy()
            label = np.rot90(label, k, axes=(1, 2)).copy()
        # 随机仿射变换
        if np.random.random() < 0.5:
            from scipy.ndimage import affine_transform
            scale = np.random.uniform(0.9, 1.1)
            matrix = np.eye(3) * scale
            offset = np.random.uniform(-2, 2, size=3)
            for c in range(image.shape[0]):
                image[c] = affine_transform(image[c], matrix, offset=offset, order=1, mode='nearest')
            label = affine_transform(label, matrix, offset=offset, order=0, mode='nearest')
        # elastic变形
        if np.random.random() < 0.3:
            from scipy.ndimage import gaussian_filter, map_coordinates
            alpha = np.random.uniform(5, 10)
            sigma = np.random.uniform(2, 4)
            shape = image.shape[1:]
            dx = gaussian_filter((np.random.rand(*shape) * 2 - 1), sigma) * alpha
            dy = gaussian_filter((np.random.rand(*shape) * 2 - 1), sigma) * alpha
            dz = gaussian_filter((np.random.rand(*shape) * 2 - 1), sigma) * alpha
            x, y, z = np.meshgrid(np.arange(shape[0]), np.arange(shape[1]), np.arange(shape[2]), indexing='ij')
            indices = np.reshape(x+dx, (-1, 1)), np.reshape(y+dy, (-1, 1)), np.reshape(z+dz, (-1, 1))
            for c in range(image.shape[0]):
                image[c] = map_coordinates(image[c], indices, order=1, mode='reflect').reshape(shape)
            label = map_coordinates(label, indices, order=0, mode='reflect').reshape(shape)
        # 随机强度变化
        if np.random.random() < 0.8:
            factor = np.random.uniform(0.85, 1.15)
            image = image * factor
        # 加性高斯噪声
        if np.random.random() < 0.8:
            noise_std = np.random.uniform(0, 0.15)
            noise = np.random.normal(0, noise_std, image.shape)
            image = image + noise
        # 随机对比度调整
        if np.random.random() < 0.8:
            mean_val = np.mean(image)
            contrast_factor = np.random.uniform(0.85, 1.15)
            image = (image - mean_val) * contrast_factor + mean_val
        # Gamma变换
        if np.random.random() < 0.8:
            gamma = np.random.uniform(0.7, 1.5)
            image_min = np.min(image)
            if image_min < 0:
                image = image - image_min
            image = np.power(image + 1e-8, gamma)
            if image_min < 0:
                image = image + image_min
        # 随机亮度
        if np.random.random() < 0.5:
            brightness = np.random.uniform(-0.1, 0.1)
            image = image + brightness
        return image, label


def create_model_nnunet_style(config):
    """创建nnUNet风格配置的3D-TransUNet模型"""
    print("创建nnUNet风格的3D-TransUNet模型...")

    # 模型参数 - 根据提供的参数更新
    model_params = {
        'patch_size': config.patch_size,
        'is_vit_pretrain': config.is_vit_pretrain,
        'vit_depth': config.vit_depth,
        'vit_hidden_size': config.vit_hidden_size,
        'vit_mlp_dim': 3072,
        'vit_num_heads': 12,
        'max_msda': '',
        'is_max_ms': config.is_max_ms,
        'is_max_ms_fpn': False,
        'max_n_fpn': 4,
        'max_ms_idxs': config.max_ms_idxs,
        'max_ss_idx': 0,
        'is_max_bottleneck_transformer': config.is_max_bottleneck_transformer,
        'max_seg_weight': 1.0,
        'max_hidden_dim': config.max_hidden_dim,
        'max_dec_layers': config.max_dec_layers,
        'mw': config.mw,
        'is_max': config.is_max,
        'is_masked_attn': config.is_masked_attn,
        'is_max_ds': config.is_max_ds,
        'is_masking': config.is_masking,
        'is_masking_argmax': False,
        'is_fam': False,
        'fam_k': 5,
        'fam_reduct_ratio': 8,
        'is_max_hungarian': config.is_max_hungarian,
        'num_queries': config.num_queries,
        'is_max_cls': config.is_max_cls,
        'point_rend': False,
        'num_point_rend': None,
        'no_object_weight': None,
        'is_mhsa_float32': config.is_mhsa_float32,
        'no_max_hw_pe': False,
        'max_infer': None,
        'cost_weight': [2.0, 5.0, 5.0],
        'vit_layer_scale': config.vit_layer_scale,
        'decoder_layer_scale': False
    }

    # 网络参数 - nnUNet风格
    norm_op_kwargs = {'eps': 1e-5, 'affine': True}
    dropout_op_kwargs = {'p': 0.3, 'inplace': True}  # Dropout率提升
    net_nonlin = nn.LeakyReLU
    net_nonlin_kwargs = {'negative_slope': 1e-2, 'inplace': True}

    # 池化和卷积核大小
    num_pool = 5
    pool_op_kernel_sizes = [[2, 2, 2]] * num_pool
    conv_kernel_sizes = [[3, 3, 3]] * (num_pool + 1)

    # 创建模型
    model = Generic_TransUNet_max_ppbp(
        input_channels=config.input_channels,
        base_num_features=config.base_num_features,
        num_classes=config.num_classes,
        num_pool=num_pool,
        num_conv_per_stage=2,
        feat_map_mul_on_downscale=2,
        conv_op=nn.Conv3d,
        norm_op=nn.InstanceNorm3d,
        norm_op_kwargs=norm_op_kwargs,
        dropout_op=nn.Dropout3d,
        dropout_op_kwargs=dropout_op_kwargs,
        nonlin=net_nonlin,
        nonlin_kwargs=net_nonlin_kwargs,
        deep_supervision=config.deep_supervision,
        dropout_in_localization=False,
        final_nonlin=lambda x: x,
        weightInitializer=InitWeights_He(1e-2),
        pool_op_kernel_sizes=pool_op_kernel_sizes,
        conv_kernel_sizes=conv_kernel_sizes,
        upscale_logits=False,
        convolutional_pooling=False,
        convolutional_upsampling=True,
        max_num_features=320,
        **model_params
    )

    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"模型总参数数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")

    return model


def calculate_dice_score(pred, target, num_classes):
    """计算Dice系数"""
    dice_scores = []

    for class_idx in range(1, num_classes):  # 跳过背景类
        pred_class = (pred == class_idx).float()
        target_class = (target == class_idx).float()

        intersection = (pred_class * target_class).sum()
        union = pred_class.sum() + target_class.sum()

        if union == 0:
            dice = 1.0  # 如果该类别在预测和真实标签中都不存在
        else:
            dice = (2.0 * intersection) / union

        dice_scores.append(dice.item() if hasattr(dice, 'item') else dice)

    return dice_scores


def debug_data_distribution(data, target, epoch, batch_idx):
    """调试数据分布"""
    if batch_idx == 0:  # 只在每个epoch的第一个batch打印
        print(f"\n=== Epoch {epoch} 数据分布调试 ===")
        # print(f"数据形状: {data.shape}")
        # print(f"标签形状: {target.shape}")
        # print(f"数据范围: [{data.min():.4f}, {data.max():.4f}]")
        # print(f"数据均值: {data.mean():.4f}, 标准差: {data.std():.4f}")

        # 检查标签分布
        unique_labels = torch.unique(target)
        print(f"标签唯一值: {unique_labels.tolist()}")

        for label_val in unique_labels:
            count = (target == label_val).sum().item()
            percentage = count / target.numel() * 100
            print(f"  标签 {label_val}: {count} 像素 ({percentage:.2f}%)")

        # 检查是否有前景像素
        foreground_pixels = (target > 0).sum().item()
        total_pixels = target.numel()
        fg_percentage = foreground_pixels / total_pixels * 100
        print(f"前景像素: {foreground_pixels}/{total_pixels} ({fg_percentage:.2f}%)")
        print("=" * 40)


def adjust_learning_rate_dynamically(optimizer, epoch, best_dice, current_dice):
    """动态调整学习率"""
    if epoch > 60:  # 在训练后期进行微调
        if current_dice > best_dice * 1.05:  # 如果有显著提升
            # 保持当前学习率
            pass
        elif current_dice < best_dice * 0.95:  # 如果性能下降
            # 降低学习率
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 0.8
                print(f"学习率调整为: {param_group['lr']:.6f}")
        elif epoch % 20 == 0:  # 每20个epoch微调一次
            # 小幅降低学习率
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 0.9
                print(f"定期学习率调整为: {param_group['lr']:.6f}")


def save_checkpoint_nnunet_style(model: nn.Module, optimizer: optim.Optimizer, scaler: GradScaler,
                                epoch: int, loss: float, dice: float, config: nnUNetConfig,
                                filename: str) -> bool:
    """保存模型检查点 - nnUNet风格，增强稳健性"""
    try:
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scaler_state_dict': scaler.state_dict(),
            'loss': loss,
            'dice': dice,
            'config': config.__dict__,
            'timestamp': time.time(),
            'pytorch_version': torch.__version__
        }

        checkpoint_path = os.path.join(config.output_dir, "checkpoints", filename)
        temp_path = checkpoint_path + '.tmp'
        backup_path = checkpoint_path + '.backup'

        # 如果存在旧文件，先备份
        if os.path.exists(checkpoint_path):
            try:
                os.rename(checkpoint_path, backup_path)
            except Exception as e:
                config.logger.warning(f"无法创建备份: {e}")

        # 保存到临时文件
        torch.save(checkpoint, temp_path)

        # 验证文件完整性
        try:
            test_checkpoint = torch.load(temp_path, weights_only=False)
            required_keys = ['model_state_dict', 'epoch', 'optimizer_state_dict', 'scaler_state_dict']
            for key in required_keys:
                if key not in test_checkpoint:
                    raise KeyError(f"缺少必要的键: {key}")
        except Exception as e:
            raise RuntimeError(f"检查点文件验证失败: {e}")

        # 验证成功，重命名临时文件
        os.rename(temp_path, checkpoint_path)

        # 删除备份文件
        if os.path.exists(backup_path):
            os.remove(backup_path)

        # 管理检查点数量
        if config.keep_only_latest_checkpoints:
            _cleanup_old_checkpoints(config)

        config.logger.info(f"模型已保存到: {checkpoint_path}")
        return True

    except Exception as e:
        error_msg = f"保存检查点失败: {e}"
        config.logger.error(error_msg)

        # 清理临时文件
        if os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except:
                pass

        # 恢复备份
        if os.path.exists(backup_path):
            try:
                os.rename(backup_path, checkpoint_path)
                config.logger.info("已恢复备份文件")
            except:
                pass

        return False


def _cleanup_old_checkpoints(config: nnUNetConfig) -> None:
    """清理旧的检查点文件"""
    try:
        checkpoint_dir = os.path.join(config.output_dir, "checkpoints")
        if not os.path.exists(checkpoint_dir):
            return

        # 获取所有检查点文件（排除best.pth和latest.pth）
        checkpoint_files = []
        for f in os.listdir(checkpoint_dir):
            if f.endswith('.pth') and f not in ['best.pth', 'latest.pth']:
                filepath = os.path.join(checkpoint_dir, f)
                mtime = os.path.getmtime(filepath)
                checkpoint_files.append((filepath, mtime))

        # 按修改时间排序，保留最新的几个
        checkpoint_files.sort(key=lambda x: x[1], reverse=True)

        # 删除多余的检查点
        for filepath, _ in checkpoint_files[config.max_checkpoints_to_keep:]:
            try:
                os.remove(filepath)
                config.logger.info(f"删除旧检查点: {filepath}")
            except Exception as e:
                config.logger.warning(f"删除检查点失败 {filepath}: {e}")

    except Exception as e:
        config.logger.warning(f"清理检查点时发生错误: {e}")


class EarlyStopping:
    """早停机制 - nnUNet风格"""
    def __init__(self, patience=15, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_weights = None

    def __call__(self, val_score, model):
        if self.best_score is None:
            self.best_score = val_score
            self.save_checkpoint(model)
        elif val_score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                if self.restore_best_weights:
                    model.load_state_dict(self.best_weights)
                return True
        else:
            self.best_score = val_score
            self.counter = 0
            self.save_checkpoint(model)
        return False

    def save_checkpoint(self, model):
        """保存最佳模型权重"""
        self.best_weights = model.state_dict().copy()


def train_epoch_nnunet_style(model, train_loader, criterion, optimizer, scaler, device, epoch, num_classes, config):
    """nnUNet风格的训练epoch - 参考原始训练循环"""
    model.train()
    total_loss = 0.0
    all_dice_scores = []

    pbar = tqdm(train_loader, desc=f"Epoch {epoch}")
    for batch_idx, batch in enumerate(pbar):
        data = batch['data'].to(device)
        target = batch['target'].to(device)

        # 调试数据分布
        debug_data_distribution(data, target, epoch, batch_idx)

        # 梯度累积：只在第一个batch或累积完成后清零梯度
        if batch_idx % config.gradient_accumulation_steps == 0:
            optimizer.zero_grad()

        # 前向传播
        with autocast('cuda'):
            output = model(data)

            # 处理3D-TransUNet的输出格式
            if isinstance(output, (list, tuple)) and len(output) > 0 and isinstance(output[0], dict):
                # 3D-TransUNet输出：[dict, tensor1, tensor2, ...]
                main_dict = output[0]
                main_output = main_dict['pred_masks']  # [B, num_queries, D, H, W]

                # 对于简化，我们将pred_masks转换为类别预测
                # 这里我们假设前num_classes个查询对应不同的类别
                if main_output.shape[1] >= config.num_classes:
                    # 取前num_classes个查询作为类别预测
                    class_output = main_output[:, :config.num_classes, :, :, :]  # [B, num_classes, D, H, W]
                else:
                    # 如果查询数少于类别数，进行填充
                    padding = torch.zeros(
                        main_output.shape[0], config.num_classes - main_output.shape[1],
                        *main_output.shape[2:], device=main_output.device
                    )
                    class_output = torch.cat([main_output, padding], dim=1)

                # 计算主要损失
                loss = criterion(class_output, target)

                # 添加深度监督损失
                for i, ds_output in enumerate(output[1:], 1):
                    # 需要将深度监督输出上采样到class_output大小
                    class_spatial = class_output.shape[2:]  # [D, H, W]
                    if ds_output.shape[2:] != class_spatial:
                        ds_output = torch.nn.functional.interpolate(
                            ds_output, size=class_spatial, mode='trilinear', align_corners=False
                        )
                    # 同时需要将target下采样到ds_output的原始大小，然后再上采样
                    target_resized = torch.nn.functional.interpolate(
                        target.float().unsqueeze(1), size=ds_output.shape[2:], mode='nearest'
                    ).squeeze(1).long()
                    target_resized = torch.nn.functional.interpolate(
                        target_resized.float().unsqueeze(1), size=class_spatial, mode='nearest'
                    ).squeeze(1).long()
                    loss += criterion(ds_output, target_resized) * (0.5 ** i)

                output = class_output  # 用于评估
            elif isinstance(output, dict):
                # 纯字典输出
                main_output = output['pred_masks']
                loss = criterion(main_output, target)
                output = main_output
            elif isinstance(output, (list, tuple)):
                # 纯张量列表输出 - 简化处理，只使用主要输出
                main_output = output[0]

                # 检查输出维度并调试
                if batch_idx == 0:
                    print(f"列表输出主要张量形状: {main_output.shape}")
                    print(f"目标形状: {target.shape}")

                # 确保输出有正确的类别维度
                if len(main_output.shape) == 4 and main_output.shape[1] != config.num_classes:
                    # 如果输出是 [B, queries, H, W] 格式，需要添加深度维度
                    if main_output.shape[1] >= config.num_classes:
                        main_output = main_output[:, :config.num_classes, :, :]
                    else:
                        # 填充到正确的类别数
                        padding = torch.zeros(
                            main_output.shape[0], config.num_classes - main_output.shape[1],
                            *main_output.shape[2:], device=main_output.device
                        )
                        main_output = torch.cat([main_output, padding], dim=1)

                loss = criterion(main_output, target)
                output = main_output
            else:
                # 单个张量输出
                loss = criterion(output, target)

        # 反向传播（支持梯度累积）
        loss = loss / config.gradient_accumulation_steps  # 缩放损失
        scaler.scale(loss).backward()

        # 每gradient_accumulation_steps步或最后一个batch时更新参数
        if (batch_idx + 1) % config.gradient_accumulation_steps == 0 or batch_idx == len(train_loader) - 1:
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)  # 梯度裁剪
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()

        total_loss += loss.item()

        # 计算Dice系数
        with torch.no_grad():
            pred = torch.argmax(output, dim=1)

            # 调试输出形状
            if batch_idx == 0:
                print(f"输出形状: {output.shape}")
                # print(f"预测形状: {pred.shape}")
                # print(f"目标形状: {target.shape}")
                # print(f"预测唯一值: {torch.unique(pred).tolist()}")

            for i in range(pred.shape[0]):
                # target现在有通道维度 [B, 1, D, H, W]，需要去掉通道维度
                target_i = target[i, 0] if len(target.shape) == 5 else target[i]
                dice_scores = calculate_dice_score(pred[i], target_i, num_classes)
                all_dice_scores.append(dice_scores)

        # 更新进度条
        if batch_idx % 10 == 0:
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Avg Loss': f'{total_loss / (batch_idx + 1):.4f}'
            })

    # 计算平均指标
    avg_loss = total_loss / len(train_loader)

    if all_dice_scores:
        all_dice_scores = np.array(all_dice_scores)
        mean_dice_scores = np.mean(all_dice_scores, axis=0)
        overall_dice = np.mean(mean_dice_scores)
    else:
        mean_dice_scores = [0.0] * (num_classes - 1)
        overall_dice = 0.0

    return avg_loss, overall_dice, mean_dice_scores


def validate_model_nnunet_style(model, val_loader, criterion, device, num_classes):
    """nnUNet风格的模型验证"""
    model.eval()
    total_loss = 0.0
    all_dice_scores = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(val_loader, desc="验证中")):
            data = batch['data'].to(device)
            target = batch['target'].to(device)

            # 前向传播
            with autocast('cuda'):
                output = model(data)

                # 处理输出格式（与训练时相同）
                if isinstance(output, (list, tuple)) and len(output) > 0 and isinstance(output[0], dict):
                    main_dict = output[0]
                    main_output = main_dict['pred_masks']

                    if main_output.shape[1] >= num_classes:
                        class_output = main_output[:, :num_classes, :, :, :]
                    else:
                        padding = torch.zeros(
                            main_output.shape[0], num_classes - main_output.shape[1],
                            *main_output.shape[2:], device=main_output.device
                        )
                        class_output = torch.cat([main_output, padding], dim=1)

                    loss = criterion(class_output, target)

                    for i, ds_output in enumerate(output[1:], 1):
                        class_spatial = class_output.shape[2:]
                        if ds_output.shape[2:] != class_spatial:
                            ds_output = torch.nn.functional.interpolate(
                                ds_output, size=class_spatial, mode='trilinear', align_corners=False
                            )
                        loss += criterion(ds_output, target) * (0.5 ** i)

                    output = class_output
                elif isinstance(output, (list, tuple)):
                    # 纯张量列表输出
                    main_output = output[0]

                    # 确保输出有正确的类别维度
                    if len(main_output.shape) == 4 and main_output.shape[1] != num_classes:
                        if main_output.shape[1] >= num_classes:
                            main_output = main_output[:, :num_classes, :, :]
                        else:
                            padding = torch.zeros(
                                main_output.shape[0], num_classes - main_output.shape[1],
                                *main_output.shape[2:], device=main_output.device
                            )
                            main_output = torch.cat([main_output, padding], dim=1)

                    loss = criterion(main_output, target)
                    output = main_output
                else:
                    loss = criterion(output, target)

            total_loss += loss.item()

            # 计算Dice系数
            pred = torch.argmax(output, dim=1)
            for i in range(pred.shape[0]):
                # target现在有通道维度 [B, 1, D, H, W]，需要去掉通道维度
                target_i = target[i, 0] if len(target.shape) == 5 else target[i]
                dice_scores = calculate_dice_score(pred[i], target_i, num_classes)
                all_dice_scores.append(dice_scores)

    # 计算平均指标
    avg_loss = total_loss / len(val_loader)

    if all_dice_scores:
        all_dice_scores = np.array(all_dice_scores)
        mean_dice_scores = np.mean(all_dice_scores, axis=0)
        overall_dice = np.mean(mean_dice_scores)
    else:
        mean_dice_scores = [0.0] * (num_classes - 1)
        overall_dice = 0.0

    return avg_loss, overall_dice, mean_dice_scores


def load_checkpoint_nnunet_style(model, optimizer, scaler, checkpoint_path):
    """加载模型检查点，增加错误处理"""
    try:
        # 首先检查文件是否存在且大小合理
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")

        file_size = os.path.getsize(checkpoint_path)
        if file_size < 1024:  # 小于1KB的文件可能损坏
            raise RuntimeError(f"检查点文件太小，可能损坏: {file_size} bytes")

        print(f"加载检查点: {checkpoint_path} (大小: {file_size / 1024 / 1024:.1f} MB)")

        # 加载检查点
        checkpoint = torch.load(checkpoint_path, weights_only=False)

        # 验证必要的键存在
        required_keys = ['model_state_dict', 'optimizer_state_dict', 'scaler_state_dict', 'epoch', 'loss', 'dice']
        for key in required_keys:
            if key not in checkpoint:
                raise KeyError(f"检查点文件缺少必要的键: {key}")

        # 加载状态
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        scaler.load_state_dict(checkpoint['scaler_state_dict'])

        return checkpoint['epoch'], checkpoint['loss'], checkpoint['dice']

    except Exception as e:
        print(f"加载检查点失败: {e}")
        print("删除损坏的检查点文件并从头开始训练...")
        if os.path.exists(checkpoint_path):
            os.remove(checkpoint_path)
        raise e


def save_class_metrics_nnunet_style(config, epoch, train_dice_per_class, val_dice_per_class=None):
    """保存每个类别的Dice分数"""
    class_metrics_file = os.path.join(config.output_dir, "class_metrics.csv")

    # 如果文件不存在，创建并写入表头
    if not os.path.exists(class_metrics_file):
        with open(class_metrics_file, 'w') as f:
            header = "epoch"
            for i in range(1, config.num_classes):  # 跳过背景类
                header += f",train_dice_class{i}"
            if val_dice_per_class is not None:
                for i in range(1, config.num_classes):
                    header += f",val_dice_class{i}"
            f.write(header + "\n")

    # 写入当前epoch的数据
    with open(class_metrics_file, 'a') as f:
        line = f"{epoch+1}"
        for dice in train_dice_per_class:
            line += f",{dice:.6f}"
        if val_dice_per_class is not None:
            for dice in val_dice_per_class:
                line += f",{dice:.6f}"
        f.write(line + "\n")


def plot_training_curves_nnunet_style(train_losses, train_dice_scores, val_losses, val_dice_scores, config):
    """绘制训练曲线 - nnUNet风格"""
    plt.figure(figsize=(15, 5))

    # 损失曲线
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='训练损失', color='blue')
    if val_losses:
        val_epochs = [i * config.validation_interval for i in range(len(val_losses))]
        plt.plot(val_epochs, val_losses, label='验证损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('训练和验证损失')
    plt.legend()
    plt.grid(True)

    # Dice系数曲线
    plt.subplot(1, 3, 2)
    plt.plot(train_dice_scores, label='训练Dice', color='blue')
    if val_dice_scores:
        val_epochs = [i * config.validation_interval for i in range(len(val_dice_scores))]
        plt.plot(val_epochs, val_dice_scores, label='验证Dice', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Dice Score')
    plt.title('训练和验证Dice系数')
    plt.legend()
    plt.grid(True)

    # 学习率曲线（PolynomialLR调度）
    plt.subplot(1, 3, 3)
    epochs = range(len(train_losses))
    lrs = []
    for epoch in epochs:
        # PolynomialLR: lr = initial_lr * (1 - epoch/total_epochs)^power
        progress = epoch / config.num_epochs
        lr = config.initial_lr * ((1 - progress) ** 0.9)
        lrs.append(lr)
    plt.plot(epochs, lrs, label='学习率 (PolynomialLR)', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('Learning Rate')
    plt.title('学习率变化 (PolynomialLR)')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(config.output_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"训练曲线已保存到: {os.path.join(config.output_dir, 'training_curves.png')}")


class FocalLoss(nn.Module):
    def __init__(self, gamma=2, alpha=0.25, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction

    def forward(self, inputs, targets):
        bce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * bce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

def train_model_nnunet_style(config):
    """nnUNet风格的主训练函数 - 参考原始训练循环"""
    print("开始nnUNet风格的训练...")

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 创建数据加载器
    train_dataset = nnUNetDataset(
        os.path.join(config.preprocessed_data_dir, 'train'),
        config.patch_size,
        do_augmentation=config.do_data_augmentation
    )

    val_dataset = nnUNetDataset(
        os.path.join(config.preprocessed_data_dir, 'val'),
        config.patch_size,
        do_augmentation=False
    )

    # 创建CSV文件保存训练指标
    metrics_file = os.path.join(config.output_dir, "training_metrics.csv")
    with open(metrics_file, 'w') as f:
        f.write("epoch,train_loss,train_dice,val_loss,val_dice,learning_rate\n")

    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=8,
        pin_memory=True
    )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # 创建模型
    model = create_model_nnunet_style(config)
    model = model.to(device)

    # 创建动态类别权重来处理数据不平衡
    # 根据当前训练进展调整权重，更激进地关注前景类
    class_weights = torch.tensor([0.05, 3.0]).to(device)  # [背景, 前景] - 更激进的权重

    # 创建损失函数 - Dice+Focal组合
    dice_loss = DC_and_CE_loss(
        soft_dice_kwargs={'batch_dice': True, 'smooth': 1e-6, 'do_bg': False},
        ce_kwargs={'weight': class_weights},
        aggregate="sum",
        weight_ce=0.0,
        weight_dice=1.0
    )
    focal_loss = FocalLoss(gamma=2, alpha=0.25)
    def combined_loss(inputs, targets):
        return 0.7 * dice_loss(inputs, targets) + 0.3 * focal_loss(inputs, targets)
    criterion = combined_loss

    # 创建优化器 - 使用AdamW，符合原始3D-TransUNet配置
    optimizer = optim.AdamW(
        model.parameters(),
        lr=5e-5,  # 调整初始学习率
        weight_decay=0.01,  # 增大权重衰减
        betas=(0.9, 0.999),
        eps=1e-8
    )

    # 学习率调度器 - 使用warmup_cosine策略
    from torch.optim.lr_scheduler import CosineAnnealingLR

    # Warmup + Cosine Annealing 调度器 - 进一步优化版本
    warmup_epochs = 5  # warmup阶段缩短为5个epoch

    class WarmupCosineScheduler:
        def __init__(self, optimizer, warmup_epochs, total_epochs, min_lr=1e-7):
            self.optimizer = optimizer
            self.warmup_epochs = warmup_epochs
            self.total_epochs = total_epochs
            self.min_lr = min_lr
            self.base_lr = optimizer.param_groups[0]['lr']

        def step(self, epoch):
            if epoch < self.warmup_epochs:
                # Warmup阶段：线性增长
                lr = self.base_lr * (epoch + 1) / self.warmup_epochs
            else:
                # Cosine annealing阶段
                progress = (epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
                lr = self.min_lr + (self.base_lr - self.min_lr) * 0.5 * (1 + math.cos(math.pi * progress))

            for param_group in self.optimizer.param_groups:
                param_group['lr'] = lr

        def get_last_lr(self):
            return [group['lr'] for group in self.optimizer.param_groups]

    import math
    scheduler = WarmupCosineScheduler(optimizer, warmup_epochs, config.num_epochs)

    # 混合精度训练
    scaler = GradScaler('cuda')

    # TensorBoard日志
    writer = SummaryWriter(os.path.join(config.output_dir, "logs"))

    # 训练历史
    train_losses = []
    train_dice_scores = []
    val_losses = []
    val_dice_scores = []

    best_dice = 0.0
    start_epoch = 0

    # 初始化早停机制
    early_stopping = EarlyStopping(
        patience=config.early_stopping_patience,
        min_delta=config.early_stopping_min_delta,
        restore_best_weights=True
    )

    # 检查是否有预训练模型
    latest_checkpoint = os.path.join(config.output_dir, "checkpoints", "latest.pth")
    if os.path.exists(latest_checkpoint):
        try:
            print("发现预训练模型，继续训练...")
            start_epoch, _, best_dice = load_checkpoint_nnunet_style(model, optimizer, scaler, latest_checkpoint)
            start_epoch += 1
        except Exception as e:
            print(f"加载检查点失败: {e}")
            print("从头开始训练...")
            start_epoch = 0
            best_dice = 0.0

    # 训练循环
    for epoch in range(start_epoch, config.num_epochs):
        print(f"\nEpoch {epoch + 1}/{config.num_epochs}")
        print("-" * 50)

        # 训练
        train_loss, train_dice, train_dice_per_class = train_epoch_nnunet_style(
            model, train_loader, criterion, optimizer, scaler, device, epoch + 1, config.num_classes, config
        )

        train_losses.append(train_loss)
        train_dice_scores.append(train_dice)

        # 验证
        if (epoch + 1) % config.validation_interval == 0:
            val_loss, val_dice, val_dice_per_class = validate_model_nnunet_style(
                model, val_loader, criterion, device, config.num_classes
            )

            val_losses.append(val_loss)
            val_dice_scores.append(val_dice)

            print(f"训练 - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}")
            print(f"验证 - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}")

            # 记录到TensorBoard
            writer.add_scalar('Loss/Train', train_loss, epoch)
            writer.add_scalar('Loss/Val', val_loss, epoch)
            writer.add_scalar('Dice/Train', train_dice, epoch)
            writer.add_scalar('Dice/Val', val_dice, epoch)
            writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], epoch)

            # 保存指标到CSV文件
            with open(os.path.join(config.output_dir, "training_metrics.csv"), 'a') as f:
                f.write(f"{epoch+1},{train_loss:.6f},{train_dice:.6f},{val_loss:.6f},{val_dice:.6f},{optimizer.param_groups[0]['lr']:.8f}\n")

            # 保存每个类别的Dice分数
            save_class_metrics_nnunet_style(config, epoch, train_dice_per_class, val_dice_per_class)

            # 动态学习率调整
            adjust_learning_rate_dynamically(optimizer, epoch, best_dice, val_dice)

            # 保存最佳模型
            if val_dice > best_dice:
                best_dice = val_dice
                save_checkpoint_nnunet_style(
                    model, optimizer, scaler, epoch, val_loss, val_dice, config, "best.pth"
                )
                print(f"新的最佳模型! Dice: {best_dice:.4f}")

            # 早停检查
            if early_stopping(val_dice, model):
                print(f"早停触发！在第 {epoch + 1} 个epoch停止训练")
                print(f"最佳验证Dice: {early_stopping.best_score:.4f}")
                break

        else:
            print(f"训练 - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}")
            writer.add_scalar('Loss/Train', train_loss, epoch)
            writer.add_scalar('Dice/Train', train_dice, epoch)
            writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], epoch)

            # 保存指标到CSV文件（无验证数据）
            with open(os.path.join(config.output_dir, "training_metrics.csv"), 'a') as f:
                f.write(f"{epoch+1},{train_loss:.6f},{train_dice:.6f},,,{optimizer.param_groups[0]['lr']:.8f}\n")

            # 仅保存训练的每个类别Dice分数
            save_class_metrics_nnunet_style(config, epoch, train_dice_per_class)

        # 更新学习率 - warmup_cosine调度器
        scheduler.step(epoch)

        # 保存最新模型
        save_checkpoint_nnunet_style(
            model, optimizer, scaler, epoch, train_loss, train_dice, config, "latest.pth"
        )

    # 关闭TensorBoard
    writer.close()

    # 绘制训练曲线
    plot_training_curves_nnunet_style(train_losses, train_dice_scores, val_losses, val_dice_scores, config)

    # 保存训练摘要
    summary_file = os.path.join(config.output_dir, "training_summary.txt")
    with open(summary_file, 'w') as f:
        f.write(f"训练完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总训练轮数: {config.num_epochs}\n")
        f.write(f"最佳Dice系数: {best_dice:.6f}\n")
        f.write(f"最终训练Loss: {train_loss:.6f}\n")
        f.write(f"最终训练Dice: {train_dice:.6f}\n")
        if val_losses:
            f.write(f"最终验证Loss: {val_losses[-1]:.6f}\n")
            f.write(f"最终验证Dice: {val_dice_scores[-1]:.6f}\n")
        f.write("\n模型配置:\n")
        for key, value in config.__dict__.items():
            f.write(f"{key}: {value}\n")

    print("训练完成!")
    print(f"最佳Dice系数: {best_dice:.4f}")
    print(f"训练指标已保存到: {metrics_file}")
    print(f"训练摘要已保存到: {summary_file}")

    return model


def main():
    """主函数 - nnUNet风格的3D-TransUNet训练 - 优化版本"""
    parser = argparse.ArgumentParser(
        description='nnUNet风格的3D-TransUNet训练脚本 - 优化版本',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # 数据相关参数
    data_group = parser.add_argument_group('数据配置')
    data_group.add_argument('--image_dir', type=str, default='/root/autodl-tmp/120HCC/image/ap',
                           help='图像目录')
    data_group.add_argument('--mask_dir', type=str, default='/root/autodl-tmp/120HCC/mask/ap',
                           help='mask目录')
    data_group.add_argument('--output_dir', type=str, default='/root/autodl-tmp/Transunet_results',
                           help='输出目录')
    data_group.add_argument('--preprocessed_data_dir', type=str, default='/root/autodl-tmp/Transunet_preprocessed',
                           help='预处理数据目录')

    # 模型相关参数
    model_group = parser.add_argument_group('模型配置')
    model_group.add_argument('--num_classes', type=int, default=2,
                            help='类别数量（包括背景）')
    model_group.add_argument('--input_channels', type=int, default=1,
                            help='输入通道数')
    model_group.add_argument('--base_num_features', type=int, default=32,
                            help='基础特征数')

    # 训练相关参数
    train_group = parser.add_argument_group('训练配置')
    train_group.add_argument('--batch_size', type=int, default=4,
                            help='批次大小')
    train_group.add_argument('--num_epochs', type=int, default=200,
                            help='训练轮数')
    train_group.add_argument('--initial_lr', type=float, default=1e-4,
                            help='初始学习率')
    train_group.add_argument('--weight_decay', type=float, default=1e-3,
                            help='权重衰减')
    train_group.add_argument('--validation_interval', type=int, default=2,
                            help='验证间隔（每N个epoch验证一次）')
    train_group.add_argument('--save_checkpoint_interval', type=int, default=50,
                            help='保存检查点间隔')

    # 数据处理相关参数
    process_group = parser.add_argument_group('数据处理配置')
    process_group.add_argument('--patch_size', nargs=3, type=int, default=[64, 192, 192],
                              help='补丁大小 [D, H, W]')
    process_group.add_argument('--target_spacing', nargs=3, type=float, default=[1.0, 1.0, 1.0],
                              help='目标spacing [x, y, z]')
    process_group.add_argument('--normalization_scheme', type=str, default='noNorm',
                              choices=['CT', 'CT2', 'noNorm', 'MRI'],
                              help='归一化方案')
    process_group.add_argument('--crop_to_nonzero', action='store_true', default=True,
                              help='是否裁剪到非零区域')
    process_group.add_argument('--parallel_workers', type=int, default=8,
                              help='并行处理的进程数')

    # 运行模式参数
    mode_group = parser.add_argument_group('运行模式')
    mode_group.add_argument('--preprocess_only', action='store_true',
                           help='仅进行数据预处理')
    mode_group.add_argument('--resume_training', action='store_true',
                           help='继续训练')
    mode_group.add_argument('--debug', action='store_true',
                           help='调试模式')
    mode_group.add_argument('--log_level', type=str, default='INFO',
                           choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                           help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    log_level = getattr(logging, args.log_level.upper())
    logger.setLevel(log_level)

    try:
        # 创建nnUNet风格的配置
        config = nnUNetConfig(logger)

        # 从命令行参数更新配置
        config.image_dir = args.image_dir
        config.mask_dir = args.mask_dir
        config.output_dir = args.output_dir
        config.preprocessed_data_dir = args.preprocessed_data_dir
        config.num_classes = args.num_classes
        config.input_channels = args.input_channels
        config.base_num_features = args.base_num_features
        config.batch_size = args.batch_size
        config.num_epochs = args.num_epochs
        config.initial_lr = args.initial_lr
        config.weight_decay = args.weight_decay
        config.patch_size = args.patch_size
        config.target_spacing = args.target_spacing
        config.normalization_scheme = args.normalization_scheme
        config.crop_to_nonzero = args.crop_to_nonzero
        config.validation_interval = args.validation_interval
        config.save_checkpoint_interval = args.save_checkpoint_interval

        logger.info("nnUNet风格的3D-TransUNet训练脚本 - 优化版本")
        logger.info("=" * 60)
        logger.info(f"图像目录: {config.image_dir}")
        logger.info(f"Mask目录: {config.mask_dir}")
        logger.info(f"输出目录: {config.output_dir}")
        logger.info(f"预处理数据目录: {config.preprocessed_data_dir}")
        logger.info(f"类别数量: {config.num_classes}")
        logger.info(f"批次大小: {config.batch_size}")
        logger.info(f"训练轮数: {config.num_epochs}")
        logger.info(f"补丁大小: {config.patch_size}")
        logger.info(f"目标spacing: {config.target_spacing}")
        logger.info(f"归一化方案: {config.normalization_scheme}")
        logger.info(f"裁剪到非零区域: {config.crop_to_nonzero}")
        logger.info(f"调试模式: {args.debug}")
        logger.info("=" * 60)

        # 验证配置
        if not config.validate_config():
            logger.error("配置验证失败，程序退出")
            return 1

        # 创建输出目录
        if not setup_directories(config):
            logger.error("创建输出目录失败，程序退出")
            return 1

        # 打印详细配置摘要
        config.print_config_summary()

        # 验证配置是否与参考参数匹配
        config.validate_against_reference_params()

        # 检查GPU可用性
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"CUDA可用，GPU数量: {gpu_count}")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            logger.warning("CUDA不可用，将使用CPU训练（速度较慢）")

    except Exception as e:
        logger.error(f"初始化配置时发生错误: {e}")
        if args.debug:
            logger.error(traceback.format_exc())
        return 1

    try:
        # nnUNet风格的数据预处理
        splits_file = os.path.join(config.preprocessed_data_dir, 'splits.pkl')
        properties_file = os.path.join(config.preprocessed_data_dir, 'dataset_properties.pkl')

        if not os.path.exists(splits_file) or not os.path.exists(properties_file):
            logger.info("开始nnUNet风格的数据预处理...")
            splits, dataset_properties = preprocess_dataset_nnunet_style(
                config, use_parallel=True, num_workers=args.parallel_workers
            )
        else:
            logger.info("发现预处理数据，跳过预处理步骤")
            try:
                with open(splits_file, 'rb') as f:
                    splits = pickle.load(f)
                with open(properties_file, 'rb') as f:
                    dataset_properties = pickle.load(f)
                logger.info("成功加载预处理数据")
            except Exception as e:
                logger.error(f"加载预处理数据失败: {e}")
                logger.info("重新进行数据预处理...")
                splits, dataset_properties = preprocess_dataset_nnunet_style(
                    config, use_parallel=True, num_workers=args.parallel_workers
                )

        if args.preprocess_only:
            logger.info("仅预处理模式，程序结束")
            return 0

        # 开始训练
        logger.info("开始模型训练...")
        model = train_model_nnunet_style(config)
        logger.info("nnUNet风格训练成功完成!")
        return 0

    except KeyboardInterrupt:
        logger.info("\n训练被用户中断")
        return 130  # SIGINT exit code
    except Exception as e:
        logger.error(f"训练过程中发生错误: {str(e)}")
        if args.debug:
            logger.error(traceback.format_exc())
        return 1
    finally:
        # 清理资源
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        logger.info("程序结束")

if __name__ == "__main__":
    """
    使用说明:
    1. 基本训练: python 3d_transunet模型训练加nnunet预处理最终版.py
    2. 仅预处理: python 3d_transunet模型训练加nnunet预处理最终版.py --preprocess_only
    3. 继续训练: python 3d_transunet模型训练加nnunet预处理最终版.py --resume_training
    4. 调试模式: python 3d_transunet模型训练加nnunet预处理最终版.py --debug --log_level DEBUG
    5. 自定义参数: python 3d_transunet模型训练加nnunet预处理最终版.py --batch_size 2 --num_epochs 100

    数据格式要求:
    - 图像和标签都应该是.nii.gz格式
    - 图像和标签文件名应该能够匹配（支持-mask后缀）
    - 图像应该是3D医学图像格式

    优化特性:
    - 增强的错误处理和日志记录
    - 自动配置验证
    - 内存优化的数据加载
    - 智能检查点管理
    - 动态学习率调整
    - GPU内存监控
    """

    # 设置环境变量
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['OPENBLAS_NUM_THREADS'] = '1'

    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"程序执行失败: {e}")
        traceback.print_exc()
        sys.exit(1)
