
#%%totalsegmentator 自动分割脏器，包括肝脏，单个病例分割
# https://github.com/wasserth/TotalSegmentator
# https://github.com/wasserth/TotalSegmentator?tab=readme-ov-file
# pip install TotalSegmentator
# TotalSegmentator -h
import os
import nibabel as nib
from totalsegmentator.python_api import totalsegmentator

if __name__ == "__main__":
    input_path = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpimg\baizhengqiang-hbp.nii.gz'
    output_path = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpseg\baizhengqiang-hbp.nii.gz'
    
    # 指定要分割的器官
    organs_to_segment = ['liver']
    
    # 调用totalsegmentator函数，并传递roi_subset参数
    totalsegmentator(input_path, output_path, roi_subset=organs_to_segment)  
    print(f"Segmentation completed. Output saved to {output_path}")

#%%totalsegmentator自动分割脏器，多个病例批量分割1，分割后文件位于同一个文件夹 
# pip install TotalSegmentator 安装
# TotalSegmentator是在大型数据集上使用nnUNet V2训练的一个AI模型，
# 可以在CT数据上自动分割全身117个器官！同时还可以分割部分血管，脑出血，胸腔积液等
#只能使用cpu，可设置快速模式
import os
import glob
from totalsegmentator.python_api import totalsegmentator
help(totalsegmentator)

if __name__ == "__main__":
    # input_dir = '/root/autodl-tmp/hbpimg'
    # output_dir = '/root/autodl-tmp/hbpseg'

    # input_dir = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpimg'
    # output_dir = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpseg'

    input_dir = '/root/autodl-tmp/ap22'
    output_dir = '/root/autodl-tmp/apseg'

    organs_to_segment = ['liver','spleen'] #liver_vessels

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 获取输入目录下的所有 .nii.gz 文件
    nii_files = glob.glob(os.path.join(input_dir, '*.nii.gz'))

    # 对每个 .nii.gz 文件调用 totalsegmentator 函数
    for nii_file in nii_files:
        filename = os.path.basename(nii_file)        
        output_path = os.path.join(output_dir, filename)        
        try:
            print(f"Processing {nii_file}...")
            totalsegmentator(nii_file, output_dir, 
                             roi_subset=organs_to_segment,
                            #  task='total', #ct
                             task='total_mr',
                             radiomics=False,
                             fast=False)
            
            # 移动并重命名分割后的文件
            for organ in organs_to_segment:
                segmented_file_path = os.path.join(output_dir, f'{organ}.nii.gz')
                new_segmented_file_path = os.path.join(output_dir, f'{filename[:-7]}-{organ}.nii.gz')
                os.rename(segmented_file_path, new_segmented_file_path)
            print(f"Saved segmented output to {new_segmented_file_path}")
        except Exception as e:
            print(f"An error occurred while processing {nii_file}: {e}")
            import traceback
            print(traceback.format_exc())

    print("Finish")

#%%totalsegmentator 自动分割脏器，多个病例批量分割2，每个病例单独文件夹 
# pip install TotalSegmentator
import os
import glob
from totalsegmentator.python_api import totalsegmentator

if __name__ == "__main__":
    input_dir = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpimg'
    output_dir = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpseg'
    organs_to_segment = ['liver']

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 获取输入目录下的所有 .nii.gz 文件
    nii_files = glob.glob(os.path.join(input_dir, '*.nii.gz'))

    # 对每个 .nii.gz 文件调用 totalsegmentator 函数
    for nii_file in nii_files:
        filename = os.path.basename(nii_file)        
        output_path = os.path.join(output_dir, filename)        
        try:
            print(f"Processing {nii_file}...")
            totalsegmentator(nii_file, output_dir, 
                             roi_subset=organs_to_segment,
                            #  task='total',
                             radiomics=False,fast=False)       
        except Exception as e:
            print(f"An error occurred while processing {nii_file}: {e}")
            import traceback
            print(traceback.format_exc())

    print("Finish")
    
#%%totalsegmentator自动分割肝脏和肿瘤，多个病例批量分割
# 先分割肝脏（liver），然后根据分割后的掩膜提取肝脏图像，
# 最后在该图像中分割肝脏血管（liver_vessels)
import os
import glob
import numpy as np
import nibabel as nib
import subprocess
from totalsegmentator.python_api import totalsegmentator

if __name__ == "__main__":
    input_dir = '/root/autodl-tmp/ap22'
    output_dir = '/root/autodl-tmp/apseg'

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    nii_files = glob.glob(os.path.join(input_dir, '*.nii.gz'))

    for nii_file in nii_files:
        filename = os.path.basename(nii_file)        
        try:
            print(f"Processing {nii_file}...")

            # 第一步：分割肝脏
            totalsegmentator(nii_file, output_dir, 
                             roi_subset=['liver'],
                             radiomics=False,
                             fast=False)
            
            liver_mask_path = os.path.join(output_dir, 'liver.nii.gz')
            liver_mask = nib.load(liver_mask_path).get_fdata()

            original_image = nib.load(nii_file).get_fdata()
            liver_image = original_image * (liver_mask > 0)

            liver_image_nifti = nib.Nifti1Image(liver_image, affine=nib.load(nii_file).affine)
            liver_image_output_path = os.path.join(output_dir, f'{filename[:-7]}-liver_image.nii.gz')
            nib.save(liver_image_nifti, liver_image_output_path)

                  
            # 第二步：在肝脏图像中分割肝脏肿瘤
            task = 'liver_vessels'
            command = f'TotalSegmentator -i "{liver_image_output_path}" -o "{output_dir}" --task {task}'
            subprocess.run(command, shell=True, check=True)

            # 检查分割后的肿瘤文件是否存在
            segmented_tumor_path = os.path.join(output_dir, 'liver_tumor.nii.gz')
            new_segmented_tumor_path = os.path.join(output_dir, f'{filename[:-7]}-liver_tumor.nii.gz')
            if os.path.exists(segmented_tumor_path):
                os.rename(segmented_tumor_path, new_segmented_tumor_path)
                print(f"Saved segmented liver tumor output to {new_segmented_tumor_path}")
            else:
                print(f"Segmented tumor file not found: {segmented_tumor_path}")

        except Exception as e:
            print(f"An error occurred while processing {nii_file}: {e}")
            import traceback
            print(traceback.format_exc())

    print("Finish")

#%% 使用 subprocess 模块在Python代码中调用命令行批量预测，成功
import os
import subprocess

input_dir = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpimg'
output_dir = r'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\hbpseg'
task = 'liver_vessels' #liver_vessels 可分割血管和肿瘤

# 遍历输入目录中的所有 .nii.gz 文件
for filename in os.listdir(input_dir):
    print(filename)
    if filename.endswith('.nii.gz'):
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, filename.replace('.nii.gz', ''))
        
        # 构建命令
        command = f'TotalSegmentator -i "{input_path}" -o "{output_path}" --task {task}'
        
        # 执行命令
        subprocess.run(command, shell=True)

#%%命令行进行totalsegmentator分割
# TotalSegmentator -i 'H:\1.HCC-VETC\734HCC\all-HCC\490HCC-suzhou\490HCC\image\ap\baizhengqiang-ap.nii.gz' -o 'H:\1.HCC-VETC\734HCC\baizhengqiang-ap.nii.gz' -rs liver

# For CT images:
TotalSegmentator -i ct.nii.gz -o segmentations
TotalSegmentator -i ct.nii.gz -o segmentations -ta <task_name>
# For MR images:
TotalSegmentator -i mri.nii.gz -o segmentations --task total_mr

# MR：
TotalSegmentator -i 'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\ap\caiyumei-ap.nii.gz' -o 'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\apseg' --task total_mr
TotalSegmentator -i 'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\ap\caopeide-ap.nii.gz' -o 'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\apseg' --task liver_vessels
TotalSegmentator -i 'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\ap\caopeide-ap.nii.gz' -o 'K:\734HCC\all-HCC\490HCC-suzhou\490HCC\image\apseg' --task liver

# 报错
# ITK loading Error When you get the following error message

# ITK ERROR: ITK only supports orthonormal direction cosines. No orthonormal definition was found!
# you should do

# pip install SimpleITK==2.0.2
# Alternatively you can try

# fslorient -copysform2qform input_file
# fslreorient2std input_file output_file

#%% 看一下totalsegmentator_snomed_mapping.csv文件，查看全部的分割器官
# Totalsegmentator可以分割哪些器官
# spleen(脾)，kidney_right(右肾)，kidney_left(左肾)，
# liver(肝)，liver_tumor；gallbladder(胆囊)，
# stomach(胃)，
# pancreas(胰腺)，adrenal_gland_right(右肾上腺)，
# adrenal_gland_left(左肾上腺)，lung_upper_lobe_left(左肺上叶)，
# lung_lower_lobe_left(左肺下叶)，lung_upper_lobe_right(右肺上叶)，
# lung_middle_lobe_right(右肺中叶)，lung_lower_lobe_right(右肺下叶)，
# esophagus(食管)，trachea(气管)，
# thyroid_gland(甲状腺)，small_bowel(小肠)，
# duodenum(十二指肠)， colon(结肠)，
# urinary_bladder(膀胱)，prostate(前列腺)，
# kidney_cyst_left(左肾囊肿)，kidney_cyst_right(右肾囊肿)，
# sacrum(骶骨)，vertebraeC1-S1(椎体，颈1椎体为C1类，胸1椎体为T1类，腰1椎体为L1类，依次类推)，
# heart(心脏)，aorta(主动脉)，
# pulmonary_vein(肺静脉)，brachiocephalic_trunk(颈总动脉干)，
# subclavian_artery_right(右锁骨下动脉)，subclavian_artery_left(左锁骨下动脉)，
# common_carotid_artery_right(右颈总动脉)，common_carotid_artery_left(左颈总动脉)，
# brachiocephalic_vein_left(左肱头静脉)，brachiocephalic_vein_right(右肱头静脉)，
# atrial_appendage_left(左心房附属物)，
# superior_vena_cava(上腔静脉)，inferior_vena_cava(下腔静脉)，
# portal_vein_and_splenic_vein(门静脉和脾静脉)，
# iliac_artery_left(左髂动脉)，iliac_artery_right(右髂动脉)，
# iliac_vena_left(左髂静脉)，iliac_vena_right(右髂静脉)，
# humerus_left(左肱骨)，humerus_right(右肱骨)，
# scapula_left(左肩胛骨)，scapula_right(右肩胛骨)，
# clavicula_left(左锁骨)，clavicula_right(右锁骨)，
# femur_left(左股骨)，femur_right(右股骨)，
# hip_left(左髋)，hip_right(右髋)，
# spinal_cord(脊髓)，
# gluteus_maximus_left(左臀大肌)，gluteus_maximus_right(右臀大肌)，
# gluteus_medius_left(左臀中肌)，gluteus_medius_right(右臀中肌)，
# gluteus_minimus_left(左臀小肌)，gluteus_minimus_right(右臀小肌)，
# autochthon_left(左本位肌)，autochthon_right(右本位肌)，
# iliopsoas_left(左髂腰肌)，iliopsoas_right(右髂腰肌)，
# brain(大脑)，skull(头骨)，
# rib_left_1-12(左肋骨1-12)，rib_right_1-12(右肋骨1-12)
# sternum(胸骨)，costal_cartilages(肋软骨)
# CT total：默认任务，包含117个主要类别（详情见类别详情）
# total_mr：默认任务，在MRI图像上包含56个主要类别（详情见类目详情）

#%% pip install monailabel

import requests
import os

# 设置服务器地址
server_url = "http://127.0.0.1:8000"

# 设置要处理的影像文件夹
studies_path = r"H:\1.HCC-VETC\monailabel-data"

# 获取所有影像文件
files = [f for f in os.listdir(studies_path) if os.path.isfile(os.path.join(studies_path, f))]

for file in files:
    file_path = os.path.join(studies_path, file)
    
    # 上传影像文件到服务器
    with open(file_path, 'rb') as f:
        response = requests.post(f"{server_url}/datastore", files={'file': f})
    
    image_id = response.json().get('id')
    
    # 请求分割任务
    response = requests.post(f"{server_url}/infer/{image_id}/segmentation")
    
    # 下载分割结果
    output_path = os.path.join(studies_path, f"seg_{file}")
    with open(output_path, 'wb') as f:
        f.write(response.content)

print("批量分割完成")

# %%
# %%docker pull projectmonai/monailabel
#docker run --rm -it -p 6006:6006/tcp -p 8888:8888/tcp projectmonai/monailabel:latest 
import os
import requests

# 设置 MONAILabel 服务器地址
MONAILABEL_SERVER_URL = "http://192.168.1.100:8000"  # 根据实际情况调整

# 设置数据路径和输出路径
# data_path = r"H:\1.HCC-VETC\734HCC\all-HCC\10HCC-taicang\image\ap"
# output_path =r"H:\1.HCC-VETC\734HCC\all-HCC\10HCC-taicang\image\apseg"

data_path = '/mnt/data/ap'
output_path = '/mnt/data/apseg'


# 确保输出路径存在
# os.makedirs(output_path, exist_ok=True)

# 获取所有 .nii.gz 文件
nii_files = [f for f in os.listdir(data_path) if f.endswith('.nii.gz')]

# 批量分割
for nii_file in nii_files:
    input_file_path = os.path.join(data_path, nii_file)
    output_file_path = os.path.join(output_path, nii_file)

    # 构建请求数据
    files = {'file': open(input_file_path, 'rb')}
    params = {'model': 'segmentation_model'}  # 根据实际情况调整模型名称

    # 发送请求到 MONAILabel 服务器
    response = requests.post(f"{MONAILABEL_SERVER_URL}/infer", files=files, params=params)

    # 保存分割结果
    if response.status_code == 200:
        with open(output_file_path, 'wb') as f:
            f.write(response.content)
        print(f"Segmentation for {nii_file} saved successfully.")
    else:
        print(f"Failed to segment {nii_file}. Status code: {response.status_code}")

print("Batch segmentation completed!")
# %% 上传数据到docker容器的路径中，只能在docker容器外运行这段代码
import os
import requests
import subprocess

# 本地路径
data_path = r"H:\1.HCC-VETC\734HCC\all-HCC\10HCC-taicang\image\ap"
output_path = r"H:\1.HCC-VETC\734HCC\all-HCC\10HCC-taicang\image\apseg"
container_id = "0d79642aaf8d"   # 替换为实际容器 ID 或名称

# 构建命令
command_data = f'docker cp "{data_path}" {container_id}:/mnt/data/ap'
command_output = f'docker cp "{output_path}" {container_id}:/mnt/data/apseg'

# 执行命令
subprocess.run(command_data, shell=True, check=True)
subprocess.run(command_output, shell=True, check=True)

# %%
import torch
import torch
print(torch.__version__)
# %% SAM 2: Segment Anything Model 2
#网址https://docs.ultralytics.com/models/sam-2/
# 方法二：基于ultralytics包的封装来调用
# 【1】安装必要的包。
# 安装ultralytics并确保其版本>=8.2.70，torch版本也需>=2.0或使用最新版本pip install -U ultralytics
# 【2】下载分割模型。下面网址中提供了4个模型，需要下载
# https://docs.ultralytics.com/models/sam-2/

from ultralytics import ASSETS, SAM

# Load a model
model = SAM(r"E:\1.githubcode\MedSAMSlicer-SAM2\sam2_s.pt")
# Display model information (optional)
model.info()

# results=model(r'E:\1.githubcode\MedSAMSlicer-SAM2\hcc.jpg')# 图片推理

# 格式：[x_min, y_min, x_max, y_max]，表示矩形框的左上角和右下角坐标
# results = model(r'E:\1.githubcode\MedSAMSlicer-SAM2\hcc.jpg', bboxes=[100, 194, 207, 283])

# 格式：[x, y]，表示点的坐标
results = model(r'E:\1.githubcode\MedSAMSlicer-SAM2\hcc.jpg', points=[137, 215], labels=[1])

# results = model(r'E:\1.githubcode\MedSAMSlicer-SAM2\output.avi') # 视频推理

for result in results:    
    result.show()


#%% Totalsegmentator MRI 全自动分割 total_mr任务
import os
import torch
import nibabel as nib
import matplotlib.pyplot as plt
from totalsegmentator.python_api import totalsegmentator
from totalsegmentator.nifti_ext_header import load_multilabel_nifti
from totalsegmentator.map_to_binary import class_map
print(class_map["total_mr"])  # 打印total_mr任务支持的所有器官

def process_mri_image(input_path, output_dir):
    """完整的MRI处理流程"""
    
    # 1. 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 2. 执行分割
    print("开始分割MRI图像...")
    totalsegmentator(
        input_path, 
        output_dir,
        task="total_mr",
        fast=True,  # 使用快速模式加快处理
        preview=True,  # 生成3D预览
        statistics=True,  # 生成统计数据
        ml=True  # 生成单个包含所有标签的nifti文件
    )
    
    # 3. 查看生成的文件
    print("\n生成的文件:")
    for file in os.listdir(output_dir):
        print(f"- {file}")
    
    # 4. 如果生成了多标签nifti文件，加载它并查看标签映射
    multilabel_file = os.path.join(output_dir, "segmentations.nii.gz")
    if os.path.exists(multilabel_file):
        print("\n加载多标签分割结果...")
        seg_img, label_map = load_multilabel_nifti(multilabel_file)
        print("可用的解剖结构标签:")
        for label_id, name in label_map.items():
            print(f"ID {label_id}: {name}")
    
    # 5. 加载并显示预览图像（如果生成）
    preview_file = os.path.join(output_dir, "preview.png")
    if os.path.exists(preview_file):
        print("\n显示预览图像...")
        img = plt.imread(preview_file)
        plt.figure(figsize=(10, 10))
        plt.imshow(img)
        plt.axis('off')
        plt.title("分割结果预览")
        plt.show()
    
    print("\nMRI处理完成!")

# 使用示例
if __name__ == "__main__":
    input_file = "path/to/your/mri.nii.gz"
    output_folder = "path/to/output/directory"
    process_mri_image(input_file, output_folder)

#%% Totalsegmentator MRI 全自动分割，total_mr模式指定器官，单个患者分割
# 根据最新github进行调试
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import os
import torch
import nibabel as nib
from totalsegmentator.python_api import totalsegmentator
import matplotlib.pyplot as plt

def segment_liver_and_spleen_mr(input_path, output_dir):
    """
    专门分割MRI图像中的肝脏和脾脏
    
    参数:
        input_path: MRI图像路径（.nii.gz格式）
        output_dir: 分割结果输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义要分割的器官 - 仅肝脏和脾脏
    organs_to_segment = ["liver", "spleen"]
    
    print(f"开始分割MRI图像中的器官: {', '.join(organs_to_segment)}")
    
    # 执行分割
    totalsegmentator(
        input_path, 
        output_dir,
        task="total_mr",  # 使用MRI专用任务
        roi_subset=organs_to_segment,  # 只分割肝脏和脾脏
        statistics=True,  # 生成体积和强度统计数据
        device="gpu" if torch.cuda.is_available() else "cpu"
    )
    
    # 输出分割结果统计
    stats_file = os.path.join(output_dir, "statistics.json")
    if os.path.exists(stats_file):
        import json
        with open(stats_file, 'r') as f:
            stats = json.load(f)
        
        print("\n分割结果统计:")
        for organ, data in stats.items():
            print(f"{organ}:")
            print(f"  体积: {data['volume_mm3']:.2f} mm³")
            print(f"  平均强度: {data['mean_intensity']:.2f}")
    
    print(f"\n分割完成! 结果保存在: {output_dir}")
    
    # 返回各器官的分割结果路径
    result_paths = {
        organ: os.path.join(output_dir, f"{organ}.nii.gz")
        for organ in organs_to_segment
    }
    
    return result_paths

def visualize_segmentation(original_mri_path, segmentation_paths):
    """
    可视化原始MRI和分割结果的中间切片
    
    参数:
        original_mri_path: 原始MRI图像路径
        segmentation_paths: 各器官分割结果的路径字典
    """
    # 加载原始MRI
    orig_img = nib.load(original_mri_path)
    orig_data = orig_img.get_fdata()
    
    # 获取中间切片索引
    middle_slice = orig_data.shape[2] // 2
    
    # 创建图像
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 显示原始MRI
    axes[0].imshow(orig_data[:, :, middle_slice].T, cmap='gray')
    axes[0].set_title('原始MRI')
    axes[0].axis('off')
    
    # 显示肝脏分割
    liver_data = nib.load(segmentation_paths["liver"]).get_fdata()
    axes[1].imshow(orig_data[:, :, middle_slice].T, cmap='gray')
    axes[1].imshow(liver_data[:, :, middle_slice].T, cmap='Reds', alpha=0.5)
    axes[1].set_title('肝脏分割')
    axes[1].axis('off')
    
    # 显示脾脏分割
    spleen_data = nib.load(segmentation_paths["spleen"]).get_fdata()
    axes[2].imshow(orig_data[:, :, middle_slice].T, cmap='gray')
    axes[2].imshow(spleen_data[:, :, middle_slice].T, cmap='Blues', alpha=0.5)
    axes[2].set_title('脾脏分割')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(os.path.dirname(segmentation_paths["liver"]), "segmentation_visualization.png"))
    plt.show()

# 使用示例
def main():
    input_file = r"K:\734HCC\all-HCC\234HCC-nantong\image\ap\baohanqing-ap.nii.gz"
    output_dir =  r"K:\734HCC\all-HCC\234HCC-nantong\image\baohanqing-apseg"
    
    # 执行分割
    result_paths = segment_liver_and_spleen_mr(input_file, output_dir)
    
    # 可视化结果
    visualize_segmentation(input_file, result_paths)
    
    # 打印各器官分割结果的路径
    print("\n分割结果文件:")
    for organ, path in result_paths.items():
        print(f"{organ}: {path}")

if __name__ == "__main__":
    main()

#%% Totalsegmentator MRI 全自动分割，total_mr模式指定器官,患者批量分割
# 根据最新github进行调试
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import torch
import nibabel as nib
from totalsegmentator.python_api import totalsegmentator
import matplotlib.pyplot as plt
import glob
import json
from totalsegmentator.map_to_binary import class_map
print(class_map["total_mr"])  # 打印total_mr任务支持的所有器官

def segment_liver_and_spleen_mr(input_path, output_dir):
    """
    专门分割MRI图像中的肝脏和脾脏
    
    参数:
        input_path: MRI图像路径（.nii.gz格式）
        output_dir: 分割结果输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义要分割的器官 - 仅肝脏和脾脏
    organs_to_segment = ["liver", "spleen"]
    
    print(f"开始分割MRI图像中的器官: {', '.join(organs_to_segment)}")
    
    # 执行分割
    totalsegmentator(
        input_path, 
        output_dir,
        task="total_mr",  # 使用MRI专用任务
        roi_subset=organs_to_segment,  # 只分割肝脏和脾脏
        statistics=True,  # 生成体积和强度统计数据
        device="gpu" if torch.cuda.is_available() else "cpu"
    )
    
    # 输出分割结果统计
    stats_file = os.path.join(output_dir, "statistics.json")
    if os.path.exists(stats_file):
        with open(stats_file, 'r') as f:
            stats = json.load(f)
        
        print("\n分割结果统计:")
        for organ, data in stats.items():
            print(f"{organ}:")
            print(f"  体积: {data['volume_mm3']:.2f} mm³")
            print(f"  平均强度: {data['mean_intensity']:.2f}")
    
    print(f"\n分割完成! 结果保存在: {output_dir}")
    
    # 返回各器官的分割结果路径
    result_paths = {
        organ: os.path.join(output_dir, f"{organ}.nii.gz")
        for organ in organs_to_segment
    }
    
    return result_paths

def visualize_segmentation(original_mri_path, segmentation_paths):
    """
    可视化原始MRI和分割结果的中间切片
    
    参数:
        original_mri_path: 原始MRI图像路径
        segmentation_paths: 各器官分割结果的路径字典
    """
    # 加载原始MRI
    orig_img = nib.load(original_mri_path)
    orig_data = orig_img.get_fdata()
    
    # 获取中间切片索引
    middle_slice = orig_data.shape[2] // 2
    
    # 创建图像
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 显示原始MRI
    axes[0].imshow(orig_data[:, :, middle_slice].T, cmap='gray')
    axes[0].set_title('原始MRI')
    axes[0].axis('off')
    
    # 显示肝脏分割
    liver_data = nib.load(segmentation_paths["liver"]).get_fdata()
    axes[1].imshow(orig_data[:, :, middle_slice].T, cmap='gray')
    axes[1].imshow(liver_data[:, :, middle_slice].T, cmap='Reds', alpha=0.5)
    axes[1].set_title('肝脏分割')
    axes[1].axis('off')
    
    # 显示脾脏分割
    spleen_data = nib.load(segmentation_paths["spleen"]).get_fdata()
    axes[2].imshow(orig_data[:, :, middle_slice].T, cmap='gray')
    axes[2].imshow(spleen_data[:, :, middle_slice].T, cmap='Blues', alpha=0.5)
    axes[2].set_title('脾脏分割')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "segmentation_visualization.png"))
    plt.show()

def batch_process_mr_images(input_dir, output_base_dir):
    """
    批量处理目录中的所有MRI图像，分割肝脏和脾脏
    
    参数:
        input_dir: 包含MRI图像(.nii.gz)的目录
        output_base_dir: 存放分割结果的基础目录
    """
    # 获取所有nii.gz文件
    mri_files = glob.glob(os.path.join(input_dir, "*.nii.gz"))
    
    if not mri_files:
        print(f"在{input_dir}中未找到.nii.gz文件")
        return
    
    print(f"找到{len(mri_files)}个MRI文件待处理")
    
    # 处理每个文件
    for file_path in mri_files:
        file_name = os.path.basename(file_path).split('.')[0]
        output_dir = os.path.join(output_base_dir, file_name)
        
        print(f"\n处理文件: {file_name}")
        result_paths = segment_liver_and_spleen_mr(file_path, output_dir)
        
        # 可视化结果，只显示3个
        visualize_segmentation(file_path, result_paths)
        
        # 打印各器官分割结果的路径
        print("\n分割结果文件:")
        for organ, path in result_paths.items():
            print(f"{organ}: {path}")

# 使用示例
def main():
    input_dir = r"K:\734HCC\all-HCC\234HCC-nantong\image"  # 输入目录
    output_base_dir = r"K:\734HCC\all-HCC\234HCC-nantong\image\segmented"  # 输出目录
    
    # 批量处理MRI图像
    batch_process_mr_images(input_dir, output_base_dir)

if __name__ == "__main__":
    main()