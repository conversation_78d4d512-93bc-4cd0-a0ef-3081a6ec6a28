# GFLOPs  GMACs   Params.(M)
#  7.19    3.53    0.965
# throughput: 186 
model:
  NAME: BaseSeg
  encoder_args:
    NAME: PointNet2Encoder
    in_channels: 4
    width: null
    strides: [4, 4, 4, 4]
    layers: 3
    use_res: False
    mlps: [[[32, 32, 64]],  # stage 1: 96
        [[64, 64, 128]], # stage 2: 256
        [[128, 128, 256]], # stage 3: 512
        [[256, 256, 512]]] # stage 4: 1024
    radius: 0.1
    num_samples: 32
    sampler: fps
    aggr_args:
      NAME: 'convpool'
      feature_type: 'dp_fj'
      anisotropic: False
      reduction: 'max'
    group_args:
      NAME: 'ballquery'
      use_xyz: True
    conv_args:
      order: conv-norm-act
    act_args:
      act: 'relu'
    norm_args:
      norm: 'bn'
  decoder_args:
    NAME: PointNet2Decoder
    fp_mlps: [[128, 128, 128], [256, 128], [256, 256], [256, 256]]
  cls_args:
    NAME: SegHead
    num_classes: 13
    in_channels: null

datatransforms:
  train: [ChromaticAutoContrast, PointsToTensor, PointCloudScaling, PointCloudXYZAlign, PointCloudJitter, ChromaticDropGPU, ChromaticNormalize]
  val: [PointsToTensor, PointCloudXYZAlign, ChromaticNormalize]
  vote: [ChromaticDropGPU]
  kwargs:
    color_drop: 0.2
    gravity_dim: 2
    scale: [0.9, 1.1]
    jitter_sigma: 0.005
    jitter_clip: 0.02