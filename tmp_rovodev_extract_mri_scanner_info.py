#!/usr/bin/env python3
"""
从NII.gz格式的MRI图像中提取扫描仪信息
Extract MRI scanner information from NII.gz files
"""

import nibabel as nib
import os
import pandas as pd
from pathlib import Path
import json

def extract_scanner_info_from_nii(nii_file_path):
    """
    从NII文件中提取扫描仪相关信息
    Extract scanner information from NII file
    """
    try:
        # 加载NII文件
        img = nib.load(nii_file_path)
        header = img.header
        
        # 获取基本信息
        scanner_info = {
            'file_path': str(nii_file_path),
            'file_name': os.path.basename(nii_file_path),
        }
        
        # 1. 从NII header中提取信息
        if hasattr(header, 'get_data_dtype'):
            scanner_info['data_type'] = str(header.get_data_dtype())
        
        if hasattr(header, 'get_data_shape'):
            scanner_info['image_shape'] = header.get_data_shape()
            
        if hasattr(header, 'get_zooms'):
            scanner_info['voxel_size'] = header.get_zooms()
            
        # 2. 检查是否有扩展头信息
        if hasattr(header, 'extensions') and header.extensions:
            scanner_info['has_extensions'] = True
            scanner_info['num_extensions'] = len(header.extensions)
            
            # 遍历扩展信息
            for i, ext in enumerate(header.extensions):
                ext_info = f'extension_{i}'
                scanner_info[f'{ext_info}_code'] = ext.get_code()
                
                # 尝试解析扩展内容
                try:
                    content = ext.get_content()
                    if isinstance(content, bytes):
                        content_str = content.decode('utf-8', errors='ignore')
                        scanner_info[f'{ext_info}_content'] = content_str[:500]  # 限制长度
                except:
                    scanner_info[f'{ext_info}_content'] = "Unable to decode"
        else:
            scanner_info['has_extensions'] = False
            
        # 3. 检查DICOM相关信息（如果从DICOM转换而来）
        # 查找可能的DICOM标签残留
        header_dict = dict(header)
        dicom_fields = {}
        
        # 常见的扫描仪相关字段
        scanner_fields = [
            'Manufacturer', 'ManufacturerModelName', 'MagneticFieldStrength',
            'ImagingFrequency', 'InstitutionName', 'StationName',
            'SoftwareVersions', 'DeviceSerialNumber', 'SequenceName',
            'ScanningSequence', 'SequenceVariant', 'MRAcquisitionType',
            'SliceThickness', 'RepetitionTime', 'EchoTime', 'FlipAngle'
        ]
        
        for field in scanner_fields:
            if field.lower() in str(header).lower():
                dicom_fields[field] = "Found in header"
                
        if dicom_fields:
            scanner_info['potential_dicom_fields'] = dicom_fields
            
        # 4. 分析文件名中的信息
        filename = os.path.basename(nii_file_path)
        filename_info = analyze_filename_for_scanner_info(filename)
        if filename_info:
            scanner_info['filename_analysis'] = filename_info
            
        return scanner_info
        
    except Exception as e:
        return {
            'file_path': str(nii_file_path),
            'error': str(e)
        }

def analyze_filename_for_scanner_info(filename):
    """
    分析文件名中可能包含的扫描仪信息
    Analyze filename for potential scanner information
    """
    filename_lower = filename.lower()
    scanner_hints = {}
    
    # 常见厂商标识
    manufacturers = {
        'siemens': ['siemens', 'sie', 'skyra', 'avanto', 'trio', 'prisma', 'vida'],
        'ge': ['ge', 'general_electric', 'signa', 'discovery', 'optima'],
        'philips': ['philips', 'achieva', 'ingenia', 'intera', 'gyroscan'],
        'toshiba': ['toshiba', 'canon', 'vantage', 'titan'],
        'hitachi': ['hitachi', 'echelon', 'oasis']
    }
    
    for manufacturer, keywords in manufacturers.items():
        for keyword in keywords:
            if keyword in filename_lower:
                scanner_hints['potential_manufacturer'] = manufacturer
                scanner_hints['keyword_found'] = keyword
                break
        if 'potential_manufacturer' in scanner_hints:
            break
    
    # 磁场强度
    field_strengths = ['1.5t', '3t', '3.0t', '7t', '0.5t', '1t']
    for strength in field_strengths:
        if strength in filename_lower:
            scanner_hints['potential_field_strength'] = strength
            break
    
    # 序列类型
    sequences = ['t1', 't2', 'flair', 'dwi', 'adc', 'swi', 'bold', 'dti']
    found_sequences = [seq for seq in sequences if seq in filename_lower]
    if found_sequences:
        scanner_hints['potential_sequences'] = found_sequences
    
    return scanner_hints if scanner_hints else None

def batch_extract_scanner_info(input_directory, output_csv=None):
    """
    批量提取目录下所有NII文件的扫描仪信息
    Batch extract scanner information from all NII files in directory
    """
    input_path = Path(input_directory)
    nii_files = list(input_path.rglob("*.nii")) + list(input_path.rglob("*.nii.gz"))
    
    if not nii_files:
        print(f"在目录 {input_directory} 中未找到NII文件")
        return None
    
    print(f"找到 {len(nii_files)} 个NII文件，开始提取信息...")
    
    all_scanner_info = []
    
    for i, nii_file in enumerate(nii_files, 1):
        print(f"处理文件 {i}/{len(nii_files)}: {nii_file.name}")
        
        scanner_info = extract_scanner_info_from_nii(nii_file)
        all_scanner_info.append(scanner_info)
    
    # 转换为DataFrame
    df = pd.json_normalize(all_scanner_info)
    
    # 保存结果
    if output_csv:
        df.to_csv(output_csv, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_csv}")
    
    return df

def analyze_single_nii_file(nii_file_path):
    """
    分析单个NII文件的详细信息
    Analyze detailed information of a single NII file
    """
    print(f"分析文件: {nii_file_path}")
    print("=" * 60)
    
    scanner_info = extract_scanner_info_from_nii(nii_file_path)
    
    # 格式化输出
    for key, value in scanner_info.items():
        if key == 'error':
            print(f"❌ 错误: {value}")
        elif key == 'file_path':
            print(f"📁 文件路径: {value}")
        elif key == 'file_name':
            print(f"📄 文件名: {value}")
        elif key == 'image_shape':
            print(f"📐 图像尺寸: {value}")
        elif key == 'voxel_size':
            print(f"📏 体素大小: {value}")
        elif key == 'has_extensions':
            print(f"🔧 扩展信息: {'有' if value else '无'}")
        elif key == 'potential_dicom_fields':
            print(f"🏥 可能的DICOM字段: {list(value.keys())}")
        elif key == 'filename_analysis':
            print(f"🔍 文件名分析:")
            for subkey, subvalue in value.items():
                print(f"   - {subkey}: {subvalue}")
        else:
            print(f"ℹ️  {key}: {value}")
    
    return scanner_info

# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='提取NII文件中的MRI扫描仪信息')
    parser.add_argument('--input', '-i', required=True, help='输入NII文件或包含NII文件的目录')
    parser.add_argument('--output', '-o', help='输出CSV文件路径（可选）')
    parser.add_argument('--single', '-s', action='store_true', help='分析单个文件的详细信息')
    
    args = parser.parse_args()
    
    input_path = Path(args.input)
    
    if input_path.is_file():
        # 单个文件分析
        if args.single:
            analyze_single_nii_file(input_path)
        else:
            scanner_info = extract_scanner_info_from_nii(input_path)
            print(json.dumps(scanner_info, indent=2, ensure_ascii=False))
    
    elif input_path.is_dir():
        # 批量分析
        output_file = args.output or f"scanner_info_{input_path.name}.csv"
        df = batch_extract_scanner_info(input_path, output_file)
        
        if df is not None:
            print("\n📊 统计信息:")
            print(f"总文件数: {len(df)}")
            
            # 统计可能的厂商
            if 'filename_analysis.potential_manufacturer' in df.columns:
                manufacturer_counts = df['filename_analysis.potential_manufacturer'].value_counts()
                if not manufacturer_counts.empty:
                    print("可能的厂商分布:")
                    for manufacturer, count in manufacturer_counts.items():
                        print(f"  - {manufacturer}: {count}")
            
            # 统计磁场强度
            if 'filename_analysis.potential_field_strength' in df.columns:
                field_counts = df['filename_analysis.potential_field_strength'].value_counts()
                if not field_counts.empty:
                    print("可能的磁场强度分布:")
                    for field, count in field_counts.items():
                        print(f"  - {field}: {count}")
    
    else:
        print(f"错误: 路径 {input_path} 不存在")

"""
使用方法:

1. 分析单个文件:
   python tmp_rovodev_extract_mri_scanner_info.py -i /path/to/file.nii.gz -s

2. 批量分析目录:
   python tmp_rovodev_extract_mri_scanner_info.py -i /path/to/nii_directory -o scanner_results.csv

3. 在Python中使用:
   from tmp_rovodev_extract_mri_scanner_info import analyze_single_nii_file
   analyze_single_nii_file("example.nii.gz")
"""