import os
import shutil
from pathlib import Path
import pandas as pd
from collections import defaultdict
import re

def extract_second_dash_content(filename):
    """
    提取文件名第2个"-"后面的内容
    
    Args:
        filename: 文件名
    
    Returns:
        str: 第2个"-"后面的内容，如果解析失败返回None
    """
    # 移除.nii.gz后缀
    name_without_ext = filename.replace('.nii.gz', '')
    
    # 找到第2个"-"的位置，提取后面的所有内容
    first_dash = name_without_ext.find('-')
    if first_dash != -1:
        second_dash = name_without_ext.find('-', first_dash + 1)
        if second_dash != -1:
            return name_without_ext[second_dash + 1:]
    
    return None

def analyze_patient_sequences(patient_folder):
    """
    分析患者文件夹中的序列分组情况
    
    Args:
        patient_folder: 患者文件夹路径
    
    Returns:
        dict: 序列分组信息
    """
    patient_path = Path(patient_folder)
    sequence_groups = defaultdict(list)
    
    # 递归扫描所有nii.gz文件
    for nii_file in patient_path.rglob("*.nii.gz"):
        second_dash_content = extract_second_dash_content(nii_file.name)
        if second_dash_content:
            sequence_groups[second_dash_content].append(nii_file)
    
    return sequence_groups

def reorganize_patient_files(patient_folder, output_base_folder, dry_run=True):
    """
    重新组织患者文件，按第2个dash后内容分类
    
    Args:
        patient_folder: 患者文件夹路径
        output_base_folder: 输出基础文件夹路径
        dry_run: 是否为预览模式（不实际移动文件）
    
    Returns:
        dict: 操作结果统计
    """
    patient_path = Path(patient_folder)
    patient_name = patient_path.name
    
    # 分析序列分组
    sequence_groups = analyze_patient_sequences(patient_folder)
    
    # 统计信息
    stats = {
        'patient_name': patient_name,
        'total_files': 0,
        'sequence_groups': len(sequence_groups),
        'operations': []
    }
    
    # 为每个序列组创建文件夹并移动文件
    for sequence_content, files in sequence_groups.items():
        # 创建序列文件夹名称（清理特殊字符）
        safe_sequence_name = re.sub(r'[<>:"/\\|?*]', '_', sequence_content)
        sequence_folder = Path(output_base_folder) / patient_name / safe_sequence_name
        
        stats['total_files'] += len(files)
        
        operation_info = {
            'sequence_content': sequence_content,
            'safe_folder_name': safe_sequence_name,
            'file_count': len(files),
            'target_folder': str(sequence_folder),
            'files': [str(f) for f in files]
        }
        
        if not dry_run:
            # 创建目标文件夹
            try:
                sequence_folder.mkdir(parents=True, exist_ok=True)
                
                # 移动文件
                for file_path in files:
                    target_file = sequence_folder / file_path.name
                    if not target_file.exists():
                        shutil.move(str(file_path), str(target_file))
                        print(f"已移动: {file_path.name} -> {safe_sequence_name}/")
                    else:
                        print(f"警告: 目标文件已存在，跳过: {target_file}")
                        
                operation_info['status'] = 'success'
            except Exception as e:
                operation_info['status'] = 'error'
                operation_info['error'] = str(e)
                print(f"错误: 处理序列 {sequence_content} 时出错: {e}")
        else:
            operation_info['status'] = 'preview'
            print(f"预览: 将创建文件夹 {safe_sequence_name}/ 包含 {len(files)} 个文件")
        
        stats['operations'].append(operation_info)
    
    return stats

def batch_reorganize_all_patients(base_input_folder, base_output_folder, dry_run=True):
    """
    批量重新组织所有患者的文件
    
    Args:
        base_input_folder: 输入基础文件夹路径
        base_output_folder: 输出基础文件夹路径
        dry_run: 是否为预览模式
    
    Returns:
        list: 所有患者的操作结果
    """
    base_input_path = Path(base_input_folder)
    all_results = []
    
    if not base_input_path.exists():
        print(f"错误: 输入目录 {base_input_folder} 不存在")
        return all_results
    
    # 遍历所有患者文件夹
    for patient_folder in base_input_path.iterdir():
        if patient_folder.is_dir():
            print(f"\n{'='*50}")
            print(f"处理患者: {patient_folder.name}")
            print(f"{'='*50}")
            
            try:
                result = reorganize_patient_files(
                    str(patient_folder), 
                    base_output_folder, 
                    dry_run=dry_run
                )
                all_results.append(result)
                
                # 显示统计信息
                print(f"患者 {result['patient_name']} 统计:")
                print(f"  总文件数: {result['total_files']}")
                print(f"  序列组数: {result['sequence_groups']}")
                
            except Exception as e:
                print(f"错误: 处理患者 {patient_folder.name} 时出错: {e}")
                all_results.append({
                    'patient_name': patient_folder.name,
                    'error': str(e),
                    'status': 'error'
                })
    
    return all_results

def save_reorganization_report(results, output_file):
    """
    保存重组操作报告到Excel文件
    
    Args:
        results: 操作结果列表
        output_file: 输出Excel文件路径
    """
    report_data = []
    
    for result in results:
        if 'operations' in result:
            for op in result['operations']:
                report_data.append({
                    '患者名称': result['patient_name'],
                    '序列内容': op['sequence_content'],
                    '文件夹名称': op['safe_folder_name'],
                    '文件数量': op['file_count'],
                    '目标文件夹': op['target_folder'],
                    '状态': op['status'],
                    '错误信息': op.get('error', '')
                })
        else:
            # 处理错误情况
            report_data.append({
                '患者名称': result['patient_name'],
                '序列内容': '',
                '文件夹名称': '',
                '文件数量': 0,
                '目标文件夹': '',
                '状态': 'error',
                '错误信息': result.get('error', '')
            })
    
    # 保存到Excel
    df = pd.DataFrame(report_data)
    df.to_excel(output_file, index=False)
    print(f"\n重组报告已保存到: {output_file}")

def main():
    # 设置路径
    input_directory = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output"
    output_directory = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output_reorganized"
    report_file = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\文件重组报告.xlsx"
    
    print("文件重组工具 - 按第2个dash后内容分类")
    print("=" * 60)
    print(f"输入目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print(f"报告文件: {report_file}")
    print()
    
    # 先预览模式运行
    print("1. 预览模式 - 分析文件分组情况")
    print("-" * 40)
    preview_results = batch_reorganize_all_patients(
        input_directory, 
        output_directory, 
        dry_run=True
    )
    
    # 保存预览报告
    preview_report_file = report_file.replace('.xlsx', '_预览.xlsx')
    save_reorganization_report(preview_results, preview_report_file)
    
    # 显示总体统计
    total_patients = len([r for r in preview_results if 'operations' in r])
    total_files = sum(r.get('total_files', 0) for r in preview_results if 'operations' in r)
    total_groups = sum(r.get('sequence_groups', 0) for r in preview_results if 'operations' in r)
    
    print(f"\n总体统计:")
    print(f"  处理患者数: {total_patients}")
    print(f"  总文件数: {total_files}")
    print(f"  总序列组数: {total_groups}")
    
    # 询问是否执行实际重组
    print("\n" + "=" * 60)
    while True:
        choice = input("是否执行实际的文件重组? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("\n2. 执行模式 - 实际重组文件")
            print("-" * 40)
            actual_results = batch_reorganize_all_patients(
                input_directory, 
                output_directory, 
                dry_run=False
            )
            
            # 保存实际操作报告
            save_reorganization_report(actual_results, report_file)
            print("\n文件重组完成!")
            break
        elif choice in ['n', 'no', '否']:
            print("取消文件重组操作")
            break
        else:
            print("请输入 y 或 n")

if __name__ == "__main__":
    main()
