import os
import xml.etree.ElementTree as ET

def parse_xml_and_save_to_txt(xml_file_path, output_txt_path):
    """
    解析XML文件，提取'zhong'和'cai'对象的边界框坐标，并保存到txt文件
    
    参数:
        xml_file_path: XML文件路径
        output_txt_path: 输出的txt文件路径
    """
    try:
        # 解析XML文件
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        
        # 打开输出文件
        with open(output_txt_path, 'w') as f:
            # 遍历所有object元素
            for obj in root.findall('.//object'):
                # 获取name元素的文本
                name = obj.find('name').text
                
                # 只处理'zhong'和'cai'对象
                if name in ['zhong', 'cai']:
                    # 获取边界框坐标
                    bbox = obj.find('.//bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    
                    # 获取图片宽高
                    size = root.find('size')
                    img_width = float(size.find('width').text)
                    img_height = float(size.find('height').text)
                    
                    # VOC转YOLO格式
                    x_center = (xmin + xmax) / 2.0 / img_width
                    y_center = (ymin + ymax) / 2.0 / img_height
                    width = (xmax - xmin) / img_width
                    height = (ymax - ymin) / img_height
                    
                    # 将'zhong'替换为1，'cai'替换为2
                    class_id = '1' if name == 'zhong' else '2'
                    
                    # 写入txt文件，格式为: class_id x_center y_center width height
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        print(f"成功将'{xml_file_path}'中的数据保存到'{output_txt_path}'")
        return True
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def process_all_xml_files(xml_dir, output_dir):
    """
    处理目录中的所有XML文件
    
    参数:
        xml_dir: 包含XML文件的目录
        output_dir: 输出txt文件的目录
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有XML文件
    xml_files = [f for f in os.listdir(xml_dir) if f.endswith('.xml')]
    
    if not xml_files:
        print(f"在'{xml_dir}'中没有找到XML文件")
        return
    
    success_count = 0
    for xml_file in xml_files:
        xml_path = os.path.join(xml_dir, xml_file)
        # 创建相同名称的txt文件
        txt_file = os.path.splitext(xml_file)[0] + '.txt'
        txt_path = os.path.join(output_dir, txt_file)
        
        if parse_xml_and_save_to_txt(xml_path, txt_path):
            success_count += 1
    
    print(f"处理完成: 成功转换{success_count}/{len(xml_files)}个文件")

# 主函数，直接使用指定路径进行批量转换
if __name__ == "__main__":
    # 设置固定的输入和输出路径
    xml_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/anno'
    output_dir = '/root/autodl-tmp/yolov12-main/datasets/twins/labels'
    
    print(f"开始处理XML文件，从'{xml_dir}'转换到'{output_dir}'")
    process_all_xml_files(xml_dir, output_dir)