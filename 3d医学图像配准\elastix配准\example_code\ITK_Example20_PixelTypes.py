#!/usr/bin/env python
# coding: utf-8

# # Input, output and internal pixel types

# itk-elastix `v0.17.0` and higher supports various types for the input images, while the output image type is defined to match the inputs. That way the users do not need to pay special attention to convert their images before using elastix or transformix

# In[1]:


import itk
import numpy as np


# ### Elastix pixel types

# In the following example, we create itk images from corresponding numpy arrays of different data types, and demonstrate that elastix is able to register them. More details on the inner workings of elastix are explained in the final section of the current notebook.

# In[2]:


# Define a rigid parameter object
parameter_object = itk.ParameterObject.New()
default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid')
parameter_object.AddParameterMap(default_rigid_parameter_map)

# Non-exhaustive list of image types supported by itk-elastix
np_dtypes = [np.uint8, np.int16, np.uint16, np.float32, np.double]

# Function to create a random 3D itk image
def create_random_image(dtype):
    arr = (100 * np.random.rand(64, 64, 64)).astype(dtype)
    return itk.image_from_array(arr)

# Loop over different dtypes
indent = 30
print("Input images type".ljust(indent) + "Output image type".ljust(indent) + "Corresponding numpy dtype")
for dtype in np_dtypes:
    image = create_random_image(dtype)
    result_image, result_transform_parameters = itk.elastix_registration_method(
    image, image,
    parameter_object=parameter_object,
    log_to_console=False)
    
    print(f"{itk.template(image)[1][0]}".ljust(indent) + f"{itk.template(result_image)[1][0]}".ljust(indent) + f"{dtype}")


# ### Transformix pixel types

# The previous example used elastix, but the same functionality works also for transformix

# In[3]:


print("Input images type".ljust(indent) + "Output image type".ljust(indent) + "Corresponding numpy dtype")
for dtype in np_dtypes:
    image = create_random_image(dtype)
    transformed_image = itk.transformix_filter(
    image,
    transform_parameter_object=result_transform_parameters,
    log_to_console=False)
    
    print(f"{itk.template(image)[1][0]}".ljust(indent) + f"{itk.template(transformed_image)[1][0]}".ljust(indent) + f"{dtype}")


# ### Internal pixel types

# Under the hood, elastix/transformix will [cast](https://itk.org/Doxygen/html/classitk_1_1CastImageFilter.html) the pixels of the input image(s) to an internal pixel type. By default, the internal pixel type is `float`, but for 3D and 4D images, it can be `short`, according to the value of the parameters `FixedInternalPixelType` and `MovingInternalPixelType` specified in the parameter object. When the registration or transformation is finished, the image will be converted back (cast) to original pixel type before it is output to the user.

# Let's do a simple test to show that actually choosing `short` as internal pixel type will truncate the floating values of the pixels:

# In[4]:


# Copy the previous transformation result to two new parameter objects
transform_parameters_float = itk.ParameterObject.New()
transform_parameters_short = itk.ParameterObject.New()

transform_parameters_float.AddParameterMap(result_transform_parameters.GetParameterMap(0))
transform_parameters_short.AddParameterMap(result_transform_parameters.GetParameterMap(0))

# Switch to linear interpolation for resampling
transform_parameters_float.SetParameter(0, 'FinalBSplineInterpolationOrder', '1')
transform_parameters_short.SetParameter(0, 'FinalBSplineInterpolationOrder', '1')

# Set transform to zero so that the input image matches the output image
transform_parameters_float.SetParameter(0, 'TransformParameters', 6 * ['0']) # 6 transformation parameters to zero
transform_parameters_short.SetParameter(0, 'TransformParameters', 6 * ['0'])

print(transform_parameters_float)


# In[5]:


# Create the input image as float
image = create_random_image(np.float32)

# Transform the image using 'float' internal pixel type
transformed_image_float = itk.transformix_filter(image,
                                                 transform_parameter_object=transform_parameters_float,
                                                )

# Transform the image using 'short' internal pixel type
transform_parameters_short.SetParameter(0, 'FixedInternalImagePixelType', 'short')
transform_parameters_short.SetParameter(0, 'MovingInternalImagePixelType', 'short')
transformed_image_short = itk.transformix_filter(image,
                                                 transform_parameter_object=transform_parameters_short,
                                                )

# Compute the differences between the result and the input image
diff_float = transformed_image_float[:] - image[:]
diff_short = transformed_image_short[:] - image[:]

print(f"For float internal pixel type - Diff min: {diff_float.min()} | max: {diff_float.max()}")
print(f"For short internal pixel type - Diff min: {diff_short.min()} | max: {diff_short.max()}")


# We observe that difference between input and output images is zero. On the other hand, for the case of `short` internal type we see that there is a difference that corresponds to the rounding error (truncation of the floating part). 

# Note: Since specifying the internal pixel type to short is meant as a way to reduce the memory requirements for the registration, elastix supports it by default only for `>=3D` images. There is a CMake option to enable support for `short` with `2D` images when building elastix on your own.

# In[ ]:




