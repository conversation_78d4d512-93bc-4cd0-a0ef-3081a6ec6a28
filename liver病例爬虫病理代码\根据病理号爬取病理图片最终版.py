# -*- coding: utf-8 -*-
"""
Created on Tue Apr 12 21:08:28 2022

@author: DELL
"""

## 下载pubmed文献主页图片的代码，成功了
# https://pubmed.ncbi.nlm.nih.gov/30930991/
# https://pubmed.ncbi.nlm.nih.gov/33311989/
# https://pubmed.ncbi.nlm.nih.gov/25574092/
# https://pubmed.ncbi.nlm.nih.gov/37382415/

import requests
from lxml import etree
import csv
import re
from bs4 import BeautifulSoup
import re
import urllib.request, urllib.error
import xlwt
import xlrd
import _sqlite3

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36"
}

def import_excel(table):
    tables = []
    for rown in range(table.nrows):
        array = {'name': '', 'ID': ''}
        array['name'] = table.cell_value(rown, 0)
        array['ID'] = table.cell_value(rown, 4)
        tables.append(array)
    return tables

    # 此处需要获取Excel的住院号内容
data = xlrd.open_workbook(r'D:\liver\HCC2023.xls')  # 此处为读取的文件名！！！！！
table = data.sheets()[0]
    # 创建一个空列表，存储Excel的数据
tables = import_excel(table)
tables

zhuyuanhao = []
for i in tables:
    id = i.get("ID")
    zhuyuanhao.append(id)
zhuyuanhao
zhuyuanhao = zhuyuanhao[1:]
zhuyuanhao

baseurl = 'http://**********/pathwebrpt/index_cxm.asp?blh='  # 此处需要添加病理号的链接

# Download and save the images
save_path = "D:/liver/images/"  # Replace with the desired save path

for id in zhuyuanhao:
    print(type(id))
    urls = baseurl +  str(id)
    print(urls)
    # datalist.append(id)
    # 程序修改处
    response = requests.get(urls, headers=headers)
    response.encoding = 'GBK'
    print(response.status_code == requests.codes.ok)

    # 解码，防止出现爬虫乱码
    # sel = etree.HTML(response.text)
    sel = etree.HTML(response.content)

    # Find the specific image elements using XPath
    # image_paths = tree.xpath('/html/body/div[2]/div[4]/ul/li/a/img/@src')
    image_paths = sel.xpath('/html/body/table[2]/tr[1]/td[1]/img/@src') #完整路径中要删除tbody，尽量用间接路径
    print(image_paths[:5])

    # name = tree.xpath('/html/body/div[2]/div[4]/ul/li/a/img/text()')
    names = sel.xpath('/html/body/strong/table[1]/tr[2]/td[2]/font/text()') #完整路径中要删除tbody
    print(names[:5])

    # title = sel.xpath('// *[ @ id = "full-view-heading"] / h1/text()')
    # print(titles[:5])
    # titles.append(title[0] if title else "")

    for i, (image_path, name) in enumerate(zip(image_paths, names), 1):
        image_url = 'http://**********/pathwebrpt/' + image_path  #图片的具体路径
        # image_url = image_path  # 图片的具体路径
        print(image_url)
        print(name)
        image_response = requests.get(image_url, headers=headers)
        with open(f"{save_path}{name}.jpg", "wb") as f:
            f.write(image_response.content)
            print(f"Image {i} downloaded and saved.")

# # Open the workbook in write mode
# workbook = xlwt.Workbook()
# sheet = workbook.add_sheet("Sheet1")
#
# # Write the existing data to the new workbook
# for i, table in enumerate(tables):
#     sheet.write(i, 0, table['name'])
#     sheet.write(i, 1, table['ID'])
#
#     if i < len(titles):  # Check if titles exist for the current index
#         sheet.write(i, 2, titles[i])  # Write titles to the new column
#     else:
#         print(f"No title found for index {i}")  # Handle the IndexError
#
# # Save the modified workbook
# workbook.save(r'G:\HCC2021.1-11-30_modified.xls')
# print("success!")

# 调试代码
# urls = "http://**********/pathwebrpt/index_cxm.asp?blh=********"
# print(urls)
#
# response = requests.get(urls, headers=headers)
# response.encoding = 'GBK'
# print(response.status_code == requests.codes.ok)
# sel = etree.HTML(response.content)
#
# image_paths = sel.xpath('/html/body/table[2]/tr[1]/td[1]/img/@src')
# image_paths = sel.xpath('//*[@id="the_note"]/text()')
# image_paths
# names = sel.xpath('/html/body/strong/table[1]/tr[2]/td[2]/font/text()')
# names


##批量下载网页图片

# import requests
# from bs4 import BeautifulSoup
# from lxml import etree
#
# headers = {
#         "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36"
#     }
#
# # # Send a GET request to the website
# url = "http://**********/pathwebrpt/index_cxm.asp?blh=********"  # Replace with the actual website URL
# response = requests.get(url,headers=headers)
# response.encoding = 'GBK'
# response.status_code == requests.codes.ok
#
# # Parse the HTML content
# html_content = response.content
# tree = etree.HTML(html_content)
# tree
# # Find the specific image elements using XPath
# # image_paths = tree.xpath('/html/body/div[2]/div[4]/ul/li/a/img/@src')
# image_paths = tree.xpath('//*[@id="yx_nav"]/div/a/img/@src')
# image_paths
#
#
# # name = tree.xpath('/html/body/div[2]/div[4]/ul/li/a/img/text()')
# # name
# names = tree.xpath('/html/body/div[2]/div[4]/ul/li/a/img/@alt')
# names[0]
#
# # Download and save the images
# save_path = "D:/images/"  # Replace with the desired save path
# for i, (image_path, name) in enumerate(zip(image_paths, names), 1):
#     image_url = '	http://fyy.sdfyy.cn' + image_path
#     print(image_url)
#     print(name)
#     image_response = requests.get(image_url, headers=headers)
#     with open(f"{save_path}{name}.jpg", "wb") as f:
#         f.write(image_response.content)
#         print(f"Image {i} downloaded and saved.")

###需要修改的地方：125行代码for k in range(1,2819): 2819为文件的行数需要修改
# ### 读取和保存的路径需要修改
# import requests
# from lxml import etree
# import csv
# import re
# from bs4 import BeautifulSoup
# import re
# import urllib.request, urllib.error
# import xlwt
# import xlrd
# import _sqlite3
#
# headers = {
#         "User-Agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36"
#     }
# #response = requests.get(baseurl, headers=headers)
# #response.status_code == requests.codes.ok
# #sel = etree.HTML(response.text)
# #t_list = sel.xpath('//html/body/table[2]/tr/td[2]/font/a/@href')
# #t_list = sel.xpath('//html/body/table[2]/tr/td[2]/font/a/text()')  #tbody需要删除
# #t_list = sel.xpath('//*[@id="xxx4"]/text()')
# #
# #t_list
# #print(t_list)
#
# def main():
#     baseurl = 'http://**********/pathwebrpt/index_main_gl.asp?keywords=&keywords_zyh='  # 此处需要添加病理的链接
#     # 此处需要获取Excel的住院号内容
#     data = xlrd.open_workbook(r'E:\liver\HCC2021.1-11-30.xls')        #此处为读取的文件名！！！！！
#     table = data.sheets()[0]
#     # 创建一个空列表，存储Excel的数据
#     tables = import_excel(table)
#     # 验证Excel文件存储到列表中的数据
#     idzong = []
#     for i in tables:
#         id = i.get("ID")
#         idzong.append(id)
#
#     datalist = getdata(baseurl,idzong)
#     savepath = "E:/liver/HCCbinglihao2021.xls"               #此处为保存的文件名！！！
#     savedata(datalist,savepath)
#
#
# def import_excel(table):
#     tables = []
#
#     for rown in range(table.nrows):
#         array = {'name': '', 'ID': ''}
#         array['name'] = table.cell_value(rown, 1)
#         array['ID'] = table.cell_value(rown, 4)
#         tables.append(array)
#     return tables
#
#
# def getdata(baseurl, idzong):
#     datalist = []
#
#     for id in idzong:
#         print(type(id))
#         url = baseurl + str(id)
#         urls = url + "&keywords_brbh=&keywords_sqxh="
#         print(urls)
#         # html = askurl(urls)
#         #print(html)
#         #datalist.append(id)
#         #程序修改处
#         response = requests.get(urls, headers=headers)
#         response.encoding = 'GBK'
#         response.status_code == requests.codes.ok
#            #解码，防止出现爬虫乱码
#         sel = etree.HTML(response.text)
#         t_list1 = sel.xpath('//*[@id="xxx1"]/text()')   #*[@class="tt"]
#         t_list2 = sel.xpath('//*[@id="xxx2"]/text()')
#         t_list3 = sel.xpath('//*[@id="xxx3"]/text()')
#         t_list4 = sel.xpath('//*[@id="xxx4"]/text()')
#         t_list5 = sel.xpath('//*[@id="xxx5"]/text()')
#         t_list6 = sel.xpath('//*[@id="xxx6"]/text()')
#         # soup = BeautifulSoup(html, "html.parser")
#         data = []
#         # item = []
#         # zhuyuanhao = soup.i
#         # zhuyuanhao = str(zhuyuanhao)
#         # data.append(zhuyuanhao)
#         t_list1 = str(t_list1)
#         t_list2 = str(t_list2)
#         t_list3 = str(t_list3)
#         t_list4 = str(t_list4)
#         t_list5 = str(t_list5)
#         t_list6 = str(t_list6)
#         print(t_list1)
#         # print(t_list[1].get_text())
#         data.append(t_list1)
#         data.append(t_list2)
#         data.append(t_list3)
#         data.append(t_list4)
#         data.append(t_list5)
#         data.append(t_list6)
#         #soup = BeautifulSoup(html, "html.parser")
#         #data = []
#         #zhuyuanhao = soup.i
#         #zhuyuanhao = str(zhuyuanhao)
#         #data.append(zhuyuanhao)
#         #t_list = soup.find_all("textarea")
#         #t_list = str(t_list)
#         #data.append(t_list)
#         datalist.append(data)  # 总data放入datalist内   #此处需要测试datalist处于的位置
#     #print(datalist)
#     return datalist
#
#
# def savedata(datalist, savepath):
#     print("save...")
#     book = xlwt.Workbook(encoding="utf-8", style_compression=0)  # 创建workbook对象
#     sheet = book.add_sheet('sheet1', cell_overwrite_ok=True)  # 创建工作表
#
#     for k in range(1,2819):          #'''此处为文件的行数！！！！'''(1,4)左闭右开
#         # print("第%d条" %k)
#         data = datalist[k]
#         # sheet.write(k,data[k])
#         for j in range(0,6):            #j表示列数
#             print(j)
#             sheet.write(k, j, data[j])
#             # print(data[j])
#     book.save(savepath)
#
# if __name__ == "__main__":
#     main()
#     print("爬去完毕")
#