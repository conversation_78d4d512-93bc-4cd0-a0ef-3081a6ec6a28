import torch
from torch import nn
from typing import Union, <PERSON>, Tuple

from monai.networks.nets import SwinUNETR

from nnunetv2.training.nnUNetTrainer.nnUNetTrainer import nnUNetTrainer


class nnUNetTrainerSwinUNETR(nnUNetTrainer):
    """
    nnUNet trainer that uses SwinUNETR from MONAI as the network architecture.
    All preprocessing remains the same as in the original nnUNet.
    """
    
    @staticmethod
    def build_network_architecture(
        architecture_class_name: str,
        arch_init_kwargs: dict,
        arch_init_kwargs_req_import: Union[List[str], Tuple[str, ...]],
        num_input_channels: int,
        num_output_channels: int,
        enable_deep_supervision: bool = True,
    ) -> nn.Module:
        """
        Builds a SwinUNETR model from MONAI instead of the default nnU-Net architecture.
        
        Args:
            architecture_class_name: Not used, kept for compatibility with parent class
            arch_init_kwargs: Dictionary containing architecture parameters. Should contain 'patch_size'
            arch_init_kwargs_req_import: Not used, kept for compatibility with parent class
            num_input_channels: Number of input channels
            num_output_channels: Number of output channels (classes)
            enable_deep_supervision: Whether to use deep supervision. Note that SwinUNETR
                                    implementation in MONAI doesn't support deep supervision
                                    in the same way as nnUNet, so this is partially ignored.
        
        Returns:
            MONAI SwinUNETR model configured for the specified inputs/outputs
        """
        # Get patch size from arch_init_kwargs
        patch_size = arch_init_kwargs.get('patch_size', (128, 128, 128))
        
        # Create SwinUNETR model from MONAI
        # Note: The original SwinUNETR from MONAI doesn't support deep supervision
        # in the same way as nnUNet. Deep supervision can be added as a separate module if needed.
        model = SwinUNETR(
            img_size=patch_size,
            in_channels=num_input_channels,
            out_channels=num_output_channels,
            feature_size=48,  # Default feature size, can be adjusted as needed
            use_checkpoint=True,  # Use gradient checkpointing to save memory
            spatial_dims=len(patch_size),
        )
        
        return model
    
    def initialize(self):
        """
        Override the initialize method to add the patch size to arch_init_kwargs
        before calling the parent's initialize method
        """
        # Make sure patch_size is available in arch_init_kwargs for build_network_architecture
        if self.configuration_manager is not None:
            patch_size = self.configuration_manager.patch_size
            if 'arch_init_kwargs' not in self.my_init_kwargs:
                self.my_init_kwargs['arch_init_kwargs'] = {}
            self.my_init_kwargs['arch_init_kwargs']['patch_size'] = patch_size
        
        # Call parent's initialize
        super().initialize()
    
    def _build_loss(self):
        """
        We override this to ensure the proper loss function is used with SwinUNETR,
        since it does not support deep supervision like the original nnUNet.
        """
        # Get the loss from the parent class
        loss = super()._build_loss()
        
        # Disable deep supervision since SwinUNETR doesn't support it natively
        if self.enable_deep_supervision:
            self.print_to_log_file("SwinUNETR doesn't support deep supervision in the same way as nnUNet. "
                                 "Only the final output will be supervised.")
            self.enable_deep_supervision = False
        
        return loss
    
    def set_deep_supervision_enabled(self, enabled: bool):
        """
        Overriding this method since SwinUNETR doesn't support deep supervision
        in the same way as the nnUNet architecture.
        """
        # SwinUNETR doesn't have deep supervision, so we just log a message
        if enabled:
            self.print_to_log_file("SwinUNETR doesn't support deep supervision. Ignoring.") 