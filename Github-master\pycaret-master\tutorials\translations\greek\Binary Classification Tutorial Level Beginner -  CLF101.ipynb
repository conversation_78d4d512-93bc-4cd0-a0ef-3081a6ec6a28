{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Y57RMM1LEQmR", "pycharm": {"name": "#%% md\n"}}, "source": ["#  <span style=\"color:orange\">Μάθημα Δυαδικής <PERSON>αξινόμησης (CLF101) - Επίπεδο Αρχάριων</span>"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**Έγινε χρήση της βιβλιοθήκης: PyCaret 3.0** <br />\n", "**Python Vesrion: 3.9** <br />\n", "**Ημερομηνία τελευταίας τροποποίησης: 14 Αυγούστου 2022**\n", "\n", "\n", "# 1.0 Στόχοι Μαθήματος\n", "\n", "Καλώς ήρθατε στο μάθημα Δυαδικής Ταξινόμησης (CLF101) - Επίπεδο Αρχάριων. Ο οδηγός προορίζεται για τις νέες χρήστριες του PyCaret, οι οποίες ενδιαφέρονται να αξιοποιήσουν το <PERSON>du<PERSON> `pycaret.classification` για να εκτελέσουν Δυαδική Ταξινόμηση:\n", "\n", "Σε αυτό το μάθημα θα διδαχθούμε:\n", "\n", "* **Λήψη Δεδομένων:** Π<PERSON>ς να εισάγουμε δεδομένα από το αποθετήριο της βιβλιοθήκης PyCaret.\n", "* **Εγκ<PERSON><PERSON><PERSON><PERSON>τα<PERSON>η Περιβάλλοντος:** Π<PERSON>ς να στήνουμε μια πειραματική διαδικασία κάνοντας χρήση της PyCaret και πώς να χτίζουμε μοντέλα ταξινόμησης.\n", "* **Δημιουργία Μοντέλου:** Π<PERSON>ς να δημιουργούμε ένα μοντέλο, να κάνουμε stratified cross validation (ΣτΜ: μέθοδος αξιολόγησης) και να αξιολογούμε τις μετρικές που χρησιμοποιούνται στην ταξινόμηση.\n", "* **Ρύθμιση Μοντέλου:** Π<PERSON>ς να ρυθμίζουμε αυτόματα τις υπερ-παραμέτρους ενός μοντέλου ταξινόμησης.\n", "* **Γραφικ<PERSON> Απεικόνιση Μοντέλου:** Π<PERSON>ς να αναλύουμε την απόδοση ενός μοντέλου αξιοποιώντας διάφορες γραφικές απεικονίσεις.\n", "* **Οριστικοποίηση Μοντέλου:** Πώς να οριστικοποιούμε το καλύτερο μοντέλο στο τέλος του πειράματος.\n", "* **Πρόβλεψη Μοντέλου:** <PERSON><PERSON>ς να κάνουμε προβλέψεις βασισμένες σε νέα / άγνωστα δεδομένα.\n", "* **Αποθήκευση / Φόρτωση Μοντέλου:** Πώς να αποθηκεύουμε / φορτώνουμε ένα μοντέλο για μελλοντική χρήση.\n", "\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>γνωσης : Περίπου 30 Λεπτά.\n", "\n", "\n", "# 1.1 Εγκατάσταση PyCaret\n", "Το πρώτο βήμα για να χρησιμοποιήσουμε τη βιβλιοθήκη PyCaret είναι να την εγκαταστήσουμε. Η εγκατάσταση είναι εύκολη και θα διαρκέσει μόνο μερικά λεπτά. Ακολουθήστε τις παρακάτω οδηγίες:\n", "\n", "# Εγκατάσταση PyCaret σε Τοπικό Jupyter Notebook.\n", "`pip install pycaret`  <br />\n", "\n", "# Εγκατάσταση PyCaret στο Google Colab η στο Azure Notebooks\n", "`!pip install pycaret`\n", "\n", "\n", "# 1.2 Προ-απαιτούμενα\n", "- Python 3.6 ή πιο σύγχρονη (ΣτΜ: H Python 3.10 δεν υποστηρίζεται τη δεδομένη στιγμή).\n", "- PyCaret 2.0 ή πιο σύγχρονη.\n", "- Σύνδεση στο Internet, ώστε να φορτώσουμε δεδομένα από το αποθετήριο της PyCaret.\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ώ<PERSON><PERSON>ις πάνω στη Δυαδική Ταξινόμηση.\n", "\n", "# 1.3 Για τους χρήστες του Google Colab:\n", "Αν εκτελείτε αυτό το notebook στο Google Colab, «τρέξτε» το παρακάτω κομμάτι κώδικα στην αρχή του notebook, ώστε να προβάλλετε διαδραστικές εικόνες.<br/>\n", "\n", "`from pycaret.utils import enable_colab` <br/>\n", "`enable_colab()`\n", "\n", "\n", "# 1.4 Δείτε επίσης:\n", "- __[Μάθημα Δυαδικής Ταξινόμησης (CLF102) - Μέσ<PERSON> Επίπεδο](https://github.com/pycaret/pycaret/blob/master/tutorials/Greek/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__\n", "- __[Μάθημα Δυαδικής Ταξινόμησης (CLF102) - Επίπεδο Προχωρημένων](https://github.com/pycaret/pycaret/blob/master/tutorials/Greek/Binary%20Classification%20Tutorial%20Level%Expert%20-%20CLF103.ipynb)__"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 2.0 Τι είναι η Δυαδική Ταξινόμηση;\n", "\n", "Η δυαδική ταξινόμηση είναι μια τεχνική επιβλεπόμενης μηχανικής μάθησης. Στοχεύει στην πρόβλεψη διακριτών ετικετών (labels) κλ<PERSON><PERSON>εων, όπως για παράδειγμα: Επιτυχία/Αποτυχία (Pass/Fail), Θετικό/Αρνητικό κλπ. Ακολουθούν ορισμένα πραγματικά προβλήματα όπου επιστρατεύεται η ταξινόμηση.\n", "\n", "- Ο ιατρικ<PERSON>ς έλεγχος ώστε να διαγνωστεί αν ένας ασθενής πάσχει από μία συγκεκριμένη ασθένεια. Η ιδιότητα που καλούμαστε να ταξινομήσουμε είναι η ύπαρξη ή μη της νόσου.\n", "- Η μέθοδος καθορισμούς της \"επιτυ<PERSON><PERSON><PERSON>ς ή αποτυχίας\" σε ένα τεστ ή ο ποιοτικός έλεγχος σε εργοστάσια, π.χ. η απόφαση για το αν πληρούνται κάποιες προδιαγραφές ή όχι.\n", "- Η ανάκτηση πληροφοριών, δηλαδ<PERSON> αν μια ιστοσε<PERSON>ίδα ή ένα άρθρο πρέπει να συμπεριλαμβάνεται στα αποτελέσματα αναζήτησης ή όχι. Η ιδιότητα που καλούμαστε να ταξινομήσουμε είναι η συνάφεια του άρθρου ή η χρησιμότητα του για τη χρήστρια.\n", "\n", "__[Περισσότερες πληροφορίες σχετικά με τη δυαδική αναζήτηση](https://medium.com/@categitau/in-one-of-my-previous-posts-i-introduced-machine-learning-and-talked-about-the-two-most-common-c1ac6e18df16)__"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 3.0 Επισκόπηση του Module Ταξινόμησης της PyCaret\n", "\n", "Το <PERSON>le της βιβλιοθήκης PyCaret που αφορά την ταξινόμηση (`pycaret.classification`) είναι ένα module επιβλεπόμενης μηχανικής μάθησης. Χρησιμοποιείται για την ταξινόμηση στοιχείων σε δύο ομάδες βάσει διάφορων τεχνικών και αλγορίθμων. Κάποια συνηθισμένα προβλήματα ταξινόμησης είναι η προεπιλογές πελατών (ναι ή όχι), η απώλεια πελάτη (θα μείνει ή θα φύγει) και η διάγνωση ασθένειας (θετικό ή αρνητικό).\n", "\n", "Το module ταξινόμησης της PyCaret μπορεί να χρησιμοποιηθεί σε προβλήματα Δυαδικής (Binary) ταξινόμησης και ταξινόμησης Πολλαπλών Κλάσεων (Multi-class classification). Έχει περισσότερους απο 18 αλγορίθμους και 14 γραφικές απεικονίσεις για την ανάλυση της απόδοσης των μοντέλων. Το module ταξινόμησης της PyCaret τα έχει όλα, είτε πρόκειται για ρύθμιση υπερ-παραμέντρων (tuning hyper-parameters), είτε για ensembling (ΣτΜ: τεχνική συνδυασμού μεθόδων), είτε για πιο προηγμένες τεχνικές όπως stacking (ΣτΜ: ό.π)."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 4.0 Το Σύνολο Δεδομένων (Dataset) του Μαθήματος"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Σε αυτό το μάθημα θα χρησιμοποιήσουμε ένα σύνολο δεδομένων (dataset) που παρέχεται από το [UCI](https://archive.ics.uci.edu/ml/index.php) και ονομάζεται **Default of Credit Card Clients Dataset**. Σε αυτό εμπεριέχονται πληροφορίες σχετικά με τις προεπιλεγμένες πληρωμές, δημογραφικοί παράγοντες, πιστωτικά δεδομένα, ιστορικ<PERSON> πληρωμών και καταστάσεις τιμολόγησης πελατών πιστωτικών καρτών στην Ταϊβάν από τον Απρίλιο του 2005 έως τον Σεπτέμβριο του 2005. Υπάρχουν 24.000 δείγματα (samples) και 25 χαρακτηριστικά (features). Ακολουθεί σύντομη περιγραφή των επιμέρους στηλών:\n", "\n", "- **ID:** Αναγνωριστικό του κάθε πελάτη.\n", "- **LIMIT_BAL:** Ποσ<PERSON> πίστωσης σε δολάρια ΝΤ (περιλαμβάνει ατομική και οικογενειακή/συμπληρωματική πίστωση).\n", "- **SEX:** Φύλο (1=άρρεν, 2=θήλυ).\n", "- **EDUCATION:** (1=μεταπτυχιακ<PERSON>, 2=πανεπιστήμιο, 3=λύκειο, 4=άλλα, 5=άγνωστο, 6=άγνωστο).\n", "- **MARRIAGE:** Οικογενειακή κατάσταση (1=παντρεμένος, 2=ανύπαντρος, 3=άλλο).\n", "- **AGE:** Ηλικία σε χρόνια.\n", "- **PAY_1 to PAY_6:** Κατάσταση αποπληρωμής πριν από Χ μήνες (PAY_1 = προηγούμενος μήνας ... PAY_6 = πριν 6 μήνες) (Labels: -1=συνεπής πληρωμή, 1=αργοπορημένη πληρωμή κατά 1 μήνα, 2=αργοπορημένη πληρωμή κατά 2 μήνες, ... 8=αργοπορημένη πληρωμή κατά 8 μήνες, 9=αργοπορημένη πληρωμή κατά 9 ή περισσότερους μήνες).\n", "- **BILL_AMT1 to BILL_AMT6:** Εκκαθαριστικό υπόλοιπο του λογαριασμού πριν από Χ μήνες ( BILL_AMT1 = προηγούμενος μήνας .. BILL_AMT6 = πριν από 6 μήνες)\n", "- **PAY_AMT1 to PAY_AMT6:** Ποσό πληρωμής πριν από Χ μήνες ( BILL_AMT1 = προηγούμενος μήνας .. BILL_AMT6 = πριν από 6 μήνες)\n", "- **default:** Προεπιλεγμένη πληρωμή (1=ναι, 0=όχι) `Στήλη-Στόχος`\n", "\n", "# Ευχαριστίες για το dataset:\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013). UCI Machine Learning Repository. Irvine, CA: University of California, School of Information and Computer Science.\n", "\n", "__[Πηγή](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)__"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 5.0 Λήψη Δεδομένων"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Μπορείτε να κατεβάσετε τα δεδομένα από την αρχική τους πηγή, __[εδώ](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)__ και να τα φορτώσετε χρησιμοποιώντας τη βιβλιοθήκη Pandas __[(μαθετε πώς)](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html)__ ή μπορείτε να χρησιμοποιήσετε το αποθετήριο της βιβλιοθήκης PyCaret και να τα φορτώσετε χρησιμοποιώντας τη συνάρτηση `get_data()` (αυτό απαιτεί σύνδεση στο Internet)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>689.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331.0</td>\n", "      <td>14948.0</td>\n", "      <td>15549.0</td>\n", "      <td>1518.0</td>\n", "      <td>1500.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>5000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314.0</td>\n", "      <td>28959.0</td>\n", "      <td>29547.0</td>\n", "      <td>2000.0</td>\n", "      <td>2019.0</td>\n", "      <td>1200.0</td>\n", "      <td>1100.0</td>\n", "      <td>1069.0</td>\n", "      <td>1000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940.0</td>\n", "      <td>19146.0</td>\n", "      <td>19131.0</td>\n", "      <td>2000.0</td>\n", "      <td>36681.0</td>\n", "      <td>10000.0</td>\n", "      <td>9000.0</td>\n", "      <td>689.0</td>\n", "      <td>679.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>19394.0</td>\n", "      <td>19619.0</td>\n", "      <td>20024.0</td>\n", "      <td>2500.0</td>\n", "      <td>1815.0</td>\n", "      <td>657.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>800.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0      20000    2          2         1   24      2      2     -1     -1   \n", "1      90000    2          2         2   34      0      0      0      0   \n", "2      50000    2          2         1   37      0      0      0      0   \n", "3      50000    1          2         1   57     -1      0     -1      0   \n", "4      50000    1          1         2   37      0      0      0      0   \n", "\n", "   PAY_5  ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0     -2  ...        0.0        0.0        0.0       0.0     689.0       0.0   \n", "1      0  ...    14331.0    14948.0    15549.0    1518.0    1500.0    1000.0   \n", "2      0  ...    28314.0    28959.0    29547.0    2000.0    2019.0    1200.0   \n", "3      0  ...    20940.0    19146.0    19131.0    2000.0   36681.0   10000.0   \n", "4      0  ...    19394.0    19619.0    20024.0    2500.0    1815.0     657.0   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  default  \n", "0       0.0       0.0       0.0        1  \n", "1    1000.0    1000.0    5000.0        0  \n", "2    1100.0    1069.0    1000.0        0  \n", "3    9000.0     689.0     679.0        0  \n", "4    1000.0    1000.0     800.0        0  \n", "\n", "[5 rows x 24 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pycaret.datasets import get_data\n", "dataset = get_data('credit')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["(24000, 24)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#check the shape of data\n", "#έλεγχος του μεγέθους των δεδομένων\n", "dataset.shape"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Για να επιδείξουμε τη λειτουργία της συνάρτησης `predict_model()` σε άγνωστα δεδομένα, δειγματοληπτούμε 1.200 καταγραφές (records) από το αρχικό σύνολο δεδομένων, οι οποίες θα χρησιμοποιηθούν για να κάνουμε τις προβλέψεις μας. Αυτό δεν πρέπει να το συγχέουμε με το διαχωρισμό δεδομένων εκπαίδευσης/ελέχου (train/test split), καθώς στη δική μας περίπτωσης εκτελείται με τέτοιο τρόπο ώστε να ανταποκρίνεται σε ένα πραγματικό σενάριο. Ένας άλλος τρόπος για να το αντιληφθούμε θα ήταν η παραδοχή ότι αυτές οι 1.200 καταγραφές δεν είναι διαθέσιμες τη στιγμή που διεξάγεται το πείραμα μηχανικής μάθησης."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Δεδομένα που χρησιμοποιούνται στη Μοντελοποίηση: (22800, 24)\n", "Άγνωστα Δεδομένα για να κάνουμε Προβλέψεις: (1200, 24)\n"]}], "source": ["data = dataset.sample(frac=0.95, random_state=786)\n", "data_unseen = dataset.drop(data.index)\n", "data.reset_index(inplace=True, drop=True)\n", "data_unseen.reset_index(inplace=True, drop=True)\n", "print('Δεδομένα που χρησιμοποιούνται στη Μοντελοποίηση: ' + str(data.shape))\n", "print('Άγνωστα Δεδομένα για να κάνουμε Προβλέψεις: ' + str(data_unseen.shape))"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 6.0 Εγκατάστα<PERSON>η Περιβάλλοντος στην PyCaret"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Η συνάρτηση `setup()` αρχικοποιεί το περιβάλλον στην pycaret και δημιουργεί το transformation pipeline που προετοιμάζει τα δεδομένα για  τις διαδικασίες της μοντελοποίησης και της ανάπτυξης. Η συνάρτηση `setup()` πρέπει να προηγείται οποιασδήποτε άλλης στην pycaret. Δέχεται δυο υποχρεωτικές παραμέτρους: έν<PERSON> pandas dataframe και το όνομα της στήλης-στόχου. Όλες οι υπόλοιπες παράμετροι είναι προαιρετικές και χρησιμοποιούνται για την εξατομίκευση του pre-processing pipeline (θα το δούμε σε επόμενα μαθήματα).\n", "\n", "Όταν εκτελείται η συνάρτηση `setup()`, οι αλγόριθμοι συμπερασμάτων (inference algorithms) της PyCaret θα καταλήξουν στους τύπους δεδομένων όλων των features βάσει συγκεκριμένων ιδιοτήτων τους. Οι τύποι των δεδομένων πρέπει να συμπεραίνονται ορθά, αλλά αυτό δε συμβαίνει πάντα. Για να αντισταθμιστεί αυτό, η PyCaret προβάλλει έναν πίνακα με τα features και του αντίστοιχους τύπους δεδομένων που προέκυψαν μετά την εκτέλεση της `setup()`. Αν όλοι οι τύποι δεδομένων εντοπίστηκαν ορθά, τότε μπορεί να πατηθεί το κουμπί `enter` για να συνεχιστεί το πείραμα. Ειδάλλως, η πληκτρολόγηση της λέξης `quit` θα το τερματίσει. Η εξασφάλιση της ορθότητας των τύπων δεδομένων είναι θεμελιωδώς σημαντική στην PyCaret, καθώς εκτελεί αυτόματα μια σειρά από εργασίες προ-επεξεργασίας (pre-processing) που είναι απολύτως απαραίτητες σε κάθε πείραμα μηχανικής μάθησης. Αυτές εκτελούνται διαφορετικά ανάλογα με τον τύπο δεδομένων, γι' αυτό είναι ιδιαίτερα σημαντική η σωστή ρύθμισή τους.\n", "\n", "Σε επόμενα μαθήματα θα μάθουμε πώς να αντικαθιστούμε τους τύπος δεδομένων, που συμπέρανε η PyCaret, χρησιμοποιώντας τις παραμέτρους της `setup()`: `numeric_features` και `categorical_values`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from pycaret.classification import *"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_6352b_row44_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_6352b\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_6352b_level0_col0\" class=\"col_heading level0 col0\" >Description</th>\n", "      <th id=\"T_6352b_level0_col1\" class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_6352b_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_6352b_row0_col1\" class=\"data row0 col1\" >123</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_6352b_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_6352b_row1_col1\" class=\"data row1 col1\" >default</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_6352b_row2_col0\" class=\"data row2 col0\" >Target Type</td>\n", "      <td id=\"T_6352b_row2_col1\" class=\"data row2 col1\" >Binary</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_6352b_row3_col0\" class=\"data row3 col0\" >Label Encoded</td>\n", "      <td id=\"T_6352b_row3_col1\" class=\"data row3 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_6352b_row4_col0\" class=\"data row4 col0\" >Original Data</td>\n", "      <td id=\"T_6352b_row4_col1\" class=\"data row4 col1\" >(22800, 24)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_6352b_row5_col0\" class=\"data row5 col0\" >Missing Values</td>\n", "      <td id=\"T_6352b_row5_col1\" class=\"data row5 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_6352b_row6_col0\" class=\"data row6 col0\" >Numeric Features</td>\n", "      <td id=\"T_6352b_row6_col1\" class=\"data row6 col1\" >14</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_6352b_row7_col0\" class=\"data row7 col0\" >Categorical Features</td>\n", "      <td id=\"T_6352b_row7_col1\" class=\"data row7 col1\" >9</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_6352b_row8_col0\" class=\"data row8 col0\" >Ordinal Features</td>\n", "      <td id=\"T_6352b_row8_col1\" class=\"data row8 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_6352b_row9_col0\" class=\"data row9 col0\" >High Cardinality Features</td>\n", "      <td id=\"T_6352b_row9_col1\" class=\"data row9 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_6352b_row10_col0\" class=\"data row10 col0\" >High Cardinality Method</td>\n", "      <td id=\"T_6352b_row10_col1\" class=\"data row10 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_6352b_row11_col0\" class=\"data row11 col0\" >Transformed Train Set</td>\n", "      <td id=\"T_6352b_row11_col1\" class=\"data row11 col1\" >(15959, 88)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_6352b_row12_col0\" class=\"data row12 col0\" >Transformed Test Set</td>\n", "      <td id=\"T_6352b_row12_col1\" class=\"data row12 col1\" >(6841, 88)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_6352b_row13_col0\" class=\"data row13 col0\" >Shuffle Train-Test</td>\n", "      <td id=\"T_6352b_row13_col1\" class=\"data row13 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_6352b_row14_col0\" class=\"data row14 col0\" >Stratify Train-Test</td>\n", "      <td id=\"T_6352b_row14_col1\" class=\"data row14 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_6352b_row15_col0\" class=\"data row15 col0\" >Fold Generator</td>\n", "      <td id=\"T_6352b_row15_col1\" class=\"data row15 col1\" >StratifiedKFold</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_6352b_row16_col0\" class=\"data row16 col0\" >Fold Number</td>\n", "      <td id=\"T_6352b_row16_col1\" class=\"data row16 col1\" >10</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_6352b_row17_col0\" class=\"data row17 col0\" >CPU Jobs</td>\n", "      <td id=\"T_6352b_row17_col1\" class=\"data row17 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_6352b_row18_col0\" class=\"data row18 col0\" >Use GPU</td>\n", "      <td id=\"T_6352b_row18_col1\" class=\"data row18 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_6352b_row19_col0\" class=\"data row19 col0\" >Log Experiment</td>\n", "      <td id=\"T_6352b_row19_col1\" class=\"data row19 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_6352b_row20_col0\" class=\"data row20 col0\" >Experiment Name</td>\n", "      <td id=\"T_6352b_row20_col1\" class=\"data row20 col1\" >clf-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_6352b_row21_col0\" class=\"data row21 col0\" >USI</td>\n", "      <td id=\"T_6352b_row21_col1\" class=\"data row21 col1\" >1e3a</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_6352b_row22_col0\" class=\"data row22 col0\" >Imputation Type</td>\n", "      <td id=\"T_6352b_row22_col1\" class=\"data row22 col1\" >simple</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_6352b_row23_col0\" class=\"data row23 col0\" >Iterative Imputation Iteration</td>\n", "      <td id=\"T_6352b_row23_col1\" class=\"data row23 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_6352b_row24_col0\" class=\"data row24 col0\" >Numeric Imputer</td>\n", "      <td id=\"T_6352b_row24_col1\" class=\"data row24 col1\" >mean</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_6352b_row25_col0\" class=\"data row25 col0\" >Iterative Imputation Numeric Model</td>\n", "      <td id=\"T_6352b_row25_col1\" class=\"data row25 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_6352b_row26_col0\" class=\"data row26 col0\" >Categorical Imputer</td>\n", "      <td id=\"T_6352b_row26_col1\" class=\"data row26 col1\" >constant</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_6352b_row27_col0\" class=\"data row27 col0\" >Iterative Imputation Categorical Model</td>\n", "      <td id=\"T_6352b_row27_col1\" class=\"data row27 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_6352b_row28_col0\" class=\"data row28 col0\" >Unknown Categoricals Handling</td>\n", "      <td id=\"T_6352b_row28_col1\" class=\"data row28 col1\" >least_frequent</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_6352b_row29_col0\" class=\"data row29 col0\" >Normalize</td>\n", "      <td id=\"T_6352b_row29_col1\" class=\"data row29 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_6352b_row30_col0\" class=\"data row30 col0\" >Normalize Method</td>\n", "      <td id=\"T_6352b_row30_col1\" class=\"data row30 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_6352b_row31_col0\" class=\"data row31 col0\" >Transformation</td>\n", "      <td id=\"T_6352b_row31_col1\" class=\"data row31 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_6352b_row32_col0\" class=\"data row32 col0\" >Transformation Method</td>\n", "      <td id=\"T_6352b_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_6352b_row33_col0\" class=\"data row33 col0\" >PCA</td>\n", "      <td id=\"T_6352b_row33_col1\" class=\"data row33 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_6352b_row34_col0\" class=\"data row34 col0\" >PCA Method</td>\n", "      <td id=\"T_6352b_row34_col1\" class=\"data row34 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_6352b_row35_col0\" class=\"data row35 col0\" >PCA Components</td>\n", "      <td id=\"T_6352b_row35_col1\" class=\"data row35 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_6352b_row36_col0\" class=\"data row36 col0\" >Ignore Low Variance</td>\n", "      <td id=\"T_6352b_row36_col1\" class=\"data row36 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_6352b_row37_col0\" class=\"data row37 col0\" >Combine Rare Levels</td>\n", "      <td id=\"T_6352b_row37_col1\" class=\"data row37 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_6352b_row38_col0\" class=\"data row38 col0\" >Rare Level Threshold</td>\n", "      <td id=\"T_6352b_row38_col1\" class=\"data row38 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_6352b_row39_col0\" class=\"data row39 col0\" >Numeric Binning</td>\n", "      <td id=\"T_6352b_row39_col1\" class=\"data row39 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "      <td id=\"T_6352b_row40_col0\" class=\"data row40 col0\" >Remove Outliers</td>\n", "      <td id=\"T_6352b_row40_col1\" class=\"data row40 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "      <td id=\"T_6352b_row41_col0\" class=\"data row41 col0\" >Outliers Threshold</td>\n", "      <td id=\"T_6352b_row41_col1\" class=\"data row41 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "      <td id=\"T_6352b_row42_col0\" class=\"data row42 col0\" >Remove Multicollinearity</td>\n", "      <td id=\"T_6352b_row42_col1\" class=\"data row42 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row43\" class=\"row_heading level0 row43\" >43</th>\n", "      <td id=\"T_6352b_row43_col0\" class=\"data row43 col0\" >Multicollinearity Threshold</td>\n", "      <td id=\"T_6352b_row43_col1\" class=\"data row43 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row44\" class=\"row_heading level0 row44\" >44</th>\n", "      <td id=\"T_6352b_row44_col0\" class=\"data row44 col0\" >Remove Perfect Collinearity</td>\n", "      <td id=\"T_6352b_row44_col1\" class=\"data row44 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row45\" class=\"row_heading level0 row45\" >45</th>\n", "      <td id=\"T_6352b_row45_col0\" class=\"data row45 col0\" >Clustering</td>\n", "      <td id=\"T_6352b_row45_col1\" class=\"data row45 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row46\" class=\"row_heading level0 row46\" >46</th>\n", "      <td id=\"T_6352b_row46_col0\" class=\"data row46 col0\" >Clustering Iteration</td>\n", "      <td id=\"T_6352b_row46_col1\" class=\"data row46 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row47\" class=\"row_heading level0 row47\" >47</th>\n", "      <td id=\"T_6352b_row47_col0\" class=\"data row47 col0\" >Polynomial Features</td>\n", "      <td id=\"T_6352b_row47_col1\" class=\"data row47 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row48\" class=\"row_heading level0 row48\" >48</th>\n", "      <td id=\"T_6352b_row48_col0\" class=\"data row48 col0\" >Polynomial Degree</td>\n", "      <td id=\"T_6352b_row48_col1\" class=\"data row48 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row49\" class=\"row_heading level0 row49\" >49</th>\n", "      <td id=\"T_6352b_row49_col0\" class=\"data row49 col0\" >Trignometry Features</td>\n", "      <td id=\"T_6352b_row49_col1\" class=\"data row49 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row50\" class=\"row_heading level0 row50\" >50</th>\n", "      <td id=\"T_6352b_row50_col0\" class=\"data row50 col0\" >Polynomial Threshold</td>\n", "      <td id=\"T_6352b_row50_col1\" class=\"data row50 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row51\" class=\"row_heading level0 row51\" >51</th>\n", "      <td id=\"T_6352b_row51_col0\" class=\"data row51 col0\" >Group Features</td>\n", "      <td id=\"T_6352b_row51_col1\" class=\"data row51 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row52\" class=\"row_heading level0 row52\" >52</th>\n", "      <td id=\"T_6352b_row52_col0\" class=\"data row52 col0\" >Feature Selection</td>\n", "      <td id=\"T_6352b_row52_col1\" class=\"data row52 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row53\" class=\"row_heading level0 row53\" >53</th>\n", "      <td id=\"T_6352b_row53_col0\" class=\"data row53 col0\" >Feature Selection Method</td>\n", "      <td id=\"T_6352b_row53_col1\" class=\"data row53 col1\" >classic</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row54\" class=\"row_heading level0 row54\" >54</th>\n", "      <td id=\"T_6352b_row54_col0\" class=\"data row54 col0\" >Features Selection Threshold</td>\n", "      <td id=\"T_6352b_row54_col1\" class=\"data row54 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row55\" class=\"row_heading level0 row55\" >55</th>\n", "      <td id=\"T_6352b_row55_col0\" class=\"data row55 col0\" >Feature Interaction</td>\n", "      <td id=\"T_6352b_row55_col1\" class=\"data row55 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row56\" class=\"row_heading level0 row56\" >56</th>\n", "      <td id=\"T_6352b_row56_col0\" class=\"data row56 col0\" >Feature Ratio</td>\n", "      <td id=\"T_6352b_row56_col1\" class=\"data row56 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row57\" class=\"row_heading level0 row57\" >57</th>\n", "      <td id=\"T_6352b_row57_col0\" class=\"data row57 col0\" >Interaction Threshold</td>\n", "      <td id=\"T_6352b_row57_col1\" class=\"data row57 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row58\" class=\"row_heading level0 row58\" >58</th>\n", "      <td id=\"T_6352b_row58_col0\" class=\"data row58 col0\" >Fix Imbalance</td>\n", "      <td id=\"T_6352b_row58_col1\" class=\"data row58 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6352b_level0_row59\" class=\"row_heading level0 row59\" >59</th>\n", "      <td id=\"T_6352b_row59_col0\" class=\"data row59 col0\" >Fix Imbalance Method</td>\n", "      <td id=\"T_6352b_row59_col1\" class=\"data row59 col1\" >SMOTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6825b50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["exp_clf101 = setup(data = data, target ='default', session_id=123)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["\n", "Όταν ολοκληρωθεί η εγκατάσταση, τυ<PERSON><PERSON><PERSON><PERSON><PERSON>τα<PERSON> κάποιες πολύ χρήσιμες πληροφορίες. Οι περισσότερες αφορούν το pre-processing pipeline που δημιουργείται με την εκτέλεση της `setup`. Η πλειοψηφία των features δεν ενδιαφέρουν στο συγκεκριμένο μάθημα, ωστόσο υπάρχουν κάποιες λίγες, α<PERSON><PERSON><PERSON> σημαντικές, επισημάνσεις που πρέπει να γίνουν σε αυτό το στάδιο:\n", "\n", "* **session_id :** Ένας ψευδο-τυχ<PERSON><PERSON><PERSON> αριθμός που διαμοιράζεται ως seed σε όλες τις συναρτήσεις, έτσι ώστε τα αποτελέσματα να μπορούν αν αναπαραχθούν. Αν δεν οριστεί το `session_id`, τότε παράγεται ένας τυχαίος αριθμός. Σε αυτό το πείραμα, το `session_id` θα ισούται `123` για να μπορούν να αναπαραχθούν τα αποτελέσματα σε μεταγενέστερο χρόνο.<br/>\n", "* **Target Type :** Binary (<PERSON><PERSON><PERSON>) ή Multiclass (Πολλαπλών Κλάσεων). Το πλήθος των κλάσεων στο οποίο μπορεί να ταξινομηθεί η μεταβλητή-στ<PERSON><PERSON><PERSON> εντοπίζεται αυτόματα και εμφανίζεται. Δεν υπάρχει διαφορά στον τρόπο εκτέλεσης του πειράματος για ταξινόμηση σε δύο ή περισσότερες κλάσεις. Όλες οι λειτουργίες είναι πανομοιότυπες.<br/>\n", "* **Label Encoded :** Όταν η μεταβλητή-στό<PERSON>ος είναι τύπου συμβολοσειράς (string), για παράδειγμα «Ναι» ή «Όχι», ατνί για 1 ή 0, τότε η ετικέτα (label) κωδικοποιείται αυτόματα σετ 1 και 0 και εμφανίζεται η αντιστοίχιση (0 : Όχι, 1 : Ναι). Σε αυτό το πείραμα δεν απαιτείται η κωδικοποίηση της ετικέτας, επειδ<PERSON> η μεταβλητή-στόχ<PERSON> είναι ήδη αριθμητικού τύπου.<br/>\n", "* **Original Data :** Εμφανίζεται το αρχικό μέγεθος του συνόλου δεδομένων. Σε αυτό το πείραμα το (22800, 24) σημαίνει 22.800 δείγματα (samples) και 24 χαρακτηριστικά (features) συμπεριλαμβανομένου και της στήλης-στόχου. <br/>\n", "* **Missing Values :** Όταν λείπουν κάποιες τιμές στα αρχικά δεδομένα, τότε παίρνει την τιμή True (Αληθές). Σε αυτό το πείραμα δε λείπουν τιμές στο σύνολο δεδομένων.<br/>\n", "* **Numeric Features :** Το πλήθος των features που χαρακτηρίστηκαν ως αριθμητικά. Σε αυτό το dataset, 14 από τα 24 features χαρακτηρίστηκαν ως τέτοια.<br/>\n", "* **Categorical Features :** Το πλήθος των features που λαμβάνουν διακριτές τιμές (categorical). Σε αυτό το dataset, 9 από τα 24 features χαρακτηρίστηκαν ως τέτοια. <br/>\n", "* **Transformed Train Set :** Εμφανίζεται το μέγεθος του μετασχηματισμένου συνόλου δεδομένων εκπαίδευσης (training set). Παρατηρ<PERSON>ύμε ότι το αρχικό μέγεθος (22800, 24) μετασχηματίζεται σε (15959, 91) και το πλήθος των features αυξήθηκε σε 91 από 24 εξαιτίας της κωδικοποίησης των categorical features (δηλ. αυτών που λαμβάνουν διακριτές τιμές).<br/>\n", "* **Transformed Test Set :** Εμφανίζεται το μέγεθος του μετασχηματισμένου συνόλου δεδομένων ελέγχου (test / hold-out set) (ΣτΜ: Εδ<PERSON> πρόκειται για ένα μέρος των Δεδομένων Μοντελοποίησης· δεν πρέπει να συγχέεται με τα Άγνωστα Δεδομένα. Για να το διαχωρίσει, λοιπόν, από το test set το αναφέρει ως test / hold-out set). Υπάρχουν 6.841 δείγματα (samples) στο σύνολο δεδομένων ελέγχου. Τα δεδομένα διαχωρίζονται βάσει της προεπιλεγμένης αναλογίας 70/30, αυτ<PERSON> μπορεί να αλλάξει ορίζοντας την τιμή της παραμέτρου `train_size` στη συνάρτηση `setup()`.<br/>\n", "\n", "Παρατηρούμε πως κάποιες εργασίες που είναι απαραίτητες για τη μοντελοποίηση γίνονται αυτόματα, όπως για παράδειγμα η διαχείριση των τιμών που λείπουν (σε αυτή την περίπτωση μπορεί να μη λείπουν τιμές στα δεδομένα εκπαίδευσης, αλλ<PERSON> εξακολουθούμε να χρειασόμαστε μια τέτοια διαχείριση για τα άγνωστα δεδομένα), η κωδικοποίηση των features που λαμβάνουν διακριτές (categorical encoding) κλπ. Οι περισσότερες παράμετροι της `setup()` είναι προαιρετικές και χρησιμοποιούνται για την εξατομίκευση του pre-processing pipeline. Αυτές οι παράμετροι είναι εκτός του πεδίου του συγκεκριμένου μαθήματος, αλλά καθώς προχωράτε στα επόμενα επίπεδα (μέσο και προχωρημένων), θα καλυφτούν με μεγαλύτερη λεπτομέρεια."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 7.0 Σύγκριση Όλων των Μοντέλων"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Αφού έχει ολοκληρωθεί η εκατάσταση (setup), η σύγκριση όλων των μοντέλων για την αξιολόγηση της απόδοσής τους είναι το πρώτο βήμα που συνιστάται (εκτ<PERSON>ς αν γνωρίζετε επακριβώς ποιο είναι το μοντέλο που χρειάζεται, το οποίο δε συνηθίζεται). Αυτή η συνάρτηση εκπαιδεύει όλα τα μοντέλα που βρίσκονται στη βιβλιοθήκη και τα αξιολογεί χρησιμοποιώντας stratified cross validation. Στην έξοδο εμφανίζεται ένας πίνακας βαθμολόγησης με τις μέσες τιμές των Accuracy, AU<PERSON>, <PERSON><PERSON><PERSON>, Precision, F1, Kappa και MCC για όλα τα folds (ΣτΜ: Το cross validation χωρίζει τα δεδομένα εκπαίδευσης σε επιμέρους κομμάτια (folds) - το προεπιλεγμένο πλήθος αυτών των folds είναι 10. Μάλιστα όταν αυτός ο διαχωρισμός είναι stratified, τότε το ποσοστό των samples που ανήκουν στην ίδια κλάση είναι περίπου ίσο σε όλα τα folds.) καθώς και τους χρόνους εκπαίδευσης."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_0c016 th {\n", "  text-align: left;\n", "}\n", "#T_0c016_row0_col0, #T_0c016_row0_col2, #T_0c016_row0_col3, #T_0c016_row0_col5, #T_0c016_row0_col6, #T_0c016_row0_col7, #T_0c016_row1_col0, #T_0c016_row1_col1, #T_0c016_row1_col2, #T_0c016_row1_col3, #T_0c016_row1_col4, #T_0c016_row2_col0, #T_0c016_row2_col1, #T_0c016_row2_col3, #T_0c016_row2_col4, #T_0c016_row2_col5, #T_0c016_row2_col6, #T_0c016_row2_col7, #T_0c016_row3_col0, #T_0c016_row3_col1, #T_0c016_row3_col2, #T_0c016_row3_col3, #T_0c016_row3_col4, #T_0c016_row3_col5, #T_0c016_row3_col6, #T_0c016_row3_col7, #T_0c016_row4_col0, #T_0c016_row4_col1, #T_0c016_row4_col2, #T_0c016_row4_col3, #T_0c016_row4_col4, #T_0c016_row4_col5, #T_0c016_row4_col6, #T_0c016_row4_col7, #T_0c016_row5_col0, #T_0c016_row5_col1, #T_0c016_row5_col2, #T_0c016_row5_col3, #T_0c016_row5_col4, #T_0c016_row5_col5, #T_0c016_row5_col6, #T_0c016_row5_col7, #T_0c016_row6_col0, #T_0c016_row6_col1, #T_0c016_row6_col2, #T_0c016_row6_col3, #T_0c016_row6_col4, #T_0c016_row6_col5, #T_0c016_row6_col6, #T_0c016_row6_col7, #T_0c016_row7_col0, #T_0c016_row7_col1, #T_0c016_row7_col2, #T_0c016_row7_col3, #T_0c016_row7_col4, #T_0c016_row7_col5, #T_0c016_row7_col6, #T_0c016_row7_col7, #T_0c016_row8_col0, #T_0c016_row8_col1, #T_0c016_row8_col2, #T_0c016_row8_col3, #T_0c016_row8_col4, #T_0c016_row8_col5, #T_0c016_row8_col6, #T_0c016_row8_col7, #T_0c016_row9_col0, #T_0c016_row9_col1, #T_0c016_row9_col2, #T_0c016_row9_col3, #T_0c016_row9_col4, #T_0c016_row9_col5, #T_0c016_row9_col6, #T_0c016_row9_col7, #T_0c016_row10_col0, #T_0c016_row10_col1, #T_0c016_row10_col2, #T_0c016_row10_col3, #T_0c016_row10_col4, #T_0c016_row10_col5, #T_0c016_row10_col6, #T_0c016_row10_col7, #T_0c016_row11_col0, #T_0c016_row11_col1, #T_0c016_row11_col2, #T_0c016_row11_col3, #T_0c016_row11_col4, #T_0c016_row11_col5, #T_0c016_row11_col6, #T_0c016_row11_col7, #T_0c016_row12_col0, #T_0c016_row12_col1, #T_0c016_row12_col2, #T_0c016_row12_col3, #T_0c016_row12_col4, #T_0c016_row12_col5, #T_0c016_row12_col6, #T_0c016_row12_col7, #T_0c016_row13_col0, #T_0c016_row13_col1, #T_0c016_row13_col2, #T_0c016_row13_col3, #T_0c016_row13_col4, #T_0c016_row13_col5, #T_0c016_row13_col6, #T_0c016_row13_col7, #T_0c016_row14_col0, #T_0c016_row14_col1, #T_0c016_row14_col2, #T_0c016_row14_col3, #T_0c016_row14_col4, #T_0c016_row14_col5, #T_0c016_row14_col6, #T_0c016_row14_col7, #T_0c016_row15_col0, #T_0c016_row15_col1, #T_0c016_row15_col2, #T_0c016_row15_col4, #T_0c016_row15_col5, #T_0c016_row15_col6, #T_0c016_row15_col7 {\n", "  text-align: left;\n", "}\n", "#T_0c016_row0_col1, #T_0c016_row0_col4, #T_0c016_row1_col5, #T_0c016_row1_col6, #T_0c016_row1_col7, #T_0c016_row2_col2, #T_0c016_row15_col3 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_0c016_row0_col8, #T_0c016_row1_col8, #T_0c016_row2_col8, #T_0c016_row3_col8, #T_0c016_row4_col8, #T_0c016_row5_col8, #T_0c016_row6_col8, #T_0c016_row7_col8, #T_0c016_row8_col8, #T_0c016_row9_col8, #T_0c016_row11_col8, #T_0c016_row12_col8, #T_0c016_row13_col8, #T_0c016_row14_col8, #T_0c016_row15_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_0c016_row10_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_0c016\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_0c016_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_0c016_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_0c016_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_0c016_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_0c016_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_0c016_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_0c016_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_0c016_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "      <th id=\"T_0c016_level0_col8\" class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row0\" class=\"row_heading level0 row0\" >ridge</th>\n", "      <td id=\"T_0c016_row0_col0\" class=\"data row0 col0\" >Ridge Classifier</td>\n", "      <td id=\"T_0c016_row0_col1\" class=\"data row0 col1\" >0.8254</td>\n", "      <td id=\"T_0c016_row0_col2\" class=\"data row0 col2\" >0.0000</td>\n", "      <td id=\"T_0c016_row0_col3\" class=\"data row0 col3\" >0.3637</td>\n", "      <td id=\"T_0c016_row0_col4\" class=\"data row0 col4\" >0.6913</td>\n", "      <td id=\"T_0c016_row0_col5\" class=\"data row0 col5\" >0.4764</td>\n", "      <td id=\"T_0c016_row0_col6\" class=\"data row0 col6\" >0.3836</td>\n", "      <td id=\"T_0c016_row0_col7\" class=\"data row0 col7\" >0.4122</td>\n", "      <td id=\"T_0c016_row0_col8\" class=\"data row0 col8\" >0.0180</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row1\" class=\"row_heading level0 row1\" >lda</th>\n", "      <td id=\"T_0c016_row1_col0\" class=\"data row1 col0\" >Linear Discriminant Analysis</td>\n", "      <td id=\"T_0c016_row1_col1\" class=\"data row1 col1\" >0.8247</td>\n", "      <td id=\"T_0c016_row1_col2\" class=\"data row1 col2\" >0.7634</td>\n", "      <td id=\"T_0c016_row1_col3\" class=\"data row1 col3\" >0.3755</td>\n", "      <td id=\"T_0c016_row1_col4\" class=\"data row1 col4\" >0.6794</td>\n", "      <td id=\"T_0c016_row1_col5\" class=\"data row1 col5\" >0.4835</td>\n", "      <td id=\"T_0c016_row1_col6\" class=\"data row1 col6\" >0.3884</td>\n", "      <td id=\"T_0c016_row1_col7\" class=\"data row1 col7\" >0.4132</td>\n", "      <td id=\"T_0c016_row1_col8\" class=\"data row1 col8\" >0.1270</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row2\" class=\"row_heading level0 row2\" >gbc</th>\n", "      <td id=\"T_0c016_row2_col0\" class=\"data row2 col0\" >Gradient Boosting Classifier</td>\n", "      <td id=\"T_0c016_row2_col1\" class=\"data row2 col1\" >0.8226</td>\n", "      <td id=\"T_0c016_row2_col2\" class=\"data row2 col2\" >0.7789</td>\n", "      <td id=\"T_0c016_row2_col3\" class=\"data row2 col3\" >0.3551</td>\n", "      <td id=\"T_0c016_row2_col4\" class=\"data row2 col4\" >0.6806</td>\n", "      <td id=\"T_0c016_row2_col5\" class=\"data row2 col5\" >0.4664</td>\n", "      <td id=\"T_0c016_row2_col6\" class=\"data row2 col6\" >0.3725</td>\n", "      <td id=\"T_0c016_row2_col7\" class=\"data row2 col7\" >0.4010</td>\n", "      <td id=\"T_0c016_row2_col8\" class=\"data row2 col8\" >0.6050</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row3\" class=\"row_heading level0 row3\" >ada</th>\n", "      <td id=\"T_0c016_row3_col0\" class=\"data row3 col0\" >Ada Boost Classifier</td>\n", "      <td id=\"T_0c016_row3_col1\" class=\"data row3 col1\" >0.8221</td>\n", "      <td id=\"T_0c016_row3_col2\" class=\"data row3 col2\" >0.7697</td>\n", "      <td id=\"T_0c016_row3_col3\" class=\"data row3 col3\" >0.3505</td>\n", "      <td id=\"T_0c016_row3_col4\" class=\"data row3 col4\" >0.6811</td>\n", "      <td id=\"T_0c016_row3_col5\" class=\"data row3 col5\" >0.4626</td>\n", "      <td id=\"T_0c016_row3_col6\" class=\"data row3 col6\" >0.3690</td>\n", "      <td id=\"T_0c016_row3_col7\" class=\"data row3 col7\" >0.3983</td>\n", "      <td id=\"T_0c016_row3_col8\" class=\"data row3 col8\" >0.1550</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row4\" class=\"row_heading level0 row4\" >catboost</th>\n", "      <td id=\"T_0c016_row4_col0\" class=\"data row4 col0\" >CatBoost Classifier</td>\n", "      <td id=\"T_0c016_row4_col1\" class=\"data row4 col1\" >0.8219</td>\n", "      <td id=\"T_0c016_row4_col2\" class=\"data row4 col2\" >0.7762</td>\n", "      <td id=\"T_0c016_row4_col3\" class=\"data row4 col3\" >0.3631</td>\n", "      <td id=\"T_0c016_row4_col4\" class=\"data row4 col4\" >0.6712</td>\n", "      <td id=\"T_0c016_row4_col5\" class=\"data row4 col5\" >0.4711</td>\n", "      <td id=\"T_0c016_row4_col6\" class=\"data row4 col6\" >0.3753</td>\n", "      <td id=\"T_0c016_row4_col7\" class=\"data row4 col7\" >0.4010</td>\n", "      <td id=\"T_0c016_row4_col8\" class=\"data row4 col8\" >5.7750</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row5\" class=\"row_heading level0 row5\" >lightgbm</th>\n", "      <td id=\"T_0c016_row5_col0\" class=\"data row5 col0\" >Light Gradient Boosting Machine</td>\n", "      <td id=\"T_0c016_row5_col1\" class=\"data row5 col1\" >0.8210</td>\n", "      <td id=\"T_0c016_row5_col2\" class=\"data row5 col2\" >0.7750</td>\n", "      <td id=\"T_0c016_row5_col3\" class=\"data row5 col3\" >0.3609</td>\n", "      <td id=\"T_0c016_row5_col4\" class=\"data row5 col4\" >0.6679</td>\n", "      <td id=\"T_0c016_row5_col5\" class=\"data row5 col5\" >0.4683</td>\n", "      <td id=\"T_0c016_row5_col6\" class=\"data row5 col6\" >0.3721</td>\n", "      <td id=\"T_0c016_row5_col7\" class=\"data row5 col7\" >0.3977</td>\n", "      <td id=\"T_0c016_row5_col8\" class=\"data row5 col8\" >0.0560</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row6\" class=\"row_heading level0 row6\" >rf</th>\n", "      <td id=\"T_0c016_row6_col0\" class=\"data row6 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_0c016_row6_col1\" class=\"data row6 col1\" >0.8199</td>\n", "      <td id=\"T_0c016_row6_col2\" class=\"data row6 col2\" >0.7598</td>\n", "      <td id=\"T_0c016_row6_col3\" class=\"data row6 col3\" >0.3663</td>\n", "      <td id=\"T_0c016_row6_col4\" class=\"data row6 col4\" >0.6601</td>\n", "      <td id=\"T_0c016_row6_col5\" class=\"data row6 col5\" >0.4707</td>\n", "      <td id=\"T_0c016_row6_col6\" class=\"data row6 col6\" >0.3727</td>\n", "      <td id=\"T_0c016_row6_col7\" class=\"data row6 col7\" >0.3965</td>\n", "      <td id=\"T_0c016_row6_col8\" class=\"data row6 col8\" >0.3480</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row7\" class=\"row_heading level0 row7\" >xgboost</th>\n", "      <td id=\"T_0c016_row7_col0\" class=\"data row7 col0\" >Extreme Gradient Boosting</td>\n", "      <td id=\"T_0c016_row7_col1\" class=\"data row7 col1\" >0.8160</td>\n", "      <td id=\"T_0c016_row7_col2\" class=\"data row7 col2\" >0.7561</td>\n", "      <td id=\"T_0c016_row7_col3\" class=\"data row7 col3\" >0.3629</td>\n", "      <td id=\"T_0c016_row7_col4\" class=\"data row7 col4\" >0.6391</td>\n", "      <td id=\"T_0c016_row7_col5\" class=\"data row7 col5\" >0.4626</td>\n", "      <td id=\"T_0c016_row7_col6\" class=\"data row7 col6\" >0.3617</td>\n", "      <td id=\"T_0c016_row7_col7\" class=\"data row7 col7\" >0.3829</td>\n", "      <td id=\"T_0c016_row7_col8\" class=\"data row7 col8\" >0.7300</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row8\" class=\"row_heading level0 row8\" >et</th>\n", "      <td id=\"T_0c016_row8_col0\" class=\"data row8 col0\" >Extra Trees Classifier</td>\n", "      <td id=\"T_0c016_row8_col1\" class=\"data row8 col1\" >0.8092</td>\n", "      <td id=\"T_0c016_row8_col2\" class=\"data row8 col2\" >0.7377</td>\n", "      <td id=\"T_0c016_row8_col3\" class=\"data row8 col3\" >0.3677</td>\n", "      <td id=\"T_0c016_row8_col4\" class=\"data row8 col4\" >0.6047</td>\n", "      <td id=\"T_0c016_row8_col5\" class=\"data row8 col5\" >0.4571</td>\n", "      <td id=\"T_0c016_row8_col6\" class=\"data row8 col6\" >0.3497</td>\n", "      <td id=\"T_0c016_row8_col7\" class=\"data row8 col7\" >0.3657</td>\n", "      <td id=\"T_0c016_row8_col8\" class=\"data row8 col8\" >0.3720</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row9\" class=\"row_heading level0 row9\" >lr</th>\n", "      <td id=\"T_0c016_row9_col0\" class=\"data row9 col0\" >Logistic Regression</td>\n", "      <td id=\"T_0c016_row9_col1\" class=\"data row9 col1\" >0.7814</td>\n", "      <td id=\"T_0c016_row9_col2\" class=\"data row9 col2\" >0.6410</td>\n", "      <td id=\"T_0c016_row9_col3\" class=\"data row9 col3\" >0.0003</td>\n", "      <td id=\"T_0c016_row9_col4\" class=\"data row9 col4\" >0.1000</td>\n", "      <td id=\"T_0c016_row9_col5\" class=\"data row9 col5\" >0.0006</td>\n", "      <td id=\"T_0c016_row9_col6\" class=\"data row9 col6\" >0.0003</td>\n", "      <td id=\"T_0c016_row9_col7\" class=\"data row9 col7\" >0.0034</td>\n", "      <td id=\"T_0c016_row9_col8\" class=\"data row9 col8\" >0.4530</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row10\" class=\"row_heading level0 row10\" >dummy</th>\n", "      <td id=\"T_0c016_row10_col0\" class=\"data row10 col0\" >Dummy Classifier</td>\n", "      <td id=\"T_0c016_row10_col1\" class=\"data row10 col1\" >0.7814</td>\n", "      <td id=\"T_0c016_row10_col2\" class=\"data row10 col2\" >0.5000</td>\n", "      <td id=\"T_0c016_row10_col3\" class=\"data row10 col3\" >0.0000</td>\n", "      <td id=\"T_0c016_row10_col4\" class=\"data row10 col4\" >0.0000</td>\n", "      <td id=\"T_0c016_row10_col5\" class=\"data row10 col5\" >0.0000</td>\n", "      <td id=\"T_0c016_row10_col6\" class=\"data row10 col6\" >0.0000</td>\n", "      <td id=\"T_0c016_row10_col7\" class=\"data row10 col7\" >0.0000</td>\n", "      <td id=\"T_0c016_row10_col8\" class=\"data row10 col8\" >0.0130</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row11\" class=\"row_heading level0 row11\" >knn</th>\n", "      <td id=\"T_0c016_row11_col0\" class=\"data row11 col0\" >K Neighbors Classifier</td>\n", "      <td id=\"T_0c016_row11_col1\" class=\"data row11 col1\" >0.7547</td>\n", "      <td id=\"T_0c016_row11_col2\" class=\"data row11 col2\" >0.5939</td>\n", "      <td id=\"T_0c016_row11_col3\" class=\"data row11 col3\" >0.1763</td>\n", "      <td id=\"T_0c016_row11_col4\" class=\"data row11 col4\" >0.3719</td>\n", "      <td id=\"T_0c016_row11_col5\" class=\"data row11 col5\" >0.2388</td>\n", "      <td id=\"T_0c016_row11_col6\" class=\"data row11 col6\" >0.1145</td>\n", "      <td id=\"T_0c016_row11_col7\" class=\"data row11 col7\" >0.1259</td>\n", "      <td id=\"T_0c016_row11_col8\" class=\"data row11 col8\" >0.2350</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row12\" class=\"row_heading level0 row12\" >dt</th>\n", "      <td id=\"T_0c016_row12_col0\" class=\"data row12 col0\" >Decision Tree Classifier</td>\n", "      <td id=\"T_0c016_row12_col1\" class=\"data row12 col1\" >0.7293</td>\n", "      <td id=\"T_0c016_row12_col2\" class=\"data row12 col2\" >0.6147</td>\n", "      <td id=\"T_0c016_row12_col3\" class=\"data row12 col3\" >0.4104</td>\n", "      <td id=\"T_0c016_row12_col4\" class=\"data row12 col4\" >0.3878</td>\n", "      <td id=\"T_0c016_row12_col5\" class=\"data row12 col5\" >0.3986</td>\n", "      <td id=\"T_0c016_row12_col6\" class=\"data row12 col6\" >0.2242</td>\n", "      <td id=\"T_0c016_row12_col7\" class=\"data row12 col7\" >0.2245</td>\n", "      <td id=\"T_0c016_row12_col8\" class=\"data row12 col8\" >0.0560</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row13\" class=\"row_heading level0 row13\" >svm</th>\n", "      <td id=\"T_0c016_row13_col0\" class=\"data row13 col0\" >SVM - Linear Kernel</td>\n", "      <td id=\"T_0c016_row13_col1\" class=\"data row13 col1\" >0.7277</td>\n", "      <td id=\"T_0c016_row13_col2\" class=\"data row13 col2\" >0.0000</td>\n", "      <td id=\"T_0c016_row13_col3\" class=\"data row13 col3\" >0.1017</td>\n", "      <td id=\"T_0c016_row13_col4\" class=\"data row13 col4\" >0.1671</td>\n", "      <td id=\"T_0c016_row13_col5\" class=\"data row13 col5\" >0.0984</td>\n", "      <td id=\"T_0c016_row13_col6\" class=\"data row13 col6\" >0.0067</td>\n", "      <td id=\"T_0c016_row13_col7\" class=\"data row13 col7\" >0.0075</td>\n", "      <td id=\"T_0c016_row13_col8\" class=\"data row13 col8\" >0.1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row14\" class=\"row_heading level0 row14\" >qda</th>\n", "      <td id=\"T_0c016_row14_col0\" class=\"data row14 col0\" >Quadratic Discriminant Analysis</td>\n", "      <td id=\"T_0c016_row14_col1\" class=\"data row14 col1\" >0.5098</td>\n", "      <td id=\"T_0c016_row14_col2\" class=\"data row14 col2\" >0.5473</td>\n", "      <td id=\"T_0c016_row14_col3\" class=\"data row14 col3\" >0.6141</td>\n", "      <td id=\"T_0c016_row14_col4\" class=\"data row14 col4\" >0.2472</td>\n", "      <td id=\"T_0c016_row14_col5\" class=\"data row14 col5\" >0.3488</td>\n", "      <td id=\"T_0c016_row14_col6\" class=\"data row14 col6\" >0.0600</td>\n", "      <td id=\"T_0c016_row14_col7\" class=\"data row14 col7\" >0.0805</td>\n", "      <td id=\"T_0c016_row14_col8\" class=\"data row14 col8\" >0.1110</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_0c016_level0_row15\" class=\"row_heading level0 row15\" >nb</th>\n", "      <td id=\"T_0c016_row15_col0\" class=\"data row15 col0\" >Naive <PERSON></td>\n", "      <td id=\"T_0c016_row15_col1\" class=\"data row15 col1\" >0.3760</td>\n", "      <td id=\"T_0c016_row15_col2\" class=\"data row15 col2\" >0.6442</td>\n", "      <td id=\"T_0c016_row15_col3\" class=\"data row15 col3\" >0.8845</td>\n", "      <td id=\"T_0c016_row15_col4\" class=\"data row15 col4\" >0.2441</td>\n", "      <td id=\"T_0c016_row15_col5\" class=\"data row15 col5\" >0.3826</td>\n", "      <td id=\"T_0c016_row15_col6\" class=\"data row15 col6\" >0.0608</td>\n", "      <td id=\"T_0c016_row15_col7\" class=\"data row15 col7\" >0.1207</td>\n", "      <td id=\"T_0c016_row15_col8\" class=\"data row15 col8\" >0.0190</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6763b80>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["best_model = compare_models()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Με δύο απλές λέξεις κώδικα ***(ούτε καν μια ολόκληρη γραμμή κώδικα)*** έχουμε εκπαιδεύσει και αξιολογήσει περισσότερα από 15 μοντέλα χρησιμοποιώντας cross validation. Ο πίνακας βαθμολόγησης επισημαίνει με έντονο χρώμα της καλύτερες επιδόσεις για λόγους σύγκρισης. Ο πίνακας κατατάσσεται με βάση το 'Accuracy', από το υψηλότερο στο χαμηλότερο. Αυτή η προεπιλογή (δηλ. το Accuracy) μπορεί να αλλάξει χρησιμοποιώντας την παράμετρο `sort`. Για παράδειγμα, το `compare_models(sort = 'Recall')` θα κατατάξει τον πίνακα βαθμολόγησης με βάση το Recall αντί του Accuracy. Αν θέλετε να αλλάξετε την προεπιλεγμένη τιμή για το πλήθος των folds (`10`) σε κάποια άλλη, τότε μπορείτε να χρησιμοποιήσετε την παράμετρο `fold`. Για παράδειγμα, το `compare_models(fold = 5)` θα συγκρίνει όλα τα μοντέλα κάνοντας 5 fold cross validation. Η μείωση του πλήθους των folds θα βελτιώσει το χρόνο εκπαίδευσης. Εκ προεπιλογής, το `compare_models` επιστρέφει το μοντέλο με την καλύτερη απόδοση με βάση την προεπιλεγμένη σειρά κατάταξης, αλλά μπορεί επίσης να επιστρέψει μια λίστα με τα N καλύτερα μοντέλα χρησιμοποιώντας την παράμετρο `n_select`."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RidgeClassifier(alpha=1.0, class_weight=None, copy_X=True, fit_intercept=True,\n", "                max_iter=None, normalize=False, random_state=123, solver='auto',\n", "                tol=0.001)\n"]}], "source": ["print(best_model)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 8.0 Δημιουργ<PERSON><PERSON> ενός <PERSON>οντέλου"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Η συνάρτηση `create_model` αποτελεί τη θεμελιωδέστερη της PyCaret και συχνά αποτελεί τη βάση πάνω στην οποία αναπτύσσονται όλες οι λειτουργίες της PyCaret. Όπως προδίδει και το όνομα της, αυτή η συνάρτηση εκπαιδεύει και αξιολογεί ένα μοντέλο χρησιμοποιώντας cross validation - το τελευταίο μπορεί να οριστεί με την παράμετρο `fold`. Στην έξοδο εκτυπώνεται ένας πίνακας βαθμολόγησης που δείχνει τις τιμές των Accuracy, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> και MCC ανά fold.\n", "\n", "Σε όλο το υπόλοιπο μάθημα θα δουλέψουμε με τα μοντέλα που παραθέτονται παρακάτω· θα τα ορίσουμε ως υποψήφια μοντέλα. Η επιλογή των μοντέλων έγινε καθαρά για λόγους επίδειξης και δε σημαίνει, απα<PERSON>α<PERSON>τήτως, ότι έχουν την καλύτερη απόδοση ή είναι τα ιδανικότερα γι' αυτό τον τύπο δεδομένων.\n", "\n", "- Decision Tree Classifier ('dt')\n", "- K Neighbors Classifier ('knn')\n", "- Random Forest Classifier ('rf')\n", "\n", "Υπάρχουν περισσότεροι από 18 ταξινομητές στη βιβλιοθήκη μοντέλων της PyCaret. Για να δείτε μια λίστα με όλους τους ταξινομητές μπορείτε να ελέγξετε το `docstring` ή να χρησιμοποιήσετε τη συνάρτηση `models`."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Reference</th>\n", "      <th>Turbo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lr</th>\n", "      <td>Logistic Regression</td>\n", "      <td>sklearn.linear_model._logistic.LogisticRegression</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>knn</th>\n", "      <td>K Neighbors Classifier</td>\n", "      <td>sklearn.neighbors._classification.KNeighborsCl...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>nb</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>sklearn.naive_bayes.GaussianNB</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <td>Decision Tree Classifier</td>\n", "      <td>sklearn.tree._classes.DecisionTreeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>svm</th>\n", "      <td>SVM - Linear Kernel</td>\n", "      <td>sklearn.linear_model._stochastic_gradient.SGDC...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rbfsvm</th>\n", "      <td>SVM - Radial <PERSON></td>\n", "      <td>sklearn.svm._classes.SVC</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gpc</th>\n", "      <td>Gaussian Process Classifier</td>\n", "      <td>sklearn.gaussian_process._gpc.GaussianProcessC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mlp</th>\n", "      <td>MLP Classifier</td>\n", "      <td>sklearn.neural_network._multilayer_perceptron....</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ridge</th>\n", "      <td>Ridge Classifier</td>\n", "      <td>sklearn.linear_model._ridge.RidgeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rf</th>\n", "      <td>Random Forest Classifier</td>\n", "      <td>sklearn.ensemble._forest.RandomForestClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>qda</th>\n", "      <td>Quadratic Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.QuadraticDiscrim...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ada</th>\n", "      <td>Ada Boost Classifier</td>\n", "      <td>sklearn.ensemble._weight_boosting.AdaBoostClas...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gbc</th>\n", "      <td>Gradient Boosting Classifier</td>\n", "      <td>sklearn.ensemble._gb.GradientBoostingClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lda</th>\n", "      <td>Linear Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.LinearDiscrimina...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>et</th>\n", "      <td>Extra Trees Classifier</td>\n", "      <td>sklearn.ensemble._forest.ExtraTreesClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>xgboost</th>\n", "      <td>Extreme Gradient Boosting</td>\n", "      <td>xgboost.sklearn.XGBClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lightgbm</th>\n", "      <td>Light Gradient Boosting Machine</td>\n", "      <td>lightgbm.sklearn.LGBMClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>catboost</th>\n", "      <td>CatBoost Classifier</td>\n", "      <td>catboost.core.CatBoostClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dummy</th>\n", "      <td>Dummy Classifier</td>\n", "      <td>sklearn.dummy.DummyClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     Name  \\\n", "ID                                          \n", "lr                    Logistic Regression   \n", "knn                K Neighbors Classifier   \n", "nb                            <PERSON><PERSON>   \n", "dt               Decision Tree Classifier   \n", "svm                   SVM - Linear Kernel   \n", "rbfsvm                SVM - Radial Kernel   \n", "gpc           Gaussian Process Classifier   \n", "mlp                        MLP Classifier   \n", "ridge                    Ridge Classifier   \n", "rf               Random Forest Classifier   \n", "qda       Quadratic Discriminant Analysis   \n", "ada                  Ada Boost Classifier   \n", "gbc          Gradient Boosting Classifier   \n", "lda          Linear Discriminant Analysis   \n", "et                 Extra Trees Classifier   \n", "xgboost         Extreme Gradient Boosting   \n", "lightgbm  Light Gradient Boosting Machine   \n", "catboost              CatBoost Classifier   \n", "dummy                    Dummy Classifier   \n", "\n", "                                                  Reference  Turbo  \n", "ID                                                                  \n", "lr        sklearn.linear_model._logistic.LogisticRegression   True  \n", "knn       sklearn.neighbors._classification.KNeighborsCl...   True  \n", "nb                           sklearn.naive_bayes.GaussianNB   True  \n", "dt             sklearn.tree._classes.DecisionTreeClassifier   True  \n", "svm       sklearn.linear_model._stochastic_gradient.SGDC...   True  \n", "rbfsvm                             sklearn.svm._classes.SVC  False  \n", "gpc       sklearn.gaussian_process._gpc.GaussianProcessC...  False  \n", "mlp       sklearn.neural_network._multilayer_perceptron....  False  \n", "ridge           sklearn.linear_model._ridge.RidgeClassifier   True  \n", "rf          sklearn.ensemble._forest.RandomForestClassifier   True  \n", "qda       sklearn.discriminant_analysis.QuadraticDiscrim...   True  \n", "ada       sklearn.ensemble._weight_boosting.AdaBoostClas...   True  \n", "gbc         sklearn.ensemble._gb.GradientBoostingClassifier   True  \n", "lda       sklearn.discriminant_analysis.LinearDiscrimina...   True  \n", "et            sklearn.ensemble._forest.ExtraTreesClassifier   True  \n", "xgboost                       xgboost.sklearn.XGBClassifier   True  \n", "lightgbm                    lightgbm.sklearn.LGBMClassifier   True  \n", "catboost                   catboost.core.CatBoostClassifier   True  \n", "dummy                         sklearn.dummy.DummyClassifier   True  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["models()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 8.1 Decision Tree Classifier"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_1aa83_row10_col0, #T_1aa83_row10_col1, #T_1aa83_row10_col2, #T_1aa83_row10_col3, #T_1aa83_row10_col4, #T_1aa83_row10_col5, #T_1aa83_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_1aa83\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_1aa83_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_1aa83_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_1aa83_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_1aa83_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_1aa83_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_1aa83_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_1aa83_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_1aa83_row0_col0\" class=\"data row0 col0\" >0.7343</td>\n", "      <td id=\"T_1aa83_row0_col1\" class=\"data row0 col1\" >0.6257</td>\n", "      <td id=\"T_1aa83_row0_col2\" class=\"data row0 col2\" >0.4327</td>\n", "      <td id=\"T_1aa83_row0_col3\" class=\"data row0 col3\" >0.4005</td>\n", "      <td id=\"T_1aa83_row0_col4\" class=\"data row0 col4\" >0.4160</td>\n", "      <td id=\"T_1aa83_row0_col5\" class=\"data row0 col5\" >0.2444</td>\n", "      <td id=\"T_1aa83_row0_col6\" class=\"data row0 col6\" >0.2447</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_1aa83_row1_col0\" class=\"data row1 col0\" >0.7325</td>\n", "      <td id=\"T_1aa83_row1_col1\" class=\"data row1 col1\" >0.6277</td>\n", "      <td id=\"T_1aa83_row1_col2\" class=\"data row1 col2\" >0.4384</td>\n", "      <td id=\"T_1aa83_row1_col3\" class=\"data row1 col3\" >0.3984</td>\n", "      <td id=\"T_1aa83_row1_col4\" class=\"data row1 col4\" >0.4175</td>\n", "      <td id=\"T_1aa83_row1_col5\" class=\"data row1 col5\" >0.2443</td>\n", "      <td id=\"T_1aa83_row1_col6\" class=\"data row1 col6\" >0.2448</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_1aa83_row2_col0\" class=\"data row2 col0\" >0.7431</td>\n", "      <td id=\"T_1aa83_row2_col1\" class=\"data row2 col1\" >0.6282</td>\n", "      <td id=\"T_1aa83_row2_col2\" class=\"data row2 col2\" >0.4241</td>\n", "      <td id=\"T_1aa83_row2_col3\" class=\"data row2 col3\" >0.4146</td>\n", "      <td id=\"T_1aa83_row2_col4\" class=\"data row2 col4\" >0.4193</td>\n", "      <td id=\"T_1aa83_row2_col5\" class=\"data row2 col5\" >0.2544</td>\n", "      <td id=\"T_1aa83_row2_col6\" class=\"data row2 col6\" >0.2544</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_1aa83_row3_col0\" class=\"data row3 col0\" >0.7274</td>\n", "      <td id=\"T_1aa83_row3_col1\" class=\"data row3 col1\" >0.6151</td>\n", "      <td id=\"T_1aa83_row3_col2\" class=\"data row3 col2\" >0.4155</td>\n", "      <td id=\"T_1aa83_row3_col3\" class=\"data row3 col3\" >0.3856</td>\n", "      <td id=\"T_1aa83_row3_col4\" class=\"data row3 col4\" >0.4000</td>\n", "      <td id=\"T_1aa83_row3_col5\" class=\"data row3 col5\" >0.2240</td>\n", "      <td id=\"T_1aa83_row3_col6\" class=\"data row3 col6\" >0.2242</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_1aa83_row4_col0\" class=\"data row4 col0\" >0.7187</td>\n", "      <td id=\"T_1aa83_row4_col1\" class=\"data row4 col1\" >0.6054</td>\n", "      <td id=\"T_1aa83_row4_col2\" class=\"data row4 col2\" >0.4040</td>\n", "      <td id=\"T_1aa83_row4_col3\" class=\"data row4 col3\" >0.3691</td>\n", "      <td id=\"T_1aa83_row4_col4\" class=\"data row4 col4\" >0.3858</td>\n", "      <td id=\"T_1aa83_row4_col5\" class=\"data row4 col5\" >0.2038</td>\n", "      <td id=\"T_1aa83_row4_col6\" class=\"data row4 col6\" >0.2042</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_1aa83_row5_col0\" class=\"data row5 col0\" >0.7187</td>\n", "      <td id=\"T_1aa83_row5_col1\" class=\"data row5 col1\" >0.6014</td>\n", "      <td id=\"T_1aa83_row5_col2\" class=\"data row5 col2\" >0.3897</td>\n", "      <td id=\"T_1aa83_row5_col3\" class=\"data row5 col3\" >0.3656</td>\n", "      <td id=\"T_1aa83_row5_col4\" class=\"data row5 col4\" >0.3773</td>\n", "      <td id=\"T_1aa83_row5_col5\" class=\"data row5 col5\" >0.1958</td>\n", "      <td id=\"T_1aa83_row5_col6\" class=\"data row5 col6\" >0.1960</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_1aa83_row6_col0\" class=\"data row6 col0\" >0.7206</td>\n", "      <td id=\"T_1aa83_row6_col1\" class=\"data row6 col1\" >0.6128</td>\n", "      <td id=\"T_1aa83_row6_col2\" class=\"data row6 col2\" >0.4212</td>\n", "      <td id=\"T_1aa83_row6_col3\" class=\"data row6 col3\" >0.3760</td>\n", "      <td id=\"T_1aa83_row6_col4\" class=\"data row6 col4\" >0.3973</td>\n", "      <td id=\"T_1aa83_row6_col5\" class=\"data row6 col5\" >0.2162</td>\n", "      <td id=\"T_1aa83_row6_col6\" class=\"data row6 col6\" >0.2168</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_1aa83_row7_col0\" class=\"data row7 col0\" >0.7331</td>\n", "      <td id=\"T_1aa83_row7_col1\" class=\"data row7 col1\" >0.5986</td>\n", "      <td id=\"T_1aa83_row7_col2\" class=\"data row7 col2\" >0.3610</td>\n", "      <td id=\"T_1aa83_row7_col3\" class=\"data row7 col3\" >0.3830</td>\n", "      <td id=\"T_1aa83_row7_col4\" class=\"data row7 col4\" >0.3717</td>\n", "      <td id=\"T_1aa83_row7_col5\" class=\"data row7 col5\" >0.2024</td>\n", "      <td id=\"T_1aa83_row7_col6\" class=\"data row7 col6\" >0.2026</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_1aa83_row8_col0\" class=\"data row8 col0\" >0.7206</td>\n", "      <td id=\"T_1aa83_row8_col1\" class=\"data row8 col1\" >0.6045</td>\n", "      <td id=\"T_1aa83_row8_col2\" class=\"data row8 col2\" >0.3983</td>\n", "      <td id=\"T_1aa83_row8_col3\" class=\"data row8 col3\" >0.3707</td>\n", "      <td id=\"T_1aa83_row8_col4\" class=\"data row8 col4\" >0.3840</td>\n", "      <td id=\"T_1aa83_row8_col5\" class=\"data row8 col5\" >0.2036</td>\n", "      <td id=\"T_1aa83_row8_col6\" class=\"data row8 col6\" >0.2038</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_1aa83_row9_col0\" class=\"data row9 col0\" >0.7442</td>\n", "      <td id=\"T_1aa83_row9_col1\" class=\"data row9 col1\" >0.6272</td>\n", "      <td id=\"T_1aa83_row9_col2\" class=\"data row9 col2\" >0.4195</td>\n", "      <td id=\"T_1aa83_row9_col3\" class=\"data row9 col3\" >0.4148</td>\n", "      <td id=\"T_1aa83_row9_col4\" class=\"data row9 col4\" >0.4171</td>\n", "      <td id=\"T_1aa83_row9_col5\" class=\"data row9 col5\" >0.2533</td>\n", "      <td id=\"T_1aa83_row9_col6\" class=\"data row9 col6\" >0.2533</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_1aa83_row10_col0\" class=\"data row10 col0\" >0.7293</td>\n", "      <td id=\"T_1aa83_row10_col1\" class=\"data row10 col1\" >0.6147</td>\n", "      <td id=\"T_1aa83_row10_col2\" class=\"data row10 col2\" >0.4104</td>\n", "      <td id=\"T_1aa83_row10_col3\" class=\"data row10 col3\" >0.3878</td>\n", "      <td id=\"T_1aa83_row10_col4\" class=\"data row10 col4\" >0.3986</td>\n", "      <td id=\"T_1aa83_row10_col5\" class=\"data row10 col5\" >0.2242</td>\n", "      <td id=\"T_1aa83_row10_col6\" class=\"data row10 col6\" >0.2245</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1aa83_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_1aa83_row11_col0\" class=\"data row11 col0\" >0.0092</td>\n", "      <td id=\"T_1aa83_row11_col1\" class=\"data row11 col1\" >0.0112</td>\n", "      <td id=\"T_1aa83_row11_col2\" class=\"data row11 col2\" >0.0218</td>\n", "      <td id=\"T_1aa83_row11_col3\" class=\"data row11 col3\" >0.0174</td>\n", "      <td id=\"T_1aa83_row11_col4\" class=\"data row11 col4\" >0.0173</td>\n", "      <td id=\"T_1aa83_row11_col5\" class=\"data row11 col5\" >0.0218</td>\n", "      <td id=\"T_1aa83_row11_col6\" class=\"data row11 col6\" >0.0218</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6800d30>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dt = create_model('dt')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='gini',\n", "                       max_depth=None, max_features=None, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.0, min_impurity_split=None,\n", "                       min_samples_leaf=1, min_samples_split=2,\n", "                       min_weight_fraction_leaf=0.0, presort='deprecated',\n", "                       random_state=123, splitter='best')\n"]}], "source": ["#trained model object is stored in the variable 'dt'.\n", "#το εκπαιδευμένο μοντέλο αποθηκεύεται στη μεταβλητή 'dt'.\n", "print(dt)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 8.2 K <PERSON>eighbors Classifier"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_5474f_row10_col0, #T_5474f_row10_col1, #T_5474f_row10_col2, #T_5474f_row10_col3, #T_5474f_row10_col4, #T_5474f_row10_col5, #T_5474f_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_5474f\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_5474f_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_5474f_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_5474f_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_5474f_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_5474f_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_5474f_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_5474f_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_5474f_row0_col0\" class=\"data row0 col0\" >0.7469</td>\n", "      <td id=\"T_5474f_row0_col1\" class=\"data row0 col1\" >0.6020</td>\n", "      <td id=\"T_5474f_row0_col2\" class=\"data row0 col2\" >0.1920</td>\n", "      <td id=\"T_5474f_row0_col3\" class=\"data row0 col3\" >0.3545</td>\n", "      <td id=\"T_5474f_row0_col4\" class=\"data row0 col4\" >0.2491</td>\n", "      <td id=\"T_5474f_row0_col5\" class=\"data row0 col5\" >0.1128</td>\n", "      <td id=\"T_5474f_row0_col6\" class=\"data row0 col6\" >0.1204</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_5474f_row1_col0\" class=\"data row1 col0\" >0.7550</td>\n", "      <td id=\"T_5474f_row1_col1\" class=\"data row1 col1\" >0.5894</td>\n", "      <td id=\"T_5474f_row1_col2\" class=\"data row1 col2\" >0.2092</td>\n", "      <td id=\"T_5474f_row1_col3\" class=\"data row1 col3\" >0.3883</td>\n", "      <td id=\"T_5474f_row1_col4\" class=\"data row1 col4\" >0.2719</td>\n", "      <td id=\"T_5474f_row1_col5\" class=\"data row1 col5\" >0.1402</td>\n", "      <td id=\"T_5474f_row1_col6\" class=\"data row1 col6\" >0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_5474f_row2_col0\" class=\"data row2 col0\" >0.7506</td>\n", "      <td id=\"T_5474f_row2_col1\" class=\"data row2 col1\" >0.5883</td>\n", "      <td id=\"T_5474f_row2_col2\" class=\"data row2 col2\" >0.1576</td>\n", "      <td id=\"T_5474f_row2_col3\" class=\"data row2 col3\" >0.3459</td>\n", "      <td id=\"T_5474f_row2_col4\" class=\"data row2 col4\" >0.2165</td>\n", "      <td id=\"T_5474f_row2_col5\" class=\"data row2 col5\" >0.0923</td>\n", "      <td id=\"T_5474f_row2_col6\" class=\"data row2 col6\" >0.1024</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_5474f_row3_col0\" class=\"data row3 col0\" >0.7419</td>\n", "      <td id=\"T_5474f_row3_col1\" class=\"data row3 col1\" >0.5818</td>\n", "      <td id=\"T_5474f_row3_col2\" class=\"data row3 col2\" >0.1519</td>\n", "      <td id=\"T_5474f_row3_col3\" class=\"data row3 col3\" >0.3136</td>\n", "      <td id=\"T_5474f_row3_col4\" class=\"data row3 col4\" >0.2046</td>\n", "      <td id=\"T_5474f_row3_col5\" class=\"data row3 col5\" >0.0723</td>\n", "      <td id=\"T_5474f_row3_col6\" class=\"data row3 col6\" >0.0790</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_5474f_row4_col0\" class=\"data row4 col0\" >0.7563</td>\n", "      <td id=\"T_5474f_row4_col1\" class=\"data row4 col1\" >0.5908</td>\n", "      <td id=\"T_5474f_row4_col2\" class=\"data row4 col2\" >0.1490</td>\n", "      <td id=\"T_5474f_row4_col3\" class=\"data row4 col3\" >0.3611</td>\n", "      <td id=\"T_5474f_row4_col4\" class=\"data row4 col4\" >0.2110</td>\n", "      <td id=\"T_5474f_row4_col5\" class=\"data row4 col5\" >0.0954</td>\n", "      <td id=\"T_5474f_row4_col6\" class=\"data row4 col6\" >0.1085</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_5474f_row5_col0\" class=\"data row5 col0\" >0.7550</td>\n", "      <td id=\"T_5474f_row5_col1\" class=\"data row5 col1\" >0.5997</td>\n", "      <td id=\"T_5474f_row5_col2\" class=\"data row5 col2\" >0.1748</td>\n", "      <td id=\"T_5474f_row5_col3\" class=\"data row5 col3\" >0.3720</td>\n", "      <td id=\"T_5474f_row5_col4\" class=\"data row5 col4\" >0.2378</td>\n", "      <td id=\"T_5474f_row5_col5\" class=\"data row5 col5\" >0.1139</td>\n", "      <td id=\"T_5474f_row5_col6\" class=\"data row5 col6\" >0.1255</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_5474f_row6_col0\" class=\"data row6 col0\" >0.7638</td>\n", "      <td id=\"T_5474f_row6_col1\" class=\"data row6 col1\" >0.5890</td>\n", "      <td id=\"T_5474f_row6_col2\" class=\"data row6 col2\" >0.1891</td>\n", "      <td id=\"T_5474f_row6_col3\" class=\"data row6 col3\" >0.4125</td>\n", "      <td id=\"T_5474f_row6_col4\" class=\"data row6 col4\" >0.2593</td>\n", "      <td id=\"T_5474f_row6_col5\" class=\"data row6 col5\" >0.1413</td>\n", "      <td id=\"T_5474f_row6_col6\" class=\"data row6 col6\" >0.1565</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_5474f_row7_col0\" class=\"data row7 col0\" >0.7613</td>\n", "      <td id=\"T_5474f_row7_col1\" class=\"data row7 col1\" >0.6240</td>\n", "      <td id=\"T_5474f_row7_col2\" class=\"data row7 col2\" >0.1633</td>\n", "      <td id=\"T_5474f_row7_col3\" class=\"data row7 col3\" >0.3904</td>\n", "      <td id=\"T_5474f_row7_col4\" class=\"data row7 col4\" >0.2303</td>\n", "      <td id=\"T_5474f_row7_col5\" class=\"data row7 col5\" >0.1163</td>\n", "      <td id=\"T_5474f_row7_col6\" class=\"data row7 col6\" >0.1318</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_5474f_row8_col0\" class=\"data row8 col0\" >0.7619</td>\n", "      <td id=\"T_5474f_row8_col1\" class=\"data row8 col1\" >0.5988</td>\n", "      <td id=\"T_5474f_row8_col2\" class=\"data row8 col2\" >0.1862</td>\n", "      <td id=\"T_5474f_row8_col3\" class=\"data row8 col3\" >0.4037</td>\n", "      <td id=\"T_5474f_row8_col4\" class=\"data row8 col4\" >0.2549</td>\n", "      <td id=\"T_5474f_row8_col5\" class=\"data row8 col5\" >0.1356</td>\n", "      <td id=\"T_5474f_row8_col6\" class=\"data row8 col6\" >0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_5474f_row9_col0\" class=\"data row9 col0\" >0.7549</td>\n", "      <td id=\"T_5474f_row9_col1\" class=\"data row9 col1\" >0.5756</td>\n", "      <td id=\"T_5474f_row9_col2\" class=\"data row9 col2\" >0.1897</td>\n", "      <td id=\"T_5474f_row9_col3\" class=\"data row9 col3\" >0.3771</td>\n", "      <td id=\"T_5474f_row9_col4\" class=\"data row9 col4\" >0.2524</td>\n", "      <td id=\"T_5474f_row9_col5\" class=\"data row9 col5\" >0.1246</td>\n", "      <td id=\"T_5474f_row9_col6\" class=\"data row9 col6\" >0.1351</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_5474f_row10_col0\" class=\"data row10 col0\" >0.7547</td>\n", "      <td id=\"T_5474f_row10_col1\" class=\"data row10 col1\" >0.5939</td>\n", "      <td id=\"T_5474f_row10_col2\" class=\"data row10 col2\" >0.1763</td>\n", "      <td id=\"T_5474f_row10_col3\" class=\"data row10 col3\" >0.3719</td>\n", "      <td id=\"T_5474f_row10_col4\" class=\"data row10 col4\" >0.2388</td>\n", "      <td id=\"T_5474f_row10_col5\" class=\"data row10 col5\" >0.1145</td>\n", "      <td id=\"T_5474f_row10_col6\" class=\"data row10 col6\" >0.1259</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5474f_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_5474f_row11_col0\" class=\"data row11 col0\" >0.0065</td>\n", "      <td id=\"T_5474f_row11_col1\" class=\"data row11 col1\" >0.0126</td>\n", "      <td id=\"T_5474f_row11_col2\" class=\"data row11 col2\" >0.0191</td>\n", "      <td id=\"T_5474f_row11_col3\" class=\"data row11 col3\" >0.0279</td>\n", "      <td id=\"T_5474f_row11_col4\" class=\"data row11 col4\" >0.0214</td>\n", "      <td id=\"T_5474f_row11_col5\" class=\"data row11 col5\" >0.0214</td>\n", "      <td id=\"T_5474f_row11_col6\" class=\"data row11 col6\" >0.0230</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6820730>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["knn = create_model('knn')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 8.3 Random Forest Classifier"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_9df14_row10_col0, #T_9df14_row10_col1, #T_9df14_row10_col2, #T_9df14_row10_col3, #T_9df14_row10_col4, #T_9df14_row10_col5, #T_9df14_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_9df14\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_9df14_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_9df14_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_9df14_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_9df14_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_9df14_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_9df14_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_9df14_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_9df14_row0_col0\" class=\"data row0 col0\" >0.8133</td>\n", "      <td id=\"T_9df14_row0_col1\" class=\"data row0 col1\" >0.7673</td>\n", "      <td id=\"T_9df14_row0_col2\" class=\"data row0 col2\" >0.3610</td>\n", "      <td id=\"T_9df14_row0_col3\" class=\"data row0 col3\" >0.6269</td>\n", "      <td id=\"T_9df14_row0_col4\" class=\"data row0 col4\" >0.4582</td>\n", "      <td id=\"T_9df14_row0_col5\" class=\"data row0 col5\" >0.3551</td>\n", "      <td id=\"T_9df14_row0_col6\" class=\"data row0 col6\" >0.3749</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_9df14_row1_col0\" class=\"data row1 col0\" >0.8239</td>\n", "      <td id=\"T_9df14_row1_col1\" class=\"data row1 col1\" >0.7615</td>\n", "      <td id=\"T_9df14_row1_col2\" class=\"data row1 col2\" >0.3782</td>\n", "      <td id=\"T_9df14_row1_col3\" class=\"data row1 col3\" >0.6735</td>\n", "      <td id=\"T_9df14_row1_col4\" class=\"data row1 col4\" >0.4844</td>\n", "      <td id=\"T_9df14_row1_col5\" class=\"data row1 col5\" >0.3882</td>\n", "      <td id=\"T_9df14_row1_col6\" class=\"data row1 col6\" >0.4117</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_9df14_row2_col0\" class=\"data row2 col0\" >0.8258</td>\n", "      <td id=\"T_9df14_row2_col1\" class=\"data row2 col1\" >0.7708</td>\n", "      <td id=\"T_9df14_row2_col2\" class=\"data row2 col2\" >0.3467</td>\n", "      <td id=\"T_9df14_row2_col3\" class=\"data row2 col3\" >0.7076</td>\n", "      <td id=\"T_9df14_row2_col4\" class=\"data row2 col4\" >0.4654</td>\n", "      <td id=\"T_9df14_row2_col5\" class=\"data row2 col5\" >0.3756</td>\n", "      <td id=\"T_9df14_row2_col6\" class=\"data row2 col6\" >0.4098</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_9df14_row3_col0\" class=\"data row3 col0\" >0.8177</td>\n", "      <td id=\"T_9df14_row3_col1\" class=\"data row3 col1\" >0.7605</td>\n", "      <td id=\"T_9df14_row3_col2\" class=\"data row3 col2\" >0.3725</td>\n", "      <td id=\"T_9df14_row3_col3\" class=\"data row3 col3\" >0.6436</td>\n", "      <td id=\"T_9df14_row3_col4\" class=\"data row3 col4\" >0.4719</td>\n", "      <td id=\"T_9df14_row3_col5\" class=\"data row3 col5\" >0.3710</td>\n", "      <td id=\"T_9df14_row3_col6\" class=\"data row3 col6\" >0.3913</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_9df14_row4_col0\" class=\"data row4 col0\" >0.8208</td>\n", "      <td id=\"T_9df14_row4_col1\" class=\"data row4 col1\" >0.7642</td>\n", "      <td id=\"T_9df14_row4_col2\" class=\"data row4 col2\" >0.3725</td>\n", "      <td id=\"T_9df14_row4_col3\" class=\"data row4 col3\" >0.6599</td>\n", "      <td id=\"T_9df14_row4_col4\" class=\"data row4 col4\" >0.4762</td>\n", "      <td id=\"T_9df14_row4_col5\" class=\"data row4 col5\" >0.3780</td>\n", "      <td id=\"T_9df14_row4_col6\" class=\"data row4 col6\" >0.4006</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_9df14_row5_col0\" class=\"data row5 col0\" >0.8283</td>\n", "      <td id=\"T_9df14_row5_col1\" class=\"data row5 col1\" >0.7638</td>\n", "      <td id=\"T_9df14_row5_col2\" class=\"data row5 col2\" >0.3954</td>\n", "      <td id=\"T_9df14_row5_col3\" class=\"data row5 col3\" >0.6866</td>\n", "      <td id=\"T_9df14_row5_col4\" class=\"data row5 col4\" >0.5018</td>\n", "      <td id=\"T_9df14_row5_col5\" class=\"data row5 col5\" >0.4070</td>\n", "      <td id=\"T_9df14_row5_col6\" class=\"data row5 col6\" >0.4297</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_9df14_row6_col0\" class=\"data row6 col0\" >0.8127</td>\n", "      <td id=\"T_9df14_row6_col1\" class=\"data row6 col1\" >0.7647</td>\n", "      <td id=\"T_9df14_row6_col2\" class=\"data row6 col2\" >0.3582</td>\n", "      <td id=\"T_9df14_row6_col3\" class=\"data row6 col3\" >0.6250</td>\n", "      <td id=\"T_9df14_row6_col4\" class=\"data row6 col4\" >0.4554</td>\n", "      <td id=\"T_9df14_row6_col5\" class=\"data row6 col5\" >0.3522</td>\n", "      <td id=\"T_9df14_row6_col6\" class=\"data row6 col6\" >0.3721</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_9df14_row7_col0\" class=\"data row7 col0\" >0.8283</td>\n", "      <td id=\"T_9df14_row7_col1\" class=\"data row7 col1\" >0.7390</td>\n", "      <td id=\"T_9df14_row7_col2\" class=\"data row7 col2\" >0.3553</td>\n", "      <td id=\"T_9df14_row7_col3\" class=\"data row7 col3\" >0.7168</td>\n", "      <td id=\"T_9df14_row7_col4\" class=\"data row7 col4\" >0.4751</td>\n", "      <td id=\"T_9df14_row7_col5\" class=\"data row7 col5\" >0.3861</td>\n", "      <td id=\"T_9df14_row7_col6\" class=\"data row7 col6\" >0.4202</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_9df14_row8_col0\" class=\"data row8 col0\" >0.8108</td>\n", "      <td id=\"T_9df14_row8_col1\" class=\"data row8 col1\" >0.7496</td>\n", "      <td id=\"T_9df14_row8_col2\" class=\"data row8 col2\" >0.3610</td>\n", "      <td id=\"T_9df14_row8_col3\" class=\"data row8 col3\" >0.6146</td>\n", "      <td id=\"T_9df14_row8_col4\" class=\"data row8 col4\" >0.4549</td>\n", "      <td id=\"T_9df14_row8_col5\" class=\"data row8 col5\" >0.3496</td>\n", "      <td id=\"T_9df14_row8_col6\" class=\"data row8 col6\" >0.3678</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_9df14_row9_col0\" class=\"data row9 col0\" >0.8176</td>\n", "      <td id=\"T_9df14_row9_col1\" class=\"data row9 col1\" >0.7565</td>\n", "      <td id=\"T_9df14_row9_col2\" class=\"data row9 col2\" >0.3621</td>\n", "      <td id=\"T_9df14_row9_col3\" class=\"data row9 col3\" >0.6462</td>\n", "      <td id=\"T_9df14_row9_col4\" class=\"data row9 col4\" >0.4641</td>\n", "      <td id=\"T_9df14_row9_col5\" class=\"data row9 col5\" >0.3645</td>\n", "      <td id=\"T_9df14_row9_col6\" class=\"data row9 col6\" >0.3867</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_9df14_row10_col0\" class=\"data row10 col0\" >0.8199</td>\n", "      <td id=\"T_9df14_row10_col1\" class=\"data row10 col1\" >0.7598</td>\n", "      <td id=\"T_9df14_row10_col2\" class=\"data row10 col2\" >0.3663</td>\n", "      <td id=\"T_9df14_row10_col3\" class=\"data row10 col3\" >0.6601</td>\n", "      <td id=\"T_9df14_row10_col4\" class=\"data row10 col4\" >0.4707</td>\n", "      <td id=\"T_9df14_row10_col5\" class=\"data row10 col5\" >0.3727</td>\n", "      <td id=\"T_9df14_row10_col6\" class=\"data row10 col6\" >0.3965</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9df14_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_9df14_row11_col0\" class=\"data row11 col0\" >0.0062</td>\n", "      <td id=\"T_9df14_row11_col1\" class=\"data row11 col1\" >0.0089</td>\n", "      <td id=\"T_9df14_row11_col2\" class=\"data row11 col2\" >0.0131</td>\n", "      <td id=\"T_9df14_row11_col3\" class=\"data row11 col3\" >0.0335</td>\n", "      <td id=\"T_9df14_row11_col4\" class=\"data row11 col4\" >0.0139</td>\n", "      <td id=\"T_9df14_row11_col5\" class=\"data row11 col5\" >0.0172</td>\n", "      <td id=\"T_9df14_row11_col6\" class=\"data row11 col6\" >0.0202</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6798520>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rf = create_model('rf')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Παρατηρήστε ότι ο μέσος όρος των σκoρ του κάθε μοντέλου ταιριάζει με το σκορ που εμφανίστηκε από τη συνάρτηση `compare_modles()`. Αυτό οφείλεται στο ότι οι μετρικές που εκτυπώνονται από τη συνάρτηση `compare_models()` αποτελούν τη μέση τιμή όλων των CV folds. Όπως και στην `compare_models()`, αν θέλετε να αλλάξετε το πλήθος των folds από την προεπιλεγμένη τιμή (10) σε κάποια άλλη, τότε μπορείτε να χρησιμοποιήσετε την παράμετρο `fold`. <PERSON><PERSON><PERSON> παράδειγμα, το `create_model('dt', fold = 5)` θα δημιουργήσει έναν Decision Tree Classifier χρησιμοποιώντας 5 fold stratified CV."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 9.0 Ρύθμιση Μοντέλου"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Όταν δημιουργείται ένα μοντέλο από τη συνάρτηση `create_model()`, τότε χρησιμοποιούνται οι προεπιλεγμένες υπερ-παράμετροι για την εκπαίδευσή του. Για να ρυθμίσουμε τις υπερ-παραμέτρους χρησιμοποιούμε τη συνάρτηση `tune_model()`. Αυτή η συνάρτηση ρυθμίζει αυτόματα τις υπερπαραμέτρους του μοντέλου χρησιμοποιώντας την τεχνική `Random Grid Search` σε ένα πεδίο τιμών που εμείς ορίζουμε. Στην έξοδο εκτυπώνεται ένας πίνακας βαθμολόγησης που δείχνει τις μετρικές Accuracy, AUC, Recall, Precision, F1, Kappa και MCC ανά fold για το καλύτερο μοντέλο. Για να το επιτύχουμε, περνάμε την παράμετρο `custom_grid` στη συνάρτηση `tune_model` (βλέπε ενότητα 9.2)."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 9.1 Decision Tree Classifier"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_76796_row10_col0, #T_76796_row10_col1, #T_76796_row10_col2, #T_76796_row10_col3, #T_76796_row10_col4, #T_76796_row10_col5, #T_76796_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_76796\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_76796_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_76796_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_76796_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_76796_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_76796_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_76796_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_76796_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_76796_row0_col0\" class=\"data row0 col0\" >0.8177</td>\n", "      <td id=\"T_76796_row0_col1\" class=\"data row0 col1\" >0.7475</td>\n", "      <td id=\"T_76796_row0_col2\" class=\"data row0 col2\" >0.3095</td>\n", "      <td id=\"T_76796_row0_col3\" class=\"data row0 col3\" >0.6835</td>\n", "      <td id=\"T_76796_row0_col4\" class=\"data row0 col4\" >0.4260</td>\n", "      <td id=\"T_76796_row0_col5\" class=\"data row0 col5\" >0.3355</td>\n", "      <td id=\"T_76796_row0_col6\" class=\"data row0 col6\" >0.3728</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_76796_row1_col0\" class=\"data row1 col0\" >0.8289</td>\n", "      <td id=\"T_76796_row1_col1\" class=\"data row1 col1\" >0.7711</td>\n", "      <td id=\"T_76796_row1_col2\" class=\"data row1 col2\" >0.3381</td>\n", "      <td id=\"T_76796_row1_col3\" class=\"data row1 col3\" >0.7375</td>\n", "      <td id=\"T_76796_row1_col4\" class=\"data row1 col4\" >0.4637</td>\n", "      <td id=\"T_76796_row1_col5\" class=\"data row1 col5\" >0.3782</td>\n", "      <td id=\"T_76796_row1_col6\" class=\"data row1 col6\" >0.4190</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_76796_row2_col0\" class=\"data row2 col0\" >0.8208</td>\n", "      <td id=\"T_76796_row2_col1\" class=\"data row2 col1\" >0.7377</td>\n", "      <td id=\"T_76796_row2_col2\" class=\"data row2 col2\" >0.2894</td>\n", "      <td id=\"T_76796_row2_col3\" class=\"data row2 col3\" >0.7266</td>\n", "      <td id=\"T_76796_row2_col4\" class=\"data row2 col4\" >0.4139</td>\n", "      <td id=\"T_76796_row2_col5\" class=\"data row2 col5\" >0.3305</td>\n", "      <td id=\"T_76796_row2_col6\" class=\"data row2 col6\" >0.3796</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_76796_row3_col0\" class=\"data row3 col0\" >0.8252</td>\n", "      <td id=\"T_76796_row3_col1\" class=\"data row3 col1\" >0.7580</td>\n", "      <td id=\"T_76796_row3_col2\" class=\"data row3 col2\" >0.3152</td>\n", "      <td id=\"T_76796_row3_col3\" class=\"data row3 col3\" >0.7333</td>\n", "      <td id=\"T_76796_row3_col4\" class=\"data row3 col4\" >0.4409</td>\n", "      <td id=\"T_76796_row3_col5\" class=\"data row3 col5\" >0.3563</td>\n", "      <td id=\"T_76796_row3_col6\" class=\"data row3 col6\" >0.4010</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_76796_row4_col0\" class=\"data row4 col0\" >0.8195</td>\n", "      <td id=\"T_76796_row4_col1\" class=\"data row4 col1\" >0.7545</td>\n", "      <td id=\"T_76796_row4_col2\" class=\"data row4 col2\" >0.3095</td>\n", "      <td id=\"T_76796_row4_col3\" class=\"data row4 col3\" >0.6968</td>\n", "      <td id=\"T_76796_row4_col4\" class=\"data row4 col4\" >0.4286</td>\n", "      <td id=\"T_76796_row4_col5\" class=\"data row4 col5\" >0.3398</td>\n", "      <td id=\"T_76796_row4_col6\" class=\"data row4 col6\" >0.3794</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_76796_row5_col0\" class=\"data row5 col0\" >0.8271</td>\n", "      <td id=\"T_76796_row5_col1\" class=\"data row5 col1\" >0.7509</td>\n", "      <td id=\"T_76796_row5_col2\" class=\"data row5 col2\" >0.3438</td>\n", "      <td id=\"T_76796_row5_col3\" class=\"data row5 col3\" >0.7186</td>\n", "      <td id=\"T_76796_row5_col4\" class=\"data row5 col4\" >0.4651</td>\n", "      <td id=\"T_76796_row5_col5\" class=\"data row5 col5\" >0.3769</td>\n", "      <td id=\"T_76796_row5_col6\" class=\"data row5 col6\" >0.4134</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_76796_row6_col0\" class=\"data row6 col0\" >0.8195</td>\n", "      <td id=\"T_76796_row6_col1\" class=\"data row6 col1\" >0.7488</td>\n", "      <td id=\"T_76796_row6_col2\" class=\"data row6 col2\" >0.3123</td>\n", "      <td id=\"T_76796_row6_col3\" class=\"data row6 col3\" >0.6943</td>\n", "      <td id=\"T_76796_row6_col4\" class=\"data row6 col4\" >0.4308</td>\n", "      <td id=\"T_76796_row6_col5\" class=\"data row6 col5\" >0.3415</td>\n", "      <td id=\"T_76796_row6_col6\" class=\"data row6 col6\" >0.3801</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_76796_row7_col0\" class=\"data row7 col0\" >0.8246</td>\n", "      <td id=\"T_76796_row7_col1\" class=\"data row7 col1\" >0.7529</td>\n", "      <td id=\"T_76796_row7_col2\" class=\"data row7 col2\" >0.2980</td>\n", "      <td id=\"T_76796_row7_col3\" class=\"data row7 col3\" >0.7482</td>\n", "      <td id=\"T_76796_row7_col4\" class=\"data row7 col4\" >0.4262</td>\n", "      <td id=\"T_76796_row7_col5\" class=\"data row7 col5\" >0.3446</td>\n", "      <td id=\"T_76796_row7_col6\" class=\"data row7 col6\" >0.3957</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_76796_row8_col0\" class=\"data row8 col0\" >0.8195</td>\n", "      <td id=\"T_76796_row8_col1\" class=\"data row8 col1\" >0.7241</td>\n", "      <td id=\"T_76796_row8_col2\" class=\"data row8 col2\" >0.3123</td>\n", "      <td id=\"T_76796_row8_col3\" class=\"data row8 col3\" >0.6943</td>\n", "      <td id=\"T_76796_row8_col4\" class=\"data row8 col4\" >0.4308</td>\n", "      <td id=\"T_76796_row8_col5\" class=\"data row8 col5\" >0.3415</td>\n", "      <td id=\"T_76796_row8_col6\" class=\"data row8 col6\" >0.3801</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_76796_row9_col0\" class=\"data row9 col0\" >0.8188</td>\n", "      <td id=\"T_76796_row9_col1\" class=\"data row9 col1\" >0.7378</td>\n", "      <td id=\"T_76796_row9_col2\" class=\"data row9 col2\" >0.3075</td>\n", "      <td id=\"T_76796_row9_col3\" class=\"data row9 col3\" >0.6903</td>\n", "      <td id=\"T_76796_row9_col4\" class=\"data row9 col4\" >0.4254</td>\n", "      <td id=\"T_76796_row9_col5\" class=\"data row9 col5\" >0.3362</td>\n", "      <td id=\"T_76796_row9_col6\" class=\"data row9 col6\" >0.3751</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_76796_row10_col0\" class=\"data row10 col0\" >0.8222</td>\n", "      <td id=\"T_76796_row10_col1\" class=\"data row10 col1\" >0.7483</td>\n", "      <td id=\"T_76796_row10_col2\" class=\"data row10 col2\" >0.3136</td>\n", "      <td id=\"T_76796_row10_col3\" class=\"data row10 col3\" >0.7123</td>\n", "      <td id=\"T_76796_row10_col4\" class=\"data row10 col4\" >0.4352</td>\n", "      <td id=\"T_76796_row10_col5\" class=\"data row10 col5\" >0.3481</td>\n", "      <td id=\"T_76796_row10_col6\" class=\"data row10 col6\" >0.3896</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_76796_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_76796_row11_col0\" class=\"data row11 col0\" >0.0037</td>\n", "      <td id=\"T_76796_row11_col1\" class=\"data row11 col1\" >0.0122</td>\n", "      <td id=\"T_76796_row11_col2\" class=\"data row11 col2\" >0.0156</td>\n", "      <td id=\"T_76796_row11_col3\" class=\"data row11 col3\" >0.0219</td>\n", "      <td id=\"T_76796_row11_col4\" class=\"data row11 col4\" >0.0159</td>\n", "      <td id=\"T_76796_row11_col5\" class=\"data row11 col5\" >0.0161</td>\n", "      <td id=\"T_76796_row11_col6\" class=\"data row11 col6\" >0.0158</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f67b00d0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_dt = tune_model(dt)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='entropy',\n", "                       max_depth=6, max_features=1.0, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.002, min_impurity_split=None,\n", "                       min_samples_leaf=5, min_samples_split=5,\n", "                       min_weight_fraction_leaf=0.0, presort='deprecated',\n", "                       random_state=123, splitter='best')\n"]}], "source": ["#tuned model object is stored in the variable 'tuned_dt'.\n", "#το μοντέλο με ρυθμισμένες υπερ-παραμέτρους αποθηκεύεται στη μεταβλητή 'tuned_dt'.\n", "print(tuned_dt)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 9.2 K <PERSON>eighbors Classifier"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_846bc_row10_col0, #T_846bc_row10_col1, #T_846bc_row10_col2, #T_846bc_row10_col3, #T_846bc_row10_col4, #T_846bc_row10_col5, #T_846bc_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_846bc\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_846bc_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_846bc_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_846bc_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_846bc_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_846bc_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_846bc_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_846bc_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_846bc_row0_col0\" class=\"data row0 col0\" >0.7813</td>\n", "      <td id=\"T_846bc_row0_col1\" class=\"data row0 col1\" >0.6482</td>\n", "      <td id=\"T_846bc_row0_col2\" class=\"data row0 col2\" >0.0372</td>\n", "      <td id=\"T_846bc_row0_col3\" class=\"data row0 col3\" >0.5000</td>\n", "      <td id=\"T_846bc_row0_col4\" class=\"data row0 col4\" >0.0693</td>\n", "      <td id=\"T_846bc_row0_col5\" class=\"data row0 col5\" >0.0402</td>\n", "      <td id=\"T_846bc_row0_col6\" class=\"data row0 col6\" >0.0876</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_846bc_row1_col0\" class=\"data row1 col0\" >0.7807</td>\n", "      <td id=\"T_846bc_row1_col1\" class=\"data row1 col1\" >0.6436</td>\n", "      <td id=\"T_846bc_row1_col2\" class=\"data row1 col2\" >0.0315</td>\n", "      <td id=\"T_846bc_row1_col3\" class=\"data row1 col3\" >0.4783</td>\n", "      <td id=\"T_846bc_row1_col4\" class=\"data row1 col4\" >0.0591</td>\n", "      <td id=\"T_846bc_row1_col5\" class=\"data row1 col5\" >0.0330</td>\n", "      <td id=\"T_846bc_row1_col6\" class=\"data row1 col6\" >0.0759</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_846bc_row2_col0\" class=\"data row2 col0\" >0.7744</td>\n", "      <td id=\"T_846bc_row2_col1\" class=\"data row2 col1\" >0.6563</td>\n", "      <td id=\"T_846bc_row2_col2\" class=\"data row2 col2\" >0.0315</td>\n", "      <td id=\"T_846bc_row2_col3\" class=\"data row2 col3\" >0.3333</td>\n", "      <td id=\"T_846bc_row2_col4\" class=\"data row2 col4\" >0.0576</td>\n", "      <td id=\"T_846bc_row2_col5\" class=\"data row2 col5\" >0.0206</td>\n", "      <td id=\"T_846bc_row2_col6\" class=\"data row2 col6\" >0.0403</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_846bc_row3_col0\" class=\"data row3 col0\" >0.7845</td>\n", "      <td id=\"T_846bc_row3_col1\" class=\"data row3 col1\" >0.6589</td>\n", "      <td id=\"T_846bc_row3_col2\" class=\"data row3 col2\" >0.0659</td>\n", "      <td id=\"T_846bc_row3_col3\" class=\"data row3 col3\" >0.5610</td>\n", "      <td id=\"T_846bc_row3_col4\" class=\"data row3 col4\" >0.1179</td>\n", "      <td id=\"T_846bc_row3_col5\" class=\"data row3 col5\" >0.0754</td>\n", "      <td id=\"T_846bc_row3_col6\" class=\"data row3 col6\" >0.1345</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_846bc_row4_col0\" class=\"data row4 col0\" >0.7826</td>\n", "      <td id=\"T_846bc_row4_col1\" class=\"data row4 col1\" >0.6645</td>\n", "      <td id=\"T_846bc_row4_col2\" class=\"data row4 col2\" >0.0315</td>\n", "      <td id=\"T_846bc_row4_col3\" class=\"data row4 col3\" >0.5500</td>\n", "      <td id=\"T_846bc_row4_col4\" class=\"data row4 col4\" >0.0596</td>\n", "      <td id=\"T_846bc_row4_col5\" class=\"data row4 col5\" >0.0368</td>\n", "      <td id=\"T_846bc_row4_col6\" class=\"data row4 col6\" >0.0903</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_846bc_row5_col0\" class=\"data row5 col0\" >0.7794</td>\n", "      <td id=\"T_846bc_row5_col1\" class=\"data row5 col1\" >0.6477</td>\n", "      <td id=\"T_846bc_row5_col2\" class=\"data row5 col2\" >0.0544</td>\n", "      <td id=\"T_846bc_row5_col3\" class=\"data row5 col3\" >0.4634</td>\n", "      <td id=\"T_846bc_row5_col4\" class=\"data row5 col4\" >0.0974</td>\n", "      <td id=\"T_846bc_row5_col5\" class=\"data row5 col5\" >0.0539</td>\n", "      <td id=\"T_846bc_row5_col6\" class=\"data row5 col6\" >0.0961</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_846bc_row6_col0\" class=\"data row6 col0\" >0.7826</td>\n", "      <td id=\"T_846bc_row6_col1\" class=\"data row6 col1\" >0.6278</td>\n", "      <td id=\"T_846bc_row6_col2\" class=\"data row6 col2\" >0.0630</td>\n", "      <td id=\"T_846bc_row6_col3\" class=\"data row6 col3\" >0.5238</td>\n", "      <td id=\"T_846bc_row6_col4\" class=\"data row6 col4\" >0.1125</td>\n", "      <td id=\"T_846bc_row6_col5\" class=\"data row6 col5\" >0.0688</td>\n", "      <td id=\"T_846bc_row6_col6\" class=\"data row6 col6\" >0.1214</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_846bc_row7_col0\" class=\"data row7 col0\" >0.7751</td>\n", "      <td id=\"T_846bc_row7_col1\" class=\"data row7 col1\" >0.6702</td>\n", "      <td id=\"T_846bc_row7_col2\" class=\"data row7 col2\" >0.0372</td>\n", "      <td id=\"T_846bc_row7_col3\" class=\"data row7 col3\" >0.3611</td>\n", "      <td id=\"T_846bc_row7_col4\" class=\"data row7 col4\" >0.0675</td>\n", "      <td id=\"T_846bc_row7_col5\" class=\"data row7 col5\" >0.0278</td>\n", "      <td id=\"T_846bc_row7_col6\" class=\"data row7 col6\" >0.0523</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_846bc_row8_col0\" class=\"data row8 col0\" >0.7813</td>\n", "      <td id=\"T_846bc_row8_col1\" class=\"data row8 col1\" >0.6409</td>\n", "      <td id=\"T_846bc_row8_col2\" class=\"data row8 col2\" >0.0630</td>\n", "      <td id=\"T_846bc_row8_col3\" class=\"data row8 col3\" >0.5000</td>\n", "      <td id=\"T_846bc_row8_col4\" class=\"data row8 col4\" >0.1120</td>\n", "      <td id=\"T_846bc_row8_col5\" class=\"data row8 col5\" >0.0662</td>\n", "      <td id=\"T_846bc_row8_col6\" class=\"data row8 col6\" >0.1146</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_846bc_row9_col0\" class=\"data row9 col0\" >0.7881</td>\n", "      <td id=\"T_846bc_row9_col1\" class=\"data row9 col1\" >0.6426</td>\n", "      <td id=\"T_846bc_row9_col2\" class=\"data row9 col2\" >0.0661</td>\n", "      <td id=\"T_846bc_row9_col3\" class=\"data row9 col3\" >0.6389</td>\n", "      <td id=\"T_846bc_row9_col4\" class=\"data row9 col4\" >0.1198</td>\n", "      <td id=\"T_846bc_row9_col5\" class=\"data row9 col5\" >0.0822</td>\n", "      <td id=\"T_846bc_row9_col6\" class=\"data row9 col6\" >0.1548</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_846bc_row10_col0\" class=\"data row10 col0\" >0.7810</td>\n", "      <td id=\"T_846bc_row10_col1\" class=\"data row10 col1\" >0.6501</td>\n", "      <td id=\"T_846bc_row10_col2\" class=\"data row10 col2\" >0.0482</td>\n", "      <td id=\"T_846bc_row10_col3\" class=\"data row10 col3\" >0.4910</td>\n", "      <td id=\"T_846bc_row10_col4\" class=\"data row10 col4\" >0.0873</td>\n", "      <td id=\"T_846bc_row10_col5\" class=\"data row10 col5\" >0.0505</td>\n", "      <td id=\"T_846bc_row10_col6\" class=\"data row10 col6\" >0.0968</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_846bc_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_846bc_row11_col0\" class=\"data row11 col0\" >0.0039</td>\n", "      <td id=\"T_846bc_row11_col1\" class=\"data row11 col1\" >0.0119</td>\n", "      <td id=\"T_846bc_row11_col2\" class=\"data row11 col2\" >0.0148</td>\n", "      <td id=\"T_846bc_row11_col3\" class=\"data row11 col3\" >0.0861</td>\n", "      <td id=\"T_846bc_row11_col4\" class=\"data row11 col4\" >0.0255</td>\n", "      <td id=\"T_846bc_row11_col5\" class=\"data row11 col5\" >0.0206</td>\n", "      <td id=\"T_846bc_row11_col6\" class=\"data row11 col6\" >0.0338</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6829d00>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "tuned_knn = tune_model(knn, custom_grid = {'n_neighbors' : np.arange(0,50,1)})"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KNeighborsClassifier(algorithm='auto', leaf_size=30, metric='minkowski',\n", "                     metric_params=None, n_jobs=-1, n_neighbors=42, p=2,\n", "                     weights='uniform')\n"]}], "source": ["print(tuned_knn)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 9.3 Random Forest Classifier"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_4abdf_row10_col0, #T_4abdf_row10_col1, #T_4abdf_row10_col2, #T_4abdf_row10_col3, #T_4abdf_row10_col4, #T_4abdf_row10_col5, #T_4abdf_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_4abdf\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_4abdf_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_4abdf_level0_col1\" class=\"col_heading level0 col1\" >AUC</th>\n", "      <th id=\"T_4abdf_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_4abdf_level0_col3\" class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th id=\"T_4abdf_level0_col4\" class=\"col_heading level0 col4\" >F1</th>\n", "      <th id=\"T_4abdf_level0_col5\" class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th id=\"T_4abdf_level0_col6\" class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_4abdf_row0_col0\" class=\"data row0 col0\" >0.8158</td>\n", "      <td id=\"T_4abdf_row0_col1\" class=\"data row0 col1\" >0.7508</td>\n", "      <td id=\"T_4abdf_row0_col2\" class=\"data row0 col2\" >0.3181</td>\n", "      <td id=\"T_4abdf_row0_col3\" class=\"data row0 col3\" >0.6647</td>\n", "      <td id=\"T_4abdf_row0_col4\" class=\"data row0 col4\" >0.4302</td>\n", "      <td id=\"T_4abdf_row0_col5\" class=\"data row0 col5\" >0.3363</td>\n", "      <td id=\"T_4abdf_row0_col6\" class=\"data row0 col6\" >0.3689</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_4abdf_row1_col0\" class=\"data row1 col0\" >0.8283</td>\n", "      <td id=\"T_4abdf_row1_col1\" class=\"data row1 col1\" >0.7675</td>\n", "      <td id=\"T_4abdf_row1_col2\" class=\"data row1 col2\" >0.3295</td>\n", "      <td id=\"T_4abdf_row1_col3\" class=\"data row1 col3\" >0.7419</td>\n", "      <td id=\"T_4abdf_row1_col4\" class=\"data row1 col4\" >0.4563</td>\n", "      <td id=\"T_4abdf_row1_col5\" class=\"data row1 col5\" >0.3719</td>\n", "      <td id=\"T_4abdf_row1_col6\" class=\"data row1 col6\" >0.4152</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_4abdf_row2_col0\" class=\"data row2 col0\" >0.8139</td>\n", "      <td id=\"T_4abdf_row2_col1\" class=\"data row2 col1\" >0.7337</td>\n", "      <td id=\"T_4abdf_row2_col2\" class=\"data row2 col2\" >0.3181</td>\n", "      <td id=\"T_4abdf_row2_col3\" class=\"data row2 col3\" >0.6529</td>\n", "      <td id=\"T_4abdf_row2_col4\" class=\"data row2 col4\" >0.4277</td>\n", "      <td id=\"T_4abdf_row2_col5\" class=\"data row2 col5\" >0.3321</td>\n", "      <td id=\"T_4abdf_row2_col6\" class=\"data row2 col6\" >0.3628</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_4abdf_row3_col0\" class=\"data row3 col0\" >0.8246</td>\n", "      <td id=\"T_4abdf_row3_col1\" class=\"data row3 col1\" >0.7588</td>\n", "      <td id=\"T_4abdf_row3_col2\" class=\"data row3 col2\" >0.3095</td>\n", "      <td id=\"T_4abdf_row3_col3\" class=\"data row3 col3\" >0.7347</td>\n", "      <td id=\"T_4abdf_row3_col4\" class=\"data row3 col4\" >0.4355</td>\n", "      <td id=\"T_4abdf_row3_col5\" class=\"data row3 col5\" >0.3514</td>\n", "      <td id=\"T_4abdf_row3_col6\" class=\"data row3 col6\" >0.3976</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_4abdf_row4_col0\" class=\"data row4 col0\" >0.8170</td>\n", "      <td id=\"T_4abdf_row4_col1\" class=\"data row4 col1\" >0.7567</td>\n", "      <td id=\"T_4abdf_row4_col2\" class=\"data row4 col2\" >0.3438</td>\n", "      <td id=\"T_4abdf_row4_col3\" class=\"data row4 col3\" >0.6557</td>\n", "      <td id=\"T_4abdf_row4_col4\" class=\"data row4 col4\" >0.4511</td>\n", "      <td id=\"T_4abdf_row4_col5\" class=\"data row4 col5\" >0.3539</td>\n", "      <td id=\"T_4abdf_row4_col6\" class=\"data row4 col6\" >0.3805</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_4abdf_row5_col0\" class=\"data row5 col0\" >0.8258</td>\n", "      <td id=\"T_4abdf_row5_col1\" class=\"data row5 col1\" >0.7506</td>\n", "      <td id=\"T_4abdf_row5_col2\" class=\"data row5 col2\" >0.3324</td>\n", "      <td id=\"T_4abdf_row5_col3\" class=\"data row5 col3\" >0.7205</td>\n", "      <td id=\"T_4abdf_row5_col4\" class=\"data row5 col4\" >0.4549</td>\n", "      <td id=\"T_4abdf_row5_col5\" class=\"data row5 col5\" >0.3676</td>\n", "      <td id=\"T_4abdf_row5_col6\" class=\"data row5 col6\" >0.4067</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_4abdf_row6_col0\" class=\"data row6 col0\" >0.8170</td>\n", "      <td id=\"T_4abdf_row6_col1\" class=\"data row6 col1\" >0.7530</td>\n", "      <td id=\"T_4abdf_row6_col2\" class=\"data row6 col2\" >0.3324</td>\n", "      <td id=\"T_4abdf_row6_col3\" class=\"data row6 col3\" >0.6629</td>\n", "      <td id=\"T_4abdf_row6_col4\" class=\"data row6 col4\" >0.4427</td>\n", "      <td id=\"T_4abdf_row6_col5\" class=\"data row6 col5\" >0.3474</td>\n", "      <td id=\"T_4abdf_row6_col6\" class=\"data row6 col6\" >0.3771</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_4abdf_row7_col0\" class=\"data row7 col0\" >0.8221</td>\n", "      <td id=\"T_4abdf_row7_col1\" class=\"data row7 col1\" >0.7507</td>\n", "      <td id=\"T_4abdf_row7_col2\" class=\"data row7 col2\" >0.3381</td>\n", "      <td id=\"T_4abdf_row7_col3\" class=\"data row7 col3\" >0.6901</td>\n", "      <td id=\"T_4abdf_row7_col4\" class=\"data row7 col4\" >0.4538</td>\n", "      <td id=\"T_4abdf_row7_col5\" class=\"data row7 col5\" >0.3621</td>\n", "      <td id=\"T_4abdf_row7_col6\" class=\"data row7 col6\" >0.3951</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_4abdf_row8_col0\" class=\"data row8 col0\" >0.8177</td>\n", "      <td id=\"T_4abdf_row8_col1\" class=\"data row8 col1\" >0.7201</td>\n", "      <td id=\"T_4abdf_row8_col2\" class=\"data row8 col2\" >0.2980</td>\n", "      <td id=\"T_4abdf_row8_col3\" class=\"data row8 col3\" >0.6933</td>\n", "      <td id=\"T_4abdf_row8_col4\" class=\"data row8 col4\" >0.4168</td>\n", "      <td id=\"T_4abdf_row8_col5\" class=\"data row8 col5\" >0.3286</td>\n", "      <td id=\"T_4abdf_row8_col6\" class=\"data row8 col6\" >0.3699</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_4abdf_row9_col0\" class=\"data row9 col0\" >0.8207</td>\n", "      <td id=\"T_4abdf_row9_col1\" class=\"data row9 col1\" >0.7484</td>\n", "      <td id=\"T_4abdf_row9_col2\" class=\"data row9 col2\" >0.3132</td>\n", "      <td id=\"T_4abdf_row9_col3\" class=\"data row9 col3\" >0.6987</td>\n", "      <td id=\"T_4abdf_row9_col4\" class=\"data row9 col4\" >0.4325</td>\n", "      <td id=\"T_4abdf_row9_col5\" class=\"data row9 col5\" >0.3439</td>\n", "      <td id=\"T_4abdf_row9_col6\" class=\"data row9 col6\" >0.3831</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_4abdf_row10_col0\" class=\"data row10 col0\" >0.8203</td>\n", "      <td id=\"T_4abdf_row10_col1\" class=\"data row10 col1\" >0.7490</td>\n", "      <td id=\"T_4abdf_row10_col2\" class=\"data row10 col2\" >0.3233</td>\n", "      <td id=\"T_4abdf_row10_col3\" class=\"data row10 col3\" >0.6915</td>\n", "      <td id=\"T_4abdf_row10_col4\" class=\"data row10 col4\" >0.4402</td>\n", "      <td id=\"T_4abdf_row10_col5\" class=\"data row10 col5\" >0.3495</td>\n", "      <td id=\"T_4abdf_row10_col6\" class=\"data row10 col6\" >0.3857</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4abdf_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_4abdf_row11_col0\" class=\"data row11 col0\" >0.0045</td>\n", "      <td id=\"T_4abdf_row11_col1\" class=\"data row11 col1\" >0.0126</td>\n", "      <td id=\"T_4abdf_row11_col2\" class=\"data row11 col2\" >0.0135</td>\n", "      <td id=\"T_4abdf_row11_col3\" class=\"data row11 col3\" >0.0310</td>\n", "      <td id=\"T_4abdf_row11_col4\" class=\"data row11 col4\" >0.0129</td>\n", "      <td id=\"T_4abdf_row11_col5\" class=\"data row11 col5\" >0.0140</td>\n", "      <td id=\"T_4abdf_row11_col6\" class=\"data row11 col6\" >0.0165</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f67cbb20>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_rf = tune_model(rf)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Εκ προεπιλο<PERSON>ής, η συνάρτηση `tune_model` βελτιστοποιεί το `Accuracy`, αλλά αυτό μπορεί να αλλάξει ορίζοντας την παράμετρο `optimize`. Για παράδειγμα, το `tune_model(dt, optimize = 'AUC')` θα αναζητήσει υπερ-παραμέτρους για έναν Ταξινομητή Decision Tree που καταλήγει να έχει το υψηλότερο `AUC` αντί για `Accuracy`. Για τις ανάγκες αυτού του παραδείγματος χρησιμοποιήσαμε την προεπιλεγμένη μετρική `Accuracy` για λόγους απλότητας. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, όταν το dataset δεν είναι ισορροπημένο (imbalanced), όπω<PERSON> αυτό με το οποίο δουλεύουμε τώρα, δε συστήνεται να λάβουμε υπόψη μια μετρική σαν το `Accuracy`. Η μεθοδολογία επιλογής της καλύτερης μετρικής για την αξιολόγηση των ταξινομητών ξεπερνά το πεδίο αυτού του μαθήματος, αλλά αν θέλετε να μάθετε περισσότερα σχετικά με αυτό, μπορείτε να κλικάρετε __[εδώ](https://medium.com/@MohammedS/performance-metrics-for-classification-problems-in-machine-learning-part-i-b085d432082b)__ για να διαβάσετε πώς να επιλέξετε την ιδανική μετρική αξιολόγησης.\n", "\n", "Η μετρική από μόνης της δεν είναι το μοναδικό κριτήριο που πρέπει να λάβετε υπόψη για να οριστικοποιήσετε το καλύτερο μοντέλο. Άλλοι παράγοντες είναι ο χρόνος εκπαίδευσης, η τυπική απόκλιση των kfolds κ.α. Στα επόμενα επίπεδα αυτής της σειράς μαθημάτων θα συζητήσουμε λεπτομερώς αυτούς τους παράγοντες. Προς το παρόν, ας συνεχίσουμε θεωρώντας το Tuned Random Forest Classifier `tuned_rf`, ως το καλύτερο μοντέλο για το υπόλοιπο αυτού του μαθήματος."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 10.0 Γραφφική Απεικόνιση Μοντέλου"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Πριν την οριστικοποίηση του μοντέλου, μπορεί να χρησιμοποιηθεί η συνάρτηση `plot_model()` για να αναλύσουμε την απόδοση από διάφορες σκοπιές, όπω<PERSON> AUC, confusion_matrix, decision boundary κλπ. Αυτή η συνάρτηση δέχεται ένα αντικείμενο (object) εκπαιδευμένου μοντέλου και επιστρέφει μια γραφική απεικόνιση βάσει του test / hold-out set.\n", "\n", "Υπάρχουν 15 διαφορετικά διαθέσιμα γραφήματα, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ανατρέξτε στο docstring της `plot_model()` για να βρείτε τη λίστα με τα διαθέσιμα γραφήματα."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 10.1 Γράφημα AUC"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'auc')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 10.2 Καμπύλη Precision-Recall"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'pr')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 10.3 Γρ<PERSON><PERSON>η<PERSON><PERSON> Σπουδαιότητας των Χαρακτηριστικών (Features)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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**********************************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\n", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot='feature')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 10.4 Confusion Matrix"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'confusion_matrix')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["*Ένας άλλος τρόπος* ανάλυσης της απόδοσης των μοντέλων γίνεται με τη χρήση της συνάρτησης `evaluate_model()`, η οποία εμφανίζει ένα γραφικό περιβάλλον με όλα τα διαθέσιμα γραφήματα για ένα δεδομένο μοντέλο. Εσωτερικά χρησιμοποιεί τη συνάρτηση `plot_model()`."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5aeb0446fe204f26adb8d51710569ef9", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(ToggleButtons(description='Plot Type:', icons=('',), options=(('Hyperparameters', 'param…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["evaluate_model(tuned_rf)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 11.0 Προβλέψεις στο Test / Hold-Out Set"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Πριν την οριστικοποίηση του μοντέλου, συνιστάται να κάνουμε έναν τελευταίο έλεγχο εξάγοντας τις προβλέψεις από το test/hold-out σετ και εξετάζοντας τις μετρικές αξιολόγησης. Αν ανατρέξετε στον πίνακα με τις πληροφορίες της Ενότητας 6, θα διαπιστώσετε ότι το 30% (6.841 δείγματα) των δεδομένων έχει διαχωριστεί - διαμορφώνοντας το test/hold-out set. Όλα τα αποτελέσματα των παραπάνω μετρικών αξιολόγησης αφορούν μόνο τα δεδομένα εκπαίδευσης (training set), δηλαδή το 70%. <PERSON><PERSON><PERSON><PERSON>, θα χρησιμοποιήσουμε το αποθηκευμένο εκπαιδευμένο μοντέλο για κάνουμε προβλέψεις στα δεδομένα που διαχωρίσαμε και να εξάγουμε τα αποτελέσματα των μετρικών αξιολόγησης. Σκοπός είναι να δούμε αν υπάρχει ουσιαστική διαφορά σε σχέση με τα αποτελέσματα από το cross validation."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_036d7\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_036d7_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_036d7_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_036d7_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_036d7_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_036d7_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_036d7_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_036d7_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_036d7_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_036d7_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_036d7_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_036d7_row0_col1\" class=\"data row0 col1\" >0.8116</td>\n", "      <td id=\"T_036d7_row0_col2\" class=\"data row0 col2\" >0.7407</td>\n", "      <td id=\"T_036d7_row0_col3\" class=\"data row0 col3\" >0.3436</td>\n", "      <td id=\"T_036d7_row0_col4\" class=\"data row0 col4\" >0.6650</td>\n", "      <td id=\"T_036d7_row0_col5\" class=\"data row0 col5\" >0.4531</td>\n", "      <td id=\"T_036d7_row0_col6\" class=\"data row0 col6\" >0.3530</td>\n", "      <td id=\"T_036d7_row0_col7\" class=\"data row0 col7\" >0.3811</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f679f3d0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(tuned_rf);"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Το accuracy στο test/hold-out set είναι **`0.8116`** για το μοντέλο `tuned_rf`. Το αποτέλεσμα της ίδιας μετρικής κάνοντας cross validation ήταν **`0.8203`** (βλέπ<PERSON> Ενότητα 9.3). Αυτή δεν είναι ουσιαστική διαφορά. Αν υπάρχει μεγαλύτερη διακύμανση μεταξύ των αποτελεσμάτων test/hold-out και CV, τότε συνήθως αυτό υπονοεί over-fitting (ΣτΜ: δηλαδή ότι το μοντέλο έχει προσαρμοστεί υπερβολικά στα δεδομένα εκπαίδευσης και δεν μπορεί να γενικεύσει σε άγνωστα δεδομένα), αλλά μπορεί να οφείλεται και σε άλλους παράγοντες που χρήζουν περαιτέρω διερεύνησης. Σε αυτή την περίπτωση, θα προχωρήσουμε παρακάτω οριστικοποιώντας το μοντέλο και εξάγοντας τις προβλέψεις για τα άγνωστα δεδομένα. Δηλαδή, το 5% των δεδομένων που είχαμε διαχωρίσει από την αρχή και δεν τα έχει δει ποτέ η PyCaret.\n", "\n", "(Συμβουλή: Ε<PERSON><PERSON><PERSON><PERSON> πάντα καλή πρακτική να κοιτάμε την τυπική απόκλιση στα αποτελέσματα από του cross validation, όταν χρησιμοποιούμε το `create_model()`.)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 12.0 Οριστικοποίηση Μοντέλου για Τελική Χρήση."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Η οριστικοποίηση του μοντέλου είναι το τελευτα<PERSON>ο βήμα του πειράματος. Μια τυπική ροή εργασιών μηχανικής μάθησης (ML workflow) ξεκινάει με τη συνάρτηση `setup()`, συνεχίζει συγκρίνοντας τα μοντέλα με την `compare_models()`, εκεί προκρίνονται μερικά υποψήφια μοντέλα (βάσει της μετρικής που ενδιαφέρει) και στα οποία εφαρμόζονται διάφορες τεχνικής μοντελοποίησης, όπως η ρύθμιση των υπερ-παραμέτρων, ensembling, stacking κλπ. Τελικά, αυτή η ροή εργασιών θα οδηγήσει στο καλύτερο μοντέλο που θα χρησιμοποιηθεί για να εξαχθούν οι προβλέψεις από νέα και άγνωστα δεδομένα. Η συνάρτηση `finalize_model()` προσαρμόζει το μοντέλο σε ολόκληρο το dataset, συμπεριλαμβανομένου του test/hold-out set (30% σε αυτή την περίπτωση). Ο σκοπός αυτής της συνάρτησης είναι να εκπαιδεύσει το μοντέλο σε ολόκληρο το dataset προτού είναι έτοιμο για τελική χρήση (deployment)."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["final_rf = finalize_model(tuned_rf)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RandomForestClassifier(bootstrap=False, ccp_alpha=0.0, class_weight={},\n", "                       criterion='entropy', max_depth=5, max_features=1.0,\n", "                       max_leaf_nodes=None, max_samples=None,\n", "                       min_impurity_decrease=0.0002, min_impurity_split=None,\n", "                       min_samples_leaf=5, min_samples_split=10,\n", "                       min_weight_fraction_leaf=0.0, n_estimators=150,\n", "                       n_jobs=-1, oob_score=False, random_state=123, verbose=0,\n", "                       warm_start=False)\n"]}], "source": ["#Final Random Forest model parameters for deployment\n", "#Οι τελικές παράμετροι του μοντέλου Random Forest,\n", "#που προορίζονται για τελική χρήση.\n", "print(final_rf)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["**Προσοχή:** Μια τελευταία προειδοποίηση! Όταν το μοντέλο οριστικοποιείται με την `finalize_model()`, χρησιμοποιείται ολόκληρο το dataset κατά την εκπαίδευση, συμπεριλαμβανομένου του test/hold-out set. Γι' αυτό το λόγο, αν το μοντέλο χρησιμοποιηθεί για να κάνει προβλέψεις βάσει των δεδομένων του test/hold-out set, τότε τα αποτελέσματα θα είναι παραπλανητικά, καθώς θα προσπαθείτε να εξάγετε προβλέψεις για δεδομένα που έχετε ήδη χρησιμοποιήσει κατά τη μοντελοποίηση. Μόνο και μόνο για να καταδείξουμε αυτό ακριβώς, θα χρησιμοποιήσουμε το `final_rf` με την `predict_model` για να συγκρίνουμε τα αποτελέσματα με αυτά της Ενότητας 11."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_c84fd\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_c84fd_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_c84fd_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_c84fd_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_c84fd_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_c84fd_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_c84fd_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_c84fd_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_c84fd_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_c84fd_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_c84fd_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_c84fd_row0_col1\" class=\"data row0 col1\" >0.8184</td>\n", "      <td id=\"T_c84fd_row0_col2\" class=\"data row0 col2\" >0.7526</td>\n", "      <td id=\"T_c84fd_row0_col3\" class=\"data row0 col3\" >0.3533</td>\n", "      <td id=\"T_c84fd_row0_col4\" class=\"data row0 col4\" >0.6985</td>\n", "      <td id=\"T_c84fd_row0_col5\" class=\"data row0 col5\" >0.4692</td>\n", "      <td id=\"T_c84fd_row0_col6\" class=\"data row0 col6\" >0.3736</td>\n", "      <td id=\"T_c84fd_row0_col7\" class=\"data row0 col7\" >0.4053</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f6798e50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(final_rf);"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Παρατηρήστε πως η AUC στο `final_rf` αυξήθηκε σε **`0,7526`** από **`0,7407`**, παρόλο που το μοντέλο παραμένει ίδιο. Αυτό συμβαίνει επειδή το `final_rf` έχει εκπαιδευτεί σε όλα τα δεδομένα, συμπεριλαμβανομένου του test/hold-out set."]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 13.0 Προβλέψεις βάσει Άγνωστων Δεδομένων"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Η συνάρτηση `predict_model()` χρησιμοπο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, επ<PERSON><PERSON><PERSON><PERSON>, για την εξαγωγή προβλέψεων βάσει άγνωστων δεδομένων. Η μόνη διαφορά από την Ενότητα 11 είναι ότι, αυτή τη φορά, θα περάσουμε την παράμετρο `data_unseen` στη συνάρτηση. Τη μεταβλητή `data_unseen` την ορίσαμε στην αρχή του μαθήματος και περιέχει το 5% (1.200 δείγματα) του αρχικού dataset, το οποίο δεν έχει εκτεθεί ποτέ στην PyCaret (βλέπε Ενότητα 5 για επεξήγηση)."]}, {"cell_type": "code", "execution_count": 27, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_8f703\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_8f703_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_8f703_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_8f703_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_8f703_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_8f703_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_8f703_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_8f703_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_8f703_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_8f703_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_8f703_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_8f703_row0_col1\" class=\"data row0 col1\" >0.8167</td>\n", "      <td id=\"T_8f703_row0_col2\" class=\"data row0 col2\" >0.7768</td>\n", "      <td id=\"T_8f703_row0_col3\" class=\"data row0 col3\" >0.3612</td>\n", "      <td id=\"T_8f703_row0_col4\" class=\"data row0 col4\" >0.6463</td>\n", "      <td id=\"T_8f703_row0_col5\" class=\"data row0 col5\" >0.4634</td>\n", "      <td id=\"T_8f703_row0_col6\" class=\"data row0 col6\" >0.3634</td>\n", "      <td id=\"T_8f703_row0_col7\" class=\"data row0 col7\" >0.3857</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f48f688ee20>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>Label</th>\n", "      <th>Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.7911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  \\\n", "0      0  ...      567.0     380.0     601.0       0.0     581.0    1687.0   \n", "1     -1  ...    11873.0   21540.0   15138.0   24677.0   11851.0   11875.0   \n", "2      2  ...     3151.0    5818.0      15.0    9102.0      17.0    3165.0   \n", "3      2  ...   149531.0    6300.0    5500.0    5500.0    5500.0    5000.0   \n", "4      0  ...     1737.0    2622.0    3301.0       0.0     360.0    1737.0   \n", "\n", "   PAY_AMT6  default  Label   Score  \n", "0    1542.0        0      0  0.8051  \n", "1    8251.0        0      0  0.9121  \n", "2    1395.0        0      0  0.8051  \n", "3    5000.0        1      1  0.7911  \n", "4     924.0        0      0  0.9121  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["unseen_predictions = predict_model(final_rf, data=data_unseen)\n", "unseen_predictions.head()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": [" Οι στήλες `Label` και `Score` προστίθενται στο σύνολο δεδομένων `data_unseen`. To Label (Ετικέτα) είναι η πρόβλεψη και το Score είναι η πιθανότητα αυτή της πρόβλεψης. Παρατηρήστε πως τα αποτελέσματα συνενώνονται στο αρχικό dataset, ενώ όλοι οι μετασχηματισμοί εκτελούνται αυτόματα στο υπόβαθρο. Επίσης, μπορείτε να ελέγξετε τις μετρικές αξιολόγησης σε αυτό το dataset, αφού είναι διαθέσιμη η στήλη-στόχ<PERSON> `default` με τις πραγματικές τιμές. Για να το κάνουμε αυτό, θα χρησιμοποιήσουμε το module `pycaret.utils`. Δείτε το παρακάτω παράδειγμα:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["0.8167"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(unseen_predictions['default'], unseen_predictions['Label'], metric = 'Accuracy')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 14.0 Αποθήκευση Μοντέλου"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Τώρα, έχουμε ολοκληρώσει το πείραμα οριστικοποιώντας το μοντέλο `tuned_rf` που είναι αποθηκευμένο στη μεταβλητή `final_rf`. Επίσης, το χρησιμοποιήσαμε για κάνουμε προβλέψεις βάσει των άγνωστων δεδομένων `data_unseen`. Έτσι, φτάσαμε στο τέλος του πειράματος, αλλά παραμένει μια ερώτηση που πρέπει να απαντηθεί: Τι γίνεται όταν έχεις νέα, περισσότερα δεδομένα για να εξάγεις προβλέψεις; Πρέπει να ξανα-εκτελέσετε το πείραμα από την αρχή; Η απάντηση είναι όχι, η ενσωματωμένη συνάρτηση της PyCaret, `save_model()` σας επιτρέπει να αποθηκεύσετε το μοντέλο μαζί με ολόκληρη τη διαδικασία μετασχηματισμού (transformation pipeline) για μελλοντική χρήση."]}, {"cell_type": "code", "execution_count": 29, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Saved\n"]}, {"data": {"text/plain": ["(Pipeline(memory=None,\n", "          steps=[('dtypes',\n", "                  DataTypes_Auto_infer(categorical_features=[],\n", "                                       display_types=True, features_todrop=[],\n", "                                       id_columns=[],\n", "                                       ml_usecase='classification',\n", "                                       numerical_features=[], target='default',\n", "                                       time_features=[])),\n", "                 ('imputer',\n", "                  Simple_Imputer(categorical_strategy='not_available',\n", "                                 fill_value_categorical=None,\n", "                                 fill_value_numerical=None,\n", "                                 numeric_stra...\n", "                  RandomForestClassifier(bootstrap=False, ccp_alpha=0.0,\n", "                                         class_weight={}, criterion='entropy',\n", "                                         max_depth=5, max_features=1.0,\n", "                                         max_leaf_nodes=None, max_samples=None,\n", "                                         min_impurity_decrease=0.0002,\n", "                                         min_impurity_split=None,\n", "                                         min_samples_leaf=5,\n", "                                         min_samples_split=10,\n", "                                         min_weight_fraction_leaf=0.0,\n", "                                         n_estimators=150, n_jobs=-1,\n", "                                         oob_score=False, random_state=123,\n", "                                         verbose=0, warm_start=False)]],\n", "          verbose=False),\n", " 'Final RF Model 14Aug2022.pkl')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["save_model(final_rf,'Final RF Model 14Aug2022')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["(Συμβουλή: Ε<PERSON><PERSON><PERSON><PERSON> πάντα καλή πρακτική να αναγράφετε την ημερομηνία στο όνομα του αρχείου όταν αποθηκεύεται μοντέλα - βοηθάει στον έλεγχο της έκδοσης (version control).)"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 15.0 Φόρτωση Αποθηκευμένου Μοντέλου"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Για να φορτώσουμε ένα μοντέλο είτε σε κάποιο μεταγενέστερο χρόνο είτε σε κάποιο άλλο περιβάλλον, θα χρησιμοποιήσουμε τη συνάρτηση τη<PERSON>, `load_model()`. Στη συνέχεια, θα εφαρμόσουμε το μοντέλο σε νέα άγνωστα δεδομένα για να εξάγουμε προβλέψεις."]}, {"cell_type": "code", "execution_count": 30, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Loaded\n"]}], "source": ["saved_final_rf = load_model('Final RF Model 14Aug2022')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Όταν φορτώσετε το μοντέλο στο περιβάλλον, μπορείτε με απλό τρόπο να κάνετε προβλέψεις σε νέα δεδομένα χρησιμοποιώντας τη συνάρτηση `predict_model()`. Παρακάτω έχουμε εφαρμόσει το φορτωμένο μοντέλο στα ίδια άγνωστα δεδομένα, `data_unseen`, που χρησιμοποιήσαμε στην Ενότητα 13."]}, {"cell_type": "code", "execution_count": 31, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_20dc8\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_20dc8_level0_col0\" class=\"col_heading level0 col0\" >Model</th>\n", "      <th id=\"T_20dc8_level0_col1\" class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th id=\"T_20dc8_level0_col2\" class=\"col_heading level0 col2\" >AUC</th>\n", "      <th id=\"T_20dc8_level0_col3\" class=\"col_heading level0 col3\" >Recall</th>\n", "      <th id=\"T_20dc8_level0_col4\" class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th id=\"T_20dc8_level0_col5\" class=\"col_heading level0 col5\" >F1</th>\n", "      <th id=\"T_20dc8_level0_col6\" class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th id=\"T_20dc8_level0_col7\" class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_20dc8_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_20dc8_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_20dc8_row0_col1\" class=\"data row0 col1\" >0.8167</td>\n", "      <td id=\"T_20dc8_row0_col2\" class=\"data row0 col2\" >0.7768</td>\n", "      <td id=\"T_20dc8_row0_col3\" class=\"data row0 col3\" >0.3612</td>\n", "      <td id=\"T_20dc8_row0_col4\" class=\"data row0 col4\" >0.6463</td>\n", "      <td id=\"T_20dc8_row0_col5\" class=\"data row0 col5\" >0.4634</td>\n", "      <td id=\"T_20dc8_row0_col6\" class=\"data row0 col6\" >0.3634</td>\n", "      <td id=\"T_20dc8_row0_col7\" class=\"data row0 col7\" >0.3857</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f49d08f46a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["new_prediction = predict_model(saved_final_rf, data=data_unseen)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>Label</th>\n", "      <th>Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.7911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  \\\n", "0      0  ...      567.0     380.0     601.0       0.0     581.0    1687.0   \n", "1     -1  ...    11873.0   21540.0   15138.0   24677.0   11851.0   11875.0   \n", "2      2  ...     3151.0    5818.0      15.0    9102.0      17.0    3165.0   \n", "3      2  ...   149531.0    6300.0    5500.0    5500.0    5500.0    5000.0   \n", "4      0  ...     1737.0    2622.0    3301.0       0.0     360.0    1737.0   \n", "\n", "   PAY_AMT6  default  Label   Score  \n", "0    1542.0        0      0  0.8051  \n", "1    8251.0        0      0  0.9121  \n", "2    1395.0        0      0  0.8051  \n", "3    5000.0        1      1  0.7911  \n", "4     924.0        0      0  0.9121  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["new_prediction.head()"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Παρατηρήστε ότι τα αποτελέσματα `unseen_predictions` και `new_prediction` είναι πανομοιότυπα."]}, {"cell_type": "code", "execution_count": 33, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["0.8167"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(new_prediction['default'], new_prediction['Label'], metric = 'Accuracy')"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# 16.0 Σύνοψη / Επόμενα Βήματα;"]}, {"cell_type": "markdown", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["Αυτό το μάθημα έχει καλύψει ολόκληρο το pipeline της μηχανικής μάθησης: λήψη δεδομένων, προ-επ<PERSON><PERSON><PERSON><PERSON><PERSON>α<PERSON>ί<PERSON>, εκ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η μοντέλου, ρύθμιση υπερ-παραμέτρων, προβλέψεις και αποθήκευση μοντέλου για μελλοντική χρήση. Ολοκληρώσαμε όλα αυτά τα βήματα με λιγότερες από 10 εντολές, οι οποίες είναι πολύ φυσικά δομημένες και εύκολο να τις θυμόμαστε διαισθητικά, όπως `create_model()`, `tune_model()`, `compare_models()`. Η εκτέλεση του πειράματος από την αρχή, χωρίς να κάνουμε χρήση της PyCaret, θα απαιτούσε περισσότερες από 100 εντολές για την πλειοψηφία των βιβλιοθηκών.\n", "\n", "Έχουμε καλύψει μόνο τα βασικά του module `pycaret.classification`. Στα επόμενα μαθήματα θα εμβαθύνουμε στην προηγμένη προ-επεξεργα<PERSON><PERSON><PERSON>, τo ensembling, το γενικευμένο stacking και άλλες τεχνικές που επιτρέπουν την πλήρη εξατομίκευση του pipeline μηχανικής μάθησης και πρέπει να είναι κτήμα κάθε Data Scientist.\n", "\n", "Τα λέμε στο επόμενο μάθημα. Ακολουθήστε το σύνδεσμο στο __[Binary Classification Tutorial (CLF102) - Intermediate Level](https://github.com/pycaret/pycaret/blob/master/tutorials/Greek/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": []}], "metadata": {"colab": {"collapsed_sections": ["Ui_rALqYEQmv", "y9s9wNcjEQn0", "it_nJo1IEQob", "P5m2pciOEQo4", "UWMSeyNhEQo-", "rWUojqBCEQpb", "nSg3OUjuEQpu", "XvpjzbGQEQqB", "BQlMCxrUEQqG", "CD-f0delEQqq", "KO3zIfs-EQrA", "w_P46O0jEQrT", "euqkQYJaEQrY", "bwyoTUDQEQrm", "_r9rwEw7EQrz", "FfWC3NEhEQr9", "RX5pYUJJEQsV", "r79BGjIfEQs1", "hUzc6tXNEQtr", "L__po3sUEQt7", "Z8OBesfkEQuU", "_HeOs8BhEQvF"], "name": "Binary Classification Tutorial (CLF101) - Level Beginner (ACN_EDITS).ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"02771b4dc3284414ab05df1906f4556b": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_22588a12c0db4067982e62ebbe7e6930", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9e338844e75b4e17be8483529f5f38fd", "value": 5}}, "0a06fb091bd94ce6b6ab892e2c6faadf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12bf8b3c6ae8444a900474912589fdf1": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsModel", "_options_labels": ["Hyperparameters", "AUC", "Confusion Matrix", "<PERSON><PERSON><PERSON><PERSON>", "Precision Recall", "Error", "Class Report", "Feature Selection", "Learning Curve", "Manifold Learning", "Calibration Curve", "Validation Curve", "Dimensions", "Feature Importance", "Decision Boundary"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonsView", "button_style": "", "description": "Plot Type:", "description_tooltip": null, "disabled": false, "icons": [""], "index": 2, "layout": "IPY_MODEL_0a06fb091bd94ce6b6ab892e2c6faadf", "style": "IPY_MODEL_8886001bc7c1463ba58a8453f5c55073", "tooltips": []}}, "22588a12c0db4067982e62ebbe7e6930": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2a81017413ca4fe789c2272a5831a069": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3cc1e83b91f34b289c7d52003f20a97a": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_8399e21b17634116861a5abaa9c0ccf7", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8d709ec9ec484944b1f9773748857f84", "value": 2}}, "41031579127f4a53b58957e601465083": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "42d5400d235d40b78190016ef0dabe11": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "state": {"_dom_classes": ["widget-interact"], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_12bf8b3c6ae8444a900474912589fdf1", "IPY_MODEL_9bb3600d38c04691b444ff375ad5e3f5"], "layout": "IPY_MODEL_41031579127f4a53b58957e601465083"}}, "57b94ac505d142769b79de2f1e5c1166": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8399e21b17634116861a5abaa9c0ccf7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8886001bc7c1463ba58a8453f5c55073": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_width": "", "description_width": "", "font_weight": ""}}, "8d709ec9ec484944b1f9773748857f84": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9bb3600d38c04691b444ff375ad5e3f5": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_4f8f81ab97b041a58a53c85a1ab97bd4", "msg_id": "", "outputs": [{"image/png": "iVBORw0KGgoAAAANSUhEUgAAAeoAAAFlCAYAAAAki6s3AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAm9n9QR+M8QEgGtgAAAABJRU5ErkJggg==\n", "metadata": {"tags": []}, "output_type": "display_data", "text/plain": "<Figure size 576x396 with 1 Axes>"}]}}, "9e338844e75b4e17be8483529f5f38fd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d5b6fce1763b4b54898ff3397b0f5bb0": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_2a81017413ca4fe789c2272a5831a069", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_57b94ac505d142769b79de2f1e5c1166", "value": 5}}}}}, "nbformat": 4, "nbformat_minor": 1}