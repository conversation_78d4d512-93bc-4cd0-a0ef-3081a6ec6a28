_meta_: {}
image_key: image
label_key: label
transforms_train:
  _target_: <PERSON>mpo<PERSON>
  transforms:
  - _target_: Compose
    transforms:
    - {_target_: LoadImaged, dtype: $np.float32, image_only: false, keys: '@image_key'}
    - {_target_: LoadImaged, dtype: $np.uint8, image_only: false, keys: '@label_key'}
  - _target_: EnsureChannelFirstd
    keys: ['@image_key', '@label_key']
  - {_target_: NormalizeIntensityd, channel_wise: true, keys: '@image_key', nonzero: true}
  - _target_: Orientationd
    axcodes: RAS
    keys: ['@image_key', '@label_key']
  - _target_: Spacingd
    align_corners: [true, true]
    keys: ['@image_key', '@label_key']
    mode: [bilinear, nearest]
    pixdim: '@training#transforms#resample_resolution'
  - _target_: CastToTyped
    dtype: [$torch.float32, $torch.uint8]
    keys: ['@image_key', '@label_key']
  - _target_: EnsureTyped
    keys: ['@image_key', '@label_key']
    track_meta: true
  - _target_: SpatialPadd
    keys: ['@image_key', '@label_key']
    mode: [constant, constant]
    spatial_size: '@training#roi_size'
  - _target_: IdentityD
    keys: ['@label_key']
  - _target_: RandCropByLabelClassesd
    keys: ['@image_key', '@label_key']
    label_key: '@label_key'
    num_classes: '@training#output_classes'
    num_samples: '@training#num_crops_per_image'
    spatial_size: '@training#roi_size'
    warn: false
  - _target_: RandRotated
    keys: ['@image_key', '@label_key']
    mode: [bilinear, nearest]
    prob: 0.2
    range_x: 0.3
    range_y: 0.3
    range_z: 0.3
  - _target_: RandZoomd
    keys: ['@image_key', '@label_key']
    max_zoom: 1.2
    min_zoom: 0.8
    mode: [trilinear, nearest]
    prob: 0.16
  - _target_: IdentityD
    keys: ['@image_key', '@label_key']
  - _target_: RandGaussianSmoothd
    keys: '@image_key'
    prob: 0.15
    sigma_x: [0.5, 1.15]
    sigma_y: [0.5, 1.15]
    sigma_z: [0.5, 1.15]
  - {_target_: RandScaleIntensityd, factors: 0.3, keys: '@image_key', prob: 0.5}
  - {_target_: RandShiftIntensityd, keys: '@image_key', offsets: 0.1, prob: 0.5}
  - {_target_: RandGaussianNoised, keys: '@image_key', prob: 0.15, std: 0.01}
  - _target_: RandFlipd
    keys: ['@image_key', '@label_key']
    prob: 0.5
    spatial_axis: 0
  - _target_: RandFlipd
    keys: ['@image_key', '@label_key']
    prob: 0.5
    spatial_axis: 1
  - _target_: RandFlipd
    keys: ['@image_key', '@label_key']
    prob: 0.5
    spatial_axis: 2
