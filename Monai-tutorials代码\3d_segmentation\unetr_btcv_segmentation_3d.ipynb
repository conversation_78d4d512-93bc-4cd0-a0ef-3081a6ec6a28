{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# 3D Multi-organ Segmentation with UNETR  (BTCV Challenge)\n", "\n", "\n", "This tutorial demonstrates how to construct a training workflow of UNETR [1] on multi-organ segmentation task using the BTCV challenge dataset.\n", "![image](https://lh3.googleusercontent.com/pw/AM-JKLU2eTW17rYtCmiZP3WWC-U1HCPOHwLe6pxOfJXwv2W-00aHfsNy7jeGV1dwUq0PXFOtkqasQ2Vyhcu6xkKsPzy3wx7O6yGOTJ7ZzA01S6LSh8szbjNLfpbuGgMe6ClpiS61KGvqu71xXFnNcyvJNFjN=w1448-h496-no?authuser=0)\n", "\n", "And it contains the following features:\n", "1. Transforms for dictionary format data.\n", "1. Define a new transform according to MONAI transform API.\n", "1. Load Nifti image with metadata, load a list of images and stack them.\n", "1. Randomly adjust intensity for data augmentation.\n", "1. <PERSON><PERSON> and transforms to accelerate training and validation.\n", "1. 3D UNETR model, DiceCE loss function, Mean Dice metric for multi-organ segmentation task.\n", "\n", "The dataset comes from https://www.synapse.org/#!Synapse:syn3193805/wiki/217752.  \n", "\n", "Under Institutional Review Board (IRB) supervision, 50 abdomen CT scans of were randomly selected from a combination of an ongoing colorectal cancer chemotherapy trial, and a retrospective ventral hernia study. The 50 scans were captured during portal venous contrast phase with variable volume sizes (512 x 512 x 85 - 512 x 512 x 198) and field of views (approx. 280 x 280 x 280 mm3 - 500 x 500 x 650 mm3). The in-plane resolution varies from 0.54 x 0.54 mm2 to 0.98 x 0.98 mm2, while the slice thickness ranges from 2.5 mm to 5.0 mm. \n", "\n", "Target: 13 abdominal organs including 1. <PERSON><PERSON><PERSON> 2. <PERSON> <PERSON><PERSON> 3. <PERSON> <PERSON><PERSON> 4.<PERSON><PERSON><PERSON><PERSON> 5.Esopha<PERSON> 6. <PERSON><PERSON> 7. <PERSON><PERSON><PERSON> 8.<PERSON>ort<PERSON> 9. IVC 10. <PERSON> and <PERSON><PERSON><PERSON> Veins 11. <PERSON>cre<PERSON> 12 Right adrenal gland 13 Left adrenal gland.\n", "\n", "Modality: CT\n", "Size: 30 3D volumes (24 Training + 6 Testing)  \n", "Challenge: BTCV MICCAI Challenge\n", "\n", "The following figure shows image patches with the organ sub-regions that are annotated in the CT (top left) and the final labels for the whole dataset (right).\n", "\n", "Data, figures and resources are taken from: \n", "\n", "\n", "1. [UNETR: Transformers for 3D Medical Image Segmentation](https://arxiv.org/abs/2103.10504)\n", "\n", "2. [High-resolution 3D abdominal segmentation with random patch network fusion (MIA)](https://www.sciencedirect.com/science/article/abs/pii/S1361841520302589)\n", "\n", "3. [Efficient multi-atlas abdominal segmentation on clinically acquired CT with SIMPLE context learning (MIA)](https://www.sciencedirect.com/science/article/abs/pii/S1361841515000766?via%3Dihub)\n", "\n", "\n", "![image](https://lh3.googleusercontent.com/pw/AM-JKLX0svvlMdcrchGAgiWWNkg40lgXYjSHsAAuRc5Frakmz2pWzSzf87JQCRgYpqFR0qAjJWPzMQLc_mmvzNjfF9QWl_1OHZ8j4c9qrbR6zQaDJWaCLArRFh0uPvk97qAa11HtYbD6HpJ-wwTCUsaPcYvM=w1724-h522-no?authuser=0)\n", "\n", "\n", "\n", "The image patches show anatomies of a subject, including: \n", "1. large organs: spleen, liver, stomach. \n", "2. Smaller organs: gallbladder, esophagus, kidneys, pancreas. \n", "3. Vascular tissues: aorta, IVC, P&S Veins. \n", "4. G<PERSON>: left and right adrenal gland\n", "\n", "If you find this tutorial helpful, please consider citing [1]:\n", "\n", "[1]: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>, D., 2022. Unetr: Transformers for 3d medical image segmentation. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (pp. 574-584).\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/unetr_btcv_segmentation_3d.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.2 -> 24.3.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install -q \"monai-weekly[nibabel, tqdm, einops]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.5.dev2447\n", "Numpy version: 1.26.4\n", "Pytorch version: 2.3.1+cu118\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 2d6751b01bf78fddabe03b2c53645c6bc9808ed8\n", "MONAI __file__: d:\\anaconda3\\Lib\\site-packages\\monai\\__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: NOT INSTALLED or UNKNOWN VERSION.\n", "ITK version: 5.4.0\n", "Nibabel version: 5.2.1\n", "scikit-image version: 0.23.2\n", "scipy version: 1.14.1\n", "Pillow version: 10.4.0\n", "Tensorboard version: 2.17.1\n", "gdown version: NOT INSTALLED or UNKNOWN VERSION.\n", "TorchVision version: 0.18.1+cu118\n", "tqdm version: 4.66.5\n", "lmdb version: 1.4.1\n", "psutil version: 5.9.0\n", "pandas version: 2.2.2\n", "einops version: 0.8.0\n", "transformers version: 4.44.2\n", "mlflow version: NOT INSTALLED or UNKNOWN VERSION.\n", "pynrrd version: 1.0.0\n", "clearml version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "\n", "from monai.losses import DiceCELoss\n", "from monai.inferers import sliding_window_inference\n", "from monai.transforms import (\n", "    As<PERSON>iscrete,\n", "    EnsureChannelFirstd,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    RandCropByPosNegLabeld,\n", "    RandShiftIntensityd,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", "    RandRotate90d,\n", ")\n", "\n", "from monai.config import print_config\n", "from monai.metrics import DiceMetric\n", "from monai.networks.nets import UNETR\n", "\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    load_decathlon_datalist,\n", "    decollate_batch,\n", ")\n", "\n", "\n", "import torch\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1yt2tker\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms for training and validation"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["monai.transforms.croppad.dictionary CropForegroundd.__init__:allow_smaller: Current default value of argument `allow_smaller=True` was changed in version 1.5 from `allow_smaller=True` to `allow_smaller=False`.\n"]}], "source": ["train_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.5, 1.5, 2.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-175,\n", "            a_max=250,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        RandCropByPosNegLabeld(\n", "            keys=[\"image\", \"label\"],\n", "            label_key=\"label\",\n", "            spatial_size=(96, 96, 96),\n", "            pos=1,\n", "            neg=1,\n", "            num_samples=4,\n", "            image_key=\"image\",\n", "            image_threshold=0,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[0],\n", "            prob=0.10,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[1],\n", "            prob=0.10,\n", "        ),\n", "        RandFlipd(\n", "            keys=[\"image\", \"label\"],\n", "            spatial_axis=[2],\n", "            prob=0.10,\n", "        ),\n", "        RandRotate90d(\n", "            keys=[\"image\", \"label\"],\n", "            prob=0.10,\n", "            max_k=3,\n", "        ),\n", "        RandShiftIntensityd(\n", "            keys=[\"image\"],\n", "            offsets=0.10,\n", "            prob=0.50,\n", "        ),\n", "    ]\n", ")\n", "val_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.5, 1.5, 2.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        ScaleIntensityRanged(keys=[\"image\"], a_min=-175, a_max=250, b_min=0.0, b_max=1.0, clip=True),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": [" ## Download dataset and format in the folder.\n", "    1. Download dataset from here: https://www.synapse.org/#!Synapse:syn3193805/wiki/89480\\n\n", "    2. Put images in the ./data/imagesTr\n", "    3. Put labels in the ./data/labelsTr\n", "    4. make JSON file accordingly: ./data/dataset_0.json\n", "    Example of JSON file:\n", "     {\n", "    \"description\": \"btcv yucheng\",\n", "    \"labels\": {\n", "        \"0\": \"background\",\n", "        \"1\": \"spleen\",\n", "        \"2\": \"rkid\",\n", "        \"3\": \"lkid\",\n", "        \"4\": \"gall\",\n", "        \"5\": \"eso\",\n", "        \"6\": \"liver\",\n", "        \"7\": \"sto\",\n", "        \"8\": \"aorta\",\n", "        \"9\": \"IVC\",\n", "        \"10\": \"veins\",\n", "        \"11\": \"pancreas\",\n", "        \"12\": \"rad\",\n", "        \"13\": \"lad\"\n", "    },\n", "    \"licence\": \"yt\",\n", "    \"modality\": {\n", "        \"0\": \"CT\"\n", "    },\n", "    \"name\": \"btcv\",\n", "    \"numTest\": 20,\n", "    \"numTraining\": 80,\n", "    \"reference\": \"Vanderbilt University\",\n", "    \"release\": \"1.0 06/08/2015\",\n", "    \"tensorImageSize\": \"3D\",\n", "    \"test\": [\n", "        \"imagesTs/img0061.nii.gz\",\n", "        \"imagesTs/img0062.nii.gz\",\n", "        \"imagesTs/img0063.nii.gz\",\n", "        \"imagesTs/img0064.nii.gz\",\n", "        \"imagesTs/img0065.nii.gz\",\n", "        \"imagesTs/img0066.nii.gz\",\n", "        \"imagesTs/img0067.nii.gz\",\n", "        \"imagesTs/img0068.nii.gz\",\n", "        \"imagesTs/img0069.nii.gz\",\n", "        \"imagesTs/img0070.nii.gz\",\n", "        \"imagesTs/img0071.nii.gz\",\n", "        \"imagesTs/img0072.nii.gz\",\n", "        \"imagesTs/img0073.nii.gz\",\n", "        \"imagesTs/img0074.nii.gz\",\n", "        \"imagesTs/img0075.nii.gz\",\n", "        \"imagesTs/img0076.nii.gz\",\n", "        \"imagesTs/img0077.nii.gz\",\n", "        \"imagesTs/img0078.nii.gz\",\n", "        \"imagesTs/img0079.nii.gz\",\n", "        \"imagesTs/img0080.nii.gz\"\n", "    ],\n", "    \"training\": [\n", "        {\n", "            \"image\": \"imagesTr/img0001.nii.gz\",\n", "            \"label\": \"labelsTr/label0001.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0002.nii.gz\",\n", "            \"label\": \"labelsTr/label0002.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0003.nii.gz\",\n", "            \"label\": \"labelsTr/label0003.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0004.nii.gz\",\n", "            \"label\": \"labelsTr/label0004.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0005.nii.gz\",\n", "            \"label\": \"labelsTr/label0005.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0006.nii.gz\",\n", "            \"label\": \"labelsTr/label0006.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0007.nii.gz\",\n", "            \"label\": \"labelsTr/label0007.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0008.nii.gz\",\n", "            \"label\": \"labelsTr/label0008.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0009.nii.gz\",\n", "            \"label\": \"labelsTr/label0009.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0010.nii.gz\",\n", "            \"label\": \"labelsTr/label0010.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0021.nii.gz\",\n", "            \"label\": \"labelsTr/label0021.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0022.nii.gz\",\n", "            \"label\": \"labelsTr/label0022.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0023.nii.gz\",\n", "            \"label\": \"labelsTr/label0023.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0024.nii.gz\",\n", "            \"label\": \"labelsTr/label0024.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0025.nii.gz\",\n", "            \"label\": \"labelsTr/label0025.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0026.nii.gz\",\n", "            \"label\": \"labelsTr/label0026.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0027.nii.gz\",\n", "            \"label\": \"labelsTr/label0027.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0028.nii.gz\",\n", "            \"label\": \"labelsTr/label0028.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0029.nii.gz\",\n", "            \"label\": \"labelsTr/label0029.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0030.nii.gz\",\n", "            \"label\": \"labelsTr/label0030.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0031.nii.gz\",\n", "            \"label\": \"labelsTr/label0031.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0032.nii.gz\",\n", "            \"label\": \"labelsTr/label0032.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0033.nii.gz\",\n", "            \"label\": \"labelsTr/label0033.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0034.nii.gz\",\n", "            \"label\": \"labelsTr/label0034.nii.gz\"\n", "        }\n", "    ],\n", "    \"validation\": [\n", "        {\n", "            \"image\": \"imagesTr/img0035.nii.gz\",\n", "            \"label\": \"labelsTr/label0035.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0036.nii.gz\",\n", "            \"label\": \"labelsTr/label0036.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0037.nii.gz\",\n", "            \"label\": \"labelsTr/label0037.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0038.nii.gz\",\n", "            \"label\": \"labelsTr/label0038.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0039.nii.gz\",\n", "            \"label\": \"labelsTr/label0039.nii.gz\"\n", "        },\n", "        {\n", "            \"image\": \"imagesTr/img0040.nii.gz\",\n", "            \"label\": \"labelsTr/label0040.nii.gz\"\n", "        }\n", "    ]\n", "}\n", "    "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Data list file \\dataset\\dataset_0.json does not exist.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 5\u001b[0m\n\u001b[0;32m      2\u001b[0m split_json \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdataset_0.json\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m      4\u001b[0m datasets \u001b[38;5;241m=\u001b[39m data_dir \u001b[38;5;241m+\u001b[39m split_json\n\u001b[1;32m----> 5\u001b[0m datalist \u001b[38;5;241m=\u001b[39m load_decathlon_datalist(datasets, \u001b[38;5;28;01mTrue\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtraining\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      6\u001b[0m val_files \u001b[38;5;241m=\u001b[39m load_decathlon_datalist(datasets, \u001b[38;5;28;01mTrue\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalidation\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      7\u001b[0m train_ds \u001b[38;5;241m=\u001b[39m CacheDataset(\n\u001b[0;32m      8\u001b[0m     data\u001b[38;5;241m=\u001b[39mdatalist,\n\u001b[0;32m      9\u001b[0m     transform\u001b[38;5;241m=\u001b[39mtrain_transforms,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     12\u001b[0m     num_workers\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m8\u001b[39m,\n\u001b[0;32m     13\u001b[0m )\n", "File \u001b[1;32md:\\anaconda3\\Lib\\site-packages\\monai\\data\\decathlon_datalist.py:120\u001b[0m, in \u001b[0;36mload_decathlon_datalist\u001b[1;34m(data_list_file_path, is_segmentation, data_list_key, base_dir)\u001b[0m\n\u001b[0;32m    118\u001b[0m data_list_file_path \u001b[38;5;241m=\u001b[39m Path(data_list_file_path)\n\u001b[0;32m    119\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data_list_file_path\u001b[38;5;241m.\u001b[39mis_file():\n\u001b[1;32m--> 120\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mV<PERSON>ueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mData list file \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata_list_file_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m does not exist.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    121\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(data_list_file_path) \u001b[38;5;28;01mas\u001b[39;00m json_file:\n\u001b[0;32m    122\u001b[0m     json_data \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mload(json_file)\n", "\u001b[1;31mValueError\u001b[0m: Data list file \\dataset\\dataset_0.json does not exist."]}], "source": ["data_dir = \"/dataset/\"\n", "split_json = \"dataset_0.json\"\n", "\n", "datasets = data_dir + split_json\n", "datalist = load_decathlon_datalist(datasets, True, \"training\")\n", "val_files = load_decathlon_datalist(datasets, True, \"validation\")\n", "train_ds = CacheDataset(\n", "    data=datalist,\n", "    transform=train_transforms,\n", "    cache_num=24,\n", "    cache_rate=1.0,\n", "    num_workers=8,\n", ")\n", "train_loader = DataLoader(train_ds, batch_size=1, shuffle=True, num_workers=8, pin_memory=True)\n", "val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_num=6, cache_rate=1.0, num_workers=4)\n", "val_loader = DataLoader(val_ds, batch_size=1, shuffle=False, num_workers=4, pin_memory=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check data shape and visualize"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image shape: torch.<PERSON><PERSON>([1, 314, 214, 234]), label shape: torch.<PERSON><PERSON>([1, 314, 214, 234])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["slice_map = {\n", "    \"img0035.nii.gz\": 170,\n", "    \"img0036.nii.gz\": 230,\n", "    \"img0037.nii.gz\": 204,\n", "    \"img0038.nii.gz\": 204,\n", "    \"img0039.nii.gz\": 204,\n", "    \"img0040.nii.gz\": 180,\n", "}\n", "case_num = 0\n", "img_name = os.path.split(val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n", "img = val_ds[case_num][\"image\"]\n", "label = val_ds[case_num][\"label\"]\n", "img_shape = img.shape\n", "label_shape = label.shape\n", "print(f\"image shape: {img_shape}, label shape: {label_shape}\")\n", "plt.figure(\"image\", (18, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"image\")\n", "plt.imshow(img[0, :, :, slice_map[img_name]].detach().cpu(), cmap=\"gray\")\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"label\")\n", "plt.imshow(label[0, :, :, slice_map[img_name]].detach().cpu())\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create Model, Loss, Optimizer\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "model = UNETR(\n", "    in_channels=1,\n", "    out_channels=14,\n", "    img_size=(96, 96, 96),\n", "    feature_size=16,\n", "    hidden_size=768,\n", "    mlp_dim=3072,\n", "    num_heads=12,\n", "    pos_embed=\"perceptron\",\n", "    norm_name=\"instance\",\n", "    res_block=True,\n", "    dropout_rate=0.0,\n", ").to(device)\n", "\n", "loss_function = DiceCELoss(to_onehot_y=True, softmax=True)\n", "torch.backends.cudnn.benchmark = True\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Execute a typical PyTorch training process"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["def validation(epoch_iterator_val):\n", "    model.eval()\n", "    with torch.no_grad():\n", "        for batch in epoch_iterator_val:\n", "            val_inputs, val_labels = (batch[\"image\"].cuda(), batch[\"label\"].cuda())\n", "            val_outputs = sliding_window_inference(val_inputs, (96, 96, 96), 4, model)\n", "            val_labels_list = decollate_batch(val_labels)\n", "            val_labels_convert = [post_label(val_label_tensor) for val_label_tensor in val_labels_list]\n", "            val_outputs_list = decollate_batch(val_outputs)\n", "            val_output_convert = [post_pred(val_pred_tensor) for val_pred_tensor in val_outputs_list]\n", "            dice_metric(y_pred=val_output_convert, y=val_labels_convert)\n", "            epoch_iterator_val.set_description(\"Validate (%d / %d Steps)\" % (global_step, 10.0))  # noqa: B038\n", "        mean_dice_val = dice_metric.aggregate().item()\n", "        dice_metric.reset()\n", "    return mean_dice_val\n", "\n", "\n", "def train(global_step, train_loader, dice_val_best, global_step_best):\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    epoch_iterator = tqdm(train_loader, desc=\"Training (X / X Steps) (loss=X.X)\", dynamic_ncols=True)\n", "    for step, batch in enumerate(epoch_iterator):\n", "        step += 1\n", "        x, y = (batch[\"image\"].cuda(), batch[\"label\"].cuda())\n", "        logit_map = model(x)\n", "        loss = loss_function(logit_map, y)\n", "        loss.backward()\n", "        epoch_loss += loss.item()\n", "        optimizer.step()\n", "        optimizer.zero_grad()\n", "        epoch_iterator.set_description(  # noqa: B038\n", "            \"Training (%d / %d Steps) (loss=%2.5f)\" % (global_step, max_iterations, loss)\n", "        )\n", "        if (global_step % eval_num == 0 and global_step != 0) or global_step == max_iterations:\n", "            epoch_iterator_val = tqdm(val_loader, desc=\"Validate (X / X Steps) (dice=X.X)\", dynamic_ncols=True)\n", "            dice_val = validation(epoch_iterator_val)\n", "            epoch_loss /= step\n", "            epoch_loss_values.append(epoch_loss)\n", "            metric_values.append(dice_val)\n", "            if dice_val > dice_val_best:\n", "                dice_val_best = dice_val\n", "                global_step_best = global_step\n", "                torch.save(model.state_dict(), os.path.join(root_dir, \"best_metric_model.pth\"))\n", "                print(\n", "                    \"Model Was Saved ! Current Best Avg. Dice: {} Current Avg. Dice: {}\".format(dice_val_best, dice_val)\n", "                )\n", "            else:\n", "                print(\n", "                    \"Model Was Not Saved ! Current Best Avg. Dice: {} Current Avg. Dice: {}\".format(\n", "                        dice_val_best, dice_val\n", "                    )\n", "                )\n", "        global_step += 1\n", "    return global_step, dice_val_best, global_step_best\n", "\n", "\n", "max_iterations = 25000\n", "eval_num = 500\n", "post_label = AsDiscrete(to_onehot=14)\n", "post_pred = AsDiscrete(argmax=True, to_onehot=14)\n", "dice_metric = DiceMetric(include_background=True, reduction=\"mean\", get_not_nans=False)\n", "global_step = 0\n", "dice_val_best = 0.0\n", "global_step_best = 0\n", "epoch_loss_values = []\n", "metric_values = []\n", "while global_step < max_iterations:\n", "    global_step, dice_val_best, global_step_best = train(global_step, train_loader, dice_val_best, global_step_best)\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train completed, best_metric: 0.8027 at iteration: 25000\n"]}], "source": ["print(f\"train completed, best_metric: {dice_val_best:.4f} \" f\"at iteration: {global_step_best}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot the loss and metric"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Iteration Average Loss\")\n", "x = [eval_num * (i + 1) for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val Mean Dice\")\n", "x = [eval_num * (i + 1) for i in range(len(metric_values))]\n", "y = metric_values\n", "plt.xlabel(\"Iteration\")\n", "plt.plot(x, y)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check best model output with the input image and label"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA80AAAF1CAYAAADSsAjEAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8vihELAAAACXBIWXMAAAsTAAALEwEAmpwYAAEAAElEQVR4nOz9eZgc53neC99vdVdXdfXesw8wM1hJEAApcBVFWoslyrJsSYx1bMW2kjhWEmVzYvtkOY6vz1+cxEl0Ep/4+MRKLPlzJMvxItuxLNnSsWRrtSRKpChBEjeAIAkQGMza0/tWvdT3x+B+5+2ewUJs3TN4fteFCzPdPdVvVYPFud/7ee5HBUEAQRAEQRAEQRAEQRA2Yw16AYIgCIIgCIIgCIIwrIhoFgRBEARBEARBEISLIKJZEARBEARBEARBEC6CiGZBEARBEARBEARBuAgimgVBEARBEARBEAThIohoFgRBEARBEARBEISLIKL5Fkcp9bRS6g2DXocgCIKwjlLqtFLqkSt4XaCUOnCV73HVPysIgiAItxoimm9xgiA4EgTBFwa9DkEQBEEQBEHYTlzvDUjZ0BxeRDQLgiAIgiAIgiAIwkUQ0XyLwzJApdQvKqX+UCn1P5VSZaXUd5VStyml/pVSalkpdVYp9X3Gz/2kUurZC699USn19/uO+y+VUgtKqfNKqb9r7pwppRyl1C8rpV5WSi0ppX5dKRW92ecuCIIwzCilHlBKPaaUKly4n/6aUirS97IfuHAPXlVK/WellGX8/Hsu3KfzSqlPK6XmbvIpCIIgbAuUUncopb5w4X77tFLqHRce/4JS6u8ar/vbSqkvX/j6Sxce/rZSqqKU+utKqTcopc4ppX7+wn35tFLq3cbPv6Lj3ejzFq4cEc2CydsB/DaADIBvAfg01v+N7ALwbwF8wHjtMoC3AUgC+EkAv6KUugcAlFLfD+B/B/AIgAMA3tD3Pu8DcBuAYxee3wXg/3sDzkcQBGE70wHwswBGAbwGwJsA/KO+1/wQgPsA3APgUQDvAQCl1KMAfh7AOwGMAfgrAL93U1YtCIKwjVBK2QD+FMBnAIwD+CcAfkcpdfulfi4Igtdd+PJVQRDEgyD46IXvJ7F+394F4CcAfPByx7rM8YQhQESzYPJXQRB8OgiCNoA/xPovWu8LgqAF4PcB7FFKpQEgCIJPBkHwQrDOF7F+o3ntheO8C8CHgiB4OgiCGoBf5BsopRSA9wL42SAI1oIgKAP4DwB+9OacoiAIwvYgCIIngyD4WhAE7SAITmN94/L1fS/7Py/cS18G8H8D+LELj/8DAP8xCIJnL9zT/wOAY+I2C4IgbOJBAHGs/87rB0HwOQB/ho376dXwC0EQNC/8jvxJrP9uLGxjRDQLJkvG13UAq0EQdIzvgfWbCpRSb1VKfU0ptaaUKgD4AazvqgHANICzxrHMr8cAeACevFACUwDw5xceFwRBEC5woUXmz5RSi0qpEtaF72jfy8z76xms338BYA7Arxr32TUACuvOhyAIgrDBNICzQRB0jcfO4Orvl/kgCKp9x5q+2IuF7YGIZuEVo5RyAPwvAL8MYCIIgjSAT2H9FzIAWACw2/iRGePrVawL8CNBEKQv/EkFQRC/8SsXBEHYVvx3AM8BOBgEQRLr5daq7zXm/XUWwPkLX58F8PeN+2w6CIJoEARfveGrFgRB2F6cBzBjZkJg/X46D6CKdbOHTF7B8TJKqVjfsXhvvprjCUOAiGbhaogAcACsAGgrpd4K4PuM5/8AwE9eCFXwAPwCn7iwi/cbWO+BHgcApdQupdRbbtrqBUEQtgcJACUAFaXUIQD/cIvX/AulVEYpNQPgpwGwB+7XAfwrpdQRAFBKpZRSP3IzFi0IgrDN+DqAGoB/qZSylVJvwHrOz+8DOA7gnUop70Kg7d/p+9klAPu2OOa/UUpFlFKvxXoG0B9eePxqjycMGBHNwivmQh/yP8W6OM4D+HEAnzCe/38B/D8APg/gFICvXXiqeeHv/4OPXyg5/EsAlw1IEARBuMX451i/v5axvtm4VSjMxwE8ifVfxD4J4DcBIAiCjwH4PwH8/oX77FMA3nrjlywIgrC9CILAx7pIfivWKyL/G4C/FQTBcwB+BYCPdTH7WwB+p+/HfxHAb11ohWHf8iLWfz8+f+H1/+DCsXCVxxOGABUEwaDXIOxwlFJ3YP0XNudCII0gCIIgCIIg7CguuNT/MwiC3Zd5qbDNEKdZuCEopX7owjzmDNbdjj8VwSwIgiAIgiAIwnZDRLNwo/j7WJ/l/ALWZ41u1YsnCIIgCIIgCIIw1Nww0ayU+n6l1Aml1Cml1M/dqPcRhpMgCL7/Qip2NgiCHwqCYGHQaxKEWw25DwuCIAweuRffOgRB8AUpzd6Z3JCeZqVUCMBJAG8GcA7AEwB+LAiCZ677mwmCIAibkPuwIAjC4JF7sSDsDG6U0/wAgFNBELx4IZHu9wE8eoPeSxAEQdiM3IcFQRAGj9yLBWEHEL5Bx90F4Kzx/TkArzZfoJR6L4D3Xvj23hu0DkEQLs9qEARjg16EcN257H0Y6L0XhxC610Py5qxOEIQeysjLvXhn8op+J5b7sCAMjkvdh2+UaL4sQRB8EMAHAUApJXOvBGFwnBn0AoTBYd6LkyobvFq9acArEoRbk78M/kjuxbcoch8WhOHgUvfhG1WePQ9gxvh+94XHBEEQhJuD3IcFQRAGj9yLBWEHcKNE8xMADiql9iqlIgB+FMAnbtB7CYIgCJuR+7AgCMLgkXuxIOwAbkh5dhAEbaXUTwH4NIAQgP8RBMHTN+K9BEEQhM3IfVgQBGHwyL1YEHYGN6ynOQiCTwH41I06viAIgnBp5D4sCIIweOReLAjbnxtVni0IgiAIgiAIgiAI2x4RzYIgCIIgCIIgCIJwEUQ0C4IgCIIgCIIgCMJFENEsCIIgCIIgCIIgCBdBRLMgCIIgCIIgCIIgXAQRzYIgCIIgCIIgCIJwEUQ0C4IgCIIgCIIgCMJFENEsCIIgCIIgCIIgCBdBRLMgCIIgCIIgCIIgXAQRzYIgCIIgCIIgCIJwEUQ0C4IgCIIgCIIgCMJFENEsCIIgCIIgCIIgCBchPOgFCMLNQCml/5gEQaD/Vkqh2+0OYnmCIAiCIAiCIAwpIpqFHYtSCpa1Xkzhui5s20YoFOp5TbvdhmVZaDabsG0btVoNQRAgCAJ0Op1BLFsQBEEQBEEQhCFCRLOwo6CbbFkWXNeF67oIggChUAiO46DT6SAcDqPT6WhBHQqF4Ps+wuEwPM9Dt9tFu91GPp+HUgpBEIgDLQiCIAiCIAi3KCKahW2PZVkIhUJQSiEcDiMcDiMSicBxHITDYbRaLe0cUwQD62K52+3q0uxIJALLshAOh9FsNuH7vi7nrtfr6HQ6Ip4FQRAEQRAE4RZDRLOwrbEsC57nIRqN9ohn27ZhWRaUUmi1WgA2+pbNvuYgCNBut/WxAKDb7UIpBcdxoJRCNBqF53moVCpotVpoNps3/0QFQRAEQRAEQRgIIpqFbUs4HIbruojH47r02rIsBEGgBXCn00G73YZSCqFQCLZto9Pp6N5mOtCO42gXud1u615nU4DzuKurq+h2u9LzLAiCIAiCIAi3ADJySth2UMgmEgnE43FEIpFN4V0UyizFDofDugSbx+Dr6E53u13tMtOVDoVCaLfb6HQ6iEQiPe9r2/ZgLoAgCIIgCIIgCDcNEc2X4OjRo4jH44NehmDAsulYLAbXdREOh7XrS4eYjjPFNZ1i/qFA5uv4GAW37/s9KdvsibYsC+12G7ZtI5VKIZlMaiEtCIIgCIIgCMLORETzRXj961+Pe++9F67rDnopgkE4HEYsFkM0GtWOsJluTSFMMc0ybb7OFMlBEPQIXoaCAdACmY+Fw2HtXjNwLBaLIZPJIJVKbRplJQiCIAiCIAjCzkBE8xYcOnQI73rXu/Dkk09idXV10MsRLsAxUpFIBKFQSId4saTa/MNSbYZ8UTT7vq/FtOM4Pc8B0IKY4rvb7SIcXm/9b7VaaLVaut/Ztm3EYjHE43HteguCIAiCIAiCsLOQ3/K34NFHH8UTTzyBc+fODXopA4Elx3RpOfeYwpKzjvk9e4GBdWHp+/4NWVc4HIbjOAiFQrAsS5dT00FmDzKfM8dDUSizfJvHAzZSszmCinObec4MDwPWe6AZGsYybwDwPA+2baNYLOrrIgiCIAiCIAjC9kdEcx+HDx+G4zj46le/ikKhMOjl3BQcx9Fl6OwDpjglFM6dTgetVgvRaFSXRZuCtNvtolqt6lJmzje+HlAYU+wyzIsl16Z4Nkut+RrzfPjarcqq6TCzLJvvQwea4hqAdrIjkYieCd1oNCRZWxAEQRAEQRB2CCKa+3jd616HJ598EvPz84Neyg2Ds40jkYgWofF4HM1mU/cCUxAzIKvb7cLzPADrQtEsjQbWBSV7iW3bRqPRQCgU0qOgGNLVaDSues0svQ6FQj1OMl1vU9izrJrnQChmKbRbrZY+XrPZRDwe13Odzffl8ZrNJlqtFiKRCDqdDjqdjr6GSikkk0mEw2FUKhURzoIgCIIgCIKwAxDRbHDs2DFUKhUcP34c1Wp10Mu5rkQiEUSjUYTDYYRCIR2kZZYs0zWlyDT7fPm8bduoVqtaWPu+D9u2tZDudDpawHa7XcRisZ5k6kqlgkqlot/rlUDBzLWZ72P2NfeXZXNslNnvrJRCu93WopnCl2Kaj3GcVf9sZ9u2daq2eb1s29bCu1arXeWnJQiCIAiCIAjCsCCi+QLhcBiPPPIInnjiCSwtLQ16OdeM4ziIx+Na6LF8mA4p3dRWq6UFIYVkOBzW7q0pSJvNJhzH0WXIZp8zS5p5LApV83mlFNLptBairVYL5XL5ihxZ8zjARrm42U9tOsqmM22KfwrvbreLZrOpX6+U0iFjnN3M69bpdGDbtnab6UwzLMxco23bPaOuTNdaEARBEARBEITth4jmCzz44IOoVCo4c+bMVbmgw0AkEkEikYBt2wiHw7pP2XRkzVnG7A02RzOZwrffxQWgBSQdZpZAU4h2u13Ytr0pkdrshU4kEvp4juOgWq2iUqlc9vzMUVH82nyO6+d6eN4U5ebr6ZJTwJsi3EzI5kYChTAddrrMfG9eQ240OI4DpdRVu+qCIAiCIAiCIAwHIpoBpFIpHDt2DN/97ne3XS+z4zjIZDL6+1QqBaUUWq2WFsmmyDQFpPm12cPcarU2Cd8gCLTDagpW9gWboVum88s/pptMBzYUCiGRSGhXvNFoXDR8zUzw5s8SilazTNp0pM3zN2c627atxTVf32g09Fgqz/P0mCmOu+LPs2Sbx6jVanBdV5excyxWp9NBuVy+yk9XEARBEARBEIRBI6IZwJvf/GasrKzg6aef3jbltLZtI5VKwfM8eJ6HdrsN3/e1qDRHJgHocV8pEBliZT5OgUtRCECXWgPQDizQK4opRE1Ras475vdEKaXXyD7gbreLSCSihSvfz+xHNlOxzffkcxTO/X+bPdAkHA7rUmw6yuY1CYVCaDabPS52q9XSvc8sxzZ7wWu1mnbdOSKr3W6jXq9fj49dEARBEARBEISbzC0vmnfv3o277roLn/vc57C6ujro5VyWSCSCbDar+44Z5EXB1z+fGMAmsWiWEgMbbixFLV1c05EFsKn32LIs+L6v3Wn+LJ1fim1+TQHZaDR0WTfX4fs+wuEwstmsdsc5L7lcLuvEaq6R59nvKHMNZm82NxNM+sdpcZOAgpkimufJ69FutxGNRnuui5nUzc+I151fdzqdGza/WhAEQRAEQRCEG8ctL5pf85rX4OTJk3j66acHvZRLEgqFkEql9EgjjnxiKbXpHgMbzqpZYm2KVLrJAPRjdJlNF5Vl3uZ4JgA9zq9Ztk2RaPZC9zvAnU4HjuNocc2wrXq9rmdGRyIReJ6HUCiEcrmMWq3Wk9Btusyme97fx8y1mKKao7HMnm1ijpgKhUI69IyOMjcq+F7meC46y/wceKxIJALXdTeVsQuCIAiCIAiCMPxYl3/JzuXIkSPYvXs3nnvuOaysrAx6ORclEolgZmYGmUxGB1fRwTV7i83Ea7O8mq+hQDTdWT5OUUkBStHI5wl7i/kcRSTf0zw2e4FNwWoez5yvDKwLzUajgVarhWaziW63i2g0irGxMUxNTSGbzSIajcJxnJ61cTazuR4ej/3J/B6A3mQwy71NIW9uIlAMs5SbAp9l6twkoEPt+37PLGiGqkWj0R53WxAEQRAEQRCE7cEt7TQ//PDDWFtbw8mTJwe9lC2xLAvpdBrpdBqu66JWq2khaorU/oArusD8uj/dmmOTTKHM9wPWx1A5jrNJ6Jo9zMRxnE19vzwW10dRSqcWgP6aQVumcOdrOTs5mUzq3u1KpaLHVLHHGNgYScX+7P70bvO9zaRsimQKXdMp52vNNHKmi3PjgNe93+nmOfJ1tm3rcVaCIAiCIAiCIGwfblnRfOzYMUQiETz++OMolUqDXs4mQqEQxsbGkEqleuYC949/MkuqgQ1x2+9qmuXU/XONzfJiij5zpjGFrOnOmiOrmCLNkm+zL7hfvFMIt1ot/TzLvtkPzV7per2OtbU1WJaFTCaDZDKp5x9bloVCodAzJ9l037lWXod2u61nKHOdZkCZGQDHknFzVjM3Hvi6SCSi3XC6/GZgGT8bimYe0/d9Ec6CIAiCIAiCsI24ZUXzbbfdhvn5eZw4cWLQS9lEKBTC1NSUHmFkClYKPQoxCjWgtzd5q15f08U1xzTxuGbwVb9Ly583S7Xp3pqBYe12G47jaBFOeJxIJKJFpek6Uwgz0IzhYEEQIJ/PQymF8fFxRKNRLe7purO32zwuhby5mUBB3mq1emZRm4ngvFbhcFifT//P0Zk2Q854jZLJJOr1uhbsFM9BECAajaLdbl/RTGpBEARBEARBEIaDW1I0v/rVr4bjOHjyySeHbhSQZVmYnJzsKQU2HVgKRQA9Jdem0CWma0xxZ5Yvm+Ka4tcUu2Z5sXksE4p2Hp9OMuE5mIRCIUQiET2+iefRH2bG41erVV0mnclkdHp1t9tFqVTSPdCNRkOvk9ejf2OA14WbESzH5rXnRgQDveggm+fK15lusu/7el28nuw9Z2q2bdtwXReNRkPcZkEQBEEQBEHYJtxyotmyLMzMzKBUKuHll18e9HJ6CIVC2Lt3LyzLQrPZ1OKN4gyAFtEsHaaA49f9JdEU0hSTruvqMVHmuCe6snw9y615XApOzjXm1xSd5s9yfBThurgmvjfLns1gMWKuu16v6/cNhUI6EGxsbAyu66JaraJWq2n3l+/D60fX1+xf5rUx08fNsnWOquJ59l/Der2urxE3GrgRYJaA8314/Wzb7pl7LQiCIAiCIAjCcHPLieb77rsPrVYLjz32WE8f66BRSmkRyPJe0800S4ZNKPb6k7RNwcxyZfbVmsKSQpKvZ0kxAC32zP5g3/e1o2yKavb78jUUnRzDZDrSvO79Y6hMeE50i7vdLgqFgj5GMplELBbTAVsUrPV6vWcTgZgJ2BToLLWmo26K3Gaz2TOWi+s2k7cpkHn+7XYbzWazp7/ZdPe5pnA4jGazeT3+2QiCIAiCIAiCcIO5pURzMpnE3r17USwWh2rElFIKExMTiMfjuj93q9FHwEZvrlmmTcxRTgy9MsueKe4cx9FuMwA9wsmcuWyKcApgzi12XRftdhvtdhuu6/YkV5vC3FxPs9nUQpNlzJFIRJcqmz3APBawEXZG0VoqlXrEaTKZhOu6eqY0HeZut6udYm4GmD3Y7KM2e73N5GuKdSaJU9jbtt3j1FOM85jmSCqOv+J705GOx+O6kkAQBEEQBEEQhOHmmkSzUuo0gDKADoB2EAT3KaWyAD4KYA+A0wDeFQRB/tqWeX249957EQQBvvzlL2/q/x0klmVhdHQU9Xpd9+/ycbLV3GUKTYpTliCbLrOZpk3hRgfX9/2e0mkes1+Mcy0UfZ1OB61WS4tjingzFIzl2abwp7NLcU733BS3xEwMNzcQfN+H7/toNBq6zzkajSKVSvUEiFGM89gUuGYwmBnyZW4YmIneZn83n+d15Jp47uxttiyrJ0281Wrp92Lpt4yfEq4n2+1eLAiCsNOQ+7Ag7Gw2q6NXzvcGQXAsCIL7Lnz/cwA+GwTBQQCfvfD9wBkbG8PExATy+fxQpRdbloX9+/drIWv2z5oJ0XQ+zUAuiuj+km1gfdYyxxsxIAvAJuHM5zkrme6q7/toNpvwfV+XJXPEkplsTXFpOtNMkjZ7oekWcx0M7zJFOwWq6Xr3j8Hie5dKJSwsLODs2bMolUqwbRujo6OYmJjA6OgoMpkMPM/TQWNmMBeFK0ut6axzXWb/M4Uw3WVzhjQd7G63C9u20Wg09GdKeD3D4XDP2K3+QDVBuA5si3uxIAjCDkbuw4KwQ7keormfRwH81oWvfwvAX7sB7/GKueOOO9DpdPDlL3950EvpwbIsxONxLcoo7igwzbFH7I/td6LpbEYikR4nlW5qf7k1j20KQ3N+sznvmJjzlykq+0vH+3uguXYK8lar1VP2zBJv9j33z4825yzzPM1y6kqlgvPnz+Ps2bMoFotQSiGVSiGdTiMejyMejyMSicB1XX0M0/Wlc0zxHgSBLlXnNaCrznJ3y7IQjUa1i8wyb/M68Rqy19oU5iwJZ5K2INxAhvJeLAiCcAsh92FB2CFca09zAOAzSqkAwAeCIPgggIkgCBYuPL8IYGKrH1RKvRfAe6/x/a+Iffv2Yd++fTh16tRQjZhSSmHPnj2o1WpaPAIbpdB0M9mvawpmsyybf7OcuR8KTQpes1SYx6LgNQW5WYpNx7Z/rjFFMHt5+XMU1Ow9VkrpMU7mbGPzWpgutNnjzPc1Z0ezhLpcLgNYd9u73S5GR0cRj8d7eq3L5bIeSUXn3nTwKcy5bl5HrtFMGo9EIvqcKbzZ58zrwd5nYGNsFz8DBow5jjNUQXTCtue63ItdeDdjrYIgCDsRuQ8Lwg7mWkXz9wRBMK+UGgfwF0qp58wngyAILtw8NnHhZvJBALjYa64Xc3NzAIDHH3/8Rr7NVeF5Xk+5uNmXbJYCm/OOzTRr/owplulo8ms6uvwZikNTjJpCmD8HbLjHnU5HC2czXZvroUjmcbgBYIaRmSFdZu+02bccDoe1oDVdcnOclrnmIAhQq9Vw9uxZVKtVRCIRLZwnJib0OimqGUrGjQI6xRThvDaRSER/BhT9dKT5vgC0Q0433bZtnZ7NdfNnzPPeasyWIFwD1+VenFTZ4Ql7EARB2F7IfVgQdjDXJJqDIJi/8PeyUupjAB4AsKSUmgqCYEEpNQVg+Tqs86q5/fbbMTc3h+PHj2s3cBhQSuHIkSM9KdNmeJc5NomuJ51KCjM+R0whzJ81nV++lt+bJd10dymSmfJMwQ1sJFpHo1E0Gg0tyF3XRalU0s40RSIAfXylFGq1mhbYZno1sOHI0qU1nWXThec6+FnS4a5UKmi324jFYnAcB4lEArFYDGNjYwiCAJVKpeca8DGKcF43CltT+HINdPyDINCl1eFwWPcym/OzWdJNV9oUy6wkMAW2IFwL2+FeLAiCsJOR+7Ag7GyuWjQrpWIArCAIyhe+/j4A/xbAJwD8BID3Xfj749djoVfL2NgYlFL47ne/O8hlbIJjpubn5/VjnU4HkUhEC0IKR7NvmE606fpynBP/mM6vKQYpqmOxGKLRqO75JXwNU63pyrKM2HRG+V6lUkmvjeKR7jD7g9n/6ziO/ppBXHRvOefZTJ42e7a5NgpzinuTdruNhYUFhEIh7N69W4+j2r17N0qlEhYXF5HP5+G6ri535/F53hTudJZZ5m1uXnS7XTSbzZ5yc3M9/HkKfQpnimoeJxqN6vJyQbhatsu9WBAEYaci92FB2Plci9M8AeBjF1zNMIDfDYLgz5VSTwD4A6XU3wFwBsC7rn2ZV8fRo0dx8OBBfPGLX9yy13fQmGOP6C5zZJHp/AIbApoOM4WmKSJNUUtX1RTO4XAYY2NjGB8fRzabRTQaRavVQqfTge/76Ha7WlCy57bRaKBarSKXy+m1UWgnEglkMhlUq1U9d9gc3wRAX3dTBHOtZsm1WYJNUQmsC1qmf/M8eSyz3BtYd3drtRoWFxfRarUwPT2N0dFRWJalnWGea6VS6RG1puhlqbbpFNMNZuI2X2f2SfPzoRvN8/R9X5d589pwc8QcuSUIV8nQ34sFQRB2OHIfFoQdzlWL5iAIXgTwqi0ezwF407Us6nqglILnefB9Hy+99NKgl7OJqakplEolPf+XIsrs4QU2kqRZnt3vxvJ7M+m6v7w7FArBcRyMjIxg//79GB8f18J3eXkZ9XodtVpNp06zJLtWqyGXy6FcLmN1dRXARn8wS6EpuvmYOQs5FouhXC5rEcz35PkxQIxjncyxUHTKlVI6fIvXpn98FN11XoNKpYJarYZqtYp2u42RkREkEgktoMvlsi6r5nvXajU4jgPLslCtVtHpdJBMJmFZFgqFgu5dDoVCKJfLehOi2WzqtdBF5uYDzxFYLyPn58IZzVu55YLwShn2e7EgCMJOR+7DgrDzudYgsKHlyJEjOHz4MP7kT/5Eu7XDglIKc3NzWFtbA7CRem06zISiq9lsIh6P6zJh03EF0OPE8nt+HY/HMT09jcnJSaTTae1+UhQvLi7qsu1Go4FSqYRKpYL5+XnUajUtcmu1mhaz1WpVr90MKXMcR/c5c64zz8MMx7JtG6FQqOc15jmwL9osad5qVBbdXdO95nEKhQLOnz+PcDiMaDSKdruNZDKpxT5nVLOUnP3RFMSu62qBzPcBoNduhrFRzPNYvB5m4Jk5t5rieasZ24IgCIIgCIIgDA87UjR7noeJiQlUKhUUCoVBL2cTnueh2+2iXq9rkdVut+H7fo8YY5qzZVloNpsAoGcac/4xRSRdTNOFpZuZzWaxa9cu7QZ3u13k83nMz88jl8uhUqkgHo+jUChgYWEBi4uL+njVarXH7aWAZCgZ+3rN9GwAutwbwKbAL1M4828eg8fpH7vFn+HXfB2ALdO4KchzuZwWtslkUs9tnpmZQSQSQaFQ0BsCTOsG1sUuy9HNgDWzB5uvZRUAPzMKf342rVYLkUhEl263Wq2ekViCIAiCIAiCIAwvO1I0z87OYnJyEr/3e7836KVsyQMPPIB8Pq9dTYpLYMN1NkO0AOiSXopizvsFoEPBzDJhiu14PK7Lk4F1MVsqlfDiiy9iZWVFi8BWq4Xl5WUUi0U0Go2ekmceyxTo7BEGNsLJ+svKuX6zTJklzaZrzI2AWq3WM1aLc5LpUjOcjNeJopPl4aZ45nWs1+s4f/48KpUKZmdnMTExoV3kdDqt34uCmNeIveIA4LquTrvmxobv+3o0le/7PeXbXAewMZOawt0sOzeTyQVBEARBEARBGE52nGjOZDK47bbbUKlUesToMNFqtVAoFHrSrzlmyXXdnmCo/rnNLFs2RSlFGoUaQ6cY/JVKpdBqtZDL5VAoFJDP57G6uoparabFeLVa1dfM933dgwtsiEoK+VgspoUjRWwoFNKBXRSCZghYp9OB67o9QWbmzGO65I7jwHGcHlFZq9V6StZ5TApvc518ji4xj18oFFCr1dBsNjE3NwelFGzbRiaT0U64WeJdr9d7xkXxczNnYNPpp2NOd5o/x2tobmj4vq9/3vzMBEEQBEEQBEEYTnacaJ6ensbs7Cze//73D3opW3Ls2LEegWYmZJs9zeY8Ydd1tVNplmWbydrmWCa6malUCpOTk0gkElhdXcXZs2dRKBTQaDR06BhLwHlMikSWFHOWcCqVQjQaRSqVQiKR6JnV7Ps+qtWqLnEG1sdPcTay2XfsOI4W3Y7jaLeWfdFmSXa73Uaz2ewZfUXhao6BAjZKpOlim/3HLL/2fR9nzpwBAH0epkDnuVA4m6nd/CwikYhOzeaxWTHAdfi+31NBwONz48Esr49EIj1jxgRBEARBEARBGC52lGjOZrO47bbbcPLkyaHtF02n0yiVSrBtW6c3A+tly+yppaCkM2mWRTPAyuzppdA2x0uFQiEkk0mk02kopVCv13WqdL9jylFRhOKQLnUkEsGePXvgui5isVhPmFWpVEKj0UAikUC5XNbr40gn3/fh+z5c19UhXN1uF/F4HLZt6+NFo9GenmAKZI6HKpfLKJfLOqiMs6Mp+in06bJ3Oh20223Ytq0fD4VCqFQqOHPmDMbGxtDtdpFMJhEOh/Xf+Xy+Z5YyZ0/7vo9ms6kTwCnSAfSUsVMw83GWmNu2Ddd10Wg09PXmHxk7JQiCIAiCIAjDy44SzfF4HJFIBB/72McGvZQtefWrX41CoaCFJcWW6Q7TaTVTnOlYUkSbzrJZPmy6nul0GuPj4z3Cz3yePcp8T37teR7i8TimpqYwPT2NZDKJUCgEz/N65kFzVJPrukilUsjn84jFYqhWq6jX69o9pZuaSqXguq52lSm86aK7rqv7kgFod5rnTNFfrVZRLBZRqVSwtramf4YClqXW5rXj+XI8F4/TbDaxZ88e3Y9M8cx0bfY5NxoN3SduBnwFQYB6va7dajM8jJ9Jv3vPnmeel5k8LgiCIAiCIAjC8LFjRPPExAQeeughfOMb3xj0Ui6KbdsoFAo95b7sraUgZcK1GbxFoWWmV5ujpuguA9Ap13SZWXpdKpV6RkZRxFEsRiIRJBIJTE1NYWpqCslkEp7n9fQJm/3WFIBMz85kMj2BWNVqFZFIBI1GA57nIRqN9ghhrtl0x3nOfB1LrIF1Jz4ej+tS50qlglwuh5WVFZRKJT0Pmi58vV7vEaoss+aGRKPRwPLyMkKhEEZHR3WiOcW+53n6szBLv+lCm04/08VZFWB+bqarzOtonre5gSAIgiAIgiAIwvCxY0Qzg7ROnTo16KVsyX333Yd6va7Lh033mH+bo50o9syRSxRgdC7ZE2wmSbfbbe0yc1TV8vIycrmc7kE2+2/pyGazWezbtw/j4+N6pjEALdbNVOhGo6FLomOxmC5dBtbd/lwuB2Bd6MZiMd2fTIFNoU73l8eLRqP6ujiO0zNzmViWpQPFXNfF+Pg4qtUq8vk8yuUyCoUCKpVKT1+4ec34/nTgl5aW0Gg0MD4+rtO0mazdbDa1CK9UKnqOs+M42r3nzGb2iJvH5zWuVCq6PJtC3LymIpoFQRAEQRAEYXjZEaI5lUrh9ttvx+OPPz7opWzJ0aNH4bouzp49qx9jarVZvsteXva5mvOAKWL7S7PN9GY6otFoFI7jwPd91Go15HI5LWrNXu9wOKwTpA8ePIiZmRld7kx31BSbdFDNXtxms4lqtYpSqaSFP0uP6RYz9Zuly3TWzTJmM4yLz5sl6gB6rgHXz7LpZDKp5zLn83msrKygWq32CFS6wBTilmXp17BHOpvNAgCi0SiazSbi8bhO8abLbs6M5gaIOZOa58DNAgpr0zlnqbyMnRIEQRAEQRCE4WZHiOZoNIrx8XF89rOfHfRStmTv3r04c+aMdiJZmgtA/03hxbAppjRTiJql2WYpM7DhdkYiEViWpecyM4SL72mKRtu2kUqlEIvFMDExgWQyiVarpY/FNQHQfcgUn3Rpfd/HwsIC6vW67l9OpVJa6FerVdi2DcdxNvUGm3OQzXJzCksKbH5PZ53l7JyZTFEKAJ7n6XTuaDSKlZUVFAoFXWJt9j5zHSwl52ZFt9tFJpPRFQEUtNFoVG8ENBoNnTbO68+Ub35epsvN1/D4vD7saR7W0WiCIAiCIAiCIOwA0UyXsVwuD3opW3LgwAEUi0Xd72r21wIbZdmmmAI2ZviaQV1mnzMdYCZqc8bz2NgYxsbGUKvV0Gg00Gg09NfmsePxODKZDEZHR5HNZnWptOn0mvOf2Z/M8VJra2u69DgSiSCbzep50ywlp3BmyTnPz3XdnrnPXBOTtHktotEoEomELm32PK9H6DK5u9vtaiEbj8d16XYkEkEymcTa2hoqlQra7XaPcOc6KVr5b6hYLGJ0dLTHIbYsC7FYTK+fP9dsNrXzbm5o8LxYxm2WwlOIs3yeLrcgCIIgCIIgCMPHthfNIyMjeNOb3jS0idkzMzPI5/Pa7WTvdbvd7nFRw+GwDq8yS6L5PAUexSZLl9n/zMdZTtxsNhEEAWq1GgqFghZ3dLEzmQwymQxisZh+r2g0qt/PdV0A0COSCoUCyuUyut0u0uk0HnjgAaTTaT0Wql6vI5fLYXV1VZc8N5tNdDod1Ot1LTw5zspM4uYmQjgc1uuamJjQCd7sszZh2NfKygoWFhZw7tw5rK6uolKp6B5ix3FQKpXgOA7W1tZQLBYRBEFPAnmr1dLJ4J1OB4VCoScYjdeLGwoMAzPLqhkCRrFvztDmRoFlWT1VAwCkNFsQBEEQBEEQtgHbXjS7rovJyUksLi4OeimbeOihh1CtVnvcTbOcmO6zmbBspkYzMIuuLcUyBTiwMdKI5dnARtJ1sVhEPp/Xgi4ajepUbIZeNRoNKKWQyWQQj8cBQL+f7/soFAoIhUKYnp7GAw88gIMHD2J2dhbxeBxra2uYn59HLpfD/Pw8KpUKEomE/nnXdfXIKfZwd7tdXWbNkVXAevlyPB7X7jMdbpZ194tmjseam5vDzMwMbr/9diwsLODs2bNYXFzUqdrtdhvj4+M9JdPctKCYZ2k2abfbWF5eRq1WQyKRQDKZ1Ot0XRfVahXxeBy+7/eUrvOzoSA3E9H7y/DJsM4TFwRBEARBEARhnW0tmunaLSwsDHopm5ienkY0GsX8/DyA3nm9DKfyfV8Lqk6no78OhUJoNps6cIpOM/uTTYeWwVrRaBSu6yIWi8G2bbRaLVSrVVQqFS1Us9ksPM/TadDpdFq7z5ZloVQqodvt6vFU8XgcBw8exFvf+lYcOXKkx/0G1nuIx8fH0e12cerUKXz2s59FPp/Xa49EIjoR29wcoNMcCoWwurqqe69t24bneZicnITv+4jH43psljniqR/LsrRrnk6nMTY2hvn5eSwtLSEej6NQKPQkfPO68Xqabi8/I163VqulU8ATiQSazaYW+3TR+8uruUHQ6XTgeV7POvk3k9Qvdk6CIAiCIAiCIAwH21o0p9NpvOENbxi62cxKKdxxxx1arJn9ygyUMoUvS34Z/mWW79J15bgmAJv6jem2j42NaZG6urqKXC6nS4Wj0SgikYgOxmJfLgPDzp49q0uU4/E4Xv/61+Nv/I2/gbGxsYueJ9fw5S9/GZ/61KfwrW99C5VKRfcaRyIR3TtMwRqLxZDNZhEEAXK5HEqlki5LB9Z71BOJBBKJBNLpNKampnD77bfj8OHDOHr0KGZmZuC6rt5MMEUny85rtRpqtVrPaCmWhzMxnOXxnU4HsVgMlUoFvu/rcVvcuGCZezqdRjKZ1A43Pzt+lgx3M0d1cROEz/M9zVnZ4jQLgiAIgiAIwnCzrUWz53k4cOAAPvKRjwx6KT286lWvQrVa1SFY9Xpdl+m2223dA8tZzPzDwCy6kGZ/LEcYtVqtnnFNoVAIiUQCU1NTSKVSsCwLa2trWFxcRKFQ0AFWALC2tqbLh1utFpaWlrQbzPFJd911F97znvfg4Ycf1mXkJmbI10c/+lF85CMfwVe+8hXt2pqEQiFd9s1eZnP2cjKZRBAESCaTWtTyNc1mE4uLi1heXsZ3vvMdvTkwMTGBQ4cO4dixYzh27Bjm5ub0TGcGlmWzWaysrKBcLusQtlAoBNd1EQSBLkm3bRuJRALFYrFnrJb5BwByuZwut04mk7oXmn3PLGU35zqz/JwbHrVarUfg87Pc6roJgiAIgiAIgjA8bFvR3O++DgtKKYyNjeHs2bPodrs9rjDFb71eh+M4qNfrCIVCusSa/a4UaOZMZpY227at+5wdx0E6ncaePXuQzWbR7XZRLBZx7tw5HT5GMdpqtXocbQDaWe52u5ibm8Ob3vQmPPLIIzh06JAuT+ZxW60W2u02vvnNb+K3f/u38du//dvaPe4/f6UUstksJiYmEI1GtaDlRgD7lBmEZY7S4nkCG+O4eB3a7TbOnTuHl156CZ/85CfheR4ymQx2796NvXv3Ynp6GolEAq7r6oRtOs62bSOdTutU7bW1NbRaLbRaLZ3azbJqrslMLy8UCqhUKvrfXSQS0aOvmJCtlNJfs3+a/emO4+gSdW6YyKgpQRAEQRAEQRh+tq1ozmazePe7343HH3980EvpYW5uDq1WS49CMucPs0SbAorhVkzVZp8tn6tWqwA2BJjZ38zgsJGREYyNjUEphXK5jOXlZeRyOV1WbFmWHsfkOE6POOdopjvvvBOPPPII7rzzTqyuruIzn/kMRkZGMDs7i2KxiC9+8Yv46Ec/io997GNaFJrwHF3XRSKRQCaT0X3JlmVtGvdEwcjyaJaqmy4tsFHiDGwkjPO8AaBWqyGXy+E73/kOLMtCPB7HyMgIdu3apcdocaOC6+MfXl+WhrPsulKp6GvH92XAV6fTwfLyMjzPw8TERM8IKfaRAxs90hw9ZfZM81i8BlKiLQiCIAiCIAjDzbYVzdFoFAcOHMC/+3f/btBL6eHo0aM4d+5cz9goClW6pQB0KbbpNrL3liOkWLYNbAhTutfs352cnITnedpVLZVKKJVKPUKMDjUd5iAIEIvFMDIygj179uDw4cMoFAr44z/+Y5TLZcRiMbRaLRw/fhxf/vKXtWjvJxQKIZVKYXR0VPcLO44Dx3G0O87Zziwr53p4HbiRYDryTNnm9eP14DVgibvneYjFYgiHw/p65XI5LCwsoNvtwnVdjIyMYP/+/Uin07pnnCOlLMvSs5/5GbHnnGs1PzcKZ1MUc+Y0S+mj0aju567Vavo4nU5Hl2Pza24SCIIgCIIgCIIwvGxL0TyspdmhUAie52mhxeRlfs/UZYaDAdDPs4yYpcF8vL8Plr2wyWQSs7OzSCaTqFarKJfLekYx3VsKbPbzep6HWq0Gz/MwOjqKkZERRCIRPP7445ifn8fLL7+MfD5/SSFn9g7v379f90RzDjRLyz3Pg+/78DxPP26Wc4fDYdRqtU3zm1m+bJZGM32bApvXjte12+0iFotpsW5ZFmq1GlZXV3H27FmcOnUK4+PjOHToEPbu3as3Dzj6isninGlt2zYqlYr+TBqNht4UANZHhXGU1/T0NFKplK4k4Hmwd5obAMBGOXyz2US1Wt2Uui0IgiAIgiAIwvCxLUVzPB7HW9/6Vnz1q18d9FJ6OHjwIM6fP6/FIGECdr8QNmcDUxBTQJql3WYJs2VZSCQSGBsb07OW2XNM4WwmdvN94/E44vG4FrFBEOBb3/oWTp8+fUVhVHRVR0dHkUwmEY/HdUl0s9nUQV0U6eFwWAeTtVot1Go17aTTsbVtW58bA9DM/mVzvjHFMoVnv4ima9vtdvWmQDabhe/7WFlZwenTp/Hcc89henoat912G/bu3Yt0Oq3nNXueh1QqhWg0iqWlJe0Ec3RWo9HQs5rNz7dUKiEcDiOZTCIUCiGZTML3fT1bulAo6IAw3/dRqVR06b4gCIIgCIIgCMPPthTNsVgM9913H/7ZP/tng15KD7t27cLS0hKazaZOdGagF4UtU7Dp1nJGMd1diimGRjH4C4Au1x4ZGcH09DTC4bDuVy4UClhdXdXuNkO/PM9DMplEKpVCJBJBLpfDuXPncP78+SsSy+FwGOFwGNPT0/A8T4dpOY4D3/eRSCQQjUa1u2z28XLkVKPRQKPR6ClLZhgaBSYddP4835fXodPpaFe6f0OBDi9d+mazCc/zEI1G9fzmVCqFF154AUtLSzh//jy++c1v4t5778Xu3bt1/3Oj0cDk5KTeaCiVSsjlcgDW3eVwOIx6va5HUjUaDayururPLJFI6LnMTNVuNpuoVCraWa7Vatf3H50gCMJFUOHe/8UH3QDobt1uIwiCINw4zPux3Iu3J9tONDOwKRqNakEzDLBHlj285kgoOsgsM6bDCkC7mWYJt+kQs3+WwjkWi2F8fBzxeFyHSJXLZT1OypxfzGCudDqNcDiMs2fP4uTJk1ck3NifvGfPHmQyGYyMjGiHnLOgI5EIYrGYnjUdj8d10Bnfn2KWoWbdblc7rpynXC6Xe9KzzeAsngtLvBnMBWy4z3TwgY2wLwA6pXtlZQW+72NychLlchn5fB6lUgmf//znEY1G8fDDD+PgwYN6/vLExASSySRWV1ehlEI+n9eCn2vgyKl2u418Po96vY6pqSm4rquD3RKJBIIgQK1WQz6fF8EsCMJNgb+cWfvmep/odtE9fVZ/Gxj3WkEQBOHq6d+k7H3SgrV3ZuP7vnsxIPfj7cC2E82u6+L1r389XnjhhUEvpYdjx46hXC7rsuh2uw3XdbXrC2wIX4pmOr10ogFoZ9q2bV1mTcEdjUaRSCSQzWa1S12pVLCwsIDV1VXtutq2jVgsph3mdruNZ599FvPz8z2CdCsYinXw4EFMTExgZGQE0WhUn0sQBEgkErrMPJVKafFLsRqPx/X5s7y6Wq3qr8vlMhqNBur1OsrlMkqlEiqVCmzbRrlcBrAREMavKZwprlmKzT8crwVAl3o3Gg0sLS1hbW1NX1/LspDJZFAqldDpdFCv1/GXf/mXePrpp3H//fdj3759GBkZQSqVwsrKChKJBE6dOgWlFOr1OprNpnadzVLzer2ur9/o6Kh23QEgk8mgUChIWbYgCNcNZUcu+lzPL2c9T1haSKt2B52z59cfD7ryC5sgCMIVoMJhQFmbHr/ofXcrjHsxAKhWG51zC3IvHnK2nWj2PA9vfOMb8au/+quDXkoPU1NTeOmll3RqM9OiOe6IPbcszwagy7LplppBU61WSzvUwLoQjMfjmJqa0v3ErVYLy8vLWF1d1S4ze4mTySRisRgKhQKeeeYZrK2tXfYcIpEI9uzZg/3792NmZqYnQMzzPL12plDTUY7H48jn89r95nlEo1FEIhF0Oh3E43HYtq0d6Xa7jXK5rOcqR6NRLZjNGcem2GXvM9fB60jHmdcxEomg2+1ibW0N+Xy+R6h2u13kcjmMjY2h0+noHvCFhQV84hOfwAMPPIC3v/3tSCaT2L17N8bHxxGNRvHcc89hYWFBJ5fzWHTCW60WcrkcbNuGUgojIyP6+mWzWTSbTV1GL4nZgiC8UvpFsrVnN2AERb5SgnBI/5KnWm10zs7LL2uCIAhbYN5/r/XeuxWBHYa1dwbKb6Fz7rzci4eUbSeaHcfB5OQkvvGNbwx6KRo6wb7va4eV4VEUskxKDoVCOhgKwCZRCGyUJLNcORwOIxaLIZvNIpvNwrZt1Ot1LC0t6dAqy7IQiUSQTqcxNjYGz/OwtLR0xYLZdV0cOnQIR48exczMjC43Z4gYe3QZwMU/5txpc/4050JzFjSFLUcy8XUM/aKb3Gg0dGgYHWTTRabzzjJtOvZcbxAE2qEvFAoXHZe1srKCXbt2IZ1O4/z583o28+OPPw7f9/GjP/qjmJ6exh133IFkMolwOKyPywoBzpk2Nz8KhYIuuWdZvOu6GB8f15shlUrl2v7BCYJwy8Bf1qy5XYC12d24HgR2GKGZXSKcBUEQDG7G/dckiNgI7Z4W4TykbCvRHAqFMD09jfn5+UEvpYexsTEthMzwKjqltm3rgCtTZIVCIV3OTdFlCmY6zZyHvHv3bj1HeHV1FUtLS7qsmb28o6OjSKfTWFtbw/PPP39ZwcxU7Lvuugu33367DvxyHEc7xYlEQvdbszya62OfM11mOt7se2Y4F68DxTKDvXjNcrlcT4AaH6dDz/emYKazbJZud7tdneTdarUuWwo9Pz+Pqakp7N69G8vLy7q3+vjx4/pznZubw0MPPaRHUlWrVS2wAej+dWC9mqBWq+n39n0fo6OjCIfD8DwPu3bt0qFolyuTFwTh1kbZEcBSsGamb84va3Z4/Ze1hSWgGyBoXT4oUhAEYaei7AhCM9MIwqGb+r5aOM8vyn14yNhWotnzPDz66KP49Kc/Peil9HDw4EEUi0Ut0uj8UjQ3m03Yto1IJLKp7BpAj8PMsmNTPCcSCczMzOigrXK5rAVzKBTSDnQ0GkUoFML58+fx7LPPYnl5+bJrz2QyOHLkCPbv34/JyUmdsp1KpeC6rnZ2zTX1r9t8jZkODkCXkZvPUagzQTuZTKJer+uyb/aEMzjNLMWmECdmObjJlYhmAFhYWMDc3Bymp6exuLiIQqEAADh+/Dg+/vGP4z3veQ/W1tZw7NgxfT5PPvmk7pPme3NNXCePwx5q9nuPj4+j2WwO3caPIAjDg3IchHZNDeSXNWtuN1S9ifbZczf1vQVBEIaFQd2DSRCxEZocl/vwkHHjt6+vI5FIBHfeeedQiWbXdZHJZHQ5McuvWY5M8cieZY4holMKbAguaws3IRaLYdeuXZiYmIBt22g0GlhcXESlUoHneZiensb+/ftx4MABZDIZLC4u4vjx41ckmOPxOA4ePIjDhw9jdnYWY2NjyGQySKfTiEQiuh+bQpaOObAxG7pftNIJNsV/OBzWrjrHRpnXx3EcHTRmjqXqF+o8Lt+H72XOtubaTOF+OUqlEizLwvT0NBzH0Y9/4hOfwBe/+EXU63WcPn0ajzzyCN72trfhyJEjSKVSun+a18c893a7jUqlglwuh2q1qs91ZGQEY2NjiEajV7Q2QRBuHZTjwHLdgf6yJgiCcCujHAeh6cnB3oO7XQRXMBZWuLlsG6eZjqtt21fUo3uzuO2229BoNHTQUyQSQSQSgeM4WgwzOIrhYCwvZik3hSDLtDlr2bIsPWIqGo2i2WxiZWUF+XwejuNgZmYGU1NTyGaz6Ha7OHfuHJ5//nkUi8VLrpmp14cPH8bBgwf1MVKpVI9DzNTvZrOpxaEp8FmyHQ6HdUk0RTXLpc0ybjO4i7OY6SgzgZpjoswNBIpRYpax03lmubd5La+UfD6PTCaDZDKJyclJnDlzRj/3K7/yK3j44YeRTqfx/PPP421vexs6nQ5WV1fx4osvotFo6A0F83PjuRUKBX0unuchHo9jcnIShUIB8/PzutddEIRbF+U4UKEQrIkxBHYYEhUoCIJw89GC2R6sPFLNFtpLlze/hJvLtnGabdvGww8/jFOnTg16KT3s378fxWKxx9kMgkC7r3RYzbJiCis6uQA2uaUM2JqYmMDo6CiUUigUClhcXITjODh06BDuvvtu3H777Xqm8HPPPXdFs6szmQzuvfdevOpVr8L09DTS6TSy2ayeU0zBS7Yqc6Y45WZBv+Nr9myzPJvnZQribreLWq2GSqWCSqWiXVkeg+/N4DEzRZvHYUAZx2Xxel6p0wxAz1CORqM9nwsA/M7v/A5mZ2d1Wfyjjz6Khx56COl0umeOdCQS0T3WXH+r1cLKygqWlpZ0/3M8Hsf09LT+XAVBuDVRjgPL8xCamoDaPTXwX9QArDscjeagVyEIgnBTsVx3KASzMLxsG9EciUTwute9Dp/5zGcGvZRNsNzadJDNgC+W6wLoeZxhYRSp7PuNRCIIh8PIZrOYnJzU6durq6sIh8M4fPgw7r//fuzduxfJZBJra2t47LHHLruhwP7au+++G8eOHcP09DRGRkYQj8f1e1AEcrSSmZJtJldzk4BBZaZzzETsdrutk6b7A7xMh7lcLmN5eRm5XE7POzbpH9FE8QxslL3T7eaxKWKvVJSaPemjo6M9Zdof+9jHcPr0aRw+fBhPPfUUVldX8df/+l/HsWPH9LXjeDGzbJw0Gg0UCgXkcjntTKdSKb1RIQjCrYdyHIQmx9fFcsS+/A/cDLpdoFhBZ2Vl0CsRBEG4aViuC2tyfDgEs2xcDi3bRjSHQiGMjY3hs5/97KCXovE8D5VKRZcxUzhTNPe7qvyezqgpBilAAfTM92X4Vy6XQ6vVwpEjR/DQQw9hdnYW8Xgc9Xod3/zmN3HixIlLrpXX7/7778fdd9+N3bt366Rtiku6uOwn5s9xfWbJs23bWqgCG3Ooed5mXzHHQfE4vu/rFPG1tTWcO3cO58+fR7FY3CTOKdDNPmfT0aaAZq84PwtuEGwVErYVXDcAZLNZTE1NIZ1OI5lMIhqN4kMf+hA6nQ5mZ2fxrW99C5OTk/hrf+2v4cCBA4hGo/r68Fqa/ezAemn+2tqantMci8WQyWSQSCSuaH2CIOwcKJgDJ3L5F98sRDALgnALogXzMGxeyn14qBmCLZXLo5TC2NgY6vU6Go3GoJej2bNnj57dayZMM/2ZjiudSDqypvgzhRbFtOd5yGazGBkZgeM4yOfzKBQK2L9/P97ylrcgk8nAsiw0Gg2cOHECx48fv2RvrG3bmJqawt13343Dhw9jfHwc6XQajuPodZkBW2Z4F2chUzC7rqtdZB7b3CzguhjwxWvAr7nOer2OXC6HU6dO4aWXXsLq6qp+ztxoMB169iyb5dh0h83SePZWx+NxxONxLVQvBYV3KBTS47YmJyfR7Xb1+K5PfOIT+Imf+Am0Wi289NJLOHr0KN70pjeh2+3ixIkTPZsNXI9ZgdBoNFAsFuF5nv4Tj8fhOI4W+4Ig7Gws113vXRbBLAiCMFBEMAuvhG0hmm3bxvd+7/fimWeeGfRSepiamkKlUkGn09HiGIAWdpFIBO12W5dvA4DjOLpE2UyFphC0LAue5yGTySAWi6HZbCKfzyOVSuFtb3sbDh8+jFwuB8uy8MILL+Bzn/scVi7xHxgDw+677z4cOXJEj6aKxWIIh8N65rCZUm2OejJ7iFk+TteX52uWXYfDYVSrVR165jiOPk44HEatVkO9Xsfi4iJefPFFnDlzBqurqwA2xDJLt3ns/tnVfB/T0eXGBK99u91GJBLB2NgYqtWqLhO/GLFYrCeN23VdxGIxffxGo4Hf/d3fxe23347XvOY1eOKJJ1AoFPDAAw/g5ZdfxurqKtbW1lCv1/V1YkAZ19lqtVAsFmFZFiYmJhCNRjE6OopSqXRFaeeCIGxvBiGYVbmKIBHb+skggKrUELQ78ouaIAi3FPp+PGjBLPfhbcO2Ec0PPvggPvCBDwx6KZpIJALXdbGysqLFGgWWbds9vbx0on3f10FRZjk2RSqwLnLpBFuWhVqtBtu28eY3vxmvfe1rUS6XkUqlMD8/j0984hN46qmnLrpGpRR2796NBx98UDvMtm33jI4yx0MxvItrNh3d/qAys5+52+3qc2N5dz6fR7FYRDQa1TOZ2Ze9uLiIs2fP6tFZ7AdmABeDw+hum/3SLKFm7zAALUxNEU3n2LZtRKPRnjCyfhzH0Wnnnufpc2RYm+d5aLVaOHv2LP7zf/7P+Df/5t9g//79OHXqFDqdDvbs2YNz587pHm463eY15efearVQKpXgui5GRkaQSCTged6m8DVBEHYeVjp1cx3mIEB7YRHhYAJQqlc8BwFQKKMtv6QJgnCLYXkerLGRm3Y/VqXKhS+M+3AQrG9qdrpyH94mbAvRbFkW9uzZgyeffHLQS9FMT0/D9/0ecUznlb3LZl+u6eIS09ENggCu6yKVSmFkZATpdBq+76PZbOL+++/HD/7gD2rhGIvFcPz4cXzpS1+65BrT6TSOHj2KI0eOYHJyUru+LBG3bXvTTGW6vGbPNYO2WHZt23ZP77GZ+N1ut1Gr1bC0tIRqtYpQKKSFaC6XQy6Xw9raGiqVinZfed1Y3sw1UgRTdPL9KKL7RWb/WngemUwGrVZLJ2Sb8JpT+HuepzcLOEqKjn+pVMLJkyfx/ve/Hz/zMz+DZDKJ06dP61FVKysrKBaLuszcFP2sJmBPd6VSgeu6sG0bqVQK0WgU1Wr1FfwLFARhu2C5LpTrAPZgHI324hIAINQd33iwG4irIQjCLcfNFswIAn0PBoz7cDcQsbzNGHrRzH5mM6hpGDhw4IAuU6aryT7gZrOpe3mBdVeaQorCj19TDDIwa2RkBKlUCrZto1wuY2RkBO94xzuQzWaxtLSEWCyGs2fP4pOf/CRKpdJF15dKpfCqV70Kd911F8bHx3U5sxn4RQHKXt7+8LJ2u90z6smcK82fYQ83BW6z2dSjsVgWzRL1QqHQ03ttClwKYZY2m/OieX0ty9KhW4SPm33i7J2msGYSOT8HfmahUAjJZBKRSESXqbPsnOvgn2q1qnuPv/SlLyEWi+Gd73yndt/T6TTS6TRWV1d1fzI3G4B199zsfa9U1ncdx8bGkMlkkE6n0Wg0hurfuCAI1weVSgKpxMDnL3dk7qcgCLcwN10wA0Ch3POt3Ie3L0MvmkOhEF7zmtfg5MmTg15KD4lEAktLSz39twB0nzDLsTudji7vNftyiSk8w+EwUqkUXNdFtVpFNBrFD/3QD+HQoUMolUq6R/hLX/oSHnvssYuuLRaL4ejRo7j77rsxMzODWCymXWCWQJsC2nRBzUAtYGN+NAU0S4wp+M3XBEGAcrmMlZUV5PN5NBoNPXuZgWl8D1OY0uWl4I1ENm5m7JXuLws3E7YpNM352HwtNy5c19UhZmYpOTcIeEyKbJ6T+bnato16vY5isYjPf/7zGB0dxZEjR9BoNBAEgS61rtfrWjib4t+sOuh2u6jX6yiXy4jFYhgZGUG5XL7kRoggCNsPy3WhIpHBCOa+X9YEQRBuVQYimAGp6NlBXHbklFLqfyillpVSTxmPZZVSf6GUev7C35kLjyul1P+jlDqllPqOUuqea11gOBzGgw8+iE9/+tPXeqjrSqfTQa1W2zSTt9/hNF3Z/hm+/YI5mUwilUppgXvnnXfiTW96E1qtlu5lPnfuHP7oj/7oosFWruvi4MGDuOeee7Bv3z4kEgntqpoOtxlGZvY3cy1M9TbFZH/JNp3pVquFVqulHealpSUUi0VUq1XUajU0Gg34vq9Fqnn+tm1r8U1hHA6H9Yxo8/0B6ORu8zFgo//Y7Bnnscw52OFwGK7rame92Wzq3mdzzrPZv83PlNcjFAqhVqvhiSeewKlTp1CtVvWGRjQa1RsTW4Wp9Y/IKpfLaDabSCaTSCaT1/rPUtjBDPpeLFwdKpFAEHUu/8IbgPyyJgjXF7kPb08sz4M1kr35UwvWCjf3/YQbypXMaf4wgO/ve+znAHw2CIKDAD574XsAeCuAgxf+vBfAf7/WBSqlMDMzgy9/+cvXeqjrRjabRblc7unxNQUp/zbLr83HTAFHUe04DiYmJhCLxVCpVBAKhfD93//9CIIAjUZDi83Pf/7z+MY3vnHJtd1xxx3Yv38/stmsFo2meCRmYrcZogVAi0OujyXQzWZTu+bVahWlUglra2tYXV3F/Pw8zp49i/n5eT0ejA6y2SNs27bu5wWgS9PpaDcaDTQaDTSbzR4nn8KzX/Q7jgPP83ocagpoOsSu6/aIYjrQ5jWgS21uBvB7bni4rqv7nk+fPo0nn3wSi4uL2llmKrm5OcD35dd8f17PRqMB27aRTqfhuu5V/7sUdjwfxgDvxcIrx/K89V7mm8VaofePIAjXmw9D7sPbCi2Yb+bm5YV7cGc1d/PeU7jhXLY8OwiCLyml9vQ9/CiAN1z4+rcAfAHA/3Hh8Y8E63bk15RSaaXUVBAEC1e7QMdxoJTC2tra1R7iujM7O6t7YzlaioLNTJ42S3HpPFMws5eYgiwWiyGbzQJYdzbvuusuHDx4UAurdDqNkydP4kMf+tBF+14jkQimp6exb98+ZDIZLcrNknCzF9l0QrlGc940gJ4UawrJcrmMIAiQz+d1QBbTspeWllAqlXrSwrl5YPb48hrxOnGUFUu/bdvW66BgpxNuQueYn4VZjm1uTphhXMBG6Jj5GfEz5HEajQZCoZAuLTfLv2u1GgqFAs6ePduzBtu24ThOT1AZ+935GZjvy+C0cDiMWCyGZDI5VLPIheFh0Pdi4ZVxU39Ry+UBAJ3c8Px/UhB2InIf3n4oL3pT78OA3It3Klfb0zxh/Ee/CGDiwte7AJw1XnfuwmObbhBKqfdifeftoliWhbvuugtnzpy5ymXeGMbGxlAul7ccgcR0ZDqiDJ8ye2TNMmc6pel0GvF4HLVaDYlEAm9/+9vR7XZRrVYRi8XQarXwuc997pIjpkZHR3Ho0CGdlM33MvuITXFMIcnH6QqbDrPZ+8yS9DNnzqBUKul+5UKhgEKhgEajoUc70QU2nWo6sEBvujXFKkWtGTrG43BN5vWjCO93j/kZ8LU8z/6AsWazCdd1ewLAzDJtzpums8735OfZbrdRKBSwurqKdDrdU7oeiUT0pgCAnvWbdLtdXbHAjZNKpbJl0rcgbMF1vRe78G7cSm8xVNS9ab+oyS9ogjBQ5D48pFieBxXzbmymhGxa3jJccxBYEASBUuoV/3sMguCDAD4IABf7+VAohDe/+c346le/eo2rvL44joNcbr3kwhShhGKaItAMrQLQU7LN42UyGViWhXq9jocffhj79++H7/soFAqYm5vDU089hY985CMXXZPnedi3b19PHzPXZDq2fG9zzBRFJbAx85gClE4/xV8+n8ezzz6LQqEA3/dRq9VQq9W0sKTzzs2AcrmMVqsF27a1aGYpNVOkzeRruvYUueZzLNU2nfMgCHRCt5mezbJrAGg2m/B9X4/cAtZFMzcWeK4U+BTizWYTzWZTXzu61+FwWLvujUYD+XweyWSyR+QzrZzXk59//4YCH282m3AcB6lUCul0GvV6/aJzpQVhK67HvTipsvKP7jpgeR5UPHZTwr/kFzVBGB7kPjxcqKh7w/uY5R5863AlPc1bsaSUmgKAC38zP30ewIzxut0XHru6xVkW7r33XnzhC1+42kNcdxzH0a6mWXILQI8MMgWTOcLJdDn5PJ3WZDKJarUKy7Lw1re+Vacrp1IpFItF/Pmf//klXeaRkREcPHgQY2NjcBxHB05RCFK8cx1mz3W/mKfIZ5kyvw6FQlhbW8PCwgKWl5extraGarWKZrO5SQg6jqPPk84r+3ZnZ2dx4MAB7N27FyMjI7rfGFgX//zZSCSinzMDy1jqzXWZpdP9veUA4Pt+T480e61NzGRwlt4zJMwc12XOiA6CQCdgt9ttRCIRdLtd3ZfMY3KdvPZmObxZBs+xV9lsFvF4/Gr+eQq3HjflXixcOYNKaBUEYWDIfXgI4eblDWVFBPOtxNU6zZ8A8BMA3nfh748bj/+UUur3AbwaQPFaejeUUhgZGcHi4uLVHuK6k06ntcNo9riy19acAWy6msCG4wtsCFfbtpHJZBAKhVAul3HkyBHMzc2h3W6jXq9jcnISzzzzDD7wgQ9ctJfZcRzs2rULc3NzSCaTPUFUW/UqmyXTFMZmMBm/7na7utyaopFl2OzfDYVCug/Z7DmmIB8bG0MikdAOajqdxvj4OCzL0qXnpVIJtVoNlUqlp+fYHB/FnmYzjZyPA9BONjc0TBedmxNm+Bc/D7Ovm+fYarX0poPjOHAcB51OR4thrk0phUajgXq9jkKhAM/z9Dr4Gdu23bNhYSaCm4KZznYoFNKOc71e12sWhItwU+7FwpWjHOfmCebl1St+qeW6eOEX777i18dPA2O/fvHRhoIgaOQ+PITcFJc5n7/8iy5BaCSLU//idgCAk1OY/s/DVVkr9HJZ0ayU+j2sBxyMKqXOAfjXWL8x/IFS6u8AOAPgXRde/ikAPwDgFIAagJ+8lsV5njd0vZ1mXzAFMgA9c7jZbOq+11AopMWzOROYr2cf6/j4OOr1OpRS+JEf+RF0u11UKhUkk0nU63U8+eSTOHXq1EXXlE6nsXfvXmQyGd0fzL5blgebwWMU86YoZpI1gJ7SbJY2s0+4Wq3qc+HPmP27kUgEjuMgm81i165dyGazyGQyGB0d1X3b0WgUpVIJvu/j9ttvR7fb1fOdi8WiDhdjUjfXwYAsik2eA4O+TExxapZscyPBTC6naKXDbQa38dpFo1FEo1E0m02dxu37vp7Lncvl4HkestksfN9HJBLR6+VnbY624mfAdYdCIR0KFovFkE6nUS6XUSwWX+k/UWGHMsh7sXBlWJ4HlYzftJnMncLl7w8qHMap992HIAy8/bVPXPGxny5M4YUDDyJ9QmHkN0Q8CwIg9+Htws1skXklWK6L53/pmP6+6wZ4x2vW78vnamkcn3hw088kTynZwBwSriQ9+8cu8tSbtnhtAOAfX+uigHV39v7778eLL754PQ533Uin0wB6R0qZjiiATQLZ7Ak2Hw+Hw0gkEnBdF8ViEQcOHMCBAwfQbrdRqVSwe/dunD17Fn/8x3980fWEw2GMjY1h165diEajWpCZLib/Nl1gCkmKQLN020zXpshk/zFDq1zX1QLTdV1dWjwyMoK9e/di//79mJmZwcTEBNLpNMbGxrSo73Q6OmSLpdvVahW5XA6FQgHFYhGrq6v6+1KphHK5jGq1inq9rnuY2+22FvQc+cRzpUDuh+cdCoX0udBd5nlRUJt90rFYTG8y8Bqa86RbrRby+TxGR0fhui6i0aieUc33MP+Ymy3mSLJ2u41Wq4VIJIJMJqPLygVhUPdi4cpREfvmucyLl5nBrBSe/79fDVgB3vHQxccUXowj6QUc+d4FfPtVu/D80Y1f5Ca+DiR/92uv+HiCsBOQ+/D24KZW/FwByo7g5C/fA4QCvOM1W9+Pd3sF7P7ezc89ffcUnr9j/R48clwh+yER0IPimoPAbhSWZeHhhx8eqvnMALB3714UCgUtLs1xTP3Osznyia8xhZPnechkMmi1Wmg2m7jvvvsArAvXeDyOVquFZ599Fp///Ocvuh7P87Br1y6MjIwgGo1qMc8wLZZlc21blYqbJc98nRkGxrLnZrOpA6oY0kVH3bZtTExM4PDhw7jrrrtw9OhRTE9PI5vNavfZTKY2w8jogjcaDT2+KpfLYWlpCaurq/r7UqmkZzhXKhU9CzoIArRarZ7EbF5Hvh+wIZi5djM5nBUBXIuZ4k3Xut1uIxQKafcdgJ7R3el0UCgUUKlUEI/HkUqlUC6Xexxtsze8v5TcLOVnijlHUK2trV20NF8QhOHA8jyodOrmucyl0sWfVAon338/Hn31KxfL/bwqO49Xfc9GG+bXb5vD8w+/GlNfAOJ/+PVrPr4gCMKOxQrh+f96HwIrwKMPXN39+Eh6AUe+Z72q/5uHZvD8fa/Wz8l9+OYytKJZKYW9e/fi/e9//6CX0oNlWbqnl0KLgsocL0RRaDq2pnANhUI6AKxUKiEcDuOhhx7S4VKJRAL5fB6f/OQnL1mi63keJiYmEI/HdTmxKRqBDefbTPCmeDZTnSka6X7SSeb5+L6Per2uBSBLmsPhMJLJJO699148+OCDOHbsGHbv3q1d237MzQZihoUB0A5yNptFqVTSrnO9XketVkOpVEK1WkW1WkWtVtNp1tVqVa/VFPUM9DKDzyiK2aPMRG9+XtFoVL+ewpkbH/w5ALqnu91uY2FhAYcPH0YqlcLa2hra7Taq1aoW8GZCt7mJYlYCMKyMvc21Wg3VavXq/sEKgnBTUOEwgoh9+RfeBE7++n149L5v3pBjv3r8DDB+Bl/dsxcnH7kfADD7pwrunz1+Q95PEAThlWDFYlCpxOBLs5XCyf9+Dx6978nrdsh7Rs/intGNKWZyH765DK1oBoBkMomlpaVBL0MzOzur3U1ztBCdTDMwiyXZpugyBXY8Htdjpmq1Gvbv34/x8XE9+9h1XZw8eRKf/exnL7oeutWpVAqu6+r3YD+zOeoI2CyUKfboRvcLZ4o6M6iKzi43AKLRKEZHR3HHHXfgnnvuwb333os9e/b09EhfKRxTFY/H0el0MDExAdd1UalUUCgUsLa2hrW1NX2NGo0G1tbWkM/n9dqYZM1Zx5VKRYtjM2TMLJEGoEPN2ONsCmzzZ7nZYdu2TkvnayzL0uXlqVQKiURCJ3ebgV5m6TcAvQnRP0qLidwMn+tP/BYEQejn5G/eh0ePfeuGv89Dky8Bk+tf/9X0Pqju/XA+deU904IgCDcCZQ/HBubJ/9+9N/xe3H8ffvmH7sPejwL2Z669ykjYzNCK5mQyOXS9nFNTU7qklqLLLLOlOGUqdn9fLUV1OByG4zhIp9O63PiOO+7Qacx0s7/xjW9cMgDMdV2Mj48jk8noECtz5nC329XhXxT3/SXipljj9/1J28C6WDQ3DLrdLlKpFLLZLA4cOIBjx47hrrvuwtTU1FUJZhIKhZBMJhGLxbQjn8lkMDU1Bd/3USqVUKlUUCqVsLa2hsXFRaytrWn3uVQqoV6v655iBnL5vt/jkAPQoWm8XmbqtplyTkEcDof152q+jkKa12p5eRnRaBSxWAz5fB7xeLyn75rv3+82mwFkLBF3XReJREIHpwmCIFyM53/rHjx69PhNf9/XTr+Ir/yjfXj5r9+jHzvwgQ7UV79909ciCIIwDDx67PhNfb/XTr8ITAN/Nb4P+Xffg/3/I4D1xRu/gXorMZSiWSmFBx98EOfOnRv0UnrwPE8HUVGUsg+YLqWZls2yZrOPmI6l67pIJpNYXV2F67rYu3cvKpUKWq0WxsfHUSgU8N3vfveSGweO4yCTyWhRZvbumvOjTeFujqMyRyFR6LI3m2KQ62+1WqhUKjosK51OY3R0FDMzM9i3bx/27duH3bt3Ixa79pl4LN+mw8zUbdd1EYvFUK/XUa1WkUgkdE9xLpfTArNarer06Wg0qudJm4KZ14GbDHR1WSJtOvVmAjmf43XlOCp+7s1mUwvlbDaLaDSKdruNWCymry1/tn9+N7/uL9WORCIYGRnRY8gEQRC24h1HvzOw93546kVgauP7L/3cfpTKd+PAf/ERPPn0wNYlCIJwIwjOXXx62Au/czfuwHdv4mo20OL5n+9D8b13Y/+vtoHHB7OWnYZ1+ZfcfJRSeN3rXodvfWu4dkgikYjutQWgS6HNUC2GOFGgsaeXJdN8bTKZ1KFPIyMj2LNnj+7HBYCXXnoJ8/PzWy/EgAKTIo+hXCwtpjAz07G36nHmc8ScKwxAJ163220kk0kkk0kkEglMT09jdnYWe/fu1RsH1wvHcQAA586dQ6FQ0G4wy6I54ikSicCyLMTjcUxOTmJqagpjY2MYGxvD6OgoMpkMksmk7vumYCZmQBqrBMwZ1jx/06mm4OamRhAEuo+6Xq9jZWUFnU4Hk5OTugoglUrpfw/m3Gg+xmvXX07v+z5isZhORxcEQejnpd+/a9BL6OF1u17A2w59F8v/uo2Xfv8uhI7cPuglCYIgXDe6lxiJ+7ZDgxepr51+EW879F2s/kIT6r6jg17OjmAoRbNlWbj77rvx2GPDFatOZ5biiWKIri7Ld83+VdP9ZdiU67qIx+O63DiZTCKVSgGAfvypp55C/jJD0ymgGELWX1rMcl9zLXyd6Sib6d6mmGNvL4V2tVqF53mIRqPaKR8ZGcHo6CgA6JCw6wUTuaempqCUQrVaRblcRq1W06XilmVhcnISc3Nz2tlNJBK6pzgWiyGRSOjRXmYaNq+BubnB1HFzY8FM+u5P1ObztVpNl4B3Oh2Uy2VdRTA2Nqb/bZijulj6zWvf/9kBvYKe5ycIwq1N8PLmDdXvP/DsAFZyeV636wX8wMFnsPS+AKHbDwx6OYIgCDeUl//wzkEvoYfXTr+I3C/6ePkP74R19NCgl7OtGcrybACYm5vD6dOnB72MTbCElwKagoZltMR83ix17nQ6SKfTiEajeu4xAJTLZSQSCUSjUZw9exbf/va3LyuabduG53na5YxEIlqEmbOhzX5Zcy6xuX46yma6NIUbXWbf95FKpZBMJnXyN/uaGQpmXgPC8vV+l5Sp1Zc7x0QigVarhVarBcdxEAQBXNeFUgr1el0fu3Zh14/nVywWN42S4oZArVbT/ckss+c1Y9k13WTf9xGPx/U1pBudSCT0HGWW6/P43W4XKysrSCQSSKfTKBQKKBQKcBwHrVZLr9XELKnn9+bnxvLzZrMpI6gE4Rame+H/G9uJ106/iK/+l70Y/em96Jx6adDLEQRBuO6c/9hhvGV2+DYwH556EQDwtf+0B9mfPYDOiYvnJQkXZ2hFM3uFhwmzJ5kCx5w5zKAmhm6ZvcNmj2wmk0EoFEKpVNIhYmtra1pAvvzyy1heXtaC+mKEQiHt/JpBX5wNbI69AtBTjs11b9XfbPZd0znl2Kvx8XE4jgOlFDKZDLLZLNLptC6T3qp8+GLC2OwDvxSWZSESiWhB7nkegI2NgWw2i/HxcS3smaTN8u5oNIrFxUX4vq/70W3b1mOc6KYD6yKdnzPL3HlN+Ecp1VNRYG5amNe2Wq1iZWVF955XKhWEw2Gk02kAQLPZ1McwHW1zprM5v1kphVgshmKxKKJZEATN8scP4Q41/L8EPTT5Er7+X+eQ/YezaJ9+edDLEQRhh2ElElDZzMDGTX3vzPMDeucr48GJ03jiV2dR92/H1D+ty334FTKUojmbzWJlZWXQy9gExy6Z5ditVgvARrAThW4kEukRoWZSdSKRQBAEKJfLCIfDcF0XjUYDtVoN+XweS0tLOjn6Uti2jVgsBsdxtPNqiimzp9kU0vye64pEIlqo9bvCFOLlclknWzcaDYyMjGjBPDIyAtd1X/H1NMO1APQI/K1eS8yfsSxLO+6ZTAatVgunT5/G0tISGo2GHokFQPdCR6NRLax5DAr4Vqul18Xy9SAIekZt8fPi8+x15/XiBgY3QyKRCMbHx5FIJFAsFvVaV1dX0Wq19GfD89zqXDl72vM8XcJvlt4LgnBrsvZnt+H1U8MvmMmrx8/gG78+i9Tf24322eEK+xQEYXujlEIQvvTvzteD7pnN9661P7sNlnrxhr/3tXL/2LpQPhmZG/BKth9DJ5qVUrj33nuHsjS7Pz2Zgg1Ydw05eoiChs4jw8Icx9E9tkzhjsfjiEajyOfzCIfDqNfrWFhY0OXcl8J0YE0X2Sw1Zh8ue3IpCM2+XrMsmOdYr9e1Q+z7PgqFAkKhkBZ5o6OjmJiYwOjoqE6NprN7tVxLyBV7f23bRjab1S5+JBJBPp9Hu91GNptFrVZDt9tFo9GAbdv6HHnNzGT0VqulHWVuRpiOPt1hlo7zmnL8VKfTQa1Ww/LyMmKxGMbGxuD7PkKhEKLRKFqtFnK5nP5sWCVgJqEDvf3N7XYbqVSqp7RfEIRbk/wnD+I1E9uv1Pm+sZfxvL170MsQBEG4KoItRoC+ZnJ73Ytrv9ZB7O/I5uUrYSiDwF71qlfhqaeeGvQyNsHeWIoos7zZDMyic0v30XVd7WR6nodIJIJqtarnCIfDYayurmJ1dRW5XE7PC76cCKVDaoo6CmBTJJt9sXzcDAUzA6nMgCqKQTqw3CBgX3MqlYJSCmtra9dtpna320W9Xr9oab7v+ygWiyiXy9o5NmHvsOlAj42NIZ1OI5FIIJvN6lFVjuPoa9xoNHrmMHPjg33XZvgXS+opqM2edY7GUkrp15VKJd3PPDY2BmD930Q2m4Xnefp6m6ne5uxm04Xmv4vL9YILgrAz6b50FgBQ+n/348GJ07DUoAoRBUEQBACo/vm+bXcvPjZyDrDld8lXwtBdLaUU9u/fjw996EODXkoPZumzWU5LQUmnkq6y67paNDGMCgASiYQWY3QcWf5bLpd1ArNZBn4xzPFLPB7dYLP02RR/dJeB3h5n8zzY80uxTYGYSCT0zOB0Oo1UKoVYLKafu1LY77vVz1iWpUXnVti2rcvOWbJMl7Zer6NYLCIIAnieB9/3US6X9bgvCk2Ws4dCITiO09PHHIvFdC93OBzWzzP8iw4zP2+WpfNz4/uYolYphWKxqEd1sffacRzE43EdCua6rnauuXnRP+ObGzeZTAbNZlO3BwiCcGsQtFuo/vk+3D/68rb7Je1Kef4j98AK91Za7f/x44NZjCAIwmU4NnL5EbHC9mfoRDMATExM4PHHHx/0MnrYu3evnstLF5Qiqr+cliFSdHUdx9GOZDgcRqlUQrlcRjQahed5upeZYrXRaEAppQOvLkar1UKlUukZDUVBRwfZHJXEnlw+b4pJc+wS06T5c6ZojsViSKfTupd5fHx8Ux/u5eBM5YtxqWMxhAvYKCWnG25ZFkZGRhCLxRAOh5HL5XD69Gmsrq6iWCzqzQQA+tqHQiH4vq8FcigU0teSn22n09HhZ2YgGJ3qdrutxW2z2dQbDea1L5VKWFlZged5SCaTWFtbg2VZiMViiMViPT/Ha2Q62zxfsxSeGySCINxa3J5e3pGC+fnfugdhp43xZGnQSxEEQRCEHoZSNFOIDBOTk5MA0LMuCpt+Z9cUcQB0QjTLuekQO46DRCKhxyix35ZCiP3O9Xp9yzWxVLleryOVSvUkMJul46aAN3tnTQec/dfm6CSGXHGclOM4yGaz2LNnD+bm5hCLxfQ5mpijrC7GtfQvm9i2Ddu2e+YX89hjY2OoVCqo1+uoVquIRCJacIfDYR0cxk2ORCKhr4m5uUA3m5UDHH3FPmpuPrAPnBUH3GzgZkapVEKlUsHo6Kjud3ZdF5lMBisrK+h0OohEIhctt6cQV0rpgLOlpSURzoJwC9H+ixnEwguDXsZ15eSH7oUT8zEaK+/IzQBBEHYuoc9Pw1LDF14sXH+GUjQPY8CRbdtavFKMmv2/dB/NJGa+ls4tBSiFUDKZ1OnXrVYL1WpVl98mEglMTU1hbGwML7+8dSR8o9FAoVBAo9HQa6FoNsdj0T1lH7PZw7zV6Cz+DEu2KbJTqRTGx8cxPj6OyclJZLPZLdd1OcF8JfOZgfXNh2aziWg0etkk8a1EuFJKC1QAWF1dhW3byGQyOg0cWN8wcBxHl2CbI7hY6s2Sch6Xz7daLR0q5rquLq02Q9jM0v18Po/R0VGMjY3plHTXdVGpVFAqlfTxzV50AJtK5oH1TRUmcAuCcGswG8/vOGHpxHyk41tvDguCIFwpnVJp/Xe7sa1/P70R7Imvbdt7cuwjZVR/dBfa56S8/EoYOtF89913Y3FxcdDL2ITZE9toNOC6LhzHQb1e31TmbJY2k3a7rcuGOa83mUzq2cZ0NsvlMrrdru67NR3UfqrVKubn53vmPXOtZhAYgB4xzbJtno8purh2bgBQBLKsOBaLIRqNIplMXlYcX4wrDbGii3y1NBoNVKtVXQY9NTWlxS7PiX3g0WgUjUZDp4NHIhHUajXdb84+Zdd1EYvFUK1WUalUekQsnfl2u63/HbDvmr3PjUYD9XodY2NjGB8fR6vV0s+Xy+WeRHM61NykoZA2y+mvl2MvCMI24AN1JMLDt6l8tZz8zfsQy9SRdHbOOQmCMGACGcd5pUxHi1j+aBfF/20C7cWlQS9n6Bk60fya17wGCwvDV3pGJ5mjpChC+TVLkilMbdtGKBTSqdLRaFSHfpnjnIANZ9GyLJ3azPLpS/X+AkCtVsPq6irq9Tpc19U/xzWbwpbvYY5Uomjs78HlJkC5XEa9XteOeDQaxfj4+DWPl7oZ0N3lOVDs0z02w7UYwhWNRnWiueM4qFQq8H0fkUhEf5ahUEiHhfG6+8b4AfOz5UYGXWxgXThHIhF4nodisYh2u41MJqNLyVnOT8yRU+Zxw+EwxsbGcP78+aFrZxAE4fqTdWrb1tHYipDbQdy9PpMXBEEQbgadU6cBhvv+1SjCqjjYBV0j424Z4Y956AZpPL0yiU6wbsZM/8gLCFqbR2vdygzVyCmlFF796lfjxIkTg17KJnzf164eQ7SYWg1siGp+T7fRDAmj0wysC6BisYhSqdQT1EWXkz87OTmJiYmJi66rXC5jcXER1WpV98JSADPkimul+0lBZp5POBzeJBg5/okiPpvNYm5u7pLuNxlkybDv+8jlclhbW9Ml5o1GA81mU/c/m+eaTCYRDoe1i86QLdu29egqc7QU+4v5M0wV5zVjMJhZ6s6+cM/z9GfMNG2OxuLXruvq3nff9/VGCj9DAPo5rl0QhMHSKZWAlbUbdnzrgzVkI9Ubdvybzcp/tZFNV67otZl3yhxRQRCGBMPJzkTqO2IjMxupYdSpwHN8eJEWvEgLC3+4H0p+v+xh6K7GxMQEnn322UEvYxPlclmLI3PElJlsbPax0nn2PK9HxHIGMUOpisUiOp0OotEoWq0WPM/TvbHAepjV3Nwclpa2LpuoVCqYn5/H2toakskk0um0fn+Kt0gkot1kOuTsseZMYtPpjsfjWpSxj5uCeWxsTJcaX4prKau+FlhOnc/n9WinQqGASqWCRqPRM+e4VqvpkutGo9HjPjPNmq9tNpvwfb9nk6TRaOjPuNPpoFQq6dfW63W9GWGODksmk5iYmNDzmSmmlVKo1WooFAo9Zfbm/G9upJijwTjiTBCEwdPJ5xECbkg/Xdxu7ohfzogbbsPvXDqrgnSHMOdEEIThpFMoIqSsG97XnPlKFtFQ+Ya+xyCJRlpY/uP9+vuxdwyfoXmzGSqnGQBmZmZw8uTJQS9jE8ViUZfHUjAD0K4wH6fDSOHqeZ5OyGa/quM48DwP4+Pj2nHO5XKIxWLIZrO6X5ruI8O3tqLT6WB5eRnnz5/vcSVZ8m32MVOA8Y/ZO8vnTcFPAcmS7GQyCcdxdNJ2P8NQIszPhQ4xH1NK6RFR/GMml7O33HTd+XnSdaYzbZZJ81pRXHNeM/9NMIQMWK80GBsbw8jIiN6woOMcj8d1jzvL483EbApoc0387Kampga2SSEIwo3H+mAN487O+eXseG73FQtmQRCEYcEszS68voR6Z+vfh7cb31iZxdeX59Dp9srCSLij/whDKJodx0GtVhv0MjZx7tw57drSmTV7mSlAKWzM9GzXdRGPx3vm/46OjmJiYkKLsVQqhVQqpUcfpdNpeJ6nv5+dnb3o2s6fP48nn3wS58+fR7PZRKPR0GOU6DKzbJvr5OgkBn0B0M40XW7f93XpcKPRQC6XQ7m89S9u3W53KFLPzY0AurDchKCgNkdt2bbds5lA15d9x+xdNsuz+bjrujrsC1gXxfycubHC58PhMOLxuP5cXdfFxMQEDh8+jIMHDyIWiyESiSAWi/X0WvNrBr2ZTjff1/O8qw5lEwRh+IlY7R3jMh/P7UajPXRFboIg7CA6+TyQy1//Axul2aNfiiMW3jmZDEFw6WDZtT+77SatZHgZut+0h7nUtNVqabeRpc5mqnK329V9syzjZvl1IpFAIpFAOBxGIpHQ44M4Lmp0dBS2bfcEjjE8Kp1OY2ZmBjMzMxdd27lz5/D0008jn8/3uJxmCS9LtgHo2c0UjZw3DawHWXFdHMHEwLGVlZUtNzUsy7psaNnNguOh1tbWkM/nUSqVdBk1y+IpfvnZRSIRXTXA57g5Yvah83pR+PJY7IemeDVFrFkOz40Qx3EwOjqKXbt24cCBAxgfH9eOtun+c1wYj2lWOXCGNF1yQRAGTyefR+fkC8Ba4foc8AN17Pau07EGzLfXdl2VYM5/8uANWI0gCDuZTm7t+t2Ht8CxBl9deT345uoM2t3Ly8GQJankQyWalVI6oXgYKRaLm0KXWD7LxGP2OVMoMWGZzi5DuPhcJBLB7OwsotEoms2mTnsul8uoVqvodrtIJpMYHR3F7OzsRfuJC4UCvvnNb+Kpp55CoVDo6VMGgGazqUutgfUEZ7qgFFwU7BTZ3BiIx+PIZDKYmprC+Pg4PM+7UZf4mmg2m6hWqzh37hxeeukl5PN5nSDOTQN+fhSl3BRgeTVRSmmXGdjo0eZxmKKeSCTguq6+hp7n6aRxfsZ8HzMEzHS0gyDAyMhITxk/qxZYxcBgMDONm2vzfR/ZbPays6wFQbiJXKcN4LDV3REu87fXdqHeuro2kp1w/oIgDIAbZMSNfTWNhD346spr5ZurM6+oVeZW38Acqhqpu+66C/Pzwztgu16vIx6P6/JeihRzFBGAHhFqWRaq1aoOEuNYqHa7jUajgQMHDmjR5fs+zp07h1wupx3i2dlZjIyMIB6PY9++fWi1Wnj88ce3XN+5c+fw9a9/HZlMRs95Zo8y+2IppNm/y/5Zrp9Cr1KpoNlsIhqNYm5uDgcOHNC9uIOApeXsBeZMZfZxVyoVLfqPHz+OM2fOaCfXcRw0m0193o7j6BFR/BnO3AbQ0xPOkC9uhHDTgqXsdOPp2jNUzNyA4L8H27YRi8X0dWS6Oc+L5dssgzeD5dgaAECX1FPkdzodXaJt9lALgiAMA9/NT1+1YBYEQbhaOrk1hJQCsulrP5bRz2yp7e+6vlLBDKxvYBY/dQCpHzh1g1Y13AyVaD548CBWVlYGvYyLMjExoR0+Ct1KpaIDoOhaAtgUrAWsu5SxWAytVgvNZhOxWEyPNWq323AcB+l0Wj9WLpd1eW48Hkc8Hkc2m4Xv+zh+/PiWa3zuuecQDod12XAymdSlwcQch2SGhQHrwq5UKmF1dRWWZele60gkovt8bzZ0d5vNJnK5HNrtNprNpnZe19bWsLy83NNHvmfPHl1ibQabAdAucavVguM4+vNgvznLoOnqskyauK7bM96L78NRXbFYTJfCcwwVy/gjkYguv6Y4Z/K2+TqeB11trpvXgoFgwLqIpnMtCMJwcD1/WdvOfDc/jaq/M8JyBEHYfnRWc+tp2pnkoJcyNFxrGGPxUwfQDRQyP/j8dVzV8DNUovnQoUNDOW6KJJNJrKysaDEVi8W0G0ghA0CHRtFBpFir1WpwXRe1Wg2tVgszMzNwHEeLPfbKxuNxKKWQyWS0o23bNkZGRnDo0CF4nodarXbRlPFnn31Wj7rat28fstksbNvWPdOtVkunMVOU0V1eWFhALpfT/beZTAaJRAK+7w+s35zXcm1tDZXKxlzPWq2GarWKQqGAYrGox0bF43EtNAHosVrdbleXp7OUngK52+2i1Wqh3W4jGo1qZ5kOcqvV0psZ9Xpdu7p8rNFo9DjK7DNm3zpFNlPRzTAy3/f1a7jZYs78Zs95fwgYy+fFYRaE4eRW/mXt2cIkis3Ljye8Um5ld0MQhGujs7ICrKwgNDEOpBKv/OdPnQa62//3rGtpk+nHUgHW/uw2ZN82fBOPbhRDJZrHx8fx1a9+ddDL2BLO1W02m9pBbrfb2vGjO+j7vhZEppiuVquIx+OoVqtYXV1FJpPRr+fPNhoNLbgA6LJilvKOjIxgYmIC4+PjCIfD+I3f+A0sLi5uWmun08GJEycQiURQr9exa9cupFIpndDMdbF0vFKpoFQq4dy5cyiXywiHwzh48KDus6UwNJOpbyYsx2ZfOAV8q9VCo9FAs9lEKBRCKpXSGwHsMwegg774Pd11ClGWajMVm5sbLP2mG02Ry7RtusK+7yMWi2nhzNfTwaYjTCHNnzPnZrMEmyFh/DfBf0dsATD/3Zmi37IsjI6OYnFxsWfDQBCEwdJZWUHIUq/4F7Xui2cQtNvodnbdoJXdGJ4rTqDQiA56GYIgCJvoLC0DS8sIT00iSMQu+druS2cRtPyexxJ/NXohmHH4pvxcjKcLUyg3nRty7JDVxeqf3obRt98awnmoRPPExMTQOs27d+/WYpPCkSKq0+loEcSe4HA4rIWW2XvKUCiWZlP8sPc1Go1qUcgyYJZy0/m1bRs/+IM/iFKphF/7tV/b0mUsFot47rnnYNs2isUiYrGYTng2Zw0vLi5iYWEBvu+j2WwiCAJks1kt7Oh6VqtVRCIRtNvtmzITmM53oVDA2toaqtWqFs8sa2bwFwPWzD5tM/XaFM8AeoLOWB1gWVZPWnan09GCl2XcdKvZu8xNB9u2da97qVTSmwz8nCmOuUHCOdD1eh31er0nMZ3PsZycbjWwER7H8zOvQygUQjwe72kHEARhONC/qE1OIEhunjIQvDyP7hYj+/y/mMOrUsOb82HSDRROlcdurGBW6oYF+wiCcOvQXlgEFta/Du/ehcBb//2we+YcgosEEjtfnMRub/VmLfGa6QYKJ4oTN0wwEzvUxfLHD2H8r53Y8ffnoRLN6XT6or26gyaRWHcJWMZr9pRSzJgiGoBOQuZYIPZCp9NpJJNJxONxBEGARqPR42wC0KKIQp2uajKZRCqVwvj4OP7u3/278H0fv/7rv75l6fTq6ipOnDgBYP3a1ut1PSaJPcorKysol8taoJluMvuIq9UqGo1GT1/wjaDVamFlZQUrKyu63LrRaKBer+s1MTgL2AgHo9tqJmOzv5yBXXRoGfDFz9I8V5avU3jSUTZDt2q1GizLQjQaRavVgud5qNfrWsyzSoBl5PzcKZp5fDPFnCKe15fl+EzWBqCd/na73dPzzE2ZZrPZM4taEITho724BCwuDXoZN4QXK6NYq9/YyQrFT+6XEm1BEK4r7XPbY2PySulemLd8ojhxXVtkLoVjt7HwsUOYeufJHVHGfjGGSjRfbJzSMMCkY8dZ37GhCCMUOyx7ZukvH6ezCWyMLzLdaAo9lvvyPVkuXKlUsLy8jFKphImJCbRaLezZswc///M/D8uy8N/+23/bUtCeP38e9Xodd9xxB8bHx3vGTnHudCaTQa1WQ6FQ6AkI63a7KBaL2jmt1Wo3xMVst9tYXV3FuXPnsLq6ql1luqimU0uhzOtqBq6Zc42ZNu04ji5x9n2/Z9YxnWIKXT7OUDB+PmbvN19juvvse+bnlc/ne3rVGVhWrVZRKpXg+752k0ulkj4Og8RY7k/Hm+9hhoSZ6yEyr1kQhEHQDZT+RU0QBEG48bS7IbSCjcnBturCUl2cKo/d8A3MrfAiLcz/0SHseufTN/29bxZDJZqHuazUdDlDoZDuWzVnMwPr7nKtVtOOIkVOtVpFs9lEKpVCIpHQDiRTj0OhkC7RjkQiOpyL4q7T6aBYLOLll1/G5OQkRkZG9Azln/3Zn0WtVsOHP/zhLYVzPp/Hd77zHRw+fBizs7NoXCgDpGvJsmPTBWVPLt8XgBaml8J8/0u9ttPpaHG5sLCAEydOoFAoaEebo56azaa+7gz0onCmUOXf3HygcDaDtExX2XRrKTS5SUDHmSXyrCgww9oA9LjD3ISg28uU7EajAcdx0Gg04Ps+Wq0WisUiCoWC/ozr9TparRYqlYqey815zbzuXB/PkU44NxO4UdNut3U6+6BC2wRBuLXoBuqmuMxE2ZFNfYaCIAi3En43jC/9r3uw630bOVBnf+EhzH7vmYFnSuzke/TQiGaKxWHFdV3U6/WeBGOKNwC6R5WOMYUMNwIopCiMKYDYR0vR0+124ThOT8AYBW29XseZM2cwPT2NVCqlHev9+/fjp37qp7C8vIxPfepTWwqmSqWCb3/72/B9H/v379euNkugzbJw9uO6rqvXz+Rn09kEekVypVLBSy+9hEKhoMdjeZ6HRCKhhSWwLi673S5SqRROnz6NEydOaIFIB5bimL3WlUoFtVpNl0FHo1GkUik4jgPHcXQ5Nq9//0gt0+2nU2yem1kaTZFs9kQzAA6ALps2HWeWgNO5ptttWZauoKjVasjn81heXtYzoUOhEGq1mhbpFNMMf+O/s/7xU7z2ZjAbR4SVSiVJ0xaEHUC9ZaMbKFhqODfBuoHC6eoIVmuXDtS5nqz9yRyy73z5on2HgiAIN4Ky7wzF/djvhvGFj9+Dmff1BifP/sfHcbb9ABKvXR7QyoC428TLv3cbZn74qYGt4UYyNKLZLEseRvrdyn7oCptup9lPGwQBkskkXNfVQojOJAUfxTZn/zIBmmKVouvUqVPIZDKYm5vT73/PPffgp3/6p7G6uorHH398S+HcbDZx/PhxdLtdHDp0SAtZll/TSWYJMwOuGHqVzWZ1H3cQBCiVSiiVSqhUKlrU53I5rK2twbIsTE9PY9euXXpMk+u6OlCL85VPnz6NQqGgBW+n09FOPcVqrVZDuVxGPp9HuVzWQjkSieg/7NU2xaU5Msq2bb3hQVHMaw5AO+sUrfyM6USbKdt8juO7WCYdj8dh2zaq1Spc10UsFkO5XNafablcxurqKrLZLEKhkC7nLxQKKJVK2gE3/331O90AdI81Pwuer+/7en64IAjbn9QPnMLJz+3CodTw9UFTMC9XNweb3UgsFWDtj2cx8iPrfYjd2vZJsRUEYfsSefMZnNfp2YOh3rHxpU/djdlf6hXMyo5g/qfvG6hgvhUYGtF8++23Y3V1eFPp2JdKUWz2uxJTfFarVS0CHcdBJpPB5OQkHMfpcQwpqJnUHAqFdOk2x01RaAPrqdjPPPMMpqenMTs725Pi/eY3vxnFYhG/8Au/gBMnTly0RPc73/kOSqUSjh49itHRUR1GRsFsjjwKhUJIJBIYGRlBOp1Gs9lEo9FAoVBAvV5HrVbTo6DK5TIWFxfh+z5SqRR839c91ewxZmhVrVbD/Pw8qtWqLk1vt9uo1+ubZg93u11UKhXkcjm0Wi0tvk03lyKUgtEsa6foNUPATLHM9/C89fJCplybmyDmPGZuclAM8zqbwW3sTzZHTrVaLVSrVRSLRUQiEcTjcTQaDVSrVT27Ox6P6wRt27ZRq9V6UsEponlOrHZglUa/6BYEQbgSuoFCs9v7K0FIBYhY7S1f+3I1e9MFM7FUgPwfTaPVsTD+4+sRuN1yeSBrEQTh1mG1Hsd0tDgQt7nesfGFzxzDnl/cPJp38R/ch9gbh0cwW7EYutXqoJdx3Rka0bxnzx5Uh/QCU5xR7NJJNtOMKWDC4bAux6VotiwLExMTWjSvra3pUC2WDNOt7E9I7i/JbbVaWFxcxKlTp3D77bdjZGRElxpHIhH88A//MBqNBn7pl34Jzz///EX7xE+fPo1yuYw777wTExMTGBkZ0X24FPG+7+s5yJZloVgsYnV1FeVyGcViUQdfURRyNBSvB8u0y+WyLvcOhUJoNpsoFotoNptaLNNVZ1I4S9cpxsvlsk4hj8fjSCQSemwX358l/uyB5udmimlTSNPNNjdCgiDQGxvsm2YIF0vaWSZuho+ZJfaE5d5MRu92u3rDIRaL6c+b58wAMm4KUCD392Kb18rsgWcJufQzC4JwpVAsrzTi+NbJuZ7noukGvm/vc/r7cstF3o+i3HJv+BiTK8EOdZH/6ATqvo1df2seHSNYURAE4XrjfN9pfOcze5B1q9jtFW6aeK62HXzxc3dh7//nsU3PKcdB98ZPgr1ikl4DL31oH+be9d1BL+W6MzSieXp6Gvl8ftDL2BJTCLE3FkCPWAagE5PpRrL31vM8eJ6H0dFR7aqyJJchVmYCNIlEIlq0UVxz5FGpVMLCwgJGRkb0OlqtFmzbxrvf/W4EQYD/8B/+A06ePHlR4ZzL5fCFL3wBMzMzOHDgACYnJ/WcaIq7Wq2GXC6HlZUVANBiks5no9HQ31Nkuq6rRSA3B7iZQLeXCdk8JnuXKZjp4haLRZw/fx7Ly8sIh8MYGRnByMiIDssyRTN/htCxZmq22QNMwcmye87KpmPL8WDc2DB/niXh/Hw5Nsp0gimyWYLNP6Yrz+vVbDa1yHddt6ePHdgoBzc3Uvi4WUbODRpeD0EQtj/LlTgOJFYRtq5/TkE3UDhfT+HJE3u2fL7dCuHFyqj+/tRn9mH2l5/E2t8+APvRleu+nqslGmnh7G/txsx7FtAZ0t8jBEHYGTjfdxpVAE//5Sxi9nrb3p5YbpOA5r1zX/zqq2irbQdLjQSe/dpe7PtXmwWz5bo4//fvgffI8LjMO5mhEc1TU1PI5XKDXsZF4WzjQqGgy3OBdbHS7xSbwpq9t3Q3o9EoMpkM8vk8isUi4vE4YrH1EBW6loSlzPybLi6FbT6f1+6rORbJsiz8+I//OADgP/2n/4Rnn332ksFQZ8+eRS6Xw913343R0VHE43Ht+ofDYZRKJV02TTFJN7bVaqFer+sU6FgspkuGzZnHSinE4+ulfHzc8zztKHNDgOITAMrlMnK5nA4JGxkZweTkpA4YY4k1RaQZ/sXvzaoAvs4c1USBaz7PDQCWdPOY5szk/sTsIAh0+XWj0UAkEuk5d543E8mr1aqe80xRTtHM73kduJliln73O9pcq2VZyGQyWF5eFsdZEHYAY+84ga9+4nY8NHX6ssL5ueLEFR/XUgHidvOighkAnCdjaP7Yov5+zi1g5W/eM1SCmcQcH6d/YxdmflhEsyAIN57wIy+DcYTPfnYGIWUGtSrgTecAK4Tn/nJy65+3ujiQ2PpeWm07OFtN4/nvzODAz3wN+7C46TWW52HxPccQfcvwCWalgNDoCDqrw6vrroahEc2Tk5P41re+NehlXBSKGpb30nFk2S3/mK9hf6nv+z1iOBqNIh6PY2FhAfF4HJ7naUcSgO5hprNZqVS0GKZ72el0sLKygoWFBezatavnfRuNBkKhEH7sx34MyWQS73vf+3D8+HE0m82LCqlarYavfOUrSCaTuP/++zE1NYV6va7FqWVZ2smu1+t6vaZz22g0tPCl+GVAl23bekOBLqwpKJsXklB57sViEaVSCd1uF5lMBrZtY2RkBNlsFolEQrvWfH8zcM10kekc89ry2gHoEcRAb+UAy7fp9PPz4+aEmYbOsnbf93XgmNnf3G63dTAc+6O5mcL3pFCmu91/fXmu/RsBDDozw8rMayMIwvaHwjnj1XEkvbDJ0fhufhrA+i9xV37QMTzxH/e+onUUHz2G8DuHTzALgiAMEutNZ7Hlb1zdDoI3zm/5M8HkBL77P6dhW51NYY+ff+Eg9v/4cRzA1j8LALl3vQrODwyfYAaAVKyOU/91N/b+mIjmG8LY2NhQO80UYWbpLIUfhSBd00QioQUchQxLeE032OxBZQkxBVkoFEK9Xtflz3ys2WxqYZXL5XDy5Ek0Gg1MTU3pnmG+LhKJ4NFHH0U0GsXv/M7v4POf/zyWlpYuOce3VCrhs5/9LPbs2YNDhw5hfHwc0WhUO+XstWWfLzcTKOYqlUpPLzdHK/Hc6awqpfT5U+TSOfZ9XydVU2AmEglks1kkk0kdkmVeSwD6OOYfut4U/Bz5xc/NFJ8Uqjwnbnbwec7mpivN8WDsJ6/X6/o5jhLjH24kUECPjo7qJHEAWmgDvfOwzbFWwMaoM/574DUyx1JJgrYg7DzG3nECAPCVP70NsUir57noW156ZQdTChjLvKIfsWIx+HE1PL80bIFSAcK7ptGePz/opQiCIFyS9uISwo8AobkZPP0/pnAkvR5qWG656K5dOjPCSiTQiikMPlni1uKy//9TSv0PAG8DsBwEwdELj/0igL8HgFvOPx8EwacuPPevAPwdAB0A/zQIgk9fyUIcx8HS0vCN1SD1eh3RaBSO46BarWqB02g00G63tQNMMcjeVmDdnaxUKmg0GjpRGQCSyaQWXebIJzO0iq9l+jTLxGOxGJRSWFlZQbPZxNmzZ+F5ng71YkgWAHzf930fRkZGsGvXLnzjG9/AU089hWKxqF3VrXqeOQrq7rvvxp49e3rCryjsGR5Wq9W0UKfg5FrpgtJJpRDl683+aG4ycE2tVgu+7yMWiyEej+vxV3RtKfwp0M3AL5ZcU7DS8afY7w/RAtAjYNvttu5RN0c6cZ1MGe90OrpE3vM8PUeaf7i5wvXx2vAztG0b0WgU0WgUtQujU/hzfC03XkznHNgo5+f1MlPEhZ3HzboXC8PN6NtPXvMxQtkMnv3fk1f8eisWQ+5H7kL4fxtulzkRbeKFXxnB3LtENAs3BrkPC9eb9pmziPy9Pfj6+9eDGBdfGsFtP/X1i77eSiSw9O6jcH5wOF3mncyVbBp/GMCvAfhI3+O/EgTBL5sPKKUOA/hRAEcATAP4S6XUbUEQXDbBJJVK6bCpYWRxcRGzs7OIRqNotVqo1WqIxWJaRNL9o+tqJmAHQYB8Po9cLgfHcXR/L51XluiaSckUhJwTbDqjDMByHAelUgnnz5+H4zhIJpN44YUXAABHjx7FoUOH4Djr+1D33HMPxsbG8MUvfhFf/epXMT8/j5WVFZw7dw6FQkGX+bI3l6JtZWUFnuchm83q8Ux0bSkied7ValULSIpm13X1HzO0q9lsalFPzN5dADqt2gz/MvuYWa7eX8ZMZ5lurJluzdfys6GLy1Lq/jRu8+f42XKDgyXr5qYH12z2aPO68dz4nuFwGKlUCsB6eXyhUEC73dbl+DzP/jWamwP9vepmW4Cw4/gwbsK9WNieWHcd2vqJLtB96rmtn7tCam88Autdw/v/ZxPLChDeM4v26VdQqi4IV86HIfdh4TrTfvE0km9d/zqJFy752uIPHEHkHcN/P1Y78F58WdEcBMGXlFJ7rvB4jwL4/SAImgBeUkqdAvAAgM2Rb324rjvUTvNzzz0Hz/OQSqVQr9f1eCA6wGYStCm6+DdF0djYGGKxGDqdDuLxeE+vKkURnVOKLZYK+76vA6gYtMXxShxVNDIyglwuh6985StYXV3F0aNHEYlEdEnwW97yFoyPj+OJJ57AmTNnsGvXLr0BMD09jSAIcPr0aaysrGjhXy6XUalUdMl3u93Wjqdt28hms4hGo3qmMxOii8Ui6vU6MpkMxsfHkU6n9esYZMXzNhO4eT6e5+ngNNd1e3qAAWhHldeZ184cA8Xn+9+PP88Sa7636fTz/E0xT2fa7Dvud3dNp5xjqCiCGRbGcm/P8/R6a7Wa3niguGcKuXk+/T3ywMa8b2HncrPuxcL2Qt17BADw0x/9oy2fL3Q8fPjHfwCqE6B7/JmbubSBkPQaeP4/ZrD3x3bOL2rC8CD3YeFK4b0ZANAFgm89fWU/5zgITW+EhwW1OjpL289VziRqOPHvR7D/3TvnXnwt7Uk/pZT6WwC+AeCfBUGQB7ALwNeM15y78NgmlFLvBfBefs9k6mHmW9/6Ft7whjdotxnoTc82w59MdxNYF7mlUgmFQgFKKcRiMd3jyvLbaDTaMw6JziVFKB1EhoNx/NTo6CjGx8dx8OBBzM3NoV6v4/jx4zh9+jTq9Tr27NmDbDaLVCqF0dFRHDmy/h/yzMwMarUaSqUSWq0WEokEAOgxWbVaTYvJRqOhz4Pn67ouMpkMpqenMT4+jlQqpfutm80mcrkczp07pzcZzJJjiknTKTXdY7rEFMsUzLw+fL0pus1eYDO52nSi+XnQVWd4WigU0p8lAC2Ezf5vc7PALCmn609Yms/3orjmtTOTsz3P0wIbgBbJLCUHNkZNmWvj+ZnXjq+l+BduGa7bvdiFd4OXKlxv/tHvfwwRdXHjKh2q4Wc++keYb2XwBz/xZqhOFzi9cEXH7jhAePLKE7kF4RZG7sPCOg/cCVgKP/M/P6ofKnQ8fOhvvg2qGyD4xlObfkSFw7D2zAAA/JkMTrx7Y3Ro9KUI9nw0BpSrQLOJRlYhsukIw0erY0HNu4NexnXlakXzfwfw7wAEF/7+vwC855UcIAiCDwL4IAAopQKzV3NYoVCKx+Mol8s6qRiAdiAZGAZAl1xTzOVyOe0uU1A1Gg0t6Ewn1Sz57XQ6PT3CoVAI1WoVtVoN8Xgc4+PjmJubw4EDB/RIqje+8Y145plncPr0aQRBoAO0LMtCOp3GG9/4Rl3qnM/nsba2hkKhgHK5jMnJSUxMTOhRVPl8Xpdgm+XWY2Nj2L17N6anp7Fr1y7E43EdEhYEAarVKqampvDiiy/C9329SUBRTBFpOvMUoalUSidM85h0owH0lF0zxIviF4AWjqawNB1pHqdfcFMI94t5fi58LddCIe95nt4IYHk+18TPlj9XrVZRLpfRaDT0iClunlSrVZRKJTSbTZ22bSZsm+9pjtLi97yWnJUt7Hiu6704qbJSrrBD2WXn8bO/+wf4dn0WX3x4CpFFG/7kpe8R9b0+zv3ofqRfaF/ydYJwiyP3YQEA0P2eY/hnH/69TY+nQzX87O/+AeZbGXz0PW+Beuzb+jkVDqP76qM48ZNbS7L6Xh/P/twI0senkH2mieLDDYzdsDO4fpQrUez7lzurqOKqRHMQBLqOWin1GwD+7MK38wBmjJfuvvDYZenvbx1Wcrkcdu3apZ1KlvBS4JgJzKZLG41G0Wg0cObMGS0MgfVZxKlUSs9ebrfbPf2/Zs8zRTNLsunExuNxpFIpLdbJ4cOHMTMzg7Nnz2J1dRUjIyM6UMskk8kgk+lNUm21Wjhx4gQee+wxPPnkk1hbW+tJyU4mk9i1axfm5uawZ88ePWvadNrpWHueh4WFBV2WbM5ENsUq/zad2WazqXuZTUFMoWj2MnOTwUzRNmcds2e52Wzq+cosB2dgGUUxy7L5fubmBwB4nqeTwTlH23EchMNh3RNulmVzLe12G/V6HYVCQW9GMBSN62blgVnyz2Owh5vfU/xz7eyTTyQSqFQqUrK9w7kR92Jh+9D53nsQUq+8X7lTKmH/r57Cs/9x7gasShBuLeQ+LJCtBLPJLjuPd/7mX+J/vffNsL58HCocRufBozj1ty8tx1QthOhKF+HPPYmJsQfR/Yny9Vz2DcEKdWEdPXTNmRrDxFWJZqXUVBAErO/6IQCsNfgEgN9VSv0XrIceHATw+BUe82qWctNZWlrC7OwsXNdFvV7vKZM1xRWdRlO4MRxqaWlJi8BcLgff9zExMdHTV8uvGbZljhGiK+l5Hvbs2YMDBw4gm81uud5EIoHDhw9f9Hwo7sxyY4q3Q4cO4ejRo9i7dy++8IUv4MUXX9Rl2ul0GqOjo5iYmEAikdAuaCgU0iXlABCLxTAxMdEThkVBbIpbYKNH2fd97cQmEgl43kapkims+fqtRjXxe7rU5qgoAHputDkrmhsRdJH7RTedYm6QmA65WWVglk/zuvZf81KphHw+r68X+5iZMG4meZuzmfuvg7k5wPXzOKaoFnYmN+JeLGwffuqDf4DQ1tNBb2lC4S6sY4dviR5uYfDIfVgAAP8t9wF49rKv2xNZxTs+8Hl8/B+/CZGnz+LEZQQzACReDCHx0a9eh1XePNLxOp7/Vwnsf/egV3L9uJKRU78H4A0ARpVS5wD8awBvUEodw3opymkAfx8AgiB4Win1BwCeAdAG8I+vNCVwu/xyX6/XdSIyRUq/40eh6ziOFo+cSayUQrVaxZkzZ5BOp/UsZnNecy6XQzgc1i4zn6OQZljU7t27sX//fszNzel+5FdKs9nE2tqa7plutVpoNBqwbRuJRALpdBqPPPIIkskk/uRP/gQvvviiLs1mD7Mp2sykb645EokgkUigXq/rWcZm8BkALQy5JvZAs8fZFMQUpv0C2RSqZsAaRbr5OF1nCupWq6UD3Uy3myO/OIuZAXAM5+JnbvZc27aNRqOhX1+r1XrEPv8dra2t6Q0R011uNpu6l5obMLxGXLuZ6M318d8KA8iEncXNuhcLwnYnFavj+X+xs35ZE4YDuQ8LF+Ofv/9/XvFrDzqL+IFf+wL+17/9Pqz/0xC2A1eSnv1jWzz8m5d4/b8H8O+vZVHDTDab1QLRdPoonABol9l0MflYKBTSM3fb7TZGR0cxOzsLz/N60pY9z0M8Hkc0GtW9tEEQ6BRuusz79u3bVG79SuC8YDq7nU4H9XpdC7hGo4GJiQk88MADqFar+MQnPoFGo6HLvPsFbL/jafb0uq6re3XNsDO+tl9IU2CaQVwmW5VNm46see3NMVE8jtnza7rfhG60KfLNZG7+4cYAy+c5ass8R3M2dLfbRa1Ww/LyMpRSSKVSWqCzr5zinD/H89mqZLu/moHXRdhZyL1YuNlYlTDi8/I7viAQuQ8L14vD7jyy//aP8W++/I5BL0W4QobmN+vtUp69f/9+/TUFIgDt8plC2QycMh1O8/XT09NIJpM9JcrxeBzJZLLHrQWAYrGI8+fPo9lsYm5uDkePHkU6nd7y2lUqFZw9exYvvPACzpw5c9H+VgaHJRIJRKNRxGIxJJNJRCIRBEGAUqmE1dVVBEGAV7/61Xj961+PsbExOI6jRyaZ16M/Fbu/pBhAT6gVU8dZGs7nwuGwfg/2f5tp0v0usxkUZvZ28zHTiebmBR9jsJl5PDq2TNVmFYD5ebKawDym67oIh8O6R9r8XLluswS7WCyiVquh3W6j0Wjo0V5M1DZnYVNE8/xM59rcvOC12y7/TQmCcHX8yyffiQ5e+X/nyo6gcffl+5m9cxbif/j1q1maIAiCcJ0J796F8tzQSLdbjmsZOXVdMdOKh5l0Oo3V1dWeEllT2ALQfbEAtIgxn6fYYYq07/twHAeWZelS3na7rccYdbtd+L6PlZUVlMtl3Hnnnfie7/kezM7OIggCNJtNBEGgZziXy2V885vfxHe/+11UKhVEo1Hce++9OHbs2JauNMOl6E6Gw2FdNs7+abrfr3vd6wAAa2trANCTXM3zNVOoKVj7y6m3CrAyryVFpplEbn5vOsJ0mtlrzOP2z23m+zL4y3EcNBoNtFot2Late9RNoW+ukyXc3W5XB7aZY6soblmWTyFLEW1Zlnbxzc+/Xq/rEWTValWXsJvn1x/y1h+o1mg0etzl/tcLgrDz2Puj30HneQuhS4yc2gorGcdL77qy14anJpE/GJYhOIIgCNeRXCeOX3ryB1/Rz5Qe2I3YG7bfzOadgmxXvAIymYwWPuZMZoZDcY4vxRBFE7DRE0t3k84kg8BY1ut5Hmzb1qXedBvPnz8P3/dx9OhRvO1tb8Ndd90FYF1Mrq6uolAooN1uY2lpCV/72tfwV3/1Vzhz5gwKhQIWFxfxne98B+fOndtU4gysi3zP8xCNRrUYi0QiGB8fx9jYGIIgQLlcRrfbRTabxUMPPYTp6Wl0Oh20Wq2e5GqWYodCIX1NOHu4UqnofmzzmvWXQAMb472YSG0KTwrsfuHMMm+GapnPmd8TroPHooCnYDXPyRxnZfYas+eZ5dRm6bYZFEZHn+45y+LT6TQsy+q5Puy3jkQi+vxNZ57XrP868DFuWAiCIPTjWT5qD+6/7OuschjpFzqovWoG3iPyS5ogCMKl+CdfvvIQhULHwy/8xQ/jtp85d9nXWqWwjP0bEoZGNG8Hp/nIkSN6ZJDZR2uGWJkJyv1hVGaqcrfbRSKR6HGTXdeF4zh6njLTtkulEgDgwQcfxNvf/nYcOXJE9+PW63XUajUEQYCFhQV8+ctfxte//nUUCgUtTFutFpaXl3H69GnUajX4vo9KpaLLgkmz2dSpzZ7nIZ1OI5lMage8VqsBAMbHx3H77bcjHA7rcmJzPBPLyikqOWKJ4r5er6NSqaBSqfQEq1E41mq1np5eurJmqjivJx1VutNcA8UjBTwFPrBRVs/ya46MMo9BQU0HmWXfPE+zx5nv2S+WWa7NgLFms4loNIp4PI5EIoFYLIZoNIpQKIRisYjV1VU0Gg1dEs51muOnzLRzvp8plLdK6xYEYefy059/N/7hF/7mFb8+G67gzA9delPNKoex64tdeB+T0mxBEIQr4eDffvKKX3vGH8XBn/o6gkYDyaciF32dVVq/F7t/KqHrw8DQ1HBuB9E8NjaGQqGAZrOpE49ZEsxgr06no11RimUKrmaziVgshiAIEIvFMDk5qUUpHUamUTcaDXS7XUSjUUxMTGDXrl148MEHEY1Gkc/nEYvFYNs2arUa1tbW0G63cf78eZw5cwa+7+s+XKZ8NxoN5PN5NJtNNBoNlEol2LaNdDoNz/O0yKVga7fbKJfL8H1fP2+6tXNzczh9+jTOnj0Ly7L0nGaO0mLYV7VaxerqKl5++WWsrKzotZn9vfze931dnuy6LrLZbE9/uDmj2CyjNt1pcxSVOXbJdJv7A73MXmkegxsbPJb5GH/GDAJjaBjfl+Xf/SOv2APN17CkvlAoaIecpeac281jmi642TNuhpzxPM0ebUEQdi63vfcJAMA//PWfwH9/5LeuyzHdFQvRP/nadTmWIAjCrcI//MvL34cLHQ8f/Is34QC+hm65jN0ffRHPHN296XXcvIx+XATzsDA0onnYmZqaQhAEyOVyWrAxpInuH13L/v5lll5T0HGck+u6GB0dheu6yOfzWFtbQ61Wg2VZmJqawsGDB5HNZrF//36MjY2hXq9jcXERtm1jZGQEAFAqlfDMM+uzKBkgZYonuo7dbrenR5mOplleHgQBHMdBEAQoFAooFouwbRvxeFyXFZvn5XkeisWidoVjsRji8bgWmrVaTQvm8+fP69FaAHpEOHuF2dNbrVa1q9s/QxnYCOGi62yWQfP6stybmxYUr8BGv685l9m2bV1qbs46Nh1lXicKfdNpNkuiTXFurpObCGa/ejgc7tngoDDn52H2glNAmz3k/ZsZ/a8VBOHW4LZ/8Dj+0X/92+vfKOC/ff+He54vdV383Kd/FFAA3ItvUlvlMEa/K/cPQRCEV4q+D29xDwbW78O/8PEfxYF/+Zh+LKjWMPKYvem1seVOj8Mc3rcHuSMhJG/EwoUrQkTzFXLnnXfq8mHXdbVzaM4FZsqzOfbHDMEyU58bjQYKhQJ2796NZDKJZDKJRqOBl19+GZlMBseOHcNrX/vaTaIXAFKplF6XUgqlUglLS0tIJpO63NecS8x1eJ6ne6Xj8TgymYwuX1ZKacHMUuxSqYROp4N4PI6xsTEtJCk6eQ3m5+exvLyMSCSCZDKJWCyGbreLUqmEYrGIxcVFVCoVLax5HK6RI5o6nY52XFOplBawdFNNgchjmM+ZpdGmkDQ3MegemynnvI78eYZ4McE6Eono8K4gCHrEv3kMc1SWmQRuOt21Wk2LdG6o1Go1NBoNPY6rHzNojJjC2fy8t0otFwTh1uDgP7lQTq0U/vH/9ZM9z4V8hYM/9xhC6RSe/eWD+nFVD2H0yY17SzTXkVJAQRCEq4T34X/8X35y03MhX2Hfzz3W81inVMLIbz626bX9VI6MI/kayZcYJEMjmrcKahomYrEYCoWCFlzm7GQz4ZliCUCP42s6lt1uFysrK0gkEqhUKj1Jx9FoFOFwGCsrK3juuecwOzurE68ZFFatVvXrMpkM7r77bpRKJbRa///2/jxKjvu870a/1VtV7z07MIOdJACCuyyRlEyakuhYWxzFjiM7dvL6zVFe2/d4S+KTYyf2PW+cm8TJeR078T12fOXIsmxLXkTJpmTJkSyLshZKJEUSBAkQJJYBAcwMZu+9q3qr+8fM9zdPFQYkIGGme2aezzk8mOnprv5V9ag039/3eb5PC9PT0wGnk06yFMmWZcFxHKRSqcCIJ4ac0TWnWKtWqyYt+sCBA6YUvVwuG4G4sLCA5eVlU5bMHl5eq3a7jXg8jmazafqXgTWntdlswvM8lMtlRCIR039Mh1e6trKvGFgrUabIls8Ph3vJEWH8XvYsS7eZn6HshZazk6Vw5VopvPm17D+Ws6ClkGfvt1yndNjDGyA8N34OhL+bfJ2GgSnKDsX3ceu/Xr+82nc9jP7dmquRqHaR+os3/oNNURRFuX6udQ9Wti59I5r7faYsA8Di8bgRQoTiCMBVDibdSpZzsy+31Wqh0WhgamoKV65cMcFPuVwOnU4HzzzzDF577TUcOHAA+Xwe+/fvx2233YZEIoFKpWLeP5FIYGRkBHv27IFt2zh//jxOnDiB2dnZgAhLpVJmvrIsLZYzgD3PQ61WA7DiRFP8eZ5nwrp4bjMzM7h06RI6nQ7S6TQcx0Gz2cTy8rJxPNnf7TiOEeCVSsUIbZmGXS6XUa1W4bqucZ0psCk45dxjWWrN8nfOUpbCk/3V0o2WvcgUv+HjMtiLM5UZ5sXnc0wVNwbYfyw/f74+3HvNr13XRaVSQaPRQDQahW3bZtyUDIwD1jZgwr9v/B0L/1xRFGU9uq6Lwh+pSFYURVE2jpqXwNDnt1e+Tt+I5n4OAmMwVlgsA2ulseyPpQALh05RbDEFmgJncXER3W4Xe/fuNWKRpd6Li4uYm5tDq9XCrl27cPToURw9ehSHDx827+84jnnd7t27MTg4iEwmg69//eu4fPmyKQUeHh7G8PBwIIVauplS+NFFTyQSSCQSSCaT5ud0aF944QWcPn3apFtz1rTruqbceHl5GQDMcyiWc7kcRkZGzONMsqbo5HvV63UjKKXQBtaCwbgJEIvFrnJ9eW78noKWTrEcKcXPKjxzmhseUmDzONLVlW6xPB7HRjWbzUCoWaPRwPz8vClxdxwHhUIBrVYL9Xr9qt50vo6bHHzPcKK3DC9TFEXZidS8BEY+a7/xExVFUbYI6VeXcOW5EQy8ab7XS7kuPDeO8T/cXhu0fSOa+/kPfZZCA4Bt26hWq8ZFBdYcRJlgLB1LPidcYkxHUZYXsxQ4lUqZ5zYaDVy6dAmvvvoqTp48ibe//e248847kc1mkUgksGvXLlNSnUqlcPvttyOZTOLcuXOYm5tDMpnEsWPHMDQ0FHBBCculWT4sxzOxfHxkZASFQgHRaBSzs7M4ceKEEcXlchnFYtF8vbi4aEqtw7AHO5VKBULDeI2ZME2HV5Y3c/41r5lt2+Yxiez9DZf9SzeWZdLhcm5ZXg3AiGwej+/P10jhzWsoZ07zmAz/YiAbe7gTiQSGhoawd+9eJBIJzM/Pm/R0rpHPl2ng3HSQgWdcJ69Tv7c9KIqi3Gw8N47xj2tppKIo24fOK2cx+twQWm/q9Up2LiqarwPO8A2X9wIIOMxMzpZCTM4WtiwLpVLJCMBut4tYLIZUKgVgLaSKz2f5shSFr7zyCqanp/G2t70Nb3vb27B//37zepJMJnH77bdj7969RpQNDAwYl1ZCV9d1XVN+btu2EfCu65qZwhwL9YUvfAEXL16E4zioVqtYXl5GsVjE3NycKbO+Fgwaq1ar2L9/vxnBJYWuZVnGPWbPM9O7GVy23lgvAIE+X9mXTbErR0wxsZpp26wSkAJYzkLmccJjtWTIGzcgGPgle4vlZkqj0TBueiqVwujoKAYGBowLLUO9AJgKAL6nLMfm82RpOBO+NUVbUZSdRL0Zx8intldJoKIoitJ7+kY09zNMrWYvabgEW5Y8U6R4nmeEjQx/Yhozy4AbjQby+bwRtAyjIhRknOfb6XQwOzuLJ554AsBK6fjw8HAgmZtkMhkTInYtKEDZs0thats22u02BgYGYNu2Ee5f+cpXcOLECSP+OCf68uXLWFpauq7ryRTtoaEhM98ZQCA0jc9JJpNIJpNwHMcIerqoTLqWvc8yPTssxKWglO8TDmrj58g+c4aEhX8e/t51Xbiua2ZNy80T9lvz90cGjcnPLpVKwXEcLC0tmXJ4fkZ0uFmaDSCwbnlesh9aURRlp9BsxpD5xFO9XoaiKIqyzegb0dzvf+DL3l2KNaZQy5RqYM35Y8+pLP2luInH43BdFxcuXEC73caRI0eMeGKAlXS1KaopmFzXxZkzZzAyMoKjR49icHDwKsf5eohGo3AcB4lEAtlsFgACjizFcqlUwvHjx/HMM8+g0+nAdV3MzMzg9OnTKBaLqFQq1/2e6XQaBw8exNDQEGzbNg4pNxUAmPWk02mkUinjuksBSZe8VqvBdV0TCAasOeiy3JoCk+dEp5+l9kzx5oYABS4/P/mZyERthsTJZHAGerVaLXNejUYD3W7XbKiwH52/Q9y0KBQKWFhYMMcBYDYL+HvI8nW5GSA/U27WKIqiKIqiKFubyL3HMP2whZFeL+Q6qHkJ7P749suV6BvRvFWQacaErh+FNMWWTEGW/aUszWZ57vLyMiqVCvL5PHzfR61Wg+/7yGQypnQYWBGSmUzGuInz8/M4e/Ys0um0GUf17RDuCZbneuXKFVy8eBEXL17EqVOnUCwWMTMzg+PHj2N+fh6Li4s3/F6HDh3C7t27TYq3dI3pmiYSCaRSKeO8yrnIFMEM32KYlizR5iYGNyhkWTZdaY6jIuFS+HCoF99flndTZHueZ2Ytcz3sS+Z7ynLtZDKJfD5vxLos18/lchgcHITrumaTpNlsXtWvzBRv2T8thb38HVUURdkJJO0Wrvyrt2HXbz7Z66UoiqLcNOp7Mxg5stDrZVwXrVYUzmee7vUybjp9I5q3wh/4cnQQRyxJMUO3UCYcs8eWQpcCjqW6hUIBe/fuNc4hj1Wv12HbtnEhWS7MNOtOp4NarYaZmRmMjIxgbGzsqvVWq1WcP38etVoNhw8fxtDQ0DXPjXOX5+bmUCqVTLjXwsICrly5gunpaczMzGByctL0L99ov2wkEsHu3btx7NgxZDKZgLPM60KnWJaNx+NxJBIJc40pVpvNpnltePYxsLbBQfFLIem6rvks5caG7F2nwyyPyZnNFPr8XFutlvmXveGu66LZbJr3ocAHVkSvbdsYGhoy55fL5UyomWVZGBgYQKlUMqJc9spzU4a/W3yNdMUZHKYoirKTsONtLN1f6/UyFEVRlG1G34jmfkf2v8oxP3SRWbbN/6SokUKaRCIRZDIZ7Nq1CyMjI6Z3VgpyYK3Umz9vtVqmtJdzfhcWFjA9PY1CoYDBwUEAK33Yp0+fxpkzZzA6Ogrf91GtVs166/U6isUilpeXjTgrFouYn5/H/Pw8ZmZm8Nprr2F6ejogBkul0rddSh+LxXD06FFzvuztZY+3dH8pAClMgbXEa8/zrprHzDJ4XjPptLKnXJY2S+HJzyf8vjy2PEZYMAMwI7b4e8INDTricl42w97S6TQKhQJSqZRxnVmd4LouWq0WFhcXzdxruRY62zymTBXnZsy1qgcURVFIbP9eLP7PtRK6Ui0J/1Q28JzmYAejm72w74Cqa2Pv7+n9T1GUrUf0jiNY+n9W/rabPz2MW//12hSAzPNTWHpiP9LvmOvV8q6LejOOif9fotfL2BD65v9Z+jk9m1C00ZmUTiZ/TsePootCi6KO4iYWiyGdTmNwcDDQ90wRSQHNOcHVahWOs5IIyudmMhm0223Mz8/j/Pnz6HQ6Zt7zq6++itdeew1DQ0MYGxsz7xmJRHDu3Dm88sorRmzPz8+jXC5jenoaly5dMk5prVZDrXZzduwjkQjGx8exa9cuE+g1MDCATCYTCPZi8jTFZ71eDzi8FIzSxeVnEg7AohCXjjGdbek8y1AwmZAu+9R5vHC4FwWxnDXN8V38PZDzvZmKnkqljHDO5/PI5XJmfdVqFZ7nmd5m/i7xGoTTswEERDx/R8LOu6IoColNjCP5xw1885bPmMdONhv4YPqf9XBV3zntdgSxLz3b62UoiqJcN9FbD6L+Oz72ZWfwuf1fAQA8e3sTv3DnBzD1/G4c+sVvoH15CoVze9B6R48X+wa021HE/nZ73oP7RjT3e3m2nJlLpFCRs3oJBQ77bx3HMX3K8XgchUIB6XTaiD+KOIpIimY6n4uLi8al5Nxfy7JQrVZx8eJFlMtlXLp0CQAwMzNjBDMALC8vw7Is5HI57Nq1CydOnMDJkydx7tw5vPjii6hWq2g0Gkaw3mwikQj27duHXC6HbDaL0dFR5PP5QMBVLBZDuVw24pTXjWOsuKlAMcsechmyJoVqIpEw4pefjZx3HC6rZ/84j8nPQPY2x2IxuK5rPttqtXpVvzLL8Hls+fvDEnRuHGSzWQwNDSGVSpnnUIBzbBQFuOzBDqdjs7Sf61anWVGU18NPJ/HYLZ/r9TIURVF2LLE9E/D/yMdYcgEf2ffVwM++y07gy3f+Jb55awf/4f7vx4XFQTQWu7i6GbN/aDTjmPjv2/fvz745s35Pz5aOIecVS1eSYoz9pfwZicViGB4eRi6Xg+u6SCQSGBwchOM4Jo2Zs4kdxzFCjEKLyc5h55qCi6FQV65cgWVZGBwcxLFjxzAyMoJOp4NkMol4PI5SqYQzZ87g2WefxV//9V+bHuaNvP6WZWF4eBj79u3Dnj17TNI35yzLMLBkMhkI/ep0OqYUW5Y5y2sr3WQeRwaySQEsNwTkRoicFU2RnEwmzegolm/L5G32VrNUmqKd85G5bqZqh+dJx+NxOI6DdDptUsQZAJdMJmHbtim9lqOqKNjb7bY5L24mhOc7K4qihInt3oXxP7py1eMHY1H896N/hpPeBH5v8qEerExRFGVnENu9C+OfLOL39n79dZ/3oBPF5458Dp+upfCfz7x3k1b37dHpRhD52vFeL2PD6BvRvBX+0KcwYokwgIBIkeW8FG0Uz9lsFnv27EEymUSpVEKr1UI2mzVJz3QfKYIouBhyJYVfq9WCbdvI5/MYGxtDoVAwrqdt2zh48CDGxsbMHOiBgQFks1mcOXMGf/VXf4XPfOYzOHv2bKD0dyOJRCK49dZbceDAAYyNjSGZTAbSxWWvMoCA62rbNmzbRqPRMEFdRPaW04mVvcjhUmrZkxxOw5bBX+x/Zmk8j8H342fcarVMTzT7obkWvofruigWi2i1WmaTgAKY33NzhFB4c2Z22DWWLnl4/rQ8hqIoSpjoyAhu+cwifmv8mat+look8KAD3Jm4gE8434XFP96H6A/N92CV3x41L4ED/7ULbUxRFKXvcWz83t7/fd1Pf9hZwD/a9zw+efG+DVyU8nr0jWjeDPH27TI2NmbGCdEVDjubLBUGEHAkWaY7OjqKXbt2GYe4VCoF3Gg5e1jOAKaYZCk4y6dHRkYwODiIsbEx7N27F4VCwZQvp1IpxGIxNBoNZLNZ2LaNJ554Ar/zO7+Dp59+GvPz85t6vYeGhnDbbbdhdHQUjuOY8mGepwze4vd0VaW7ymtPh5WCl+XLYXHJz0n2JzMMTJY3y9J6mULNx/h8imTbtlGv1001AJO8WY3AqgMK5kuXLpnS+FQqFXC9W60WyuUy2u222TSRmy08LzrLDD3jdZLhYOGea0VRlDCWnVhXMEsyEQdOtI3chSa2Ug51pxOB/+xLvV6GoijK6xIdGcE9n5q8odecajn4u4XDG7Qi5XroK9HMMtR+Y9euXUYYyRAw6fYBa04fRREFdSKRwNDQEPL5PCKRCIrFohE/0kmmMIvH40gmk4HRQXRR6YICK0I7n89jZGQE2Wz2qp5qx3FQKpXwx3/8x/jwhz+Ml156yaQ8bxbsZR4fH0ehUDCOMM9Z9hzz+rGfVzrvvF5y80J+FnKjgf/Kr/kedInXc2LlrGMptvl6WUUgg71kWTf/ZeL48vJyoCpB9mQ3Gg0AQK1WQyaTMaFo5XLZpKdLx5xfyznTAMy58Xl8D0VRFEl0aBD3f/b6/lD70d3fxC/+sx/q6/45RVGUrYhlJ/Cfx07c0GsWOxks1NMbtCLleugb0dxsNpHL5bC0tNTrpVxFoVAwwofBTMCa0ydLdylsZSBTNBqF4zimlLderwdcQYpgWd4rw8H4Po7joF6vY2lpCclkEvv37zcJ1GHBDABTU1P4rd/6LfzJn/wJrly50pMNiUgkgv3792NwcNDMZgZgxB3dccKft9tttFotpNMrNwiKZlmGLcusKZzl/GW+Lty3LF9HAcrE8mvN2fZ9H4lEIhDMxj5lrokuOKsDKpUKlpeXzbl5nodyuYyBgQHjGsfjcdRqNbRaLSOUK5UKGo2GKdOW6eoyPVteKwp3ufmgKIpCorkc3vF3r+HfDJ67rudPxJYxNlba4FUpiqIo2wGvFcO+X26i/5ttv336RjRXKhUMDw/3pWimSKIYkX2pwJqTSUEmnWG6yalUypRlF4tF47oSCnGmSMtjJpNJI+A8z8Pi4iJqtRqSySRyudy6Imlubg7/7b/9N3zkIx9BuVzuSbmuZVnYs2cP9u3bh6GhIZNsLUdCyX5hXtf1enT5Na8lxzpRTLKUWTqsdGNlijmAQLK2DHKTGyL8HsBV5fhy3JUsh6a73O120Ww2UalUrnJ8Pc8zIrlWq6FerxvRzkRwJnJzo4SOu+d5iMfjgevGjQdeo/CILUVRFABAPHbdgvlks4H/9No/2uAF3VzqzTgO/Nv6tv6DTVGUrU90eAjf/4XjN/Sar7td/PbFd27Mgm4SHd9C5+UzvV7GhtI3llS1WsXIyEivl7EuFCgUQ3QUZXiZdD/pIDLRWpYdV6tVACul0wyDko6lnBvM95Z9z3SsbdvGrl27UCgUrlrvmTNn8Cu/8iv48Ic/bHqne4FlWbjtttuwa9cuJJNJI+YoEuV8ZDqkdHwpnqUobLVapnw9k8kglUohkUgESr7Dx+PnBqyVXQNrVQJ0kvkZJxKJQJ96s9k0GxnS2ZWfUbfbheu6pq+ZpdeLi4tXXZN2u41KpYJ6vY5isYhqtWpcctd1UalUUCwWjfiWmyks5ee6ZfAZ18Hz2qjRYYqibH9qfgxLjVSvl3HdNJpx7P1XdXTOnO/1UhRFUV4XKxbDTxWmrvv5T3st/IfJf4CS52zgqpTroW+c5qWlJQwODvZ6GevCwCeKN6Y60+lzXReO48DzPCNomZycTCbRbrdRr9fR7XZRLBaRy+VMinR4XBBFHPunGQJFtzOVSmFgYABjY2MYGBgwzjYTuE+dOoX/+l//Kx577DHU6/WeXTPLsnDo0CHce++9GBkZMW6pFLJ00oEVUZhIJOC6LizLMjOo6bCzNJpl8hzLRMHMtHE5borim5sZ3KTgceUYsUgkgnq9bpxdGSIWiUTM9wBMujnLwSmo+TnV63WUy+VrJsK3Wi00Gg24rotWq4VcLmc+Z14Lnj+R/da8dnS7eR1lQFilUlHRrCjKDfFqq4Z/PflDaHW2Vvq+D6A9+Vqvl6EoivK6RFIpnPnNXdf9/Ge9Jn75/D9CxbM3cFXK9dI3onl+fh67d+/u9TKuCUum2btKoUzBQkeYP6dIozPIAC726VLwyV5pHocuq5zHK/tyR0ZGcOedd2L//v0A1nqgn3nmGfzar/0a/vf//t8mZKpXpNNpPPTQQ9i3b5+ZEU2YQi1Tq6UoDgdbsZeZ17PRaJiwNM65ZsmyrAoAVsq5WWZNASyD2qTwZPk4P0fpQvN5FM98n0ajYcrwWSXgui5qtWtnzjJZOxKJoFAomORzvpbv63memc0tXxsOHpOhZwDMRo6KZkVRrpdzrSp+9uyPbDk3o9mOYu/PlNF/EaKKoihrWLaNVz90BKOFMj5w/lH8+aG/fd3nn2i6+KXz/1gFcx/RN6J5cXERY2P9mdMZi8WMk8lSXdu2jViSjiTFsxRclmWhWq0inU4jkUhgcHDQONHsW2W/suu6gTFLFIAUWalUCvv27cPRo0cDI5ZOnDiBX//1X8fnPvc5I8R7hWVZuPfee3HgwAHjulMIM0CLopebBe122whQXhsApgxZ9um2223UajVTzh2Px83zXNc1861lT3OlUjGvZT8znX2KbVYFNJvNQKo5RTI3L1gyzj7karV6VZk0Q73Wo9FomDC34eFh5PN508OcTqdRq9VM6TZFsyzR53sQ2f/O7/sxhV5RlM3Hiidw9g/uQCx+7XvCxXYVP3X2n2zJP85830L78vWXOiqKomw2VjyBsx+5HSOFCnzfwqVK4XWF88lmA//q7A+j1kxs8kqV16NvRPPMzAzuv//+Xi9jXTiXt9PpwHEcI0ji8XjAzZM9qBRa7N1lCXEikTBCUY4fomsYTtLm8SnuUqkUbr/9doyPj5v3ffHFF/Frv/ZrePzxx3sumAHg7rvvxtve9jaMjY3Btlf+CGO5eTQaRS6XQzabNS6x53lGIEr3l24wsFYiT1gG7fu+EcjAWvhXJpMBsBK8RaeYx+AGhwxi4yZIOF2bIh5YE6p0yCmcHccxYWusHng9p5m/G7ZtmzWkUinE43F0Oh14nodGo2H60WUJu/xdobvOr7mJw/dQFGUHE4li8mN3AgCG8xVYlo+/9/L3r/vUrm/pH2eKoigbgBWL4fwf3o7hfNU89nrC+eVmHT939kf0ntyH9I1onpycxDvf2Z/JcBQgnB3MkCY+xufIEmrbtpFKpUw5LXueR0ZGjOiSbjRfK8uA6aBSzMViMezfvx9Hjhwx73v58mV8+MMfxqc+9anXdTc3i7vvvhvvfOc7ccstt5igLvYWx+NxZLNZ5HI5I6Y9zzPjlmTauOzlpoMvhWC73Tausgzq4rxmikleVxmgxffhe1B8ysAwBovJ+cgU1OwV588pnrvdLiqVCjzPuy7RGovFzPXg71a73Ua5XAawIuTpNPO85HxqeT4yvb0ffg8URek9g/m1zTvft7akk/xG2PE2zv7xfbj1nz7f66UoiqIEiUQx+bFjGMxebaRQOP/I5Dvxpwe/hMlWFf/izI+i61totOLrHEzpNX0jmi9cuLBuEnQ/QKEiy7SBNeeR7ia/lq8DYMKl6vU6kskkUqlUIPhJCmUA5mfVatWIsVgshsHBQRw+fBi5XA4AMD09jd/6rd/C7/7u7/aFUDpy5AgeffRRHD58GAMDAwExKl1Z9jgzZdp13cAoKs45pjssBahMF2cZN91rYE2E87my15mP8XORj1OEUqCzPFuOyeKaGf5Vq9XMdbdtG7ZtG+H7RtANz+fzxnFn73YikUAymTS/A7wu0mmX/fJcP4V3tVpVp1lRdjKWhYt/dgx59DbbYrNIZ9xeL0FRFCXI6n14IH3tUF7ft/BaeQDvOPl++L4Ft903skxZh74ZOXXlyhUkk8leL+OasKdViqRoNIpmsxnot+UoKYZEAWuzlx3HgW3byGQyJjiK/bGdTseIPQY5hcX5yMgIcrkcfN/HzMwMfud3fgf/43/8j74oyT5w4AAeeeQRHDp0CMPDw6ZvmBsG/I+9xxTTMuiMGxN0eilq6ehSjIZTuJlOzoAxKZSl0yx7f6VLLMUoxTqhQKaw7Xa7pryejzPkjKL7erEsC8lk0rjkdLW5ueD7fiAYjb8rFP48B47dYm99qVS6aj60oig7h8uPHUM+vTMEMwCkEi2c+/i9vV6GoiiK4Xrvw/6qs6yCuf/pm0+ITl4/QtFCwcQgKjkLmOW7LBcGVgQYRRBFciwWM721sgeV4V8U4eyXlt93Oh0sLi5ifn4ejz32GH7jN37DCO1esnv3bjzyyCM4cuSIEcwsKaZzytnK7Mtlynij0TCbB7yOspyagpbXX/Yly6RrPk6R22g0jPiW6dLys5KuMIU2xb50nT3PM7+b/IzY587wMrkBcj2bGHx/ro2fN8dtcewVP3+uX1YUyPL+eDxuXPZ+2ERRFGXzmf6LYwCArL3z7gGO0/v/L1QURQFW7sU78T683ekb0Qygb0Uz3UsKLjleiGJXlmfLMDDOGA6XAlMotVotIxBt20aj0UCj0TDH4bxe13VRLBZx9uxZPPnkk/jQhz7UF4L5gQcewJve9CbcfffdyOfzV/XY+r6PXC6HgYEB05fM0uZqtWrELkvQWXYMIOA6M3WbzrFMKWdaNIWrZVlIpVJGdCYSCaRSKfMcblZYlmXKsHk8roOimYKZJfNy/JQMiGOJuSzVfz3ke/KcKaDl6DF5fYBgMjZ//2TonKIoO5e03fs2nV6RcTyc//i9OPSjx3u9FEVRdjBX/vJ2pBM7716cSrQw+ad34+CPnOj1UjaMvhLNsky2n6A4k4FWMkEZQKA0mInLfB1LeCkS2a/Kkm4el6Kv3W6jUqmg0Wggm80imUyi0+lgenoazzzzDB5//PGez2FOpVJ45JFH8OCDD2JiYgL5fD5QEkzXNZlMolAoIJFImNJnhl0xYVrOHuboJbrzAIybSrhZQMFLkdput43TLGc6M2CNgp0OsxTHEj6fApkuvyyj5noY8MbPnOf3RshwL/bJy98L9nVLZ5nXhufLtfAayt89RVF2FnOPH4W9w6cVxxM7+/wVRektc48fRTLee0OrVyQS/anjbhZ9JZqXl5dh23bflZdyhjDLhSkIZQkxnUPOJaaDCKzM5U2lUqhWq6jX68hms4EyYQqdZrMJ13XRbDbRbDaN4G40GqhWq1hcXMTTTz+N5eXlnl6PwcFBPPzww3jggQewe/dupFKpwFgkGXDGkmwpKIvFIkqlktmAkG4rr4ksSQbWyqopfPlewIrolMdguTKw1g/O5zDZnNddur0sbWZfetg1Do94kiXZ6XTa9KFfz+YPQ9A8z0M6nTYbMRTTzWYTrVYrIIJl+TnXzcfphs/Ozmo/s6LsMOY/fQR2TAWjoihKL5h7/CgikS7sqN6HtzN9EwQGAEtLS7jlllt6vYyroHiiK9lutwNuohR6fJy9yyzZ5XHm5+dNr3M8HjeBT3QUKZo4ngkAqtUqrly5glOnTuHKlSubfPZBJiYm8PDDD+P+++/H2NgYMpkMksmkcUcBmHNLp9Mm3I0bAktLS1haWjIbC0Bw/rEsdScsgScUuQxYo2PM4Cw5nooly+xFdl3XfC5ytBSvfzqdNqncnucFNjW4OcK1xONx07Mt5z5fT5tBs9lEsVhEq9VCOp3G0NBQwPl2XdfMoWaptizFppsuZ0o7jmNeoyjKzmD+00eQiG3v3X1FUZR+Ze7xo7DjbcSjalhkHA8X/uzuXi9jw+g7p/n+++/HqVOner2UAOGgJpbrAmslwYSOJsUyhRxd07m5OeTzeQwPDxunlCKHQimZTAbEdLVaxczMDM6ePbvJZ75GJBLBsWPHcNddd+HIkSPYvXs3MplMQCjSKWUPMWcGs8d5cXERjUYjEO4lw9ToFLOvmKFr4VJ2/itDsih+GQpGKHi73S4cxzHBXlwzP1M6xEz8Zpo518L3DI8UY8gZE6yvl0ajgYWFBXMtms1mwCnn7wsFMlPC+Th7oFndwBJ+FcyKsjOYe/wo4rEOEpYKZkVRlF5AwaysEd3Gmwd9JZonJydx5MiRXi/jKth7S5FHEUVBQ8Ejy2ZlIrQUWtVqFefPn0ckEkEmkwGwNr6KoonH6Xa7qNVqWFpawtTU1Oaf+CrJZBJ333033vrWt2Lv3r0YHh5GMpk05dY8dxmyRfeVrm6pVEK9Xjfp1CzNpshjCTc3HRKJhEnfBhAoUeZnwPJquq4AjOjudDqmzJriUs47lmnoUhgDuGo+dL1eN58R3Vy+NjwKi8Fdb0S1WsXU1JQJfuNILlYr+L4fEP8yeV2WmEvYv60oyvZl+i+OIZlowbb0D7UwuZSLC392Nw788PYNolEUpT+48pe37+j+5Z3IG5ZnW5a117KsJyzLOmVZ1knLsn5+9fFBy7L+xrKsM6v/Dqw+blmW9VuWZZ21LOuEZVlvut7FnD59GocOHfr2z2aDkCW5TDSW/aThECsAgX5Xz/OMu9ntdlGpVFAul41LyuPHYjHYto1Op4OlpSVUKhVUKhVcuXIF8/PzPThzYGxsDPfffz/uu+8+7N+/3zjM7KFNJpPmnGXJNM+11WqhXq+bFGs5a5ilxgzbAtZmHVNsy4AtlmXza66BTrL8l33BPF7YLZafGZ8nBTeFtQz8krOhKdoBBAQs5zlfD/V6HfPz86jVambdnDldLBZRr9evcpeJTBzndbl8+fJ1hZApW5PNvBcr/cnUp+5A2m4iYunm2LWw9NooG4jehxUAmPnL25FMqGDeaVxPT3MbwC/4vn8MwIMAftqyrGMAfgnA3/q+fxuAv139HgDeA+C21f9+AsD/vN7FvPjiixgcHLyB5W8OFDQUTO12O9DHyt7WdrttArwohBOJBGq1GiKRiBFeruvi8uXLWFxcNPOp2XMrBXan08Hc3BxeeeWVnpz34OAgbrvtNtx777245557MDQ0ZMqfWR7McCxuHrBcud1um1Az9hnLnme66ixhD5day7FLdJb5M2AtOVoGhsmv+S/FsPyabjePIUPMAJjxTXRtk8mkEdflcjkgXnktZGn19ZZp022mOAbWSsZ5bjIhOxqNwrbtQJJ7u92GbdtmHcq2ZtPuxYqiKMq66H14hzP1qTuQUsG8I3lD0ez7/ozv+8+tfl0B8DKACQDvB/DR1ad9FMA/XP36/QD+0F/hmwAKlmXtvp7FLC0tYXx8/MbOYBOgoJHzgcPCqdlsmhFLwIpAazabRhAxkIoirdVqoVKpmJRkuo2NRgP1eh3ASo/z4uJiT9zDoaEhHD58GMeOHcPBgwdNqBfdYm4eyHNjOXWn00Gz2USj0UClUoHneYFxTQDMBoQUrzJxPBwSxtJo27YBrI0no+CVvcsUu81mE7VaDb7vGzHPXnFZVs334RqvtR5ugsgNA5bsy7LqcNn0teDc7eXl5cBnLlPU5Tq5uSBL/+nsT09Pq8u8zdnMe7HSf1x67E5knP6aLKEoOw29D+9s9D78xuRSLl7787t6vYwN4YbSsy3LOgDgPgBPARjzfX9m9UdXAIytfj0B4JJ42eXVx64LiqJ+wnVdI47oohKKK/aaUjzJubl0XVOpVECEMRSLvau+76Ner6NcLgNYEaH8ejNJJpM4ePAgjh49iltuuQWDg4NwHMf8XJZXy2RwYK1smKJajkbiucsRXdJRZZm0nM8MwDxXvg+PJ0dV8edynBeFLQPJ+BopbHl8WSVAR5obHHw/zuOWIWZS2IfX+Hq4rmvGb01NTeHixYs4e/YsLl26hEqlEhivRVgyTneb5+e6rvYz7yA2416sKIqiXBu9D+8sXvvzu5BLub1ehtJDrjsIzLKsDIBPAviXvu+XpTDwfd+3brCRyLKsn8BKqUoAGX7UL7BXOdy7SyFGoUcXE4ARz47joNVqGaFJl7pWq6FWq6FQKJjybMuyTI8w3efNnlltWRYmJiZw6NAhHDhwAKOjo6asXIpJil06zXSYGQQmhS57jB3HMW6sHNNFxxmAOT6w4hTLcmomacv+ZDkXmUKSnwFFZbvdhud55rgAAr3kwJqQ5/lwDBXPg+/N17EHm58d30/2dF8PrutiYWEBlUoF58+fR6PRQKlUQrFYNP3M8trI8nS+J4PJlJ3BRt6LHaRu5lKVm4Fl4bU/uxOFVKPXK1EUZRW9D+8sJv/kHgxmar1ehtJjrstptiwrjpWbw8d83//U6sOzLDFZ/Xdu9fEpAHvFy/esPhbA9/0P+b7/Zt/33ywfbzQaSKfTN3YWG0yjsfLHihRrruteVSYLBEu5GQIWFmTSEeTXfF0mk0Emk0GlUsH09DSWl5c37TwBYGRkBIcOHcK+ffswNDQE27YDc4dlbzKdZLkZwJFOfJwCmaKXX/u+bwSnFNgAAiXSDASTwjjsKgMwnwNLmvm9dIGlQJeOtrWa1s0RV/yZ7FGXwlWGwgEIiHEGxV0vnuehWCxicXERs7OzqFarqFQqqFarpuw/PCdahpk5joPLly+bOdPK9maj78Vx9F+lz05n8uN3o5BRwawo/YLeh3cY11k9qGx/ric92wLwYQAv+77/G+JHnwbw46tf/ziAx8Xj/8dqYuCDAEqiZOUNuXDhAt7ylrdc79O/I6SL93osLy+bACbP864SKOx3jcViiMfjgeApii8KbwqzfD6PkZER44Qmk0mMjY1hZGQEruuiXC6jVqvBdTevFCQajWJ8fBx79+7F6Ogo0ul0oMRZllHLdHCWoAPBTYCwMObmAp1a+RiFaTweD6RW08GWs5t5fApz2VtMN5jI9GsKZ4pxusZ0ohnqJoUqBT0/WxkWxv8oxOlI38i8Zq6FPdvdbtf0N8tNAdkrzesfnvOtbG82+16sKIqiBNH78A4jEsW5P74Xg3l1mZXrK8/+bgD/DMCLlmUdX33s3wH4LwD+3LKsDwJ4DcAHVn/2OQDvBXAWQB3AP7+RBb3wwgu4/fbb8eUvf/lGXrahNBqNQFo2sCbmKJgpoCgAZW9vJBJBrVZDPp834jCRSGDv3r0YGBhAJBJBoVBANps1vc7NZhPVanVTz3P37t2YmJjA8PAw8vk84vE4IpGIEWnxeNwkSwNr45v4mBzVRCcUWEuY5jxrmVYtQ7d4PF43inS+F8vk6WDLcUwySVsKfF5/lo3HYjHUajUj5llmb1mWKeGmuGZZuDx2PB5Ho9GAbdvmmEwNlyL8eqFQZ991uVw2VQw8LznOitej2+3Ctu2A865sezb1Xqz0HisWA3SEkqL0E3of3kGc+YN7MDqw+dlCSn/yhqLZ9/2vAbiWHfvoOs/3Afz0t7MY3/dx4sQJPPzww9/Oy28Yup3XW05Ld4/CaL1AKdd1kU6nA6OHEomECXZKpVKIRCJoNBqIRCLIZDJIp9MYHh5GLpdDMpk0gVO12ububGUyGYyOjpo+ZorGZrMJx3HMOCRZItxqtZBMJs2IKM/zkEwmEYvF0Gq1jOiT85TliCheV1k2TXHKY8rNCopXKZxluTdFLv/lmvk9XyvdYH91DrQMLqNQ5maA3ADh50Mhz98froNO8fXA3yEep1QqoVarBc6X18XzPNi2bXrDHcfB5OSklmbvEDbzXqz0HiuewNmPHsNwbnM3TxVFuTZ6H1aUncsNpWdvBs899xz27NmzKe/led4N9Z/KdGS6nCy/Zkk2hR5Leeksu65rBGYqlQqMQxofH8ett96KoaEhDA4OIpfLmXnOm0Umk0GhUEA+n0c6nTbl6LL8mecuRaRt24Eeb1l2LV15YG2TgkIXCI6Ukoncss+Z6wCCAViyt5gCmm404WM8RjjtXJ4TX0cHWQpuPp/Cm+XiLPuWQVw3UiHAcmwGw/Ezl73hskyba+Nn5XmehoApyjbk1Q/fieGCCmZFUZReEHEc3GCem7LN6TvRfOnSJQwNDfV6GVdh27Yph5V9qyy1ZmgUsNJzytRrjptqNpsmjTkej6NaraLdbiOdTmNiYgL5fB5DQ0M4evQoxsbGYNv2piaJDw0NYffu3cjn82YeMbAWykWHnf23dHZlOFV4xJR0eCksY7FYwKmlGKYQlcFiLH3n93ImM9dBR1r2WFN8819ZPi/7sQEY8c810GHudDpoNBrmONz8oLvM8+M66KSXy+XrLs9OpVLI5XIm7KxWq5m53sBaGjc3CPgeLCNn1YKiKIqiKIpy83jld+/AyFCl18tQ+oi+/Iu7H8tNPc8z5b8UNRQx/FqWIAMws3PZz1utVhGJRJBKpdDpdFCtVtHtdpHNZjEyMoKJiQns2rULt9xySyCxejNIJpPI5/PIZrNIJBKmz5eC2PM8+L4Px3HMLG2K4HDJNc+ZAWFS8MrZwrJfmRsRsqdZjnuiAKcDLJ9H+HU4cIwbAOGQMPnZhcU7sFZRIPuKZRWBTPWmyL/e/mLLspDL5TA0NATP81Aul1Gv143Dz+fwPbl29mKn02lcvnx5U6sRFEXZHCLpNKyIOhyKoiiK0i/0pWguFovI5XK9XkaAdrttxJpMLmbIFB1WOp90Yyn2OH+XfbK2baNUKqFerwfKsuPxOG6//fZNdRAdxzFBZBwxxZLkSCRiHPZGo2EcW5Yu1+t1I1B5brJ3mUKSDi4d23DCuOwPl4nU6yVIy+AsGTzG/wjTtnl827ZNqTxdbIp7imUp/um4U9TLoDEZ+tVqteB5Hmq1GhYWFq7rmhcKBRw5cgTJZBKNRgOLi4twXddcJ65B9lLz9y6bzSKZTGJyctKksiuKsn145bePYmRQHQ5FURRF6Rf6UjTPzMzg3e9+d6+XEaBSWfkDJjxqCVjr2aWglG4jBU8sFkO1WoXruiZka2lpCZVKBaOjo4H+3/379yObzW7auXE2dCaTMcIyHJhFwcsRSdwMsG3bPMbnydJnbhwwYZquuxyXJPvDKdalGOa1pZAOX1/Z5yyfz+RvKXilkxsOBONGB8u+paNN15yutBTk/F2Ynp7G1NRV4xevwrIs5PN5DA8Pw/M8VKtVUwrO9G65ESM/i2g0ioGBgcDGg6IoiqIoinJziA4MAFrto4ToO9Hs+z7OnDmD+++/v9dLCUAHUYZ/8XuKHYpClhXTCaXArNfr8H0f2WzW9G3XajUj4ghHUG0WuVwO2WwW2Ww2INjoqkrxRpEpHV+eP0O4KCTDJdt0Z7mR0Gw2TXlxuNyaZdkUy+E+YpaG8/pLV5bXnseSQWHNZhOZTAa5XM440PF43FQScN18D1kSLceM8V+edyQSMRsrb0Q+n8fBgwcBrJT91+v1gPMtA9CkA8/3SyaTWFxc7Ms2BkVRFEVRlK3My//PLRgd1lFTSpC+FM1PP/00jhw50uulBJienjaCEFgTYoQiC1jrhZWjkCj+KpUKbNtGLpdDOp1GsVi8qhQ7Ho9j3759m3ZuqVQKw8PDcBzH9AxTkFIQ8rxliTqFrvyZ7/toNptGxMowL2AtHKzZbAbcXDlmitdQ9vJScMt0bBk6BiCwTmDtM+JGAEV1q9UygpPnQLdYOsvhFHA63+z3lnOTl5aWsLi4+IbXOhKJIJ1OmwCwpaWlQO8735sbAbwGwIoTns1mUSgUMDk5uekjyRRFURRFURRlJ9J3ohkAXnjhBdx22229XkYAWVJMcSMFV7j3lj+Px+OmnNn3fSwvL8N1XVMKXK/XryqzTSQSuOOOOzbt3OhesjybfbwAjLvKoDOeF9PACcuv6ZICwaC08NgqPs6RTXw+EJxNzNfQUaYDLWdFc43dbtc4whTZfA1LvznKq16vG3HPNa1X6i3DxbgO2QvN50xPT+PChQuve50ty8Lo6Cjuu+8+RCIRlEolNBqNQNp4uB+ej7MnO51Oo1wuq8usKNuU2O5dsKLaeqEoiqJsPbq+BW8x2etlbAh9KZoBBHpN+wmOl5LhV1JQSvFm27YRX+xVrVarKBaLyGQySKVS8DzP9MF2u124rgvbtnH33XdvyvnYto1kMml6menEsk9Zjn4CYEqm5agp13UDrjI3EmSJM48rHXo+h6XusmdZBoHJ4C2GbtFlluIZwFVhbDy+FMQs8Zbusywd5/txs4Ol3lwLP2eus1qtYnZ29g1HTaXTadxyyy3IZDJwXRcLCwtmHBfPm2KYGwi81rLv+9y5czc0C1pRlK3Dqf/PHh1z8m3S6UbQnEv1ehmKoig7lmrDxuH/19O9XsaG0LeimeKxn6jX66ZUeL25whKWHlNA87mu62Jubg6WZWFgYACe5+Fb3/qWOf7MzAwAmJCtjYZiPpFIXDUWisKYvdqyn5dOKOcUU0wyGZw/p+vLcm9ZYi3FrhSiMnVcJlTL/nDpVstea1nSLcW3fK4s45Y9xPKc6TDza54LS73ZH91qtVAqlXDu3LnXvc6RSATDw8M4cOAAut0ulpeXUavVzPpIuIqBopm/Z7VaDTMzM2YOuKIoirJCzU3gtp95qtfLUBRlCxM7sA9WXKt9lKvpS9Hs+z5OnTqF++67r9dLCTAzM4NEIgHHcdb9OYUae3sJnUKKv2KxCNd1kcvlEIlE8PTTKzsyyWTSCDjHcXDvvfdu+DlRnDMJmuulSKNIleJNpkrLEmwmaQMwYlmKZjrAFIbcVOC1a7VapodaPo+O8Hr90RTSclwVHeJ4PG7KvynW5dgqPs6KAK5DrgmAmfMciUQCmyRMvl5cXHzd1GyWZb/5zW9GNBpFvV5HsVgEANM/zv/k9ZebCjzXqakp1Ov1m/DJK4qiKIqiKJJT/3YMoyMaAvbt0PUtuFOZXi9jw+hb0fy1r32t7xK05+fnkU6nTSm2DP4CguOPwn2qcnRQpVJBsVhEKpVCNpvFuXPnAmFPrVYLuVwO73vf+zb8nCgs5fooTlnqTCFKQdxutwOimF9LsSdnHvPa8Pzk9eLoJhmAFY1GjRjn92EBKZOyZcm3dKkpNOUMbRkwFi7rpnCmw8/Sab5eHo/jt4rFIs6ePfu61ziZTOLw4cPIZrNotVpYWFgwpdwy+Zs91uuljsdiMVOloC6zoiiKoiiK0k/UvQRu+/lv9noZG0ZfiuZut4u/+7u/w7Fjx3q9lADLy8tGwIXHI9HZlGOQ5Kgm+Zp6vY7Z2Vn4vo9cLofl5WVcuHDBJCtfvnwZ6XQaDz/88KadmxwpFQ42k4FncsRTNBo1Y7VkPzdDwngN2IMsS7ylKGdytUzk5vtKocz1hN1n2ZcsS+XDohhAoMyb58BzDW8CSJc9fDxWDszMzODEiRPXvK7RaBSjo6PYv38/XNfFlStXAqOpKJDl+cjS9Xg8bmZLLy8vo1wu63xmRVEURVEUpW/o+hbcyWyvl7Gh9KVoBoCXX34Zo6OjvV7GVYQdZACBmc3h8VH8PhxytbS0hHq9jnQ6jU6ng0984hOwLAuFQgHlctmU9Mbj8Q0/Jzq1UoRK95YiUyJLrLmRQOHLVGkpTvk+dKnj8bhJ6WaPs5wLLQPWpDMdLtPmJoUUmzwXKfrlZoAsC5dl0Czf5nnQPefnyP70eDyOZrOJxcVFnDx5EuXy+mU8lmVhcHAQDz74ICzLMmOpWq3WVeXY8veJwl2eY6fTQalUMvOqFUVRlDU63Qha57b3H2yKoij9ituK4ZZf2L4uM9DHopkkk/0VW86SXWBNBMpyYIor2RtLoRiLxYwz22g0sLy8jHg8jmw2i+eeew6e5yEajWJoaAjlchnJZHLDR09RCIbHZVGsS1FJp1gKVYpYOqV8PoUzQ8YoxDmaigFkruteVf4s1yYTq+XmxLWEswwCkxsczWbTvD97maULTTHN95XXhQKWx+D7zM3N4cUXX7zmtU0kErjllluQz+fRbDYxOzuLarUacMXlaCyulX3TLF1nT3V440JRFEVZEcyll4Zw6Be/0eulKIqyxUlPxlFvbrxhpWw9+lY0+76Pubk53HXXXRv+XuHk69eDfbh8nXRhLcsKpDIDa+ONmLoMrI3TWlxchO/7yOfzWFpawokTJxCNRrFr1y6Uy2WMjIzggx/84E07z/VoNptGuAII9NqGBV3Y0eU5suRZbhjwOsiALtmvLFOow+6wdL15LXltpQsrR1LxNXycjnC43JvQwZbjs/gePOZ6Dnu73Uaj0YDnebh48SJKpdK61zUSiWB0dBR33HEHXNc1glkGiXE9PB+ukddMimZ+Ha5kUBRF2el47SgO/pIKZkVRvnMm/suTqCyke72MLUXXt+CeKvR6GRtO3/4F7vs+XnrpJXz3d3/3hr+XTGS+HpLJZMCFZEmyLAGWYU4UYCxDpmAsl8uo1+vI5XJot9v49Kc/bV7TbreRSCTwPd/zPRs6eqrRaKBaraJer5vSX+nUcs3sq6VTG56TTGSZsUTOPwZg5iTLHmkAgfcNj4mSM5spcFlCLXuU6QoDMJ+TDGST4WJStLNsmi44102hy00Bz/MwOzuLkydPXvO62raNO++8E8DK5sji4mJgjjPnXYfDzWRQGtcfiUTgOA5SqRQSicQNbfIoiqJsZ7q+Be/FQq+XoSiKsmNptaM4+O+2/8Zl34rmbreLr33ta3jwwQc3/L1YWnw9NJtNM4M53G8shdB6ydocdUTh57ouSqUSut0ustksXn75ZVy+fNmUMM/OziKdTm+o285RTJ7nodFomFJr2csrS7XlOcpyar6OgWiy19nzvEAvMQVqq9UyQWDrOc0U8byG3HjgWCqKZmCtdFk62tzM4HnI8Db5GvkYk6llKJocR9XpdFCtVnHixIlrzmaORqOYmJgwFQMLCwtoNptm7BbXKc9NJnOzBUBel2g0ilQqhVwutyl97oqiKP1O17dQeWYEB/7f2/+PNUVRlH6k61twnx/s9TI2hb4Vzb7v4/Tp09i9e3evlxJgeno6UBZMQUXXElgbGxVOf5ZCj4KIgiqfz6NareIv/uIvTF/z/Pw8UqkUfuAHfmBDz8l1Xbiui1arZfqZ6ZBzvFaz2TQuqCyd5nnwa1k6LfuLZc+x7I+W4pCEU69lwBeFO48rS5uJnLMs1yZTtuXrpZhm7zR7t2WfNsX+1NQUnn322Wtez1wuhwcffNB8vtVqNVDuDgQ3alh2LjdbeB1k+BlHlDmOo2XairJNyZ9IoOYler2MvqfrW6g8NYJ9//7JXi9FURRlx9LpWjvmPtzXf3nTpewnZ212dtYINVm6S6TTKUcHhf/lcxcWFlAul5FKpZBMJvGNb3wDly9fNgFapVIJd999N/L5/IadU61WQ7VaNaOhHMcJuLDSAV5P2HHzgP3AdIN5HegmyxA1imcZNEZhSTErZzXLEutwWrfciGAqN11tim6KVDnaievnZwmsbHh4nmdc7nBAV6VSwfHjxzE1NbXutYzFYti/fz9isRhKpRLK5bLZQOE6ubkSHvMlRb50+vn+LNPOZDJXbRQoirI9GPutJ1Er9VcAZj/i+xb2/Yed8YeaoihKP9L1LbjfGO71MjaNvhfNFy5cwOHDh3u9FEOlUgk4sBRTcjQSxwlJ95LCUrqarVYLjUYDS0tLJkV7fn4ef/VXf4VYLIaxsTHUajXkcjm87W1v27Bzcl3XiGbZ50vnWfY4s+yaIpel0lIMMxgsHOTFY3CGM0cpSXErhaTsNZfiGlhxarm+8Gxped1l0rZco3yczi3FPbBWOcBj8PupqalrzmWORCKYmJjALbfcgmq1GtiIoBjm70m4JF2mfRO66jIwLRaLIZlMqtOsKMqOpvG1nfOHmqIom8vAt+KouhuXJ7Rd8H0Le/7zztm87Ou/vLvdLp555hk8+uijvV5KgHa7Ddu2jXva6XSMGy5LtOmkAmvlwRRsfB7n73a7XaTTadi2jaeffhrnz59HNps1QvCRRx7ZsPMpl8solUqo1WpwXdeIxbDLK/u0uS5+z3ORKdCyZF2KQjlSiVA4SkdbBofxmsrZxuH/KKRlWXM8Hg9saEg3ly64DAOTI6/kpkG73cbi4iKeffZZLC4urnsdc7kc7rzzTliWhUajgUajYX7GNXJDgL8bHGMlryOvR7vdNj+X4pu90RoIpijKTqTrW5j4LzvnDzVFUTaXkd/9Buplp9fL6HvcL++szcu+Fs2dTgdf/vKXTQpxv1CpVK4KoJLJyM1m07iYFDnhdGTpijYaDdTrdSQSCQwMDODKlSt47LHHUCqVkM/n0Wq1MDQ0hEOHDm3I+dTrdczOzmJ5eRm1Ws2ISNmHzLVK95PiTY6Ikj3K8pyl+Cbh+cTyX/6c7y1TyWWptuwHpmvNzyPscPPndMiBFaFKh12W1rMqgO9XrVbx6quv4vnnn1/3GsZiMRw7dgyFQsG0FbiuG3DCpZiXfdK8nuEkcp4HhbN08G3bVtGsKMqOxPubkV4vQVEUZUfT+MIoxn99Z21e9rVo9n0fly5dQi6X6/VSAszOzgbcS5n0LJGhUwACwWEsdWa/7czMDJrNJrLZLOLxOJ5++mk8+eSTphQ3Ho/j4Ycf3rBzmpubw9TUFBYWFlCv140rKvuMuVYGZfm+j0QiESixBtb6f8MjlCgIOTYpnIK9XoK2dIGl6Oa1lLOOpWjn+4dHV8k52nwPvr8s5yaRSASe5+Hy5cv4yle+gkqlctW1i0ajuPfee3HPPfcgl8uh2+2iWq3CdV2zgcLj8/rx2HIjgMie8bDbzNFmspRcUZTtxfCXE6g0tDRwPbq+hV3/fWf9oaYoyuaj9+Fr4/716I68D/f9X910+Q4cONDrpRhmZ2eRSqVMYJZMyZZiDEAgZIo9vLLHlTObr1y5guXlZUQiEeRyOdTrdfz1X/81zpw5g0QigWQyiVtvvRVHjx7dkHMqlUq4dOkS5ufn0Wg0Ak4o10nHHFgTvmFBLM9Pis9wGjYAUzotZyJL8S1dYgAmYEwSdqOlk09XXAawhUPbKJrD/dBSbC8vL+PZZ5/F6dOnr7pukUgE9957Lx599FEUCgXYtm16xDkrmuuU14mvlcng7JWXc73Dv0/csNDybEXZvgz8wTfQqOkfa+vR/szOKgdUFKU36H342oz9f3eeYAa2gGjudrt45ZVX8K53vavXSzE0m004jrPu6B8KY5YvU8TJcm75H4OxOM+30+kgnU4jmUzi7Nmz+OxnP4uFhQUkk0ns3r0bDz30EFKp1E0/p263i9nZWczNzaFSqRh3Mzy+iQI3kUiYc5LCWYo/eW0ohvkcinBuKjCVO+woSzeZ7yHL2ynSpZvP48vHZagYv5eutRTZsmy6UqngzJkzeOaZZ666ZpFIBPfffz/e+973YnBwZUbdlStXsLS0ZBKzee5cB3umpRsvy8/DjnNYNMvUcHWaFWX7Mvr5hAbRrMPI7+pMZkVRNge9D19N6y93bntM3//V3W638cQTT+CBBx7o9VICyLRlih5gRUxSCEq3VZZpy7JcOXd4cXERnuchkUggm80iFovhueeew1NPPYVqtYpkMol9+/ZtWI/30tISLly4gEuXLmFubi4wu5llwuGRSOzZlufGEmgKO5ZJS9eYDrt0U8Pim49RKErhy+suBa9M+g5vWMg10s3tdrvGMQeCgWbdbhee5+HChQt44oknrgr/siwL9913Hz7wgQ/gwIEDcBwHc3NzuHjxIhYXF03vsfzcuW55jvJ9+bPwBoDsJZcCWrYIKIqyvch/7JvIfiKrM5tX6Tw2gs5jO/ePNUVRNh+9D1/N8Id27sZl34vmbreLV199Fbt37+71UgIsLCwE3NRweBaFnOzXXa9vVTqI1WoVc3Nz8H0fmUwGAwMD8DwPzz//PE6cOIFGo4GBgQHcc889G3I9Wq0Wzp8/jxMnTuD8+fOYm5szfbRSsMkgLdnvzP/C7qkMuOJGQavVCiRHS2HJ5/G1MtFaHoeEnWf+XJYw8ziy/Dt8PApcz/NQr9cxPz+Pr3/96zh79mxgbZZl4YEHHsCP/uiPYt++fUgkElhaWsLly5exuLhozkmemxyLxWPI4/F54TFlcn4zrxnPVwp+RVG2H7k/+SY8N97rZfQFg7//DQz+/s79Y01RlN6Q+5NvIvunOdSbei/u/tnO3rjse9EMwAiZ8fHxXi/F8PLLLwdcVTqLUjCylJnPobCTwU8steXXs7OzqFarsG0bhUIBw8PDKJVKeOGFF3DmzBm4rouhoSHceeedyGQyN/28yuUyTp06hVdeeQXnz5/H0tISXNcNCFd5rixzln3DMjmcgWIyaCsajQZCrYjcWJCzkelmh0dSyVRumZAdPlar1TLvFRat4d5zJl8vLi7i+PHjOH78eOD6WJaF7/u+78O/+Bf/AocPH0Y6ncbS0hJeffVVzM7OwnXdwPPp0jP5muuTpddcvxT2YSc6vDkTi8XWbQ9QFGV7MfpJB/jYMBo7+Q+2j2kfs6IovSP7Z99E9mO5HX8fHvjozt64jL3xU3pPp9PBqVOn8Na3vhWf/OQne70cAMDi4iKi0SgSiQSazWagbBi4usSYs5nlY7KEF1gRRMViEfPz82ZmM93mpaUlvPDCCzh48CCy2SwOHTqE2dlZnDhx4qafW7lcxiuvvGKc2vWcTa650WgEytOZCi5HK7GMuNVqBXqZeZ2kEy2DwggFreyLlgFfUnQSOROb15lCm+8nBTvDxFqtFiqVCk6dOoVvfOMbqNfrgbW8613vwj//5/8cyWQS6XQaCwsLOH78OC5dumRSx2OxmOlpl+XV8trJkVjydyTs2nNtMnm93W4jkUgE+soVRdmepB97CgAQbzyA6o+XYMfbb/CK7Uf+Y9/s9RIURdnhZD7xFCKdnXcfjvzhMOCvbBzsdLaEaG42m/jCF76Af/AP/kHfiGZgRcxQuNBBZAhWs9k0Yjos1igAKS4p4JrNJlzXxZUrVzAwMIChoSHYto3h4WHUajXMz8/D8zwcOHAAhUIBd911F2q1Gs6dO3fTz21paQkvvvgiOp2OmVucTqcRj8eRSCSM0PM8D47jmHOXIVwUtnSY2RMtHXrgaoeZx5EOvfyP15J90mG3W/Y1yzJyKdBlD7aclT03N4dz587h7/7u7zA/Px+4Jj/0Qz+EH/7hHzYbA51OB1/96lfx8ssvY3l52Tjv/F2QopniVqZ7yznQ8nz5PGDld5/iX6aMyznZMqVbUZTtSepTT8Hq3I9uzELj/1xGPNp94xdtA2IfHgJw9g2fpyiKstHwPlz9YAmJWOeNX7CFif7BMKyOj/QnVSyTLSGa6TT/3M/9XK+XEmB2dhYDAwMBl5OpyRRLzWbTOLRytq4c6RTueS2VSpiamoJt20ilUkilUhgbG8OlS5ewvLxsjjM0NIR7770X7XYbr7322k09N9/3sbS0hJdeesk8Njg4iGw2i4GBASQSiUDwlnRTWZYtS6jlOCg67mFHmY/JTYXwKCj5Nb8H1txbPkYRKsul+VrZc9zpdFCtVjE9PY1Lly7hueeew/LyMmZmZgJr+8AHPoAf+7Efg+d5yOVyyOVy+OIXv4jnnnsOS0tL8DzPbC7ws+X7ADAONNfBnmuZ1M0RXPJayPMP90X7vm9+F8I94YqibD+Sjz8NAIh698OPAu5PLCNi+W/wqq1J/PeGYHV9JB9/qtdLURRFMSQffxqR9ltQ+anStty8jP3+ECItH8lPPwX42/P/X75dtoRoBmCSnPft24eLFy/2ejkAgEuXLmF8fNyU40rXWLqc4V5fCqhOp2PcaAqjeDxueptt28bBgweRSCSQyWQwNDSEhYUFFItFRKNR7N27F+Pj43jzm98MADddOANAsVjEiy++iFarhd27d2P37t1wXRftdhuO45gUbNnDzGsRj6/0fkgBHQ5Mk0IZWBO/fI0sCZeiWCZTU1AS2S9MpFCu1+uwLAvVahWzs7OYnJzEc889h1KpdJW7DAC/+Iu/iEcffRT1eh2ZTAYHDx7EZz7zGXzpS1/C/Px8QCTL/m1+pvys+TWRZefcdOBmhAw0s207cN34Ouk+K4qyc3D+akU8R5pvQf1nittCOMf/1xCi7tofoPZfP61/sCmK0pfYn30GVuct8CNA/ae3xz0YWLkPpz/9LPz2zik/vxG2jGhut9s4efIk3v3ud+NDH/pQr5cDAKjX60ilUqavGUDAFWR5MkucWa4bDtKSz+Hra7UaZmZmkM/nsWvXLiQSCQwMDKDdbmNhYQHLy8tIJBIYHR3F6OiocZynpqZu+nmWSiW89NJLuHTpEvbt24eJiQmMj49jeHgYuVwO7XYb2WzWpGKHx0eFU7Bl/6503MMbC+HHKYK5MSGdenk8vh83LYC1MmcK48XFRUxOTuLVV19FqVS6aqQU+ZVf+RW8733vQ7FYxODgIPbt24cnnngCn//85zE9PQ3P8wL96Z1Ox7i/FOoUuQwCowimyJbXi8/ha2X/N68dsFZWHp5jrSjKzsH+62dQ/5nber2Mb5vY7w8hUV75/4XEl/QPNUVRtg6J//0MACDS/C4gYqH+L4u9XdC3id6Hr58tI5o9z8PnPvc5/NzP/VzfiGZgxdV0HAfVatU8RkEnS5hlP6p8jnRcpejzPA+VSgVXrlxBNpuFbduIx+MYHByE67oolUqYnZ2F7/sYGhrC+Pg47r//fjz//PO4dOlSIO36ZlCr1dBoNFAqlXD27FkcOHAAExMT2LVrF/bt2xdI0mbStSyzZqVAKpUyJewyJVqmWcvRUfJrGfzFTYbw6CkAxsGn+9toNFCpVDA/P4/z58/j3LlzWFpaQrVaRaVSueY5/+qv/ioefvhhzM7OIp/P44477sBnP/tZPPbYY7hw4YJxjpkuHu5hZvAZP28pbrmpIsU+e5fl+XDzgK4952UnEiszA2W5tqIoO4/cr2cBANV/U+7xSq6f2O8PwVlsIfbkcfieBwDYHj6Noig7jfgXnwUAZFpvArB17sV6H75xtoxo7nQ6uHjxIgYGBnq9lADnz5/HyMiIEU+2bZtybZbkSjEcFkWyH1UGitGppAvKcVvsb/Y8D41GA1euXEGr1cKuXbswMjKC++67D+l0GhcuXECtVrup50oHvFaroV6v49y5czhy5AgajQbK5TIKhQIymYwRguzb5cilcOhZuOwaWOv95XWSc4vD46vYn8xQLr4HU8jr9ToWFhZw8eJFvPLKK0Yo12q1N9xU+Nmf/Vnce++9qNVqGBsbQ6FQwOOPP45PfOITOHfuHFzXNWXXUtTzHCKRiAlMA2Cey4Ru+dnLud4MNwuXn4dDw4C10m3bts2xFUXZWUSfeA4AkG3du/KAZaHyb6+9GdhLon8wjNS0i9i3XkDXdfUPNEVRtg3mXty+r2/vwdYfDSNzaSXrR+/DN86WEc3ASontwsIC7rjjDpw8ebLXywEAXLhwAXv27DFCLzwPmH2ndEaBNddRhoZJ8SzLdsvlMmZnZ43bHIvFMDg4iEqlgmq1inK5bERVoVBANpvFnXfeiZGREZw8eXLdHt2bQb1eR71ex4svvohisYg9e/ZgfHwcExMTyGazyGaziMfjsG07MCqpXq8jkUiY0nX+TJZSy8RuWdZN51imVPPnrVbLuPOlUgmTk5OYnJzEhQsXUC6XUalUrtt9/6mf+ikcO3YMtVoNhw4dQjKZxGOPPYYvfvGLmJqagud5AVEvP8Nms2n6lhOJRKCvXZaqU+DyM2fqOp1qXhv2xksxzWtJ95kjvVQ0K8rOJfK14+brXOce+BELlX9XvfYLNpnIR4eR++yL6NZq2H7ROYqiKCtEvvo8cp17AMtC+Zf74x7s/8kI8ufqiL54Et3VCku9D984W0o0e56Hr3/963jf+97XN6LZ8zy0Wi04jgPXdY0wku4pXUTP84zA4bgg9gDTQaVDTWHZ7XaxsLCATCaDiYkJOI6DWCyG0dFRLC4uolwuG8e5VCphYGAAuVwO+/btw8DAAF5++WVcuHAhkCJ9M6nVajh//jwuX76M0dFR7NmzB4VCASMjIxgeHkYqlUI+nzeCNxaLIZVKGTHIcVWNRsMIZW48cCSXdOllX3Oz2YTneWg2m2YD4cKFC2Y95XLZfCbXQzabxQc+8AG85S1vMf3i8/Pz+PKXv4yvfvWrmJ2dNZ8PP0e66RS6TMTudruwbfuqnms64bIkfb1ebtnHLR1pOctZ9m+rYFYUhVhPvgALQOH/vss81nFim+p+dP98BIMn1/5gtE6tCGZFUZTtjvXkCwCC9+BuIrrpIpr34cgrJ9Epl1Uof4dsKdHsui6+/OUv4z/+x//Y66UEmJubM+4vsOKaSreUwphuYSKRCDipYYFN15oiutFoYHFxEel0GrlcDtFoFIVCAbt27UKz2TROZKfTgeu6cF0XhUIBQ0NDeMc73oHFxUWcOHECk5OT8Fb7Fm4mnEnNGdOxWAwDAwMYGxvD0NAQ8vm86T/O5XIoFAqwbds4xgxSo3PK1Gw5fomPN5tN1Ot148IzTXxhYQHT09NYWlpCvV431/x6+Z7v+R7ccccduOeee1AoFGBZFk6ePIknn3wSk5OTRjBTsMpZyRSw/MxZji5neMv+ZVmFEB6zJUO/6Izz94biW/Yws9xdnWZFUcL4z7xovo5YFgq/fCzw8+ZI8qaG1zQ/M4LRb67080UmT6FTLK2t5aa9i6IoytZA3oOtde7BrUEHtV8ohV92w9T/dhTjX7r6OLwPb++J0pvHlhLNvu+jUqkgnU4jlUqhXq/3ekkAVvqaJyYmEI/HjYiVAokih45krVZDLpczziTFER1IAIHy3Wg0ikajgfn5eZNYDQCFQgGVSgVzc3OBcUyVSsX0PB84cAD3338/HnjgAczPz+OFF17Ak08+iVLpO/8faRgmQgMrDjQFNAVzMpnE0NAQHMdBJpNBLBYz5dupVArA2oaDbdvGxbUsy4js2dlZzM/Pw3VdLC4uolKpmPfla2+EO+64A29+85tx4MABDA4OolAooFqt4sknn8TLL79srq2sIGBJNSsCGNwmQ8rorDOwS/aqs8+b10o66OGKAzmbWZZu87nAyqaFzmlWFOV18X34zwcrtBLxBOzpQzftLayp0+gsLwOA/pGmKIoiWeceHI/FMDB763d86MHZM+is046p9+GbyxuKZsuy9gL4QwBjWNks/pDv+//Dsqx/D+D/AsBP6d/5vv+51df8WwAfxMrn9XO+73/+Zi3Y8zycO3cO73nPe/DJT37yZh32O6LZbMJxHDiOY3pYZZCTDIFiKS1FFrDWkwrAlOjKIDG+R7FYNPObbdtGKpXC0NCQ6dflmCXf91Gv11Gr1eB5HhKJBN70pjfhvvvuwzve8Q784A/+IL71rW/hk5/8JBYWFjbkmtB5lc42nWFeGxmMFk4XD5cuUzRSbMoy5xslkUjg3nvvNaOzxsfHEY/HUSqVcP78eUxNTeHMmTMolUpXlVGzXFrOpJbjn2S/unSm+Rp+PqwmYIk1gIBwlt9TZIeTximuZaWCsj3pt/uwsj3wW034L53u9TIUZcug92LlZuK323oP3kJcj9PcBvALvu8/Z1lWFsCzlmX9zerPftP3/V+XT7Ys6xiAHwFwB4BxAF+0LOuw7/s3ZcOjVqvh8ccfx4/+6I/2jWgGgMXFRTN6aj1XUPapUhRRDPFfOot0WmVfrO/7pnc5nU5jfHwc6XQanuchk8mYkU50Jl3XNQnVzz33HBqNBvL5PO666y7s27cPDzzwAH7iJ34CL7zwAn7lV34F09PTG36NWILcK44dO2aSsFOplHH+Z2dnUalUMDMzg4WFBXS7XTQaDdM3DcCEdMnPlsJZ9izLsnuK4nDJNWc5JxIJs6kgZ06TcD90GJlKrmx7+uo+rCiKskPRe7Gi7FDeUDT7vj8DYGb164plWS8DmHidl7wfwJ/6vu8BmLQs6yyA+wF84yasF61WC+fOnUM+n78Zh7tpPP/883jrW9+KWCxmhBDFkRwxFB4ZFE7WBmBKeoE1p5VjjMrlsumhdhwHiUTClGk3m03jOlJsua6LdruNF154wfTgfvd3fzcsy0K1WsUP/uAP4h3veAfOnj2LX/3VX8VTTz0VSLLe6mSzWdx9990YHR1FPp83wWGlUsm4wNxgmJ+fR7VaDYx64rXgZgSvq0z/lu6z67rmM2UFAV9D+Hmytx2AKUMPz1yWTjp/B2QgmLIz6Lf7sKIoyk5E78WKsnO5ob+6Lcs6AOA+AE+tPvQzlmWdsCzr9y3L4gDlCQCXxMsu4/VvKDdMvV7H9PQ03vzmN9/Mw35HeJ4Hy7KMkKUryXJd9uhSfLXbbTSbTRPkxPFCLDtuNpsmJEuW/VLcLS4uGvHExGw5BolOaLvdhuu6KJfLePHFF/G//tf/wqc//Wl0Oh3s3r3b9BM/9NBD+MQnPoFXX30Vn/70p/GP//E/Doj3rcaRI0fwfd/3fXj00Udx2223IZVKYXl5GcViEUtLSygWiyiVSlhYWMDU1BQWFxfNhoOcuyxTr3k9KWRZng2sbVJQMPP1DAbjhkYsFjNzvDmeSoZ78TiJRMIIaTnHGwgGo8nSfmVn0C/3YUVRlJ2M3osVZWdx3aLZsqwMgE8C+Je+75cB/E8AtwC4Fyu7bv/tRt7YsqyfsCzrW5ZlfetGXgeslGh/9atfxfvf//4bfemGsri4aMp4ZZk0H5PltxTOwIrLGHahWdrNsu1ms2lEmud5WFxcRLW6El3vOA5yuZwR3QBM8jR7XtvtNqrVKi5duoSPf/zj+PVf/3VcuHAhEM41OjqKAwcO4N3vfjd+93d/FydPnsT73ve+gAvez6RSKdxxxx14xzvegaNHj6JQKAAArly5gqmpKczMzGB+fh7Ly8uoVqsolUoolUpGwFL0slIgkUgExkbxc+12u0gkEnAcJzDyiZUFsl+Z7nAkEjGfCX83+HnL5wJr6eHcgCF0tVkiroJ553Gz78OrxzT34hZufrq+oijKdmMj/ybW+7Ci9CfXlZ5tWVYcKzeHj/m+/ykA8H1/Vvz89wD81eq3UwD2ipfvWX0sgO/7HwLwodXX31CaU6PRwAsvvIB/+k//6Y28bMM5deoUHn74YRSLRRPwRFeRZduyr5lCzXGcQLlvPB6H67qB/memNHO009zcHAYGBjAyMgIAyOVyyOfzphybYo/lwhTodOmLxSJOnjyJhx56CB/84AcxNDRk3svzPORyOQwMDOCP/uiPcOnSJfzkT/4knnrqqb4aazQxMYFbb70V4+PjKBQKiMViaDabaDQaKJfLJiCNX3P+s6wK4LgsClr2KHOTgp9Bq9UygpghXzwGgID7zE0SwpnbFLvhkDMKYABmLrXc9JDIgDmejwrnncFG3IdXj2HuxTlrsH/+B64oitKHbPTfxHofVpT+5HrSsy0AHwbwsu/7vyEe373a2wEAPwDgpdWvPw3g45Zl/QZWQg9uA/D0TV01VoRztVrF0aNHcfp0fyTPUcSk02nUajXzmCzBpcMoxw1R5LJ/li5zWAzxcd/3UavVMD8/j3w+j1gsBsdxMD4+jlqthnK5bMQfRz7RDQVgZhxzbNPx48fxzne+Ez/yIz+CTCaDdDpt3juZTOLWW2/FRz/6UZw6dQp/+Zd/iT/+4z++4dFO3y6xWAwTExN48MEH8cADD+Do0aMYGhqCZVlmPnOpVAo4x/V63bjyFMXlchmWZSGTyZjrT8eXz5ViV15rOrpy/nYikTAhXHSoGRxm27Y5hm3bZuY03Wh+7hxNxmNz0wSASVuXJftSiDORXdkZ9Ot9WFEUZSeh92JF2blcj9P83QD+GYAXLcs6vvrYvwPwTyzLuhcrkfsXAPwkAPi+f9KyrD8HcAorKYM/vREpgeVyGV/5ylfwnve8p29EMwBMT08jl8uh0WgAgCnppXsow6RkT6zjOKZM23XdQJ8rj8NRSxRg8/PzyOVyGB0dRSwWw9DQEObm5lCv1837AAiUD1NE871LpRJOnjyJyclJfOpTn8Idd9yBH/iBH8D9999vxHi9XsfCwgJarRa+93u/F/fddx/m5+dx8eJFvPbaa1hcXMTJkyevvhjfBgMDA3j729+O7/3e78Wb3/xm7NmzB6lUCrFYDPF43AjdVquFyclJc/08z0Or1UKr1YLneahUKsZpZtCXDPiiEHZdN3B9pQAmzWYTiUTClFDbtm0+g1arZWZN0yWmAKfDT/HL6y4rDuRnwu+j0agR0YRVC/IzjUQi8DxP07N3Bn15H1YURdlh6L1YUXYo15Oe/TUA69V/fu51XvOfAPyn72Bdb0ilUsGXvvQl/OzP/uxGvs0Nc/r0aTzyyCNG/AJrgVGyN5hiRzrPdJvpEjMATJZ6k06ng1arhWKxiEwmg2QyCQCwbRvxeBye5wVcbopLlmwTCsBYLIZGo4EXX3wRZ86cwYEDB3D48GEcOHAA+/btMwKuXC4bNzeTyeD222+H53m4++67AaxUAMzNzWFhYQGzs7MolUrrXifHcTAxMYF9+/bh0KFDOHDgAPbv349jx45h3759xkG/VkK07/twHAepVAqLi4soFouYm5vD8vIyKpUKisUiXNcNBGu1Wi3UarWAuwvACN5Wq2XENIUsnWN+dnR4uS7Zq8xjyTXKudPsUZfl2fJ7CV1u+V4U1LIvXkuzdwb9eh9WFEXZSei9WFF2LtfV09yvNBoNVCoVjIyMYH5+/o1fsAl0u13E43HTp+y6rumFpcNJ1/Nac385PxiACQLjcaVI8jwPCwsLSKVSxm12HAe2bcPzPOOWyr5YirdoNIpkMgnHcTA0NIRcLgfbtpFMJlGv11Gv1/Hyyy/j7NmzJmQsHo+j0WigVCphZmYGV65cMe54Mpk04VX79u3D4cOHTW9wo9EI9Gbn83mMjo5iZGQEu3fvxtDQEAYGBjA0NISRkRGzAfB6tNtt1Ot1TE1N4fTp03jttdcwNTVlStNbrZbZHJAzlGWYF0O8+LnI4C6GdEnRyn5zXld+prJMmkJb9h7zcX6W/J6/L3JNsrdZpqGHw9gSiUSg5F5RFEVRFEVRlI1hS4vmSqWCl156CR/4wAfw27/9271ejuHChQsYHx83ZdaEziLFlBRW7FulYGaCsxRMTMFmmTKwIpyXlpaM69pqta6a1wwg4FhnMhlkMhkT9jU4OAjbtpFOp80xOTaLwWIUkByFxTFaS0tLZm10f2OxGFKplHFC4/E4ms2mEfSO46DT6aBWq6HdbiOfz2Pfvn0YHBx8Xee00+lgcXHRlIWfO3cOk5OTmJ2dNaOkarVawKHmtQVW+rNZUs31UyjTXaZIluX0PGeK5GaziXg8bj5LAGYDhNet3W6bXma6/twgoUCmmJdr4mN0yfm5yZJ+9k5zbSqcFUVRFEVRFGXj2PKi+ZlnnsFP/dRP9XopAc6cOYNDhw4hkUig0WiYBG1gLeCJYimc3Ow4DjzPM4KOQps/p/Ci+GJfciqVMiOSKNyANdHc6XSQSCSMYKZYzuVyZhY0hRgFJQUse4Vt2zb9xXv37sWBAwewvLyMcrmMqakp817yvSk8M5kMUqmUGdPU6XRQqVRw6tQpTE5OIpvNYnh42IhtAEZcV6tVlMtl1Ot1lMtlzM3NoVwuo1gsolgsol6vo1qtGoEvRzxx5JfjOMYV5vVlSXytVgtcV24A0N1n2Xar1TLXipseFNfc9EilUoFgr0QigWaziVarZRx06W7ze35msi9afn4UzXS6pVhX0awoiqIoiqIoG8eWFs3AiqCr1+uBxOpeI/uGZagUXUOZyMxgK4qjarUaKBWmuJKOM48DIDAmyvd9I07L5bLp041EIqZ3GVhxXNkHTWeYx6LoZOAVxaZt26jX6wBgjhmNRjE+Po6BgQEUCgUjbKWLyjJwjsBiGTdDuBio5bouKpWKEbq+7yMej6PT6aDRaKBYLKJSqaBWqxlHmc67dNZl6ngsFjPin8+VZfAM0uLa6PTKhPJkMmkc6na7jVarZVxnjgXjpogsx+c1BlZKqV3XNZ89xbR0qmXPO38uA74oqmV5fTiwTFEURVEURVGUm8+WF82lUgkvvPAC3v72t+Ozn/1sr5djePbZZ3HPPfeY8lkKNvY3y5JcYK10mwKWTihToSnm6FLKECmW87ZaLeTzeRQKBROEJccqsZyXYjVcCs7UZrqrcjYxxTvTtOns0nVNJBIYGRlBvV4PJIczhCsejyORSCCRSASCyXi+vu+bc+U4sWazaZKwKZqbzSbq9XpgRBNFMJ14KU75M34fi8WQTCYDpc8Uvfye52TbNlzXRaFQgOd55ud09Cnu5RgqWZotA7wIHWvpILOMm5+tDBCTYV/cTOF7ep4X6F1XFEVRFEVRFOXms+VFc7lcxre+9S38/b//93u9lACVSgWDg4Mol8sol8sA1mb/Mqma4iwcFkUns9lsmuPJsmOKQdu2TVkwS5hHR0eRz+dh2zaq1ap5X4qyWq2GbreLbDYLAMhms6ZkutFomPJglk9nMhk4jmNKkykyU6mUEbUUnXSWbds2x+IGAQWuFJQUvJZlmRLmer2OYrGIxcVFM0uas5YplHnOPLYMxKK7nkwmzUaCvK4AjNink8/ryXNwXRepVArRaNT0I8sAMCligbUEbVkNwN5nOaNZutS8HhTxPEf5+yDdem4IyGoDjt5S0awoiqIoiqIoG8eWF83AyhzdpaUl45D2C7Ozs0ZcUtxQaLEEl4FPFH50kGUQlZwlDASdSwq4breLRqOBer1uhCtFKUUa+1+r1SrOnj2LpaUlDA4OYmhoCIVCwSRh0yFNpVIAYMSn4ziIRqNoNBqm3xgA6vW6EbOcQcy1s3ebyd8cb0WxWq/X4bquSeVeWFhAuVxGtVo1qdsypZpikZsHDCXjtZMiGID5fZCCVgp2GcRGsc2ycMdxjDBl+JrsMZefJTcOuIlAJ1r2o8v3BNbSuHmNPc8L/IxiW44nk6FxFNmKoiiKoiiKomwc20I0l0olTE5O4u1vfzv+5m/+ptfLMTz11FN49NFHUS6XAz2zFMScyUzxJx1T9iDT5eTzKdyIFNy1Wg2NRgOFQgGZTAZLS0tGWMnEbjqvy8vLKBaLmJ2dxeDgIEZHRzE8PIxcLmfKk1myzR5bOsUU6bVaDb7vo1KpwPM8I6TpUPMcuGnA4DL+u7i4aL6n6GdJuyxL57XjBgCvI91nivRKpWJeC8AIT7khwLJ0XkuKctu20Ww2AyXQqVQq0AvN18k+dFYFSAErXWquTZaI87PjOnmOTBsn8rNmv7TOZlYURVEURVGUzWNbiGbXdXHlyhXccccdvV7KVdRqNcTjcVMqzfFFMhCKqdVS0NFtprvLwCoAgfJdCjqGWpXLZeTzeYyMjMB1XczNzRnhTRFH0cxjua6LUqmE+fl5jIyMYNeuXXAcB+l0Go1GwwhCGU4me4+ZEk5Hna6qbduwbRuRSARLS0tIJpNmtjZLymVwGM+L50lnXPZ9c80UtrIcmv/xvVnekbvx5wAALqhJREFUzmvKzQo6ySzHphBnfzHdcl4rjvGSrrksqae7LJGBasBKEjjXI4PDuKEQdtTlqDJeC5nWzffQ0mxFURRFURRF2Vi2hWgG1npI+61E+/Tp07j77ruxvLxs3GLZ/yt7baUjSeFJYST7aWWZriz7bTQaWFxcxNDQEAYHB00/cKVSMWJXzvyV6c10d4vFIs6fP4+xsTET+sXRTNVqNSBWeXwA5rFEIgEARhTLfmjHcQIBYo1GI5AgnslkjCAsFoumPFsGY0mRTKQrzTAuXl9Zns3ScF5finD+3lCUSnh9WfKdTqfNhoMUxdy8YDUABThL6ynWpVjmMfhZ0sEP97lz/Fj491r7mRVFURRFURRl49k2onlqagpf/vKX8cgjj+CLX/xir5djKBaLSCQSKBQKpn8ZWAsF63Q6cF03MMOXTqNMiKYQozijKyl7e33fx+LiIhYXFzE6OoqRkREAwMLCAmq1GqrVaiC5Wjq5TGamqL9y5UrAyaao5MziaDSKer1uSrgpGOkcO45jXsNz4UgmikYKdj4nnU4jnU7D8zzU6/VAWrTsRyb8GUuvCYU2e4bZ1y0dXhkCxucxoI3XnT3ddJqTyaQR9bL8m+fGdHGuR7rPsuSbbrEsM6fTTcHPay+de4puRVEURVEURVE2j20jmtkvm8lkcMstt+DcuXO9XpKhUqkgl8uZ8CbHcYxwi0ajSKfTgbRs2f/M8m2ZDk3xynJuCkIKvmKxiHK5jHQ6jZGRESQSCRSLRWSzWdOLzD5rzkGmmyoTmllaTiEPrIR+DQ0NAUDgNa7rmlRsinEKbopBimsAgXFbiUQCmUwGjUYDjUYDrusaN1qO2aITz80CuQYKTZZR87h0y3ldZQk3Xy9dZ5aycwOBQjk8i5mPseyaYWEAzNzndruNVCoVGGvFjQSug3OwuYkgy7XlRoAMkZPPVadZURRFURRFUTaWyBs/ZeswOzuL06dP453vfGevlxLgqaeeMi4hxROhCKTYY69trVYDAPM9hR3HQ8n5vexLlmXTFMWZTAZjY2M4cOAADhw4gNtuuw179+5FJpMxDin7jinuWC5t27Ypt6YITCaTgTFOdFzZhwys9R5Ho1E0m00TGCaddhnC1el0UK1WUavVTBDYesKY10H2VvP8eZ1isRhyuRyGhoaQy+UCPycUt3TwGUImy6YzmUyglJ1rqNVq5ljNZtOM8JKbHCxN5/oowGXAFwU931OuUc53pvPMqgRuvHieF9hMURRFURRFURRlY9g2TjOpVquoVqs4evQoTp8+3evlGObn541IlOJRpjsDCJRCy+fSeZQlzXRJZco0sCLmSqUSRkZGEI1GzRilRCJhSoer1aoRd81mM5DqTLHIecyEwpVCV6Y4x+Nxs1aWHrMnmTOTec6e55kRTrwG3Dygeyq/5jnTGed1kGuIRqNwHAepVMo4xdxMYLCX7P/mBoMcg0Vhy/Wn02m0Wq3AWmT5NEvS5RgtutoUw+GSbukY83rwHFhhEO5fl591JBIx7rmKZkVRFEVRFEXZeLadaL58+TJOnDiBBx98EK+88krfiIqXXnoJDz30EBYWFgJuM11Kfk2xR3HHoDBZ0itLtmWQFEVou91GsVjE8vIyksmkEXMUu4lEAuPj43AcB+VyGcvLy2bEValUMmXHyWQShULBjLPyPA+lUsn02cZiMROMRTENBFO9rxV0xtCwdrsd6OflsSic5SaB7APmZoPv+0ilUkilUiZIjH3TjUbDzDjmZgTDtmRCNa8Py9fj8TiazSZs24bneeZ84/E4HMcxLq8Ma6PY5dcU0Tx/fmbSuaaIlucDwJSxy40SltVHIhGkUqmr0rUVRVEURVEURdkYtp1oBlaCr1zXxfd93/fh85//fK+XY4hEIigUCsYNZx9zWAAlEgkzykg6rXRx2Qcr3VbpxtJBnpmZge/7yOVyRsTxOACQz+cxODiIfD4Pz/NQKBTQaDRMWbZ0ULPZLEqlknGoWbYtndhEIoFOp2PEIQUsR2vJEnOeZ7j3Wc4t5sYBBS6/57kPDg4inU4jlUoZockSbwpnClGKXl5zuXHBXmjLspDJZIwrzhJ5eV1Zvi77lGVZO7CWpM2SdQpqil/ZC87zkc/hpoLv+2bUGEu/uaki08MVRVEURVEURdk4tqVonp2dxZkzZ/CmN70pMP+313zlK1/BI488gnK5bMQRXUmKQjmyKRyeJcVpWATLsmOWUc/NzcHzPORyOWQyGcTjcSSTSSO+OPKJQV6NRgMDAwNGHNbrdRSLRSNmXdcNjKCi2JS92hyzRNeZZc8ATJk0KZfLAGB6eSkaWXJOEQqslTBTPBYKBYyMjMBxHLOuRqOBpaWlgFi2LMuUgssRUXKsVzweNy5yeMwWxSxd+nq9jmQyadx0niedZ36O4XVzPXxffm502OUsaj4uU7Sj0WggCExLsxVFURRFURRlc9iWohkAzpw5g5GREbzvfe/DZz7zmV4vx1AqlQKJzrIUN9wvTEeShIPEmKJNsUYBzFLfZrOJYrGISqVixNzg4CAymQwAmH5cCvhSqWRcWtu2UavVTKAYXe9wWBWRCdAUc0wDp8CnW0vkDGQpaHkesr8XABzHQTabxeDgIIaGhmDbNhqNhunhptilCJUju+Q1ZCI4HeFoNIp8Pm8SyGUPNOcrsyxejoyiEJfXQ1YF8HzoTHMzgCXy4Z5mfs3rFXaiZUI4Z2YriqIoiqIoirKxbFvRXC6XceXKFdx3330YHR3F3Nxcr5cEADh+/DgefPBB089LESQDtCQU0FIYyz5nOq8AAoFiwIpw8zzPiDYAaDQaZjQUBWMymUS9XjdiUZZah+ckc31cF93heDxu+ofDwWQUyixFD7vnsrScTivPg+fqOA7Gx8cxMTGBTCYDy7JQLBaxuLiIhYUFFIvFgGMLINA3HR7Z5Ps+EolE4Hqw9JlJ5zx3Xn9er06nY1LM6UjzHJnI3Ww2zTWjyOaGBzcqwmFmcoNA9jKHk8LltVUURVEURVEUZWPZ1mlCJ0+exNTUFN7znvf0VXDS3NxcIFU5XIobFkpE9szSrWR/MJ/L8wwLRcKeY8/zzJin5eVlVKtVtFotUxYty3/ljGiKYopljp1yXdc8J5lMBsK8ZF/yejOcuS6eo3RxHcfByMgI7rjjDhw7dgy7du0ygnl6ehrT09MoFotmZBTfE1gLHiN8nOfjOI7pO2afOd+Tx5Gl4izl5iaCHA3F9dMlZlk6NzZk/zRHfck1cV0yQE1uKMjje56nLrOiKIqiKIqibBL9oyQ3gFarhbm5OcTjcRw4cKDXyzGcP38enU4HqVTKOKkUaHQ35dgnijVC0UeX2XGcQN8u05j5WDQahW3bRpBypBX7ki3LQiqVMo6qTKiW6dy2bRv3miKZx2VPNfuAZeAXHXA+V/YZUxgyvZtikpsCY2NjuP3223Hrrbcil8uZgLNz585hamoK9Xo9MAbKtm3Ytn1VYJYUmXTI6YhToMrrLkU7hWsqlTLnKAUty9B5DGCt71j+jJUF8rylYyx70nkMAObz4u9KuGxfURRFURRFUZSNY9uWZ5Pjx48jn8/jbW97Gy5dumRCqXrNyZMn8V3f9V2mv5lCUpYEs78ZWOv/DYs1WfZLQQWsOc7SeQ2HTcnjcNSTZVmo1+umRFn2IsdiMWQymavcW64BgBmLJEuXec3lGmUAVqfTMe9FwZ5MJjExMYG9e/dieHgYwMo4scnJSSwtLcHzvED/MgU+v2YwF51ZWe7N9fJ9fd+H67qmJB1YSfZmaTfXy75mXk+KYG5s1Go1uK5rnHamhktRT8EuNz54DXk9AQRmZst5zQxwU6dZURRFURRFUTaHbS+aAWB+fh7Hjh3D/fffj69//eu9Xo5hYWEBjuMESqjp2FL8UhTKcmGW+DLsS/bIhntxgaC448+63a4pwZZ9yjx2eMwVsOaoUryxj5nIEm35vpx5LMO9ZCI1xS2d81wuh/HxcRw4cADJZBKu6+Ly5cs4e/YsKpWKeQ++D3uSm82mCTFjv3AsFjMl2LIUWjraAJBMJk0pthTh3JAIl33zuocTs3k9E4kEqtWq+Uxln7ccWRUeH8VryzUzgE06y41Go28S4RVFURRFURRlu7Oty7PJqVOncPr0aezbt8+UDPca3/fx2muvodVqmRFSTFaW7q10jmWgFEuJKZxZMh12h+X4JSnQ5LxgPsZeXjqustxblggDuGqtdGQBXOWWU8yzHzhcKg3AlJAPDg5i//792LdvH5LJJJaXl/HSSy/hpZdeMoKZLjPFKjcKZJk7HVzXda+a9czXcoNApnVbloV0Og0AJhAtmUyaz0AKX25ceJ5nQs7oXrMMno9TaPO9LcsKBMDJ6yHnVYdLuNVlVhRFURRFUZTNZUeIZgB49dVX4TgOHn744V4vxeD7PiYnJwOp13JWMd1MAEZAS9EvRylRGEuhSxFJUUkxRrFG5PPZW0w3mMISCM4cjsfjcF03MGIqPIqJo5oozmVpthSvFJgDAwPYs2cPdu/ejXg8junpabzyyiu4fPmyeS8+Pzw/mWvkWtgzTSdclqbznGWKN4Vou902iePsSab7z5Jv3/fNjGuZui3FL0vMuaEgy+X5OVFgy58TlneHP38GtimKoiiKoiiKsjnsiPJsAJiamsKlS5cwODiIVCpl5iT3mm63iwsXLuDgwYNG0Ml+Yzl2SAotOdeYr1kvVCrcf8wwMFnyKxOaOXKJ70P3Vc6Qlu4yXVwKUPYBUyTT+QbWZhA3Gg3E43HzvplMBsPDwzh48CCy2Szq9TouXbqEmZkZ1Go14+QCCIRg0a2Vfb8UuCwfl9eD10j2VvM5vOYyoZwCWvYyS4EOAOl02ghqHpOl2Pzs5GfZbDYDPcrhsm9eZ252yDFZPDdFURRFURRFUTaPHeM0A8AzzzwDAHj44Yf7agRVs9nE/Pw8gDX3mL2+stwaQKAsWYo8ijAZsCWTrynQiCy1JhRwnuddFawle6UBGOHIsmf+yxJkPlcKcvZQU0wDKyXhY2NjuPXWWzE8PIxisYjJyUlMTU2Z0Uo8B1leLlOk+S/FO8U0w9X4M763FM9yg0CeL4Wr67oBt56bEVJAcwOA10C6x9LNliXhTCOXoWjyOXJMVXi+tKIoiqIoiqIom8eOcZoBoFQq4cKFC5iYmMDo6CiuXLnS6yUBWBGrS0tLsCwL+XzeOMrAmkgOpy8TikYiE6VJeINAhotJPM9DIpEwrq5MwQ6XN8sEbjn/mEKZP5dl4BSYDO7KZrMYHx/Hrl27kM1mceXKFZw5cwYLCwsBtzWRSAQcYumU0z2XvdpS9EpBynJuusYsIQ+nWctydB6DPed8HTcVms1moCc9PHKK7rxMOef1XC9RWyaIM6yNJfDFYjEQvKYoiqIoiqIoysbTP3brJvH0008jEongwQcfDJTv9hrf97G4uIhisWjczvVm8UphBSDgIlOwyZ/JQDEZwhUu25ZlxHI+M5/P11OUy/FM8nhciyw9ppCmqxuNRpFKpbBr1y7s378ftm3j0qVLOHPmDObn5+G6LiqVigm9isVicBzHHEuGoclANNlTLEdDSYe52WyaY8k5yDLsTDrWMqiMgWBcE8+RDrMsx5b933JjQV4jXj+6+/xeCnj5+bBUXFEURVEURVGUzWPHiWYAeO2115BMJrF3795eLyUAHefZ2Vnj+lLA0k2W4hZAQFjThQ3PQZZhXfL5FIUUgBR3fC+WQMugL4aANRqNwJglll5TQFJgS9EZiUTgOA4GBgZw8OBB7Nu3D67r4sKFCzh79iyWl5cDydjSsabAle41188SbrlmzlkGYILGAFxVks7HpfCmIwys9ZRblgXHccxmgRTkchSXFM0s6ebz+JlIQc73o5DmefO9+bkwrVxRFEVRFEVRlM1lR4rmb33rWygWizhy5AhSqVSvlxPA931UKhXMzMyYftpweTVFKYWWLPcFEOi9lf23AIxDK8cf8TV0asMjoWQptHx/6S4DMGnZLHmmwKVI5AzmQ4cOYdeuXWg0GpicnMTly5dRLpcD458olCk85eMcscVjMymbbjEA0wvO2c/sEZbXheXVcq40PwOWhcvS71arhWQyeZVzHO61lk4yS7rD4V98n/BrKKxlaXkkEjHjxBRFURRFURRF2Vx2pGgGgJdffhkTExM4duxYr5eyLu12G9PT0ygWi4HxQ7ZtA4ARZdLJBXCVC8uvw7OaKeDCP5Mjq/iYFM6yxJpCm6FWTKyWjiqwIrJt28bo6CgOHDiAgYEBFItFnD17FrOzs2aclBSePFeZ2L1eCJZMAE8kEuY1FMjhMnQKcdmTzL5hGe4l3W3bto14pzCXbrAs85fXjuuTTj+AgPsse8D5ellCL4PFNARMURRFURRFUTafHSuaL1y4ANd1cdtttyGXy/V6OevS7XaxtLSE6elplMtlM9aIgg9AQBhKxzMsGMNCmMKZriqwkuLtuq55DUuMpWPNPmk5UkquhcnV8n3T6TR2796NAwcOIJ1OY2ZmBufPn8fc3Jw5fngslRSmcj2WZcG2bVMqzevhOI7pSabIBGDmWruuGyiJluFp3AyQIpabAa1Wy4horpNl8/JaSic5POdauvgkXAHA18o+Zwr3SqUSuKaKoiiKoiiKomweO1Y0A8BXv/pVDA8P4/bbb+/1Ul6XdruNubk5TE1NYXl5GY1GA57nXZWqDay5l7KkOuxQ8ns6sjItGkDA2Q271TKEjO6s7/totVrGBecx2MPMwK9UKoXp6WlcvHjRnAcTsYkstZbl4hTrjuMgm80il8shk8kgmUwimUya95Z91PJcJTwez1uWqLMsO7wpQQFLMct1StHMaypL5cPCmD3icqOAcBNDblCoaFYURVEURVGU3rKjRk6Fee2113Dx4kXceuutuHDhAmZnZ3u9pNel1WphZmYGwIq4zOfzcBzHjFGSJdoUnOuJM4pSKfRYBkzRR9dThmKFQ7IoCGVYlhSLAwMDGBgYwK5du9BqtTA9PY0LFy4Eep3D86UBmFJollqz57fb7SKVSiGVSgWCu9jvK/uAKXwpWMOjsmSPtHyODPliGbpEzrfm+nm92WudTCZNCBk/A7rVdMbDo7DCI6j4HjwvRVEURVEURVF6w452mgHgySefxJ133om3vvWtfTWC6o1otVpYWFjA9PQ0FhYWUKlUUK/X0Wq1jEsrQ6zCPc+y3Phaadyyb1r2PdMdpnNKMU3BaVkWMpkM9u3bh7GxMbiui4sXL2JychKVSuWq2dMUrewn5nH4eDweRyqVCswurlarqNVqqNfrRjTTKQYQKK+W5xbeROD70DmW732tPmpgTdgDwdL3TqeDZrMZGPsVnqstR4TJ3nMZrMZND9d1A+egKIqiKIqiKMrmsqOdZgCYn5/H17/+dRw8eBATExO4cOFCr5d0Q3S7XVSrVVSrVQArPbyZTAbpdDrQFyydVgpVOV9YjooCggna641g4tfcaEgkEsapTiQSGB0dheM4WFpaMkngcv6xPFY4jbrVaplAMTkner2kawAmyEsKeV4bvic3Eq7VJ81RWuzplmKW7w8AnucZV1kGndXr9cDzCMu7Ze83xTn7wulwU0TzOvJaaACYoiiKoiiKovSOHe80A8AXvvAFRCIR3HvvvUin071ezneE67pYWFjAlStXsLS0hGq1amYfAzAlz7IcWM4DZsmyLLemsyzFM5Ep1ywRp9icmZnB1NQUlpaWjLBl2XJ4fJVM9JbJ3hS0rVbLuLsMLONs6PCGwHour3R1wyFdDDTjOfMcKWqBtbFQvBbhGdg8HznySiZyh1PNpWCWa+X37XYb9Xpde5kVRVEURVEUpcfseKcZWHEqv/a1r+Hv/b2/h1dffRWnTp3q9ZK+YzzPg+d5AFaEHNOmbds2/b7rubZ0UZkATTEre5f5PArpeDyOSqUCz/PQaDTQbDaNc0pHNewoS4EYTgCnmJRlyuuleIePE41GzfxmpmGzRD0SiQTmQANBN5nvI/uz+S9Fcjg4jcdvtVqmHDzcDx2PxwMuPUU818P3lsFjwMrvZKVS0dJsRVEURVEURekxKppXefbZZ/Hggw/i8OHDuHjxoil33g5wbBKwItZSqZQZz8TUaVnKTbeU85cpGMP9z7VazYjicrlsrpkscw67qXRqw328MjmaDi7fU/ZKS9EpnWk+T84zDgd5SUEOBEUzxThfK0uzZcm0dN75OuluE7nOa6Vlc02ypJzl6J7nqcusKIqiKIqiKH3AG4pmy7IcAF8BYK8+/zHf9/9vy7IOAvhTAEMAngXwz3zfb1qWZQP4QwDfBWARwA/7vn9hg9Z/U/n85z+P7//+78f4+DheffXVXi9nQ2APNOHMYbrPFHDhRGn21sre22q1uq6wy2az5mvpwoZLpxOJhBG9fE+KR4p1mR4txzVxbXSjuS6uke5uuHyb7y17oKUjLgUsRTrPUbrudOLZfy1LueV1ku6zDD2TJeFyZFU0GkWj0TAbEopCdtK9WFEUpR/R+7Ci7Fyup6fZA/BO3/fvAXAvgHdblvUggP8K4Dd9378VwDKAD64+/4MAllcf/83V520Jzp49i/n5edx3330YGBjo9XI2hWaziXq9jmKxiKWlJSwsLGBxcRHFYhHFYhHz8/NYXFzE/Pw85ufnsbS0hOXlZdOnHIal4FI4UpjyX1muLfuQwyFaDCtbL6WaP5eCVPYry7FbQFA88/nhEnEKZx6H78VxUXLNUgzLUnEZ2kW3nJsDPK58Hznrmj+r1+umtF5RBDvmXqwoitKn6H1YUXYobyia/RVoTcZX//MBvBPAY6uPfxTAP1z9+v2r32P1549a4drVPuaJJ57AkSNHsHv37l4vpWe0223zX6PRgOu6153g7DhOoD9Z9uq22+1AAJkcV8WyaYpHvj+/l2Xbch40XXAg6ERLx5jvL18nXWZgzQWWLja/DpdO82v5HhTqcuY0X8/+ap4/YUm8LD1vNBqo1+uamK1cxU67FyuKovQbeh9WlJ3LdaVnW5YVtSzrOIA5AH8D4ByAou/7TJC6DGBi9esJAJcAYPXnJayUq2wJpqamcPz4cbzlLW/B8PBwr5ez5WC5N4CAEGaZN0Wm4zhGNNJh5YxmiRS66z0e7hWWYlMKWFnKLYW1LIume8z1h91pWeZNl1uKXv6M6eRcG8uweW7SHQcQKIVnKriirMdOuhcriqL0I3ofVpSdyXWJZt/3O77v3wtgD4D7ARz9Tt/YsqyfsCzrW5Zlfes7PdbN5itf+QruuOMO3HPPPSYoS7k+KDilSGXptXR5HccBADM+SgpLus8AAs5v2NkFYIQux1HJGc1Mt5aiGVgr86ZoZvAZQ76IdMPZgy1fR+FNIS7XQ7g2IsW+dLkBoF6vo9Fo3JwPQtmWbPS9uAVtC1AURXk99D6sKDuTG5rT7Pt+EcATAN4KoGBZFptG9wCYWv16CsBeAFj9eR4r4QfhY33I9/03+77/5m9v6RtHsVjEk08+ife+973YtWtXr5ezZZACmaXOLL2Ox+MBZ5milkKZz5EzmoE1ESpLrsPjqbrdLprNpvleHl/2H0vHmuKXI7h83ze9x/J81gvjikajgRRu6SyzB1u62rJ3GVhx42UpeLfbRb1eR6lUUpdZuS426l4ch24SKoqiXA96H1aUncUbimbLskYsyyqsfp0E8PcAvIyVG8UPrT7txwE8vvr1p1e/x+rPv+RvwQbNL33pS8hkMnj729+OTCbT6+VsCZgiLRO2iWVZgfnQnucFgrwIxzrJVOlwwNZ6wVsUrGHhTMLv1Ww20Ww2A+taL4xMusJy3jIFOB3ycBCZ7IcOty/JknMKbnWZlTdip96LFUVR+gW9DyvKzuV6nObdAJ6wLOsEgGcA/I3v+38F4BcB/GvLss5ipT/jw6vP/zCAodXH/zWAX7r5y954yuUyPvaxj+Ghhx7S3ubrRLqrQDBASyZNU+DSHaYwlUFhsu93vfAuKYDXK5eWyMRrHkeGnTHUa71jc03xeNy4wHTQ2+22eazdbhunms45+6PDrnl4Ta7rolarafiX8kbsyHuxoihKH6H3YUXZobzhnGbf908AuG+dx89jpZcj/LgL4B/flNX1mG984xt417vehfe85z34+Mc/jlKp1Osl9T0UyrJvuN1uIxaLGYEpQ7cAmJ7hsOsM4Cr3V4Z3UXRKYSoTqyng+RwpWCnW2+02PM8zQlj+LLz+SCSCVqt1VQiZ/M+yLLTbbfP+stSc62XJt78657larZoEcEW5Fjv5XqwoitIP6H1YUXYuN9TTvNNotVr4yEc+gsOHD2uJ9nVAcSj7gGUptRSWwFoAlnw9RScdaT4vFoutm64NwPRQyxAx9j9L55rCWYaLxeNxsyaWlxMehwKZa+CaZEI2S7IplHkOdN/5fhTKfB1HeimKoiiKoiiK0p+oaH4Dzp49iw9/+MNYXLwqt0FZB4pVGbxF0ShnNvPnLHPudDpGtFJcs6xbpm+vJ5qlIJdQvLJsW64PgAnskvOagTWhz/5o+Tw+l+thnzPFdiKRMMdn+fd6Qp3HLZVK6zrsiqIoiqIoiqL0Byqar4OXXnpJ3cDrQPYo879ms2n6gilew/OR6dJKVzYMn0+XNix01+tH5uPtdhu2bRu3mY6zDPZiT7XsXW61WgH3mSKfwp2zpqXDLHu0eUwZThaNRpFKpRCJRFCv17UsW1EURVEURVH6HBXNyk2DAlE6qwACXxOWPofLoYFgPzSAgMAGYIS4fC5fGx4vJfud5RrZn9xutwPvxfehu8w5zXwtYW+z7JWmay7HZkWjUSQSCTiOg3Q6jUwmg0QigVqtpj3yiqIoiqIoirIFeMMgMEW5XpiKLQO0EokEms2mEc4UmVIYy7nOQDBMjMelS8t06nDIlnSqw7OOeXy6vVxHs9kMOL0Ux9FoFJ7nmfdgL3M40ZvONYWyXDMdbdu2A6FklmWhXC7jypUrARGuKIqiKIqiKEp/ok6zclOpVCrGaZU9v3J+cbjHWbq8FN7h7yk4pdiWolYmVFOYs++Yvc38OfucKcLpOFP8UuzyeXwdS7rlutlzzY2BWCyGeDwOx3GQy+WQTqfhOA4cx4FlWSgWi3jttde0LFtRFEVRFEVRtggqmpWbSrVaNY4sBaosjwauTs2WwVtEusv8meyJln3IshybTrecyUwR3el0YNt2wFFOJBKB8VOyrJrHC4d+SeecRKNR2LaNTCaDfD4Px3FMCXoikUC73Ua5XMbly5fhed53fqEVRVEURVEURdkUVDQrN5Vut2tKm5l+zf5g+Rz5nxzfxORtilUiS7+lcyzfh8JZpm+zTFo60SzXlqOp+Dw+TjHearXM62VgGdeayWSMWE6lUsjlcnAcxwjldruNer0O3/extLSkgXKKoiiKoiiKssXQnmblplMsFpFKpQKjp8IjnSh86fqyb5gl03xOOIyLj1MUJxIJ2LZtkrv5HDrD0WjUuMBh91l+zWO5rgvf902KNhO0Y7EYksmkEc6pVAqO4wAAXNc1XzebTTMzWq6jXC5jaWlpcz4ARVEURVEURVFuGiqalZuO67qo1+vI5/OmD1mGaNH1DfcMU2TzeQACAVp0hXk8vpavicfjgX5out6yt1kGeLmua1xlAKYnmc64bduBknCOmGLAGcPEotGoSdPme1H8R6NRVCoVTE5O6jxmRVEURVEURdmCqGhWbjq+76NSqSCRSBhXmCKSfcPRaDQwv5ml2BTJMoVb9hgDQCKRAABT/sxwrrBg5lo6nY55nm3b5r3DCdhyzjN7kmOxGBKJRGD2MnukKdwp6gEESsJt20aj0cDc3JwGfymKoiiKoijKFkVFs7Ih1Go1RKNRDAwMAFibtUxRDKw4wxSc7DOWzwUQKO2mwGYvMoUrn8ev5exllnPTDfY8D5lMJlAuThEeiUSQyWTQbDaRzWbNYywj73a7qFQqsG0btVrNJGN7nmdEdrvdNkFg9XodZ8+eRbFY3KjLrCiKoiiKoijKBqOiWdkQ6DYnk0lkMhkAa73GLHGW0PGl0OVMZY55Ys+xFMEAruqNZnk0kWOqGFAWj8dNHzR7ntk/nUwm0Wg00Ol0EI/HEY1GUavVAmXaHGvFnulUKgUA2L17N7rdLrLZLNrtNl555RVMTU1dlbStKIqiKIqiKMrWQUWzsmF0u10sLCwgEokgl8uZEmWGfknHmc+nS0xHORaLodFoGKHs+77pRWaqtXSbKYzT6TQAwPM8ZLNZxONxRCIRpFIp5PP5QC9zJBJBvV43645Go1eNhZL91jxWJBLByMiIcctHR0fR7Xbhui7Onz+Pl19+WfuYFUVRFEVRFGWLo6JZ2VDa7TaWlpbgOI5xhCmWpfhlTzJdXyLTt9kvzF7iWCwGx3HQ7XZNeXSr1YJt20bYtlotE+Bl2zY8z0M8HjejpGTqNsu/6VTLUVSRSASFQgG1Wg2FQsGsa2RkBK1Wy5SWO46DU6dO4emnnw70WCuKoiiKoiiKsjVR0axsOJ7noVqtIpfLmfLoVqtleolZBi1dZqZVy1Jritd0Om3ELAV4LBZDKpUyYloGe7FEm/82m00jnjkeyrIsJBKJwLipWCyGfD5vxmcVCgWMjY2h3W4jm82iXC6j2+3Ctm20Wi0MDAygVquhWq1eVX6uKIqiKIqiKMrWREWzsuH4vo9isYiBgQFkMhkjhlmO7TgOEomE6Q2mc8teaM5ABtaEM0Uuw8AooGXfMQUx5z+3Wi3TxyyFMrBSop1MJhGJRIwAj8VimJiYMM42Bbzneeh0OsjlchgdHcUtt9yCQ4cOIR6P4/HHH8fzzz+/yVdYURRFURRFUZSNQkWzsik0m01MTU3h6NGjsG3bzEmmsytLoh3HQbPZRDKZBAA0Gg0AK6XeFNosnWZIGEdUsT+a/7LvOZFImH5nYG2klG3bcBzH/JvJZJBKpeC6LjqdDlKpFOLxOOr1ujn+xMQE9u/fj/vuuw979uzBV7/6VZw6dQo/+ZM/adaoKIqiKIqiKMr2QEWzsmnUajW89tprmJiYMC4yHdxms2kcYs/z0G63Tfk0g7bo+FIgs/Sax2AvNEu7bdtGIpGAbdvIZDImtdu2bdi2jWQyaVK42QedTCbRarWMiLdtG7t27UIymcThw4dx1113mfLtwcHBwLp0FrOiKIqiKIqibD9UNCubhu/7WFhYgOd52Ldvnym7pjPLPmL2I7Nfmc4yS6fleCmOiopGo0YkA2siOBqNIp/PI5FIYHBwEO12G8lk0vQ300FmsFc2mzVzlkdHR3H33XdjbGwMX/va13D69GksLS3h/e9/v1mvoiiKoiiKoijbGxXNyqbC+c2XL1/Grl27UCgUAKz1KgMwvcoUs+wvprMci8UQjUYRj8fN61jyzX9TqZQJFRsYGIDv+2g2myaAzLIsUxaezWYxNDSE8fFxPPTQQ0ilUsa1tm3brJsiXlOxFUVRFEVRFGXnoKJZ2XQonB3HgeM4GBsbM0KWgV0UwgBM4jZFayKRQDweN24xS7AZBtbtdo3jXC6X0Wq1kEwmzfsNDg5ieHgYt912G8bHxxGJRLC0tISf//mfx9e+9jWMjIzg3/ybf4Nut6sCWVEURVEURVF2OCqalZ7Q6XRQLBZRKBQQj8cxPDxshDMA4yjLfmXZv0wRTeEcjUZNOBiwEgQ2MDCAPXv2YM+ePbj11luxf/9+ExBGhoaGUCwWAcA42QCMQFcURVEURVEUZWejolnpGa7r4ty5c2g2m9izZw8mJiZMuTb7lyORiBkPxe9jsRjS6bQZU5XNZpFOpzEyMoLx8XGMjo6a8mwAeOSRRzA3N4dCoYDjx4/D8zyzhrA4VrGsKIqiKIqiKIpERbPSU1qtFi5duoROp4NoNIp0Oo3h4WHk83nE43Gk02nkcjkz33lwcBBjY2OmpHs93vWud+ELX/jCJp+JoiiKoiiKoijbERXNSs/J5XJ48MEH8aY3vQl33XUXjhw5gj179iCRSOAjH/kIfuzHfqzXS1QURVEURVEUZYdi9UM5qmVZ8wBqABZ6vZbvkGHoOfQDeg43xn7f90c26b2UPsayrAqAV3q9ju8Q/d9//7AdzkPvxcqmon8T9xV6Dv1BX9yH+0I0A4BlWd/yff/NvV7Hd4KeQ3+g56Ao3x7b4fdOz6F/2A7nsR3OQdl6bIffOz2H/kDP4eYR6fUCFEVRFEVRFEVRFKVfUdGsKIqiKIqiKIqiKNegn0Tzh3q9gJuAnkN/oOegKN8e2+H3Ts+hf9gO57EdzkHZemyH3zs9h/5Az+Em0Tc9zYqiKIqiKIqiKIrSb/ST06woiqIoiqIoiqIofUXPRbNlWe+2LOsVy7LOWpb1S71ez/ViWdYFy7JetCzruGVZ31p9bNCyrL+xLOvM6r8DvV5nGMuyft+yrDnLsl4Sj627bmuF31r9bE5YlvWm3q18jWucw7+3LGtq9fM4blnWe8XP/u3qObxiWda7erPqNSzL2mtZ1hOWZZ2yLOukZVk/v/r4lvoclO2F3os3D70P9/4+DOi9WOk/9D68uei9uPf34q10H+6paLYsKwrgtwG8B8AxAP/EsqxjvVzTDfIO3/fvFTHovwTgb33fvw3A365+32/8AYB3hx671rrfA+C21f9+AsD/3KQ1vhF/gKvPAQB+c/XzuNf3/c8BwOrv048AuGP1Nb+z+nvXS9oAfsH3/WMAHgTw06vr3Gqfg7JN0HvxpvMH0Ptwr+/DgN6LlT5C78M94Q+g9+Je34u3zH24107z/QDO+r5/3vf9JoA/BfD+Hq/pO+H9AD66+vVHAfzD3i1lfXzf/wqApdDD11r3+wH8ob/CNwEULMvavSkLfR2ucQ7X4v0A/tT3fc/3/UkAZ7Hye9czfN+f8X3/udWvKwBeBjCBLfY5KNsKvRdvInof7v19GNB7sdJ36H14k9F7ce/vxVvpPtxr0TwB4JL4/vLqY1sBH8AXLMt61rKsn1h9bMz3/ZnVr68AGOvN0m6Ya617q30+P7NaqvH7ogyor8/BsqwDAO4D8BS2z+egbD228u/YdrkXb5f//W+5+zCg92KlL9jKv1/b5T4MbJ///W+5e3G/34d7LZq3Mg/5vv8mrJQJ/LRlWd8jf+ivxJJvuWjyrbpurJRn3ALgXgAzAP5bT1dzHViWlQHwSQD/0vf9svzZFv4cFGWz2Xb34q245lW23H0Y0HuxotwEtt19GNi668YWvBdvhftwr0XzFIC94vs9q4/1Pb7vT63+OwfgL7BS3jDLEoHVf+d6t8Ib4lrr3jKfj+/7s77vd3zf7wL4PayVm/TlOViWFcfKzeFjvu9/avXhLf85KFuWLfs7to3uxVv+f/9b7T4M6L1Y6Su27O/XNroPA9vgf/9b7V68Ve7DvRbNzwC4zbKsg5ZlJbDSnP7pHq/pDbEsK21ZVpZfA/g+AC9hZe0/vvq0HwfweG9WeMNca92fBvB/rCbVPQigJEol+opQP8MPYOXzAFbO4Ucsy7ItyzqIleCApzd7fRLLsiwAHwbwsu/7vyF+tOU/B2XLovfi3rPl//e/le7DgN6Llb5D78P9wZb/3/9Wuhdvqfuw7/s9/Q/AewG8CuAcgF/u9Xquc82HALyw+t9JrhvAEFYS3s4A+CKAwV6vdZ21/wlWSjVaWOkD+OC11g3AwkqS4zkALwJ4c6/X/zrn8EerazyBlf9B7RbP/+XVc3gFwHv6YP0PYaXM5ASA46v/vXerfQ763/b6T+/Fm7puvQ/3xznovVj/66v/9D686WvXe3Hv179l7sPW6gIURVEURVEURVEURQnR6/JsRVEURVEURVEURelbVDQriqIoiqIoiqIoyjVQ0awoiqIoiqIoiqIo10BFs6IoiqIoiqIoiqJcAxXNiqIoiqIoiqIoinINVDQriqIoiqIoiqIoyjVQ0awoiqIoiqIoiqIo10BFs6IoiqIoiqIoiqJcg/8/XNMHPyTcV70AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1296x432 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["case_num = 4\n", "model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "with torch.no_grad():\n", "    img_name = os.path.split(val_ds[case_num][\"image\"].meta[\"filename_or_obj\"])[1]\n", "    img = val_ds[case_num][\"image\"]\n", "    label = val_ds[case_num][\"label\"]\n", "    val_inputs = torch.unsqueeze(img, 1).cuda()\n", "    val_labels = torch.unsqueeze(label, 1).cuda()\n", "    val_outputs = sliding_window_inference(val_inputs, (96, 96, 96), 4, model, overlap=0.8)\n", "    plt.figure(\"check\", (18, 6))\n", "    plt.subplot(1, 3, 1)\n", "    plt.title(\"image\")\n", "    plt.imshow(val_inputs.cpu().numpy()[0, 0, :, :, slice_map[img_name]], cmap=\"gray\")\n", "    plt.subplot(1, 3, 2)\n", "    plt.title(\"label\")\n", "    plt.imshow(val_labels.cpu().numpy()[0, 0, :, :, slice_map[img_name]])\n", "    plt.subplot(1, 3, 3)\n", "    plt.title(\"output\")\n", "    plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, slice_map[img_name]])\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}