#!/usr/bin/env python
# coding: utf-8

# # Spleen 3D segmentation with MONAI
# This tutorial shows how to integrate MONAI into an existing PyTorch medical DL program.

# And easily use below features:
# 1. Transforms for dictionary format data.
# 1. Load Nifti image with metadata.
# 1. Add channel dim to the data if no channel dimension.
# 1. Scale medical image intensity with expected range.
# 1. Crop out a batch of balanced images based on positive / negative label ratio.
# 1. Cache IO and transforms to accelerate training and validation.
# 1. 3D UNet model, Dice loss function, Mean Dice metric for 3D segmentation task.
# 1. Sliding window inference method.
# 1. Deterministic training for reproducibility.

# The Spleen dataset can be downloaded from http://medicaldecathlon.com/.
# [![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/spleen_segmentation_3d.ipynb)
#%% Autodl云服务器和本地图像快速传输方法
# 复制您的ssh登录指令，指令格式为：ssh -p 12603 <EMAIL>

# （注意35394为端口号，region-1.autodl.com为远程地址，请更换为您的实例端口和地址）

# 那么scp远程拷贝文件的指令为：
# scp -rP 12603 "H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\503HCC\image\ap.zip" <EMAIL>:/root/autodl-tmp/HCC503ap 
# # （注意需要在您本地的机器的终端上执行）

# # 如果是将实例中的数据拷贝到本地，那么scp远程拷贝指令为：
# # scp -rP 35394 root@************:<实例中的文件/文件夹> <本地文件/文件夹>
# scp -rP 47477 <EMAIL>:/root/autodl-tmp/nnUNet/nnUNet_results/Dataset503 'j:\nnUNet'

# In[1]: 3d unet,swinunetr模型图像分割，成功了
# 自己数据集运行成功，在autodl；适用于标签为2，即病灶和背景

# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[gdown, nibabel, tqdm, ignite]"')
# get_ipython().system('python -c "import matplotlib" || pip install -q matplotlib')
# get_ipython().run_line_magic('matplotlib', 'inline')
import monai
print(monai.__version__)

# In[2]:
from monai.utils import first, set_determinism
from monai.transforms import (
    AsDiscrete,
    AsDiscreted,
    EnsureChannelFirstd,
    Compose,
    CropForegroundd,
    LoadImaged,
    Orientationd,
    RandCropByPosNegLabeld,
    SaveImaged,
    ScaleIntensityRanged,
    Spacingd,
    Invertd,
    RandRotate90d,
    RandFlipd,
    RandScaleIntensityd,
    RandAffined
)
from monai.handlers.utils import from_engine
from monai.networks.nets import UNet,UNETR,VNet,SwinUNETR,AttentionUnet,SegResNet
from monai.networks.layers import Norm
from monai.metrics import DiceMetric
from monai.losses import DiceLoss
from monai.inferers import sliding_window_inference
from monai.data import CacheDataset, DataLoader, Dataset, decollate_batch
from monai.config import print_config
from monai.apps import download_and_extract
import torch
import matplotlib.pyplot as plt
import tempfile
import shutil
import os
import glob

print_config()


# In[5]: 移动文件夹在终端 mv /tmp/tmpyzowfms0/Task09_Spleen.tar /root/autodl-tmp/
#脾脏分割数据集
# root_dir = '/tmp/tmpyzowfms0'
# data_dir = os.path.join(root_dir, "Task09_Spleen")
# print(data_dir)
# train_images = sorted(glob.glob(os.path.join(data_dir, "imagesTr", "*.nii.gz")))
# train_labels = sorted(glob.glob(os.path.join(data_dir, "labelsTr", "*.nii.gz")))
# data_dicts = [{"image": image_name, "label": label_name} for image_name, label_name in zip(train_images, train_labels)]
# train_files, val_files = data_dicts[:-9], data_dicts[-9:]
# train_files[:5]

#自己的数据集
# root_dir = '/root/autodl-tmp/Task09_Spleen'
# train_images = '/root/autodl-tmp/Task09_Spleen/imagesTr'  
# train_labels = '/root/autodl-tmp/Task09_Spleen/labelsTr'

root_dir = '/root/autodl-tmp/HCC503ap'
train_images = '/root/autodl-tmp/HCC503ap/ap2'
train_labels = '/root/autodl-tmp/HCC503ap/apmask2'

train_images_list = sorted(glob.glob(os.path.join(train_images, "*.nii.gz")))
train_labels_list = sorted(glob.glob(os.path.join(train_labels, "*.nii.gz")))

print(f"Number of training images: {len(train_images_list)}")
print(f"Number of training labels: {len(train_labels_list)}")

data_dicts = [{"image": image_name, "label": label_name} for image_name, label_name in zip(train_images_list, train_labels_list)]

split_index = int(len(data_dicts) * 0.8)# 按照 8:2 划分
train_files, val_files = data_dicts[:split_index], data_dicts[split_index:]
print(f"Total number of samples: {len(data_dicts)}")
print(f"Number of training samples: {len(train_files)}")
print(f"Number of validation samples: {len(val_files)}")
train_files[:5]

# In[6]:# Set deterministic training for reproducibility

set_determinism(seed=0)

# In[7]:数据增强与预处理
import numpy as np
from monai.transforms import LoadImaged

spatial_size = (96, 96, 96)

train_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-50,
            a_max=150,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image", "label"], source_key="image"),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.5, 1.5, 2.0), mode=("bilinear", "nearest")),
        RandCropByPosNegLabeld(
            keys=["image", "label"],
            label_key="label",
            spatial_size= spatial_size,
            pos=1,
            neg=1,
            num_samples=4,
            image_key="image",
            image_threshold=0,
        ),
        # user can also add other random transforms
        # RandAffined(
        #     keys=['image', 'label'],
        #     mode=('bilinear', 'nearest'),
        #     prob=1.0, spatial_size=(96, 96, 96),
        #     rotate_range=(0, 0, np.pi/15),
        #     scale_range=(0.1, 0.1, 0.1)),
    ]
)
val_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-50,
            a_max=150,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image", "label"], source_key="image"),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.5, 1.5, 2.0), mode=("bilinear", "nearest")),
    ]
)

# In[8]:

check_ds = Dataset(data=val_files, transform=val_transforms)
check_loader = DataLoader(check_ds, batch_size=1)
check_data = first(check_loader)
image, label = (check_data["image"][0][0], check_data["label"][0][0])
print(f"image shape: {image.shape}, label shape: {label.shape}")
# plot the slice [:, :, 80]
plt.figure("check", (12, 6))
plt.subplot(1, 2, 1)
plt.title("image")
plt.imshow(image[:, :, 80], cmap="gray")
plt.subplot(1, 2, 2)
plt.title("label")
plt.imshow(label[:, :, 80])
plt.show()

## Define CacheDataset and DataLoader for training and validation
# Here we use CacheDataset to accelerate training and validation process, it's 10x faster than the regular Dataset.  
# To achieve best performance, set `cache_rate=1.0` to cache all the data, if memory is not enough, set lower value.  
# Users can also set `cache_num` instead of `cache_rate`, will use the minimum value of the 2 settings.  
# And set `num_workers` to enable multi-threads during caching.  
# If want to to try the regular Dataset, just change to use the commented code below.

# In[9]:
train_ds = CacheDataset(data=train_files, transform=train_transforms, cache_rate=1, num_workers=2)
# train_ds = Dataset(data=train_files, transform=train_transforms)
train_loader = DataLoader(train_ds, batch_size=1, shuffle=True, num_workers=2)

val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_rate=1, num_workers=2)
# val_ds = Dataset(data=val_files, transform=val_transforms)
val_loader = DataLoader(val_ds, batch_size=1, num_workers=2)

#%%定义模型、损失函数与优化器
from monai.networks.nets import UNet,UNETR,VNet,SwinUNETR,AttentionUnet,SegResNet
from monai.losses import DiceCELoss
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


#1.UNet模型成功了!
model = UNet(
    spatial_dims=3,
    in_channels=1,
    out_channels=2,#病灶和背景，所以label为2
    channels=(16, 32, 64, 128, 256),
    strides=(2, 2, 2, 2),
    num_res_units=2,
    norm=Norm.BATCH,
).to(device)
print(model)

#2.UNETR模型(TransformerUnet)成功了!
# model = UNETR(
#     in_channels=1,
#     out_channels=2,#病灶和背景，所以label为2
#     img_size=(96, 96, 96),
#     feature_size=16,
#     hidden_size=768,
#     mlp_dim=3072,
#     num_heads=12,
#     pos_embed="perceptron",
#     norm_name="instance",
#     res_block=True,
#     dropout_rate=0.0,
# ).to(device)
# print(model)

#3.Swinunetr模型(SwinTransformerUnet)成功了!最新的！
# model = SwinUNETR(
#     img_size=(96, 96, 96),
#     in_channels=1,
#     out_channels=2,
#     feature_size=48,
#     use_checkpoint=True,
# ).to(device)
# print(model)

# 4.SegResNet模型成功了!
# model = SegResNet(
#     spatial_dims=3,          # 输入的空间维度（3D）
#     init_filters=8,          # 初始卷积层的过滤器数量
#     in_channels=1,           # 输入通道数（例如，单通道灰度图像）
#     out_channels=2,          # 输出通道数（例如，背景和目标）
#     dropout_prob=0.1,       # Dropout 概率，用于正则化    
#     num_groups=8,           # 组归一化的组数
#     use_conv_final=True,     # 是否使用最终卷积层   
# ).to(device)
# print(model)

# 5.VNet模型成功了!
# model = VNet(
#     spatial_dims=3,          # 输入的空间维度（3D）
#     in_channels=1,           # 输入通道数（例如，单通道灰度图像）
#     out_channels=2,          # 输出通道数（例如，分割的类别数）
#     dropout_prob_down=0.2,   # 下采样阶段的 Dropout 概率
#     dropout_prob_up=(0.2, 0.2),   # 上采样阶段的 Dropout 概率
#     dropout_dim=3,                # Dropout 的维度
#     bias=False,                   # 是否在卷积层中使用偏置
# ).to(device)
# print(model)


# 6.AttentionUnet模型成功了!慢！
# model = AttentionUnet(
#     spatial_dims=3, # 3D 输入
#     in_channels=1,
#     out_channels=2,
#     channels = [32, 64, 128],  # 各层的通道数
#     strides=[1, 2] ,
#     kernel_size=3,
#     up_kernel_size=3,
#     dropout=0
# ).to(device)
# print(model)

# 损失函数选项 1: DiceCELoss (推荐)
loss_function = DiceCELoss(to_onehot_y=True, softmax=True)
# torch.backends.cudnn.benchmark = True
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)
# optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-5)#防止过拟合
# scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)  # 官网代码没用，效果不好
dice_metric_train = DiceMetric(include_background=False, reduction="mean")  # 新增
dice_metric_val = DiceMetric(include_background=False, reduction="mean")    # 新增


#%% 初始化记录列表
train_loss_values = []      # 训练 loss
val_loss_values = []        # 验证 loss
train_dice_values = []      # 训练 dice
val_dice_values = []        # 验证 dice
val_epochs = []
best_metric = -1
best_metric_epoch = -1

post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])
post_label = Compose([AsDiscrete(to_onehot=2)])

#%%模型训练
max_epochs = 50
val_interval = 1

for epoch in range(max_epochs):
    print("-" * 10)
    print(f"epoch {epoch + 1}/{max_epochs}")
    model.train()
    train_epoch_loss = 0
    step = 0
    dice_metric_train.reset()   

    for batch_data in train_loader:
        step += 1
        inputs, labels = (
            batch_data["image"].to(device),
            batch_data["label"].to(device),
        )
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = loss_function(outputs, labels)
        loss.backward()
        optimizer.step()
        train_epoch_loss += loss.item()

        # 计算训练 Dice - 修改后的版本
        train_outputs = [post_pred(i) for i in decollate_batch(outputs)]
        train_labels = [post_label(i) for i in decollate_batch(labels)]
        dice_metric_train(y_pred=train_outputs, y=train_labels)
     
    # scheduler.step()
    torch.cuda.empty_cache()

    train_epoch_loss /= step
    train_loss_values.append(train_epoch_loss)
    
    # 计算并记录训练 Dice
    train_dice = dice_metric_train.aggregate().item()
    train_dice_values.append(train_dice)
    dice_metric_train.reset()
    
    print(f"epoch {epoch + 1} average loss: {train_epoch_loss:.4f}, train Dice: {train_dice:.4f}")

    if (epoch + 1) % val_interval == 0:
        model.eval()
        val_epoch_loss = 0
        val_steps = 0
        with torch.no_grad():
            for val_data in val_loader:
                val_steps += 1
                val_inputs, val_labels = (
                    val_data["image"].to(device),
                    val_data["label"].to(device),
                )
                roi_size = spatial_size  # 使用与训练时相同的空间尺寸(96, 96, 96)
                sw_batch_size = 1  # 减小批次大小以减少内存使用
                val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
                
                # 计算验证 loss
                val_loss = loss_function(val_outputs, val_labels)
                val_epoch_loss += val_loss.item()
                
                val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]
                val_labels = [post_label(i) for i in decollate_batch(val_labels)]
                dice_metric_val(y_pred=val_outputs, y=val_labels)

            # 计算平均验证 loss
            val_epoch_loss /= val_steps
            val_loss_values.append(val_epoch_loss)

            # 计算验证 Dice
            val_dice = dice_metric_val.aggregate().item()
            val_dice_values.append(val_dice)
            dice_metric_val.reset()

            val_epochs.append(epoch + 1)
            if val_dice > best_metric:
                best_metric = val_dice
                best_metric_epoch = epoch + 1
                torch.save(model.state_dict(), os.path.join(root_dir, "best_metric_model.pth"))
                print("saved new best metric model")
            print(
                f"current epoch: {epoch + 1} validation loss: {val_epoch_loss:.4f}, validation dice: {val_dice:.4f}"
                f"\nbest validation dice: {best_metric:.4f} at epoch: {best_metric_epoch}"
            )

print(f"Training completed, best validation dice: {best_metric:.4f} at epoch: {best_metric_epoch}")

#%% 绘制训练曲线
plt.figure("Training Curves", (15, 10))

# 1. Loss 曲线
plt.subplot(2, 2, 1)
plt.title("Loss during Training and Validation")
epochs = list(range(1, max_epochs + 1))
plt.plot(epochs, train_loss_values, label="Training Loss")
plt.plot(val_epochs, val_loss_values, label="Validation Loss")
plt.xlabel("Epoch")
plt.ylabel("Loss")
plt.legend()
plt.grid(True)

# 2. Dice 曲线
plt.subplot(2, 2, 2)
plt.title("Dice Scores during Training and Validation")
plt.plot(epochs, train_dice_values, label="Training Dice")
plt.plot(val_epochs, val_dice_values, label="Validation Dice")
plt.xlabel("Epoch")
plt.ylabel("Dice Score")
plt.legend()
plt.grid(True)


#%%导出训练指标到 Excel
import pandas as pd
df_train = pd.DataFrame({
    'Epoch': epochs,
    'Train Loss': train_loss_values,
    'Train Dice': train_dice_values,
})

df_val = pd.DataFrame({
    'Epoch': val_epochs,
    'Validation Loss': val_loss_values,
    'Validation Dice': val_dice_values
})

# 合并数据框
df_combined = pd.merge(df_train, df_val, on='Epoch', how='left')

# 导出到 Excel
output_path = os.path.join(root_dir, "training_metrics.xlsx")
df_combined.to_excel(output_path, index=False)

print(f"Training metrics and curves have been saved to {root_dir}")

# In[11]: Check best model output with the input image and label
model.load_state_dict(torch.load(os.path.join(root_dir, "best_metric_model.pth")))
model.eval()
with torch.no_grad():
    for i, val_data in enumerate(val_loader):
        roi_size = spatial_size  # 使用与训练时相同的空间尺寸(96, 96, 96)
        sw_batch_size = 1  # 减小批次大小以减少内存使用
        val_outputs = sliding_window_inference(val_data["image"].to(device), roi_size, sw_batch_size, model)
        # plot the slice [:, :, 80]
        plt.figure("check", (18, 6))
        plt.subplot(1, 3, 1)
        plt.title(f"image {i}")
        plt.imshow(val_data["image"][0, 0, :, :, 80], cmap="gray")
        plt.subplot(1, 3, 2)
        plt.title(f"label {i}")
        plt.imshow(val_data["label"][0, 0, :, :, 80])
        plt.subplot(1, 3, 3)
        plt.title(f"output {i}")
        plt.imshow(torch.argmax(val_outputs, dim=1).detach().cpu()[0, :, :, 80])
        plt.show()
        if i == 2:
            break

#Evaluation on original image spacings

# In[12]:
val_org_transforms = Compose(
    [
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.5, 1.5, 2.0), mode="bilinear"),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-57,
            a_max=164,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image"], source_key="image"),
    ]
)

val_org_ds = Dataset(data=val_files, transform=val_org_transforms)
val_org_loader = DataLoader(val_org_ds, batch_size=1, num_workers=4)

post_transforms = Compose(
    [
        Invertd(
            keys="pred",
            transform=val_org_transforms,
            orig_keys="image",
            meta_keys="pred_meta_dict",
            orig_meta_keys="image_meta_dict",
            meta_key_postfix="meta_dict",
            nearest_interp=False,
            to_tensor=True,
            device="cpu",
        ),
        AsDiscreted(keys="pred", argmax=True, to_onehot=2),
        AsDiscreted(keys="label", to_onehot=2),
    ]
)

# In[16]:计算验证集的平均dice

model.load_state_dict(torch.load(os.path.join(root_dir, "best_metric_model.pth")))
model.eval()

with torch.no_grad():
    for val_data in val_org_loader:
        val_inputs = val_data["image"].to(device)
        roi_size = spatial_size  # 使用与训练时相同的空间尺寸
        sw_batch_size = 1  # 减小批次大小
        val_data["pred"] = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
        val_data = [post_transforms(i) for i in decollate_batch(val_data)]
        val_outputs, val_labels = from_engine(["pred", "label"])(val_data)
        # compute metric for current iteration
        dice_metric_val(y_pred=val_outputs, y=val_labels)

    # aggregate the final mean dice result
    metric_org = dice_metric_val.aggregate().item()
    # reset the status for next validation round
    dice_metric_val.reset()

print("Metric on original image spacing: ", metric_org)


#%%模型对test数据集进行批量预测，得到预测的mask
import glob

test_dir = '/root/autodl-tmp/Task09_Spleen/imagesTs'  # 修改为imageTs目录，而不是labelsTs
test_images = sorted(glob.glob(os.path.join(test_dir, "*.nii.gz")))


# 检查test集目录是否存在，如果不存在则创建
test_mask = '/root/autodl-tmp/Task09_Spleen/test_predmask'

if not os.path.exists(test_mask):
    os.makedirs(test_mask)

# 将路径单独列出来
for image_path in test_images:
    print(image_path)

test_data = [{"image": image} for image in test_images]

test_org_transforms = Compose(
    [
        LoadImaged(keys="image"),
        EnsureChannelFirstd(keys="image"),
        Orientationd(keys=["image"], axcodes="RAS"),
        Spacingd(keys=["image"], pixdim=(1.5, 1.5, 2.0), mode="bilinear"),
        ScaleIntensityRanged(
            keys=["image"],
            a_min=-57,
            a_max=164,
            b_min=0.0,
            b_max=1.0,
            clip=True,
        ),
        CropForegroundd(keys=["image"], source_key="image"),
    ]
)

# test_org_ds = CacheDataset(data=test_data, transform=val_transforms, cache_rate=1.0, num_workers=4)
test_org_ds = Dataset(data=test_data, transform=test_org_transforms)

test_org_loader = DataLoader(test_org_ds, batch_size=1, num_workers=4)


post_transforms = Compose(
    [
        Invertd(
            keys="pred",
            transform=test_org_transforms,
            orig_keys="image",
            meta_keys="pred_meta_dict",
            orig_meta_keys="image_meta_dict",
            meta_key_postfix="meta_dict",
            nearest_interp=False,
            to_tensor=True,
        ),
        AsDiscreted(keys="pred", argmax=True, to_onehot=2),
        SaveImaged(keys="pred", meta_keys="pred_meta_dict", output_dir=test_mask, output_postfix="seg", resample=False),
    ]
)

# 调用模型进行预测
model.load_state_dict(torch.load(os.path.join(root_dir, "best_metric_model.pth")))
model.eval()

spatial_size = (96, 96, 96)

with torch.no_grad():
    for idx, test_data in enumerate(test_org_loader):
        test_inputs = test_data["image"].to(device)
        
        # 保存原始图像便于可视化
        input_image = test_data["image"][0, 0].detach().cpu().numpy()
        
        # 推理过程
        roi_size = spatial_size  # 使用与训练时相同的空间尺寸
        sw_batch_size = 1  # 减小批次大小
        test_data["pred"] = sliding_window_inference(test_inputs, roi_size, sw_batch_size, model)
        
        # 获取预测结果用于可视化
        pred_before_post = torch.argmax(test_data["pred"], dim=1)[0].detach().cpu().numpy()
        
        # 应用后处理变换
        test_data = [post_transforms(i) for i in decollate_batch(test_data)]
        
        # 可视化预测结果
        # 选择中间的切片以确保能看到内容
        middle_slice = input_image.shape[2] // 2
        
        plt.figure("Prediction Results", (12, 6))
        plt.subplot(1, 2, 1)
        plt.title(f"Original Image (slice {middle_slice})")
        plt.imshow(input_image[:, :, middle_slice], cmap="gray")
        
        plt.subplot(1, 2, 2)
        plt.title(f"Prediction Mask (slice {middle_slice})")
        plt.imshow(pred_before_post[:, :, middle_slice])
        
        plt.tight_layout()
        plt.savefig(os.path.join(test_mask, f"visualization_{idx}.png"))
        plt.show()
        
        print(f"处理完成 {idx+1}/{len(test_org_loader)} - 已保存预测结果和可视化")

# %%
