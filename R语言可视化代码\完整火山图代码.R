# ==================== 完整火山图绘制代码 ====================
# 包含示例数据和完整的火山图绘制功能
# 可以直接运行，无需额外数据文件

#%% 清理环境
rm(list = ls())

# 检查并安装必要的包
required_packages <- c("ggplot2", "dplyr", "ggrepel")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    cat("正在安装包:", pkg, "\n")
    install.packages(pkg, dependencies = TRUE)
    library(pkg, character.only = TRUE)
  }
}

cat("所有必需包已加载完成\n")

# ==================== 方法1: 使用内置示例数据 ====================

# 生成示例数据
set.seed(123)
n_genes <- 200

# 基因名称
gene_names <- c(
  # 真实基因名
  "TP53", "BRCA1", "BRCA2", "EGFR", "MYC", "KRAS", "BRAF", "PIK3CA", 
  "PTEN", "APC", "RB1", "VHL", "MLH1", "MSH2", "ATM", "CHEK2",
  # 模拟基因名
  paste0("Gene_", sprintf("%03d", 1:(n_genes-16)))
)

# 生成log2 fold change
log2FC <- rnorm(n_genes, mean = 0, sd = 1.2)

# 为一些基因设置显著差异
significant_up <- sample(1:n_genes, 15)
significant_down <- sample(setdiff(1:n_genes, significant_up), 15)

log2FC[significant_up] <- log2FC[significant_up] + runif(15, 1.5, 3)
log2FC[significant_down] <- log2FC[significant_down] - runif(15, 1.5, 3)

# 生成p值
p_values <- runif(n_genes, 0.01, 0.8)
p_values[c(significant_up, significant_down)] <- runif(30, 0.0001, 0.01)

# 创建数据框
volcano_data <- data.frame(
  Gene = gene_names,
  log2FC = log2FC,
  p_value = p_values,
  stringsAsFactors = FALSE
)

# ==================== 方法2: 读取CSV文件（如果存在） ====================

if (file.exists("示例数据.csv")) {
  cat("发现CSV文件，正在读取...\n")
  volcano_data <- read.csv("示例数据.csv", stringsAsFactors = FALSE)
  cat("已读取CSV数据，包含", nrow(volcano_data), "个基因\n")
} else {
  cat("使用内置示例数据，包含", nrow(volcano_data), "个基因\n")
}

# ==================== 数据预处理 ====================

# 计算-log10(p值)
volcano_data$neg_log10_p <- -log10(volcano_data$p_value)

# 设置阈值
FC_THRESHOLD <- 1.0
P_THRESHOLD <- 0.05
neg_log10_p_threshold <- -log10(P_THRESHOLD)

# 分类基因
if (!"significance" %in% colnames(volcano_data)) {
  volcano_data$significance <- "Not Significant"
  volcano_data$significance[volcano_data$log2FC > FC_THRESHOLD & 
                           volcano_data$p_value < P_THRESHOLD] <- "Up-regulated"
  volcano_data$significance[volcano_data$log2FC < -FC_THRESHOLD & 
                           volcano_data$p_value < P_THRESHOLD] <- "Down-regulated"
}

# 转换为因子
volcano_data$significance <- factor(volcano_data$significance, 
                                   levels = c("Up-regulated", "Down-regulated", "Not Significant"))

# ==================== 选择标注基因 ====================

# 选择最显著的基因进行标注
top_genes <- volcano_data %>%
  filter(significance != "Not Significant") %>%
  arrange(p_value) %>%
  head(10)

# ==================== 火山图绘制函数 ====================

create_volcano_plot <- function(data, genes_to_label = NULL, title = "Volcano Plot") {
  
  # 基础图层
  p <- ggplot(data, aes(x = log2FC, y = neg_log10_p)) +
    
    # 绘制点
    geom_point(aes(color = significance), alpha = 0.7, size = 2) +
    
    # 设置颜色
    scale_color_manual(
      values = c("Up-regulated" = "#E74C3C", 
                 "Down-regulated" = "#3498DB", 
                 "Not Significant" = "#95A5A6"),
      labels = c("上调基因", "下调基因", "无显著差异")
    ) +
    
    # 添加阈值线
    geom_vline(xintercept = c(-FC_THRESHOLD, FC_THRESHOLD), 
               linetype = "dashed", color = "gray50", alpha = 0.8) +
    geom_hline(yintercept = neg_log10_p_threshold, 
               linetype = "dashed", color = "gray50", alpha = 0.8) +
    
    # 标题和标签
    labs(
      title = title,
      x = "log₂(Fold Change)",
      y = "-log₁₀(p-value)",
      color = "基因类型"
    ) +
    
    # 主题设置
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      axis.title = element_text(size = 12, face = "bold"),
      axis.text = element_text(size = 10),
      legend.position = "bottom",
      legend.title = element_text(size = 11, face = "bold"),
      panel.grid.minor = element_blank(),
      panel.border = element_rect(color = "black", fill = NA, size = 0.5)
    )
  
  # 添加基因标注（如果提供）
  if (!is.null(genes_to_label) && nrow(genes_to_label) > 0) {
    p <- p + geom_text_repel(
      data = genes_to_label,
      aes(label = Gene),
      size = 3,
      box.padding = 0.5,
      point.padding = 0.3,
      segment.color = "gray50",
      max.overlaps = 20
    )
  }
  
  return(p)
}

# ==================== 绘制不同风格的火山图 ====================

# 1. 基础火山图
cat("正在绘制基础火山图...\n")
basic_plot <- create_volcano_plot(volcano_data, title = "基础火山图")
print(basic_plot)

# 2. 带基因标注的火山图
cat("正在绘制带标注的火山图...\n")
labeled_plot <- create_volcano_plot(volcano_data, top_genes, 
                                   title = "带基因标注的火山图")
print(labeled_plot)

# 3. 统计信息火山图
n_total <- nrow(volcano_data)
n_up <- sum(volcano_data$significance == "Up-regulated")
n_down <- sum(volcano_data$significance == "Down-regulated")

stats_plot <- create_volcano_plot(
  volcano_data, 
  top_genes,
  title = paste0("差异表达基因分析\n上调: ", n_up, " | 下调: ", n_down, " | 总计: ", n_total)
) +
  # 添加统计注释
  annotate("text", 
           x = max(volcano_data$log2FC) * 0.7, 
           y = max(volcano_data$neg_log10_p) * 0.9,
           label = paste0("阈值:\n|log₂FC| > ", FC_THRESHOLD, "\np < ", P_THRESHOLD),
           size = 3.5, 
           hjust = 0,
           color = "#2C3E50",
           fontface = "bold")

cat("正在绘制统计信息火山图...\n")
print(stats_plot)

# ==================== 科学期刊风格火山图 ====================

journal_plot <- ggplot(volcano_data, aes(x = log2FC, y = neg_log10_p)) +
  geom_point(aes(color = significance), alpha = 0.8, size = 1.5) +
  scale_color_manual(
    values = c("Up-regulated" = "#D32F2F", 
               "Down-regulated" = "#1976D2", 
               "Not Significant" = "#757575"),
    name = "Gene regulation"
  ) +
  geom_vline(xintercept = c(-FC_THRESHOLD, FC_THRESHOLD), 
             linetype = "dashed", color = "black", alpha = 0.6) +
  geom_hline(yintercept = neg_log10_p_threshold, 
             linetype = "dashed", color = "black", alpha = 0.6) +
  geom_text_repel(data = top_genes, 
                  aes(label = Gene), 
                  size = 2.5,
                  box.padding = 0.3,
                  point.padding = 0.2,
                  segment.color = "gray40",
                  max.overlaps = 15) +
  labs(
    title = "Differential Gene Expression Analysis",
    x = expression(log[2]~"(Fold Change)"),
    y = expression(-log[10]~"(p-value)")
  ) +
  theme_classic() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.title = element_text(size = 11, face = "bold"),
    axis.text = element_text(size = 9),
    legend.position = "bottom",
    legend.title = element_text(size = 10, face = "bold"),
    legend.text = element_text(size = 9)
  )

cat("正在绘制期刊风格火山图...\n")
print(journal_plot)

# ==================== 保存图片 ====================

# 创建输出目录
output_dir <- "volcano_plots"
if (!dir.exists(output_dir)) {
  dir.create(output_dir)
  cat("已创建输出目录:", output_dir, "\n")
}

# 保存图片
tryCatch({
  ggsave(file.path(output_dir, "basic_volcano.png"), basic_plot, 
         width = 10, height = 8, dpi = 300)
  ggsave(file.path(output_dir, "labeled_volcano.png"), labeled_plot, 
         width = 12, height = 10, dpi = 300)
  ggsave(file.path(output_dir, "stats_volcano.png"), stats_plot, 
         width = 12, height = 10, dpi = 300)
  ggsave(file.path(output_dir, "journal_volcano.png"), journal_plot, 
         width = 10, height = 8, dpi = 300)
  
  cat("图片已保存到", output_dir, "目录\n")
}, error = function(e) {
  cat("保存图片时出错:", e$message, "\n")
})

# 保存数据
tryCatch({
  write.csv(volcano_data, file.path(output_dir, "volcano_data.csv"), row.names = FALSE)
  write.csv(top_genes, file.path(output_dir, "top_genes.csv"), row.names = FALSE)
  cat("数据已保存到", output_dir, "目录\n")
}, error = function(e) {
  cat("保存数据时出错:", e$message, "\n")
})

# ==================== 输出统计摘要 ====================

cat("\n==================== 火山图分析摘要 ====================\n")
cat("总基因数:", n_total, "\n")
cat("上调基因:", n_up, "(", round(n_up/n_total*100, 1), "%)\n")
cat("下调基因:", n_down, "(", round(n_down/n_total*100, 1), "%)\n")
cat("无显著差异:", n_total - n_up - n_down, "(", round((n_total - n_up - n_down)/n_total*100, 1), "%)\n")
cat("使用阈值: |log₂FC| >", FC_THRESHOLD, ", p <", P_THRESHOLD, "\n")

if (nrow(top_genes) > 0) {
  cat("\n最显著的差异基因:\n")
  print(top_genes[, c("Gene", "log2FC", "p_value", "significance")])
}

cat("\n程序运行完成！\n")
cat("请查看", output_dir, "目录中的结果文件\n")

# ==================== 使用说明 ====================

cat("\n==================== 使用说明 ====================\n")
cat("1. 如需使用自己的数据，请准备CSV文件，包含以下列：\n")
cat("   - Gene: 基因名称\n")
cat("   - log2FC: log2 fold change值\n")
cat("   - p_value: p值\n")
cat("   - significance: 显著性分类（可选）\n\n")

cat("2. 修改阈值参数：\n")
cat("   - FC_THRESHOLD: fold change阈值\n")
cat("   - P_THRESHOLD: p值阈值\n\n")

cat("3. 自定义颜色：\n")
cat("   - 修改scale_color_manual中的values参数\n\n")

cat("4. 输出文件：\n")
cat("   - PNG格式图片文件\n")
cat("   - CSV格式数据文件\n")
