{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\n", "    \"/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/luna16/annotations/luna16_training_annotations.csv\"\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Get train, val and test splits based on stratification by malignancy\n", "train_df, test_df = train_test_split(df, test_size=0.5, random_state=21, stratify=df[\"malignancy\"])\n", "\n", "val_df, test_df = train_test_split(test_df, test_size=0.5, random_state=21, stratify=test_df[\"malignancy\"])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(338, 169, 170)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["len(train_df), len(val_df), len(test_df)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["train_df.to_csv(\"./annotations/task2_train.csv\", index=False)\n", "val_df.to_csv(\"./annotations/task2_val.csv\", index=False)\n", "test_df.to_csv(\"./annotations/task2_test.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "fmcib_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}