#!/usr/bin/env python
# coding: utf-8
#%% 2dCNN-Vit 多任务学习代码,成功
import torch
print(torch.__version__)
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
# get_ipython().run_line_magic('matplotlib', 'inline')
import torchvision
import glob
from torchvision import transforms
from torch.utils import data
from PIL import Image
import torchvision.models as models
import timm
import os
import pandas as pd

#%%
def get_file_list(directory):
    file_list = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.jpg'):
                file_list.append(os.path.join(root, file))
    file_list.sort()  # 按照文件名排序
    return file_list

def get_label_list(file_path):   
    label_file = pd.read_excel(file_path)    
    name_values = label_file['name'].tolist()# 从'name'和'VETC'列中获取数值
    label_values1 = label_file['MVI'].tolist()   
    label_values2 = label_file['VETC'].tolist()
    sorted_label_values1 = [label for _, label in sorted(zip(name_values, label_values1))] # 根据'name'列对数值进行排序 
    sorted_label_values2 = [label for _, label in sorted(zip(name_values, label_values2))]
    label_list1 = sorted_label_values1  # 将排序后的VETC数值存储在label_list中
    label_list2 = sorted_label_values2     
    return label_list1, label_list2

train_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\train"
test_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\test"
train_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\train30.xlsx"
test_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\test.xlsx"

train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5]);# 打印标签列表
print(len(train_image_list));print(len(test_image_list))

# 调用函数获取标签列表
train_label_list1 = get_label_list(train_file_path)[0]
train_label_list2 = get_label_list(train_file_path)[1]
test_label_list1 = get_label_list(test_file_path)[0]
test_label_list2 = get_label_list(test_file_path)[1]
print(train_label_list1[:5]);print(train_label_list2[:5])
print(len(train_label_list1));print(len(test_label_list1))

# 打印类别分布情况
print("Task 1 class distribution:", np.unique(train_label_list1, return_counts=True))
print("Task 2 class distribution:", np.unique(train_label_list2, return_counts=True))

# In[14]:

transforms = transforms.Compose([
    transforms.Resize((96, 96)),
    transforms.RandomHorizontalFlip(p=0.7),
    transforms.RandomRotation(30),
    transforms.RandomAffine(degrees=0, translate=(0.15, 0.15)),
    transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.2),
    transforms.RandomPerspective(distortion_scale=0.2, p=0.5),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# In[15]:
class Multi_output_dataset(data.Dataset):
    def __init__(self, imgs_path, label1, label2):
        self.imgs_path = imgs_path
        self.label1 = label1
        self.label2 = label2

    def __getitem__(self, index):
        img_path = self.imgs_path[index]
        pil_img = Image.open(img_path)
        pil_img = pil_img.convert("RGB")
        pil_img = transforms(pil_img)
        label_1 = self.label1[index]
        label_2 = self.label2[index]
        return pil_img, (label_1, label_2)
    
    def __len__(self):
        return len(self.imgs_path)

# In[16]:
train_dataset = Multi_output_dataset(train_image_list, train_label_list1, train_label_list2)
test_dataset = Multi_output_dataset(test_image_list, test_label_list1, test_label_list2)
print(len(train_dataset), len(test_dataset))

# In[18]:

BTACH_SIZE = 32

# In[19]:

train_dl = torch.utils.data.DataLoader(train_dataset,batch_size=BTACH_SIZE,shuffle=True,num_workers=0, pin_memory=True)

test_dl = torch.utils.data.DataLoader(test_dataset,batch_size=BTACH_SIZE,shuffle=False, num_workers=0, pin_memory=True)

imgs, labels = next(iter(train_dl))

# In[22]:

imgs.shape

labels

# In[24]:
im = imgs[0].permute(1, 2, 0).numpy()

labels[0][0]
labels[1][0]

plt.imshow(im)

# In[28]:

# class Net(nn.Module):
#     def __init__(self, num_classes1, num_classes2):
#         super(Net, self).__init__()
#         resnet50 = models.resnet50(pretrained=True)
#         # Remove the final fully connected layer of ResNet50
#         self.features = nn.Sequential(*list(resnet50.children())[:-1])
#         # Add custom fully connected layers
#         self.fc1 = nn.Linear(2048, num_classes1)
#         self.fc2 = nn.Linear(2048, num_classes2)

#     def forward(self, x):
#         x = self.features(x)
#         x = x.view(x.size(0), -1)
#         x1 = self.fc1(x)
#         x2 = self.fc2(x)
#         return x1, x2

# # Device and model instantiation
# device = "cuda" if torch.cuda.is_available() else "cpu"
# print("Using {} device".format(device))

# model = Net(num_classes1=3, num_classes2=4).to(device)

#%% torchvision系列模型
class CustomModel(nn.Module):
    def __init__(self, model, num_classes1, num_classes2):
        super(CustomModel, self).__init__()
        self.model = model
        self.remove_final_fc_layer()
        in_features = self.get_input_features()
        
        # 共享特征提取层
        self.shared_layers = nn.Sequential(
            nn.Linear(in_features, 1024),
            nn.BatchNorm1d(1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # Task1专用层
        self.task1_layers = nn.Sequential(
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_classes1)
        )
        
        # Task2专用层
        self.task2_layers = nn.Sequential(
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, num_classes2)
        )
        
        # 添加任务权重参数
        self.task1_weight = nn.Parameter(torch.FloatTensor([1.0]))
        self.task2_weight = nn.Parameter(torch.FloatTensor([1.0]))

    def forward(self, x):
        x = self.model(x)
        x = x.view(x.size(0), -1)
        x = self.shared_layers(x)
        x1 = self.task1_layers(x)
        x2 = self.task2_layers(x)
        return x1, x2
    def remove_final_fc_layer(self):
        # Remove the final fully connected layer of the model
        if hasattr(self.model, 'fc'):
            self.model = nn.Sequential(*list(self.model.children())[:-1])
    def get_input_features(self):
        # Function to dynamically determine the number of input features
        with torch.no_grad():
            x = torch.randn(1, 3, 224, 224)  # Assumes input size 3x224x224, modify if necessary
            features = self.model(x)
            return features.view(features.size(0), -1).size(1)

# Device and model instantiation
device = "cuda" if torch.cuda.is_available() else "cpu"
print("Using {} device".format(device))

pretrained_model = models.resnet50(pretrained=True)
# pretrained_model = models.densenet121(pretrained=True)
# pretrained_model = models.swin_v2_b(pretrained=True)
# pretrained_model = models.vit_b_32(pretrained=True)#vit还有vit_b_32，vit_l_16，vit_l_32
# pretrained_model = models.vgg19 (pretrained=True)
# pretrained_model = models.efficientnet_b1(pretrained=True)

model = CustomModel(pretrained_model, num_classes1=2, num_classes2=2).to(device)

print(model)

#%%timm系列模型
# import timm
# from pprint import pprint
# model_names = timm.list_models(pretrained=True)
# pprint(model_names)

# #用于学术资源加速
# import subprocess
# import os
# result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
# output = result.stdout
# for line in output.splitlines():
#     if '=' in line:
#         var, value = line.split('=', 1)
#         os.environ[var] = value
# # unset http_proxy && unset https_proxy #取消学术加速

# pretrained_model = timm.create_model('convit_base', pretrained=True)
# # pretrained_model = timm.create_model('deit_base_distilled_patch16_224', pretrained=True)
# # pretrained_model = timm.create_model('twins_svt_small', pretrained=True)
# print(pretrained_model)

# # pretrained_model = timm.create_model('convit_base', pretrained=False)
# # checkpoint = torch.hub.load_state_dict_from_url(
# #     url="https://dl.fbaipublicfiles.com/convit/convit_base.pth",
# #     map_location="cpu", check_hash=True
# # )
# # pretrained_model.load_state_dict(checkpoint)

# class CustomModel(nn.Module):
#     def __init__(self, model, num_classes1, num_classes2):
#         super(CustomModel, self).__init__()
#         self.model = model
#         in_features = self.model.head.in_features
#         self.remove_final_fc_layer()        
#         self.fc1 = nn.Linear(in_features, num_classes1)
#         self.fc2 = nn.Linear(in_features, num_classes2)
    
#     def forward(self, x):
#         x = self.model(x)        
#         x = x.view(x.size(0), -1)
#         x1 = self.fc1(x)
#         x2 = self.fc2(x)
#         return x1, x2
    
#     def remove_final_fc_layer(self):
#         # Remove the final fully connected layer of the model
#         self.model.head = nn.Identity()

# # Device and model instantiation
# device = "cuda" if torch.cuda.is_available() else "cpu"
# print("Using {} device".format(device))

# model = CustomModel(pretrained_model, num_classes1=2, num_classes2=2).to(device)
# print(model)


#%%此处使用训练代码1

# loss_fn = nn.CrossEntropyLoss()
# optimizer = torch.optim.AdamW(model.parameters(), lr=0.00001, weight_decay=1e-5)  # weight_decay 为 L2 正则化参数
# scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)


# def train(dataloader, model, loss_fn, optimizer, scheduler):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     train_loss, correct1, correct2 = 0, 0, 0
#     model.train()
#     for X, y in dataloader:
#         y1 = y[0]
#         y2 = y[1]
#         X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
#         # Compute prediction error
#         pred = model(X)
#         loss1 = loss_fn(pred[0], y1)
#         loss2 = loss_fn(pred[1], y2)
#         loss = loss1 + loss2

#         # Backpropagation
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
        
#         with torch.no_grad():
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
#             train_loss += loss.item()
    
#     train_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
    
#     # 更新学习率
#     scheduler.step(train_loss)  # 使用训练损失来更新学习率
    
#     return train_loss, correct1, correct2

# # 测试代码
# def test(dataloader, model):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     model.eval()
#     test_loss, correct1, correct2 = 0, 0, 0
#     with torch.no_grad():
#         for X, y in dataloader:
#             y1 = y[0]
#             y2 = y[1]
#             X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
#             pred = model(X)
#             loss1 = loss_fn(pred[0], y1)
#             loss2 = loss_fn(pred[1], y2)
#             loss = loss1 + loss2
#             test_loss += loss.item()
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
    
#     test_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
#     return test_loss, correct1, correct2


#%%根据类别权重训练代码2
# 计算类别权重
# class_counts = [len(train_label_list1), len(train_label_list2)]  # 假设您有两个类别的样本数量
# class_weights = [sum(class_counts) / count for count in class_counts]
# class_weights = torch.FloatTensor(class_weights).to(device)

# # 在损失函数中使用类别权重
# loss_fn = nn.CrossEntropyLoss(weight=class_weights)
# optimizer = torch.optim.AdamW(model.parameters(), lr=0.00001, weight_decay=1e-5)  # weight_decay 为 L2 正则化参数
# scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)

# # 计算每个任务的类别权重
# def calculate_class_weights(labels):
#     # 统计每个类别的样本数量
#     unique_labels, counts = np.unique(labels, return_counts=True)
#     total_samples = sum(counts)
#     # 计算权重：样本数量越少，权重越大
#     weights = total_samples / (len(unique_labels) * counts)
#     # 归一化权重
#     weights = weights / weights.sum()
#     return torch.FloatTensor(weights).to(device)

# # 计算两个任务的类别权重
# weights1 = calculate_class_weights(train_label_list1)
# weights2 = calculate_class_weights(train_label_list2)

# # 创建两个带权重的损失函数
# loss_fn1 = nn.CrossEntropyLoss(weight=weights1)
# loss_fn2 = nn.CrossEntropyLoss(weight=weights2)

# # 在训练循环中使用
# def train(dataloader, model, loss_fn1, loss_fn2, optimizer, scheduler):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     train_loss, correct1, correct2 = 0, 0, 0
#     model.train()
#     for X, y in dataloader:
#         y1 = y[0]
#         y2 = y[1]
#         X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
        
#         # Compute prediction error
#         pred = model(X)
#         # 使用各自的权重损失函数
#         loss1 = loss_fn1(pred[0], y1)
#         loss2 = loss_fn2(pred[1], y2)
#         # 可以调整两个任务的权重
#         loss = loss1 + loss2  # 或者使用不同的权重，如: 0.5 * loss1 + 0.5 * loss2

#         # Backpropagation
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
        
#         with torch.no_grad():
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
#             train_loss += loss.item()
    
#     train_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
    
#     # 更新学习率
#     scheduler.step(train_loss)
    
#     return train_loss, correct1, correct2

# # 测试函数也需要相应修改
# def test(dataloader, model, loss_fn1, loss_fn2):
#     size = len(dataloader.dataset)
#     num_batches = len(dataloader)
#     model.eval()
#     test_loss, correct1, correct2 = 0, 0, 0
#     with torch.no_grad():
#         for X, y in dataloader:
#             y1 = y[0]
#             y2 = y[1]
#             X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
#             pred = model(X)
#             loss1 = loss_fn1(pred[0], y1)
#             loss2 = loss_fn2(pred[1], y2)
#             loss = loss1 + loss2
#             test_loss += loss.item()
#             correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
#             correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
    
#     test_loss /= num_batches
#     correct1 /= size
#     correct2 /= size
#     return test_loss, correct1, correct2

# epochs = 100

# train_loss = []
# train_acc1 = []
# train_acc2 = []
# test_loss = []
# test_acc1 = []
# test_acc2 = []

# for epoch in range(epochs):
#     epoch_loss, epoch_acc1, epoch_acc2 = train(train_dl, model, loss_fn1, loss_fn2, optimizer, scheduler)
#     epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model,loss_fn1, loss_fn2)
#     train_loss.append(epoch_loss)
#     train_acc1.append(epoch_acc1)
#     train_acc2.append(epoch_acc2)
#     test_loss.append(epoch_test_loss)
#     test_acc1.append(epoch_test_acc1)
#     test_acc2.append(epoch_test_acc2)
    
#     template = ("epoch:{:2d}, train_loss: {:.5f}, train_acc1: {:.1f}% , train_acc2: {:.1f}% ," 
#                 "test_loss: {:.5f}, test_acc1: {:.1f}%, test_acc2: {:.1f}%")
#     print(template.format(
#           epoch, epoch_loss, epoch_acc1*100, epoch_acc2*100, 
#          epoch_test_loss, epoch_test_acc1*100, epoch_test_acc2*100))
    
# print("Done!")

#%%训练代码3
# 添加自动调参功能
class AutoMLConfig:
    def __init__(self, dataset_size, input_shape):
        self.dataset_size = dataset_size
        self.input_shape = input_shape
        
    def get_optimal_batch_size(self):
        # 减小batch size以增加随机性
        if self.dataset_size < 1000:
            return 8
        elif self.dataset_size < 5000:
            return 16
        else:
            return 32
            
    def get_optimal_learning_rate(self):
        # 调整学习率范围
        return [1e-4, 5e-3]
        
    def get_optimal_augmentation(self):
        # 增强数据增强的强度
        return {
            'rotation_range': [-30, 30],
            'flip_prob': 0.7,
            'brightness_range': [0.7, 1.3]
        }

# 在训练前初始化自动调参
automl_config = AutoMLConfig(
    dataset_size=len(train_dataset),
    input_shape=(3, 96, 96)
)

# 使用自动调参配置
BATCH_SIZE = automl_config.get_optimal_batch_size()

# 修改训练参数
epochs = 200  # 减少训练轮数
patience = 20  # 减少早停耐心值

# 修改优化器参数
optimizer = torch.optim.AdamW(
    model.parameters(),
    lr=0.0005,  # 调整基础学习率
    weight_decay=0.01,  # 减小权重衰减
    betas=(0.9, 0.999),  # 调整动量参数
    eps=1e-8
)

# 修改学习率调度器
scheduler = torch.optim.lr_scheduler.OneCycleLR(
    optimizer,
    max_lr=0.001,  # 降低最大学习率
    epochs=epochs,
    steps_per_epoch=len(train_dl),
    pct_start=0.2,  # 减少预热时间
    anneal_strategy='cos',
    div_factor=25.0,  # 初始学习率 = max_lr/div_factor
    final_div_factor=1000.0  # 最终学习率 = max_lr/(div_factor*final_div_factor)
)

# 修改损失函数为带权重的交叉熵
def calculate_class_weights(labels):
    unique_labels, counts = np.unique(labels, return_counts=True)
    weights = 1.0 / counts
    weights = weights / weights.sum() * len(weights)
    return torch.FloatTensor(weights).to(device)

# 计算每个任务的类别权重
weights1 = calculate_class_weights(train_label_list1)
weights2 = calculate_class_weights(train_label_list2)

loss_fn1 = nn.CrossEntropyLoss(weight=weights1)
loss_fn2 = nn.CrossEntropyLoss(weight=weights2)

# 修改训练函数
def train(dataloader, model, loss_fn1, loss_fn2, optimizer, scheduler):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    train_loss, correct1, correct2 = 0, 0, 0
    model.train()
    
    for X, y in dataloader:
        y1, y2 = y[0], y[1]
        X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
        
        # 计算预测和损失
        pred1, pred2 = model(X)
        
        # 使用带权重的损失函数
        loss1 = loss_fn1(pred1, y1)
        loss2 = loss_fn2(pred2, y2)
        
        # 使用动态任务权重
        task1_weight = torch.exp(model.task1_weight)
        task2_weight = torch.exp(model.task2_weight)
        total_weight = task1_weight + task2_weight
        
        # 归一化权重
        task1_weight = task1_weight / total_weight
        task2_weight = task2_weight / total_weight
        
        # 组合损失
        loss = task1_weight * loss1 + task2_weight * loss2
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        
        optimizer.step()
        scheduler.step()
        
        # 统计
        with torch.no_grad():
            correct1 += (pred1.argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred2.argmax(1) == y2).type(torch.float).sum().item()
            train_loss += loss.item()
    
    return train_loss/num_batches, correct1/size, correct2/size

# 修改测试函数
def test(dataloader, model, loss_fn1, loss_fn2):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss, correct1, correct2 = 0, 0, 0
    
    with torch.no_grad():
        for X, y in dataloader:
            y1, y2 = y[0], y[1]
            X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
            
            pred1, pred2 = model(X)
            loss1 = loss_fn1(pred1, y1)
            loss2 = loss_fn2(pred2, y2)
            
            task1_weight = torch.exp(model.task1_weight)
            task2_weight = torch.exp(model.task2_weight)
            total_weight = task1_weight + task2_weight
            
            task1_weight = task1_weight / total_weight
            task2_weight = task2_weight / total_weight
            
            loss = task1_weight * loss1 + task2_weight * loss2
            
            test_loss += loss.item()
            correct1 += (pred1.argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred2.argmax(1) == y2).type(torch.float).sum().item()
    
    test_loss /= num_batches
    correct1 /= size
    correct2 /= size
    return test_loss, correct1, correct2


# In[36]:
epochs = 200
patience = 50  # 减小早停耐心值
best_val_loss = float('inf')
best_epoch = 0
no_improve_count = 0

# 保存训练和测试指标
train_loss = []
train_acc1 = []
train_acc2 = []
test_loss = []
test_acc1 = []
test_acc2 = []

# 创建模型保存路径
model_dir = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\models_automl"
os.makedirs(model_dir, exist_ok=True)

# 最佳模型路径
best_model_path = os.path.join(model_dir, "best_model_automl.pth")
final_model_path = os.path.join(model_dir, "final_model_automl.pth")

# 创建训练日志文件
training_log_path = os.path.join(model_dir, "training_log.txt")
with open(training_log_path, 'w', encoding='utf-8') as f:
    f.write("Training Log\n")
    f.write("============\n\n")
    f.write("Epoch  Train Loss  Train Acc1  Train Acc2  Test Loss  Test Acc1  Test Acc2\n")
    f.write("=================================================================\n")

for epoch in range(epochs):
    epoch_loss, epoch_acc1, epoch_acc2 = train(train_dl, model, loss_fn1, loss_fn2, optimizer, scheduler)
    epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model, loss_fn1, loss_fn2)
    
    # 记录指标
    train_loss.append(epoch_loss)
    train_acc1.append(epoch_acc1)
    train_acc2.append(epoch_acc2)
    test_loss.append(epoch_test_loss)
    test_acc1.append(epoch_test_acc1)
    test_acc2.append(epoch_test_acc2)
    
    # 打印当前训练状态
    template = ("epoch:{:2d}, train_loss: {:.5f}, train_acc1: {:.1f}% , train_acc2: {:.1f}% ," 
                "test_loss: {:.5f}, test_acc1: {:.1f}%, test_acc2: {:.1f}%")
    print(template.format(
          epoch, epoch_loss, epoch_acc1*100, epoch_acc2*100, 
          epoch_test_loss, epoch_test_acc1*100, epoch_test_acc2*100))
    
    # 将每个epoch的结果写入日志文件
    with open(training_log_path, 'a', encoding='utf-8') as f:
        f.write(f"{epoch:5d}  {epoch_loss:.6f}  {epoch_acc1*100:9.2f}  {epoch_acc2*100:9.2f}  "
                f"{epoch_test_loss:.6f}  {epoch_test_acc1*100:8.2f}  {epoch_test_acc2*100:8.2f}\n")
    
    # 检查是否是最佳模型
    if epoch_test_loss < best_val_loss:
        best_val_loss = epoch_test_loss
        best_epoch = epoch
        no_improve_count = 0
        
        # 保存最佳模型
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'train_loss': train_loss,
            'train_acc1': train_acc1,
            'train_acc2': train_acc2,
            'test_loss': test_loss,
            'test_acc1': test_acc1,
            'test_acc2': test_acc2,
            'best_val_loss': best_val_loss,
            'task1_weight': model.task1_weight.item(),
            'task2_weight': model.task2_weight.item(),
        }, best_model_path)
        
        print(f"Saved best model at epoch {epoch} with validation loss: {best_val_loss:.5f}")
        
        # 记录最佳模型信息到日志文件
        with open(training_log_path, 'a', encoding='utf-8') as f:
            f.write(f"\nNew best model saved at epoch {epoch} with validation loss: {best_val_loss:.5f}\n\n")
    else:
        no_improve_count += 1
        print(f"No improvement for {no_improve_count} epochs. Best epoch: {best_epoch}")
    
    # 早停检查
    if no_improve_count >= patience:
        print(f"Early stopping triggered after {epoch+1} epochs. Best epoch: {best_epoch}")
        # 记录早停信息到日志文件
        with open(training_log_path, 'a', encoding='utf-8') as f:
            f.write(f"\nEarly stopping triggered after {epoch+1} epochs. Best epoch: {best_epoch}\n")
        break

# 保存最终模型
torch.save({
    'epoch': epoch,
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'scheduler_state_dict': scheduler.state_dict(),
    'train_loss': train_loss,
    'train_acc1': train_acc1,
    'train_acc2': train_acc2,
    'test_loss': test_loss,
    'test_acc1': test_acc1,
    'test_acc2': test_acc2,
    'best_val_loss': best_val_loss,
    'best_epoch': best_epoch,
    'task1_weight': model.task1_weight.item(),
    'task2_weight': model.task2_weight.item(),
}, final_model_path)

print(f"Training completed. Final model saved at epoch {epoch}.")
print(f"Best model was at epoch {best_epoch} with validation loss: {best_val_loss:.5f}")


#%% 绘制损失曲线
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.plot(range(1, len(train_loss)+1), train_loss, label='train_loss')
plt.plot(range(1, len(test_loss)+1), test_loss, label='test_loss')
plt.axvline(x=best_epoch+1, color='r', linestyle='--', label=f'Best epoch ({best_epoch+1})')
plt.legend()
plt.title("Loss Curves")

# 绘制准确率曲线
plt.subplot(1, 2, 2)
plt.plot(range(1, len(train_acc1)+1), train_acc1, label='train_task1_acc')
plt.plot(range(1, len(test_acc1)+1), test_acc1, label='test_task1_acc')
plt.plot(range(1, len(train_acc2)+1), train_acc2, label='train_task2_acc')
plt.plot(range(1, len(test_acc2)+1), test_acc2, label='test_task2_acc')
plt.axvline(x=best_epoch+1, color='r', linestyle='--', label=f'Best epoch ({best_epoch+1})')
plt.legend(loc='lower right', fontsize='small')
plt.title("Accuracy Curves")

plt.tight_layout()
plt.savefig(os.path.join(model_dir, "training_curves.png"), dpi=300)
plt.show()

#%% 定义获取预测结果的函数
def get_predictions(model, dataloader, image_paths):
    model.eval()
    true_label1 = []
    true_label2 = []
    predict_label1 = []
    predict_label2 = []
    predict_prob1 = []
    predict_prob2 = []
    
    with torch.no_grad():
        for x, (y1, y2) in dataloader:
            if torch.cuda.is_available():
                x = x.to('cuda')
                y1 = y1.to('cuda')
                y2 = y2.to('cuda')
            
            y_pred1, y_pred2 = model(x)
            _, y_pred_class1 = torch.max(y_pred1, dim=1)
            _, y_pred_class2 = torch.max(y_pred2, dim=1)
            
            # 获取预测概率
            y_pred_prob1 = torch.softmax(y_pred1, dim=1)
            y_pred_prob2 = torch.softmax(y_pred2, dim=1)
            
            true_label1.extend(y1.cpu().tolist())
            true_label2.extend(y2.cpu().tolist())
            predict_label1.extend(y_pred_class1.cpu().tolist())
            predict_label2.extend(y_pred_class2.cpu().tolist())
            predict_prob1.extend(y_pred_prob1.cpu().tolist())
            predict_prob2.extend(y_pred_prob2.cpu().tolist())
    
    return true_label1, true_label2, predict_label1, predict_label2, predict_prob1, predict_prob2

# 保存预测结果的函数
def save_predictions(model, train_dataset, test_dataset, prefix=""):
    # 创建数据加载器，不需要使用indices
    new_train_dl = torch.utils.data.DataLoader(train_dataset, batch_size=16, shuffle=False)
    test_dl = torch.utils.data.DataLoader(test_dataset, batch_size=16, shuffle=False)

    # 直接使用dataset的imgs_path属性
    train_image_paths = train_dataset.imgs_path
    test_image_paths = test_dataset.imgs_path
    
    # 获取预测结果
    train_true_label1, train_true_label2, train_predict_label1, train_predict_label2, train_predict_prob1, train_predict_prob2 = get_predictions(model, new_train_dl, train_image_paths)
    test_true_label1, test_true_label2, test_predict_label1, test_predict_label2, test_predict_prob1, test_predict_prob2 = get_predictions(model, test_dl, test_image_paths)

    # 创建结果DataFrame
    train_data = {
        'Image Path': train_image_paths,
        'True Label1': train_true_label1,
        'Predict Label1': train_predict_label1,
        'True Label2': train_true_label2,
        'Predict Label2': train_predict_label2,
        'Predict Prob1': train_predict_prob1,
        'Predict Prob2': train_predict_prob2
    }

    test_data = {
        'Image Path': test_image_paths,
        'True Label1': test_true_label1,
        'Predict Label1': test_predict_label1,
        'True Label2': test_true_label2,
        'Predict Label2': test_predict_label2,
        'Predict Prob1': test_predict_prob1,
        'Predict Prob2': test_predict_prob2
    }

    # 保存结果
    train_results = pd.DataFrame(train_data)
    test_results = pd.DataFrame(test_data)

    train_results.to_excel(os.path.join(model_dir, f"{prefix}train_results.xlsx"), index=False)
    test_results.to_excel(os.path.join(model_dir, f"{prefix}test_results.xlsx"), index=False)
    
    # 计算并返回评估指标
    train_acc1 = sum(1 for true, pred in zip(train_true_label1, train_predict_label1) if true == pred) / len(train_true_label1)
    train_acc2 = sum(1 for true, pred in zip(train_true_label2, train_predict_label2) if true == pred) / len(train_true_label2)
    test_acc1 = sum(1 for true, pred in zip(test_true_label1, test_predict_label1) if true == pred) / len(test_true_label1)
    test_acc2 = sum(1 for true, pred in zip(test_true_label2, test_predict_label2) if true == pred) / len(test_true_label2)
    
    return train_acc1, train_acc2, test_acc1, test_acc2

# 加载最佳模型并保存其预测结果
print("Generating predictions for best model...")
best_checkpoint = torch.load(best_model_path)
model.load_state_dict(best_checkpoint['model_state_dict'])
best_train_acc1, best_train_acc2, best_test_acc1, best_test_acc2 = save_predictions(model, train_dataset, test_dataset, prefix="best_")

# 加载最终模型并保存其预测结果
print("Generating predictions for final model...")
final_checkpoint = torch.load(final_model_path)
model.load_state_dict(final_checkpoint['model_state_dict'])
final_train_acc1, final_train_acc2, final_test_acc1, final_test_acc2 = save_predictions(model, train_dataset, test_dataset, prefix="final_")

# 打印最佳模型和最终模型的性能比较
print("\nModel Performance Comparison:")
print(f"Best Model (Epoch {best_epoch}):")
print(f"  Train Acc Task1: {best_train_acc1*100:.2f}%, Train Acc Task2: {best_train_acc2*100:.2f}%")
print(f"  Test Acc Task1: {best_test_acc1*100:.2f}%, Test Acc Task2: {best_test_acc2*100:.2f}%")
print(f"Final Model (Epoch {epoch}):")
print(f"  Train Acc Task1: {final_train_acc1*100:.2f}%, Train Acc Task2: {final_train_acc2*100:.2f}%")
print(f"  Test Acc Task1: {final_test_acc1*100:.2f}%, Test Acc Task2: {final_test_acc2*100:.2f}%")

#%% 保存结果到txt文件
results_txt = os.path.join(model_dir, "model_performance.txt")
with open(results_txt, 'w', encoding='utf-8') as f:
    f.write("Model Performance Comparison\n")
    f.write("===========================\n\n")
    
    # 添加训练过程总结
    f.write("Training Summary\n")
    f.write("---------------\n")
    f.write(f"Total Epochs Run: {epoch + 1}\n")
    f.write(f"Best Epoch: {best_epoch}\n")
    f.write(f"Best Validation Loss: {best_val_loss:.6f}\n")
    f.write(f"Early Stopping: {'Yes' if no_improve_count >= patience else 'No'}\n\n")
    
    f.write("Best Model Performance (Epoch {best_epoch}):\n")
    f.write(f"  Train Acc Task1: {best_train_acc1*100:.2f}%\n")
    f.write(f"  Train Acc Task2: {best_train_acc2*100:.2f}%\n")
    f.write(f"  Test Acc Task1: {best_test_acc1*100:.2f}%\n")
    f.write(f"  Test Acc Task2: {best_test_acc2*100:.2f}%\n\n")
    
    f.write("Final Model Performance (Epoch {epoch}):\n")
    f.write(f"  Train Acc Task1: {final_train_acc1*100:.2f}%\n")
    f.write(f"  Train Acc Task2: {final_train_acc2*100:.2f}%\n")
    f.write(f"  Test Acc Task1: {final_test_acc1*100:.2f}%\n")
    f.write(f"  Test Acc Task2: {final_test_acc2*100:.2f}%\n\n")
    
    # 添加训练配置信息
    f.write("Training Configuration\n")
    f.write("=====================\n")
    f.write(f"Total Epochs: {epochs}\n")
    f.write(f"Early Stopping Patience: {patience}\n")
    f.write(f"Batch Size: {BATCH_SIZE}\n")
    f.write(f"Initial Learning Rate: {optimizer.param_groups[0]['lr']:.6f}\n")
    f.write(f"Weight Decay: {optimizer.param_groups[0]['weight_decay']}\n")
    f.write(f"Final Task1 Weight: {model.task1_weight.item():.4f}\n")
    f.write(f"Final Task2 Weight: {model.task2_weight.item():.4f}\n\n")
    
    # 添加数据集信息
    f.write("Dataset Information\n")
    f.write("==================\n")
    f.write(f"Training Set Size: {len(train_dataset)}\n")
    f.write(f"Test Set Size: {len(test_dataset)}\n\n")
    
    # 添加类别分布信息
    f.write("Class Distribution\n")
    f.write("=================\n")
    f.write("Task 1:\n")
    unique_labels1, counts1 = np.unique(train_label_list1, return_counts=True)
    for label, count in zip(unique_labels1, counts1):
        f.write(f"  Class {label}: {count} samples\n")
    f.write("\nTask 2:\n")
    unique_labels2, counts2 = np.unique(train_label_list2, return_counts=True)
    for label, count in zip(unique_labels2, counts2):
        f.write(f"  Class {label}: {count} samples\n")

print(f"\nResults have been saved to:")
print(f"1. Training log: {training_log_path}")
print(f"2. Final performance report: {results_txt}")
print("Done!")


# %%
