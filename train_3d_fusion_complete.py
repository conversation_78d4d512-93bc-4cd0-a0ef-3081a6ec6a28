"""
3D ResNet + 3D ViT 融合模型完整训练脚本
整合了所有功能，可直接运行
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.optim.lr_scheduler import _LRScheduler, CosineAnnealingLR, StepLR
import pandas as pd
import numpy as np
from torchvision.models.video import r3d_18
import monai
from monai.data import DataLoader, Dataset
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, 
    RandRotate90d, RandFlipd, RandZoomd, RandShiftIntensityd,
    Compose, EnsureTyped
)
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns
import copy
import time
from datetime import datetime
from einops import rearrange

# 导入配置
from config_3d_fusion import get_config, print_config, validate_config

# 设置环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# ==================== 模型定义 ====================
class Simple3DViT(nn.Module):
    """简化的3D Vision Transformer"""
    def __init__(self, image_size=(96, 96, 64), patch_size=16, dim=512, depth=6, heads=8, mlp_dim=1024, num_classes=3):
        super().__init__()
        
        self.patch_size = patch_size
        num_patches = (image_size[0] // patch_size) * (image_size[1] // patch_size) * (image_size[2] // patch_size)
        patch_dim = patch_size ** 3
        
        self.patch_embedding = nn.Linear(patch_dim, dim)
        self.pos_embedding = nn.Parameter(torch.randn(1, num_patches + 1, dim))
        self.cls_token = nn.Parameter(torch.randn(1, 1, dim))
        
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=dim,
                nhead=heads,
                dim_feedforward=mlp_dim,
                dropout=0.1,
                batch_first=True
            ),
            num_layers=depth
        )
        
        self.norm = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, num_classes)
        
    def forward(self, x, return_features=False):
        B, C, H, W, D = x.shape
        p = self.patch_size
        
        x = rearrange(x, 'b c (h p1) (w p2) (d p3) -> b (h w d) (p1 p2 p3 c)', p1=p, p2=p, p3=p)
        x = self.patch_embedding(x)
        
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)
        x += self.pos_embedding
        
        x = self.transformer(x)
        features = self.norm(x[:, 0])
        
        if return_features:
            return features
            
        return self.head(features)

class ResNet3DExtractor(nn.Module):
    """3D ResNet特征提取器"""
    def __init__(self, pretrained=True):
        super().__init__()
        self.backbone = r3d_18(pretrained=pretrained)
        self.backbone.fc = nn.Identity()
        
        # 冻结前面的层
        for param in list(self.backbone.parameters())[:-20]:
            param.requires_grad = False
            
    def forward(self, x):
        if x.size(1) == 1:
            x = x.expand(-1, 3, -1, -1, -1)
        return self.backbone(x)

class ResNetViTFusion(nn.Module):
    """ResNet和ViT融合模型"""
    def __init__(self, config):
        super().__init__()
        
        self.resnet_extractor = ResNet3DExtractor(pretrained=True)
        self.vit_extractor = Simple3DViT(
            image_size=config.IMAGE_SIZE,
            patch_size=config.VIT_CONFIG['patch_size'],
            dim=config.VIT_CONFIG['dim'],
            depth=config.VIT_CONFIG['depth'],
            heads=config.VIT_CONFIG['heads'],
            mlp_dim=config.VIT_CONFIG['mlp_dim'],
            num_classes=config.NUM_CLASSES
        )
        
        self.fusion_method = config.FUSION_METHOD
        
        # 特征适配层
        self.resnet_adapter = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.vit_adapter = nn.Sequential(
            nn.Linear(config.VIT_CONFIG['dim'], 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 融合后的分类器
        if self.fusion_method == 'concat':
            classifier_input_dim = 512
        else:
            classifier_input_dim = 256
            
        self.classifier = nn.Sequential(
            nn.Linear(classifier_input_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, config.NUM_CLASSES)
        )
        
        if self.fusion_method == 'attention':
            self.attention = nn.Sequential(
                nn.Linear(512, 128),
                nn.ReLU(),
                nn.Linear(128, 2),
                nn.Softmax(dim=1)
            )
    
    def forward(self, x):
        # ResNet特征提取
        resnet_features = self.resnet_extractor(x)
        resnet_features = self.resnet_adapter(resnet_features)
        
        # ViT特征提取
        vit_features = self.vit_extractor(x, return_features=True)
        vit_features = self.vit_adapter(vit_features)
        
        # 特征融合
        if self.fusion_method == 'concat':
            fused_features = torch.cat([resnet_features, vit_features], dim=1)
        elif self.fusion_method == 'add':
            fused_features = resnet_features + vit_features
        elif self.fusion_method == 'attention':
            concat_features = torch.cat([resnet_features, vit_features], dim=1)
            weights = self.attention(concat_features)
            fused_features = weights[:, 0:1] * resnet_features + weights[:, 1:2] * vit_features
        
        output = self.classifier(fused_features)
        return output

# ==================== 损失函数 ====================
class FocalLoss(nn.Module):
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        return focal_loss

class LabelSmoothingLoss(nn.Module):
    def __init__(self, num_classes, smoothing=0.1):
        super().__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        
    def forward(self, inputs, targets):
        log_prob = F.log_softmax(inputs, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.num_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        return (-true_dist * log_prob).sum(dim=-1).mean()

def get_loss_function(config):
    """根据配置获取损失函数"""
    loss_config = config.LOSS_CONFIG
    
    if loss_config['type'] == 'ce':
        return nn.CrossEntropyLoss()
    elif loss_config['type'] == 'focal':
        return FocalLoss(alpha=loss_config['focal_alpha'], gamma=loss_config['focal_gamma'])
    elif loss_config['type'] == 'label_smooth':
        return LabelSmoothingLoss(config.NUM_CLASSES, smoothing=loss_config['label_smoothing'])
    elif loss_config['type'] == 'combined':
        focal_loss = FocalLoss(alpha=loss_config['focal_alpha'], gamma=loss_config['focal_gamma'])
        smooth_loss = LabelSmoothingLoss(config.NUM_CLASSES, smoothing=loss_config['label_smoothing'])
        
        def combined_loss(outputs, targets):
            w1, w2 = loss_config['loss_weights']
            return w1 * focal_loss(outputs, targets) + w2 * smooth_loss(outputs, targets)
        
        return combined_loss
    else:
        return nn.CrossEntropyLoss()

# ==================== 学习率调度器 ====================
class PolyLRScheduler(_LRScheduler):
    def __init__(self, optimizer, max_epochs, power=0.9, min_lr=1e-6):
        self.max_epochs = max_epochs
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer)

    def get_lr(self):
        return [max(self.min_lr, base_lr * (1 - self.last_epoch / self.max_epochs) ** self.power)
                for base_lr in self.base_lrs]

def get_scheduler(optimizer, config):
    """根据配置获取学习率调度器"""
    if config.SCHEDULER_TYPE == 'cosine':
        return CosineAnnealingLR(optimizer, T_max=config.NUM_EPOCHS)
    elif config.SCHEDULER_TYPE == 'step':
        return StepLR(optimizer, step_size=config.NUM_EPOCHS//3, gamma=0.1)
    elif config.SCHEDULER_TYPE == 'poly':
        return PolyLRScheduler(optimizer, max_epochs=config.NUM_EPOCHS)
    else:
        return None

# ==================== 早停机制 ====================
class EarlyStopping:
    def __init__(self, patience=15, min_delta=1e-4):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = None
        self.early_stop = False
        self.best_model = None

    def __call__(self, val_loss, model):
        if self.best_loss is None:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
        elif val_loss > self.best_loss - self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
            self.counter = 0

# ==================== 数据处理 ====================
def create_dataset(data_dir, label_excel, config):
    """创建数据集"""
    df = pd.read_excel(label_excel)
    
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    data = []
    for img_path in image_files:
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]
        file_names = file_name.split('-')[0]
        
        if file_names in df[config.NAME_COLUMN].values:
            label = df.loc[df[config.NAME_COLUMN] == file_names, config.LABEL_COLUMN].values[0]
            data.append({"image": img_path, "label": label, "file_name": file_names})
    
    return data

def get_transforms(config):
    """获取数据变换"""
    aug_config = config.DATA_AUGMENTATION
    intensity_config = config.INTENSITY_RANGE
    
    train_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=config.IMAGE_SIZE),
        ScaleIntensityRanged(
            keys=["image"], 
            a_min=intensity_config['a_min'], 
            a_max=intensity_config['a_max'], 
            b_min=intensity_config['b_min'], 
            b_max=intensity_config['b_max'], 
            clip=True
        ),
        RandRotate90d(keys=["image"], prob=aug_config['rotation_prob'], spatial_axes=[0, 1]),
        RandFlipd(keys=["image"], prob=aug_config['flip_prob'], spatial_axis=0),
        RandZoomd(keys=["image"], prob=aug_config['zoom_prob'], 
                 min_zoom=aug_config['zoom_range'][0], max_zoom=aug_config['zoom_range'][1]),
        RandShiftIntensityd(keys=["image"], offsets=aug_config['intensity_shift_offset'], 
                           prob=aug_config['intensity_shift_prob']),
        EnsureTyped(keys=["image"])
    ])

    val_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=config.IMAGE_SIZE),
        ScaleIntensityRanged(
            keys=["image"], 
            a_min=intensity_config['a_min'], 
            a_max=intensity_config['a_max'], 
            b_min=intensity_config['b_min'], 
            b_max=intensity_config['b_max'], 
            clip=True
        ),
        EnsureTyped(keys=["image"])
    ])
    
    return train_transforms, val_transforms

# ==================== 训练函数 ====================
def train_model(model, train_loader, val_loader, config):
    """训练模型"""
    
    device = torch.device(config.DEVICE)
    model = model.to(device)
    
    # 损失函数和优化器
    criterion = get_loss_function(config)
    optimizer = optim.AdamW(model.parameters(), lr=config.LEARNING_RATE, weight_decay=config.WEIGHT_DECAY)
    scheduler = get_scheduler(optimizer, config)
    
    # 早停
    early_stopping = EarlyStopping(
        patience=config.EARLY_STOPPING['patience'], 
        min_delta=config.EARLY_STOPPING['min_delta']
    )
    
    # 记录训练历史
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': [],
        'lr': []
    }
    
    best_acc = 0.0
    start_time = time.time()
    
    # 创建保存目录
    os.makedirs(config.SAVE_DIR, exist_ok=True)
    
    for epoch in range(config.NUM_EPOCHS):
        epoch_start_time = time.time()
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        current_lr = optimizer.param_groups[0]['lr']
        history['lr'].append(current_lr)
        
        print(f'\\nEpoch [{epoch+1}/{config.NUM_EPOCHS}] - LR: {current_lr:.2e}')
        print('-' * 60)
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(device)
            labels = batch['label'].long().to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            if config.GRAD_CLIP_NORM > 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config.GRAD_CLIP_NORM)
            
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            # 打印训练进度
            if batch_idx % config.LOG_INTERVAL == 0:
                print(f'Batch [{batch_idx}/{len(train_loader)}] - Loss: {loss.item():.4f}')
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device)
                labels = batch['label'].long().to(device)
                
                outputs = model(images)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # 更新学习率
        if scheduler:
            scheduler.step()
        
        # 计算指标
        train_loss = train_loss / len(train_loader)
        train_acc = 100 * train_correct / train_total
        val_loss = val_loss / len(val_loader)
        val_acc = 100 * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        epoch_time = time.time() - epoch_start_time
        
        print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        print(f'Epoch Time: {epoch_time:.2f}s')
        
        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            model_path = os.path.join(config.SAVE_DIR, f'{config.MODEL_NAME}_best.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'config': config
            }, model_path)
            print(f'✓ Best model saved! Val Acc: {best_acc:.2f}%')
            
            # 打印分类报告
            if epoch % 10 == 0 or epoch == config.NUM_EPOCHS - 1:
                print("\\nClassification Report:")
                print(classification_report(all_labels, all_predictions, 
                                          target_names=[f'Class_{i}' for i in range(config.NUM_CLASSES)]))
        
        # 早停检查
        early_stopping(val_loss, model)
        if early_stopping.early_stop:
            print("\\n🛑 Early stopping triggered")
            model.load_state_dict(early_stopping.best_model)
            break
    
    total_time = time.time() - start_time
    print(f'\\n🎉 Training completed! Total time: {total_time/3600:.2f}h')
    print(f'Best validation accuracy: {best_acc:.2f}%')
    
    return history, best_acc

# ==================== 可视化函数 ====================
def plot_training_history(history, save_path=None):
    """绘制训练历史"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 损失曲线
    axes[0, 0].plot(history['train_loss'], label='Train Loss', color='blue')
    axes[0, 0].plot(history['val_loss'], label='Val Loss', color='red')
    axes[0, 0].set_title('Loss History')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 准确率曲线
    axes[0, 1].plot(history['train_acc'], label='Train Acc', color='blue')
    axes[0, 1].plot(history['val_acc'], label='Val Acc', color='red')
    axes[0, 1].set_title('Accuracy History')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy (%)')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 学习率曲线
    axes[1, 0].plot(history['lr'], color='green')
    axes[1, 0].set_title('Learning Rate History')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Learning Rate')
    axes[1, 0].grid(True)
    
    # 损失差异
    loss_diff = [abs(t - v) for t, v in zip(history['train_loss'], history['val_loss'])]
    axes[1, 1].plot(loss_diff, color='purple')
    axes[1, 1].set_title('Train-Val Loss Difference')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Loss Difference')
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练曲线已保存到: {save_path}")
    
    plt.show()

def plot_confusion_matrix(y_true, y_pred, class_names, save_path=None):
    """绘制混淆矩阵"""
    cm = confusion_matrix(y_true, y_pred)
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"混淆矩阵已保存到: {save_path}")
    
    plt.show()

# ==================== 主函数 ====================
def main():
    """主函数"""
    print("🚀 开始3D ResNet + 3D ViT融合模型训练")
    print("=" * 60)
    
    # 获取配置
    config = get_config()
    
    # 打印和验证配置
    print_config(config)
    if not validate_config(config):
        return
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 创建数据集
    print("\\n📁 加载数据集...")
    all_data = create_dataset(config.DATA_DIR, config.TRAIN_LABEL_EXCEL, config)
    print(f"总样本数: {len(all_data)}")
    
    # 检查类别分布
    labels = [d["label"] for d in all_data]
    label_counts = Counter(labels)
    print(f"类别分布: {dict(label_counts)}")
    
    # 划分数据集
    train_data, val_data = train_test_split(
        all_data, 
        test_size=config.VALIDATION_SPLIT, 
        random_state=42,
        stratify=labels
    )
    
    print(f"训练集样本数: {len(train_data)}")
    print(f"验证集样本数: {len(val_data)}")
    
    # 获取数据变换
    train_transforms, val_transforms = get_transforms(config)
    
    # 创建数据加载器
    train_dataset = Dataset(data=train_data, transform=train_transforms)
    val_dataset = Dataset(data=val_data, transform=val_transforms)
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config.BATCH_SIZE, 
        shuffle=True, 
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=config.BATCH_SIZE, 
        shuffle=False, 
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    # 创建模型
    print("\\n🏗️ 创建模型...")
    model = ResNetViTFusion(config)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    # 训练模型
    print("\\n🎯 开始训练...")
    history, best_acc = train_model(model, train_loader, val_loader, config)
    
    # 保存训练历史
    if config.SAVE_HISTORY:
        history_df = pd.DataFrame(history)
        history_path = os.path.join(config.SAVE_DIR, f'{config.MODEL_NAME}_history.xlsx')
        history_df.to_excel(history_path, index=False)
        print(f"训练历史已保存到: {history_path}")
    
    # 绘制训练曲线
    plot_path = os.path.join(config.SAVE_DIR, f'{config.MODEL_NAME}_training_curves.png')
    plot_training_history(history, save_path=plot_path)
    
    print("\\n✅ 训练完成!")
    print(f"最佳验证准确率: {best_acc:.2f}%")
    print(f"模型和结果保存在: {config.SAVE_DIR}")

if __name__ == "__main__":
    main()