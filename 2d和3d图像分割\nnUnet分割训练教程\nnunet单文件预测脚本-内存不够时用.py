#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nnUNet单文件预测脚本
逐个文件预测，最大化内存优化

解决方案:
1. 一次只预测一个文件
2. 每次预测后清理内存
3. 使用最小资源配置
4. 支持断点续传

作者: 基于nnUNet项目代码整合
"""

import os
import sys
import gc
import subprocess
import logging
import time
from pathlib import Path
import shutil

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nnunet_single_file_prediction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def setup_memory_optimization():
    """设置内存优化环境"""
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['OPENBLAS_NUM_THREADS'] = '1'
    os.environ['NUMEXPR_NUM_THREADS'] = '1'
    
    # 强制垃圾回收
    gc.collect()
    
    # 如果有GPU，清理GPU缓存
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    except ImportError:
        pass


def predict_single_file(input_file, output_file, config):
    """
    预测单个文件
    
    参数:
        input_file: 输入文件路径
        output_file: 输出文件路径
        config: 配置字典
    
    返回:
        bool: 预测是否成功
    """
    logger.info(f"开始预测单个文件: {input_file.name}")
    
    # 检查输出文件是否已存在
    if output_file.exists():
        logger.info(f"输出文件已存在，跳过: {output_file.name}")
        return True
    
    # 创建临时文件夹
    temp_dir = Path(config["output_folder"]) / "temp_single_prediction"
    temp_input_dir = temp_dir / "input"
    temp_output_dir = temp_dir / "output"
    
    try:
        # 清理并创建临时目录
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        temp_input_dir.mkdir(parents=True, exist_ok=True)
        temp_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制单个文件到临时输入目录
        temp_input_file = temp_input_dir / input_file.name
        shutil.copy2(input_file, temp_input_file)
        
        # 设置nnUNet环境变量
        env = os.environ.copy()
        env.update({
            'nnUNet_raw': config["nnunet_raw"],
            'nnUNet_preprocessed': config["nnunet_preprocessed"],
            'nnUNet_results': config["nnunet_results"]
        })
        
        # 构建预测命令
        cmd = [
            "nnUNetv2_predict",
            "-i", str(temp_input_dir),
            "-o", str(temp_output_dir),
            "-d", str(config["dataset_id"]),
            "-c", config["configuration"],
            "-tr", "nnUNetTrainer",
            "-f", "0",
            "-chk", "checkpoint_final.pth",
            "-npp", "1",  # 最小并行进程数
            "-nps", "1"   # 最小并行进程数
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 执行预测
        start_time = time.time()
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            # 预测成功，移动结果文件
            predicted_files = list(temp_output_dir.glob("*.nii.gz"))
            if predicted_files:
                # 找到预测结果文件
                predicted_file = predicted_files[0]
                
                # 移动到最终输出位置
                shutil.move(predicted_file, output_file)
                
                logger.info(f"✓ 预测成功: {input_file.name} -> {output_file.name} (耗时: {duration:.1f}秒)")
                return True
            else:
                logger.error(f"❌ 预测失败: 未找到输出文件")
                return False
        else:
            logger.error(f"❌ 预测失败: {input_file.name}")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ 预测超时: {input_file.name}")
        return False
    except Exception as e:
        logger.error(f"❌ 预测异常: {input_file.name} - {e}")
        return False
    finally:
        # 清理临时文件夹
        if temp_dir.exists():
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"清理临时文件夹失败: {e}")
        
        # 强制内存清理
        setup_memory_optimization()


def batch_predict_single_files(config):
    """
    批量单文件预测
    
    参数:
        config: 配置字典
    """
    logger.info("开始批量单文件预测")
    logger.info("=" * 60)
    
    # 获取输入文件
    input_folder = Path(config["input_folder"])
    output_folder = Path(config["output_folder"])
    output_folder.mkdir(parents=True, exist_ok=True)
    
    # 查找所有.nii.gz文件
    input_files = sorted(list(input_folder.glob("*.nii.gz")))
    if not input_files:
        logger.error(f"在 {input_folder} 中未找到.nii.gz文件")
        return
    
    logger.info(f"找到 {len(input_files)} 个输入文件")
    
    # 检查已完成的文件
    completed_files = []
    pending_files = []
    
    for input_file in input_files:
        # 生成输出文件名（去掉_0000后缀）
        output_name = input_file.name.replace("_0000.nii.gz", ".nii.gz")
        output_file = output_folder / output_name
        
        if output_file.exists():
            completed_files.append(input_file.name)
        else:
            pending_files.append((input_file, output_file))
    
    logger.info(f"已完成: {len(completed_files)} 个文件")
    logger.info(f"待预测: {len(pending_files)} 个文件")
    
    if not pending_files:
        logger.info("🎉 所有文件都已完成预测!")
        return
    
    # 逐个预测文件
    success_count = 0
    failed_files = []
    
    for i, (input_file, output_file) in enumerate(pending_files, 1):
        logger.info(f"\n[{i}/{len(pending_files)}] 预测文件: {input_file.name}")
        
        # 预测前内存优化
        setup_memory_optimization()
        
        # 预测单个文件
        success = predict_single_file(input_file, output_file, config)
        
        if success:
            success_count += 1
        else:
            failed_files.append(input_file.name)
        
        # 预测后等待一段时间，让系统释放资源
        if i < len(pending_files):  # 不是最后一个文件
            logger.info("等待系统释放资源...")
            time.sleep(5)
    
    # 总结结果
    logger.info("\n" + "=" * 60)
    logger.info("批量预测完成!")
    logger.info("=" * 60)
    logger.info(f"总文件数: {len(input_files)}")
    logger.info(f"已完成: {len(completed_files)} (之前完成)")
    logger.info(f"本次成功: {success_count}")
    logger.info(f"本次失败: {len(failed_files)}")
    
    if failed_files:
        logger.warning("失败的文件:")
        for failed_file in failed_files:
            logger.warning(f"  - {failed_file}")
        logger.warning("建议: 检查这些文件是否损坏或格式不正确")


def main():
    """主函数"""
    # 配置参数
    config = {
        # nnUNet环境
        "nnunet_raw": r"J:\nnUNet_workspace\nnUNet_raw",
        "nnunet_preprocessed": r"J:\nnUNet_workspace\nnUNet_preprocessed", 
        "nnunet_results": r"J:\nnUNet_workspace\nnUNet_results",
        
        # 数据路径 - 使用已准备好的输入文件
        "input_folder": r"M:\新建文件夹\HCC数据集\HCC新增待整理\HCC2024-2025\ap",
        "output_folder": r"M:\新建文件夹\HCC数据集\HCC新增待整理\HCC2024-2025\ap_prediction",
        
        # 模型配置
        "dataset_id": 503,
        "configuration": "3d_fullres",
    }
    
    try:
        logger.info("nnUNet单文件预测工具")
        logger.info("=" * 50)
        
        # 初始内存优化
        setup_memory_optimization()
        
        # 检查路径
        input_path = Path(config["input_folder"])
        if not input_path.exists():
            logger.error(f"输入路径不存在: {input_path}")
            return 1
        
        # 执行批量单文件预测
        batch_predict_single_files(config)
        
        logger.info("\n程序执行完成!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("\n用户中断程序")
        return 1
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    """
    单文件预测的优势:
    
    1. 内存使用最小化:
       - 一次只处理一个文件
       - 每次预测后清理内存
       - 避免批量处理的内存累积
    
    2. 更好的错误处理:
       - 单个文件失败不影响其他文件
       - 可以精确定位问题文件
       - 支持断点续传
    
    3. 系统资源友好:
       - 预测间隔让系统释放资源
       - 避免长时间占用大量内存
       - 适合资源受限的环境
    
    使用建议:
    - 适合内存不足的情况
    - 适合大文件或大批量预测
    - 虽然总时间可能更长，但更稳定可靠
    """
    exit_code = main()
    sys.exit(exit_code)
