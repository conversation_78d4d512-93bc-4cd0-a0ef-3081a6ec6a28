#!/usr/bin/env python
# coding: utf-8

# ## Note: 
# - This code is to perform image processing including registration, and N4 bias correction via Nipype (example images were skull-stripped for de-idenficiation), automated tumor segmentation, extraction of tumor loci and shape features, and IDH status prediction which is the final step of hybrid model. 
# 
# <img src="workflow.png">
#   
# - Python module requirements  : Nipype /  FSL /  ANTs / PyRadiomics / PyTorch
# - The process resquires GPU.

# In[ ]:
from sys import path

path.append('D:/python日月光华深度学习/code_automated_hybrid_IDH_master')
print(path)
import code_automated_hybrid_IDH_master
from img_processing import *


# ### Image processing - resampling, registration and bias correction

# In[ ]:


# for de-identification, example files were skull-striped. 
    
    # file path of skull-stripped images
T1C_bet_file = r'D:\data\t1c_bet.nii.gz'
T2_bet_file = r'D:\data\t2_bet.nii.gz'
FLAIR_bet_file = r'D:\data\flair_bet.nii.gz'
    
# file path of the mask for T1C skull stripping
brainmask_T1C_file = r'D:\data\t1c_mask.nii.gz'

# filenames to save isovoxel images / brain mask
T1C_iso_file = r'D:\data\t1c_isovoxel.nii.gz'
T2_iso_file = r'D:\data\t2_isovoxel.nii.gz'
FLAIR_iso_file = r'D:\data\flair_isovoxel.nii.gz'
brainmask_iso_file = r'D:\data\mask_brain_isovoxel.nii.gz'

# filenames to save bias-corrected images   
T1C_corrected_file = r'D:\data\t1c_corrected.nii.gz'
T2_corrected_file = r'D:\data\t2_corrected.nii.gz'
FLAIR_corrected_file = r'D:\data\flair_corrected.nii.gz'


# In[ ]:
def func_img_proc(T1C_bet, T2_bet, FLAIR_bet, mask_T1C_bet,
                  T1C_isovoxel, T2_isovoxel, FLAIR_isovoxel, mask_T1C_bet_iso,
                  T1C_corrected, T2_corrected, FLAIR_corrected):
    t1c_isovoxel = func_resample_isovoxel(T1C_bet, isseg=False)
    sitk.WriteImage(t1c_isovoxel, T1C_isovoxel)

    bmask_isovoxel = func_resample_isovoxel(mask_T1C_bet, isseg=True)
    sitk.WriteImage(bmask_isovoxel, mask_T1C_bet_iso)
    print("resampling T1C & brain mask - completed")
    func_register(T2_bet, T1C_isovoxel, T2_isovoxel)
    print("register T2 to T1C_isovoxel - completed")
    func_register(FLAIR_bet, T1C_isovoxel, FLAIR_isovoxel)
    print("register FLAIR to T1C_isovoxel - completed")
    func_n4bias(T1C_isovoxel, T1C_corrected)
    print("T1C bias correction done...")
    func_n4bias(T2_isovoxel, T2_corrected)
    print("T2 bias correction done...")
    func_n4bias(FLAIR_isovoxel, FLAIR_corrected)
    print("FLAIR bias correction done...")

func_img_proc(T1C_bet_file, T2_bet_file, FLAIR_bet_file, brainmask_T1C_file,
              T1C_iso_file, T2_iso_file, FLAIR_iso_file, brainmask_iso_file,
              T1C_corrected_file, T2_corrected_file, FLAIR_corrected_file)

# ### Model 1 : Automatic tumor segmentation
# #### Preprocessing for Model 1

(t1c_unet_arr, flair_unet_arr, cropdown_info) = func_norm_model1(T1C_corrected_file, FLAIR_corrected_file, brainmask_iso_file)

# cropdown_info will be used for resmampling the predicted tumor mask to original isovoxel space, and preprcessing for Model2.
cropdown_info


# #### Get tumor mask from Model 1

# In[ ]:



predmask_arr = func_get_predmask(t1c_unet_arr, flair_unet_arr)


# #### Resample the predicted mask back to original isovoxel space

# In[ ]:

predmask_isovoxel_arr = func_mask_back2iso(predmask_arr, cropdown_info)
predmask_isovoxel_arr_sitk = np.transpose(predmask_isovoxel_arr, (2,1,0))
predmask_isovoxel_img = sitk.GetImageFromArray(predmask_isovoxel_arr_sitk)

predmask_isovoxel_file = 'predmask_isovoxel.nii.gz' #filename for predicted mask of isovoxel resolution
sitk.WriteImage(predmask_isovoxel_img, predmask_isovoxel_file)   # save the automatic segmentation of isovoxel resolution


# ### Model 2 : CNN classifier for IDH status prediction

# #### Preprocessing for Model 2

# In[ ]:


t1c_corrected_img = nb.load(T1C_corrected_file)
t1c_corrected_arr = t1c_corrected_img.get_data()
t2_corrected_img = nb.load(T2_corrected_file)
t2_corrected_arr = t2_corrected_img.get_data()
brain_mask = nb.load(brainmask_iso_file)
brain_mask_arr = brain_mask.get_data()

t1c_resnet_arr = func_norm_resnet(t1c_corrected_arr, predmask_isovoxel_arr, brain_mask_arr, cropdown_info)
t2_resnet_arr = func_norm_resnet(t2_corrected_arr, predmask_isovoxel_arr, brain_mask_arr, cropdown_info)

    


# #### Get shape and loci features from tumor mask of 1mm isovoxel

# In[ ]:


sla_features = func_shapeloci(T1C_iso_file, predmask_isovoxel_file)


# #### Add patient's age

# In[ ]:


sla_features['age'] = pd.Series(50)  ## change 50 to the patient's age.


# In[ ]:


sla_features = np.array(sla_features)
sla_arr = np.repeat(sla_features, 5, axis=0) #这行代码把sla_features的label扩大了5倍
sla_arr.shape


# #### Get probability of IDH mutation from Model 2

# In[ ]:


#download Model 2
from torch.hub import download_url_to_file

url='https://github.com/yoonchoi-neuro/automated_hybrid_IDH/releases/download/final/MODEL2_CNNclassifier.pth'
download_url_to_file(url, 'MODEL2_CNNclassifier.pth')


# In[ ]:

def get_IDH_pred(t1c_resnet_arr, t2_resnet_arr, mask_arr, sla_arr):
    print("Calling pretrained Model 2...")
    model_pre = ResNet(3, BasicBlock, [3, 4, 6, 3])
    model = ResNet_transfer(model_pre, 2, 20)
    model_filename = 'MODEL2_CNNclassifier.pth'
    checkpoint = torch.load(model_filename)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.cuda()
    print("Calculating predicted probabilitiy...")
    x_arr = get_maxROI(t1c_resnet_arr, t2_resnet_arr, mask_arr)
    x_arr = torch.from_numpy(x_arr).float()
    x_arr = x_arr.cuda()

    sla_arr = torch.from_numpy(sla_arr).float()
    sla_arr = sla_arr.cuda()

    with torch.no_grad():
        output = model(x_arr, sla_arr)

    output = nn.Softmax(dim=1)(output)

    output_mean = torch.sum(output[:, 1]) / 5  #对预测概率进行平均
    print("IDH mutation probability: %s" % (output_mean.cpu().item() * 100)
    return output_mean

output_mean = get_IDH_pred(t1c_resnet_arr, t2_resnet_arr, predmask_arr, sla_arr)







