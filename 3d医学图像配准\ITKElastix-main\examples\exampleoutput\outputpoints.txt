Point	0	; InputIndex = [ 86 83 92 ]	; InputPoint = [ -36.351000 -36.974000 -1204.500000 ]	; OutputIndexFixed = [ 83 76 106 ]	; OutputPoint = [ -40.438741 -46.656028 -1169.780881 ]	; Deformation = [ -4.087740 -9.682028 34.719120 ]	; OutputIndexMoving = [ 89 85 92 ]
Point	1	; InputIndex = [ 185 91 44 ]	; InputPoint = [ 98.883000 -26.046000 -1324.500000 ]	; OutputIndexFixed = [ 186 89 58 ]	; OutputPoint = [ 100.733689 -28.167599 -1288.799801 ]	; Deformation = [ 1.850689 -2.121599 35.700199 ]	; OutputIndexMoving = [ 192 98 44 ]
Point	2	; InputIndex = [ 144 60 90 ]	; InputPoint = [ 42.877000 -68.392000 -1209.500000 ]	; OutputIndexFixed = [ 143 56 106 ]	; OutputPoint = [ 41.312200 -74.265195 -1170.401913 ]	; Deformation = [ -1.564800 -5.873195 39.098087 ]	; OutputIndexMoving = [ 149 65 92 ]
Point	3	; InputIndex = [ 65 126 36 ]	; InputPoint = [ -65.037000 21.764000 -1344.500000 ]	; OutputIndexFixed = [ 64 121 49 ]	; OutputPoint = [ -66.852934 15.153240 -1311.051260 ]	; Deformation = [ -1.815934 -6.610760 33.448742 ]	; OutputIndexMoving = [ 70 130 35 ]
Point	4	; InputIndex = [ 140 112 66 ]	; InputPoint = [ 37.413000 2.640000 -1269.500000 ]	; OutputIndexFixed = [ 139 111 80 ]	; OutputPoint = [ 36.442975 0.737229 -1233.361894 ]	; Deformation = [ -0.970025 -1.902771 36.138107 ]	; OutputIndexMoving = [ 145 119 66 ]
Point	5	; InputIndex = [ 44 77 62 ]	; InputPoint = [ -93.723000 -45.170000 -1279.500000 ]	; OutputIndexFixed = [ 41 68 73 ]	; OutputPoint = [ -98.112399 -57.189610 -1250.998412 ]	; Deformation = [ -4.389399 -12.019609 28.501587 ]	; OutputIndexMoving = [ 47 77 59 ]
Point	6	; InputIndex = [ 84 41 78 ]	; InputPoint = [ -39.083000 -94.346000 -1239.500000 ]	; OutputIndexFixed = [ 84 34 92 ]	; OutputPoint = [ -39.227099 -103.289150 -1205.195945 ]	; Deformation = [ -0.144099 -8.943150 34.304054 ]	; OutputIndexMoving = [ 90 43 78 ]
Point	7	; InputIndex = [ 59 109 96 ]	; InputPoint = [ -73.233000 -1.458000 -1194.500000 ]	; OutputIndexFixed = [ 53 104 110 ]	; OutputPoint = [ -81.087899 -7.902160 -1159.686119 ]	; Deformation = [ -7.854899 -6.444160 34.813881 ]	; OutputIndexMoving = [ 59 113 96 ]
Point	8	; InputIndex = [ 159 86 84 ]	; InputPoint = [ 63.367000 -32.876000 -1224.500000 ]	; OutputIndexFixed = [ 159 84 99 ]	; OutputPoint = [ 62.723334 -36.265964 -1186.664380 ]	; Deformation = [ -0.643666 -3.389964 37.835621 ]	; OutputIndexMoving = [ 164 92 85 ]
Point	9	; InputIndex = [ 172 56 64 ]	; InputPoint = [ 81.125000 -73.856000 -1274.500000 ]	; OutputIndexFixed = [ 172 52 79 ]	; OutputPoint = [ 81.706285 -79.132400 -1236.571374 ]	; Deformation = [ 0.581285 -5.276401 37.928627 ]	; OutputIndexMoving = [ 178 61 65 ]
Point	10	; InputIndex = [ 172 119 44 ]	; InputPoint = [ 81.125000 12.202000 -1324.500000 ]	; OutputIndexFixed = [ 173 119 58 ]	; OutputPoint = [ 82.374453 11.860080 -1289.752529 ]	; Deformation = [ 1.249454 -0.341920 34.747471 ]	; OutputIndexMoving = [ 179 128 44 ]
Point	11	; InputIndex = [ 81 99 52 ]	; InputPoint = [ -43.181000 -15.118000 -1304.500000 ]	; OutputIndexFixed = [ 79 89 65 ]	; OutputPoint = [ -45.383782 -28.304224 -1271.853222 ]	; Deformation = [ -2.202782 -13.186224 32.646778 ]	; OutputIndexMoving = [ 85 98 51 ]
Point	12	; InputIndex = [ 87 108 98 ]	; InputPoint = [ -34.985000 -2.824000 -1189.500000 ]	; OutputIndexFixed = [ 83 104 113 ]	; OutputPoint = [ -40.476302 -8.381681 -1153.048643 ]	; Deformation = [ -5.491302 -5.557681 36.451359 ]	; OutputIndexMoving = [ 89 113 99 ]
Point	13	; InputIndex = [ 69 66 54 ]	; InputPoint = [ -59.573000 -60.196000 -1299.500000 ]	; OutputIndexFixed = [ 70 57 67 ]	; OutputPoint = [ -58.340586 -72.397779 -1265.753845 ]	; Deformation = [ 1.232414 -12.201779 33.746155 ]	; OutputIndexMoving = [ 76 66 53 ]
Point	14	; InputIndex = [ 164 111 90 ]	; InputPoint = [ 70.197000 1.274000 -1209.500000 ]	; OutputIndexFixed = [ 164 110 105 ]	; OutputPoint = [ 69.621515 -0.040790 -1172.296077 ]	; Deformation = [ -0.575485 -1.314790 37.203922 ]	; OutputIndexMoving = [ 169 119 91 ]
Point	15	; InputIndex = [ 139 84 92 ]	; InputPoint = [ 36.047000 -35.608000 -1204.500000 ]	; OutputIndexFixed = [ 138 81 107 ]	; OutputPoint = [ 34.197495 -39.775393 -1166.147761 ]	; Deformation = [ -1.849504 -4.167394 38.352238 ]	; OutputIndexMoving = [ 144 90 93 ]
Point	16	; InputIndex = [ 157 101 54 ]	; InputPoint = [ 60.635000 -12.386000 -1299.500000 ]	; OutputIndexFixed = [ 157 99 68 ]	; OutputPoint = [ 60.843774 -14.600363 -1263.589547 ]	; Deformation = [ 0.208774 -2.214363 35.910454 ]	; OutputIndexMoving = [ 163 108 54 ]
Point	17	; InputIndex = [ 147 102 102 ]	; InputPoint = [ 46.975000 -11.020000 -1179.500000 ]	; OutputIndexFixed = [ 146 100 117 ]	; OutputPoint = [ 45.207038 -13.574348 -1141.354137 ]	; Deformation = [ -1.767962 -2.554348 38.145863 ]	; OutputIndexMoving = [ 152 109 103 ]
Point	18	; InputIndex = [ 67 90 98 ]	; InputPoint = [ -62.305000 -27.412000 -1189.500000 ]	; OutputIndexFixed = [ 62 84 112 ]	; OutputPoint = [ -68.875457 -35.514766 -1154.082760 ]	; Deformation = [ -6.570457 -8.102766 35.417240 ]	; OutputIndexMoving = [ 68 93 98 ]
Point	19	; InputIndex = [ 61 97 48 ]	; InputPoint = [ -70.501000 -17.850000 -1314.500000 ]	; OutputIndexFixed = [ 58 89 60 ]	; OutputPoint = [ -74.931038 -28.859793 -1283.518271 ]	; Deformation = [ -4.430038 -11.009793 30.981730 ]	; OutputIndexMoving = [ 64 98 46 ]
Point	20	; InputIndex = [ 60 55 86 ]	; InputPoint = [ -71.867000 -75.222000 -1219.500000 ]	; OutputIndexFixed = [ 56 46 100 ]	; OutputPoint = [ -76.649013 -87.282594 -1184.044241 ]	; Deformation = [ -4.782013 -12.060594 35.455761 ]	; OutputIndexMoving = [ 62 55 86 ]
Point	21	; InputIndex = [ 74 125 72 ]	; InputPoint = [ -52.743000 20.398000 -1254.500000 ]	; OutputIndexFixed = [ 72 119 85 ]	; OutputPoint = [ -55.858725 12.865309 -1223.219775 ]	; Deformation = [ -3.115725 -7.532691 31.280224 ]	; OutputIndexMoving = [ 78 128 71 ]
Point	22	; InputIndex = [ 145 96 70 ]	; InputPoint = [ 44.243000 -19.216000 -1259.500000 ]	; OutputIndexFixed = [ 144 94 85 ]	; OutputPoint = [ 43.375625 -22.218619 -1222.622417 ]	; Deformation = [ -0.867375 -3.002619 36.877583 ]	; OutputIndexMoving = [ 150 103 71 ]
Point	23	; InputIndex = [ 69 110 54 ]	; InputPoint = [ -59.573000 -0.092000 -1299.500000 ]	; OutputIndexFixed = [ 66 102 66 ]	; OutputPoint = [ -63.012421 -11.199494 -1268.646626 ]	; Deformation = [ -3.439421 -11.107494 30.853374 ]	; OutputIndexMoving = [ 72 111 52 ]
Point	24	; InputIndex = [ 164 71 78 ]	; InputPoint = [ 70.197000 -53.366000 -1239.500000 ]	; OutputIndexFixed = [ 164 68 93 ]	; OutputPoint = [ 69.969210 -57.754076 -1201.426192 ]	; Deformation = [ -0.227790 -4.388076 38.073807 ]	; OutputIndexMoving = [ 170 77 79 ]
Point	25	; InputIndex = [ 79 96 84 ]	; InputPoint = [ -45.913000 -19.216000 -1224.500000 ]	; OutputIndexFixed = [ 74 90 97 ]	; OutputPoint = [ -52.121498 -27.874095 -1191.444919 ]	; Deformation = [ -6.208498 -8.658095 33.055080 ]	; OutputIndexMoving = [ 80 99 83 ]
Point	26	; InputIndex = [ 73 82 62 ]	; InputPoint = [ -54.109000 -38.340000 -1279.500000 ]	; OutputIndexFixed = [ 72 73 75 ]	; OutputPoint = [ -55.940126 -50.871099 -1247.549019 ]	; Deformation = [ -1.831126 -12.531098 31.950981 ]	; OutputIndexMoving = [ 78 82 61 ]
Point	27	; InputIndex = [ 171 95 54 ]	; InputPoint = [ 79.759000 -20.582000 -1299.500000 ]	; OutputIndexFixed = [ 172 93 68 ]	; OutputPoint = [ 80.631777 -22.839628 -1263.426172 ]	; Deformation = [ 0.872777 -2.257627 36.073826 ]	; OutputIndexMoving = [ 177 102 54 ]
Point	28	; InputIndex = [ 59 76 64 ]	; InputPoint = [ -73.233000 -46.536000 -1274.500000 ]	; OutputIndexFixed = [ 57 68 76 ]	; OutputPoint = [ -75.530167 -58.065782 -1245.167697 ]	; Deformation = [ -2.297168 -11.529782 29.332304 ]	; OutputIndexMoving = [ 63 76 62 ]
Point	29	; InputIndex = [ 75 71 90 ]	; InputPoint = [ -51.377000 -53.366000 -1209.500000 ]	; OutputIndexFixed = [ 73 64 104 ]	; OutputPoint = [ -53.801659 -63.534541 -1174.972530 ]	; Deformation = [ -2.424659 -10.168541 34.527470 ]	; OutputIndexMoving = [ 79 72 90 ]
Point	30	; InputIndex = [ 48 93 80 ]	; InputPoint = [ -88.259000 -23.314000 -1234.500000 ]	; OutputIndexFixed = [ 44 86 92 ]	; OutputPoint = [ -93.689963 -33.347226 -1203.370375 ]	; Deformation = [ -5.430963 -10.033226 31.129625 ]	; OutputIndexMoving = [ 50 95 78 ]
Point	31	; InputIndex = [ 79 118 36 ]	; InputPoint = [ -45.913000 10.836000 -1344.500000 ]	; OutputIndexFixed = [ 78 112 49 ]	; OutputPoint = [ -47.633614 2.255971 -1312.104139 ]	; Deformation = [ -1.720614 -8.580029 32.395859 ]	; OutputIndexMoving = [ 84 121 35 ]
Point	32	; InputIndex = [ 158 86 54 ]	; InputPoint = [ 62.001000 -32.876000 -1299.500000 ]	; OutputIndexFixed = [ 158 84 69 ]	; OutputPoint = [ 62.247238 -36.223577 -1263.057468 ]	; Deformation = [ 0.246238 -3.347577 36.442532 ]	; OutputIndexMoving = [ 164 92 55 ]
Point	33	; InputIndex = [ 98 98 108 ]	; InputPoint = [ -19.959000 -16.484000 -1164.500000 ]	; OutputIndexFixed = [ 93 94 124 ]	; OutputPoint = [ -26.594807 -21.412882 -1124.663637 ]	; Deformation = [ -6.635808 -4.928882 39.836361 ]	; OutputIndexMoving = [ 99 103 110 ]
Point	34	; InputIndex = [ 92 90 42 ]	; InputPoint = [ -28.155000 -27.412000 -1329.500000 ]	; OutputIndexFixed = [ 91 82 56 ]	; OutputPoint = [ -29.542563 -37.987682 -1294.301234 ]	; Deformation = [ -1.387563 -10.575682 35.198765 ]	; OutputIndexMoving = [ 97 91 42 ]
Point	35	; InputIndex = [ 163 55 88 ]	; InputPoint = [ 68.831000 -75.222000 -1214.500000 ]	; OutputIndexFixed = [ 163 51 104 ]	; OutputPoint = [ 68.232004 -80.905324 -1175.385551 ]	; Deformation = [ -0.598996 -5.683323 39.114449 ]	; OutputIndexMoving = [ 168 60 90 ]
Point	36	; InputIndex = [ 154 116 44 ]	; InputPoint = [ 56.537000 8.104000 -1324.500000 ]	; OutputIndexFixed = [ 154 115 58 ]	; OutputPoint = [ 56.925409 6.986732 -1289.579928 ]	; Deformation = [ 0.388409 -1.117268 34.920071 ]	; OutputIndexMoving = [ 160 124 44 ]
Point	37	; InputIndex = [ 151 104 70 ]	; InputPoint = [ 52.439000 -8.288000 -1259.500000 ]	; OutputIndexFixed = [ 151 102 85 ]	; OutputPoint = [ 51.863427 -10.489267 -1222.929951 ]	; Deformation = [ -0.575572 -2.201267 36.570049 ]	; OutputIndexMoving = [ 156 111 71 ]
Point	38	; InputIndex = [ 73 86 84 ]	; InputPoint = [ -54.109000 -32.876000 -1224.500000 ]	; OutputIndexFixed = [ 69 80 97 ]	; OutputPoint = [ -59.386658 -40.709832 -1190.848460 ]	; Deformation = [ -5.277658 -7.833832 33.651539 ]	; OutputIndexMoving = [ 75 89 83 ]
Point	39	; InputIndex = [ 149 88 94 ]	; InputPoint = [ 49.707000 -30.144000 -1199.500000 ]	; OutputIndexFixed = [ 148 85 109 ]	; OutputPoint = [ 48.274941 -33.704513 -1161.233869 ]	; Deformation = [ -1.432059 -3.560513 38.266132 ]	; OutputIndexMoving = [ 154 94 95 ]
Point	40	; InputIndex = [ 70 101 84 ]	; InputPoint = [ -58.207000 -12.386000 -1224.500000 ]	; OutputIndexFixed = [ 67 94 97 ]	; OutputPoint = [ -62.942589 -21.556469 -1191.989047 ]	; Deformation = [ -4.735589 -9.170468 32.510952 ]	; OutputIndexMoving = [ 72 103 83 ]
Point	41	; InputIndex = [ 81 112 82 ]	; InputPoint = [ -43.181000 2.640000 -1229.500000 ]	; OutputIndexFixed = [ 78 107 95 ]	; OutputPoint = [ -47.817430 -4.467147 -1195.979181 ]	; Deformation = [ -4.636430 -7.107147 33.520821 ]	; OutputIndexMoving = [ 83 116 81 ]
Point	42	; InputIndex = [ 164 109 44 ]	; InputPoint = [ 70.197000 -1.458000 -1324.500000 ]	; OutputIndexFixed = [ 165 108 58 ]	; OutputPoint = [ 71.057840 -2.816684 -1289.366293 ]	; Deformation = [ 0.860840 -1.358684 35.133709 ]	; OutputIndexMoving = [ 170 117 44 ]
Point	43	; InputIndex = [ 148 103 46 ]	; InputPoint = [ 48.341000 -9.654000 -1319.500000 ]	; OutputIndexFixed = [ 148 101 60 ]	; OutputPoint = [ 48.371679 -11.965243 -1284.000707 ]	; Deformation = [ 0.030679 -2.311243 35.499294 ]	; OutputIndexMoving = [ 154 110 46 ]
Point	44	; InputIndex = [ 147 119 70 ]	; InputPoint = [ 46.975000 12.202000 -1259.500000 ]	; OutputIndexFixed = [ 146 118 84 ]	; OutputPoint = [ 46.218798 11.043501 -1223.451120 ]	; Deformation = [ -0.756202 -1.158499 36.048878 ]	; OutputIndexMoving = [ 152 127 70 ]
Point	45	; InputIndex = [ 50 89 44 ]	; InputPoint = [ -85.527000 -28.778000 -1324.500000 ]	; OutputIndexFixed = [ 47 81 56 ]	; OutputPoint = [ -89.874745 -39.742452 -1293.812466 ]	; Deformation = [ -4.347745 -10.964452 30.687534 ]	; OutputIndexMoving = [ 53 90 42 ]
Point	46	; InputIndex = [ 143 73 90 ]	; InputPoint = [ 41.511000 -50.634000 -1209.500000 ]	; OutputIndexFixed = [ 142 69 105 ]	; OutputPoint = [ 39.907369 -55.529096 -1170.862564 ]	; Deformation = [ -1.603631 -4.895096 38.637436 ]	; OutputIndexMoving = [ 148 78 91 ]
Point	47	; InputIndex = [ 57 105 78 ]	; InputPoint = [ -75.965000 -6.922000 -1239.500000 ]	; OutputIndexFixed = [ 54 98 91 ]	; OutputPoint = [ -80.608109 -16.269675 -1206.289408 ]	; Deformation = [ -4.643109 -9.347674 33.210590 ]	; OutputIndexMoving = [ 59 107 77 ]
Point	48	; InputIndex = [ 157 92 108 ]	; InputPoint = [ 60.635000 -24.680000 -1164.500000 ]	; OutputIndexFixed = [ 156 90 123 ]	; OutputPoint = [ 59.149893 -27.722944 -1125.754014 ]	; Deformation = [ -1.485107 -3.042944 38.745987 ]	; OutputIndexMoving = [ 162 99 109 ]
Point	49	; InputIndex = [ 77 73 58 ]	; InputPoint = [ -48.645000 -50.634000 -1289.500000 ]	; OutputIndexFixed = [ 77 64 71 ]	; OutputPoint = [ -48.808195 -62.527301 -1255.890778 ]	; Deformation = [ -0.163195 -11.893301 33.609222 ]	; OutputIndexMoving = [ 83 73 57 ]
Point	50	; InputIndex = [ 67 84 42 ]	; InputPoint = [ -62.305000 -35.608000 -1329.500000 ]	; OutputIndexFixed = [ 64 75 55 ]	; OutputPoint = [ -66.467431 -47.680438 -1298.199803 ]	; Deformation = [ -4.162431 -12.072437 31.300198 ]	; OutputIndexMoving = [ 70 84 41 ]
Point	51	; InputIndex = [ 80 104 70 ]	; InputPoint = [ -44.547000 -8.288000 -1259.500000 ]	; OutputIndexFixed = [ 77 96 83 ]	; OutputPoint = [ -48.229708 -19.587126 -1227.009826 ]	; Deformation = [ -3.682709 -11.299126 32.490173 ]	; OutputIndexMoving = [ 83 105 69 ]
Point	52	; InputIndex = [ 78 82 104 ]	; InputPoint = [ -47.279000 -38.340000 -1174.500000 ]	; OutputIndexFixed = [ 73 76 119 ]	; OutputPoint = [ -53.542716 -46.095341 -1136.117681 ]	; Deformation = [ -6.263716 -7.755342 38.382320 ]	; OutputIndexMoving = [ 79 85 105 ]
Point	53	; InputIndex = [ 88 95 66 ]	; InputPoint = [ -33.619000 -20.582000 -1269.500000 ]	; OutputIndexFixed = [ 86 85 79 ]	; OutputPoint = [ -36.587530 -34.646674 -1237.125445 ]	; Deformation = [ -2.968530 -14.064674 32.374554 ]	; OutputIndexMoving = [ 92 94 65 ]
Point	54	; InputIndex = [ 85 95 100 ]	; InputPoint = [ -37.717000 -20.582000 -1184.500000 ]	; OutputIndexFixed = [ 81 90 115 ]	; OutputPoint = [ -43.455081 -27.337453 -1146.821698 ]	; Deformation = [ -5.738081 -6.755454 37.678303 ]	; OutputIndexMoving = [ 87 99 101 ]
Point	55	; InputIndex = [ 167 103 64 ]	; InputPoint = [ 74.295000 -9.654000 -1274.500000 ]	; OutputIndexFixed = [ 167 102 79 ]	; OutputPoint = [ 74.669818 -11.435940 -1238.231766 ]	; Deformation = [ 0.374818 -1.781940 36.268234 ]	; OutputIndexMoving = [ 173 111 65 ]
Point	56	; InputIndex = [ 168 81 80 ]	; InputPoint = [ 75.661000 -39.706000 -1234.500000 ]	; OutputIndexFixed = [ 168 78 95 ]	; OutputPoint = [ 75.568428 -43.202742 -1196.704767 ]	; Deformation = [ -0.092572 -3.496742 37.795235 ]	; OutputIndexMoving = [ 174 87 81 ]
Point	57	; InputIndex = [ 169 121 66 ]	; InputPoint = [ 77.027000 14.934000 -1269.500000 ]	; OutputIndexFixed = [ 169 121 80 ]	; OutputPoint = [ 77.447063 14.603556 -1233.788783 ]	; Deformation = [ 0.420063 -0.330444 35.711216 ]	; OutputIndexMoving = [ 175 130 66 ]
Point	58	; InputIndex = [ 63 98 108 ]	; InputPoint = [ -67.769000 -16.484000 -1164.500000 ]	; OutputIndexFixed = [ 56 94 123 ]	; OutputPoint = [ -76.812862 -22.353570 -1127.251879 ]	; Deformation = [ -9.043862 -5.869570 37.248119 ]	; OutputIndexMoving = [ 62 103 109 ]
Point	59	; InputIndex = [ 148 85 72 ]	; InputPoint = [ 48.341000 -34.242000 -1254.500000 ]	; OutputIndexFixed = [ 147 82 87 ]	; OutputPoint = [ 47.546760 -38.012142 -1217.147353 ]	; Deformation = [ -0.794240 -3.770142 37.352646 ]	; OutputIndexMoving = [ 153 91 73 ]
Point	60	; InputIndex = [ 80 89 48 ]	; InputPoint = [ -44.547000 -28.778000 -1314.500000 ]	; OutputIndexFixed = [ 78 80 61 ]	; OutputPoint = [ -46.912889 -40.817648 -1281.605270 ]	; Deformation = [ -2.365889 -12.039648 32.894730 ]	; OutputIndexMoving = [ 84 89 47 ]
Point	61	; InputIndex = [ 176 88 78 ]	; InputPoint = [ 86.589000 -30.144000 -1239.500000 ]	; OutputIndexFixed = [ 176 86 93 ]	; OutputPoint = [ 86.945498 -32.851815 -1202.076975 ]	; Deformation = [ 0.356498 -2.707815 37.423023 ]	; OutputIndexMoving = [ 182 95 79 ]
Point	62	; InputIndex = [ 153 66 76 ]	; InputPoint = [ 55.171000 -60.196000 -1244.500000 ]	; OutputIndexFixed = [ 152 62 91 ]	; OutputPoint = [ 54.477359 -65.298663 -1206.300733 ]	; Deformation = [ -0.693641 -5.102663 38.199268 ]	; OutputIndexMoving = [ 158 71 77 ]
Point	63	; InputIndex = [ 63 51 66 ]	; InputPoint = [ -67.769000 -80.686000 -1269.500000 ]	; OutputIndexFixed = [ 62 43 78 ]	; OutputPoint = [ -68.870874 -91.160087 -1238.604956 ]	; Deformation = [ -1.101874 -10.474087 30.895044 ]	; OutputIndexMoving = [ 68 52 64 ]
Point	64	; InputIndex = [ 69 59 78 ]	; InputPoint = [ -59.573000 -69.758000 -1239.500000 ]	; OutputIndexFixed = [ 68 52 91 ]	; OutputPoint = [ -60.704152 -79.733826 -1207.641676 ]	; Deformation = [ -1.131152 -9.975826 31.858324 ]	; OutputIndexMoving = [ 74 61 77 ]
Point	65	; InputIndex = [ 76 114 110 ]	; InputPoint = [ -50.011000 5.372000 -1159.500000 ]	; OutputIndexFixed = [ 70 112 125 ]	; OutputPoint = [ -58.227620 2.045015 -1122.836854 ]	; Deformation = [ -8.216620 -3.326985 36.663147 ]	; OutputIndexMoving = [ 76 120 111 ]
Point	66	; InputIndex = [ 153 60 100 ]	; InputPoint = [ 55.171000 -68.392000 -1184.500000 ]	; OutputIndexFixed = [ 152 56 116 ]	; OutputPoint = [ 53.723155 -74.018036 -1144.969067 ]	; Deformation = [ -1.447845 -5.626036 39.530933 ]	; OutputIndexMoving = [ 158 65 102 ]
Point	67	; InputIndex = [ 140 92 46 ]	; InputPoint = [ 37.413000 -24.680000 -1319.500000 ]	; OutputIndexFixed = [ 140 90 60 ]	; OutputPoint = [ 37.054381 -28.085564 -1283.578756 ]	; Deformation = [ -0.358619 -3.405564 35.921246 ]	; OutputIndexMoving = [ 146 98 46 ]
Point	68	; InputIndex = [ 78 103 112 ]	; InputPoint = [ -47.279000 -9.654000 -1154.500000 ]	; OutputIndexFixed = [ 72 100 127 ]	; OutputPoint = [ -54.811419 -14.173359 -1117.662741 ]	; Deformation = [ -7.532419 -4.519359 36.837257 ]	; OutputIndexMoving = [ 78 109 113 ]
Point	69	; InputIndex = [ 59 66 54 ]	; InputPoint = [ -73.233000 -60.196000 -1299.500000 ]	; OutputIndexFixed = [ 59 57 67 ]	; OutputPoint = [ -72.990249 -72.598481 -1267.575030 ]	; Deformation = [ 0.242751 -12.402481 31.924971 ]	; OutputIndexMoving = [ 65 66 53 ]
Point	70	; InputIndex = [ 157 99 70 ]	; InputPoint = [ 60.635000 -15.118000 -1259.500000 ]	; OutputIndexFixed = [ 157 97 85 ]	; OutputPoint = [ 60.342339 -17.526163 -1222.773199 ]	; Deformation = [ -0.292661 -2.408163 36.726803 ]	; OutputIndexMoving = [ 163 106 71 ]
Point	71	; InputIndex = [ 152 100 52 ]	; InputPoint = [ 53.805000 -13.752000 -1304.500000 ]	; OutputIndexFixed = [ 152 98 66 ]	; OutputPoint = [ 53.836989 -16.189828 -1268.628765 ]	; Deformation = [ 0.031989 -2.437828 35.871235 ]	; OutputIndexMoving = [ 158 107 52 ]
Point	72	; InputIndex = [ 149 96 58 ]	; InputPoint = [ 49.707000 -19.216000 -1289.500000 ]	; OutputIndexFixed = [ 149 94 73 ]	; OutputPoint = [ 49.405563 -22.069011 -1253.195653 ]	; Deformation = [ -0.301437 -2.853011 36.304348 ]	; OutputIndexMoving = [ 155 103 59 ]
Point	73	; InputIndex = [ 152 98 68 ]	; InputPoint = [ 53.805000 -16.484000 -1264.500000 ]	; OutputIndexFixed = [ 152 96 83 ]	; OutputPoint = [ 53.335554 -19.115628 -1227.812416 ]	; Deformation = [ -0.469446 -2.631628 36.687584 ]	; OutputIndexMoving = [ 158 105 69 ]
Point	74	; InputIndex = [ 157 104 68 ]	; InputPoint = [ 60.635000 -8.288000 -1264.500000 ]	; OutputIndexFixed = [ 157 103 83 ]	; OutputPoint = [ 60.408267 -10.303540 -1228.044885 ]	; Deformation = [ -0.226733 -2.015540 36.455116 ]	; OutputIndexMoving = [ 163 111 69 ]
Point	75	; InputIndex = [ 70 86 92 ]	; InputPoint = [ -58.207000 -32.876000 -1204.500000 ]	; OutputIndexFixed = [ 65 79 106 ]	; OutputPoint = [ -64.630064 -41.973752 -1169.527701 ]	; Deformation = [ -6.423064 -9.097753 34.972298 ]	; OutputIndexMoving = [ 71 88 92 ]
Point	76	; InputIndex = [ 69 105 58 ]	; InputPoint = [ -59.573000 -6.922000 -1289.500000 ]	; OutputIndexFixed = [ 67 97 70 ]	; OutputPoint = [ -61.970311 -18.503837 -1258.560128 ]	; Deformation = [ -2.397311 -11.581837 30.939873 ]	; OutputIndexMoving = [ 73 105 56 ]
Point	77	; InputIndex = [ 162 98 64 ]	; InputPoint = [ 67.465000 -16.484000 -1274.500000 ]	; OutputIndexFixed = [ 162 96 79 ]	; OutputPoint = [ 67.597790 -18.804471 -1238.035012 ]	; Deformation = [ 0.132790 -2.320471 36.464989 ]	; OutputIndexMoving = [ 168 105 65 ]
Point	78	; InputIndex = [ 144 89 92 ]	; InputPoint = [ 42.877000 -28.778000 -1204.500000 ]	; OutputIndexFixed = [ 143 86 107 ]	; OutputPoint = [ 41.269524 -32.406863 -1166.344515 ]	; Deformation = [ -1.607476 -3.628863 38.155483 ]	; OutputIndexMoving = [ 149 95 93 ]
Point	79	; InputIndex = [ 81 93 54 ]	; InputPoint = [ -43.181000 -23.314000 -1299.500000 ]	; OutputIndexFixed = [ 80 83 67 ]	; OutputPoint = [ -44.978851 -36.316033 -1267.291066 ]	; Deformation = [ -1.797851 -13.002033 32.208935 ]	; OutputIndexMoving = [ 86 92 53 ]
Point	80	; InputIndex = [ 83 69 94 ]	; InputPoint = [ -40.449000 -56.098000 -1199.500000 ]	; OutputIndexFixed = [ 82 62 108 ]	; OutputPoint = [ -41.708170 -65.805452 -1163.443932 ]	; Deformation = [ -1.259170 -9.707453 36.056068 ]	; OutputIndexMoving = [ 88 71 94 ]
Point	81	; InputIndex = [ 76 90 84 ]	; InputPoint = [ -50.011000 -27.412000 -1224.500000 ]	; OutputIndexFixed = [ 72 84 98 ]	; OutputPoint = [ -55.482214 -35.873616 -1190.734244 ]	; Deformation = [ -5.471214 -8.461617 33.765755 ]	; OutputIndexMoving = [ 78 93 84 ]
Point	82	; InputIndex = [ 153 112 54 ]	; InputPoint = [ 55.171000 2.640000 -1299.500000 ]	; OutputIndexFixed = [ 153 111 68 ]	; OutputPoint = [ 55.196409 1.158175 -1263.967859 ]	; Deformation = [ 0.025409 -1.481825 35.532143 ]	; OutputIndexMoving = [ 159 120 54 ]
Point	83	; InputIndex = [ 71 91 82 ]	; InputPoint = [ -56.841000 -26.046000 -1229.500000 ]	; OutputIndexFixed = [ 68 84 95 ]	; OutputPoint = [ -61.230469 -35.110600 -1197.036227 ]	; Deformation = [ -4.389470 -9.064600 32.463772 ]	; OutputIndexMoving = [ 74 93 81 ]
Point	84	; InputIndex = [ 141 104 70 ]	; InputPoint = [ 38.779000 -8.288000 -1259.500000 ]	; OutputIndexFixed = [ 140 102 85 ]	; OutputPoint = [ 37.726209 -10.790753 -1222.893586 ]	; Deformation = [ -1.052791 -2.502753 36.606415 ]	; OutputIndexMoving = [ 146 111 71 ]
Point	85	; InputIndex = [ 160 105 60 ]	; InputPoint = [ 64.733000 -6.922000 -1284.500000 ]	; OutputIndexFixed = [ 160 104 74 ]	; OutputPoint = [ 64.900150 -8.750194 -1248.463969 ]	; Deformation = [ 0.167150 -1.828194 36.036030 ]	; OutputIndexMoving = [ 166 113 60 ]
Point	86	; InputIndex = [ 143 95 92 ]	; InputPoint = [ 41.511000 -20.582000 -1204.500000 ]	; OutputIndexFixed = [ 142 93 107 ]	; OutputPoint = [ 39.859906 -23.775666 -1166.555165 ]	; Deformation = [ -1.651094 -3.193666 37.944836 ]	; OutputIndexMoving = [ 148 102 93 ]
Point	87	; InputIndex = [ 74 95 86 ]	; InputPoint = [ -52.743000 -20.582000 -1219.500000 ]	; OutputIndexFixed = [ 70 88 99 ]	; OutputPoint = [ -57.737288 -29.752788 -1187.140240 ]	; Deformation = [ -4.994288 -9.170788 32.359760 ]	; OutputIndexMoving = [ 76 97 85 ]
Point	88	; InputIndex = [ 74 111 48 ]	; InputPoint = [ -52.743000 1.274000 -1314.500000 ]	; OutputIndexFixed = [ 72 103 61 ]	; OutputPoint = [ -56.099469 -9.196723 -1282.317987 ]	; Deformation = [ -3.356468 -10.470722 32.182014 ]	; OutputIndexMoving = [ 77 112 47 ]
Point	89	; InputIndex = [ 148 100 88 ]	; InputPoint = [ 48.341000 -13.752000 -1214.500000 ]	; OutputIndexFixed = [ 147 98 103 ]	; OutputPoint = [ 47.056951 -16.397464 -1176.938149 ]	; Deformation = [ -1.284049 -2.645464 37.561852 ]	; OutputIndexMoving = [ 153 107 89 ]
Point	90	; InputIndex = [ 155 96 54 ]	; InputPoint = [ 57.903000 -19.216000 -1299.500000 ]	; OutputIndexFixed = [ 155 94 68 ]	; OutputPoint = [ 58.012911 -21.878448 -1263.403702 ]	; Deformation = [ 0.109911 -2.662448 36.096298 ]	; OutputIndexMoving = [ 161 103 54 ]
Point	91	; InputIndex = [ 156 109 60 ]	; InputPoint = [ 59.269000 -1.458000 -1284.500000 ]	; OutputIndexFixed = [ 156 108 74 ]	; OutputPoint = [ 59.247998 -3.096558 -1248.592281 ]	; Deformation = [ -0.021002 -1.638559 35.907719 ]	; OutputIndexMoving = [ 162 117 60 ]
Point	92	; InputIndex = [ 86 92 54 ]	; InputPoint = [ -36.351000 -24.680000 -1299.500000 ]	; OutputIndexFixed = [ 85 82 67 ]	; OutputPoint = [ -37.714646 -38.240413 -1266.739401 ]	; Deformation = [ -1.363646 -13.560412 32.760597 ]	; OutputIndexMoving = [ 91 91 53 ]
Point	93	; InputIndex = [ 78 103 46 ]	; InputPoint = [ -47.279000 -9.654000 -1319.500000 ]	; OutputIndexFixed = [ 76 95 59 ]	; OutputPoint = [ -49.577414 -19.934107 -1286.342197 ]	; Deformation = [ -2.298414 -10.280107 33.157803 ]	; OutputIndexMoving = [ 82 104 45 ]
Point	94	; InputIndex = [ 144 109 72 ]	; InputPoint = [ 42.877000 -1.458000 -1254.500000 ]	; OutputIndexFixed = [ 143 108 87 ]	; OutputPoint = [ 41.908286 -3.487356 -1217.989952 ]	; Deformation = [ -0.968714 -2.029356 36.510048 ]	; OutputIndexMoving = [ 149 116 73 ]
Point	95	; InputIndex = [ 66 75 62 ]	; InputPoint = [ -63.671000 -47.902000 -1279.500000 ]	; OutputIndexFixed = [ 64 67 74 ]	; OutputPoint = [ -65.861150 -59.104766 -1249.130922 ]	; Deformation = [ -2.190150 -11.202765 30.369078 ]	; OutputIndexMoving = [ 70 76 60 ]
Point	96	; InputIndex = [ 166 95 56 ]	; InputPoint = [ 72.929000 -20.582000 -1294.500000 ]	; OutputIndexFixed = [ 166 93 70 ]	; OutputPoint = [ 73.500659 -22.995206 -1258.314875 ]	; Deformation = [ 0.571659 -2.413206 36.185123 ]	; OutputIndexMoving = [ 172 102 56 ]
Point	97	; InputIndex = [ 83 86 98 ]	; InputPoint = [ -40.449000 -32.876000 -1189.500000 ]	; OutputIndexFixed = [ 79 80 113 ]	; OutputPoint = [ -45.420250 -40.826176 -1151.910713 ]	; Deformation = [ -4.971250 -7.950176 37.589287 ]	; OutputIndexMoving = [ 85 89 99 ]
Point	98	; InputIndex = [ 150 83 94 ]	; InputPoint = [ 51.073000 -36.974000 -1199.500000 ]	; OutputIndexFixed = [ 149 80 109 ]	; OutputPoint = [ 49.685243 -40.892152 -1161.058933 ]	; Deformation = [ -1.387756 -3.918152 38.441067 ]	; OutputIndexMoving = [ 155 89 95 ]
Point	99	; InputIndex = [ 153 105 54 ]	; InputPoint = [ 55.171000 -6.922000 -1299.500000 ]	; OutputIndexFixed = [ 153 104 68 ]	; OutputPoint = [ 55.191622 -8.946727 -1263.717858 ]	; Deformation = [ 0.020622 -2.024727 35.782143 ]	; OutputIndexMoving = [ 159 112 54 ]
