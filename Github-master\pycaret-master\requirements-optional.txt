# Analysis
shap~=0.44.0 # 0.44.0 fixes matplotlib use_line_collection error
interpret>=0.2.7
umap-learn>=0.5.2
pyyaml
ydata-profiling>=4.3.1
explainerdashboard>=0.3.8  # For dashboard method
fairlearn==0.7.0  # For check_fairness method

# Models
xgboost>=1.1.0; python_version != "3.11" or platform_system != "Windows"
catboost>=0.23.2; platform_system != "Darwin"
catboost>=0.23.2,<1.2; platform_system == "Darwin"  # https://github.com/pycaret/pycaret/issues/3563
kmodes>=0.11.1
mlxtend>=0.19.0
# Can not be 1.6.0 since parallel argument in AutoARIMA was deprecated and this is not fixed in sktime
statsforecast>=0.5.5,<1.6.0
scikit-learn-intelex>=2023.0.1; platform_machine == 'x86_64' or platform_machine == 'AMD64'

# Tuners
tune-sklearn>=0.2.1; python_version != "3.11" and platform_system != "Windows"
ray[tune]>=1.0.0; python_version != "3.11" and platform_system != "Windows"
hyperopt>=0.2.7
optuna>=3.0.0
optuna-integration
scikit-optimize>=0.9.0

# MLOps
mlflow>=2.0.0
gradio>=3.50.2
boto3>=1.24.56  # For deploy_model method
fastapi  # For web api
uvicorn>=0.17.6  # For web api
m2cgen>=0.9.0  # For model conversion
evidently~=0.4.16

# Parallel
dask>=2024.4.1            # Python 3.11.9 breaks Dask (https://github.com/dask/dask/issues/11038)
distributed>=2024.4.1     # Python 3.11.9 breaks Dask (https://github.com/dask/dask/issues/11038)
fugue~=0.8.0
flask
Werkzeug>=2.2,<3.0
