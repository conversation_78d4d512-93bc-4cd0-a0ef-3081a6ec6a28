# ActivityNet Tools


### Requirements

 1. youtube-dl (https://github.com/rg3/youtube-dl/)

### Fetch ActivityNet

To download all the ActivityNet videos run the following command line:
```
mkdir $VIDEO_PATH
chmod +x fetch_activitynet_videos.sh
./fetch_activitynet_videos.sh $VIDEO_PATH activity_net.v1-X.json
```

Where `$VIDEO_PATH` is the path where the videos will be located. If you already 
have a subset of the videos, input that directory.


## Missing Data Requests

To accommodate missing data requests, we have made the full dataset available on Google and Baidu drives. 
Please fill in this [request form](https://docs.google.com/forms/d/e/1FAIpQLSeKaFq9ZfcmZ7W0B0PbEhfbTHY41GeEgwsa7WobJgGUhn4DTQ/viewform) 
to have a 7-day-access to download the videos from the drive folders.
