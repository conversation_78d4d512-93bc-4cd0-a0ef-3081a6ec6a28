# Module: containers.metrics.clustering
# Author: <PERSON><PERSON> (Yard1) <<EMAIL>>
# License: MIT

# The purpose of this module is to serve as a central repository of clustering metrics. The `clustering` module will
# call `get_all_metrics_containers()`, which will return instances of all classes in this module that have `ClusterMetricContainer`
# as a base (but not `ClusterMetricContainer` itself). In order to add a new model, you only need to create a new class that has
# `ClusterMetricContainer` as a base, set all of the required parameters in the `__init__` and then call `super().__init__`
# to complete the process. Refer to the existing classes for examples.

from typing import Any, Dict, Optional, Union

from sklearn import metrics
from sklearn.metrics._scorer import _BaseScorer

import pycaret.containers.base_container
import pycaret.internal.metrics
from pycaret.containers.metrics.base_metric import MetricContainer


class ClusterMetricContainer(MetricContainer):
    """
    Base clustering metric container class, for easier definition of containers. Ensures consistent format
    before being turned into a dataframe row.

    Parameters
    ----------
    id : str
        ID used as index.
    name : str
        Full name.
    score_func : type
        The callable used for the score function, eg. sklearn.metrics.accuracy_score.
    scorer : str or callable, default = None
        The scorer passed to models. Can be a string representing a built-in sklearn scorer,
        a sklearn Scorer object, or None, in which case a Scorer object will be created from
        score_func and args.
    target : str, default = 'pred'
        The target of the score function.
        - 'pred' for the prediction table
        - 'pred_proba' for pred_proba
        - 'threshold' for decision_function or predict_proba
    args : dict, default = {} (empty dict)
        The arguments to always pass to constructor when initializing score_func of class_def class.
    display_name : str, default = None
        Display name (shorter than name). Used in display dataframe header. If None or empty, will use name.
    greater_is_better: bool, default = True
        Whether score_func is a score function (default), meaning high is good,
        or a loss function, meaning low is good. In the latter case, the
        scorer object will sign-flip the outcome of the score_func.
    needs_ground_truth: bool, default = False
        Whether the metric needs ground truth to be calculated.
    is_custom : bool, default = False
        Is the metric custom. Should be False for all metrics defined in PyCaret.

    Attributes
    ----------
    id : str
        ID used as index.
    name : str
        Full name.
    score_func : type
        The callable used for the score function, eg. metrics.accuracy_score.
    scorer : str or callable
        The scorer passed to models. Can be a string representing a built-in sklearn scorer,
        a sklearn Scorer object, or None, in which case a Scorer object will be created from
        score_func and args.
    target : str, default = 'pred'
        The target of the score function. Only 'pred' is supported for clustering.
    args : dict
        The arguments to always pass to constructor when initializing score_func of class_def class.
    display_name : str
        Display name (shorter than name). Used in display dataframe header.
    greater_is_better: bool
        Whether score_func is a score function (default), meaning high is good,
        or a loss function, meaning low is good. In the latter case, the
        scorer object will sign-flip the outcome of the score_func.
    needs_ground_truth: bool
        Whether the metric needs ground truth to be calculated.
    is_custom : bool
        Is the metric custom. Should be False for all metrics defined in PyCaret.

    """

    def __init__(
        self,
        id: str,
        name: str,
        score_func: type,
        scorer: Optional[Union[str, _BaseScorer]] = None,
        target: str = "pred",
        args: Dict[str, Any] = None,
        display_name: Optional[str] = None,
        greater_is_better: bool = True,
        needs_ground_truth: bool = False,
        is_custom: bool = False,
    ) -> None:
        allowed_targets = ["pred"]
        if target not in allowed_targets:
            raise ValueError(f"Target must be one of {', '.join(allowed_targets)}.")

        if not args:
            args = {}
        if not isinstance(args, dict):
            raise TypeError("args needs to be a dictionary.")

        scorer = (
            scorer
            if scorer
            else pycaret.internal.metrics.make_scorer_with_error_score(
                score_func,
                greater_is_better=greater_is_better,
                error_score=0.0,
                **args,
            )
        )

        super().__init__(
            id=id,
            name=name,
            score_func=score_func,
            scorer=scorer,
            args=args,
            display_name=display_name,
            greater_is_better=greater_is_better,
            is_custom=is_custom,
        )

        self.target = target
        self.needs_ground_truth = needs_ground_truth

    def get_dict(self, internal: bool = True) -> Dict[str, Any]:
        """
        Returns a dictionary of the model properties, to
        be turned into a pandas DataFrame row.

        Parameters
        ----------
        internal : bool, default = True
            If True, will return all properties. If False, will only
            return properties intended for the user to see.

        Returns
        -------
        dict of str : Any

        """
        d = {
            "ID": self.id,
            "Name": self.name,
            "Display Name": self.display_name,
            "Score Function": self.score_func,
            "Scorer": self.scorer,
            "Target": self.target,
            "Args": self.args,
            "Greater is Better": self.greater_is_better,
            "Needs Ground Truth": self.needs_ground_truth,
            "Custom": self.is_custom,
        }

        return d


class SilhouetteMetricContainer(ClusterMetricContainer):
    def __init__(self, globals_dict: dict) -> None:
        super().__init__(
            id="silhouette",
            name="Silhouette",
            score_func=metrics.silhouette_score,
        )


class CHSMetricContainer(ClusterMetricContainer):
    def __init__(self, globals_dict: dict) -> None:
        super().__init__(
            id="chs",
            name="Calinski-Harabasz",
            score_func=metrics.calinski_harabasz_score,
        )


class DBMetricContainer(ClusterMetricContainer):
    def __init__(self, globals_dict: dict) -> None:
        super().__init__(
            id="db",
            name="Davies-Bouldin",
            score_func=metrics.davies_bouldin_score,
        )


class HSMetricContainer(ClusterMetricContainer):
    def __init__(self, globals_dict: dict) -> None:
        super().__init__(
            id="hs",
            name="Homogeneity Score",
            display_name="Homogeneity",
            score_func=metrics.homogeneity_score,
            needs_ground_truth=True,
        )


class ARIMetricContainer(ClusterMetricContainer):
    def __init__(self, globals_dict: dict) -> None:
        super().__init__(
            id="ari",
            name="Rand Index",
            score_func=metrics.adjusted_rand_score,
            needs_ground_truth=True,
        )


class CSMetricContainer(ClusterMetricContainer):
    def __init__(self, globals_dict: dict) -> None:
        super().__init__(
            id="cs",
            name="Completeness Score",
            display_name="Completeness",
            score_func=metrics.completeness_score,
            needs_ground_truth=True,
        )


def get_all_metric_containers(
    globals_dict: dict, raise_errors: bool = True
) -> Dict[str, ClusterMetricContainer]:
    return pycaret.containers.base_container.get_all_containers(
        globals(), globals_dict, ClusterMetricContainer, raise_errors
    )
