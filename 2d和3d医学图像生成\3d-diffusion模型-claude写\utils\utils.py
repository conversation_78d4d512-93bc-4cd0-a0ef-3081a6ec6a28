import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import nibabel as nib
from PIL import Image
import os

def save_checkpoint(model, optimizer, scheduler, epoch, loss, filepath):
    """保存checkpoint"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'loss': loss
    }
    torch.save(checkpoint, filepath)
    print(f"Checkpoint saved at epoch {epoch}")

def load_checkpoint(filepath, model, optimizer=None, scheduler=None, device='cpu'):
    """加载checkpoint"""
    checkpoint = torch.load(filepath, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    epoch = checkpoint['epoch']
    loss = checkpoint['loss']
    
    if optimizer is not None:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    if scheduler is not None:
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    print(f"Checkpoint loaded from epoch {epoch}")
    return epoch, loss

def save_samples(ap_images, hbp_real, hbp_generated, save_path, normalize=True):
    """保存生成的样本图像"""
    batch_size = ap_images.size(0)
    
    # 创建子图
    fig, axes = plt.subplots(batch_size, 3, figsize=(12, 4 * batch_size))
    if batch_size == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(batch_size):
        # 转换为numpy并归一化
        ap_img = tensor_to_numpy(ap_images[i], normalize)
        real_img = tensor_to_numpy(hbp_real[i], normalize)
        gen_img = tensor_to_numpy(hbp_generated[i], normalize)
        
        # 绘制图像
        axes[i, 0].imshow(ap_img, cmap='gray')
        axes[i, 0].set_title('AP Input')
        axes[i, 0].axis('off')
        
        axes[i, 1].imshow(real_img, cmap='gray')
        axes[i, 1].set_title('HBP Real')
        axes[i, 1].axis('off')
        
        axes[i, 2].imshow(gen_img, cmap='gray')
        axes[i, 2].set_title('HBP Generated')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()

def tensor_to_numpy(tensor, normalize=True):
    """将tensor转换为numpy数组"""
    if tensor.dim() == 3:
        img = tensor.squeeze(0).cpu().numpy()
    else:
        img = tensor.cpu().numpy()
    
    if normalize:
        img = (img - img.min()) / (img.max() - img.min() + 1e-8)
    
    return img

def numpy_to_tensor(array):
    """将numpy数组转换为tensor"""
    return torch.from_numpy(array).float()

def save_nii_image(image_array, save_path, affine=None):
    """保存为NII格式"""
    if affine is None:
        affine = np.eye(4)
    
    img = nib.Nifti1Image(image_array, affine)
    nib.save(img, save_path)

def load_nii_image(file_path):
    """加载NII图像"""
    img = nib.load(file_path)
    return img.get_fdata(), img.affine

def create_gif_from_samples(sample_dir, output_path, duration=500):
    """从样本图像创建GIF"""
    sample_files = sorted(Path(sample_dir).glob("samples_epoch_*.png"))
    
    images = []
    for file in sample_files:
        img = Image.open(file)
        images.append(img)
    
    if images:
        images[0].save(
            output_path,
            save_all=True,
            append_images=images[1:],
            duration=duration,
            loop=0
        )
        print(f"GIF saved to {output_path}")

def calculate_metrics(real_images, generated_images):
    """计算图像质量指标"""
    from skimage.metrics import structural_similarity as ssim
    from skimage.metrics import peak_signal_noise_ratio as psnr
    
    metrics = {
        'ssim': [],
        'psnr': [],
        'mse': [],
        'mae': []
    }
    
    for real, gen in zip(real_images, generated_images):
        # 转换为numpy
        if torch.is_tensor(real):
            real = tensor_to_numpy(real)
        if torch.is_tensor(gen):
            gen = tensor_to_numpy(gen)
        
        # 确保值在[0,1]范围内
        real = np.clip(real, 0, 1)
        gen = np.clip(gen, 0, 1)
        
        # 计算指标
        ssim_val = ssim(real, gen, data_range=1.0)
        psnr_val = psnr(real, gen, data_range=1.0)
        mse_val = np.mean((real - gen) ** 2)
        mae_val = np.mean(np.abs(real - gen))
        
        metrics['ssim'].append(ssim_val)
        metrics['psnr'].append(psnr_val)
        metrics['mse'].append(mse_val)
        metrics['mae'].append(mae_val)
    
    # 计算平均值
    for key in metrics:
        metrics[key] = np.mean(metrics[key])
    
    return metrics

def visualize_training_progress(log_dir, save_path):
    """可视化训练进度"""
    from torch.utils.tensorboard import SummaryWriter
    import pandas as pd
    
    # 这里需要从tensorboard日志中提取数据
    # 简化版本，假设有loss历史记录
    pass

def set_seed(seed=42):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def count_parameters(model):
    """计算模型参数数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total': total_params,
        'trainable': trainable_params,
        'non_trainable': total_params - trainable_params
    }

def get_device():
    """获取可用设备"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"使用GPU: {torch.cuda.get_device_name()}")
    else:
        device = torch.device('cpu')
        print("使用CPU")
    
    return device

def create_lr_scheduler(optimizer, scheduler_type='cosine', **kwargs):
    """创建学习率调度器"""
    if scheduler_type == 'cosine':
        return torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, 
            T_max=kwargs.get('T_max', 100),
            eta_min=kwargs.get('eta_min', 1e-6)
        )
    elif scheduler_type == 'step':
        return torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=kwargs.get('step_size', 30),
            gamma=kwargs.get('gamma', 0.1)
        )
    elif scheduler_type == 'reduce_on_plateau':
        return torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=kwargs.get('factor', 0.5),
            patience=kwargs.get('patience', 10)
        )
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")

def preprocess_medical_image(image, target_range=(0, 1), clip_percentile=None):
    """预处理医学图像"""
    # 移除异常值
    if clip_percentile is not None:
        low_p, high_p = clip_percentile
        low_val = np.percentile(image, low_p)
        high_val = np.percentile(image, high_p)
        image = np.clip(image, low_val, high_val)
    
    # 标准化
    mean = np.mean(image)
    std = np.std(image)
    image = (image - mean) / (std + 1e-8)
    
    # 缩放到目标范围
    image_min = np.min(image)
    image_max = np.max(image)
    image = (image - image_min) / (image_max - image_min + 1e-8)
    image = image * (target_range[1] - target_range[0]) + target_range[0]
    
    return image

def check_data_integrity(data_dir):
    """检查数据完整性"""
    data_dir = Path(data_dir)
    
    issues = []
    file_count = 0
    
    for file_path in data_dir.rglob("*.nii.gz"):
        file_count += 1
        try:
            img = nib.load(file_path)
            data = img.get_fdata()
            
            # 检查数据形状
            if len(data.shape) not in [2, 3]:
                issues.append(f"{file_path}: 异常的数据维度 {data.shape}")
            
            # 检查是否有无效值
            if np.isnan(data).any():
                issues.append(f"{file_path}: 包含NaN值")
            
            if np.isinf(data).any():
                issues.append(f"{file_path}: 包含无穷大值")
            
            # 检查数据范围
            if data.min() == data.max():
                issues.append(f"{file_path}: 数据无变化（常数图像）")
                
        except Exception as e:
            issues.append(f"{file_path}: 加载失败 - {str(e)}")
    
    print(f"检查了 {file_count} 个文件")
    if issues:
        print(f"发现 {len(issues)} 个问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("所有文件检查通过!")
    
    return issues

def split_dataset(file_list, train_ratio=0.8, val_ratio=0.1, seed=42):
    """划分数据集"""
    np.random.seed(seed)
    n_files = len(file_list)
    
    # 随机打乱
    indices = np.random.permutation(n_files)
    
    # 计算分割点
    train_end = int(train_ratio * n_files)
    val_end = train_end + int(val_ratio * n_files)
    
    train_indices = indices[:train_end]
    val_indices = indices[train_end:val_end]
    test_indices = indices[val_end:]
    
    train_files = [file_list[i] for i in train_indices]
    val_files = [file_list[i] for i in val_indices]
    test_files = [file_list[i] for i in test_indices]
    
    return train_files, val_files, test_files

if __name__ == "__main__":
    # 测试工具函数
    print("测试工具函数...")
    
    # 测试设备检测
    device = get_device()
    
    # 测试参数计数
    model = torch.nn.Linear(100, 50)
    params = count_parameters(model)
    print(f"模型参数: {params}")
    
    # 测试图像预处理
    dummy_image = np.random.randn(256, 256)
    processed = preprocess_medical_image(dummy_image)
    print(f"预处理前范围: [{dummy_image.min():.3f}, {dummy_image.max():.3f}]")
    print(f"预处理后范围: [{processed.min():.3f}, {processed.max():.3f}]")
    
    print("工具函数测试完成!")