{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 9. Point set and Mask Transformation"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["<a id='section_id10'></a>\n", "Transformix can be used to transform point sets and mask images as well. Masks can be seen as images so the registration of masks is done in similar fashion as the registration of a moving image.\n", "When transforming an image or mask (defined in the moving image domain), it is transformed to the fixed image domain. This is useful for example when one needs to deform a segmentation from the moving image to the fixed image, to obtain an estimate of a segmentation in the fixed image. When transforming a point set, this happens the other way around: from the fixed image domain to the moving image domain. This is useful for example when you want to know where a point maps to.\n", "Point sets therefor need not be transformed with the backwards mapping protocol, but can instead be transformed with regular forward transformation (fixed -> moving).\n", "Transforming point sets can be used to get the regions of interest (ROI) in the moving image, based on ROI of the fixed image."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Elastix"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "from itkwidgets import compare, checkerboard,view"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import Images\n", "fixed_image = itk.imread('data/CT_3D_lung_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_3D_lung_moving.mha', itk.F)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_map_rigid = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(parameter_map_rigid)\n", "parameter_map_affine= parameter_object.GetDefaultParameterMap('affine')\n", "parameter_object.AddParameterMap(parameter_map_affine)\n", "parameter_map_bspline = parameter_object.GetDefaultParameterMap('bspline')\n", "parameter_object.AddParameterMap(parameter_map_bspline)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Registration with the registration function..."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object,\n", "    log_to_console=True)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Mask Transformation"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Import Mask\n", "# Note: Version v0.17.0 and onwards supports various image types for elastix and transformix functions/objects\n", "moving_mask = itk.imread('data/CT_3D_lung_moving_mask.mha', itk.UC)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Mask image is a binary image, therefore the FinalBSplineInterpolationOrder should be 0\n", "result_transform_parameters.SetParameter('FinalBSplineInterpolationOrder','0')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Transformation of a mask is similar to the transformation of an image and can either be done in one line with the transformix function..."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Procedural interface of transformix filter\n", "result_moving_mask = itk.transformix_filter(\n", "    moving_mask,\n", "    result_transform_parameters)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an transformix image filter object."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Load Transformix Object, initialize with 3D Mask. \n", "transformix_object = itk.TransformixFilter.New(moving_mask)\n", "\n", "transformix_object.SetTransformParameterObject(result_transform_parameters)\n", "\n", "# Update object (required)\n", "transformix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Transformation\n", "result_moving_mask = transformix_object.GetOutput()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Segmentation Transformation Evaluation"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The result of a segmentation transformation can be evaluated by means of, for example, the dice loss. The dice loss is the proportion of the 2 segmentations that overlap. In the example above the masks used were segmentations of the images. (This is offcourse not always the case, masks could also hide artifacts, in which case they're not (only) image segmentations). The dice loss of the segmentation transformation can therefore be calculated with the dice loss of the 2 masks."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# In case of boolaen segmentation, the dice loss is equal to 2 x dot product of the flattened arrays, \n", "# divided by the total volume of both masks.\n", "def dice_loss(y_true, y_pred):\n", "    y_true = y_true.flatten()\n", "    y_pred = y_pred.flatten()\n", "    intersection = y_true.dot(y_pred)\n", "    union = sum(y_true) + sum(y_pred)\n", "    dice = 2 * intersection/union\n", "    return dice"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Import groundtruth segmentation\n", "fixed_mask = itk.imread('data/CT_3D_lung_fixed_mask.mha', itk.UC)\n", "\n", "# Cast itk images to numpy arrays and round result image to boolean array.\n", "fixed_mask_np = np.asarray(fixed_mask)\n", "result_mask_np = np.asarray(result_moving_mask).round().astype(int)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dice loss: 0.9862246731348647\n"]}], "source": ["print(\"Dice loss:\",dice_loss(fixed_mask_np, result_mask_np))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c76e48b027c84e37963cf1e02ee8a702", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=False, description='cmap'), Checkbox(v…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compare(fixed_mask, result_moving_mask)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Point Set Transformation"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Procedural interface of transformix filter\n", "result_point_set = itk.transformix_pointset(\n", "    moving_image, result_transform_parameters,\n", "    fixed_point_set_file_name='data/CT_3D_lung_fixed_point_set_corrected.txt',\n", "    output_directory = './exampleoutput')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an transformix image filter object."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Load Transformix Object\n", "transformix_object = itk.TransformixFilter.New(moving_image)\n", "transformix_object.SetFixedPointSetFileName('data/CT_3D_lung_fixed_point_set_corrected.txt')\n", "transformix_object.SetTransformParameterObject(result_transform_parameters)\n", "transformix_object.SetLogToConsole(True)\n", "transformix_object.SetOutputDirectory('./exampleoutput/')\n", "\n", "# Update object (required)\n", "transformix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Transformation\n", "# -- Bug? -- Output is saved as .txt file in outputdirectory.\n", "# The .GetOutput() function outputs an empty image.\n", "output_transformix = transformix_object.GetOutput()\n", "result_point_set = np.loadtxt('exampleoutput/outputpoints.txt', dtype='str')[:,30:33].astype('float64')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3a044dd051e04f2f92d3dae77639590e", "version_major": 2, "version_minor": 0}, "text/plain": ["Viewer(geometries=[], gradient_opacity=0.22, point_set_colors=array([[0.8392157, 0.       , 0.       ]], dtype…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["view(moving_image, point_sets=[result_point_set])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}