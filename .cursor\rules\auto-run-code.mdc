---
description: agent模式自动跟终端结果进行交互
globs: 
alwaysApply: false
---
# Auto Run Code in Agent Mode

When operating in agent mode, after writing or modifying code:

1. Automatically propose to run the code using the `run_terminal_cmd` function 
2. Include a clear explanation of what the command will do
3. Set appropriate parameters based on the script's requirements
4. Verify the command has all necessary permissions and parameters before execution

This behavior is particularly applicable when:
- Creating new scripts or modifying existing executable scripts
- Implementing data processing pipelines that should be verified immediately

Example workflow:
1. Write or edit the code
2. Automatically propose a command to run the script
3. Show the execution results to verify functionality


