########
Examples
########

This page contains examples of use of ``giotto-tda``.

.. toctree::
   :maxdepth: 1
   
   gravitational_waves_detection
   classifying_shapes
   lorenz_attractor
   MNIST_classification
   local_hom_NLP_disambiguation
   voids_on_the_plane

..
   include:: lorenz_attractor.rst
   :end-before: Import libraries

   Try it on `github <https://github.com/giotto-ai/giotto-tda/blob/master/examples/lorenz_attractor.ipynb>`__ for full interactivity,
   or check `the static version <lorenz_attractor.html>`__.

    .. include:: classifying_shapes.rst
       :end-before: Import libraries

    Try it on `github <https://github.com/giotto-ai/giotto-tda/blob/master/examples/classifying_shapes.ipynb>`__ for full interactivity,
    or check `the static version <classifying_shapes.html>`__.

    .. include:: voids_on_the_plane.rst
       :end-before: Import libraries

    Try it on `github <https://github.com/giotto-ai/giotto-tda/blob/master/examples/voids_on_the_plane.ipynb>`__ for full interactivity,
    or check `the static version <voids_on_the_plane.html>`__.
