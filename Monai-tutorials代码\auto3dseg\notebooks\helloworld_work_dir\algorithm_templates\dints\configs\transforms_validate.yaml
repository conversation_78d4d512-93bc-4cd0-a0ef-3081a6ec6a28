---
image_key: image
label_key: label
transforms_validate:
  _target_: <PERSON>mpo<PERSON>
  transforms:
  - _target_: Compo<PERSON>
    transforms:
    - _target_: LoadImaged
      keys: "@image_key"
      dtype: "$np.float32"
      image_only: false
    - _target_: LoadImaged
      keys: "@label_key"
      dtype: "$np.uint8"
      image_only: false
  - _target_: EnsureChannelFirstd
    keys: ["@image_key", "@label_key"]
  - PLACEHOLDER_INTENSITY_NORMALIZATION
  - _target_: Orientationd
    keys: ["@image_key", "@label_key"]
    axcodes: RAS
  - _target_: Spacingd
    keys: ["@image_key", "@label_key"]
    pixdim: "@training#transforms#resample_resolution"
    mode: [bilinear, nearest]
    align_corners: [true, true]
  - _target_: CastToTyped
    keys: ["@image_key", "@label_key"]
    dtype: ["$torch.float32", "$torch.uint8"]
  - _target_: EnsureTyped
    keys: ['@image_key', '@label_key']
    track_meta: false
