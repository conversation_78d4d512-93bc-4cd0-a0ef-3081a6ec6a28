{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Activation Mapping and Saliency Analysis\n", "\n", "This notebook demonstrates the process of generating activation maps and performing saliency analysis on a trained deep learning model for cancer image biomarker detection. The workflow includes:\n", "\n", "1. Loading the necessary libraries and dependencies\n", "2. Importing the trained model checkpoint\n", "3. Reconstructing the model architecture (ResNet50)\n", "4. Loading and preprocessing input images\n", "5. Generating activation maps for specific layers\n", "6. Applying saliency methods (e.g., GradCAM) to visualize important regions\n", "7. Analyzing and interpreting the results\n", "\n", "Follow the steps below to understand how the model focuses on different parts of medical images to make predictions."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import scipy\n", "from monai.visualize import GuidedBackpropSmoothGrad, blend_images\n", "\n", "from fmcib.preprocessing import preprocess"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Model Loading and Configuration\n", "\n", "This section handles loading the trained model checkpoint and configuring it for analysis:\n", "\n", "1. Download the checkpoint from Hugging <PERSON> using the command (in the root dir of the repo):\n", "   ```\n", "   huggingface-cli download surajpaib/FMCIB --repo-type model --local-dir .\n", "   ```\n", "2. Load the state dictionary from the downloaded SSL finetuned checkpoint\n", "3. Extract the trunk (feature extractor) and head (classifier) components\n", "4. Configure a MONAI ResNet50 model to match the SSL model architecture\n", "5. Load the trained weights into the MONAI model\n", "\n", "This process ensures we have a properly loaded and configured model ready for activation mapping and saliency analysis.\n", "\n", "Note: Make sure you have the 'huggingface-cli' installed. If not, you can install it using:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(odict_keys(['conv1.weight', 'bn1.weight', 'bn1.bias', 'bn1.running_mean', 'bn1.running_var', 'bn1.num_batches_tracked', 'layer1.0.conv1.weight', 'layer1.0.bn1.weight', 'layer1.0.bn1.bias', 'layer1.0.bn1.running_mean', 'layer1.0.bn1.running_var', 'layer1.0.bn1.num_batches_tracked', 'layer1.0.conv2.weight', 'layer1.0.bn2.weight', 'layer1.0.bn2.bias', 'layer1.0.bn2.running_mean', 'layer1.0.bn2.running_var', 'layer1.0.bn2.num_batches_tracked', 'layer1.0.conv3.weight', 'layer1.0.bn3.weight', 'layer1.0.bn3.bias', 'layer1.0.bn3.running_mean', 'layer1.0.bn3.running_var', 'layer1.0.bn3.num_batches_tracked', 'layer1.0.downsample.0.weight', 'layer1.0.downsample.0.bias', 'layer1.0.downsample.1.weight', 'layer1.0.downsample.1.bias', 'layer1.0.downsample.1.running_mean', 'layer1.0.downsample.1.running_var', 'layer1.0.downsample.1.num_batches_tracked', 'layer1.1.conv1.weight', 'layer1.1.bn1.weight', 'layer1.1.bn1.bias', 'layer1.1.bn1.running_mean', 'layer1.1.bn1.running_var', 'layer1.1.bn1.num_batches_tracked', 'layer1.1.conv2.weight', 'layer1.1.bn2.weight', 'layer1.1.bn2.bias', 'layer1.1.bn2.running_mean', 'layer1.1.bn2.running_var', 'layer1.1.bn2.num_batches_tracked', 'layer1.1.conv3.weight', 'layer1.1.bn3.weight', 'layer1.1.bn3.bias', 'layer1.1.bn3.running_mean', 'layer1.1.bn3.running_var', 'layer1.1.bn3.num_batches_tracked', 'layer1.2.conv1.weight', 'layer1.2.bn1.weight', 'layer1.2.bn1.bias', 'layer1.2.bn1.running_mean', 'layer1.2.bn1.running_var', 'layer1.2.bn1.num_batches_tracked', 'layer1.2.conv2.weight', 'layer1.2.bn2.weight', 'layer1.2.bn2.bias', 'layer1.2.bn2.running_mean', 'layer1.2.bn2.running_var', 'layer1.2.bn2.num_batches_tracked', 'layer1.2.conv3.weight', 'layer1.2.bn3.weight', 'layer1.2.bn3.bias', 'layer1.2.bn3.running_mean', 'layer1.2.bn3.running_var', 'layer1.2.bn3.num_batches_tracked', 'layer2.0.conv1.weight', 'layer2.0.bn1.weight', 'layer2.0.bn1.bias', 'layer2.0.bn1.running_mean', 'layer2.0.bn1.running_var', 'layer2.0.bn1.num_batches_tracked', 'layer2.0.conv2.weight', 'layer2.0.bn2.weight', 'layer2.0.bn2.bias', 'layer2.0.bn2.running_mean', 'layer2.0.bn2.running_var', 'layer2.0.bn2.num_batches_tracked', 'layer2.0.conv3.weight', 'layer2.0.bn3.weight', 'layer2.0.bn3.bias', 'layer2.0.bn3.running_mean', 'layer2.0.bn3.running_var', 'layer2.0.bn3.num_batches_tracked', 'layer2.0.downsample.0.weight', 'layer2.0.downsample.0.bias', 'layer2.0.downsample.1.weight', 'layer2.0.downsample.1.bias', 'layer2.0.downsample.1.running_mean', 'layer2.0.downsample.1.running_var', 'layer2.0.downsample.1.num_batches_tracked', 'layer2.1.conv1.weight', 'layer2.1.bn1.weight', 'layer2.1.bn1.bias', 'layer2.1.bn1.running_mean', 'layer2.1.bn1.running_var', 'layer2.1.bn1.num_batches_tracked', 'layer2.1.conv2.weight', 'layer2.1.bn2.weight', 'layer2.1.bn2.bias', 'layer2.1.bn2.running_mean', 'layer2.1.bn2.running_var', 'layer2.1.bn2.num_batches_tracked', 'layer2.1.conv3.weight', 'layer2.1.bn3.weight', 'layer2.1.bn3.bias', 'layer2.1.bn3.running_mean', 'layer2.1.bn3.running_var', 'layer2.1.bn3.num_batches_tracked', 'layer2.2.conv1.weight', 'layer2.2.bn1.weight', 'layer2.2.bn1.bias', 'layer2.2.bn1.running_mean', 'layer2.2.bn1.running_var', 'layer2.2.bn1.num_batches_tracked', 'layer2.2.conv2.weight', 'layer2.2.bn2.weight', 'layer2.2.bn2.bias', 'layer2.2.bn2.running_mean', 'layer2.2.bn2.running_var', 'layer2.2.bn2.num_batches_tracked', 'layer2.2.conv3.weight', 'layer2.2.bn3.weight', 'layer2.2.bn3.bias', 'layer2.2.bn3.running_mean', 'layer2.2.bn3.running_var', 'layer2.2.bn3.num_batches_tracked', 'layer2.3.conv1.weight', 'layer2.3.bn1.weight', 'layer2.3.bn1.bias', 'layer2.3.bn1.running_mean', 'layer2.3.bn1.running_var', 'layer2.3.bn1.num_batches_tracked', 'layer2.3.conv2.weight', 'layer2.3.bn2.weight', 'layer2.3.bn2.bias', 'layer2.3.bn2.running_mean', 'layer2.3.bn2.running_var', 'layer2.3.bn2.num_batches_tracked', 'layer2.3.conv3.weight', 'layer2.3.bn3.weight', 'layer2.3.bn3.bias', 'layer2.3.bn3.running_mean', 'layer2.3.bn3.running_var', 'layer2.3.bn3.num_batches_tracked', 'layer3.0.conv1.weight', 'layer3.0.bn1.weight', 'layer3.0.bn1.bias', 'layer3.0.bn1.running_mean', 'layer3.0.bn1.running_var', 'layer3.0.bn1.num_batches_tracked', 'layer3.0.conv2.weight', 'layer3.0.bn2.weight', 'layer3.0.bn2.bias', 'layer3.0.bn2.running_mean', 'layer3.0.bn2.running_var', 'layer3.0.bn2.num_batches_tracked', 'layer3.0.conv3.weight', 'layer3.0.bn3.weight', 'layer3.0.bn3.bias', 'layer3.0.bn3.running_mean', 'layer3.0.bn3.running_var', 'layer3.0.bn3.num_batches_tracked', 'layer3.0.downsample.0.weight', 'layer3.0.downsample.0.bias', 'layer3.0.downsample.1.weight', 'layer3.0.downsample.1.bias', 'layer3.0.downsample.1.running_mean', 'layer3.0.downsample.1.running_var', 'layer3.0.downsample.1.num_batches_tracked', 'layer3.1.conv1.weight', 'layer3.1.bn1.weight', 'layer3.1.bn1.bias', 'layer3.1.bn1.running_mean', 'layer3.1.bn1.running_var', 'layer3.1.bn1.num_batches_tracked', 'layer3.1.conv2.weight', 'layer3.1.bn2.weight', 'layer3.1.bn2.bias', 'layer3.1.bn2.running_mean', 'layer3.1.bn2.running_var', 'layer3.1.bn2.num_batches_tracked', 'layer3.1.conv3.weight', 'layer3.1.bn3.weight', 'layer3.1.bn3.bias', 'layer3.1.bn3.running_mean', 'layer3.1.bn3.running_var', 'layer3.1.bn3.num_batches_tracked', 'layer3.2.conv1.weight', 'layer3.2.bn1.weight', 'layer3.2.bn1.bias', 'layer3.2.bn1.running_mean', 'layer3.2.bn1.running_var', 'layer3.2.bn1.num_batches_tracked', 'layer3.2.conv2.weight', 'layer3.2.bn2.weight', 'layer3.2.bn2.bias', 'layer3.2.bn2.running_mean', 'layer3.2.bn2.running_var', 'layer3.2.bn2.num_batches_tracked', 'layer3.2.conv3.weight', 'layer3.2.bn3.weight', 'layer3.2.bn3.bias', 'layer3.2.bn3.running_mean', 'layer3.2.bn3.running_var', 'layer3.2.bn3.num_batches_tracked', 'layer3.3.conv1.weight', 'layer3.3.bn1.weight', 'layer3.3.bn1.bias', 'layer3.3.bn1.running_mean', 'layer3.3.bn1.running_var', 'layer3.3.bn1.num_batches_tracked', 'layer3.3.conv2.weight', 'layer3.3.bn2.weight', 'layer3.3.bn2.bias', 'layer3.3.bn2.running_mean', 'layer3.3.bn2.running_var', 'layer3.3.bn2.num_batches_tracked', 'layer3.3.conv3.weight', 'layer3.3.bn3.weight', 'layer3.3.bn3.bias', 'layer3.3.bn3.running_mean', 'layer3.3.bn3.running_var', 'layer3.3.bn3.num_batches_tracked', 'layer3.4.conv1.weight', 'layer3.4.bn1.weight', 'layer3.4.bn1.bias', 'layer3.4.bn1.running_mean', 'layer3.4.bn1.running_var', 'layer3.4.bn1.num_batches_tracked', 'layer3.4.conv2.weight', 'layer3.4.bn2.weight', 'layer3.4.bn2.bias', 'layer3.4.bn2.running_mean', 'layer3.4.bn2.running_var', 'layer3.4.bn2.num_batches_tracked', 'layer3.4.conv3.weight', 'layer3.4.bn3.weight', 'layer3.4.bn3.bias', 'layer3.4.bn3.running_mean', 'layer3.4.bn3.running_var', 'layer3.4.bn3.num_batches_tracked', 'layer3.5.conv1.weight', 'layer3.5.bn1.weight', 'layer3.5.bn1.bias', 'layer3.5.bn1.running_mean', 'layer3.5.bn1.running_var', 'layer3.5.bn1.num_batches_tracked', 'layer3.5.conv2.weight', 'layer3.5.bn2.weight', 'layer3.5.bn2.bias', 'layer3.5.bn2.running_mean', 'layer3.5.bn2.running_var', 'layer3.5.bn2.num_batches_tracked', 'layer3.5.conv3.weight', 'layer3.5.bn3.weight', 'layer3.5.bn3.bias', 'layer3.5.bn3.running_mean', 'layer3.5.bn3.running_var', 'layer3.5.bn3.num_batches_tracked', 'layer4.0.conv1.weight', 'layer4.0.bn1.weight', 'layer4.0.bn1.bias', 'layer4.0.bn1.running_mean', 'layer4.0.bn1.running_var', 'layer4.0.bn1.num_batches_tracked', 'layer4.0.conv2.weight', 'layer4.0.bn2.weight', 'layer4.0.bn2.bias', 'layer4.0.bn2.running_mean', 'layer4.0.bn2.running_var', 'layer4.0.bn2.num_batches_tracked', 'layer4.0.conv3.weight', 'layer4.0.bn3.weight', 'layer4.0.bn3.bias', 'layer4.0.bn3.running_mean', 'layer4.0.bn3.running_var', 'layer4.0.bn3.num_batches_tracked', 'layer4.0.downsample.0.weight', 'layer4.0.downsample.0.bias', 'layer4.0.downsample.1.weight', 'layer4.0.downsample.1.bias', 'layer4.0.downsample.1.running_mean', 'layer4.0.downsample.1.running_var', 'layer4.0.downsample.1.num_batches_tracked', 'layer4.1.conv1.weight', 'layer4.1.bn1.weight', 'layer4.1.bn1.bias', 'layer4.1.bn1.running_mean', 'layer4.1.bn1.running_var', 'layer4.1.bn1.num_batches_tracked', 'layer4.1.conv2.weight', 'layer4.1.bn2.weight', 'layer4.1.bn2.bias', 'layer4.1.bn2.running_mean', 'layer4.1.bn2.running_var', 'layer4.1.bn2.num_batches_tracked', 'layer4.1.conv3.weight', 'layer4.1.bn3.weight', 'layer4.1.bn3.bias', 'layer4.1.bn3.running_mean', 'layer4.1.bn3.running_var', 'layer4.1.bn3.num_batches_tracked', 'layer4.2.conv1.weight', 'layer4.2.bn1.weight', 'layer4.2.bn1.bias', 'layer4.2.bn1.running_mean', 'layer4.2.bn1.running_var', 'layer4.2.bn1.num_batches_tracked', 'layer4.2.conv2.weight', 'layer4.2.bn2.weight', 'layer4.2.bn2.bias', 'layer4.2.bn2.running_mean', 'layer4.2.bn2.running_var', 'layer4.2.bn2.num_batches_tracked', 'layer4.2.conv3.weight', 'layer4.2.bn3.weight', 'layer4.2.bn3.bias', 'layer4.2.bn3.running_mean', 'layer4.2.bn3.running_var', 'layer4.2.bn3.num_batches_tracked']),\n", " odict_keys(['0.0.weight', '0.0.bias', '1.0.weight', '1.0.bias', '2.0.weight', '2.0.bias', '3.0.weight', '3.0.bias']))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["CHECKPOINT_PATH = \"../models/baselines/task2/fmcib_finetuned.torch\"\n", "state_dict = torch.load(CHECKPOINT_PATH)\n", "trained_trunk = state_dict[\"trunk_state_dict\"]\n", "trained_head = state_dict[\"head_state_dict\"]\n", "\n", "trained_trunk.keys(), trained_head.keys()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0.weight: torch.<PERSON><PERSON>([2048, 4096])\n", "0.0.bias: <PERSON><PERSON><PERSON>([2048])\n", "1.0.weight: torch.Size([512, 2048])\n", "1.0.bias: <PERSON><PERSON><PERSON>([512])\n", "2.0.weight: torch.Size([256, 512])\n", "2.0.bias: <PERSON><PERSON><PERSON>([256])\n", "3.0.weight: torch.<PERSON><PERSON>([2, 256])\n", "3.0.bias: <PERSON><PERSON><PERSON><PERSON>([2])\n"]}], "source": ["# Get shapes of trained_head keys\n", "for key, value in trained_head.items():\n", "    print(f\"{key}: {value.shape}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Add trunk of the model\n", "from monai.networks.nets.resnet import ResNet, ResNetBottleneck\n", "\n", "trunk = ResNet(\n", "    block=ResNetBottleneck,\n", "    layers=(3, 4, 6, 3),\n", "    block_inplanes=(64, 128, 256, 512),\n", "    spatial_dims=3,\n", "    n_input_channels=1,\n", "    conv1_t_stride=2,\n", "    conv1_t_size=7,\n", "    widen_factor=2,\n", ")\n", "trunk.fc = nn.Identity()\n", "\n", "# Add head part of the model\n", "head0 = nn.Sequential(nn.Linear(4096, 2048, bias=True), nn.ReLU(inplace=True))\n", "head1 = nn.Sequential(nn.Linear(2048, 512, bias=True), nn.ReLU(inplace=True))\n", "head2 = nn.Sequential(nn.Linear(512, 256, bias=True), nn.ReLU(inplace=True))\n", "head3 = nn.Sequential(nn.Linear(256, 2, bias=True))\n", "head = nn.Sequential(head0, head1, head2, head3)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["Sequential(\n", "  (0): ResNet(\n", "    (conv1): Conv3d(1, 128, kernel_size=(7, 7, 7), stride=(2, 2, 2), padding=(3, 3, 3), bias=False)\n", "    (bn1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (relu): ReLU(inplace=True)\n", "    (maxpool): MaxPool3d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)\n", "    (layer1): Sequential(\n", "      (0): ResNetBottleneck(\n", "        (conv1): Conv3d(128, 128, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(128, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(128, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "        (downsample): Sequential(\n", "          (0): Conv3d(128, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1))\n", "          (1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        )\n", "      )\n", "      (1): ResNetBottleneck(\n", "        (conv1): Conv3d(512, 128, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(128, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(128, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (2): ResNetBottleneck(\n", "        (conv1): Conv3d(512, 128, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(128, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(128, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "    )\n", "    (layer2): Sequential(\n", "      (0): ResNetBottleneck(\n", "        (conv1): Conv3d(512, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(2, 2, 2), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(256, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "        (downsample): Sequential(\n", "          (0): Conv3d(512, 1024, kernel_size=(1, 1, 1), stride=(2, 2, 2))\n", "          (1): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        )\n", "      )\n", "      (1): ResNetBottleneck(\n", "        (conv1): Conv3d(1024, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(256, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (2): ResNetBottleneck(\n", "        (conv1): Conv3d(1024, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(256, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (3): ResNetB<PERSON>leneck(\n", "        (conv1): Conv3d(1024, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(256, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "    )\n", "    (layer3): Sequential(\n", "      (0): ResNetBottleneck(\n", "        (conv1): Conv3d(1024, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(512, 512, kernel_size=(3, 3, 3), stride=(2, 2, 2), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(512, 2048, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "        (downsample): Sequential(\n", "          (0): Conv3d(1024, 2048, kernel_size=(1, 1, 1), stride=(2, 2, 2))\n", "          (1): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        )\n", "      )\n", "      (1): ResNetBottleneck(\n", "        (conv1): Conv3d(2048, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(512, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(512, 2048, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (2): ResNetBottleneck(\n", "        (conv1): Conv3d(2048, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(512, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(512, 2048, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (3): ResNetB<PERSON>leneck(\n", "        (conv1): Conv3d(2048, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(512, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(512, 2048, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (4): ResNetB<PERSON><PERSON><PERSON>(\n", "        (conv1): Conv3d(2048, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(512, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(512, 2048, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (5): ResNetB<PERSON><PERSON><PERSON>(\n", "        (conv1): Conv3d(2048, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(512, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(512, 2048, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "    )\n", "    (layer4): Sequential(\n", "      (0): ResNetBottleneck(\n", "        (conv1): Conv3d(2048, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(1024, 1024, kernel_size=(3, 3, 3), stride=(2, 2, 2), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(1024, 4096, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(4096, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "        (downsample): Sequential(\n", "          (0): Conv3d(2048, 4096, kernel_size=(1, 1, 1), stride=(2, 2, 2))\n", "          (1): BatchNorm3d(4096, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        )\n", "      )\n", "      (1): ResNetBottleneck(\n", "        (conv1): Conv3d(4096, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(1024, 1024, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(1024, 4096, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(4096, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "      (2): ResNetBottleneck(\n", "        (conv1): Conv3d(4096, 1024, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn1): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv2): Conv3d(1024, 1024, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "        (bn2): BatchNorm3d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (conv3): Conv3d(1024, 4096, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)\n", "        (bn3): BatchNorm3d(4096, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "        (relu): ReLU(inplace=True)\n", "      )\n", "    )\n", "    (avgpool): AdaptiveAvgPool3d(output_size=(1, 1, 1))\n", "    (fc): Identity()\n", "  )\n", "  (1): Sequential(\n", "    (0): Sequential(\n", "      (0): Linear(in_features=4096, out_features=2048, bias=True)\n", "      (1): ReLU(inplace=True)\n", "    )\n", "    (1): Sequential(\n", "      (0): Linear(in_features=2048, out_features=512, bias=True)\n", "      (1): ReLU(inplace=True)\n", "    )\n", "    (2): Sequential(\n", "      (0): Linear(in_features=512, out_features=256, bias=True)\n", "      (1): ReLU(inplace=True)\n", "    )\n", "    (3): Sequential(\n", "      (0): Linear(in_features=256, out_features=2, bias=True)\n", "    )\n", "  )\n", ")"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["trunk.load_state_dict(trained_trunk)\n", "head.load_state_dict(trained_head)\n", "model = nn.Sequential(trunk, head)\n", "model.eval()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Visualize and Preprocess Dataset Images\n", "\n", "This section demonstrates how to:\n", "1. Visualize coordinate annotations on the original images\n", "2. Preprocess and crop images for input to the neural network\n", "\n", "Key steps:\n", "1. Load the CSV file containing image paths and coordinate annotations\n", "2. Visualize the original image with annotated coordinates\n", "3. Preprocess the image to create the network input\n", "\n", "Note: \n", "- Ensure your CSV file has correct paths to the dataset images\n", "- The CSV should contain columns: image_path, coordX, coordY, coordZ\n", "- You can adapt this for custom datasets by following the same format\n", "\n", "For more details on data format and preprocessing, refer to the quick start guide."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"../data/preprocessing/luna16/annotations/task2_test.csv\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["Unnamed: 0                                                  263\n", "malignancy                                                    1\n", "coordX                                               -32.199997\n", "coordY                                                 27.34375\n", "coordZ                                                   -169.5\n", "image_path    /mnt/data1/suraj/RadiomicsFoundationModel/LUNA...\n", "Name: 0, dtype: object"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df.il<PERSON>[0]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from fmcib.visualization.verify_io import visualize_seed_point\n", "\n", "visualize_seed_point(df.iloc[0])  # Visualize annotations on the 0th row."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["preprocessed = preprocess(df.iloc[0])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def plot_3d_image(image, cmap=None):\n", "    \"\"\"\n", "    Plot 3D image slices (axial, coronal, sagittal) at the mid values of each dimension.\n", "\n", "    Args:\n", "        image (numpy.ndarray): 3D or 4D image array (single or 3-channel)\n", "    \"\"\"\n", "\n", "    # Handle single channel and 3-channel images\n", "    if image.ndim == 3:\n", "        image = image[np.newax<PERSON>, ...]  # Add channel dimension\n", "    elif image.ndim != 4:\n", "        raise ValueError(\"Image must be 3D or 4D (with channels)\")\n", "\n", "    # Calculate mid values\n", "    channels, depth, height, width = image.shape\n", "    mid_z, mid_y, mid_x = depth // 2, height // 2, width // 2\n", "\n", "    # Function to plot a slice\n", "    def plot_slice(slice_data, title, position):\n", "        plt.subplot(1, 3, position)\n", "        if channels == 1:\n", "            plt.imshow(slice_data[0], cmap=cmap)\n", "        else:\n", "            plt.imshow(np.moveaxis(slice_data, 0, -1))\n", "        plt.title(title)\n", "        plt.axis(\"off\")\n", "\n", "    # Plot axial slice\n", "    plot_slice(image[:, mid_z, :, :], \"Axial\", 1)\n", "\n", "    # Plot coronal slice\n", "    plot_slice(np.flipud(image[:, :, mid_y, :]), \"Coronal\", 2)\n", "\n", "    # Plot sagittal slice\n", "    plot_slice(np.flipud(image[:, :, :, mid_x]), \"Sagittal\", 3)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "# Example usage:\n", "# plot_3d_image(image)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_3d_image(preprocessed, cmap=\"gray\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Saliency Map Generation and Visualization\n", "\n", "This section demonstrates the process of generating and visualizing saliency maps for a 3D medical image using the GuidedBackpropSmoothGrad technique.\n", "\n", "The code below implements these steps and provides functions for plotting and displaying the 3D images.\n", "\n", "Key components:\n", "- `GuidedBackpropSmoothGrad`: The saliency map generation technique\n", "- `scipy.ndimage.gaussian_filter`: For smoothing the output\n", "- `blend_images`: Custom function to overlay the activation map on the original image\n", "- `plot_3d_image`: Function to display 3D images in axial, coronal, and sagittal views\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["model = model.to(\"cuda:0\")\n", "activation_model = GuidedBackpropSmoothGrad(model)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Computing GuidedBackpropSmoothGrad:   4%|▍         | 1/25 [00:00<00:16,  1.42it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Computing GuidedBackpropSmoothGrad: 100%|██████████| 25/25 [00:01<00:00, 18.02it/s]\n", "Auto-removal of overlapping axes is deprecated since 3.6 and will be removed two minor releases later; explicitly call ax.remove() as needed.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 3000x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 3000x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 3000x1000 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Process the input data\n", "data = preprocessed.to(\"cuda\").unsqueeze(dim=0)\n", "out = activation_model(data)\n", "data = data.cpu().squeeze().detach().numpy()\n", "\n", "# Apply Gaussian smoothing to the output and normalize\n", "smooth_out = scipy.ndimage.gaussian_filter(out.cpu().squeeze().detach().numpy(), sigma=1)\n", "smooth_out = (smooth_out - smooth_out.min()) / (smooth_out.max() - smooth_out.min())\n", "\n", "# Blend the original image with the activation map\n", "blended = blend_images(\n", "    torch.from_numpy(data).unsqueeze(0),\n", "    torch.from_numpy(smooth_out).unsqueeze(0),\n", "    alpha=0.6,\n", "    cmap=\"jet\",\n", "    rescale_arrays=False,\n", "    transparent_background=False,\n", ")\n", "\n", "\n", "# Function to plot and display images\n", "def plot_and_show(image, title, cmap=None):\n", "    plt.figure(figsize=(30, 10))\n", "    plt.title(title)\n", "    plot_3d_image(image, cmap=cmap)\n", "    plt.show()\n", "\n", "\n", "# Plot blended image, original image, and activation map\n", "plot_and_show(blended.numpy(), \"Blended Image\")\n", "plot_and_show(data, \"Original Image\", cmap=\"gray\")\n", "plot_and_show(smooth_out, \"Activation Map\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "modelling", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "vscode": {"interpreter": {"hash": "7d003e26552b6296bb63e27739d6afbcbaf12ea49bed6314617b964a3b37dee0"}}}, "nbformat": 4, "nbformat_minor": 2}