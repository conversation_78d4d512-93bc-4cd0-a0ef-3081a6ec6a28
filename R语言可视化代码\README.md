# R语言火山图绘制代码集合

本目录包含了完整的R语言火山图绘制代码和示例数据，适用于RNA-seq、蛋白质组学等差异表达分析结果的可视化。

## 📁 文件结构

```
R语言可视化代码/
├── README.md                    # 本说明文件
├── 火山图绘制代码.R              # 基础火山图绘制代码
├── 示例数据生成.R               # 生成示例数据的代码
├── 使用真实数据绘制火山图.R      # 使用真实数据的完整代码
└── volcano_data/               # 生成的示例数据目录
    ├── complete_volcano_dataset.csv
    ├── simple_volcano_data.csv
    ├── significant_genes.csv
    └── README.md
```

## 🚀 快速开始

### 1. 运行示例代码（推荐新手）

```r
# 运行基础火山图代码（包含示例数据生成）
source("火山图绘制代码.R")
```

### 2. 生成示例数据

```r
# 生成符合真实分析格式的示例数据
source("示例数据生成.R")
```

### 3. 使用真实数据

```r
# 使用真实数据绘制多种风格的火山图
source("使用真实数据绘制火山图.R")
```

## 📊 数据格式要求

### 最简格式（必需列）
```csv
Gene,log2FC,p_value
TP53,2.5,0.001
BRCA1,-1.8,0.01
MYC,3.2,0.0001
```

### 完整格式（推荐）
```csv
Gene_Symbol,Log2_Fold_Change,P_Value,Adjusted_P_Value,Base_Expression
TP53,2.5,0.001,0.01,8.5
BRCA1,-1.8,0.01,0.05,7.2
MYC,3.2,0.0001,0.002,9.1
```

## 🎨 火山图类型

### 1. 基础火山图
- 简单的散点图显示
- 基本的阈值线
- 三种颜色分类（上调/下调/无显著差异）

### 2. 增强版火山图
- 基因名称标注
- 改进的视觉效果
- 统计信息显示

### 3. 科学期刊风格
- 符合期刊发表标准
- 简洁的配色方案
- 专业的排版

### 4. 高对比度版本
- 深色主题
- 高对比度配色
- 适合演示使用

## ⚙️ 自定义参数

### 阈值设置
```r
FC_THRESHOLD <- 1.0      # log2 fold change阈值
P_THRESHOLD <- 0.05      # p值阈值
TOP_GENES <- 10          # 标注的基因数量
```

### 颜色设置
```r
# 自定义颜色（上调，下调，无显著差异）
colors <- c("#E74C3C", "#3498DB", "#95A5A6")
```

### 图片参数
```r
point_size <- 1.5        # 点的大小
label_size <- 3          # 标签字体大小
```

## 📋 使用步骤

### 步骤1: 准备数据
确保您的数据包含以下列：
- 基因名称（Gene/Gene_Symbol）
- log2 fold change值
- p值

### 步骤2: 修改代码中的文件路径
```r
# 修改为您的数据文件路径
data <- read.csv("your_data_file.csv", stringsAsFactors = FALSE)
```

### 步骤3: 调整参数
根据您的需求调整阈值和视觉参数

### 步骤4: 运行代码
执行R脚本生成火山图

## 🔧 依赖包

代码会自动安装以下R包：
- `ggplot2` - 主要绘图包
- `dplyr` - 数据处理
- `ggrepel` - 标签避免重叠
- `RColorBrewer` - 颜色方案
- `scales` - 坐标轴格式化
- `gridExtra` - 多图组合
- `viridis` - 颜色方案

## 📈 输出文件

运行代码后会生成：

### 图片文件
- `basic_volcano_plot.png` - 基础火山图
- `enhanced_volcano_plot.png` - 增强版火山图
- `custom_volcano_plot.png` - 自定义风格火山图
- `combined_volcano.png` - 组合图

### 数据文件
- `volcano_data.csv` - 处理后的数据
- `significant_genes.csv` - 显著差异基因列表

## 🎯 应用场景

1. **RNA-seq差异表达分析**
   - 基因表达量比较
   - 转录组学研究

2. **蛋白质组学分析**
   - 蛋白质丰度比较
   - 蛋白质组学研究

3. **代谢组学分析**
   - 代谢物浓度比较
   - 代谢通路分析

4. **其他组学数据**
   - 任何涉及fold change和p值的比较分析

## 💡 使用技巧

### 1. 基因标注优化
```r
# 基于p值选择最显著基因
select_top_genes_by_pvalue(data, n_genes = 5)

# 基于综合评分选择基因
select_top_genes_combined(data, n_genes = 5)
```

### 2. 颜色主题选择
```r
# 经典红蓝配色
colors <- c("#E74C3C", "#3498DB", "#95A5A6")

# 科学期刊配色
colors <- c("#D32F2F", "#1976D2", "#757575")

# 高对比度配色
colors <- c("#FF4444", "#00AA44", "#888888")
```

### 3. 图片质量设置
```r
# 高质量输出
ggsave("volcano_plot.png", plot, 
       width = 12, height = 10, dpi = 300)

# 期刊质量输出
ggsave("volcano_plot.pdf", plot, 
       width = 8, height = 6, device = "pdf")
```

## ❓ 常见问题

### Q1: 如何处理p值为0的情况？
```r
# 将p值0替换为极小值
data$p_value[data$p_value == 0] <- 1e-300
```

### Q2: 如何调整基因标签避免重叠？
```r
# 使用ggrepel包的参数
geom_text_repel(
  box.padding = 0.5,      # 增加标签框间距
  point.padding = 0.3,    # 增加点标签间距
  max.overlaps = 20       # 允许的最大重叠数
)
```

### Q3: 如何自定义阈值线？
```r
# 添加自定义阈值线
geom_vline(xintercept = c(-2, 2), linetype = "dashed") +
geom_hline(yintercept = -log10(0.01), linetype = "dashed")
```

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：
1. R包是否正确安装
2. 数据格式是否符合要求
3. 文件路径是否正确
4. R版本是否兼容（推荐R >= 4.0）

## 📄 许可证

本代码集合采用MIT许可证，可自由使用和修改。

---

**最后更新**: 2024年
**作者**: AI助手
**版本**: 1.0
