name: Run basic tests on all OS
on:
  push:
    paths-ignore:
      - '**.md'
jobs:

  run-tests:
    strategy:
      matrix:
        # os: [ubuntu-latest, windows-latest, macos-latest]  # fails on windows until https://github.com/MIC-DKFZ/nnUNet/issues/2396 is resolved
        os: [ubuntu-latest, macos-latest]
        python-version: ["3.10"]
    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies on Ubuntu
      if: runner.os == 'Linux'
      run: |
        python -m pip install --upgrade pip
        pip install pytest Cython fury
        pip install torch==2.4.0 -f https://download.pytorch.org/whl/cpu
        pip install .

    - name: Install dependencies on Windows / MacOS
      if: runner.os == 'Windows' || runner.os == 'macOS'
      run: |
        python -m pip install --upgrade pip
        pip install pytest Cython
        pip install torch==2.4.0
        pip install .

    - name: Run test script
      run: python tests/tests_os.py
