{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Y57RMM1LEQmR"}, "source": ["#  <span style=\"color:orange\">二元分类教程 (CLF101) - 初级</span>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "GM-nQ7LqEQma"}, "source": ["**使用版本: PyCaret 2.2** <br />\n", "**更新时间: 2020年11月11**\n", "\n", "# 1.0 教程简介\n", "欢迎阅读二元分类的初级教程**(CLF101)**. 本教程使用`pycaret.classification`模块来介绍二元分类的基本操作，面向的读者是PyCaret的新手。\n", "\n", "在本教程中你会学到:\n", "\n", "\n", "* **获取数据:** 如何从PyCaret中获取数据？\n", "* **环境搭建:** 如何搭建二元分类所需的必要环境？\n", "* **创建模型:**  如何创建模型、执行分层交叉验证和评估分类指标？\n", "* **调参数:**  如何自动调超参数？\n", "* **模型性能图:**  如何使用多种图像来分析模型的性能？\n", "* **最终模型:** 如何选择最佳的最终模型？\n", "* **预测:** 如何用训练好的模型来预测新的数据集？\n", "* **保存/加载模型:**  如何保存/加载模型以便之后使用？\n", "\n", "阅读时间 : 大约30分钟\n", "\n", "\n", "# 1.1 安装 PyCaret\n", "安装PyCaret一般只需要几分钟。请按照以下说明进行操作。\n", "\n", "# 在本地 Jupyter Notebook 安装 PyCaret\n", "`pip install pycaret`  <br />\n", "\n", "# 在 Google Colab 或者 Azure Notebooks 安装 PyCaret\n", "`!pip install pycaret`\n", "\n", "\n", "# 1.2 预先要求\n", "- Python 3.6 或更高\n", "- PyCaret 2.0 或更高\n", "- 网络链接来获取PyCaret中的数据\n", "- 异常检测的基础知识\n", "\n", "# 1.3 Google Colab 用户:\n", "如何你在 Google Colab跑这个Notebook, 请运行以下代码来显示交互式的图像。<br/>\n", "<br/>\n", "`from pycaret.utils import enable_colab` <br/>\n", "`enable_colab()`\n", "\n", "# 1.4 其他阅读资料:\n", "- __[Binary Classification Tutorial (CLF102) - Intermediate Level](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__\n", "- __[Binary Classification Tutorial (CLF103) - Expert Level](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Expert%20-%20CLF103.ipynb)__"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "2DJaOwC_EQme"}, "source": ["# 2.0 什么是二元分类？\n", "二元分类是一种有监督的机器学习技术，其目标是预测离散和无序的分类标签，例如通过/失败、正/负、默认/非默认等。下面列出了一些现实生活中的例子：\n", "\n", "- 用于确定患者是否患有某种疾病的医学测试 - 疾病是否存在。\n", "- 工厂中的“通过或失败”测试方法或质量控制，即确定是否满足规范 - 通过/不通过。\n", "- 信息检索，即决定一个页面或一篇文章是否应该在搜索的结果集中 - 分类属性是文章的相关性，或者对用户的有用性。\n", "\n", "__[更多资料](https://medium.com/@categitau/in-one-of-my-previous-posts-i-introduced-machine-learning-and-talked-about-the-two-most-common-c1ac6e18df16)__"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XC3kSuueEQmh"}, "source": ["# 3.0 PyCaret的分类模块概述\n", "PyCaret 的分类模块（`pycaret.classification`）是一个有监督的机器学习模块，用于根据各种技术和算法将元素分类为二进制组。分类问题的一些常见用例包括预测客户违约（是或否）、客户流失（客户将离开或留下）、发现疾病（正面或负面）。\n", "\n", "\n", "PyCaret 分类模块可用于二分类或多分类问题。它有超过 18 种算法和 14 个图来分析模型的性能。无论是超参数调整、集成还是堆叠等高级技术，PyCaret 的分类模块都应有尽有。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "aAKRo-EbEQml"}, "source": ["# 4.0 教程数据集"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "VLKxlFjrEQmq"}, "source": ["在本教程中，我们将使用来自 UCI 的数据集，称为**信用卡客户数据集的默认值**。该数据集包含 2005 年 4 月至 2005 年 9 月期间台湾信用卡客户的违约付款、人口统计因素、信用数据、付款历史和帐单等信息。有 24,000 个样本和 25 个特征。每列的简短描述如下：\n", "\n", "- **ID:** 每个客户的 ID\n", "- **LIMIT_BAL:** 以新台币计的信贷金额（包括个人和家庭/补充信贷）\n", "- **SEX:** 性别（1=男性，2=女性）\n", "- **EDUCATION:** （1=研究生院，2=大学，3=高中，4=其他，5=未知，6=未知）\n", "- **MARRIAGE:** 婚姻状况（1=已婚，2=单身，3=其他）\n", "- **AGE:** 年龄\n", "- **PAY_0 to PAY_6:** n 个月前的还款状态（PAY_0 = 上个月 ... PAY_6 = 6 个月前）（标签：-1=按时付款，1=延迟付款一个月，2=延迟付款两个月，... 8=付款延迟八个月，9=付款延迟九个月及以上）\n", "- **BILL_AMT1 to BILL_AMT6:** n 个月前的账单金额（BILL_AMT1 = last_month .. BILL_AMT6 = 6 个月前）\n", "- **PAY_AMT1 to PAY_AMT6:** n 个月前的付款金额（BILL_AMT1 = last_month .. BILL_AMT6 = 6 个月前）\n", "- **default:** 默认付款（1=是，0=否） `Target Column`\n", "\n", "# 数据集作者与联系方式:\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013). UCI Machine Learning Repository. Irvine, CA: University of California, School of Information and Computer Science.\n", "\n", " __[原始数据集链接](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)__ "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Ui_rALqYEQmv"}, "source": ["# 5.0 获取数据"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "BfqIMeJNEQmz"}, "source": ["你可以选择下载数据 （__[下载链接](https://archive.ics.uci.edu/ml/datasets/Mice+Protein+Expression)__），并用pandas来加载 __[(pandas教程)](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html)__。 或者你可以使用 PyCaret的`get_data()` 函数来获取数据 （需要网络链接）。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211}, "colab_type": "code", "id": "lUvE187JEQm3", "outputId": "8741262c-0e33-4ec0-b54d-3c8fb41e52c0"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>689.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331.0</td>\n", "      <td>14948.0</td>\n", "      <td>15549.0</td>\n", "      <td>1518.0</td>\n", "      <td>1500.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>5000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314.0</td>\n", "      <td>28959.0</td>\n", "      <td>29547.0</td>\n", "      <td>2000.0</td>\n", "      <td>2019.0</td>\n", "      <td>1200.0</td>\n", "      <td>1100.0</td>\n", "      <td>1069.0</td>\n", "      <td>1000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940.0</td>\n", "      <td>19146.0</td>\n", "      <td>19131.0</td>\n", "      <td>2000.0</td>\n", "      <td>36681.0</td>\n", "      <td>10000.0</td>\n", "      <td>9000.0</td>\n", "      <td>689.0</td>\n", "      <td>679.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>19394.0</td>\n", "      <td>19619.0</td>\n", "      <td>20024.0</td>\n", "      <td>2500.0</td>\n", "      <td>1815.0</td>\n", "      <td>657.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>800.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0      20000    2          2         1   24      2      2     -1     -1   \n", "1      90000    2          2         2   34      0      0      0      0   \n", "2      50000    2          2         1   37      0      0      0      0   \n", "3      50000    1          2         1   57     -1      0     -1      0   \n", "4      50000    1          1         2   37      0      0      0      0   \n", "\n", "   PAY_5  ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0     -2  ...        0.0        0.0        0.0       0.0     689.0       0.0   \n", "1      0  ...    14331.0    14948.0    15549.0    1518.0    1500.0    1000.0   \n", "2      0  ...    28314.0    28959.0    29547.0    2000.0    2019.0    1200.0   \n", "3      0  ...    20940.0    19146.0    19131.0    2000.0   36681.0   10000.0   \n", "4      0  ...    19394.0    19619.0    20024.0    2500.0    1815.0     657.0   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  default  \n", "0       0.0       0.0       0.0        1  \n", "1    1000.0    1000.0    5000.0        0  \n", "2    1100.0    1069.0    1000.0        0  \n", "3    9000.0     689.0     679.0        0  \n", "4    1000.0    1000.0     800.0        0  \n", "\n", "[5 rows x 24 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pycaret.datasets import get_data\n", "dataset = get_data('credit')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 33}, "colab_type": "code", "id": "kMqDGBkJEQnN", "outputId": "b2015b7a-4c1a-4377-d9cf-3e9ac5ce3ea2"}, "outputs": [{"data": {"text/plain": ["(24000, 24)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看数据的形状\n", "dataset.shape"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "LyGFryEhEQne"}, "source": ["为了用看不见的数据来演示`predict_model()` 函数，我们在原始数据集中保留了 1200 条记录的样本以用于预测。这不应与训练/测试拆分混淆，因为执行此特定拆分是为了模拟现实生活场景。另一种思考方式是，在执行机器学习实验时，这 1200 条记录不可用。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "hXmaL1xFEQnj", "outputId": "f1f62a7d-5d3d-4832-ee00-a4d20ee39c41"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data for Modeling: (22800, 24)\n", "Unseen Data For Predictions: (1200, 24)\n"]}], "source": ["data = dataset.sample(frac=0.95, random_state=786)\n", "data_unseen = dataset.drop(data.index)\n", "data.reset_index(inplace=True, drop=True)\n", "data_unseen.reset_index(inplace=True, drop=True)\n", "print('Data for Modeling: ' + str(data.shape))\n", "print('Unseen Data For Predictions: ' + str(data_unseen.shape))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "y9s9wNcjEQn0"}, "source": ["# 6.0 配置 PyCaret 环境"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZlA01j6NEQn7"}, "source": ["`setup()` 函数在 PyCaret 中初始化环境并创建转换管道以准备数据并进行建模和部署。 `setup()` 必须在执行 PyCaret 中的任何其他函数之前调用。它需要两个强制性参数：pandas dataframe，和目标列的名称。所有其他参数都是可选的，用于自定义预处理管道（我们将在后面的教程中看到它们）。\n", "\n", "当 `setup()` 被执行时，PyCaret 的推理算法会根据某些属性自动推断所有特征的数据类型。通常PyCaret会正确地推断数据类型，但情况并非总是如此。为了解决这个问题，PyCaret 会在执行 setup() 后显示一个包含特征及其推断数据类型的表。如果所有数据类型都被正确识别，可以按“enter”继续，或者可以输入“quit”结束实验。确保数据类型正确在 PyCaret 中至关重要，因为它会自动执行一些预处理任务，这对于任何机器学习实验都是必不可少的。对于每种数据类型，这些任务的执行方式不同，这意味着正确配置它们非常重要。\n", "\n", "在后面的教程中，我们将学习如何使用 `setup()` 中的 `numeric_features` 和 `categorical_features` 参数覆盖 PyCaret 的推断数据类型。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "BOmRR0deEQoA"}, "outputs": [], "source": ["from pycaret.classification import *"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 803}, "colab_type": "code", "id": "k2IuvfDHEQoO", "outputId": "c7754ae9-b060-4218-b6f0-de65a815aa3a", "scrolled": false}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "</style><table id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Description</th>        <th class=\"col_heading level0 col1\" >Value</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow0_col0\" class=\"data row0 col0\" >session_id</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow0_col1\" class=\"data row0 col1\" >123</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow1_col0\" class=\"data row1 col0\" >Target</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow1_col1\" class=\"data row1 col1\" >default</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow2_col0\" class=\"data row2 col0\" >Target Type</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow2_col1\" class=\"data row2 col1\" >Binary</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow3_col0\" class=\"data row3 col0\" >Label Encoded</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow3_col1\" class=\"data row3 col1\" >0: 0, 1: 1</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow4_col0\" class=\"data row4 col0\" >Original Data</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow4_col1\" class=\"data row4 col1\" >(22800, 24)</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow5_col0\" class=\"data row5 col0\" >Missing Values</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow5_col1\" class=\"data row5 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow6_col0\" class=\"data row6 col0\" >Numeric Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow6_col1\" class=\"data row6 col1\" >14</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow7_col0\" class=\"data row7 col0\" >Categorical Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow7_col1\" class=\"data row7 col1\" >9</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow8_col0\" class=\"data row8 col0\" >Ordinal Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow8_col1\" class=\"data row8 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow9_col0\" class=\"data row9 col0\" >High Cardinality Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow9_col1\" class=\"data row9 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow10_col0\" class=\"data row10 col0\" >High Cardinality Method</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow10_col1\" class=\"data row10 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow11_col0\" class=\"data row11 col0\" >Transformed Train Set</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow11_col1\" class=\"data row11 col1\" >(15959, 88)</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow12_col0\" class=\"data row12 col0\" >Transformed Test Set</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow12_col1\" class=\"data row12 col1\" >(6841, 88)</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow13_col0\" class=\"data row13 col0\" >Shuffle Train-Test</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow13_col1\" class=\"data row13 col1\" >True</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow14_col0\" class=\"data row14 col0\" >Stratify Train-Test</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow14_col1\" class=\"data row14 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow15_col0\" class=\"data row15 col0\" >Fold Generator</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow15_col1\" class=\"data row15 col1\" >StratifiedKFold</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow16_col0\" class=\"data row16 col0\" >Fold Number</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow16_col1\" class=\"data row16 col1\" >10</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow17_col0\" class=\"data row17 col0\" >CPU Jobs</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow17_col1\" class=\"data row17 col1\" >-1</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow18_col0\" class=\"data row18 col0\" >Use GPU</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow18_col1\" class=\"data row18 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow19_col0\" class=\"data row19 col0\" >Log Experiment</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow19_col1\" class=\"data row19 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow20_col0\" class=\"data row20 col0\" >Experiment Name</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow20_col1\" class=\"data row20 col1\" >clf-default-name</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow21_col0\" class=\"data row21 col0\" >USI</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow21_col1\" class=\"data row21 col1\" >877b</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow22_col0\" class=\"data row22 col0\" >Imputation Type</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow22_col1\" class=\"data row22 col1\" >simple</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow23_col0\" class=\"data row23 col0\" >Iterative Imputation Iteration</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow23_col1\" class=\"data row23 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow24_col0\" class=\"data row24 col0\" >Numeric Imputer</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow24_col1\" class=\"data row24 col1\" >mean</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow25_col0\" class=\"data row25 col0\" >Iterative Imputation Numeric Model</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow25_col1\" class=\"data row25 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow26_col0\" class=\"data row26 col0\" >Categorical Imputer</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow26_col1\" class=\"data row26 col1\" >constant</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow27_col0\" class=\"data row27 col0\" >Iterative Imputation Categorical Model</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow27_col1\" class=\"data row27 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow28_col0\" class=\"data row28 col0\" >Unknown Categoricals Handling</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow28_col1\" class=\"data row28 col1\" >least_frequent</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow29_col0\" class=\"data row29 col0\" >Normalize</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow29_col1\" class=\"data row29 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow30_col0\" class=\"data row30 col0\" >Normalize Method</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow30_col1\" class=\"data row30 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow31_col0\" class=\"data row31 col0\" >Transformation</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow31_col1\" class=\"data row31 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow32_col0\" class=\"data row32 col0\" >Transformation Method</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow32_col1\" class=\"data row32 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow33_col0\" class=\"data row33 col0\" >PCA</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow33_col1\" class=\"data row33 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow34_col0\" class=\"data row34 col0\" >PCA Method</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow34_col1\" class=\"data row34 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow35_col0\" class=\"data row35 col0\" >PCA Components</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow35_col1\" class=\"data row35 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow36_col0\" class=\"data row36 col0\" >Ignore Low Variance</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow36_col1\" class=\"data row36 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow37_col0\" class=\"data row37 col0\" >Combine Rare Levels</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow37_col1\" class=\"data row37 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow38_col0\" class=\"data row38 col0\" >Rare Level Threshold</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow38_col1\" class=\"data row38 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow39_col0\" class=\"data row39 col0\" >Numeric Binning</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow39_col1\" class=\"data row39 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow40_col0\" class=\"data row40 col0\" >Remove Outliers</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow40_col1\" class=\"data row40 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow41_col0\" class=\"data row41 col0\" >Outliers Threshold</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow41_col1\" class=\"data row41 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow42_col0\" class=\"data row42 col0\" >Remove Multicollinearity</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow42_col1\" class=\"data row42 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row43\" class=\"row_heading level0 row43\" >43</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow43_col0\" class=\"data row43 col0\" >Multicollinearity Threshold</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow43_col1\" class=\"data row43 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row44\" class=\"row_heading level0 row44\" >44</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow44_col0\" class=\"data row44 col0\" >Clustering</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow44_col1\" class=\"data row44 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row45\" class=\"row_heading level0 row45\" >45</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow45_col0\" class=\"data row45 col0\" >Clustering Iteration</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow45_col1\" class=\"data row45 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row46\" class=\"row_heading level0 row46\" >46</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow46_col0\" class=\"data row46 col0\" >Polynomial Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow46_col1\" class=\"data row46 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row47\" class=\"row_heading level0 row47\" >47</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow47_col0\" class=\"data row47 col0\" >Polynomial Degree</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow47_col1\" class=\"data row47 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row48\" class=\"row_heading level0 row48\" >48</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow48_col0\" class=\"data row48 col0\" >Trignometry Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow48_col1\" class=\"data row48 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row49\" class=\"row_heading level0 row49\" >49</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow49_col0\" class=\"data row49 col0\" >Polynomial Threshold</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow49_col1\" class=\"data row49 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row50\" class=\"row_heading level0 row50\" >50</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow50_col0\" class=\"data row50 col0\" >Group Features</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow50_col1\" class=\"data row50 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row51\" class=\"row_heading level0 row51\" >51</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow51_col0\" class=\"data row51 col0\" >Feature Selection</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow51_col1\" class=\"data row51 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row52\" class=\"row_heading level0 row52\" >52</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow52_col0\" class=\"data row52 col0\" >Features Selection Threshold</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow52_col1\" class=\"data row52 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row53\" class=\"row_heading level0 row53\" >53</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow53_col0\" class=\"data row53 col0\" >Feature Interaction</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow53_col1\" class=\"data row53 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row54\" class=\"row_heading level0 row54\" >54</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow54_col0\" class=\"data row54 col0\" >Feature Ratio</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow54_col1\" class=\"data row54 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row55\" class=\"row_heading level0 row55\" >55</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow55_col0\" class=\"data row55 col0\" >Interaction Threshold</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow55_col1\" class=\"data row55 col1\" >None</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row56\" class=\"row_heading level0 row56\" >56</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow56_col0\" class=\"data row56 col0\" >Fix Imbalance</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow56_col1\" class=\"data row56 col1\" >False</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83dalevel0_row57\" class=\"row_heading level0 row57\" >57</th>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow57_col0\" class=\"data row57 col0\" >Fix Imbalance Method</td>\n", "                        <td id=\"T_a0294fda_2b73_11eb_8b7f_482ae32b83darow57_col1\" class=\"data row57 col1\" >SMOTE</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5dd3aa88>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["exp_clf101 = setup(data = data, target = 'default', session_id=123) "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "JJSOhIOxEQoY"}, "source": ["成功执行设置后，它会打印包含几条重要信息的信息网格。大部分信息与执行 setup() 时构建的预处理管道有关。这些功能中的大部分都超出了本教程的范围，但是在此阶段需要注意的一些重要事项包括：\n", "\n", "- **session_id:**  一个自定义的数字作为种子，这在之后用于再现结果。如果`session_id`没有被传入，一个随机数会被生成。在这次试验中，我们使用`123`作为种子。<br/>\n", "<br/>\n", "- **Target Type:**  二进制或多类。目标类型会自动检测并显示。对于二元或多类问题，实验的执行方式没有区别。所有功能都是相同的。<br/>\n", "<br/>\n", "- **Label Encoded :**  当目标变量是字符串类型（即“是”或“否”）而不是 1 或 0 时，它会自动将标签编码为 1 和 0，并显示映射（0：否，1：是）以供参考。在这个实验中，不需要标签编码，因为目标变量是数字类型的。 <br/>\n", "<br/>\n", "- **Original Data :**  显示数据集的原始形状。在这个实验中 (22800, 24) 意味着 22,800 个样本和 24 个特征（包括目标列）。 <br/>\n", "<br/>\n", "- **Missing Values 缺失值:**  当原数据中存在缺失值时，该项会显示`True`，PyCaret会自动使用`mean`来填充数字，`constant`来填充类别。这些填充方法都可以使用 `numeric_imputation`和`categorical_imputation`参数在`setup()`中设置。 <br/>\n", "<br/>\n", "- **Numeric Features :**  推断为数字的特征数量。在这个数据集中，24 个特征中有 14 个被推断为数字。 <br/>\n", "<br/>\n", "- **Categorical Features :**  推断为分类的特征数量。在这个数据集中，24 个特征中有 9 个被推断为分类特征。<br/>\n", "<br/>\n", "- **Transformed Train Set :**  显示转换后的训练集的形状。请注意，对于变换后的训练集，(22800, 24) 的原始形状被转换为 (15959, 91)，并且由于分类编码，特征数量从 24 个增加到 91 个 <br/>\n", "<br/>\n", "- **Transformed Test Set :**  显示转换后的测试/保持集的形状。测试/保留集中有 6841 个样本。此拆分基于默认值 70/30，可以使用 setup 中的 `train_size` 参数进行更改。 <br/>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "it_nJo1IEQob"}, "source": ["# 7.0 模型比较"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "apb_B9bBEQof"}, "source": ["比较所有模型以评估性能是在设置完成后建模的起点（除非你确切知道需要哪种模型，但通常情况并非如此）。此函数训练模型库中的所有模型，并使用分层交叉验证对它们进行评分以进行度量评估。输出打印一个分数网格，显示平均准确率、AUC、召回率、精度、F1、Kappa 和 MCC 跨折叠（默认为 10）以及训练时间。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {}, "colab_type": "code", "id": "AsG0b1NIEQoj", "outputId": "a6e3a510-45a1-4782-8ffe-0ec138a64eed", "scrolled": false}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83da th {\n", "          text-align: left;\n", "    }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            background-color:  yellow;\n", "            background-color:  lightgrey;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col0 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col1 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col2 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col3 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col4 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col5 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col6 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col7 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "        }    #T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col8 {\n", "            text-align:  left;\n", "            text-align:  left;\n", "            : ;\n", "            background-color:  lightgrey;\n", "        }</style><table id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Model</th>        <th class=\"col_heading level0 col1\" >Accuracy</th>        <th class=\"col_heading level0 col2\" >AUC</th>        <th class=\"col_heading level0 col3\" >Recall</th>        <th class=\"col_heading level0 col4\" >Prec.</th>        <th class=\"col_heading level0 col5\" >F1</th>        <th class=\"col_heading level0 col6\" >Kappa</th>        <th class=\"col_heading level0 col7\" >MCC</th>        <th class=\"col_heading level0 col8\" >TT (Sec)</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >ridge</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col0\" class=\"data row0 col0\" >Ridge Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.8254</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.0000</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.3637</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.6913</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.4764</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.3836</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col7\" class=\"data row0 col7\" >0.4122</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow0_col8\" class=\"data row0 col8\" >0.0490</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >lda</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col0\" class=\"data row1 col0\" >Linear Discriminant Analysis</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.8247</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.7634</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.3755</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.6794</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.4835</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.3884</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col7\" class=\"data row1 col7\" >0.4132</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow1_col8\" class=\"data row1 col8\" >0.1880</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >gbc</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col0\" class=\"data row2 col0\" >Gradient Boosting Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.8225</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.7790</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.3548</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.6800</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.4661</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.3721</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col7\" class=\"data row2 col7\" >0.4005</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow2_col8\" class=\"data row2 col8\" >2.0260</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >ada</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col0\" class=\"data row3 col0\" >Ada Boost Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.8221</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.7697</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.3505</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.6811</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.4626</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.3690</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col7\" class=\"data row3 col7\" >0.3983</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow3_col8\" class=\"data row3 col8\" >0.4990</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >lightgbm</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col0\" class=\"data row4 col0\" >Light Gradient Boosting Machine</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.8220</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.7759</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.3591</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.6745</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.4685</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.3734</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col7\" class=\"data row4 col7\" >0.4003</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow4_col8\" class=\"data row4 col8\" >0.2180</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >rf</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col0\" class=\"data row5 col0\" >Random Forest Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.8180</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.7618</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.3591</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.6531</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.4631</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.3645</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col7\" class=\"data row5 col7\" >0.3884</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow5_col8\" class=\"data row5 col8\" >0.8030</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >xgboost</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col0\" class=\"data row6 col0\" >Extreme Gradient Boosting</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.8160</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.7561</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.3629</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.6391</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.4626</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.3617</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col7\" class=\"data row6 col7\" >0.3829</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow6_col8\" class=\"data row6 col8\" >2.0060</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >et</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col0\" class=\"data row7 col0\" >Extra Trees Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.8082</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.7381</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.3669</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.6010</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.4553</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.3471</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col7\" class=\"data row7 col7\" >0.3629</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow7_col8\" class=\"data row7 col8\" >0.8700</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >lr</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col0\" class=\"data row8 col0\" >Logistic Regression</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.7814</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.6410</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.0003</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.1000</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.0006</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.0003</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col7\" class=\"data row8 col7\" >0.0034</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow8_col8\" class=\"data row8 col8\" >0.7680</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >knn</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col0\" class=\"data row9 col0\" >K Neighbors Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.7547</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.5939</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.1763</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.3719</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.2388</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.1145</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col7\" class=\"data row9 col7\" >0.1259</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow9_col8\" class=\"data row9 col8\" >0.3000</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >svm</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col0\" class=\"data row10 col0\" >SVM - Linear Kernel</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.7285</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.0000</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.1003</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.1454</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.0957</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.0067</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col7\" class=\"data row10 col7\" >0.0075</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow10_col8\" class=\"data row10 col8\" >0.1610</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >dt</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col0\" class=\"data row11 col0\" >Decision Tree Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.7262</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.6134</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.4127</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.3832</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.3970</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.2204</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col7\" class=\"data row11 col7\" >0.2208</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow11_col8\" class=\"data row11 col8\" >0.1140</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row12\" class=\"row_heading level0 row12\" >qda</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col0\" class=\"data row12 col0\" >Quadratic Discriminant Analysis</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col1\" class=\"data row12 col1\" >0.5171</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col2\" class=\"data row12 col2\" >0.5572</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col3\" class=\"data row12 col3\" >0.6286</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col4\" class=\"data row12 col4\" >0.2731</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col5\" class=\"data row12 col5\" >0.3675</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col6\" class=\"data row12 col6\" >0.0892</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col7\" class=\"data row12 col7\" >0.1014</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow12_col8\" class=\"data row12 col8\" >0.1480</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row13\" class=\"row_heading level0 row13\" >nb</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col0\" class=\"data row13 col0\" >Na<PERSON></td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col1\" class=\"data row13 col1\" >0.3760</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col2\" class=\"data row13 col2\" >0.6442</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col3\" class=\"data row13 col3\" >0.8845</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col4\" class=\"data row13 col4\" >0.2441</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col5\" class=\"data row13 col5\" >0.3826</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col6\" class=\"data row13 col6\" >0.0608</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col7\" class=\"data row13 col7\" >0.1207</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow13_col8\" class=\"data row13 col8\" >0.0290</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83dalevel0_row14\" class=\"row_heading level0 row14\" >catboost</th>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col0\" class=\"data row14 col0\" >CatBoost Classifier</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col1\" class=\"data row14 col1\" >0.2453</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col2\" class=\"data row14 col2\" >0.2303</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col3\" class=\"data row14 col3\" >0.1064</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col4\" class=\"data row14 col4\" >0.1957</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col5\" class=\"data row14 col5\" >0.1378</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col6\" class=\"data row14 col6\" >0.1083</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col7\" class=\"data row14 col7\" >0.1156</td>\n", "                        <td id=\"T_f29b17c0_2b73_11eb_8cf9_482ae32b83darow14_col8\" class=\"data row14 col8\" >5.0130</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5ba62208>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["best_model = compare_models()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nZAUhQGLEQoz"}, "source": ["两个简单的代码词***（甚至不是一行）***已经使用交叉验证训练和评估了超过 15 个模型。上面打印的分数网格突出显示了性能最高的指标，仅用于比较目的。默认情况下，网格使用 'Accuracy'（从最高到最低）排序，可以通过传递 `sort` 参数来更改。例如，`compare_models(sort = 'Recall')` 将按 Recall 而不是 Accuracy 对网格进行排序。如果要将 fold 参数从默认值 `10` 更改为不同的值，则可以使用 `fold` 参数。例如 `compare_models(fold = 5)` 将在 5 折交叉验证上比较所有模型。减少折叠次数将改善训练时间。默认情况下，`compare_models` 根据默认排序顺序返回性能最佳的模型，但可用于通过使用 `n_select` 参数返回前 N 个模型的列表。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RidgeClassifier(alpha=1.0, class_weight=None, copy_X=True, fit_intercept=True,\n", "                max_iter=None, normalize=False, random_state=123, solver='auto',\n", "                tol=0.001)\n"]}], "source": ["print(best_model)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "P5m2pciOEQo4"}, "source": ["# 8.0 创建模型"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "u_6cIilfEQo7"}, "source": ["`create_model` 是 PyCaret 中最精细的函数，通常是大多数 PyCaret 功能背后的基础。顾名思义，此函数使用可以使用“折叠”参数设置的交叉验证来训练和评估模型。输出打印一个分数网格，按折叠显示 Accuracy、AUC、Recall、Precision、F1、Kappa 和 MCC。\n", "\n", "对于本教程的剩余部分，我们将使用以下模型作为我们的候选模型。这些选择仅用于说明目的，并不一定意味着它们是此类数据的最佳表现或理想选择。\n", "\n", "- Decision Tree Classifier ('dt')\n", "- K Neighbors Classifier ('knn')\n", "- Random Forest Classifier ('rf')\n", "\n", "PyCaret 的模型库中有 18 个分类器可用。要查看所有分类器的列表，请检查 `docstring` 或使用 `models` 函数查看库。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Reference</th>\n", "      <th>Turbo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lr</th>\n", "      <td>Logistic Regression</td>\n", "      <td>sklearn.linear_model._logistic.LogisticRegression</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>knn</th>\n", "      <td>K Neighbors Classifier</td>\n", "      <td>sklearn.neighbors._classification.KNeighborsCl...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>nb</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>sklearn.naive_bayes.GaussianNB</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <td>Decision Tree Classifier</td>\n", "      <td>sklearn.tree._classes.DecisionTreeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>svm</th>\n", "      <td>SVM - Linear Kernel</td>\n", "      <td>sklearn.linear_model._stochastic_gradient.SGDC...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rbfsvm</th>\n", "      <td>SVM - Radial <PERSON></td>\n", "      <td>sklearn.svm._classes.SVC</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gpc</th>\n", "      <td>Gaussian Process Classifier</td>\n", "      <td>sklearn.gaussian_process._gpc.GaussianProcessC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mlp</th>\n", "      <td>MLP Classifier</td>\n", "      <td>pycaret.internal.tunable.TunableMLPClassifier</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ridge</th>\n", "      <td>Ridge Classifier</td>\n", "      <td>sklearn.linear_model._ridge.RidgeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rf</th>\n", "      <td>Random Forest Classifier</td>\n", "      <td>sklearn.ensemble._forest.RandomForestClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>qda</th>\n", "      <td>Quadratic Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.QuadraticDiscrim...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ada</th>\n", "      <td>Ada Boost Classifier</td>\n", "      <td>sklearn.ensemble._weight_boosting.AdaBoostClas...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gbc</th>\n", "      <td>Gradient Boosting Classifier</td>\n", "      <td>sklearn.ensemble._gb.GradientBoostingClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lda</th>\n", "      <td>Linear Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.LinearDiscrimina...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>et</th>\n", "      <td>Extra Trees Classifier</td>\n", "      <td>sklearn.ensemble._forest.ExtraTreesClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>xgboost</th>\n", "      <td>Extreme Gradient Boosting</td>\n", "      <td>xgboost.sklearn.XGBClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lightgbm</th>\n", "      <td>Light Gradient Boosting Machine</td>\n", "      <td>lightgbm.sklearn.LGBMClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>catboost</th>\n", "      <td>CatBoost Classifier</td>\n", "      <td>catboost.core.CatBoostClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     Name  \\\n", "ID                                          \n", "lr                    Logistic Regression   \n", "knn                K Neighbors Classifier   \n", "nb                            <PERSON><PERSON>   \n", "dt               Decision Tree Classifier   \n", "svm                   SVM - Linear Kernel   \n", "rbfsvm                SVM - Radial Kernel   \n", "gpc           Gaussian Process Classifier   \n", "mlp                        MLP Classifier   \n", "ridge                    Ridge Classifier   \n", "rf               Random Forest Classifier   \n", "qda       Quadratic Discriminant Analysis   \n", "ada                  Ada Boost Classifier   \n", "gbc          Gradient Boosting Classifier   \n", "lda          Linear Discriminant Analysis   \n", "et                 Extra Trees Classifier   \n", "xgboost         Extreme Gradient Boosting   \n", "lightgbm  Light Gradient Boosting Machine   \n", "catboost              CatBoost Classifier   \n", "\n", "                                                  Reference  Turbo  \n", "ID                                                                  \n", "lr        sklearn.linear_model._logistic.LogisticRegression   True  \n", "knn       sklearn.neighbors._classification.KNeighborsCl...   True  \n", "nb                           sklearn.naive_bayes.GaussianNB   True  \n", "dt             sklearn.tree._classes.DecisionTreeClassifier   True  \n", "svm       sklearn.linear_model._stochastic_gradient.SGDC...   True  \n", "rbfsvm                             sklearn.svm._classes.SVC  False  \n", "gpc       sklearn.gaussian_process._gpc.GaussianProcessC...  False  \n", "mlp           pycaret.internal.tunable.TunableMLPClassifier  False  \n", "ridge           sklearn.linear_model._ridge.RidgeClassifier   True  \n", "rf          sklearn.ensemble._forest.RandomForestClassifier   True  \n", "qda       sklearn.discriminant_analysis.QuadraticDiscrim...   True  \n", "ada       sklearn.ensemble._weight_boosting.AdaBoostClas...   True  \n", "gbc         sklearn.ensemble._gb.GradientBoostingClassifier   True  \n", "lda       sklearn.discriminant_analysis.LinearDiscrimina...   True  \n", "et            sklearn.ensemble._forest.ExtraTreesClassifier   True  \n", "xgboost                       xgboost.sklearn.XGBClassifier   True  \n", "lightgbm                    lightgbm.sklearn.LGBMClassifier   True  \n", "catboost                   catboost.core.CatBoostClassifier   True  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["models()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "UWMSeyNhEQo-"}, "source": ["# 8.1 决策树分类器"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "LP896uSIEQpD", "outputId": "d6d31562-feb5-4052-ee23-0a444fecaacf"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col0 {\n", "            background:  yellow;\n", "        }    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col1 {\n", "            background:  yellow;\n", "        }    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col2 {\n", "            background:  yellow;\n", "        }    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col3 {\n", "            background:  yellow;\n", "        }    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col4 {\n", "            background:  yellow;\n", "        }    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col5 {\n", "            background:  yellow;\n", "        }    #T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col6 {\n", "            background:  yellow;\n", "        }</style><table id=\"T_f411b634_2b73_11eb_af4c_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Accuracy</th>        <th class=\"col_heading level0 col1\" >AUC</th>        <th class=\"col_heading level0 col2\" >Recall</th>        <th class=\"col_heading level0 col3\" >Prec.</th>        <th class=\"col_heading level0 col4\" >F1</th>        <th class=\"col_heading level0 col5\" >Kappa</th>        <th class=\"col_heading level0 col6\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col0\" class=\"data row0 col0\" >0.7331</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.6239</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.4298</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.3979</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.4132</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.2408</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.2411</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col0\" class=\"data row1 col0\" >0.7325</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.6359</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.4642</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.4030</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.4314</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.2576</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.2587</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col0\" class=\"data row2 col0\" >0.7419</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.6254</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.4183</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.4113</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.4148</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.2492</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.2492</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col0\" class=\"data row3 col0\" >0.7256</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.6116</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.4097</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.3813</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.3950</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.2179</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.2181</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col0\" class=\"data row4 col0\" >0.7124</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.6127</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.4355</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.3671</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.3984</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.2113</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.2126</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col0\" class=\"data row5 col0\" >0.7193</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.6111</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.4155</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.3728</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.3930</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.2111</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.2116</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col0\" class=\"data row6 col0\" >0.7212</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.6098</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.4126</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.3750</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.3929</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.2125</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.2129</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col0\" class=\"data row7 col0\" >0.7287</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.5932</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.3524</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.3727</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.3623</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.1902</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.1903</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col0\" class=\"data row8 col0\" >0.7105</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.5898</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.3754</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.3493</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.3619</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.1750</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.1752</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col0\" class=\"data row9 col0\" >0.7373</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.6207</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.4138</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.4011</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.4074</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.2387</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.2387</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col0\" class=\"data row10 col0\" >0.7262</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.6134</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.4127</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.3832</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.3970</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.2204</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.2208</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f411b634_2b73_11eb_af4c_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col0\" class=\"data row11 col0\" >0.0099</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.0134</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.0292</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.0185</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.0209</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.0249</td>\n", "                        <td id=\"T_f411b634_2b73_11eb_af4c_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.0249</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c51c266c8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dt = create_model('dt')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {}, "colab_type": "code", "id": "FRat05yGEQpQ", "outputId": "c8e6a190-8bec-4646-d2c8-8a92b129c484"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='gini',\n", "                       max_depth=None, max_features=None, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.0, min_impurity_split=None,\n", "                       min_samples_leaf=1, min_samples_split=2,\n", "                       min_weight_fraction_leaf=0.0, presort='deprecated',\n", "                       random_state=123, splitter='best')\n"]}], "source": ["#trained model object is stored in the variable 'dt'. \n", "print(dt)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "rWUojqBCEQpb"}, "source": ["# 8.2 K 邻居分类器"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "2uonD20gEQpe", "outputId": "560e3cb6-41d5-4293-b1c5-2bd1cf3bc63b"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col0 {\n", "            background:  yellow;\n", "        }    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col1 {\n", "            background:  yellow;\n", "        }    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col2 {\n", "            background:  yellow;\n", "        }    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col3 {\n", "            background:  yellow;\n", "        }    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col4 {\n", "            background:  yellow;\n", "        }    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col5 {\n", "            background:  yellow;\n", "        }    #T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col6 {\n", "            background:  yellow;\n", "        }</style><table id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Accuracy</th>        <th class=\"col_heading level0 col1\" >AUC</th>        <th class=\"col_heading level0 col2\" >Recall</th>        <th class=\"col_heading level0 col3\" >Prec.</th>        <th class=\"col_heading level0 col4\" >F1</th>        <th class=\"col_heading level0 col5\" >Kappa</th>        <th class=\"col_heading level0 col6\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col0\" class=\"data row0 col0\" >0.7469</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.6020</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.1920</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.3545</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.2491</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.1128</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.1204</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col0\" class=\"data row1 col0\" >0.7550</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.5894</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.2092</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.3883</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.2719</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.1402</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.1500</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col0\" class=\"data row2 col0\" >0.7506</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.5883</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.1576</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.3459</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.2165</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.0923</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.1024</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col0\" class=\"data row3 col0\" >0.7419</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.5818</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.1519</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.3136</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.2046</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.0723</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.0790</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col0\" class=\"data row4 col0\" >0.7563</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.5908</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.1490</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.3611</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.2110</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.0954</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.1085</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col0\" class=\"data row5 col0\" >0.7550</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.5997</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.1748</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.3720</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.2378</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.1139</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.1255</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col0\" class=\"data row6 col0\" >0.7638</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.5890</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.1891</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.4125</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.2593</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.1413</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.1565</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col0\" class=\"data row7 col0\" >0.7613</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.6240</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.1633</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.3904</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.2303</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.1163</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.1318</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col0\" class=\"data row8 col0\" >0.7619</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.5988</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.1862</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.4037</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.2549</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.1356</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.1500</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col0\" class=\"data row9 col0\" >0.7549</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.5756</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.1897</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.3771</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.2524</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.1246</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.1351</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col0\" class=\"data row10 col0\" >0.7547</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.5939</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.1763</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.3719</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.2388</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.1145</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.1259</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col0\" class=\"data row11 col0\" >0.0065</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.0126</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.0191</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.0279</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.0214</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.0214</td>\n", "                        <td id=\"T_f6b08a5e_2b73_11eb_ad15_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.0230</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5ca61448>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["knn = create_model('knn')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nSg3OUjuEQpu"}, "source": ["# 8.3 随机森林分类器"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "FGCoUiQpEQpz", "outputId": "212cb736-6dcb-4b77-e45b-14ad895bff43"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col0 {\n", "            background:  yellow;\n", "        }    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col1 {\n", "            background:  yellow;\n", "        }    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col2 {\n", "            background:  yellow;\n", "        }    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col3 {\n", "            background:  yellow;\n", "        }    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col4 {\n", "            background:  yellow;\n", "        }    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col5 {\n", "            background:  yellow;\n", "        }    #T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col6 {\n", "            background:  yellow;\n", "        }</style><table id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Accuracy</th>        <th class=\"col_heading level0 col1\" >AUC</th>        <th class=\"col_heading level0 col2\" >Recall</th>        <th class=\"col_heading level0 col3\" >Prec.</th>        <th class=\"col_heading level0 col4\" >F1</th>        <th class=\"col_heading level0 col5\" >Kappa</th>        <th class=\"col_heading level0 col6\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col0\" class=\"data row0 col0\" >0.8114</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.7666</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.3467</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.6237</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.4457</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.3430</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.3645</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col0\" class=\"data row1 col0\" >0.8264</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.7527</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.3897</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.6800</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.4954</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.3998</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.4224</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col0\" class=\"data row2 col0\" >0.8258</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.7701</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.3496</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.7052</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.4674</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.3772</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.4104</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col0\" class=\"data row3 col0\" >0.8195</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.7662</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.3754</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.6517</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.4764</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.3768</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.3977</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col0\" class=\"data row4 col0\" >0.8177</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.7654</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.3524</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.6543</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.4581</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.3601</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.3851</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col0\" class=\"data row5 col0\" >0.8283</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.7750</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.3897</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.6904</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.4982</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.4041</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.4282</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col0\" class=\"data row6 col0\" >0.8076</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.7717</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.3352</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.6094</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.4325</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.3283</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.3495</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col0\" class=\"data row7 col0\" >0.8195</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.7401</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.3381</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.6743</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.4504</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.3564</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.3868</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col0\" class=\"data row8 col0\" >0.8095</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.7461</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.3582</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.6098</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.4513</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.3453</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.3632</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col0\" class=\"data row9 col0\" >0.8144</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.7643</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.3563</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.6327</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.4559</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.3544</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.3756</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col0\" class=\"data row10 col0\" >0.8180</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.7618</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.3591</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.6531</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.4631</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.3645</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.3884</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col0\" class=\"data row11 col0\" >0.0069</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.0110</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.0186</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.0322</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.0202</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.0234</td>\n", "                        <td id=\"T_fdfadeb0_2b73_11eb_8e78_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.0249</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5cb71dc8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rf = create_model('rf')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "z6F3Fk7TEQp8"}, "source": ["请注意，所有模型的平均分数与 `compare_models()` 中打印的分数相匹配。这是因为在 `compare_models()` 得分网格中打印的指标是所有 CV 折叠的平均得分。与 `compare_models()` 类似，如果要将 fold 参数从默认值 10 更改为不同的值，则可以使用 `fold` 参数。例如：`create_model('dt', fold = 5)` 将使用 5 折分层 CV 创建决策树分类器。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XvpjzbGQEQqB"}, "source": ["# 9.0 调参数"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nc_GgksHEQqE"}, "source": ["当使用 `create_model()` 函数创建模型时，它使用默认超参数来训练模型。为了调整超参数，使用了 `tune_model()` 函数。此功能使用“随机网格搜索”在预定义的搜索空间上自动调整模型的超参数。输出打印一个分数网格，按折叠显示准确度、AUC、召回率、精度、F1、Kappa 和 MCC，以获得最佳模型。要使用自定义搜索网格，您可以在 `tune_model` 函数中传递 `custom_grid` 参数（请参阅下面的 9.2 KNN 调整） <br/>\n", "<br/>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "BQlMCxrUEQqG"}, "source": ["# 9.1 决策树分类器"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "of46aj6vEQqJ", "outputId": "26f7f708-739a-489b-bb76-b33e0a800362"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col0 {\n", "            background:  yellow;\n", "        }    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col1 {\n", "            background:  yellow;\n", "        }    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col2 {\n", "            background:  yellow;\n", "        }    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col3 {\n", "            background:  yellow;\n", "        }    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col4 {\n", "            background:  yellow;\n", "        }    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col5 {\n", "            background:  yellow;\n", "        }    #T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col6 {\n", "            background:  yellow;\n", "        }</style><table id=\"T_ffb28312_2b73_11eb_919b_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Accuracy</th>        <th class=\"col_heading level0 col1\" >AUC</th>        <th class=\"col_heading level0 col2\" >Recall</th>        <th class=\"col_heading level0 col3\" >Prec.</th>        <th class=\"col_heading level0 col4\" >F1</th>        <th class=\"col_heading level0 col5\" >Kappa</th>        <th class=\"col_heading level0 col6\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col0\" class=\"data row0 col0\" >0.8177</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.7475</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.3095</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.6835</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.4260</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.3355</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.3728</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col0\" class=\"data row1 col0\" >0.8289</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.7711</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.3381</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.7375</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.4637</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.3782</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.4190</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col0\" class=\"data row2 col0\" >0.8208</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.7377</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.2894</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.7266</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.4139</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.3305</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.3796</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col0\" class=\"data row3 col0\" >0.8252</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.7580</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.3152</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.7333</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.4409</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.3563</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.4010</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col0\" class=\"data row4 col0\" >0.8195</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.7545</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.3095</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.6968</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.4286</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.3398</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.3794</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col0\" class=\"data row5 col0\" >0.8271</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.7509</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.3438</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.7186</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.4651</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.3769</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.4134</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col0\" class=\"data row6 col0\" >0.8195</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.7488</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.3123</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.6943</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.4308</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.3415</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.3801</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col0\" class=\"data row7 col0\" >0.8246</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.7529</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.2980</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.7482</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.4262</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.3446</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.3957</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col0\" class=\"data row8 col0\" >0.8195</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.7241</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.3123</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.6943</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.4308</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.3415</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.3801</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col0\" class=\"data row9 col0\" >0.8188</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.7378</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.3075</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.6903</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.4254</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.3362</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.3751</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col0\" class=\"data row10 col0\" >0.8222</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.7483</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.3136</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.7123</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.4352</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.3481</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.3896</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_ffb28312_2b73_11eb_919b_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col0\" class=\"data row11 col0\" >0.0037</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.0122</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.0156</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.0219</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.0159</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.0161</td>\n", "                        <td id=\"T_ffb28312_2b73_11eb_919b_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.0158</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5b1aa308>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_dt = tune_model(dt)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {}, "colab_type": "code", "id": "__anDkttEQqV", "outputId": "7cf46ace-012a-4131-b8b8-370f9d4a63cb"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='entropy',\n", "                       max_depth=6, max_features=1.0, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.002, min_impurity_split=None,\n", "                       min_samples_leaf=5, min_samples_split=5,\n", "                       min_weight_fraction_leaf=0.0, presort='deprecated',\n", "                       random_state=123, splitter='best')\n"]}], "source": ["#tuned model object is stored in the variable 'tuned_dt'. \n", "print(tuned_dt)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "CD-f0delEQqq"}, "source": ["# 9.2 K 邻居分类器"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "xN1nYwFXEQqv", "outputId": "e4ab669d-bee0-4a9d-f5c7-2ed07ec613b9"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col0 {\n", "            background:  yellow;\n", "        }    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col1 {\n", "            background:  yellow;\n", "        }    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col2 {\n", "            background:  yellow;\n", "        }    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col3 {\n", "            background:  yellow;\n", "        }    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col4 {\n", "            background:  yellow;\n", "        }    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col5 {\n", "            background:  yellow;\n", "        }    #T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col6 {\n", "            background:  yellow;\n", "        }</style><table id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Accuracy</th>        <th class=\"col_heading level0 col1\" >AUC</th>        <th class=\"col_heading level0 col2\" >Recall</th>        <th class=\"col_heading level0 col3\" >Prec.</th>        <th class=\"col_heading level0 col4\" >F1</th>        <th class=\"col_heading level0 col5\" >Kappa</th>        <th class=\"col_heading level0 col6\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col0\" class=\"data row0 col0\" >0.7813</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.6482</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.0372</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.5000</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.0693</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.0402</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.0876</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col0\" class=\"data row1 col0\" >0.7807</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.6436</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.0315</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.4783</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.0591</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.0330</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.0759</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col0\" class=\"data row2 col0\" >0.7744</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.6563</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.0315</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.3333</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.0576</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.0206</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.0403</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col0\" class=\"data row3 col0\" >0.7845</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.6589</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.0659</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.5610</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.1179</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.0754</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.1345</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col0\" class=\"data row4 col0\" >0.7826</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.6645</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.0315</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.5500</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.0596</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.0368</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.0903</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col0\" class=\"data row5 col0\" >0.7794</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.6477</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.0544</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.4634</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.0974</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.0539</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.0961</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col0\" class=\"data row6 col0\" >0.7826</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.6278</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.0630</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.5238</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.1125</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.0688</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.1214</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col0\" class=\"data row7 col0\" >0.7751</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.6702</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.0372</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.3611</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.0675</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.0278</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.0523</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col0\" class=\"data row8 col0\" >0.7813</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.6409</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.0630</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.5000</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.1120</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.0662</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.1146</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col0\" class=\"data row9 col0\" >0.7881</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.6426</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.0661</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.6389</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.1198</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.0822</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.1548</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col0\" class=\"data row10 col0\" >0.7810</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.6501</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.0482</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.4910</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.0873</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.0505</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.0968</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col0\" class=\"data row11 col0\" >0.0039</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.0119</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.0148</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.0861</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.0255</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.0206</td>\n", "                        <td id=\"T_14076d4c_2b74_11eb_b8eb_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.0338</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5b4eaec8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "tuned_knn = tune_model(knn, custom_grid = {'n_neighbors' : np.arange(0,50,1)})"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KNeighborsClassifier(algorithm='auto', leaf_size=30, metric='minkowski',\n", "                     metric_params=None, n_jobs=-1, n_neighbors=42, p=2,\n", "                     weights='uniform')\n"]}], "source": ["print(tuned_knn)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "KO3zIfs-EQrA"}, "source": ["# 9.3 随机森林分类器"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "gmaIfnBMEQrE", "outputId": "a59cebfa-f81e-477c-f83c-e9443fd80b0f"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col0 {\n", "            background:  yellow;\n", "        }    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col1 {\n", "            background:  yellow;\n", "        }    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col2 {\n", "            background:  yellow;\n", "        }    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col3 {\n", "            background:  yellow;\n", "        }    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col4 {\n", "            background:  yellow;\n", "        }    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col5 {\n", "            background:  yellow;\n", "        }    #T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col6 {\n", "            background:  yellow;\n", "        }</style><table id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Accuracy</th>        <th class=\"col_heading level0 col1\" >AUC</th>        <th class=\"col_heading level0 col2\" >Recall</th>        <th class=\"col_heading level0 col3\" >Prec.</th>        <th class=\"col_heading level0 col4\" >F1</th>        <th class=\"col_heading level0 col5\" >Kappa</th>        <th class=\"col_heading level0 col6\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col0\" class=\"data row0 col0\" >0.8158</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.7508</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.3181</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.6647</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.4302</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.3363</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.3689</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col0\" class=\"data row1 col0\" >0.8283</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col1\" class=\"data row1 col1\" >0.7675</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col2\" class=\"data row1 col2\" >0.3295</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col3\" class=\"data row1 col3\" >0.7419</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col4\" class=\"data row1 col4\" >0.4563</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col5\" class=\"data row1 col5\" >0.3719</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow1_col6\" class=\"data row1 col6\" >0.4152</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col0\" class=\"data row2 col0\" >0.8139</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col1\" class=\"data row2 col1\" >0.7337</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col2\" class=\"data row2 col2\" >0.3181</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col3\" class=\"data row2 col3\" >0.6529</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col4\" class=\"data row2 col4\" >0.4277</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col5\" class=\"data row2 col5\" >0.3321</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow2_col6\" class=\"data row2 col6\" >0.3628</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col0\" class=\"data row3 col0\" >0.8246</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col1\" class=\"data row3 col1\" >0.7588</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col2\" class=\"data row3 col2\" >0.3095</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col3\" class=\"data row3 col3\" >0.7347</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col4\" class=\"data row3 col4\" >0.4355</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col5\" class=\"data row3 col5\" >0.3514</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow3_col6\" class=\"data row3 col6\" >0.3976</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col0\" class=\"data row4 col0\" >0.8170</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col1\" class=\"data row4 col1\" >0.7567</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col2\" class=\"data row4 col2\" >0.3438</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col3\" class=\"data row4 col3\" >0.6557</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col4\" class=\"data row4 col4\" >0.4511</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col5\" class=\"data row4 col5\" >0.3539</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow4_col6\" class=\"data row4 col6\" >0.3805</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col0\" class=\"data row5 col0\" >0.8258</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col1\" class=\"data row5 col1\" >0.7513</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col2\" class=\"data row5 col2\" >0.3324</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col3\" class=\"data row5 col3\" >0.7205</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col4\" class=\"data row5 col4\" >0.4549</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col5\" class=\"data row5 col5\" >0.3676</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow5_col6\" class=\"data row5 col6\" >0.4067</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col0\" class=\"data row6 col0\" >0.8170</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col1\" class=\"data row6 col1\" >0.7529</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col2\" class=\"data row6 col2\" >0.3324</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col3\" class=\"data row6 col3\" >0.6629</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col4\" class=\"data row6 col4\" >0.4427</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col5\" class=\"data row6 col5\" >0.3474</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow6_col6\" class=\"data row6 col6\" >0.3771</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col0\" class=\"data row7 col0\" >0.8221</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col1\" class=\"data row7 col1\" >0.7507</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col2\" class=\"data row7 col2\" >0.3381</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col3\" class=\"data row7 col3\" >0.6901</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col4\" class=\"data row7 col4\" >0.4538</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col5\" class=\"data row7 col5\" >0.3621</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow7_col6\" class=\"data row7 col6\" >0.3951</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col0\" class=\"data row8 col0\" >0.8177</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col1\" class=\"data row8 col1\" >0.7201</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col2\" class=\"data row8 col2\" >0.2980</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col3\" class=\"data row8 col3\" >0.6933</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col4\" class=\"data row8 col4\" >0.4168</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col5\" class=\"data row8 col5\" >0.3286</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow8_col6\" class=\"data row8 col6\" >0.3699</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col0\" class=\"data row9 col0\" >0.8207</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col1\" class=\"data row9 col1\" >0.7484</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col2\" class=\"data row9 col2\" >0.3132</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col3\" class=\"data row9 col3\" >0.6987</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col4\" class=\"data row9 col4\" >0.4325</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col5\" class=\"data row9 col5\" >0.3439</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow9_col6\" class=\"data row9 col6\" >0.3831</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col0\" class=\"data row10 col0\" >0.8203</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col1\" class=\"data row10 col1\" >0.7491</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col2\" class=\"data row10 col2\" >0.3233</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col3\" class=\"data row10 col3\" >0.6915</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col4\" class=\"data row10 col4\" >0.4402</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col5\" class=\"data row10 col5\" >0.3495</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow10_col6\" class=\"data row10 col6\" >0.3857</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83dalevel0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col0\" class=\"data row11 col0\" >0.0045</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col1\" class=\"data row11 col1\" >0.0126</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col2\" class=\"data row11 col2\" >0.0135</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col3\" class=\"data row11 col3\" >0.0310</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col4\" class=\"data row11 col4\" >0.0129</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col5\" class=\"data row11 col5\" >0.0140</td>\n", "                        <td id=\"T_b54590fa_2b74_11eb_8a7b_482ae32b83darow11_col6\" class=\"data row11 col6\" >0.0165</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5aadf748>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_rf = tune_model(rf)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "IqxEZRi1EQrO"}, "source": ["默认情况下，`tune_model` 会优化 `Accuracy`，但这可以使用 `optimize` 参数进行更改。例如：`tune_model(dt, optimize = 'AUC')` 将搜索决策树分类器的超参数，它会导致最高的 `AUC` 而不是 `Accuracy`。出于本示例的目的，我们使用默认指标“准确度”只是为了简单起见。通常，当数据集不平衡时（例如我们正在使用的信用数据集），“准确性”不是一个很好的考虑指标。选择正确指标来评估分类器的方法超出了本教程的范围，但如果您想了解更多信息，可以__[点击此处](https://medium.com/@MohammedS/performance-metrics-for-classification-problems-in-machine-learning-part-i-b085d432082b)__来阅读有关如何选择正确评估指标的文章。\n", "\n", "在最终确定最佳生产模型时，指标本身并不是您应该考虑的唯一标准。其他要考虑的因素包括训练时间、kfolds 的标准差等。随着您学习本教程系列，我们将在中级和专家级别详细讨论这些因素。现在，让我们继续考虑调整随机森林分类器“tuned_rf”，作为本教程其余部分的最佳模型。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "w_P46O0jEQrT"}, "source": ["# 10.0 绘制模型"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "FGM9GOtjEQrV"}, "source": ["在模型最终确定之前，`plot_model()` 函数可用于分析不同方面的性能，例如 AUC、confusion_matrix、决策边界等。此函数采用经过训练的模型对象并返回基于测试/保留的图放。\n", "\n", "有 15 种不同的图可用，请参阅 `plot_model()` 文档字符串以获取可用图的列表。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "euqkQYJaEQrY"}, "source": ["# 10.1 AUC 曲线"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {}, "colab_type": "code", "id": "RLbLqvkHEQra", "outputId": "fe40b5e3-6375-43e8-e97d-1d487e02eb2d"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'auc')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "bwyoTUDQEQrm"}, "source": ["# 10.2 精确召回曲线"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {}, "colab_type": "code", "id": "4IvchQoiEQrr", "outputId": "fdff2076-86fc-42f5-beee-f0051ea30dd4"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'pr')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "_r9rwEw7EQrz"}, "source": ["# 10.3 特征重要性图"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {}, "colab_type": "code", "id": "nVScSxJ-EQr2", "outputId": "f44f4b08-b749-4d0e-dcc9-d7e3dc6240c8"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot='feature')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "FfWC3NEhEQr9"}, "source": ["# 10.4 混淆矩阵"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {}, "colab_type": "code", "id": "OAB5mes-EQsA", "outputId": "bd82130d-2cc3-4b63-df5d-03b7aa54bf52"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_rf, plot = 'confusion_matrix')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "deClKJrbEQsJ"}, "source": ["另一种分析模型性能的方法是使用 `evaluate_model()` 函数，该函数显示给定模型的所有可用图的用户界面。它在内部使用 `plot_model()` 函数。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 436, "referenced_widgets": ["42d5400d235d40b78190016ef0dabe11", "41031579127f4a53b58957e601465083", "12bf8b3c6ae8444a900474912589fdf1", "9bb3600d38c04691b444ff375ad5e3f5", "8886001bc7c1463ba58a8453f5c55073", "0a06fb091bd94ce6b6ab892e2c6faadf", "3cc1e83b91f34b289c7d52003f20a97a", "8d709ec9ec484944b1f9773748857f84", "8399e21b17634116861a5abaa9c0ccf7", "d5b6fce1763b4b54898ff3397b0f5bb0", "57b94ac505d142769b79de2f1e5c1166", "2a81017413ca4fe789c2272a5831a069", "02771b4dc3284414ab05df1906f4556b", "9e338844e75b4e17be8483529f5f38fd", "22588a12c0db4067982e62ebbe7e6930"]}, "colab_type": "code", "id": "OcLV1Ln6EQsN", "outputId": "7b5b8b4e-8d4a-4371-9a4f-cabb0a96265a"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fde1a3ec54a1486cae471768f0b13aab", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(ToggleButtons(description='Plot Type:', icons=('',), options=(('Hyperparameters', 'param…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["evaluate_model(tuned_rf)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "RX5pYUJJEQsV"}, "source": ["# 11.0 在保留样本上测试"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "mFSvRYiaEQsd"}, "source": ["在最终确定模型之前，建议通过预测测试/保留集并查看评估指标来执行最终检查。如果您查看上面第 6 节中的信息网格，您将看到 30%（6,841 个样本）的数据已作为测试/保留样本分离出来。我们在上面看到的所有评估指标都是仅基于训练集 (70%) 的交叉验证结果。现在，使用存储在 `tuned_rf` 变量中的最终训练模型，我们将针对保留样本进行预测并评估指标，以查看它们是否与 CV 结果存在重大差异"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {}, "colab_type": "code", "id": "nwaZk6oTEQsi", "outputId": "d30c8533-d347-4fa6-f18e-5b2abc937bec"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "</style><table id=\"T_b784461e_2b74_11eb_acf1_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Model</th>        <th class=\"col_heading level0 col1\" >Accuracy</th>        <th class=\"col_heading level0 col2\" >AUC</th>        <th class=\"col_heading level0 col3\" >Recall</th>        <th class=\"col_heading level0 col4\" >Prec.</th>        <th class=\"col_heading level0 col5\" >F1</th>        <th class=\"col_heading level0 col6\" >Kappa</th>        <th class=\"col_heading level0 col7\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_b784461e_2b74_11eb_acf1_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.8116</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.7407</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.3436</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.6650</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.4531</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.3530</td>\n", "                        <td id=\"T_b784461e_2b74_11eb_acf1_482ae32b83darow0_col7\" class=\"data row0 col7\" >0.3811</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c5ba7d648>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(tuned_rf);"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "E-fHsX2AEQsx"}, "source": ["测试/保持集的准确度是 **`0.8116`** 与 **`0.8203`** 在 `tuned_rf` CV 结果（在上面的第 9.3 节中）上实现的。这不是一个显着的区别。如果测试/保留结果和 CV 结果之间存在很大差异，则这通常表明过度拟合，但也可能是由于其他几个因素，需要进一步调查。在这种情况下，我们将继续完成模型并预测看不见的数据（我们在开始时分离并且从未暴露于 PyCaret 的 5%）。\n", "\n", "（提示：使用 `create_model()` 时查看 CV 结果的标准差总是好的。）"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "r79BGjIfEQs1"}, "source": ["# 12.0 最终确定部署模型"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "B-6xJ9kQEQs7"}, "source": ["模型定型是实验的最后一步。 PyCaret 中的正常机器学习工作流程从“setup()”开始，然后使用“compare_models()”比较所有模型，并列出一些候选模型（基于感兴趣的指标）以执行多种建模技术，例如超参数调整，集成、堆叠等。此工作流程最终将引导您找到用于对新的和未见过的数据进行预测的最佳模型。 `finalize_model()` 函数将模型拟合到完整的数据集上，包括测试/保留样本（在这种情况下为 30%）。此功能的目的是在将模型部署到生产环境之前在完整数据集上训练模型。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {}, "colab_type": "code", "id": "_--tO4KGEQs-"}, "outputs": [], "source": ["final_rf = finalize_model(tuned_rf)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 147}, "colab_type": "code", "id": "U9W6kXsSEQtQ", "outputId": "794b24a4-9c95-4730-eddd-f82e4925b866"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RandomForestClassifier(bootstrap=False, ccp_alpha=0.0, class_weight={},\n", "                       criterion='entropy', max_depth=5, max_features=1.0,\n", "                       max_leaf_nodes=None, max_samples=None,\n", "                       min_impurity_decrease=0.0002, min_impurity_split=None,\n", "                       min_samples_leaf=5, min_samples_split=10,\n", "                       min_weight_fraction_leaf=0.0, n_estimators=150,\n", "                       n_jobs=-1, oob_score=False, random_state=123, verbose=0,\n", "                       warm_start=False)\n"]}], "source": ["#Final Random Forest model parameters for deployment\n", "print(final_rf)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "kgdOjxypEQtd"}, "source": ["**警告：** 使用 finalize_model() 完成模型后，包括测试/保留集在内的整个数据集将用于训练。因此，如果在使用 `finalize_model()` 后模型用于对保留集的预测，则打印的信息网格将具有误导性，因为您试图在用于建模的相同数据上进行预测。为了证明这一点，我们将使用 `predict_model()` 下的 `final_rf` 将信息网格与上面第 11 节中的信息网格进行比较。"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {}, "colab_type": "code", "id": "NJDk3I-EEQtg", "outputId": "4d75663a-e86f-4826-c8e4-c9aa722648df"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "</style><table id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Model</th>        <th class=\"col_heading level0 col1\" >Accuracy</th>        <th class=\"col_heading level0 col2\" >AUC</th>        <th class=\"col_heading level0 col3\" >Recall</th>        <th class=\"col_heading level0 col4\" >Prec.</th>        <th class=\"col_heading level0 col5\" >F1</th>        <th class=\"col_heading level0 col6\" >Kappa</th>        <th class=\"col_heading level0 col7\" >MCC</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col1\" class=\"data row0 col1\" >0.8184</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col2\" class=\"data row0 col2\" >0.7526</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col3\" class=\"data row0 col3\" >0.3533</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.6985</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.4692</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.3736</td>\n", "                        <td id=\"T_29160d9e_2b75_11eb_83c4_482ae32b83darow0_col7\" class=\"data row0 col7\" >0.4053</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x25c58d48c88>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(final_rf);"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "V77JC5JVEQtp"}, "source": ["请注意 `final_rf` 中的 AUC 如何从 **`0.7407`** 增加到 **`0.7526`**，即使模型相同。这是因为 `final_rf` 变量已经在包括测试/保留集在内的完整数据集上进行了训练。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "hUzc6tXNEQtr"}, "source": ["# 13.0 预测新数据"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "dx5vXjChEQtt"}, "source": ["`predict_model()` 函数也用于在看不见的数据集上进行预测。与上面第 11 节的唯一区别是，这次我们将传递 `data_unseen` 参数。 `data_unseen` 是在教程开始时创建的变量，包含 5%（1200 个样本）的原始数据集，从未暴露给 PyCaret。 （解释见第 5 节）"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211}, "colab_type": "code", "id": "0y5KWLC6EQtx", "outputId": "30771f87-7847-43ce-e984-9963cff7d043"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>Label</th>\n", "      <th>Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.7911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  \\\n", "0      0  ...      567.0     380.0     601.0       0.0     581.0    1687.0   \n", "1     -1  ...    11873.0   21540.0   15138.0   24677.0   11851.0   11875.0   \n", "2      2  ...     3151.0    5818.0      15.0    9102.0      17.0    3165.0   \n", "3      2  ...   149531.0    6300.0    5500.0    5500.0    5500.0    5000.0   \n", "4      0  ...     1737.0    2622.0    3301.0       0.0     360.0    1737.0   \n", "\n", "   PAY_AMT6  default  Label   Score  \n", "0    1542.0        0      0  0.8051  \n", "1    8251.0        0      0  0.9121  \n", "2    1395.0        0      0  0.8051  \n", "3    5000.0        1      1  0.7911  \n", "4     924.0        0      0  0.9121  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["unseen_predictions = predict_model(final_rf, data=data_unseen)\n", "unseen_predictions.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "oPYmVpugEQt5"}, "source": ["`Label` 和 `Score` 列被添加到 `data_unseen` 集合中。标签是预测，分数是预测的概率。请注意，预测结果会连接到原始数据集，而所有转换都是在后台自动执行的。您还可以检查这方面的指标，因为您有实际的目标列“默认”可用。为此，我们将使用 `pycaret.utils` 模块。请参见下面的示例："]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8167"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(unseen_predictions['default'], unseen_predictions['Label'], metric = 'Accuracy')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "L__po3sUEQt7"}, "source": ["# 14.0 保存模型"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "1sQPT7jrEQt-"}, "source": ["我们现在已经通过最终确定存储在 `final_rf` 变量中的 `tuned_rf` 模型完成了实验。我们还使用 `final_rf` 中存储的模型来预测 `data_unseen`。这使我们的实验结束，但仍有一个问题要问：当您有更多新数据要预测时会发生什么？您是否必须再次完成整个实验？答案是否定的，PyCaret 的内置函数 `save_model()` 允许您将模型与整个转换管道一起保存以供以后使用。"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {}, "colab_type": "code", "id": "ln1YWIXTEQuA", "outputId": "d3cb0652-f72e-44e8-9455-824b12740bff"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Succesfully Saved\n"]}, {"data": {"text/plain": ["(Pipeline(memory=None,\n", "          steps=[('dtypes',\n", "                  DataTypes_Auto_infer(categorical_features=[],\n", "                                       display_types=True, features_todrop=[],\n", "                                       id_columns=[],\n", "                                       ml_usecase='classification',\n", "                                       numerical_features=[], target='default',\n", "                                       time_features=[])),\n", "                 ('imputer',\n", "                  Simple_Imputer(categorical_strategy='not_available',\n", "                                 fill_value_categorical=None,\n", "                                 fill_value_numerical=None,\n", "                                 numeric_stra...\n", "                  RandomForestClassifier(bootstrap=False, ccp_alpha=0.0,\n", "                                         class_weight={}, criterion='entropy',\n", "                                         max_depth=5, max_features=1.0,\n", "                                         max_leaf_nodes=None, max_samples=None,\n", "                                         min_impurity_decrease=0.0002,\n", "                                         min_impurity_split=None,\n", "                                         min_samples_leaf=5,\n", "                                         min_samples_split=10,\n", "                                         min_weight_fraction_leaf=0.0,\n", "                                         n_estimators=150, n_jobs=-1,\n", "                                         oob_score=False, random_state=123,\n", "                                         verbose=0, warm_start=False)]],\n", "          verbose=False), 'Final RF Model 11Nov2020.pkl')"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["save_model(final_rf,'Final RF Model 11Nov2020')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "WE6f48AYEQuR"}, "source": ["（提示：保存模型时最好在文件名中使用日期，这有利于版本控制。）"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Z8OBesfkEQuU"}, "source": ["# 15.0 加载保存的模型"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "V2K_WLaaEQuW"}, "source": ["要在未来某个日期在相同或替代环境中加载保存的模型，我们将使用 PyCaret 的 `load_model()` 函数，然后轻松地将保存的模型应用于新的看不见的数据进行预测。"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {}, "colab_type": "code", "id": "Siw_2EIUEQub", "outputId": "5da8b7c9-01f7-469c-f0c9-b19c8ce11bcc"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Loaded\n"]}], "source": ["saved_final_rf = load_model('Final RF Model 11Nov2020')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "1<PERSON>i6-<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, "source": ["在环境中加载模型后，你可以使用相同的`predict_model()`函数简单地使用它来预测任何新数据。下面我们应用加载的模型来预测我们在上面第 13 节中使用的相同的`data_unseen`。"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {}, "colab_type": "code", "id": "HMPO1ka9EQut"}, "outputs": [], "source": ["new_prediction = predict_model(saved_final_rf, data=data_unseen)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {}, "colab_type": "code", "id": "7wyDQQSzEQu8", "outputId": "23065436-42e3-4441-ed58-a8863f8971f9"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>Label</th>\n", "      <th>Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.8051</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.7911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.9121</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  \\\n", "0      0  ...      567.0     380.0     601.0       0.0     581.0    1687.0   \n", "1     -1  ...    11873.0   21540.0   15138.0   24677.0   11851.0   11875.0   \n", "2      2  ...     3151.0    5818.0      15.0    9102.0      17.0    3165.0   \n", "3      2  ...   149531.0    6300.0    5500.0    5500.0    5500.0    5000.0   \n", "4      0  ...     1737.0    2622.0    3301.0       0.0     360.0    1737.0   \n", "\n", "   PAY_AMT6  default  Label   Score  \n", "0    1542.0        0      0  0.8051  \n", "1    8251.0        0      0  0.9121  \n", "2    1395.0        0      0  0.8051  \n", "3    5000.0        1      1  0.7911  \n", "4     924.0        0      0  0.9121  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["new_prediction.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "bf8I1uqcEQvD"}, "source": ["请注意，`unseen_predictions` 和 `new_prediction` 的结果是相同的。"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8167"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(new_prediction['default'], new_prediction['Label'], metric = 'Accuracy')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "_HeOs8BhEQvF"}, "source": ["# 16.0 总结/下一步"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "VqG1NnwXEQvK"}, "source": ["本教程涵盖了从数据摄取、预处理、模型训练、超参数调整、预测和保存模型以备后用的整个机器学习管道。我们在不到 10 个命令中完成了所有这些步骤，这些命令是自然构造的并且非常容易记住，例如`create_model()`、`tune_model()`、`compare_models()`。在大多数库中，如果不使用 PyCaret 重新创建整个实验，将需要 100 多行代码。\n", "\n", "__[Binary Classification Tutorial (CLF102) - Intermediate Level](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)__"]}], "metadata": {"colab": {"collapsed_sections": ["Ui_rALqYEQmv", "y9s9wNcjEQn0", "it_nJo1IEQob", "P5m2pciOEQo4", "UWMSeyNhEQo-", "rWUojqBCEQpb", "nSg3OUjuEQpu", "XvpjzbGQEQqB", "BQlMCxrUEQqG", "CD-f0delEQqq", "KO3zIfs-EQrA", "w_P46O0jEQrT", "euqkQYJaEQrY", "bwyoTUDQEQrm", "_r9rwEw7EQrz", "FfWC3NEhEQr9", "RX5pYUJJEQsV", "r79BGjIfEQs1", "hUzc6tXNEQtr", "L__po3sUEQt7", "Z8OBesfkEQuU", "_HeOs8BhEQvF"], "name": "Binary Classification Tutorial (CLF101) - Level Beginner (ACN_EDITS).ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"02771b4dc3284414ab05df1906f4556b": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_22588a12c0db4067982e62ebbe7e6930", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9e338844e75b4e17be8483529f5f38fd", "value": 5}}, "0a06fb091bd94ce6b6ab892e2c6faadf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12bf8b3c6ae8444a900474912589fdf1": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsModel", "_options_labels": ["Hyperparameters", "AUC", "Confusion Matrix", "<PERSON><PERSON><PERSON><PERSON>", "Precision Recall", "Error", "Class Report", "Feature Selection", "Learning Curve", "Manifold Learning", "Calibration Curve", "Validation Curve", "Dimensions", "Feature Importance", "Decision Boundary"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonsView", "button_style": "", "description": "Plot Type:", "description_tooltip": null, "disabled": false, "icons": [""], "index": 2, "layout": "IPY_MODEL_0a06fb091bd94ce6b6ab892e2c6faadf", "style": "IPY_MODEL_8886001bc7c1463ba58a8453f5c55073", "tooltips": []}}, "22588a12c0db4067982e62ebbe7e6930": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2a81017413ca4fe789c2272a5831a069": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3cc1e83b91f34b289c7d52003f20a97a": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_8399e21b17634116861a5abaa9c0ccf7", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8d709ec9ec484944b1f9773748857f84", "value": 2}}, "41031579127f4a53b58957e601465083": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "42d5400d235d40b78190016ef0dabe11": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "state": {"_dom_classes": ["widget-interact"], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_12bf8b3c6ae8444a900474912589fdf1", "IPY_MODEL_9bb3600d38c04691b444ff375ad5e3f5"], "layout": "IPY_MODEL_41031579127f4a53b58957e601465083"}}, "57b94ac505d142769b79de2f1e5c1166": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8399e21b17634116861a5abaa9c0ccf7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8886001bc7c1463ba58a8453f5c55073": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_width": "", "description_width": "", "font_weight": ""}}, "8d709ec9ec484944b1f9773748857f84": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9bb3600d38c04691b444ff375ad5e3f5": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_4f8f81ab97b041a58a53c85a1ab97bd4", "msg_id": "", "outputs": [{"image/png": "iVBORw0KGgoAAAANSUhEUgAAAeoAAAFlCAYAAAAki6s3AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAm9n9QR+M8QEgGtgAAAABJRU5ErkJggg==\n", "metadata": {"tags": []}, "output_type": "display_data", "text/plain": "<Figure size 576x396 with 1 Axes>"}]}}, "9e338844e75b4e17be8483529f5f38fd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d5b6fce1763b4b54898ff3397b0f5bb0": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_2a81017413ca4fe789c2272a5831a069", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_57b94ac505d142769b79de2f1e5c1166", "value": 5}}}}}, "nbformat": 4, "nbformat_minor": 1}