# -*- coding = utf-8 -*-
# @Time : 2020/11/23 23:45
# <AUTHOR> none
# @File : binglirun3.py
# @Software : PyCharm


from bs4 import BeautifulSoup
import re
import urllib.request, urllib.error
import xlwt
import xlrd
import _sqlite3from bs4 import BeautifulSoup
import re


def main():
    baseurl = "http://10.1.1.176/pathwebrpt/index_z.asp?zyh="  # 此处需要添加病理的链接
    # 此处需要获取Excel的住院号内容

    data = xlrd.open_workbook(r'gan.xls')        #此处为读取的文件名！！！！！
    table = data.sheets()[0]
    # 创建一个空列表，存储Excel的数据

    tables = import_excel(table)
    # 验证Excel文件存储到列表中的数据
    idzong = []
    for i in tables:
        id = i.get("ID")
        idzong.append(id)

    datalist = getdata(baseurl, idzong)
    savepath = "binglishouji.xls"                 #此处为保存的文件名！！！
    savedata(datalist, savepath)


def getdata(baseurl, idzong):
    datalist = []

    for j in idzong:
        print(type(j))
        url = baseurl + j
        print(url)
        html = askurl(url)
        # print(html)

        #程序修改处
        soup = BeautifulSoup(html, "html.parser")      #beautifulsoup读取HTML


        data = []
        # item = []

        zhuyuanhao = soup.i

        zhuyuanhao = str(zhuyuanhao)

        data.append(zhuyuanhao)

        for l in range(1,8):
            urls = url + str("&xh=")
            #print(urls)
            urls = urls + str(l)
            #print(urls)

            htmls = askurl(urls)

            soup = BeautifulSoup(htmls, "html.parser")

            t_list = soup.find_all("textarea")
            # print(type(t_list))
            t_list = str(t_list)
            print(t_list)
            data.append(t_list)












        #soup = BeautifulSoup(html, "html.parser")


        #data = []


        #zhuyuanhao = soup.i

        #zhuyuanhao = str(zhuyuanhao)

        #data.append(zhuyuanhao)


        #t_list = soup.find_all("textarea")

        #t_list = str(t_list)
        #data.append(t_list)


        datalist.append(data)  # 总data放入datalist内            #此处需要测试datalist处于的位置
    #print(datalist)
    return datalist


def import_excel(table):
    tables = []

    for rown in range(table.nrows):
        array = {'name': '', 'ID': ''}

        array['name'] = table.cell_value(rown, 1)

        array['ID'] = table.cell_value(rown, 4)
        tables.append(array)
    return tables


def askurl(url):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Safari/537.36"
    }
    request = urllib.request.Request(url, headers=headers)
    html = ""

    try:
        response = urllib.request.urlopen(request)
        html = response.read().decode("GBK")
        # print(html)
    except urllib.error.URLError as e:
        if hasattr(e, "code"):
            print(e.code)
        if hasattr(e, "reason"):
            print(e.reason)

    return html


def savedata(datalist, savepath):
    print("save...")
    book = xlwt.Workbook(encoding="GBK", style_compression=0)  # 创建workbook对象
    sheet = book.add_sheet('sheet1', cell_overwrite_ok=True)  # 创建工作表

    for k in range(0, 4060):          #'''此处为文件的行数！！！！'''
        # print("第%d条" %k)

        data = datalist[k]
        for j in range(0, 8):
            print(j)
            sheet.write(k, j, data[j])
            #print(data[j])

    book.save(savepath)


if __name__ == "__main__":
    main()
    print("爬去完毕")