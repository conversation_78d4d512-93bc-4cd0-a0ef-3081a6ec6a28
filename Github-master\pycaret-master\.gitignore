venv/
.ipynb_checkpoints/
.pytest_cache/
__pycache__/
mlruns/
catboost_info/
/stacker.py
/stacknet.py
/cli.py
/app.py
/cli_app.py
/logo.png
/model
/Results.html
/build
/pycaret_nightly.egg-info
/pycaret.egg-info
/dist
.vscode/settings.json
*code-workspace
/logs.log
examples/logs.log
.log
tutorials/logs.log
/docs/build
.idea/
*.log
trained_models/
*.pkl
/mlruns.db
/mlruns.db-journal
mlflow_backend.py
demo4.py
dask*/
/.venv*
/.devcontainer
tmp/
nbtest1.ipynb
.DS_Store