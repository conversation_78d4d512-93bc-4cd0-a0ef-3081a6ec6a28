import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional, Tuple

class SinusoidalPositionEmbedding(nn.Module):
    """正弦位置编码"""
    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        
    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings

def get_num_groups(channels):
    """动态计算GroupNorm的组数"""
    for num_groups in [32, 16, 8, 4, 2, 1]:
        if channels % num_groups == 0:
            return num_groups
    return 1

class ResidualBlock(nn.Module):
    """残差块"""
    def __init__(self, in_channels, out_channels, time_emb_dim, dropout=0.1):
        super().__init__()
        self.time_mlp = nn.Sequential(
            nn.SiLU(),
            nn.Linear(time_emb_dim, out_channels)
        )

        self.block1 = nn.Sequential(
            nn.GroupNorm(get_num_groups(in_channels), in_channels),
            nn.SiLU(),
            nn.Conv2d(in_channels, out_channels, 3, padding=1)
        )

        self.block2 = nn.Sequential(
            nn.GroupNorm(get_num_groups(out_channels), out_channels),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Conv2d(out_channels, out_channels, 3, padding=1)
        )
        
        self.residual_conv = nn.Conv2d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()
        
    def forward(self, x, time_emb):
        h = self.block1(x)
        
        # 添加时间嵌入
        time_emb = self.time_mlp(time_emb)
        h = h + time_emb[:, :, None, None]
        
        h = self.block2(h)
        
        return h + self.residual_conv(x)

class AttentionBlock(nn.Module):
    """注意力块"""
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        self.norm = nn.GroupNorm(get_num_groups(channels), channels)
        self.q = nn.Conv2d(channels, channels, 1)
        self.k = nn.Conv2d(channels, channels, 1)
        self.v = nn.Conv2d(channels, channels, 1)
        self.proj_out = nn.Conv2d(channels, channels, 1)
        
    def forward(self, x):
        b, c, h, w = x.shape
        
        # 标准化
        x_norm = self.norm(x)
        
        # 计算注意力
        q = self.q(x_norm)
        k = self.k(x_norm)
        v = self.v(x_norm)
        
        # 重塑为序列
        q = q.reshape(b, c, h * w).transpose(1, 2)
        k = k.reshape(b, c, h * w).transpose(1, 2)
        v = v.reshape(b, c, h * w).transpose(1, 2)
        
        # 计算注意力权重
        attention = torch.softmax(torch.bmm(q, k.transpose(1, 2)) / math.sqrt(c), dim=-1)
        
        # 应用注意力
        out = torch.bmm(attention, v)
        out = out.transpose(1, 2).reshape(b, c, h, w)
        
        # 投影输出
        out = self.proj_out(out)
        
        return x + out

class DownSample(nn.Module):
    """下采样"""
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, in_channels, 3, stride=2, padding=1)
        
    def forward(self, x):
        return self.conv(x)

class UpSample(nn.Module):
    """上采样"""
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, in_channels, 3, padding=1)
        
    def forward(self, x):
        x = F.interpolate(x, scale_factor=2, mode='nearest')
        return self.conv(x)

class ConditionalUNet(nn.Module):
    """条件U-Net架构"""
    def __init__(self, in_channels=1, out_channels=1, time_emb_dim=128, 
                 base_channels=64, channel_multipliers=(1, 2, 4, 8)):
        super().__init__()
        
        self.time_emb_dim = time_emb_dim
        self.base_channels = base_channels
        
        # 时间嵌入
        self.time_embedding = SinusoidalPositionEmbedding(time_emb_dim)
        self.time_mlp = nn.Sequential(
            nn.Linear(time_emb_dim, time_emb_dim * 4),
            nn.SiLU(),
            nn.Linear(time_emb_dim * 4, time_emb_dim * 4)
        )
        
        # 条件编码器（用于AP图像）
        self.condition_encoder = nn.Sequential(
            nn.Conv2d(in_channels, base_channels, 3, padding=1),
            nn.GroupNorm(get_num_groups(base_channels), base_channels),
            nn.SiLU(),
            nn.Conv2d(base_channels, base_channels, 3, padding=1),
            nn.GroupNorm(get_num_groups(base_channels), base_channels),
            nn.SiLU()
        )
        
        # 输入投影
        self.input_projection = nn.Conv2d(in_channels, base_channels, 3, padding=1)
        
        # 下采样路径
        self.down_blocks = nn.ModuleList()
        self.down_samples = nn.ModuleList()
        
        channels = [base_channels * mult for mult in channel_multipliers]
        in_ch = base_channels
        
        for i, out_ch in enumerate(channels):
            # 残差块
            self.down_blocks.append(nn.ModuleList([
                ResidualBlock(in_ch, out_ch, time_emb_dim * 4),
                ResidualBlock(out_ch, out_ch, time_emb_dim * 4),
                AttentionBlock(out_ch) if i >= 2 else nn.Identity()
            ]))
            
            # 下采样（除了最后一层）
            if i < len(channels) - 1:
                self.down_samples.append(DownSample(out_ch))
            else:
                self.down_samples.append(nn.Identity())
                
            in_ch = out_ch
        
        # 中间层
        self.mid_block1 = ResidualBlock(channels[-1], channels[-1], time_emb_dim * 4)
        self.mid_attention = AttentionBlock(channels[-1])
        self.mid_block2 = ResidualBlock(channels[-1], channels[-1], time_emb_dim * 4)
        
        # 上采样路径
        self.up_samples = nn.ModuleList()
        self.up_blocks = nn.ModuleList()
        
        up_channels = list(reversed(channels))
        
        for i, out_ch in enumerate(up_channels):
            # 上采样（除了第一层）
            if i > 0:
                self.up_samples.append(UpSample(up_channels[i-1]))
            else:
                self.up_samples.append(nn.Identity())
            
            # 计算输入通道数（考虑跳跃连接）
            in_ch = up_channels[i-1] if i > 0 else up_channels[0]
            skip_ch = channels[len(channels) - 1 - i]  # 对应的下采样层通道数
            
            # 残差块
            self.up_blocks.append(nn.ModuleList([
                ResidualBlock(in_ch + skip_ch, out_ch, time_emb_dim * 4),
                ResidualBlock(out_ch, out_ch, time_emb_dim * 4),
                AttentionBlock(out_ch) if i < 2 else nn.Identity()
            ]))
        
        # 恢复channels顺序
        channels.reverse()
        
        # 输出层
        final_channels = up_channels[-1]
        self.output_projection = nn.Sequential(
            nn.GroupNorm(get_num_groups(final_channels), final_channels),
            nn.SiLU(),
            nn.Conv2d(final_channels, out_channels, 3, padding=1)
        )
        
    def forward(self, x, timesteps, condition=None):
        # 时间嵌入
        time_emb = self.time_embedding(timesteps)
        time_emb = self.time_mlp(time_emb)
        
        # 条件编码
        condition_features = None
        if condition is not None:
            condition_features = self.condition_encoder(condition)
        
        # 输入投影
        x = self.input_projection(x)
        
        # 如果有条件，添加到输入
        if condition_features is not None:
            x = x + condition_features
        
        # 下采样
        skip_connections = []
        
        for i, (blocks, downsample) in enumerate(zip(self.down_blocks, self.down_samples)):
            block1, block2, attention = blocks
            
            x = block1(x, time_emb)
            x = block2(x, time_emb)
            x = attention(x)
            
            skip_connections.append(x)
            x = downsample(x)
        
        # 中间层
        x = self.mid_block1(x, time_emb)
        x = self.mid_attention(x)
        x = self.mid_block2(x, time_emb)
        
        # 上采样
        for i, (upsample, blocks) in enumerate(zip(self.up_samples, self.up_blocks)):
            x = upsample(x)
            
            # 跳跃连接
            if i < len(skip_connections):
                skip = skip_connections[-(i+1)]
                x = torch.cat([x, skip], dim=1)
            
            block1, block2, attention = blocks
            x = block1(x, time_emb)
            x = block2(x, time_emb)
            x = attention(x)
        
        # 输出投影
        x = self.output_projection(x)
        
        return x

class DiffusionModel(nn.Module):
    """扩散模型"""
    def __init__(self, model: nn.Module, timesteps: int = 1000, beta_start: float = 1e-4, 
                 beta_end: float = 0.02, device='cuda'):
        super().__init__()
        
        self.model = model
        self.timesteps = timesteps
        self.device = device
        
        # 定义beta调度
        self.betas = torch.linspace(beta_start, beta_end, timesteps, device=device)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0)
        
        # 计算采样所需的量
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        
        # 计算后验分布参数
        self.posterior_variance = self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        
    def add_noise(self, x_start, t, noise=None):
        """添加噪声"""
        if noise is None:
            noise = torch.randn_like(x_start)

        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t]
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t]

        # 重塑张量以匹配图像维度 [batch_size, channels, height, width]
        sqrt_alphas_cumprod_t = sqrt_alphas_cumprod_t.view(-1, 1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = sqrt_one_minus_alphas_cumprod_t.view(-1, 1, 1, 1)

        return (sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise), noise
    
    def sample_timesteps(self, n):
        """采样时间步"""
        return torch.randint(low=1, high=self.timesteps, size=(n,), device=self.device)
    
    def forward(self, x, t, condition=None):
        """前向传播"""
        return self.model(x, t, condition)

if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    unet = ConditionalUNet(in_channels=1, out_channels=1)
    diffusion = DiffusionModel(unet, timesteps=1000, device=device)
    
    # 测试输入
    batch_size = 4
    x = torch.randn(batch_size, 1, 256, 256, device=device)
    condition = torch.randn(batch_size, 1, 256, 256, device=device)
    t = torch.randint(0, 1000, (batch_size,), device=device)
    
    # 前向传播
    output = diffusion(x, t, condition)
    print(f"输出形状: {output.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in diffusion.parameters())}")