#%%3D深度学习图像分类参数优化版，不带smote平衡数据
# 可以先使用raytune进行参数优化，然后使用优化后的参数进行训练
# swin3d，r3d_18，mvit_v1_b
# pip install monai
# pip install nibabel
# pip install SimpleITK
#安装monai依赖包
# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]"')
# !python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]"
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import monai
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
import torchvision
from torchvision.models.video import *
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, 
    RandRotate90d, RandAffined, RandGaussianNoised,Rand3DElasticd, Compose,
    EnsureTyped, RandFlipd,RandGaussianSmoothd, RandAdjustContrastd,RandZoomd, RandShiftIntensityd, NormalizeIntensityd
)
import torch.nn.functional as F
from sklearn.model_selection import train_test_split
from monai.data.image_reader import ITKReader

#resize成统一de 图像体积
resized_shape = (96, 96, 64) #(128, 128, 64) #其他模型可用
# resized_shape = (224, 224, 16) #对于mvit模型，要用这个体积
# resized_shape = (224, 224,64) #对于s3d模型，要用这个体积

#复杂版数据预处理
train_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(
        keys=["image"], 
        a_min=-200, 
        a_max=300, 
        b_min=0.0, 
        b_max=1.0, 
        clip=True
    ),
    RandRotate90d(keys=["image"], prob=0.3, spatial_axes=[0, 1]),    
    RandFlipd(keys=["image"], prob=0.5, spatial_axis=0),
    RandZoomd(keys=["image"], prob=0.3, min_zoom=0.9, max_zoom=1.1),
    RandShiftIntensityd(keys=["image"], offsets=0.1, prob=0.5),
    EnsureTyped(keys=["image"])
])

# 定义测试转换（通常不应用数据增强）
test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(
        keys=["image"], 
        a_min=-200, 
        a_max=300, 
        b_min=0.0, 
        b_max=1.0, 
        clip=True
    ),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'VETC' 

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label,"file_name": file_names})    
    return data

def split_dataset(data_dir, label_excel, test_size=0.3, random_state=42):
    all_files = TumorDataset(data_dir, label_excel)
    train_files, val_files = train_test_split(
        all_files, 
        test_size=test_size, 
        random_state=random_state,
        stratify=[f["label"] for f in all_files]
    )
    return train_files, val_files

# 使用示例，如果使用docker时，路径需要上传到docker，否则找不到
train_dir = '/root/autodl-tmp/HCC-tumor2/train/ap'
test_dir = '/root/autodl-tmp/HCC-tumor2/test/ap'
train_file_path = '/root/autodl-tmp/HCC-tumor2/traindata.xlsx'
test_file_path = '/root/autodl-tmp/HCC-tumor2/testdata.xlsx'


#加载数据
# train_files, test_files = split_dataset(train_dir, train_file_path)

train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:10]
test_files[:10]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# # 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=4)

#%%3d 模型迁移学习
#目前有很多模型 r3d_18、mc3_18, s3d ，mvit_v1_b;swin3d_b，swin3d_s，r2plus1d_18
model = swin3d_s(pretrained=True) 
# model = r3d_18(pretrained=True) 
# model = mvit_v1_b(pretrained=True)
# model = s3d(pretrained=True)
model

#调用预训练权重
# model = swin3d_s(pretrained=False) 
# model.head = nn.Linear(model.head.in_features,2) #swin3d_b
# # checkpoint = torch.load(r"H:\1.HCC-dataset\850HCC\all-HCC\swin3d_hbp_best_model.pth")  # 加载保存的权重文件
# checkpoint = torch.load("/root/autodl-tmp/HCC-tumor/ap_best_model.pth")
# model.load_state_dict(checkpoint)
# model

# #只训练全连接层，效果不好
for param in model.parameters():
    param.requires_grad = False

#解冻部分层方法1
# 下面的示例基于 mvit_v1_b 的结构
# for name, param in model.named_parameters():
#     if "head" in name:
#         param.requires_grad = True  # 仅解冻 head 部分
#     else:
#         param.requires_grad = False  # 其它部分保持冻结

#数据集中分类的类别数
num_classes = 2  #num_classes表示输出特征

# model.fc = nn.Linear(model.fc.in_features, num_classes) #r3d_18
model.head = nn.Linear(model.head.in_features, num_classes) #swin3d_b
# model.head[1] = nn.Linear(model.head[1].in_features, num_classes) #mvit_v1_b
# # model.classifier[1] = nn.Conv3d(1024, num_classes, kernel_size=(1, 1, 1), stride=(1, 1, 1)) # s3d
model

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)
print("Model successfully moved to", device)

#%% 自定义融合模型
# 方法1:ResNet3D + Swin3D特征融合,计算量大
class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        self.model = r3d_18(pretrained=pretrained)
        # 去掉最后一层全连接层
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FeatureExtractorSwin3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorSwin3D, self).__init__()
        self.model = swin3d_s(pretrained=pretrained)
        # self.model = mvit_v1_b(pretrained=pretrained) # 使用mvit_v1_b模型
        # 去掉最后一层分类层
        self.model.head = nn.Identity()
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FusionModel(nn.Module):
    def __init__(self, num_classes):
        super(FusionModel, self).__init__()
        self.feature_extractor_resnet3d = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d = FeatureExtractorSwin3D(pretrained=True)
        # 定义一个全连接层来结合两个特征向量
        self.fc = nn.Linear(512 + 768, num_classes)  # 512是ResNet3D的特征维度，768是Swin3D的特征维度
        
    def forward(self, x):
        features_resnet3d = self.feature_extractor_resnet3d(x)
        features_resnet3d = features_resnet3d.view(features_resnet3d.size(0), -1)
        
        features_swin3d = self.feature_extractor_swin3d(x)
        features_swin3d = features_swin3d.view(features_swin3d.size(0), -1)
        
        combined_features = torch.cat((features_resnet3d, features_swin3d), dim=1)        
        out = self.fc(combined_features)        
        return out


##方法2 resnet3d提取局部特征，mvit聚合特征
# 特征聚合更加智能,利用MViT的注意力机制,适合医学图像
# 计算效率更高,参数量更少,不容易过拟合
# class FeatureExtractorResNet3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorResNet3D, self).__init__()
#         self.model = r3d_18(pretrained=pretrained)
#         # 去掉最后一层全连接层
#         self.model = nn.Sequential(*list(self.model.children())[:-1])
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FeatureAggregatorMViT(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureAggregatorMViT, self).__init__()
#         self.model = mvit_v1_b(pretrained=pretrained)
        
#         # Spatial adaptation layer
#         self.spatial_adapt = nn.Sequential(
#             nn.Conv3d(512, 3, kernel_size=1, bias=False),
#             nn.BatchNorm3d(3),
#             nn.ReLU(inplace=True),
#             # Add spatial resizing to match MViT's expected input size
#             nn.AdaptiveAvgPool3d((16, 224, 224))  # MViT's default input size
#         )
        
#         # 去掉最后一层分类层
#         self.model.head = nn.Identity()
        
#         # 冻结部分参数
#         for param in list(self.model.parameters())[:-2]:
#             param.requires_grad = False
        
#     def forward(self, x):
#         # Adapt spatial dimensions and channels
#         x = self.spatial_adapt(x)
#         # Pass through MViT
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes):
#         super(FusionModel, self).__init__()
#         self.feature_extractor = FeatureExtractorResNet3D(pretrained=True)
#         self.feature_aggregator = FeatureAggregatorMViT(pretrained=True)
        
#         # Add batch normalization and dropout for better regularization
#         self.fc = nn.Sequential(
#             nn.Linear(768, 512),
#             nn.BatchNorm1d(512),
#             nn.ReLU(inplace=True),
#             nn.Dropout(0.4),
#             nn.Linear(512, 256),
#             nn.BatchNorm1d(256),
#             nn.ReLU(inplace=True),
#             nn.Dropout(0.3),
#             nn.Linear(256, num_classes)
#         )
        
#     def forward(self, x):
#         # Extract features
#         features = self.feature_extractor(x)  # [B, 512, T, H, W]
#         # Aggregate features
#         aggregated_features = self.feature_aggregator(features)  # [B, 768]
#         # Classification
#         out = self.fc(aggregated_features)
#         return out

# 分类的类别数
num_classes = 2
model = FusionModel(num_classes=num_classes)
model

# 将模型移动到GPU
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)
model.to(device)

#%%定义损失函数和优化器
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import LinearLR, CosineAnnealingLR, SequentialLR,_LRScheduler

# 定义Focal Loss
class FocalLoss(nn.Module):
    """
    Focal Loss: 用于处理类别不平衡问题
    """
    def __init__(self, alpha=0.25, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()

# 组合损失函数：Focal Loss + Label Smoothing
class CombinedLoss(nn.Module):
    """
    组合损失函数：Focal Loss + Label Smoothing
    """
    def __init__(self, alpha=0.25, gamma=2, smoothing=0.1):
        super(CombinedLoss, self).__init__()
        self.focal = FocalLoss(alpha=alpha, gamma=gamma)
        self.smoothing = smoothing
        
    def forward(self, inputs, targets):
        focal_loss = self.focal(inputs, targets)
        
        # Label Smoothing
        log_prob = F.log_softmax(inputs, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (inputs.size(-1) - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        smooth_loss = (-true_dist * log_prob).sum(dim=-1).mean()
        
        return focal_loss + smooth_loss

# 使用新的损失函数替换原来的criterion
criterion = CombinedLoss(alpha=0.75, gamma=2.0, smoothing=0.1)
# criterion = FocalLoss(alpha=0.8, gamma=2.0)


# criterion = nn.CrossEntropyLoss()
# from torch.optim import lr_scheduler
# # optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
# optimizer = torch.optim.Adam(model.head.parameters(), lr=0.0001) #swin3d_b
# exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

#1.Adam优化器配置
# initial_lr = 0.0005  # Adam通常可以使用稍大的学习率
# weight_decay = 8e-5  # Adam通常使用较小的权重衰减

# optimizer = optim.Adam(model.parameters(),
#                       lr=initial_lr,
#                       weight_decay=weight_decay,
#                       betas=(0.9, 0.999),  # Adam默认的beta值
#                       eps=1e-8)  # 数值稳定性参数

#2.AdamW优化器配置
initial_lr = 1e-4  # AdamW通常使用更小的学习率
weight_decay = 1e-4 # AdamW通常使用更大的权重衰减

optimizer = optim.AdamW(model.parameters(),
                       lr=initial_lr,
                       weight_decay=weight_decay,
                       betas=(0.9, 0.999))

# 3.SGD优化器和学习率调度器设置
# initial_lr = 1e-3  # 调整初始学习率
# weight_decay = 1e-4  # 调整权重衰减
# optimizer = optim.SGD(model.parameters(), 
#                      lr=initial_lr,
#                      momentum=0.9,
#                      weight_decay=weight_decay,
#                      nesterov=True)


#1.修改PolyLR调度器实现
class PolyLRScheduler(_LRScheduler):
    def __init__(self, optimizer, max_epochs, power=0.9, min_lr=1e-6):
        self.max_epochs = max_epochs
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer)

    def get_lr(self):
        return [max(self.min_lr, base_lr * (1 - self.last_epoch / self.max_epochs) ** self.power)
                for base_lr in self.base_lrs]

# 配置学习率调度器
num_epochs = 100
lr_scheduler = PolyLRScheduler(optimizer, max_epochs=num_epochs, power=0.9)

#2.使用带预热的余弦退火学习率调度器
# class WarmupPolyLR(_LRScheduler):
#     def __init__(self, optimizer, max_epochs, warmup_epochs=5, power=0.9, min_lr=1e-6):
#         self.max_epochs = max_epochs
#         self.warmup_epochs = warmup_epochs
#         self.power = power
#         self.min_lr = min_lr
#         super().__init__(optimizer)

#     def get_lr(self):
#         if self.last_epoch < self.warmup_epochs:
#             # Linear warmup
#             alpha = self.last_epoch / self.warmup_epochs
#             return [base_lr * alpha for base_lr in self.base_lrs]
#         else:
#             # Poly decay after warmup
#             current_epoch = self.last_epoch - self.warmup_epochs
#             total_epochs = self.max_epochs - self.warmup_epochs
#             return [max(self.min_lr, 
#                        base_lr * (1 - current_epoch / total_epochs) ** self.power)
#                     for base_lr in self.base_lrs]

# # 配置学习率调度器:
# lr_scheduler = WarmupPolyLR(
#     optimizer, 
#     max_epochs=num_epochs,
#     warmup_epochs=5,
#     power=0.9,
#     min_lr=1e-6
# )

#3.使用余弦退火调度器
# from torch.optim.lr_scheduler import CosineAnnealingLR
# num_epochs = 100
# lr_scheduler = CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-6)

# 添加早停机制
import copy

class EarlyStopping:
    def __init__(self, patience=15, min_delta=1e-4):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = None
        self.early_stop = False
        self.best_model = None

    def __call__(self, val_loss, model):
        if self.best_loss is None:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
        elif val_loss > self.best_loss - self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
            self.counter = 0

# 在训练循环中使用
early_stopping = EarlyStopping(patience=50, min_delta=1e-4)  # 增加耐心值和最小变化阈值

#%% 模型训练循环
num_epochs = 50

# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/root/autodl-tmp/HCC-tumor/swin3d_ap_best_model1.pth"

# 初始化记录列表
train_losses = []
train_accs = []
val_losses = []
val_accs = []
lr_history = []

for epoch in range(num_epochs):    
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # 记录当前学习率
    current_lr = optimizer.param_groups[0]['lr']
    lr_history.append(current_lr)
    print(f'Epoch [{epoch+1}/{num_epochs}], Current learning rate: {current_lr:.2e}')
    
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)
        images = images.expand(-1, 3, -1, -1, -1)
        labels = batch['label'].long().to(device)
        
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        
        optimizer.step()
        
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
    
    # 更新学习率
    lr_scheduler.step()    
   
    # 计算并保存训练指标
    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    train_losses.append(epoch_loss)
    train_accs.append(epoch_acc)
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
   
    # 验证循环
    model.eval()   
    correct = 0
    total = 0
    val_loss = 0.0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)

            y_pred = model(images)
            loss = criterion(y_pred, labels)
            val_loss += loss.item() * images.size(0)
            
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
    # 计算并保存验证指标
    val_loss = val_loss / len(test_dataset)
    val_acc = 100 * correct / total
    val_losses.append(val_loss)
    val_accs.append(val_acc)
   
    # # 使用早停机制，传入验证损失和模型
    # early_stopping(val_loss, model)
    
    # if early_stopping.early_stop:
    #     print("Early stopping triggered")       
    #     model.load_state_dict(early_stopping.best_model) # 加载最佳模型
    #     break
    
    print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.3f}%')
    
    # 如果是最佳模型，保存它
    if val_acc > best_accuracy:
        best_accuracy = val_acc
        torch.save(model.state_dict(), best_model_path)
        print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#%%先训练自己的模型，再微调预训练模型
for param in model.parameters():  #重新训练所有可训练参数
    param.requires_grad = True

len(list(model.parameters())) 

for param in list(model.parameters())[-5:]:  #最后4层重新训练
    print(param)
    param.requires_grad = True 

num_epochs = 50

from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)                #model.parameters()为整个可训练模型参数
optimizer = torch.optim.Adam(list(model.parameters())[-5:], lr=0.0001)   # 优化器要传入解冻层的参数
# lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)
lr_scheduler = PolyLRScheduler(optimizer, max_epochs=num_epochs, power=0.9)


# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/root/autodl-tmp/HCC-tumor/swin3d_ap_best_model1.pth"

# 初始化记录列表
train_losses = []
train_accs = []
val_losses = []
val_accs = []
lr_history = []

for epoch in range(num_epochs):    
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # 记录当前学习率
    current_lr = optimizer.param_groups[0]['lr']
    lr_history.append(current_lr)
    print(f'Epoch [{epoch+1}/{num_epochs}], Current learning rate: {current_lr:.2e}')
    
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)
        images = images.expand(-1, 3, -1, -1, -1)
        labels = batch['label'].long().to(device)
        
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        
        optimizer.step()
        
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
    
    # 更新学习率
    lr_scheduler.step()    
   
    # 计算并保存训练指标
    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    train_losses.append(epoch_loss)
    train_accs.append(epoch_acc)
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
   
    # 验证循环
    model.eval()   
    correct = 0
    total = 0
    val_loss = 0.0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)

            y_pred = model(images)
            loss = criterion(y_pred, labels)
            val_loss += loss.item() * images.size(0)
            
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
    # 计算并保存验证指标
    val_loss = val_loss / len(test_dataset)
    val_acc = 100 * correct / total
    val_losses.append(val_loss)
    val_accs.append(val_acc)
   
    # 使用早停机制，传入验证损失和模型
    # early_stopping(val_loss, model)
    
    # if early_stopping.early_stop:
    #     print("Early stopping triggered")       
    #     model.load_state_dict(early_stopping.best_model) # 加载最佳模型
    #     break
    
    print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.3f}%')
    
    # 如果是最佳模型，保存它
    if val_acc > best_accuracy:
        best_accuracy = val_acc
        torch.save(model.state_dict(), best_model_path)
        print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#%% 绘制loss曲线
import matplotlib.pyplot as plt
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.plot(train_losses, label='Train Loss')
plt.plot(val_losses, label='Val Loss')
plt.title('Loss History')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

# 绘制准确率acc曲线
plt.subplot(1, 3, 2)
plt.plot(train_accs, label='Train Acc')
plt.plot(val_accs, label='Val Acc')
plt.title('Accuracy History')
plt.xlabel('Epoch')
plt.ylabel('Accuracy (%)')
plt.legend()

# 绘制学习率lr曲线
plt.subplot(1, 3, 3)
plt.plot(lr_history)
plt.title('Learning Rate History')
plt.xlabel('Epoch')
plt.ylabel('Learning Rate')

plt.tight_layout()
# plt.savefig('training_curves.png')
plt.show()

#%%保存结果
import pandas as pd

# 创建训练历史数据框
history_df = pd.DataFrame({
    'Epoch': list(range(1, len(train_losses) + 1)),
    'Training Loss': train_losses,
    'Training Accuracy': train_accs,
    'Validation Loss': val_losses,
    'Validation Accuracy': val_accs,
    'Learning Rate': lr_history
})

# 保存为Excel文件
excel_path = '/root/autodl-tmp/HCC-tumor/training_history.xlsx'
history_df.to_excel(excel_path, index=False)
print(f"Training history saved to {excel_path}")

# 添加一个总结sheet
summary_data = {
    'Metric': ['Best Validation Accuracy', 'Final Training Loss', 'Final Training Accuracy',
               'Final Validation Loss', 'Final Validation Accuracy'],
    'Value': [best_accuracy, train_losses[-1], train_accs[-1],
              val_losses[-1], val_accs[-1]]
}
summary_df = pd.DataFrame(summary_data)

# 创建ExcelWriter对象来写入多个sheet
with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a') as writer:
    summary_df.to_excel(writer, sheet_name='Summary', index=False)


#%%3D深度学习图像分类参数优化版，带somote平衡数据
# swin3d，r3d_18，mvit_v1_b
# pip install monai
# pip install nibabel
# pip install SimpleITK
#安装monai依赖包
# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]"')
# !python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]"
# 在文件开头添加以下环境变量设置
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['VECLIB_MAXIMUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
# 新增以下设置
os.environ['OPENBLAS_MAIN_FREE'] = '1'
os.environ['GOTOBLAS_MAIN_FREE'] = '1'

import monai
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
import torchvision
from torchvision.models.video import *
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, 
    RandRotate90d, RandAffined, RandGaussianNoised,Rand3DElasticd, Compose,
    EnsureTyped, RandFlipd,RandGaussianSmoothd, RandAdjustContrastd,RandZoomd, RandShiftIntensityd, NormalizeIntensityd
)
import torch.nn.functional as F
from sklearn.model_selection import train_test_split
from monai.data.image_reader import ITKReader
from imblearn.over_sampling import SMOTE
from collections import Counter

#resize成统一de 图像体积
resized_shape = (96, 96, 64) #(128, 128, 64) #其他模型可用
# resized_shape = (224, 224, 16) #对于mvit模型，要用这个体积
# resized_shape = (224, 224,64) #对于s3d模型，要用这个体积

#复杂版数据预处理
train_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(
        keys=["image"], 
        a_min=-200, 
        a_max=300, 
        b_min=0.0, 
        b_max=1.0, 
        clip=True
    ),
    RandRotate90d(keys=["image"], prob=0.3, spatial_axes=[0, 1]),    
    RandFlipd(keys=["image"], prob=0.5, spatial_axis=0),
    RandZoomd(keys=["image"], prob=0.3, min_zoom=0.9, max_zoom=1.1),
    RandShiftIntensityd(keys=["image"], offsets=0.1, prob=0.5),
    EnsureTyped(keys=["image"])
])

# 定义测试转换（通常不应用数据增强）
test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(
        keys=["image"], 
        a_min=-200, 
        a_max=300, 
        b_min=0.0, 
        b_max=1.0, 
        clip=True
    ),
    EnsureTyped(keys=["image"])
])


# 定义标签
label_name = 'VETC' 

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label,"file_name": file_names})    
    return data

def split_dataset(data_dir, label_excel, test_size=0.3, random_state=42):
    all_files = TumorDataset(data_dir, label_excel)
    train_files, val_files = train_test_split(
        all_files, 
        test_size=test_size, 
        random_state=random_state,
        stratify=[f["label"] for f in all_files]
    )
    return train_files, val_files

# 使用示例，如果使用docker时，路径需要上传到docker，否则找不到
train_dir = '/root/autodl-tmp/HCC-tumor2/train/ap'
test_dir = '/root/autodl-tmp/HCC-tumor2/test/ap'
train_file_path = '/root/autodl-tmp/HCC-tumor2/traindata.xlsx'
test_file_path = '/root/autodl-tmp/HCC-tumor2/testdata.xlsx'

#加载数据
# train_files, test_files = split_dataset(train_dir, train_file_path)

train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:10]
test_files[:10]

# 使用SMOTE对训练数据进行平衡
def balance_dataset_with_smote(train_files):   
    # 提取特征和标签，首先加载所有图像数据
    all_features = []
    all_labels = []
    all_file_names = []
    
    # 使用简单的transforms来加载和预处理图像
    basic_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=resized_shape),
        ScaleIntensityRanged(
            keys=["image"], 
            a_min=-200, 
            a_max=300, 
            b_min=0.0, 
            b_max=1.0, 
            clip=True
        ),
        EnsureTyped(keys=["image"])
    ])
    
    print("Loading images for SMOTE processing...")
    for file_dict in train_files:
        # 加载和预处理图像
        processed = basic_transforms({"image": file_dict["image"]})
        img = processed["image"].numpy()
        
        # 将3D图像展平为1D特征向量
        flattened = img.flatten()
        all_features.append(flattened)
        all_labels.append(file_dict["label"])
        all_file_names.append(file_dict["file_name"])
    
    X = np.array(all_features)
    y = np.array(all_labels)
    
    # 打印原始类别分布
    print("Original dataset shape:", Counter(y))
    
    # 应用SMOTE
    print("Applying SMOTE...")
    smote = SMOTE(random_state=42)
    X_resampled, y_resampled = smote.fit_resample(X, y)
    
    # 打印平衡后的类别分布
    print("Balanced dataset shape:", Counter(y_resampled))
    
    # 重建数据集格式
    balanced_files = []
    for i, (features, label) in enumerate(zip(X_resampled, y_resampled)):
        if i < len(train_files):  # 原始样本
            balanced_files.append(train_files[i])
        else:  # SMOTE生成的新样本
            # 找到与当前标签相同的原始样本
            original_samples = [f for f in train_files if f["label"] == label]
            if original_samples:
                # 随机选择一个原始样本作为模板
                template = original_samples[np.random.randint(len(original_samples))]
                # 创建新的样本记录
                new_sample = {
                    "image": template["image"],  # 使用模板图像路径
                    "label": label,
                    "file_name": f"smote_generated_{i}"
                }
                balanced_files.append(new_sample)
    
    print(f"Dataset balanced: {len(balanced_files)} total samples")
    return balanced_files

# 应用SMOTE平衡训练数据
print("Balancing training dataset...")
balanced_train_files = balance_dataset_with_smote(train_files)

# 使用平衡后的数据创建数据集
train_dataset = Dataset(data=balanced_train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

#原始数据集
# train_dataset = Dataset(data=train_files, transform=train_transforms)
# test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=4)

#3d 模型迁移学习
#目前有很多模型 r3d_18、mc3_18, s3d ，mvit_v1_b;swin3d_b，swin3d_s，r2plus1d_18
model = swin3d_s(pretrained=True) 
# model = r3d_18(pretrained=True) 
# model = mvit_v1_b(pretrained=True)
# model = s3d(pretrained=True)
model

#调用预训练权重
# model = swin3d_s(pretrained=False) 
# model.head = nn.Linear(model.head.in_features,2) #swin3d_b
# # checkpoint = torch.load(r"H:\1.HCC-dataset\850HCC\all-HCC\swin3d_hbp_best_model.pth")  # 加载保存的权重文件
# checkpoint = torch.load("/root/autodl-tmp/HCC-tumor/ap_best_model.pth")
# model.load_state_dict(checkpoint)
# model

# #只训练全连接层，效果不好
for param in model.parameters():
    param.requires_grad = False

#解冻部分层方法1
# 下面的示例基于 mvit_v1_b 的结构
# for name, param in model.named_parameters():
#     if "head" in name:
#         param.requires_grad = True  # 仅解冻 head 部分
#     else:
#         param.requires_grad = False  # 其它部分保持冻结

#数据集中分类的类别数
num_classes = 2  #num_classes表示输出特征

# model.fc = nn.Linear(model.fc.in_features, num_classes) #r3d_18
model.head = nn.Linear(model.head.in_features, num_classes) #swin3d_b
# model.head[1] = nn.Linear(model.head[1].in_features, num_classes) #mvit_v1_b
# # model.classifier[1] = nn.Conv3d(1024, num_classes, kernel_size=(1, 1, 1), stride=(1, 1, 1)) # s3d
model

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)
print("Model successfully moved to", device)

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import LinearLR, CosineAnnealingLR, SequentialLR,_LRScheduler

# 定义Focal Loss
class FocalLoss(nn.Module):
    """
    Focal Loss: 用于处理类别不平衡问题
    """
    def __init__(self, alpha=0.25, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()

# 组合损失函数：Focal Loss + Label Smoothing
class CombinedLoss(nn.Module):
    """
    组合损失函数：Focal Loss + Label Smoothing
    """
    def __init__(self, alpha=0.25, gamma=2, smoothing=0.1):
        super(CombinedLoss, self).__init__()
        self.focal = FocalLoss(alpha=alpha, gamma=gamma)
        self.smoothing = smoothing
        
    def forward(self, inputs, targets):
        focal_loss = self.focal(inputs, targets)
        
        # Label Smoothing
        log_prob = F.log_softmax(inputs, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (inputs.size(-1) - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        smooth_loss = (-true_dist * log_prob).sum(dim=-1).mean()
        
        return focal_loss + smooth_loss

criterion = CombinedLoss(alpha=0.75, gamma=2.0, smoothing=0.1)

# 计算类别权重，使用权重的交叉熵损失
# def calculate_class_weights(train_files):
#     labels = [f["label"] for f in train_files]
#     class_counts = Counter(labels)
#     total = len(labels)
#     weights = {cls: total / (len(class_counts) * count) 
#               for cls, count in class_counts.items()}
#     return torch.FloatTensor([weights[i] for i in range(len(class_counts))])

# class_weights = calculate_class_weights(train_files).to(device)
# criterion = nn.CrossEntropyLoss(weight=class_weights)


# criterion = nn.CrossEntropyLoss()
# from torch.optim import lr_scheduler
# # optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
# optimizer = torch.optim.Adam(model.head.parameters(), lr=0.0001) #swin3d_b
# exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

#1.Adam优化器配置
# initial_lr = 0.0005  # Adam通常可以使用稍大的学习率
# weight_decay = 8e-5  # Adam通常使用较小的权重衰减

# optimizer = optim.Adam(model.parameters(),
#                       lr=initial_lr,
#                       weight_decay=weight_decay,
#                       betas=(0.9, 0.999),  # Adam默认的beta值
#                       eps=1e-8)  # 数值稳定性参数

#2.AdamW优化器配置，解决过拟合
# initial_lr = 1e-5  # 降低学习率
# weight_decay = 1e-3  # 增加权重衰减系数

# optimizer = optim.AdamW(model.parameters(),
#                        lr=initial_lr,
#                        weight_decay=weight_decay,  # 增加L2正则化强度
#                        betas=(0.9, 0.999),
#                        eps=1e-8)

# 3.SGD优化器设置
initial_lr = 1e-3  # 调整初始学习率
weight_decay = 1e-4  # 调整权重衰减
optimizer = optim.SGD(model.parameters(), 
                     lr=initial_lr,
                     momentum=0.9,
                     weight_decay=weight_decay,
                     nesterov=True)


#1.修改PolyLR调度器实现
class PolyLRScheduler(_LRScheduler):
    def __init__(self, optimizer, max_epochs, power=0.9, min_lr=1e-6):
        self.max_epochs = max_epochs
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer)

    def get_lr(self):
        return [max(self.min_lr, base_lr * (1 - self.last_epoch / self.max_epochs) ** self.power)
                for base_lr in self.base_lrs]

lr_scheduler = PolyLRScheduler(optimizer, max_epochs=200, power=0.9)# 配置学习率调度器

#2.使用带预热的余弦退火学习率调度器
# class WarmupPolyLR(_LRScheduler):
#     def __init__(self, optimizer, max_epochs, warmup_epochs=5, power=0.9, min_lr=1e-6):
#         self.max_epochs = max_epochs
#         self.warmup_epochs = warmup_epochs
#         self.power = power
#         self.min_lr = min_lr
#         super().__init__(optimizer)

#     def get_lr(self):
#         if self.last_epoch < self.warmup_epochs:
#             # Linear warmup
#             alpha = self.last_epoch / self.warmup_epochs
#             return [base_lr * alpha for base_lr in self.base_lrs]
#         else:
#             # Poly decay after warmup
#             current_epoch = self.last_epoch - self.warmup_epochs
#             total_epochs = self.max_epochs - self.warmup_epochs
#             return [max(self.min_lr, 
#                        base_lr * (1 - current_epoch / total_epochs) ** self.power)
#                     for base_lr in self.base_lrs]

# lr_scheduler = WarmupPolyLR(optimizer, max_epochs=200,warmup_epochs=5,power=0.9,min_lr=1e-6)

#3.使用余弦退火调度器
# from torch.optim.lr_scheduler import CosineAnnealingLR
# num_epochs = 100
# lr_scheduler= CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-6)

# 添加早停机制
import copy

class EarlyStopping:
    def __init__(self, patience=15, min_delta=1e-4):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = None
        self.early_stop = False
        self.best_model = None

    def __call__(self, val_loss, model):
        if self.best_loss is None:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
        elif val_loss > self.best_loss - self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
            self.counter = 0

# 在训练循环中使用
early_stopping = EarlyStopping(patience=20, min_delta=1e-4)  # 增加耐心值和最小变化阈值

#%% 模型训练循环
num_epochs = 100

# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/root/autodl-tmp/HCC-tumor/swin3d_ap_best_model5.pth"

# 初始化记录列表
train_losses = []
train_accs = []
val_losses = []
val_accs = []
lr_history = []

for epoch in range(num_epochs):    
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # 记录当前学习率
    current_lr = optimizer.param_groups[0]['lr']
    lr_history.append(current_lr)
    print(f'Epoch [{epoch+1}/{num_epochs}], Current learning rate: {current_lr:.2e}')
    
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)
        images = images.expand(-1, 3, -1, -1, -1)
        labels = batch['label'].long().to(device)
        
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        
        optimizer.step()
        
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
    
    # 更新学习率
    lr_scheduler.step()    
   
    # 计算并保存训练指标
    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    train_losses.append(epoch_loss)
    train_accs.append(epoch_acc)
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
   
    # 验证循环
    model.eval()   
    correct = 0
    total = 0
    val_loss = 0.0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)

            y_pred = model(images)
            loss = criterion(y_pred, labels)
            val_loss += loss.item() * images.size(0)
            
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
    # 计算并保存验证指标
    val_loss = val_loss / len(test_dataset)
    val_acc = 100 * correct / total
    val_losses.append(val_loss)
    val_accs.append(val_acc)
   
    # 使用早停机制，传入验证损失和模型
    # early_stopping(val_loss, model)
    
    # if early_stopping.early_stop:
    #     print("Early stopping triggered")       
    #     model.load_state_dict(early_stopping.best_model) # 加载最佳模型
    #     break
    
    print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.3f}%')
    
    # 如果是最佳模型，保存它
    if val_acc > best_accuracy:
        best_accuracy = val_acc
        torch.save(model.state_dict(), best_model_path)
        print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#%%先训练自己的模型，再微调预训练模型
for param in model.parameters():  #重新训练所有可训练参数
    param.requires_grad = True

len(list(model.parameters())) 

for param in list(model.parameters())[-5:]:  #最后4层重新训练
    print(param)
    param.requires_grad = True 

num_epochs = 20

from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)                #model.parameters()为整个可训练模型参数
optimizer = torch.optim.Adam(list(model.parameters())[-5:], lr=0.0001)   # 优化器要传入解冻层的参数
# lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)
lr_scheduler = PolyLRScheduler(optimizer, max_epochs=num_epochs, power=0.9)


# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/root/autodl-tmp/HCC-tumor/swin3d_ap_best_model1.pth"

# 初始化记录列表
train_losses = []
train_accs = []
val_losses = []
val_accs = []
lr_history = []

for epoch in range(num_epochs):    
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # 记录当前学习率
    current_lr = optimizer.param_groups[0]['lr']
    lr_history.append(current_lr)
    print(f'Epoch [{epoch+1}/{num_epochs}], Current learning rate: {current_lr:.2e}')
    
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)
        images = images.expand(-1, 3, -1, -1, -1)
        labels = batch['label'].long().to(device)
        
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        
        optimizer.step()
        
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
    
    # 更新学习率
    lr_scheduler.step()    
   
    # 计算并保存训练指标
    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    train_losses.append(epoch_loss)
    train_accs.append(epoch_acc)
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
   
    # 验证循环
    model.eval()   
    correct = 0
    total = 0
    val_loss = 0.0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)

            y_pred = model(images)
            loss = criterion(y_pred, labels)
            val_loss += loss.item() * images.size(0)
            
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
    # 计算并保存验证指标
    val_loss = val_loss / len(test_dataset)
    val_acc = 100 * correct / total
    val_losses.append(val_loss)
    val_accs.append(val_acc)
   
    # 使用早停机制，传入验证损失和模型
    # early_stopping(val_loss, model)
    
    # if early_stopping.early_stop:
    #     print("Early stopping triggered")       
    #     model.load_state_dict(early_stopping.best_model) # 加载最佳模型
    #     break
    
    print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.3f}%')
    
    # 如果是最佳模型，保存它
    if val_acc > best_accuracy:
        best_accuracy = val_acc
        torch.save(model.state_dict(), best_model_path)
        print(f'Best model saved with accuracy: {best_accuracy:.3f}%')


#%% 绘制loss曲线
import matplotlib.pyplot as plt
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.plot(train_losses, label='Train Loss')
plt.plot(val_losses, label='Val Loss')
plt.title('Loss History')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

# 绘制准确率acc曲线
plt.subplot(1, 3, 2)
plt.plot(train_accs, label='Train Acc')
plt.plot(val_accs, label='Val Acc')
plt.title('Accuracy History')
plt.xlabel('Epoch')
plt.ylabel('Accuracy (%)')
plt.legend()

# 绘制学习率lr曲线
plt.subplot(1, 3, 3)
plt.plot(lr_history)
plt.title('Learning Rate History')
plt.xlabel('Epoch')
plt.ylabel('Learning Rate')

plt.tight_layout()
# plt.savefig('training_curves.png')
plt.show()

#%%保存结果
import pandas as pd

# 创建训练历史数据框
history_df = pd.DataFrame({
    'Epoch': list(range(1, len(train_losses) + 1)),
    'Training Loss': train_losses,
    'Training Accuracy': train_accs,
    'Validation Loss': val_losses,
    'Validation Accuracy': val_accs,
    'Learning Rate': lr_history
})

# 保存为Excel文件
excel_path = '/root/autodl-tmp/HCC-tumor/training_history.xlsx'
history_df.to_excel(excel_path, index=False)
print(f"Training history saved to {excel_path}")

# 添加一个总结sheet
summary_data = {
    'Metric': ['Best Validation Accuracy', 'Final Training Loss', 'Final Training Accuracy',
               'Final Validation Loss', 'Final Validation Accuracy'],
    'Value': [best_accuracy, train_losses[-1], train_accs[-1],
              val_losses[-1], val_accs[-1]]
}
summary_df = pd.DataFrame(summary_data)

# 创建ExcelWriter对象来写入多个sheet
with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a') as writer:
    summary_df.to_excel(writer, sheet_name='Summary', index=False)

#%% Ray Tune进行超参数搜索，成功了
# pip install ray[tune]
# import os
# os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
# os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
# os.environ["RAY_USE_MULTIPROCESSING_CPU_COUNT"] = "1" 
# import ray
# from ray import tune, train
# from ray.tune.schedulers import ASHAScheduler
# from ray.tune.search.optuna import OptunaSearch
# import torch
# import torch.nn as nn
# import torch.optim as optim
# from torchvision.models.video import swin3d_s


# def validate_model(model, data_loader):
#     """验证模型性能"""
#     model.eval()
#     correct = 0
#     total = 0
    
#     with torch.no_grad():
#         for batch in data_loader:
#             images = batch['image'].to(device)
#             images = images.expand(-1, 3, -1, -1, -1)
#             labels = batch['label'].long().to(device)
            
#             outputs = model(images)
#             _, predicted = torch.max(outputs.data, 1)
#             total += labels.size(0)
#             correct += (predicted == labels).sum().item()
    
#     return 100.0 * correct / total

# def train_model(config):
#     """训练模型的函数"""
#     try:
#         # 模型初始化
#         model = swin3d_s(pretrained=True)
#         model.head = nn.Linear(model.head.in_features, 2)
#         model = model.to(device)
        
#         # 创建数据加载器 - 减小worker数量以提高稳定性
#         train_loader = DataLoader(
#             train_dataset, 
#             batch_size=int(config["batch_size"]), 
#             shuffle=True, 
#             num_workers=0  # 改为0以避免多进程问题
#         )
#         val_loader = DataLoader(
#             test_dataset,  
#             batch_size=int(config["batch_size"]),
#             num_workers=0  # 改为0以避免多进程问题
#         )
        
#         # 定义优化器和损失函数
#         optimizer = optim.Adam(
#             model.parameters(),
#             lr=config["lr"],
#             weight_decay=config["weight_decay"]
#         )
#         criterion = nn.CrossEntropyLoss()
        
#         # 添加异常处理和进度报告
#         for epoch in range(5):
#             model.train()
#             running_loss = 0.0
            
#             for batch in train_loader:
#                 images = batch['image'].to(device)
#                 images = images.expand(-1, 3, -1, -1, -1)
#                 labels = batch['label'].long().to(device)
                
#                 optimizer.zero_grad()
#                 outputs = model(images)
#                 loss = criterion(outputs, labels)
#                 loss.backward()
#                 optimizer.step()
                
#                 running_loss += loss.item()
            
#             # 计算验证集准确率
#             val_accuracy = validate_model(model, val_loader)
            
#             # 报告结果给Ray Tune                      
#             train.report(
#                 metrics={
#                     "loss": running_loss / len(train_loader),
#                     "accuracy": val_accuracy,
#                     "epoch": epoch
#                 }
#             )
            
#             # 主动清理GPU内存
#             torch.cuda.empty_cache()
            
#     except Exception as e:
#         print(f"Error in training: {str(e)}")
#         raise e

# def main():
#     ray.init(ignore_reinit_error=True)
    
#     # 定义搜索空间
#     search_space = {
#         "lr": tune.loguniform(1e-4, 1e-3),
#         "weight_decay": tune.loguniform(1e-5, 1e-5),
#         "batch_size": tune.choice([8,16])  # 简化batch size选择
#     }

#     # 配置调度器
#     scheduler = ASHAScheduler(
#         max_t=3,          # 减少最大epochs
#         grace_period=1,    
#         reduction_factor=2,
#         metric="accuracy",
#         mode="max"
#     )

#     # 运行优化
#     analysis = tune.run(
#         train_model,
#         config=search_space,
#         num_samples=3,    # 减少试验次数
#         scheduler=scheduler,
#         search_alg=OptunaSearch(metric="accuracy", mode="max"),
#         resources_per_trial={
#             "cpu": 2,     # 减少CPU使用
#             "gpu": 0.6    # 确保每个trial有足够的GPU内存
#         },
#         name="swin3d_tune",
#         local_dir="./ray_results",
#         resume=False,
#         verbose=3,        # 增加日志详细程度
#         fail_fast=True    # 快速失败以便调试
#     )

#     # 获取最佳trial结果
#     best_trial = analysis.get_best_trial("accuracy", mode="max", scope="all")
#     print("Best trial config: {}".format(best_trial.config))
#     print("Best trial final validation accuracy: {}".format(
#         best_trial.last_result["accuracy"]))

#     # 保存最佳参数
#     best_config = best_trial.config
#     print("Best hyperparameters found:", best_config)
    
#     # 可以选择将最佳参数保存到文件
#     import json
#     with open('best_hyperparameters.json', 'w') as f:
#         json.dump(best_config, f)

# if __name__ == "__main__":
#     try:
#         main()
#     except Exception as e:
#         print(f"Error in main: {str(e)}")
#     finally:
#         ray.shutdown()

#%% Ray Tune进行超参数搜索，完整版
# pip install ray[tune]
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["RAY_USE_MULTIPROCESSING_CPU_COUNT"] = "1" 
import ray
from ray import tune, train
from ray.tune.schedulers import ASHAScheduler
from ray.tune.search.optuna import OptunaSearch
import torch
import torch.nn as nn
import torch.optim as optim
from torchvision.models.video import swin3d_s

# 定义多个损失函数
class FocalLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()

class CombinedLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2, smoothing=0.1):
        super(CombinedLoss, self).__init__()
        self.focal = FocalLoss(alpha=alpha, gamma=gamma)
        self.smoothing = smoothing
        
    def forward(self, inputs, targets):
        focal_loss = self.focal(inputs, targets)
        
        # Label Smoothing
        log_prob = F.log_softmax(inputs, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (inputs.size(-1) - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        smooth_loss = (-true_dist * log_prob).sum(dim=-1).mean()
        return focal_loss + smooth_loss

def get_weighted_cross_entropy(train_files):
    """获取带权重的交叉熵损失"""
    labels = [f["label"] for f in train_files]
    class_counts = Counter(labels)
    total = len(labels)
    weights = {cls: total / (len(class_counts) * count) 
              for cls, count in class_counts.items()}
    class_weights = torch.FloatTensor([weights[i] for i in range(len(class_counts))])
    return nn.CrossEntropyLoss(weight=class_weights)

# 定义优化器工厂函数
def get_optimizer(name, model_params, **kwargs):
    optimizers = {
        'adam': lambda p, lr, wd: optim.Adam(
            p, lr=lr, weight_decay=wd, betas=(0.9, 0.999), eps=1e-8
        ),
        'adamw': lambda p, lr, wd: optim.AdamW(
            p, lr=lr, weight_decay=wd, betas=(0.9, 0.999), eps=1e-8
        ),
        'sgd': lambda p, lr, wd: optim.SGD(
            p, lr=lr, momentum=0.9, weight_decay=wd, nesterov=True
        )
    }
    
    if name not in optimizers:
        raise ValueError(f"Unsupported optimizer: {name}")
    
    return optimizers[name](
        model_params,
        kwargs.get('lr', 0.001),
        kwargs.get('weight_decay', 1e-4)
    )

# 定义学习率调度器
class PolyLRScheduler(_LRScheduler):
    def __init__(self, optimizer, max_epochs, power=0.9, min_lr=1e-6):
        self.max_epochs = max_epochs
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer)

    def get_lr(self):
        return [max(self.min_lr, base_lr * (1 - self.last_epoch / self.max_epochs) ** self.power)
                for base_lr in self.base_lrs]

class WarmupPolyLR(_LRScheduler):
    def __init__(self, optimizer, max_epochs, warmup_epochs=5, power=0.9, min_lr=1e-6):
        self.max_epochs = max_epochs
        self.warmup_epochs = warmup_epochs
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer)

    def get_lr(self):
        if self.last_epoch < self.warmup_epochs:
            alpha = self.last_epoch / self.warmup_epochs
            return [base_lr * alpha for base_lr in self.base_lrs]
        else:
            current_epoch = self.last_epoch - self.warmup_epochs
            total_epochs = self.max_epochs - self.warmup_epochs
            return [max(self.min_lr, 
                       base_lr * (1 - current_epoch / total_epochs) ** self.power)
                    for base_lr in self.base_lrs]

def get_scheduler(name, optimizer, **kwargs):
    schedulers = {
        'poly': lambda: PolyLRScheduler(
            optimizer,
            max_epochs=kwargs.get('max_epochs', 100),
            power=kwargs.get('power', 0.9),
            min_lr=kwargs.get('min_lr', 1e-6)
        ),
        'warmup_poly': lambda: WarmupPolyLR(
            optimizer,
            max_epochs=kwargs.get('max_epochs', 100),
            warmup_epochs=kwargs.get('warmup_epochs', 5),
            power=kwargs.get('power', 0.9),
            min_lr=kwargs.get('min_lr', 1e-6)
        ),
        'cosine': lambda: CosineAnnealingLR(
            optimizer,
            T_max=kwargs.get('max_epochs', 100),
            eta_min=kwargs.get('min_lr', 1e-6)
        )
    }
    
    if name not in schedulers:
        raise ValueError(f"Unsupported scheduler: {name}")
    
    return schedulers[name]()

# 训练配置类
class TrainingConfig:
    def __init__(self, **kwargs):
        self.loss_type = kwargs.get('loss_type', 'combined')
        self.optimizer_type = kwargs.get('optimizer_type', 'adamw')
        self.scheduler_type = kwargs.get('scheduler_type', 'poly')
        self.learning_rate = kwargs.get('learning_rate', 1e-3)
        self.weight_decay = kwargs.get('weight_decay', 1e-4)
        self.num_epochs = kwargs.get('num_epochs',20)
        self.batch_size = kwargs.get('batch_size', 8)
        self.focal_alpha = kwargs.get('focal_alpha', 0.75)
        self.focal_gamma = kwargs.get('focal_gamma', 2.0)
        self.label_smoothing = kwargs.get('label_smoothing', 0.1)

def setup_training(config, model, train_files):
    """设置训练组件"""
    # 设置损失函数
    if config.loss_type == 'combined':
        criterion = CombinedLoss(
            alpha=config.focal_alpha,
            gamma=config.focal_gamma,
            smoothing=config.label_smoothing
        )
    elif config.loss_type == 'focal':
        criterion = FocalLoss(alpha=config.focal_alpha, gamma=config.focal_gamma)
    elif config.loss_type == 'weighted_ce':
        criterion = get_weighted_cross_entropy(train_files)
    else:
        criterion = nn.CrossEntropyLoss()

    # 设置优化器
    optimizer = get_optimizer(
        config.optimizer_type,
        model.parameters(),
        lr=config.learning_rate,
        weight_decay=config.weight_decay
    )

    # 设置学习率调度器
    scheduler = get_scheduler(
        config.scheduler_type,
        optimizer,
        max_epochs=config.num_epochs
    )

    return criterion, optimizer, scheduler

def train_model_with_config(model, train_loader, test_loader, config, device):
    """使用指定配置训练模型"""
    criterion, optimizer, scheduler = setup_training(config, model, train_files)
    
    best_accuracy = 0.0
    best_model_state = None
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    lr_history = []
    
    for epoch in range(config.num_epochs):
        # 训练阶段
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        current_lr = optimizer.param_groups[0]['lr']
        lr_history.append(current_lr)
        print(f'Epoch [{epoch+1}/{config.num_epochs}], LR: {current_lr:.2e}')
        
        for batch in train_loader:
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            optimizer.step()
            
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        # 更新学习率
        scheduler.step()
        
        # 计算训练指标
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100 * correct / total
        train_losses.append(epoch_loss)
        train_accs.append(epoch_acc)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch in test_loader:
                images = batch['image'].to(device)
                images = images.expand(-1, 3, -1, -1, -1)
                labels = batch['label'].long().to(device)
                
                outputs = model(images)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        # 计算验证指标
        val_loss = val_loss / len(test_loader)
        val_acc = 100 * correct / total
        val_losses.append(val_loss)
        val_accs.append(val_acc)
        
        print(f'Epoch [{epoch+1}/{config.num_epochs}]:')
        print(f'Train Loss: {epoch_loss:.4f}, Train Acc: {epoch_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        # 保存最佳模型
        if val_acc > best_accuracy:
            best_accuracy = val_acc
            best_model_state = model.state_dict().copy()
    
    return {
        'best_accuracy': best_accuracy,
        'best_model_state': best_model_state,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'train_accs': train_accs,
        'val_accs': val_accs,
        'lr_history': lr_history
    }

# 定义要尝试的不同配置
configurations = [
    TrainingConfig(
        loss_type='combined',
        optimizer_type='adamw',
        scheduler_type='poly',
        learning_rate=1e-3,
        weight_decay=1e-4
    ),
    TrainingConfig(
        loss_type='focal',
        optimizer_type='sgd',
        scheduler_type='warmup_poly',
        learning_rate=1e-2,
        weight_decay=1e-4
    ),
    TrainingConfig(
        loss_type='weighted_ce',
        optimizer_type='adam',
        scheduler_type='cosine',
        learning_rate=5e-4,
        weight_decay=1e-5
    )
]

# 执行多次训练尝试
results = []
for i, config in enumerate(configurations):
    print(f"\nTraining with configuration {i+1}/{len(configurations)}")
    print(f"Configuration: {config.__dict__}")
    
    # 重新初始化模型
    model = swin3d_s(pretrained=True)
    model.head = nn.Linear(model.head.in_features, 2)
    model = model.to(device)
    
    # 训练模型
    result = train_model_with_config(model, train_loader, test_loader, config, device)
    results.append({
        'config': config.__dict__,
        'best_accuracy': result['best_accuracy'],
        'train_losses': result['train_losses'],
        'val_losses': result['val_losses'],
        'train_accs': result['train_accs'],
        'val_accs': result['val_accs'],
        'lr_history': result['lr_history']
    })   
   

# 分析结果
for i, result in enumerate(results):
    print(f"\nConfiguration {i+1} Results:")
    print(f"Best Validation Accuracy: {result['best_accuracy']:.2f}%")
    print("Configuration:", result['config'])

# 可视化不同配置的训练过程
import matplotlib.pyplot as plt
plt.figure(figsize=(15, 10))

# 绘制损失曲线
plt.subplot(2, 2, 1)
for i, result in enumerate(results):
    plt.plot(result['train_losses'], label=f'Config {i+1} Train')
    plt.plot(result['val_losses'], label=f'Config {i+1} Val')
plt.title('Loss History')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

# 绘制准确率曲线
plt.subplot(2, 2, 2)
for i, result in enumerate(results):
    plt.plot(result['train_accs'], label=f'Config {i+1} Train')
    plt.plot(result['val_accs'], label=f'Config {i+1} Val')
plt.title('Accuracy History')
plt.xlabel('Epoch')
plt.ylabel('Accuracy (%)')
plt.legend()

# 绘制学习率曲线
plt.subplot(2, 2, 3)
for i, result in enumerate(results):
    plt.plot(result['lr_history'], label=f'Config {i+1}')
plt.title('Learning Rate History')
plt.xlabel('Epoch')
plt.ylabel('Learning Rate')
plt.legend()

plt.tight_layout()
plt.show()


# Configuration 1 Results:
# Best Validation Accuracy: 53.48%
# Configuration: {'loss_type': 'combined', 'optimizer_type': 'adamw', 
# 'scheduler_type': 'poly', 'learning_rate': 0.001, 'weight_decay': 0.0001, 
# 'num_epochs': 20, 'batch_size': 8, 'focal_alpha': 0.75, 'focal_gamma': 2.0, 
# 'label_smoothing': 0.1}


# %% 带知识蒸馏模型,未成功
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models.video import r3d_18, mvit_v1_b, swin3d_s

class FeatureDistillationLoss(nn.Module):
    """特征蒸馏损失"""
    def __init__(self, temp=4.0):
        super().__init__()
        self.temp = temp
        
    def forward(self, student_features, teacher_features):
        # 将特征转换为概率分布
        student_features = F.normalize(student_features, dim=-1)
        teacher_features = F.normalize(teacher_features, dim=-1)
        
        # 计算特征相似度
        similarity = torch.matmul(student_features, teacher_features.t()) / self.temp
        # 计算 KL 散度损失
        loss = F.kl_div(
            F.log_softmax(similarity, dim=-1),
            F.softmax(similarity, dim=-1),
            reduction='batchmean'
        )
        return loss

class OutputDistillationLoss(nn.Module):
    """输出蒸馏损失"""
    def __init__(self, temp=4.0):
        super().__init__()
        self.temp = temp
        
    def forward(self, student_logits, teacher_logits):
        soft_targets = F.softmax(teacher_logits / self.temp, dim=-1)
        student_log_probs = F.log_softmax(student_logits / self.temp, dim=-1)
        loss = F.kl_div(student_log_probs, soft_targets, reduction='batchmean') * (self.temp ** 2)
        return loss

class TeacherModel(nn.Module):
    """教师模型 - 使用Swin3D作为基础模型"""
    def __init__(self, num_classes):
        super().__init__()
        self.model = swin3d_s(pretrained=True)
        self.model.head = nn.Linear(self.model.head.in_features, num_classes)
        
        # 冻结教师模型参数
        for param in self.model.parameters():
            param.requires_grad = False
            
    def forward(self, x, return_features=False):
        # 简化版本 - 只返回最终特征
        x = self.model.patch_embed(x)
        x = self.model.pos_drop(x)
        
        # 获取中间特征
        feature1 = self.model.patch_embed(x)
        feature2 = self.model.norm(feature1)
        
        # 最终输出
        x = self.model.avgpool(feature2)
        x = x.flatten(1)
        outputs = self.model.head(x)
        
        if return_features:
            return outputs, [feature1, feature2]
        return outputs

class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        # 使用预训练的 r3d_18 模型
        self.model = r3d_18(pretrained=pretrained)
        # 移除最后的全连接层
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        # 冻结部分参数
        for param in list(self.model.parameters())[:-2]:  # 保持最后几层可训练
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FeatureAggregatorMViT(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureAggregatorMViT, self).__init__()
        # 加载预训练的 MViT 模型
        self.model = mvit_v1_b(pretrained=pretrained)
        
        # 修改模型结构以用于特征聚合
        # 移除原始分类头
        self.model.head = nn.Identity()
        
        # 添加特征转换层
        self.feature_transform = nn.Sequential(
            nn.Linear(768, 768),  # MViT 的特征维度是 768
            nn.LayerNorm(768),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 冻结部分参数
        self._freeze_layers()
        
    def _freeze_layers(self):
        # 冻结除最后几个 transformer blocks 之外的所有层
        for name, param in self.model.named_parameters():
            if 'blocks' in name:
                block_num = int(name.split('.')[1])
                if block_num < 12:  # 假设总共有16个blocks，只训练最后4个
                    param.requires_grad = False
            elif 'head' not in name:  # 不冻结新添加的头部
                param.requires_grad = False
                
    def forward(self, x):
        # 通过 MViT 主干网络
        x = self.model(x)
        # 转换特征
        x = self.feature_transform(x)
        return x


class DistillationFusionModel(nn.Module):
    """带知识蒸馏的学生模型"""
    def __init__(self, num_classes):
        super().__init__()
        # 特征提取器
        self.feature_extractor = FeatureExtractorResNet3D(pretrained=True)
        # 特征聚合器
        self.feature_aggregator = FeatureAggregatorMViT(pretrained=True)
        # 分类器
        self.fc = nn.Sequential(
            nn.Linear(768, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
        
        # 添加额外的特征转换层，用于对齐教师特征
        self.feature_transforms = nn.ModuleList([
            nn.Conv3d(512, 768, 1),  # 转换ResNet特征维度以匹配教师特征
            nn.Conv3d(96, 768, 1)    # 转换MViT特征维度以匹配教师特征
        ])
        
    def forward(self, x, return_features=False):
        # 提取特征
        features = self.feature_extractor(x)
        
        # 转换特征用于蒸馏
        distill_features = []
        distill_features.append(self.feature_transforms[0](features))
        
        # 聚合特征
        aggregated = self.feature_aggregator(features)
        distill_features.append(self.feature_transforms[1](aggregated))
        
        # 分类
        out = self.fc(aggregated)
        
        if return_features:
            return distill_features, out
        return out

def train_with_distillation(student_model, teacher_model, train_loader, optimizer, 
                           num_epochs, device, alpha=0.5, temp=4.0):
    """训练函数"""
    # 损失函数
    ce_criterion = nn.CrossEntropyLoss()
    feature_distill_criterion = FeatureDistillationLoss(temp=temp)
    output_distill_criterion = OutputDistillationLoss(temp=temp)
    
    for epoch in range(num_epochs):
        student_model.train()
        teacher_model.eval()
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)
            
            # 前向传播
            with torch.no_grad():
                teacher_features, teacher_outputs = teacher_model(images)
            
            student_features, student_outputs = student_model(images, return_features=True)
            
            # 计算损失
            # 1. 分类损失
            ce_loss = ce_criterion(student_outputs, labels)
            
            # 2. 特征蒸馏损失
            feature_loss = sum(feature_distill_criterion(sf, tf) 
                             for sf, tf in zip(student_features, teacher_features))
            
            # 3. 输出蒸馏损失
            output_loss = output_distill_criterion(student_outputs, teacher_outputs)
            
            # 总损失
            total_loss = ce_loss + alpha * (feature_loss + output_loss)
            
            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
            
            if batch_idx % 10 == 0:
                print(f'Epoch: {epoch}, Batch: {batch_idx}, '
                      f'Loss: {total_loss.item():.4f}, '
                      f'CE Loss: {ce_loss.item():.4f}, '
                      f'Distill Loss: {(feature_loss + output_loss).item():.4f}')

# 定义训练函数
def train_with_distillation(student_model, teacher_model, train_loader, optimizer, 
                            num_epochs, device, alpha=0.5, temp=4.0):
    # 设置损失函数
    ce_criterion = nn.CrossEntropyLoss()
    feature_distill_criterion = FeatureDistillationLoss(temp=temp)
    output_distill_criterion = OutputDistillationLoss(temp=temp)
    
    # 训练循环
    for epoch in range(num_epochs):
        student_model.train()
        teacher_model.eval()  # 教师模型始终处于评估模式
        
        running_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(device)
            images = images.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)
            
            # 获取教师模型的输出和特征
            with torch.no_grad():
                teacher_outputs, teacher_features = teacher_model(images, return_features=True)
            
            # 获取学生模型的输出和特征
            student_outputs, student_features = student_model(images, return_features=True)
            
            # 1. 分类损失
            ce_loss = ce_criterion(student_outputs, labels)
            
            # 2. 特征蒸馏损失
            feature_loss = sum(feature_distill_criterion(sf, tf) 
                                for sf, tf in zip(student_features, teacher_features))
            
            # 3. 输出蒸馏损失
            output_loss = output_distill_criterion(student_outputs, teacher_outputs)
            
            # 总损失
            total_loss = ce_loss + alpha * (feature_loss + output_loss)
            
            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
            
            # 计算准确率
            _, predicted = torch.max(student_outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            running_loss += total_loss.item()
            
            if batch_idx % 10 == 0:
                print(f'Epoch: {epoch+1}/{num_epochs}, Batch: {batch_idx}, '
                        f'Loss: {total_loss.item():.4f}, '
                        f'CE Loss: {ce_loss.item():.4f}, '
                        f'Distill Loss: {(feature_loss + output_loss).item():.4f}')
        
        # 每个epoch结束后打印统计信息
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100 * correct / total
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'Average Loss: {epoch_loss:.4f}')
        print(f'Accuracy: {epoch_acc:.2f}%')            
        
        # 验证步骤
        model.eval()
        val_correct = 0
        val_total = 0
        val_loss = 0.0
        
        with torch.no_grad():
            for val_batch in test_loader:
                val_images = val_batch['image'].to(device)
                val_images = val_images.expand(-1, 3, -1, -1, -1)
                val_labels = val_batch['label'].long().to(device)
                
                # 获取教师模型和学生模型的输出
                teacher_outputs, teacher_features = teacher_model(val_images, return_features=True)
                student_outputs, student_features = student_model(val_images, return_features=True)
                
                # 计算验证损失
                val_ce_loss = ce_criterion(student_outputs, val_labels)
                val_feature_loss = sum(feature_distill_criterion(sf, tf) 
                                        for sf, tf in zip(student_features, teacher_features))
                val_output_loss = output_distill_criterion(student_outputs, teacher_outputs)
                val_total_loss = val_ce_loss + alpha * (val_feature_loss + val_output_loss)
                
                val_loss += val_total_loss.item()
                
                # 计算准确率
                _, val_predicted = torch.max(student_outputs.data, 1)
                val_total += val_labels.size(0)
                val_correct += (val_predicted == val_labels).sum().item()
        
        # 计算平均验证损失和准确率
        avg_val_loss = val_loss / len(test_loader)
        val_accuracy = 100 * val_correct / val_total
        
        print(f'Validation Loss: {avg_val_loss:.4f}')
        print(f'Validation Accuracy: {val_accuracy:.2f}%')
        
        # 保存最佳模型
        if val_accuracy > best_accuracy:
            best_accuracy = val_accuracy
            torch.save({
                'epoch': epoch,
                'student_model_state_dict': student_model.state_dict(),
                'teacher_model_state_dict': teacher_model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_accuracy': best_accuracy,
            }, best_model_path)
            print(f'Best model saved with accuracy: {best_accuracy:.2f}%')
        
        # 更新学习率
        scheduler.step()
        
        # 记录训练历史
        history = {
            'train_loss': epoch_loss,
            'train_acc': epoch_acc,
            'val_loss': avg_val_loss,
            'val_acc': val_accuracy,
            'lr': optimizer.param_groups[0]['lr']
        }
        
        # 早停检查
        early_stopping(avg_val_loss, student_model)
        if early_stopping.early_stop:
            print("Early stopping triggered")
            break

# 使用示例
def main():
    num_classes = 2
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 初始化模型
    teacher_model = TeacherModel(num_classes).to(device)
    student_model = DistillationFusionModel(num_classes).to(device)
    
    # 加载教师模型预训练权重（如果有）
    # teacher_checkpoint = torch.load('teacher_model.pth')
    # teacher_model.load_state_dict(teacher_checkpoint)
    
    # 优化器
    optimizer = torch.optim.AdamW(student_model.parameters(), lr=1e-4)
    
    # 训练
    train_with_distillation(
        student_model=student_model,
        teacher_model=teacher_model,
        train_loader=train_loader,
        optimizer=optimizer,
        num_epochs=50,
        device=device,
        alpha=0.5,  # 蒸馏损失权重
        temp=4.0    # 温度参数
    )
    

if __name__ == "__main__":
    main()
# %%
