{"cells": [{"cell_type": "markdown", "id": "fe72d689", "metadata": {}, "source": ["# Minimal example on how to transform a mesh of points, using TransformixFilter with TranslationTransform"]}, {"cell_type": "markdown", "id": "2ef87562", "metadata": {}, "source": ["This example shows how to transform a mesh, using `itk.TransformixFilter` with `itk.TranslationTransform`.\n", "\n", "Start by importing packages, defining a little print helper function, and creating an TransformixFilter object:"]}, {"cell_type": "code", "execution_count": 1, "id": "852e83ee", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating a TransformixFilter (might take a while, please wait)...\n", "\n"]}], "source": ["import itk\n", "\n", "def print_mesh(mesh):\n", "    for i in range(mesh.GetNumberOfPoints()):\n", "        print(mesh.GetPoint(i), ' ', end='')\n", "    print('\\n')    \n", "    \n", "print('Creating a TransformixFilter (might take a while, please wait)...\\n')\n", "ImageType = itk.Image[itk.F, 2]\n", "transformix_filter = itk.TransformixFilter[ImageType].New()\n"]}, {"cell_type": "markdown", "id": "b09bb751", "metadata": {}, "source": ["Create the example input: a mesh of three points, a small translation (0.03125, 0.0625), a \"dummy\" (1x1) image, and a rather trivial parameter map."]}, {"cell_type": "code", "execution_count": 2, "id": "539fc713", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Mesh:\n", "itkPointF2 ([0, 0])  itkPointF2 ([0, 1])  itkPointF2 ([1, 0])  \n", "\n", "Translation: [0.03125, 0.0625]\n"]}], "source": ["input_mesh = itk.Mesh[itk.F, 2].New()\n", "input_mesh.SetPoint(0, [0.0, 0.0])\n", "input_mesh.SetPoint(1, [0.0, 1.0])\n", "input_mesh.SetPoint(2, [1.0, 0.0])\n", "\n", "print('Input Mesh:')\n", "print_mesh(input_mesh)\n", "\n", "translation = [0.03125, 0.0625]\n", "print('Translation:', translation)\n", "\n", "transform = itk.TranslationTransform.New()\n", "transform.SetOffset(translation)\n", "\n", "# A moving image is required for TransformixFilter, just create a very small one.\n", "moving_image = ImageType.New()\n", "moving_image.SetRegions([1, 1])\n", "moving_image.Allocate(True)\n", "\n", "parameter_map = {\n", "                 \"Direction\": (\"1\", \"0\", \"0\", \"1\"),\n", "                 \"Index\": (\"0\", \"0\"),\n", "                 \"Origin\": (\"0\", \"0\"),\n", "                 \"Size\": (\"1\", \"1\"),\n", "                 \"Spacing\": (\"1\", \"1\")\n", "                }"]}, {"cell_type": "markdown", "id": "3c1541e7", "metadata": {}, "source": ["Pass the example input to the TransformixFilter object, apply the transformation, and retrieve the output mesh. Ready!"]}, {"cell_type": "code", "execution_count": 3, "id": "606a65b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output Mesh:\n", "itkPointF2 ([0.03125, 0.0625])  itkPointF2 ([0.03125, 1.0625])  itkPointF2 ([1.03125, 0.0625])  \n", "\n"]}], "source": ["parameter_object = itk.ParameterObject.New()\n", "parameter_object.AddParameterMap(parameter_map)\n", "\n", "transformix_filter.SetMovingImage(moving_image)\n", "transformix_filter.SetTransformParameterObject(parameter_object)\n", "transformix_filter.SetTransform(transform)\n", "transformix_filter.SetInputMesh(input_mesh)\n", "transformix_filter.Update()\n", "\n", "output_mesh = transformix_filter.GetOutputMesh()\n", "print('Output Mesh:')\n", "print_mesh(output_mesh)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 5}