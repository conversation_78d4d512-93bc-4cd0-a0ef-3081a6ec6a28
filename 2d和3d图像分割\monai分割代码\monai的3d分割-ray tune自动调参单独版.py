#%%安装依赖
# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[gdown, nibabel, tqdm, ignite]"')
# get_ipython().system('python -c "import matplotlib" || pip install -q matplotlib')
# get_ipython().run_line_magic('matplotlib', 'inline')

# pip install monai nibabel matplotlib pandas ray hyperopt "ray[tune]" einops

# 只保留基础包的import
import os
import glob
import numpy as np
import torch
import ray
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.search.hyperopt import HyperOptSearch
from hyperopt import hp
from ray.air import session

# 定义全局变量
train_files_global = None
val_files_global = None

# 将主要数据集准备代码保留在全局
root_dir = '/root/autodl-tmp/HCC320'
train_images = '/root/autodl-tmp/HCC320/image/ap'
train_labels = '/root/autodl-tmp/HCC320/mask/ap'

train_images_list = sorted(glob.glob(os.path.join(train_images, "*.nii.gz")))
train_labels_list = sorted(glob.glob(os.path.join(train_labels, "*.nii.gz")))

print(f"Number of training images: {len(train_images_list)}")
print(f"Number of training labels: {len(train_labels_list)}")

data_dicts = [{"image": image_name, "label": label_name} for image_name, label_name in zip(train_images_list[0:100], train_labels_list[0:100])]  

split_index = int(len(data_dicts) * 0.8)# 按照 8:2 划分
train_files, val_files = data_dicts[:split_index], data_dicts[split_index:]
print(f"Total number of samples: {len(data_dicts)}")
print(f"Number of training samples: {len(train_files)}")
print(f"Number of validation samples: {len(val_files)}")

def train_model_tune(config, checkpoint_dir=None):
    # 所有MONAI导入移至函数内部
    import monai
    print(monai.__version__)
    from monai.utils import set_determinism
    from monai.transforms import (
        AsDiscrete,
        AsDiscreted,
        EnsureChannelFirstd,
        Compose,
        CropForegroundd,
        LoadImaged,
        Orientationd,
        RandCropByPosNegLabeld,
        SaveImaged,
        ScaleIntensityRanged,
        Spacingd,
        Invertd,
        RandRotate90d,
        RandFlipd,
        RandScaleIntensityd,
        RandAffined,
        SpatialPadd,
        ResizeWithPadOrCropd
    )
    from monai.networks.nets import UNet
    from monai.networks.layers import Norm
    from monai.metrics import DiceMetric
    from monai.losses import DiceCELoss, FocalLoss
    from monai.data import CacheDataset, DataLoader, decollate_batch
    
    set_determinism(seed=0)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 使用全局变量获取数据
    global train_files_global, val_files_global
    train_files = train_files_global
    val_files = val_files_global
    
    batch_size = config['batch_size']
    learning_rate = config['learning_rate']
    weight_decay = config['weight_decay']  # 添加weight_decay参数
    spatial_size = tuple(config['spatial_size'])  # 从配置中获取spatial_size
    
    train_transforms = Compose([
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        ScaleIntensityRanged(keys=["image"], a_min=-50, a_max=150, b_min=0.0, b_max=1.0, clip=True),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.5, 1.5, 2.0), mode=("bilinear", "nearest")),
        CropForegroundd(keys=["image"], source_key="image", margin=10),
        CropForegroundd(keys=["label"], source_key="label", margin=10),
        ResizeWithPadOrCropd(keys=["image", "label"], spatial_size=spatial_size),
        RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=0),
        RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=1),
        RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=2),
        RandScaleIntensityd(keys="image", factors=0.1, prob=0.5),
    ])
    
    val_transforms = Compose([
        LoadImaged(keys=["image", "label"]),
        EnsureChannelFirstd(keys=["image", "label"]),
        ScaleIntensityRanged(keys=["image"], a_min=-50, a_max=150, b_min=0.0, b_max=1.0, clip=True),
        Orientationd(keys=["image", "label"], axcodes="RAS"),
        Spacingd(keys=["image", "label"], pixdim=(1.5, 1.5, 2.0), mode=("bilinear", "nearest")),
        CropForegroundd(keys=["image"], source_key="image", margin=10),
        CropForegroundd(keys=["label"], source_key="label", margin=10),
        ResizeWithPadOrCropd(keys=["image", "label"], spatial_size=spatial_size),
    ])
    
    train_ds = CacheDataset(data=train_files, transform=train_transforms, cache_rate=0.5, num_workers=2)
    train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=True, num_workers=0)
    val_ds = CacheDataset(data=val_files, transform=val_transforms, cache_rate=0.5, num_workers=2)
    val_loader = DataLoader(val_ds, batch_size=1, num_workers=0)
    
    model = UNet(spatial_dims=3, in_channels=1, out_channels=2, channels=(16, 32, 64, 128, 256), strides=(2, 2, 2, 2), num_res_units=2, norm=Norm.BATCH).to(device)
    
    # 2.UNETR模型(TransformerUnet)成功了!
    # model = UNETR(in_channels=1, out_channels=2,img_size=(96, 96, 96),feature_size=16,hidden_size=768,mlp_dim=3072,num_heads=12,pos_embed="perceptron",norm_name="instance",res_block=True,dropout_rate=0.0).to(device)
    # print(model)

    #3.Swinunetr模型(SwinTransformerUnet)成功了!最新的！
    # model = SwinUNETR(img_size=(96, 96, 96), in_channels=1, out_channels=2, feature_size=48, use_checkpoint=True).to(device)
    # print(model)

    # 4.SegResNet模型成功了!
    # model = SegResNet(spatial_dims=3, init_filters=8, in_channels=1, out_channels=2, dropout_prob=0.1, num_groups=8, use_conv_final=True).to(device)
    # print(model)

    # 5.VNet模型成功了!
    # model = VNet(spatial_dims=3, in_channels=1, out_channels=2, dropout_prob_down=0.2, dropout_prob_up=(0.2, 0.2), dropout_dim=3, bias=False).to(device)
    # print(model)

    # 6.AttentionUnet模型成功了!慢！
    # model = AttentionUnet(spatial_dims=3, in_channels=1, out_channels=2, channels = [32, 64, 128], strides=[1, 2] , kernel_size=3, up_kernel_size=3, dropout=0).to(device)
    # print(model)

    loss_type = config.get("loss_type", "dicece")
    if loss_type == "dicece":
        loss_function = DiceCELoss(to_onehot_y=True, softmax=True, smooth_dr=1e-6, smooth_nr=1e-6)
    elif loss_type == "focal":
        loss_function = FocalLoss(to_onehot_y=True, gamma=2.0).to(device)
    elif loss_type == "dice+focal":
        loss_function_dice = DiceCELoss(to_onehot_y=True, softmax=True, smooth_dr=1e-6, smooth_nr=1e-6)
        loss_function_focal = FocalLoss(to_onehot_y=True, gamma=2.0).to(device)
        def loss_function(pred, target):
            return loss_function_dice(pred, target) + 0.5 * loss_function_focal(pred, target)
    else:
        raise ValueError(f"Unknown loss_type: {loss_type}")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)  # 应用weight_decay参数
    
    dice_metric_val = DiceMetric(include_background=False, reduction="mean")
    post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])
    post_label = Compose([AsDiscrete(to_onehot=2)])
    
    max_epochs = 5
    val_interval = 2
    best_metric = -1
    
    for epoch in range(max_epochs):
        model.train()
        for batch_data in train_loader:
            inputs, labels = batch_data["image"].to(device), batch_data["label"].to(device)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = loss_function(outputs, labels)
            loss.backward()
            optimizer.step()
            
        if (epoch + 1) % val_interval == 0:
            model.eval()
            with torch.no_grad():
                for val_data in val_loader:
                    val_inputs, val_labels = val_data["image"].to(device), val_data["label"].to(device)
                    val_outputs = model(val_inputs)
                    val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]
                    val_labels = [post_label(i) for i in decollate_batch(val_labels)]
                    dice_metric_val(y_pred=val_outputs, y=val_labels)
                    
                val_dice = dice_metric_val.aggregate().item()
                dice_metric_val.reset()
                
                if val_dice > best_metric:
                    best_metric = val_dice
                
                session.report({"val_dice": val_dice})

def run_ray_tune_quick(train_files, val_files):
    global train_files_global, val_files_global
    train_files_global = train_files
    val_files_global = val_files
    
    # 添加weight_decay和spatial_size到超参数搜索空间
    search_space = {
        "learning_rate": hp.loguniform("learning_rate", np.log(1e-5), np.log(1e-3)),
        "batch_size": hp.choice("batch_size", [2, 4]),
        "weight_decay": hp.loguniform("weight_decay", np.log(1e-6), np.log(1e-3)),  # 添加weight_decay
        "spatial_size": hp.choice("spatial_size", [
            [64, 64, 64],          
            [96, 96, 96],
            [112, 112, 112]
        ]),  # 添加spatial_size选项
        "loss_type": hp.choice("loss_type", ["dicece", "focal", "dice+focal"]),  # 新增
    }
    
    algo = HyperOptSearch(search_space, metric="val_dice", mode="max")
    scheduler = ASHAScheduler(metric="val_dice", mode="max", grace_period=2)
    
    print("\n[Ray Tune] 启动超参数调优...\n")
    
    # 使用更少的资源进行测试
    result = tune.run(
        train_model_tune,
        search_alg=algo,
        scheduler=scheduler,
        num_samples=2, # 参数组合次数，增加到4以便更好地探索新增的超参数
        resources_per_trial={"cpu": 1, "gpu": 0 if not torch.cuda.is_available() else 0.5},  # 减少资源使用
        storage_path="/root/autodl-tmp/raytune_quick_results",
        name="raytune_quick",
        verbose=1
    )
    
    best_trial = result.get_best_trial("val_dice", "max", "last")
    print("\n[Ray Tune] 最佳超参数:")
    print(best_trial.config)
    print(f"最佳验证Dice: {best_trial.last_result['val_dice']:.4f}")
    
    return best_trial.config

if __name__ == "__main__":
    # 运行Ray Tune
    best_params = run_ray_tune_quick(train_files, val_files)
    print('Ray Tune调参结果:', best_params)