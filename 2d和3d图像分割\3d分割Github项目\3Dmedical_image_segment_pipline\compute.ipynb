{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["右侧脑室脑池的体积为 8.319 cm³\n", "右侧脑沟的体积为 9.241 cm³\n", "左侧脑室脑池的体积为 7.662 cm³\n", "左侧脑沟的体积为 10.884 cm³\n"]}], "source": ["import SimpleITK as sitk\n", "import numpy as np\n", "import os\n", "\n", "def calculate_volume(mask_image_path):\n", "    # 读取分割结果的图像文件\n", "    mask_image = sitk.ReadImage(mask_image_path)\n", "\n", "    # 获取图像的大小、原点和间距\n", "    size = mask_image.GetSize()\n", "    origin = mask_image.GetOrigin()\n", "    spacing = mask_image.GetSpacing()\n", "\n", "    # 将 SimpleITK 图像转换为 NumPy 数组\n", "    mask_array = sitk.GetArrayFromImage(mask_image)\n", "\n", "    # if len(np.unique(mask_array)) != 5:\n", "    #     print(mask_image_path[-15:-12])\n", "    #     print(np.unique(mask_array))\n", "    \n", "    # 计算非零像素的数量\n", "    one_voxels = (mask_array == 1).sum()\n", "    two_voxels = (mask_array == 2).sum()\n", "    three_voxels = (mask_array == 3).sum()\n", "    four_voxels = (mask_array == 4).sum()\n", "    # print(one_voxels,two_voxels,three_voxels,four_voxels)\n", "    # 计算像素的体积（以立方毫米为单位）\n", "    voxel_volume_mm3 = spacing[0] * spacing[1] * spacing[2]\n", "\n", "    # 计算体积（以 mm³ 为单位）\n", "    V_Right_ventricular_cistern = one_voxels * voxel_volume_mm3 / 1000.0\n", "    V_Right_cerebral_sulcus = two_voxels * voxel_volume_mm3 / 1000.0\n", "    V_Left_ventricular_cistern = three_voxels * voxel_volume_mm3 / 1000.0\n", "    V_Left_cerebral_sulcus = four_voxels * voxel_volume_mm3 / 1000.0\n", "    # 如果需要以其他单位（例如 cm³）显示，请进行适当的单位转换\n", "    # volume_cm3 = volume_mm3 / 1000.0\n", "\n", "    return size,spacing,V_Right_ventricular_cistern, V_Right_cerebral_sulcus, V_Left_ventricular_cistern, V_Left_cerebral_sulcus\n", "\n", "maskFilePath = r\"F:\\sth\\23Fall\\fcpro\\code\\seg.nii.gz\"\n", "size,spacing,V_Right_ventricular_cistern, V_Right_cerebral_sulcus, V_Left_ventricular_cistern, V_Left_cerebral_sulcus = calculate_volume(maskFilePath)\n", "\n", "# 遍历labelsTr文件夹下的所有文件\n", "# for i in os.listdir(r\"F:\\sth\\23Fall\\fcpro\\brain_image_copy\\labelsTr\"):\n", "#     # print(i)\n", "#     size,spacing,V_Right_ventricular_cistern, V_Right_cerebral_sulcus, V_Left_ventricular_cistern, V_Left_cerebral_sulcus= calculate_volume(os.path.join(r\"F:\\sth\\23Fall\\fcpro\\brain_image_copy\\labelsTr\",i))\n", "\n", "# print(\"================================\")\n", "# print(size,spacing)\n", "\n", "print(f'右侧脑室脑池的体积为 {V_Right_ventricular_cistern} cm³')\n", "print(f'右侧脑沟的体积为 {V_Right_cerebral_sulcus} cm³')\n", "print(f'左侧脑室脑池的体积为 {V_Left_ventricular_cistern} cm³')\n", "print(f'左侧脑沟的体积为 {V_Left_cerebral_sulcus} cm³')\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}