
<div align="center">
This is the official repository for the paper
<br/><br/>
</div>

<div style="display: flex; flex-direction: column; align-items: center;">
    <img src="./docs/assets/Header.png" style="width: 100%;"/>
    <div style="display: flex; justify-content: space-between; width: 100%;">
        <img src="./docs/assets/Mhub_image.png" style="width: 49%;"/>
        <img src="./docs/assets/Mhub_image2.png" style="width: 50%;"/>
    </div>
    <a href="https://www.nature.com/articles/s42256-024-00807-9"><img src="./docs/assets/readpaper_logo.png" style="width: 100%;"></a>
</div>

<br/><br/>
<div align="center">

[![Build Status](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/actions/workflows/build.yml/badge.svg)](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/actions/workflows/build.yml)
[![Python Version](https://img.shields.io/pypi/pyversions/foundation-cancer-image-biomarker.svg)](https://pypi.org/project/foundation-cancer-image-biomarker/)
[![Dependencies Status](https://img.shields.io/badge/dependencies-up%20to%20date-brightgreen.svg)](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/pulls?utf8=%E2%9C%93&q=is%3Apr%20author%3Aapp%2Fdependabot)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Security: bandit](https://img.shields.io/badge/security-bandit-green.svg)](https://github.com/PyCQA/bandit)
[![Pre-commit](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white)](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/blob/master/.pre-commit-config.yaml)
[![Semantic Versions](https://img.shields.io/badge/%20%20%F0%9F%93%A6%F0%9F%9A%80-semantic--versions-e10079.svg)](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/releases)
[![License](https://img.shields.io/github/license/AIM-Harvard/foundation-cancer-image-biomarker)](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/blob/master/LICENSE)
[![Coverage](docs/assets/images/coverage.svg)](https://github.com/AIM-Harvard/foundation-cancer-image-biomarker/blob/master/docs/assets/images/coverage.svg)



---
**NOTE:**
 For detailed documentation check our [website](https://aim-harvard.github.io/foundation-cancer-image-biomarker/) 

---

</div>
