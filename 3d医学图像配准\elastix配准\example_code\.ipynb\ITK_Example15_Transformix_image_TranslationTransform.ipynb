{"cells": [{"cell_type": "markdown", "id": "4ba94dda", "metadata": {}, "source": ["# Minimal example on how to transform an image, using TransformixFilter with TranslationTransform"]}, {"cell_type": "markdown", "id": "5fc4a7b9", "metadata": {}, "source": ["This example shows how to transform an image, using `itk.TransformixFilter` with `itk.TranslationTransform`.\n", "\n", "Start by importing packages and creating an TransformixFilter object:"]}, {"cell_type": "code", "execution_count": 1, "id": "852e83ee", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating a TransformixFilter (might take a while, please wait)...\n", "\n"]}], "source": ["import itk\n", "import numpy as np\n", "\n", "print('Creating a TransformixFilter (might take a while, please wait)...\\n')\n", "ImageType = itk.Image[itk.F, 2]\n", "transformix_filter = itk.TransformixFilter[ImageType].New()"]}, {"cell_type": "markdown", "id": "65d4e03c", "metadata": {}, "source": ["Create the example input: a small (5x6) image, a translation (1, -2), and a rather trivial parameter map.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "9fbb7de3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Moving image:\n", "[[ 1.  2.  3.  4.  5.]\n", " [ 6.  7.  8.  9. 10.]\n", " [11. 12. 13. 14. 15.]\n", " [16. 17. 18. 19. 20.]\n", " [21. 22. 23. 24. 25.]\n", " [26. 27. 28. 29. 30.]]\n", "\n", "Translation: [1, -2]\n"]}], "source": ["number_of_columns = 5\n", "number_of_rows = 6\n", "\n", "moving_image = ImageType.New()\n", "moving_image.SetRegions([number_of_columns, number_of_rows])\n", "moving_image.Allocate(True)\n", "\n", "# Set the pixel values consecutively to 1, 2, 3, ..., n.\n", "moving_image[:] =  np.arange(1, number_of_columns*number_of_rows + 1).reshape((number_of_rows, number_of_columns))\n", "                            \n", "print('Moving image:')\n", "print(np.asarray(moving_image))\n", "print()\n", "\n", "translation = [1, -2]\n", "print('Translation:', translation)\n", "\n", "transform = itk.TranslationTransform.New()\n", "transform.SetOffset(translation)\n", "\n", "parameter_map = {\n", "                 \"Direction\": (\"1\", \"0\", \"0\", \"1\"),\n", "                 \"Index\": (\"0\", \"0\"),\n", "                 \"Origin\": (\"0\", \"0\"),\n", "                 \"Size\": (str(number_of_columns), str(number_of_rows)),\n", "                 \"Spacing\": (\"1\", \"1\")\n", "                }"]}, {"cell_type": "markdown", "id": "2947cf4a", "metadata": {}, "source": ["Pass the example input to the TransformixFilter object, apply the transformation, and retrieve the output image. Ready!"]}, {"cell_type": "code", "execution_count": 3, "id": "26f9394e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output image:\n", "[[ 0.  0.  0.  0.  0.]\n", " [ 0.  0.  0.  0.  0.]\n", " [ 2.  3.  4.  5.  0.]\n", " [ 7.  8.  9. 10.  0.]\n", " [12. 13. 14. 15.  0.]\n", " [17. 18. 19. 20.  0.]]\n", "\n"]}], "source": ["parameter_object = itk.ParameterObject.New()\n", "parameter_object.AddParameterMap(parameter_map)\n", "\n", "transformix_filter.SetMovingImage(moving_image)\n", "transformix_filter.SetTransformParameterObject(parameter_object)\n", "transformix_filter.SetTransform(transform)\n", "transformix_filter.Update()\n", "\n", "output_image = transformix_filter.GetOutput()\n", "\n", "print('Output image:')\n", "print(np.asarray(output_image))\n", "print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 5}