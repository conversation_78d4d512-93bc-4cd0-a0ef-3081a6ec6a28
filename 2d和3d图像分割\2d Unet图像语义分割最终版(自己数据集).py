#!/usr/bin/env python
# coding: utf-8

#%% In[5]: 已完全成功
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils import data
import numpy as np
import matplotlib
# matplotlib.use('TkAgg') 
# matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
get_ipython().run_line_magic('matplotlib', 'inline')
import torchvision
from torchvision import transforms
import os
import glob
from PIL import Image
torch.__version__

# In[3]:
# pil_img = Image.open(r'H:\1.HCC-VETC\datasets\HCC-suzhou\unet_data\image\caozhenda-AP-ROI\53.jpg')
# np_img = np.array(pil_img)
# plt.imshow(np_img)
# plt.show()

# np.unique(np_img)
# np_img.max(), np_img.min()
# np_img.shape

# all_pics = glob.glob(r'H:\1.HCC-VETC\datasets\HCC-suzhou\unet_data\image\*\*.jpg')
# all_pics[:5]

# images = [p for p in all_pics]
# len(images)

# annotations = glob.glob(r'H:\1.HCC-VETC\datasets\HCC-suzhou\unet_data\image\*\*.jpg')
# len(annotations)

# images[:5]
# annotations[:5]

# i = int(len(images)*0.7)

# train_images = images[:i]
# train_labels = annotations[:i]
# len(train_images)

# test_images = images[i: ]
# test_labels = annotations[i: ]
# test_images[:5]
# test_labels[:5]

# np.random.seed(2021)
# index = np.random.permutation(len(images))
#
# train_images  = np.array(train_images)[index]
# train_images [:5]

# train_labels= np.array(train_labels)[index]
# train_labels[:5]

# In[22]:
# all_test_pics = glob.glob(r'G:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\*.png')
# test_images = [p for p in all_test_pics if 'matte' not in p]
# test_anno = [p for p in all_test_pics if 'matte' in p]

import os
import numpy as np

image_dir = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\training\image'
mask_dir = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\training\mask'

# 获取目录下的所有文件
image_files = sorted([os.path.join(image_dir, f) for f in os.listdir(image_dir)])
mask_files = sorted([os.path.join(mask_dir, f) for f in os.listdir(mask_dir)])

# 划分训练和测试集
i = int(len(image_files) * 0.7)

train_images = image_files[:i]
train_labels = mask_files[:i]
print("Number of training images:", len(train_images))

test_images = image_files[i:]
test_labels = mask_files[i:]

# 设置随机种子
np.random.seed(2021)

# 生成随机索引
index = np.random.permutation(len(train_images))  # 使用 train_images 的长度

# 使用随机索引重新排列训练图像和标签
train_images = np.array(train_images)[index]
train_labels = np.array(train_labels)[index]

# 打印前5个训练图像和标签
print("Train images:", train_images[:5])
print("Train labels:", train_labels[:5])

#%% ToTensor作用：归一化，channel放前面，转为tensor

transform = transforms.Compose([
                    transforms.Resize((256, 256)),
                    transforms.ToTensor(),
])

#%% 原始代码
class Portrait_dataset(data.Dataset):
    def __init__(self, img_paths, anno_paths):
        self.imgs = img_paths
        self.annos = anno_paths

    def __getitem__(self, index):
        img = self.imgs[index]
        anno = self.annos[index]

        # 灰度影像图转为RGB
        pil_img = Image.open(img).convert("RGB")
        img_tensor = transform(pil_img)

        pil_anno = Image.open(anno)
        anno_tensor = transform(pil_anno)
        anno_tensor = torch.squeeze(anno_tensor).type(torch.long)
        anno_tensor[anno_tensor > 0] = 1

        return img_tensor, anno_tensor

    def __len__(self):
        return len(self.imgs)

#修改后代码
#如果anno_tensor像素为0，则返回none，意味着删除 annotation and corresponding image
# class Portrait_dataset(data.Dataset):
#     def __init__(self, img_paths, anno_paths):
#         self.imgs = img_paths
#         self.annos = anno_paths
#     def __getitem__(self, index):
#         img = self.imgs[index]
#         anno = self.annos[index]
#         # 灰度影像图转为RGB
#         pil_img = Image.open(img).convert("RGB")
#         img_tensor = transform(pil_img)
#         pil_anno = Image.open(anno)
#         anno_tensor = transform(pil_anno)
#         anno_tensor = torch.squeeze(anno_tensor).type(torch.long)
#         anno_tensor[anno_tensor > 0] = 1
#         # Check for pixels equal to 0 in annotation
#         nonzero_indices = torch.nonzero(anno_tensor)
#         if len(nonzero_indices) == 0:
#             # Remove annotation and corresponding image if annotation pixels are all 0
#             del self.imgs[index]
#             del self.annos[index]
#             return self.__getitem__(index)  # Recursively call __getitem__ to get the next valid item
#         return img_tensor, anno_tensor
#     def __len__(self):
#         return len(self.imgs)

# In[26]:

BATCH_SIZE = 8

train_dataset = Portrait_dataset(train_images,train_labels)
len(train_dataset)
test_dataset = Portrait_dataset(test_images, test_labels)
len(test_dataset)

train_dl = data.DataLoader(
                           train_dataset,
                           batch_size=BATCH_SIZE,
                           shuffle=True,
)

test_dl = data.DataLoader(
                          test_dataset,
                          batch_size=BATCH_SIZE,
)

## In[30]:

imgs_batch, annos_batch = next(iter(train_dl))
print("Image shape:", imgs_batch.shape)  # 打印图像的形状
print("Mask shape:", annos_batch.shape)  # 打印mask的形状

imgs_batch[0].shape  #去掉第一个维度
annos_batch[0].shape #去掉第一个维度

img = imgs_batch[0].permute(1,2,0).numpy()
anno = annos_batch[0].numpy()
img.shape
anno.shape

# In[32]:

plt.subplot(1,2,1)
plt.imshow(img)
plt.subplot(1,2,2)
plt.imshow(anno)

#In[33]:

class Downsample(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(Downsample, self).__init__()
        self.conv_relu = nn.Sequential(
                            nn.Conv2d(in_channels, out_channels, 
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True),
                            nn.Conv2d(out_channels, out_channels, 
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True)
            )
        self.pool = nn.MaxPool2d(kernel_size=2)
    def forward(self, x, is_pool=True):
        if is_pool:
            x = self.pool(x)
        x = self.conv_relu(x)
        return x


class Upsample(nn.Module):
    def __init__(self, channels):
        super(Upsample, self).__init__()
        self.conv_relu = nn.Sequential(
                            nn.Conv2d(2*channels, channels, 
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True),
                            nn.Conv2d(channels, channels,  
                                      kernel_size=3, padding=1),
                            nn.ReLU(inplace=True)
            )
        self.upconv_relu = nn.Sequential(
                               nn.ConvTranspose2d(channels, 
                                                  channels//2, 
                                                  kernel_size=3,
                                                  stride=2,
                                                  padding=1,
                                                  output_padding=1),
                               nn.ReLU(inplace=True)
            )
        
    def forward(self, x):
        x = self.conv_relu(x)
        x = self.upconv_relu(x)
        return x


class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.down1 = Downsample(3, 64)
        self.down2 = Downsample(64, 128)
        self.down3 = Downsample(128, 256)
        self.down4 = Downsample(256, 512)
        self.down5 = Downsample(512, 1024)
        
        self.up = nn.Sequential(
                               nn.ConvTranspose2d(1024, 
                                                  512, 
                                                  kernel_size=3,
                                                  stride=2,
                                                  padding=1,
                                                  output_padding=1),
                               nn.ReLU(inplace=True)
            )
        
        self.up1 = Upsample(512)
        self.up2 = Upsample(256)
        self.up3 = Upsample(128)
        
        self.conv_2 = Downsample(128, 64)
        self.last = nn.Conv2d(64, 2, kernel_size=1)

    def forward(self, x):
        x1 = self.down1(x, is_pool=False)
        x2 = self.down2(x1)
        x3 = self.down3(x2)
        x4 = self.down4(x3)
        x5 = self.down5(x4)
        
        x5 = self.up(x5)
        
        x5 = torch.cat([x4, x5], dim=1)           # 32*32*1024
        x5 = self.up1(x5)                         # 64*64*256)
        x5 = torch.cat([x3, x5], dim=1)           # 64*64*512  
        x5 = self.up2(x5)                         # 128*128*128
        x5 = torch.cat([x2, x5], dim=1)           # 128*128*256
        x5 = self.up3(x5)                         # 256*256*64
        x5 = torch.cat([x1, x5], dim=1)           # 256*256*128
        
        x5 = self.conv_2(x5, is_pool=False)       # 256*256*64
        
        x5 = self.last(x5)                        # 256*256*3
        return x5


#%%
model = Net()

# In[37]:

if torch.cuda.is_available():
    model.to('cuda')

# In[38]:

loss_fn = nn.CrossEntropyLoss()

# In[39]:

from torch.optim import lr_scheduler
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

## 方法1:计算acc和loss  

def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    
    model.train()
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')          
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
    exp_lr_scheduler.step()
    epoch_loss = running_loss / len(trainloader.dataset)
    # epoch_acc = correct / (total*256*256)     
    epoch_acc = correct / (total * x.size(2) * x.size(3))  
    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    
    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)    
    # epoch_test_acc = test_correct / (test_total*256*256)
    epoch_test_acc = test_correct / (test_total * x.size(2) * x.size(3))
        
    print('epoch: ', epoch, 
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
             )
        
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc

#%%
epochs = 10

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)

#查看训练的设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Training on: ", device)

#%%方法2，计算iou值和acc值  

def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []

    model.train()
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.true_divide(torch.sum(intersection),
                                          torch.sum(union))
            epoch_iou.append(batch_iou.cpu())

    exp_lr_scheduler.step()
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / (total * x.size(2) * x.size(3))  

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    epoch_test_iou = []

    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
               x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.true_divide(torch.sum(intersection),
                                          torch.sum(union))
            epoch_test_iou.append(batch_iou.cpu())

    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / (test_total * x.size(2) * x.size(3))

    print('epoch: ', epoch,
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean([iou.numpy() for iou in epoch_iou]), 3))
    # print()
    print('     ', 'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
          'test_iou:', round(np.mean([iou.numpy() for iou in epoch_test_iou]), 3)
          )
    return epoch_loss, epoch_acc, epoch_iou, epoch_test_loss, epoch_test_acc,epoch_test_iou


# In[44]:
epochs = 5

train_loss = []
train_acc = []
train_iou = []
test_loss = []
test_acc = []
test_iou = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_iou, epoch_test_loss, epoch_test_acc,epoch_test_iou = fit(epoch,
                                                                                            model,
                                                                                            train_dl,
                                                                                            test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    train_iou.append(epoch_iou)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    test_iou.append(epoch_test_iou)


#查看训练的设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Training on: ", device)

#%% 保存模型
PATH = r'H:\weight\hkunet_model.pth' #路径不能有中文
torch.save(model.state_dict(), PATH)

# 测试模型
PATH = r'H:\weight\hkunet_model.pth' #路径不能有中文
my_model = Net()
my_model.load_state_dict(torch.load(PATH))

#%% 在test数据上测试

num=3

image, mask = next(iter(test_dl))
pred_mask = my_model(image)
pred_mask[0].shape
image.shape

plt.figure(figsize=(10, 10))
for i in range(num):
    plt.subplot(num, 3, i*num+1)
    plt.imshow(image[i].permute(1,2,0).cpu().numpy())
    plt.subplot(num, 3, i*num+2)
    plt.imshow(mask[i].cpu().numpy())
    plt.subplot(num, 3, i*num+3)
    plt.imshow(torch.argmax(pred_mask[i].permute(1,2,0), axis=-1).detach().numpy())

#%% 在train数据上测试

image, mask = next(iter(train_dl))
pred_mask = my_model(image)

plt.figure(figsize=(10, 10))
for i in range(num):
    plt.subplot(num, 3, i*num+1)
    plt.imshow(image[i].permute(1,2,0).cpu().numpy())
    plt.subplot(num, 3, i*num+2)
    plt.imshow(mask[i].cpu().numpy())
    plt.subplot(num, 3, i*num+3)
    plt.imshow(torch.argmax(pred_mask[i].permute(1,2,0), axis=-1).detach().numpy())

#%%批量模型预测1，预测的mask是彩色的,预测的mask大小256*256
import os
import cv2
import numpy as np

transform = transforms.Compose([
                    transforms.Resize((256, 256)),
                    transforms.ToTensor(),
])

images_path = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\image'    #注意路径r'H:\data\predimg'或者'H:/data/predimg'，不能有中文
masks_path = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\mask'       #注意路径r'H:\data\predimg'或者'H:/data/predimg'，不能有中文


def add_mask2image_binary(images_path, masks_path):
# Add binary masks to images
    for img_item in os.listdir(images_path):
        print(img_item)
        img_path = os.path.join(images_path, img_item)        #image和mask的文件名字要一样
        pil_imgs = Image.open(img_path).convert("RGB")
        img_tensor = transform(pil_imgs)
        print(img_tensor.shape)
        img_tensor_batch = torch.unsqueeze(img_tensor,0)
        img_tensor_batch.shape
        pred = my_model(img_tensor_batch)
        print(pred.shape)
        pred = torch.argmax(pred[0].permute(1,2,0),axis=-1).numpy()
        print(pred.shape)
        # pred = pred * 255.0
        pred = (pred * 255.0).astype(np.uint8)  # 确保 pred 是 uint8 类型
        cv2.imwrite(os.path.join(masks_path, img_item),pred)


add_mask2image_binary(images_path, masks_path)

#%%批量模型预测2，预测的mask是黑白的,预测的mask大小256*256
import os
import cv2
import numpy as np
import torch
from torchvision import transforms
from PIL import Image

transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.ToTensor(),
])

images_path = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\image'
masks_path = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\mask'

def add_mask2image_binary(images_path, masks_path):
    # Add binary masks to images
    for img_item in os.listdir(images_path):
        print(img_item)
        img_path = os.path.join(images_path, img_item)  # image和mask的文件名字要一样
        pil_imgs = Image.open(img_path).convert("RGB")
        img_tensor = transform(pil_imgs)
        print(img_tensor.shape)
        img_tensor_batch = torch.unsqueeze(img_tensor, 0)
        print(img_tensor_batch.shape)
        
        pred = my_model(img_tensor_batch)  # 确保 my_model 已定义
        print(pred.shape)
        
        # 将输出转换为二值图像
        pred = torch.argmax(pred[0].permute(1, 2, 0), axis=-1).numpy()  # 获取类别图
        binary_mask = (pred > 0).astype(np.uint8)  # 将值大于0的设为1，其余设为0
        print(binary_mask.shape)
        
        # 保存二值掩膜
        cv2.imwrite(os.path.join(masks_path, img_item), binary_mask * 255)  # 保存为0和255的图像

add_mask2image_binary(images_path, masks_path)

#%%批量模型预测3，预测的mask大小和对应的image一样
import os
import cv2
import numpy as np
import torch
from torchvision import transforms
from PIL import Image

transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.ToTensor(),
])

images_path = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\image'
masks_path = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\mask'

# 测试模型
PATH = r'H:\weight\hkunet_model.pth' #路径不能有中文
my_model = Net()
my_model.load_state_dict(torch.load(PATH))


def add_mask2image_binary(images_path, masks_path):
    # Add binary masks to images
    for img_item in os.listdir(images_path):
        print(img_item)
        img_path = os.path.join(images_path, img_item)  # image和mask的文件名字要一样
        pil_imgs = Image.open(img_path).convert("RGB")
        
        # 保存原始图像的尺寸
        original_size = pil_imgs.size
        
        img_tensor = transform(pil_imgs)
        print(img_tensor.shape)
        img_tensor_batch = torch.unsqueeze(img_tensor, 0)
        print(img_tensor_batch.shape)
        
        pred = my_model(img_tensor_batch)  # 确保 my_model 已定义
        print(pred.shape)
        
        # 将输出转换为二值图像
        pred = torch.argmax(pred[0].permute(1, 2, 0), axis=-1).numpy()  # 获取类别图
        binary_mask = (pred > 0).astype(np.uint8)  # 将值大于0的设为1，其余设为0
        print(binary_mask.shape)
        
        # 将二值掩膜调整为原始图像的大小
        binary_mask_resized = cv2.resize(binary_mask, original_size, interpolation=cv2.INTER_NEAREST)
        
        # 保存二值掩膜
        cv2.imwrite(os.path.join(masks_path, img_item), binary_mask_resized * 255)  # 保存为0和255的图像

add_mask2image_binary(images_path, masks_path)

#%%png转nii格式
import os
import numpy as np
import nibabel as nib
from PIL import Image

def png_to_nifti(images_path, output_path):
    # 获取路径下所有的 PNG 文件
    png_files = [f for f in os.listdir(images_path) if f.endswith('.png')]
    
    for png_file in png_files:
        img_path = os.path.join(images_path, png_file)
        # 读取 PNG 图像
        img = Image.open(img_path).convert('L')  # 转换为灰度图像
        img_array = np.array(img)  # 转换为 NumPy 数组
        
        # 创建 NIfTI 图像对象
        nifti_img = nib.Nifti1Image(img_array, affine=np.eye(4))  # 使用单位矩阵作为仿射矩阵
        
        # 生成输出文件名，替换扩展名为 .nii.gz
        output_file_name = os.path.splitext(png_file)[0] + '.nii.gz'
        output_file = os.path.join(output_path, output_file_name)
        
        # 保存为 NIfTI 格式
        nib.save(nifti_img, output_file)
        print(f'Saved NIfTI file to {output_file}')


# 使用示例
images_path = r'f:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\4.Unet语义分割(第13章)参考代码和数据集\第13章\hk\testing\mask'  # 替换为您的 PNG 文件路径
output_path = images_path      # 替换为您希望保存 NIfTI 文件的路径
png_to_nifti(images_path, output_path)
# %%
