name: <PERSON><PERSON> (<PERSON><PERSON>)

on:
  # Triggers the workflow on push or pull request events
  push:
    branches: [ master ]
  pull_request:
    branches:
      - "*"

permissions:
  contents: read

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

    - uses: actions/setup-python@0a5c61591373683505ea898e09a3ea4f39ef2b9c # v5.0.0
      with:
        python-version: '3.9'

    - uses: pre-commit/action@2c7b3805fd2a0fd8c1884dcaebf91fc102a13ecd # v3.0.1
