{"project_name": "medical_diffusion_ap_to_hbp", "data": {"ap_dir": "path/to/ap/images", "hbp_dir": "path/to/hbp/images", "image_size": [256, 256], "train_ratio": 0.8, "val_ratio": 0.1, "test_ratio": 0.1, "batch_size": 8, "num_workers": 4, "slice_range": null, "augmentation": {"enabled": true, "rotation_range": 15, "intensity_shift": 0.1, "noise_std": 0.01}}, "model": {"in_channels": 1, "out_channels": 1, "time_emb_dim": 128, "base_channels": 64, "channel_multipliers": [1, 2, 4, 8], "num_res_blocks": 2, "attention_resolutions": [16, 8], "dropout": 0.1, "use_scale_shift_norm": true}, "diffusion": {"timesteps": 1000, "beta_start": 0.0001, "beta_end": 0.02, "beta_schedule": "linear", "loss_type": "l2", "parameterization": "eps", "clip_denoised": true}, "training": {"num_epochs": 1000, "learning_rate": 0.0002, "weight_decay": 0.0001, "grad_clip": 1.0, "scheduler": {"type": "cosine", "T_max": 1000, "eta_min": 1e-06}, "optimizer": {"type": "AdamW", "betas": [0.9, 0.999], "eps": 1e-08}}, "loss": {"use_perceptual_loss": true, "perceptual_weight": 0.1, "perceptual_layers": ["conv_4"], "use_ssim_loss": false, "ssim_weight": 0.5, "use_gan_loss": false, "gan_weight": 0.01, "lambda_gp": 10}, "validation": {"val_interval": 5, "sample_interval": 10, "num_samples": 8, "save_samples": true, "calculate_metrics": true, "metrics": ["ssim", "psnr", "mse", "mae"]}, "inference": {"num_inference_steps": 50, "guidance_scale": 1.0, "eta": 0.0, "clip_sample": true, "sample_batch_size": 4}, "logging": {"log_interval": 50, "save_checkpoint_interval": 100, "tensorboard_log_dir": "logs", "wandb_project": null, "wandb_entity": null}, "paths": {"save_dir": "outputs/medical_diffusion_experiment", "checkpoint_dir": "outputs/medical_diffusion_experiment/checkpoints", "sample_dir": "outputs/medical_diffusion_experiment/samples", "log_dir": "outputs/medical_diffusion_experiment/logs"}, "system": {"device": "cuda", "mixed_precision": true, "compile_model": false, "seed": 42, "deterministic": true, "benchmark": true}, "evaluation": {"evaluate_interval": 20, "save_generated_images": true, "generate_test_samples": true, "num_test_samples": 100, "save_metrics_plot": true}}