name: Installation Issue
description: Report issues installing the pycaret library on your system
title: "[INSTALL]: "
labels: [installation]

body:
  - type: checkboxes
    id: checks
    attributes:
      label: Installation check
      options:
        - label: >
            I have read the [installation guide](https://pycaret.gitbook.io/docs/get-started/installation).
          required: true
  - type: input
    id: platform
    attributes:
      label: Platform
      description: >
        Please provide the output of ``import platform; print(platform.platform())``
    validations:
      required: true
  - type: dropdown
    id: method
    attributes:
      label: Installation Method
      description: >
        Please provide how you tried to install pycaret from a clean environment.
      options:
        - pip install
        - conda install
        - apt-get install
        - Built from source
        - Other
    validations:
      required: true
  - type: input
    id: pycaret
    attributes:
      label: pycaret Version
      description: >
        Please provide the version of pycaret you are trying to install.
    validations:
      required: true
  - type: input
    id: python
    attributes:
      label: Python Version
      description: >
        Please provide the installed version of Python.
    validations:
      required: true
  - type: textarea
    id: description
    attributes:
      label: Description
      description: >
        Please describe the issue that you are facing during installation.
  - type: textarea
    id: logs
    attributes:
      label: Installation Logs
      description: >
        If possible, please copy and paste the installation logs when attempting to install pycaret.
      value: >
        <details>


        Replace this line with the installation logs.


        </details>
