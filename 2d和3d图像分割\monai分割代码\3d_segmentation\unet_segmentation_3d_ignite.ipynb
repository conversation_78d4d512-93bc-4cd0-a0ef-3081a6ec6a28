{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License. \n", "\n", "# 3D Segmentation with UNet\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/3d_segmentation/unet_segmentation_3d_ignite.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[ignite, nibabel, tensorboard, mlflow]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 0.9.1\n", "Numpy version: 1.22.4\n", "Pytorch version: 1.13.0a0+340c412\n", "MONAI flags: HAS_EXT = True, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 356d2d2f41b473f588899d705bbc682308cee52c\n", "MONAI __file__: /opt/monai/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.9\n", "Nibabel version: 4.0.1\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.0.1\n", "Tensorboard version: 2.9.1\n", "gdown version: 4.5.1\n", "TorchVision version: 0.13.0a0\n", "tqdm version: 4.64.0\n", "lmdb version: 1.3.0\n", "psutil version: 5.9.1\n", "pandas version: 1.3.5\n", "einops version: 0.4.1\n", "transformers version: 4.20.1\n", "mlflow version: 1.27.0\n", "pynrrd version: 0.4.3\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import glob\n", "import logging\n", "import os\n", "from pathlib import Path\n", "import shutil\n", "import sys\n", "import tempfile\n", "\n", "import nibabel as nib\n", "import numpy as np\n", "from monai.config import print_config\n", "from monai.data import ArrayDataset, create_test_image_3d, decollate_batch, DataLoader\n", "from monai.handlers import (\n", "    MeanDice,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    TensorBoard<PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    TensorBoardStats<PERSON>and<PERSON>,\n", ")\n", "from monai.losses import DiceLoss\n", "from monai.networks.nets import UNet\n", "from monai.transforms import (\n", "    Activations,\n", "    EnsureChannelFirst,\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON>,\n", "    LoadImage,\n", "    RandSpatialCrop,\n", "    Resize,\n", "    ScaleIntensity,\n", ")\n", "from monai.utils import first\n", "\n", "import ignite\n", "import torch\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/workspace/data/medical\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup logging"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(stream=sys.stdout, level=logging.INFO)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup demo data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["for i in range(40):\n", "    im, seg = create_test_image_3d(128, 128, 128, num_seg_classes=1)\n", "\n", "    n = nib.Nifti1Image(im, np.eye(4))\n", "    nib.save(n, os.path.join(root_dir, f\"im{i}.nii.gz\"))\n", "\n", "    n = nib.Nifti1Image(seg, np.eye(4))\n", "    nib.save(n, os.path.join(root_dir, f\"seg{i}.nii.gz\"))\n", "\n", "images = sorted(glob.glob(os.path.join(root_dir, \"im*.nii.gz\")))\n", "segs = sorted(glob.glob(os.path.join(root_dir, \"seg*.nii.gz\")))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms, dataset"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([10, 1, 96, 96, 96]) torch.<PERSON><PERSON>([10, 1, 96, 96, 96])\n"]}], "source": ["# Define transforms for image and segmentation\n", "imtrans = Compose(\n", "    [\n", "        LoadImage(image_only=True),\n", "        ScaleIntensity(),\n", "        EnsureChannelFirst(),\n", "        RandSpatialCrop((96, 96, 96), random_size=False),\n", "    ]\n", ")\n", "segtrans = Compose(\n", "    [\n", "        LoadImage(image_only=True),\n", "        EnsureChannelFirst(),\n", "        RandSpatialCrop((96, 96, 96), random_size=False),\n", "    ]\n", ")\n", "\n", "# Define nifti dataset, dataloader\n", "ds = ArrayDataset(images, imtrans, segs, segtrans)\n", "loader = DataLoader(ds, batch_size=10, num_workers=2, pin_memory=torch.cuda.is_available())\n", "im, seg = first(loader)\n", "print(im.shape, seg.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Model, Loss, Optimizer"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and Adam optimizer\n", "device = torch.device(\"cuda:0\")\n", "net = UNet(\n", "    spatial_dims=3,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    channels=(16, 32, 64, 128, 256),\n", "    strides=(2, 2, 2, 2),\n", "    num_res_units=2,\n", ").to(device)\n", "\n", "loss = DiceLoss(sigmoid=True)\n", "lr = 1e-3\n", "opt = torch.optim.Adam(net.parameters(), lr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create supervised_trainer using ignite"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Create trainer\n", "trainer = ignite.engine.create_supervised_trainer(net, opt, loss, device, False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup event handlers for checkpointing and logging"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"tags": []}, "outputs": [], "source": ["# optional section for checkpoint and tensorboard logging\n", "# adding checkpoint handler to save models (network\n", "# params and optimizer stats) during training\n", "log_dir = os.path.join(root_dir, \"logs\")\n", "checkpoint_handler = ignite.handlers.ModelCheckpoint(log_dir, \"net\", n_saved=10, require_empty=False)\n", "trainer.add_event_handler(\n", "    event_name=ignite.engine.Events.EPOCH_COMPLETED,\n", "    handler=checkpoint_handler,\n", "    to_save={\"net\": net, \"opt\": opt},\n", ")\n", "\n", "# StatsHandler prints loss at every iteration\n", "# user can also customize print functions and can use output_transform to convert\n", "# engine.state.output if it's not a loss value\n", "train_stats_handler = StatsHandler(name=\"trainer\", output_transform=lambda x: x)\n", "train_stats_handler.attach(trainer)\n", "\n", "# TensorBoardStatsHandler plots loss at every iteration\n", "train_tensorboard_stats_handler = TensorBoardStatsHandler(log_dir=log_dir, output_transform=lambda x: x)\n", "train_tensorboard_stats_handler.attach(trainer)\n", "\n", "# MLFlowHandler plots loss at every iteration on MLFlow web UI\n", "mlflow_dir = os.path.join(log_dir, \"mlruns\")\n", "train_mlflow_handler = MLFlowHandler(tracking_uri=Path(mlflow_dir).as_uri(), output_transform=lambda x: x)\n", "train_mlflow_handler.attach(trainer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add Validation every N epochs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# optional section for model validation during training\n", "validation_every_n_epochs = 1\n", "# Set parameters for validation\n", "metric_name = \"Mean_Dice\"\n", "# add evaluation metric to the evaluator engine\n", "val_metrics = {metric_name: MeanDice()}\n", "post_pred = Compose([Activations(sigmoid=True), AsDiscrete(threshold=0.5)])\n", "post_label = Compose([AsDiscrete(threshold=0.5)])\n", "# Ignite evaluator expects batch=(img, seg) and\n", "# returns output=(y_pred, y) at every iteration,\n", "# user can add output_transform to return other values\n", "evaluator = ignite.engine.create_supervised_evaluator(\n", "    net,\n", "    val_metrics,\n", "    device,\n", "    True,\n", "    output_transform=lambda x, y, y_pred: (\n", "        [post_pred(i) for i in decollate_batch(y_pred)],\n", "        [post_label(i) for i in decollate_batch(y)],\n", "    ),\n", ")\n", "\n", "# create a validation data loader\n", "val_imtrans = Compose(\n", "    [\n", "        LoadImage(image_only=True),\n", "        ScaleIntensity(),\n", "        EnsureChannelFirst(),\n", "        Resize((96, 96, 96)),\n", "    ]\n", ")\n", "val_segtrans = Compose(\n", "    [\n", "        LoadImage(image_only=True),\n", "        EnsureChannelFirst(),\n", "        Resize((96, 96, 96)),\n", "    ]\n", ")\n", "val_ds = ArrayDataset(images[21:], val_imtrans, segs[21:], val_segtrans)\n", "val_loader = DataLoader(val_ds, batch_size=5, num_workers=8, pin_memory=torch.cuda.is_available())\n", "\n", "\n", "@trainer.on(ignite.engine.Events.EPOCH_COMPLETED(every=validation_every_n_epochs))\n", "def run_validation(engine):\n", "    evaluator.run(val_loader)\n", "\n", "\n", "# Add stats event handler to print validation stats via evaluator\n", "val_stats_handler = StatsHandler(\n", "    name=\"evaluator\",\n", "    # no need to print loss value, so disable per iteration output\n", "    output_transform=lambda x: None,\n", "    # fetch global epoch number from trainer\n", "    global_epoch_transform=lambda x: trainer.state.epoch,\n", ")\n", "val_stats_handler.attach(evaluator)\n", "\n", "# add handler to record metrics to TensorBoard at every validation epoch\n", "val_tensorboard_stats_handler = TensorBoardStatsHandler(\n", "    log_dir=log_dir,\n", "    # no need to plot loss value, so disable per iteration output\n", "    output_transform=lambda x: None,\n", "    # fetch global epoch number from trainer\n", "    global_epoch_transform=lambda x: trainer.state.epoch,\n", ")\n", "val_tensorboard_stats_handler.attach(evaluator)\n", "\n", "# add handler to record metrics to MLFlow at every validation epoch\n", "val_mlflow_handler = MLFlowHandler(\n", "    tracking_uri=Path(mlflow_dir).as_uri(),\n", "    # no need to plot loss value, so disable per iteration output\n", "    output_transform=lambda x: None,\n", "    # fetch global epoch number from trainer\n", "    global_epoch_transform=lambda x: trainer.state.epoch,\n", ")\n", "val_mlflow_handler.attach(evaluator)\n", "\n", "# add handler to draw the first image and the corresponding\n", "# label and model output in the last batch\n", "# here we draw the 3D output as GIF format along Depth\n", "# axis, at every validation epoch\n", "val_tensorboard_image_handler = TensorBoardImageHandler(\n", "    log_dir=log_dir,\n", "    batch_transform=lambda batch: (batch[0], batch[1]),\n", "    output_transform=lambda output: output[0],\n", "    global_iter_transform=lambda x: trainer.state.epoch,\n", ")\n", "evaluator.add_event_handler(\n", "    event_name=ignite.engine.Events.EPOCH_COMPLETED,\n", "    handler=val_tensorboard_image_handler,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run training loop"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# create a training data loader\n", "train_ds = ArrayDataset(images[:20], imtrans, segs[:20], segtrans)\n", "train_loader = DataLoader(\n", "    train_ds,\n", "    batch_size=5,\n", "    shuffle=True,\n", "    num_workers=8,\n", "    pin_memory=torch.cuda.is_available(),\n", ")\n", "\n", "max_epochs = 10\n", "state = trainer.run(train_loader, max_epochs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Tensorboard logs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext tensorboard\n", "%tensorboard --logdir=$log_dir"]}, {"attachments": {"image-2.png": {"image/png": "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"}, "image-4.png": {"image/png": "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********************************+jGL1vvvv++OPfbYvPzaPW3x2LdvX/fggw/6fD7//PPu3nvvrfo8t8VyUpqbWloFmGpAJAFJQBKQBDJJoGTAfOSRR9yPP/7ovv32WzdhwgR39NFHuw8//DAjIP3000/urbfeyng9bMRzASbvPgZwv/vuu7zCC8OupN8hYC5cuNDNnDkzlfzQAZg4cWIqYRUjr9aOv5g0656mEJlNHgLMTNWqzksCkoAkIAmUDJjPPfdcE4C5+eab3fDhw5ucs0aKBsl+53Nsj4CZj1zy9dPagNfa8ecrJ/nLHypDWQkw1YBIApKAJCAJZJJA6oB52223uYEDB3qQBBBPOOEEN2LECHfSSSe5OXPmuBdffNFdfPHFEWh+/PHHDhBhKPyOO+5wWPMeeOCB6H7Ov/rqq+7MM8/0YTz88MNuw4YNPhwbnueIJXX16tXeovnaa6+5Sy+91B1//PGO9CxfvjyK75dffvHnSBdD+jNmzIiu8T5l4iatp59+ug+zoaHBX892LWx0+Y1Fl7wcd9xx7owzznCjRo2K4uD6J5984uMmDQyH9+jRIxoiHz16tLv++uu9/5UrV/r8LFq0KLr/ySefdDfddJP/jxz4TxykmbD++OMPh/9QNldeeaX3j4yIq0+fPr5cyCNTFigDZEF6kBeWYctTJnllk3Wm+C1MHYsDukqTWzkAc/fdd3dvv/12pvpK5yUBSUASkATaiARSA0wam/fee88dc8wxHlhoDAFMQAfgBDqAkhAwV61a5cGoX79+7uuvv/awA5SFgMn93bt399enTZvmw//ggw/cunXr3K+//urDZ1h57dq1EWACrEDcp59+6s4991z36KOPemBas2ZN9P/nn3/28TF39IcffvDXmQcJvAGI3A+AMSeSvGS7Fjb8yOHss892gwcPdr///ruXxYknnuhIO/4sz0AeeX7jjTc8XNsczEIAE5AmjQsWLHDff/+9u/HGG72sSAOyBhrHjx/v6urqfNwAJuXDPFmmNAwdOtSR/8suuyySF7J76KGHvP9s8jLATJJ1pvhDOel324dMypnnED2hA7ZkyZKSqz06O+hWSzk6U5MnTy4oOuodnq199tnHHXDAAf65Qwb5OqYQAdJ0BuOO6TFco1M8a9Ys/5v6KXQ87/jh2Q7dli1bfJqeeuopP00JP/bZa6+93H/+8x936623+ro4vC+f36XmOZ845EcSkASqSwIlAybAgpWRIzDYv39/DycABIDJebMCci4ETCyTp512modDA464BZP7aXDs+rXXXuvGjh3r/8fnYBr0YCk1/0888YS75ZZb/H+sdTQMdo0jFj2zMF511VXu8ccfj64DmTafNNu1MDzy+uWXX/pG187fddddbtiwYT5cGrNTTz21SZ579epVlAUTcEYeWDKJi/mtr7/+epR+4C+cgwlgmizwb/KaOnVqdA+ytUVY2eRl92aSNeHH4zd56Nj24ZIyLAdgtnT1ir5TJ+TrNm/e7EGNZ4ROKh016rALLrgg3yAi+MsFmADjvvvu60aOHNkk7CFDhrj99tvPXXHFFU3O8ywClL/99lsUxxdffOFhlbrh3Xff9SM7HTp08J3MJjdn+ZNGnrMEr0uSgCRQpRIoGTCBFix+9LIZbg6tfklzKEPABJAYkg2BY8CAAU0smPFV5ACSQWAmwPz888+jMENgYm4owMpQsH34jzWRNFABcx4rJpZLLDKWtmzXzI8dsSbefffdflgf6yVxYNHkOnkOIY9z4SKfQiyYNBrnnHOObyAB6a+++ipKL+HGAY+yuv3225v4oVMwb9686FxYPtnkZYCZSdZJ8XNOn+qRQTkAE0CyIXLAb9CgQY4OGM/QoYce6q2FAA/uo48+8kCF1e+UU05xhx9+uJ8WQ8cQ99dff0XX/Qnn3N9//x2d43kAtvbcc09v+QMYczkWKGK5RI/NMY2EdFMP5uPytWAS1tVXX+2f8TBcpsPQkScdQKg5RkF4nnHZ4gBQ9957bw+edm+2Yxp5zha+rkkCkkB1SqBkwIwv8gHozj//fF8B5wJMoCgOPOUGTOCRhiT8LFu2LAIfhvqw3N1zzz1+DmW4ojvbNQOn+vr6aN4lw+Gc79mzZwSYjz32mB/GNv8ciwVM7mVqAHJmWBs4NvjmWhqAmUleAszqAcVQFwv5XW7AZOoGQ9AMFeNqamr8f+oc3Pz58z3YMboATOKAJ6x+PIe5ABP/d955Z0EWTJ5V5oPH3YEHHuhHC+Lnk/5ng79wiJx7X375ZQ/BTEXAMS8bmKXOOuigg/y8doujS5cuHjz5ny2OTZs2ecCkrs7HpZHnfOKRH0lAEqguCZQFMG0YOhdgMpyLxZPK0xo2rInhHMxsFkwqWXrstk1RLuhh7iHD0zYnkTi5hyOgBpwxb9LSArRhbcx2zfzakXmVpMngkvMs+DELJvMmmRdqw9pcJ89JczCRC9s+0ZBa+FhAgT7+v/TSS37upF2bPn26t/RY2JRDfIg8DvTZLJjZ5JVL1qQpHr+lU8fqgNOWAEw6q6Gj00SHDWeAiSXTHGBpVtByACYjLiyaizueI+qPfJzBH5bT+GePPfbw6WcOJo65nZybMmWK/z9p0iQ/b5o/N9xwg+vdu7c/v3jxYn8fMsFZHEnD8FynXqWezcelked84pEfSUASqC4JlAyYAA/WQIZr33nnHQ9wWCaBiFyACaScfPLJDqulLfKxii/T/eEQOeAHgGHRYDg7F/RwHesDDRRQyvwkVrSTbuJj5TnzJRnuZ1ESDQmr1nNd47p9aBjIA3Jh8RGrvGl8WMiEH8vzmDFj/G8WIjHklQSYFi+rw5nrBUAiLwNMFunQ4H722WeOYTosDeEKfVajk1eG7gir0CHybPLKJWvii8fPOX2qRwYtAZjMkQ4dVk06STgDTCyboWPomKkm5QJMLKZxxzNOvZePM/hjziR1TfgZN25cE8AkPOosIA/HM0VdgsO6Sbw46sCDDz7Ybd++3f+3ODIBZqdOnSJrp78hyxdxl5rnLMHrkiQgCVSpBEoGTCo4PoAeljkqWcAPkMgFmPhhvhSQxPAu8xYBPIOtpPtDwOR+4uNehp7zgR5ADUADArGessCHhpKwWCnJinW2N2JOF5ZFs0Rmu8a94QdgPe+88zw4EgawaXnCX5hnVnUCkHY9nIOJX+Y4XnLJJdHcUGDcABPrBlZWrLLIgD1Imf9pabG5aSwE4lyhgMk9meSVj6zj8Vu6dGyqL21VHi0BmAxhhy4JMJmLHTrmFzKHOgkwmbOIhRPdxBU6RM7cR7YFizuG8rEu5uOywV98iJzw6KQy/3Tbtm3uXwcc6K2TnLfh8qVLl/qdIAy8uZYtDuo0LKcAaj4ujTznE4/8SAKSQHVJoCTATKNhNBi1sICncB6hnW8vRwCT7YbaS36Vz7YLm5UCmFgCzTFPGoBkyzSseYAUW4SZY4V1KYBJWFhImYZijk4YYdIJzcdlg78kwCRcwmcHivjCHjrDL7zwgp93GuYzWxx05AFidrzIx6WR53zikR9JQBKoLgm0KmDSk2Z7D1ZoU4mygpnVogyXt0fwYNgeC63NQW2PMlCe2w5wVgpgMtcXsMRiyeI8hor5jWOfV+ZA47Zu3eq3JWPluFkwedbww9xNW53uPWf4wgLauXNnP2TM1CDqKiDv8ssvz3BH89PZ4C8JMAmBUSLqRiy4oWOPYc5jtQ2h1+KwbYoYKme3CEYz2BOT6Tb5ujTynG9c8icJSALVI4FWBUxggoqOuY8MWXOkgm2vkHHWWWf5FfjtFbDba7m31XxXCmBiYQMSse6xLRhQZY45zlw78sgj/TWm3TDMzDQWHPOkGX7Gojd37ly7LeuRBTXMdSY+7mNoOl9rIAEb/CXNj8wEmAxTY8W0FfSWQDam5zzTaEJncXCNDwuFyCdzOJlfXqgrNc+Fxif/koAk0PYl0OqA2VYbV6W77VjaVFblKatyAGYhVaot8inkLTqFhC+/koAkIAlIAsVLQIAZW6AjGCkPjEiu1SdXAWbxFa/ulAQkAUmg2iUgwBRgttspCYLe0qC32gCTbckY8s72YSV3Nsfm59nuZ2i9klwaea6k/CgtkoAkUDkSEGAKMAWY0oGidKC1AbNyqlGlRBKQBCQBSSAuAQGm4KIouJD1rzTrXzXIT4AZr071XxKQBCQBScAkIMAUYAowpQNF6YAA06pRHSUBSUASkATiEhBgCi6KgotqsMApD6VZYQWY8epU/yUBSUASkARMAgJMAaYAUzpQlA4IMK0a1VESkAQkAUkgLgEBpuCiKLiQ9a806181yE+AGa9O9V8SkAQkAUnAJCDAFGAKMKUDRemAANOqUR0lAUlAEpAE4hIQYAouioKLarDAKQ+lWWEFmPHqVP8lAUlAEpAETAICTAGmAFM6UJQOCDCtGtVREpAEJAFJIC4BAabgoii4kPWvNOtfNchPgBmvTvVfEpAEJAFJwCQgwBRgCjClA0XpwIYNG9y6detcXV2dW7FihVu6dKnVKzpKApKAJCAJtHMJCDAFF0XBRTVY4JSH0qywAGZDQ0MEmH/++Wc7r06VfUlAEpAEJAGTgABTgCnAlA4UpQMGmPX19W7VqlWupqbG6hUdJQFJe3iXRwAAIABJREFUQBKQBNq5BASYgoui4ELWv9Ksf9UgPwCTeZhr1651a9asiYbJf/vtN6ePZCAdkA5IB9q3DggwBZgCTOlA0ToQWjFXr17twbKdd9qVfUlAEpAEJAHnnABTcFE0XFSDFU55KM0Sa1ZMFvswVI7FQk4SkAQkAUlAEhBgCjAFmNKBknTAIJMFPwJMNSqSgCQgCUgCSECAKbgoCS5kASzNAlgt8gMy+Qgw1bBIApKAJCAJIAEBZisBZt38Sa7mmcvdssH/9R9+181/WbDXSuVRLaDX2vkQYKphkQQkAUlAEkACAswWBpoN9Ss8WC7v09ElfQBN/LQ2KLS3+GfOnOm6d+/uPz169HC9e/d2EyZM8KujkcWsWbOi6+aP47x583xZff75527o0KHu7rvvdg8++KCbPHmyX12d694xY8YkhkvY7777bpvTAwGmGhZJQBKQBCQBJCDAbGHArH3mcg+Wy3onAybQCWS2N8Br7fwCmAAi2+4wl3DJkiVu+PDh7oUXXvBlAWDadfzYh2HhX375xYPlggUL/KbjixcvdiNGjPCAaoCZ6V6bv0h4Tz75pJs6dWqTsFtbLoXGL8BUwyIJSAKSgCSABASYLQiYDIsDkNng0qya+C20cZf/4udDGmCGMvzggw/c4MGDmwBmeN1+z5kzxw0cOLBJeX3//ffN4NT8ZzoCmNOmTWsSTia/lXpegKmGRRKQBCQBSQAJCDBbEDDNemkQme2I30qFiGpMVwiYWBV5rzZWyJdeeiknYPIGm/vvv9+9+uqrbtmyZc3KzayfueQmwFSlLAlIApKAJFAtEmgVwASelvXNPEScDbza8rV8LJeWv6UDjnbvvfdeu/rkArByXg/nYNocy0mTJjn2dyTepDmYDHtbmmpra90rr7zievXq5YYMGeJmz57tV1Xnc6+FIcCslmpV+ZAEJAFJQBJoPcDMsMjFAKsajwUBZv+u7QougWkDrdY4hhZM4mf+5dtvvx2lKV8rJHMpFy1a5IfWAVTCyvdeAaYqZElAEpAEJIFqkUCrAGZrAEQlxKkh8uLnSJa7/OKA+eWXX7qePXs2WUUeWizD9MyYMSNaTW7nWVV+3333CTCrpaZUPiQBSUASkAQKkoAAswXnYNoin3yss1rk07IwGgdMQBEr5ptvvpkTEtmq6IEHHnBff/2135qI4fJnnnnGPfHEEznvNSDlKAtmQXWXPEsCkoAkIAlUsAQEmC0ImEBEPlZM/ITgod/lh80kwPzqq6/cvffe61avXp04B5O5mtxH+QCZDz/8sLvrrrs8bD733HMO0ORa0vzN8F4rXwFmBdeUSpokIAlIApJAQRIQYLYwYLKJejbI5Jo2Wi8/UBrU6ZiurLVNUUH1rzxLApKAJFC1EhBgtjBgGtAwBA5MLht0jP/wW8Pi6cKOyVrHlpOrALNq2wplTBKQBFpAAmvuOceFn4kXdHW77767m3n5cU3Oh35K/V2ubAkwWwkwBT0tBz2SdcvJWoBZrqpa4UoCkkB7kEAcFkPAXNr9dHdt54Pd/3bYwx22zz/c42d29NC56u6z3J3dDnX7/2MPd+Ih+7ij9tvbXfyfA/y1ficd4Q7cu4Pbf68O7sauB7uaO89sBqrlkqsAU4Cp+Z7SgdR0IA3APProox0r8yvNsW1V586d3V577eWOP/5499lnn+VM4sqVK7314Ysvvmjm98wzz/TvrefCRRdd5HctCD3ddttt7uKLLw5Pud69e7uzzjrLn7vnnnt82Fg39tlnH3fqqae66dOnN/Gf7Q9vmzr99NPd3nvv7Y488kj/5qkk/7wu9ZprrvGXevTo4fr375/krWLOoYP//ve/3d9//+3TVEy5VUxmlJB2J4FsgHn3sYe6vfbc3Y0+u5Pr/t9D/fM/7bJjnUHond3+Pzfm7E5urz1294D58bUnej+DTj7SPX9+F3fw//7DTbigqwBTVqeWszpJ1pJ1WjqQBmACNH/++WeLNCxdu3Z1LMjK5chXhw4d/Er/H3/80b+56aCDDvLvjc92b76AyRZYp5xySpOggL599903AiUuApcGeAAm/3/55Rf33XffubFjx3r/48aNaxJO0h/g66ijjnI33XSTAzQnTJjg85cEzcTxzjvv+GB4feqnn36aFGTFnONFBw8++KBPT7HlVjGZUULanQSyAWan/fZ25x2xvwfE2jvPdB322N3dd/xh7q6d4Fl7V6N18oh9Gy2YP992mvvXXh1cpwP2dn1OPMwBnPHw+V8uJwumrFepWa/SghSF03aBNw3ALFdllxRuvoD5yCOPuJNPPjkKYsuWLe6AAw7wm/FHJxN+5AuY77//vreMEi7u999/95bFww47zC1cuNCf27Ztm7dU8r56HICJ5TN0kydP9n7WrFkTnm72G0gEmOvr66Nrl1xyid+7NTrhnPvjjz8cIG3pCq9V4u8dO3a4jh07OnaAwBVbbpWYN6WpfUggDoBmnWQOZrcD/9ddcGTj0DeAuefuu7v7jz/MecvmHru7FXed5QHSAJOwfrr1VDfstP/jTv33fm6P3f/HTb302GaQWS7JCjAFmAJM6UBqOpAGYIbg1q9fP3fVVVf5rZ/2339/B3DxhiQc74xnePjRRx91gCJDvZdddpnfHJ/rvOaT699++21Uf7KV1DnnnOPPcc0+ITxGnoMfN954o2N4OHQML7NXajaXL2CuXbvWp4UN/nEvvviiT+f111/vRo4c6c9haSS9bH+FSwJMIJTh4YkTJ3o/mb6wEmPBDN3gwYPdueeeG57ysmWrLnNXXnmlt97yny26iIv9XjlibWU/2GXLlrmzzz7bl8exxx7reOmAOaytDPv/85//dIceeqh/rapd4/j000/7sChrwqJcxo8fH3mZM2eOO+6443xcxPHDDz9E1/gxf/58xxQLc8WWm92voyTQ0hLIBpj3HXeoH/4ee24nD5XUBwDjpAuP8XUDoBkOkXOe+ZrPndfFTb20m/fzxM55m2E85cqjAFNwkRpcyPLYdi2PaZVdOQBzjz328K/eBLAGDRrkwaWuri4CTOYHslcpcTM3krmLuGyAuX37dn8/YDp69Gi3efPmrHXspZde6oDd0HGuT58+4almv/MFTG4kLTa8DRhhfXv++eejeZiANcPm5pIAk2vM7bRhdPMbP44aNaqJRZbrnDvxxBObeCVN4bB5HDBp4HhjFWUD1PIfcOQFBYD9FVdc4Y455hgfJvBL+kk3UyDYOxaQfOutt/z1Tz75xFHWTz31lA+PPBCeASbTAJhripUWCy/D4FgrQ+vq3Xff7aHYMlFsudn9OkoCLS2BEPz4HVow/+h+uruhy8F+MQ+LfB45/f94a+Tqu8/yw+Qs5Dn5kH38gp7LOh7oWPzT+8Qj3KH7/MPt22EPd1Wng9yfPc6QBTOtBk/hCHqkAy2nA+UATBavmPvrr788dGCpMgtmuICGTe0Z+sVfNsC08ACofOZgMhQdB0yspTbXz8KLHw0wWRiEhTX8AE/h/YDxHXfc4YMA0rD8/frrr9E8TF5dikXTXCbABOrY8D+bw+oYt9oCdgC6OaypcStnHDDJF+Bo7qSTTvIAaf+BU/K5adMmP5eUoWt+myOtvXr18n+ZD4q1OnQApAHm7bff3iRs4gVQAVUcoHnggQf6YX0Lo9hys/t1lARaWgJxwMznPyvDHzj+MDflkm7uyTM7ut3/53/c4JOPbAaSmcIqVx5lwZQFUxZM6UBqOlAOwIzPM9xzzz0di02SALOmpsYDDelIEzCZnxi3ViZZx+IVtQEmVjqGh8MPMBYCJtbKbt26uZ9++smDk4Hb4Ycf7udhsghozJgxURSZAPO0005rBsPRTTt/8NaouLUybtVkiJoFM6GLAybAHDqmHzANwRxWTADT5oSyQIowmOoAHNIZIB84OhKPPfaY3eqPnTp1igAT+AVosWLah7BtygRzUxk2D12x5RaGod+SQEtKIBMEZju/5I7T3dWdDmrcjugfe7hbjk7ejihTGOXKnwBTcJEaXMhS2HKWwkqVdWsD5tKlS8sCmFjXzLpolTEgx/zPbM4AM7Symv9wmyLOffPNN36IGPjDsmfu1ltv9fEAV+F8xiTAZHU4i3KYw5nNcR3rYOiYfnDBBRf4U4TDvMp4eZYCmFiVbd5lQ0ODj+faa6+NABN5YlkNXRwwGTZneDz8WFhXX321Xw0f3l9suYVh6Lck0JISyASB5Tpf17vpVmhp5lWAKcAUYEoHUtOBOJAUU1nFF/nksmAuWLAgioY9D22IfOvWrR7YGE43B6BgZTPHgpB8hsgff/xxv7jE7mM4lnTm2neyEMDEYolljoUxzz77rEXlLXScAzDD+YZJgAk4YlVctWpVdH/SD0AVSzBzV81h7bPh6vfee89bFO2aHUsBzEWLFnn4NyAkTBb8mAWTKQIAZ+j+85//RBZM5qVefvnl4eVomygWSWERxWodumLLLQxDvyWBlpTAunED8h7aTgM6GyaOKFv2BJiCi9TgolKtakpXy1lWWwMwzzvvPL+aGJBkzmD37t2jCpMVx1i2WIQyZcoU968DDmwCmFgRgRqGrrO5JUuWeHAFWJgXCYgdcsgh/tnJdl8hgEk4wC/Dvj///HMULAtiOBfOReUiYMaQMNY88oflk9XZzzzzTHRvph8scmL+KXM6iYsFOoC5bYkEzCWBdymACcwCv0A+8zsZSidfZhmm/IBeVpKTn/giH+ZvsgiIxU/kmSHxgw8+2DEtglXxcTgl78WWWya56bwkUG4J7Ni03gGZWBbTAMhsYQCXxFcuJ8AUYAowpQOp6UBrACagwkpl4AULF9YscwxNY/***************************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"}}, "cell_type": "markdown", "metadata": {}, "source": ["Expected training curve on TensorBoard:\n", "![image-2.png](attachment:image-2.png) ![image-4.png](attachment:image-4.png)"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["## Visualizing training status in MLFlow\n", "\n", "As `mlflow` is not IPython component, please switch to the `log_dir` and execute command `mlflow ui` to launch MLFlow UI.\n", "\n", "Expected training curve on MLFlow UI:\n", "\n", "![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}