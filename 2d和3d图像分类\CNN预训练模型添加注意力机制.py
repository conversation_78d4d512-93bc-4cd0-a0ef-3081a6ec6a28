#!/usr/bin/env python
# coding: utf-8

##
from torch.utils import data
from torchvision.datasets import ImageFolder
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import torchvision
from torchvision import transforms
import os
from tqdm import tqdm
import shutil
import pandas as pd
from sklearn.metrics import precision_recall_curve, average_precision_score
from sklearn.metrics import roc_curve, auc
from sklearn.metrics import confusion_matrix
from scipy import interp
from itertools import cycle
import itertools
import matplotlib.pyplot as plt
from torchvision import models

## In[2]:
import random
def setup_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
# 设置随机数种子
setup_seed(20)
##
base_dir = r'I:\1.HCC-VETC\datasets\HCC-suzhou2\tumor'

# base_dir = r'D:\python日月光华深度学习\2.卷积部分(第8-11章)参考代码和数据集\datasets\4weather-train-test'

# base_dir = r'E:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\2.卷积部分(第8-11章)参考代码和数据集\datasets\EGFR'

train_dir = os.path.join(base_dir , 'train')
test_dir = os.path.join(base_dir , 'test')


#不做数据增强

train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                             std=[0.229, 0.224, 0.225])
   ])


test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])


#%%
#  In[4]:

#     # #做数据增强
# # train_transform = transforms.Compose([
# #     transforms.Resize(224),
# #     transforms.RandomResizedCrop(192, scale=(0.6,1.0), ratio=(0.8,1.0)),
# #     transforms.RandomHorizontalFlip(),
# #     transforms.RandomRotation(0.2),
# #     torchvision.transforms.ColorJitter(brightness=0.5, contrast=0, saturation=0, hue=0),
# #     torchvision.transforms.ColorJitter(brightness=0, contrast=0.5, saturation=0, hue=0),
# #     transforms.ToTensor(),
# #     transforms.Normalize(mean=[.5, .5, .5], std=[.5, .5, .5])
# # ])


# test_transform = transforms.Compose([
#     transforms.Resize((192, 192)),
#     transforms.ToTensor(),
#     transforms.Normalize(mean=[.5, .5, .5], std=[.5, .5, .5])
# ])


## In[5]:


train_ds =  torchvision.datasets.ImageFolder(
        train_dir,
        transform=train_transform
    )


# In[6]:


test_ds =  torchvision.datasets.ImageFolder(
        test_dir,
        transform=test_transform
    )

#%%

print(train_ds.class_to_idx)   #ImageFolder方式导入的文件夹名和对应label
# print(test_ds.class_to_idx)
print(train_ds.imgs[:5])
# print(dataset[0][1])# 第一维是第几张图，第二维为1返回label
print(train_ds[0][0].size()) # 深度学习中图片数据一般保存成CxHxW，即通道数x图片高x图片宽

## In[7]:

BATCH_SIZE = 32

train_dl = torch.utils.data.DataLoader(
                            train_ds,
                            batch_size=BATCH_SIZE,
                            shuffle=True
)

test_dl = torch.utils.data.DataLoader(
                            test_ds,
                            batch_size=BATCH_SIZE,
)

for inputs, targets in test_dl:
    print(inputs.shape)
## In[10]:
# #预训练模型选择torchvision.models.vgg16,19 （mobilenet_v2，resnet18，AlexNet,DenseNet,densenet121,161,169,201;
# GoogLeNet;Inception3,inception_v3,resnet18,34,50,152;）； squeezenet1_0；mnasnet1_0;shufflenet_v2_x1_0;efficientnet-b7

model = torchvision.models.vgg19 (pretrained=True)
# # model = torchvision.models.vgg19 (pretrained=True)
model = torchvision.models.resnet18(pretrained=True)
# model = torchvision.models.resnet101(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
model = torchvision.models.densenet161(pretrained=True)
model = torchvision.models.googlenet(pretrained=True)
model = torchvision.models.alexnet(pretrained=True)
model = torchvision.models.mobilenet_v2(pretrained=True)
model = torchvision.models.mnasnet1_0(pretrained=True)
model = torchvision.models.mobilenet_v3_large(pretrained=True)
model.parameters

## 给rennet50添加注意力机制
# 定义注意力机制模块
class Attention(nn.Module):
    def __init__(self, in_channels):
        super(Attention, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, 1, kernel_size=1)
        self.sigmoid = nn.Sigmoid()
     def forward(self, x):
        attention = self.conv1(x)
        attention = self.sigmoid(attention)
        return attention * x

 # 导入2D ResNet预训练模型
model = torchvision.models.resnet50(pretrained=True)

# 冻结除了注意力模块以外的权重
for param in model.parameters():
    param.requires_grad = False


# 修改模型，添加注意力机制
model.avgpool = nn.Sequential(
    nn.AdaptiveAvgPool2d(1),
    Attention(2048)  # Change this line
)

num_classes=3
model.fc = nn.Linear(2048, num_classes)
model

if torch.cuda.is_available():
    model.to('cuda')

loss_fn = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

## 2D DenseNet161预训练模型添加注意力机制，并仅仅训练模型中修改后的部分，其余部分仍使用预训练的权重

model = torchvision.models.densenet161(pretrained=True)
# 加载预训练的DenseNet161模型并冻结其所有层

for param in model.parameters():
    param.requires_grad = False

# 将最后一层全连接层替换为具有注意力机制的全连接层
class AttentionLayer(nn.Module):
    def __init__(self, in_features, out_features):
        super(AttentionLayer, self).__init__()
        self.fc1 = nn.Linear(in_features, out_features)
        self.fc2 = nn.Linear(out_features, in_features)

    def forward(self, x):
        y = torch.sigmoid(self.fc1(x))
        return self.fc2(y * x)

model.classifier = nn.Sequential(
    nn.Linear(2208, 512),
    nn.ReLU(inplace=True),
    nn.Linear(512, 256),
    nn.ReLU(inplace=True),
    AttentionLayer(256, 256),
    nn.Linear(256, 4)
)

# 冻结特征提取和分类层之外的所有层
for param in model.features.parameters():
    param.requires_grad = False
for param in model.classifier[:4].parameters():
    param.requires_grad = False

if torch.cuda.is_available():
    model.to('cuda')

loss_fn = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
optimizer = optim.Adam(model.classifier[4].parameters(), lr=0.001)
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

## 2D vgg19预训练模型添加注意力机制，并仅仅训练模型中修改后的部分，其余部分仍使用预训练的权重
# 定义注意力机制
num_classes=3

class Attention(nn.Module):
    def __init__(self):
        super(Attention, self).__init__()
        self.conv1 = nn.Conv2d(512, 1, kernel_size=1)
        self.sigmoid = nn.Sigmoid()
    def forward(self, x):
        attention = self.conv1(x)
        attention = self.sigmoid(attention)
        return x * attention

class VGG16Attention(nn.Module):
    def __init__(self):
        super(VGG16Attention, self).__init__()
        self.vgg16 = models.vgg16(pretrained=True)
        for param in self.vgg16.parameters():
            param.requires_grad = False
        self.attention = Attention()
        self.avgpool = nn.AdaptiveAvgPool2d(output_size=(7, 7))
        self.classifier = nn.Sequential(
            nn.Linear(in_features=512 * 7 * 7, out_features=4096, bias=True),
            nn.ReLU(inplace=True),
            nn.Linear(in_features=4096, out_features=4096, bias=True),
            nn.ReLU(inplace=True),
            nn.Linear(in_features=4096, out_features=num_classes, bias=True)
        )
    def forward(self, x):
        x = self.vgg16.features(x)
        x = self.attention(x)
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x

model = VGG16Attention()

## In[15]:

if torch.cuda.is_available():
    model.to('cuda')

loss_fn = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler

# optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.001)  #alexnet,vgg注意修改模型最后一层名称
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.001) #densenet模型
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

##定义训练函数
def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    model.train()
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)  # 计算损失
        optimizer.zero_grad()  # 梯度清零，否则会累加
        loss.backward()  # 计算梯度
        optimizer.step()  # 权重更新
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
    exp_lr_scheduler.step()  # 注意这个代码有学习率，因此这里需要加一行
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / total

    test_correct = 0
    test_total = 0
    test_running_loss = 0

    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()

    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / test_total

    print('epoch: ', epoch,
          'train_loss： ', round(epoch_loss, 3),
          'train_accuracy:', round(epoch_acc, 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
          )

    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc


## In[19]:

epochs = 30

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)

 ## 保存模型
torch.save(model.state_dict(), 'E:/resnet_with_attention.ckpt')

##ResNet50预训练模型的全连接层替换为SVM，并进行迭代
import torch
import torch.nn as nn
import torch.optim as optim
from torchvision.models import resnet50
from torchvision import datasets, transforms
from sklearn import svm
import numpy as np
 # 加载预训练的ResNet50模型并冻结其所有层
model = resnet50(pretrained=True)
for param in model.parameters():
    param.requires_grad = False
 # 替换最后一个全连接层为标识函数
num_ftrs = model.fc.in_features
model.fc = nn.Identity()
 # 定义用于训练模型的数据集和转换
base_dir = r'D:\python日月光华深度学习\2.卷积部分(第8-11章)参考代码和数据集\datasets\4weather-train-test'
train_dir = os.path.join(base_dir, 'train')
val_dir = os.path.join(base_dir, 'test')

data_transforms = {
    'train': transforms.Compose([
        transforms.RandomResizedCrop(224),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])]),
    'val': transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
}
train_dataset = datasets.ImageFolder(train_dir, transform=data_transforms['train'])
val_dataset = datasets.ImageFolder(val_dir, transform=data_transforms['val'])
train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32, shuffle=False)
 # 提取ResNet50模型的特征表示
def extract_features(loader, model):
    model.eval()
    features = []
    labels = []
    with torch.no_grad():
        for data, target in loader:
            feat = model(data)
            features.append(feat.cpu().numpy())
            labels.append(target.cpu().numpy())
    features = np.concatenate(features, axis=0)
    labels = np.concatenate(labels, axis=0)
    return features, labels
 train_features, train_labels = extract_features(train_loader, model)
val_features, val_labels = extract_features(val_loader, model)
 # 训练SVM模型
svm_model = svm.LinearSVC(max_iter=10000)
num_epochs = 10
for epoch in range(num_epochs):
    svm_model.fit(train_features, train_labels)
    train_acc = svm_model.score(train_features, train_labels)
    val_acc = svm_model.score(val_features, val_labels)
    print('Epoch {}/{} -- Train Accuracy: {:.6f} | Validation Accuracy: {:.6f}'.format(
        epoch+1, num_epochs, train_acc, val_acc))

##ResNet50预训练模型的全连接层替换为SVM，无迭代
import torch
import torch.nn as nn
import torch.optim as optim
from torchvision.models import resnet50
from torchvision import datasets, transforms
from sklearn import svm
import numpy as np
 # 加载预训练的ResNet50模型并冻结其所有层
model = resnet50(pretrained=True)
for param in model.parameters():
    param.requires_grad = False
 # 替换最后一个全连接层为标识函数
num_ftrs = model.fc.in_features
model.fc = nn.Identity()
 # 定义用于训练模型的数据集和转换
base_dir = r'D:\python日月光华深度学习\2.卷积部分(第8-11章)参考代码和数据集\datasets\4weather-train-test'
train_dir = os.path.join(base_dir, 'train')
val_dir = os.path.join(base_dir, 'test')
data_transforms = {
    'train': transforms.Compose([
        transforms.RandomResizedCrop(224),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])]),
    'val': transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
}
train_dataset = datasets.ImageFolder(train_dir, transform=data_transforms['train'])
val_dataset = datasets.ImageFolder(val_dir, transform=data_transforms['val'])
train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32, shuffle=False)
 # 提取ResNet50模型的特征表示
def extract_features(loader, model):
    model.eval()
    features = []
    labels = []
    with torch.no_grad():
        for data, target in loader:
            feat = model(data)
            features.append(feat)
            labels.append(target)
    features = torch.cat(features)
    labels = torch.cat(labels)
    return features, labels
 train_features, train_labels = extract_features(train_loader, model)
val_features, val_labels = extract_features(val_loader, model)
 # 训练SVM模型
svm_model = svm.LinearSVC()
svm_model.fit(train_features, train_labels)
 # 评估SVM模型
train_acc = svm_model.score(train_features, train_labels)
val_acc = svm_model.score(val_features, val_labels)
print('Train Accuracy: {:.6f} | Validation Accuracy: {:.6f}'.format(train_acc, val_acc))

##ResNet50预训练模型的全连接层替换为RF，无迭代
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.ensemble import RandomForestClassifier
from torchvision.models import resnet50
from torchvision import datasets, transforms
  # 加载预训练的ResNet50模型并冻结其所有层
model = resnet50(pretrained=True)
for param in model.parameters():
    param.requires_grad = False
  # 替换最后一个全连接层为标识函数
num_ftrs = model.fc.in_features
model.fc = nn.Identity()
  # 定义用于训练模型的数据集和转换
base_dir = r'D:\python日月光华深度学习\2.卷积部分(第8-11章)参考代码和数据集\datasets\4weather-train-test'
train_dir = os.path.join(base_dir, 'train')
val_dir = os.path.join(base_dir, 'test')
data_transforms = {
    'train': transforms.Compose([
        transforms.RandomResizedCrop(224),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])]),
    'val': transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
}
train_dataset = datasets.ImageFolder(train_dir, transform=data_transforms['train'])
val_dataset = datasets.ImageFolder(val_dir, transform=data_transforms['val'])
train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32, shuffle=False)
  # 提取ResNet50模型的特征表示
def extract_features(loader, model):
    model.eval()
    features = []
    labels = []
    with torch.no_grad():
        for data, target in loader:
            feat = model(data)
            features.append(feat)
            labels.append(target)
    features = torch.cat(features)
    labels = torch.cat(labels)
    return features, labels
train_features, train_labels = extract_features(train_loader, model)
val_features, val_labels = extract_features(val_loader, model)
  # 训练随机森林模型
rf_model = RandomForestClassifier(n_estimators=100, max_depth=10)
rf_model.fit(train_features.numpy(), train_labels.numpy())
  # 评估随机森林模型
train_acc = rf_model.score(train_features.numpy(), train_labels.numpy())
val_acc = rf_model.score(val_features.numpy(), val_labels.numpy())
print('Train Accuracy: {:.6f} | Validation Accuracy: {:.6f}'.format(train_acc, val_acc))


#%%CBAM 注意力机制的pytorch版本   CSDN：注意力机制的原理及实现（pytorch）
import torch
import torch.nn as nn
import torchvision
 
 
class ChannelAttentionModule(nn.Module):
    def __init__(self, channel, ratio=16):
        super(ChannelAttentionModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
 
        self.shared_MLP = nn.Sequential(
            nn.Conv2d(channel, channel // ratio, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(channel // ratio, channel, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
 
    def forward(self, x):
        avgout = self.shared_MLP(self.avg_pool(x))
        print(avgout.shape)
        maxout = self.shared_MLP(self.max_pool(x))
        return self.sigmoid(avgout + maxout)
 
 
class SpatialAttentionModule(nn.Module):
    def __init__(self):
        super(SpatialAttentionModule, self).__init__()
        self.conv2d = nn.Conv2d(in_channels=2, out_channels=1, kernel_size=7, stride=1, padding=3)
        self.sigmoid = nn.Sigmoid()
 
    def forward(self, x):
        avgout = torch.mean(x, dim=1, keepdim=True)
        maxout, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avgout, maxout], dim=1)
        out = self.sigmoid(self.conv2d(out))
        return out
 
 
class CBAM(nn.Module):
    def __init__(self, channel):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttentionModule(channel)
        self.spatial_attention = SpatialAttentionModule()
 
    def forward(self, x):
        out = self.channel_attention(x) * x
        print('outchannels:{}'.format(out.shape))
        out = self.spatial_attention(out) * out
        return out
 
 
class ResBlock_CBAM(nn.Module):
    def __init__(self,in_places, places, stride=1,downsampling=False, expansion = 4):
        super(ResBlock_CBAM,self).__init__()
        self.expansion = expansion
        self.downsampling = downsampling
 
        self.bottleneck = nn.Sequential(
            nn.Conv2d(in_channels=in_places,out_channels=places,kernel_size=1,stride=1, bias=False),
            nn.BatchNorm2d(places),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=places, out_channels=places, kernel_size=3, stride=stride, padding=1, bias=False),
            nn.BatchNorm2d(places),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=places, out_channels=places*self.expansion, kernel_size=1, stride=1, bias=False),
            nn.BatchNorm2d(places*self.expansion),
        )
        self.cbam = CBAM(channel=places*self.expansion)
 
        if self.downsampling:
            self.downsample = nn.Sequential(
                nn.Conv2d(in_channels=in_places, out_channels=places*self.expansion, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(places*self.expansion)
            )
        self.relu = nn.ReLU(inplace=True)
 
    def forward(self, x):
        residual = x
        out = self.bottleneck(x)
        print(x.shape)
        out = self.cbam(out)
        if self.downsampling:
            residual = self.downsample(x)
 
        out += residual
        out = self.relu(out)
        return out
 
 
model = ResBlock_CBAM(in_places=16, places=4)
print(model)
 
input = torch.randn(2, 16, 64, 64)
out = model(input)
print(out.shape)
 