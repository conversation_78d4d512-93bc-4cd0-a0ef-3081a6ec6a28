Modules
=======

This page provides an overview of all the Modules available in PyCaret. A Module is a building block for creating experiments. Each module encapsulates specific machine learning algorithms and functions that are consistently used across different modules. For example, the ``create_model`` function trains and evaluates a model in all modules. Depending on the type of experiment, one of the six available modules must be imported into the environment. 

- :ref:`Classification`

- :ref:`Regression`

- :ref:`Time Series`

- :ref:`Clustering`

- :ref:`Anomaly Detection`

- :ref:`Natural Language Processing`

- :ref:`Association Rules`