{"cells": [{"cell_type": "markdown", "metadata": {"id": "Y57RMM1LEQmR"}, "source": ["#  <span style=\"color:black\">２値分類のチュートリアル - 初心者向け</span>"]}, {"cell_type": "markdown", "metadata": {"id": "GM-nQ7LqEQma"}, "source": ["**Created using:** PyCaret 3.0.0rc6 <br />\n", "**Date Updated:** January 25, 2023\n", "\n", "# チュートリアルの目的\n", "初心者向けの２値分類のチュートリアルへようこそ。 このチュートリアルは、PyCaretが未経験の人を前提に作成されています。\n", "\n", "このチュートリアルでは、次のことを学びます。\n", "\n", "* **PyCaret のインストール:** PyCaretのインストール方法\n", "* **はじめに:** ２値分類の紹介\n", "* **データの取得:** PyCaretのデータセットリポジトリに含まれるサンプルデータを取得する方法\n", "* **セットアップ:** PyCaretでの実験を初期化する方法\n", "* **モデルの作成:** 交差検証を使用したモデルをトレーニングする方法\n", "* **モデルの調整:** モデルのハイパーパラメーターを自動で調整する方法\n", "* **モデルのプロット:** 描画機能を使用してモデルのパフォーマンスを分析する方法\n", "* **モデルの完成:** 実験の最後に最適なモデルを完成させる方法\n", "* **モデルの予測:** 新しいデータや未知のデータを予測する方法\n", "* **モデルの保存:** 今後の使用のためにモデルを保存する方法\n", "* **モデルの読み込み:** 保存したモデルをファイルから読み込む方法\n", "\n", "**読了時間:** 約45分 <br />\n", "**実行時間:** <5 分\n", "\n", "\n", "# PyCaret をインストール\n", "pip を使用して pycaret をインストールできます。\n", "\n", "`pip install pycaret` <br />\n", "\n", "すべての依存関係を含むフル バージョンをインストールするには、次を使用します。 <br />\n", "\n", "`pip install pycaret[full]`\n", "\n", "\n", "＃関連チュートリアル：\n", "- [２値分類チュートリアル - 中級レベル](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)\n", "- [２値分類チュートリアル - エキスパート レベル](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Expert%20-%20CLF103.ipynb)"]}, {"cell_type": "markdown", "metadata": {"id": "2DJaOwC_EQme"}, "source": ["# 二値分類とは？\n", "二値分類は教師あり機械学習の手法の一つで、合格/不合格、陽性/陰性、デフォルト/非デフォルトなど、順序のない離散的なクラスラベルの予測を目的としています。以下に、分類の実際の使用例をいくつか示します。\n", "\n", "- 患者がある病気に罹患しているかどうかを判定する医療検査 - 分類特性は病気の有無です。\n", "- 工場における「合格・不合格」のテスト方法や品質管理。つまり、ある仕様が満たされているか満たされていないかの判断 - 通過/非通過の分類。\n", "- 情報検索のおいて、記事を検索結果に含めるかどうかを決定します。分類特性は、記事の関連性、またはユーザーにとっての有用性になります。\n", "\n", "[二値分類についてより詳しく](https://towardsdatascience.com/introduction-to-binary-classification-with-pycaret-a37b3e89ad8d)"]}, {"cell_type": "markdown", "metadata": {"id": "aAKRo-EbEQml"}, "source": ["# データセット"]}, {"cell_type": "markdown", "metadata": {"id": "VLKxlFjrEQmq"}, "source": ["このチュートリアルでは、UCIのデータセットである **Default of Credit Card Clients Dataset** を使用します。このデータセットには、2005年4月から2005年9月までの台湾のクレジットカード顧客の支払いの未納、人口統計学的要因、信用データ、支払い履歴、請求書に関する情報が含まれています。サンプル数は24,000、特徴量は25です。各カラムの簡単な説明は以下の通りです。\n", "\n", "- **ID**: 各顧客のID\n", "- **LIMIT_BAL**: 与信額（単位：台湾ドル）（個人、家族・補填を含む。\n", "- **SEX**: 性別 (1=男性、2=女性)\n", "- **学歴**: (1=大学院、2=大学、3=高校、4=その他、5=不明、6=未定)\n", "- **結婚歴**： 結婚の有無（1=既婚、2=独身、3=その他）。\n", "- **AGE**: 年齢\n", "- **PAY_1～PAY_6**： nヶ月前の返済状況 (PAY_1 = 先月 ... PAY_6 = 6ヶ月前) (ラベル: -1=正常に支払う, 1=1ヶ月の支払い遅延, 2=2ヶ月の支払い遅延, ...。8=8ヶ月の支払い遅延, 9=9ヶ月以上の支払い遅延)\n", "- **BILL_AMT1〜BILL_AMT6**: nヶ月前の請求明細の金額 ( BILL_AMT1 = last_month ... BILL_AMT6 = 6ヶ月前)\n", "- **PAY_AMT1 to PAY_AMT6**: nヵ月前の支払額 ( BILL_AMT1 = last_month ... BILL_AMT6 = 6 ヵ月前 )\n", "- **default**: デフォルトの支払額 (1=yes, 0=no) `目的編数`.\n", "\n", "# データセットへの謝辞:\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013). UCI Machine Learning Repository. Irvine, CA: University of California, School of Information and Computer Science.\n", "\n", "元のデータセットとその説明は[こちら](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)で確認できます。"]}, {"cell_type": "markdown", "metadata": {"id": "Ui_rALqYEQmv"}, "source": ["# データの取得"]}, {"cell_type": "markdown", "metadata": {"id": "BfqIMeJNEQmz"}, "source": ["`credit`データセットは`datasets`モジュールの`get_data`関数を使用して読みこむことができます。  \n", "[Source](https://archive.ics.uci.edu/ml/datasets/default+of+credit+card+clients)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 300}, "id": "lUvE187JEQm3", "outputId": "eb3672a9-42b7-4fca-a043-2bc47f408d83"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0      20000    2          2         1   24      2      2     -1     -1   \n", "1      90000    2          2         2   34      0      0      0      0   \n", "2      50000    2          2         1   37      0      0      0      0   \n", "3      50000    1          2         1   57     -1      0     -1      0   \n", "4      50000    1          1         2   37      0      0      0      0   \n", "\n", "   PAY_5  ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0     -2  ...        0.0        0.0        0.0       0.0     689.0       0.0   \n", "1      0  ...    14331.0    14948.0    15549.0    1518.0    1500.0    1000.0   \n", "2      0  ...    28314.0    28959.0    29547.0    2000.0    2019.0    1200.0   \n", "3      0  ...    20940.0    19146.0    19131.0    2000.0   36681.0   10000.0   \n", "4      0  ...    19394.0    19619.0    20024.0    2500.0    1815.0     657.0   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  default  \n", "0       0.0       0.0       0.0        1  \n", "1    1000.0    1000.0    5000.0        0  \n", "2    1100.0    1069.0    1000.0        0  \n", "3    9000.0     689.0     679.0        0  \n", "4    1000.0    1000.0     800.0        0  \n", "\n", "[5 rows x 24 columns]"], "text/html": ["\n", "  <div id=\"df-18097aa6-98e9-46b6-9f26-449b2b16d442\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>689.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331.0</td>\n", "      <td>14948.0</td>\n", "      <td>15549.0</td>\n", "      <td>1518.0</td>\n", "      <td>1500.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>5000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314.0</td>\n", "      <td>28959.0</td>\n", "      <td>29547.0</td>\n", "      <td>2000.0</td>\n", "      <td>2019.0</td>\n", "      <td>1200.0</td>\n", "      <td>1100.0</td>\n", "      <td>1069.0</td>\n", "      <td>1000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940.0</td>\n", "      <td>19146.0</td>\n", "      <td>19131.0</td>\n", "      <td>2000.0</td>\n", "      <td>36681.0</td>\n", "      <td>10000.0</td>\n", "      <td>9000.0</td>\n", "      <td>689.0</td>\n", "      <td>679.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>19394.0</td>\n", "      <td>19619.0</td>\n", "      <td>20024.0</td>\n", "      <td>2500.0</td>\n", "      <td>1815.0</td>\n", "      <td>657.0</td>\n", "      <td>1000.0</td>\n", "      <td>1000.0</td>\n", "      <td>800.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-18097aa6-98e9-46b6-9f26-449b2b16d442')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-18097aa6-98e9-46b6-9f26-449b2b16d442 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-18097aa6-98e9-46b6-9f26-449b2b16d442');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}}], "source": ["from pycaret.datasets import get_data\n", "dataset = get_data('credit')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kMqDGBkJEQnN", "outputId": "25d6ff92-9671-479a-9448-ca25300ae528"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(24000, 24)"]}, "metadata": {}, "execution_count": 4}], "source": ["#check the shape of data\n", "dataset.shape"]}, {"cell_type": "markdown", "metadata": {"id": "LyGFryEhEQne"}, "source": ["pycaretのpredict関数を後ほど試すために、データセットから5%のサンプルを取っておきます。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hXmaL1xFEQnj", "outputId": "fe095aff-9a3b-4a1d-9b4c-094360b636dc"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Data for Modeling: (22800, 24)\n", "Unseen Data For Predictions: (1200, 24)\n"]}], "source": ["# split data into 95% and 5%\n", "data = dataset.sample(frac=0.95, random_state=786)\n", "data_unseen = dataset.drop(data.index)\n", "data.reset_index(inplace=True, drop=True)\n", "data_unseen.reset_index(inplace=True, drop=True)\n", "print('Data for Modeling: ' + str(data.shape))\n", "print('Unseen Data For Predictions: ' + str(data_unseen.shape))"]}, {"cell_type": "markdown", "metadata": {"id": "y9s9wNcjEQn0"}, "source": ["# Setupの初期化"]}, {"cell_type": "markdown", "metadata": {"id": "ZlA01j6NEQn7"}, "source": ["`setup` 関数は、PyCaretの実験を初期化し、変換処理のパイプラインを関数へ渡されるすべてのパラメーターに基づいて作成します。 setup関数は、他の関数を実行する前に呼び出す必要があります。 `data` と `target` の 2つの必須パラメータを取り、それ以外のパラメーターは任意です。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "BOmRR0deEQoA"}, "outputs": [], "source": ["from pycaret.classification import *"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 645}, "id": "k2IuvfDHEQoO", "outputId": "eaab9955-ea94-40d3-c537-c1592ccf362b", "scrolled": false}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f3627b9afa0>"], "text/html": ["<style type=\"text/css\">\n", "#T_bb28b_row8_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_bb28b_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Description</th>\n", "      <th class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_bb28b_row0_col0\" class=\"data row0 col0\" >Session id</td>\n", "      <td id=\"T_bb28b_row0_col1\" class=\"data row0 col1\" >123</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_bb28b_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_bb28b_row1_col1\" class=\"data row1 col1\" >default</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_bb28b_row2_col0\" class=\"data row2 col0\" >Target type</td>\n", "      <td id=\"T_bb28b_row2_col1\" class=\"data row2 col1\" >Binary</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_bb28b_row3_col0\" class=\"data row3 col0\" >Original data shape</td>\n", "      <td id=\"T_bb28b_row3_col1\" class=\"data row3 col1\" >(22800, 24)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_bb28b_row4_col0\" class=\"data row4 col0\" >Transformed data shape</td>\n", "      <td id=\"T_bb28b_row4_col1\" class=\"data row4 col1\" >(22800, 24)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_bb28b_row5_col0\" class=\"data row5 col0\" >Transformed train set shape</td>\n", "      <td id=\"T_bb28b_row5_col1\" class=\"data row5 col1\" >(15959, 24)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_bb28b_row6_col0\" class=\"data row6 col0\" >Transformed test set shape</td>\n", "      <td id=\"T_bb28b_row6_col1\" class=\"data row6 col1\" >(6841, 24)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_bb28b_row7_col0\" class=\"data row7 col0\" >Numeric features</td>\n", "      <td id=\"T_bb28b_row7_col1\" class=\"data row7 col1\" >23</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_bb28b_row8_col0\" class=\"data row8 col0\" >Preprocess</td>\n", "      <td id=\"T_bb28b_row8_col1\" class=\"data row8 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_bb28b_row9_col0\" class=\"data row9 col0\" >Imputation type</td>\n", "      <td id=\"T_bb28b_row9_col1\" class=\"data row9 col1\" >simple</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_bb28b_row10_col0\" class=\"data row10 col0\" >Numeric imputation</td>\n", "      <td id=\"T_bb28b_row10_col1\" class=\"data row10 col1\" >mean</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_bb28b_row11_col0\" class=\"data row11 col0\" >Categorical imputation</td>\n", "      <td id=\"T_bb28b_row11_col1\" class=\"data row11 col1\" >mode</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_bb28b_row12_col0\" class=\"data row12 col0\" >Fold Generator</td>\n", "      <td id=\"T_bb28b_row12_col1\" class=\"data row12 col1\" >StratifiedKFold</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_bb28b_row13_col0\" class=\"data row13 col0\" >Fold Number</td>\n", "      <td id=\"T_bb28b_row13_col1\" class=\"data row13 col1\" >10</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_bb28b_row14_col0\" class=\"data row14 col0\" >CPU Jobs</td>\n", "      <td id=\"T_bb28b_row14_col1\" class=\"data row14 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_bb28b_row15_col0\" class=\"data row15 col0\" >Use GPU</td>\n", "      <td id=\"T_bb28b_row15_col1\" class=\"data row15 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_bb28b_row16_col0\" class=\"data row16 col0\" >Log Experiment</td>\n", "      <td id=\"T_bb28b_row16_col1\" class=\"data row16 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_bb28b_row17_col0\" class=\"data row17 col0\" >Experiment Name</td>\n", "      <td id=\"T_bb28b_row17_col1\" class=\"data row17 col1\" >clf-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bb28b_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_bb28b_row18_col0\" class=\"data row18 col0\" >USI</td>\n", "      <td id=\"T_bb28b_row18_col1\" class=\"data row18 col1\" >f9a3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}], "source": ["exp_clf101 = setup(data = data, target = 'default', session_id = 123) "]}, {"cell_type": "markdown", "metadata": {"id": "JJSOhIOxEQoY"}, "source": ["セットアップが正常に実行されると、実験の情報が表形式で表示されます。\n", "\n", "- **Session id**: 再現性の確保のためにすべての関数へ渡される疑似乱数のシード値。 `session_id` が渡されない場合は、自動的に生成された乱数がすべての関数に渡されます。<br/>\n", "<br/>\n", "- **Target type**: ２値または多クラス分類。 目的タイプは自動的に検出されます。 <br/>\n", "<br/>\n", "- **Label Encoding**: 目的変数が 1 または 0 ではなく文字列型 (例えば、「はい」または「いいえ」) の場合、ラベルを 1 と 0 に自動的に変換し、割り当てを参考までに表示します (0 : いいえ、 1 : はい) 。 このチュートリアルでは、ターゲット変数が数値型であるため、ラベルのエンコードは必要ありません。 <br/>\n", "<br/>\n", "- **Original data shape**: 変換前の元のデータの形状。 <br/>\n", "<br/>\n", "- **Transformed train set shape**: 変換後の訓練データの形状 <br/>\n", "<br/>\n", "- **Transformed test set shape**: 変換後のテストデータの形状 <br/>\n", "<br/>\n", "- **Numeric features**: 数値として扱われる特徴量の数。 このチュートリアルでは、すべての特徴量が数値変数として扱われます。 <br/>\n", "<br/>\n", "- **Categorical features**: カテゴリ変数として扱われる特徴量の数。 このチュートリアルでは該当の特徴はありません。 <br/>"]}, {"cell_type": "markdown", "metadata": {"id": "it_nJo1IEQob"}, "source": ["# モデルの比較"]}, {"cell_type": "markdown", "metadata": {"id": "apb_B9bBEQof"}, "source": ["`compare_models` 関数は、モデルライブラリで利用可能な全ての推論アルゴリズムで訓練と性能評価をクロスバリデーションを用いて行います。この関数の出力は、クロスバリデーションの平均スコアを表形式で表示します。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488, "referenced_widgets": ["26f832d18f2c459481770897ca456589", "ba5c8e2aa4c644a5afe8c02c0a5db311", "c57621c5903647cab6bb30303f6fb2f8", "a7f41fb0dece4cb8a18879df89885402", "301336eb48d547b2b8ef15846f7d7a80", "c13eecb39b7f4e1687378c0730f1042f", "1d20a4e95295477c835c649b9f9a6fba", "eb5f70b3dc844b9882f13510991772c0", "9bebf8a26c0e4ae0a41627f41228c305", "b2b6cdfb923b49198c6fb0599a361ec8", "89791e54334b4bf6a82b21637f3b64ff"]}, "id": "AsG0b1NIEQoj", "outputId": "da1935bc-1084-4e3e-de70-8b8ef11f44d1", "scrolled": false}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f36382f13d0>"], "text/html": ["<style type=\"text/css\">\n", "#T_15320_ th {\n", "  text-align: left;\n", "}\n", "#T_15320_row0_col0, #T_15320_row0_col3, #T_15320_row0_col4, #T_15320_row0_col5, #T_15320_row1_col0, #T_15320_row1_col1, #T_15320_row1_col2, #T_15320_row1_col3, #T_15320_row1_col4, #T_15320_row1_col5, #T_15320_row1_col6, #T_15320_row1_col7, #T_15320_row2_col0, #T_15320_row2_col1, #T_15320_row2_col2, #T_15320_row2_col3, #T_15320_row2_col4, #T_15320_row2_col5, #T_15320_row2_col6, #T_15320_row2_col7, #T_15320_row3_col0, #T_15320_row3_col1, #T_15320_row3_col2, #T_15320_row3_col3, #T_15320_row3_col4, #T_15320_row3_col6, #T_15320_row3_col7, #T_15320_row4_col0, #T_15320_row4_col1, #T_15320_row4_col2, #T_15320_row4_col3, #T_15320_row4_col4, #T_15320_row4_col5, #T_15320_row4_col6, #T_15320_row4_col7, #T_15320_row5_col0, #T_15320_row5_col1, #T_15320_row5_col2, #T_15320_row5_col3, #T_15320_row5_col4, #T_15320_row5_col5, #T_15320_row5_col6, #T_15320_row5_col7, #T_15320_row6_col0, #T_15320_row6_col1, #T_15320_row6_col2, #T_15320_row6_col3, #T_15320_row6_col5, #T_15320_row6_col6, #T_15320_row6_col7, #T_15320_row7_col0, #T_15320_row7_col1, #T_15320_row7_col2, #T_15320_row7_col3, #T_15320_row7_col4, #T_15320_row7_col5, #T_15320_row7_col6, #T_15320_row7_col7, #T_15320_row8_col0, #T_15320_row8_col1, #T_15320_row8_col2, #T_15320_row8_col3, #T_15320_row8_col4, #T_15320_row8_col5, #T_15320_row8_col6, #T_15320_row8_col7, #T_15320_row9_col0, #T_15320_row9_col1, #T_15320_row9_col2, #T_15320_row9_col3, #T_15320_row9_col4, #T_15320_row9_col5, #T_15320_row9_col6, #T_15320_row9_col7, #T_15320_row10_col0, #T_15320_row10_col1, #T_15320_row10_col2, #T_15320_row10_col3, #T_15320_row10_col4, #T_15320_row10_col5, #T_15320_row10_col6, #T_15320_row10_col7, #T_15320_row11_col0, #T_15320_row11_col1, #T_15320_row11_col2, #T_15320_row11_col3, #T_15320_row11_col4, #T_15320_row11_col5, #T_15320_row11_col6, #T_15320_row11_col7, #T_15320_row12_col0, #T_15320_row12_col1, #T_15320_row12_col2, #T_15320_row12_col3, #T_15320_row12_col4, #T_15320_row12_col5, #T_15320_row12_col6, #T_15320_row12_col7, #T_15320_row13_col0, #T_15320_row13_col1, #T_15320_row13_col2, #T_15320_row13_col4, #T_15320_row13_col5, #T_15320_row13_col6, #T_15320_row13_col7 {\n", "  text-align: left;\n", "}\n", "#T_15320_row0_col1, #T_15320_row0_col2, #T_15320_row0_col6, #T_15320_row0_col7, #T_15320_row3_col5, #T_15320_row6_col4, #T_15320_row13_col3 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_15320_row0_col8, #T_15320_row1_col8, #T_15320_row2_col8, #T_15320_row3_col8, #T_15320_row4_col8, #T_15320_row5_col8, #T_15320_row6_col8, #T_15320_row8_col8, #T_15320_row9_col8, #T_15320_row10_col8, #T_15320_row11_col8, #T_15320_row12_col8, #T_15320_row13_col8 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_15320_row7_col8 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_15320_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Model</th>\n", "      <th class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col2\" >AUC</th>\n", "      <th class=\"col_heading level0 col3\" >Recall</th>\n", "      <th class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th class=\"col_heading level0 col5\" >F1</th>\n", "      <th class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th class=\"col_heading level0 col7\" >MCC</th>\n", "      <th class=\"col_heading level0 col8\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row0\" class=\"row_heading level0 row0\" >gbc</th>\n", "      <td id=\"T_15320_row0_col0\" class=\"data row0 col0\" >Gradient Boosting Classifier</td>\n", "      <td id=\"T_15320_row0_col1\" class=\"data row0 col1\" >0.8225</td>\n", "      <td id=\"T_15320_row0_col2\" class=\"data row0 col2\" >0.7890</td>\n", "      <td id=\"T_15320_row0_col3\" class=\"data row0 col3\" >0.3756</td>\n", "      <td id=\"T_15320_row0_col4\" class=\"data row0 col4\" >0.6791</td>\n", "      <td id=\"T_15320_row0_col5\" class=\"data row0 col5\" >0.4834</td>\n", "      <td id=\"T_15320_row0_col6\" class=\"data row0 col6\" >0.3868</td>\n", "      <td id=\"T_15320_row0_col7\" class=\"data row0 col7\" >0.4117</td>\n", "      <td id=\"T_15320_row0_col8\" class=\"data row0 col8\" >4.9430</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row1\" class=\"row_heading level0 row1\" >lightgbm</th>\n", "      <td id=\"T_15320_row1_col0\" class=\"data row1 col0\" >Light Gradient Boosting Machine</td>\n", "      <td id=\"T_15320_row1_col1\" class=\"data row1 col1\" >0.8202</td>\n", "      <td id=\"T_15320_row1_col2\" class=\"data row1 col2\" >0.7842</td>\n", "      <td id=\"T_15320_row1_col3\" class=\"data row1 col3\" >0.3816</td>\n", "      <td id=\"T_15320_row1_col4\" class=\"data row1 col4\" >0.6630</td>\n", "      <td id=\"T_15320_row1_col5\" class=\"data row1 col5\" >0.4842</td>\n", "      <td id=\"T_15320_row1_col6\" class=\"data row1 col6\" >0.3848</td>\n", "      <td id=\"T_15320_row1_col7\" class=\"data row1 col7\" >0.4064</td>\n", "      <td id=\"T_15320_row1_col8\" class=\"data row1 col8\" >0.4990</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row2\" class=\"row_heading level0 row2\" >ada</th>\n", "      <td id=\"T_15320_row2_col0\" class=\"data row2 col0\" >Ada Boost Classifier</td>\n", "      <td id=\"T_15320_row2_col1\" class=\"data row2 col1\" >0.8200</td>\n", "      <td id=\"T_15320_row2_col2\" class=\"data row2 col2\" >0.7802</td>\n", "      <td id=\"T_15320_row2_col3\" class=\"data row2 col3\" >0.3476</td>\n", "      <td id=\"T_15320_row2_col4\" class=\"data row2 col4\" >0.6847</td>\n", "      <td id=\"T_15320_row2_col5\" class=\"data row2 col5\" >0.4604</td>\n", "      <td id=\"T_15320_row2_col6\" class=\"data row2 col6\" >0.3660</td>\n", "      <td id=\"T_15320_row2_col7\" class=\"data row2 col7\" >0.3966</td>\n", "      <td id=\"T_15320_row2_col8\" class=\"data row2 col8\" >1.0450</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row3\" class=\"row_heading level0 row3\" >rf</th>\n", "      <td id=\"T_15320_row3_col0\" class=\"data row3 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_15320_row3_col1\" class=\"data row3 col1\" >0.8193</td>\n", "      <td id=\"T_15320_row3_col2\" class=\"data row3 col2\" >0.7748</td>\n", "      <td id=\"T_15320_row3_col3\" class=\"data row3 col3\" >0.3870</td>\n", "      <td id=\"T_15320_row3_col4\" class=\"data row3 col4\" >0.6552</td>\n", "      <td id=\"T_15320_row3_col5\" class=\"data row3 col5\" >0.4864</td>\n", "      <td id=\"T_15320_row3_col6\" class=\"data row3 col6\" >0.3855</td>\n", "      <td id=\"T_15320_row3_col7\" class=\"data row3 col7\" >0.4053</td>\n", "      <td id=\"T_15320_row3_col8\" class=\"data row3 col8\" >3.2360</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row4\" class=\"row_heading level0 row4\" >lda</th>\n", "      <td id=\"T_15320_row4_col0\" class=\"data row4 col0\" >Linear Discriminant Analysis</td>\n", "      <td id=\"T_15320_row4_col1\" class=\"data row4 col1\" >0.8151</td>\n", "      <td id=\"T_15320_row4_col2\" class=\"data row4 col2\" >0.7212</td>\n", "      <td id=\"T_15320_row4_col3\" class=\"data row4 col3\" >0.2739</td>\n", "      <td id=\"T_15320_row4_col4\" class=\"data row4 col4\" >0.7152</td>\n", "      <td id=\"T_15320_row4_col5\" class=\"data row4 col5\" >0.3958</td>\n", "      <td id=\"T_15320_row4_col6\" class=\"data row4 col6\" >0.3114</td>\n", "      <td id=\"T_15320_row4_col7\" class=\"data row4 col7\" >0.3618</td>\n", "      <td id=\"T_15320_row4_col8\" class=\"data row4 col8\" >0.0980</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row5\" class=\"row_heading level0 row5\" >et</th>\n", "      <td id=\"T_15320_row5_col0\" class=\"data row5 col0\" >Extra Trees Classifier</td>\n", "      <td id=\"T_15320_row5_col1\" class=\"data row5 col1\" >0.8130</td>\n", "      <td id=\"T_15320_row5_col2\" class=\"data row5 col2\" >0.7677</td>\n", "      <td id=\"T_15320_row5_col3\" class=\"data row5 col3\" >0.3793</td>\n", "      <td id=\"T_15320_row5_col4\" class=\"data row5 col4\" >0.6277</td>\n", "      <td id=\"T_15320_row5_col5\" class=\"data row5 col5\" >0.4728</td>\n", "      <td id=\"T_15320_row5_col6\" class=\"data row5 col6\" >0.3674</td>\n", "      <td id=\"T_15320_row5_col7\" class=\"data row5 col7\" >0.3847</td>\n", "      <td id=\"T_15320_row5_col8\" class=\"data row5 col8\" >1.7260</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row6\" class=\"row_heading level0 row6\" >ridge</th>\n", "      <td id=\"T_15320_row6_col0\" class=\"data row6 col0\" >Ridge Classifier</td>\n", "      <td id=\"T_15320_row6_col1\" class=\"data row6 col1\" >0.8007</td>\n", "      <td id=\"T_15320_row6_col2\" class=\"data row6 col2\" >0.0000</td>\n", "      <td id=\"T_15320_row6_col3\" class=\"data row6 col3\" >0.1558</td>\n", "      <td id=\"T_15320_row6_col4\" class=\"data row6 col4\" >0.7328</td>\n", "      <td id=\"T_15320_row6_col5\" class=\"data row6 col5\" >0.2567</td>\n", "      <td id=\"T_15320_row6_col6\" class=\"data row6 col6\" >0.1943</td>\n", "      <td id=\"T_15320_row6_col7\" class=\"data row6 col7\" >0.2735</td>\n", "      <td id=\"T_15320_row6_col8\" class=\"data row6 col8\" >0.0800</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row7\" class=\"row_heading level0 row7\" >dummy</th>\n", "      <td id=\"T_15320_row7_col0\" class=\"data row7 col0\" >Dummy Classifier</td>\n", "      <td id=\"T_15320_row7_col1\" class=\"data row7 col1\" >0.7788</td>\n", "      <td id=\"T_15320_row7_col2\" class=\"data row7 col2\" >0.5000</td>\n", "      <td id=\"T_15320_row7_col3\" class=\"data row7 col3\" >0.0000</td>\n", "      <td id=\"T_15320_row7_col4\" class=\"data row7 col4\" >0.0000</td>\n", "      <td id=\"T_15320_row7_col5\" class=\"data row7 col5\" >0.0000</td>\n", "      <td id=\"T_15320_row7_col6\" class=\"data row7 col6\" >0.0000</td>\n", "      <td id=\"T_15320_row7_col7\" class=\"data row7 col7\" >0.0000</td>\n", "      <td id=\"T_15320_row7_col8\" class=\"data row7 col8\" >0.0420</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row8\" class=\"row_heading level0 row8\" >lr</th>\n", "      <td id=\"T_15320_row8_col0\" class=\"data row8 col0\" >Logistic Regression</td>\n", "      <td id=\"T_15320_row8_col1\" class=\"data row8 col1\" >0.7786</td>\n", "      <td id=\"T_15320_row8_col2\" class=\"data row8 col2\" >0.6400</td>\n", "      <td id=\"T_15320_row8_col3\" class=\"data row8 col3\" >0.0000</td>\n", "      <td id=\"T_15320_row8_col4\" class=\"data row8 col4\" >0.0000</td>\n", "      <td id=\"T_15320_row8_col5\" class=\"data row8 col5\" >0.0000</td>\n", "      <td id=\"T_15320_row8_col6\" class=\"data row8 col6\" >-0.0004</td>\n", "      <td id=\"T_15320_row8_col7\" class=\"data row8 col7\" >-0.0032</td>\n", "      <td id=\"T_15320_row8_col8\" class=\"data row8 col8\" >1.1820</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row9\" class=\"row_heading level0 row9\" >knn</th>\n", "      <td id=\"T_15320_row9_col0\" class=\"data row9 col0\" >K Neighbors Classifier</td>\n", "      <td id=\"T_15320_row9_col1\" class=\"data row9 col1\" >0.7505</td>\n", "      <td id=\"T_15320_row9_col2\" class=\"data row9 col2\" >0.6100</td>\n", "      <td id=\"T_15320_row9_col3\" class=\"data row9 col3\" >0.1799</td>\n", "      <td id=\"T_15320_row9_col4\" class=\"data row9 col4\" >0.3694</td>\n", "      <td id=\"T_15320_row9_col5\" class=\"data row9 col5\" >0.2418</td>\n", "      <td id=\"T_15320_row9_col6\" class=\"data row9 col6\" >0.1133</td>\n", "      <td id=\"T_15320_row9_col7\" class=\"data row9 col7\" >0.1239</td>\n", "      <td id=\"T_15320_row9_col8\" class=\"data row9 col8\" >2.6160</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row10\" class=\"row_heading level0 row10\" >dt</th>\n", "      <td id=\"T_15320_row10_col0\" class=\"data row10 col0\" >Decision Tree Classifier</td>\n", "      <td id=\"T_15320_row10_col1\" class=\"data row10 col1\" >0.7295</td>\n", "      <td id=\"T_15320_row10_col2\" class=\"data row10 col2\" >0.6207</td>\n", "      <td id=\"T_15320_row10_col3\" class=\"data row10 col3\" >0.4244</td>\n", "      <td id=\"T_15320_row10_col4\" class=\"data row10 col4\" >0.3960</td>\n", "      <td id=\"T_15320_row10_col5\" class=\"data row10 col5\" >0.4096</td>\n", "      <td id=\"T_15320_row10_col6\" class=\"data row10 col6\" >0.2345</td>\n", "      <td id=\"T_15320_row10_col7\" class=\"data row10 col7\" >0.2348</td>\n", "      <td id=\"T_15320_row10_col8\" class=\"data row10 col8\" >0.5840</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row11\" class=\"row_heading level0 row11\" >svm</th>\n", "      <td id=\"T_15320_row11_col0\" class=\"data row11 col0\" >SVM - Linear Kernel</td>\n", "      <td id=\"T_15320_row11_col1\" class=\"data row11 col1\" >0.6188</td>\n", "      <td id=\"T_15320_row11_col2\" class=\"data row11 col2\" >0.0000</td>\n", "      <td id=\"T_15320_row11_col3\" class=\"data row11 col3\" >0.3895</td>\n", "      <td id=\"T_15320_row11_col4\" class=\"data row11 col4\" >0.2528</td>\n", "      <td id=\"T_15320_row11_col5\" class=\"data row11 col5\" >0.2759</td>\n", "      <td id=\"T_15320_row11_col6\" class=\"data row11 col6\" >0.0585</td>\n", "      <td id=\"T_15320_row11_col7\" class=\"data row11 col7\" >0.0654</td>\n", "      <td id=\"T_15320_row11_col8\" class=\"data row11 col8\" >0.3320</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row12\" class=\"row_heading level0 row12\" >qda</th>\n", "      <td id=\"T_15320_row12_col0\" class=\"data row12 col0\" >Quadratic Discriminant Analysis</td>\n", "      <td id=\"T_15320_row12_col1\" class=\"data row12 col1\" >0.5668</td>\n", "      <td id=\"T_15320_row12_col2\" class=\"data row12 col2\" >0.7254</td>\n", "      <td id=\"T_15320_row12_col3\" class=\"data row12 col3\" >0.7861</td>\n", "      <td id=\"T_15320_row12_col4\" class=\"data row12 col4\" >0.3143</td>\n", "      <td id=\"T_15320_row12_col5\" class=\"data row12 col5\" >0.4468</td>\n", "      <td id=\"T_15320_row12_col6\" class=\"data row12 col6\" >0.1910</td>\n", "      <td id=\"T_15320_row12_col7\" class=\"data row12 col7\" >0.2460</td>\n", "      <td id=\"T_15320_row12_col8\" class=\"data row12 col8\" >0.0570</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_15320_level0_row13\" class=\"row_heading level0 row13\" >nb</th>\n", "      <td id=\"T_15320_row13_col0\" class=\"data row13 col0\" >Naive <PERSON></td>\n", "      <td id=\"T_15320_row13_col1\" class=\"data row13 col1\" >0.3889</td>\n", "      <td id=\"T_15320_row13_col2\" class=\"data row13 col2\" >0.6816</td>\n", "      <td id=\"T_15320_row13_col3\" class=\"data row13 col3\" >0.8765</td>\n", "      <td id=\"T_15320_row13_col4\" class=\"data row13 col4\" >0.2493</td>\n", "      <td id=\"T_15320_row13_col5\" class=\"data row13 col5\" >0.3881</td>\n", "      <td id=\"T_15320_row13_col6\" class=\"data row13 col6\" >0.0668</td>\n", "      <td id=\"T_15320_row13_col7\" class=\"data row13 col7\" >0.1273</td>\n", "      <td id=\"T_15320_row13_col8\" class=\"data row13 col8\" >0.0860</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/61 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "26f832d18f2c459481770897ca456589"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["best_model = compare_models()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WvC1D53qb6xE", "outputId": "b0fffbca-cc30-4fae-c923-24f93a494624"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["GradientBoostingClassifier(ccp_alpha=0.0, criterion='friedman_mse', init=None,\n", "                           learning_rate=0.1, loss='deviance', max_depth=3,\n", "                           max_features=None, max_leaf_nodes=None,\n", "                           min_impurity_decrease=0.0, min_samples_leaf=1,\n", "                           min_samples_split=2, min_weight_fraction_leaf=0.0,\n", "                           n_estimators=100, n_iter_no_change=None,\n", "                           random_state=123, subsample=1.0, tol=0.0001,\n", "                           validation_fraction=0.1, verbose=0,\n", "                           warm_start=False)\n"]}], "source": ["print(best_model)"]}, {"cell_type": "markdown", "metadata": {"id": "P5m2pciOEQo4"}, "source": ["# モデル作成"]}, {"cell_type": "markdown", "metadata": {"id": "u_6cIilfEQo7"}, "source": ["`create_model` 関数は，指定した推論アルゴリズムの性能評価をクロスバリデーションを用いて行います．この関数の出力は，CVスコアを含むフォールド毎の結果を表形式で表示します．利用可能なモデルは，関数 `models` を用いて確認できます."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 614}, "id": "lck3FYHib6xE", "outputId": "ce7831ef-c8b4-4305-ce26-1ed98f277762"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                     Name  \\\n", "ID                                          \n", "lr                    Logistic Regression   \n", "knn                K Neighbors Classifier   \n", "nb                            <PERSON><PERSON>   \n", "dt               Decision Tree Classifier   \n", "svm                   SVM - Linear Kernel   \n", "rbfsvm                SVM - Radial Kernel   \n", "gpc           Gaussian Process Classifier   \n", "mlp                        MLP Classifier   \n", "ridge                    Ridge Classifier   \n", "rf               Random Forest Classifier   \n", "qda       Quadratic Discriminant Analysis   \n", "ada                  Ada Boost Classifier   \n", "gbc          Gradient Boosting Classifier   \n", "lda          Linear Discriminant Analysis   \n", "et                 Extra Trees Classifier   \n", "lightgbm  Light Gradient Boosting Machine   \n", "dummy                    Dummy Classifier   \n", "\n", "                                                  Reference  Turbo  \n", "ID                                                                  \n", "lr        sklearn.linear_model._logistic.LogisticRegression   True  \n", "knn       sklearn.neighbors._classification.KNeighborsCl...   True  \n", "nb                           sklearn.naive_bayes.GaussianNB   True  \n", "dt             sklearn.tree._classes.DecisionTreeClassifier   True  \n", "svm       sklearn.linear_model._stochastic_gradient.SGDC...   True  \n", "rbfsvm                             sklearn.svm._classes.SVC  False  \n", "gpc       sklearn.gaussian_process._gpc.GaussianProcessC...  False  \n", "mlp       sklearn.neural_network._multilayer_perceptron....  False  \n", "ridge           sklearn.linear_model._ridge.RidgeClassifier   True  \n", "rf          sklearn.ensemble._forest.RandomForestClassifier   True  \n", "qda       sklearn.discriminant_analysis.QuadraticDiscrim...   True  \n", "ada       sklearn.ensemble._weight_boosting.AdaBoostClas...   True  \n", "gbc         sklearn.ensemble._gb.GradientBoostingClassifier   True  \n", "lda       sklearn.discriminant_analysis.LinearDiscrimina...   True  \n", "et            sklearn.ensemble._forest.ExtraTreesClassifier   True  \n", "lightgbm                    lightgbm.sklearn.LGBMClassifier   True  \n", "dummy                         sklearn.dummy.DummyClassifier   True  "], "text/html": ["\n", "  <div id=\"df-273ee27c-0c19-4312-942b-ccd9d90be79b\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Reference</th>\n", "      <th>Turbo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lr</th>\n", "      <td>Logistic Regression</td>\n", "      <td>sklearn.linear_model._logistic.LogisticRegression</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>knn</th>\n", "      <td>K Neighbors Classifier</td>\n", "      <td>sklearn.neighbors._classification.KNeighborsCl...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>nb</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>sklearn.naive_bayes.GaussianNB</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <td>Decision Tree Classifier</td>\n", "      <td>sklearn.tree._classes.DecisionTreeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>svm</th>\n", "      <td>SVM - Linear Kernel</td>\n", "      <td>sklearn.linear_model._stochastic_gradient.SGDC...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rbfsvm</th>\n", "      <td>SVM - Radial <PERSON></td>\n", "      <td>sklearn.svm._classes.SVC</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gpc</th>\n", "      <td>Gaussian Process Classifier</td>\n", "      <td>sklearn.gaussian_process._gpc.GaussianProcessC...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mlp</th>\n", "      <td>MLP Classifier</td>\n", "      <td>sklearn.neural_network._multilayer_perceptron....</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ridge</th>\n", "      <td>Ridge Classifier</td>\n", "      <td>sklearn.linear_model._ridge.RidgeClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rf</th>\n", "      <td>Random Forest Classifier</td>\n", "      <td>sklearn.ensemble._forest.RandomForestClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>qda</th>\n", "      <td>Quadratic Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.QuadraticDiscrim...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ada</th>\n", "      <td>Ada Boost Classifier</td>\n", "      <td>sklearn.ensemble._weight_boosting.AdaBoostClas...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gbc</th>\n", "      <td>Gradient Boosting Classifier</td>\n", "      <td>sklearn.ensemble._gb.GradientBoostingClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lda</th>\n", "      <td>Linear Discriminant Analysis</td>\n", "      <td>sklearn.discriminant_analysis.LinearDiscrimina...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>et</th>\n", "      <td>Extra Trees Classifier</td>\n", "      <td>sklearn.ensemble._forest.ExtraTreesClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lightgbm</th>\n", "      <td>Light Gradient Boosting Machine</td>\n", "      <td>lightgbm.sklearn.LGBMClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dummy</th>\n", "      <td>Dummy Classifier</td>\n", "      <td>sklearn.dummy.DummyClassifier</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-273ee27c-0c19-4312-942b-ccd9d90be79b')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-273ee27c-0c19-4312-942b-ccd9d90be79b button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-273ee27c-0c19-4312-942b-ccd9d90be79b');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 10}], "source": ["models()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 457, "referenced_widgets": ["a43063cad0854f7f868c501b8ac11c74", "2759fb13f6a94cc2990c64b27756da3d", "21746269ecc44d9b9ff8eb434a1e2472", "f3574d8d8b9a489eb5802336649aed9a", "0161f2cd69384040b3397bbb9184d57d", "67c7128f486a451781f64d6f1e43566d", "7270ddefb9d142c88ee072a1fc259269", "c25e36340b214bc2815378284ac8b025", "63dda3a99946489c9458886b8c65d6d9", "8ab857b1b63b4f11a31b98b89b88b5d2", "d3e419f035c9453b84102a8d6d130fc6"]}, "id": "LP896uSIEQpD", "outputId": "c1dff062-9c50-43ab-a13f-6bb158079fc3"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f36279e5e50>"], "text/html": ["<style type=\"text/css\">\n", "#T_77c63_row10_col0, #T_77c63_row10_col1, #T_77c63_row10_col2, #T_77c63_row10_col3, #T_77c63_row10_col4, #T_77c63_row10_col5, #T_77c63_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_77c63_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col1\" >AUC</th>\n", "      <th class=\"col_heading level0 col2\" >Recall</th>\n", "      <th class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th class=\"col_heading level0 col4\" >F1</th>\n", "      <th class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_77c63_row0_col0\" class=\"data row0 col0\" >0.7356</td>\n", "      <td id=\"T_77c63_row0_col1\" class=\"data row0 col1\" >0.6274</td>\n", "      <td id=\"T_77c63_row0_col2\" class=\"data row0 col2\" >0.4334</td>\n", "      <td id=\"T_77c63_row0_col3\" class=\"data row0 col3\" >0.4080</td>\n", "      <td id=\"T_77c63_row0_col4\" class=\"data row0 col4\" >0.4203</td>\n", "      <td id=\"T_77c63_row0_col5\" class=\"data row0 col5\" >0.2493</td>\n", "      <td id=\"T_77c63_row0_col6\" class=\"data row0 col6\" >0.2495</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_77c63_row1_col0\" class=\"data row1 col0\" >0.7237</td>\n", "      <td id=\"T_77c63_row1_col1\" class=\"data row1 col1\" >0.6158</td>\n", "      <td id=\"T_77c63_row1_col2\" class=\"data row1 col2\" >0.4193</td>\n", "      <td id=\"T_77c63_row1_col3\" class=\"data row1 col3\" >0.3854</td>\n", "      <td id=\"T_77c63_row1_col4\" class=\"data row1 col4\" >0.4016</td>\n", "      <td id=\"T_77c63_row1_col5\" class=\"data row1 col5\" >0.2224</td>\n", "      <td id=\"T_77c63_row1_col6\" class=\"data row1 col6\" >0.2227</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_77c63_row2_col0\" class=\"data row2 col0\" >0.7444</td>\n", "      <td id=\"T_77c63_row2_col1\" class=\"data row2 col1\" >0.6514</td>\n", "      <td id=\"T_77c63_row2_col2\" class=\"data row2 col2\" >0.4816</td>\n", "      <td id=\"T_77c63_row2_col3\" class=\"data row2 col3\" >0.4304</td>\n", "      <td id=\"T_77c63_row2_col4\" class=\"data row2 col4\" >0.4545</td>\n", "      <td id=\"T_77c63_row2_col5\" class=\"data row2 col5\" >0.2883</td>\n", "      <td id=\"T_77c63_row2_col6\" class=\"data row2 col6\" >0.2891</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_77c63_row3_col0\" class=\"data row3 col0\" >0.7199</td>\n", "      <td id=\"T_77c63_row3_col1\" class=\"data row3 col1\" >0.6094</td>\n", "      <td id=\"T_77c63_row3_col2\" class=\"data row3 col2\" >0.4079</td>\n", "      <td id=\"T_77c63_row3_col3\" class=\"data row3 col3\" >0.3770</td>\n", "      <td id=\"T_77c63_row3_col4\" class=\"data row3 col4\" >0.3918</td>\n", "      <td id=\"T_77c63_row3_col5\" class=\"data row3 col5\" >0.2103</td>\n", "      <td id=\"T_77c63_row3_col6\" class=\"data row3 col6\" >0.2106</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_77c63_row4_col0\" class=\"data row4 col0\" >0.7419</td>\n", "      <td id=\"T_77c63_row4_col1\" class=\"data row4 col1\" >0.6233</td>\n", "      <td id=\"T_77c63_row4_col2\" class=\"data row4 col2\" >0.4108</td>\n", "      <td id=\"T_77c63_row4_col3\" class=\"data row4 col3\" >0.4155</td>\n", "      <td id=\"T_77c63_row4_col4\" class=\"data row4 col4\" >0.4131</td>\n", "      <td id=\"T_77c63_row4_col5\" class=\"data row4 col5\" >0.2477</td>\n", "      <td id=\"T_77c63_row4_col6\" class=\"data row4 col6\" >0.2477</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_77c63_row5_col0\" class=\"data row5 col0\" >0.7299</td>\n", "      <td id=\"T_77c63_row5_col1\" class=\"data row5 col1\" >0.6248</td>\n", "      <td id=\"T_77c63_row5_col2\" class=\"data row5 col2\" >0.4363</td>\n", "      <td id=\"T_77c63_row5_col3\" class=\"data row5 col3\" >0.3990</td>\n", "      <td id=\"T_77c63_row5_col4\" class=\"data row5 col4\" >0.4168</td>\n", "      <td id=\"T_77c63_row5_col5\" class=\"data row5 col5\" >0.2415</td>\n", "      <td id=\"T_77c63_row5_col6\" class=\"data row5 col6\" >0.2419</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_77c63_row6_col0\" class=\"data row6 col0\" >0.7174</td>\n", "      <td id=\"T_77c63_row6_col1\" class=\"data row6 col1\" >0.6015</td>\n", "      <td id=\"T_77c63_row6_col2\" class=\"data row6 col2\" >0.3938</td>\n", "      <td id=\"T_77c63_row6_col3\" class=\"data row6 col3\" >0.3697</td>\n", "      <td id=\"T_77c63_row6_col4\" class=\"data row6 col4\" >0.3813</td>\n", "      <td id=\"T_77c63_row6_col5\" class=\"data row6 col5\" >0.1985</td>\n", "      <td id=\"T_77c63_row6_col6\" class=\"data row6 col6\" >0.1986</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_77c63_row7_col0\" class=\"data row7 col0\" >0.7325</td>\n", "      <td id=\"T_77c63_row7_col1\" class=\"data row7 col1\" >0.6213</td>\n", "      <td id=\"T_77c63_row7_col2\" class=\"data row7 col2\" >0.4221</td>\n", "      <td id=\"T_77c63_row7_col3\" class=\"data row7 col3\" >0.4005</td>\n", "      <td id=\"T_77c63_row7_col4\" class=\"data row7 col4\" >0.4110</td>\n", "      <td id=\"T_77c63_row7_col5\" class=\"data row7 col5\" >0.2381</td>\n", "      <td id=\"T_77c63_row7_col6\" class=\"data row7 col6\" >0.2382</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_77c63_row8_col0\" class=\"data row8 col0\" >0.7268</td>\n", "      <td id=\"T_77c63_row8_col1\" class=\"data row8 col1\" >0.6147</td>\n", "      <td id=\"T_77c63_row8_col2\" class=\"data row8 col2\" >0.4136</td>\n", "      <td id=\"T_77c63_row8_col3\" class=\"data row8 col3\" >0.3893</td>\n", "      <td id=\"T_77c63_row8_col4\" class=\"data row8 col4\" >0.4011</td>\n", "      <td id=\"T_77c63_row8_col5\" class=\"data row8 col5\" >0.2244</td>\n", "      <td id=\"T_77c63_row8_col6\" class=\"data row8 col6\" >0.2245</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_77c63_row9_col0\" class=\"data row9 col0\" >0.7229</td>\n", "      <td id=\"T_77c63_row9_col1\" class=\"data row9 col1\" >0.6174</td>\n", "      <td id=\"T_77c63_row9_col2\" class=\"data row9 col2\" >0.4249</td>\n", "      <td id=\"T_77c63_row9_col3\" class=\"data row9 col3\" >0.3856</td>\n", "      <td id=\"T_77c63_row9_col4\" class=\"data row9 col4\" >0.4043</td>\n", "      <td id=\"T_77c63_row9_col5\" class=\"data row9 col5\" >0.2243</td>\n", "      <td id=\"T_77c63_row9_col6\" class=\"data row9 col6\" >0.2248</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_77c63_row10_col0\" class=\"data row10 col0\" >0.7295</td>\n", "      <td id=\"T_77c63_row10_col1\" class=\"data row10 col1\" >0.6207</td>\n", "      <td id=\"T_77c63_row10_col2\" class=\"data row10 col2\" >0.4244</td>\n", "      <td id=\"T_77c63_row10_col3\" class=\"data row10 col3\" >0.3960</td>\n", "      <td id=\"T_77c63_row10_col4\" class=\"data row10 col4\" >0.4096</td>\n", "      <td id=\"T_77c63_row10_col5\" class=\"data row10 col5\" >0.2345</td>\n", "      <td id=\"T_77c63_row10_col6\" class=\"data row10 col6\" >0.2348</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_77c63_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_77c63_row11_col0\" class=\"data row11 col0\" >0.0086</td>\n", "      <td id=\"T_77c63_row11_col1\" class=\"data row11 col1\" >0.0126</td>\n", "      <td id=\"T_77c63_row11_col2\" class=\"data row11 col2\" >0.0224</td>\n", "      <td id=\"T_77c63_row11_col3\" class=\"data row11 col3\" >0.0175</td>\n", "      <td id=\"T_77c63_row11_col4\" class=\"data row11 col4\" >0.0187</td>\n", "      <td id=\"T_77c63_row11_col5\" class=\"data row11 col5\" >0.0236</td>\n", "      <td id=\"T_77c63_row11_col6\" class=\"data row11 col6\" >0.0237</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/4 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a43063cad0854f7f868c501b8ac11c74"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["# train a decision tree model\n", "dt = create_model('dt')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FRat05yGEQpQ", "outputId": "a7309db6-7035-48e2-9549-097c10091bf9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='gini',\n", "                       max_depth=None, max_features=None, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.0, min_samples_leaf=1,\n", "                       min_samples_split=2, min_weight_fraction_leaf=0.0,\n", "                       random_state=123, splitter='best')\n"]}], "source": ["print(dt)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 457, "referenced_widgets": ["8fe80ee681da418a8aae8e4d63590440", "43f1961604bc4c3fa8bb4bf0b42b6b1b", "f5fd48c57a5b48b88522124944d7947c", "45a015029bca4c48b4527a45ef26d1a0", "e7b576e3f96040e6bdb254891f480a27", "ebfa10a4cbd94e27a6eafbf549de63ca", "2fa4f1a9341647e4b8b79d2fefc41129", "0715c20dd73d4ff5a02f3c01a05614c7", "d32be97eebdd42cabc36229c9f13a4b7", "90a63886bca44b35bf85e23d3f5d137e", "ee9c6b0d77b64348b7192a768707a21a"]}, "id": "Izaqz_MBb6xF", "outputId": "736ffe8d-05e9-421e-c9c6-5919cd9814eb"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f3639944730>"], "text/html": ["<style type=\"text/css\">\n", "#T_3e771_row10_col0, #T_3e771_row10_col1, #T_3e771_row10_col2, #T_3e771_row10_col3, #T_3e771_row10_col4, #T_3e771_row10_col5, #T_3e771_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_3e771_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col1\" >AUC</th>\n", "      <th class=\"col_heading level0 col2\" >Recall</th>\n", "      <th class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th class=\"col_heading level0 col4\" >F1</th>\n", "      <th class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_3e771_row0_col0\" class=\"data row0 col0\" >0.7412</td>\n", "      <td id=\"T_3e771_row0_col1\" class=\"data row0 col1\" >0.5894</td>\n", "      <td id=\"T_3e771_row0_col2\" class=\"data row0 col2\" >0.1671</td>\n", "      <td id=\"T_3e771_row0_col3\" class=\"data row0 col3\" >0.3315</td>\n", "      <td id=\"T_3e771_row0_col4\" class=\"data row0 col4\" >0.2222</td>\n", "      <td id=\"T_3e771_row0_col5\" class=\"data row0 col5\" >0.0868</td>\n", "      <td id=\"T_3e771_row0_col6\" class=\"data row0 col6\" >0.0941</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_3e771_row1_col0\" class=\"data row1 col0\" >0.7350</td>\n", "      <td id=\"T_3e771_row1_col1\" class=\"data row1 col1\" >0.5767</td>\n", "      <td id=\"T_3e771_row1_col2\" class=\"data row1 col2\" >0.1473</td>\n", "      <td id=\"T_3e771_row1_col3\" class=\"data row1 col3\" >0.2989</td>\n", "      <td id=\"T_3e771_row1_col4\" class=\"data row1 col4\" >0.1973</td>\n", "      <td id=\"T_3e771_row1_col5\" class=\"data row1 col5\" >0.0601</td>\n", "      <td id=\"T_3e771_row1_col6\" class=\"data row1 col6\" >0.0655</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_3e771_row2_col0\" class=\"data row2 col0\" >0.7632</td>\n", "      <td id=\"T_3e771_row2_col1\" class=\"data row2 col1\" >0.6671</td>\n", "      <td id=\"T_3e771_row2_col2\" class=\"data row2 col2\" >0.2096</td>\n", "      <td id=\"T_3e771_row2_col3\" class=\"data row2 col3\" >0.4277</td>\n", "      <td id=\"T_3e771_row2_col4\" class=\"data row2 col4\" >0.2814</td>\n", "      <td id=\"T_3e771_row2_col5\" class=\"data row2 col5\" >0.1590</td>\n", "      <td id=\"T_3e771_row2_col6\" class=\"data row2 col6\" >0.1735</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_3e771_row3_col0\" class=\"data row3 col0\" >0.7481</td>\n", "      <td id=\"T_3e771_row3_col1\" class=\"data row3 col1\" >0.5990</td>\n", "      <td id=\"T_3e771_row3_col2\" class=\"data row3 col2\" >0.1530</td>\n", "      <td id=\"T_3e771_row3_col3\" class=\"data row3 col3\" >0.3439</td>\n", "      <td id=\"T_3e771_row3_col4\" class=\"data row3 col4\" >0.2118</td>\n", "      <td id=\"T_3e771_row3_col5\" class=\"data row3 col5\" >0.0875</td>\n", "      <td id=\"T_3e771_row3_col6\" class=\"data row3 col6\" >0.0977</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_3e771_row4_col0\" class=\"data row4 col0\" >0.7531</td>\n", "      <td id=\"T_3e771_row4_col1\" class=\"data row4 col1\" >0.6086</td>\n", "      <td id=\"T_3e771_row4_col2\" class=\"data row4 col2\" >0.1983</td>\n", "      <td id=\"T_3e771_row4_col3\" class=\"data row4 col3\" >0.3867</td>\n", "      <td id=\"T_3e771_row4_col4\" class=\"data row4 col4\" >0.2622</td>\n", "      <td id=\"T_3e771_row4_col5\" class=\"data row4 col5\" >0.1320</td>\n", "      <td id=\"T_3e771_row4_col6\" class=\"data row4 col6\" >0.1427</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_3e771_row5_col0\" class=\"data row5 col0\" >0.7619</td>\n", "      <td id=\"T_3e771_row5_col1\" class=\"data row5 col1\" >0.6214</td>\n", "      <td id=\"T_3e771_row5_col2\" class=\"data row5 col2\" >0.1898</td>\n", "      <td id=\"T_3e771_row5_col3\" class=\"data row5 col3\" >0.4161</td>\n", "      <td id=\"T_3e771_row5_col4\" class=\"data row5 col4\" >0.2607</td>\n", "      <td id=\"T_3e771_row5_col5\" class=\"data row5 col5\" >0.1418</td>\n", "      <td id=\"T_3e771_row5_col6\" class=\"data row5 col6\" >0.1574</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_3e771_row6_col0\" class=\"data row6 col0\" >0.7419</td>\n", "      <td id=\"T_3e771_row6_col1\" class=\"data row6 col1\" >0.5895</td>\n", "      <td id=\"T_3e771_row6_col2\" class=\"data row6 col2\" >0.1671</td>\n", "      <td id=\"T_3e771_row6_col3\" class=\"data row6 col3\" >0.3333</td>\n", "      <td id=\"T_3e771_row6_col4\" class=\"data row6 col4\" >0.2226</td>\n", "      <td id=\"T_3e771_row6_col5\" class=\"data row6 col5\" >0.0879</td>\n", "      <td id=\"T_3e771_row6_col6\" class=\"data row6 col6\" >0.0954</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_3e771_row7_col0\" class=\"data row7 col0\" >0.7594</td>\n", "      <td id=\"T_3e771_row7_col1\" class=\"data row7 col1\" >0.6133</td>\n", "      <td id=\"T_3e771_row7_col2\" class=\"data row7 col2\" >0.1898</td>\n", "      <td id=\"T_3e771_row7_col3\" class=\"data row7 col3\" >0.4061</td>\n", "      <td id=\"T_3e771_row7_col4\" class=\"data row7 col4\" >0.2587</td>\n", "      <td id=\"T_3e771_row7_col5\" class=\"data row7 col5\" >0.1371</td>\n", "      <td id=\"T_3e771_row7_col6\" class=\"data row7 col6\" >0.1513</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_3e771_row8_col0\" class=\"data row8 col0\" >0.7475</td>\n", "      <td id=\"T_3e771_row8_col1\" class=\"data row8 col1\" >0.6091</td>\n", "      <td id=\"T_3e771_row8_col2\" class=\"data row8 col2\" >0.1898</td>\n", "      <td id=\"T_3e771_row8_col3\" class=\"data row8 col3\" >0.3641</td>\n", "      <td id=\"T_3e771_row8_col4\" class=\"data row8 col4\" >0.2495</td>\n", "      <td id=\"T_3e771_row8_col5\" class=\"data row8 col5\" >0.1155</td>\n", "      <td id=\"T_3e771_row8_col6\" class=\"data row8 col6\" >0.1243</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_3e771_row9_col0\" class=\"data row9 col0\" >0.7542</td>\n", "      <td id=\"T_3e771_row9_col1\" class=\"data row9 col1\" >0.6260</td>\n", "      <td id=\"T_3e771_row9_col2\" class=\"data row9 col2\" >0.1870</td>\n", "      <td id=\"T_3e771_row9_col3\" class=\"data row9 col3\" >0.3860</td>\n", "      <td id=\"T_3e771_row9_col4\" class=\"data row9 col4\" >0.2519</td>\n", "      <td id=\"T_3e771_row9_col5\" class=\"data row9 col5\" >0.1256</td>\n", "      <td id=\"T_3e771_row9_col6\" class=\"data row9 col6\" >0.1374</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_3e771_row10_col0\" class=\"data row10 col0\" >0.7505</td>\n", "      <td id=\"T_3e771_row10_col1\" class=\"data row10 col1\" >0.6100</td>\n", "      <td id=\"T_3e771_row10_col2\" class=\"data row10 col2\" >0.1799</td>\n", "      <td id=\"T_3e771_row10_col3\" class=\"data row10 col3\" >0.3694</td>\n", "      <td id=\"T_3e771_row10_col4\" class=\"data row10 col4\" >0.2418</td>\n", "      <td id=\"T_3e771_row10_col5\" class=\"data row10 col5\" >0.1133</td>\n", "      <td id=\"T_3e771_row10_col6\" class=\"data row10 col6\" >0.1239</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_3e771_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_3e771_row11_col0\" class=\"data row11 col0\" >0.0090</td>\n", "      <td id=\"T_3e771_row11_col1\" class=\"data row11 col1\" >0.0239</td>\n", "      <td id=\"T_3e771_row11_col2\" class=\"data row11 col2\" >0.0192</td>\n", "      <td id=\"T_3e771_row11_col3\" class=\"data row11 col3\" >0.0399</td>\n", "      <td id=\"T_3e771_row11_col4\" class=\"data row11 col4\" >0.0253</td>\n", "      <td id=\"T_3e771_row11_col5\" class=\"data row11 col5\" >0.0297</td>\n", "      <td id=\"T_3e771_row11_col6\" class=\"data row11 col6\" >0.0327</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/4 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8fe80ee681da418a8aae8e4d63590440"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["# train a k nearest neighbor model\n", "knn = create_model('knn')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 457, "referenced_widgets": ["9cf908bb9238434e8da330ccc13db02e", "e8f469ab2da94d5ebf824a86d865f795", "f48dd44cfac74ef69f4be3039a4cacd3", "9736a846649f4581b895de1baf8beece", "a83fa8e4c0f549ec8e4abd8a57946905", "4f4578dffa88454683738e7003a7c1a4", "0bd7d74bcc104abe8311e739735ea625", "0dbff54a34f64872a6f825e3d8e9b6ec", "2bcf10850e8142b2979c7e466a2e1c7d", "8720dd231a67484c813350234850c39a", "80986d24fe4048ae8348298d865500eb"]}, "id": "FGCoUiQpEQpz", "outputId": "c491b47b-9249-422a-afe3-0163ca635b8b"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f3627cedb20>"], "text/html": ["<style type=\"text/css\">\n", "#T_dd6ed_row10_col0, #T_dd6ed_row10_col1, #T_dd6ed_row10_col2, #T_dd6ed_row10_col3, #T_dd6ed_row10_col4, #T_dd6ed_row10_col5, #T_dd6ed_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_dd6ed_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col1\" >AUC</th>\n", "      <th class=\"col_heading level0 col2\" >Recall</th>\n", "      <th class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th class=\"col_heading level0 col4\" >F1</th>\n", "      <th class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_dd6ed_row0_col0\" class=\"data row0 col0\" >0.8227</td>\n", "      <td id=\"T_dd6ed_row0_col1\" class=\"data row0 col1\" >0.7717</td>\n", "      <td id=\"T_dd6ed_row0_col2\" class=\"data row0 col2\" >0.4108</td>\n", "      <td id=\"T_dd6ed_row0_col3\" class=\"data row0 col3\" >0.6591</td>\n", "      <td id=\"T_dd6ed_row0_col4\" class=\"data row0 col4\" >0.5061</td>\n", "      <td id=\"T_dd6ed_row0_col5\" class=\"data row0 col5\" >0.4051</td>\n", "      <td id=\"T_dd6ed_row0_col6\" class=\"data row0 col6\" >0.4219</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_dd6ed_row1_col0\" class=\"data row1 col0\" >0.8271</td>\n", "      <td id=\"T_dd6ed_row1_col1\" class=\"data row1 col1\" >0.7726</td>\n", "      <td id=\"T_dd6ed_row1_col2\" class=\"data row1 col2\" >0.4023</td>\n", "      <td id=\"T_dd6ed_row1_col3\" class=\"data row1 col3\" >0.6860</td>\n", "      <td id=\"T_dd6ed_row1_col4\" class=\"data row1 col4\" >0.5071</td>\n", "      <td id=\"T_dd6ed_row1_col5\" class=\"data row1 col5\" >0.4108</td>\n", "      <td id=\"T_dd6ed_row1_col6\" class=\"data row1 col6\" >0.4323</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_dd6ed_row2_col0\" class=\"data row2 col0\" >0.8233</td>\n", "      <td id=\"T_dd6ed_row2_col1\" class=\"data row2 col1\" >0.8079</td>\n", "      <td id=\"T_dd6ed_row2_col2\" class=\"data row2 col2\" >0.4108</td>\n", "      <td id=\"T_dd6ed_row2_col3\" class=\"data row2 col3\" >0.6621</td>\n", "      <td id=\"T_dd6ed_row2_col4\" class=\"data row2 col4\" >0.5070</td>\n", "      <td id=\"T_dd6ed_row2_col5\" class=\"data row2 col5\" >0.4065</td>\n", "      <td id=\"T_dd6ed_row2_col6\" class=\"data row2 col6\" >0.4237</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_dd6ed_row3_col0\" class=\"data row3 col0\" >0.8120</td>\n", "      <td id=\"T_dd6ed_row3_col1\" class=\"data row3 col1\" >0.7501</td>\n", "      <td id=\"T_dd6ed_row3_col2\" class=\"data row3 col2\" >0.3711</td>\n", "      <td id=\"T_dd6ed_row3_col3\" class=\"data row3 col3\" >0.6268</td>\n", "      <td id=\"T_dd6ed_row3_col4\" class=\"data row3 col4\" >0.4662</td>\n", "      <td id=\"T_dd6ed_row3_col5\" class=\"data row3 col5\" >0.3611</td>\n", "      <td id=\"T_dd6ed_row3_col6\" class=\"data row3 col6\" >0.3794</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_dd6ed_row4_col0\" class=\"data row4 col0\" >0.8139</td>\n", "      <td id=\"T_dd6ed_row4_col1\" class=\"data row4 col1\" >0.7623</td>\n", "      <td id=\"T_dd6ed_row4_col2\" class=\"data row4 col2\" >0.3541</td>\n", "      <td id=\"T_dd6ed_row4_col3\" class=\"data row4 col3\" >0.6443</td>\n", "      <td id=\"T_dd6ed_row4_col4\" class=\"data row4 col4\" >0.4570</td>\n", "      <td id=\"T_dd6ed_row4_col5\" class=\"data row4 col5\" >0.3560</td>\n", "      <td id=\"T_dd6ed_row4_col6\" class=\"data row4 col6\" >0.3793</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_dd6ed_row5_col0\" class=\"data row5 col0\" >0.8221</td>\n", "      <td id=\"T_dd6ed_row5_col1\" class=\"data row5 col1\" >0.7827</td>\n", "      <td id=\"T_dd6ed_row5_col2\" class=\"data row5 col2\" >0.3909</td>\n", "      <td id=\"T_dd6ed_row5_col3\" class=\"data row5 col3\" >0.6667</td>\n", "      <td id=\"T_dd6ed_row5_col4\" class=\"data row5 col4\" >0.4929</td>\n", "      <td id=\"T_dd6ed_row5_col5\" class=\"data row5 col5\" >0.3937</td>\n", "      <td id=\"T_dd6ed_row5_col6\" class=\"data row5 col6\" >0.4144</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_dd6ed_row6_col0\" class=\"data row6 col0\" >0.8120</td>\n", "      <td id=\"T_dd6ed_row6_col1\" class=\"data row6 col1\" >0.7607</td>\n", "      <td id=\"T_dd6ed_row6_col2\" class=\"data row6 col2\" >0.3711</td>\n", "      <td id=\"T_dd6ed_row6_col3\" class=\"data row6 col3\" >0.6268</td>\n", "      <td id=\"T_dd6ed_row6_col4\" class=\"data row6 col4\" >0.4662</td>\n", "      <td id=\"T_dd6ed_row6_col5\" class=\"data row6 col5\" >0.3611</td>\n", "      <td id=\"T_dd6ed_row6_col6\" class=\"data row6 col6\" >0.3794</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_dd6ed_row7_col0\" class=\"data row7 col0\" >0.8258</td>\n", "      <td id=\"T_dd6ed_row7_col1\" class=\"data row7 col1\" >0.7959</td>\n", "      <td id=\"T_dd6ed_row7_col2\" class=\"data row7 col2\" >0.4023</td>\n", "      <td id=\"T_dd6ed_row7_col3\" class=\"data row7 col3\" >0.6794</td>\n", "      <td id=\"T_dd6ed_row7_col4\" class=\"data row7 col4\" >0.5053</td>\n", "      <td id=\"T_dd6ed_row7_col5\" class=\"data row7 col5\" >0.4079</td>\n", "      <td id=\"T_dd6ed_row7_col6\" class=\"data row7 col6\" >0.4286</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_dd6ed_row8_col0\" class=\"data row8 col0\" >0.8208</td>\n", "      <td id=\"T_dd6ed_row8_col1\" class=\"data row8 col1\" >0.7602</td>\n", "      <td id=\"T_dd6ed_row8_col2\" class=\"data row8 col2\" >0.3739</td>\n", "      <td id=\"T_dd6ed_row8_col3\" class=\"data row8 col3\" >0.6701</td>\n", "      <td id=\"T_dd6ed_row8_col4\" class=\"data row8 col4\" >0.4800</td>\n", "      <td id=\"T_dd6ed_row8_col5\" class=\"data row8 col5\" >0.3821</td>\n", "      <td id=\"T_dd6ed_row8_col6\" class=\"data row8 col6\" >0.4058</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_dd6ed_row9_col0\" class=\"data row9 col0\" >0.8138</td>\n", "      <td id=\"T_dd6ed_row9_col1\" class=\"data row9 col1\" >0.7839</td>\n", "      <td id=\"T_dd6ed_row9_col2\" class=\"data row9 col2\" >0.3824</td>\n", "      <td id=\"T_dd6ed_row9_col3\" class=\"data row9 col3\" >0.6308</td>\n", "      <td id=\"T_dd6ed_row9_col4\" class=\"data row9 col4\" >0.4762</td>\n", "      <td id=\"T_dd6ed_row9_col5\" class=\"data row9 col5\" >0.3711</td>\n", "      <td id=\"T_dd6ed_row9_col6\" class=\"data row9 col6\" >0.3883</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_dd6ed_row10_col0\" class=\"data row10 col0\" >0.8193</td>\n", "      <td id=\"T_dd6ed_row10_col1\" class=\"data row10 col1\" >0.7748</td>\n", "      <td id=\"T_dd6ed_row10_col2\" class=\"data row10 col2\" >0.3870</td>\n", "      <td id=\"T_dd6ed_row10_col3\" class=\"data row10 col3\" >0.6552</td>\n", "      <td id=\"T_dd6ed_row10_col4\" class=\"data row10 col4\" >0.4864</td>\n", "      <td id=\"T_dd6ed_row10_col5\" class=\"data row10 col5\" >0.3855</td>\n", "      <td id=\"T_dd6ed_row10_col6\" class=\"data row10 col6\" >0.4053</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_dd6ed_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_dd6ed_row11_col0\" class=\"data row11 col0\" >0.0055</td>\n", "      <td id=\"T_dd6ed_row11_col1\" class=\"data row11 col1\" >0.0170</td>\n", "      <td id=\"T_dd6ed_row11_col2\" class=\"data row11 col2\" >0.0184</td>\n", "      <td id=\"T_dd6ed_row11_col3\" class=\"data row11 col3\" >0.0207</td>\n", "      <td id=\"T_dd6ed_row11_col4\" class=\"data row11 col4\" >0.0186</td>\n", "      <td id=\"T_dd6ed_row11_col5\" class=\"data row11 col5\" >0.0208</td>\n", "      <td id=\"T_dd6ed_row11_col6\" class=\"data row11 col6\" >0.0207</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/4 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9cf908bb9238434e8da330ccc13db02e"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["# train a random forest model\n", "rf = create_model('rf')"]}, {"cell_type": "markdown", "metadata": {"id": "z6F3Fk7TEQp8"}, "source": ["ここでの平均スコアは `compare_models` 関数の出力と一致します。これは、 `compare_models` のスコアの表に出力される指標値が、CVの全てのフォールドの平均スコアであるためです。"]}, {"cell_type": "markdown", "metadata": {"id": "XvpjzbGQEQqB"}, "source": ["# モデルの調整"]}, {"cell_type": "markdown", "metadata": {"id": "nc_GgksHEQqE"}, "source": ["`tune_model` 関数は事前に定義したグリッドを用いて、モデルのハイパーパラメータを自動でチューニングします。この関数の出力は表形式で表示され、クロスバリデーションによるフォールドごとのスコアでる。最適なモデルは `optimize` パラメータで定義された指標に基づいて選択されます。カスタム探索グリッドを使用するには、 `custom_grid` パラメータを渡す必要があります。デフォルトの検索方法は `randomgridsearch` であり、チューニングにより必ずパフォーマンスが向上する保証はありません。パフォーマンスが改善されない場合は、 `n_iter` パラメータを大きくしてみることが考えられます。"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 474, "referenced_widgets": ["5137a556f8c74253bdeedfe201deb6b9", "be899c649ae043b7a2720bc18aff5bbc", "b1114f6ea38f4455834dc57c93b13c94", "4366f00d02864da984daef7d3f3c7602", "09d0c50f131d4a39b1c7958473ec9967", "d328a3d2942743eca0a73b5de5904760", "54835add990f4b4e8d460c36ec388087", "ce58945cf8a04ad6953a9bffaa1e684a", "fff2b88c772a4987a5f592e24be76c9b", "6719ef18056e4bf298477a17e4341619", "2298a5809b3948988271aa55f759984a"]}, "id": "of46aj6vEQqJ", "outputId": "3cfef802-efc7-4521-8277-33343c5ccbfe"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f3639944220>"], "text/html": ["<style type=\"text/css\">\n", "#T_2e730_row10_col0, #T_2e730_row10_col1, #T_2e730_row10_col2, #T_2e730_row10_col3, #T_2e730_row10_col4, #T_2e730_row10_col5, #T_2e730_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_2e730_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col1\" >AUC</th>\n", "      <th class=\"col_heading level0 col2\" >Recall</th>\n", "      <th class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th class=\"col_heading level0 col4\" >F1</th>\n", "      <th class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_2e730_row0_col0\" class=\"data row0 col0\" >0.8264</td>\n", "      <td id=\"T_2e730_row0_col1\" class=\"data row0 col1\" >0.6573</td>\n", "      <td id=\"T_2e730_row0_col2\" class=\"data row0 col2\" >0.3541</td>\n", "      <td id=\"T_2e730_row0_col3\" class=\"data row0 col3\" >0.7184</td>\n", "      <td id=\"T_2e730_row0_col4\" class=\"data row0 col4\" >0.4744</td>\n", "      <td id=\"T_2e730_row0_col5\" class=\"data row0 col5\" >0.3845</td>\n", "      <td id=\"T_2e730_row0_col6\" class=\"data row0 col6\" >0.4191</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_2e730_row1_col0\" class=\"data row1 col0\" >0.8271</td>\n", "      <td id=\"T_2e730_row1_col1\" class=\"data row1 col1\" >0.6527</td>\n", "      <td id=\"T_2e730_row1_col2\" class=\"data row1 col2\" >0.3399</td>\n", "      <td id=\"T_2e730_row1_col3\" class=\"data row1 col3\" >0.7362</td>\n", "      <td id=\"T_2e730_row1_col4\" class=\"data row1 col4\" >0.4651</td>\n", "      <td id=\"T_2e730_row1_col5\" class=\"data row1 col5\" >0.3782</td>\n", "      <td id=\"T_2e730_row1_col6\" class=\"data row1 col6\" >0.4185</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_2e730_row2_col0\" class=\"data row2 col0\" >0.8221</td>\n", "      <td id=\"T_2e730_row2_col1\" class=\"data row2 col1\" >0.6515</td>\n", "      <td id=\"T_2e730_row2_col2\" class=\"data row2 col2\" >0.3456</td>\n", "      <td id=\"T_2e730_row2_col3\" class=\"data row2 col3\" >0.6971</td>\n", "      <td id=\"T_2e730_row2_col4\" class=\"data row2 col4\" >0.4621</td>\n", "      <td id=\"T_2e730_row2_col5\" class=\"data row2 col5\" >0.3697</td>\n", "      <td id=\"T_2e730_row2_col6\" class=\"data row2 col6\" >0.4024</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_2e730_row3_col0\" class=\"data row3 col0\" >0.8170</td>\n", "      <td id=\"T_2e730_row3_col1\" class=\"data row3 col1\" >0.6432</td>\n", "      <td id=\"T_2e730_row3_col2\" class=\"data row3 col2\" >0.3314</td>\n", "      <td id=\"T_2e730_row3_col3\" class=\"data row3 col3\" >0.6763</td>\n", "      <td id=\"T_2e730_row3_col4\" class=\"data row3 col4\" >0.4449</td>\n", "      <td id=\"T_2e730_row3_col5\" class=\"data row3 col5\" >0.3503</td>\n", "      <td id=\"T_2e730_row3_col6\" class=\"data row3 col6\" >0.3823</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_2e730_row4_col0\" class=\"data row4 col0\" >0.8239</td>\n", "      <td id=\"T_2e730_row4_col1\" class=\"data row4 col1\" >0.6486</td>\n", "      <td id=\"T_2e730_row4_col2\" class=\"data row4 col2\" >0.3343</td>\n", "      <td id=\"T_2e730_row4_col3\" class=\"data row4 col3\" >0.7195</td>\n", "      <td id=\"T_2e730_row4_col4\" class=\"data row4 col4\" >0.4565</td>\n", "      <td id=\"T_2e730_row4_col5\" class=\"data row4 col5\" >0.3678</td>\n", "      <td id=\"T_2e730_row4_col6\" class=\"data row4 col6\" >0.4063</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_2e730_row5_col0\" class=\"data row5 col0\" >0.8252</td>\n", "      <td id=\"T_2e730_row5_col1\" class=\"data row5 col1\" >0.6474</td>\n", "      <td id=\"T_2e730_row5_col2\" class=\"data row5 col2\" >0.3286</td>\n", "      <td id=\"T_2e730_row5_col3\" class=\"data row5 col3\" >0.7342</td>\n", "      <td id=\"T_2e730_row5_col4\" class=\"data row5 col4\" >0.4540</td>\n", "      <td id=\"T_2e730_row5_col5\" class=\"data row5 col5\" >0.3675</td>\n", "      <td id=\"T_2e730_row5_col6\" class=\"data row5 col6\" >0.4097</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_2e730_row6_col0\" class=\"data row6 col0\" >0.8189</td>\n", "      <td id=\"T_2e730_row6_col1\" class=\"data row6 col1\" >0.6434</td>\n", "      <td id=\"T_2e730_row6_col2\" class=\"data row6 col2\" >0.3286</td>\n", "      <td id=\"T_2e730_row6_col3\" class=\"data row6 col3\" >0.6905</td>\n", "      <td id=\"T_2e730_row6_col4\" class=\"data row6 col4\" >0.4453</td>\n", "      <td id=\"T_2e730_row6_col5\" class=\"data row6 col5\" >0.3530</td>\n", "      <td id=\"T_2e730_row6_col6\" class=\"data row6 col6\" >0.3878</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_2e730_row7_col0\" class=\"data row7 col0\" >0.8283</td>\n", "      <td id=\"T_2e730_row7_col1\" class=\"data row7 col1\" >0.6494</td>\n", "      <td id=\"T_2e730_row7_col2\" class=\"data row7 col2\" >0.3286</td>\n", "      <td id=\"T_2e730_row7_col3\" class=\"data row7 col3\" >0.7582</td>\n", "      <td id=\"T_2e730_row7_col4\" class=\"data row7 col4\" >0.4585</td>\n", "      <td id=\"T_2e730_row7_col5\" class=\"data row7 col5\" >0.3749</td>\n", "      <td id=\"T_2e730_row7_col6\" class=\"data row7 col6\" >0.4213</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_2e730_row8_col0\" class=\"data row8 col0\" >0.8233</td>\n", "      <td id=\"T_2e730_row8_col1\" class=\"data row8 col1\" >0.6503</td>\n", "      <td id=\"T_2e730_row8_col2\" class=\"data row8 col2\" >0.3399</td>\n", "      <td id=\"T_2e730_row8_col3\" class=\"data row8 col3\" >0.7101</td>\n", "      <td id=\"T_2e730_row8_col4\" class=\"data row8 col4\" >0.4598</td>\n", "      <td id=\"T_2e730_row8_col5\" class=\"data row8 col5\" >0.3695</td>\n", "      <td id=\"T_2e730_row8_col6\" class=\"data row8 col6\" >0.4054</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_2e730_row9_col0\" class=\"data row9 col0\" >0.8125</td>\n", "      <td id=\"T_2e730_row9_col1\" class=\"data row9 col1\" >0.6424</td>\n", "      <td id=\"T_2e730_row9_col2\" class=\"data row9 col2\" >0.3371</td>\n", "      <td id=\"T_2e730_row9_col3\" class=\"data row9 col3\" >0.6467</td>\n", "      <td id=\"T_2e730_row9_col4\" class=\"data row9 col4\" >0.4432</td>\n", "      <td id=\"T_2e730_row9_col5\" class=\"data row9 col5\" >0.3437</td>\n", "      <td id=\"T_2e730_row9_col6\" class=\"data row9 col6\" >0.3701</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_2e730_row10_col0\" class=\"data row10 col0\" >0.8225</td>\n", "      <td id=\"T_2e730_row10_col1\" class=\"data row10 col1\" >0.6486</td>\n", "      <td id=\"T_2e730_row10_col2\" class=\"data row10 col2\" >0.3368</td>\n", "      <td id=\"T_2e730_row10_col3\" class=\"data row10 col3\" >0.7087</td>\n", "      <td id=\"T_2e730_row10_col4\" class=\"data row10 col4\" >0.4564</td>\n", "      <td id=\"T_2e730_row10_col5\" class=\"data row10 col5\" >0.3659</td>\n", "      <td id=\"T_2e730_row10_col6\" class=\"data row10 col6\" >0.4023</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2e730_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_2e730_row11_col0\" class=\"data row11 col0\" >0.0047</td>\n", "      <td id=\"T_2e730_row11_col1\" class=\"data row11 col1\" >0.0045</td>\n", "      <td id=\"T_2e730_row11_col2\" class=\"data row11 col2\" >0.0080</td>\n", "      <td id=\"T_2e730_row11_col3\" class=\"data row11 col3\" >0.0307</td>\n", "      <td id=\"T_2e730_row11_col4\" class=\"data row11 col4\" >0.0094</td>\n", "      <td id=\"T_2e730_row11_col5\" class=\"data row11 col5\" >0.0123</td>\n", "      <td id=\"T_2e730_row11_col6\" class=\"data row11 col6\" >0.0162</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/7 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5137a556f8c74253bdeedfe201deb6b9"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "stream", "name": "stdout", "text": ["Fitting 10 folds for each of 10 candidates, totalling 100 fits\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["# tune decision tree model\n", "tuned_dt = tune_model(dt)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "__anDkttEQqV", "outputId": "42a146ad-b0da-4bbb-f67d-fc1af0bc8611"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["DecisionTreeClassifier(ccp_alpha=0.0, class_weight=None, criterion='gini',\n", "                       max_depth=1, max_features=1.0, max_leaf_nodes=None,\n", "                       min_impurity_decrease=0.01, min_samples_leaf=6,\n", "                       min_samples_split=5, min_weight_fraction_leaf=0.0,\n", "                       random_state=123, splitter='best')\n"]}], "source": ["print(tuned_dt)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 474, "referenced_widgets": ["06aca29a7eb1482080ba41243ca8c490", "97cd62dcfd6a45e791c464caa811a0bd", "8d673899f3fc443191a1eb3afa2771dd", "c4f6420e4417402bae0e1a193a916e28", "abb50535bcb24b89bd8c6944d0898cd8", "98ef97d8741742f582f47a1c8c2313b7", "d93426816fb64a94bd2ce89b1aac63f8", "8b21cb19cc2f4f85ae24771d944853f3", "a3918f9396874183aa8484895c2ac57b", "ad890f68783c4dafab99711ba50aef6b", "5525ec133ceb4a309d032191fa8df30f"]}, "id": "xN1nYwFXEQqv", "outputId": "e999de98-f264-42d2-dee2-68ce2fd028b0"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f3627bbcdf0>"], "text/html": ["<style type=\"text/css\">\n", "#T_ce243_row10_col0, #T_ce243_row10_col1, #T_ce243_row10_col2, #T_ce243_row10_col3, #T_ce243_row10_col4, #T_ce243_row10_col5, #T_ce243_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_ce243_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col1\" >AUC</th>\n", "      <th class=\"col_heading level0 col2\" >Recall</th>\n", "      <th class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th class=\"col_heading level0 col4\" >F1</th>\n", "      <th class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_ce243_row0_col0\" class=\"data row0 col0\" >0.7794</td>\n", "      <td id=\"T_ce243_row0_col1\" class=\"data row0 col1\" >0.6342</td>\n", "      <td id=\"T_ce243_row0_col2\" class=\"data row0 col2\" >0.0595</td>\n", "      <td id=\"T_ce243_row0_col3\" class=\"data row0 col3\" >0.5122</td>\n", "      <td id=\"T_ce243_row0_col4\" class=\"data row0 col4\" >0.1066</td>\n", "      <td id=\"T_ce243_row0_col5\" class=\"data row0 col5\" >0.0635</td>\n", "      <td id=\"T_ce243_row0_col6\" class=\"data row0 col6\" >0.1139</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_ce243_row1_col0\" class=\"data row1 col0\" >0.7826</td>\n", "      <td id=\"T_ce243_row1_col1\" class=\"data row1 col1\" >0.6624</td>\n", "      <td id=\"T_ce243_row1_col2\" class=\"data row1 col2\" >0.0453</td>\n", "      <td id=\"T_ce243_row1_col3\" class=\"data row1 col3\" >0.6154</td>\n", "      <td id=\"T_ce243_row1_col4\" class=\"data row1 col4\" >0.0844</td>\n", "      <td id=\"T_ce243_row1_col5\" class=\"data row1 col5\" >0.0558</td>\n", "      <td id=\"T_ce243_row1_col6\" class=\"data row1 col6\" >0.1222</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_ce243_row2_col0\" class=\"data row2 col0\" >0.7832</td>\n", "      <td id=\"T_ce243_row2_col1\" class=\"data row2 col1\" >0.7013</td>\n", "      <td id=\"T_ce243_row2_col2\" class=\"data row2 col2\" >0.0623</td>\n", "      <td id=\"T_ce243_row2_col3\" class=\"data row2 col3\" >0.5946</td>\n", "      <td id=\"T_ce243_row2_col4\" class=\"data row2 col4\" >0.1128</td>\n", "      <td id=\"T_ce243_row2_col5\" class=\"data row2 col5\" >0.0740</td>\n", "      <td id=\"T_ce243_row2_col6\" class=\"data row2 col6\" >0.1386</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_ce243_row3_col0\" class=\"data row3 col0\" >0.7801</td>\n", "      <td id=\"T_ce243_row3_col1\" class=\"data row3 col1\" >0.6399</td>\n", "      <td id=\"T_ce243_row3_col2\" class=\"data row3 col2\" >0.0510</td>\n", "      <td id=\"T_ce243_row3_col3\" class=\"data row3 col3\" >0.5294</td>\n", "      <td id=\"T_ce243_row3_col4\" class=\"data row3 col4\" >0.0930</td>\n", "      <td id=\"T_ce243_row3_col5\" class=\"data row3 col5\" >0.0563</td>\n", "      <td id=\"T_ce243_row3_col6\" class=\"data row3 col6\" >0.1096</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_ce243_row4_col0\" class=\"data row4 col0\" >0.7794</td>\n", "      <td id=\"T_ce243_row4_col1\" class=\"data row4 col1\" >0.6404</td>\n", "      <td id=\"T_ce243_row4_col2\" class=\"data row4 col2\" >0.0482</td>\n", "      <td id=\"T_ce243_row4_col3\" class=\"data row4 col3\" >0.5152</td>\n", "      <td id=\"T_ce243_row4_col4\" class=\"data row4 col4\" >0.0881</td>\n", "      <td id=\"T_ce243_row4_col5\" class=\"data row4 col5\" >0.0522</td>\n", "      <td id=\"T_ce243_row4_col6\" class=\"data row4 col6\" >0.1029</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_ce243_row5_col0\" class=\"data row5 col0\" >0.7820</td>\n", "      <td id=\"T_ce243_row5_col1\" class=\"data row5 col1\" >0.6774</td>\n", "      <td id=\"T_ce243_row5_col2\" class=\"data row5 col2\" >0.0595</td>\n", "      <td id=\"T_ce243_row5_col3\" class=\"data row5 col3\" >0.5676</td>\n", "      <td id=\"T_ce243_row5_col4\" class=\"data row5 col4\" >0.1077</td>\n", "      <td id=\"T_ce243_row5_col5\" class=\"data row5 col5\" >0.0686</td>\n", "      <td id=\"T_ce243_row5_col6\" class=\"data row5 col6\" >0.1286</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_ce243_row6_col0\" class=\"data row6 col0\" >0.7857</td>\n", "      <td id=\"T_ce243_row6_col1\" class=\"data row6 col1\" >0.6404</td>\n", "      <td id=\"T_ce243_row6_col2\" class=\"data row6 col2\" >0.0567</td>\n", "      <td id=\"T_ce243_row6_col3\" class=\"data row6 col3\" >0.6897</td>\n", "      <td id=\"T_ce243_row6_col4\" class=\"data row6 col4\" >0.1047</td>\n", "      <td id=\"T_ce243_row6_col5\" class=\"data row6 col5\" >0.0736</td>\n", "      <td id=\"T_ce243_row6_col6\" class=\"data row6 col6\" >0.1536</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_ce243_row7_col0\" class=\"data row7 col0\" >0.7788</td>\n", "      <td id=\"T_ce243_row7_col1\" class=\"data row7 col1\" >0.6633</td>\n", "      <td id=\"T_ce243_row7_col2\" class=\"data row7 col2\" >0.0623</td>\n", "      <td id=\"T_ce243_row7_col3\" class=\"data row7 col3\" >0.5000</td>\n", "      <td id=\"T_ce243_row7_col4\" class=\"data row7 col4\" >0.1108</td>\n", "      <td id=\"T_ce243_row7_col5\" class=\"data row7 col5\" >0.0650</td>\n", "      <td id=\"T_ce243_row7_col6\" class=\"data row7 col6\" >0.1131</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_ce243_row8_col0\" class=\"data row8 col0\" >0.7732</td>\n", "      <td id=\"T_ce243_row8_col1\" class=\"data row8 col1\" >0.6275</td>\n", "      <td id=\"T_ce243_row8_col2\" class=\"data row8 col2\" >0.0340</td>\n", "      <td id=\"T_ce243_row8_col3\" class=\"data row8 col3\" >0.3636</td>\n", "      <td id=\"T_ce243_row8_col4\" class=\"data row8 col4\" >0.0622</td>\n", "      <td id=\"T_ce243_row8_col5\" class=\"data row8 col5\" >0.0253</td>\n", "      <td id=\"T_ce243_row8_col6\" class=\"data row8 col6\" >0.0499</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_ce243_row9_col0\" class=\"data row9 col0\" >0.7781</td>\n", "      <td id=\"T_ce243_row9_col1\" class=\"data row9 col1\" >0.6681</td>\n", "      <td id=\"T_ce243_row9_col2\" class=\"data row9 col2\" >0.0482</td>\n", "      <td id=\"T_ce243_row9_col3\" class=\"data row9 col3\" >0.4857</td>\n", "      <td id=\"T_ce243_row9_col4\" class=\"data row9 col4\" >0.0876</td>\n", "      <td id=\"T_ce243_row9_col5\" class=\"data row9 col5\" >0.0497</td>\n", "      <td id=\"T_ce243_row9_col6\" class=\"data row9 col6\" >0.0954</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_ce243_row10_col0\" class=\"data row10 col0\" >0.7802</td>\n", "      <td id=\"T_ce243_row10_col1\" class=\"data row10 col1\" >0.6555</td>\n", "      <td id=\"T_ce243_row10_col2\" class=\"data row10 col2\" >0.0527</td>\n", "      <td id=\"T_ce243_row10_col3\" class=\"data row10 col3\" >0.5373</td>\n", "      <td id=\"T_ce243_row10_col4\" class=\"data row10 col4\" >0.0958</td>\n", "      <td id=\"T_ce243_row10_col5\" class=\"data row10 col5\" >0.0584</td>\n", "      <td id=\"T_ce243_row10_col6\" class=\"data row10 col6\" >0.1128</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ce243_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_ce243_row11_col0\" class=\"data row11 col0\" >0.0032</td>\n", "      <td id=\"T_ce243_row11_col1\" class=\"data row11 col1\" >0.0219</td>\n", "      <td id=\"T_ce243_row11_col2\" class=\"data row11 col2\" >0.0086</td>\n", "      <td id=\"T_ce243_row11_col3\" class=\"data row11 col3\" >0.0830</td>\n", "      <td id=\"T_ce243_row11_col4\" class=\"data row11 col4\" >0.0150</td>\n", "      <td id=\"T_ce243_row11_col5\" class=\"data row11 col5\" >0.0137</td>\n", "      <td id=\"T_ce243_row11_col6\" class=\"data row11 col6\" >0.0265</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/7 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "06aca29a7eb1482080ba41243ca8c490"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "stream", "name": "stdout", "text": ["Fitting 10 folds for each of 10 candidates, totalling 100 fits\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["# tune knn model\n", "import numpy as np\n", "tuned_knn = tune_model(knn, custom_grid = {'n_neighbors' : np.arange(0,50,1)})"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WNCGb2HAb6xG", "outputId": "c661d3e6-63ae-4033-e6b5-5096e070cf91"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["KNeighborsClassifier(algorithm='auto', leaf_size=30, metric='minkowski',\n", "                     metric_params=None, n_jobs=-1, n_neighbors=42, p=2,\n", "                     weights='uniform')\n"]}], "source": ["print(tuned_knn)"]}, {"cell_type": "markdown", "metadata": {"id": "IqxEZRi1EQrO"}, "source": ["デフォルトでは `tune_model` は `Accuracy` に基づき最適化しますが、`optimize` パラメータで変更ができます。例えば、 `tune_model(dt, optimize = 'AUC')` は、最も`AUC`が大きくなる決定木分類器のハイパーパラメータを探索します。"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 474, "referenced_widgets": ["1a002b747073467c8cc47622d8499128", "ae213f04bc7145319e395277db148532", "f552ab5f1c884070be9f767b3f893715", "218cc7c9c964473d8fd9c6d6b098e83a", "7bee3988fbb64728b11ec0c601b0dfcf", "d16205e40f624ca2911850b308707bbb", "b1d7231ec4ab4acaa9a15fcaf69cdfe0", "435ad7433a274fff8de9957589698826", "bf18e18ea07b4d7b9ab8750ac5aed32a", "8e65629da34a486696d49053bd20ec9f", "50c7486147d34c9e8a728b2251392985"]}, "id": "6AKYlRPub6xG", "outputId": "81d68be4-9b9d-4500-9b3b-a696719af0f3"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f363180fc40>"], "text/html": ["<style type=\"text/css\">\n", "#T_2310d_row10_col0, #T_2310d_row10_col1, #T_2310d_row10_col2, #T_2310d_row10_col3, #T_2310d_row10_col4, #T_2310d_row10_col5, #T_2310d_row10_col6 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_2310d_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col1\" >AUC</th>\n", "      <th class=\"col_heading level0 col2\" >Recall</th>\n", "      <th class=\"col_heading level0 col3\" >Prec.</th>\n", "      <th class=\"col_heading level0 col4\" >F1</th>\n", "      <th class=\"col_heading level0 col5\" >Kappa</th>\n", "      <th class=\"col_heading level0 col6\" >MCC</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Fold</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_2310d_row0_col0\" class=\"data row0 col0\" >0.7682</td>\n", "      <td id=\"T_2310d_row0_col1\" class=\"data row0 col1\" >0.7853</td>\n", "      <td id=\"T_2310d_row0_col2\" class=\"data row0 col2\" >0.6317</td>\n", "      <td id=\"T_2310d_row0_col3\" class=\"data row0 col3\" >0.4816</td>\n", "      <td id=\"T_2310d_row0_col4\" class=\"data row0 col4\" >0.5466</td>\n", "      <td id=\"T_2310d_row0_col5\" class=\"data row0 col5\" >0.3946</td>\n", "      <td id=\"T_2310d_row0_col6\" class=\"data row0 col6\" >0.4012</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_2310d_row1_col0\" class=\"data row1 col0\" >0.7638</td>\n", "      <td id=\"T_2310d_row1_col1\" class=\"data row1 col1\" >0.7883</td>\n", "      <td id=\"T_2310d_row1_col2\" class=\"data row1 col2\" >0.6431</td>\n", "      <td id=\"T_2310d_row1_col3\" class=\"data row1 col3\" >0.4749</td>\n", "      <td id=\"T_2310d_row1_col4\" class=\"data row1 col4\" >0.5463</td>\n", "      <td id=\"T_2310d_row1_col5\" class=\"data row1 col5\" >0.3915</td>\n", "      <td id=\"T_2310d_row1_col6\" class=\"data row1 col6\" >0.3997</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_2310d_row2_col0\" class=\"data row2 col0\" >0.7870</td>\n", "      <td id=\"T_2310d_row2_col1\" class=\"data row2 col1\" >0.8120</td>\n", "      <td id=\"T_2310d_row2_col2\" class=\"data row2 col2\" >0.6884</td>\n", "      <td id=\"T_2310d_row2_col3\" class=\"data row2 col3\" >0.5137</td>\n", "      <td id=\"T_2310d_row2_col4\" class=\"data row2 col4\" >0.5884</td>\n", "      <td id=\"T_2310d_row2_col5\" class=\"data row2 col5\" >0.4487</td>\n", "      <td id=\"T_2310d_row2_col6\" class=\"data row2 col6\" >0.4575</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_2310d_row3_col0\" class=\"data row3 col0\" >0.7588</td>\n", "      <td id=\"T_2310d_row3_col1\" class=\"data row3 col1\" >0.7668</td>\n", "      <td id=\"T_2310d_row3_col2\" class=\"data row3 col2\" >0.5864</td>\n", "      <td id=\"T_2310d_row3_col3\" class=\"data row3 col3\" >0.4641</td>\n", "      <td id=\"T_2310d_row3_col4\" class=\"data row3 col4\" >0.5181</td>\n", "      <td id=\"T_2310d_row3_col5\" class=\"data row3 col5\" >0.3602</td>\n", "      <td id=\"T_2310d_row3_col6\" class=\"data row3 col6\" >0.3645</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_2310d_row4_col0\" class=\"data row4 col0\" >0.7801</td>\n", "      <td id=\"T_2310d_row4_col1\" class=\"data row4 col1\" >0.7766</td>\n", "      <td id=\"T_2310d_row4_col2\" class=\"data row4 col2\" >0.5949</td>\n", "      <td id=\"T_2310d_row4_col3\" class=\"data row4 col3\" >0.5024</td>\n", "      <td id=\"T_2310d_row4_col4\" class=\"data row4 col4\" >0.5447</td>\n", "      <td id=\"T_2310d_row4_col5\" class=\"data row4 col5\" >0.4011</td>\n", "      <td id=\"T_2310d_row4_col6\" class=\"data row4 col6\" >0.4036</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_2310d_row5_col0\" class=\"data row5 col0\" >0.7757</td>\n", "      <td id=\"T_2310d_row5_col1\" class=\"data row5 col1\" >0.7910</td>\n", "      <td id=\"T_2310d_row5_col2\" class=\"data row5 col2\" >0.6374</td>\n", "      <td id=\"T_2310d_row5_col3\" class=\"data row5 col3\" >0.4945</td>\n", "      <td id=\"T_2310d_row5_col4\" class=\"data row5 col4\" >0.5569</td>\n", "      <td id=\"T_2310d_row5_col5\" class=\"data row5 col5\" >0.4099</td>\n", "      <td id=\"T_2310d_row5_col6\" class=\"data row5 col6\" >0.4159</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_2310d_row6_col0\" class=\"data row6 col0\" >0.7531</td>\n", "      <td id=\"T_2310d_row6_col1\" class=\"data row6 col1\" >0.7490</td>\n", "      <td id=\"T_2310d_row6_col2\" class=\"data row6 col2\" >0.6006</td>\n", "      <td id=\"T_2310d_row6_col3\" class=\"data row6 col3\" >0.4559</td>\n", "      <td id=\"T_2310d_row6_col4\" class=\"data row6 col4\" >0.5183</td>\n", "      <td id=\"T_2310d_row6_col5\" class=\"data row6 col5\" >0.3565</td>\n", "      <td id=\"T_2310d_row6_col6\" class=\"data row6 col6\" >0.3626</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_2310d_row7_col0\" class=\"data row7 col0\" >0.7726</td>\n", "      <td id=\"T_2310d_row7_col1\" class=\"data row7 col1\" >0.7828</td>\n", "      <td id=\"T_2310d_row7_col2\" class=\"data row7 col2\" >0.6374</td>\n", "      <td id=\"T_2310d_row7_col3\" class=\"data row7 col3\" >0.4891</td>\n", "      <td id=\"T_2310d_row7_col4\" class=\"data row7 col4\" >0.5535</td>\n", "      <td id=\"T_2310d_row7_col5\" class=\"data row7 col5\" >0.4044</td>\n", "      <td id=\"T_2310d_row7_col6\" class=\"data row7 col6\" >0.4108</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_2310d_row8_col0\" class=\"data row8 col0\" >0.7519</td>\n", "      <td id=\"T_2310d_row8_col1\" class=\"data row8 col1\" >0.7568</td>\n", "      <td id=\"T_2310d_row8_col2\" class=\"data row8 col2\" >0.6062</td>\n", "      <td id=\"T_2310d_row8_col3\" class=\"data row8 col3\" >0.4544</td>\n", "      <td id=\"T_2310d_row8_col4\" class=\"data row8 col4\" >0.5194</td>\n", "      <td id=\"T_2310d_row8_col5\" class=\"data row8 col5\" >0.3568</td>\n", "      <td id=\"T_2310d_row8_col6\" class=\"data row8 col6\" >0.3635</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_2310d_row9_col0\" class=\"data row9 col0\" >0.7705</td>\n", "      <td id=\"T_2310d_row9_col1\" class=\"data row9 col1\" >0.7743</td>\n", "      <td id=\"T_2310d_row9_col2\" class=\"data row9 col2\" >0.6147</td>\n", "      <td id=\"T_2310d_row9_col3\" class=\"data row9 col3\" >0.4855</td>\n", "      <td id=\"T_2310d_row9_col4\" class=\"data row9 col4\" >0.5425</td>\n", "      <td id=\"T_2310d_row9_col5\" class=\"data row9 col5\" >0.3922</td>\n", "      <td id=\"T_2310d_row9_col6\" class=\"data row9 col6\" >0.3970</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_2310d_row10_col0\" class=\"data row10 col0\" >0.7682</td>\n", "      <td id=\"T_2310d_row10_col1\" class=\"data row10 col1\" >0.7783</td>\n", "      <td id=\"T_2310d_row10_col2\" class=\"data row10 col2\" >0.6241</td>\n", "      <td id=\"T_2310d_row10_col3\" class=\"data row10 col3\" >0.4816</td>\n", "      <td id=\"T_2310d_row10_col4\" class=\"data row10 col4\" >0.5435</td>\n", "      <td id=\"T_2310d_row10_col5\" class=\"data row10 col5\" >0.3916</td>\n", "      <td id=\"T_2310d_row10_col6\" class=\"data row10 col6\" >0.3976</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2310d_level0_row11\" class=\"row_heading level0 row11\" >Std</th>\n", "      <td id=\"T_2310d_row11_col0\" class=\"data row11 col0\" >0.0108</td>\n", "      <td id=\"T_2310d_row11_col1\" class=\"data row11 col1\" >0.0171</td>\n", "      <td id=\"T_2310d_row11_col2\" class=\"data row11 col2\" >0.0285</td>\n", "      <td id=\"T_2310d_row11_col3\" class=\"data row11 col3\" >0.0186</td>\n", "      <td id=\"T_2310d_row11_col4\" class=\"data row11 col4\" >0.0204</td>\n", "      <td id=\"T_2310d_row11_col5\" class=\"data row11 col5\" >0.0270</td>\n", "      <td id=\"T_2310d_row11_col6\" class=\"data row11 col6\" >0.0276</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Processing:   0%|          | 0/7 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1a002b747073467c8cc47622d8499128"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}, {"output_type": "stream", "name": "stdout", "text": ["Fitting 10 folds for each of 10 candidates, totalling 100 fits\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}], "source": ["# tune random forest model (this may take some time)\n", "tuned_rf = tune_model(rf, optimize = 'AUC')"]}, {"cell_type": "markdown", "metadata": {"id": "w_P46O0jEQrT"}, "source": ["# モデル結果の描画"]}, {"cell_type": "markdown", "metadata": {"id": "FGM9GOtjEQrV"}, "source": ["`plot_mdoel` 関数は、テスト/ホールドアウトのデータにおける学習済みモデルのパフォーマンスを分析します。場合によっては、モデルの再トレーニングが必要になるかもしれません。利用可能な描画タイプは 15 種類以上あります。利用可能な描画タイプの一覧は `plot_model` docstring を参照してください。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 294}, "id": "RLbLqvkHEQra", "outputId": "4b9b2e16-1474-4a0a-9694-c0cab88c0cb9"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["# auc plot\n", "plot_model(tuned_rf, plot = 'auc')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 294}, "id": "4IvchQoiEQrr", "outputId": "b8edde6f-77af-4d56-b531-70ce32648037"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["# precision recall curve plot\n", "plot_model(tuned_rf, plot = 'pr')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 478}, "id": "nVScSxJ-EQr2", "outputId": "8167b1aa-e215-4104-9234-0218bbe295a4"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["# feature importance plot\n", "plot_model(tuned_rf, plot='feature')"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 297}, "id": "OAB5mes-EQsA", "outputId": "22701dd4-c82d-41e6-87dd-1aad4f6bb8c9"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["plot_model(tuned_rf, plot = 'confusion_matrix')"]}, {"cell_type": "markdown", "metadata": {"id": "deClKJrbEQsJ"}, "source": ["モデルのパフォーマンスを分析するもう一つの方法は、与えられたモデルに対して利用可能な全ての描画をユーザーインターフェースで表示できる `evaluate_model` 関数を使用することです。これは、Jupyterと互換性のあるノートブック環境を使用している場合にのみ利用可能です。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 234, "referenced_widgets": ["5485b433e63443c592fa1faac9c0f78e", "9ab139bc1f9a4f78942e742cfe6bf1d0", "a6dd9c3fd9c1479b8eceae52e638041a", "8a77291c3fda449f94e2fe3850cbde10", "d4373f1fcd1a426cb3689f416c2c36a5", "2ce35ab7b1a540c7bb4d38c4f6c688f8", "4ad9962bc6624f048973be7a60e530d6"]}, "id": "OcLV1Ln6EQsN", "outputId": "6b5ce5a6-b984-480a-8fef-546295d9d7ec"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["interactive(children=(ToggleButtons(description='Plot Type:', icons=('',), options=(('Pipeline Plot', 'pipelin…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5485b433e63443c592fa1faac9c0f78e"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/b3e629b1971e1542/manager.min.js"}}}}}], "source": ["evaluate_model(tuned_rf)"]}, {"cell_type": "markdown", "metadata": {"id": "RX5pYUJJEQsV"}, "source": ["# テスト／ホールドアウトサンプルの予測"]}, {"cell_type": "markdown", "metadata": {"id": "mFSvRYiaEQsd"}, "source": ["pycaret の `predict_model` 関数は、与えられた学習済みモデルに対するラベルを生成します。 `data` が None の場合、テスト/ホールドアウトのデータに対して `prediction_label` と `prediction_score` を生成し、性能指標も表示します。"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 363}, "id": "nwaZk6oTEQsi", "outputId": "d6c52f7b-1934-4bd8-971b-5a69a489577e"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<pandas.io.formats.style.Styler at 0x7f3627bbc6a0>"], "text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_6a3c3_\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Model</th>\n", "      <th class=\"col_heading level0 col1\" >Accuracy</th>\n", "      <th class=\"col_heading level0 col2\" >AUC</th>\n", "      <th class=\"col_heading level0 col3\" >Recall</th>\n", "      <th class=\"col_heading level0 col4\" >Prec.</th>\n", "      <th class=\"col_heading level0 col5\" >F1</th>\n", "      <th class=\"col_heading level0 col6\" >Kappa</th>\n", "      <th class=\"col_heading level0 col7\" >MCC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_6a3c3_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_6a3c3_row0_col0\" class=\"data row0 col0\" >Random Forest Classifier</td>\n", "      <td id=\"T_6a3c3_row0_col1\" class=\"data row0 col1\" >0.7509</td>\n", "      <td id=\"T_6a3c3_row0_col2\" class=\"data row0 col2\" >0.7483</td>\n", "      <td id=\"T_6a3c3_row0_col3\" class=\"data row0 col3\" >0.5796</td>\n", "      <td id=\"T_6a3c3_row0_col4\" class=\"data row0 col4\" >0.4509</td>\n", "      <td id=\"T_6a3c3_row0_col5\" class=\"data row0 col5\" >0.5072</td>\n", "      <td id=\"T_6a3c3_row0_col6\" class=\"data row0 col6\" >0.3440</td>\n", "      <td id=\"T_6a3c3_row0_col7\" class=\"data row0 col7\" >0.3489</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["       LIMIT_BAL  SEX  EDUCATION  MARRIAGE   AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "15959    30000.0  1.0        1.0       1.0  38.0    8.0    7.0    6.0    5.0   \n", "15960   110000.0  1.0        3.0       1.0  43.0   -1.0   -1.0   -1.0   -1.0   \n", "15961   290000.0  2.0        1.0       2.0  27.0    2.0    0.0    0.0    0.0   \n", "15962   110000.0  2.0        3.0       0.0  31.0    0.0    0.0    0.0    0.0   \n", "15963   360000.0  1.0        3.0       2.0  34.0    0.0    0.0    0.0    0.0   \n", "\n", "       PAY_5  ...  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  \\\n", "15959    4.0  ...    31085.0       0.0       0.0       0.0       0.0   \n", "15960    0.0  ...    31073.0     390.0    5050.0   63032.0    1100.0   \n", "15961    2.0  ...   278260.0   16500.0   15000.0   20000.0   10000.0   \n", "15962    0.0  ...    63208.0    4000.0    5000.0    3000.0    3000.0   \n", "15963    0.0  ...   235916.0   15000.0    9221.0    9225.0    8112.0   \n", "\n", "       PAY_AMT5  PAY_AMT6  default  prediction_label  prediction_score  \n", "15959       0.0       0.0        1                 1            0.8522  \n", "15960    1100.0    1000.0        0                 0            0.6116  \n", "15961    9701.0       0.0        0                 1            0.7464  \n", "15962    3000.0    8954.0        0                 0            0.7321  \n", "15963    8369.0    9000.0        0                 0            0.6824  \n", "\n", "[5 rows x 26 columns]"], "text/html": ["\n", "  <div id=\"df-9b86a885-695d-418e-9a5d-6c92399e7f5c\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "      <th>prediction_label</th>\n", "      <th>prediction_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>15959</th>\n", "      <td>30000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>38.0</td>\n", "      <td>8.0</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "      <td>4.0</td>\n", "      <td>...</td>\n", "      <td>31085.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.8522</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15960</th>\n", "      <td>110000.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>43.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>31073.0</td>\n", "      <td>390.0</td>\n", "      <td>5050.0</td>\n", "      <td>63032.0</td>\n", "      <td>1100.0</td>\n", "      <td>1100.0</td>\n", "      <td>1000.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.6116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15961</th>\n", "      <td>290000.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>27.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>278260.0</td>\n", "      <td>16500.0</td>\n", "      <td>15000.0</td>\n", "      <td>20000.0</td>\n", "      <td>10000.0</td>\n", "      <td>9701.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0.7464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15962</th>\n", "      <td>110000.0</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>31.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>63208.0</td>\n", "      <td>4000.0</td>\n", "      <td>5000.0</td>\n", "      <td>3000.0</td>\n", "      <td>3000.0</td>\n", "      <td>3000.0</td>\n", "      <td>8954.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.7321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15963</th>\n", "      <td>360000.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>34.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>235916.0</td>\n", "      <td>15000.0</td>\n", "      <td>9221.0</td>\n", "      <td>9225.0</td>\n", "      <td>8112.0</td>\n", "      <td>8369.0</td>\n", "      <td>9000.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.6824</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9b86a885-695d-418e-9a5d-6c92399e7f5c')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-9b86a885-695d-418e-9a5d-6c92399e7f5c button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-9b86a885-695d-418e-9a5d-6c92399e7f5c');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 25}], "source": ["pred_holdout = predict_model(tuned_rf)\n", "pred_holdout.head()"]}, {"cell_type": "markdown", "metadata": {"id": "r79BGjIfEQs1"}, "source": ["# モデルの決定"]}, {"cell_type": "markdown", "metadata": {"id": "B-6xJ9kQEQs7"}, "source": ["`finalize_model` 関数は、テスト/ホールドアウトサンプル (このチュートリアルでは 30%) を含む完全なデータセットに対してパイプライン全体をフィットさせます。この関数の目的は、本番環境に導入する前に、完全なデータセットでモデルを学習させることです。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "_--tO4KGEQs-"}, "outputs": [], "source": ["final_rf = finalize_model(tuned_rf)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U9W6kXsSEQtQ", "outputId": "4571aee3-fc72-47fd-f67b-61182f4befdd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Pipeline(memory=Memory(location=/tmp/joblib),\n", "         steps=[('numerical_imputer',\n", "                 TransformerWrapper(exclude=None,\n", "                                    include=['LIMIT_BAL', 'SEX', 'EDUCATION',\n", "                                             'MARRIAGE', 'AGE', 'PAY_1',\n", "                                             'PAY_2', 'PAY_3', 'PAY_4', 'PAY_5',\n", "                                             'PAY_6', 'BILL_AMT1', 'BILL_AMT2',\n", "                                             'BILL_AMT3', 'BILL_AMT4',\n", "                                             'BILL_AMT5', 'BILL_AMT6',\n", "                                             'PAY_AMT1', 'PAY_AMT2', 'PAY_AMT3',\n", "                                             'PAY_AMT4', 'PAY_AMT5',\n", "                                             'PAY_AMT6'],\n", "                                    tr...\n", "                 RandomForestClassifier(bootstrap=False, ccp_alpha=0.0,\n", "                                        class_weight='balanced_subsample',\n", "                                        criterion='gini', max_depth=6,\n", "                                        max_features='log2',\n", "                                        max_leaf_nodes=None, max_samples=None,\n", "                                        min_impurity_decrease=0.001,\n", "                                        min_samples_leaf=6, min_samples_split=9,\n", "                                        min_weight_fraction_leaf=0.0,\n", "                                        n_estimators=190, n_jobs=-1,\n", "                                        oob_score=False, random_state=123,\n", "                                        verbose=0, warm_start=False))],\n", "         verbose=False)\n"]}], "source": ["print(final_rf)"]}, {"cell_type": "markdown", "metadata": {"id": "hUzc6tXNEQtr"}, "source": ["# 未知データの予測"]}, {"cell_type": "markdown", "metadata": {"id": "dx5vXjChEQtt"}, "source": ["`predict_model`関数もまた、未知のデータセットに対する予測で使用されます。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 300}, "id": "gBSAOEbqb6xI", "outputId": "30e8c2f2-8348-4c71-a812-2f0585cf0c7a"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0     100000    2          2         2   23      0     -1     -1      0   \n", "1     380000    1          2         2   32     -1     -1     -1     -1   \n", "2     200000    2          2         1   32     -1     -1     -1     -1   \n", "3     200000    1          1         1   53      2      2      2      2   \n", "4     240000    1          1         2   41      1     -1     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0      0  ...      221.0     -159.0      567.0     380.0     601.0       0.0   \n", "1     -1  ...    32018.0    11849.0    11873.0   21540.0   15138.0   24677.0   \n", "2      2  ...     5247.0     3848.0     3151.0    5818.0      15.0    9102.0   \n", "3      2  ...   144098.0   147124.0   149531.0    6300.0    5500.0    5500.0   \n", "4      0  ...     3164.0      360.0     1737.0    2622.0    3301.0       0.0   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  default  \n", "0     581.0    1687.0    1542.0        0  \n", "1   11851.0   11875.0    8251.0        0  \n", "2      17.0    3165.0    1395.0        0  \n", "3    5500.0    5000.0    5000.0        1  \n", "4     360.0    1737.0     924.0        0  \n", "\n", "[5 rows x 24 columns]"], "text/html": ["\n", "  <div id=\"df-a42a7a0a-daf5-4729-a206-1579324775cb\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>221.0</td>\n", "      <td>-159.0</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>32018.0</td>\n", "      <td>11849.0</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>32</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>5247.0</td>\n", "      <td>3848.0</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>53</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>144098.0</td>\n", "      <td>147124.0</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>3164.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a42a7a0a-daf5-4729-a206-1579324775cb')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-a42a7a0a-daf5-4729-a206-1579324775cb button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-a42a7a0a-daf5-4729-a206-1579324775cb');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 28}], "source": ["# 5% sample witheld in the beginning\n", "data_unseen.head()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"id": "K4tgMsBNb6xI"}, "outputs": [], "source": ["# drop the default column from data_unseen\n", "data_unseen.drop('default', axis = 1, inplace = True)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 300}, "id": "0y5KWLC6EQtx", "outputId": "c2201aa3-2955-406c-a083-507ee244d6b3"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE   AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0   100000.0  2.0        2.0       2.0  23.0    0.0   -1.0   -1.0    0.0   \n", "1   380000.0  1.0        2.0       2.0  32.0   -1.0   -1.0   -1.0   -1.0   \n", "2   200000.0  2.0        2.0       1.0  32.0   -1.0   -1.0   -1.0   -1.0   \n", "3   200000.0  1.0        1.0       1.0  53.0    2.0    2.0    2.0    2.0   \n", "4   240000.0  1.0        1.0       2.0  41.0    1.0   -1.0   -1.0    0.0   \n", "\n", "   PAY_5  ...  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  \\\n", "0    0.0  ...     -159.0      567.0     380.0     601.0       0.0     581.0   \n", "1   -1.0  ...    11849.0    11873.0   21540.0   15138.0   24677.0   11851.0   \n", "2    2.0  ...     3848.0     3151.0    5818.0      15.0    9102.0      17.0   \n", "3    2.0  ...   147124.0   149531.0    6300.0    5500.0    5500.0    5500.0   \n", "4    0.0  ...      360.0     1737.0    2622.0    3301.0       0.0     360.0   \n", "\n", "   PAY_AMT5  PAY_AMT6  prediction_label  prediction_score  \n", "0    1687.0    1542.0                 0            0.5623  \n", "1   11875.0    8251.0                 0            0.7345  \n", "2    3165.0    1395.0                 0            0.5606  \n", "3    5000.0    5000.0                 1            0.8407  \n", "4    1737.0     924.0                 1            0.5078  \n", "\n", "[5 rows x 25 columns]"], "text/html": ["\n", "  <div id=\"df-4bba63f5-b978-47f6-98a0-744664df40ba\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>prediction_label</th>\n", "      <th>prediction_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>23.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>-159.0</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0.5623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>32.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>...</td>\n", "      <td>11849.0</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0.7345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>32.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>3848.0</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0.5606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>53.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>147124.0</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>0.8407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>41.0</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>1</td>\n", "      <td>0.5078</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-4bba63f5-b978-47f6-98a0-744664df40ba')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-4bba63f5-b978-47f6-98a0-744664df40ba button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-4bba63f5-b978-47f6-98a0-744664df40ba');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 30}], "source": ["pred_unseen = predict_model(final_rf, data=data_unseen)\n", "pred_unseen.head()"]}, {"cell_type": "markdown", "metadata": {"id": "oPYmVpugEQt5"}, "source": ["入力データセットには `prediction_label` と `prediction_score` というカラムが追加されます。`prediction_label` はラベル予測で、 `prediction_score` は **予測されたクラス** の確率になります。"]}, {"cell_type": "markdown", "metadata": {"id": "L__po3sUEQt7"}, "source": ["# モデルの保存"]}, {"cell_type": "markdown", "metadata": {"id": "1sQPT7jrEQt-"}, "source": ["`save_model` 関数は、パイプライン (モデルを含む) を `pkl` ファイルとしてディスクに保存するために使用します。"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ln1YWIXTEQuA", "outputId": "51bb408b-6e7a-4cdc-ce0e-e3768c3aba12"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Transformation Pipeline and Model Successfully Saved\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["(Pipeline(memory=Memory(location=/tmp/joblib),\n", "          steps=[('numerical_imputer',\n", "                  TransformerWrapper(exclude=None,\n", "                                     include=['LIMIT_BAL', 'SEX', 'EDUCATION',\n", "                                              'MARRIAGE', 'AGE', 'PAY_1',\n", "                                              'PAY_2', 'PAY_3', 'PAY_4', 'PAY_5',\n", "                                              'PAY_6', 'BILL_AMT1', 'BILL_AMT2',\n", "                                              'BILL_AMT3', 'BILL_AMT4',\n", "                                              'BILL_AMT5', 'BILL_AMT6',\n", "                                              'PAY_AMT1', 'PAY_AMT2', 'PAY_AMT3',\n", "                                              'PAY_AMT4', 'PAY_AMT5',\n", "                                              'PAY_AMT6'],\n", "                                     tr...\n", "                  RandomForestClassifier(bootstrap=False, ccp_alpha=0.0,\n", "                                         class_weight='balanced_subsample',\n", "                                         criterion='gini', max_depth=6,\n", "                                         max_features='log2',\n", "                                         max_leaf_nodes=None, max_samples=None,\n", "                                         min_impurity_decrease=0.001,\n", "                                         min_samples_leaf=6, min_samples_split=9,\n", "                                         min_weight_fraction_leaf=0.0,\n", "                                         n_estimators=190, n_jobs=-1,\n", "                                         oob_score=False, random_state=123,\n", "                                         verbose=0, warm_start=False))],\n", "          verbose=False), 'rf_final_pipeline.pkl')"]}, "metadata": {}, "execution_count": 31}], "source": ["save_model(final_rf,'rf_final_pipeline')"]}, {"cell_type": "markdown", "metadata": {"id": "Z8OBesfkEQuU"}, "source": ["# モデルの読み込み"]}, {"cell_type": "markdown", "metadata": {"id": "V2K_WLaaEQuW"}, "source": ["`load_model` 関数は、パイプラインの `pkl` ファイルをディスクから読み込みます。"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Siw_2EIUEQub", "outputId": "f0208318-1285-473c-9b21-6f8960e9999a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Transformation Pipeline and Model Successfully Loaded\n"]}], "source": ["rf_pipeline_from_disk = load_model('rf_final_pipeline')"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 300}, "id": "HMPO1ka9EQut", "outputId": "e4c9132d-eb13-4892-fde3-23e4f44a08f5"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": []}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE   AGE  PAY_1  PAY_2  PAY_3  PAY_4  \\\n", "0   100000.0  2.0        2.0       2.0  23.0    0.0   -1.0   -1.0    0.0   \n", "1   380000.0  1.0        2.0       2.0  32.0   -1.0   -1.0   -1.0   -1.0   \n", "2   200000.0  2.0        2.0       1.0  32.0   -1.0   -1.0   -1.0   -1.0   \n", "3   200000.0  1.0        1.0       1.0  53.0    2.0    2.0    2.0    2.0   \n", "4   240000.0  1.0        1.0       2.0  41.0    1.0   -1.0   -1.0    0.0   \n", "\n", "   PAY_5  ...  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  PAY_AMT4  \\\n", "0    0.0  ...     -159.0      567.0     380.0     601.0       0.0     581.0   \n", "1   -1.0  ...    11849.0    11873.0   21540.0   15138.0   24677.0   11851.0   \n", "2    2.0  ...     3848.0     3151.0    5818.0      15.0    9102.0      17.0   \n", "3    2.0  ...   147124.0   149531.0    6300.0    5500.0    5500.0    5500.0   \n", "4    0.0  ...      360.0     1737.0    2622.0    3301.0       0.0     360.0   \n", "\n", "   PAY_AMT5  PAY_AMT6  prediction_label  prediction_score  \n", "0    1687.0    1542.0                 0            0.5623  \n", "1   11875.0    8251.0                 0            0.7345  \n", "2    3165.0    1395.0                 0            0.5606  \n", "3    5000.0    5000.0                 1            0.8407  \n", "4    1737.0     924.0                 1            0.5078  \n", "\n", "[5 rows x 25 columns]"], "text/html": ["\n", "  <div id=\"df-ecb4731f-69c2-4b7c-89e5-b62efc98635e\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_1</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>prediction_label</th>\n", "      <th>prediction_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100000.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>23.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>-159.0</td>\n", "      <td>567.0</td>\n", "      <td>380.0</td>\n", "      <td>601.0</td>\n", "      <td>0.0</td>\n", "      <td>581.0</td>\n", "      <td>1687.0</td>\n", "      <td>1542.0</td>\n", "      <td>0</td>\n", "      <td>0.5623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>380000.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>32.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>...</td>\n", "      <td>11849.0</td>\n", "      <td>11873.0</td>\n", "      <td>21540.0</td>\n", "      <td>15138.0</td>\n", "      <td>24677.0</td>\n", "      <td>11851.0</td>\n", "      <td>11875.0</td>\n", "      <td>8251.0</td>\n", "      <td>0</td>\n", "      <td>0.7345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>200000.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>32.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>3848.0</td>\n", "      <td>3151.0</td>\n", "      <td>5818.0</td>\n", "      <td>15.0</td>\n", "      <td>9102.0</td>\n", "      <td>17.0</td>\n", "      <td>3165.0</td>\n", "      <td>1395.0</td>\n", "      <td>0</td>\n", "      <td>0.5606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>200000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>53.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>147124.0</td>\n", "      <td>149531.0</td>\n", "      <td>6300.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5500.0</td>\n", "      <td>5000.0</td>\n", "      <td>5000.0</td>\n", "      <td>1</td>\n", "      <td>0.8407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>240000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>41.0</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>2622.0</td>\n", "      <td>3301.0</td>\n", "      <td>0.0</td>\n", "      <td>360.0</td>\n", "      <td>1737.0</td>\n", "      <td>924.0</td>\n", "      <td>1</td>\n", "      <td>0.5078</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ecb4731f-69c2-4b7c-89e5-b62efc98635e')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-ecb4731f-69c2-4b7c-89e5-b62efc98635e button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-ecb4731f-69c2-4b7c-89e5-b62efc98635e');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 33}], "source": ["new_prediction = predict_model(rf_pipeline_from_disk, data=data_unseen)\n", "new_prediction.head()"]}, {"cell_type": "markdown", "metadata": {"id": "bf8I1uqcEQvD"}, "source": ["`unseen_predictions` と `new_prediction` の結果が同じであることに注意してください。"]}, {"cell_type": "markdown", "metadata": {"id": "_HeOs8BhEQvF"}, "source": ["# Wrap-up / Next Steps?"]}, {"cell_type": "markdown", "metadata": {"id": "VqG1NnwXEQvK"}, "source": ["このチュートリアルでは、データの読み込みから、前処理、モデルの学習、ハイパーパラメータの調整、予測、モデルの保存まで、機械学習パイプライン全体を扱いました。これらのステップは10個以下のコマンドで完了します。これらのコマンドは `create_model`, `tune_model`, `compare_models` のように自然な形で構成されており、非常に直感的で、覚えやすいよう仕様になっています。PyCaretを用いずに同様な実験を再構成すると、100行を簡単に超えるコード量が必要になるはずです。\n", "\n", "ここでは、`pycaret.classification`の基本的な部分のみを取り上げました。次のチュートリアルでは、高度な前処理、アンサンブル、一般化スタッキング、そして機械学習パイプラインを完全にカスタマイズすることができる、データサイエンティストなら必ず知っておくべきその他のテクニックに深く踏み込んでいきます。\n", "\n", "See you at the next tutorial. Follow the link to [Binary Classification Tutorial (CLF102) - Intermediate Level](https://github.com/pycaret/pycaret/blob/master/tutorials/Binary%20Classification%20Tutorial%20Level%20Intermediate%20-%20CLF102.ipynb)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "pycaretrc5", "language": "python", "name": "pycaretrc5"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.15"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"26f832d18f2c459481770897ca456589": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ba5c8e2aa4c644a5afe8c02c0a5db311", "IPY_MODEL_c57621c5903647cab6bb30303f6fb2f8", "IPY_MODEL_a7f41fb0dece4cb8a18879df89885402"], "layout": "IPY_MODEL_301336eb48d547b2b8ef15846f7d7a80"}}, "ba5c8e2aa4c644a5afe8c02c0a5db311": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c13eecb39b7f4e1687378c0730f1042f", "placeholder": "​", "style": "IPY_MODEL_1d20a4e95295477c835c649b9f9a6fba", "value": "Processing: 100%"}}, "c57621c5903647cab6bb30303f6fb2f8": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eb5f70b3dc844b9882f13510991772c0", "max": 61, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9bebf8a26c0e4ae0a41627f41228c305", "value": 61}}, "a7f41fb0dece4cb8a18879df89885402": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b2b6cdfb923b49198c6fb0599a361ec8", "placeholder": "​", "style": "IPY_MODEL_89791e54334b4bf6a82b21637f3b64ff", "value": " 61/61 [02:56&lt;00:00,  1.84s/it]"}}, "301336eb48d547b2b8ef15846f7d7a80": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "c13eecb39b7f4e1687378c0730f1042f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1d20a4e95295477c835c649b9f9a6fba": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "eb5f70b3dc844b9882f13510991772c0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9bebf8a26c0e4ae0a41627f41228c305": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b2b6cdfb923b49198c6fb0599a361ec8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89791e54334b4bf6a82b21637f3b64ff": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a43063cad0854f7f868c501b8ac11c74": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2759fb13f6a94cc2990c64b27756da3d", "IPY_MODEL_21746269ecc44d9b9ff8eb434a1e2472", "IPY_MODEL_f3574d8d8b9a489eb5802336649aed9a"], "layout": "IPY_MODEL_0161f2cd69384040b3397bbb9184d57d"}}, "2759fb13f6a94cc2990c64b27756da3d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_67c7128f486a451781f64d6f1e43566d", "placeholder": "​", "style": "IPY_MODEL_7270ddefb9d142c88ee072a1fc259269", "value": "Processing: 100%"}}, "21746269ecc44d9b9ff8eb434a1e2472": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c25e36340b214bc2815378284ac8b025", "max": 4, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_63dda3a99946489c9458886b8c65d6d9", "value": 4}}, "f3574d8d8b9a489eb5802336649aed9a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ab857b1b63b4f11a31b98b89b88b5d2", "placeholder": "​", "style": "IPY_MODEL_d3e419f035c9453b84102a8d6d130fc6", "value": " 4/4 [00:03&lt;00:00,  1.15it/s]"}}, "0161f2cd69384040b3397bbb9184d57d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "67c7128f486a451781f64d6f1e43566d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7270ddefb9d142c88ee072a1fc259269": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c25e36340b214bc2815378284ac8b025": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63dda3a99946489c9458886b8c65d6d9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8ab857b1b63b4f11a31b98b89b88b5d2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d3e419f035c9453b84102a8d6d130fc6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8fe80ee681da418a8aae8e4d63590440": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_43f1961604bc4c3fa8bb4bf0b42b6b1b", "IPY_MODEL_f5fd48c57a5b48b88522124944d7947c", "IPY_MODEL_45a015029bca4c48b4527a45ef26d1a0"], "layout": "IPY_MODEL_e7b576e3f96040e6bdb254891f480a27"}}, "43f1961604bc4c3fa8bb4bf0b42b6b1b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ebfa10a4cbd94e27a6eafbf549de63ca", "placeholder": "​", "style": "IPY_MODEL_2fa4f1a9341647e4b8b79d2fefc41129", "value": "Processing:  75%"}}, "f5fd48c57a5b48b88522124944d7947c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0715c20dd73d4ff5a02f3c01a05614c7", "max": 4, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d32be97eebdd42cabc36229c9f13a4b7", "value": 4}}, "45a015029bca4c48b4527a45ef26d1a0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_90a63886bca44b35bf85e23d3f5d137e", "placeholder": "​", "style": "IPY_MODEL_ee9c6b0d77b64348b7192a768707a21a", "value": " 3/4 [00:14&lt;00:04,  4.96s/it]"}}, "e7b576e3f96040e6bdb254891f480a27": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "ebfa10a4cbd94e27a6eafbf549de63ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2fa4f1a9341647e4b8b79d2fefc41129": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0715c20dd73d4ff5a02f3c01a05614c7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d32be97eebdd42cabc36229c9f13a4b7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "90a63886bca44b35bf85e23d3f5d137e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee9c6b0d77b64348b7192a768707a21a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9cf908bb9238434e8da330ccc13db02e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e8f469ab2da94d5ebf824a86d865f795", "IPY_MODEL_f48dd44cfac74ef69f4be3039a4cacd3", "IPY_MODEL_9736a846649f4581b895de1baf8beece"], "layout": "IPY_MODEL_a83fa8e4c0f549ec8e4abd8a57946905"}}, "e8f469ab2da94d5ebf824a86d865f795": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4f4578dffa88454683738e7003a7c1a4", "placeholder": "​", "style": "IPY_MODEL_0bd7d74bcc104abe8311e739735ea625", "value": "Processing: 100%"}}, "f48dd44cfac74ef69f4be3039a4cacd3": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0dbff54a34f64872a6f825e3d8e9b6ec", "max": 4, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2bcf10850e8142b2979c7e466a2e1c7d", "value": 4}}, "9736a846649f4581b895de1baf8beece": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8720dd231a67484c813350234850c39a", "placeholder": "​", "style": "IPY_MODEL_80986d24fe4048ae8348298d865500eb", "value": " 4/4 [00:38&lt;00:00,  8.93s/it]"}}, "a83fa8e4c0f549ec8e4abd8a57946905": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "4f4578dffa88454683738e7003a7c1a4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bd7d74bcc104abe8311e739735ea625": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0dbff54a34f64872a6f825e3d8e9b6ec": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2bcf10850e8142b2979c7e466a2e1c7d": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8720dd231a67484c813350234850c39a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80986d24fe4048ae8348298d865500eb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5137a556f8c74253bdeedfe201deb6b9": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_be899c649ae043b7a2720bc18aff5bbc", "IPY_MODEL_b1114f6ea38f4455834dc57c93b13c94", "IPY_MODEL_4366f00d02864da984daef7d3f3c7602"], "layout": "IPY_MODEL_09d0c50f131d4a39b1c7958473ec9967"}}, "be899c649ae043b7a2720bc18aff5bbc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d328a3d2942743eca0a73b5de5904760", "placeholder": "​", "style": "IPY_MODEL_54835add990f4b4e8d460c36ec388087", "value": "Processing:  86%"}}, "b1114f6ea38f4455834dc57c93b13c94": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce58945cf8a04ad6953a9bffaa1e684a", "max": 7, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fff2b88c772a4987a5f592e24be76c9b", "value": 7}}, "4366f00d02864da984daef7d3f3c7602": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6719ef18056e4bf298477a17e4341619", "placeholder": "​", "style": "IPY_MODEL_2298a5809b3948988271aa55f759984a", "value": " 6/7 [00:06&lt;00:00,  1.11it/s]"}}, "09d0c50f131d4a39b1c7958473ec9967": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "d328a3d2942743eca0a73b5de5904760": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "54835add990f4b4e8d460c36ec388087": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ce58945cf8a04ad6953a9bffaa1e684a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fff2b88c772a4987a5f592e24be76c9b": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6719ef18056e4bf298477a17e4341619": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2298a5809b3948988271aa55f759984a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "06aca29a7eb1482080ba41243ca8c490": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_97cd62dcfd6a45e791c464caa811a0bd", "IPY_MODEL_8d673899f3fc443191a1eb3afa2771dd", "IPY_MODEL_c4f6420e4417402bae0e1a193a916e28"], "layout": "IPY_MODEL_abb50535bcb24b89bd8c6944d0898cd8"}}, "97cd62dcfd6a45e791c464caa811a0bd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_98ef97d8741742f582f47a1c8c2313b7", "placeholder": "​", "style": "IPY_MODEL_d93426816fb64a94bd2ce89b1aac63f8", "value": "Processing:  86%"}}, "8d673899f3fc443191a1eb3afa2771dd": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8b21cb19cc2f4f85ae24771d944853f3", "max": 7, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a3918f9396874183aa8484895c2ac57b", "value": 7}}, "c4f6420e4417402bae0e1a193a916e28": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ad890f68783c4dafab99711ba50aef6b", "placeholder": "​", "style": "IPY_MODEL_5525ec133ceb4a309d032191fa8df30f", "value": " 6/7 [01:21&lt;00:11, 11.94s/it]"}}, "abb50535bcb24b89bd8c6944d0898cd8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "98ef97d8741742f582f47a1c8c2313b7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d93426816fb64a94bd2ce89b1aac63f8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8b21cb19cc2f4f85ae24771d944853f3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3918f9396874183aa8484895c2ac57b": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ad890f68783c4dafab99711ba50aef6b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5525ec133ceb4a309d032191fa8df30f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1a002b747073467c8cc47622d8499128": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ae213f04bc7145319e395277db148532", "IPY_MODEL_f552ab5f1c884070be9f767b3f893715", "IPY_MODEL_218cc7c9c964473d8fd9c6d6b098e83a"], "layout": "IPY_MODEL_7bee3988fbb64728b11ec0c601b0dfcf"}}, "ae213f04bc7145319e395277db148532": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d16205e40f624ca2911850b308707bbb", "placeholder": "​", "style": "IPY_MODEL_b1d7231ec4ab4acaa9a15fcaf69cdfe0", "value": "Processing: 100%"}}, "f552ab5f1c884070be9f767b3f893715": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_435ad7433a274fff8de9957589698826", "max": 7, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bf18e18ea07b4d7b9ab8750ac5aed32a", "value": 7}}, "218cc7c9c964473d8fd9c6d6b098e83a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8e65629da34a486696d49053bd20ec9f", "placeholder": "​", "style": "IPY_MODEL_50c7486147d34c9e8a728b2251392985", "value": " 7/7 [08:18&lt;00:00, 55.27s/it]"}}, "7bee3988fbb64728b11ec0c601b0dfcf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "d16205e40f624ca2911850b308707bbb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1d7231ec4ab4acaa9a15fcaf69cdfe0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "435ad7433a274fff8de9957589698826": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf18e18ea07b4d7b9ab8750ac5aed32a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8e65629da34a486696d49053bd20ec9f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "50c7486147d34c9e8a728b2251392985": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5485b433e63443c592fa1faac9c0f78e": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": ["widget-interact"], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_9ab139bc1f9a4f78942e742cfe6bf1d0", "IPY_MODEL_a6dd9c3fd9c1479b8eceae52e638041a"], "layout": "IPY_MODEL_8a77291c3fda449f94e2fe3850cbde10"}}, "9ab139bc1f9a4f78942e742cfe6bf1d0": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsModel", "_options_labels": ["Pipeline Plot", "Hyperparameters", "AUC", "Confusion Matrix", "<PERSON><PERSON><PERSON><PERSON>", "Precision Recall", "Prediction Error", "Class Report", "Feature Selection", "Learning Curve", "Manifold Learning", "Calibration Curve", "Validation Curve", "Dimensions", "Feature Importance", "Feature Importance (All)", "Decision Boundary", "Lift Chart", "Gain Chart", "Decision Tree", "KS Statistic Plot"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonsView", "button_style": "", "description": "Plot Type:", "description_tooltip": null, "disabled": false, "icons": [""], "index": 0, "layout": "IPY_MODEL_d4373f1fcd1a426cb3689f416c2c36a5", "style": "IPY_MODEL_2ce35ab7b1a540c7bb4d38c4f6c688f8", "tooltips": []}}, "a6dd9c3fd9c1479b8eceae52e638041a": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_4ad9962bc6624f048973be7a60e530d6", "msg_id": "", "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 1200x600 with 1 Axes>", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}}, "8a77291c3fda449f94e2fe3850cbde10": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d4373f1fcd1a426cb3689f416c2c36a5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ce35ab7b1a540c7bb4d38c4f6c688f8": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_width": "", "description_width": "", "font_weight": ""}}, "4ad9962bc6624f048973be7a60e530d6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}