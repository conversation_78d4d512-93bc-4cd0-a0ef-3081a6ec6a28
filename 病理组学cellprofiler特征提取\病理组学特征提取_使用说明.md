# 病理组学特征提取工具使用说明

## 概述
这是一个基于CellProfiler核心算法的病理组学特征提取工具，能够一键批量提取病理图像的多种特征，包括形态学、纹理、强度等特征。

## 功能特点
- ✅ **批量处理**: 支持文件夹内所有图像的批量处理
- ✅ **多种特征**: 提取形态学、纹理、强度等多维度特征
- ✅ **自动分割**: 基于CellProfiler算法的细胞核自动分割
- ✅ **可视化报告**: 自动生成特征分布图、相关性分析等
- ✅ **Excel输出**: 结果保存为Excel格式，便于后续分析
- ✅ **多格式支持**: 支持jpg、png、tif、bmp等常见图像格式

## 安装依赖

```bash
# 安装必要的Python包
pip install opencv-python scikit-image pandas numpy matplotlib seaborn
pip install mahotas pillow openpyxl scikit-learn
```

## 使用方法

### 方法1: 直接运行
```bash
python 病理组学特征提取_CellProfiler_一键运行.py
```

### 方法2: 在代码中使用
```python
from 病理组学特征提取_CellProfiler_一键运行 import PathomicsFeatureExtractor

# 创建特征提取器
extractor = PathomicsFeatureExtractor(
    input_folder="./病理图像",  # 输入图像文件夹
    output_folder="./结果"     # 输出结果文件夹（可选）
)

# 执行批量处理
extractor.batch_process()
```

## 文件夹结构

### 输入文件夹结构
```
病理图像/
├── 患者1.jpg
├── 患者2.png
├── 患者3.tif
└── ...
```

### 输出结果结构
```
pathomics_results/
├── pathomics_features.xlsx          # 完整特征表
└── visualizations/                  # 可视化图表
    ├── Morphological_features_distribution.png
    ├── Texture_features_distribution.png
    ├── Intensity_features_distribution.png
    ├── correlation_matrix.png
    ├── pca_variance_explained.png
    └── feature_importance.png
```

## 提取的特征类型

### 1. 形态学特征 (Morphological Features)
基于CellProfiler的MeasureObjectSizeShape模块：
- **面积特征**: Area_Mean, Area_Std, Area_Min, Area_Max, Area_Median
- **周长特征**: Perimeter_Mean, Perimeter_Std
- **形状特征**: Eccentricity_Mean, Eccentricity_Std, Solidity_Mean, Solidity_Std
- **圆形度**: FormFactor_Mean, FormFactor_Std
- **轴长**: MajorAxisLength_Mean, MinorAxisLength_Mean
- **细胞核**: Nuclei_Count, Nuclei_Density

### 2. 纹理特征 (Texture Features)
基于CellProfiler的MeasureTexture模块：
- **Haralick特征**: Contrast, Dissimilarity, Homogeneity, Energy, Correlation
- **LBP特征**: Local Binary Pattern直方图特征
- **多尺度**: 支持不同距离的纹理分析

### 3. 强度特征 (Intensity Features)
基于CellProfiler的MeasureObjectIntensity模块：
- **区域强度**: Intensity_Mean, Intensity_Std, Intensity_Min, Intensity_Max, Intensity_Median
- **全局强度**: Global_Intensity_Mean, Global_Intensity_Std等

## 核心算法

### 1. 图像预处理
- 灰度转换
- 高斯滤波去噪
- 强度归一化

### 2. 细胞核分割
- Otsu阈值分割
- 形态学操作
- 距离变换
- 分水岭算法

### 3. 特征计算
- 区域属性分析
- 灰度共生矩阵
- 局部二值模式
- 统计特征计算

## 输出文件说明

### Excel文件包含以下工作表：
1. **All_Features**: 所有特征的完整表格
2. **Morphological_Features**: 形态学特征
3. **Texture_Features**: 纹理特征
4. **Intensity_Features**: 强度特征
5. **Statistical_Summary**: 统计摘要

### 可视化图表：
1. **特征分布图**: 各类特征的分布直方图
2. **相关性热图**: 特征间相关性分析
3. **PCA分析**: 主成分分析结果
4. **特征重要性**: 基于方差的特征重要性排序

## 注意事项

1. **图像质量**: 确保输入图像质量良好，避免过度模糊或过暗的图像
2. **文件格式**: 支持jpg、jpeg、png、tif、tiff、bmp格式
3. **内存使用**: 大量图像处理可能占用较多内存，建议分批处理
4. **处理时间**: 处理时间取决于图像数量和大小，请耐心等待

## 常见问题

### Q: 如何处理大量图像？
A: 可以将图像分批放入不同文件夹，分别处理，最后合并结果。

### Q: 特征提取失败怎么办？
A: 检查图像是否损坏，确保图像格式正确，查看控制台错误信息。

### Q: 如何自定义特征？
A: 可以修改代码中的特征提取函数，添加自定义的特征计算方法。

### Q: 结果如何用于机器学习？
A: Excel文件可以直接导入到Python、R或其他机器学习工具中进行后续分析。

## 技术支持

如有问题，请检查：
1. Python环境和依赖包是否正确安装
2. 输入路径是否存在且包含图像文件
3. 是否有足够的磁盘空间保存结果
4. 图像文件是否可以正常读取

## 更新日志

- v1.0: 初始版本，支持基础病理组学特征提取
- 基于CellProfiler 4.2.8的核心算法实现
- 支持批量处理和自动可视化报告生成
