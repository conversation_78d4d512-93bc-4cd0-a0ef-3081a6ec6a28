#!/usr/bin/env python3
"""
简化版MRI扫描仪信息提取演示
Simplified MRI Scanner Information Extraction Demo
(不依赖nibabel等特殊库)
"""

import os
import json
import re
from pathlib import Path

def demo_scanner_detection_methods():
    """演示从NII.gz文件获取MRI机器信息的各种方法"""
    
    print("🔬 从NII.gz文件获取MRI扫描仪信息的方法")
    print("="*60)
    
    print("""
📋 回答您的问题：从NII.gz格式的MRI图像能得到MRI的机器类型吗？

✅ 答案：可能可以，取决于以下几个因素：

1️⃣ 信息来源的可能性：
""")
    
    # 方法1：JSON元数据文件
    print("🥇 方法1：BIDS格式JSON元数据文件（最可靠）")
    print("-" * 40)
    
    sample_json = {
        "Manufacturer": "Siemens",
        "ManufacturerModelName": "Skyra",
        "MagneticFieldStrength": 3.0,
        "SoftwareVersions": "syngo MR E11",
        "DeviceSerialNumber": "167006",
        "StationName": "AWP167006",
        "InstitutionName": "Hospital Name",
        "SequenceName": "ep2d_diff",
        "ScanningSequence": "EP",
        "RepetitionTime": 8400,
        "EchoTime": 94
    }
    
    print("示例JSON文件内容：")
    print(json.dumps(sample_json, indent=2, ensure_ascii=False))
    print("📊 可靠性：★★★★★ (最高)")
    print("💡 说明：如果NII文件是从BIDS格式数据集转换的，通常会有对应的JSON文件")
    
    # 方法2：NII文件头扩展信息
    print(f"\n🥈 方法2：NII文件头扩展信息（中等可靠）")
    print("-" * 40)
    print("""
NII文件头可能包含扩展信息，特别是从DICOM转换时：
- 厂商信息 (Manufacturer)
- 设备型号 (Model)
- 磁场强度 (Field Strength)
- 软件版本 (Software Version)
- 扫描参数 (Scan Parameters)

📊 可靠性：★★★☆☆ (中等)
💡 说明：取决于转换工具是否保留了原始DICOM信息
""")
    
    # 方法3：文件名分析
    print(f"\n🥉 方法3：文件名模式识别（低可靠性）")
    print("-" * 40)
    
    filename_examples = [
        "Patient001_Siemens_Skyra_3T_T1_MPRAGE.nii.gz",
        "sub-01_ses-01_GE_Signa_1.5T_T2_FLAIR.nii.gz",
        "HCC_Philips_Achieva_3.0T_DWI_b1000.nii.gz",
        "study_toshiba_vantage_titan_T1W.nii.gz"
    ]
    
    print("示例文件名：")
    for filename in filename_examples:
        print(f"  📄 {filename}")
        
        # 分析文件名
        analysis = analyze_filename_demo(filename)
        for key, value in analysis.items():
            print(f"     → {key}: {value}")
        print()
    
    print("📊 可靠性：★★☆☆☆ (较低)")
    print("💡 说明：依赖于文件命名规范，容易出错")
    
    # 方法4：目录结构分析
    print(f"\n🏗️ 方法4：目录结构分析（最低可靠性）")
    print("-" * 40)
    
    directory_examples = [
        "/data/Siemens_Skyra_3T/Patient001/T1_MPRAGE/image.nii.gz",
        "/study/GE_Scanner/1.5T_Data/sub-01/FLAIR.nii.gz",
        "/hospital/MRI_Philips_Achieva/3T_Studies/DWI_data.nii.gz"
    ]
    
    print("示例目录结构：")
    for path in directory_examples:
        print(f"  📁 {path}")
        analysis = analyze_directory_demo(path)
        for key, value in analysis.items():
            print(f"     → {key}: {value}")
        print()
    
    print("📊 可靠性：★☆☆☆☆ (最低)")
    print("💡 说明：完全依赖于数据组织方式")

def analyze_filename_demo(filename):
    """演示文件名分析"""
    filename_lower = filename.lower()
    analysis = {}
    
    # 厂商检测
    manufacturers = {
        'siemens': ['siemens', 'skyra', 'avanto', 'trio', 'prisma'],
        'ge': ['ge', 'signa', 'discovery', 'optima'],
        'philips': ['philips', 'achieva', 'ingenia', 'intera'],
        'toshiba': ['toshiba', 'canon', 'vantage', 'titan'],
        'hitachi': ['hitachi', 'echelon', 'oasis']
    }
    
    for manufacturer, keywords in manufacturers.items():
        for keyword in keywords:
            if keyword in filename_lower:
                analysis['厂商'] = manufacturer.upper()
                analysis['关键词'] = keyword
                break
        if '厂商' in analysis:
            break
    
    # 磁场强度检测
    field_patterns = ['1.5t', '3t', '3.0t', '7t', '0.5t']
    for pattern in field_patterns:
        if pattern in filename_lower:
            analysis['磁场强度'] = pattern.upper()
            break
    
    # 序列类型检测
    sequences = ['t1', 't2', 'flair', 'dwi', 'bold', 'mprage']
    found_sequences = [seq.upper() for seq in sequences if seq in filename_lower]
    if found_sequences:
        analysis['序列类型'] = ', '.join(found_sequences)
    
    return analysis

def analyze_directory_demo(path):
    """演示目录结构分析"""
    path_lower = path.lower()
    analysis = {}
    
    # 从路径中提取信息
    if 'siemens' in path_lower:
        analysis['目录厂商'] = 'SIEMENS'
    elif 'ge' in path_lower:
        analysis['目录厂商'] = 'GE'
    elif 'philips' in path_lower:
        analysis['目录厂商'] = 'PHILIPS'
    
    if '3t' in path_lower:
        analysis['目录磁场强度'] = '3T'
    elif '1.5t' in path_lower:
        analysis['目录磁场强度'] = '1.5T'
    
    return analysis

def practical_recommendations():
    """实用建议"""
    print(f"\n💡 实用建议和最佳实践")
    print("="*60)
    
    print("""
🎯 提高检测成功率的方法：

1. 📄 检查BIDS格式数据
   - 查找与NII文件同名的JSON文件
   - JSON文件包含完整的扫描参数和设备信息
   
2. 🔧 使用专业工具
   - dcm2niix: 转换时保留元数据
   - nibabel: Python库读取NII文件头
   - pydicom: 如果有原始DICOM文件
   
3. 📋 建立数据管理规范
   - 统一的文件命名规则
   - 包含设备信息的目录结构
   - 维护扫描参数记录表
   
4. 🔍 多方法结合验证
   - 同时使用多种检测方法
   - 交叉验证结果的一致性
   - 建立置信度评估机制

⚠️ 注意事项：

1. 数据来源影响：
   - 从DICOM直接转换：信息保留较好
   - 二次处理的数据：信息可能丢失
   - 匿名化处理：设备信息可能被移除

2. 转换工具差异：
   - 不同工具保留的信息量不同
   - 某些工具会丢弃扩展信息
   - 建议使用dcm2niix等专业工具

3. 文件格式限制：
   - NII格式本身不是为存储设备信息设计
   - 扩展信息存储有限制
   - 建议保留原始DICOM文件
""")

def code_examples():
    """代码示例"""
    print(f"\n💻 Python代码示例")
    print("="*60)
    
    print("""
# 1. 使用nibabel读取NII文件信息
import nibabel as nib

def extract_scanner_info(nii_file):
    img = nib.load(nii_file)
    header = img.header
    
    # 检查扩展信息
    if hasattr(header, 'extensions') and header.extensions:
        for ext in header.extensions:
            content = ext.get_content()
            # 解析扩展内容中的设备信息
            
    return scanner_info

# 2. 检查JSON元数据文件
import json
from pathlib import Path

def check_json_metadata(nii_file):
    json_file = Path(nii_file).with_suffix('.json')
    if json_file.exists():
        with open(json_file, 'r') as f:
            metadata = json.load(f)
        return metadata.get('Manufacturer'), metadata.get('MagneticFieldStrength')
    return None, None

# 3. 批量分析
def batch_analyze_scanner_info(directory):
    results = []
    for nii_file in Path(directory).glob('*.nii.gz'):
        info = extract_scanner_info(nii_file)
        results.append(info)
    return results
""")

if __name__ == "__main__":
    demo_scanner_detection_methods()
    practical_recommendations()
    code_examples()
    
    print(f"\n🎉 总结")
    print("="*60)
    print("""
✅ 从NII.gz文件获取MRI机器信息是可能的，但成功率取决于：

1. 数据来源和处理历史
2. 转换工具的选择
3. 是否有配套的元数据文件
4. 文件命名和组织规范

🏆 最佳方案：
- 优先查找BIDS格式的JSON元数据文件
- 使用专业工具（如我创建的检测器）进行多方法分析
- 建立数据管理规范以提高未来的可追溯性

📞 如需进一步帮助，可以：
- 提供具体的NII文件进行分析
- 讨论数据管理最佳实践
- 定制化开发检测工具
""")