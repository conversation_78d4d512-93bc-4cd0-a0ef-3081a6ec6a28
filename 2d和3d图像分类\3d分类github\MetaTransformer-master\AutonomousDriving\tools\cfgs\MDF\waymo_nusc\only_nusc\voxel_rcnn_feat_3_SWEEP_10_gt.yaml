CLASS_NAMES: ['car', 'pedestrian', 'bicycle']

DATA_CONFIG:
    _BASE_CONFIG_: cfgs/dataset_configs/nuscenes/MDF/da_nuscenes_dataset_gt_D_p.yaml
    CLASS_NAMES: ['car', 'pedestrian', 'bicycle']
    MAX_SWEEPS: 10
    SHIFT_COOR: [0.0, 0.0, 1.8]
    PRED_VELOCITY: False
    BALANCED_RESAMPLING: False

MODEL:
    NAME: VoxelRCNN

    VFE:
        NAME: DynMeanVFE

    BACKBONE_3D:
        NAME: VoxelBackBone8x

    MAP_TO_BEV:
        NAME: HeightCompression
        NUM_BEV_FEATURES: 256

    BACKBONE_2D:
        NAME: BaseBEVBackbone

        LAYER_NUMS: [5, 5]
        LAYER_STRIDES: [1, 2]
        NUM_FILTERS: [128, 256]
        UPSAMPLE_STRIDES: [1, 2]
        NUM_UPSAMPLE_FILTERS: [256, 256]

    DENSE_HEAD:
        NAME: CenterHead
        CLASS_AGNOSTIC: False
        
        CLASS_NAMES_EACH_HEAD: [
            ['car', 'pedestrian', 'bicycle']
        ]

        SHARED_CONV_CHANNEL: 64
        USE_BIAS_BEFORE_NORM: True
        NUM_HM_CONV: 2
        SEPARATE_HEAD_CFG:
            HEAD_ORDER: [ 'center', 'center_z', 'dim', 'rot' ]
            HEAD_DICT: {
                'center': { 'out_channels': 2, 'num_conv': 2 },
                'center_z': { 'out_channels': 1, 'num_conv': 2 },
                'dim': { 'out_channels': 3, 'num_conv': 2 },
                'rot': { 'out_channels': 2, 'num_conv': 2 },
            }

        TARGET_ASSIGNER_CONFIG:
            FEATURE_MAP_STRIDE: 8
            NUM_MAX_OBJS: 500
            GAUSSIAN_OVERLAP: 0.1
            MIN_RADIUS: 2

        LOSS_CONFIG:
            LOSS_WEIGHTS: {
                'cls_weight': 1.0,
                'loc_weight': 2.0,
                'code_weights': [ 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0 ]
            }

        POST_PROCESSING:
            SCORE_THRESH: 0.1
            POST_CENTER_LIMIT_RANGE: [ -75.2, -75.2, -2, 75.2, 75.2, 4 ] #[ -75.2, -75.2, -2, 75.2, 75.2, 4 ] [-51.2, -51.2, -5.0, 51.2, 51.2, 3.0] [-61.2, -61.2, -10.0, 61.2, 61.2, 10.0]
            MAX_OBJ_PER_SAMPLE: 500
            NMS_CONFIG:
                NMS_TYPE: nms_gpu
                NMS_THRESH: 0.2 #0.7  0.2
                NMS_PRE_MAXSIZE: 1000 #4096  1000
                NMS_POST_MAXSIZE: 83 #500  83

    ROI_HEAD:
        NAME: VoxelRCNNHead
        CLASS_AGNOSTIC: True

        SHARED_FC: [256, 256]
        CLS_FC: [256, 256]
        REG_FC: [256, 256]
        DP_RATIO: 0.3

        NMS_CONFIG:
            TRAIN:
                NMS_TYPE: nms_gpu
                MULTI_CLASSES_NMS: False
                NMS_PRE_MAXSIZE: 9000
                NMS_POST_MAXSIZE: 512
                NMS_THRESH: 0.8
            TEST:
                NMS_TYPE: nms_gpu
                MULTI_CLASSES_NMS: False
                NMS_PRE_MAXSIZE: 1024
                NMS_POST_MAXSIZE: 100
                NMS_THRESH: 0.7
                # NMS_PRE_MAXSIZE: 4096
                # NMS_POST_MAXSIZE: 300
                # NMS_THRESH: 0.85

        ROI_GRID_POOL:
            FEATURES_SOURCE: ['x_conv2', 'x_conv3', 'x_conv4']
            PRE_MLP: True
            GRID_SIZE: 6
            POOL_LAYERS:
                x_conv2:
                    MLPS: [ [ 64, 64 ] ]
                    QUERY_RANGES: [ [ 3, 3, 2 ] ]
                    POOL_RADIUS: [ 0.4 ]
                    NSAMPLE: [ 16 ]
                    POOL_METHOD: max_pool
                x_conv3:
                    MLPS: [ [ 64, 64 ] ]
                    QUERY_RANGES: [ [ 3, 3, 2 ] ]
                    POOL_RADIUS: [ 0.8 ]
                    NSAMPLE: [ 16 ]
                    POOL_METHOD: max_pool
                x_conv4:
                    MLPS: [ [ 64, 64 ] ]
                    QUERY_RANGES: [ [ 3, 3, 2 ] ]
                    POOL_RADIUS: [ 1.6 ]
                    NSAMPLE: [ 16 ]
                    POOL_METHOD: max_pool

        TARGET_CONFIG:
            BOX_CODER: ResidualCoder
            ROI_PER_IMAGE: 128
            FG_RATIO: 0.5

            SAMPLE_ROI_BY_EACH_CLASS: True
            CLS_SCORE_TYPE: roi_iou

            CLS_FG_THRESH: 0.75
            CLS_BG_THRESH: 0.25
            CLS_BG_THRESH_LO: 0.1
            HARD_BG_RATIO: 0.8

            REG_FG_THRESH: 0.55

        LOSS_CONFIG:
            CLS_LOSS: BinaryCrossEntropy
            REG_LOSS: smooth-l1
            CORNER_LOSS_REGULARIZATION: True
            LOSS_WEIGHTS: {
                'rcnn_cls_weight': 1.0,
                'rcnn_reg_weight': 1.0,
                'rcnn_corner_weight': 1.0,
                'code_weights': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }

    POST_PROCESSING:
        RECALL_THRESH_LIST: [0.3, 0.5, 0.7]
        SCORE_THRESH: 0.1
        OUTPUT_RAW_SCORE: False

        EVAL_METRIC: kitti

        NMS_CONFIG:
            MULTI_CLASSES_NMS: False
            NMS_TYPE: nms_gpu
            NMS_THRESH: 0.2 #0.1  0.2
            NMS_PRE_MAXSIZE: 1000 #4096  1000
            NMS_POST_MAXSIZE: 83 #500  83


OPTIMIZATION:
    BATCH_SIZE_PER_GPU: 8
    NUM_EPOCHS: 30

    OPTIMIZER: adam_onecycle
    LR: 0.01
    WEIGHT_DECAY: 0.001
    MOMENTUM: 0.9

    MOMS: [0.95, 0.85]
    PCT_START: 0.4
    DIV_FACTOR: 10
    DECAY_STEP_LIST: [35, 45]
    LR_DECAY: 0.1
    LR_CLIP: 0.0000001

    LR_WARMUP: False
    WARMUP_EPOCH: 1

    GRAD_NORM_CLIP: 10