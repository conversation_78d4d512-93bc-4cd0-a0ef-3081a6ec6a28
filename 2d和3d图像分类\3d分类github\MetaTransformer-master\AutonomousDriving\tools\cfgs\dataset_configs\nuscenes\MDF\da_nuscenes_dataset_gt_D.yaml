DATASET: 'NuScenesDataset'
DATA_PATH: '../data/nuscenes'
# OSS_PATH: 's3://${PATH_TO_DATASET}/nuScenes/original_raw_data/v1.0-trainval'

VERSION: 'v1.0-trainval'
SET_NAN_VELOCITY_TO_ZEROS: True
FILTER_MIN_POINTS_IN_GT: 1

DATA_SPLIT: {
    'train': train,
    'test': val
}

INFO_PATH: {
    'train': [nuscenes_infos_10sweeps_train.pkl],
    'test': [nuscenes_infos_10sweeps_val.pkl],
}

POINT_CLOUD_RANGE: [-75.2, -75.2, -2, 75.2, 75.2, 4]
MIN_POINTS_OF_GT: 1

DATA_AUGMENTOR:
    DISABLE_AUG_LIST: ['placeholder']
    AUG_CONFIG_LIST:
        - NAME: gt_sampling
          DB_INFO_PATH:
              - nuscenes_dbinfos_10sweeps_withvelo.pkl
          PREPARE: {
             filter_by_min_points: [
                 'car:5'
             ],
          }

          SAMPLE_GROUPS: [
              'car:2'
          ]

          NUM_POINT_FEATURES: 5
          DATABASE_WITH_FAKELIDAR: False
          REMOVE_EXTRA_WIDTH: [0.0, 0.0, 0.0]
          LIMIT_WHOLE_SCENE: True

        - NAME: random_world_flip
          ALONG_AXIS_LIST: ['x', 'y']

        - NAME: random_world_rotation
          WORLD_ROT_ANGLE: [-0.3925, 0.3925]

        - NAME: random_world_scaling
          WORLD_SCALE_RANGE: [0.95, 1.05]


POINT_FEATURE_ENCODING: {
    encoding_type: absolute_coordinates_encoding,
    used_feature_list: ['x', 'y', 'z'],
    src_feature_list: ['x', 'y', 'z', 'intensity', 'timestamp'],
}

DATA_PROCESSOR:
    - NAME: mask_points_and_boxes_outside_range
      REMOVE_OUTSIDE_BOXES: True

    - NAME: shuffle_points
      SHUFFLE_ENABLED: {
        'train': True,
        'test': False
      }

    - NAME: transform_points_to_voxels
      VOXEL_SIZE: [0.1, 0.1, 0.15]
      MAX_POINTS_PER_VOXEL: 5
      MAX_NUMBER_OF_VOXELS: {
        'train': 80000,
        'test': 90000
      }