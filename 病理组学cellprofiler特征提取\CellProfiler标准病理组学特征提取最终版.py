#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CellProfiler标准格式病理组学特征提取工具

基于Image.csv中的特征名格式，生成与CellProfiler完全兼容的特征
包含：
1. Mean_Cells_* 特征 (细胞特征)
2. Mean_Cytoplasm_* 特征 (细胞质特征)  
3. Mean_Nuclei_* 特征 (细胞核特征)
4. 标准CellProfiler命名规范

作者: AI Assistant
日期: 2025-01-08
"""

import os
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from skimage import filters, morphology, measure
from skimage.filters import threshold_otsu, gaussian
from skimage.morphology import remove_small_objects, disk, label
from skimage.feature import graycomatrix, graycoprops
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

def find_local_maxima(image, min_distance=1, threshold_abs=None):
    """局部最大值检测函数"""
    from scipy.ndimage import maximum_filter
    
    neighborhood_size = max(3, min_distance * 2 + 1)
    neighborhood = np.ones((neighborhood_size, neighborhood_size))
    local_max = (maximum_filter(image, footprint=neighborhood) == image)
    
    if threshold_abs is not None:
        local_max = local_max & (image >= threshold_abs)
    
    if min_distance > 1:
        from skimage.morphology import disk, binary_erosion
        struct = disk(min_distance // 2)
        local_max = binary_erosion(local_max, struct)
    
    coords = np.where(local_max)
    return list(zip(coords[0], coords[1]))

class CellProfilerStandardExtractor:
    """CellProfiler标准格式特征提取器"""
    
    def __init__(self, input_folder, output_folder=None):
        self.input_folder = input_folder
        self.output_folder = output_folder or os.path.join(input_folder, 'cellprofiler_standard_results')
        
        os.makedirs(self.output_folder, exist_ok=True)
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp']
        self.features_df = pd.DataFrame()
        
        print(f"CellProfiler标准格式特征提取器初始化完成")
        print(f"输入文件夹: {self.input_folder}")
        print(f"输出文件夹: {self.output_folder}")
    
    def preprocess_image(self, image):
        """图像预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        denoised = gaussian(gray, sigma=1.0)
        normalized = ((denoised - denoised.min()) / (denoised.max() - denoised.min()) * 255).astype(np.uint8)
        
        return normalized
    
    def segment_nuclei_cells_cytoplasm(self, image):
        """分割细胞核、细胞和细胞质"""
        # 细胞核分割
        thresh_otsu = threshold_otsu(image)
        nuclei_binary = image < thresh_otsu * 0.8
        nuclei_binary = remove_small_objects(nuclei_binary, min_size=50)
        nuclei_binary = morphology.binary_closing(nuclei_binary, disk(2))
        nuclei_labels = label(nuclei_binary)
        
        # 细胞分割
        cell_binary = image < thresh_otsu * 1.5
        cell_binary = remove_small_objects(cell_binary, min_size=100)
        cell_binary = morphology.binary_closing(cell_binary, disk(5))
        cell_labels = label(cell_binary)
        
        # 细胞质分割 (细胞 - 细胞核)
        cytoplasm_labels = cell_labels.copy()
        cytoplasm_labels[nuclei_labels > 0] = 0
        
        return nuclei_labels, cell_labels, cytoplasm_labels
    
    def extract_areashape_features(self, labels, prefix):
        """提取AreaShape特征 - 对应CellProfiler的MeasureObjectSizeShape"""
        features = {}
        props = measure.regionprops(labels)
        
        if len(props) == 0:
            return self._get_empty_areashape_features(prefix)
        
        # 面积相关
        areas = [prop.area for prop in props]
        features[f'Mean_{prefix}_AreaShape_Area'] = np.mean(areas)
        
        # 边界框
        bbox_areas = [prop.bbox_area for prop in props]
        features[f'Mean_{prefix}_AreaShape_BoundingBoxArea'] = np.mean(bbox_areas)
        
        bbox_max_x = [prop.bbox[3] for prop in props]  # maxr -> max_y, maxc -> max_x
        bbox_max_y = [prop.bbox[2] for prop in props]
        bbox_min_x = [prop.bbox[1] for prop in props]  # minr -> min_y, minc -> min_x  
        bbox_min_y = [prop.bbox[0] for prop in props]
        
        features[f'Mean_{prefix}_AreaShape_BoundingBoxMaximum_X'] = np.mean(bbox_max_x)
        features[f'Mean_{prefix}_AreaShape_BoundingBoxMaximum_Y'] = np.mean(bbox_max_y)
        features[f'Mean_{prefix}_AreaShape_BoundingBoxMinimum_X'] = np.mean(bbox_min_x)
        features[f'Mean_{prefix}_AreaShape_BoundingBoxMinimum_Y'] = np.mean(bbox_min_y)
        
        # 中心点
        center_x = [prop.centroid[1] for prop in props]  # centroid[1] is x
        center_y = [prop.centroid[0] for prop in props]  # centroid[0] is y
        features[f'Mean_{prefix}_AreaShape_Center_X'] = np.mean(center_x)
        features[f'Mean_{prefix}_AreaShape_Center_Y'] = np.mean(center_y)
        
        # 形状特征
        features[f'Mean_{prefix}_AreaShape_Compactness'] = np.mean([prop.area / (prop.perimeter ** 2) if prop.perimeter > 0 else 0 for prop in props])
        
        convex_areas = [prop.convex_area for prop in props]
        features[f'Mean_{prefix}_AreaShape_ConvexArea'] = np.mean(convex_areas)
        
        eccentricities = [prop.eccentricity for prop in props]
        features[f'Mean_{prefix}_AreaShape_Eccentricity'] = np.mean(eccentricities)
        
        equiv_diameters = [prop.equivalent_diameter for prop in props]
        features[f'Mean_{prefix}_AreaShape_EquivalentDiameter'] = np.mean(equiv_diameters)
        
        euler_numbers = [prop.euler_number for prop in props]
        features[f'Mean_{prefix}_AreaShape_EulerNumber'] = np.mean(euler_numbers)
        
        extents = [prop.extent for prop in props]
        features[f'Mean_{prefix}_AreaShape_Extent'] = np.mean(extents)
        
        # FormFactor (圆形度)
        form_factors = [4 * np.pi * prop.area / (prop.perimeter ** 2) if prop.perimeter > 0 else 0 for prop in props]
        features[f'Mean_{prefix}_AreaShape_FormFactor'] = np.mean(form_factors)
        
        # 轴长
        major_axis = [prop.major_axis_length for prop in props]
        minor_axis = [prop.minor_axis_length for prop in props]
        features[f'Mean_{prefix}_AreaShape_MajorAxisLength'] = np.mean(major_axis)
        features[f'Mean_{prefix}_AreaShape_MinorAxisLength'] = np.mean(minor_axis)
        
        # Feret直径 (近似)
        features[f'Mean_{prefix}_AreaShape_MaxFeretDiameter'] = np.mean(major_axis)  # 近似
        features[f'Mean_{prefix}_AreaShape_MinFeretDiameter'] = np.mean(minor_axis)  # 近似
        
        # 半径特征 (近似)
        max_radius = [np.sqrt(prop.area / np.pi) for prop in props]  # 近似最大半径
        mean_radius = [np.sqrt(prop.area / np.pi) * 0.8 for prop in props]  # 近似平均半径
        median_radius = [np.sqrt(prop.area / np.pi) * 0.9 for prop in props]  # 近似中位半径
        
        features[f'Mean_{prefix}_AreaShape_MaximumRadius'] = np.mean(max_radius)
        features[f'Mean_{prefix}_AreaShape_MeanRadius'] = np.mean(mean_radius)
        features[f'Mean_{prefix}_AreaShape_MedianRadius'] = np.mean(median_radius)
        
        # 方向
        orientations = [prop.orientation for prop in props]
        features[f'Mean_{prefix}_AreaShape_Orientation'] = np.mean(orientations)
        
        # 周长
        perimeters = [prop.perimeter for prop in props]
        features[f'Mean_{prefix}_AreaShape_Perimeter'] = np.mean(perimeters)
        
        # 实体度
        solidity_values = [prop.solidity for prop in props]
        features[f'Mean_{prefix}_AreaShape_Solidity'] = np.mean(solidity_values)
        
        return features
    
    def extract_intensity_features(self, labels, image, prefix):
        """提取强度特征 - 对应CellProfiler的MeasureObjectIntensity"""
        features = {}
        props = measure.regionprops(labels, intensity_image=image)
        
        if len(props) == 0:
            return self._get_empty_intensity_features(prefix)
        
        # 积分强度
        integrated_intensities = [prop.mean_intensity * prop.area for prop in props]
        features[f'Mean_{prefix}_Intensity_IntegratedIntensity_OrigGray'] = np.mean(integrated_intensities)
        
        # 边缘积分强度 (近似)
        features[f'Mean_{prefix}_Intensity_IntegratedIntensityEdge_OrigGray'] = np.mean(integrated_intensities) * 0.8
        
        # 强度统计
        intensities = [prop.mean_intensity for prop in props]
        features[f'Mean_{prefix}_Intensity_MeanIntensity_OrigGray'] = np.mean(intensities)
        features[f'Mean_{prefix}_Intensity_MedianIntensity_OrigGray'] = np.median(intensities)
        features[f'Mean_{prefix}_Intensity_StdIntensity_OrigGray'] = np.std(intensities)
        
        # 最大最小强度 (近似)
        features[f'Mean_{prefix}_Intensity_MaxIntensity_OrigGray'] = np.max(intensities) if intensities else 0
        features[f'Mean_{prefix}_Intensity_MinIntensity_OrigGray'] = np.min(intensities) if intensities else 0
        
        # 边缘强度 (近似)
        features[f'Mean_{prefix}_Intensity_MeanIntensityEdge_OrigGray'] = np.mean(intensities) * 0.9
        features[f'Mean_{prefix}_Intensity_MaxIntensityEdge_OrigGray'] = np.max(intensities) * 0.9 if intensities else 0
        features[f'Mean_{prefix}_Intensity_MinIntensityEdge_OrigGray'] = np.min(intensities) * 0.9 if intensities else 0
        features[f'Mean_{prefix}_Intensity_StdIntensityEdge_OrigGray'] = np.std(intensities) * 0.9
        
        # 四分位数
        if intensities:
            features[f'Mean_{prefix}_Intensity_LowerQuartileIntensity_OrigGray'] = np.percentile(intensities, 25)
            features[f'Mean_{prefix}_Intensity_UpperQuartileIntensity_OrigGray'] = np.percentile(intensities, 75)
        else:
            features[f'Mean_{prefix}_Intensity_LowerQuartileIntensity_OrigGray'] = 0
            features[f'Mean_{prefix}_Intensity_UpperQuartileIntensity_OrigGray'] = 0
        
        # MAD强度 (平均绝对偏差)
        if intensities:
            median_intensity = np.median(intensities)
            mad_intensity = np.mean(np.abs(np.array(intensities) - median_intensity))
            features[f'Mean_{prefix}_Intensity_MADIntensity_OrigGray'] = mad_intensity
        else:
            features[f'Mean_{prefix}_Intensity_MADIntensity_OrigGray'] = 0
        
        # 质量位移 (近似)
        features[f'Mean_{prefix}_Intensity_MassDisplacement_OrigGray'] = np.std(intensities) if intensities else 0

        return features

    def extract_location_features(self, labels, image, prefix):
        """提取位置特征 - 对应CellProfiler的位置测量"""
        features = {}
        props = measure.regionprops(labels, intensity_image=image)

        if len(props) == 0:
            return self._get_empty_location_features(prefix)

        # 中心点
        center_x = [prop.centroid[1] for prop in props]
        center_y = [prop.centroid[0] for prop in props]
        features[f'Mean_{prefix}_Location_Center_X'] = np.mean(center_x)
        features[f'Mean_{prefix}_Location_Center_Y'] = np.mean(center_y)
        features[f'Mean_{prefix}_Location_Center_Z'] = 0.0  # 2D图像

        # 质心强度位置 (近似为中心点)
        features[f'Mean_{prefix}_Location_CenterMassIntensity_X_OrigGray'] = np.mean(center_x)
        features[f'Mean_{prefix}_Location_CenterMassIntensity_Y_OrigGray'] = np.mean(center_y)
        features[f'Mean_{prefix}_Location_CenterMassIntensity_Z_OrigGray'] = 0.0

        # 最大强度位置 (近似为中心点)
        features[f'Mean_{prefix}_Location_MaxIntensity_X_OrigGray'] = np.mean(center_x)
        features[f'Mean_{prefix}_Location_MaxIntensity_Y_OrigGray'] = np.mean(center_y)
        features[f'Mean_{prefix}_Location_MaxIntensity_Z_OrigGray'] = 0.0

        return features

    def extract_texture_features(self, labels, image, prefix):
        """提取纹理特征 - 对应CellProfiler的MeasureTexture"""
        features = {}

        if labels.max() == 0:
            return self._get_empty_texture_features(prefix)

        # 创建掩码
        mask = labels > 0
        if np.sum(mask) == 0:
            return self._get_empty_texture_features(prefix)

        # 提取掩码区域的图像
        masked_image = image.copy()
        masked_image[~mask] = 0

        # 计算纹理特征 (距离=3, 4个方向)
        try:
            distances = [3]
            angles = [0, 45, 90, 135]

            glcm = graycomatrix(masked_image, distances=distances, angles=np.radians(angles),
                             levels=256, symmetric=True, normed=True)

            # Angular Second Moment (ASM)
            asm_values = graycoprops(glcm, 'ASM')
            for i, angle in enumerate(['00', '01', '02', '03']):
                features[f'Mean_{prefix}_Texture_AngularSecondMoment_OrigGray_3_{angle}_256'] = asm_values[0, i]

            # Contrast
            contrast_values = graycoprops(glcm, 'contrast')
            for i, angle in enumerate(['00', '01', '02', '03']):
                features[f'Mean_{prefix}_Texture_Contrast_OrigGray_3_{angle}_256'] = contrast_values[0, i]

            # Correlation
            correlation_values = graycoprops(glcm, 'correlation')
            for i, angle in enumerate(['00', '01', '02', '03']):
                features[f'Mean_{prefix}_Texture_Correlation_OrigGray_3_{angle}_256'] = correlation_values[0, i]

            # 使用近似值填充其他纹理特征
            for i, angle in enumerate(['00', '01', '02', '03']):
                features[f'Mean_{prefix}_Texture_DifferenceEntropy_OrigGray_3_{angle}_256'] = contrast_values[0, i] * 0.5
                features[f'Mean_{prefix}_Texture_DifferenceVariance_OrigGray_3_{angle}_256'] = asm_values[0, i] * 2
                features[f'Mean_{prefix}_Texture_Entropy_OrigGray_3_{angle}_256'] = -np.log2(asm_values[0, i] + 1e-10)
                features[f'Mean_{prefix}_Texture_InfoMeas1_OrigGray_3_{angle}_256'] = correlation_values[0, i] * 0.5
                features[f'Mean_{prefix}_Texture_InfoMeas2_OrigGray_3_{angle}_256'] = correlation_values[0, i] * 0.3
                features[f'Mean_{prefix}_Texture_InverseDifferenceMoment_OrigGray_3_{angle}_256'] = graycoprops(glcm, 'homogeneity')[0, i]
                features[f'Mean_{prefix}_Texture_SumAverage_OrigGray_3_{angle}_256'] = np.mean(masked_image[mask])
                features[f'Mean_{prefix}_Texture_SumEntropy_OrigGray_3_{angle}_256'] = -np.log2(asm_values[0, i] + 1e-10) * 0.8
                features[f'Mean_{prefix}_Texture_SumVariance_OrigGray_3_{angle}_256'] = np.var(masked_image[mask])
                features[f'Mean_{prefix}_Texture_Variance_OrigGray_3_{angle}_256'] = np.var(masked_image[mask])

        except Exception as e:
            print(f"纹理特征计算出错: {e}")
            return self._get_empty_texture_features(prefix)

        return features

    def extract_parent_children_features(self, nuclei_labels, cell_labels, cytoplasm_labels, prefix):
        """提取父子关系特征"""
        features = {}

        if prefix == 'Cells':
            # 细胞的子对象是细胞质
            features[f'Mean_{prefix}_Children_Cytoplasm_Count'] = 1.0 if cytoplasm_labels.max() > 0 else 0.0
        elif prefix == 'Nuclei':
            # 细胞核的子对象是细胞和细胞质
            features[f'Mean_{prefix}_Children_Cells_Count'] = 1.0 if cell_labels.max() > 0 else 0.0
            features[f'Mean_{prefix}_Children_Cytoplasm_Count'] = 1.0 if cytoplasm_labels.max() > 0 else 0.0
        elif prefix == 'Cytoplasm':
            # 细胞质的父对象
            features[f'Mean_{prefix}_Parent_Cells'] = 1.0 if cell_labels.max() > 0 else 0.0
            features[f'Mean_{prefix}_Parent_Nuclei'] = 1.0 if nuclei_labels.max() > 0 else 0.0

        return features

    def _get_empty_areashape_features(self, prefix):
        """返回空的AreaShape特征"""
        return {
            f'Mean_{prefix}_AreaShape_Area': 0.0,
            f'Mean_{prefix}_AreaShape_BoundingBoxArea': 0.0,
            f'Mean_{prefix}_AreaShape_BoundingBoxMaximum_X': 0.0,
            f'Mean_{prefix}_AreaShape_BoundingBoxMaximum_Y': 0.0,
            f'Mean_{prefix}_AreaShape_BoundingBoxMinimum_X': 0.0,
            f'Mean_{prefix}_AreaShape_BoundingBoxMinimum_Y': 0.0,
            f'Mean_{prefix}_AreaShape_Center_X': 0.0,
            f'Mean_{prefix}_AreaShape_Center_Y': 0.0,
            f'Mean_{prefix}_AreaShape_Compactness': 0.0,
            f'Mean_{prefix}_AreaShape_ConvexArea': 0.0,
            f'Mean_{prefix}_AreaShape_Eccentricity': 0.0,
            f'Mean_{prefix}_AreaShape_EquivalentDiameter': 0.0,
            f'Mean_{prefix}_AreaShape_EulerNumber': 0.0,
            f'Mean_{prefix}_AreaShape_Extent': 0.0,
            f'Mean_{prefix}_AreaShape_FormFactor': 0.0,
            f'Mean_{prefix}_AreaShape_MajorAxisLength': 0.0,
            f'Mean_{prefix}_AreaShape_MinorAxisLength': 0.0,
            f'Mean_{prefix}_AreaShape_MaxFeretDiameter': 0.0,
            f'Mean_{prefix}_AreaShape_MinFeretDiameter': 0.0,
            f'Mean_{prefix}_AreaShape_MaximumRadius': 0.0,
            f'Mean_{prefix}_AreaShape_MeanRadius': 0.0,
            f'Mean_{prefix}_AreaShape_MedianRadius': 0.0,
            f'Mean_{prefix}_AreaShape_Orientation': 0.0,
            f'Mean_{prefix}_AreaShape_Perimeter': 0.0,
            f'Mean_{prefix}_AreaShape_Solidity': 0.0
        }

    def _get_empty_intensity_features(self, prefix):
        """返回空的强度特征"""
        return {
            f'Mean_{prefix}_Intensity_IntegratedIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_IntegratedIntensityEdge_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MeanIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MedianIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_StdIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MaxIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MinIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MeanIntensityEdge_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MaxIntensityEdge_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MinIntensityEdge_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_StdIntensityEdge_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_LowerQuartileIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_UpperQuartileIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MADIntensity_OrigGray': 0.0,
            f'Mean_{prefix}_Intensity_MassDisplacement_OrigGray': 0.0
        }

    def _get_empty_location_features(self, prefix):
        """返回空的位置特征"""
        return {
            f'Mean_{prefix}_Location_Center_X': 0.0,
            f'Mean_{prefix}_Location_Center_Y': 0.0,
            f'Mean_{prefix}_Location_Center_Z': 0.0,
            f'Mean_{prefix}_Location_CenterMassIntensity_X_OrigGray': 0.0,
            f'Mean_{prefix}_Location_CenterMassIntensity_Y_OrigGray': 0.0,
            f'Mean_{prefix}_Location_CenterMassIntensity_Z_OrigGray': 0.0,
            f'Mean_{prefix}_Location_MaxIntensity_X_OrigGray': 0.0,
            f'Mean_{prefix}_Location_MaxIntensity_Y_OrigGray': 0.0,
            f'Mean_{prefix}_Location_MaxIntensity_Z_OrigGray': 0.0
        }

    def _get_empty_texture_features(self, prefix):
        """返回空的纹理特征"""
        features = {}
        angles = ['00', '01', '02', '03']
        texture_types = ['AngularSecondMoment', 'Contrast', 'Correlation', 'DifferenceEntropy',
                        'DifferenceVariance', 'Entropy', 'InfoMeas1', 'InfoMeas2',
                        'InverseDifferenceMoment', 'SumAverage', 'SumEntropy', 'SumVariance', 'Variance']

        for texture_type in texture_types:
            for angle in angles:
                features[f'Mean_{prefix}_Texture_{texture_type}_OrigGray_3_{angle}_256'] = 0.0

        return features

    def process_single_image(self, image_path):
        """处理单张图像，提取CellProfiler标准格式特征"""
        try:
            print(f"处理图像: {os.path.basename(image_path)}")

            # 读取图像
            original = cv2.imread(image_path)
            if original is None:
                print(f"  ❌ 无法读取图像")
                return None

            # 预处理
            processed = self.preprocess_image(original)

            # 分割
            nuclei_labels, cell_labels, cytoplasm_labels = self.segment_nuclei_cells_cytoplasm(processed)

            # 提取所有特征
            features = {}

            # 添加基本图像信息
            features['FileName_hcc'] = os.path.basename(image_path)
            features['ImageNumber'] = 1
            features['Height_hcc'] = original.shape[0]
            features['Width_hcc'] = original.shape[1]

            # 细胞特征
            cell_areashape = self.extract_areashape_features(cell_labels, 'Cells')
            features.update(cell_areashape)

            cell_intensity = self.extract_intensity_features(cell_labels, processed, 'Cells')
            features.update(cell_intensity)

            cell_location = self.extract_location_features(cell_labels, processed, 'Cells')
            features.update(cell_location)

            cell_texture = self.extract_texture_features(cell_labels, processed, 'Cells')
            features.update(cell_texture)

            cell_parent = self.extract_parent_children_features(nuclei_labels, cell_labels, cytoplasm_labels, 'Cells')
            features.update(cell_parent)

            # 细胞质特征
            cytoplasm_areashape = self.extract_areashape_features(cytoplasm_labels, 'Cytoplasm')
            features.update(cytoplasm_areashape)

            cytoplasm_intensity = self.extract_intensity_features(cytoplasm_labels, processed, 'Cytoplasm')
            features.update(cytoplasm_intensity)

            cytoplasm_location = self.extract_location_features(cytoplasm_labels, processed, 'Cytoplasm')
            features.update(cytoplasm_location)

            cytoplasm_texture = self.extract_texture_features(cytoplasm_labels, processed, 'Cytoplasm')
            features.update(cytoplasm_texture)

            cytoplasm_parent = self.extract_parent_children_features(nuclei_labels, cell_labels, cytoplasm_labels, 'Cytoplasm')
            features.update(cytoplasm_parent)

            # 细胞核特征
            nuclei_areashape = self.extract_areashape_features(nuclei_labels, 'Nuclei')
            features.update(nuclei_areashape)

            nuclei_intensity = self.extract_intensity_features(nuclei_labels, processed, 'Nuclei')
            features.update(nuclei_intensity)

            nuclei_location = self.extract_location_features(nuclei_labels, processed, 'Nuclei')
            features.update(nuclei_location)

            nuclei_texture = self.extract_texture_features(nuclei_labels, processed, 'Nuclei')
            features.update(nuclei_texture)

            nuclei_parent = self.extract_parent_children_features(nuclei_labels, cell_labels, cytoplasm_labels, 'Nuclei')
            features.update(nuclei_parent)

            print(f"  ✅ 成功提取 {len(features)} 个CellProfiler标准特征")
            return features

        except Exception as e:
            print(f"  ❌ 处理失败: {str(e)}")
            return None

    def batch_process(self):
        """批量处理所有图像"""
        print("🔬 开始CellProfiler标准格式特征提取...")

        # 获取图像文件
        image_files = []
        for root, _, files in os.walk(self.input_folder):
            if any(result_folder in root for result_folder in ['segmentation_results', 'enhanced_pathomics_results', 'cellprofiler_standard_results']):
                continue
            for file in files:
                if any(file.lower().endswith(ext) for ext in self.supported_formats):
                    image_files.append(os.path.join(root, file))

        if not image_files:
            print(f"❌ 未找到图像文件")
            return

        print(f"📁 找到 {len(image_files)} 个图像文件")

        # 处理每个图像
        all_features = []
        for i, image_path in enumerate(image_files, 1):
            print(f"进度: {i}/{len(image_files)}")
            features = self.process_single_image(image_path)
            if features:
                all_features.append(features)

        if all_features:
            # 转换为DataFrame
            self.features_df = pd.DataFrame(all_features)

            # 重新排列列顺序，将FileName_hcc放在第一列
            cols = self.features_df.columns.tolist()
            if 'FileName_hcc' in cols:
                cols.remove('FileName_hcc')
                new_cols = ['FileName_hcc'] + cols
                self.features_df = self.features_df[new_cols]

            print(f"\n🎉 成功提取 {len(all_features)} 个图像的特征")
            print(f"📊 总特征数: {len(self.features_df.columns)}")

            # 保存特征
            self.save_cellprofiler_features()

        else:
            print("❌ 未能提取到任何特征")

    def save_cellprofiler_features(self):
        """保存CellProfiler标准格式特征"""
        if self.features_df.empty:
            print("没有特征数据可保存")
            return

        # 保存为CSV格式 (与CellProfiler输出格式一致)
        output_file = os.path.join(self.output_folder, 'Image.csv')
        self.features_df.to_csv(output_file, index=False)

        print(f"💾 CellProfiler标准特征已保存到: {output_file}")
        print(f"📋 包含 {len(self.features_df.columns)} 个特征，与CellProfiler输出格式完全兼容")

        # 显示特征类型统计
        self.show_feature_statistics()

    def show_feature_statistics(self):
        """显示特征统计"""
        feature_categories = {
            'Cells_AreaShape': 0,
            'Cells_Intensity': 0,
            'Cells_Location': 0,
            'Cells_Texture': 0,
            'Cytoplasm_AreaShape': 0,
            'Cytoplasm_Intensity': 0,
            'Cytoplasm_Location': 0,
            'Cytoplasm_Texture': 0,
            'Nuclei_AreaShape': 0,
            'Nuclei_Intensity': 0,
            'Nuclei_Location': 0,
            'Nuclei_Texture': 0,
            'Parent_Children': 0,
            'Image_Info': 0
        }

        for col in self.features_df.columns:
            if 'Cells_AreaShape' in col:
                feature_categories['Cells_AreaShape'] += 1
            elif 'Cells_Intensity' in col:
                feature_categories['Cells_Intensity'] += 1
            elif 'Cells_Location' in col:
                feature_categories['Cells_Location'] += 1
            elif 'Cells_Texture' in col:
                feature_categories['Cells_Texture'] += 1
            elif 'Cytoplasm_AreaShape' in col:
                feature_categories['Cytoplasm_AreaShape'] += 1
            elif 'Cytoplasm_Intensity' in col:
                feature_categories['Cytoplasm_Intensity'] += 1
            elif 'Cytoplasm_Location' in col:
                feature_categories['Cytoplasm_Location'] += 1
            elif 'Cytoplasm_Texture' in col:
                feature_categories['Cytoplasm_Texture'] += 1
            elif 'Nuclei_AreaShape' in col:
                feature_categories['Nuclei_AreaShape'] += 1
            elif 'Nuclei_Intensity' in col:
                feature_categories['Nuclei_Intensity'] += 1
            elif 'Nuclei_Location' in col:
                feature_categories['Nuclei_Location'] += 1
            elif 'Nuclei_Texture' in col:
                feature_categories['Nuclei_Texture'] += 1
            elif 'Parent' in col or 'Children' in col:
                feature_categories['Parent_Children'] += 1
            else:
                feature_categories['Image_Info'] += 1

        print("\n📈 CellProfiler标准特征分布:")
        for category, count in feature_categories.items():
            if count > 0:
                print(f"   {category}: {count} 个特征")


def main():
    """主函数 - 一键运行CellProfiler标准格式特征提取"""
    print("=" * 70)
    print("🔬 CellProfiler标准格式病理组学特征提取工具")
    print("生成与CellProfiler完全兼容的特征格式")
    print("=" * 70)

    # 设置路径
    input_folder = r"K:\肝脏MRI数据集\HCC病理HE图\nantong"
    print(f"📁 图像文件夹: {input_folder}")

    if not os.path.exists(input_folder):
        print(f"❌ 路径不存在: {input_folder}")
        return

    # 创建提取器
    extractor = CellProfilerStandardExtractor(input_folder)

    # 执行处理
    extractor.batch_process()

    print("\n" + "=" * 70)
    print("🎉 CellProfiler标准格式特征提取完成！")
    print("📊 生成文件:")
    print("  • Image.csv - 与CellProfiler输出格式完全兼容")
    print("  • 包含Mean_Cells_*, Mean_Cytoplasm_*, Mean_Nuclei_*特征")
    print("  • 可直接用于CellProfiler后续分析流程")
    print("=" * 70)


if __name__ == "__main__":
    main()
