#!/usr/bin/env python
# coding: utf-8

# ## 9. Point set and Mask Transformation

# <a id='section_id10'></a>
# Transformix can be used to transform point sets and mask images as well. Masks can be seen as images so the registration of masks is done in similar fashion as the registration of a moving image.
# When transforming an image or mask (defined in the moving image domain), it is transformed to the fixed image domain. This is useful for example when one needs to deform a segmentation from the moving image to the fixed image, to obtain an estimate of a segmentation in the fixed image. When transforming a point set, this happens the other way around: from the fixed image domain to the moving image domain. This is useful for example when you want to know where a point maps to.
# Point sets therefor need not be transformed with the backwards mapping protocol, but can instead be transformed with regular forward transformation (fixed -> moving).
# Transforming point sets can be used to get the regions of interest (ROI) in the moving image, based on ROI of the fixed image.

# ### Elastix

# In[1]:


import itk
import numpy as np
from itkwidgets import compare, checkerboard,view


# In[2]:


# Import Images
fixed_image = itk.imread('data/CT_3D_lung_fixed.mha', itk.F)
moving_image = itk.imread('data/CT_3D_lung_moving.mha', itk.F)

# Import Default Parameter Map
parameter_object = itk.ParameterObject.New()
parameter_map_rigid = parameter_object.GetDefaultParameterMap('rigid')
parameter_object.AddParameterMap(parameter_map_rigid)
parameter_map_affine= parameter_object.GetDefaultParameterMap('affine')
parameter_object.AddParameterMap(parameter_map_affine)
parameter_map_bspline = parameter_object.GetDefaultParameterMap('bspline')
parameter_object.AddParameterMap(parameter_map_bspline)


# Registration with the registration function...

# In[3]:


# Call registration function
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image,
    parameter_object=parameter_object,
    log_to_console=True)


# ### Mask Transformation

# In[4]:


# Import Mask
# Note: Version v0.17.0 and onwards supports various image types for elastix and transformix functions/objects
moving_mask = itk.imread('data/CT_3D_lung_moving_mask.mha', itk.UC)


# In[5]:


# Mask image is a binary image, therefore the FinalBSplineInterpolationOrder should be 0
result_transform_parameters.SetParameter('FinalBSplineInterpolationOrder','0')


# Transformation of a mask is similar to the transformation of an image and can either be done in one line with the transformix function...

# In[6]:


# Procedural interface of transformix filter
result_moving_mask = itk.transformix_filter(
    moving_mask,
    result_transform_parameters)


# .. or by initiating an transformix image filter object.

# In[7]:


# Load Transformix Object, initialize with 3D Mask. 
transformix_object = itk.TransformixFilter.New(moving_mask)

transformix_object.SetTransformParameterObject(result_transform_parameters)

# Update object (required)
transformix_object.UpdateLargestPossibleRegion()

# Results of Transformation
result_moving_mask = transformix_object.GetOutput()


# ### Segmentation Transformation Evaluation

# The result of a segmentation transformation can be evaluated by means of, for example, the dice loss. The dice loss is the proportion of the 2 segmentations that overlap. In the example above the masks used were segmentations of the images. (This is offcourse not always the case, masks could also hide artifacts, in which case they're not (only) image segmentations). The dice loss of the segmentation transformation can therefore be calculated with the dice loss of the 2 masks.

# In[8]:


# In case of boolaen segmentation, the dice loss is equal to 2 x dot product of the flattened arrays, 
# divided by the total volume of both masks.
def dice_loss(y_true, y_pred):
    y_true = y_true.flatten()
    y_pred = y_pred.flatten()
    intersection = y_true.dot(y_pred)
    union = sum(y_true) + sum(y_pred)
    dice = 2 * intersection/union
    return dice


# In[9]:


# Import groundtruth segmentation
fixed_mask = itk.imread('data/CT_3D_lung_fixed_mask.mha', itk.UC)

# Cast itk images to numpy arrays and round result image to boolean array.
fixed_mask_np = np.asarray(fixed_mask)
result_mask_np = np.asarray(result_moving_mask).round().astype(int)


# In[10]:


print("Dice loss:",dice_loss(fixed_mask_np, result_mask_np))


# In[11]:


compare(fixed_mask, result_moving_mask)


# ### Point Set Transformation

# In[12]:


# Procedural interface of transformix filter
result_point_set = itk.transformix_pointset(
    moving_image, result_transform_parameters,
    fixed_point_set_file_name='data/CT_3D_lung_fixed_point_set_corrected.txt',
    output_directory = './exampleoutput')


# .. or by initiating an transformix image filter object.

# In[13]:


# Load Transformix Object
transformix_object = itk.TransformixFilter.New(moving_image)
transformix_object.SetFixedPointSetFileName('data/CT_3D_lung_fixed_point_set_corrected.txt')
transformix_object.SetTransformParameterObject(result_transform_parameters)
transformix_object.SetLogToConsole(True)
transformix_object.SetOutputDirectory('./exampleoutput/')

# Update object (required)
transformix_object.UpdateLargestPossibleRegion()

# Results of Transformation
# -- Bug? -- Output is saved as .txt file in outputdirectory.
# The .GetOutput() function outputs an empty image.
output_transformix = transformix_object.GetOutput()
result_point_set = np.loadtxt('exampleoutput/outputpoints.txt', dtype='str')[:,30:33].astype('float64')


# In[14]:


view(moving_image, point_sets=[result_point_set])

