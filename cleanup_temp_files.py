import os
import shutil
import time
from pathlib import Path
import tempfile

def get_size_str(size_bytes):
    """将字节数转换为可读的大小格式"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"

def get_folder_size(folder_path):
    """计算文件夹总大小"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                try:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
                except (OSError, FileNotFoundError):
                    continue
    except (OSError, PermissionError):
        pass
    return total_size

def safe_delete_file(file_path):
    """安全删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
    except (OSError, PermissionError):
        return False
    return False

def safe_delete_folder(folder_path):
    """安全删除文件夹"""
    try:
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            shutil.rmtree(folder_path)
            return True
    except (OSError, PermissionError):
        return False
    return False

def clean_temp_folder(temp_path):
    """清理临时文件夹"""
    print(f"正在清理: {temp_path}")
    
    if not os.path.exists(temp_path):
        print(f"  路径不存在，跳过")
        return 0, 0
    
    # 计算清理前大小
    before_size = get_folder_size(temp_path)
    print(f"  清理前大小: {get_size_str(before_size)}")
    
    deleted_files = 0
    deleted_folders = 0
    
    try:
        for item in os.listdir(temp_path):
            item_path = os.path.join(temp_path, item)
            
            try:
                if os.path.isfile(item_path):
                    if safe_delete_file(item_path):
                        deleted_files += 1
                elif os.path.isdir(item_path):
                    if safe_delete_folder(item_path):
                        deleted_folders += 1
            except (OSError, PermissionError):
                continue
                
    except (OSError, PermissionError):
        print(f"  无法访问某些文件")
    
    # 计算清理后大小
    after_size = get_folder_size(temp_path)
    freed_space = before_size - after_size
    
    print(f"  删除文件: {deleted_files} 个")
    print(f"  删除文件夹: {deleted_folders} 个")
    print(f"  释放空间: {get_size_str(freed_space)}")
    print(f"  清理后大小: {get_size_str(after_size)}")
    
    return freed_space, deleted_files + deleted_folders

def clean_downloads_interactive(downloads_path):
    """交互式清理下载文件夹"""
    print(f"正在分析下载文件夹: {downloads_path}")
    
    if not os.path.exists(downloads_path):
        print(f"  路径不存在，跳过")
        return 0, 0
    
    # 获取所有文件并按大小排序
    files_info = []
    
    try:
        for item in os.listdir(downloads_path):
            item_path = os.path.join(downloads_path, item)
            if os.path.isfile(item_path):
                try:
                    size = os.path.getsize(item_path)
                    mod_time = os.path.getmtime(item_path)
                    files_info.append((item_path, item, size, mod_time))
                except (OSError, FileNotFoundError):
                    continue
    except (OSError, PermissionError):
        print(f"  无法访问下载文件夹")
        return 0, 0
    
    if not files_info:
        print(f"  下载文件夹为空")
        return 0, 0
    
    # 按大小排序（大文件在前）
    files_info.sort(key=lambda x: x[2], reverse=True)
    
    total_size = sum(info[2] for info in files_info)
    print(f"  总大小: {get_size_str(total_size)}")
    print(f"  文件数量: {len(files_info)}")
    
    # 显示大文件（>10MB）
    large_files = [info for info in files_info if info[2] > 10 * 1024 * 1024]
    
    if large_files:
        print(f"\n  大文件列表 (>10MB):")
        for i, (file_path, filename, size, mod_time) in enumerate(large_files[:20], 1):
            mod_date = time.strftime("%Y-%m-%d", time.localtime(mod_time))
            print(f"    {i:2d}. {get_size_str(size):>10s} - {filename} ({mod_date})")
    
    print(f"\n选择清理方式:")
    print(f"1. 清理所有文件")
    print(f"2. 只清理大文件 (>10MB)")
    print(f"3. 手动选择文件")
    print(f"4. 跳过下载文件夹清理")
    
    while True:
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            # 清理所有文件
            return clean_all_downloads(files_info)
        elif choice == "2":
            # 只清理大文件
            return clean_large_downloads(large_files)
        elif choice == "3":
            # 手动选择
            return clean_selected_downloads(files_info)
        elif choice == "4":
            print("  跳过下载文件夹清理")
            return 0, 0
        else:
            print("请输入有效选择 (1-4)")

def clean_all_downloads(files_info):
    """清理所有下载文件"""
    print("  正在清理所有下载文件...")
    
    deleted_files = 0
    freed_space = 0
    
    for file_path, filename, size, mod_time in files_info:
        if safe_delete_file(file_path):
            deleted_files += 1
            freed_space += size
            print(f"    已删除: {filename}")
    
    print(f"  删除文件: {deleted_files} 个")
    print(f"  释放空间: {get_size_str(freed_space)}")
    
    return freed_space, deleted_files

def clean_large_downloads(large_files):
    """清理大文件"""
    print("  正在清理大文件 (>10MB)...")
    
    deleted_files = 0
    freed_space = 0
    
    for file_path, filename, size, mod_time in large_files:
        if safe_delete_file(file_path):
            deleted_files += 1
            freed_space += size
            print(f"    已删除: {filename} ({get_size_str(size)})")
    
    print(f"  删除文件: {deleted_files} 个")
    print(f"  释放空间: {get_size_str(freed_space)}")
    
    return freed_space, deleted_files

def clean_selected_downloads(files_info):
    """手动选择清理文件"""
    print("  手动选择模式 - 显示所有文件:")
    
    # 显示所有文件
    for i, (file_path, filename, size, mod_time) in enumerate(files_info, 1):
        mod_date = time.strftime("%Y-%m-%d", time.localtime(mod_time))
        print(f"    {i:2d}. {get_size_str(size):>10s} - {filename} ({mod_date})")
    
    print(f"\n请输入要删除的文件编号，用逗号分隔 (例如: 1,3,5-8)")
    print(f"或输入 'all' 删除所有文件，'cancel' 取消")
    
    while True:
        selection = input("选择: ").strip()
        
        if selection.lower() == 'cancel':
            return 0, 0
        elif selection.lower() == 'all':
            return clean_all_downloads(files_info)
        
        try:
            # 解析选择
            indices = []
            for part in selection.split(','):
                part = part.strip()
                if '-' in part:
                    start, end = map(int, part.split('-'))
                    indices.extend(range(start, end + 1))
                else:
                    indices.append(int(part))
            
            # 验证索引
            valid_indices = [i for i in indices if 1 <= i <= len(files_info)]
            
            if not valid_indices:
                print("没有有效的选择")
                continue
            
            # 删除选中的文件
            deleted_files = 0
            freed_space = 0
            
            for i in sorted(valid_indices):
                file_path, filename, size, mod_time = files_info[i-1]
                if safe_delete_file(file_path):
                    deleted_files += 1
                    freed_space += size
                    print(f"    已删除: {filename}")
            
            print(f"  删除文件: {deleted_files} 个")
            print(f"  释放空间: {get_size_str(freed_space)}")
            
            return freed_space, deleted_files
            
        except ValueError:
            print("输入格式错误，请重新输入")

def main():
    print("C盘临时文件清理工具")
    print("=" * 50)
    
    # 清理路径
    cleanup_paths = [
        ("用户临时文件", r"C:\Users\<USER>\AppData\Local\Temp"),
        ("系统临时文件", tempfile.gettempdir()),
        ("系统日志", r"C:\Windows\Logs"),
    ]
    
    downloads_path = r"C:\Users\<USER>\Downloads"
    
    total_freed = 0
    total_items = 0
    
    # 清理临时文件和日志
    for name, path in cleanup_paths:
        print(f"\n{name}:")
        print("-" * 30)
        freed, items = clean_temp_folder(path)
        total_freed += freed
        total_items += items
    
    # 交互式清理下载文件夹
    print(f"\n下载文件夹:")
    print("-" * 30)
    freed, items = clean_downloads_interactive(downloads_path)
    total_freed += freed
    total_items += items
    
    # 总结
    print(f"\n" + "=" * 50)
    print(f"清理完成!")
    print(f"总释放空间: {get_size_str(total_freed)}")
    print(f"总删除项目: {total_items} 个")
    
    if total_freed > 0:
        print(f"\n建议重启电脑以完全释放空间")

if __name__ == "__main__":
    try:
        # 确认清理
        print("警告: 此操作将删除临时文件和下载文件夹中的文件")
        print("建议在清理前备份重要文件")
        
        confirm = input("\n确认继续清理? (y/n): ").strip().lower()
        
        if confirm in ['y', 'yes', '是']:
            main()
        else:
            print("清理已取消")
            
    except KeyboardInterrupt:
        print("\n\n清理已取消")
    except Exception as e:
        print(f"\n发生错误: {e}")
    
    input("\n按回车键退出...")
