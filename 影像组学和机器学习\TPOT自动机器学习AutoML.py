# -*- coding: utf-8 -*-
"""
aHVPG预测模型开发 - 使用TPOT自动机器学习方法
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, KFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, r2_score
from tpot import TPOTRegressor
import matplotlib.pyplot as plt
import seaborn as sns
import joblib

# 设置随机种子以确保结果可重现
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)

# 加载数据
def load_data(file_path):
    """
    加载血管参数数据
    """
    data = pd.read_excel(file_path)
    print(f"加载了 {data.shape[0]} 个样本，{data.shape[1]} 个特征")
    return data

# 数据预处理
def preprocess_data(data):
    """
    预处理数据：分离特征和目标变量，处理缺失值
    """
    # 假设HVPG值在最后一列
    X = data.iloc[:, 1:-1]  # 除去ID列和HVPG列
    y = data.iloc[:, -1]    # HVPG值
    
    # 处理缺失值
    X = X.fillna(X.mean())
    
    print(f"特征数量: {X.shape[1]}")
    print(f"目标变量范围: {y.min()} - {y.max()}")
    
    return X, y

# 使用TPOT进行自动机器学习
def train_tpot_model(X, y):
    """
    使用TPOT训练最佳回归模型
    """
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=RANDOM_SEED
    )
    
    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 配置TPOT
    tpot = TPOTRegressor(
        generations=300,
        population_size=50,
        cv=10,
        random_state=RANDOM_SEED,
        verbosity=2,
        n_jobs=-1,  # 使用所有可用CPU核心
        scoring='neg_mean_absolute_error'
    )
    
    # 训练模型
    print("开始TPOT自动机器学习...")
    tpot.fit(X_train_scaled, y_train)
    
    # 评估最佳模型
    y_pred = tpot.predict(X_test_scaled)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    print(f"最佳模型MAE: {mae:.3f}")
    print(f"最佳模型R²: {r2:.3f}")
    print(f"最佳管道: {tpot.fitted_pipeline_}")
    
    # 保存模型和预处理器
    joblib.dump(tpot.fitted_pipeline_, 'aHVPG_best_model.pkl')
    joblib.dump(scaler, 'aHVPG_scaler.pkl')
    
    # 保存TPOT导出的Python代码
    tpot.export('aHVPG_tpot_pipeline.py')
    
    return tpot, scaler, X_test_scaled, y_test, y_pred

# 可视化结果
def visualize_results(y_test, y_pred):
    """
    可视化预测结果
    """
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, y_pred, alpha=0.7)
    
    # 添加对角线（完美预测线）
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    plt.xlabel('实际HVPG值')
    plt.ylabel('预测HVPG值')
    plt.title('aHVPG预测模型性能')
    plt.grid(True, alpha=0.3)
    
    # 添加R²和MAE信息
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    plt.annotate(f'R² = {r2:.3f}\nMAE = {mae:.3f}', 
                 xy=(0.05, 0.95), xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('aHVPG_prediction_results.png', dpi=300)
    plt.show()

# 特征重要性分析
def analyze_feature_importance(tpot, X):
    """
    分析特征重要性（如果模型支持）
    """
    try:
        # 尝试获取特征重要性
        model = tpot.fitted_pipeline_.steps[-1][1]
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
            feature_names = X.columns
            
            # 创建特征重要性DataFrame
            feature_importance = pd.DataFrame({
                'Feature': feature_names,
                'Importance': importances
            })
            
            # 按重要性排序
            feature_importance = feature_importance.sort_values('Importance', ascending=False)
            
            # 可视化特征重要性
            plt.figure(figsize=(12, 8))
            sns.barplot(x='Importance', y='Feature', data=feature_importance.head(15))
            plt.title('Top 15 特征重要性')
            plt.tight_layout()
            plt.savefig('aHVPG_feature_importance.png', dpi=300)
            plt.show()
            
            return feature_importance
        else:
            print("当前模型不支持直接的特征重要性分析")
            return None
    except:
        print("无法分析特征重要性")
        return None

# 主函数
def main():
    # 数据文件路径
    data_file = "vessel_params_with_hvpg.xlsx"
    
    # 加载和预处理数据
    data = load_data(data_file)
    X, y = preprocess_data(data)
    
    # 训练模型
    tpot, scaler, X_test_scaled, y_test, y_pred = train_tpot_model(X, y)
    
    # 可视化结果
    visualize_results(y_test, y_pred)
    
    # 分析特征重要性
    feature_importance = analyze_feature_importance(tpot, X)
    if feature_importance is not None:
        print("Top 5 重要特征:")
        print(feature_importance.head(5))

if __name__ == "__main__":
    main()