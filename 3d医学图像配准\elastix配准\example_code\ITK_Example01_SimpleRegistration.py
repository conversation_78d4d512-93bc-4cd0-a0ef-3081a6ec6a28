#!/usr/bin/env python
# coding: utf-8

# ## 1. Simple Elastix

# ### Registration

# In[1]:


import itk
from itkwidgets import compare, checkerboard
import numpy as np


# **Image registration** finds the *spatial transformation that aligns images in the presence of noise*.

# In image registration, we typically identify the two images as the fixed and moving image. Our goal is to find the spatial transformation that makes the moving image align with the fixed image.
# 
# First, let's load our **fixed image** and the image we will align to our fixed image, the **moving image**.

# In[2]:


# Load images with itk floats (itk.F). Necessary for elastix
fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)
moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)


# For now we will use a default parametermap of elastix, for more info about parametermaps, see [example2](ITK_Example02_CustomOrMultipleParameterMaps.ipynb#section_id2)

# In[3]:


parameter_object = itk.ParameterObject.New()
default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid')
parameter_object.AddParameterMap(default_rigid_parameter_map)


# Registration can either be done in one line with the registration function...

# In[4]:


# Call registration function
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image,
    parameter_object=parameter_object,
    log_to_console=False)


# .. or by initiating an elastix image filter object. The result of these to methods should only differ due to the stochastic nature of elastix. 
# 
# The object oriented method below can be used when more explicit function calls are preferred.

# In[5]:


# Load Elastix Image Filter Object
elastix_object = itk.ElastixRegistrationMethod.New(fixed_image, moving_image)
# elastix_object.SetFixedImage(fixed_image)
# elastix_object.SetMovingImage(moving_image)
elastix_object.SetParameterObject(parameter_object)

# Set additional options
elastix_object.SetLogToConsole(False)

# Update filter object (required)
elastix_object.UpdateLargestPossibleRegion()

# Results of Registration
result_image = elastix_object.GetOutput()
result_transform_parameters = elastix_object.GetTransformParameterObject()


# In[6]:


# Save image with itk
itk.imwrite(result_image,'exampleoutput/result_image.mha')


# The output of the elastix algorithm is the registered (transformed) version of the moving image. The parameters of this transformation can also be obtained after registation.
