#!/bin/bash

# 医学图像扩散模型训练脚本

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0

# 配置文件路径
CONFIG_PATH="configs/config.json"

# 数据路径（需要根据实际情况修改）
AP_DIR=r"K:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\image\ap"
HBP_DIR=r"K:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\image\hbp"

# 检查配置文件是否存在
if [ ! -f "$CONFIG_PATH" ]; then
    echo "配置文件不存在: $CONFIG_PATH"
    exit 1
fi

# 检查数据目录是否存在
if [ ! -d "$AP_DIR" ]; then
    echo "AP图像目录不存在: $AP_DIR"
    echo "请修改脚本中的数据路径"
    exit 1
fi

if [ ! -d "$HBP_DIR" ]; then
    echo "HBP图像目录不存在: $HBP_DIR"
    echo "请修改脚本中的数据路径"
    exit 1
fi

# 更新配置文件中的数据路径
python -c "
import json
with open('$CONFIG_PATH', 'r') as f:
    config = json.load(f)
config['data']['ap_dir'] = '$AP_DIR'
config['data']['hbp_dir'] = '$HBP_DIR'
with open('$CONFIG_PATH', 'w') as f:
    json.dump(config, f, indent=2)
"

echo "开始训练医学图像扩散模型..."
echo "AP图像目录: $AP_DIR"
echo "HBP图像目录: $HBP_DIR"
echo "配置文件: $CONFIG_PATH"

# 开始训练
python train.py --config $CONFIG_PATH

echo "训练完成!"