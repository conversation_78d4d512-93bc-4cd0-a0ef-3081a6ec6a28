#!/usr/bin/env python
# coding: utf-8

# # Minimal example on how to transform an image, using TransformixFilter with TranslationTransform

# This example shows how to transform an image, using `itk.TransformixFilter` with `itk.TranslationTransform`.
# 
# Start by importing packages and creating an TransformixFilter object:

# In[1]:


import itk
import numpy as np

print('Creating a TransformixFilter (might take a while, please wait)...\n')
ImageType = itk.Image[itk.F, 2]
transformix_filter = itk.TransformixFilter[ImageType].New()


# Create the example input: a small (5x6) image, a translation (1, -2), and a rather trivial parameter map.
# 

# In[2]:


number_of_columns = 5
number_of_rows = 6

moving_image = ImageType.New()
moving_image.SetRegions([number_of_columns, number_of_rows])
moving_image.Allocate(True)

# Set the pixel values consecutively to 1, 2, 3, ..., n.
moving_image[:] =  np.arange(1, number_of_columns*number_of_rows + 1).reshape((number_of_rows, number_of_columns))
                            
print('Moving image:')
print(np.asarray(moving_image))
print()

translation = [1, -2]
print('Translation:', translation)

transform = itk.TranslationTransform.New()
transform.SetOffset(translation)

parameter_map = {
                 "Direction": ("1", "0", "0", "1"),
                 "Index": ("0", "0"),
                 "Origin": ("0", "0"),
                 "Size": (str(number_of_columns), str(number_of_rows)),
                 "Spacing": ("1", "1")
                }


# Pass the example input to the TransformixFilter object, apply the transformation, and retrieve the output image. Ready!

# In[3]:


parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterMap(parameter_map)

transformix_filter.SetMovingImage(moving_image)
transformix_filter.SetTransformParameterObject(parameter_object)
transformix_filter.SetTransform(transform)
transformix_filter.Update()

output_image = transformix_filter.GetOutput()

print('Output image:')
print(np.asarray(output_image))
print()

