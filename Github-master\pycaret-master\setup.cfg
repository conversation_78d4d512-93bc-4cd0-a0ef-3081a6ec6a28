[isort]
profile = black

[flake8]
# TODO: Back to 89
max-line-length = 300
ignore =
    # Assign a lambda expression
    E731  
    # Line break before binary operator
    W503  
    # Invalid escape sequence (/ in docstrings)
    W605  
    # Whitespace before ':' (due to black)
    E203  
per-file-ignores =
    # Imported but unused
    __init__.py: F401
    pycaret/distributions.py: F401
    pycaret/internal/preprocess/iterative_imputer.py: F401
    pycaret/internal/display/display_backend.py: F401
    # Redefinition of unused imports
    pycaret/containers/models/classification.py: F811
    pycaret/containers/models/clustering.py: F811
    pycaret/containers/models/regression.py: F811
    pycaret/containers/models/time_series.py: F811
    # Local variable is assigned to but never used
    tests/test_time_series_mlflow.py: F841

[tool:pytest]
testpaths = tests/
python_files = *.py
python_functions = test_*
markers =
    benchmark
    plotting
    tuning_grid
    tuning_random
