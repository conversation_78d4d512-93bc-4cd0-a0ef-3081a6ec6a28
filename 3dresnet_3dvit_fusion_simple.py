"""
3D ResNet和3D ViT模型融合进行3D图像3分类 - 简化版
基于您现有代码结构，提供更简洁易用的实现
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import pandas as pd
import numpy as np
from torchvision.models.video import r3d_18
import monai
from monai.data import DataLoader, Dataset
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, 
    RandRotate90d, RandFlipd, Compose, EnsureTyped
)
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from einops import rearrange

# 设置环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# ==================== 简化的3D ViT ====================
class Simple3DViT(nn.Module):
    """简化的3D Vision Transformer"""
    def __init__(self, image_size=(96, 96, 64), patch_size=16, dim=512, depth=6, heads=8, mlp_dim=1024, num_classes=3):
        super().__init__()
        
        # 计算patch数量
        self.patch_size = patch_size
        num_patches = (image_size[0] // patch_size) * (image_size[1] // patch_size) * (image_size[2] // patch_size)
        patch_dim = patch_size ** 3
        
        # Patch embedding
        self.patch_embedding = nn.Linear(patch_dim, dim)
        self.pos_embedding = nn.Parameter(torch.randn(1, num_patches + 1, dim))
        self.cls_token = nn.Parameter(torch.randn(1, 1, dim))
        
        # Transformer blocks
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=dim,
                nhead=heads,
                dim_feedforward=mlp_dim,
                dropout=0.1,
                batch_first=True
            ),
            num_layers=depth
        )
        
        # Classification head
        self.norm = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, num_classes)
        
    def forward(self, x, return_features=False):
        B, C, H, W, D = x.shape
        p = self.patch_size
        
        # 将3D图像分割成patches
        x = rearrange(x, 'b c (h p1) (w p2) (d p3) -> b (h w d) (p1 p2 p3 c)', p1=p, p2=p, p3=p)
        
        # Patch embedding
        x = self.patch_embedding(x)
        
        # 添加cls token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)
        
        # 添加位置编码
        x += self.pos_embedding
        
        # Transformer
        x = self.transformer(x)
        
        # 取cls token的输出
        features = self.norm(x[:, 0])
        
        if return_features:
            return features
            
        return self.head(features)

# ==================== 3D ResNet特征提取器 ====================
class ResNet3DExtractor(nn.Module):
    """3D ResNet特征提取器"""
    def __init__(self, pretrained=True):
        super().__init__()
        # 使用预训练的3D ResNet
        self.backbone = r3d_18(pretrained=pretrained)
        # 移除分类层
        self.backbone.fc = nn.Identity()
        
        # 冻结前面的层
        for param in list(self.backbone.parameters())[:-20]:
            param.requires_grad = False
            
    def forward(self, x):
        # 确保输入是3通道
        if x.size(1) == 1:
            x = x.expand(-1, 3, -1, -1, -1)
        return self.backbone(x)

# ==================== 融合模型 ====================
class ResNetViTFusion(nn.Module):
    """ResNet和ViT融合模型"""
    def __init__(self, num_classes=3, image_size=(96, 96, 64), fusion_method='concat'):
        super().__init__()
        
        # 特征提取器
        self.resnet_extractor = ResNet3DExtractor(pretrained=True)
        self.vit_extractor = Simple3DViT(
            image_size=image_size,
            patch_size=16,
            dim=512,
            depth=6,
            heads=8,
            mlp_dim=1024,
            num_classes=num_classes
        )
        
        self.fusion_method = fusion_method
        
        # 特征适配层
        self.resnet_adapter = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.vit_adapter = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 融合后的分类器
        if fusion_method == 'concat':
            classifier_input_dim = 512  # 256 + 256
        else:  # add or attention
            classifier_input_dim = 256
            
        self.classifier = nn.Sequential(
            nn.Linear(classifier_input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(128, num_classes)
        )
        
        # 注意力融合权重
        if fusion_method == 'attention':
            self.attention = nn.Sequential(
                nn.Linear(512, 128),
                nn.ReLU(),
                nn.Linear(128, 2),
                nn.Softmax(dim=1)
            )
    
    def forward(self, x):
        # ResNet特征提取
        resnet_features = self.resnet_extractor(x)
        resnet_features = self.resnet_adapter(resnet_features)
        
        # ViT特征提取
        vit_features = self.vit_extractor(x, return_features=True)
        vit_features = self.vit_adapter(vit_features)
        
        # 特征融合
        if self.fusion_method == 'concat':
            fused_features = torch.cat([resnet_features, vit_features], dim=1)
        elif self.fusion_method == 'add':
            fused_features = resnet_features + vit_features
        elif self.fusion_method == 'attention':
            concat_features = torch.cat([resnet_features, vit_features], dim=1)
            weights = self.attention(concat_features)
            fused_features = weights[:, 0:1] * resnet_features + weights[:, 1:2] * vit_features
        
        # 分类
        output = self.classifier(fused_features)
        return output

# ==================== 数据处理 ====================
def create_dataset(data_dir, label_excel, label_column='label'):
    """创建数据集"""
    df = pd.read_excel(label_excel)
    
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    data = []
    for img_path in image_files:
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]
        file_names = file_name.split('-')[0]
        
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_column].values[0]
            data.append({"image": img_path, "label": label, "file_name": file_names})
    
    return data

def get_transforms(image_size=(96, 96, 64)):
    """获取数据变换"""
    train_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=image_size),
        ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=300, b_min=0.0, b_max=1.0, clip=True),
        RandRotate90d(keys=["image"], prob=0.3, spatial_axes=[0, 1]),
        RandFlipd(keys=["image"], prob=0.5, spatial_axis=0),
        EnsureTyped(keys=["image"])
    ])

    val_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=image_size),
        ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=300, b_min=0.0, b_max=1.0, clip=True),
        EnsureTyped(keys=["image"])
    ])
    
    return train_transforms, val_transforms

# ==================== 训练函数 ====================
def train_model(model, train_loader, val_loader, num_epochs=50, device='cuda'):
    """训练模型"""
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # 记录训练历史
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    best_acc = 0.0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch in train_loader:
            images = batch['image'].to(device)
            labels = batch['label'].long().to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device)
                labels = batch['label'].long().to(device)
                
                outputs = model(images)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        # 计算指标
        train_loss = train_loss / len(train_loader)
        train_acc = 100 * train_correct / train_total
        val_loss = val_loss / len(val_loader)
        val_acc = 100 * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        # 更新学习率
        scheduler.step()
        
        print(f'Epoch [{epoch+1}/{num_epochs}]:')
        print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            torch.save(model.state_dict(), 'best_fusion_model.pth')
            print(f'Best model saved! Val Acc: {best_acc:.2f}%')
        
        print('-' * 50)
    
    return history, best_acc

# ==================== 可视化 ====================
def plot_history(history):
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 损失曲线
    ax1.plot(history['train_loss'], label='Train Loss')
    ax1.plot(history['val_loss'], label='Val Loss')
    ax1.set_title('Loss History')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    
    # 准确率曲线
    ax2.plot(history['train_acc'], label='Train Acc')
    ax2.plot(history['val_acc'], label='Val Acc')
    ax2.set_title('Accuracy History')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    
    plt.tight_layout()
    plt.show()

# ==================== 使用示例 ====================
def main():
    """主函数 - 使用示例"""
    
    # 配置参数
    DATA_DIR = '/path/to/your/data'  # 修改为您的数据路径
    LABEL_EXCEL = '/path/to/your/labels.xlsx'  # 修改为您的标签文件
    LABEL_COLUMN = 'label'  # 标签列名
    IMAGE_SIZE = (96, 96, 64)  # 图像尺寸
    BATCH_SIZE = 4  # 批次大小
    NUM_EPOCHS = 50  # 训练轮数
    FUSION_METHOD = 'attention'  # 融合方法: 'concat', 'add', 'attention'
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # 创建数据集
    all_data = create_dataset(DATA_DIR, LABEL_EXCEL, LABEL_COLUMN)
    print(f'Total samples: {len(all_data)}')
    
    # 划分数据集
    train_data, val_data = train_test_split(
        all_data, test_size=0.2, random_state=42,
        stratify=[d["label"] for d in all_data]
    )
    
    # 获取数据变换
    train_transforms, val_transforms = get_transforms(IMAGE_SIZE)
    
    # 创建数据加载器
    train_dataset = Dataset(data=train_data, transform=train_transforms)
    val_dataset = Dataset(data=val_data, transform=val_transforms)
    
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=2)
    
    print(f'Train samples: {len(train_dataset)}')
    print(f'Val samples: {len(val_dataset)}')
    
    # 创建模型
    model = ResNetViTFusion(
        num_classes=3,
        image_size=IMAGE_SIZE,
        fusion_method=FUSION_METHOD
    ).to(device)
    
    print(f'Model parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}')
    
    # 训练模型
    print('Starting training...')
    history, best_acc = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=NUM_EPOCHS,
        device=device
    )
    
    print(f'Training completed! Best validation accuracy: {best_acc:.2f}%')
    
    # 绘制训练历史
    plot_history(history)
    
    # 保存训练历史
    history_df = pd.DataFrame(history)
    history_df.to_excel('training_history.xlsx', index=False)
    print('Training history saved to training_history.xlsx')

if __name__ == "__main__":
    main()