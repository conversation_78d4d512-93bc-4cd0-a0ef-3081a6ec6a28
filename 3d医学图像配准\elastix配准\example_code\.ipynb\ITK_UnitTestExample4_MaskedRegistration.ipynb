{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def image_generator(x1, x2, y1, y2, mask=False, artefact=False):\n", "    if mask:\n", "        image = np.zeros([100, 100], np.uint8)\n", "    else:\n", "        image = np.zeros([100, 100], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    if artefact:\n", "        image[-10:, :] = 1\n", "    image = itk.image_view_from_array(image)\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Masked Registration Test"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Create rigid transformed test images with artefact\n", "fixed_image = image_generator(25, 75, 25, 75, artefact=True)\n", "moving_image = image_generator(1, 51, 10, 60, artefact=True)\n", "\n", "# Create mask for artefact\n", "fixed_mask = image_generator(0, 100, 0, 90, mask=True)\n", "moving_mask = image_generator(0, 100, 0, 90, mask=True)\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(default_rigid_parameter_map)\n", "\n", "# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object,\n", "    fixed_mask=fixed_mask,\n", "    moving_mask=moving_mask)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Masked Registration Test Visualization"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 2160x2160 with 5 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(1,5, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0].imshow(fixed_mask)\n", "axs[0].set_title('Fixed Mask', fontsize=30)\n", "axs[1].imshow(moving_mask)\n", "axs[1].set_title('Moving Mask', fontsize=30)\n", "axs[2].imshow(fixed_image)\n", "axs[2].set_title('Fixed', fontsize=30)\n", "axs[3].imshow(moving_image)\n", "axs[3].set_title('Moving', fontsize=30)\n", "axs[4].imshow(result_image)\n", "axs[4].set_title('Result', fontsize=30)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}