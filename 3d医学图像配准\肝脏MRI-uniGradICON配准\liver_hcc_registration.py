#!/usr/bin/env python3
"""
HCC肝脏多序列MRI配准脚本
使用uniGradICON进行HCC数据集的肝脏多序列MRI图像配准
支持ap（动脉期）、pp（门静脉期）、hbp（肝胆期）三个序列的配准
"""

import os
import argparse
import subprocess
import numpy as np
import nibabel as nib
from pathlib import Path
import logging
from typing import List, Dict, Tuple, Optional
import json
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiverHCCRegistration:
    """HCC肝脏多序列MRI配准类"""
    
    def __init__(self, data_root: str = "/root/autodl-tmp/120HCC/image", 
                 output_dir: str = "/root/autodl-tmp/3d MRI配准/results"):
        """
        初始化配准器
        
        Args:
            data_root: 120HCC数据根目录
            output_dir: 输出目录
        """
        self.data_root = Path(data_root)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # HCC数据集的序列类型
        self.sequences = ['ap', 'pp', 'hbp']  # 动脉期、门静脉期、肝胆期
        
        # 配准参数
        self.registration_params = {
            'model': 'unigradicon',  # 或 'multigradicon'
            'io_iterations': 50,
            'io_sim': 'lncc2',  # lncc, lncc2, mind
            'modality': 'mri'
        }
    
    def check_unigradicon_installation(self) -> bool:
        """检查uniGradICON是否已安装"""
        try:
            result = subprocess.run(['unigradicon-register', '--help'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def install_unigradicon(self):
        """安装uniGradICON"""
        logger.info("正在安装uniGradICON...")
        try:
            subprocess.run(['pip', 'install', 'unigradicon'], check=True)
            logger.info("uniGradICON安装成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"uniGradICON安装失败: {e}")
            raise
    
    def get_patient_list(self) -> List[str]:
        """获取患者列表"""
        # 从ap目录获取所有患者名称
        ap_dir = self.data_root / 'ap'
        if not ap_dir.exists():
            logger.error(f"AP序列目录不存在: {ap_dir}")
            return []
        
        patients = []
        for file_path in ap_dir.glob('*-ap.nii.gz'):
            # 提取患者名称（去掉-ap.nii.gz后缀）
            patient_name = file_path.stem.replace('-ap', '').replace('.nii', '')
            patients.append(patient_name)
        
        logger.info(f"找到 {len(patients)} 个患者")
        return sorted(patients)
    
    def get_patient_sequences(self, patient_name: str) -> Dict[str, str]:
        """
        获取患者的所有序列文件路径
        
        Args:
            patient_name: 患者名称
            
        Returns:
            序列字典 {序列名: 文件路径}
        """
        sequences = {}
        
        for seq in self.sequences:
            seq_dir = self.data_root / seq
            seq_file = seq_dir / f"{patient_name}-{seq}.nii.gz"
            
            if seq_file.exists():
                sequences[seq] = str(seq_file)
            else:
                logger.warning(f"患者 {patient_name} 缺少 {seq} 序列: {seq_file}")
        
        return sequences
    
    def validate_image(self, image_path: str) -> bool:
        """验证图像文件是否有效"""
        try:
            img = nib.load(image_path)
            if len(img.shape) != 3:
                logger.warning(f"图像 {image_path} 不是3D图像")
                return False
            return True
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            return False
    
    def register_sequence_pair(self, fixed_path: str, moving_path: str, 
                             fixed_seq: str, moving_seq: str,
                             patient_name: str) -> Dict[str, str]:
        """
        配准两个序列
        
        Args:
            fixed_path: 固定图像路径
            moving_path: 移动图像路径
            fixed_seq: 固定图像序列类型
            moving_seq: 移动图像序列类型
            patient_name: 患者名称
            
        Returns:
            包含输出文件路径的字典
        """
        # 创建患者输出目录
        patient_output_dir = self.output_dir / patient_name
        patient_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建输出文件名
        pair_name = f"{moving_seq}_to_{fixed_seq}"
        transform_out = patient_output_dir / f"transform_{pair_name}.hdf5"
        warped_out = patient_output_dir / f"warped_{patient_name}_{pair_name}.nii.gz"
        
        # 构建配准命令
        cmd = [
            'unigradicon-register',
            '--fixed', fixed_path,
            '--fixed_modality', self.registration_params['modality'],
            '--moving', moving_path,
            '--moving_modality', self.registration_params['modality'],
            '--transform_out', str(transform_out),
            '--warped_moving_out', str(warped_out),
            '--model', self.registration_params['model'],
            '--io_iterations', str(self.registration_params['io_iterations']),
            '--io_sim', self.registration_params['io_sim']
        ]
        
        logger.info(f"开始配准患者 {patient_name}: {moving_seq} -> {fixed_seq}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"配准完成: {patient_name} {pair_name}")
            
            return {
                'patient_name': patient_name,
                'transform': str(transform_out),
                'warped_image': str(warped_out),
                'fixed_sequence': fixed_seq,
                'moving_sequence': moving_seq,
                'status': 'success'
            }
        except subprocess.CalledProcessError as e:
            logger.error(f"配准失败 {patient_name} {pair_name}: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return {
                'patient_name': patient_name,
                'transform': None,
                'warped_image': None,
                'fixed_sequence': fixed_seq,
                'moving_sequence': moving_seq,
                'status': 'failed',
                'error': str(e)
            }
    
    def register_patient(self, patient_name: str, reference_sequence: str = 'ap') -> Dict[str, any]:
        """
        配准单个患者的所有序列
        
        Args:
            patient_name: 患者名称
            reference_sequence: 参考序列（默认为ap动脉期）
            
        Returns:
            配准结果字典
        """
        logger.info(f"开始处理患者: {patient_name}")
        start_time = time.time()
        
        # 获取患者的所有序列
        sequences = self.get_patient_sequences(patient_name)
        
        if len(sequences) < 2:
            return {
                'patient_name': patient_name,
                'status': 'failed',
                'message': f'序列数量不足: {len(sequences)} < 2',
                'sequences': list(sequences.keys())
            }
        
        # 检查参考序列是否存在
        if reference_sequence not in sequences:
            # 如果参考序列不存在，使用第一个可用序列
            reference_sequence = list(sequences.keys())[0]
            logger.warning(f"患者 {patient_name} 缺少参考序列，使用 {reference_sequence}")
        
        # 验证所有图像
        for seq_name, seq_path in sequences.items():
            if not self.validate_image(seq_path):
                return {
                    'patient_name': patient_name,
                    'status': 'failed',
                    'message': f'图像验证失败: {seq_name}',
                    'sequences': list(sequences.keys())
                }
        
        results = {
            'patient_name': patient_name,
            'reference_sequence': reference_sequence,
            'sequences': list(sequences.keys()),
            'registrations': [],
            'status': 'success'
        }
        
        reference_path = sequences[reference_sequence]
        
        # 配准每个序列到参考序列
        for seq_name, seq_path in sequences.items():
            if seq_name == reference_sequence:
                continue
            
            reg_result = self.register_sequence_pair(
                fixed_path=reference_path,
                moving_path=seq_path,
                fixed_seq=reference_sequence,
                moving_seq=seq_name,
                patient_name=patient_name
            )
            
            results['registrations'].append(reg_result)
        
        # 统计结果
        successful = sum(1 for r in results['registrations'] if r['status'] == 'success')
        total = len(results['registrations'])
        
        if successful == 0:
            results['status'] = 'failed'
            results['message'] = '所有配准都失败了'
        elif successful < total:
            results['status'] = 'partial'
            results['message'] = f'部分配准成功: {successful}/{total}'
        
        results['success_rate'] = successful / total if total > 0 else 0
        results['processing_time'] = time.time() - start_time
        
        # 保存患者结果
        patient_output_dir = self.output_dir / patient_name
        results_file = patient_output_dir / f'{patient_name}_registration_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"患者 {patient_name} 处理完成，成功率: {successful}/{total}")
        
        return results
    
    def batch_register_all_patients(self, reference_sequence: str = 'ap', 
                                   max_patients: int = None) -> Dict[str, any]:
        """
        批量配准所有患者
        
        Args:
            reference_sequence: 参考序列
            max_patients: 最大处理患者数（用于测试）
            
        Returns:
            批量处理结果
        """
        patients = self.get_patient_list()
        
        if max_patients:
            patients = patients[:max_patients]
            logger.info(f"限制处理患者数量: {max_patients}")
        
        if not patients:
            return {
                'status': 'failed',
                'message': '未找到患者数据'
            }
        
        batch_results = {
            'total_patients': len(patients),
            'processed_patients': 0,
            'successful_patients': 0,
            'failed_patients': 0,
            'partial_patients': 0,
            'results': [],
            'start_time': time.time(),
            'reference_sequence': reference_sequence
        }
        
        logger.info(f"开始批量处理 {len(patients)} 个患者")
        
        for i, patient_name in enumerate(patients, 1):
            logger.info(f"进度: {i}/{len(patients)} - 处理患者: {patient_name}")
            
            try:
                result = self.register_patient(patient_name, reference_sequence)
                batch_results['results'].append(result)
                batch_results['processed_patients'] += 1
                
                if result['status'] == 'success':
                    batch_results['successful_patients'] += 1
                elif result['status'] == 'partial':
                    batch_results['partial_patients'] += 1
                else:
                    batch_results['failed_patients'] += 1
                    
            except Exception as e:
                logger.error(f"患者 {patient_name} 处理异常: {e}")
                batch_results['results'].append({
                    'patient_name': patient_name,
                    'status': 'error',
                    'message': str(e)
                })
                batch_results['failed_patients'] += 1
                batch_results['processed_patients'] += 1
        
        # 计算总处理时间
        batch_results['total_time'] = time.time() - batch_results['start_time']
        
        # 保存批量处理结果
        batch_results_file = self.output_dir / 'batch_registration_results.json'
        with open(batch_results_file, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False)
        
        # 打印总结
        self.print_batch_summary(batch_results)
        
        return batch_results
    
    def print_batch_summary(self, batch_results: Dict):
        """打印批量处理总结"""
        print("\n" + "="*60)
        print("HCC肝脏多序列MRI配准完成!")
        print("="*60)
        print(f"总患者数: {batch_results['total_patients']}")
        print(f"完全成功: {batch_results['successful_patients']}")
        print(f"部分成功: {batch_results['partial_patients']}")
        print(f"处理失败: {batch_results['failed_patients']}")
        print(f"总成功率: {(batch_results['successful_patients'] + batch_results['partial_patients'])/batch_results['total_patients']:.1%}")
        print(f"参考序列: {batch_results['reference_sequence']}")
        print(f"总处理时间: {batch_results['total_time']:.1f} 秒")
        print(f"平均处理时间: {batch_results['total_time']/batch_results['total_patients']:.1f} 秒/患者")
        print(f"输出目录: {self.output_dir}")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='HCC肝脏多序列MRI配准')
    parser.add_argument('--data_root', type=str, 
                       default='/root/autodl-tmp/120HCC/image',
                       help='HCC数据根目录')
    parser.add_argument('--output_dir', type=str, 
                       default='/root/autodl-tmp/3d MRI配准/results',
                       help='输出目录')
    parser.add_argument('--reference_sequence', type=str, default='pp',
                       choices=['ap', 'pp', 'hbp'],
                       help='参考序列 (ap=动脉期, pp=门静脉期, hbp=肝胆期)')
    parser.add_argument('--patient_name', type=str, default=None,
                       help='处理单个患者（可选）')
    parser.add_argument('--max_patients', type=int, default=None,
                       help='最大处理患者数（用于测试）')
    parser.add_argument('--model', type=str, default='unigradicon',
                       choices=['unigradicon', 'multigradicon'],
                       help='使用的模型')
    parser.add_argument('--io_iterations', type=int, default=50,
                       help='实例优化迭代次数')
    parser.add_argument('--similarity', type=str, default='lncc2',
                       choices=['lncc', 'lncc2', 'mind'],
                       help='相似性度量')
    
    args = parser.parse_args()
    
    # 创建配准器
    registrator = LiverHCCRegistration(args.data_root, args.output_dir)
    
    # 更新配准参数
    registrator.registration_params.update({
        'model': args.model,
        'io_iterations': args.io_iterations,
        'io_sim': args.similarity
    })
    
    # 检查并安装uniGradICON
    if not registrator.check_unigradicon_installation():
        registrator.install_unigradicon()
    
    try:
        if args.patient_name:
            # 处理单个患者
            logger.info(f"处理单个患者: {args.patient_name}")
            result = registrator.register_patient(args.patient_name, args.reference_sequence)
            
            if result['status'] == 'success':
                print(f"\n✓ 患者 {args.patient_name} 配准成功!")
            else:
                print(f"\n✗ 患者 {args.patient_name} 配准失败: {result.get('message', '未知错误')}")
        else:
            # 批量处理所有患者
            results = registrator.batch_register_all_patients(
                reference_sequence=args.reference_sequence,
                max_patients=args.max_patients
            )
            
            if results['successful_patients'] > 0:
                print(f"\n✓ 批量处理完成，成功处理 {results['successful_patients']} 个患者")
            else:
                print("\n✗ 批量处理完成，但没有成功处理任何患者")
                
    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()


# python liver_hcc_registration.py --max_patients 2