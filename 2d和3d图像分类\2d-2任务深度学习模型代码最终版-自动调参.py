# ...existing code...

#%%保存图片路径、预测概率、真实的label、预测label
import torch
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix

# 创建数据加载器，不需要使用indices
new_train_dl = torch.utils.data.DataLoader(train_dataset, batch_size=16, shuffle=False)
test_dl = torch.utils.data.DataLoader(test_dataset, batch_size=16, shuffle=False)

# 直接使用dataset的imgs_path属性
train_image_paths = train_dataset.imgs_path
test_image_paths = test_dataset.imgs_path

def get_predictions(model, dataloader, image_paths):
    model.eval()
    true_label1 = []
    true_label2 = []
    predict_label1 = []
    predict_label2 = []
    predict_prob1 = []
    predict_prob2 = []
    
    with torch.no_grad():
        for x, (y1, y2) in dataloader:
            if torch.cuda.is_available():
                x = x.to('cuda')
                y1 = y1.to('cuda')
                y2 = y2.to('cuda')
            
            y_pred1, y_pred2 = model(x)
            _, y_pred_class1 = torch.max(y_pred1, dim=1)
            _, y_pred_class2 = torch.max(y_pred2, dim=1)
            
            # 获取预测概率
            y_pred_prob1 = torch.softmax(y_pred1, dim=1)
            y_pred_prob2 = torch.softmax(y_pred2, dim=1)
            
            true_label1.extend(y1.cpu().tolist())
            true_label2.extend(y2.cpu().tolist())
            predict_label1.extend(y_pred_class1.cpu().tolist())
            predict_label2.extend(y_pred_class2.cpu().tolist())
            predict_prob1.extend(y_pred_prob1.cpu().tolist())
            predict_prob2.extend(y_pred_prob2.cpu().tolist())
    
    return true_label1, true_label2, predict_label1, predict_label2, predict_prob1, predict_prob2

# 获取预测结果
train_true_label1, train_true_label2, train_predict_label1, train_predict_label2, train_predict_prob1, train_predict_prob2 = get_predictions(model, new_train_dl, train_image_paths)
test_true_label1, test_true_label2, test_predict_label1, test_predict_label2, test_predict_prob1, test_predict_prob2 = get_predictions(model, test_dl, test_image_paths)

# 创建结果DataFrame
train_data = {
    'Image Path': train_image_paths,
    'True Label1': train_true_label1,
    'Predict Label1': train_predict_label1,
    'True Label2': train_true_label2,
    'Predict Label2': train_predict_label2,
    'Predict Prob1': train_predict_prob1,
    'Predict Prob2': train_predict_prob2
}

test_data = {
    'Image Path': test_image_paths,
    'True Label1': test_true_label1,
    'Predict Label1': test_predict_label1,
    'True Label2': test_true_label2,
    'Predict Label2': test_predict_label2,
    'Predict Prob1': test_predict_prob1,
    'Predict Prob2': test_predict_prob2
}

# 保存结果
train_results = pd.DataFrame(train_data)
test_results = pd.DataFrame(test_data)

train_results.to_excel(r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\train.xlsx", index=False)
test_results.to_excel(r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\test.xlsx", index=False)

# %%
