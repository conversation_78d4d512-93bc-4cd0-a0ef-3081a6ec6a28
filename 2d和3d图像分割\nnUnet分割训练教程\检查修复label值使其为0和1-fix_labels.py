#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和修复标签文件中的异常值
确保标签文件只包含0（背景）和1（HCC）
"""

import os
import numpy as np
import nibabel as nib
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('label_fix_log.txt', encoding='utf-8')
        ]
    )

def check_label_values(label_path):
    """
    检查标签文件中的唯一值
    
    Args:
        label_path (str): 标签文件路径
        
    Returns:
        tuple: (unique_values, needs_fix)
    """
    try:
        # 加载标签文件
        label_img = nib.load(label_path)
        label_data = label_img.get_fdata()
        
        # 获取唯一值
        unique_values = np.unique(label_data)
        
        # 检查是否只包含0和1
        expected_values = {0, 1}
        actual_values = set(unique_values)
        
        needs_fix = not actual_values.issubset(expected_values)
        
        return unique_values, needs_fix
        
    except Exception as e:
        logging.error(f"读取标签文件失败 {label_path}: {e}")
        return None, False

def fix_label_values(label_path, backup=True):
    """
    修复标签文件中的异常值
    将所有非0值设为1
    
    Args:
        label_path (str): 标签文件路径
        backup (bool): 是否创建备份
        
    Returns:
        bool: 修复是否成功
    """
    try:
        # 创建备份
        if backup:
            backup_path = label_path.replace('.nii.gz', '_backup.nii.gz')
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(label_path, backup_path)
                logging.info(f"创建备份: {backup_path}")
        
        # 加载标签文件
        label_img = nib.load(label_path)
        label_data = label_img.get_fdata()
        
        # 记录原始唯一值
        original_values = np.unique(label_data)
        logging.info(f"原始标签值: {original_values}")
        
        # 修复标签值：将所有非0值设为1
        fixed_data = np.where(label_data > 0, 1, 0).astype(np.uint8)
        
        # 检查修复后的值
        fixed_values = np.unique(fixed_data)
        logging.info(f"修复后标签值: {fixed_values}")
        
        # 保存修复后的文件
        fixed_img = nib.Nifti1Image(fixed_data, label_img.affine, label_img.header)
        nib.save(fixed_img, label_path)
        
        logging.info(f"标签文件修复成功: {label_path}")
        return True
        
    except Exception as e:
        logging.error(f"修复标签文件失败 {label_path}: {e}")
        return False

def process_label_directory(label_dir):
    """
    处理标签目录中的所有文件
    
    Args:
        label_dir (str): 标签目录路径
    """
    if not os.path.exists(label_dir):
        logging.error(f"标签目录不存在: {label_dir}")
        return
    
    # 获取所有.nii.gz文件
    label_files = list(Path(label_dir).glob("*.nii.gz"))
    
    if not label_files:
        logging.warning(f"在目录 {label_dir} 中未找到.nii.gz文件")
        return
    
    logging.info(f"找到 {len(label_files)} 个标签文件")
    
    problem_files = []
    fixed_files = []
    
    # 检查每个标签文件
    for label_file in label_files:
        label_path = str(label_file)
        filename = label_file.name
        
        logging.info(f"检查文件: {filename}")
        
        # 检查标签值
        unique_values, needs_fix = check_label_values(label_path)
        
        if unique_values is None:
            continue
            
        if needs_fix:
            logging.warning(f"发现异常标签值 {filename}: {unique_values}")
            problem_files.append((filename, unique_values))
            
            # 尝试修复
            if fix_label_values(label_path):
                fixed_files.append(filename)
            else:
                logging.error(f"修复失败: {filename}")
        else:
            logging.info(f"标签值正常 {filename}: {unique_values}")
    
    # 输出总结
    logging.info(f"\n=== 处理总结 ===")
    logging.info(f"总文件数: {len(label_files)}")
    logging.info(f"问题文件数: {len(problem_files)}")
    logging.info(f"成功修复数: {len(fixed_files)}")
    
    if problem_files:
        logging.info(f"\n问题文件列表:")
        for filename, values in problem_files:
            logging.info(f"  {filename}: {values}")
    
    if fixed_files:
        logging.info(f"\n成功修复的文件:")
        for filename in fixed_files:
            logging.info(f"  {filename}")

def main():
    """主函数"""
    setup_logging()
    
    # 标签目录路径
    label_directory = r"K:\肝脏MRI数据集\HCC新增待整理\84HCC\mask\T2"
    
    logging.info("开始检查和修复标签文件...")
    logging.info(f"标签目录: {label_directory}")
    
    # 处理标签目录
    process_label_directory(label_directory)
    
    logging.info("标签文件检查和修复完成!")

if __name__ == "__main__":
    main()
