import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import vgg19
import numpy as np

class DiffusionLoss(nn.Module):
    """扩散模型损失函数"""
    def __init__(self, loss_type='l2'):
        super().__init__()
        self.loss_type = loss_type
        
    def forward(self, predicted, target):
        if self.loss_type == 'l1':
            return F.l1_loss(predicted, target)
        elif self.loss_type == 'l2':
            return F.mse_loss(predicted, target)
        elif self.loss_type == 'huber':
            return F.smooth_l1_loss(predicted, target)
        else:
            raise ValueError(f"Unknown loss type: {self.loss_type}")

class PerceptualLoss(nn.Module):
    """感知损失"""
    def __init__(self, feature_layers=None):
        super().__init__()
        
        if feature_layers is None:
            feature_layers = ['conv_4']
        
        self.feature_layers = feature_layers
        
        # 加载预训练VGG19
        vgg = vgg19(pretrained=True)
        self.vgg = vgg.features
        
        # 冻结VGG参数
        for param in self.vgg.parameters():
            param.requires_grad = False
        
        # 特征层映射
        self.layer_name_mapping = {
            'conv_1': 0, 'conv_2': 5, 'conv_3': 10,
            'conv_4': 19, 'conv_5': 28
        }
        
    def forward(self, predicted, target):
        # 转换为3通道（VGG需要3通道输入）
        if predicted.size(1) == 1:
            predicted = predicted.repeat(1, 3, 1, 1)
        if target.size(1) == 1:
            target = target.repeat(1, 3, 1, 1)
        
        # 标准化到[0,1]
        predicted = torch.clamp(predicted, 0, 1)
        target = torch.clamp(target, 0, 1)
        
        # ImageNet标准化
        mean = torch.tensor([0.485, 0.456, 0.406]).to(predicted.device)
        std = torch.tensor([0.229, 0.224, 0.225]).to(predicted.device)
        
        predicted = (predicted - mean.view(1, 3, 1, 1)) / std.view(1, 3, 1, 1)
        target = (target - mean.view(1, 3, 1, 1)) / std.view(1, 3, 1, 1)
        
        # 提取特征
        pred_features = self.extract_features(predicted)
        target_features = self.extract_features(target)
        
        # 计算损失
        loss = 0
        for layer in self.feature_layers:
            loss += F.mse_loss(pred_features[layer], target_features[layer])
        
        return loss / len(self.feature_layers)
    
    def extract_features(self, x):
        features = {}
        
        for layer_name, layer_idx in self.layer_name_mapping.items():
            x = self.vgg[layer_idx](x)
            features[layer_name] = x
            
            # 如果已经提取了所有需要的特征，提前退出
            if layer_name in self.feature_layers:
                if all(layer in features for layer in self.feature_layers):
                    break
        
        return features

class GANLoss(nn.Module):
    """GAN损失（如果需要对抗训练）"""
    def __init__(self, gan_mode='lsgan'):
        super().__init__()
        self.gan_mode = gan_mode
        
        if gan_mode == 'lsgan':
            self.loss = nn.MSELoss()
        elif gan_mode == 'vanilla':
            self.loss = nn.BCEWithLogitsLoss()
        elif gan_mode == 'wgangp':
            self.loss = None
        else:
            raise NotImplementedError(f'GAN mode {gan_mode} not implemented')
    
    def forward(self, prediction, target_is_real):
        if self.gan_mode == 'lsgan':
            target = torch.ones_like(prediction) if target_is_real else torch.zeros_like(prediction)
            loss = self.loss(prediction, target)
        elif self.gan_mode == 'vanilla':
            target = torch.ones_like(prediction) if target_is_real else torch.zeros_like(prediction)
            loss = self.loss(prediction, target)
        elif self.gan_mode == 'wgangp':
            if target_is_real:
                loss = -prediction.mean()
            else:
                loss = prediction.mean()
        
        return loss

class SSIMLoss(nn.Module):
    """SSIM损失"""
    def __init__(self, window_size=11, size_average=True):
        super().__init__()
        self.window_size = window_size
        self.size_average = size_average
        self.channel = 1
        self.window = self.create_window(window_size, self.channel)
        
    def forward(self, img1, img2):
        (_, channel, _, _) = img1.size()
        
        if channel == self.channel and self.window.data.type() == img1.data.type():
            window = self.window
        else:
            window = self.create_window(self.window_size, channel)
            
            if img1.is_cuda:
                window = window.cuda(img1.get_device())
            window = window.type_as(img1)
            
            self.window = window
            self.channel = channel
            
        return self._ssim(img1, img2, window, self.window_size, channel, self.size_average)
    
    def gaussian(self, window_size, sigma):
        gauss = torch.Tensor([np.exp(-(x - window_size//2)**2/float(2*sigma**2)) for x in range(window_size)])
        return gauss/gauss.sum()
    
    def create_window(self, window_size, channel):
        _1D_window = self.gaussian(window_size, 1.5).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window
    
    def _ssim(self, img1, img2, window, window_size, channel, size_average=True):
        mu1 = F.conv2d(img1, window, padding=window_size//2, groups=channel)
        mu2 = F.conv2d(img2, window, padding=window_size//2, groups=channel)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1*mu2
        
        sigma1_sq = F.conv2d(img1*img1, window, padding=window_size//2, groups=channel) - mu1_sq
        sigma2_sq = F.conv2d(img2*img2, window, padding=window_size//2, groups=channel) - mu2_sq
        sigma12 = F.conv2d(img1*img2, window, padding=window_size//2, groups=channel) - mu1_mu2
        
        C1 = 0.01**2
        C2 = 0.03**2
        
        ssim_map = ((2*mu1_mu2 + C1)*(2*sigma12 + C2))/((mu1_sq + mu2_sq + C1)*(sigma1_sq + sigma2_sq + C2))
        
        if size_average:
            return 1 - ssim_map.mean()
        else:
            return 1 - ssim_map.mean(1).mean(1).mean(1)

class CombinedLoss(nn.Module):
    """组合损失函数"""
    def __init__(self, loss_weights=None):
        super().__init__()
        
        if loss_weights is None:
            loss_weights = {
                'l1': 1.0,
                'perceptual': 0.1,
                'ssim': 0.5
            }
        
        self.loss_weights = loss_weights
        self.l1_loss = nn.L1Loss()
        self.perceptual_loss = PerceptualLoss()
        self.ssim_loss = SSIMLoss()
        
    def forward(self, predicted, target):
        total_loss = 0
        losses = {}
        
        if 'l1' in self.loss_weights:
            l1 = self.l1_loss(predicted, target)
            losses['l1'] = l1
            total_loss += self.loss_weights['l1'] * l1
        
        if 'perceptual' in self.loss_weights:
            perceptual = self.perceptual_loss(predicted, target)
            losses['perceptual'] = perceptual
            total_loss += self.loss_weights['perceptual'] * perceptual
        
        if 'ssim' in self.loss_weights:
            ssim = self.ssim_loss(predicted, target)
            losses['ssim'] = ssim
            total_loss += self.loss_weights['ssim'] * ssim
        
        losses['total'] = total_loss
        return total_loss, losses

class GradientPenalty(nn.Module):
    """梯度惩罚（用于WGAN-GP）"""
    def __init__(self, lambda_gp=10):
        super().__init__()
        self.lambda_gp = lambda_gp
        
    def forward(self, discriminator, real_samples, fake_samples, device):
        # 随机插值
        alpha = torch.rand(real_samples.size(0), 1, 1, 1).to(device)
        interpolates = (alpha * real_samples + (1 - alpha) * fake_samples).requires_grad_(True)
        
        # 计算判别器输出
        d_interpolates = discriminator(interpolates)
        
        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=d_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(d_interpolates),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # 计算梯度惩罚
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return self.lambda_gp * gradient_penalty

if __name__ == "__main__":
    # 测试损失函数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 测试数据
    predicted = torch.randn(4, 1, 256, 256).to(device)
    target = torch.randn(4, 1, 256, 256).to(device)
    
    # 测试各种损失函数
    diffusion_loss = DiffusionLoss()
    perceptual_loss = PerceptualLoss()
    ssim_loss = SSIMLoss()
    combined_loss = CombinedLoss()
    
    print(f"扩散损失: {diffusion_loss(predicted, target).item():.4f}")
    print(f"感知损失: {perceptual_loss(predicted, target).item():.4f}")
    print(f"SSIM损失: {ssim_loss(predicted, target).item():.4f}")
    
    total_loss, losses = combined_loss(predicted, target)
    print(f"组合损失: {total_loss.item():.4f}")
    for name, loss in losses.items():
        print(f"  {name}: {loss.item():.4f}")