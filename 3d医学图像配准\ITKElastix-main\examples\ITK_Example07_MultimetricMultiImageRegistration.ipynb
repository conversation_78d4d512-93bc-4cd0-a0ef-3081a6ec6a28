{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 7. Registration with multiple metrics and with multi-spectral images."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multimetric Registration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import itk"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterObject (000001852EB86E00)\n", "  RTTI typeinfo:   class elastix::ParameterObject\n", "  Reference Count: 1\n", "  Modified Time: 55\n", "  Debug: Off\n", "  Object Name: \n", "  Observers: \n", "    none\n", "ParameterMap 0: \n", "  (AutomaticScalesEstimation \"true\")\n", "  (AutomaticTransformInitialization \"true\")\n", "  (BSplineInterpolationOrder 1)\n", "  (DefaultPixelValue 0)\n", "  (<PERSON>rodeMask \"false\")\n", "  (FinalBSplineInterpolationOrder 3)\n", "  (FixedImagePyramid \"FixedSmoothingImagePyramid\" \"FixedSmoothingImagePyramid\")\n", "  (FixedInternalImagePixelType \"float\")\n", "  (HowToCombineTransforms \"Compose\")\n", "  (ImageSampler \"RandomCoordinate\" \"RandomCoordinate\")\n", "  (Interpolator \"BSplineInterpolator\" \"BSplineInterpolator\")\n", "  (MaximumNumberOfIterations 250)\n", "  (Metric \"AdvancedMattesMutualInformation\" \"AdvancedMeanSquares\")\n", "  (Metric0Weight 0.125)\n", "  (Metric1Weight 0.125)\n", "  (MovingImagePyramid \"MovingSmoothingImagePyramid\" \"MovingSmoothingImagePyramid\")\n", "  (MovingInternalImagePixelType \"float\")\n", "  (NewSamplesEveryIteration \"true\")\n", "  (NumberOfHistogramBins 32)\n", "  (NumberOfResolutions 4)\n", "  (NumberOfSpatialSamples 2048)\n", "  (Optimizer \"AdaptiveStochasticGradientDescent\")\n", "  (Registration \"MultiMetricMultiResolutionRegistration\")\n", "  (ResampleInterpolator \"FinalBSplineInterpolator\")\n", "  (Resampler \"DefaultResampler\")\n", "  (ResultImageFormat \"mhd\")\n", "  (ResultImagePixelType \"short\")\n", "  (Transform \"BSplineTransform\")\n", "  (UseDirectionCosines \"true\")\n", "  (WriteResultImage \"true\")\n", "\n"]}], "source": ["# Import Images\n", "fixed_image = itk.imread('data/CT_2D_head_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_2D_head_moving.mha', itk.F)\n", "\n", "# Import Multimetric Parameter Map (see elastix documentation, \n", "# KNNGraphAlphaMutualInformation is not supported yet by ITKElastix)\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_object.AddParameterFile('data/parameters_Bspline_Multimetric.txt')\n", "print(parameter_object)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Registration can either be done in one line with the registration function..."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object,\n", "    log_to_console=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": [".. or by initiating an elastix image filter object."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Load Elastix Image Filter Object\n", "elastix_object = itk.ElastixRegistrationMethod.New(fixed_image,moving_image)\n", "elastix_object.SetParameterObject(parameter_object)\n", "\n", "# Set additional options\n", "elastix_object.SetLogToConsole(False)\n", "\n", "# Update filter object (required)\n", "elastix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Registration\n", "result_image = elastix_object.GetOutput()\n", "result_transform_parameters = elastix_object.GetTransformParameterObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multi-spectral image registration"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Import Images of multiple spectra\n", "# Images of actual different spectra should be used here, for now same images are used.\n", "fixed_image_spectrum1 = itk.imread('data/CT_2D_head_fixed.mha', itk.F)\n", "moving_image_spectrum1 = itk.imread('data/CT_2D_head_moving.mha', itk.F)\n", "fixed_image_spectrum2 = itk.imread('data/CT_2D_head_fixed.mha', itk.F)\n", "moving_image_spectrum2 = itk.imread('data/CT_2D_head_moving.mha', itk.F)\n", "\n", "# Import Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "parameter_object.AddParameterFile('data/parameters_BSpline.txt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Multi-spectral registration can only be done with the object-oriented method in ITKElastix"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Load Elastix Image Filter Object\n", "elastix_object = itk.ElastixRegistrationMethod.New(fixed_image_spectrum1, moving_image_spectrum1)\n", "# elastix_object.SetFixedImage(fixed_image_spectrum1)\n", "elastix_object.AddFixedImage(fixed_image_spectrum2)\n", "\n", "# elastix_object.SetMovingImage(moving_image_spectrum1)\n", "elastix_object.AddMovingImage(moving_image_spectrum2)\n", "\n", "elastix_object.SetParameterObject(parameter_object)\n", "\n", "# Set additional options\n", "elastix_object.SetLogToConsole(False)\n", "\n", "# Update filter object (required)\n", "elastix_object.UpdateLargestPossibleRegion()\n", "\n", "# Results of Registration\n", "result_image = elastix_object.GetOutput()\n", "result_transform_parameters = elastix_object.GetTransformParameterObject()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}