#!/usr/bin/env python
# coding: utf-8

# # Working with NumPy Array Images

# In[1]:


import itk
import numpy as np
import imageio
from itkwidgets import view, compare, checkerboard


# Images as NumPy arrays can be registered.
# 
# Convert them to floating point arrays for registration.

# In[2]:


fixed = imageio.imread('data/CT_2D_head_fixed.mha')
fixed = np.asarray(fixed).astype(np.float32)
type(fixed)


# In[3]:


moving = imageio.imread('data/CT_2D_head_moving.mha')
moving = np.asarray(moving).astype(np.float32)
type(moving)


# In[4]:


compare(fixed, moving, ui_collapsed=True)


# Before registration, the moving image is not aligned with the fixed image.

# In[5]:


checkerboard(fixed, moving, pattern=10)


# In[6]:


# Register!
registered, parameters = itk.elastix_registration_method(fixed, moving)
type(registered)


# In[7]:


compare(fixed, registered, ui_collapsed=True)


# The registered moving image is aligned with the fixed image

# In[8]:


checkerboard(fixed, registered, pattern=10)


# In[ ]:




