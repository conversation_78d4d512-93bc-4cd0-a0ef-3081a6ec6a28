#%% https://monai.io/model-zoo
pip install "monai[fire]"
python -m monai.bundle download -h

# python -m monai.bundle download "spleen_ct_segmentation" --bundle_dir "bundles/"
python -m monai.bundle download "swin_unetr_btcv_segmentation" --bundle_dir "bundles/"

# 修改configs/train.json中的参数
# "dataset_dir": "/workspace/data/RawData/",
#     "images": "$list(sorted(glob.glob(@dataset_dir + '/imagesTr/*.nii.gz')))",
#     "labels": "$list(sorted(glob.glob(@dataset_dir + '/labelsTr/*.nii.gz')))",
#     "val_interval": 5,
#     "device": "$torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')",
#     "network_def": {
#         "_target_": "SwinUNETR",
#         "spatial_dims": 3,
#         "img_size": 96,
#         "in_channels": 1,
#         "out_channels": 2, 修改输出通道为2
#         "feature_size": 48,
#         "use_checkpoint": true
#     },

#%%图像报错处理：使用重抽样，使得image和mask的Direction、origin和spacing一致
import SimpleITK as sitk
import os

# 指定图像和标签路径
image_dir = '/root/autodl-tmp/HCC503ap/ap'
label_dir = '/root/autodl-tmp/HCC503ap/apmask'

# 获取所有 .nii.gz 文件并排序
nii_images = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])
nii_labels = sorted([filename for filename in os.listdir(label_dir) if filename.endswith('.nii.gz')])

# 遍历图像和标签
for img_filename, label_filename in zip(nii_images, nii_labels):
    # 构建完整路径
    image_path = os.path.join(image_dir, img_filename)
    label_path = os.path.join(label_dir, label_filename)
    
    try:
        # 检查文件大小是否为0
        if os.path.getsize(image_path) == 0 or os.path.getsize(label_path) == 0:
            print(f"警告: 文件 {image_path} 或 {label_path} 大小为0，跳过")
            continue
            
        # 读取图像和标签
        image = sitk.ReadImage(image_path)
        label = sitk.ReadImage(label_path)
        
        # 检查方向、原点和间距是否一致
        if (image.GetDirection() != label.GetDirection() or
            image.GetOrigin() != label.GetOrigin() or
            image.GetSpacing() != label.GetSpacing()):
            
            # 创建一个默认的 Transform（Identity Transform）
            transform = sitk.Transform()
            
            # 使用图像作为参考对标签进行重采样
            resampled_label = sitk.Resample(
                label,  # 要重采样的图像
                image,  # 参考图像
                transform,  # 使用单位变换
                sitk.sitkNearestNeighbor,  # 插值方式
                0,  # 默认像素值
                label.GetPixelID()  # 输出像素类型
            )
            
            # 保存重采样后的标签
            resampled_label_path = os.path.join(label_dir, label_filename)
            sitk.WriteImage(resampled_label, resampled_label_path)
            print(f"Resampled label saved for {label_filename}")

        # 打印图像和标签的形状
        print(f"Image shape for {img_filename}: {image.GetSize()}, Label shape for {label_filename}: {label.GetSize()}")
    
    except Exception as e:
        print(f"错误处理文件 {image_path} 和 {label_path}: {e}")
        continue

#%%
cd bundles
cd swin_unetr_btcv_segmentation

pip install pytorch-ignite

# 运行训练
python -m monai.bundle run --config_file configs/train.json

# 运行训练并指定数据集路径，成功了
python -m monai.bundle run --config_file configs/train.json --dataset_dir /root/autodl-tmp/Task09_Spleen
python -m monai.bundle run --config_file configs/train2.json --dataset_dir /root/autodl-tmp/HCC503ap

# 模型评估
python -m monai.bundle run --config_file "['configs/train2.json','configs/evaluate.json']"


# 模型推理
python -m monai.bundle run --config_file configs/inference.json
# 或者
python -m monai.bundle run --config_file "['configs/inference.json', 'configs/inference_trt.json']"

# 模型推理并指定数据集路径
python -m monai.bundle run --config_file configs/inference.json --dataset_dir /root/autodl-tmp/HCC503ap


