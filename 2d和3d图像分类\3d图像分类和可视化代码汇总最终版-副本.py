#%% 恒源云登录网盘和下载
curl -L https://download.gpushare.com/download/gpushare-cli-linux -o /usr/local/bin/gpushare-cli
chmod u+x /usr/local/bin/gpushare-cli
gpushare-cli version

gpushare-cli login -u 13616205361 -p yu110720
gpushare-cli ali ls
gpushare-cli ali ls /4.肝癌课题图像汇总  
gpushare-cli baidu ls
gpushare-cli baidu ls /4.肝癌课题图像汇总/苏大附一院HCC病例  
gpushare-cli baidu down /4.肝癌课题图像汇总/苏大附一院HCC病例/孙慧琳-双表HCC.zip /hy-tmp/
~ cd /hy-tmp/
~ ls 
calib.zip
~ unzip calib.zip 

# 上传文件
~ gpushare-cli baidu up /hy-tmp/dataset/train.zip /dataset/

# 如何加速从 GitHub 上克隆代码或下载文件？
# 加速克隆代码可以将按照如下操作方式 ，
# 如仓库地址为 https://github.com/kelseyhightower/nocode.git，
# 然后写为 https://ghproxy.com/https://github.com/kelseyhightower/nocode.git。

# 原始地址 https://github.com/kelseyhightower/nocode.git
# 写为如下
git clone https://ghproxy.com/https://github.com/kelseyh

#pip install pandas SimpleITK torchio monai openpyxl

#%% 解压rar文件
!pip install rarfile
import rarfile

def unrar(rar_file, dest_dir):
    with rarfile.RarFile(rar_file) as rf:
        rf.extractall(dest_dir)

rar_file = '/hy-tmp/2024朱珠.rar'
dest_dir = '/hy-tmp/'
unrar(rar_file, dest_dir)

#%%
#学术资源加速
import subprocess
import os

result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
output = result.stdout
for line in output.splitlines():
    if '=' in line:
        var, value = line.split('=', 1)
        os.environ[var] = value

!pip install torch==2.1.1 torchvision==0.16.1 torchaudio==2.1.1 --index-url https://download.pytorch.org/whl/cu118
#%% 3D ResNet和swin3d_s等提取深度学习特征
# 注意所有image要resize同样的大小才行 如64*64*64
# 从分割后的nii.gz图像中提取特征，而不需要使用mask
import numpy as np
import torch
import pandas as pd
import SimpleITK as sitk
from torch.utils.data import DataLoader
from torchvision.transforms import Compose
from torchvision.models.video import *
import os
import glob
import nibabel as nib
import torch
import torch.nn as nn
import torchvision.models.video as video_models
model_names = [name for name in dir(video_models) ]
for name in model_names:
    print(name)

# 定义数据存储路径
data_dir = '/root/autodl-tmp/tumor/hbp/val'
excel_path = '/root/autodl-tmp/tumor/hbp/val-hbp-r3d-features.xlsx'

model = r3d_18(pretrained=True)
model = model.cuda()  # 使用GPU版本
model 

# 截断模型，将全连接层之前的部分作为特征提取器
feature_extractor = torch.nn.Sequential(*list(model.children())[:-1])  # 3D ResNet
feature_extractor = feature_extractor.cuda()  # 使用GPU版本
feature_extractor

# 加载预训练的swin3d_s模型
# model = swin3d_s(pretrained=True) 
# model = model.cuda()  # 使用GPU版本
# model 
# model.head = torch.nn.Sequential(*list(model.head.children())[:-1])# swin3d
# feature_extractor = model
# feature_extractor = feature_extractor.cuda()  # 使用GPU版本
# feature_extractor

# 找到图像文件并存储它们的路径
img_files_list = []
extensions = ['*.nii.gz']
for ext in extensions:
    img_files_list.extend(glob.glob(os.path.join(data_dir, ext)))

max_shape = (0, 0, 0)
max_image_path = ""

for image_path in img_files_list:
    image = nib.load(image_path)
    shape = image.shape
    max_shape = tuple(max(max_shape[i], shape[i]) for i in range(3))    
    max_num = max_shape[2]
    max_size = max(max_shape[0], max_shape[1])
    if shape[2] == max_shape[2]:  # 如果当前图像的z轴尺寸等于最大尺寸
        max_image_path = image_path
print("Maximum shape of the nii images:", max_shape)
print("Path of the image with maximum size:", max_image_path)
print("Maximum number of slices:", max_num)
print("Maximum size of the image:", max_size)


def extractor(img_path, feature_extractor, use_gpu=True):
    image = sitk.ReadImage(img_path)
    np_image = sitk.GetArrayFromImage(image)
    np_image = np_image.astype(np.float32)   
    
    # 确保图像至少有一定的尺寸
    min_size = max_size  # 假设模型需要的最小尺寸是64x64x64
    if np_image.shape[0] < min_size or np_image.shape[1] < min_size or np_image.shape[2] < min_size:
        # 使用线性插值重新采样图像
        new_size = (max(min_size, np_image.shape[0]), max(min_size, np_image.shape[1]), max(min_size, np_image.shape[2]))
        resampler = sitk.ResampleImageFilter()
        resampler.SetSize(new_size)
        resampler.SetInterpolator(sitk.sitkLinear)
        image = resampler.Execute(image)
        np_image = sitk.GetArrayFromImage(image)
    
    # 将灰度图像转换为伪RGB（如果需要）
    if np_image.ndim == 3:
        np_image = np.stack([np_image]*3, axis=0)
    else:
        np_image = np_image[np.newaxis, :, :, :]

    # 转换为张量
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    print(tensor_image.shape)
    # 使用GPU
    if use_gpu:
        tensor_image = tensor_image.cuda()
    # 使用模型提取特征
    with torch.no_grad():
        features = feature_extractor(tensor_image)
    # 将特征转换回CPU并转换为numpy数组
    return features.squeeze().cpu().numpy()

features = []

for img_path in img_files_list:
    print(img_path)
    file_name = os.path.splitext(os.path.basename(img_path))[0]    
    feature = extractor(img_path, feature_extractor, use_gpu=True)  # 使用GPU
    features.append([file_name, img_path] + list(feature))   
    torch.cuda.empty_cache() # 在每个迭代之后释放内存

# 创建数据帧的列名称
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]

# 将特征转换为numpy数组并将其类型转换为float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)

# 创建数据帧
df = pd.DataFrame(features, columns=columns)

# 使用astype将文件名和图像路径转换为字符串并将其设置为数据帧的索引列
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)

# 将数据帧保存到Excel文件中
df.to_excel(excel_path)

#%% 3D mvit_v1_b和mvit_v2_s模型提取深度学习特征，注意所有image要resize同样的大小16*224*224
# 从分割后的nii.gz图像中提取特征，而不需要使用mask
import numpy as np
import torch
import pandas as pd
import SimpleITK as sitk
from torch.utils.data import DataLoader
from torchvision.transforms import Compose
from torchvision.models.video import *
import os
import glob
import nibabel as nib
import torch
import torch.nn as nn

# 定义数据存储路径
data_dir = '/root/autodl-tmp/HCC/tumor/hbp/test'
excel_path = '/root/autodl-tmp/HCC/tumor/hbp/test-hbp-mvit-features.xlsx'

# data_dir = r"D:\dataset\shuju\nii"
# excel_path = r"D:\dataset\shuju\nii4.xlsx"

# 加载预训练的mvit_v1_b或者mvit_v2_s模型
model = mvit_v2_s(pretrained=True) 
model = model.cuda()  # 使用GPU版本
model 
model.head = torch.nn.Sequential(*list(model.head.children())[:-1])# mvit_v1_b
feature_extractor = model
feature_extractor = feature_extractor.cuda()  # 使用GPU版本
feature_extractor

# 找到图像文件并存储它们的路径
img_files_list = []
extensions = ['*.nii.gz']
for ext in extensions:
    img_files_list.extend(glob.glob(os.path.join(data_dir, ext)))


def extractor(img_path, feature_extractor, use_gpu=True):
    image = sitk.ReadImage(img_path)
    np_image = sitk.GetArrayFromImage(image)
    np_image = np_image.astype(np.float32)
    print(np_image.shape)

    # 确保图像至少有一定的尺寸
    expected_frames = 16
    expected_height = 224
    expected_width = 224
       
    # # 使用线性插值重采样图像到期望尺寸16*224*224
    new_size = (expected_frames, expected_height, expected_width)
    resampler = sitk.ResampleImageFilter() #会调整为（width, height, depth）224*224*16
    resampler.SetSize(new_size)
    resampler.SetInterpolator(sitk.sitkLinear)
    image = resampler.Execute(image)  
    np_image = sitk.GetArrayFromImage(image)   
    np_image = np.transpose(np_image, (2,1,0))  # 将通道、高度和宽度重新排列为16*224*224
    print("Resized shape:", np_image.shape)

    # 将灰度图像转换为伪RGB（如果需要）
    if np_image.ndim == 3:
        np_image = np.stack([np_image]*3, axis=0)  # 注意这里的变化，确保通道维度正确
        print("np_image shape:", np_image.shape)
    # 转换为张量
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    print("tensor_image shape:", tensor_image.shape)
    
    # 调整张量维度顺序以匹配模型期望的输入
    tensor_image = tensor_image.permute(0, 1, 2, 3, 4)  # 调整为(batch_size, channels, frames, height, width)
    print("tensor_image2 shape:",tensor_image.shape)

    # 使用GPU
    if use_gpu:
        tensor_image = tensor_image.cuda()
    
    # 使用模型提取特征
    with torch.no_grad():
        features = feature_extractor(tensor_image)
    
    # 将特征转换回CPU并转换为numpy数组
    return features.squeeze().cpu().numpy()

features = []

for img_path in img_files_list:
    file_name = os.path.splitext(os.path.basename(img_path))[0]    
    feature = extractor(img_path, feature_extractor, use_gpu=True)  # 使用GPU
    features.append([file_name, img_path] + list(feature))   
    torch.cuda.empty_cache() # 在每个迭代之后释放内存

# 创建数据帧的列名称
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]

# 将特征转换为numpy数组并将其类型转换为float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)

# 创建数据帧
df = pd.DataFrame(features, columns=columns)

# 使用astype将文件名和图像路径转换为字符串并将其设置为数据帧的索引列
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)

# 将数据帧保存到Excel文件中
df.to_excel(excel_path)

#%% 3d resnet18和swin3d_s提取3d深度学习特征  已成功 
# 需要image和mask文件夹 
import numpy as np
import torch
import pandas as pd
import SimpleITK as sitk
from torch.utils.data import DataLoader
from torchvision.transforms import Compose
from torchvision.models.video import *
import os
import glob

# 定义数据和掩码存储路径
data_dir = "/hy-tmp/nantong/image/pp"  
mask_dir = "/hy-tmp/nantong/mask/pp"
excel_path = "/hy-tmp/nantong/pp-swin3d-features.xlsx"

# 加载预训练的3D ResNet模型
# model = r3d_18(pretrained=True)
# model = model.cuda()  # 使用GPU版本
# # model   # 使用cpu版本
# feature_extractor = torch.nn.Sequential(*list(model.children())[:-1])  # 3D ResNet
# feature_extractor = feature_extractor.cuda()  # 使用GPU版本
# feature_extractor

# 加载预训练的swin3d_s模型
model = swin3d_s(pretrained=True) 
model = model.cuda()  # 使用GPU版本
model 
model.head = torch.nn.Sequential(*list(model.head.children())[:-1])# swin3d
feature_extractor = model
feature_extractor = feature_extractor.cuda()  # 使用GPU版本
feature_extractor

# 找到图像和掩码文件并存储它们的路径
img_files_list = []
mask_files_list = []
extensions = ['*.nii.gz']
# extensions = ['*.nii']

for ext in extensions:
    img_files_list.extend(sorted(glob.glob(os.path.join(data_dir, ext))))
    mask_files_list.extend(sorted(glob.glob(os.path.join(mask_dir, ext))))

# 定义一个函数，使用特征提取器从图像和掩码中提取特征
def extractor(img_path, mask_path, feature_extractor, use_gpu=True):
    image = sitk.ReadImage(img_path)
    mask = sitk.ReadImage(mask_path)
    np_image = sitk.GetArrayFromImage(image).astype(np.float32)
    np_mask = sitk.GetArrayFromImage(mask).astype(np.float32)
    np_image = np_image * np_mask  # 将图像根据掩码进行处理
    np_image = np_image.astype(np.float32)
    if np_image.ndim == 3:  # 将灰度图像添加两个通道，使其类似于RGB图像
        np_image = np.stack([np_image.squeeze()]*3, axis=0)
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    if use_gpu:
        tensor_image = tensor_image.cuda()
    with torch.no_grad():
        features = feature_extractor(tensor_image)
    return features.squeeze().cpu().numpy()

features = []

for img_path, mask_path in zip(img_files_list, mask_files_list):
    file_name = os.path.splitext(os.path.basename(img_path))[0]
    print(img_path);print(mask_path)
    feature = extractor(img_path, mask_path, feature_extractor, use_gpu=True)  # 使用GPU
    # feature = extractor(img_path, mask_path, feature_extractor, use_gpu=False)  # 使用CPU
    features.append([file_name, img_path] + list(feature))
    # 在每个迭代之后释放内存
    torch.cuda.empty_cache()

# 创建数据帧的列名称
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]

# 将特征转换为numpy数组并将其类型转换为float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)

# 创建数据帧
df = pd.DataFrame(features, columns=columns)

# 使用astype将文件名和图像路径转换为字符串并将其设置为数据帧的索引列
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)

# 将数据帧保存到Excel文件中
df.to_excel(excel_path)

#%%3d mvit_v2_s和mvit_v1_b提取3d深度学习特征 
# 需要image和mask文件夹 
import numpy as np
import torch
import pandas as pd
import SimpleITK as sitk
from torch.utils.data import DataLoader
from torchvision.transforms import Compose
from torchvision.models.video import *
import os
import glob

# 定义数据和掩码存储路径
data_dir = r"D:\dataset\shuju\images"
mask_dir = r"D:\dataset\shuju\masks"
excel_path = r"D:\dataset\shuju\mvitfeatures2.xlsx"

# 加载预训练的mvit_v2_s和mvit_v1_b模型
model = mvit_v1_b(pretrained=True) 
# model = model.cuda()  # 使用GPU版本
model 
model.head = torch.nn.Sequential(*list(model.head.children())[:-1])
feature_extractor = model
# feature_extractor = feature_extractor.cuda()  # 使用GPU版本
feature_extractor

# 找到图像和掩码文件并存储它们的路径
img_files_list = []
mask_files_list = []
extensions = ['*.nii.gz']

for ext in extensions:
    img_files_list.extend(sorted(glob.glob(os.path.join(data_dir, ext))))
    mask_files_list.extend(sorted(glob.glob(os.path.join(mask_dir, ext))))

def extractor(img_path, mask_path, feature_extractor, use_gpu=False):
    image = sitk.ReadImage(img_path)
    mask = sitk.ReadImage(mask_path)
    np_image = sitk.GetArrayFromImage(image).astype(np.float32)
    np_mask = sitk.GetArrayFromImage(mask).astype(np.float32)
    np_image = np_image * np_mask  # 应用掩码
    print(np_image.shape)

    # 确保图像至少有一定的尺寸
    expected_frames = 16
    expected_height = 224
    expected_width = 224
      
    # 使用线性插值重新采样图像到期望尺寸
    new_size = (expected_frames, expected_height, expected_width)
    resampler = sitk.ResampleImageFilter()
    resampler.SetSize(new_size)
    resampler.SetInterpolator(sitk.sitkLinear)
    image = resampler.Execute(image)
    np_image = sitk.GetArrayFromImage(image)
    np_image = np.transpose(np_image, (2,1,0))  # 将通道、高度和宽度重新排列为16*224*224
    print("Resized shape:", np_image.shape)    

    # 将灰度图像转换为伪RGB（如果需要）
    if np_image.ndim == 3:
       np_image = np.stack([np_image]*3, axis=0)  # 注意这里的变化，确保通道维度正确

    # 转换为张量
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    print(tensor_image.shape)

    # 调整张量维度顺序以匹配模型期望的输入
    tensor_image = tensor_image.permute(0, 1, 2, 3, 4)  # 调整为(batch_size, channels, frames, height, width)
    print(tensor_image.shape)

    # 使用GPU
    if use_gpu:
        tensor_image = tensor_image.cuda()
    
    with torch.no_grad():
        print("Tensor shape before model:", tensor_image.shape)
        features = feature_extractor(tensor_image)
    
    return features.squeeze().cpu().numpy()

features = []

for img_path, mask_path in zip(img_files_list, mask_files_list):
    file_name = os.path.splitext(os.path.basename(img_path))[0]
    # feature = extractor(img_path, mask_path, feature_extractor, use_gpu=True)  # 使用GPU
    feature = extractor(img_path, mask_path, feature_extractor, use_gpu=False)  # 使用CPU
    features.append([file_name, img_path] + list(feature))
    # 在每个迭代之后释放内存
    torch.cuda.empty_cache()

# 创建数据帧的列名称
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]

# 将特征转换为numpy数组并将其类型转换为float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)

# 创建数据帧
df = pd.DataFrame(features, columns=columns)

# 使用astype将文件名和图像路径转换为字符串并将其设置为数据帧的索引列
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)

# 将数据帧保存到Excel文件中
df.to_excel(excel_path)

#%%添加自注意力机制的3d resnet代码-cpu版本  已成功
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import SimpleITK as sitk
from torchvision.models.video import *
import os
import glob

# 添加以下代码以设置随机种子：
import random
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

set_seed(42)

# 定义数据和掩码存储路径
data_dir = r"D:\dataset\shuju\images"
mask_dir = r"D:\dataset\shuju\masks"
excel_path = r"D:\dataset\shuju\features_attention2.xlsx"

class SEModule(nn.Module):
    def __init__(self, channels, reduction=16):
        super(SEModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.fcl = nn.Linear(channels, channels // reduction, bias=False)
        self.relu = nn.ReLU(inplace=True)
        self.fcl2 = nn.Linear(channels // reduction, channels, bias=False)
        self.sigmoid = nn.Sigmoid()
    def forward(self, x):
        scale = self.avg_pool(x)
        scale = torch.flatten(scale, 1)
        scale = self.fcl(scale)
        scale = self.relu(scale)
        scale = self.fcl2(scale)
        scale = self.sigmoid(scale)
        scale = scale.view(scale.size(0), scale.size(1), 1, 1, 1)
        x = x * scale
        return x
def apply_se_to_blocks(model, blocks):
    for block_name in blocks:
        block = getattr(model, block_name)
        for i, layer in enumerate(block.children()):
            # check type of layer
            if isinstance(layer, nn.Conv3d):
                out_channels = layer.out_channels
            elif isinstance(layer, nn.Sequential):
                for l in layer.children():
                    if isinstance(l, nn.Conv3d):
                        out_channels = l.out_channels
                        break
            else:
                out_channels = None
            if out_channels is not None:
                se_module = SEModule(out_channels)
                setattr(block, f"se_module_{i}", se_module)
    return model

 # Load pretrained 3D ResNet model and replace the last layers
model = r3d_18(pretrained=True)
model.fc = nn.Sequential(nn.AdaptiveAvgPool3d(1), nn.Flatten())
 # Apply SE modules to each of the residual blocks
model_se = r3d_18(pretrained=False)
model_se = apply_se_to_blocks(model_se, ["layer1", "layer2", "layer3", "layer4"])
 # Apply an SE module to the stem of the network
model_se.stem.add_module("se_module", SEModule(64))
 # Set the custom 3D ResNet model to evaluation mode
model_se.eval()
 # Transfer the base model's state dict to the custom model, skipping the keys (fc weight and bias) that do not match
model_dict = model.state_dict()
model_se_dict = model_se.state_dict()
for k in model_dict.keys():
    if k in model_se_dict and model_se_dict[k].shape == model_dict[k].shape:
        model_se_dict[k] = model_dict[k]
model_se.load_state_dict(model_se_dict)
feature_extractor = torch.nn.Sequential(*list(model_se.children())[:-1])  # 3D ResNet

img_files_list = []
mask_files_list = []
extensions = ['*.nii.gz']

for ext in extensions:
    img_files_list.extend(sorted(glob.glob(os.path.join(data_dir, ext))))
    mask_files_list.extend(sorted(glob.glob(os.path.join(mask_dir, ext))))


def extractor(img_path, model, use_gpu=False):
    image = sitk.ReadImage(img_path)
    np_image = sitk.GetArrayFromImage(image).astype(np.float32)
    if np_image.ndim == 3:
        np_image = np.stack([np_image.squeeze()] * 3, axis=0)
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    if use_gpu:
        tensor_image = tensor_image.cuda()
    with torch.no_grad():
        features = model(tensor_image)
    return features.squeeze().cpu().numpy()

features = []
for img_path in img_files_list:
    file_name = os.path.splitext(os.path.basename(img_path))[0]
    feature = extractor(img_path, feature_extractor, use_gpu=False)
    features.append([file_name, img_path] + list(feature))

# 创建数据帧的列名称
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]

# 将特征转换为numpy数组并将其类型转换为float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)

# 创建数据帧
df = pd.DataFrame(features, columns=columns)

# 使用astype将文件名和图像路径转换为字符串并将其设置为数据帧的索引列
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)

# 将数据帧保存到Excel文件中
df.to_excel(excel_path)

#%% 3d 深度学习模型代码完整版；
#r3d_18,swin3d_b预训练模型迁移学习，已成功
#根据剪切后的tumor图像nii格式进行训练 
# pip install opencv-python
import torch           
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import nibabel as nib
import pandas as pd
import numpy as np
import os
import SimpleITK as sitk
# import cv2
import torchvision.models as models
import torchvision.models.video as video_models
model_names = [name for name in dir(video_models) ]
for name in model_names:
    print(name)

def get_file_list(directory):
    file_list = [os.path.join(directory, file) for file in os.listdir(directory) if file.endswith('.nii.gz')]
    file_list.sort()  # 按照文件名排序
    return file_list

#注意要修改标签名称，从'name'和'PHCC'列中获取数值
def get_label_list(file_path):   
    label_file = pd.read_excel(file_path)    
    name_values = label_file['name'].tolist()
    label_values = label_file['PHCC'].tolist()
    # 根据'name'列对数值进行排序
    sorted_label_values = [label for _, label in sorted(zip(name_values, label_values))]
    # 将排序后的VETC数值存储在label_list中
    label_list = sorted_label_values
    return label_list

train_dir = '/hy-tmp/tumor/ap/train'   
test_dir = '/hy-tmp/tumor/ap/val'
train_file_path = '/hy-tmp/data/train.xlsx'  
test_file_path = '/hy-tmp/data/val.xlsx'

train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5])
print(len(train_image_list));print(len(test_image_list))

# 调用函数获取标签列表
train_label_list = get_label_list(train_file_path)
test_label_list = get_label_list(test_file_path)
# 打印标签列表
print(train_label_list[:5])
print(len(train_label_list))
print(len(test_label_list))

#获取最大的图像的shape
image_list = train_image_list + test_image_list

max_shape = (0, 0, 0)
max_image_path = ""

for image_path in image_list:
    image = nib.load(image_path)
    shape = image.shape
    max_shape = tuple(max(max_shape[i], shape[i]) for i in range(3))    
    max_num = max_shape[2]
    max_size = max(max_shape[0], max_shape[1])
    if shape[2] == max_shape[2]:  # 如果当前图像的z轴尺寸等于最大尺寸
        max_image_path = image_path
print("Maximum shape of the nii images:", max_shape)
print("Path of the image with maximum size:", max_image_path)
print("Maximum number of slices:", max_num)
print("Maximum size of the image:", max_size)
# max_num = 64  #要比实际最大的要大
# max_size = max(280,280)
# print(max_size)

class TumorDataset(Dataset):
    def __init__(self, image_list, label_list, transform=None):
        self.image_list = image_list
        self.label_list = label_list
        self.transform = transform    

    def __len__(self):
        return len(self.image_list)    

    def __getitem__(self, index):
        image_path = self.image_list[index]
        label = self.label_list[index]
        image = sitk.ReadImage(image_path)
        img_array = sitk.GetArrayFromImage(image)
        # print(img_array.shape)
        img_array = img_array.astype(np.float32)
        if img_array.ndim == 3:
            img_array = np.stack([img_array.squeeze()] * 3, axis=0)
            # print(img_array.shape)
            pad_shape = ((0, 0), (0, max(0, max_num - img_array.shape[1])), #图像数量大小可以修改
                         (0, max(0, max_size - img_array.shape[2])), #图像大小可以修改
                         (0, max(0, max_size - img_array.shape[3])))#图像大小可以修改        
        img_array = np.pad(img_array, pad_shape, mode='constant')
        # Crop the image to size 256
        # img_array = img_array[:, :256, :256, :256] #剪切图像，可以不用
        tensor_image = torch.from_numpy(img_array).float()
        label_array = np.array(label)
        tensor_label = torch.from_numpy(label_array).float()
        return tensor_image, tensor_label

train_ds = TumorDataset(train_image_list,train_label_list, transform=None)
test_ds = TumorDataset(test_image_list,test_label_list, transform=None)
train_loader = DataLoader(train_ds, batch_size=4, shuffle=True)
test_loader = DataLoader(test_ds, batch_size=4, shuffle=False)

#目前有很多模型 r3d_18、mc3_18, s3d ，mvit_v1_b;swin3d_b，swin3d_s，r2plus1d_18
model = video_models.swin3d_s(pretrained=True) 
# model = video_models.r3d_18(pretrained=True) 
# model = video_models.mvit_v1_b(pretrained=True)
# model = video_models.s3d(pretrained=True)
model

model.parameters
for param in model.parameters():
    param.requires_grad = False

#数据集中分类的类别数
num_classes = 2  # num_classes表示输出特征
# model.fc = nn.Linear(model.fc.in_features, num_classes) #r3d_18
model.head = nn.Linear(model.head.in_features, num_classes) #swin3d_b
# model.head[1] = nn.Linear(model.head[1].in_features, num_classes) #mvit_v1_b
model

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

criterion = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.01) #resnet   优化器要传入最后一层所有参数
optimizer = torch.optim.Adam(model.head.parameters(), lr=0.001) #swin3d_b
# optimizer = torch.optim.Adam(model.head[1].parameters(), lr=0.006) #mvit_v1_b
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

num_epochs = 20

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, (images, labels) in enumerate(train_loader):
        images = images.float().to(device)
        labels = labels.long().to(device)
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
    exp_lr_scheduler.step()  # 注意这个代码有学习率，因此这里需要加一行
     # Release memory after each training iteration
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_ds)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')

    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for images, labels in test_loader:
            images = images.float().to(device)
            labels = labels.long().to(device)
            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            # Release memory after each testing iteration
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}],Testing Accuracy: {accuracy:.3f}%')

#%%3dCNN 预训练模型迁移学习代码  
# r3d_18，mvit_v1_b;swin3d_b成功
#3d图像monai预处理和数据增强代码
# pip install monai
#安装monai依赖包
# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]"')

import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

#resize成统一de 图像体积
resized_shape = (128, 128, 64) #其他模型可用
# resized_shape = (224, 224, 16) #对于mvit模型，要用这个体积
# resized_shape = (224, 224,64) #对于s3d模型，要用这个体积

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_shape),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.3, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label,"file_name": file_names})    
    return data

# 使用示例，如果使用docker时，路径需要上传到docker，否则找不到
train_dir = '/root/autodl-tmp/HCC2/tumor/train/ap'
test_dir = '/root/autodl-tmp/HCC2/tumor/val/ap'
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/val2.xlsx'

# train_dir = '/hy-tmp/tumor/ap/train'   
# test_dir = '/hy-tmp/tumor/ap/val'
# train_file_path = '/hy-tmp/data/train.xlsx'  
# test_file_path = '/hy-tmp/data/val.xlsx'

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=4)

#3d 模型迁移学习
#目前有很多模型 r3d_18、mc3_18, s3d ，mvit_v1_b;swin3d_b，swin3d_s，r2plus1d_18
model = swin3d_s(pretrained=True) 
# model = r3d_18(pretrained=True) 
# model = mvit_v1_b(pretrained=True)
# model = s3d(pretrained=True)
model

for param in model.parameters():
    param.requires_grad = False

#数据集中分类的类别数
num_classes = 2  #num_classes表示输出特征

# model.fc = nn.Linear(model.fc.in_features, num_classes) #r3d_18
model.head = nn.Linear(model.head.in_features, num_classes) #swin3d_b
# model.head[1] = nn.Linear(model.head[1].in_features, num_classes) #mvit_v1_b
# model.classifier[1] = nn.Conv3d(1024, num_classes, kernel_size=(1, 1, 1), stride=(1, 1, 1)) # s3d
model

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

criterion = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
optimizer = torch.optim.Adam(model.head.parameters(), lr=0.005) #swin3d_b
# optimizer = torch.optim.Adam(model.head[1].parameters(), lr=0.01) #mvit_v1_b
# optimizer = torch.optim.Adam(model.classifier[1].parameters(), lr=0.01) # s3d
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/root/autodl-tmp/tumor/hbp_best_model.pth"

# 训练循环
num_epochs = 20

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 检查是否是最佳模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)
    #     print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#保存模型
# PATH = '/root/autodl-tmp/HCC/ap_r3d-swin3d.pth'
# # PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
# torch.save(model.state_dict(), PATH)

# 加载最优模型
# model.load_state_dict(torch.load(best_model_path))
# print(f'Loaded best model with accuracy: {best_accuracy:.3f}%')

#%% 3d深度学习模型融合代码  
# r3d_18和swin 3d、mvit模型融合  成功
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

#Resize成统一图像体积
resized_shape = (224,224, 70) #r3d_18和swin3d融合用这个
# resized_shape = (224, 224, 16) #r3d_18和mvit融合用这个

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_shape),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.5, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label,"file_name": file_names})    
    return data

# 使用示例
# train_dir = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor\ap\train'
# test_dir = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor\ap\val'
# train_file_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\train.xlsx'
# test_file_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\val.xlsx'

train_dir = '/root/autodl-tmp/HCC/tumor/train/ap'   
test_dir = '/root/autodl-tmp/HCC/tumor/test/ap'
train_file_path = '/root/autodl-tmp/data/train.xlsx'  
test_file_path = '/root/autodl-tmp/data/test.xlsx'


#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=2)

# #3d 模型迁移学习
# #目前有很多模型 r3d_18、mc3_18, s3d ，mvit_v1_b;swin3d_b，swin3d_s，r2plus1d_18
# model1 = r3d_18(pretrained=True)
# model2 = swin3d_s(pretrained=True)
# # model2 = mvit_v1_b(pretrained=True)
# # model2 = s3d(pretrained=True)

# 自定义融合模型
class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        self.model = r3d_18(pretrained=pretrained)
        # 去掉最后一层全连接层
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FeatureExtractorSwin3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorSwin3D, self).__init__()
        self.model = swin3d_s(pretrained=pretrained)
        # self.model = mvit_v1_b(pretrained=pretrained) # 使用mvit_v1_b模型
        # 去掉最后一层分类层
        self.model.head = nn.Identity()
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FusionModel(nn.Module):
    def __init__(self, num_classes):
        super(FusionModel, self).__init__()
        self.feature_extractor_resnet3d = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d = FeatureExtractorSwin3D(pretrained=True)
        # 定义一个全连接层来结合两个特征向量
        self.fc = nn.Linear(512 + 768, num_classes)  # 512是ResNet3D的特征维度，768是Swin3D的特征维度
        
    def forward(self, x):
        features_resnet3d = self.feature_extractor_resnet3d(x)
        features_resnet3d = features_resnet3d.view(features_resnet3d.size(0), -1)
        
        features_swin3d = self.feature_extractor_swin3d(x)
        features_swin3d = features_swin3d.view(features_swin3d.size(0), -1)
        
        combined_features = torch.cat((features_resnet3d, features_swin3d), dim=1)        
        out = self.fc(combined_features)        
        return out

# 分类的类别数
num_classes = 2
model = FusionModel(num_classes=num_classes)
model

# 将模型移动到GPU
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)
model.to(device)

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.003)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/hy-tmp/tumor/ap_best_model.pth"

# 训练和测试
num_epochs = 10

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 检查是否是最佳模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)
    #     print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#保存模型
PATH = '/root/autodl-tmp/HCC/ap_r3d-swin3d.pth'
# PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
torch.save(model.state_dict(), PATH)

## 加载最优模型
# model.load_state_dict(torch.load(best_model_path))
# print(f'Loaded best model with accuracy: {best_accuracy:.3f}%')

#%% 多序列特征融合，3d模型不融合，处理类别不平衡问题
#多序列特征融合，3d模型不融合
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped,RandFlipd,RandAffined
)
from monai.data import DataLoader, Dataset
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import torch.nn.functional as F
import torch
import torch.nn as nn
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

# resize成统一图像体积
resized_shape = (112,112,64) # r3d_18和swin3d融合用这个
# resized_shape = (224, 224, 16) #对于mvit模型，要用这个体积

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    RandRotate90d(keys=["image_ap", "image_pp", "image_hbp"], prob=0.5, spatial_axes=[0, 1]),
    RandFlipd(keys=["image_ap", "image_pp", "image_hbp"],  prob=0.3, spatial_axis=0),
    RandAffined(keys=["image_ap", "image_pp", "image_hbp"], prob=0.3, rotate_range=(0.1, 0.1, 0.1), scale_range=(0.1, 0.1, 0.1)), 
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])

test_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),    
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])


# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取各个子文件夹中的图像文件列表
    ap_files = [f for f in os.listdir(os.path.join(data_dir, 'ap')) if f.endswith('.nii.gz')]
    pp_files = [f for f in os.listdir(os.path.join(data_dir, 'pp')) if f.endswith('.nii.gz')]
    hbp_files = [f for f in os.listdir(os.path.join(data_dir, 'hbp')) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for ap_file in ap_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(ap_file)
        file_name = os.path.splitext(base_name)[0]
        file_names = file_name.split('-')[0]
        
        # 检查相应的 pp 和 hbp 文件是否存在
        pp_file = f"{file_names}-pp.nii.gz"
        hbp_file = f"{file_names}-hbp.nii.gz"
        
        if pp_file in pp_files and hbp_file in hbp_files:
            # 从DataFrame中查找对应的标签
            if file_names in df['name'].values:
                # print(file_names)
                label = df.loc[df['name'] == file_names, label_name].values[0]
                data.append({
                    "image_ap": os.path.join(data_dir, 'ap', ap_file),
                    "image_pp": os.path.join(data_dir, 'pp', pp_file),
                    "image_hbp": os.path.join(data_dir, 'hbp', hbp_file),
                    "label": label,"file_name": file_names
                })
    
    return data

# 使用示例
train_dir = '/root/autodl-tmp/HCC2/tumor/train' #train下面有ap pp hbp
test_dir = '/root/autodl-tmp/HCC2/tumor/test'    #val下面有ap pp hbp
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/test2.xlsx'

# 加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
print(train_files[:5]);print(test_files[:5])

# 打印数据集长度
print("Number of training files:", len(train_files))
print("Number of testing files:", len(test_files))

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=16, num_workers=2)

#定义ResNet3D多序列联合模型
class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        self.model = r3d_18(pretrained=pretrained)
        # 去掉最后一层全连接层
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FusionModel(nn.Module):
    def __init__(self, num_classes):
        super(FusionModel, self).__init__()
        self.feature_extractor_resnet3d_ap = FeatureExtractorResNet3D(pretrained=True)  
        self.feature_extractor_resnet3d_pp = FeatureExtractorResNet3D(pretrained=True)        
        self.feature_extractor_resnet3d_hbp = FeatureExtractorResNet3D(pretrained=True)        
        
        # 定义一个全连接层来结合多个特征向量
        self.fc1 = nn.Linear(512 * 3, 1024)  # 第一个全连接层，将特征维度降到1024        
        self.bn = nn.BatchNorm1d(1024)
        self.relu = nn.ReLU()  # 激活函数
        self.dropout = nn.Dropout(p=0.3)  # 减少Dropout值              
        self.fc2 = nn.Linear(1024, num_classes)  # 第二个全连接层，输出类别数  

    def forward(self, ap, pp, hbp):
        features_resnet3d_ap = self.feature_extractor_resnet3d_ap(ap).view(ap.size(0), -1)
        features_resnet3d_pp = self.feature_extractor_resnet3d_pp(pp).view(pp.size(0), -1)
        features_resnet3d_hbp = self.feature_extractor_resnet3d_hbp(hbp).view(hbp.size(0), -1)        
        combined_features = torch.cat(
            (features_resnet3d_ap, features_resnet3d_pp, features_resnet3d_hbp), 
            dim=1
        )             
        x = self.fc1(combined_features)
        x = self.bn(x)
        x = self.relu(x)
        x = self.dropout(x)
        out = self.fc2(x)         
        return out

# Swin3D多序列联合模型
class FeatureExtractorSwin3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorSwin3D, self).__init__()
        self.model = swin3d_s(pretrained=pretrained)
        # 去掉最后一层分类层
        self.model.head = nn.Identity()
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FusionModel(nn.Module):
    def __init__(self, num_classes):
        super(FusionModel, self).__init__()
        
        self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True) 
        self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
        self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)        
        
        # 定义两个全连接层来结合多个特征向量，每个特征提取器的输出分别为512和768维度，处理3个序列
        self.fc1 = nn.Linear(768 * 3, 768)  # 第一个全连接层，将特征维度降到1024
        self.bn = nn.BatchNorm1d(768)
        self.relu = nn.ReLU()  # 激活函数
        self.dropout = nn.Dropout(p=0.3)  # 减少Dropout值              
        self.fc2 = nn.Linear(768, num_classes)  # 第二个全连接层，输出类别数    

    def forward(self, ap, pp, hbp):
        features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
        features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
        features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
        combined_features = torch.cat(
            (features_swin3d_ap, features_swin3d_pp, features_swin3d_hbp), 
            dim=1
        )   
        x = self.fc1(combined_features)
        x = self.bn(x)
        x = self.relu(x)
        x = self.dropout(x)
        out = self.fc2(x)       
        return out       


# mvit_v1_b多序列联合模型
# class FeatureExtractorSwin3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorSwin3D, self).__init__()
#         self.model = mvit_v1_b(pretrained=pretrained)
#         # 去掉最后一层分类层
#         self.model.head = nn.Identity()
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes):
#         super(FusionModel, self).__init__()
        
#         self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True) 
#         self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
#         self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)        
        
#         # 定义两个全连接层来结合多个特征向量，每个特征提取器的输出分别为512和768维度，处理3个序列
#         self.fc1 = nn.Linear(768 * 3, 1024)  # 第一个全连接层，将特征维度降到1024
#         self.relu = nn.ReLU()  # 激活函数
#         self.fc2 = nn.Linear(1024, num_classes)  # 第二个全连接层，输出类别数

#     def forward(self, ap, pp, hbp):
#         features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
#         features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
#         features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
#         combined_features = torch.cat(
#             (features_swin3d_ap, features_swin3d_pp, features_swin3d_hbp), 
#             dim=1
#         )   
#         x = self.fc1(combined_features)
#         x = self.relu(x)
#         out = self.fc2(x)       
#         return out


# 定义设备
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)

# 分类的类别数
num_classes = 2
model = FusionModel(num_classes=num_classes)
model
model.to(device)

# 计算类别权重;手动调整类别1的权重，使其更高
# labels = [item['label'] for item in train_files]
# class_counts = np.bincount(labels)
# class_weights = 1.0 / class_counts
# print(class_weights)

# focus_class = 1
# focus_weight = 0.01  # 设置类别1的权重
# class_weights[focus_class] = focus_weight

# # 加权交叉熵损失函数
# class_weights = torch.tensor(class_weights, dtype=torch.float32).to(device)
# criterion = nn.CrossEntropyLoss(weight=class_weights)

#定义focalloss解决样本不平衡
# class FocalLoss(nn.Module):
#     def __init__(self, gamma=2, alpha=None, reduction='mean'):
#         super(FocalLoss, self).__init__()
#         self.gamma = gamma
#         self.alpha = alpha
#         self.reduction = reduction

#         if isinstance(alpha, (float, int)):
#             self.alpha = torch.Tensor([alpha, 1 - alpha])
#         if isinstance(alpha, list):
#             self.alpha = torch.Tensor(alpha)
    
#     def forward(self, inputs, targets):
#         # Convert inputs to probabilities
#         inputs = F.softmax(inputs, dim=1)
#         # Get the log probabilities
#         log_p = torch.log(inputs)
#         # Gather log probabilities for the correct class
#         log_p = log_p.gather(1, targets.unsqueeze(1))
#         log_p = log_p.view(-1)
        
#         # Gather probabilities for the correct class
#         p_t = inputs.gather(1, targets.unsqueeze(1))
#         p_t = p_t.view(-1)
        
#         # Apply the focal loss formula
#         loss = -1 * (1 - p_t) ** self.gamma * log_p
        
#         # Apply alpha if provided
#         if self.alpha is not None:
#             if self.alpha.device != inputs.device:
#                 self.alpha = self.alpha.to(inputs.device)
#             alpha_t = self.alpha.gather(0, targets)
#             loss = loss * alpha_t
        
#         if self.reduction == 'mean':
#             return loss.mean()
#         elif self.reduction == 'sum':
#             return loss.sum()
#         else:
#             return loss

# criterion = FocalLoss(gamma=2, alpha=[0.4, 0.6])  #设置 gamma（调节难易样本权重的参数）、alpha（平衡不同类别权重的参数）

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
# best_model_path = "/root/autodl-tmp/HCC/tumor/swintumor_best_model.pth"

# 训练和测试
num_epochs = 30

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images_ap = batch['image_ap'].to(device)
        images_pp = batch['image_pp'].to(device)
        images_hbp = batch['image_hbp'].to(device)
        
        images_ap = images_ap.expand(-1, 3, -1, -1, -1)
        images_pp = images_pp.expand(-1, 3, -1, -1, -1)
        images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
        
        labels = batch['label'].long().to(device)

        optimizer.zero_grad()
        y_pred = model(images_ap, images_pp, images_hbp)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images_ap.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)
            
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            
            labels = batch['label'].long().to(device)

            y_pred = model(images_ap, images_pp, images_hbp)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 保存最优模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)

#保存模型
# PATH = '/root/autodl-tmp/HCC/tumor/mvit-tumor_3p-final_model.pth'
# # PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
# torch.save(model.state_dict(), PATH)

#%%3d 多序列特征融合及3d模型融合 成功
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped,RandFlipd,RandAffined
)
from monai.data import DataLoader, Dataset
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import torch.nn.functional as F
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

#resize成统一图像体积
resized_shape =  (64, 64, 64) # r3d_18和swin3d融合用这个

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    RandRotate90d(keys=["image_ap", "image_pp", "image_hbp"], prob=0.3, spatial_axes=[0, 1]),
    RandFlipd(keys=["image_ap", "image_pp", "image_hbp"],  prob=0.3, spatial_axis=0),
    RandAffined(keys=["image_ap", "image_pp", "image_hbp"], prob=0.3, rotate_range=(0.1, 0.1, 0.1), scale_range=(0.1, 0.1, 0.1)), 
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])

test_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取各个子文件夹中的图像文件列表
    ap_files = [f for f in os.listdir(os.path.join(data_dir, 'ap')) if f.endswith('.nii.gz')]
    pp_files = [f for f in os.listdir(os.path.join(data_dir, 'pp')) if f.endswith('.nii.gz')]
    hbp_files = [f for f in os.listdir(os.path.join(data_dir, 'hbp')) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for ap_file in ap_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(ap_file)
        file_name = os.path.splitext(base_name)[0]
        file_names = file_name.split('-')[0]
        
        # 检查相应的 pp 和 hbp 文件是否存在
        pp_file = f"{file_names}-pp.nii.gz"
        hbp_file = f"{file_names}-hbp.nii.gz"
        
        if pp_file in pp_files and hbp_file in hbp_files:
            # 从DataFrame中查找对应的标签
            if file_names in df['name'].values:
                # print(file_names)
                label = df.loc[df['name'] == file_names, label_name].values[0]
                data.append({
                    "image_ap": os.path.join(data_dir, 'ap', ap_file),
                    "image_pp": os.path.join(data_dir, 'pp', pp_file),
                    "image_hbp": os.path.join(data_dir, 'hbp', hbp_file),
                    "label": label,"file_name": file_names
                })
    
    return data

#使用示例
train_dir = '/root/autodl-tmp/HCC2/tumor/train' #train下面有ap pp hbp
test_dir = '/root/autodl-tmp/HCC2/tumor/val'    #val下面有ap pp hbp
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/val2.xlsx'

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
print(train_files[:5])
print(test_files[:5])

#创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

#创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=16, num_workers=2)

#定义多序列联合及3d融合模型
class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        self.model = r3d_18(pretrained=pretrained)
        # 去掉最后一层全连接层
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FeatureExtractorSwin3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorSwin3D, self).__init__()
        self.model = swin3d_s(pretrained=pretrained)
        # 去掉最后一层分类层
        self.model.head = nn.Identity()
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FusionModel(nn.Module):
    def __init__(self, num_classes):
        super(FusionModel, self).__init__()
        self.feature_extractor_resnet3d_ap = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True)
        
        self.feature_extractor_resnet3d_pp = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
        
        self.feature_extractor_resnet3d_hbp = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)
        
        # 定义一个全连接层来结合多个特征向量       
        self.fc1 = nn.Linear((512 + 768) * 3, 2048)# 每个特征提取器的输出分别为512和768维度，处理3个序列
        self.bn1 = nn.BatchNorm1d(2048)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(p=0.3)  #如果模型训练时有过拟合的倾向，可以逐步增加   
        self.fc2 = nn.Linear(2048, 1024)
        self.bn2 = nn.BatchNorm1d(1024)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(p=0.3)  # 减少Dropout值      
        self.fc3 = nn.Linear(1024, num_classes)

    def forward(self, ap, pp, hbp):
        features_resnet3d_ap = self.feature_extractor_resnet3d_ap(ap).view(ap.size(0), -1)
        features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
        
        features_resnet3d_pp = self.feature_extractor_resnet3d_pp(pp).view(pp.size(0), -1)
        features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
        
        features_resnet3d_hbp = self.feature_extractor_resnet3d_hbp(hbp).view(hbp.size(0), -1)
        features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
        
        combined_features = torch.cat(
            (features_resnet3d_ap, features_swin3d_ap, features_resnet3d_pp, features_swin3d_pp, features_resnet3d_hbp, features_swin3d_hbp), 
            dim=1
        )             

        x = self.fc1(combined_features)
        x = self.bn1(x)
        x = self.relu1(x)
        x = self.dropout1(x)        
        x = self.fc2(x)
        x = self.bn2(x)
        x = self.relu2(x)
        x = self.dropout2(x)        
        out = self.fc3(x)        
        return out
        
##resnet3d和swin3d级联深度学习模型
# class CascadeModel(nn.Module):
#     def __init__(self, num_classes):
#         super(CascadeModel, self).__init__()
#         self.r3d = r3d_18(pretrained=True)
#         self.r3d.fc = nn.Identity()  # Remove the final fully connected layer
        
#         # Assuming r3d_18 outputs 512 channels
#         self.conv = nn.Conv3d(512, 3, kernel_size=1)  # Adjust feature dimensions to fit Swin3D input
        
#         self.swin3d = swin3d_s(pretrained=True)  # Use the swin3d_s model
#         self.swin3d.head = nn.Identity()  # Remove the final classification layer
        
#         # Final classification layer
#         self.fc = nn.Linear(self.swin3d.num_features * 3, num_classes)

#     def forward(self, ap, pp, hbp):
#         # Process AP sequence
#         ap_features = self.r3d(ap)
#         print(f'ap_features shape after r3d: {ap_features.shape}')
#         ap_features = ap_features.view(ap_features.size(0), 512, 1, 1, 1)  # Reshape to 5D shape
#         print(f'ap_features shape after view: {ap_features.shape}')
#         ap_features = self.conv(ap_features)
#         print(f'ap_features shape after conv: {ap_features.shape}')
#         # No squeeze operation here
#         ap_features = self.swin3d(ap_features)
#         print(f'ap_features shape after swin3d: {ap_features.shape}')

#         # Process PP sequence
#         pp_features = self.r3d(pp)
#         print(f'pp_features shape after r3d: {pp_features.shape}')
#         pp_features = pp_features.view(pp_features.size(0), 512, 1, 1, 1)  # Reshape to 5D shape
#         print(f'pp_features shape after view: {pp_features.shape}')
#         pp_features = self.conv(pp_features)
#         print(f'pp_features shape after conv: {pp_features.shape}')
#         # No squeeze operation here
#         pp_features = self.swin3d(pp_features)
#         print(f'pp_features shape after swin3d: {pp_features.shape}')

#         # Process HBP sequence
#         hbp_features = self.r3d(hbp)
#         print(f'hbp_features shape after r3d: {hbp_features.shape}')
#         hbp_features = hbp_features.view(hbp_features.size(0), 512, 1, 1, 1)  # Reshape to 5D shape
#         print(f'hbp_features shape after view: {hbp_features.shape}')
#         hbp_features = self.conv(hbp_features)
#         print(f'hbp_features shape after conv: {hbp_features.shape}')
#         # No squeeze operation here
#         hbp_features = self.swin3d(hbp_features)
#         print(f'hbp_features shape after swin3d: {hbp_features.shape}')

#         # Concatenate all features together
#         combined_features = torch.cat((ap_features, pp_features, hbp_features), dim=1)
#         print(f'combined_features shape: {combined_features.shape}')
#         out = self.fc(combined_features)
#         return out

# 定义设备
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)

# 分类的类别数
num_classes = 2
model = FusionModel(num_classes=num_classes)# 融合模型
# model = CascadeModel(num_classes=num_classes) #级联深度模型
model
model.to(device)

# 计算类别权重，解决不平衡
# labels = [item['label'] for item in train_files]
# class_counts = np.bincount(labels)
# class_weights = 1.0 / class_counts
# class_weights = torch.tensor(class_weights, dtype=torch.float32).to(device)

# # 定义损失函数和优化器
# criterion = nn.CrossEntropyLoss(weight=class_weights)


#定义focalloss解决样本不平衡
class FocalLoss(nn.Module):
    def __init__(self, gamma=2, alpha=None, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction

        if isinstance(alpha, (float, int)):
            self.alpha = torch.Tensor([alpha, 1 - alpha])
        if isinstance(alpha, list):
            self.alpha = torch.Tensor(alpha)
    
    def forward(self, inputs, targets):
        # Convert inputs to probabilities
        inputs = F.softmax(inputs, dim=1)
        # Get the log probabilities
        log_p = torch.log(inputs)
        # Gather log probabilities for the correct class
        log_p = log_p.gather(1, targets.unsqueeze(1))
        log_p = log_p.view(-1)
        
        # Gather probabilities for the correct class
        p_t = inputs.gather(1, targets.unsqueeze(1))
        p_t = p_t.view(-1)
        
        # Apply the focal loss formula
        loss = -1 * (1 - p_t) ** self.gamma * log_p
        
        # Apply alpha if provided
        if self.alpha is not None:
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            alpha_t = self.alpha.gather(0, targets)
            loss = loss * alpha_t
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss

# 定义损失函数
criterion = FocalLoss(gamma=2, alpha=[0.3, 0.7])  #设置 gamma（调节难易样本权重的参数）、alpha（平衡不同类别权重的参数）

# 定义损失函数和优化器
# criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
# best_model_path = "/root/autodl-tmp/tumor/best_model.pth"

# 训练和测试
num_epochs = 10

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images_ap = batch['image_ap'].to(device)
        images_pp = batch['image_pp'].to(device)
        images_hbp = batch['image_hbp'].to(device)
        
        images_ap = images_ap.expand(-1, 3, -1, -1, -1)
        images_pp = images_pp.expand(-1, 3, -1, -1, -1)
        images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
        
        labels = batch['label'].long().to(device)

        optimizer.zero_grad()
        y_pred = model(images_ap, images_pp, images_hbp)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images_ap.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)
            
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            
            labels = batch['label'].long().to(device)

            y_pred = model(images_ap, images_pp, images_hbp)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 保存最优模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)

# #保存模型
PATH = '/root/autodl-tmp/HCC/tumor/resnetswin_final_model2.pth'
# PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
torch.save(model.state_dict(), PATH)

#%% 多序列影像特征和临床特征融合，3d模型融合
import os
import pandas as pd
import torch
import torch.nn as nn
from torchvision.models.video import r3d_18, swin3d_s
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped,RandFlipd,RandAffined
)
from monai.data import DataLoader, Dataset
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import torch.nn.functional as F
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

# resize成统一图像体积
resized_shape = (128, 128, 64) # r3d_18和swin3d融合用这个

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    RandRotate90d(keys=["image_ap", "image_pp", "image_hbp"], prob=0.1, spatial_axes=[0, 1]),
    # RandFlipd(keys=["image_ap", "image_pp", "image_hbp"],  prob=0.1, spatial_axis=0),
    # RandAffined(keys=["image_ap", "image_pp", "image_hbp"], prob=0.1, rotate_range=(0.1, 0.1, 0.1), scale_range=(0.1, 0.1, 0.1)), 
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])

test_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])


def TumorDataset(data_dir, label_excel, label_name):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取各个子文件夹中的图像文件列表
    ap_files = [f for f in os.listdir(os.path.join(data_dir, 'ap')) if f.endswith('.nii.gz')]
    pp_files = [f for f in os.listdir(os.path.join(data_dir, 'pp')) if f.endswith('.nii.gz')]
    hbp_files = [f for f in os.listdir(os.path.join(data_dir, 'hbp')) if f.endswith('.nii.gz')]
    
    # # 获取所有临床特征列名（除了标签列）      
    # clinical_feature_columns = df.columns[3:12].tolist()#第3到13列
    # print(clinical_feature_columns) #临床特征列名
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for ap_file in ap_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(ap_file)
        file_name = os.path.splitext(base_name)[0]
        file_names = file_name.split('-')[0]
        
        # 检查相应的 pp 和 hbp 文件是否存在
        pp_file = f"{file_names}-pp.nii.gz"
        hbp_file = f"{file_names}-hbp.nii.gz"
        
        if pp_file in pp_files and hbp_file in hbp_files:
            # 从DataFrame中查找对应的标签和临床特征
            if file_names in df['name'].values:
                # print(file_names)
                label = df.loc[df['name'] == file_names, label_name].values[0]
                clinical_data = df.loc[df['name'] == file_names, clinical_feature_columns].values[0]
                data.append({
                    "image_ap": os.path.join(data_dir, 'ap', ap_file),
                    "image_pp": os.path.join(data_dir, 'pp', pp_file),
                    "image_hbp": os.path.join(data_dir, 'hbp', hbp_file),
                    "label": label,
                    "clinical_data": clinical_data,"file_name": file_names
                })
    
    return data

#定义标签
label_name = 'PHCC'

#使用示例
train_dir = '/root/autodl-tmp/HCC2/tumor/train' #train下面有ap pp hbp
test_dir = '/root/autodl-tmp/HCC2/tumor/test'    #val下面有ap pp hbp
train_file_path = '/root/autodl-tmp/HCC2/train_clinical_radiomics.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/test_clinical_radiomics.xlsx'

df = pd.read_excel(train_file_path)
print(df.columns)

# 获取所有临床特征列名（除了标签列）
clinical_feature_columns = df.columns[3:21].tolist()#第3到12列
print(clinical_feature_columns) #临床特征列名

# 加载数据
train_files = TumorDataset(train_dir, train_file_path, label_name)
test_files = TumorDataset(test_dir, test_file_path, label_name)
print(train_files[:5])
print(test_files[:5])

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=2)


#定义ResNet3D多序列联合模型1，特征拼接在fc1
#融合模型定义
# class FeatureExtractorResNet3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorResNet3D, self).__init__()
#         self.model = r3d_18(pretrained=pretrained)
#         # 去掉最后一层全连接层
#         self.model = nn.Sequential(*list(self.model.children())[:-1])
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes, clinical_feature_dim):
#         super(FusionModel, self).__init__()
#         self.feature_extractor_resnet3d_ap = FeatureExtractorResNet3D(pretrained=True)  
#         self.feature_extractor_resnet3d_pp = FeatureExtractorResNet3D(pretrained=True)
#         self.feature_extractor_resnet3d_hbp = FeatureExtractorResNet3D(pretrained=True)
                
#         # 定义一个全连接层来结合多个特征向量
#         self.fc1 = nn.Linear(512 * 3 + clinical_feature_dim, 1024)  # 每个特征提取器的输出分别为512和768维度，处理3个序列，加上临床特征维度
#         self.bn1 = nn.BatchNorm1d(1024)
#         self.relu1 = nn.ReLU()
#         self.dropout1 = nn.Dropout(p=0.3)  #如果模型训练时有过拟合的倾向，可以逐步增加   
#         self.fc2 = nn.Linear(1024, 512)
#         self.bn2 = nn.BatchNorm1d(512)
#         self.relu2 = nn.ReLU()
#         # self.dropout2 = nn.Dropout(p=0.3)  # 减少Dropout值      
#         self.fc3 = nn.Linear(512, num_classes)

#     def forward(self, ap, pp, hbp, clinical_data):
#         features_resnet3d_ap = self.feature_extractor_resnet3d_ap(ap).view(ap.size(0), -1)
#         features_resnet3d_pp = self.feature_extractor_resnet3d_pp(pp).view(pp.size(0), -1)
#         features_resnet3d_hbp = self.feature_extractor_resnet3d_hbp(hbp).view(hbp.size(0), -1)
#         combined_features = torch.cat(
#             (features_resnet3d_ap, features_resnet3d_pp, features_resnet3d_hbp, clinical_data), 
#             dim=1
#         )    

#         x = self.fc1(combined_features)
#         x = self.bn1(x)
#         x = self.relu1(x)
#         x = self.dropout1(x)        
#         x = self.fc2(x)
#         x = self.bn2(x)
#         x = self.relu2(x)
#         # x = self.dropout2(x)        
#         out = self.fc3(x)        
#         return out


#定义ResNet3D多序列联合模型2，特征拼接在fc2
# class FeatureExtractorResNet3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorResNet3D, self).__init__()
#         self.model = r3d_18(pretrained=pretrained)
#         # 去掉最后一层全连接层
#         self.model = nn.Sequential(*list(self.model.children())[:-1])
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes, clinical_feature_dim):
#         super(FusionModel, self).__init__()
#         self.feature_extractor_resnet3d_ap = FeatureExtractorResNet3D(pretrained=True)  
#         self.feature_extractor_resnet3d_pp = FeatureExtractorResNet3D(pretrained=True)
#         self.feature_extractor_resnet3d_hbp = FeatureExtractorResNet3D(pretrained=True)
                
#         # 定义一个全连接层来结合多个特征向量
#         self.fc1 = nn.Linear(512 * 3, 512)  # 每个特征提取器的输出为512维度，处理3个序列
#         self.bn1 = nn.BatchNorm1d(512)
#         self.relu1 = nn.ReLU()
#         self.dropout1 = nn.Dropout(p=0.3)  # 如果模型训练时有过拟合的倾向，可以逐步增加   
        
#         # 在这里将临床特征拼接到其他特征之前
#         self.fc2 = nn.Linear(512 + clinical_feature_dim, 512+ clinical_feature_dim)
#         self.bn2 = nn.BatchNorm1d(512+ clinical_feature_dim)
#         self.relu2 = nn.ReLU()        
#         self.fc3 = nn.Linear(512+ clinical_feature_dim, num_classes)

#     def forward(self, ap, pp, hbp, clinical_data):
#         features_resnet3d_ap = self.feature_extractor_resnet3d_ap(ap).view(ap.size(0), -1)
#         features_resnet3d_pp = self.feature_extractor_resnet3d_pp(pp).view(pp.size(0), -1)
#         features_resnet3d_hbp = self.feature_extractor_resnet3d_hbp(hbp).view(hbp.size(0), -1)
        
#         combined_features = torch.cat(
#             (features_resnet3d_ap, features_resnet3d_pp, features_resnet3d_hbp), 
#             dim=1
#         )
        
#         x = self.fc1(combined_features)
#         x = self.bn1(x)
#         x = self.relu1(x)
#         x = self.dropout1(x)
        
#         # 将临床特征拼接到其他特征之前
#         combined_with_clinical = torch.cat((x, clinical_data), dim=1)        
#         x = self.fc2(combined_with_clinical)
#         x = self.bn2(x)
#         x = self.relu2(x)        
#         out = self.fc3(x)
#         return out

# Swin3D多序列联合模型1，特征拼接在fc1
# class FeatureExtractorSwin3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorSwin3D, self).__init__()
#         self.model = swin3d_s(pretrained=pretrained)
#         # 去掉最后一层分类层
#         self.model.head = nn.Identity()
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes, clinical_feature_dim):
#         super(FusionModel, self).__init__()        
#         self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True) 
#         self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
#         self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)
        
#         # 定义一个全连接层来结合多个特征向量
#         self.fc1 = nn.Linear(768 * 3 + clinical_feature_dim, 1024)  # 每个特征提取器的输出分别为512和768维度，处理3个序列，加上临床特征维度
#         self.bn1 = nn.BatchNorm1d(1024)
#         self.relu1 = nn.ReLU()
#         self.dropout1 = nn.Dropout(p=0.3)  #如果模型训练时有过拟合的倾向，可以逐步增加   
#         self.fc2 = nn.Linear(1024, 768)
#         self.bn2 = nn.BatchNorm1d(768)
#         self.relu2 = nn.ReLU()
#         # self.dropout2 = nn.Dropout(p=0.3)  # 减少Dropout值      
#         self.fc3 = nn.Linear(768, num_classes)

#     def forward(self, ap, pp, hbp, clinical_data):
#         features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
#         features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
#         features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
#         combined_features = torch.cat(
#             (features_swin3d_ap, features_swin3d_pp, features_swin3d_hbp, clinical_data), 
#             dim=1
#         )    
#         x = self.fc1(combined_features)
#         x = self.bn1(x)
#         x = self.relu1(x)
#         x = self.dropout1(x)        
#         x = self.fc2(x)
#         x = self.bn2(x)
#         x = self.relu2(x)
#         # x = self.dropout2(x)        
#         out = self.fc3(x)        
#         return out    

# Swin3D多序列联合模型2，特征拼接在fc2
# class FeatureExtractorSwin3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorSwin3D, self).__init__()
#         self.model = swin3d_s(pretrained=pretrained)
#         # 去掉最后一层分类层
#         self.model.head = nn.Identity()
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes, clinical_feature_dim):
#         super(FusionModel, self).__init__()        
#         self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True) 
#         self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
#         self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)
        
#         # 定义一个全连接层来结合多个特征向量
#         self.fc1 = nn.Linear(768 * 3, 768)  # 每个特征提取器的输出分别为768维度，处理3个序列
#         self.bn1 = nn.BatchNorm1d(768)
#         self.relu1 = nn.ReLU()
#         self.dropout1 = nn.Dropout(p=0.3)  # 如果模型训练时有过拟合的倾向，可以逐步增加   
        
#         # 在这里将临床特征拼接到fc2层
#         self.fc2 = nn.Linear(768 + clinical_feature_dim, 768)
#         self.bn2 = nn.BatchNorm1d(768)
#         self.relu2 = nn.ReLU()        
#         self.fc3 = nn.Linear(768, num_classes)

#     def forward(self, ap, pp, hbp, clinical_data):
#         features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
#         features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
#         features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
        
#         combined_features = torch.cat(
#             (features_swin3d_ap, features_swin3d_pp, features_swin3d_hbp), 
#             dim=1
#         )    
        
#         x = self.fc1(combined_features)
#         x = self.bn1(x)
#         x = self.relu1(x)
#         x = self.dropout1(x)
        
#         # 将临床特征拼接到其他特征之前
#         combined_with_clinical = torch.cat((x, clinical_data), dim=1)        
#         x = self.fc2(combined_with_clinical)
#         x = self.bn2(x)
#         x = self.relu2(x)        
#         out = self.fc3(x)        
#         return out


#resnet-swin融合模型1，特征拼接在fc1
# class FeatureExtractorResNet3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorResNet3D, self).__init__()
#         self.model = r3d_18(pretrained=pretrained)
#         # 去掉最后一层全连接层
#         self.model = nn.Sequential(*list(self.model.children())[:-1])
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FeatureExtractorSwin3D(nn.Module):
#     def __init__(self, pretrained=True):
#         super(FeatureExtractorSwin3D, self).__init__()
#         self.model = swin3d_s(pretrained=pretrained)
#         # 去掉最后一层分类层
#         self.model.head = nn.Identity()
#         # 冻结参数
#         for param in self.model.parameters():
#             param.requires_grad = False
        
#     def forward(self, x):
#         x = self.model(x)
#         return x

# class FusionModel(nn.Module):
#     def __init__(self, num_classes, clinical_feature_dim):
#         super(FusionModel, self).__init__()
#         self.feature_extractor_resnet3d_ap = FeatureExtractorResNet3D(pretrained=True)
#         self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True)
        
#         self.feature_extractor_resnet3d_pp = FeatureExtractorResNet3D(pretrained=True)
#         self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
        
#         self.feature_extractor_resnet3d_hbp = FeatureExtractorResNet3D(pretrained=True)
#         self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)
        
#         # 定义一个全连接层来结合多个特征向量
#         self.fc1 = nn.Linear((512 + 768) * 3 + clinical_feature_dim, 2048)  # 每个特征提取器的输出分别为512和768维度，处理3个序列，加上临床特征维度
#         self.bn1 = nn.BatchNorm1d(2048)
#         self.relu1 = nn.ReLU()
#         self.dropout1 = nn.Dropout(p=0.3)  #如果模型训练时有过拟合的倾向，可以逐步增加   
#         self.fc2 = nn.Linear(2048, 1024)
#         self.bn2 = nn.BatchNorm1d(1024)
#         self.relu2 = nn.ReLU()
#         # self.dropout2 = nn.Dropout(p=0.3)  # 减少Dropout值      
#         self.fc3 = nn.Linear(1024, num_classes)

#     def forward(self, ap, pp, hbp, clinical_data):
#         features_resnet3d_ap = self.feature_extractor_resnet3d_ap(ap).view(ap.size(0), -1)
#         features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
        
#         features_resnet3d_pp = self.feature_extractor_resnet3d_pp(pp).view(pp.size(0), -1)
#         features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
        
#         features_resnet3d_hbp = self.feature_extractor_resnet3d_hbp(hbp).view(hbp.size(0), -1)
#         features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
        
#         combined_features = torch.cat(
#             (features_resnet3d_ap, features_swin3d_ap, features_resnet3d_pp, features_swin3d_pp, features_resnet3d_hbp, features_swin3d_hbp, clinical_data), 
#             dim=1
#         )    

#         x = self.fc1(combined_features)
#         x = self.bn1(x)
#         x = self.relu1(x)
#         x = self.dropout1(x)        
#         x = self.fc2(x)
#         x = self.bn2(x)
#         x = self.relu2(x)
#         # x = self.dropout2(x)        
#         out = self.fc3(x)        
#         return out

#Resnet-swin融合模型2，特征拼接在fc2，512个特征
class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        self.model = r3d_18(pretrained=pretrained)
        # 去掉最后一层全连接层
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FeatureExtractorSwin3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorSwin3D, self).__init__()
        self.model = swin3d_s(pretrained=pretrained)
        # 去掉最后一层分类层
        self.model.head = nn.Identity()
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

class FusionModel(nn.Module):
    def __init__(self, num_classes, clinical_feature_dim):
        super(FusionModel, self).__init__()
        self.feature_extractor_resnet3d_ap = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d_ap = FeatureExtractorSwin3D(pretrained=True)
        
        self.feature_extractor_resnet3d_pp = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d_pp = FeatureExtractorSwin3D(pretrained=True)
        
        self.feature_extractor_resnet3d_hbp = FeatureExtractorResNet3D(pretrained=True)
        self.feature_extractor_swin3d_hbp = FeatureExtractorSwin3D(pretrained=True)
        
        # 定义一个全连接层来结合多个特征向量
        self.fc1 = nn.Linear((512 + 768) * 3, 512)  # 每个特征提取器的输出分别为512和768维度，处理3个序列
        self.bn1 = nn.BatchNorm1d(512)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(p=0.3)  # 如果模型训练时有过拟合的倾向，可以逐步增加   
        
        # 在这里将临床特征拼接到其他特征之前
        self.fc2 = nn.Linear(512 + clinical_feature_dim, 512)
        self.bn2 = nn.BatchNorm1d(512)
        self.relu2 = nn.ReLU()        
        self.fc3 = nn.Linear(512, num_classes)

    def forward(self, ap, pp, hbp, clinical_data):
        features_resnet3d_ap = self.feature_extractor_resnet3d_ap(ap).view(ap.size(0), -1)
        features_swin3d_ap = self.feature_extractor_swin3d_ap(ap).view(ap.size(0), -1)
        
        features_resnet3d_pp = self.feature_extractor_resnet3d_pp(pp).view(pp.size(0), -1)
        features_swin3d_pp = self.feature_extractor_swin3d_pp(pp).view(pp.size(0), -1)
        
        features_resnet3d_hbp = self.feature_extractor_resnet3d_hbp(hbp).view(hbp.size(0), -1)
        features_swin3d_hbp = self.feature_extractor_swin3d_hbp(hbp).view(hbp.size(0), -1)
        
        combined_features = torch.cat(
            (features_resnet3d_ap, features_swin3d_ap, features_resnet3d_pp, features_swin3d_pp, features_resnet3d_hbp, features_swin3d_hbp), 
            dim=1
        )
        
        x = self.fc1(combined_features)
        x = self.bn1(x)
        x = self.relu1(x)
        x = self.dropout1(x)
        
        # 将临床特征拼接到其他特征之前
        combined_with_clinical = torch.cat((x, clinical_data), dim=1)        
        x = self.fc2(combined_with_clinical)
        x = self.bn2(x)
        x = self.relu2(x)        
        out = self.fc3(x)
        return out


# 分类的类别数
num_classes = 2
clinical_feature_dim = len(train_files[0]['clinical_data'])  # 临床特征的维度
print(clinical_feature_dim)
model = FusionModel(num_classes=num_classes, clinical_feature_dim=clinical_feature_dim)
model

# 将模型移动到GPU
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)
model.to(device)

# 计算类别权重，解决不平衡，效果一般
# labels = [item['label'] for item in train_files]
# class_counts = np.bincount(labels)
# class_weights = 1.0 / class_counts
# class_weights = torch.tensor(class_weights, dtype=torch.float32).to(device)

# # 定义损失函数和优化器
# criterion = nn.CrossEntropyLoss(weight=class_weights)


#定义focalloss解决样本不平衡，效果好
class FocalLoss(nn.Module):
    def __init__(self, gamma=2, alpha=None, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction

        if isinstance(alpha, (float, int)):
            self.alpha = torch.Tensor([alpha, 1 - alpha])
        if isinstance(alpha, list):
            self.alpha = torch.Tensor(alpha)
    
    def forward(self, inputs, targets):
        # Convert inputs to probabilities
        inputs = F.softmax(inputs, dim=1)
        # Get the log probabilities
        log_p = torch.log(inputs)
        # Gather log probabilities for the correct class
        log_p = log_p.gather(1, targets.unsqueeze(1))
        log_p = log_p.view(-1)
        
        # Gather probabilities for the correct class
        p_t = inputs.gather(1, targets.unsqueeze(1))
        p_t = p_t.view(-1)
        
        # Apply the focal loss formula
        loss = -1 * (1 - p_t) ** self.gamma * log_p
        
        # Apply alpha if provided
        if self.alpha is not None:
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            alpha_t = self.alpha.gather(0, targets)
            loss = loss * alpha_t
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss

criterion = FocalLoss(gamma=2, alpha=[0.3, 0.7])  #设置 gamma（调节难易样本权重的参数）、alpha（平衡不同类别权重的参数）

# 定义损失函数和优化器
# criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
# best_model_path = "/hy-tmp/tumor/ap_best_model.pth"

#%% 训练和测试
num_epochs = 10

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images_ap = batch['image_ap'].to(device)
        images_pp = batch['image_pp'].to(device)
        images_hbp = batch['image_hbp'].to(device)
        
        images_ap = images_ap.expand(-1, 3, -1, -1, -1)
        images_pp = images_pp.expand(-1, 3, -1, -1, -1)
        images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
        
        labels = batch['label'].long().to(device)
        clinical_data = torch.tensor(batch['clinical_data'], dtype=torch.float32).to(device)       

        optimizer.zero_grad()
        y_pred = model(images_ap, images_pp, images_hbp, clinical_data)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images_ap.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)
            
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            
            labels = batch['label'].long().to(device)
            clinical_data = torch.tensor(batch['clinical_data'], dtype=torch.float32).to(device)           

            y_pred = model(images_ap, images_pp, images_hbp, clinical_data)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')
    
#     # 如果当前测试准确率高于历史最佳准确率，则保存当前模型
#     if accuracy > best_accuracy:
#         best_accuracy = accuracy
#         torch.save(model.state_dict(), best_model_path)
#         print(f'Saved Best Model with Accuracy: {best_accuracy:.3f}%')

# print(f'Best Testing Accuracy: {best_accuracy:.3f}%')

# #保存模型
PATH = '/root/autodl-tmp/HCC2/resnetswin_radiomics_model.pth'
# PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
torch.save(model.state_dict(), PATH)

#%% 3d CNN 深度学习代码 成功
#torchio图像预处理和数据增强代码
# pip install torchio
import torch
import torchio as tio
import os       
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models as models
import pandas as pd
import numpy as np
import SimpleITK as sitk
import torchvision.models.video 

# 调整图像大小
resized_shape = (256, 256, 64)  

# 定义转换操作
train_transforms = tio.Compose([
    tio.RescaleIntensity(out_min_max=(0, 1)), # 强度重缩放到[0, 1]
    tio.RandomAffine( # 随机仿射变换
        scales=(0.9, 1.1),
        degrees=(10),
        translation=(5, 5, 5),
        isotropic=True,
        center='image',
    ),
    tio.RandomFlip(axes=(0,)), # 随机翻转
    # tio.RandomElasticDeformation(num_control_points=5, max_displacement=6),
    tio.RandomNoise(mean=0, std=0.1),
    tio.RandomMotion(),
    tio.CropOrPad(target_shape=resized_shape),
])

test_transforms = tio.Compose([
    tio.RescaleIntensity(out_min_max=(0, 1)),
    tio.CropOrPad(target_shape=resized_shape)
])

# 函数来获取图像文件路径和标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_file):
    label_df = pd.read_excel(label_file)  # 从Excel文件中读取标签信息
    subjects = []
    for f in os.listdir(data_dir):
        if f.endswith('.nii.gz'):
            image_path = os.path.join(data_dir, f)
            file_name = os.path.splitext(f)[0]  # 获取文件名（不包含扩展名）
            label_row = label_df[label_df['name'] == file_name.split('-')[0]]  # 根据文件名匹配标签行
            if not label_row.empty:
                label = label_row[label_name].values[0]  # 获取标签值
                subjects.append(tio.Subject(image=tio.ScalarImage(image_path), label=label))
    return subjects

# 加载数据和标签
train_dir = '/hy-tmp/tumor/ap/train'   
test_dir = '/hy-tmp/tumor/ap/val'
train_file_path = '/hy-tmp/data/train.xlsx'  
test_file_path = '/hy-tmp/data/val.xlsx'

# train_dir = '/root/autodl-tmp/HCC/tumor/ap/train'
# test_dir = '/root/autodl-tmp/HCC/tumor/ap/val'
# train_file_path = '/root/autodl-tmp/HCC/data/train.xlsx'
# test_file_path = '/root/autodl-tmp/HCC/data/val.xlsx'

train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

train_dataset = tio.SubjectsDataset(train_files, transform=train_transforms)
test_dataset = tio.SubjectsDataset(test_files, transform=test_transforms)

# 创建数据加载器
train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=2)
test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=2, num_workers=2)


#目前有很多模型 r3d_18、mc3_18, s3d ，mvit_v1_b;swin3d_b，swin3d_s，r2plus1d_18
model = models.video.swin3d_s(pretrained=True) 
# model = torchvision.models.video.r3d_18(pretrained=True) 
# model = models.video.mvit_v1_b(pretrained=True)
# model = models.video.s3d(pretrained=True)
model

model.parameters
for param in model.parameters():
    param.requires_grad = False

#数据集中分类的类别数
num_classes = 2  # num_classes表示输出特征
# model.fc = nn.Linear(model.fc.in_features, num_classes) #r3d_18
model.head = nn.Linear(model.head.in_features, num_classes) #swin3d_b
# model.head[1] = nn.Linear(model.head[1].in_features, num_classes) #mvit_v1_b
model

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

criterion = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
optimizer = torch.optim.Adam(model.head.parameters(), lr=0.0001) #swin3d_b
# optimizer = torch.optim.Adam(model.head[1].parameters(), lr=0.0001) #mvit_v1_b

# optimizer = torch.optim.Adam(model.parameters(), lr=0.0001) #非预训练模型
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

num_epochs = 20

# 训练循环
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'][tio.DATA].float().to(device)  # 直接获取处理后的图像张量
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'][tio.DATA].float().to(device)  # 直接获取处理后的图像张量
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

#%% 3d nii图像image和label获取及图像预处理，padding到同样的大小
#批量安装 pip install pandas SimpleITK torchio monai openpyxl
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
import math
from functools import partial
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import torchvision.models as models
import nibabel as nib
import pandas as pd
import numpy as np
import os
import SimpleITK as sitk
#import cv2
import glob
from torch.optim.lr_scheduler import ExponentialLR
import torchio as tio

# train_dir = '/root/autodl-tmp/train/*.nii.gz'  # '/mnt/hcc/ap'
# train_image_list = glob.glob(train_dir)

def get_file_list(directory):
    file_list = [os.path.join(directory, file) for file in os.listdir(directory) if file.endswith('.nii.gz')]
    file_list.sort()  # 按照文件名排序
    return file_list

def get_label_list(file_path):   
    label_file = pd.read_excel(file_path) # 读取Excel文件   
    name_values = label_file['name'].tolist() # 从'name'和'VETC'列中获取数值
    label_values = label_file['PHCC'].tolist()   
    sorted_label_values = [label for _, label in sorted(zip(name_values, label_values))] # 根据'name'列对数值进行排序    
    label_list = sorted_label_values# 将排序后的VETC数值存储在label_list中
    return label_list

train_dir = '/root/autodl-tmp/tumor/ap/train'
test_dir = '/root/autodl-tmp/tumor/ap/val'
train_file_path = '/root/autodl-tmp/data/train.xlsx'
test_file_path = '/root/autodl-tmp/data/val.xlsx'

# train_dir = '/hy-tmp/tumor/ap/train'   
# test_dir = '/hy-tmp/tumor/ap/val'
# train_file_path = '/hy-tmp/data/train.xlsx'  
# test_file_path = '/hy-tmp/data/val.xlsx'


train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5])
print(len(train_image_list));print(len(test_image_list))

# 调用函数获取标签列表
train_label_list = get_label_list(train_file_path)
test_label_list = get_label_list(test_file_path)
# 打印标签列表
print(train_label_list[:5])
print(len(train_label_list))
print(len(test_label_list))

# 获取最大的图像的shape
image_list = train_image_list + test_image_list

max_shape = (0, 0, 0)
for image_path in image_list:
    image = nib.load(image_path)
    shape = image.shape
    max_shape = tuple(max(max_shape[i], shape[i]) for i in range(3))
print("Maximum shape of the nii images:", max_shape)
max_num = max_shape[2]
max_size = max(max_shape[0], max_shape[1])
print(max_size)
# max_num = 64  #要比实际最大的要大
# max_size = max(224, 224)
# print(max_size)

# 定义数据集，适用于单通道或多通道输入，不进行transform 
# class TumorDataset(Dataset):
#     def __init__(self, image_list, label_list, transform=None):
#         self.image_list = image_list
#         self.label_list = label_list
#         self.transform = transform                

#     def __len__(self):
#         return len(self.image_list)

#     def __getitem__(self, index):
#         image_path = self.image_list[index]
#         label = self.label_list[index]
#         image = sitk.ReadImage(image_path)
#         img_array = sitk.GetArrayFromImage(image)
#         img_array = img_array.astype(np.float32)        
#         if img_array.ndim == 3:
#            img_array = np.stack([img_array] * 3, axis=0)  # 多通道处理
#         else:
#            img_array = np.stack([img_array] * 1, axis=0)  # 单通道处理

#         pad_shape = calculate_pad_shape(img_array)  # 计算填充形状的函数，根据具体需求实现
#         img_array = np.pad(img_array, pad_shape, mode='constant')
#         # print("填充后的图像大小和形状：", img_array.shape) # （3，70，253，253）
#         tensor_image = torch.from_numpy(img_array).float() 
#         label_array = np.array(label)
#         tensor_label = torch.from_numpy(label_array).float()        
#         return tensor_image, tensor_label

# #定义数据集，适用于单通道或多通道输入，并进行transform 
class TumorDataset(Dataset):
    def __init__(self, image_list, label_list, transform=None):
        self.image_list = image_list
        self.label_list = label_list
        self.transform = transform    

    def __len__(self):
        return len(self.image_list)

    def __getitem__(self, index):
        image_path = self.image_list[index]
        label = self.label_list[index]
        image = sitk.ReadImage(image_path)
        img_array = sitk.GetArrayFromImage(image)
        img_array = img_array.astype(np.float32)        
        if img_array.ndim == 3:  #3d图像
        #    img_array = np.stack([img_array] * 1, axis=0)  # 3d图像单通道 (1, 26, 56, 66)
           img_array = np.stack([img_array] * 3, axis=0)  # 3d图像3通道 (3, 26, 56, 66)
        else:  #2d图像
           img_array = np.stack([img_array] * 1, axis=0)  # 适用2d图像堆叠为3d  (26, 56, 66)

        pad_shape = calculate_pad_shape(img_array)  # 计算填充形状的函数，根据具体需求实现        
        img_array = np.pad(img_array, pad_shape, mode='constant')
        # print("填充后的图像大小和形状：", img_array.shape) # （3，70，253，253）
        tensor_image = torch.from_numpy(img_array).float()            
        if self.transform: # Apply transform 
            tensor_image = self.transform(tensor_image)           
        label_array = np.array(label)
        tensor_label = torch.from_numpy(label_array).float() #(C, 26, 56, 66)       
        return tensor_image, tensor_label

def calculate_pad_shape(img_array):
    if img_array.ndim == 4:  # 多通道输入3d图像 (C, D, H, W)
        pad_shape = ((0, 0),
                     (0, max(0, max_num - img_array.shape[1])),  # 图像数量大小可以修改
                     (0, max(0, max_size - img_array.shape[2])),  # 图像大小可以修改
                     (0, max(0, max_size - img_array.shape[3])))  # 图像大小可以修改
    else: # 单通道输入，2d图像堆叠
        pad_shape = ((0, max(0, max_num - img_array.shape[0])),  # 图像数量大小可以修改
                     (0, max(0, max_size - img_array.shape[1])),  # 图像大小可以修改
                     (0, max(0, max_size - img_array.shape[2])))  # 图像大小可以修改
    return pad_shape


# 定义转换操作
train_transforms = tio.Compose([
    tio.RescaleIntensity(out_min_max=(0, 1)), # 强度重缩放到[0, 1]
    tio.RandomAffine( # 随机仿射变换
        scales=(0.95, 1.05),# 减小缩放的范围
        # degrees=(5), # 旋转的角度
        # translation=(5, 5, 5),# 减小平移的距离
        isotropic=True,
        center='image',
    ),
    tio.RandomFlip(axes=(0,)), # 随机翻转
    # tio.RandomElasticDeformation(num_control_points=10, max_displacement=15),
    tio.RandomNoise(mean=0, std=0.05),
    tio.RandomMotion(),
    # tio.CropOrPad(target_shape=(max_size,max_size, max_num)),
])

test_transforms = tio.Compose([
    tio.RescaleIntensity(out_min_max=(0, 1)),
    # tio.CropOrPad(target_shape=(max_size, max_size, max_num))
])


# train_dataset = TumorDataset(train_image_list,train_label_list, transform=None)
# test_dataset = TumorDataset(test_image_list,test_label_list, transform=None)
train_dataset = TumorDataset(train_image_list,train_label_list, transform=train_transforms)
test_dataset = TumorDataset(test_image_list,test_label_list, transform=test_transforms)

train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, num_workers=2)

#%% 基于monoai的数据预处理
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

#Resize成统一图像体积
resized_shape = (224,224, 70) #r3d_18和swin3d融合用这个
# resized_shape = (224, 224, 16) #r3d_18和mvit融合用这个

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_shape),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.5, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label,"file_name": file_names})    
    return data

# 使用示例
# train_dir = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor\ap\train'
# test_dir = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor\ap\val'
# train_file_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\train.xlsx'
# test_file_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\val.xlsx'

train_dir = '/root/autodl-tmp/HCC2/tumor/train/ap' #train下面有ap pp hbp
test_dir = '/root/autodl-tmp/HCC2/tumor/val/ap'    #val下面有ap pp hbp
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/val2.xlsx'

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=2)

#%% 3d nii格式图像进行3d resnet(Med3d)预训练模型，已成功  https://github.com/Tencent/MedicalNet
#根据剪切后的tumor图像nii格式进行训练 
# 创建Resnet3D分类模型并加载预训练模型 https://github.com/Tencent/MedicalNet
import sys
# sys.path.append(r'D:\anaconda3\Lib\site-packages\MedicalNet-master')
# sys.path.append(r'D:\anaconda3\envs\MedicalNet-master')
# sys.path.append('/root/autodl-tmp/MedicalNet-master')  #把models文件夹的上一级文件夹路径加到系统变量
sys.path.append('/root/autodl-tmp/HCC2/tumor/MedicalNet-master') 
sys.path
from models import resnet  #把models文件夹的上一级文件夹路径加到系统变量


#generate_model根据项目修改的，跟MedicalNet-master项目中的不一样
def generate_model(model_type='resnet', model_depth=50,
                   input_W=224, input_H=224, input_D=70, resnet_shortcut='B',
                   no_cuda=False, gpu_id=[0],
                   pretrain_path = '/root/autodl-tmp/resnet_50.pth',
                   nb_class=1):
    assert model_type in [
        'resnet'
    ]

    if model_type == 'resnet':
        assert model_depth in [10, 18, 34, 50, 101, 152, 200]

    if model_depth == 10:
        model = resnet.resnet10(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 256
    elif model_depth == 18:
        model = resnet.resnet18(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 512
    elif model_depth == 34:
        model = resnet.resnet34(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 512
    elif model_depth == 50:
        model = resnet.resnet50(         #此处也修改，之前代码为resnet.resnet50
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 101:
        model = resnet.resnet101(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 152:
        model = resnet.resnet152(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048
    elif model_depth == 200:
        model = resnet.resnet200(
            sample_input_W=input_W,
            sample_input_H=input_H,
            sample_input_D=input_D,
            shortcut_type=resnet_shortcut,
            no_cuda=no_cuda,
            num_seg_classes=1)
        fc_input = 2048

    model.conv_seg = nn.Sequential(nn.AdaptiveAvgPool3d((1, 1, 1)), nn.Flatten(),
                                   nn.Linear(in_features=fc_input, out_features=nb_class, bias=True))

    if not no_cuda:
        if len(gpu_id) > 1:
            model = model.cuda()
            model = nn.DataParallel(model, device_ids=gpu_id)
            net_dict = model.state_dict()
        else:
            import os
            os.environ["CUDA_VISIBLE_DEVICES"]=str(gpu_id[0])
            model = model.cuda()
            model = nn.DataParallel(model, device_ids=None)
            net_dict = model.state_dict()
    else:
        net_dict = model.state_dict()

    print('loading pretrained model {}'.format(pretrain_path))
    pretrain = torch.load(pretrain_path)
    pretrain_dict = {k: v for k, v in pretrain['state_dict'].items() if k in net_dict.keys()}
    # k 是每一层的名称，v是权重数值
    net_dict.update(pretrain_dict) #字典 dict2 的键/值对更新到 dict 里。
    model.load_state_dict(net_dict) #model.load_state_dict()函数把加载的权重复制到模型的权重中去

    print("-------- pre-train model load successfully --------")

    return model

model = generate_model(model_type='resnet', model_depth=50,#注意修改
                   input_W=224, input_H=224, input_D=70, resnet_shortcut='B',
                   no_cuda=False, gpu_id=[0],
                   # pretrain_path = '/root/autodl-tmp/resnet_50.pth',
                   # pretrain_path = '/root/autodl-tmp/resnet_34.pth',
                   pretrain_path = '/root/autodl-tmp/HCC2/tumor/MedicalNet-master/pretrain/resnet_50.pth', #预训练权重要与前面的深度匹配
                   nb_class=2)

# 将模型移动到GPU
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)
model.to(device)

## 使用较小的学习率进行微调
from torch.optim import lr_scheduler
# optimizer  = torch.optim.Adam(model.parameters(), lr=0.005)

# 定义L2正则化项的权重；定义优化器，并添加L2正则化
# l2_lambda = 0.01
# optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=l2_lambda)


# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/hy-tmp/tumor/ap_best_model.pth"

# 训练和测试
num_epochs = 20

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量
        images = images.expand(-1, 1, -1, -1, -1)  # 扩展1通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量
            images = images.expand(-1, 1, -1, -1, -1)  # 扩展1通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 检查是否是最佳模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)
    #     print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

# #保存模型
# PATH = '/root/autodl-tmp/HCC/ap_r3d-swin3d.pth'
# # PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
# torch.save(model.state_dict(), PATH)

## 加载最优模型
# model.load_state_dict(torch.load(best_model_path))
# print(f'Loaded best model with accuracy: {best_accuracy:.3f}%')


# Train and test the model
# num_epochs = 30
# for epoch in range(num_epochs):
#     model.train()
#     running_loss = 0.0
#     correct = 0
#     total = 0
#     for batch_idx, (images, labels) in enumerate(train_loader):
#         images = images.float().to(device)
#         labels = labels.long().to(device)
#         optimizer.zero_grad()
#         y_pred = model(images)
#         loss = criterion(y_pred, labels)
#         loss.backward()
#         optimizer.step()
#         with torch.no_grad():
#             y_pred = torch.argmax(y_pred, dim=1)
#             correct += (y_pred == labels).sum().item()
#             total += labels.size(0)
#             running_loss += loss.item() * images.size(0)
#         torch.cuda.empty_cache()
#     train_loss = running_loss / len(train_dataset)
#     train_acc = 100 * correct / total
#     print(f'Training Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Training Accuracy: {train_acc:.2f}%')
#     model.eval()
#     correct = 0
#     total = 0
#     best_acc = 0.0
#     with torch.no_grad():
#         for images, labels in test_loader:
#             images = images.float().to(device)
#             labels = labels.long().to(device)
#             y_pred = model(images)
#             _, predicted = torch.max(y_pred, 1)
#             total += labels.size(0)
#             correct += (predicted == labels).sum().item()
#             running_loss += loss.item() * images.size(0)
#             torch.cuda.empty_cache()
#     test_loss = running_loss / len(test_dataset)
#     test_acc = 100 * correct / total

#     if test_acc > best_acc:
#             best_acc = test_acc
#             torch.save(model.state_dict(), '/root/autodl-tmp/best_model.pt')

#     print(f'Testing Epoch [{epoch+1}/{num_epochs}],Test Loss: {test_loss:.4f}, Testing Accuracy: {test_acc:.2f}%')

#%%基于monoai自带的多个3D CNN模型  已成功
import torch.nn.functional as F
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

resized_mask = (128, 128, 64)

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_mask),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.1, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_mask),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label})    
    return data


# 使用示例
train_dir = '/root/autodl-tmp/HCC2/tumor/train/ap'
test_dir = '/root/autodl-tmp/HCC2/tumor/val/ap'
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/val2.xlsx'

# train_dir = '/hy-tmp/tumor/ap/train'   
# test_dir = '/hy-tmp/tumor/ap/val'
# train_file_path = '/hy-tmp/data/train.xlsx'  
# test_file_path = '/hy-tmp/data/val.xlsx'

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=2, num_workers=2)

#基于monai创建3d CNN模型
import monai.networks.nets as nets
print(dir(nets))# 查看所有的3D模型

#3d DenseNet121模型
# model = monai.networks.nets.DenseNet121(spatial_dims=3, in_channels=3, out_channels=2)
#3d SEResNet50模型
# model = monai.networks.nets.SEResNet50(spatial_dims=3, in_channels=3, num_classes=2)
#3d resnet34,50,101,152模型
model = monai.networks.nets.resnet50(spatial_dims=3, num_classes=2)

# 使用 MONAI 提供的3D EfficientNet 默认配置
# blocks_args_str = [
#     'r1_k3_s11_e1_i32_o16_se0.25',
#     'r2_k3_s22_e6_i16_o24_se0.25',
#     'r2_k5_s22_e6_i24_o40_se0.25',
#     'r3_k3_s22_e6_i40_o80_se0.25',
#     'r3_k5_s11_e6_i80_o112_se0.25',
#     'r4_k5_s22_e6_i112_o192_se0.25',
#     'r1_k3_s11_e6_i192_o320_se0.25',
# ]

# 初始化3D EfficientNet 模型
# model = monai.networks.nets.EfficientNet(spatial_dims=3, in_channels=3, num_classes=2, blocks_args_str=blocks_args_str).to(device)

# 3D VIT 模型
model = nets.ViT(
    in_channels=3,         # 输入通道数
    img_size=(128, 128, 64),  # 输入图像的大小
    patch_size=(16, 16, 16),   # patch 的大小
    hidden_size=768,       # 隐藏层大小
    mlp_dim=3072,          # MLP 层的维度
    num_layers=12,         # Transformer 层的数量
    num_heads=12,          # 注意力头的数量
    pos_embed='conv',      # 位置嵌入的类型
    classification=True,   # 是否用于分类任务
    num_classes=2          # 分类的类别数
)

# 打印模型结构以确认初始化成功
print(model)

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

#定义focalloss解决样本不平衡，效果好
# class FocalLoss(nn.Module):
#     def __init__(self, gamma=2, alpha=None, reduction='mean'):
#         super(FocalLoss, self).__init__()
#         self.gamma = gamma
#         self.alpha = alpha
#         self.reduction = reduction

#         if isinstance(alpha, (float, int)):
#             self.alpha = torch.Tensor([alpha, 1 - alpha])
#         if isinstance(alpha, list):
#             self.alpha = torch.Tensor(alpha)
    
#     def forward(self, inputs, targets):
#         # Convert inputs to probabilities
#         inputs = F.softmax(inputs, dim=1)
#         # Get the log probabilities
#         log_p = torch.log(inputs)
#         # Gather log probabilities for the correct class
#         log_p = log_p.gather(1, targets.unsqueeze(1))
#         log_p = log_p.view(-1)
        
#         # Gather probabilities for the correct class
#         p_t = inputs.gather(1, targets.unsqueeze(1))
#         p_t = p_t.view(-1)
        
#         # Apply the focal loss formula
#         loss = -1 * (1 - p_t) ** self.gamma * log_p
        
#         # Apply alpha if provided
#         if self.alpha is not None:
#             if self.alpha.device != inputs.device:
#                 self.alpha = self.alpha.to(inputs.device)
#             alpha_t = self.alpha.gather(0, targets)
#             loss = loss * alpha_t
        
#         if self.reduction == 'mean':
#             return loss.mean()
#         elif self.reduction == 'sum':
#             return loss.sum()
#         else:
#             return loss

# criterion = FocalLoss(gamma=2, alpha=[0.4, 0.6])  #设置 gamma（调节难易样本权重的参数）、alpha（平衡不同类别权重的参数）


 # Set the optimizer and scheduler
criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)


num_epochs = 10

#训练和测试  模型通用
# for epoch in range(num_epochs):
#     model.train()
#     running_loss = 0.0
#     correct = 0
#     total = 0
#     for batch_idx, batch in enumerate(train_loader):
#         images = batch['image'].to(device)  # 直接获取处理后的图像张量
#         # print(f"Input image shape: {images.shape}")
#         images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
#         labels = batch['label'].long().to(device)  # 假设标签也在batch中

#         optimizer.zero_grad()
#         y_pred = model(images)
#         loss = criterion(y_pred, labels)
#         loss.backward()
#         optimizer.step()

#         with torch.no_grad():
#             y_pred = torch.argmax(y_pred, dim=1)
#             correct += (y_pred == labels).sum().item()
#             total += labels.size(0)
#             running_loss += loss.item() * images.size(0)

#     exp_lr_scheduler.step()
#     torch.cuda.empty_cache()

#     epoch_loss = running_loss / len(train_dataset)
#     epoch_acc = 100 * correct / total
#     print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')

#     # 测试循环
#     model.eval()
#     correct = 0
#     total = 0
#     with torch.no_grad():
#         for batch in test_loader:
#             images = batch['image'].to(device)  # 直接获取处理后的图像张量
#             images = images.expand(-1, 3, -1, -1, -1)  # 扩展为3通道维度
#             labels = batch['label'].long().to(device)  # 假设标签也在batch中

#             y_pred = model(images)
#             _, predicted = torch.max(y_pred, 1)
#             total += labels.size(0)
#             correct += (predicted == labels).sum().item()
#             torch.cuda.empty_cache()

#     accuracy = 100 * correct / total
#     print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

##3d VIT模型 用这个
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)

        # 如果模型返回的是元组，提取实际的输出张量
        if isinstance(y_pred, tuple):
            y_pred = y_pred[0]

        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')

    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展为3通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)

            # 如果模型返回的是元组，提取实际的输出张量
            if isinstance(y_pred, tuple):
                y_pred = y_pred[0]

            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

#%% 3d ShuffleNet3D模型  已成功，比较慢
#多序列特征融合，3d模型不融合
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import swin3d_s
import os
from sklearn.metrics import classification_report, confusion_matrix
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

# resize成统一图像体积
resized_shape = (128, 128, 64) # r3d_18和swin3d融合用这个

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    RandRotate90d(keys=["image_ap", "image_pp", "image_hbp"], prob=0.1, spatial_axes=[0, 1]),# 适当降低旋转概率
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])

test_transforms = Compose([
    LoadImaged(keys=["image_ap", "image_pp", "image_hbp"]),
    EnsureChannelFirstd(keys=["image_ap", "image_pp", "image_hbp"]),
    Resized(keys=["image_ap", "image_pp", "image_hbp"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image_ap", "image_pp", "image_hbp"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image_ap", "image_pp", "image_hbp"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取各个子文件夹中的图像文件列表
    ap_files = [f for f in os.listdir(os.path.join(data_dir, 'ap')) if f.endswith('.nii.gz')]
    pp_files = [f for f in os.listdir(os.path.join(data_dir, 'pp')) if f.endswith('.nii.gz')]
    hbp_files = [f for f in os.listdir(os.path.join(data_dir, 'hbp')) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for ap_file in ap_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(ap_file)
        file_name = os.path.splitext(base_name)[0]
        file_names = file_name.split('-')[0]
        
        # 检查相应的 pp 和 hbp 文件是否存在
        pp_file = f"{file_names}-pp.nii.gz"
        hbp_file = f"{file_names}-hbp.nii.gz"
        
        if pp_file in pp_files and hbp_file in hbp_files:
            # 从DataFrame中查找对应的标签
            if file_names in df['name'].values:
                label = df.loc[df['name'] == file_names, label_name].values[0]
                data.append({
                    "image_ap": os.path.join(data_dir, 'ap', ap_file),
                    "image_pp": os.path.join(data_dir, 'pp', pp_file),
                    "image_hbp": os.path.join(data_dir, 'hbp', hbp_file),
                    "label": label,
                    "file_name": file_names
                })
    
    return data

# 使用示例
train_dir = '/root/autodl-tmp/HCC2/tumor/train' #train下面有ap pp hbp
test_dir = '/root/autodl-tmp/HCC2/tumor/val'    #val下面有ap pp hbp
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/val2.xlsx'

# 加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
print(train_files[:5])
print(test_files[:5])

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=2)

##ShuffleNet3D
class ShuffleUnit(nn.Module):
    def __init__(self, in_channels, out_channels, groups=3):
        super(ShuffleUnit, self).__init__()
        mid_channels = out_channels // 2
        self.groups = groups
        self.conv1 = nn.Conv3d(in_channels, mid_channels, kernel_size=1, stride=1, padding=0, bias=False)
        self.bn1 = nn.BatchNorm3d(mid_channels)
        self.conv2 = nn.Conv3d(mid_channels, mid_channels, kernel_size=3, stride=1, padding=1, groups=groups, bias=False)
        self.bn2 = nn.BatchNorm3d(mid_channels)
        self.conv3 = nn.Conv3d(mid_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=False)
        self.bn3 = nn.BatchNorm3d(out_channels)
        self.conv_res = None
        if in_channels != out_channels:
            self.conv_res = nn.Conv3d(in_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=False)
            self.bn_res = nn.BatchNorm3d(out_channels)
    def forward(self, x):
        identity = x
        if self.conv_res is not None:
            identity = self.conv_res(identity)
            identity = self.bn_res(identity)
        out = self.conv1(x)
        out = self.bn1(out)
        out = F.relu(out)
        out = self.conv2(out)
        out = self.bn2(out)
        out = F.relu(out)
        out = self.conv3(out)
        out = self.bn3(out)
        out += identity
        out = F.relu(out)
        return out

num_classes = "num_classes"

class ShuffleNet3D(nn.Module):
    def __init__(self, in_channels=3, num_classes=num_classes):
        super(ShuffleNet3D, self).__init__()
        self.num_classes = num_classes
        self.conv1 = nn.Conv3d(in_channels, 24, kernel_size=3, stride=2, padding=1, bias=False)
        self.bn1 = nn.BatchNorm3d(24)
        self.maxpool = nn.MaxPool3d(kernel_size=3, stride=2, padding=1)
        self.stage2 = self._make_stage(24, 240, 4, groups=3)
        self.stage3 = self._make_stage(240, 480, 8, groups=3)
        self.stage4 = self._make_stage(480, 960, 4, groups=3)
        self.avgpool = nn.AdaptiveAvgPool3d((1, 1, 1))
        self.fc = nn.Linear(960, num_classes)
    def _make_stage(self, in_channels, out_channels, num_blocks, groups):
        layers = [ShuffleUnit(in_channels, out_channels, groups=groups)]
        for _ in range(num_blocks - 1):
            layers.append(ShuffleUnit(out_channels, out_channels, groups=groups))
        return nn.Sequential(*layers)
    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = F.relu(x)
        x = self.maxpool(x)
        x = self.stage2(x)
        x = self.stage3(x)
        x = self.stage4(x)
        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x

 # Create the 3D ShuffleNet model
num_classes = 2
model = ShuffleNet3D(num_classes=num_classes)
 # Set the device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# 计算类别权重
labels = [item['label'] for item in train_files]
class_counts = np.bincount(labels)
class_weights = 1.0 / class_counts
class_weights = torch.tensor(class_weights, dtype=torch.float32).to(device)

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss(weight=class_weights)
optimizer = torch.optim.Adam(model.parameters(), lr=0.005)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
best_model_path = "/root/autodl-tmp/HCC/tumor/swintumor_best_model.pth"

# 训练和测试
num_epochs = 10

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images_ap = batch['image_ap'].to(device)
        images_pp = batch['image_pp'].to(device)
        images_hbp = batch['image_hbp'].to(device)
        
        images_ap = images_ap.expand(-1, 3, -1, -1, -1)
        images_pp = images_pp.expand(-1, 3, -1, -1, -1)
        images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
        
        labels = batch['label'].long().to(device)

        optimizer.zero_grad()
        y_pred = model(images_ap, images_pp, images_hbp)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images_ap.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)
            
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            
            labels = batch['label'].long().to(device)

            y_pred = model(images_ap, images_pp, images_hbp)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 保存最优模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)

#保存模型
# PATH = '/root/autodl-tmp/HCC2/tumor/swin-tumor_3p-final_model.pth'
# torch.save(model.state_dict(), PATH)

#%%3d squeezenet模型代码  已成功，比较快  Talkx gpt3.5生成
##SqueezeNet3D
class FireModule(nn.Module):
    def __init__(self, in_channels, squeeze_channels, expand1x1_channels, expand3x3_channels):
        super(FireModule, self).__init__()
        self.squeeze = nn.Conv3d(in_channels, squeeze_channels, kernel_size=1)
        self.expand1x1 = nn.Conv3d(squeeze_channels, expand1x1_channels, kernel_size=1)
        self.expand3x3 = nn.Conv3d(squeeze_channels, expand3x3_channels, kernel_size=3, padding=1)
    def forward(self, x):
        x = self.squeeze(x)
        x = nn.functional.relu(x)
        out1x1 = self.expand1x1(x)
        out3x3 = self.expand3x3(x)
        out = torch.cat([out1x1, out3x3], dim=1)
        return out
    
class SqueezeNet3D(nn.Module):
    def __init__(self, num_classes=1000):
        super(SqueezeNet3D, self).__init__()
        self.features = nn.Sequential(
            nn.Conv3d(3, 64, kernel_size=3, stride=2),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=3, stride=2, ceil_mode=True),
            FireModule(64, 16, 64, 64),
            FireModule(128, 16, 64, 64),
            nn.MaxPool3d(kernel_size=3, stride=2, ceil_mode=True),
            FireModule(128, 32, 128, 128),
            FireModule(256, 32, 128, 128),
            nn.MaxPool3d(kernel_size=3, stride=2, ceil_mode=True),
            FireModule(256, 48, 192, 192),
            FireModule(384, 48, 192, 192),
            FireModule(384, 64, 256, 256),
            FireModule(512, 64, 256, 256),
        )
        self.classifier = nn.Sequential(
            nn.Dropout(p=0.5),
            nn.Conv3d(512, num_classes, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        x = x.view(x.size(0), -1)
        return x
 # Example usage
model = SqueezeNet3D(num_classes=2)

 # Set the device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

criterion = nn.CrossEntropyLoss()

 # Set the optimizer and scheduler
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

 # Train and test the model
num_epochs = 20
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, (images, labels) in enumerate(train_loader):
        images = images.float().to(device)
        labels = labels.long().to(device)
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
        torch.cuda.empty_cache()
    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.2f}%')
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for images, labels in test_loader:
            images = images.float().to(device)
            labels = labels.long().to(device)
            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()
    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.2f}%')


#%%3d图像monai预处理和数据增强代码
# pip install monai
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

#resize成统一的体积
resized_mask = (256, 256, 80)

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_mask),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=200, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.5, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_mask),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=200, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签名
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label})    
    return data

# 使用示例
# train_dir = '/root/autodl-tmp/HCC/tumor/ap/train'
# test_dir = '/root/autodl-tmp/HCC/tumor/ap/val'
# train_file_path = '/root/autodl-tmp/HCC/data/train.xlsx'
# test_file_path = '/root/autodl-tmp/HCC/data/val.xlsx'

train_dir = '/hy-tmp/tumor/ap/train'   
test_dir = '/hy-tmp/tumor/ap/val'
train_file_path = '/hy-tmp/data/train.xlsx'  
test_file_path = '/hy-tmp/data/val.xlsx'

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=2, num_workers=2)

#%% 3D-CNN-PyTorch-master  #项目代码:https://github.com/xmuyzz/3D-CNN-PyTorch
import sys
# path = r'K:\2020-2023HCC\579hcc\模型代码总结2\Githubcode\3D-CNN-PyTorch-master'
path = '/hy-tmp/3D-CNN-PyTorch-master'
sys.path.append(path) 
sys.path

from models import utils
from models import DenseNet,EfficientNet  #把models文件夹的上一级文件夹路径加到系统变量
from models import MobileNetV2,WideResNet,ShuffleNetV2,ResNetV2

# model = ResNetV2.generate_model(
#             model_depth=50,
#             n_classes=2,
#             n_input_channels=3,
#             shortcut_type='B',
#             conv1_t_size=7,
#             conv1_t_stride=1,
#             no_max_pool=False,
#             widen_factor=1.0)

# model = EfficientNet.EfficientNet3D.from_name(
#             'efficientnet-b2', 
#             override_params={'num_classes':2}, 
#             in_channels=1)
model = MobileNetV2.get_model(
            sample_size=112,
            num_classes=2,
            in_channels=3)
# model = WideResNet.generate_model(
#             model_depth=50,
#             n_classes=2,
#             n_input_channels=3)

# model = DenseNet.generate_model(model_depth = 121,
#                                 n_input_channels=1,
#                                 # num_init_features=64,
#                                 # growth_rate=32,
#                                 # block_config=(6, 12, 24, 16),
#                                 num_classes=2)

# model = ShuffleNetV2.get_model(num_classes=2,sample_size=112,width_mult=1, in_channels=3)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = model.to(device)

## 使用较小的学习率进行微调
from torch.optim import lr_scheduler
optimizer  = torch.optim.Adam(model.parameters(), lr=0.0001)

# 定义L2正则化项的权重；定义优化器，并添加L2正则化
# l2_lambda = 0.01
# optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=l2_lambda)

criterion = nn.CrossEntropyLoss()
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

 # Train and test the model
num_epochs = 30

# 训练循环
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展为3通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

# PATH = '/hy-tmp/ap_swin3d.pth'
# # PATH = '/root/autodl-tmp/appphbp_swin_model.pth'   
   
# torch.save(model.state_dict(), PATH)

#%% Efficient_3DCNNs_master
#项目代码:https://github.com/okankop/Efficient-3DCNNs/
import torch
from torch import nn
import sys
# path = r"K:\2020-2023HCC\579hcc\模型代码总结2\Githubcode\Efficient_3DCNNs_master"
path = '/root/autodl-tmp'
sys.path.append(path) 
sys.path

from models import c3d,squeezenet, mobilenet, shufflenet, mobilenetv2, shufflenetv2, resnext, resnet

# from models.shufflenetv2 import get_fine_tuning_parameters
# model = shufflenetv2.get_model(num_classes=2)

# from models.squeezenet import get_fine_tuning_parameters
# model = squeezenet.get_model(num_classes=2,sample_size = max_size, 
#                              sample_duration = max_num) #sample_size =112 224 

# from models.mobilenetv2 import get_fine_tuning_parameters
# model = mobilenetv2.get_model(num_classes=2, sample_size=224, width_mult=1.)

# from models.resnext import get_fine_tuning_parameters
# model = resnext.resnext50(num_classes=2,shortcut_type='B', cardinality=32,
#                           sample_size = max_size, sample_duration = max_num)

from models.resnet import get_fine_tuning_parameters
model = resnet.resnet50(num_classes=2,shortcut_type='B', 
                        sample_size = max_size, #sample_size表示reszie后的图像大小
                        sample_duration = max_num) #sample_duration表示pad后的图像数

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = model.to(device)
print(model)

## 使用较小的学习率进行微调
from torch.optim import lr_scheduler
optimizer  = torch.optim.Adam(model.parameters(), lr=0.0001)

# 定义L2正则化项的权重；定义优化器，并添加L2正则化
# l2_lambda = 0.01
# optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=l2_lambda)

criterion = nn.CrossEntropyLoss()
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

 # Train and test the model
num_epochs = 3
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, (images, labels) in enumerate(train_loader):
        images = images.float().to(device)
        labels = labels.long().to(device)
        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)
        torch.cuda.empty_cache()
    train_loss = running_loss / len(train_dataset)
    train_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Training Accuracy: {train_acc:.2f}%')
    model.eval()
    correct = 0
    total = 0
    best_acc = 0.0
    with torch.no_grad():
        for images, labels in test_loader:
            images = images.float().to(device)
            labels = labels.long().to(device)
            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            running_loss += loss.item() * images.size(0)
            torch.cuda.empty_cache()
    test_loss = running_loss / len(test_dataset)
    test_acc = 100 * correct / total

    if test_acc > best_acc:
            best_acc = test_acc
            # torch.save(model.state_dict(), '/root/autodl-tmp/best_model.pt')

    print(f'Testing Epoch [{epoch+1}/{num_epochs}],Test Loss: {test_loss:.4f}, Testing Accuracy: {test_acc:.2f}%')

#%%vit3d_pytorch 3D Vision Transformer已跑通
#代码地址https://github.com/lucidrains/vit-pytorch
##地址 https://github.com/JamesQFreeman/vit3d-pytorch
#%3d图像monai预处理和数据增强代码
# pip install monai
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

resized_mask = (256, 256, 80)

# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_mask),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=200, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.5, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_mask),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=200, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label})    
    return data


# 使用示例
train_dir = '/root/autodl-tmp/HCC2/tumor/train/ap'
test_dir = '/root/autodl-tmp/HCC2/tumor/val/ap'
train_file_path = '/root/autodl-tmp/HCC2/train2.xlsx'
test_file_path = '/root/autodl-tmp/HCC2/val2.xlsx'

# train_dir = '/hy-tmp/tumor/ap/train'   
# test_dir = '/hy-tmp/tumor/ap/val'
# train_file_path = '/hy-tmp/data/train.xlsx'  
# test_file_path = '/hy-tmp/data/val.xlsx'

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
train_files[:5]
test_files[:5]

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=2, num_workers=2)


##地址 https://github.com/JamesQFreeman/vit3d-pytorch
import sys
# path = r"K:\2020-2023HCC\579hcc\模型代码总结2\Githubcode\vit3d-pytorch-main"
path = '/root/autodl-tmp/HCC2/tumor/vit3d-pytorch-main'
sys.path.append(path)  #把models文件夹的上一级文件夹路径加到系统变量
sys.path

import torch
from vit3d_pytorch import ViT3D 

model = ViT3D(
            image_size=(256, 256, 80),#注意image_size(256, 256, 80)中的每个数都要能被patch_size整除
            patch_size=16,
            num_classes=2,
            dim=1024,
            depth=6,
            heads=16,
            mlp_dim=2048,
            dropout=0.1,
            emb_dropout=0.1
    )

device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print('device =', device)
model.to(device)

# 使用较小的学习率进行微调
from torch.optim import lr_scheduler

optimizer = torch.optim.Adam(model.parameters(), lr=0.00001)
criterion = nn.CrossEntropyLoss()
exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)


num_epochs = 30

#训练和测试
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量
        # print(f"Input image shape: {images.shape}")
        # images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')

    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量
            # images = images.expand(-1, 3, -1, -1, -1)  # 扩展为3通道维度
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

#%%Hyperopt深度学习超参数搜索迭代
import hyperopt
from hyperopt import fmin, tpe, hp, Trials
import numpy as np

# def objective(params):
#     lr = params['lr']
#     batch_size = params['batch_size']
    
#     model.train()
#     running_loss = 0.0
#     correct = 0
#     total = 0
#     for batch_idx, batch in enumerate(train_loader):
#         images = batch['image'].to(device)  # 直接获取处理后的图像张量
#         images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
#         labels = batch['label'].long().to(device)  # 假设标签也在batch中

#         optimizer.zero_grad()
#         y_pred = model(images)
#         loss = criterion(y_pred, labels)
#         loss.backward()
#         optimizer.step()

#         with torch.no_grad():
#             y_pred = torch.argmax(y_pred, dim=1)
#             correct += (y_pred == labels).sum().item()
#             total += labels.size(0)
#             running_loss += loss.item() * images.size(0)

#     exp_lr_scheduler.step()
#     torch.cuda.empty_cache()

#     epoch_loss = running_loss / len(train_dataset)
#     epoch_acc = 100 * correct / total
#     print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
#     # 测试循环
#     model.eval()
#     correct = 0
#     total = 0
#     with torch.no_grad():
#         for batch in test_loader:
#             images = batch['image'].to(device)  # 直接获取处理后的图像张量
#             images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
#             labels = batch['label'].long().to(device)  # 假设标签也在batch中

#             y_pred = model(images)
#             _, predicted = torch.max(y_pred, 1)
#             total += labels.size(0)
#             correct += (predicted == labels).sum().item()
#             torch.cuda.empty_cache()

#     accuracy = 100 * correct / total
#     print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

#     # 计算损失值
#     loss = epoch_loss

#     return {'loss': loss, 'status': hyperopt.STATUS_OK}

def objective(params):
    lr = params['lr']
    batch_size = params['batch_size']
    
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images_ap = batch['image_ap'].to(device)
        images_pp = batch['image_pp'].to(device)
        images_hbp = batch['image_hbp'].to(device)        
        images_ap = images_ap.expand(-1, 3, -1, -1, -1)
        images_pp = images_pp.expand(-1, 3, -1, -1, -1)
        images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images_ap, images_pp, images_hbp)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)            
            running_loss += loss.item() * images_ap.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)        
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images_ap, images_pp, images_hbp)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # 计算损失值
    loss = epoch_loss

    return {'loss': loss, 'status': hyperopt.STATUS_OK}

# 定义超参数搜索空间
space = {
    'lr': hp.loguniform('lr', np.log(0.001), np.log(0.01)),
    'batch_size': hp.choice('batch_size', [4, 8, 16,32])
}

# 创建Trials对象用于记录每次试验的结果
trials = Trials()

# 运行超参数搜索
best = fmin(fn=objective, space=space, algo=tpe.suggest, max_evals=5, trials=trials)

# 输出最佳超参数组合和对应的性能
best_params = hyperopt.space_eval(space, best)
best_accuracy = -trials.best_trial['result']['loss']
print("最佳超参数组合：", best_params)
print("最佳性能：", best_accuracy)

#%%
import torch
from torch import nn, optim
import hyperopt
from hyperopt import fmin, tpe, hp, Trials
import numpy as np

# 定义目标函数
def objective(params):
    lr = params['lr']
    batch_size = params['batch_size']
    optimizer_type = params['optimizer']
    weight_decay = params['weight_decay']
    momentum = params['momentum']
    step_size = params['step_size']
    gamma = params['gamma']
    num_layers = params['num_layers']
    hidden_size = params['hidden_size']
    dropout = params['dropout']
    
    # 根据超参数初始化模型、优化器、调度器等
    model = model(num_layers=num_layers, hidden_size=hidden_size, dropout=dropout).to(device)
    if optimizer_type == 'Adam':
        optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    elif optimizer_type == 'SGD':
        optimizer = optim.SGD(model.parameters(), lr=lr, weight_decay=weight_decay, momentum=momentum)
    
    criterion = nn.CrossEntropyLoss()
    exp_lr_scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=step_size, gamma=gamma)

    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images_ap = batch['image_ap'].to(device)
        images_pp = batch['image_pp'].to(device)
        images_hbp = batch['image_hbp'].to(device)        
        images_ap = images_ap.expand(-1, 3, -1, -1, -1)
        images_pp = images_pp.expand(-1, 3, -1, -1, -1)
        images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
        labels = batch['label'].long().to(device)

        optimizer.zero_grad()
        y_pred = model(images_ap, images_pp, images_hbp)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images_ap.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)        
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(device)

            y_pred = model(images_ap, images_pp, images_hbp)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # 计算损失值
    loss = epoch_loss

    return {'loss': loss, 'status': hyperopt.STATUS_OK}

# 定义超参数搜索空间
space = {
    'lr': hp.loguniform('lr', np.log(0.001), np.log(0.01)),
    'batch_size': hp.choice('batch_size', [4, 8, 16, 32]),
    'optimizer': hp.choice('optimizer', ['Adam', 'SGD']),
    'weight_decay': hp.loguniform('weight_decay', np.log(1e-5), np.log(1e-3)),
    # 'momentum': hp.uniform('momentum', 0.8, 0.99),
    'step_size': hp.choice('step_size', [7, 10, 14]),
    'gamma': hp.uniform('gamma', 0.1, 0.9),
    # 'num_layers': hp.choice('num_layers', [1, 2, 3, 4]),
    # 'hidden_size': hp.choice('hidden_size', [64, 128, 256, 512]),
    # 'dropout': hp.uniform('dropout', 0.1, 0.5)
}

# 创建Trials对象用于记录每次试验的结果
trials = Trials()

# 运行超参数搜索
best = fmin(fn=objective, space=space, algo=tpe.suggest, max_evals=50, trials=trials)

# 输出最佳超参数组合和对应的性能
best_params = hyperopt.space_eval(space, best)
best_accuracy = -trials.best_trial['result']['loss']
print("最佳超参数组合：", best_params)
print("最佳性能：", best_accuracy)

#%% pip install ray[tune] 自动调参
import torch
from torch import nn, optim
from ray import tune
from ray.tune import Trainable
from ray.tune.schedulers import ASHAScheduler
import numpy as np

class TrainableModel(Trainable):
    def setup(self, config):
        self.lr = config["lr"]
        self.batch_size = config["batch_size"]
        
        # Initialize your model, optimizer, criterion, scheduler, etc. here
        self.model = model().to(device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr)
        self.criterion = nn.CrossEntropyLoss()
        self.exp_lr_scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=7, gamma=0.1)
        
        # Assuming train_loader and test_loader are defined globally
        self.train_loader = get_train_loader(self.batch_size)
        self.test_loader = get_test_loader(self.batch_size)
        self.train_dataset = get_train_dataset()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
    
    def step(self):
        self.model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        for batch in self.train_loader:
            images_ap = batch['image_ap'].to(self.device)
            images_pp = batch['image_pp'].to(self.device)
            images_hbp = batch['image_hbp'].to(self.device)        
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
            labels = batch['label'].long().to(self.device)

            self.optimizer.zero_grad()
            y_pred = self.model(images_ap, images_pp, images_hbp)
            loss = self.criterion(y_pred, labels)
            loss.backward()
            self.optimizer.step()

            with torch.no_grad():
                y_pred = torch.argmax(y_pred, dim=1)
                correct += (y_pred == labels).sum().item()
                total += labels.size(0)
                running_loss += loss.item() * images_ap.size(0)

        self.exp_lr_scheduler.step()
        torch.cuda.empty_cache()

        epoch_loss = running_loss / len(self.train_dataset)
        epoch_acc = 100 * correct / total

        # Validation step
        self.model.eval()
        correct = 0
        total = 0
        with torch.no_grad():
            for batch in self.test_loader:
                images_ap = batch['image_ap'].to(self.device)
                images_pp = batch['image_pp'].to(self.device)
                images_hbp = batch['image_hbp'].to(self.device)
                images_ap = images_ap.expand(-1, 3, -1, -1, -1)
                images_pp = images_pp.expand(-1, 3, -1, -1, -1)
                images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)
                labels = batch['label'].long().to(self.device)

                y_pred = self.model(images_ap, images_pp, images_hbp)
                _, predicted = torch.max(y_pred, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
                torch.cuda.empty_cache()

        accuracy = 100 * correct / total

        # Report results to Ray Tune
        tune.report(loss=epoch_loss, accuracy=accuracy)

    def save_checkpoint(self, checkpoint_dir):
        checkpoint_path = os.path.join(checkpoint_dir, "checkpoint")
        torch.save(self.model.state_dict(), checkpoint_path)
        return checkpoint_path

    def load_checkpoint(self, checkpoint_path):
        self.model.load_state_dict(torch.load(checkpoint_path))

def get_train_loader(batch_size):
    # Your code to create and return train_loader
    pass

def get_test_loader(batch_size):
    # Your code to create and return test_loader
    pass

def get_train_dataset():
    # Your code to create and return train_dataset
    pass

# Define the search space

search_space = {
    "lr": tune.loguniform(0.001, 0.01),
    "batch_size": tune.choice([4, 8, 16, 32]),
    "step_size": tune.choice([7, 10, 14]),
    "gamma": tune.uniform(0.1, 0.9),
    # "num_layers": tune.choice([1, 2, 3, 4]),
    # "hidden_size": tune.choice([64, 128, 256, 512]),
    "dropout": tune.uniform(0.1, 0.5),
    # "weight_decay": tune.loguniform(1e-5, 1e-3)
}


# Configure the scheduler
scheduler = ASHAScheduler(
    metric="accuracy",
    mode="max",
    max_t=100,
    grace_period=10,
    reduction_factor=2
)

# Run the hyperparameter optimization
analysis = tune.run(
    TrainableModel,
    resources_per_trial={"cpu": 1, "gpu": 1},  # Adjust according to your resources
    config=search_space,
    num_samples=10,
    scheduler=scheduler
)

print("Best config: ", analysis.best_config)

#%% 保存模型权重并预测新的数据
import pandas as pd
import torch
import torchvision.models as models
import copy
# 计算混淆矩阵和评价指标
from sklearn.metrics import classification_report, confusion_matrix

# state_dict就是一个简单的Python字典，它将模型中的可训练参数（比如weights和biases，batchnorm的running_mean、
# torch.optim参数等）通过将模型每层与层的参数张量之间一一映射，实现保存、更新、变化和再存储。
# PATH = '/root/autodl-tmp/my_resnet50_model.pth'
PATH = '/root/autodl-tmp/HCC2/resnetswin_radiomics_model.pth'
# PATH = '/root/autodl-tmp/appphbp_swin_model.pth'   
   
# torch.save(model.state_dict(), PATH)

# 方法1 创建一个与训练模型结构相同的新模型
# new_model = models.resnet50(num_classes=2)
# new_model = models.vit_b_32(num_classes=2)
# new_model = models.swin_v2_b(num_classes=2)
# new_model = models.densenet121(num_classes=2)
# new_model = models.vgg19(num_classes=2)
# new_model = models.googlenet(num_classes=2)
# new_model

# 方法2 加载非预训练的ResNet-50模型
# pretrained_model = models.googlenet(pretrained=False)
# # 创建一个新的模型
# new_model = copy.deepcopy(pretrained_model)
# new_model.fc = torch.nn.Linear(pretrained_model.fc.in_features, 2)
# new_model

# 加载新模型的权重
num_classes = 2
new_model= FusionModel(num_classes=num_classes)
new_model
new_model.load_state_dict(torch.load(PATH))


# 将新模型移动到GPU上（如果可用）
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

if torch.cuda.is_available():
    new_model = new_model.to(device)

# test_data =  '/hy-tmp/train'  # r'D:\HCC\ap\test'    #'/root/autodl-tmp/ap/validation'

# test_dataset =  torchvision.datasets.ImageFolder(test_data,transform=test_transform)
# test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=32)

# train_loader = torch.utils.data.DataLoader(train_ds,batch_size=BATCH_SIZE)

# 可将新训练好的模型应用于测试集，单个序列预测
# def get_predictions(model, dataloader):
#     model.eval()
#     predict_label = []
#     probability = []
#     true_labels = []
#     file_paths = []
    
#     with torch.no_grad():
#         for batch in dataloader:
#             images = batch['image'].to(device)  # 获取处理后的图像张量
#             images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
#             labels = batch['label'].long().to(device)  # 获取标签
#             batch_file_paths = batch['file_name']  # 获取文件名          

#             y_pred = model(images)
#             y_pred_prob = torch.softmax(y_pred, dim=1)
#             max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)

#             predict_label.extend(y_pred_class.tolist())
#             probability.extend(y_pred_prob.tolist())
#             true_labels.extend(labels.tolist())
#             file_paths.extend(batch_file_paths)  # 将文件名添加到 file_paths 列表中           
#             print(f"Processed batch file names: {batch_file_paths}") # 打印当前批次的文件名

    # return true_labels, predict_label, probability, file_paths

#多序列预测函数
def get_predictions(model, dataloader):
    model.eval()
    predict_label = []
    probability = []
    true_labels = []
    file_paths = []
    
    with torch.no_grad():
        for batch in dataloader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)
        
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)

            labels = batch['label'].long().to(device)  # 获取标签
            batch_file_paths = batch['file_name']  # 获取文件名
            # clinical_data = torch.tensor(batch['clinical_data'], dtype=torch.float32).to(device)
            # y_pred = model(images_ap, images_pp, images_hbp, clinical_data)
            y_pred = model(images_ap, images_pp, images_hbp)
            y_pred_prob = torch.softmax(y_pred, dim=1)
            max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)

            predict_label.extend(y_pred_class.tolist())
            probability.extend(y_pred_prob.tolist())
            true_labels.extend(labels.tolist())
            file_paths.extend(batch_file_paths)  # 将文件名添加到 file_paths 列表中           
            print(f"Processed batch file names: {batch_file_paths}") # 打印当前批次的文件名

    return true_labels, predict_label, probability, file_paths


# 获取训练集和测试集的预测结果、概率和最大概率
test_true_label,test_predict_label, test_probability,test_paths = get_predictions(new_model, test_loader)


#测试集混淆矩阵
cm=confusion_matrix(test_true_label,test_predict_label)
print(classification_report(test_true_label,test_predict_label,digits=3))

# 创建字典来保存预测结果、概率和最大概率
test_data =  {'Image Path': test_paths,'True Labels': test_true_label,
               'Predict_label': test_predict_label,'Probability': test_probability}       

# 创建DataFrame保存数据
test_results = pd.DataFrame(test_data)

# 将结果保存到Excel文件
test_results.to_excel('/root/autodl-tmp/HCC/test_r3dswin3d.xlsx', index=False)

# skplt.metrics.plot_roc(labels,probabilities, #注意要用多类别预测概率列，而不是最大预测概率
#                        title='ROC Plot for swin', #Densenet121
#                        # classes_to_plot=['class1', 'class2','class3'],
#                        cmap='Set1') # cmap 设置调色板
# # 调整图例的位置和大小
# plt.legend(loc='lower right', fontsize='xx-small', markerscale=0.1)
# plt.show()

#%% 保存图片路径、真实label、预测概率、预测label
# train_dl不能做shuffle，否则路径对不上
import torch
import pandas as pd
# 计算混淆矩阵和评价指标
from sklearn.metrics import classification_report, confusion_matrix

new_train_dl = DataLoader(train_dataset, batch_size=8, shuffle=False, num_workers=2)#不做shuffle
test_loader = DataLoader(test_dataset, batch_size=8, num_workers=2)

# PATH = '/root/autodl-tmp/HCC2/resnetswin_radiomics_model.pth'
# model.load_state_dict(torch.load(PATH))
# model

# def get_predictions(model, dataloader): #单个序列
#     model.eval()
#     predict_label = []
#     probability = []
#     true_labels = []
#     file_paths = []
    
#     with torch.no_grad():
#         for batch in dataloader:
#             images = batch['image'].to(device)  # 获取处理后的图像张量
#             images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
#             labels = batch['label'].long().to(device)  # 获取标签
#             batch_file_paths = batch['file_name']  # 获取文件名          

#             y_pred = model(images)
#             y_pred_prob = torch.softmax(y_pred, dim=1)
#             max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)

#             predict_label.extend(y_pred_class.tolist())
#             probability.extend(y_pred_prob.tolist())
#             true_labels.extend(labels.tolist())
#             file_paths.extend(batch_file_paths)  # 将文件名添加到 file_paths 列表中           
#             print(f"Processed batch file names: {batch_file_paths}") # 打印当前批次的文件名

#     return true_labels, predict_label, probability, file_paths

def get_predictions(model, dataloader):#多序列
    model.eval()
    predict_label = []
    probability = []
    true_label = []
    file_paths = []
    
    with torch.no_grad():
        for batch in dataloader:
            images_ap = batch['image_ap'].to(device)
            images_pp = batch['image_pp'].to(device)
            images_hbp = batch['image_hbp'].to(device)
        
            images_ap = images_ap.expand(-1, 3, -1, -1, -1)
            images_pp = images_pp.expand(-1, 3, -1, -1, -1)
            images_hbp = images_hbp.expand(-1, 3, -1, -1, -1)

            labels = batch['label'].long().to(device)  # 获取标签
            batch_file_paths = batch['file_name']  # 获取文件名          
            clinical_data = torch.tensor(batch['clinical_data'], dtype=torch.float32).to(device)
            y_pred = model(images_ap, images_pp, images_hbp, clinical_data)#3期和临床融合
            # y_pred = model(images_ap, images_pp, images_hbp) #3期融合
            y_pred_prob = torch.softmax(y_pred, dim=1)
            max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)

            predict_label.extend(y_pred_class.tolist())
            probability.extend(y_pred_prob.tolist())
            true_label.extend(labels.tolist())
            file_paths.extend(batch_file_paths)  # 将文件名添加到 file_paths 列表中           
            print(f"Processed batch file names: {batch_file_paths}") # 打印当前批次的文件名

    return true_label, predict_label, probability, file_paths


##保存训练集的预测概率和预测标签
train_true_label,train_predict_label, train_probability,train_paths = get_predictions(model, new_train_dl)
train_data = {'Image Path':train_paths,'True_label': train_true_label,
               'Predict_label': train_predict_label,'Probability': train_probability}
train_results = pd.DataFrame(train_data)  # 创建DataFrame保存数据          
# ## 将结果保存到Excel文件
# train_results.to_excel('/root/autodl-tmp/HCC2/train-resnetswin3期组学融合.xlsx', index=False)

# 训练集混淆矩阵
cm1=confusion_matrix(train_true_label,train_predict_label)
print(classification_report(train_true_label,train_predict_label,digits=3))


#保存测试集的预测概率和预测标签
test_true_label,test_predict_label, test_probability,test_paths = get_predictions(model, test_loader)
test_data =  {'Image Path': test_paths,'True_label': test_true_label,
               'Predict_label': test_predict_label,'Probability': test_probability}      
test_results = pd.DataFrame(test_data)
test_results.to_excel('/root/autodl-tmp/HCC2/test_resnetswin3期组学融合.xlsx', index=False)

#测试集混淆矩阵
cm=confusion_matrix(test_true_label,test_predict_label)
print(classification_report(test_true_label,test_predict_label,digits=3))

#%%提取自己训练的模型的深度学习特征  方法1
# # PATH = '/root/autodl-tmp/my_resnet50_model.pth'
# PATH =  '/hy-tmp/ap_resnet50_model.pth'
# # 方法1 创建一个与训练模型结构相同的新模型
# model = torchvision.models.resnet50(num_classes=2)
# # model = torchvision.models.vgg19(num_classes=2)
# # model = models.vit_b_32(num_classes=2)
# # model = models.densenet121(num_classes=2)
# model.load_state_dict(torch.load(PATH))
# model

# 提取基于预训练权重的特征
# model = torchvision.models.resnet50(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
# model = torchvision.models.vgg19(pretrained=True)
# model = model.cuda()

#删除掉模型最后一层或者全连接层的最后一层：
model
# model = torch.nn.Sequential(*(list(model.children())[:-1])) #resnet
# model.classifier=torch.nn.Sequential(*list(model.classifier.children())[:-1]) #densenet,vgg19
model.mlp_head=torch.nn.Sequential(*list(model.mlp_head.children())[:-1]) #vit3D
# model.head=torch.nn.Sequential(*list(model.head.children())[:-1]) #swin
new_model=model;new_model = new_model.cuda()
print(new_model)

BATCH_SIZE = 4

train_data =  '/root/autodl-tmp/3dnii/apnii/train'  
train_dataset = TumorDataset(train_image_list, train_label_list, transform=None)
train_loader2 = DataLoader(train_dataset, batch_size=4, shuffle=False)

test_data =  '/root/autodl-tmp/3dnii/apnii/validation'  
test_dataset = TumorDataset(test_image_list, test_label_list, transform=None)
test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False)

#GPU版本提取训练集特征
train_labels = []
train_features = []

for image,label in train_loader2:
    print(label)
    o = new_model(image.cuda()) # 放到cuda中
    o = o.view(o.size(0),-1)
    train_labels.extend(label)
    train_features.extend(o.cpu().data) # 将数据转移到CPU上
     
#GPU版本提取测试集特征
test_labels = []
test_features = []

for image, label in test_loader:
    o = new_model(image.cuda())
    # o = my_resnet18(im)
    o = o.view(o.size(0), -1)
    test_labels.extend(label)
    test_features.extend(o.cpu().data)
    
num_columns = len(test_features[0])# 获取列表的列数
num_rows = len(test_features)# 获取列表的行数
print("列数：", num_columns);print("行数：", num_rows)

# # 将Tensor对象转换为数据框格式
# train_features2= torch.tensor([item.cpu().detach().numpy() for item in train_features]) #多维度tensor转numpy
# train_features2= train_features2.tolist() #numpy格式转list
# train_features2 = pd.DataFrame(train_features2)  #数据框格式
# train_features2

#%%导出上面提取的深度学习特征  也成功
import numpy as np
import pandas as pd

def save_deep_features(deep_features,labels):  
    deep_features=torch.tensor([item.cpu().detach().numpy() for item in deep_features])       
    deep_features= deep_features.tolist()
    deep_features = pd.DataFrame(deep_features)  # 转换为数据框格式    
    labels = np.array(labels)#转换为numpy数组
    labels = labels.tolist()  # 转换为列表格式
    labels = pd.DataFrame(labels)  # 转换为数据框格式    
    return deep_features,labels

train_features,train_labels = save_deep_features(train_features,train_labels)
test_features,test_labels = save_deep_features(test_features,test_labels)

train_image_list = pd.DataFrame(train_image_list) 
test_image_list = pd.DataFrame(test_image_list) 

# 合并数据为一个数据框
train_data = pd.concat([train_image_list,train_labels,train_features], axis=1)
test_data = pd.concat([test_image_list,test_labels,test_features], axis=1)

# 保存数据到Excel文件
# train_data.to_excel('/root/autodl-tmp/3dnii/apnii/trainfeature3dvit2.xlsx', index=False)
# test_features.to_excel('/root/autodl-tmp/3dnii/apnii/testfeature3dvit2.xlsx', index=False)
train_data.to_csv('/root/autodl-tmp/3dnii/apnii/trainfeature3dvit2.csv', index=False) #/hy-tmp/
test_data.to_csv('/root/autodl-tmp/3dnii/apnii/testfeature3dvit2.csv', index=False)

#%%混淆矩阵代码
import itertools

classes=2  #定义分类类别

def plot_confusion_matrix(cm, classes, normalize= False, title='Confusion matrix', cmap=plt.cm.Blues):
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        print("Normalized confusion matrix")
    else:
        print('Confusion matrix, without normalization')
    print(cm)
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    # plt.colorbar(shrink=0.75)  # 调整进度条长度为矩形框的0.75倍
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45,fontsize=14)# 调整"VETC-"和"VETC+"标签的字体大小
    plt.yticks(tick_marks, classes,rotation=90,fontsize=14)
    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() * 0.6  # 将阈值调整为比例的0.5倍
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        plt.text(j, i, format(cm[i, j], fmt), horizontalalignment="center", color="white" if cm[i, j] > thresh else "black")
        # plt.text(j, i, format(cm[i, j], fmt), horizontalalignment="center", color="black" if cm[i, j] > thresh else "black")
    plt.tight_layout()
    plt.ylabel('True label', labelpad=8,fontsize=14)  # 调整标签与轴之间的间距
    plt.xlabel('Predicted label',labelpad=2,fontsize=14)
    
#直接画出矩阵表和矩阵图
names = ('ischemia','necrosis',
         # 'VETC-','label0','label1','label2','label3',
    )
plt.figure(figsize=(6.5,6.5))
plt.subplots_adjust(left=0.2,right=0.8,bottom=0.2,top=0.8)
plt.rcParams['font.size'] = 16
plot_confusion_matrix(cm, names,normalize= True) #True显示的是比例，False显示的是样本数
plt.show()
# plt.savefig("/root/autodl-tmp/cm.tiff")

#%%混淆矩阵和acc,loss曲线
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix
import seaborn as sns

# 初始化混淆矩阵
num_classes = 2  # 根据您的数据集确定类别数量
confusion_mat = np.zeros((num_classes, num_classes))

# 在测试集上进行预测，同时记录真实标签和预测标签
true_labels = []
pred_labels = []

model.eval()
with torch.no_grad():
    for images, labels in test_loader:
        images = images.float().to(device)
        labels = labels.long().to(device)
        y_pred = model(images)
        _, predicted = torch.max(y_pred, 1)
        true_labels.extend(labels.cpu().numpy())
        pred_labels.extend(predicted.cpu().numpy())
        torch.cuda.empty_cache()

# 计算混淆矩阵
confusion_mat = confusion_matrix(true_labels, pred_labels)

# 打印混淆矩阵
print("Confusion Matrix:")
print(confusion_mat)

# 绘制混淆矩阵图
plt.figure(figsize=(8, 6))
sns.heatmap(confusion_mat, annot=True, fmt="d", cmap='Blues')
plt.xlabel('Predicted Labels')
plt.ylabel('True Labels')
plt.title('Confusion Matrix')
plt.xticks(np.arange(num_classes), class_names, rotation=45)
plt.yticks(np.arange(num_classes), class_names)
plt.tight_layout()
plt.show()

#%%ROC、PR曲线直接绘图
##ROC、PR曲线直接绘图
import scikitplot as skplt
import matplotlib.pyplot as plt

# 创建一个大一些的图像
plt.figure(figsize=(10, 8))

# 绘制ROC曲线代码
skplt.metrics.plot_roc(test_labels,test_probability_value,
                       title='ROC Plot for Resnet50',
                       #Densenet121
                       # classes_to_plot=['class1', 'class2','class3'],
                       cmap='Set1') # cmap 设置调色板

# 在绘制ROC曲线之前添加以下代码
# plt.rcParams["legend.fontsize"] = 3  # 调整图例的字体大小

# 调整图例的位置和大小
plt.legend(loc='lower right', fontsize='xx-small', markerscale=0.1)
plt.show()

# 绘制PR曲线代码
skplt.metrics.plot_precision_recall(test_labels,test_probability_value,
                                    #,ResNet50 Inception_v3,SqueezeNet_v1.0
                                    # classes_to_plot = ([0,'VETC++'],[1,'VETC+'],[2,'VETC-']),
                                    # classes_to_plot= ['VETC++','VETC+','VETC-'],
                                   cmap='Set1')
# 设置图表标题
plt.legend(loc='lower right', fontsize='x-small', markerscale=0.1)
plt.title('PR Curve')
plt.show()

# 绘制校准曲线代码
probas_list = [test_probability_value]
clf_names = ['resnet50']

skplt.metrics.plot_calibration_curve(test_labels,
                                     probas_list,
                                     clf_names)
plt.show()

#%% 只能单个模型roc曲线
## ROC曲线1
#pip install scikit-plot
import pandas as pd;import numpy as np
import scikitplot as skplt
import matplotlib.pyplot as plt
import ast;import re
from sklearn.metrics import roc_auc_score,roc_curve, auc

df = pd.read_excel('/root/autodl-tmp/HCC/test_r3dswin3d.xlsx')

# 获取标签列和预测概率列
true_label = df['True_label'].values.tolist()
true_label

#注意要用多类别预测概率列，而不是最大预测概率；
# 当表格中Probability列的格式为[0.8306153 0.1693847]或[0.8306153,0.1693847]时用
predicted_probability = df['Probability'].apply(lambda x: re.sub(r'\s*,\s*', ',', x))
predicted_probability = predicted_probability.str.replace(r'\s+', ',', regex=True)
predicted_probability = predicted_probability.tolist()
predicted_probability = [ast.literal_eval(x) for x in predicted_probability]
predicted_probability

# Calculate ROC curve，用分组为1的概率和真实标签计算auc
fpr, tpr, thresholds = roc_curve(true_label, np.array(predicted_probability)[:, 1])
roc_auc = auc(fpr, tpr)

# Calculate 95% CI
ci_lower = roc_auc - 1.96 * np.sqrt((roc_auc * (1 - roc_auc)) / len(true_label))
ci_upper = roc_auc + 1.96 * np.sqrt((roc_auc * (1 - roc_auc)) / len(true_label))
ci_lower_upper = "{:.3f}-{:.3f}".format(ci_lower, ci_upper)
print(f"{round(roc_auc, 3)} ({ci_lower_upper})")

# 创建一个大一些的图像
plt.figure(figsize=(10, 8))

# 绘制ROC曲线代码,scikitplot库默认将AUC值截断为两位小数
skplt.metrics.plot_roc(true_label,predicted_probability,
                       title='ROC for Vision Transformer',
                       #Densenet121  Vision Transformer Swin Transformer                          
                       cmap='Set1') # cmap 设置调色板
# 调整图例的位置和大小
plt.legend(loc='lower right', fontsize='x-small', markerscale=0.1)
#fontsize可等于'xx-small','x-small','small','medium','large','x-large'
# plt.savefig(r'K:\2020-2023HCC\579hcc\578hcc\data\hbp\train_vit_hbp.jpg',dpi=300)
plt.show()

#%%ROC曲线2  单个模型ROC
import numpy as np
import pandas as pd
from sklearn.metrics import roc_curve, auc
import matplotlib.pyplot as plt
import ast;import re

df = pd.read_excel('/root/autodl-tmp/HCC/test_r3dswin3d.xlsx')

# 获取标签列和预测概率列
true_label = df['True_label'].values.tolist()

# 注意要用多类别预测概率列，而不是最大预测概率；
# 当表格中Probability列的格式为[0.8306153 0.1693847]或[0.8306153,0.1693847]时用
predicted_probability = df['Probability'].apply(lambda x: re.sub(r'\s*,\s*', ',', x))
predicted_probability = predicted_probability.str.replace(r'\s+', ',', regex=True)
predicted_probability = predicted_probability.tolist()
predicted_probability = [ast.literal_eval(x) for x in predicted_probability]
np.array(predicted_probability)[:, 1]

#用分组为1的概率和真实标签计算auc
fpr, tpr, thresholds = roc_curve(true_label, np.array(predicted_probability)[:, 1])
roc_auc = auc(fpr, tpr)
ci_lower = roc_auc - 1.96 * np.sqrt((roc_auc * (1 - roc_auc)) / len(true_label))
ci_upper = roc_auc + 1.96 * np.sqrt((roc_auc * (1 - roc_auc)) / len(true_label))
ci_lower_upper = "{:.3f}-{:.3f}".format(ci_lower, ci_upper)
print(round(roc_auc, 3));print(ci_lower_upper)

#设置图形样式
plt.style.use('seaborn-darkgrid')

#绘制ROC curve
plt.figure(figsize=(10, 8))
plt.plot(fpr, tpr, color='darkorange', lw=4, label=f'VIT (AUC = {roc_auc:.3f}, 95%CI:{ci_lower:.3f}-{ci_upper:.3f})')
plt.plot([0, 1], [0, 1], color='navy', lw=4, linestyle='--')
#设置图例和标题
plt.xlabel('False Positive Rate', fontsize=20)
plt.ylabel('True Positive Rate', fontsize=20)
plt.title('ROC for Swin Transformer', fontsize=20)
plt.legend(loc='lower right', fontsize=16)
plt.xlim([0, 1])
plt.ylim([0, 1])  # 要绘制的ROC曲线的x轴和y轴的0点重叠
# plt.savefig(r'K:\2020-2023HCC\579hcc\578hcc\data\ap\train_SWIN.jpg', dpi=300)
plt.show()

#%% 多个模型roc曲线
## ROC曲线3
import pandas as pd
from sklearn.metrics import roc_curve, auc
import matplotlib.pyplot as plt
 # 读取数据
# data = pd.read_excel('D:/pythondata/306hcc-clinical2.xlsx')
df = pd.read_excel(r'k:\2020-2023HCC\579hcc\578hcc\data\ap\ROC-test.xlsx')
print(df.columns)

#获取目标变量
target = df['True_label']

#将目标变量转换为二进制标签
target = (target == 1).astype(int)  #把group=1定为1，其他定为0

# 获取预测变量，为阳性组即VETC=1的预测概率
predictors = df.iloc[:, 2:]  # 第n行之后的变量为预测变量
predictors

# # 设置图形样式
# plt.style.use('seaborn-darkgrid')

# 绘制ROC曲线
plt.figure(figsize=(8, 6))
plt.plot([0, 1], [0, 1], 'k--', linewidth=3)

for col in predictors.columns:
    # 获取当前预测变量的预测结果
    y_pred = predictors[col]
    # 计算ROC曲线和AUC值
    fpr, tpr, thresholds = roc_curve(target, y_pred)
    roc_auc = auc(fpr, tpr)
    # 计算95%CI
    ci = 1.96 * (roc_auc * (1 - roc_auc) / len(target)) ** 0.5
    print("%s: AUC = %0.3f, 95%% CI = (%0.3f, %0.3f)" % (col, roc_auc, roc_auc - ci, roc_auc + ci))
    # 绘制ROC曲线
    # plt.plot(fpr, tpr, label='%s (AUC = %0.3f)' % (col, roc_auc)) #只显示AUC值
    # plt.plot(fpr, tpr, label='%s (AUC = %0.3f, 95%% CI = [%0.3f, %0.3f])' % (col, roc_auc,roc_auc - ci, roc_auc + ci))
    plt.plot(fpr, tpr,
             label='%s (AUC = %0.3f[95%% CI:%0.3f-%0.3f])' % (col, roc_auc, roc_auc - ci, roc_auc + ci), linewidth=3)  # 显示AUC值和95%CI
# 设置图例和标题
plt.xlabel('False Positive Rate', fontsize=15)
plt.ylabel('True Positive Rate', fontsize=15)
plt.title('ROC for External test', fontsize=15)  #   External test Internal test
plt.legend(loc='lower right', fontsize=12)
plt.xlim([0, 1])
plt.ylim([0, 1])  # 要绘制的ROC曲线的x轴和y轴的0点重叠
plt.savefig(r'k:\2020-2023HCC\579hcc\578hcc\data\ap\ROC-test.jpg', dpi=300)
plt.show()

#%%混淆矩阵和acc,loss曲线
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix
import seaborn as sns

# 初始化混淆矩阵
num_classes = 2  # 根据您的数据集确定类别数量
confusion_mat = np.zeros((num_classes, num_classes))

# 在测试集上进行预测，同时记录真实标签和预测标签
true_labels = []
pred_labels = []

model.eval()
with torch.no_grad():
    for images, labels in test_loader:
        images = images.float().to(device)
        labels = labels.long().to(device)
        y_pred = model(images)
        _, predicted = torch.max(y_pred, 1)
        true_labels.extend(labels.cpu().numpy())
        pred_labels.extend(predicted.cpu().numpy())
        torch.cuda.empty_cache()

# 计算混淆矩阵
confusion_mat = confusion_matrix(true_labels, pred_labels)

# 打印混淆矩阵
print("Confusion Matrix:")
print(confusion_mat)

# 绘制混淆矩阵图
plt.figure(figsize=(8, 6))
sns.heatmap(confusion_mat, annot=True, fmt="d", cmap='Blues')
plt.xlabel('Predicted Labels')
plt.ylabel('True Labels')
plt.title('Confusion Matrix')
plt.xticks(np.arange(num_classes), class_names, rotation=45)
plt.yticks(np.arange(num_classes), class_names)
plt.tight_layout()
plt.show()

# 绘制准确率和损失曲线
plt.figure(figsize=(10, 4))
plt.subplot(1, 2, 1)
plt.plot(range(1, num_epochs+1), train_losses, label='Training Loss')
plt.plot(range(1, num_epochs+1), test_losses, label='Testing Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(range(1, num_epochs+1), train_accs, label='Training Accuracy')
plt.plot(range(1, num_epochs+1), test_accs, label='Testing Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()
plt.tight_layout()
plt.show()


#%%单个模型评价指标计算
#计算模型的AUC、95% CI、准确率、敏感性、特异性、阳性预测值、阴性预测值、精确率、召回率和F1分数
# 数据格式如下：
# Image Path	True Labels	Predict_label	Probability
# baizhengqiang  	0	      0	       [0.6307259202003479, 0.3692740797996521]
# caimingyao	    1	      1	       [0.20197848975658417, 0.7980214953422546]
# caopeide	        0	      0	       [0.7363142967224121, 0.2636857330799103]

import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.utils import resample
from openpyxl import Workbook

# 假设您的数据保存在名为 data.csv 的文件中
data = pd.read_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\数据结果\test-resnet3期融合.xlsx')

# 提取真实标签和预测标签以及概率
y_true = data['True_label']
y_pred = data['Predict_label']
probabilities = data['Probability'].apply(eval).apply(lambda x: x[1])

# 计算AUC
roc_auc = roc_auc_score(y_true, probabilities)

# 计算95% CI
ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
ci_lower = roc_auc - ci
ci_upper = roc_auc + ci

# 计算混淆矩阵以获得其他指标
tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

# 计算准确率，敏感性，特异性，阳性预测值，阴性预测值，精确率，召回率，f1分数
accuracy = accuracy_score(y_true, y_pred)
sensitivity = recall_score(y_true, y_pred)
specificity = tn / (tn + fp)
ppv = precision_score(y_true, y_pred)
npv = tn / (tn + fn)
precision = ppv
recall = sensitivity
f1 = f1_score(y_true, y_pred)

# 将结果保存到Excel文件中
results = {
    'Metric': ['AUC', '95% CI Lower', '95% CI Upper', 'Accuracy', 'Sensitivity', 'Specificity', 'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'],
    'Value': [roc_auc, ci_lower, ci_upper, accuracy, sensitivity, specificity, ppv, npv, precision, recall, f1]
}

df_results = pd.DataFrame(results).T
df_results.columns = df_results.iloc[0]
df_results = df_results[1:]

# 保存到Excel文件
df_results.to_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\数据结果\test_result.xlsx', index=False)

#%% 多个模型评价指标批量计算
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os

# 目标文件夹路径
folder_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\数据结果\1'
output_path = os.path.join(folder_path, 'result_summary.xlsx')

# 初始化结果列表
results_list = []

# 遍历文件夹中的所有Excel文件
for filename in os.listdir(folder_path):
    if filename.endswith('.xlsx') and filename != 'result_summary.xlsx':
        file_path = os.path.join(folder_path, filename)
        
        # 读取数据
        data = pd.read_excel(file_path)

        # 提取真实标签和预测标签以及概率
        y_true = data['True_label']
        y_pred = data['Predict_label']
        probabilities = data['Probability'].apply(eval).apply(lambda x: x[1])

        # 计算AUC
        roc_auc = roc_auc_score(y_true, probabilities)

        # 计算95% CI
        ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
        ci_lower = roc_auc - ci
        ci_upper = roc_auc + ci

        # 计算混淆矩阵以获得其他指标
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

        # 计算准确率，敏感性，特异性，阳性预测值，阴性预测值，精确率，召回率，f1分数
        accuracy = accuracy_score(y_true, y_pred)
        sensitivity = recall_score(y_true, y_pred)
        specificity = tn / (tn + fp)        
        ppv = precision_score(y_true, y_pred)
        npv = tn / (tn + fn)
        precision = ppv
        recall = sensitivity
        f1 = f1_score(y_true, y_pred)

        # 添加结果到列表
        results_list.append([
            filename, roc_auc, ci_lower, ci_upper, accuracy, sensitivity, specificity, ppv, npv, precision, recall, f1
        ])

# 创建结果DataFrame
results_df = pd.DataFrame(results_list, columns=[
    'Filename', 'AUC', '95% CI Lower', '95% CI Upper', 'Accuracy', 'Sensitivity', 'Specificity', 
    'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'
])

# 保存结果到Excel文件
results_df.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%%元学习，深度元学习，多实例学习 需要进一步研究


#%% MambaVision:A Hybrid Mamba-Transformer Vision Backbone 迁移学习 
# MambaVision:2D 模型迁移学习 成功，不是3D模型 
# autodl云端成功，本地未安装成功   https://github.com/NVlabs/MambaVision
# pip install mambavision  或者 pip install git+https://github.com/NVlabs/MambaVision.git
# pip install mambavision --index-url=https://pypi.org/simple  使用pip源安装
# pip install mambavision --index-url https://mirrors.aliyun.com/pypi/simple 
# pip install mamba_ssm --index-url https://pypi.tuna.tsinghua.edu.cn/simple
# 下载预训练权重https://huggingface.co/nvidia/MambaVision-T-1K
#需要linux环境，windows不行
# linux中安装pip
# curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
# sudo python3 get-pip.py

# from mambavision import create_model
# model = create_model('mamba_vision_T', pretrained=True, model_path="D:/anaconda3/envs/MambaVision-main/pretrained/mambavision_tiny_1k.pth.tar")
# # model = create_model('mamba_vision_T',pretrained=True,model_path="/root/autodl-tmp/mambavision_tiny_1k.pth.tar")
# model

from transformers import AutoModelForImageClassification
model = AutoModelForImageClassification.from_pretrained("nvidia/MambaVision-T-1K", trust_remote_code=True)
model


#%% 3D 深度学习模型数据预处理 
#安装monai及其依赖包get_ipython().system('pip install monai')
# get_ipython().system('python -c "import monai" || pip install -q "monai-weekly[nibabel, tqdm]" ')# 尝试导入MONAI，如果失败则安静模式安装monai-weekly及其依赖
# get_ipython().system('python -c "import matplotlib" || pip install -q matplotlib  ')# 尝试导入matplotlib，如果失败则安静模式安装matplotlib
# get_ipython().run_line_magic('matplotlib', 'inline')# 设置matplotlib图表在Jupyter笔记本中内联显示
# pip install pandas SimpleITK openpyxl
import os
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset
import torch           
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import SimpleITK as sitk
# from torchvision.models.video import *
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

#resize成统一de 图像体积
# resized_shape = (224, 224, 32) #vivit
resized_shape = (224, 224, 8) # videomamba_small时为8,videomamba_middle=8
# resized_shape = (224, 224, 16) #对于mvit模型，要用这个体积


# 定义转换操作
train_transforms = Compose([
    LoadImaged(keys=["image"]),  # 加载图像
    EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
    Resized(keys=["image"], spatial_size= resized_shape),  # 调整图像尺寸
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
    RandRotate90d(keys=["image"], prob=0.3, spatial_axes=[0, 1]),  # 随机90度旋转
    EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=resized_shape),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# 定义标签
label_name = 'PHCC'

def TumorDataset(data_dir, label_excel):
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        # 从图像文件路径中提取文件名（不包含扩展名）
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        # 从DataFrame中查找对应的标签
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label,"file_name": file_names})    
    return data

# 使用示例
train_dir = '/root/autodl-tmp/tumor/train/ap'
test_dir = '/root/autodl-tmp/tumor/val/ap'
train_file_path = '/root/autodl-tmp/tumor/train2.xlsx'
test_file_path = '/root/autodl-tmp/tumor/val2.xlsx'

# train_dir = r'K:\734HCC\all-HCC\734hcctumor\tumor\train\ap'   
# test_dir = r'K:\734HCC\all-HCC\734hcctumor\tumor\val\ap'
# train_file_path = r'K:\734HCC\all-HCC\734hcctumor\data\train2.xlsx'  
# test_file_path = r'K:\734HCC\all-HCC\734hcctumor\data\val2.xlsx' 

#使用docker时，整个文件夹路径挂载到docker服务器
# docker run -v K:\734HCC\all-HCC\734hcctumor:/root/tumor -it mambavision
# ls /root/tumor
# train_dir = '/root/tumor/tumor/train/ap'   
# test_dir = '/root/tumor/tumor/val/ap'
# train_file_path = '/root/tumor/data/train2.xlsx'  
# test_file_path = '/root/tumor/data/val2.xlsx' 

#加载数据
train_files = TumorDataset(train_dir, train_file_path)
test_files = TumorDataset(test_dir, test_file_path)
print(train_files[:5])
print(test_files[:5])

# 创建数据集
train_dataset = Dataset(data=train_files, transform=train_transforms)
test_dataset = Dataset(data=test_files, transform=test_transforms)

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=2, num_workers=2)

# %% 3D VideoMamba模型虚拟环境,autodl上运行成功了
# 您可以使用 VideoMamba 替换视频任务中的主干网络。以下是替换步骤：
# git clone https://github.com/OpenGVLab/VideoMamba
# cd VideoMamba
# conda create -n mamba python=3.10
# conda activate mamba
# pip install torch==2.1.1 torchvision==0.16.1 torchaudio==2.1.1 --index-url https://download.pytorch.org/whl/cu118
#上面的已下载到阿里云盘
# pip install -r requirements.txt  报错时把apex版本改成==0.9.10.dev0，需要安装scikit-image 包而不是 skimage
# pip install torch-2.1.1+cu118-cp310-cp310-linux_x86_64.whl 再安装一遍，替换错误的torch版本
# pip install -e causal-conv1d
# pip install -e mamba
# 换到mamba虚拟环境调用代码,如果报错的话是torchvision版本问题，请用pip install torchvision==0.16.1

import torch
import torch.nn as nn
import sys
sys.path.append('/root/autodl-tmp/VideoMamba')
# sys.path.append(r'E:\1.githubcode\VideoMamba')

#1.videomamba_middle_pretrain模型
# from videomamba.video_sm.models.videomamba_pretrain import videomamba_middle_pretrain  #videomamba_pretrain.py
# model = videomamba_middle_pretrain(pretrained=True)
# model

#2.VisionMamba模型 
# from videomamba.video_sm.models.videomamba_pretrain import VisionMamba 
# model = VisionMamba()
# model
# model.clip_decoder[0].head = nn.Linear(in_features=768, out_features=2, bias=True)
# model.clip_decoder[0].norm = nn.LayerNorm((2,), eps=1e-05, elementwise_affine=True)
# model

#3.videomamba_small模型
from videomamba.video_sm.models.videomamba import videomamba_small,videomamba_middle

#1.videomamba_small
# model = videomamba_small()
# # 加载模型权重
# model.load_state_dict(torch.load('/root/autodl-tmp/VideoMamba/videomamba_s16_in1k_res224.pth'), strict=False)
# model

# for param in model.parameters():
#     param.requires_grad = False

# model.head = nn.Linear(in_features=384, out_features=2, bias=True)# videomamba_small
# model

# 2.videomamba_middle
model = videomamba_middle()
# 加载模型权重
model.load_state_dict(torch.load('/root/autodl-tmp/VideoMamba/videomamba_m16_in1k_res224to448.pth'), strict=False)
model

for param in model.parameters():
    param.requires_grad = False

model.head = nn.Linear(in_features=576, out_features=2, bias=True)# videomamba_small
model

#%% 模型训练videomamba_small 跑成功
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

criterion = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.005) #vivit 
optimizer = torch.optim.Adam(model.parameters(), lr=0.01) #videomamba
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
# best_model_path = "/root/autodl-tmp/tumor/hbp_best_model.pth"

# 训练循环
num_epochs = 20

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  #shape (B,3,H,W,D)   
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度                  
        images = images.permute(0, 1, 4, 2, 3)  #videomamba模型        
        # print(images.shape)  # 应该是 (B, 3, D, H, W)  D=8
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量           
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度            
            images = images.permute(0, 1, 4, 2, 3)  #videomamba模型 
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 检查是否是最佳模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)
    #     print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#保存模型
# PATH = '/root/autodl-tmp/HCC/ap_r3d-swin3d.pth'
# # PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
# torch.save(model.state_dict(), PATH)

# 加载最优模型
# model.load_state_dict(torch.load(best_model_path))
# print(f'Loaded best model with accuracy: {best_accuracy:.3f}%')

#%% vivit模型,已跑成功
#报错的话用conda install conda-forge::transformers 更新一下transformers
#报错huggingface-hub的话用 pip install huggingface-hub --upgrade
# import os
# os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  #huggingface国内镜像源

# 把huggingface的files and versions文件均下载到本地，然后直接加载路径，模型名称要一样
#https://huggingface.co/google/vivit-b-16x2/tree/main

from transformers import AutoTokenizer, AutoModelForVideoClassification

# 本地模型路径
# model_path = r"C:\Users\<USER>\.cache\huggingface\hub\vivit-b-16x2"
model_path = "/root/autodl-tmp/vivit-b-16x2"        

# 从本地路径加载模型
model = AutoModelForVideoClassification.from_pretrained(model_path)

# 打印模型以确认加载成功
print(model)

model.classifier = nn.Linear(in_features=768, out_features=2, bias=True)
print(model)

# 方法2
# from transformers import AutoTokenizer, AutoModelForVideoClassification
# tokenizer = AutoTokenizer.from_pretrained("google/vivit-b-16x2")
# model = AutoModelForVideoClassification.from_pretrained("google/vivit-b-16x2")


#% 模型训练，适用于vivit模型，已跑成功
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

criterion = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.005) #vivit 
optimizer = torch.optim.Adam(model.parameters(), lr=0.001) #videomamba
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 保存最优模型的变量
best_accuracy = 0.0
# best_model_path = "/root/autodl-tmp/tumor/hbp_best_model.pth"

# 训练循环
num_epochs = 20

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    for batch_idx, batch in enumerate(train_loader):
        images = batch['image'].to(device)  # 直接获取处理后的图像张量   
        images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
        # print(images.shape)
        images = images.permute(0, 4, 1, 2, 3) #对于vivit模型，视频的输入格式
        labels = batch['label'].long().to(device)  # 假设标签也在batch中

        optimizer.zero_grad()
        y_pred = model(images)
        loss = criterion(y_pred.logits, labels)
        loss.backward()
        optimizer.step()

        with torch.no_grad():
            y_pred = torch.argmax(y_pred.logits, dim=1)
            correct += (y_pred == labels).sum().item()
            total += labels.size(0)
            running_loss += loss.item() * images.size(0)

    exp_lr_scheduler.step()
    torch.cuda.empty_cache()

    epoch_loss = running_loss / len(train_dataset)
    epoch_acc = 100 * correct / total
    print(f'Training Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.3f}%')
  
    # 测试循环
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)  # 直接获取处理后的图像张量           
            images = images.expand(-1, 3, -1, -1, -1)  # 扩展3通道维度
            images = images.permute(0, 4, 1, 2, 3)  #对于vivit模型，视频的输入格式
            labels = batch['label'].long().to(device)  # 假设标签也在batch中

            y_pred = model(images)
            _, predicted = torch.max(y_pred.logits, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            torch.cuda.empty_cache()

    accuracy = 100 * correct / total
    print(f'Testing Epoch [{epoch+1}/{num_epochs}], Testing Accuracy: {accuracy:.3f}%')

    # # 检查是否是最佳模型
    # if accuracy > best_accuracy:
    #     best_accuracy = accuracy
    #     torch.save(model.state_dict(), best_model_path)
    #     print(f'Best model saved with accuracy: {best_accuracy:.3f}%')

#保存模型
# PATH = '/root/autodl-tmp/HCC/ap_r3d-swin3d.pth'
# # PATH = '/root/autodl-tmp/appphbp_swin_model.pth' 
# torch.save(model.state_dict(), PATH)

# 加载最优模型
# model.load_state_dict(torch.load(best_model_path))
# print(f'Loaded best model with accuracy: {best_accuracy:.3f}%')

#%%基于3DResnet模型的CAM可视化热图——PyTorch实现   根据csdn代码修改
#根据这个代码修改而来 https://github.com/KennanYang/CAM-demo-for-3d-medical-image 
import numpy as np
import torch
import cv2
import nibabel
from torchvision.models.video import r3d_18, R3D_18_Weights
from skimage.transform import resize
from matplotlib import pyplot as plt
import scipy.ndimage as ndimage

# 读取数据
MRI_path = r'H:\1.HCC-VETC\734HCC\all-HCC\490HCC-suzhou\490HCC\image\ap\baoxiuying-ap.nii.gz'
MRI = nibabel.load(MRI_path)
MRI_array = MRI.get_fdata()
MRI_array = MRI_array.astype('float32')

# 数据预处理
max_value = MRI_array.max()
MRI_array = MRI_array / max_value

# Repeat the single channel to create a 3-channel tensor
MRI_array_3ch = np.repeat(MRI_array[np.newaxis, :, :, :], 3, axis=0)

MRI_tensor = torch.FloatTensor(MRI_array_3ch).unsqueeze(0)  # (N, C, D, H, W)

# 将数据移动到GPU
MRI_tensor = MRI_tensor.cuda()

# 加载预训练的ResNet3D模型
grad_model = r3d_18(pretrained=True).cuda()
grad_model.eval()

# 使用register_forward_hook()获取特征图
class LayerActivations:
    features = None

    def __init__(self, model, layer_num):
        self.hook = model.layer3[1].conv2.register_forward_hook(self.hook_fn)

    def hook_fn(self, module, input, output):
        self.features = output.cpu()

    def remove(self):  # 移除hook
        self.hook.remove()

# 实例化并获取特定层的输出
conv_out = LayerActivations(grad_model, 0)

# 前向传播
output = grad_model(MRI_tensor)
cam = conv_out.features  # 获取特定层的输出
conv_out.remove()  # 删除hook

# 处理CAM
cam = cam.cpu().detach().numpy().squeeze()
cam = cam[1]  # 选择特定通道
capi = resize(cam, (MRI_tensor.shape[2], MRI_tensor.shape[3], MRI_tensor.shape[4]), order=3)  # 使用立方插值
capi = np.maximum(capi, 0)
if capi.max() == capi.min():
    heatmap = np.zeros_like(capi)
else:
    heatmap = (capi - capi.min()) / (capi.max() - capi.min())

# 应用高斯滤波以减少伪影
heatmap = ndimage.gaussian_filter(heatmap, sigma=3)  # sigma值可以根据需要调整

# 打印MRI数组和热图的形状
print("MRI_array shape:", MRI_array.shape)
print("heatmap shape:", heatmap.shape)

# 自动设置切片计数
axial_slice_count = MRI_array.shape[2] // 2
coronal_slice_count = MRI_array.shape[1] // 2
sagittal_slice_count = MRI_array.shape[0] // 2

# 可视化
f, axarr = plt.subplots(3, 3, figsize=(12, 12))
f.suptitle('CAM_3D_medical_image', fontsize=30)

# Axial view (水平翻转180°)
axial_MRI_img_flipped = np.flip(np.squeeze(MRI_array[:, :, axial_slice_count]), axis=1)  # 水平翻转
img_plot = axarr[0, 0].imshow(np.rot90(axial_MRI_img_flipped, 1), cmap='gray', aspect='auto')
axarr[0, 0].axis('off')
axarr[0, 0].set_title('Axial MRI', fontsize=25)

img_plot = axarr[0, 1].imshow(np.rot90(heatmap[:, :, axial_slice_count], 1), cmap='jet', aspect='auto')
axarr[0, 1].axis('off')
axarr[0, 1].set_title('Weight-CAM', fontsize=25)

axial_overlay = cv2.addWeighted(axial_MRI_img_flipped, 0.3, heatmap[:, :, axial_slice_count], 0.6, 0)
img_plot = axarr[0, 2].imshow(np.rot90(axial_overlay, 1), cmap='jet', aspect='auto')
axarr[0, 2].axis('off')
axarr[0, 2].set_title('Overlay', fontsize=25)

# Coronal view
coronal_MRI_img = np.squeeze(MRI_array[:, coronal_slice_count, :])
coronal_grad_cmap_img = heatmap[:, coronal_slice_count, :]
img_plot = axarr[1, 0].imshow(np.rot90(coronal_MRI_img, 1), cmap='gray', aspect='auto')
axarr[1, 0].axis('off')
axarr[1, 0].set_title('Coronal MRI', fontsize=25)

img_plot = axarr[1, 1].imshow(np.rot90(coronal_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[1, 1].axis('off')
axarr[1, 1].set_title('Weight-CAM', fontsize=25)

coronal_overlay = cv2.addWeighted(coronal_MRI_img, 0.3, coronal_grad_cmap_img, 0.6, 0)
img_plot = axarr[1, 2].imshow(np.rot90(coronal_overlay, 1), cmap='jet', aspect='auto')
axarr[1, 2].axis('off')
axarr[1, 2].set_title('Overlay', fontsize=25)

# Sagittal view
sagittal_MRI_img = np.squeeze(MRI_array[sagittal_slice_count, :, :])
sagittal_grad_cmap_img = heatmap[sagittal_slice_count, :, :]
img_plot = axarr[2, 0].imshow(np.rot90(sagittal_MRI_img, 1), cmap='gray', aspect='auto')
axarr[2, 0].axis('off')
axarr[2, 0].set_title('Sagittal MRI', fontsize=25)

img_plot = axarr[2, 1].imshow(np.rot90(sagittal_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[2, 1].axis('off')
axarr[2, 1].set_title('Weight-CAM', fontsize=25)

sagittal_overlay = cv2.addWeighted(sagittal_MRI_img, 0.3, sagittal_grad_cmap_img, 0.6, 0)
img_plot = axarr[2, 2].imshow(np.rot90(sagittal_overlay, 1), cmap='jet', aspect='auto')
axarr[2, 2].axis('off')
axarr[2, 2].set_title('Overlay', fontsize=25)

plt.colorbar(img_plot, ax=axarr.ravel().tolist(), shrink=0.5)  # color bar if needed
plt.savefig(r'H:\1.HCC-VETC\734HCC\all-HCC\734hcctumor\tumor\train\gradcam\CAM_HCC2.jpg', dpi=300)
plt.show()

#%% 3D mvit_v2_s模型CAM可视化热图
import numpy as np
import torch
import cv2
import nibabel
from torchvision.models.video import mvit_v2_s, MViT_V2_S_Weights
from skimage.transform import resize
from matplotlib import pyplot as plt
import scipy.ndimage as ndimage

# 读取数据
MRI_path = r'H:\1.HCC-VETC\734HCC\all-HCC\734hcctumor\tumor\val\ap\guofengzhen-ap.nii.gz'
MRI = nibabel.load(MRI_path)
MRI_array = MRI.get_fdata()
MRI_array = MRI_array.astype('float32')

# 数据预处理
max_value = MRI_array.max()
MRI_array = MRI_array / max_value

# 调整尺寸使其与模型的输入形状匹配
target_shape = (16, 224, 224)
MRI_array_resized = resize(MRI_array, target_shape, mode='reflect', anti_aliasing=True)

# Repeat the single channel to create a 3-channel tensor
MRI_array_3ch = np.repeat(MRI_array_resized[np.newaxis, :, :, :], 3, axis=0)

MRI_tensor = torch.FloatTensor(MRI_array_3ch).unsqueeze(0)  # (N, C, D, H, W)

# 将数据移动到GPU
MRI_tensor = MRI_tensor.cuda()

# 加载预训练的MViT模型
grad_model = mvit_v2_s(weights=MViT_V2_S_Weights.DEFAULT).cuda()
grad_model
grad_model.eval()

# 使用register_forward_hook()获取特征图,如果图不好，请修改图层
class LayerActivations:
    features = None

    def __init__(self, model, layer_name):
        self.hook = model.blocks[-2].norm2.register_forward_hook(self.hook_fn)
        # self.hook = model.norm.register_forward_hook(self.hook_fn)

    def hook_fn(self, module, input, output):
        self.features = output.cpu()

    def remove(self):  # 移除hook
        self.hook.remove()

# 实例化并获取特定层的输出
conv_out = LayerActivations(grad_model, 'blocks[-2].norm2')

# 前向传播
output = grad_model(MRI_tensor)
cam = conv_out.features  # 获取特定层的输出
conv_out.remove()  # 删除hook

# 处理CAM
cam = cam.cpu().detach().numpy().squeeze()
cam = cam[1]  # 选择特定通道
capi = resize(cam, (MRI_tensor.shape[2], MRI_tensor.shape[3], MRI_tensor.shape[4]), order=3)  # 使用立方插值
capi = np.maximum(capi, 0)
if capi.max() == capi.min():
    heatmap = np.zeros_like(capi)
else:
    heatmap = (capi - capi.min()) / (capi.max() - capi.min())

# 应用高斯滤波以减少伪影
heatmap = ndimage.gaussian_filter(heatmap, sigma=3)  # sigma值可以根据需要调整

# 打印MRI数组和热图的形状
print("MRI_array shape:", MRI_array.shape)
print("heatmap shape:", heatmap.shape)

# 自动设置切片计数
axial_slice_count = target_shape[2] // 2
coronal_slice_count = target_shape[1] // 2
sagittal_slice_count = target_shape[0] // 2

# 可视化
f, axarr = plt.subplots(3, 3, figsize=(12, 12))
f.suptitle('CAM_3D_medical_image', fontsize=30)

# Axial view (水平翻转180°)
axial_MRI_img_flipped = np.flip(np.squeeze(MRI_array_resized[:, :, axial_slice_count]), axis=1)  # 水平翻转
img_plot = axarr[0, 0].imshow(np.rot90(axial_MRI_img_flipped, 1), cmap='gray', aspect='auto')
axarr[0, 0].axis('off')
axarr[0, 0].set_title('Axial MRI', fontsize=25)

axial_grad_cmap_img = np.squeeze(heatmap[:, :, axial_slice_count])
img_plot = axarr[0, 1].imshow(np.rot90(axial_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[0, 1].axis('off')
axarr[0, 1].set_title('Weight-CAM', fontsize=25)

axial_overlay = cv2.addWeighted(axial_MRI_img_flipped, 0.3, axial_grad_cmap_img, 0.6, 0)
img_plot = axarr[0, 2].imshow(np.rot90(axial_overlay, 1), cmap='jet', aspect='auto')
axarr[0, 2].axis('off')
axarr[0, 2].set_title('Overlay', fontsize=25)

# Coronal view
coronal_MRI_img = np.squeeze(MRI_array_resized[:, coronal_slice_count, :])
coronal_grad_cmap_img = np.squeeze(heatmap[:, coronal_slice_count, :])
img_plot = axarr[1, 0].imshow(np.rot90(coronal_MRI_img, 1), cmap='gray', aspect='auto')
axarr[1, 0].axis('off')
axarr[1, 0].set_title('Coronal MRI', fontsize=25)

img_plot = axarr[1, 1].imshow(np.rot90(coronal_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[1, 1].axis('off')
axarr[1, 1].set_title('Weight-CAM', fontsize=25)

coronal_overlay = cv2.addWeighted(coronal_MRI_img, 0.3, coronal_grad_cmap_img, 0.6, 0)
img_plot = axarr[1, 2].imshow(np.rot90(coronal_overlay, 1), cmap='jet', aspect='auto')
axarr[1, 2].axis('off')
axarr[1, 2].set_title('Overlay', fontsize=25)

# Sagittal view
sagittal_MRI_img = np.squeeze(MRI_array_resized[sagittal_slice_count, :, :])
sagittal_grad_cmap_img = np.squeeze(heatmap[sagittal_slice_count, :, :])
img_plot = axarr[2, 0].imshow(np.rot90(sagittal_MRI_img, 1), cmap='gray', aspect='auto')
axarr[2, 0].axis('off')
axarr[2, 0].set_title('Sagittal MRI', fontsize=25)

img_plot = axarr[2, 1].imshow(np.rot90(sagittal_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[2, 1].axis('off')
axarr[2, 1].set_title('Weight-CAM', fontsize=25)

sagittal_overlay = cv2.addWeighted(sagittal_MRI_img, 0.3, sagittal_grad_cmap_img, 0.6, 0)
img_plot = axarr[2, 2].imshow(np.rot90(sagittal_overlay, 1), cmap='jet', aspect='auto')
axarr[2, 2].axis('off')
axarr[2, 2].set_title('Overlay', fontsize=25)

plt.colorbar(img_plot, ax=axarr.ravel().tolist(), shrink=0.5)  # color bar if needed
plt.savefig(r'H:\1.HCC-VETC\734HCC\all-HCC\734hcctumor\tumor\train\gradcam\CAM_mvit.png')
plt.show()

#%% Swin3d_s模型CAM可视化热图 
import numpy as np
import torch
import cv2
import nibabel
from torchvision.models.video import swin3d_s, Swin3D_S_Weights
from skimage.transform import resize
from matplotlib import pyplot as plt
import scipy.ndimage as ndimage

# 读取数据
MRI_path = r'H:\1.HCC-VETC\734HCC\all-HCC\734hcctumor\tumor\val\ap\guofengzhen-ap.nii.gz'
MRI = nibabel.load(MRI_path)
MRI_array = MRI.get_fdata()
MRI_array = MRI_array.astype('float32')

# 数据预处理
max_value = MRI_array.max()
MRI_array = MRI_array / max_value

# Repeat the single channel to create a 3-channel tensor
MRI_array_3ch = np.repeat(MRI_array[np.newaxis, :, :, :], 3, axis=0)

MRI_tensor = torch.FloatTensor(MRI_array_3ch).unsqueeze(0)  # (N, C, D, H, W)

# 将数据移动到GPU
MRI_tensor = MRI_tensor.cuda()

# 加载预训练的Swin3D模型
grad_model = swin3d_s(pretrained=True).cuda()
grad_model
grad_model.eval()

# 使用register_forward_hook()获取特征图，修改可视化的层名
class LayerActivations:
    features = None

    def __init__(self, model, layer_name):
        self.hook = model.features[-5].register_forward_hook(self.hook_fn)
        # self.hook = model.norm.register_forward_hook(self.hook_fn)
    def hook_fn(self, module, input, output):
        self.features = output.cpu()

    def remove(self):  # 移除hook
        self.hook.remove()

# 实例化并获取特定层的输出
conv_out = LayerActivations(grad_model, 0)

# 前向传播
output = grad_model(MRI_tensor)
cam = conv_out.features  # 获取特定层的输出
conv_out.remove()  # 删除hook

# 处理CAM
cam = cam.cpu().detach().numpy().squeeze()
cam = cam[1]  # 选择特定通道
capi = resize(cam, (MRI_tensor.shape[2], MRI_tensor.shape[3], MRI_tensor.shape[4]))
capi = np.maximum(capi, 0)
if capi.max() == capi.min():
    heatmap = np.zeros_like(capi)
else:
    heatmap = (capi - capi.min()) / (capi.max() - capi.min())

# 应用高斯滤波以减少伪影
heatmap = ndimage.gaussian_filter(heatmap, sigma=3)  # sigma值可以根据需要调整

# 打印MRI数组和热图的形状
print("MRI_array shape:", MRI_array.shape)
print("heatmap shape:", heatmap.shape)

# 自动设置切片计数
axial_slice_count = MRI_array.shape[2] // 2
coronal_slice_count = MRI_array.shape[1] // 2
sagittal_slice_count = MRI_array.shape[0] // 2

# 可视化
f, axarr = plt.subplots(3, 3, figsize=(12, 12))
f.suptitle('CAM_3D_medical_image', fontsize=30)

# Axial view (水平翻转180°)
axial_MRI_img_flipped = np.flip(np.squeeze(MRI_array[:, :, axial_slice_count]), axis=1)  # 水平翻转
img_plot = axarr[0, 0].imshow(np.rot90(axial_MRI_img_flipped, 1), cmap='gray', aspect='auto')
axarr[0, 0].axis('off')
axarr[0, 0].set_title('Axial MRI', fontsize=25)

axial_grad_cmap_img = np.squeeze(heatmap[:, :, axial_slice_count])
img_plot = axarr[0, 1].imshow(np.rot90(axial_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[0, 1].axis('off')
axarr[0, 1].set_title('Weight-CAM', fontsize=25)

axial_overlay = cv2.addWeighted(axial_MRI_img_flipped, 0.3, axial_grad_cmap_img, 0.6, 0)
img_plot = axarr[0, 2].imshow(np.rot90(axial_overlay, 1), cmap='jet', aspect='auto')
axarr[0, 2].axis('off')
axarr[0, 2].set_title('Overlay', fontsize=25)

# Coronal view
coronal_MRI_img = np.squeeze(MRI_array[:, coronal_slice_count, :])
coronal_grad_cmap_img = np.squeeze(heatmap[:, coronal_slice_count, :])
img_plot = axarr[1, 0].imshow(np.rot90(coronal_MRI_img, 1), cmap='gray', aspect='auto')
axarr[1, 0].axis('off')
axarr[1, 0].set_title('Coronal MRI', fontsize=25)

img_plot = axarr[1, 1].imshow(np.rot90(coronal_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[1, 1].axis('off')
axarr[1, 1].set_title('Weight-CAM', fontsize=25)

coronal_overlay = cv2.addWeighted(coronal_MRI_img, 0.3, coronal_grad_cmap_img, 0.6, 0)
img_plot = axarr[1, 2].imshow(np.rot90(coronal_overlay, 1), cmap='jet', aspect='auto')
axarr[1, 2].axis('off')
axarr[1, 2].set_title('Overlay', fontsize=25)

# Sagittal view
sagittal_MRI_img = np.squeeze(MRI_array[sagittal_slice_count, :, :])
sagittal_grad_cmap_img = np.squeeze(heatmap[sagittal_slice_count, :, :])
img_plot = axarr[2, 0].imshow(np.rot90(sagittal_MRI_img, 1), cmap='gray', aspect='auto')
axarr[2, 0].axis('off')
axarr[2, 0].set_title('Sagittal MRI', fontsize=25)

img_plot = axarr[2, 1].imshow(np.rot90(sagittal_grad_cmap_img, 1), cmap='jet', aspect='auto')
axarr[2, 1].axis('off')
axarr[2, 1].set_title('Weight-CAM', fontsize=25)

sagittal_overlay = cv2.addWeighted(sagittal_MRI_img, 0.3, sagittal_grad_cmap_img, 0.6, 0)
img_plot = axarr[2, 2].imshow(np.rot90(sagittal_overlay, 1), cmap='jet', aspect='auto')
axarr[2, 2].axis('off')
axarr[2, 2].set_title('Overlay', fontsize=25)

plt.colorbar(img_plot, ax=axarr.ravel().tolist(), shrink=0.5)  # color bar if needed
plt.savefig(r'H:\1.HCC-VETC\734HCC\all-HCC\734hcctumor\tumor\train\gradcam\CAM_swin3d.png')
plt.show()

#%%

# %% KAN新的网络学习 
# KAN 项目地址 https://github.com/KindXiaoming/pykan?tab=readme-ov-file, 官方有详细的学习文件
# pytorch版 https://github.com/Blealtan/efficient-kan?tab=readme-ov-file
# https://github.com/chenziwenhaoshuai/Vision-KAN    #2d模型
# pip install VisionKAN  
# from VisionKAN import create_model, train_one_epoch, evaluate
# KAN_model = create_model(
#     model_name='deit_tiny_patch16_224_KAN',
#     pretrained=False,
#     hdim_kan=192,
#     num_classes=100,
#     drop_rate=0.0,
#     drop_path_rate=0.05,
#     img_size=224,
#     batch_size=144
# )
# KAN_model 


#%% conda install conda-forge::transformers 更新一下transformers
# import os
# os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  #huggingface国内镜像源

# 把huggingface的files and versions文件均下载到本地，然后直接加载路径，模型名称要一样
#https://huggingface.co/google/vivit-b-16x2/tree/main

from transformers import AutoTokenizer, AutoModelForVideoClassification

# 本地模型路径
model_path = r"C:\Users\<USER>\.cache\huggingface\hub\vivit-b-16x2"

# 从本地路径加载模型
model = AutoModelForVideoClassification.from_pretrained(model_path)

# 打印模型以确认加载成功
print(model)

# %%https://github.com/ge-xing/SegMamba
# git clone https://github.com/ge-xing/SegMamba.git
# cd SegMamba
# cd causal-conv1d
# python setup.py install
# cd mamba
# python setup.py install
# pip install monai
# python 0_inference.py
import sys
sys.path.append('/root/autodl-tmp/SegMamba')
import torch 
from model_segmamba.segmamba import SegMamba

t1 = torch.rand(1, 4, 128, 128, 128).cuda()


model = SegMamba(in_chans=4,
                 out_chans=4,
                 depths=[2,2,2,2],
                 feat_size=[48, 96, 192, 384]).cuda()

out = model(t1)

print(out.shape)

# %%
