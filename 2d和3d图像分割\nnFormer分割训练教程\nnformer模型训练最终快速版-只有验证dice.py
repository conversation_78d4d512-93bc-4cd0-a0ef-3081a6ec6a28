#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nnFormer一键训练脚本 - 使用nnUNet预处理 + nnFormer训练
基于nnFormer项目，数据预处理完全采用nnUNet方式

特性:
- 仅进行3D数据预处理，跳过2D预处理以提高效率
- 显示总体预处理进度，不显示详细的单个文件进度
- 智能检查已有预处理数据，避免重复处理

作者: AI Assistant
日期: 2024
"""
# pip install nibabel
# pip install batchgenerators==0.21

import os
import sys
import json
import shutil
import logging
import argparse
import subprocess
from pathlib import Path
from typing import List, Optional
import numpy as np
import torch
import random

# 设置随机种子
def set_random_seeds(seed=42):
    """设置所有随机种子以确保可重现性"""
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = False
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.enabled = True
    random.seed(seed)

# 设置日志
def setup_logging(log_file: str = None) -> logging.Logger:
    """设置日志配置"""
    logger = logging.getLogger('nnFormer_Training')
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

class nnFormerConfig:
    """nnFormer配置类"""
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # 基础路径配置
        self.base_dir = "/root/autodl-tmp"
        self.image_dir = "/root/autodl-tmp/data/images"  # 原始图像目录
        self.mask_dir = "/root/autodl-tmp/data/masks"    # 原始标签目录
        
        # nnFormer环境变量配置
        self.nnformer_raw_data_base = os.path.join(self.base_dir, "nnFormer_data")
        self.nnformer_preprocessed = os.path.join(self.base_dir, "nnFormer_preprocessed") 
        self.results_folder = os.path.join(self.base_dir, "nnFormer_results")
        
        # 数据集配置
        self.dataset_id = 100
        self.dataset_name = "CustomDataset"
        self.task_name = f"Task{self.dataset_id:03d}_{self.dataset_name}"
        
        # 训练配置
        self.network = "3d_fullres"
        self.trainer = "nnFormerTrainerV2"
        self.fold = 0
        self.max_epochs = 1000
        self.initial_lr = 1e-2
        
        # 数据配置
        self.channel_names = {"0": "CT"}  # 可根据实际情况修改
        self.labels = {"background": 0, "target": 1}  # 可根据实际情况修改
        self.train_ratio = 0.8
        
        # 预处理配置
        self.num_threads = 8
        self.verify_dataset_integrity = False  # 默认跳过验证以避免numpy兼容性问题

        # 控制选项
        self.run_preprocessing = True
        self.run_training = True
        self.continue_training = False
        self.validation_only = False
        self.force_reprocess = False  # 是否强制重新处理（跳过已有数据检查）
        
    def setup_environment(self):
        """设置nnFormer环境变量"""
        env_vars = {
            'nnFormer_raw_data_base': self.nnformer_raw_data_base,
            'nnFormer_preprocessed': self.nnformer_preprocessed,
            'RESULTS_FOLDER': self.results_folder,
            'nnFormer_def_n_proc': str(self.num_threads)
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            self.logger.info(f"设置环境变量: {key} = {value}")
        
        # 创建必要目录
        directories = [
            self.nnformer_raw_data_base,
            self.nnformer_preprocessed,
            self.results_folder
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            self.logger.info(f"创建目录: {directory}")

class nnUNetDataConverter:
    """nnUNet风格的数据转换器"""
    def __init__(self, config: nnFormerConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
    def check_conversion_exists(self) -> bool:
        """检查数据格式转换是否已完成"""
        try:
            task_dir = os.path.join(self.config.nnformer_raw_data_base, "nnFormer_raw_data", self.config.task_name)

            # 检查任务目录是否存在
            if not os.path.exists(task_dir):
                return False

            # 检查dataset.json是否存在
            dataset_json = os.path.join(task_dir, "dataset.json")
            if not os.path.exists(dataset_json):
                return False

            # 检查图像和标签目录
            images_tr_dir = os.path.join(task_dir, "imagesTr")
            labels_tr_dir = os.path.join(task_dir, "labelsTr")

            if not os.path.exists(images_tr_dir) or not os.path.exists(labels_tr_dir):
                return False

            # 检查是否有文件
            image_files = [f for f in os.listdir(images_tr_dir) if f.endswith('.nii.gz')]
            label_files = [f for f in os.listdir(labels_tr_dir) if f.endswith('.nii.gz')]

            if len(image_files) == 0 or len(label_files) == 0:
                return False

            self.logger.info(f"发现已转换的数据: {len(image_files)} 个图像文件, {len(label_files)} 个标签文件")
            return True

        except Exception as e:
            self.logger.error(f"检查数据转换状态时出错: {e}")
            return False

    def convert_to_nnunet_format(self) -> bool:
        """将数据转换为nnUNet格式"""
        try:
            # 首先检查是否已完成转换（除非强制重新处理）
            if not self.config.force_reprocess and self.check_conversion_exists():
                self.logger.info("发现已转换的数据，跳过数据格式转换步骤")
                return True

            self.logger.info("开始数据格式转换...")

            # 创建任务目录
            task_dir = os.path.join(self.config.nnformer_raw_data_base, "nnFormer_raw_data", self.config.task_name)
            images_tr_dir = os.path.join(task_dir, "imagesTr")
            labels_tr_dir = os.path.join(task_dir, "labelsTr")
            images_ts_dir = os.path.join(task_dir, "imagesTs")

            for directory in [images_tr_dir, labels_tr_dir, images_ts_dir]:
                os.makedirs(directory, exist_ok=True)

            # 获取所有图像文件
            image_files = self._get_image_files(self.config.image_dir)
            mask_files = self._get_image_files(self.config.mask_dir)

            if len(image_files) == 0:
                raise ValueError(f"在 {self.config.image_dir} 中未找到图像文件")

            self.logger.info(f"找到 {len(image_files)} 个图像文件")

            # 复制和重命名文件，并获取案例名称
            case_names = self._copy_and_rename_files(image_files, mask_files, images_tr_dir, labels_tr_dir)

            # 创建dataset.json
            self._create_dataset_json(task_dir, case_names)

            self.logger.info("数据格式转换完成")
            return True

        except Exception as e:
            self.logger.error(f"数据格式转换失败: {e}")
            return False
    
    def _get_image_files(self, directory: str) -> List[str]:
        """获取目录中的图像文件"""
        if not os.path.exists(directory):
            return []
        
        extensions = ['.nii.gz', '.nii', '.mha', '.mhd']
        files = []
        
        for ext in extensions:
            files.extend(Path(directory).glob(f"*{ext}"))
        
        return sorted([str(f) for f in files])
    
    def _extract_case_name_from_filename(self, file_path: str) -> str:
        """从文件路径中提取案例名称"""
        import re

        # 获取文件名（不包含路径）
        filename = os.path.basename(file_path)

        # 移除文件扩展名
        # 处理 .nii.gz, .nii, .mha, .mhd 等格式
        if filename.endswith('.nii.gz'):
            base_name = filename[:-7]
        elif filename.endswith('.nii'):
            base_name = filename[:-4]
        elif filename.endswith('.mha'):
            base_name = filename[:-4]
        elif filename.endswith('.mhd'):
            base_name = filename[:-4]
        else:
            # 移除最后一个点及其后的内容
            base_name = filename.rsplit('.', 1)[0]

        # 移除常见的后缀（如 -ap, -mask, _mask, -label, _label 等）
        # 这里可以根据您的数据命名规则进行调整
        suffixes_to_remove = ['-ap', '-mask', '_mask', '-label', '_label', '-seg', '_seg']
        for suffix in suffixes_to_remove:
            if base_name.endswith(suffix):
                base_name = base_name[:-len(suffix)]
                break

        # 清理文件名，只保留字母、数字、下划线和连字符
        clean_name = re.sub(r'[^a-zA-Z0-9_-]', '_', base_name)

        # 确保名称不为空且不以数字开头
        if not clean_name or clean_name[0].isdigit():
            clean_name = f"case_{clean_name}"

        return clean_name

    def _copy_and_rename_files(self, image_files: List[str], mask_files: List[str],
                              images_tr_dir: str, labels_tr_dir: str) -> List[str]:
        """复制并重命名文件为nnUNet格式，使用原始文件名，返回案例名称列表"""
        # 创建文件名映射，确保唯一性
        case_names = []
        name_counts = {}

        for image_file in image_files:
            # 从图像文件名提取案例名称
            case_name = self._extract_case_name_from_filename(image_file)

            # 处理重复名称
            if case_name in name_counts:
                name_counts[case_name] += 1
                unique_case_name = f"{case_name}_{name_counts[case_name]:02d}"
            else:
                name_counts[case_name] = 0
                unique_case_name = case_name

            case_names.append(unique_case_name)

        # 复制和重命名文件
        for i, (image_file, case_name) in enumerate(zip(image_files, case_names)):
            # 生成nnUNet格式的文件名，使用原始文件名而不是数字编号
            case_id = f"{self.config.task_name}_{case_name}"

            # 复制图像文件
            image_dst = os.path.join(images_tr_dir, f"{case_id}_0000.nii.gz")
            shutil.copy2(image_file, image_dst)
            self.logger.info(f"复制图像: {os.path.basename(image_file)} -> {os.path.basename(image_dst)}")

            # 复制标签文件（如果存在）
            if i < len(mask_files):
                label_dst = os.path.join(labels_tr_dir, f"{case_id}.nii.gz")
                shutil.copy2(mask_files[i], label_dst)
                self.logger.info(f"复制标签: {os.path.basename(mask_files[i])} -> {os.path.basename(label_dst)}")

            if (i + 1) % 10 == 0:
                self.logger.info(f"已处理 {i + 1}/{len(image_files)} 个文件")

        # 保存文件名映射信息
        self._save_filename_mapping(image_files, case_names)

        # 返回案例名称列表
        return case_names

    def _save_filename_mapping(self, image_files: List[str], case_names: List[str]):
        """保存原始文件名到案例名称的映射"""
        mapping = {}
        for image_file, case_name in zip(image_files, case_names):
            original_name = os.path.basename(image_file)
            final_case_id = f"{self.config.task_name}_{case_name}"
            mapping[original_name] = final_case_id

        # 保存映射到任务目录
        task_dir = os.path.join(self.config.nnformer_raw_data_base, "nnFormer_raw_data", self.config.task_name)
        mapping_file = os.path.join(task_dir, "filename_mapping.json")

        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, indent=2, ensure_ascii=False)

        self.logger.info(f"文件名映射已保存到: {mapping_file}")
        self.logger.info("映射示例:")
        for orig, new in list(mapping.items())[:3]:  # 显示前3个映射
            self.logger.info(f"  {orig} -> {new}")
        if len(mapping) > 3:
            self.logger.info(f"  ... 共 {len(mapping)} 个文件映射")

    def _create_dataset_json(self, task_dir: str, case_names: List[str]):
        """创建dataset.json文件，使用实际的案例名称"""
        # 生成训练案例列表
        training_cases = []
        for case_name in case_names:
            case_id = f"{self.config.task_name}_{case_name}"
            training_cases.append({
                "image": f"./imagesTr/{case_id}.nii.gz",  # dataset.json中的图像路径不包含_0000
                "label": f"./labelsTr/{case_id}.nii.gz"   # 标签文件路径不包含_0000
            })

        dataset_json = {
            "channel_names": self.config.channel_names,
            "labels": self.config.labels,
            "modality": self.config.channel_names,  # 添加modality字段
            "numTraining": len(case_names),
            "numTest": 0,
            "training": training_cases,
            "test": []
        }

        json_path = os.path.join(task_dir, "dataset.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_json, f, indent=2, ensure_ascii=False)

        self.logger.info(f"创建dataset.json: {json_path}")

class nnFormerPreprocessor:
    """nnFormer预处理器 - 使用nnUNet预处理方式"""
    def __init__(self, config: nnFormerConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)

    def check_preprocessing_exists(self) -> bool:
        """检查3D预处理数据是否已存在"""
        try:
            # 检查预处理输出目录
            preprocessed_dir = os.path.join(self.config.nnformer_preprocessed, self.config.task_name)

            if not os.path.exists(preprocessed_dir):
                self.logger.info(f"预处理目录不存在: {preprocessed_dir}")
                return False

            # 检查关键文件是否存在（只检查3D相关文件）
            required_files = [
                "dataset.json",
                "nnFormerPlansv2.1_plans_3D.pkl"  # 只检查3D plans文件
            ]

            for file_name in required_files:
                file_path = os.path.join(preprocessed_dir, file_name)
                if not os.path.exists(file_path):
                    self.logger.info(f"缺少3D预处理文件: {file_path}")
                    return False

            # 检查3D预处理数据目录
            stage_dirs = [
                "nnFormerData_plans_v2.1_stage0",
                "nnFormerData_plans_v2.1_stage1"
            ]

            for stage_dir in stage_dirs:
                stage_path = os.path.join(preprocessed_dir, stage_dir)
                if os.path.exists(stage_path):
                    # 检查是否有.npz文件
                    npz_files = [f for f in os.listdir(stage_path) if f.endswith('.npz')]
                    if len(npz_files) > 0:
                        self.logger.info(f"发现3D预处理数据: {stage_path} (包含 {len(npz_files)} 个文件)")
                        return True

            self.logger.info("未发现有效的3D预处理数据")
            return False

        except Exception as e:
            self.logger.error(f"检查3D预处理数据时出错: {e}")
            return False

    def run_preprocessing(self) -> bool:
        """运行3D预处理"""
        try:
            # 首先检查是否已有预处理数据（除非强制重新处理）
            if not self.config.force_reprocess and self.check_preprocessing_exists():
                self.logger.info("发现已存在的3D预处理数据，跳过预处理步骤")
                return True

            self.logger.info("开始nnFormer 3D预处理...")
            self.logger.info("注意: 只进行3D数据预处理，跳过2D预处理")

            # 导入nnFormer模块
            sys.path.insert(0, '/root/autodl-tmp/nnFormer')

            # 运行预处理
            success = self._run_nnformer_preprocessing()

            if success:
                self.logger.info("3D预处理完成")
                return True
            else:
                self.logger.error("3D预处理失败")
                return False

        except Exception as e:
            self.logger.error(f"3D预处理过程中出现错误: {e}")
            return False

    def _run_nnformer_preprocessing(self) -> bool:
        """运行nnFormer 3D预处理"""
        try:
            # 使用subprocess调用nnFormer预处理命令，只进行3D预处理
            cmd = [
                sys.executable, "-m", "nnformer.experiment_planning.nnFormer_plan_and_preprocess",
                "-t", str(self.config.dataset_id),
                "-tf", str(self.config.num_threads),
                "-tl", str(self.config.num_threads),
                "-pl2d", "None"  # 跳过2D预处理
            ]

            if self.config.verify_dataset_integrity:
                cmd.append("--verify_dataset_integrity")

            self.logger.info(f"执行3D预处理命令: {' '.join(cmd)}")
            self.logger.info("预处理配置: 仅3D (跳过2D)")
            self.logger.info("开始预处理，显示总体进度...")

            # 设置环境
            env = os.environ.copy()
            env['PYTHONPATH'] = '/root/autodl-tmp/nnFormer:' + env.get('PYTHONPATH', '')

            # 使用实时输出来显示总体进度
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                bufsize=1,
                universal_newlines=True
            )

            # 跟踪预处理进度
            self._track_preprocessing_progress(process)

            process.wait()

            if process.returncode == 0:
                self.logger.info("3D预处理命令执行成功")
                return True
            else:
                self.logger.error(f"3D预处理命令执行失败，返回码: {process.returncode}")
                return False

        except Exception as e:
            self.logger.error(f"执行3D预处理命令时出错: {e}")
            return False

    def _track_preprocessing_progress(self, process):
        """跟踪预处理进度，只显示总体进度"""
        import re

        # 预处理阶段标识
        stages = {
            "cropping": "数据裁剪",
            "analyzing": "数据分析",
            "planning": "实验规划",
            "preprocessing": "数据预处理"
        }

        current_stage = None
        processed_files = 0
        total_files = 0

        for line in process.stdout:
            line = line.strip()
            if not line:
                continue

            # 检测预处理阶段
            if "cropping" in line.lower() or "crop" in line.lower():
                if current_stage != "cropping":
                    current_stage = "cropping"
                    self.logger.info(f"📋 {stages[current_stage]}阶段开始...")

            elif "analyzing" in line.lower() or "dataset_analyzer" in line.lower():
                if current_stage != "analyzing":
                    current_stage = "analyzing"
                    self.logger.info(f"📊 {stages[current_stage]}阶段开始...")

            elif "planning" in line.lower() or "experiment" in line.lower():
                if current_stage != "planning":
                    current_stage = "planning"
                    self.logger.info(f"📝 {stages[current_stage]}阶段开始...")

            elif "preprocessing" in line.lower() or "run_preprocessing" in line.lower():
                if current_stage != "preprocessing":
                    current_stage = "preprocessing"
                    self.logger.info(f"⚙️ {stages[current_stage]}阶段开始...")

            # 检测文件处理进度（简化显示）
            if "processing case" in line.lower() or "saving:" in line.lower():
                processed_files += 1
                if processed_files % 5 == 0:  # 每5个文件显示一次进度
                    self.logger.info(f"   已处理 {processed_files} 个文件...")

            # 检测总文件数
            file_count_match = re.search(r'(\d+)\s+cases', line)
            if file_count_match and total_files == 0:
                total_files = int(file_count_match.group(1))
                self.logger.info(f"   总共需要处理 {total_files} 个案例")

            # 显示重要信息（错误、警告、完成信息）
            if any(keyword in line.lower() for keyword in ['error', 'warning', 'completed', 'finished', 'done']):
                if 'error' in line.lower():
                    self.logger.error(f"   {line}")
                elif 'warning' in line.lower():
                    self.logger.warning(f"   {line}")
                else:
                    self.logger.info(f"   {line}")

        if processed_files > 0:
            self.logger.info(f"✅ 预处理完成，共处理了 {processed_files} 个文件")

class nnFormerTrainer:
    """nnFormer训练器"""
    def __init__(self, config: nnFormerConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)

    def check_training_exists(self) -> bool:
        """检查是否已有训练好的模型"""
        try:
            # 构建模型输出路径
            model_dir = os.path.join(
                self.config.results_folder,
                "nnFormer",
                self.config.network,
                self.config.task_name,
                f"{self.config.trainer}__nnFormerPlansv2.1",
                f"fold_{self.config.fold}"
            )

            if not os.path.exists(model_dir):
                self.logger.info(f"训练目录不存在: {model_dir}")
                return False

            # 检查关键模型文件
            model_files = [
                "model_best.model",
                "model_best.model.pkl",
                "model_final_checkpoint.model",
                "model_final_checkpoint.model.pkl"
            ]

            existing_models = []
            for model_file in model_files:
                model_path = os.path.join(model_dir, model_file)
                if os.path.exists(model_path):
                    existing_models.append(model_file)

            if len(existing_models) >= 2:  # 至少有一个完整的模型（.model + .pkl）
                self.logger.info(f"发现已训练的模型: {existing_models}")
                return True

            self.logger.info("未发现完整的训练模型")
            return False

        except Exception as e:
            self.logger.error(f"检查训练状态时出错: {e}")
            return False

    def run_training(self) -> bool:
        """运行训练"""
        try:
            # 检查是否已有训练好的模型（除非强制重新处理或继续训练）
            if not self.config.force_reprocess and not self.config.continue_training and self.check_training_exists():
                self.logger.info("发现已训练的模型，跳过训练步骤")
                self.logger.info("如需重新训练，请设置 continue_training=True 或 force_reprocess=True")
                return True

            self.logger.info("开始nnFormer训练...")

            # 导入nnFormer模块
            sys.path.insert(0, '/root/autodl-tmp/nnFormer')

            # 运行训练
            success = self._run_nnformer_training()

            if success:
                self.logger.info("训练完成")
                return True
            else:
                self.logger.error("训练失败")
                return False

        except Exception as e:
            self.logger.error(f"训练过程中出现错误: {e}")
            return False

    def _run_nnformer_training(self) -> bool:
        """运行nnFormer训练"""
        try:
            # 构建训练命令
            cmd = [
                sys.executable, "-m", "nnformer.run.run_training",
                self.config.network,
                self.config.trainer,
                str(self.config.dataset_id),
                str(self.config.fold)
            ]

            # 添加可选参数
            if self.config.continue_training:
                cmd.append("-c")

            if self.config.validation_only:
                cmd.append("-val")

            cmd.extend(["--npz"])  # 保存验证结果

            self.logger.info(f"执行训练命令: {' '.join(cmd)}")

            # 设置环境
            env = os.environ.copy()
            env['PYTHONPATH'] = '/root/autodl-tmp/nnFormer:' + env.get('PYTHONPATH', '')

            # 运行训练（实时输出）
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                     text=True, env=env, bufsize=1, universal_newlines=True)

            # 实时输出日志
            for line in process.stdout:
                self.logger.info(line.strip())

            process.wait()

            if process.returncode == 0:
                self.logger.info("训练命令执行成功")
                return True
            else:
                self.logger.error(f"训练命令执行失败，返回码: {process.returncode}")
                return False

        except Exception as e:
            self.logger.error(f"执行训练命令时出错: {e}")
            return False

class nnFormerPipeline:
    """nnFormer完整流水线"""
    def __init__(self, config: nnFormerConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)

        # 初始化各个组件
        self.data_converter = nnUNetDataConverter(config, logger)
        self.preprocessor = nnFormerPreprocessor(config, logger)
        self.trainer = nnFormerTrainer(config, logger)

    def run_full_pipeline(self) -> bool:
        """运行完整的训练流水线"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("开始nnFormer完整训练流水线")
            self.logger.info("=" * 80)

            # 显示配置信息
            self.logger.info("流水线配置:")
            self.logger.info(f"  强制重新处理: {self.config.force_reprocess}")
            self.logger.info(f"  运行预处理: {self.config.run_preprocessing} (仅3D)")
            self.logger.info(f"  运行训练: {self.config.run_training}")
            self.logger.info(f"  继续训练: {self.config.continue_training}")
            self.logger.info(f"  仅验证: {self.config.validation_only}")

            # 设置环境
            self.config.setup_environment()

            # 1. 数据格式转换
            self.logger.info("-" * 60)
            self.logger.info("步骤 1: 数据格式转换")
            self.logger.info("-" * 60)
            if not self.data_converter.convert_to_nnunet_format():
                self.logger.error("数据格式转换失败")
                return False

            # 2. 数据预处理（仅3D）
            self.logger.info("-" * 60)
            self.logger.info("步骤 2: 数据预处理（仅3D，跳过2D）")
            self.logger.info("-" * 60)
            if self.config.run_preprocessing:
                if not self.preprocessor.run_preprocessing():
                    self.logger.error("数据预处理失败")
                    return False
            else:
                self.logger.info("跳过数据预处理")

            # 3. 模型训练
            self.logger.info("-" * 60)
            self.logger.info("步骤 3: 模型训练")
            self.logger.info("-" * 60)
            if self.config.run_training:
                if not self.trainer.run_training():
                    self.logger.error("模型训练失败")
                    return False
            else:
                self.logger.info("跳过模型训练")

            self.logger.info("=" * 80)
            self.logger.info("nnFormer训练流水线完成！")
            self.logger.info("=" * 80)

            # 显示结果位置
            if self.config.run_training:
                model_dir = os.path.join(
                    self.config.results_folder,
                    "nnFormer",
                    self.config.network,
                    self.config.task_name,
                    f"{self.config.trainer}__nnFormerPlansv2.1",
                    f"fold_{self.config.fold}"
                )
                self.logger.info(f"训练结果保存在: {model_dir}")

            return True

        except Exception as e:
            self.logger.error(f"流水线执行失败: {e}")
            return False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="nnFormer一键训练脚本 - 使用nnUNet预处理 + nnFormer训练",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # 数据路径参数
    parser.add_argument("--image_dir", type=str, default="/root/autodl-tmp/120HCC/image/ap",
                       help="原始图像目录路径")
    parser.add_argument("--mask_dir", type=str, default="/root/autodl-tmp/120HCC/mask/ap",
                       help="原始标签目录路径")
    parser.add_argument("--base_dir", type=str, default="/root/autodl-tmp",
                       help="工作目录基础路径")

    # 数据集参数
    parser.add_argument("--dataset_id", type=int, default=120,
                       help="数据集ID")
    parser.add_argument("--dataset_name", type=str, default="HCC",
                       help="数据集名称")

    # 训练参数
    parser.add_argument("--network", type=str, default="3d_fullres",
                       choices=["2d", "3d_fullres", "3d_lowres", "3d_cascade_fullres"],
                       help="网络配置")
    parser.add_argument("--trainer", type=str, default="nnFormerTrainerV2",
                       help="训练器类名")
    parser.add_argument("--fold", type=int, default=0,
                       help="交叉验证折数")
    parser.add_argument("--max_epochs", type=int, default=100,
                       help="最大训练轮数")

    # 预处理参数
    parser.add_argument("--num_threads", type=int, default=8,
                       help="预处理线程数")
    parser.add_argument("--verify", action="store_true",
                       help="启用数据集完整性验证（默认跳过以避免numpy兼容性问题）")
    parser.add_argument("--no_verify", action="store_true",
                       help="跳过数据集完整性验证（已废弃，现在默认跳过）")

    # 控制选项
    parser.add_argument("--no_preprocessing", action="store_true",
                       help="跳过预处理步骤")
    parser.add_argument("--no_training", action="store_true",
                       help="跳过训练步骤")
    parser.add_argument("--continue_training", action="store_true",
                       help="继续之前的训练")
    parser.add_argument("--validation_only", action="store_true",
                       help="仅运行验证")
    parser.add_argument("--force_reprocess", action="store_true",
                       help="强制重新处理（忽略已有数据）")

    # 数据配置
    parser.add_argument("--channel_names", type=str, default='{"0": "CT"}',
                       help="通道名称配置（JSON格式）")
    parser.add_argument("--labels", type=str, default='{"0": "background", "1": "target"}',
                       help="标签配置（JSON格式）")

    # 日志配置
    parser.add_argument("--log_file", type=str, default=None,
                       help="日志文件路径")

    return parser.parse_args()

def main():
    """主函数"""
    # 设置随机种子
    set_random_seeds(42)

    # 解析命令行参数
    args = parse_arguments()

    # 设置日志
    log_file = args.log_file or os.path.join(args.base_dir, "logs", "nnformer_training.log")
    logger = setup_logging(log_file)

    try:
        # 创建配置
        config = nnFormerConfig(logger)

        # 更新配置
        config.base_dir = args.base_dir
        config.image_dir = args.image_dir
        config.mask_dir = args.mask_dir
        config.dataset_id = args.dataset_id
        config.dataset_name = args.dataset_name
        config.task_name = f"Task{args.dataset_id:03d}_{args.dataset_name}"
        config.network = args.network
        config.trainer = args.trainer
        config.fold = args.fold
        config.max_epochs = args.max_epochs
        config.num_threads = args.num_threads
        # 默认跳过验证以避免numpy兼容性问题，除非明确要求验证
        config.verify_dataset_integrity = args.verify if hasattr(args, 'verify') and args.verify else False
        config.run_preprocessing = not args.no_preprocessing
        config.run_training = not args.no_training
        config.continue_training = args.continue_training
        config.validation_only = args.validation_only
        config.force_reprocess = args.force_reprocess

        # 解析JSON配置
        try:
            config.channel_names = json.loads(args.channel_names)
            config.labels = json.loads(args.labels)
        except json.JSONDecodeError as e:
            logger.error(f"JSON配置解析失败: {e}")
            return False

        # 更新环境变量路径
        config.nnformer_raw_data_base = os.path.join(config.base_dir, "nnFormer_data")
        config.nnformer_preprocessed = os.path.join(config.base_dir, "nnFormer_preprocessed")
        config.results_folder = os.path.join(config.base_dir, "nnFormer_results")

        # 验证输入路径
        if not os.path.exists(config.image_dir):
            logger.error(f"图像目录不存在: {config.image_dir}")
            return False

        if not os.path.exists(config.mask_dir):
            logger.error(f"标签目录不存在: {config.mask_dir}")
            return False

        # 打印配置信息
        logger.info("配置信息:")
        logger.info(f"  图像目录: {config.image_dir}")
        logger.info(f"  标签目录: {config.mask_dir}")
        logger.info(f"  工作目录: {config.base_dir}")
        logger.info(f"  数据集: {config.task_name}")
        logger.info(f"  网络: {config.network}")
        logger.info(f"  训练器: {config.trainer}")
        logger.info(f"  折数: {config.fold}")
        logger.info(f"  线程数: {config.num_threads}")

        # 创建并运行流水线
        pipeline = nnFormerPipeline(config, logger)
        success = pipeline.run_full_pipeline()

        if success:
            logger.info("训练流水线执行成功！")
            return True
        else:
            logger.error("训练流水线执行失败！")
            return False

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# python nnformer模型训练最终版.py --dataset_id 100 --dataset_name HCC --image_dir /root/autodl-tmp/HCC_100/images --mask_dir /root/autodl-tmp/HCC_100/masks