name: nnFormer
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - ca-certificates=2021.5.25=h06a4308_1
  - certifi=2021.5.30=py36h06a4308_0
  - ld_impl_linux-64=2.33.1=h53a641e_7
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.1.0=hdf63c60_0
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - ncurses=6.2=he6710b0_1
  - openssl=1.1.1k=h27cfd23_0
  - pip=21.1.1=py36h06a4308_0
  - python=3.6.13=hdb3f193_0
  - readline=8.1=h27cfd23_0
  - setuptools=52.0.0=py36h06a4308_0
  - sqlite=3.35.4=hdfb4753_0
  - tk=8.6.10=hbc83047_0
  - wheel=0.36.2=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zlib=1.2.11=h7b6447c_3
  - pip:
    - argparse==1.4.0
    - batchgenerators==0.21
    - bertviz==1.1.0
    - boto3==1.18.1
    - botocore==1.21.1
    - click==8.0.1
    - cycler==0.10.0
    - dataclasses==0.8
    - decorator==4.4.2
    - dicom2nifti==2.3.0
    - diffdist==0.1
    - dnn-printer==0.0.2
    - einops==0.3.0
    - filelock==3.0.12
    - future==0.18.2
    - huggingface-hub==0.0.12
    - imageio==2.9.0
    - importlib-metadata==4.6.1
    - jmespath==0.10.0
    - joblib==1.0.1
    - kiwisolver==1.3.1
    - linecache2==1.0.0
    - matplotlib==3.3.4
    - medpy==0.4.0
    - networkx==2.5.1
    - nibabel==3.2.1
    - numpy==1.19.5
    - packaging==20.9
    - pandas==1.1.5
    - pillow==8.2.0
    - protobuf==3.17.3
    - pydicom==2.1.2
    - pyparsing==2.4.7
    - python-dateutil==2.8.1
    - pytz==2021.1
    - pywavelets==1.1.1
    - pyyaml==5.4.1
    - regex==2021.7.6
    - s3transfer==0.5.0
    - sacremoses==0.0.45
    - scikit-image==0.17.2
    - scikit-learn==0.24.2
    - scipy==1.5.4
    - sentencepiece==0.1.96
    - simpleitk==2.0.2
    - six==1.16.0
    - sklearn==0.0
    - tensorboardx==2.3
    - threadpoolctl==2.1.0
    - tifffile==2020.9.3
    - timm==0.4.9
    - tokenizers==0.10.3
    - torch==1.8.1
    - torchsummary==1.5.1
    - torchvision==0.9.1
    - tqdm==4.61.0
    - traceback2==1.4.0
    - transformers==4.8.2
    - typing-extensions==********
    - unittest2==1.1.0
    - yacs==0.1.8
    - zipp==3.5.0

