:mod:`gtda.local_homology`: Persistent local homology
=====================================================

.. automodule:: gtda.local_homology
   :no-members:
   :no-inherited-members:

Local simplicial homology
-------------------------
.. currentmodule:: gtda

.. autosummary::
   :toctree: generated/local_homology/
   :template: class.rst

   local_homology.KNeighborsLocalVietorisRips
   local_homology.RadiusLocalVietorisRips
