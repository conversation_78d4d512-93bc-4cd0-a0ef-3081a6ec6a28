{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Image generator functions\n", "def image_generator_2D(x1, x2, y1, y2):\n", "    image = np.zeros([100, 100], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    return image\n", "\n", "\n", "def image_generator_3D(x1, x2, y1, y2, z1, z2):\n", "    image = np.zeros([10, 10, 10], np.float32)\n", "    image[z1:z2, y1:y2, x1:x2] = 1\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Groupwise Registration Test (3D registration, 2D+time)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'np' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Create a vector of images for a 2D+time example in numpy\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m array_of_images \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mzeros([\u001b[38;5;241m6\u001b[39m, \u001b[38;5;241m100\u001b[39m, \u001b[38;5;241m100\u001b[39m], np\u001b[38;5;241m.\u001b[39mfloat32)\n\u001b[0;32m      3\u001b[0m i \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m30\u001b[39m, \u001b[38;5;241m5\u001b[39m):\n", "\u001b[1;31mNameError\u001b[0m: name 'np' is not defined"]}], "source": ["# Create a vector of images for a 2D+time example in numpy\n", "array_of_images = np.zeros([6, 100, 100], np.float32)\n", "i = 0\n", "for x in range(0, 30, 5):\n", "    image = image_generator_2D(x, x+50, x, x+50)\n", "    array_of_images[i] = image\n", "    i += 1\n", "\n", "# Convert numpy array to itk 3D image\n", "image_itk_3D = itk.image_view_from_array(array_of_images)\n", "\n", "# Create Groupwise Parameter Object\n", "parameter_object = itk.ParameterObject.New()\n", "groupwise_parameter_map = parameter_object.GetDefaultParameterMap('groupwise',4)\n", "groupwise_parameter_map['FinalBSplineInterpolationOrder'] = ['0']\n", "groupwise_parameter_map['Transform'] = ['EulerStackTransform']\n", "groupwise_parameter_map['AutomaticScalesEstimation'] = ['true']\n", "groupwise_parameter_map['AutomaticScalesEstimationStackTransform'] = ['true']\n", "\n", "parameter_object.AddParameterMap(groupwise_parameter_map)\n", "\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    image_itk_3D, image_itk_3D,\n", "    parameter_object,\n", "    log_to_console=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Groupwise Registration Test Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["%matplotlib inline\n", "result_image_np = np.asarray(result_image).astype(np.float32)\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(2,3, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0,0].imshow(array_of_images[0])\n", "axs[0,0].set_title('Image0', fontsize=30)\n", "axs[0,1].imshow(array_of_images[1])\n", "axs[0,1].set_title('Image1', fontsize=30)\n", "axs[0,2].imshow(array_of_images[2])\n", "axs[0,2].set_title('Image2', fontsize=30)\n", "axs[1,0].imshow(array_of_images[3])\n", "axs[1,0].set_title('Image3', fontsize=30)\n", "axs[1,1].imshow(array_of_images[4])\n", "axs[1,1].set_title('Image4', fontsize=30)\n", "axs[1,2].imshow(result_image_np[5])\n", "axs[1,2].set_title('Result', fontsize=30)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Groupwise Registration Test (4D registration, 3D+time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a vector of images for a 3D+time example in numpy\n", "array_of_images = np.zeros([6, 10, 10, 10], np.float32)\n", "i = 0\n", "for x in range(0, 30, 5):\n", "    image = image_generator_3D(x, x+50, x, x+50, x, x+50)\n", "    array_of_images[i] = image\n", "    i += 1\n", "\n", "# Convert numpy array to itk 4D image\n", "image_itk_4D = itk.image_view_from_array(array_of_images)\n", "\n", "# Create Groupwise Parameter Object\n", "parameter_object = itk.ParameterObject.New()\n", "groupwise_parameter_map = parameter_object.GetDefaultParameterMap('groupwise',2)\n", "groupwise_parameter_map['FinalBSplineInterpolationOrder'] = ['0']\n", "groupwise_parameter_map['Transform'] = ['EulerStackTransform']\n", "groupwise_parameter_map['AutomaticScalesEstimation'] = ['true']\n", "groupwise_parameter_map['AutomaticScalesEstimationStackTransform'] = ['true']\n", "\n", "parameter_object.AddParameterMap(groupwise_parameter_map)\n", "\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    image_itk_4D, image_itk_4D,\n", "    parameter_object,\n", "    log_to_console=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}