###### SSL COMPARISONS ######

# ModSimCLR feature extraction
CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/test_fmcib/task1/train_features.csv" &
CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/test_fmcib/task1/val_features.csv" &
CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/test_fmcib/task1/test_features.csv" &

# SimCLR feature extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr/task1/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr/task1/val_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr/task1/test_features.csv" &

# SimCLR linear eval
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/simclr_features.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr_0.5/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/simclr_features_50_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr_0.2/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/simclr_features_20_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/simclr_0.1/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/simclr_features_10_percent.csv &

# SwAV feature extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/swav/task1/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/swav/task1/val_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/swav/task1/test_features.csv" &

# NNCLR feature extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/nnclr/task1/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/nnclr/task1/val_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/nnclr/task1/test_features.csv" &

# Autoencoder feature extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/autoencoder/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/autoencoder/task1/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/autoencoder/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/autoencoder/task1/val_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/autoencoder/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/autoencoder/task1/test_features.csv" &


# Pretraining data features extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_8k/task1/test_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_8k/last.ckpt" &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_8k/task1/train_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_8k/last.ckpt" &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_8k/task1/val_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_8k/last.ckpt" &

# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_5k/task1/train_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_5k/last.ckpt" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_5k/task1/test_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_5k/last.ckpt" &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_5k/task1/val_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_5k/last.ckpt" &

# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_3k/task1/train_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_3k/last.ckpt" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_3k/task1/test_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_3k/last.ckpt" &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/pretraining/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/pretrain_3k/task1/val_features.csv"  --system#model#weights_path="/mnt/data1/RadiomicsFoundationModel/checkpoints/simclr_pretrain_3k/last.ckpt" &

###### SOTA COMPARISONS ######
# Task 1 feature extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task1/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task1/val_features.csv" & 
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/deeplesion/annotations/task1_test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task1/test_features.csv" & 


## Task 2 feature extraction
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/luna16/luna16/train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task2/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/luna16/luna16/val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task2/val_features.csv" & 
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/luna16/luna16/test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task2/test_features.csv" & 

# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/luna16/luna16/test.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task2/test_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/luna16/luna16/train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task2/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/luna16/luna16/val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task2/val_features.csv" &

#### Task 1 linear eval
# cd ./experiments/linear_evaluation
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis_0.5/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features_50_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis_0.2/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features_20_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis_0.1/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features_10_percent.csv &

# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_features.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d_0.5/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_features_50_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d_0.2/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_features_20_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d_0.1/task1 Coarse_lesion_type --trials 300 --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_features_10_percent.csv &

#### Task 2 linear eval
# cd ./experiments/linear_evaluation
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis_0.5/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features_50_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis_0.2/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features_20_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis_0.1/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_features_10_percent.csv &

# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_features.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d_0.5/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_features_50_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d_0.2/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_features_20_percent.csv &
# python run.py /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d_0.1/task2 malignancy --trials 300 --scoring roc_auc --csv /home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_features_10_percent.csv &


#### Task 3 feature extractor
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/deepprognosis/train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task3/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/deepprognosis/val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task3/val_features.csv" & 
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/lung1/annotations.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task3/lung1_features.csv" & 
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/radio/nsclc_radiogenomics.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/med3d/task3/radio_features.csv" & 

# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/deepprognosis/train.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task3/train_features.csv" &
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/deepprognosis/val.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task3/val_features.csv" & 
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/lung1/annotations.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task3/lung1_features.csv" & 
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/extract_features.yaml --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/radio/nsclc_radiogenomics.csv" --trainer#callbacks#0#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/features/models_genesis/task3/radio_features.csv" & 



# # ModelsGenesis
# ### Task 1 fine-tuned
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/modelsgen_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_task1_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/modelsgen_finetuned_10_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_0.1_task1_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/modelsgen_finetuned_20_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_0.2_task1_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=3 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/modelsgen_finetuned_50_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_0.5_task1_finetune/last.ckpt &

# # #### Task 2 fine-tuned
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_task2_finetune/last-v1.ckpt &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_finetuned_10_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_0.1_task2_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_finetuned_20_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_0.2_task2_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=3 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/modelsgen_finetuned_50_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_0.5_task2_finetune/last.ckpt &

# # Task 3 fine-tuned
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task3.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task3/LUNG1/modelsgen_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_task3_finetune/last-v1.ckpt &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task3.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task3/RADIO/modelsgen_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_task3_finetune/last-v1.ckpt --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/radio/nsclc_radiogenomics.csv" &


# # Med3D
# #### Task 1 fine-tuned
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_task1_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_finetuned_10_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_0.1_task1_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_finetuned_20_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_0.2_task1_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=3 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task1.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task1/med3d_finetuned_50_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_0.5_task1_finetune/last.ckpt &

# # #### Task 2 fine-tuned
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_task2_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_finetuned_10_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_0.1_task2_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=2 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_finetuned_20_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_0.2_task2_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=3 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task2.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task2/med3d_finetuned_50_percent.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_0.5_task2_finetune/last.ckpt &

# # Task 3 fine-tuned
# CUDA_VISIBLE_DEVICES=0 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task3.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task3/LUNG1/med3d_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_task3_finetune/last.ckpt &
# CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task3.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task3/RADIO/med3d_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_task3_finetune/last.ckpt --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/preprocessing/radio/nsclc_radiogenomics.csv" &
CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/med3d/supervised_training/get_predictions_task3.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task3/HarvardRT/med3d_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/med3d_task3_finetune/last.ckpt --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/deepprognosis/val.csv" &
CUDA_VISIBLE_DEVICES=1 lighter predict --config_file ./experiments/sota_comparisons/models_genesis/supervised_training/get_predictions_task3.yaml --trainer#callbacks#0#path=/home/<USER>/Repositories/foundation-cancer-image-biomarker/outputs/predictions/task3/HarvardRT/modelsgen_finetuned.csv --system#model#weights_path=/mnt/data1/RadiomicsFoundationModel/checkpoints/modelsgen_task3_finetune/last-v1.ckpt --system#predict_dataset#path="/home/<USER>/Repositories/foundation-cancer-image-biomarker/data/csvs/deepprognosis/val.csv" &
