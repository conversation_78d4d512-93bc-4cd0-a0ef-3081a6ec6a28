<!--
Thanks for contributing a pull request! Please ensure you have taken a look at
the guidelines for contributing: https://giotto-ai.github.io/gtda-docs/latest/contributing/#guidelines
-->

**Reference issues/PRs**
<!--
Example: Fixes #1234. See also #3456.
Please use keywords (e.g., Fixes) to create link to the issues or pull requests
you resolved, so that they will automatically be closed when your pull request
is merged. See https://github.com/blog/1506-closing-issues-via-pull-requests.

Pull requests which are not related to open issues may be rejected!
If suggesting a new feature or change, please discuss it in an issue first.
If fixing a bug, there should be an issue describing it with steps to reproduce.
-->

**Types of changes**
<!--
What types of changes does your code introduce? Put an `x` in all the boxes that apply.
-->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)

**Description**
<!--
Describe your changes in detail.
-->

**Screenshots (if appropriate)**

**Any other comments?**

**Checklist**
<!--
Go over all the following points, and put an `x` in all the boxes that apply. 
If you're unsure about any of these, don't hesitate to ask. We're here to help!
-->
- [ ] I have read the [guidelines for contributing](https://giotto-ai.github.io/gtda-docs/latest/contributing/#guidelines).
- [ ] My code follows the code style of this project. I used `flake8` to check my Python changes.
- [ ] My change requires a change to the documentation.
- [ ] I have updated the documentation accordingly.
- [ ] I have added tests to cover my changes.
- [ ] All new and existing tests passed. I used `pytest` to check this on Python tests.

<!--
We value all user contributions, no matter how minor they are. If we are slow to
review, either the pull request needs some benchmarking, tinkering,
convincing, etc. or more likely the reviewers are simply busy. In either
case, we ask for your understanding during the review process.

Thanks for contributing!
-->
