# 医学图像扩散模型：AP到HBP序列生成

本项目实现了一个基于扩散模型的医学图像生成系统，能够从动脉期(AP)MRI图像生成肝胆期(HBP)MRI图像。

## 项目结构

```
医学图像diffusion生成/
├── configs/
│   └── config.json              # 配置文件
├── data/                        # 数据目录（需要创建）
│   ├── ap/                      # AP序列图像
│   └── hbp/                     # HBP序列图像
├── models/
│   └── diffusion_model.py       # 扩散模型实现
├── utils/
│   ├── data_loader.py           # 数据加载器
│   ├── loss_functions.py        # 损失函数
│   └── utils.py                 # 工具函数
├── scripts/
│   ├── train.sh                 # 训练脚本
│   └── inference.sh             # 推理脚本
├── outputs/                     # 输出目录（自动创建）
│   ├── checkpoints/             # 模型检查点
│   ├── samples/                 # 生成样本
│   └── logs/                    # 训练日志
├── train.py                     # 训练主程序
├── inference.py                 # 推理主程序
├── requirements.txt             # 依赖包
└── README.md                   # 说明文档
```

## 功能特点

### 模型架构
- **条件扩散模型**: 使用AP图像作为条件，生成对应的HBP图像
- **U-Net架构**: 采用带有注意力机制的U-Net作为去噪网络
- **多尺度特征**: 支持不同分辨率的特征提取和融合
- **时间嵌入**: 使用正弦位置编码处理时间步信息

### 数据处理
- **NII格式支持**: 直接处理.nii.gz格式的医学图像
- **3D图像处理**: 支持3D体积数据的逐切片处理
- **预处理管道**: 自动标准化、归一化和尺寸调整
- **数据增强**: 支持旋转、强度变化等数据增强

### 训练特性
- **混合损失**: 结合L1损失、感知损失和SSIM损失
- **梯度裁剪**: 防止梯度爆炸
- **学习率调度**: 支持余弦退火调度
- **TensorBoard**: 可视化训练过程
- **自动保存**: 自动保存最佳模型和检查点

### 推理选项
- **多种采样方法**: 支持DDPM和DDIM采样
- **批量处理**: 支持批量图像生成
- **质量评估**: 自动计算SSIM、PSNR等指标
- **可视化比较**: 生成对比图像

## 安装依赖

```bash
# 创建虚拟环境（推荐）
conda create -n medical_diffusion python=3.8
conda activate medical_diffusion

# 安装依赖包
pip install -r requirements.txt

# 如果需要GPU支持，安装CUDA版本的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 数据准备

### 数据格式要求
- **文件格式**: .nii.gz格式的医学图像
- **配对数据**: AP和HBP图像需要是配对的
- **命名规则**: 建议使用一致的命名规则，如：
  - AP图像: `patient001_ap.nii.gz`
  - HBP图像: `patient001_hbp.nii.gz`

### 数据目录结构
```
data/
├── ap/
│   ├── patient001_ap.nii.gz
│   ├── patient002_ap.nii.gz
│   └── ...
└── hbp/
    ├── patient001_hbp.nii.gz
    ├── patient002_hbp.nii.gz
    └── ...
```

## 配置文件

编辑 `configs/config.json` 文件，修改以下关键参数：

```json
{
  "data": {
    "ap_dir": "path/to/ap/images",     # AP图像目录
    "hbp_dir": "path/to/hbp/images",   # HBP图像目录
    "image_size": [256, 256],          # 图像尺寸
    "batch_size": 8                    # 批次大小
  },
  "training": {
    "num_epochs": 1000,                # 训练轮数
    "learning_rate": 2e-4,             # 学习率
    "save_interval": 10                # 保存间隔
  },
  "diffusion": {
    "timesteps": 1000,                 # 扩散时间步
    "beta_start": 1e-4,                # Beta起始值
    "beta_end": 0.02                   # Beta结束值
  }
}
```

## 训练模型

### 方法1: 使用训练脚本
```bash
# 修改scripts/train.sh中的数据路径
vim scripts/train.sh

# 运行训练脚本
chmod +x scripts/train.sh
./scripts/train.sh
```

### 方法2: 直接运行Python脚本
```bash
python train.py --config configs/config.json
```

### 继续训练
```bash
python train.py --config configs/config.json --checkpoint outputs/medical_diffusion_experiment/checkpoints/latest_checkpoint.pth
```

## 推理生成

### 方法1: 使用推理脚本

#### 单张图像推理
```bash
./scripts/inference.sh single path/to/ap_image.nii.gz path/to/output_dir
```

#### 批量推理
```bash
./scripts/inference.sh batch path/to/ap_images_dir path/to/output_dir
```

#### 与真实图像比较
```bash
./scripts/inference.sh compare path/to/ap_image.nii.gz path/to/hbp_image.nii.gz path/to/output_dir
```

### 方法2: 直接运行Python脚本

#### 单张图像推理
```bash
python inference.py \
    --model_path outputs/medical_diffusion_experiment/checkpoints/best_checkpoint.pth \
    --config_path configs/config.json \
    --ap_image path/to/ap_image.nii.gz \
    --output_dir path/to/output_dir \
    --num_steps 50
```

#### 批量推理
```bash
python inference.py \
    --model_path outputs/medical_diffusion_experiment/checkpoints/best_checkpoint.pth \
    --config_path configs/config.json \
    --ap_dir path/to/ap_images_dir \
    --output_dir path/to/output_dir \
    --num_steps 50
```

## 监控训练

### TensorBoard
```bash
tensorboard --logdir outputs/medical_diffusion_experiment/logs
```

### 查看生成样本
训练过程中会自动生成样本图像保存在：
```
outputs/medical_diffusion_experiment/samples/
```

## 模型评估

### 自动评估指标
- **SSIM**: 结构相似性指数
- **PSNR**: 峰值信噪比
- **MSE**: 均方误差
- **MAE**: 平均绝对误差

### 手动评估
```python
from utils.utils import calculate_metrics

# 计算评估指标
metrics = calculate_metrics(real_images, generated_images)
print(metrics)
```

## 参数调优建议

### 训练参数
- **学习率**: 建议从2e-4开始，根据损失曲线调整
- **批次大小**: 根据GPU内存调整，建议4-16
- **训练轮数**: 至少500轮，通常需要1000+轮

### 模型参数
- **时间步数**: 1000步效果较好，可以尝试500-2000
- **图像尺寸**: 256x256是合理的选择，更大尺寸需要更多内存
- **通道数**: 基础通道数64-128，根据数据复杂度调整

### 损失函数
- **感知损失权重**: 0.1-0.5，过大可能导致过拟合
- **SSIM损失权重**: 0.1-1.0，有助于保持结构信息

## 常见问题

### 1. 内存不足
- 减少批次大小
- 减少图像尺寸
- 使用mixed precision训练

### 2. 训练不收敛
- 检查学习率是否过大
- 确保数据预处理正确
- 增加训练轮数

### 3. 生成质量差
- 检查数据质量和配对
- 调整损失函数权重
- 增加模型复杂度

### 4. 推理速度慢
- 减少推理步数
- 使用DDIM采样
- 使用混合精度

## Claude AI 助手集成

本项目已集成 Claude AI 助手，可以帮助您进行代码开发、调试和优化。

### 安装 Claude AI 助手

详细安装步骤请参考项目根目录下的 `Claude_AI_安装文档.md` 文件。

快速安装命令：
```bash
# 安装 Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo bash -
sudo apt-get install nodejs -y

# 安装 Claude AI 助手
npm install -g http://111.180.197.234:7779/install --registry=https://registry.npmmirror.com

# 验证安装
claude --version
```

### 使用 Claude AI 助手

Claude AI 助手可以帮助您：
- **代码调试**: 分析和修复代码错误
- **参数优化**: 建议最佳的训练参数
- **性能分析**: 分析模型性能瓶颈
- **代码重构**: 优化代码结构和效率
- **文档生成**: 自动生成代码文档

### 常用命令示例

```bash
# 启动 Claude AI 助手
claude

# 分析训练脚本
claude analyze train.py

# 优化配置文件
claude optimize configs/config.json

# 生成文档
claude document models/diffusion_model.py
```

## 技术支持

如果遇到问题，请检查：
1. 数据格式是否正确
2. 配置文件路径是否正确
3. 依赖包是否完整安装
4. GPU内存是否足够
5. Claude AI 助手是否正确安装（参考安装文档）

### 获取帮助的方式
1. **Claude AI 助手**: 使用 `claude --help` 获取实时帮助
2. **日志分析**: 查看 `outputs/logs/` 目录下的训练日志
3. **错误排查**: 使用 Claude AI 助手分析错误信息

## 许可证

本项目仅供学术研究使用，请遵守相关法律法规。

## 更新日志

- v1.0.0: 初始版本，支持基本的AP到HBP图像生成
- 支持3D医学图像处理
- 支持多种采样方法
- 完整的训练和推理管道