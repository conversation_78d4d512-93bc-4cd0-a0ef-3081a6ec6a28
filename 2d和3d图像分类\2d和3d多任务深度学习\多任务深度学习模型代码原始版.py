#!/usr/bin/env python
# coding: utf-8
#%% 2dCNN-Vit 多任务学习代码,成功
import torch
print(torch.__version__)
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
get_ipython().run_line_magic('matplotlib', 'inline')
import torchvision
import glob
from torchvision import transforms
from torch.utils import data
from PIL import Image
import torchvision.models as models
import timm
import os
import pandas as pd

#%%
def get_file_list(directory):
    file_list = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.jpg'):
                file_list.append(os.path.join(root, file))
    file_list.sort()  # 按照文件名排序
    return file_list

def get_label_list(file_path):   
    label_file = pd.read_excel(file_path)    
    name_values = label_file['name'].tolist()# 从'name'和'VETC'列中获取数值
    label_values1 = label_file['MTM'].tolist()   
    label_values2 = label_file['VETC'].tolist()
    sorted_label_values1 = [label for _, label in sorted(zip(name_values, label_values1))] # 根据'name'列对数值进行排序 
    sorted_label_values2 = [label for _, label in sorted(zip(name_values, label_values2))]
    label_list1 = sorted_label_values1  # 将排序后的VETC数值存储在label_list中
    label_list2 = sorted_label_values2     
    return label_list1, label_list2

train_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\train"
test_dir = r"K:\734HCC\all-HCC\578hcc\tumor\ap\jpg_maxslice\jpg_maxslice\test"
train_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\train30.xlsx"
test_file_path = r"K:\734HCC\all-HCC\578hcc\clinical data\data\test.xlsx"

train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5]);# 打印标签列表
print(len(train_image_list));print(len(test_image_list))

# 调用函数获取标签列表
train_label_list1 = get_label_list(train_file_path)[0]
train_label_list2 = get_label_list(train_file_path)[1]
test_label_list1 = get_label_list(test_file_path)[0]
test_label_list2 = get_label_list(test_file_path)[1]
print(train_label_list1[:5]);print(train_label_list2[:5])
print(len(train_label_list1));print(len(test_label_list1))

# In[14]:

transforms = transforms.Compose([
    transforms.Resize((96, 96)),
    transforms.ToTensor(),
])

# In[15]:
class Multi_output_dataset(data.Dataset):
    def __init__(self, imgs_path, label1, label2):
        self.imgs_path = imgs_path
        self.label1 = label1
        self.label2 = label2

    def __getitem__(self, index):
        img_path = self.imgs_path[index]
        pil_img = Image.open(img_path)
        pil_img = pil_img.convert("RGB")
        pil_img = transforms(pil_img)
        label_1 = self.label1[index]
        label_2 = self.label2[index]
        return pil_img, (label_1, label_2)
    
    def __len__(self):
        return len(self.imgs_path)

# In[16]:
train_dataset = Multi_output_dataset(train_image_list, train_label_list1, train_label_list2)
test_dataset = Multi_output_dataset(test_image_list, test_label_list1, test_label_list2)
print(len(train_dataset), len(test_dataset))

# In[18]:

BTACH_SIZE = 16

# In[19]:

train_dl = torch.utils.data.DataLoader(train_dataset,batch_size=BTACH_SIZE,shuffle=True,num_workers=0, pin_memory=True)

test_dl = torch.utils.data.DataLoader(test_dataset,batch_size=BTACH_SIZE,shuffle=False, num_workers=0, pin_memory=True)

imgs, labels = next(iter(train_dl))

# In[22]:

imgs.shape

labels

# In[24]:
im = imgs[0].permute(1, 2, 0).numpy()

labels[0][0]
labels[1][0]

plt.imshow(im)

# In[28]:

# class Net(nn.Module):
#     def __init__(self, num_classes1, num_classes2):
#         super(Net, self).__init__()
#         resnet50 = models.resnet50(pretrained=True)
#         # Remove the final fully connected layer of ResNet50
#         self.features = nn.Sequential(*list(resnet50.children())[:-1])
#         # Add custom fully connected layers
#         self.fc1 = nn.Linear(2048, num_classes1)
#         self.fc2 = nn.Linear(2048, num_classes2)

#     def forward(self, x):
#         x = self.features(x)
#         x = x.view(x.size(0), -1)
#         x1 = self.fc1(x)
#         x2 = self.fc2(x)
#         return x1, x2

# # Device and model instantiation
# device = "cuda" if torch.cuda.is_available() else "cpu"
# print("Using {} device".format(device))

# model = Net(num_classes1=3, num_classes2=4).to(device)

# #%% torchvision系列模型
class CustomModel(nn.Module):
    def __init__(self, model, num_classes1, num_classes2):
        super(CustomModel, self).__init__()
        self.model = model
        self.remove_final_fc_layer()
        in_features = self.get_input_features()
        self.fc1 = nn.Linear(in_features, num_classes1)
        self.fc2 = nn.Linear(in_features, num_classes2)
    def forward(self, x):
        x = self.model(x)
        x = x.view(x.size(0), -1)
        x1 = self.fc1(x)
        x2 = self.fc2(x)
        return x1, x2
    def remove_final_fc_layer(self):
        # Remove the final fully connected layer of the model
        if hasattr(self.model, 'fc'):
            self.model = nn.Sequential(*list(self.model.children())[:-1])
    def get_input_features(self):
        # Function to dynamically determine the number of input features
        with torch.no_grad():
            x = torch.randn(1, 3, 224, 224)  # Assumes input size 3x224x224, modify if necessary
            features = self.model(x)
            return features.view(features.size(0), -1).size(1)

# Device and model instantiation
device = "cuda" if torch.cuda.is_available() else "cpu"
print("Using {} device".format(device))

pretrained_model = models.resnet50(pretrained=True)
# pretrained_model = models.densenet121(pretrained=True)
# pretrained_model = models.swin_v2_b(pretrained=True)
# pretrained_model = models.vit_b_32(pretrained=True)#vit还有vit_b_32，vit_l_16，vit_l_32
# pretrained_model = models.vgg19 (pretrained=True)
# pretrained_model = models.efficientnet_b1(pretrained=True)

model = CustomModel(pretrained_model, num_classes1=2, num_classes2=2).to(device)

print(model)

#%%timm系列模型
# import timm
# from pprint import pprint
# model_names = timm.list_models(pretrained=True)
# pprint(model_names)

# #用于学术资源加速
# import subprocess
# import os
# result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
# output = result.stdout
# for line in output.splitlines():
#     if '=' in line:
#         var, value = line.split('=', 1)
#         os.environ[var] = value
# # unset http_proxy && unset https_proxy #取消学术加速

# pretrained_model = timm.create_model('convit_base', pretrained=True)
# # pretrained_model = timm.create_model('deit_base_distilled_patch16_224', pretrained=True)
# # pretrained_model = timm.create_model('twins_svt_small', pretrained=True)
# print(pretrained_model)

# # pretrained_model = timm.create_model('convit_base', pretrained=False)
# # checkpoint = torch.hub.load_state_dict_from_url(
# #     url="https://dl.fbaipublicfiles.com/convit/convit_base.pth",
# #     map_location="cpu", check_hash=True
# # )
# # pretrained_model.load_state_dict(checkpoint)

# class CustomModel(nn.Module):
#     def __init__(self, model, num_classes1, num_classes2):
#         super(CustomModel, self).__init__()
#         self.model = model
#         in_features = self.model.head.in_features
#         self.remove_final_fc_layer()        
#         self.fc1 = nn.Linear(in_features, num_classes1)
#         self.fc2 = nn.Linear(in_features, num_classes2)
    
#     def forward(self, x):
#         x = self.model(x)        
#         x = x.view(x.size(0), -1)
#         x1 = self.fc1(x)
#         x2 = self.fc2(x)
#         return x1, x2
    
#     def remove_final_fc_layer(self):
#         # Remove the final fully connected layer of the model
#         self.model.head = nn.Identity()

# # Device and model instantiation
# device = "cuda" if torch.cuda.is_available() else "cpu"
# print("Using {} device".format(device))

# model = CustomModel(pretrained_model, num_classes1=2, num_classes2=2).to(device)
# print(model)

#%% Loss function and optimizer
loss_fn = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)

#%%此处使用训练代码
def train(dataloader, model, loss_fn, optimizer):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    train_loss, correct1, correct2 = 0, 0, 0
    model.train()
    for X, y in dataloader:
        y1 = y[0]
        y2 = y[1]
        X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
        # Compute prediction error
        pred = model(X)
        loss1 = loss_fn(pred[0], y1)
        loss2 = loss_fn(pred[1], y2)
        loss = loss1 + loss2

        # Backpropagation
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        with torch.no_grad():
            correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
            train_loss += loss.item()
    train_loss /= num_batches
    correct1 /= size
    correct2 /= size
    return train_loss, correct1, correct2

# In[35]:测试代码
def test(dataloader, model):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss, correct1, correct2 = 0, 0, 0
    with torch.no_grad():
        for X, y in dataloader:
            y1 = y[0]
            y2 = y[1]
            X, y1, y2 = X.to(device), y1.to(device), y2.to(device)
            pred = model(X)
            loss1 = loss_fn(pred[0], y1)
            loss2 = loss_fn(pred[1], y2)
            loss = loss1 + loss2
            test_loss += loss.item()
            correct1 += (pred[0].argmax(1) == y1).type(torch.float).sum().item()
            correct2 += (pred[1].argmax(1) == y2).type(torch.float).sum().item()
    
    test_loss /= num_batches
    correct1 /= size
    correct2 /= size
    return test_loss, correct1, correct2

# In[36]:
epochs = 30

train_loss = []
train_acc1 = []
train_acc2 = []
test_loss = []
test_acc1 = []
test_acc2 = []

for epoch in range(epochs):
    epoch_loss, epoch_acc1, epoch_acc2 = train(train_dl, model, loss_fn, optimizer)
    epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model)
    train_loss.append(epoch_loss)
    train_acc1.append(epoch_acc1)
    train_acc2.append(epoch_acc2)
    test_loss.append(epoch_test_loss)
    test_acc1.append(epoch_test_acc1)
    test_acc2.append(epoch_test_acc2)
    
    template = ("epoch:{:2d}, train_loss: {:.5f}, train_acc1: {:.1f}% , train_acc2: {:.1f}% ," 
                "test_loss: {:.5f}, test_acc1: {:.1f}%, test_acc2: {:.1f}%")
    print(template.format(
          epoch, epoch_loss, epoch_acc1*100, epoch_acc2*100, 
         epoch_test_loss, epoch_test_acc1*100, epoch_test_acc2*100))
    
print("Done!")

# In[38]:
plt.plot(range(1, epochs+1), train_loss, label='train_loss')
plt.plot(range(1, epochs+1), test_loss, label='test_loss')
plt.legend()

#%%绘制acc曲线
# plt.plot(range(1, len(train_loss)+1), train_acc1, label='train_VETC_acc')
# plt.plot(range(1, len(train_loss)+1), test_acc1 , label='test_VETC_acc')
plt.plot(range(1, len(train_loss)+1), train_acc2 , label='train_immune score_acc')
plt.plot(range(1, len(train_loss)+1), test_acc2 , label='test_immune score_acc')
plt.legend(loc='lower right', fontsize='small', markerscale=0.1) #x-small
plt.title("ConViT") #ResNet50,DenseNet121,Inception_v3,SqueezeNet_v1.0
plt.rcParams['font.size'] = 16
plt.show()

# In[39]:
# epoch_test_loss, epoch_test_acc1, epoch_test_acc2 = test(test_dl, model)
# print(epoch_test_loss, epoch_test_acc1, epoch_test_acc2)

#%%保存模型权重
PATH = '/root/autodl-tmp/data/resnet50.pth'   
torch.save(model.state_dict(), PATH)

#%%保存图片路径、预测概率、真实的label、预测label
import torch
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix

new_train_dl = torch.utils.data.DataLoader(train_dataset, batch_size=16, shuffle=False)

train_image_paths = [train_dataset.dataset.imgs_path[i] for i in train_dataset.indices]
test_image_paths = [test_dataset.dataset.imgs_path[i] for i in test_dataset.indices]

def get_predictions(model, dataloader, imgs_path):
    model.eval()
    image_paths = []
    true_label1 = []
    true_label2 = []
    predict_label1 = []
    predict_label2 = []
    predict_prob1 = []  # 存储预测概率1
    predict_prob2 = []  # 存储预测概率2
    for x, (y1, y2) in dataloader:
        if torch.cuda.is_available():
            x = x.to('cuda')
            y1 = y1.to('cuda')
            y2 = y2.to('cuda')
        y_pred1, y_pred2 = model(x)
        _, y_pred_class1 = torch.max(y_pred1, dim=1)
        _, y_pred_class2 = torch.max(y_pred2, dim=1)
        # 获取预测概率
        y_pred_prob1 = torch.softmax(y_pred1, dim=1)
        y_pred_prob2 = torch.softmax(y_pred2, dim=1)
        # 清空image_paths列表
        image_paths.clear()
        image_paths.extend(imgs_path)
        true_label1.extend(y1.tolist())
        true_label2.extend(y2.tolist())
        predict_label1.extend(y_pred_class1.tolist())
        predict_label2.extend(y_pred_class2.tolist())
        predict_prob1.extend(y_pred_prob1.tolist())
        predict_prob2.extend(y_pred_prob2.tolist())
    return image_paths, true_label1, true_label2, predict_label1, predict_label2, predict_prob1, predict_prob2

train_image_paths, train_true_label1, train_true_label2, train_predict_label1, train_predict_label2, train_predict_prob1, train_predict_prob2 = get_predictions(model, new_train_dl, train_image_paths)
test_image_paths, test_true_label1, test_true_label2, test_predict_label1, test_predict_label2, test_predict_prob1, test_predict_prob2 = get_predictions(model, test_dl, test_image_paths)

train_data = {'Image Path': train_image_paths, 'True Label1': train_true_label1, 'Predict Label1': train_predict_label1, 'True Label2': train_true_label2, 'Predict Label2': train_predict_label2, 'Predict Prob1': train_predict_prob1, 'Predict Prob2': train_predict_prob2}
test_data = {'Image Path': test_image_paths, 'True Label1': test_true_label1, 'Predict Label1': test_predict_label1, 'True Label2': test_true_label2, 'Predict Label2': test_predict_label2, 'Predict Prob1': test_predict_prob1, 'Predict Prob2': test_predict_prob2}

train_results = pd.DataFrame(train_data)
test_results = pd.DataFrame(test_data)

train_results.to_excel('/root/autodl-tmp/data3/train_convit.xlsx', index=False)
test_results.to_excel('/root/autodl-tmp/data3/test_convit.xlsx', index=False)

#%% 3d CNN多任务学习代码
def get_file_list(directory):
    file_list = [os.path.join(directory, file) for file in os.listdir(directory) if file.endswith('.nii.gz')]
    file_list.sort()  # 按照文件名排序
    return file_list

def get_label_list(file_path):   
    label_file = pd.read_excel(file_path)    
    name_values = label_file['name'].tolist()# 从'name'和'VETC'列中获取数值
    label_values1 = label_file['MTM'].tolist()   
    label_values2 = label_file['VETC'].tolist()
    sorted_label_values1 = [label for _, label in sorted(zip(name_values, label_values1))] # 根据'name'列对数值进行排序 
    sorted_label_values2 = [label for _, label in sorted(zip(name_values, label_values2))]
    label_list1 = sorted_label_values1  # 将排序后的VETC数值存储在label_list中
    label_list2 = sorted_label_values2     
    return label_list1, label_list2

train_dir = r'K:\2020-2023HCC\579hcc\578hcc\ap\jpg_maxslice\jpg_maxslice\train'
test_dir = r'K:\2020-2023HCC\579hcc\578hcc\ap\jpg_maxslice\jpg_maxslice\test'
train_file_path = r'K:\2020-2023HCC\579hcc\clinical data\data\train30.xlsx'
test_file_path = r'K:\2020-2023HCC\579hcc\clinical data\data\test.xlsx'

train_image_list = get_file_list(train_dir)
test_image_list = get_file_list(test_dir)
print(train_image_list[:5]);# 打印标签列表
print(len(train_image_list));print(len(test_image_list))

# 调用函数获取标签列表
train_label_list1 = get_label_list(train_file_path)[0]
train_label_list2 = get_label_list(train_file_path)[1]
test_label_list1 = get_label_list(test_file_path)[0]
test_label_list2 = get_label_list(test_file_path)[1]
print(train_label_list1[:5]);print(train_label_list2[:5])
print(len(train_label_list1));print(len(test_label_list1))


