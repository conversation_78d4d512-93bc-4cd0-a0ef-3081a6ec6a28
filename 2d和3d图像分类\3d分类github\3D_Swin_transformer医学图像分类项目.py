# 3D_Swin_transformer_classification医学图像分类项目
# https://github.com/software-ai-life/3D_Swin_transformer_classification
# Introduction
# The model is modified by Swin_UNTER. The model has been augmented with a classification head, 
# which can classify the type you want. Noticed that the model is for 3D medical image. 
# Please modify your dataloader (data_utils.py) to customize the dataset.

# 安装依赖
pip install -r requirements.txt

# 训练
python main.py --batch_size=<batch_size> --logdir=<model_name> --optim_lr=1e-4 --lrschedule=warmup_cosine --roi_x=64 --roi_y=64 --roi_z=64 --val_every 1 --save_checkpoint

python main.py --batch_size=16 --logdir=3Dunet_test --optim_lr=1e-4 --lrschedule=warmup_cosine --roi_x=64 --roi_y=64 --roi_z=64 --val_every 1 --save_checkpoint

# 测试
python evaluation.py


