name: Run subtask tests
on:
  push:
    paths-ignore:
      - '**.md'
jobs:

  run-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{matrix.python-version}}
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python-version}}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest Cython fury
          pip install torch==2.3.0 -f https://download.pytorch.org/whl/cpu
          pip install .

      - name: Run test script
        run: ./tests/tests_subtasks.sh ${{ secrets.TOTALSEGMENTATOR_LICENSE }}
        shell: bash
