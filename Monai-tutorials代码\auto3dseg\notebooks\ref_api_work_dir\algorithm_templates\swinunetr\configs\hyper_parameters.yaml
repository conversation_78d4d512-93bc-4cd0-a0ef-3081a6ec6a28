bundle_root: null
ckpt_path: "$@bundle_root + '/model_fold' + str(@fold)"
data_file_base_dir: null
data_list_file_path: null
fold: 0

transforms:
  resample_resolution: "$@resample_resolution"
  lazy_resampling: false
cache_rate: 0
train_cache_rate: "$@cache_rate"
validate_cache_rate: "$@cache_rate"
show_cache_progress: false

auto_scale_allowed: true
amp: true
input_channels: null
learning_rate: 0.0004
log_output_file: "$@bundle_root + '/model_fold' + str(@fold) + '/training.log'"
mlflow_tracking_uri: "$@bundle_root + '/model_fold' + str(@fold) + '/mlruns/'"
mlflow_experiment_name: "Auto3DSeg"
num_images_per_batch: 2
num_epochs: null
auto_scale_max_epochs: 1000
num_epochs_per_validation: 5
num_crops_per_image: null
num_patches_per_iter: null
num_sw_batch_size: "$@num_patches_per_iter"
num_workers: 8
num_workers_validation: 2
num_cache_workers: 8
output_classes: null
n_cases: null
overlap_ratio: 0.125
overlap_ratio_final: 0.625
roi_size: null
roi_size_valid: null
random_seed: 0
resample_resolution: null
sw_input_on_cpu: false
softmax: true
valid_at_orig_resolution_at_last: true
valid_at_orig_resolution_only: false
use_pretrain: true
pretrained_path: $@bundle_root + '/pretrained_model' + '/swin_unetr.base_5000ep_f48_lr2e-4_pretrained.pt'
adapt_valid_mode: true
adapt_valid_progress_percentages: [10, 40, 70]
adapt_valid_num_epochs_per_validation: [5, 5, 5]

early_stop_mode: true
early_stop_delta: 0
early_stop_patience: 5

loss:
  _target_: DiceCELoss
  include_background: true
  squared_pred: true
  smooth_nr: 0
  smooth_dr: 1.0e-05
  softmax: $@softmax
  sigmoid: $not @softmax
  to_onehot_y: $@softmax

optimizer:
  _target_: torch.optim.AdamW
  lr: "@learning_rate"
  weight_decay: 1.0e-05
lr_scheduler:
  _target_: monai.optimizers.WarmupCosineSchedule
  optimizer: "$@optimizer"
  warmup_steps: $@num_epochs//100
  t_total: '$@num_epochs // @num_epochs_per_validation + 1'
  warmup_multiplier: 0.1

# fine-tuning
finetune:
  activate: false
  pretrained_ckpt_name: "$@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'"

# validation
validate:
  ckpt_name: "$@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'"
  save_mask: true
  log_output_file: "$@bundle_root + '/model_fold' + str(@fold) + '/validation.log'"
  output_path: "$@bundle_root + '/prediction_fold' + str(@fold)"

# inference
infer:
  ckpt_name: "$@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'"
  fast: false
  data_list_key: testing
  log_output_file: "$@bundle_root + '/model_fold' + str(@fold) + '/inference.log'"
  output_path: "$@bundle_root + '/prediction_' + @infer#data_list_key"
