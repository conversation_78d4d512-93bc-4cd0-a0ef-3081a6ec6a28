_meta_: {}
bundle_root: ./ref_api_work_dir\dints_3
ckpt_path: $@bundle_root + '/model_fold' + str(@fold)
data_file_base_dir: C:\Users\<USER>\AppData\Local\Temp\tmp9lauk690\Task04_Hippocampus
data_list_file_path: "f:\\2. python\u6DF1\u5EA6\u5B66\u4E60\u8BFE\u7A0B\\1.\u65E5\u6708\
  \u5149\u534E-pyorch\u6DF1\u5EA6\u5B66\u4E60\u8BFE\u7A0B\\0.\u6DF1\u5EA6\u5B66\u4E60\
  \u548CR\u4EE3\u7801\u603B\u7ED3\\\u6DF1\u5EA6\u5B66\u4E60\u4EE3\u7801\u603B\u7ED3\
  \u6700\u65B0\u7248\\Monai-tutorials\\auto3dseg\\tasks\\msd\\Task04_Hippocampus\\\
  msd_task04_hippocampus_folds.json"
finetune:
  activate_finetune: false
  overwrite:
    adapt_valid_mode: false
    early_stop_mode: false
    learning_rate: 0.001
    lr_scheduler: {_target_: torch.optim.lr_scheduler.ConstantLR, factor: 1.0, optimizer: '@training#optimizer',
      total_iters: $@training#num_epochs // @training#num_epochs_per_validation +
        1}
    num_epochs: 20
    num_epochs_per_validation: 1
  pretrained_ckpt_name: $@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt'
fold: 3
infer: {ckpt_name: $@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt',
  data_list_key: testing, fast: true, log_output_file: $@bundle_root + '/model_fold'
    + str(@fold) + '/inference.log', output_path: $@bundle_root + '/prediction_' +
    @infer#data_list_key, save_prob: false}
mlflow_experiment_name: Auto3DSeg
mlflow_tracking_uri: $@bundle_root + '/model_fold' + str(@fold) + '/mlruns/'
show_cache_progress: false
stats_summary:
  image_stats:
    shape:
      mean: [35.4, 50.666666666666664, 36.06666666666667]
  n_cases: 30
training:
  adapt_valid_mode: true
  adapt_valid_num_epochs_per_validation: [2, 4, 2]
  adapt_valid_progress_percentages: [10, 40, 70]
  amp: true
  auto_scale_allowed: true
  cache_rate: 0
  data_list_key: null
  early_stop_delta: 0
  early_stop_mode: true
  early_stop_patience: 20
  epoch_divided_factor: 36
  input_channels: 1
  learning_rate: 0.2
  log_output_file: $@bundle_root + '/model_fold' + str(@fold) + '/training.log'
  loss: {_target_: DiceFocalLoss, batch: true, include_background: true, sigmoid: $not
      @training#softmax, smooth_dr: 1.0e-05, smooth_nr: 1.0e-05, softmax: '@training#softmax',
    squared_pred: true, to_onehot_y: '@training#softmax'}
  lr_scheduler: {_target_: torch.optim.lr_scheduler.PolynomialLR, optimizer: '@training#optimizer',
    power: 0.5, total_iters: $@training#num_epochs // @training#num_epochs_per_validation
      + 1}
  num_cache_workers: 8
  num_crops_per_image: 1
  num_epochs: 200
  num_epochs_per_validation: 2
  num_images_per_batch: 2
  num_patches_per_iter: 1
  num_sw_batch_size: null
  num_workers: 8
  num_workers_validation: 2
  optimizer: {_target_: torch.optim.SGD, lr: '@training#learning_rate', momentum: 0.9,
    weight_decay: 4.0e-05}
  output_classes: 3
  overlap_ratio: 0.625
  overlap_ratio_train: 0.125
  random_seed: 0
  resample_resolution: [1.0, 1.0, 1.0]
  roi_size: [32, 32, 32]
  roi_size_valid: [32, 32, 32]
  softmax: true
  sw_input_on_cpu: false
  train_cache_rate: '@training#cache_rate'
  transforms: {resample_resolution: '@training#resample_resolution'}
  valid_at_orig_resolution_at_last: true
  valid_at_orig_resolution_only: false
  validate_cache_rate: '@training#cache_rate'
validate: {ckpt_name: $@bundle_root + '/model_fold' + str(@fold) + '/best_metric_model.pt',
  data_list_key: null, log_output_file: $@bundle_root + '/model_fold' + str(@fold)
    + '/validation.log', output_path: $@bundle_root + '/prediction_fold' + str(@fold),
  save_mask: true}
