#!/usr/bin/env python
# coding: utf-8

# ## 6. Groupwise Image Registration

# Groupwise registration methods try to mitigate uncertainties associated with any one image by simultaneously registering all images in a population. This incorporates all image information in registration process and eliminates bias towards a chosen reference frame. The method described here uses a 3D (2D+time) free-form B-spline deformation model and a similarity metric that minimizes variance of intensities under the constraint that the average deformation over images is zero. This constraint defines a true mean frame of reference that lie in the center of the population without having to calculate it explicitly.
# 
# The method can take into account temporal smoothness of the deformations and a cyclic transform in the time dimension. This may be appropriate if it is known a priori that the anatomical motion has a cyclic nature e.g. in cases of cardiac or respiratory motion.

# ### Registration

# In[1]:


import itk


# In[2]:


# Load folder containing images.
images = itk.imread("data/00", itk.F)


# In[3]:


# Create Groupwise Parameter Object
parameter_object = itk.ParameterObject.New()
groupwise_parameter_map = parameter_object.GetDefaultParameterMap('groupwise')
parameter_object.AddParameterMap(groupwise_parameter_map)


# Registration can either be done in one line with the registration function...

# In[ ]:


# Call registration function
# both fixed and moving image should be set with the vector_itk to prevent elastix from throwing errors

result_image, result_transform_parameters = itk.elastix_registration_method(
    images, images,
    parameter_object=parameter_object,
    log_to_console=True)


# .. or by initiating an elastix image filter object.

# In[ ]:


# Load Elastix Image Filter Object
# Fixed and moving image should be given to the Elastix method to ensure that
# the correct 3D class is initialized.
# Both fixed and moving image should be set with the vector_itk to prevent elastix from throwing errors

elastix_object = itk.ElastixRegistrationMethod.New(images, images)
elastix_object.SetParameterObject(parameter_object)

# Set additional options
elastix_object.SetLogToConsole(False)

# Update filter object (required)
elastix_object.UpdateLargestPossibleRegion()

# Results of Registration
result_image = elastix_object.GetOutput()
result_transform_parameters = elastix_object.GetTransformParameterObject()

