#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
细胞核和细胞质分割可视化工具

功能:
1. 对病理图像进行细胞核和细胞质分割
2. 生成分割结果的可视化图像
3. 保存原图、分割掩码、叠加图像等多种格式
4. 支持批量处理

作者: AI Assistant
日期: 2025-01-08
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage import filters, morphology, segmentation
from skimage.filters import threshold_otsu, gaussian
from skimage.morphology import remove_small_objects, disk, binary_dilation
from skimage.segmentation import watershed
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

def find_local_maxima(image, min_distance=1, threshold_abs=None):
    """
    自定义的局部最大值检测函数
    """
    from scipy.ndimage import maximum_filter
    
    # 使用形态学操作找局部最大值
    neighborhood_size = max(3, min_distance * 2 + 1)
    neighborhood = np.ones((neighborhood_size, neighborhood_size))
    local_max = (maximum_filter(image, footprint=neighborhood) == image)
    
    # 应用阈值
    if threshold_abs is not None:
        local_max = local_max & (image >= threshold_abs)
    
    # 确保最小距离
    if min_distance > 1:
        from skimage.morphology import disk, binary_erosion
        struct = disk(min_distance // 2)
        local_max = binary_erosion(local_max, struct)
    
    # 返回坐标列表
    coords = np.where(local_max)
    return list(zip(coords[0], coords[1]))

class CellSegmentationVisualizer:
    """细胞分割可视化器"""
    
    def __init__(self, input_folder, output_folder=None):
        """
        初始化分割可视化器
        
        参数:
        input_folder: 输入图像文件夹路径
        output_folder: 输出结果文件夹路径
        """
        self.input_folder = input_folder
        self.output_folder = output_folder or os.path.join(input_folder, 'segmentation_results')
        
        # 创建输出文件夹
        os.makedirs(self.output_folder, exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'original'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'nuclei_masks'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'cell_masks'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'cytoplasm_masks'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'overlay_images'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'combined_view'), exist_ok=True)
        
        # 支持的图像格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp']
        
        print(f"细胞分割可视化器初始化完成")
        print(f"输入文件夹: {self.input_folder}")
        print(f"输出文件夹: {self.output_folder}")
    
    def preprocess_image(self, image):
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯滤波去噪
        denoised = gaussian(gray, sigma=1.0)
        
        # 对比度增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply((denoised * 255).astype(np.uint8))
        
        # 归一化
        normalized = ((enhanced - enhanced.min()) / (enhanced.max() - enhanced.min()) * 255).astype(np.uint8)
        
        return normalized
    
    def segment_nuclei_and_cells(self, image):
        """
        分割细胞核和细胞 - 优化版本
        返回: nuclei_labels, cell_labels, cytoplasm_labels
        """
        # 1. 多阈值细胞核分割
        thresh_otsu = threshold_otsu(image)

        # 尝试多个阈值策略
        thresholds = [thresh_otsu * 0.6, thresh_otsu * 0.8, thresh_otsu * 1.0]
        best_nuclei_labels = None
        max_nuclei_count = 0

        for thresh in thresholds:
            # 细胞核通常比背景暗
            nuclei_binary = image < thresh

            # 形态学操作
            nuclei_binary = morphology.remove_small_objects(nuclei_binary, min_size=20)
            nuclei_binary = morphology.binary_closing(nuclei_binary, disk(2))
            nuclei_binary = morphology.binary_opening(nuclei_binary, disk(1))

            if np.sum(nuclei_binary) == 0:
                continue

            # 分水岭分割
            distance_nuclei = ndimage.distance_transform_edt(nuclei_binary)
            if distance_nuclei.max() == 0:
                continue

            local_maxima_nuclei = find_local_maxima(
                distance_nuclei, min_distance=5, threshold_abs=0.2*distance_nuclei.max()
            )

            if len(local_maxima_nuclei) == 0:
                continue

            markers_nuclei = np.zeros_like(distance_nuclei, dtype=int)
            markers_nuclei[tuple(zip(*local_maxima_nuclei))] = np.arange(1, len(local_maxima_nuclei) + 1)

            nuclei_labels = watershed(-distance_nuclei, markers_nuclei, mask=nuclei_binary)
            nuclei_count = nuclei_labels.max()

            if nuclei_count > max_nuclei_count:
                max_nuclei_count = nuclei_count
                best_nuclei_labels = nuclei_labels

        # 如果没有找到细胞核，使用简单阈值
        if best_nuclei_labels is None or max_nuclei_count == 0:
            # 使用更宽松的阈值
            nuclei_binary = image < thresh_otsu * 1.1
            nuclei_binary = morphology.remove_small_objects(nuclei_binary, min_size=10)
            nuclei_binary = morphology.binary_closing(nuclei_binary, disk(3))

            # 简单的连通组件标记
            best_nuclei_labels = morphology.label(nuclei_binary)

        # 2. 细胞分割 - 使用更宽松的阈值
        cell_thresholds = [thresh_otsu * 1.3, thresh_otsu * 1.5, thresh_otsu * 1.8]
        best_cell_labels = None
        max_cell_count = 0

        for thresh in cell_thresholds:
            cell_binary = image < thresh

            # 形态学操作
            cell_binary = morphology.remove_small_objects(cell_binary, min_size=50)
            cell_binary = morphology.binary_closing(cell_binary, disk(5))

            if np.sum(cell_binary) == 0:
                continue

            # 使用细胞核作为种子
            if best_nuclei_labels is not None and best_nuclei_labels.max() > 0:
                distance_cells = ndimage.distance_transform_edt(cell_binary)
                markers_cells = best_nuclei_labels.copy()
                cell_labels = watershed(-distance_cells, markers_cells, mask=cell_binary)
            else:
                cell_labels = morphology.label(cell_binary)

            cell_count = cell_labels.max()
            if cell_count > max_cell_count:
                max_cell_count = cell_count
                best_cell_labels = cell_labels

        # 如果没有找到细胞，使用简单方法
        if best_cell_labels is None or max_cell_count == 0:
            cell_binary = image < thresh_otsu * 2.0
            cell_binary = morphology.remove_small_objects(cell_binary, min_size=30)
            best_cell_labels = morphology.label(cell_binary)

        # 3. 细胞质分割
        if best_cell_labels is not None and best_nuclei_labels is not None:
            cytoplasm_labels = best_cell_labels.copy()
            cytoplasm_labels[best_nuclei_labels > 0] = 0
        else:
            cytoplasm_labels = np.zeros_like(image, dtype=int)

        # 确保返回值不为None
        if best_nuclei_labels is None:
            best_nuclei_labels = np.zeros_like(image, dtype=int)
        if best_cell_labels is None:
            best_cell_labels = np.zeros_like(image, dtype=int)

        return best_nuclei_labels, best_cell_labels, cytoplasm_labels
    
    def create_colored_mask(self, labels, colormap='viridis'):
        """创建彩色掩码"""
        if labels.max() == 0:
            return np.zeros((*labels.shape, 3), dtype=np.uint8)
        
        # 归一化标签
        normalized = labels.astype(float) / labels.max()
        
        # 应用颜色映射
        cmap = plt.get_cmap(colormap)
        colored = cmap(normalized)
        
        # 转换为RGB格式
        rgb = (colored[:, :, :3] * 255).astype(np.uint8)
        
        # 背景设为黑色
        rgb[labels == 0] = [0, 0, 0]
        
        return rgb
    
    def create_overlay_image(self, original, nuclei_labels, cell_labels, cytoplasm_labels):
        """创建叠加图像"""
        # 确保原图是RGB格式
        if len(original.shape) == 3:
            overlay = original.copy()
        else:
            overlay = cv2.cvtColor(original, cv2.COLOR_GRAY2RGB)
        
        # 创建掩码
        nuclei_mask = nuclei_labels > 0
        cytoplasm_mask = cytoplasm_labels > 0
        
        # 叠加颜色
        overlay[nuclei_mask] = overlay[nuclei_mask] * 0.6 + np.array([255, 0, 0]) * 0.4  # 红色细胞核
        overlay[cytoplasm_mask] = overlay[cytoplasm_mask] * 0.6 + np.array([0, 255, 0]) * 0.4  # 绿色细胞质
        
        return overlay.astype(np.uint8)
    
    def create_combined_view(self, original, nuclei_labels, cell_labels, cytoplasm_labels, image_name):
        """创建组合视图"""
        # 创建2x3的子图
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 原图
        if len(original.shape) == 3:
            axes[0, 0].imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
        else:
            axes[0, 0].imshow(original, cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # 细胞核掩码
        nuclei_colored = self.create_colored_mask(nuclei_labels, 'Reds')
        axes[0, 1].imshow(nuclei_colored)
        axes[0, 1].set_title(f'Nuclei Segmentation ({nuclei_labels.max()} nuclei)')
        axes[0, 1].axis('off')
        
        # 细胞掩码
        cell_colored = self.create_colored_mask(cell_labels, 'Blues')
        axes[0, 2].imshow(cell_colored)
        axes[0, 2].set_title(f'Cell Segmentation ({cell_labels.max()} cells)')
        axes[0, 2].axis('off')
        
        # 细胞质掩码
        cytoplasm_colored = self.create_colored_mask(cytoplasm_labels, 'Greens')
        axes[1, 0].imshow(cytoplasm_colored)
        axes[1, 0].set_title('Cytoplasm Segmentation')
        axes[1, 0].axis('off')
        
        # 叠加图像
        overlay = self.create_overlay_image(original, nuclei_labels, cell_labels, cytoplasm_labels)
        axes[1, 1].imshow(overlay)
        axes[1, 1].set_title('Overlay (Red: Nuclei, Green: Cytoplasm)')
        axes[1, 1].axis('off')
        
        # 轮廓图
        if len(original.shape) == 3:
            contour_img = cv2.cvtColor(original, cv2.COLOR_BGR2RGB).copy()
        else:
            contour_img = cv2.cvtColor(original, cv2.COLOR_GRAY2RGB)
        
        # 绘制轮廓
        nuclei_contours = cv2.findContours((nuclei_labels > 0).astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
        cell_contours = cv2.findContours((cell_labels > 0).astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
        
        cv2.drawContours(contour_img, nuclei_contours, -1, (255, 0, 0), 2)  # 红色细胞核轮廓
        cv2.drawContours(contour_img, cell_contours, -1, (0, 255, 0), 1)   # 绿色细胞轮廓
        
        axes[1, 2].imshow(contour_img)
        axes[1, 2].set_title('Contours (Red: Nuclei, Green: Cells)')
        axes[1, 2].axis('off')
        
        plt.suptitle(f'Cell Segmentation Analysis - {image_name}', fontsize=16)
        plt.tight_layout()
        
        return fig
    
    def process_single_image(self, image_path):
        """处理单张图像"""
        try:
            print(f"处理图像: {os.path.basename(image_path)}")
            
            # 读取图像
            original = cv2.imread(image_path)
            if original is None:
                print(f"无法读取图像: {image_path}")
                return False
            
            # 预处理
            processed_image = self.preprocess_image(original)
            
            # 分割
            nuclei_labels, cell_labels, cytoplasm_labels = self.segment_nuclei_and_cells(processed_image)
            
            # 获取文件名（不含扩展名）
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            
            # 保存原图
            cv2.imwrite(os.path.join(self.output_folder, 'original', f'{image_name}_original.jpg'), original)
            
            # 保存掩码
            cv2.imwrite(os.path.join(self.output_folder, 'nuclei_masks', f'{image_name}_nuclei_mask.png'), 
                       (nuclei_labels > 0).astype(np.uint8) * 255)
            cv2.imwrite(os.path.join(self.output_folder, 'cell_masks', f'{image_name}_cell_mask.png'), 
                       (cell_labels > 0).astype(np.uint8) * 255)
            cv2.imwrite(os.path.join(self.output_folder, 'cytoplasm_masks', f'{image_name}_cytoplasm_mask.png'), 
                       (cytoplasm_labels > 0).astype(np.uint8) * 255)
            
            # 保存彩色掩码
            nuclei_colored = self.create_colored_mask(nuclei_labels, 'Reds')
            cell_colored = self.create_colored_mask(cell_labels, 'Blues')
            cytoplasm_colored = self.create_colored_mask(cytoplasm_labels, 'Greens')
            
            cv2.imwrite(os.path.join(self.output_folder, 'nuclei_masks', f'{image_name}_nuclei_colored.jpg'), 
                       cv2.cvtColor(nuclei_colored, cv2.COLOR_RGB2BGR))
            cv2.imwrite(os.path.join(self.output_folder, 'cell_masks', f'{image_name}_cell_colored.jpg'), 
                       cv2.cvtColor(cell_colored, cv2.COLOR_RGB2BGR))
            cv2.imwrite(os.path.join(self.output_folder, 'cytoplasm_masks', f'{image_name}_cytoplasm_colored.jpg'), 
                       cv2.cvtColor(cytoplasm_colored, cv2.COLOR_RGB2BGR))
            
            # 保存叠加图像
            overlay = self.create_overlay_image(original, nuclei_labels, cell_labels, cytoplasm_labels)
            cv2.imwrite(os.path.join(self.output_folder, 'overlay_images', f'{image_name}_overlay.jpg'), overlay)
            
            # 保存组合视图
            fig = self.create_combined_view(original, nuclei_labels, cell_labels, cytoplasm_labels, image_name)
            fig.savefig(os.path.join(self.output_folder, 'combined_view', f'{image_name}_combined.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            print(f"  ✅ 成功分割并保存: 细胞核 {nuclei_labels.max()} 个, 细胞 {cell_labels.max()} 个")
            return True
            
        except Exception as e:
            print(f"  ❌ 处理失败: {str(e)}")
            return False

    def batch_process(self):
        """批量处理所有图像"""
        print("🔬 开始批量细胞分割可视化...")

        # 获取所有图像文件，排除结果文件夹
        image_files = []
        for root, dirs, files in os.walk(self.input_folder):
            # 跳过结果文件夹
            if 'segmentation_results' in root or 'enhanced_pathomics_results' in root:
                continue
            for file in files:
                if any(file.lower().endswith(ext) for ext in self.supported_formats):
                    image_files.append(os.path.join(root, file))

        if not image_files:
            print(f"❌ 在 {self.input_folder} 中未找到支持的图像文件")
            return

        print(f"📁 找到 {len(image_files)} 个图像文件")

        # 处理每个图像
        success_count = 0
        for i, image_path in enumerate(image_files, 1):
            print(f"进度: {i}/{len(image_files)}")
            if self.process_single_image(image_path):
                success_count += 1

        print(f"\n🎉 批量处理完成!")
        print(f"✅ 成功处理: {success_count}/{len(image_files)} 个图像")
        print(f"📁 结果保存在: {self.output_folder}")

        # 显示输出文件夹结构
        self.show_output_structure()

    def show_output_structure(self):
        """显示输出文件夹结构"""
        print("\n📋 输出文件夹结构:")
        print(f"{self.output_folder}/")
        print("├── original/              # 原始图像")
        print("├── nuclei_masks/          # 细胞核掩码 (黑白 + 彩色)")
        print("├── cell_masks/            # 细胞掩码 (黑白 + 彩色)")
        print("├── cytoplasm_masks/       # 细胞质掩码 (黑白 + 彩色)")
        print("├── overlay_images/        # 叠加图像 (红色细胞核 + 绿色细胞质)")
        print("└── combined_view/         # 组合视图 (6合1分析图)")


def main():
    """主函数 - 一键运行细胞分割可视化"""
    print("=" * 70)
    print("🔬 细胞核和细胞质分割可视化工具")
    print("基于CellProfiler算法的细胞分割和可视化")
    print("=" * 70)

    # 直接设置图像路径
    input_folder = r"K:\肝脏MRI数据集\HCC病理HE图\ceshi"
    print(f"📁 图像文件夹: {input_folder}")

    # 检查输入路径是否存在
    if not os.path.exists(input_folder):
        print(f"❌ 错误: 输入路径 {input_folder} 不存在")
        print("请检查路径是否正确")
        return

    # 创建分割可视化器
    visualizer = CellSegmentationVisualizer(input_folder)

    # 执行批量处理
    visualizer.batch_process()

    print("\n" + "=" * 70)
    print("🎉 细胞分割可视化完成！")
    print("📊 生成的文件类型:")
    print("  • 原始图像备份")
    print("  • 细胞核分割掩码 (黑白 + 彩色)")
    print("  • 细胞分割掩码 (黑白 + 彩色)")
    print("  • 细胞质分割掩码 (黑白 + 彩色)")
    print("  • 叠加可视化图像")
    print("  • 6合1组合分析图")
    print("=" * 70)


if __name__ == "__main__":
    main()
