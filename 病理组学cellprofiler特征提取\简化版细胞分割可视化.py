#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版细胞核和细胞质分割可视化工具

功能:
1. 简化的细胞核和细胞质分割算法
2. 生成分割结果的可视化图像
3. 保存多种格式的分割结果
4. 快速批量处理

作者: AI Assistant
日期: 2025-01-08
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage import filters, morphology
from skimage.filters import threshold_otsu, gaussian
from skimage.morphology import remove_small_objects, disk, label
import warnings
warnings.filterwarnings('ignore')

class SimpleCellSegmentation:
    """简化版细胞分割器"""
    
    def __init__(self, input_folder, output_folder=None):
        self.input_folder = input_folder
        self.output_folder = output_folder or os.path.join(input_folder, 'simple_segmentation_results')
        
        # 创建输出文件夹
        os.makedirs(self.output_folder, exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'segmentation_overlay'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'nuclei_masks'), exist_ok=True)
        os.makedirs(os.path.join(self.output_folder, 'cell_masks'), exist_ok=True)
        
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp']
        
        print(f"简化版细胞分割器初始化完成")
        print(f"输入文件夹: {self.input_folder}")
        print(f"输出文件夹: {self.output_folder}")
    
    def preprocess_image(self, image):
        """简化的图像预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯滤波
        denoised = gaussian(gray, sigma=1.0)
        
        # 归一化
        normalized = ((denoised - denoised.min()) / (denoised.max() - denoised.min()) * 255).astype(np.uint8)
        
        return normalized
    
    def simple_nuclei_segmentation(self, image):
        """简化的细胞核分割"""
        try:
            # 使用Otsu阈值
            thresh = threshold_otsu(image)
            
            # 细胞核通常比背景暗
            binary = image < thresh * 0.8
            
            # 形态学操作
            binary = remove_small_objects(binary, min_size=50)
            binary = morphology.binary_closing(binary, disk(2))
            binary = morphology.binary_opening(binary, disk(1))
            
            # 标记连通组件
            labels = label(binary)
            
            return labels, binary
            
        except Exception as e:
            print(f"细胞核分割出错: {e}")
            return np.zeros_like(image, dtype=int), np.zeros_like(image, dtype=bool)
    
    def simple_cell_segmentation(self, image, nuclei_labels):
        """简化的细胞分割"""
        try:
            # 使用更宽松的阈值
            thresh = threshold_otsu(image)
            binary = image < thresh * 1.5
            
            # 形态学操作
            binary = remove_small_objects(binary, min_size=100)
            binary = morphology.binary_closing(binary, disk(5))
            
            # 标记连通组件
            labels = label(binary)
            
            return labels, binary
            
        except Exception as e:
            print(f"细胞分割出错: {e}")
            return np.zeros_like(image, dtype=int), np.zeros_like(image, dtype=bool)
    
    def create_overlay_visualization(self, original, nuclei_binary, cell_binary):
        """创建叠加可视化"""
        # 确保原图是RGB格式
        if len(original.shape) == 3:
            overlay = original.copy()
        else:
            overlay = cv2.cvtColor(original, cv2.COLOR_GRAY2RGB)
        
        # 叠加颜色
        overlay[nuclei_binary] = overlay[nuclei_binary] * 0.5 + np.array([255, 0, 0]) * 0.5  # 红色细胞核
        
        # 细胞质 = 细胞 - 细胞核
        cytoplasm_binary = cell_binary & ~nuclei_binary
        overlay[cytoplasm_binary] = overlay[cytoplasm_binary] * 0.5 + np.array([0, 255, 0]) * 0.5  # 绿色细胞质
        
        return overlay.astype(np.uint8)
    
    def create_combined_view(self, original, nuclei_labels, nuclei_binary, cell_labels, cell_binary, image_name):
        """创建组合视图"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 原图
        if len(original.shape) == 3:
            axes[0, 0].imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
        else:
            axes[0, 0].imshow(original, cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # 细胞核二值图
        axes[0, 1].imshow(nuclei_binary, cmap='Reds')
        axes[0, 1].set_title(f'Nuclei Binary ({nuclei_labels.max()} nuclei)')
        axes[0, 1].axis('off')
        
        # 细胞二值图
        axes[0, 2].imshow(cell_binary, cmap='Blues')
        axes[0, 2].set_title(f'Cell Binary ({cell_labels.max()} cells)')
        axes[0, 2].axis('off')
        
        # 细胞核标签
        axes[1, 0].imshow(nuclei_labels, cmap='viridis')
        axes[1, 0].set_title('Nuclei Labels')
        axes[1, 0].axis('off')
        
        # 细胞标签
        axes[1, 1].imshow(cell_labels, cmap='plasma')
        axes[1, 1].set_title('Cell Labels')
        axes[1, 1].axis('off')
        
        # 叠加图
        overlay = self.create_overlay_visualization(original, nuclei_binary, cell_binary)
        axes[1, 2].imshow(overlay)
        axes[1, 2].set_title('Overlay (Red: Nuclei, Green: Cytoplasm)')
        axes[1, 2].axis('off')
        
        plt.suptitle(f'Simple Cell Segmentation - {image_name}', fontsize=16)
        plt.tight_layout()
        
        return fig
    
    def process_single_image(self, image_path):
        """处理单张图像"""
        try:
            print(f"处理图像: {os.path.basename(image_path)}")
            
            # 读取图像
            original = cv2.imread(image_path)
            if original is None:
                print(f"  ❌ 无法读取图像")
                return False
            
            # 预处理
            processed = self.preprocess_image(original)
            
            # 分割
            nuclei_labels, nuclei_binary = self.simple_nuclei_segmentation(processed)
            cell_labels, cell_binary = self.simple_cell_segmentation(processed, nuclei_labels)
            
            # 获取文件名
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            
            # 保存掩码
            cv2.imwrite(os.path.join(self.output_folder, 'nuclei_masks', f'{image_name}_nuclei.png'), 
                       nuclei_binary.astype(np.uint8) * 255)
            cv2.imwrite(os.path.join(self.output_folder, 'cell_masks', f'{image_name}_cells.png'), 
                       cell_binary.astype(np.uint8) * 255)
            
            # 保存叠加图
            overlay = self.create_overlay_visualization(original, nuclei_binary, cell_binary)
            cv2.imwrite(os.path.join(self.output_folder, 'segmentation_overlay', f'{image_name}_overlay.jpg'), overlay)
            
            # 保存组合视图
            fig = self.create_combined_view(original, nuclei_labels, nuclei_binary, 
                                          cell_labels, cell_binary, image_name)
            fig.savefig(os.path.join(self.output_folder, f'{image_name}_analysis.png'), 
                       dpi=200, bbox_inches='tight')
            plt.close(fig)
            
            nuclei_count = nuclei_labels.max()
            cell_count = cell_labels.max()
            print(f"  ✅ 成功: 细胞核 {nuclei_count} 个, 细胞 {cell_count} 个")
            return True
            
        except Exception as e:
            print(f"  ❌ 处理失败: {str(e)}")
            return False
    
    def batch_process(self):
        """批量处理"""
        print("🔬 开始简化版细胞分割...")
        
        # 获取图像文件
        image_files = []
        for root, _, files in os.walk(self.input_folder):
            if 'segmentation_results' in root or 'enhanced_pathomics_results' in root:
                continue
            for file in files:
                if any(file.lower().endswith(ext) for ext in self.supported_formats):
                    image_files.append(os.path.join(root, file))
        
        if not image_files:
            print(f"❌ 未找到图像文件")
            return
        
        print(f"📁 找到 {len(image_files)} 个图像文件")
        
        success_count = 0
        for i, image_path in enumerate(image_files, 1):
            print(f"进度: {i}/{len(image_files)}")
            if self.process_single_image(image_path):
                success_count += 1
        
        print(f"\n🎉 处理完成!")
        print(f"✅ 成功: {success_count}/{len(image_files)} 个图像")
        print(f"📁 结果保存在: {self.output_folder}")


def main():
    """主函数"""
    print("=" * 60)
    print("🔬 简化版细胞分割可视化工具")
    print("=" * 60)
    
    # 设置路径
    input_folder = r"K:\肝脏MRI数据集\HCC病理HE图\ceshi"
    print(f"📁 图像文件夹: {input_folder}")
    
    if not os.path.exists(input_folder):
        print(f"❌ 路径不存在: {input_folder}")
        return
    
    # 创建分割器
    segmenter = SimpleCellSegmentation(input_folder)
    
    # 执行处理
    segmenter.batch_process()
    
    print("\n" + "=" * 60)
    print("🎉 简化版细胞分割完成！")
    print("📊 生成文件:")
    print("  • 细胞核掩码 (nuclei_masks/)")
    print("  • 细胞掩码 (cell_masks/)")
    print("  • 叠加可视化 (segmentation_overlay/)")
    print("  • 分析图表 (*_analysis.png)")
    print("=" * 60)


if __name__ == "__main__":
    main()
