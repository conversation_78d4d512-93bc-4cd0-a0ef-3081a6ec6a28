import torch
import torch.nn.functional as F
import numpy as np
import nibabel as nib
import argparse
import json
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt

from models.diffusion_model import ConditionalUNet, DiffusionModel
from utils.utils import load_nii_image, save_nii_image, preprocess_medical_image, tensor_to_numpy
from utils.data_loader import MedicalImageDataset

class DiffusionInference:
    def __init__(self, model_path, config_path, device='cuda'):
        self.device = torch.device(device)
        
        # 加载配置
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        # 创建模型
        self.model = self._create_model()
        
        # 加载模型权重
        self.load_model(model_path)
        
        self.model.eval()
        
    def _create_model(self):
        """创建模型"""
        model_config = self.config['model']
        diffusion_config = self.config['diffusion']
        
        unet = ConditionalUNet(
            in_channels=model_config['in_channels'],
            out_channels=model_config['out_channels'],
            time_emb_dim=model_config['time_emb_dim'],
            base_channels=model_config['base_channels'],
            channel_multipliers=model_config['channel_multipliers']
        )
        
        diffusion = DiffusionModel(
            model=unet,
            timesteps=diffusion_config['timesteps'],
            beta_start=diffusion_config['beta_start'],
            beta_end=diffusion_config['beta_end'],
            device=self.device
        )
        
        return diffusion.to(self.device)
    
    def load_model(self, model_path):
        """加载模型权重"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
        
        print(f"模型加载成功: {model_path}")
    
    def preprocess_image(self, image_path):
        """预处理输入图像"""
        # 加载图像
        image_data, affine = load_nii_image(image_path)
        
        # 预处理
        processed_image = preprocess_medical_image(image_data, target_range=(0, 1))
        
        return processed_image, affine
    
    def generate_single_image(self, ap_image_path, output_path, num_inference_steps=50):
        """生成单张图像"""
        # 预处理输入图像
        ap_image, affine = self.preprocess_image(ap_image_path)
        
        # 处理3D图像
        if len(ap_image.shape) == 3:
            # 对于3D图像，逐切片处理
            generated_volume = np.zeros_like(ap_image)
            
            for slice_idx in tqdm(range(ap_image.shape[2]), desc="生成切片"):
                ap_slice = ap_image[:, :, slice_idx]
                generated_slice = self._generate_slice(ap_slice, num_inference_steps)
                generated_volume[:, :, slice_idx] = generated_slice
            
            # 保存生成的体积
            save_nii_image(generated_volume, output_path, affine)
            
        else:
            # 2D图像直接处理
            generated_image = self._generate_slice(ap_image, num_inference_steps)
            save_nii_image(generated_image, output_path, affine)
        
        print(f"生成完成: {output_path}")
    
    def _generate_slice(self, ap_slice, num_inference_steps):
        """生成单个切片"""
        # 调整图像大小
        target_size = self.config['data']['image_size']
        
        # 转换为tensor
        ap_tensor = torch.from_numpy(ap_slice).float()
        
        # 调整大小
        if ap_tensor.shape != tuple(target_size):
            ap_tensor = ap_tensor.unsqueeze(0).unsqueeze(0)
            ap_tensor = F.interpolate(ap_tensor, size=target_size, mode='bilinear', align_corners=False)
            ap_tensor = ap_tensor.squeeze(0).squeeze(0)
        
        # 添加batch和channel维度
        ap_tensor = ap_tensor.unsqueeze(0).unsqueeze(0).to(self.device)
        
        # 生成
        with torch.no_grad():
            generated_tensor = self.ddpm_sample(ap_tensor, num_inference_steps)
        
        # 转换回numpy
        generated_slice = tensor_to_numpy(generated_tensor.squeeze(0))
        
        # 调整回原始大小
        if generated_slice.shape != ap_slice.shape:
            generated_tensor = torch.from_numpy(generated_slice).float().unsqueeze(0).unsqueeze(0)
            generated_tensor = F.interpolate(generated_tensor, size=ap_slice.shape, mode='bilinear', align_corners=False)
            generated_slice = generated_tensor.squeeze(0).squeeze(0).numpy()
        
        return generated_slice
    
    def ddpm_sample(self, condition, num_inference_steps=50):
        """DDPM采样"""
        # 从纯噪声开始
        shape = condition.shape
        x = torch.randn(shape, device=self.device)

        # 计算采样时间步
        timesteps = torch.linspace(
            self.model.timesteps - 1, 0, num_inference_steps,
            dtype=torch.long, device=self.device
        )

        for i, t in enumerate(tqdm(timesteps, desc="采样步骤", leave=False)):
            t_batch = t.repeat(shape[0])

            # 预测噪声
            with torch.no_grad():
                predicted_noise = self.model(x, t_batch, condition=condition)

            # 计算去噪参数
            alpha_t = self.model.alphas[t]
            alpha_cumprod_t = self.model.alphas_cumprod[t]
            beta_t = self.model.betas[t]

            # 计算前一个时间步的alpha_cumprod
            if i < len(timesteps) - 1:
                alpha_cumprod_prev = self.model.alphas_cumprod[timesteps[i + 1]]
            else:
                alpha_cumprod_prev = torch.tensor(1.0, device=self.device)

            # 计算x_0的预测
            pred_x0 = (x - torch.sqrt(1 - alpha_cumprod_t) * predicted_noise) / torch.sqrt(alpha_cumprod_t)

            # 限制x_0的范围
            pred_x0 = torch.clamp(pred_x0, -1, 1)

            # 计算x_{t-1}
            if t > 0:
                # 计算后验方差
                posterior_variance = beta_t * (1 - alpha_cumprod_prev) / (1 - alpha_cumprod_t)

                # 计算均值
                pred_mean = torch.sqrt(alpha_cumprod_prev) * beta_t / (1 - alpha_cumprod_t) * pred_x0 + \
                           torch.sqrt(alpha_t) * (1 - alpha_cumprod_prev) / (1 - alpha_cumprod_t) * x

                # 添加噪声
                noise = torch.randn_like(x)
                x = pred_mean + torch.sqrt(posterior_variance) * noise
            else:
                # 最后一步，直接使用预测的x_0
                x = pred_x0

        return x
    
    def ddim_sample(self, condition, num_inference_steps=50, eta=0.0):
        """DDIM采样"""
        # 从纯噪声开始
        shape = condition.shape
        x = torch.randn(shape, device=self.device)
        
        # 计算采样时间步
        timesteps = torch.linspace(
            self.model.timesteps - 1, 0, num_inference_steps, 
            dtype=torch.long, device=self.device
        )
        
        for i, t in enumerate(tqdm(timesteps, desc="DDIM采样", leave=False)):
            t_batch = t.repeat(shape[0])
            
            # 预测噪声
            with torch.no_grad():
                predicted_noise = self.model(x, t_batch, condition=condition)
            
            # 计算前一个时间步
            prev_t = timesteps[i + 1] if i + 1 < len(timesteps) else torch.tensor(0)
            
            # DDIM更新
            alpha_t = self.model.alphas_cumprod[t]
            alpha_prev = self.model.alphas_cumprod[prev_t]
            
            pred_x0 = (x - torch.sqrt(1 - alpha_t) * predicted_noise) / torch.sqrt(alpha_t)
            
            # 方向向量
            direction = torch.sqrt(1 - alpha_prev) * predicted_noise
            
            # 添加随机性
            if eta > 0:
                sigma = eta * torch.sqrt((1 - alpha_prev) / (1 - alpha_t)) * torch.sqrt(1 - alpha_t / alpha_prev)
                noise = torch.randn_like(x)
                direction = direction + sigma * noise
            
            x = torch.sqrt(alpha_prev) * pred_x0 + direction
        
        return x
    
    def batch_inference(self, ap_dir, output_dir, num_inference_steps=50, sample_method='ddpm'):
        """批量推理"""
        ap_dir = Path(ap_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取所有AP图像文件
        ap_files = sorted(ap_dir.glob("*.nii.gz"))
        
        if not ap_files:
            print(f"在 {ap_dir} 中没有找到 .nii.gz 文件")
            return
        
        print(f"开始批量推理，共 {len(ap_files)} 个文件")
        
        for ap_file in tqdm(ap_files, desc="批量推理"):
            output_file = output_dir / f"generated_{ap_file.stem}.nii.gz"
            
            try:
                self.generate_single_image(ap_file, output_file, num_inference_steps)
            except Exception as e:
                print(f"处理 {ap_file} 时出错: {e}")
        
        print(f"批量推理完成，结果保存到 {output_dir}")
    
    def compare_with_ground_truth(self, ap_image_path, hbp_image_path, output_path, num_inference_steps=50):
        """与真实图像比较"""
        # 生成图像
        ap_image, affine = self.preprocess_image(ap_image_path)
        hbp_image, _ = self.preprocess_image(hbp_image_path)
        
        # 选择中间切片进行比较
        if len(ap_image.shape) == 3:
            mid_slice = ap_image.shape[2] // 2
            ap_slice = ap_image[:, :, mid_slice]
            hbp_slice = hbp_image[:, :, mid_slice]
        else:
            ap_slice = ap_image
            hbp_slice = hbp_image
        
        # 生成图像
        generated_slice = self._generate_slice(ap_slice, num_inference_steps)
        
        # 可视化比较
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        axes[0].imshow(ap_slice, cmap='gray')
        axes[0].set_title('AP Input')
        axes[0].axis('off')
        
        axes[1].imshow(hbp_slice, cmap='gray')
        axes[1].set_title('HBP Ground Truth')
        axes[1].axis('off')
        
        axes[2].imshow(generated_slice, cmap='gray')
        axes[2].set_title('Generated HBP')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"比较图像保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Medical Image Diffusion Inference')
    parser.add_argument('--model_path', type=str, required=True, help='Path to trained model')
    parser.add_argument('--config_path', type=str, required=True, help='Path to config file')
    parser.add_argument('--ap_image', type=str, help='Path to AP image for single inference')
    parser.add_argument('--ap_dir', type=str, help='Directory containing AP images for batch inference')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory')
    parser.add_argument('--num_steps', type=int, default=50, help='Number of inference steps')
    parser.add_argument('--sample_method', type=str, default='ddpm', choices=['ddpm', 'ddim'], help='Sampling method')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--hbp_image', type=str, help='Path to HBP image for comparison')
    
    args = parser.parse_args()
    
    # 创建推理器
    inference = DiffusionInference(args.model_path, args.config_path, args.device)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.ap_image:
        # 单张图像推理
        if args.hbp_image:
            # 与真实图像比较
            comparison_path = output_dir / 'comparison.png'
            inference.compare_with_ground_truth(
                args.ap_image, args.hbp_image, comparison_path, args.num_steps
            )
        else:
            # 单张图像生成
            output_path = output_dir / 'generated.nii.gz'
            inference.generate_single_image(args.ap_image, output_path, args.num_steps)
    
    elif args.ap_dir:
        # 批量推理
        inference.batch_inference(args.ap_dir, output_dir, args.num_steps, args.sample_method)
    
    else:
        print("请指定 --ap_image 或 --ap_dir 参数")

if __name__ == "__main__":
    main()