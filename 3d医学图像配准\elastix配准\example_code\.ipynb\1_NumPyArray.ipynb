{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Working with NumPy Array Images"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import imageio\n", "from itkwidgets import view, compare, checkerboard"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Images as NumPy arrays can be registered.\n", "\n", "Convert them to floating point arrays for registration."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["numpy.n<PERSON><PERSON>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["fixed = imageio.imread('data/CT_2D_head_fixed.mha')\n", "fixed = np.asarray(fixed).astype(np.float32)\n", "type(fixed)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["numpy.n<PERSON><PERSON>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["moving = imageio.imread('data/CT_2D_head_moving.mha')\n", "moving = np.asarray(moving).astype(np.float32)\n", "type(moving)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "71241595c02d4bdab4c5da75531373ad", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=False, description='cmap'), Checkbox(v…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compare(fixed, moving, ui_collapsed=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before registration, the moving image is not aligned with the fixed image."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fbce1ccbcc51461195e41a6628ae3863", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Viewer(annotations=False, interpolation=False, rendered_image=<itk.itkImagePython.itkImageF2; p…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["checkerboard(fixed, moving, pattern=10)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["itk.itkPyBufferPython.NDArrayITKBase"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Register!\n", "registered, parameters = itk.elastix_registration_method(fixed, moving)\n", "type(registered)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a53ed7e6f48c405582228821d5b58464", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=False, description='cmap'), Checkbox(v…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compare(fixed, registered, ui_collapsed=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The registered moving image is aligned with the fixed image"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f43861b9188e47ee979b74c64b7e68d6", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Viewer(annotations=False, interpolation=False, rendered_image=<itk.itkImagePython.itkImageF2; p…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["checkerboard(fixed, registered, pattern=10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}