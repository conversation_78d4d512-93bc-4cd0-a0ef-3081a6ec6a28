#!/usr/bin/env python
# coding: utf-8

# ### 14. World Coordinates Explanation
# This notebook shows to importance of using the world coordinates of images when registering them.
# 

# In[1]:


import itk
from itkwidgets import compare, checkerboard, view
import numpy as np


# In[2]:


# Import Images with world coordinates
fixed_image = itk.imread('data/CT_3D_lung_fixed.mha', itk.F)
moving_image = itk.imread('data/CT_3D_lung_moving.mha', itk.F)

# Recast Image to numpy, then to itk to replace original world coordinates with itk default once.
fixed_image_np = np.asarray(fixed_image).astype(np.float32)
fixed_image_np = itk.image_view_from_array(fixed_image_np)
moving_image_np = np.asarray(moving_image).astype(np.float32)
moving_image_np = itk.image_view_from_array(moving_image_np)

# Registration with original itk image 
result_image, result_transform_parameters = itk.elastix_registration_method(
    fixed_image, moving_image)

# Registration with recasted numpy image with default world coordinates.
result_image_np, result_transform_parameters = itk.elastix_registration_method(
    fixed_image_np, moving_image_np)


# In[3]:


# Compare result images with itk widgets, images do not occupy same fysical space.
compare(result_image, result_image_np)


# In[4]:


# Set origin and spacing equal for pixel-wise image comparison
result_image_no_wc = np.asarray(result_image).astype(np.float32)
result_image_np_no_wc = np.asarray(result_image_np).astype(np.float32)


# In[5]:


# Compare result images, images now occupy same fysical space, but still have (smaller) differences.
compare(result_image_no_wc, result_image_np_no_wc)

