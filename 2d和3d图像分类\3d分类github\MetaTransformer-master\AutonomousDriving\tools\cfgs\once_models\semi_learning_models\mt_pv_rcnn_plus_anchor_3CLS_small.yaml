CLASS_NAMES: ['Vehicle', 'Pedestrian', 'Cyclist']

USE_PRETRAIN_MODEL: True
PRETRAIN_CKPT: ../output/cfgs/once_models/sup_models/pv_rcnn_plus_anchor_3CLS/default/ckpt/checkpoint_epoch_80.pth

DATA_CONFIG: 
    _BASE_CONFIG_: cfgs/dataset_configs/once/SEMI/once_semi_dataset_vehicle.yaml
    LABELED_DATA_FOR: ['teacher', 'student']
    UNLABELED_DATA_FOR: ['teacher', 'student']
    DATA_SPLIT: {
      'train': train,
      'test': val,
      'raw': raw_small,
    }

    TEACHER_AUGMENTOR:
      DISABLE_AUG_LIST: ['random_world_scaling']
      AUG_CONFIG_LIST:
        - NAME: random_world_scaling
          WORLD_SCALE_RANGE: [0.95, 1.05]

    STUDENT_AUGMENTOR:
      DISABLE_AUG_LIST: ['placeholder']
      AUG_CONFIG_LIST:
        - NAME: random_world_flip
          ALONG_AXIS_LIST: ['x', 'y']

        - NAME: random_world_rotation
          WORLD_ROT_ANGLE: [-0.78539816, 0.78539816]

        - NAME: random_world_scaling
          WORLD_SCALE_RANGE: [0.95, 1.05]

OPTIMIZATION:
    PRETRAIN:
        BATCH_SIZE_PER_GPU: 4
        NUM_EPOCHS: 80
        OPTIMIZER: adam_onecycle
        LR: 0.003
        WEIGHT_DECAY: 0.01
        MOMENTUM: 0.9
        MOMS: [0.95, 0.85]
        PCT_START: 0.4
        DIV_FACTOR: 10
        DECAY_STEP_LIST: [35, 45]
        LR_DECAY: 0.1
        LR_CLIP: 0.0000001
        LR_WARMUP: False
        WARMUP_EPOCH: 1
        GRAD_NORM_CLIP: 10

    SEMI_SUP_LEARNING:
        NAME: SESS
        LD_BATCH_SIZE_PER_GPU: 2
        UD_BATCH_SIZE_PER_GPU: 8
        NUM_EPOCHS: 25 # 3 epoch for medium

        FILTER_BY_NMS: False
        NMS:
            SCORE_THRESH: 0.1
            NMS_CONFIG:
                MULTI_CLASSES_NMS: False
                NMS_TYPE: nms_gpu
                NMS_THRESH: 0.01
                NMS_PRE_MAXSIZE: 4096
                NMS_POST_MAXSIZE: 500

        FILTER_BY_SCORE_THRESHOLD: True
        SCORE_THRESHOLD: 0.1
        FILTER_BY_TOPK: False
        TOPK: 128

        CONSISTENCY_WEIGHT: 1 # 10
        CENTER_WEIGHT: 0
        SIZE_WEIGHT: 0
        CLASS_WEIGHT: 2

        TEACHER:
            NUM_ITERS_PER_UPDATE: 1
            EMA_EPOCH: [-1, 24]
            RAMPUP_EMA_MOMENTUM: 0.99
            EMA_MOMENTUM: 0.999

        STUDENT:
            OPTIMIZER: adam_onecycle
            LR: 0.001
            WEIGHT_DECAY: 0.01
            MOMENTUM: 0.9
            MOMS: [0.95, 0.85]
            PCT_START: 0.4
            DIV_FACTOR: 10
            DECAY_STEP_LIST: [35, 45]
            LR_DECAY: 0.1
            LR_CLIP: 0.0000001
            LR_WARMUP: False
            WARMUP_EPOCH: -1
            GRAD_NORM_CLIP: 10

    TEST:
        BATCH_SIZE_PER_GPU: 4

MODEL:
    NAME: SemiPVRCNNPlusPlus

    VFE:
        NAME: MeanVFE

    BACKBONE_3D:
        NAME: VoxelResBackBone8x

    MAP_TO_BEV:
        NAME: HeightCompression
        NUM_BEV_FEATURES: 256

    BACKBONE_2D:
        NAME: BaseBEVBackbone

        LAYER_NUMS: [5, 5]
        LAYER_STRIDES: [1, 2]
        NUM_FILTERS: [128, 256]
        UPSAMPLE_STRIDES: [1, 2]
        NUM_UPSAMPLE_FILTERS: [256, 256]

    DENSE_HEAD:
        NAME: AnchorHeadSemi
        CLASS_AGNOSTIC: False

        USE_DIRECTION_CLASSIFIER: True
        DIR_OFFSET: 0.78539
        DIR_LIMIT_OFFSET: 0.0
        NUM_DIR_BINS: 2

        ANCHOR_GENERATOR_CONFIG: [
            {
                'class_name': 'Vehicle',
                'anchor_sizes': [[4.68, 1.88, 1.66]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-1.70],
                'align_center': False,
                'feature_map_stride': 8,
                'matched_threshold': 0.6,
                'unmatched_threshold': 0.45
            },
            {
                'class_name': 'Pedestrian',
                'anchor_sizes': [[0.75, 0.76, 1.69]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-1.62],
                'align_center': False,
                'feature_map_stride': 8,
                'matched_threshold': 0.3,
                'unmatched_threshold': 0.15
            },
            {
                'class_name': 'Cyclist',
                'anchor_sizes': [[2.18, 0.79, 1.43]],
                'anchor_rotations': [0, 1.57],
                'anchor_bottom_heights': [-1.65],
                'align_center': False,
                'feature_map_stride': 8,
                'matched_threshold': 0.5,
                'unmatched_threshold': 0.35
            }
        ]

        TARGET_ASSIGNER_CONFIG:
            NAME: AxisAlignedTargetAssigner
            POS_FRACTION: -1.0
            SAMPLE_SIZE: 512
            NORM_BY_NUM_EXAMPLES: False
            MATCH_HEIGHT: False
            BOX_CODER: ResidualCoder

        LOSS_CONFIG:
            LOSS_WEIGHTS: {
                'cls_weight': 1.0,
                'loc_weight': 2.0,
                'dir_weight': 0.2,
                'code_weights': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }

    PFE:
        NAME: VoxelSetAbstraction
        POINT_SOURCE: raw_points
        NUM_KEYPOINTS: 4096
        NUM_OUTPUT_FEATURES: 90
        SAMPLE_METHOD: SPC
        SPC_SAMPLING:
            NUM_POINTS_OF_EACH_SAMPLE_PART: 10000000
            NUM_SECTORS: 6
            SAMPLE_RADIUS_WITH_ROI: 1.6

        FEATURES_SOURCE: ['bev', 'x_conv3', 'x_conv4', 'raw_points']
        SA_LAYER:
            raw_points:
                NAME: VectorPoolAggregationModuleMSG
                NUM_GROUPS: 2
                LOCAL_AGGREGATION_TYPE: local_interpolation
                NUM_REDUCED_CHANNELS: 1
                NUM_CHANNELS_OF_LOCAL_AGGREGATION: 32
                MSG_POST_MLPS: [ 32 ]
                FILTER_NEIGHBOR_WITH_ROI: True
                RADIUS_OF_NEIGHBOR_WITH_ROI: 2.4

                GROUP_CFG_0:
                    NUM_LOCAL_VOXEL: [ 2, 2, 2 ]
                    MAX_NEIGHBOR_DISTANCE: 0.2
                    NEIGHBOR_NSAMPLE: -1
                    POST_MLPS: [ 32, 32 ]
                GROUP_CFG_1:
                    NUM_LOCAL_VOXEL: [ 3, 3, 3 ]
                    MAX_NEIGHBOR_DISTANCE: 0.4
                    NEIGHBOR_NSAMPLE: -1
                    POST_MLPS: [ 32, 32 ]

            x_conv3:
                DOWNSAMPLE_FACTOR: 4
                INPUT_CHANNELS: 64

                NAME: VectorPoolAggregationModuleMSG
                NUM_GROUPS: 2
                LOCAL_AGGREGATION_TYPE: local_interpolation
                NUM_REDUCED_CHANNELS: 32
                NUM_CHANNELS_OF_LOCAL_AGGREGATION: 32
                MSG_POST_MLPS: [128]
                FILTER_NEIGHBOR_WITH_ROI: True
                RADIUS_OF_NEIGHBOR_WITH_ROI: 4.0

                GROUP_CFG_0:
                    NUM_LOCAL_VOXEL: [3, 3, 3]
                    MAX_NEIGHBOR_DISTANCE: 1.2
                    NEIGHBOR_NSAMPLE: -1
                    POST_MLPS: [64, 64]
                GROUP_CFG_1:
                    NUM_LOCAL_VOXEL: [ 3, 3, 3 ]
                    MAX_NEIGHBOR_DISTANCE: 2.4
                    NEIGHBOR_NSAMPLE: -1
                    POST_MLPS: [ 64, 64 ]

            x_conv4:
                DOWNSAMPLE_FACTOR: 8
                INPUT_CHANNELS: 64

                NAME: VectorPoolAggregationModuleMSG
                NUM_GROUPS: 2
                LOCAL_AGGREGATION_TYPE: local_interpolation
                NUM_REDUCED_CHANNELS: 32
                NUM_CHANNELS_OF_LOCAL_AGGREGATION: 32
                MSG_POST_MLPS: [ 128 ]
                FILTER_NEIGHBOR_WITH_ROI: True
                RADIUS_OF_NEIGHBOR_WITH_ROI: 6.4

                GROUP_CFG_0:
                    NUM_LOCAL_VOXEL: [ 3, 3, 3 ]
                    MAX_NEIGHBOR_DISTANCE: 2.4
                    NEIGHBOR_NSAMPLE: -1
                    POST_MLPS: [ 64, 64 ]
                GROUP_CFG_1:
                    NUM_LOCAL_VOXEL: [ 3, 3, 3 ]
                    MAX_NEIGHBOR_DISTANCE: 4.8
                    NEIGHBOR_NSAMPLE: -1
                    POST_MLPS: [ 64, 64 ]


    POINT_HEAD:
        NAME: PointHeadSemi
        CLS_FC: [256, 256]
        CLASS_AGNOSTIC: True
        USE_POINT_FEATURES_BEFORE_FUSION: True
        TARGET_CONFIG:
            GT_EXTRA_WIDTH: [0.2, 0.2, 0.2]
        LOSS_CONFIG:
            LOSS_REG: smooth-l1
            LOSS_WEIGHTS: {
                'point_cls_weight': 1.0,
            }

    ROI_HEAD:
        NAME: PVRCNNHeadSemi
        CLASS_AGNOSTIC: True

        SHARED_FC: [256, 256]
        CLS_FC: [256, 256]
        REG_FC: [256, 256]
        DP_RATIO: 0.3

        NMS_CONFIG:
            TRAIN:
                NMS_TYPE: nms_gpu
                MULTI_CLASSES_NMS: False
                NMS_PRE_MAXSIZE: 9000
                NMS_POST_MAXSIZE: 512
                NMS_THRESH: 0.8
            TEST:
                NMS_TYPE: nms_gpu
                MULTI_CLASSES_NMS: False
                NMS_PRE_MAXSIZE: 1024
                NMS_POST_MAXSIZE: 100
                NMS_THRESH: 0.7
                SCORE_THRESH: 0.1

#                NMS_PRE_MAXSIZE: 4096
#                NMS_POST_MAXSIZE: 500
#                NMS_THRESH: 0.85


        ROI_GRID_POOL:
            GRID_SIZE: 6

            NAME: VectorPoolAggregationModuleMSG
            NUM_GROUPS: 2
            LOCAL_AGGREGATION_TYPE: voxel_random_choice
            NUM_REDUCED_CHANNELS: 30
            NUM_CHANNELS_OF_LOCAL_AGGREGATION: 32
            MSG_POST_MLPS: [ 128 ]

            GROUP_CFG_0:
                NUM_LOCAL_VOXEL: [ 3, 3, 3 ]
                MAX_NEIGHBOR_DISTANCE: 0.8
                NEIGHBOR_NSAMPLE: 32
                POST_MLPS: [ 64, 64 ]
            GROUP_CFG_1:
                NUM_LOCAL_VOXEL: [ 3, 3, 3 ]
                MAX_NEIGHBOR_DISTANCE: 1.6
                NEIGHBOR_NSAMPLE: 32
                POST_MLPS: [ 64, 64 ]

        TARGET_CONFIG:
            BOX_CODER: ResidualCoder
            ROI_PER_IMAGE: 128
            FG_RATIO: 0.5

            SAMPLE_ROI_BY_EACH_CLASS: True
            CLS_SCORE_TYPE: roi_iou

            CLS_FG_THRESH: 0.75
            CLS_BG_THRESH: 0.25
            CLS_BG_THRESH_LO: 0.1
            HARD_BG_RATIO: 0.8

            REG_FG_THRESH: 0.55

        LOSS_CONFIG:
            CLS_LOSS: BinaryCrossEntropy
            REG_LOSS: smooth-l1
            CORNER_LOSS_REGULARIZATION: True
            LOSS_WEIGHTS: {
                'rcnn_cls_weight': 1.0,
                'rcnn_reg_weight': 1.0,
                'rcnn_corner_weight': 1.0,
                'code_weights': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }

    POST_PROCESSING:
        RECALL_THRESH_LIST: [0.3, 0.5, 0.7]
        SCORE_THRESH: 0.1
        OUTPUT_RAW_SCORE: False

        EVAL_METRIC: once

        NMS_CONFIG:
            MULTI_CLASSES_NMS: False
            NMS_TYPE: nms_gpu
            NMS_THRESH: 0.01
            NMS_PRE_MAXSIZE: 4096
            NMS_POST_MAXSIZE: 500