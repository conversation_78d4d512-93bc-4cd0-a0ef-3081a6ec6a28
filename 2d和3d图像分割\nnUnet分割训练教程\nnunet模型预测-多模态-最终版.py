#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多序列nnUNet预测脚本
用于对新的多序列或单序列医学图像进行HCC分割预测

使用方法:
1. 批量预测（默认ap序列）:
   python nnunet_predict_multisequence.py

2. 批量预测指定序列:
   python nnunet_predict_multisequence.py --single_sequence pp

3. 批量预测指定路径:
   python nnunet_predict_multisequence.py --input_folder /path/to/images --output_folder /path/to/output

4. 使用最终权重预测:
   python nnunet_predict_multisequence.py --checkpoint final

5. 组合使用:
   python nnunet_predict_multisequence.py --single_sequence hbp --checkpoint final

输入数据格式:
批量预测模式（默认）:
images_folder/
├── patient1-ap.nii.gz   # 病例1动脉期
├── patient2-ap.nii.gz   # 病例2动脉期
├── patient3-ap.nii.gz   # 病例3动脉期
└── ...

支持的文件命名格式:
- patient_name-sequence.nii.gz  (例如: zhang-ap.nii.gz)
- patient_name_sequence.nii.gz  (例如: zhang_ap.nii.gz)
- 序列类型: ap(动脉期), pp(门静脉期), hbp(肝胆期)
"""

import os
import sys
import argparse
import logging
import subprocess
import shutil
from pathlib import Path
import SimpleITK as sitk
import tempfile
import json

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class nnUNetMultiSequencePredictor:
    """多序列nnUNet预测器"""

    def __init__(self, model_path=None, dataset_id=120, configuration="3d_fullres", fold=0,
                 single_sequence=None, checkpoint="best", reference_sequence="ap"):
        """
        初始化预测器

        Args:
            model_path: 模型路径，如果为None则使用默认路径
            dataset_id: 数据集ID
            configuration: 配置类型
            fold: 交叉验证折数
            single_sequence: 单序列模式，可选值: 'ap', 'pp', 'hbp', None(多序列模式)
            checkpoint: 模型权重选择，'best'或'final'
            reference_sequence: 多序列模式下的参考序列，可选值: 'ap', 'pp', 'hbp'
        """
        self.dataset_id = dataset_id
        self.configuration = configuration
        self.fold = fold
        self.single_sequence = single_sequence
        self.checkpoint = checkpoint
        self.reference_sequence = reference_sequence

        # 验证参考序列
        if reference_sequence not in ['ap', 'pp', 'hbp']:
            raise ValueError(f"参考序列必须是 'ap', 'pp', 或 'hbp'，当前值: {reference_sequence}")

        # 设置默认模型路径
        if model_path is None:
            self.model_path = f"./nnUNet_workspace/nnUNet_results/Dataset{dataset_id:03d}_HCC_MultiSequence"
        else:
            self.model_path = model_path

        # 检查模型是否存在
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")

        logger.info(f"使用模型: {self.model_path}")
        logger.info(f"配置: {configuration}, 折数: {fold}")
        logger.info(f"模型权重: checkpoint_{self.checkpoint} ({'最佳权重' if self.checkpoint == 'best' else '最终权重'})")

        if self.single_sequence:
            logger.info(f"单序列预测模式: {self.single_sequence}")
        else:
            logger.info(f"多序列预测模式: ap + pp + hbp (参考序列: {self.reference_sequence.upper()})")
    
    def _resample_image_to_reference(self, moving_image_path, reference_image_path, output_path):
        """将图像配准到参考空间"""
        try:
            # 读取图像
            moving = sitk.ReadImage(moving_image_path)
            reference = sitk.ReadImage(reference_image_path)
            
            # 配准到参考空间
            resampler = sitk.ResampleImageFilter()
            resampler.SetReferenceImage(reference)
            resampler.SetInterpolator(sitk.sitkLinear)
            resampler.SetDefaultPixelValue(0)
            
            resampled = resampler.Execute(moving)
            sitk.WriteImage(resampled, output_path)
            
            logger.debug(f"配准完成: {Path(moving_image_path).name} -> {Path(output_path).name}")
            return True
            
        except Exception as e:
            logger.error(f"配准失败: {e}")
            return False
    
    def prepare_input_data(self, input_folder, temp_folder, case_name):
        """
        准备输入数据，转换为nnUNet格式

        Args:
            input_folder: 输入文件夹路径
            temp_folder: 临时文件夹路径
            case_name: 病例名称

        Returns:
            bool: 是否成功
        """
        input_path = Path(input_folder)
        temp_path = Path(temp_folder)

        if self.single_sequence:
            # 单序列模式
            return self._prepare_single_sequence_data(input_path, temp_path, case_name)
        else:
            # 多序列模式
            return self._prepare_multi_sequence_data(input_path, temp_path, case_name)

    def _prepare_single_sequence_data(self, input_path, temp_path, case_name):
        """准备单序列数据"""
        seq = self.single_sequence
        logger.info(f"单序列模式: 使用{seq}序列进行预测")

        # 查找指定序列文件
        patterns = [
            f"{case_name}-{seq}.nii.gz",
            f"{case_name}_{seq}.nii.gz",
            f"{case_name}-{seq}.nii",
            f"{case_name}_{seq}.nii"
        ]

        sequence_file = None
        for pattern in patterns:
            file_path = input_path / pattern
            if file_path.exists():
                sequence_file = str(file_path)
                break

        if not sequence_file:
            logger.error(f"未找到{seq}序列文件，尝试的模式: {patterns}")
            return False

        logger.info(f"找到{seq}序列文件: {Path(sequence_file).name}")

        try:
            # 对于单序列预测，我们需要创建3个相同的通道来匹配训练时的多通道格式
            # 或者只使用一个通道（需要修改模型配置）
            # 这里我们复制同一个序列到3个通道，让模型以为是多序列输入
            for i in range(3):
                output_file = temp_path / f"{case_name}_{i:04d}.nii.gz"
                shutil.copy2(sequence_file, output_file)
                logger.debug(f"复制{seq}序列到通道{i}: {output_file.name}")

            logger.info(f"单序列数据准备完成: {case_name} ({seq})")
            return True

        except Exception as e:
            logger.error(f"单序列数据准备失败: {e}")
            return False

    def _prepare_multi_sequence_data(self, input_path, temp_path, case_name):
        """准备多序列数据"""
        # 查找多序列文件
        sequences = ['ap', 'pp', 'hbp']
        sequence_files = {}

        for seq in sequences:
            # 首先尝试在子文件夹中查找
            seq_folder = input_path / seq
            if seq_folder.exists():
                # 在子文件夹中查找文件
                patterns = [
                    f"{case_name}-{seq}.nii.gz",
                    f"{case_name}_{seq}.nii.gz",
                    f"{case_name}-{seq}.nii",
                    f"{case_name}_{seq}.nii"
                ]

                found = False
                for pattern in patterns:
                    file_path = seq_folder / pattern
                    if file_path.exists():
                        sequence_files[seq] = str(file_path)
                        found = True
                        break

                if not found:
                    logger.error(f"在{seq}文件夹中未找到{case_name}的{seq}序列文件，尝试的模式: {patterns}")
                    return False
            else:
                # 如果没有子文件夹，尝试在主文件夹中查找
                patterns = [
                    f"{case_name}-{seq}.nii.gz",
                    f"{case_name}_{seq}.nii.gz",
                    f"{case_name}-{seq}.nii",
                    f"{case_name}_{seq}.nii"
                ]

                found = False
                for pattern in patterns:
                    file_path = input_path / pattern
                    if file_path.exists():
                        sequence_files[seq] = str(file_path)
                        found = True
                        break

                if not found:
                    logger.error(f"未找到{seq}序列文件，尝试的模式: {patterns}")
                    return False

        logger.info(f"找到序列文件: {list(sequence_files.keys())}")

        # 使用指定序列作为参考空间
        reference_file = sequence_files[self.reference_sequence]
        logger.info(f"使用{self.reference_sequence.upper()}序列作为参考空间: {Path(reference_file).name}")

        # 序列到通道的映射
        sequence_to_channel = {'ap': 0, 'pp': 1, 'hbp': 2}

        # 转换为nnUNet格式
        try:
            # 处理每个序列
            for seq in ['ap', 'pp', 'hbp']:
                channel_id = sequence_to_channel[seq]
                output_file = temp_path / f"{case_name}_{channel_id:04d}.nii.gz"

                if seq == self.reference_sequence:
                    # 参考序列直接复制
                    shutil.copy2(sequence_files[seq], output_file)
                    logger.debug(f"{seq.upper()}序列 (通道{channel_id}) - 参考空间，直接复制")
                else:
                    # 其他序列配准到参考空间
                    if not self._resample_image_to_reference(sequence_files[seq], reference_file, str(output_file)):
                        shutil.copy2(sequence_files[seq], output_file)  # 配准失败时使用原文件
                        logger.warning(f"{seq.upper()}序列配准到{self.reference_sequence.upper()}空间失败，使用原文件")
                    else:
                        logger.debug(f"{seq.upper()}序列 (通道{channel_id}) - 已配准到{self.reference_sequence.upper()}空间")

            logger.info(f"多序列数据准备完成: {case_name} ({self.reference_sequence.upper()}序列参考空间)")
            return True

        except Exception as e:
            logger.error(f"多序列数据准备失败: {e}")
            return False
    
    def predict_single_case(self, input_folder, output_folder, case_name=None):
        """
        预测单个病例
        
        Args:
            input_folder: 输入文件夹路径
            output_folder: 输出文件夹路径
            case_name: 病例名称，如果为None则自动推断
            
        Returns:
            bool: 是否成功
        """
        input_path = Path(input_folder)
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 自动推断病例名称
        if case_name is None:
            # 查找第一个nii.gz文件并提取病例名
            nii_files = list(input_path.glob("*.nii.gz")) + list(input_path.glob("*.nii"))
            if not nii_files:
                logger.error("输入文件夹中未找到.nii.gz或.nii文件")
                return False
            
            # 从文件名提取病例名（去除序列后缀）
            first_file = nii_files[0].stem
            if first_file.endswith('.nii'):
                first_file = first_file[:-4]
            
            # 移除常见的序列后缀
            for suffix in ['-ap', '-pp', '-hbp', '_ap', '_pp', '_hbp']:
                if first_file.endswith(suffix):
                    case_name = first_file[:-len(suffix)]
                    break
            else:
                case_name = first_file
        
        logger.info(f"预测病例: {case_name}")
        
        # 创建临时文件夹
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_input = Path(temp_dir) / "input"
            temp_output = Path(temp_dir) / "output"
            temp_input.mkdir()
            temp_output.mkdir()
            
            # 准备输入数据
            if not self.prepare_input_data(input_folder, temp_input, case_name):
                return False
            
            try:
                # 设置nnUNet环境变量
                env = os.environ.copy()
                # 设置正确的nnUNet路径
                nnunet_workspace = Path("./nnUNet_workspace").resolve()
                env['nnUNet_results'] = str(nnunet_workspace / "nnUNet_results")
                env['nnUNet_preprocessed'] = str(nnunet_workspace / "nnUNet_preprocessed")
                env['nnUNet_raw'] = str(nnunet_workspace / "nnUNet_raw")
                
                # 构建预测命令
                cmd = [
                    "nnUNetv2_predict",
                    "-i", str(temp_input),
                    "-o", str(temp_output),
                    "-d", str(self.dataset_id),
                    "-c", self.configuration,
                    "-f", str(self.fold),
                    "-tr", "nnUNetTrainer",  # 指定训练器
                    "-chk", f"checkpoint_{self.checkpoint}.pth",  # 指定checkpoint类型
                    "--disable_tta"  # 禁用测试时增强以加快速度
                ]
                
                logger.info(f"执行预测命令: {' '.join(cmd)}")
                
                # 执行预测
                result = subprocess.run(cmd, env=env, capture_output=True, text=True, check=True)
                logger.info("预测完成!")
                
                # 复制结果到输出文件夹
                pred_file = temp_output / f"{case_name}.nii.gz"
                if pred_file.exists():
                    if self.single_sequence:
                        final_output = output_path / f"{case_name}_prediction_{self.single_sequence}.nii.gz"
                    else:
                        final_output = output_path / f"{case_name}_prediction_{self.reference_sequence}_space.nii.gz"
                    shutil.copy2(pred_file, final_output)
                    logger.info(f"预测结果保存到: {final_output}")
                    if not self.single_sequence:
                        logger.info(f"注意: 预测结果在{self.reference_sequence.upper()}序列的空间坐标系中")
                    return True
                else:
                    logger.error("预测文件未生成")
                    return False
                    
            except subprocess.CalledProcessError as e:
                logger.error(f"预测命令执行失败: {e}")
                logger.error(f"错误输出: {e.stderr}")
                return False
            except Exception as e:
                logger.error(f"预测过程出错: {e}")
                return False
    
    def predict_batch(self, input_folder, output_folder):
        """
        批量预测多个病例

        Args:
            input_folder: 包含多个病例文件的输入路径
            output_folder: 输出文件夹路径

        Returns:
            dict: 预测结果统计
        """
        input_path = Path(input_folder)
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)

        # 查找所有图像文件
        if self.single_sequence:
            # 单序列模式：查找指定序列的文件
            seq = self.single_sequence
            # 首先尝试在子文件夹中查找
            seq_folder = input_path / seq
            if seq_folder.exists():
                patterns = [f"*-{seq}.nii.gz", f"*_{seq}.nii.gz", f"*-{seq}.nii", f"*_{seq}.nii"]
                case_files = []
                for pattern in patterns:
                    case_files.extend(seq_folder.glob(pattern))
            else:
                # 如果没有子文件夹，在主文件夹中查找
                patterns = [f"*-{seq}.nii.gz", f"*_{seq}.nii.gz", f"*-{seq}.nii", f"*_{seq}.nii"]
                case_files = []
                for pattern in patterns:
                    case_files.extend(input_path.glob(pattern))
        else:
            # 多序列模式：查找参考序列文件
            ref_seq = self.reference_sequence
            ref_folder = input_path / ref_seq
            if ref_folder.exists():
                # 在参考序列子文件夹中查找
                patterns = [f"*-{ref_seq}.nii.gz", f"*_{ref_seq}.nii.gz", f"*-{ref_seq}.nii", f"*_{ref_seq}.nii"]
                case_files = []
                for pattern in patterns:
                    case_files.extend(ref_folder.glob(pattern))
            else:
                # 如果没有子文件夹，在主文件夹中查找
                patterns = [f"*-{ref_seq}.nii.gz", f"*_{ref_seq}.nii.gz", f"*-{ref_seq}.nii", f"*_{ref_seq}.nii"]
                case_files = []
                for pattern in patterns:
                    case_files.extend(input_path.glob(pattern))

        if not case_files:
            logger.error(f"输入文件夹中未找到匹配的图像文件，搜索模式: {patterns}")
            return {"success": 0, "failed": 0, "total": 0}
        
        # 提取病例名称
        case_names = []
        for case_file in case_files:
            file_stem = case_file.stem
            if file_stem.endswith('.nii'):
                file_stem = file_stem[:-4]

            # 移除序列后缀
            if self.single_sequence:
                suffixes = [f'-{self.single_sequence}', f'_{self.single_sequence}']
            else:
                # 多序列模式，使用参考序列的后缀
                suffixes = [f'-{self.reference_sequence}', f'_{self.reference_sequence}']

            for suffix in suffixes:
                if file_stem.endswith(suffix):
                    case_name = file_stem[:-len(suffix)]
                    if case_name not in case_names:
                        case_names.append(case_name)
                    break

        logger.info(f"找到 {len(case_names)} 个病例文件")

        success_count = 0
        failed_count = 0

        for i, case_name in enumerate(case_names):
            logger.info(f"处理病例 {case_name} ({i + 1}/{len(case_names)})")

            if self.predict_single_case(input_folder, output_path, case_name):
                success_count += 1
                logger.info(f"✅ {case_name} 预测成功")
            else:
                failed_count += 1
                logger.error(f"❌ {case_name} 预测失败")

        results = {
            "success": success_count,
            "failed": failed_count,
            "total": len(case_names)
        }

        logger.info(f"批量预测完成: 成功 {success_count}/{len(case_names)}")
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="多序列/单序列nnUNet预测脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用默认配置进行多序列预测（AP序列作为参考空间）
  python nnunet_predict_multisequence.py

  # 使用PP序列作为参考空间进行多序列预测
  python nnunet_predict_multisequence.py --reference_sequence pp

  # 使用HBP序列作为参考空间进行多序列预测
  python nnunet_predict_multisequence.py --reference_sequence hbp

  # 单序列预测
  python nnunet_predict_multisequence.py -s ap

  # 指定输入输出路径和参考序列
  python nnunet_predict_multisequence.py -i /path/to/input -o /path/to/output -r pp
        """
    )

    parser.add_argument("--input_folder", "-i",
                       default="/root/autodl-tmp/120HCC/image",
                       help="输入文件夹路径 (批量模式: 包含多个病例文件; 单个模式: 单个病例文件夹)")
    parser.add_argument("--output_folder", "-o",
                        default="./predictions",
                       help="输出文件夹路径 ")
    parser.add_argument("--model_path", "-m",
                       default="./nnUNet_workspace/nnUNet_results/Dataset120_HCC_MultiSequence/nnUNetTrainer__nnUNetPlans__3d_fullres",
                       help="模型路径")
    parser.add_argument("--dataset_id", "-d", type=int,default=120,help="数据集ID")
    parser.add_argument("--configuration", "-c", default="3d_fullres", help="配置类型")
    parser.add_argument("--fold", "-f", type=int,default=0,help="交叉验证折数 ")

    # 移除batch参数，默认就是批量预测模式
    parser.add_argument("--single_sequence", "-s", choices=['ap', 'pp', 'hbp'],
                       default=None,
                       help="单序列预测模式，选择ap/pp/hbp中的一个序列。如果不指定则使用多序列模式 (默认: 多序列)")
    parser.add_argument("--checkpoint", "-chk", choices=['best', 'final'],
                       default="final",
                       help="选择模型权重: best=最佳权重(验证集上表现最好), final=最终权重(训练结束时) (默认: best)")
    parser.add_argument("--reference_sequence", "-r", choices=['ap', 'pp', 'hbp'],
                       default="hbp", #就是预测哪个序列的mask
                       help="多序列模式下的参考序列: ap(动脉期), pp(门静脉期), hbp(肝胆期) (默认: ap)")

    args = parser.parse_args()

    # 显示配置信息
    logger.info("=" * 60)
    logger.info("🏥 多序列/单序列nnUNet HCC分割预测")
    logger.info("=" * 60)
    logger.info(f"输入文件夹: {args.input_folder}")
    logger.info(f"输出文件夹: {args.output_folder}")
    logger.info(f"模型路径: {args.model_path}")
    logger.info(f"模型权重: {args.checkpoint} ({'最佳权重' if args.checkpoint == 'best' else '最终权重'})")
    if args.single_sequence is None:
        logger.info(f"预测模式: 多序列 (ap+pp+hbp) - 参考序列: {args.reference_sequence.upper()}")
    else:
        logger.info(f"预测模式: 单序列 ({args.single_sequence})")
    logger.info("批量预测模式")

    # 检查输入路径
    if not os.path.exists(args.input_folder):
        logger.error(f"❌ 输入路径不存在: {args.input_folder}")
        logger.info("请确保输入路径正确，或创建相应的文件夹并放入图像文件")
        return 1

    try:
        # 创建预测器
        predictor = nnUNetMultiSequencePredictor(
            model_path=args.model_path,
            dataset_id=args.dataset_id,
            configuration=args.configuration,
            fold=args.fold,
            single_sequence=args.single_sequence,
            checkpoint=args.checkpoint,
            reference_sequence=args.reference_sequence
        )
        
        # 批量预测
        logger.info("🚀 开始批量预测...")
        results = predictor.predict_batch(args.input_folder, args.output_folder)
        logger.info("=" * 60)
        logger.info("📊 批量预测结果汇总:")
        logger.info(f"  成功: {results['success']}")
        logger.info(f"  失败: {results['failed']}")
        logger.info(f"  总计: {results['total']}")
        logger.info("=" * 60)

        if results['failed'] > 0:
            logger.warning(f"⚠️  有 {results['failed']} 个病例预测失败")
            return 1
        else:
            logger.info("🎉 所有病例预测成功!")
            return 0

    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    """
    nnUNet HCC分割批量预测脚本主入口

    快速使用方法:
    1. 使用默认配置: python nnunet_predict_multisequence.py
    2. 指定路径: python nnunet_predict_multisequence.py -i /path/to/images -o /path/to/output
    3. 指定序列: python nnunet_predict_multisequence.py -s pp
    4. 指定权重: python nnunet_predict_multisequence.py --checkpoint final
    """

    # 设置环境变量
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['OPENBLAS_NUM_THREADS'] = '1'

    exit_code = main()
    sys.exit(exit_code)
