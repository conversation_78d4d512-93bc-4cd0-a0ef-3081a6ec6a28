"""
3D ResNet和3D ViT模型融合进行3D图像3分类
结合了您现有代码的最佳实践，包括数据预处理、模型融合、训练策略等
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.optim.lr_scheduler import _LRScheduler
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import r3d_18
import monai
from monai.data import DataLoader, Dataset
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, 
    RandRotate90d, RandFlipd, RandZoomd, RandShiftIntensityd, 
    Compose, EnsureTyped
)
from sklearn.model_selection import train_test_split
from collections import Counter
import matplotlib.pyplot as plt
import copy
from einops import rearrange, repeat

# 设置环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# ==================== 3D ViT 模型定义 ====================
class Residual(nn.Module):
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(x, **kwargs) + x

class PreNorm(nn.Module):
    def __init__(self, dim, fn):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)

class FeedForward(nn.Module):
    def __init__(self, dim, hidden_dim, dropout=0.):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, dim),
            nn.Dropout(dropout)
        )

    def forward(self, x):
        return self.net(x)

class Attention(nn.Module):
    def __init__(self, dim, heads=8, dim_head=64, dropout=0.):
        super().__init__()
        inner_dim = dim_head * heads
        self.heads = heads
        self.scale = dim ** -0.5

        self.to_qkv = nn.Linear(dim, inner_dim * 3, bias=False)
        self.to_out = nn.Sequential(
            nn.Linear(inner_dim, dim),
            nn.Dropout(dropout)
        )

    def forward(self, x, mask=None):
        b, n, _, h = *x.shape, self.heads
        qkv = self.to_qkv(x).chunk(3, dim=-1)
        q, k, v = map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), qkv)

        dots = torch.einsum('bhid,bhjd->bhij', q, k) * self.scale
        mask_value = -torch.finfo(dots.dtype).max

        if mask is not None:
            mask = F.pad(mask.flatten(1), (1, 0), value=True)
            assert mask.shape[-1] == dots.shape[-1], 'mask has incorrect dimensions'
            mask = mask[:, None, :] * mask[:, :, None]
            dots.masked_fill_(~mask, mask_value)
            del mask

        attn = dots.softmax(dim=-1)

        out = torch.einsum('bhij,bhjd->bhid', attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        out = self.to_out(out)
        return out

class Transformer(nn.Module):
    def __init__(self, dim, depth, heads, dim_head, mlp_dim, dropout):
        super().__init__()
        self.layers = nn.ModuleList([])
        for _ in range(depth):
            self.layers.append(nn.ModuleList([
                Residual(PreNorm(dim, Attention(dim, heads=heads,
                                                dim_head=dim_head, dropout=dropout))),
                Residual(PreNorm(dim, FeedForward(
                    dim, mlp_dim, dropout=dropout)))
            ]))

    def forward(self, x, mask=None):
        for attn, ff in self.layers:
            x = attn(x, mask=mask)
            x = ff(x)
        return x

class ViT3D(nn.Module):
    def __init__(self, *, image_size, patch_size, num_classes, dim, depth, heads, mlp_dim, 
                 pool='cls', channels=1, dim_head=64, dropout=0., emb_dropout=0.):
        super().__init__()
        assert all([each_dimension % patch_size == 0 for each_dimension in image_size])
        
        num_patches = (image_size[0] // patch_size) * (image_size[1] // patch_size) * (image_size[2] // patch_size)
        patch_dim = channels * patch_size ** 3
        
        assert num_patches > 16, f'patches数量({num_patches})太少，至少需要16个'
        assert pool in {'cls', 'mean'}, 'pool类型必须是cls或mean'

        self.patch_size = patch_size
        self.pos_embedding = nn.Parameter(torch.randn(1, num_patches + 1, dim))
        self.patch_to_embedding = nn.Linear(patch_dim, dim)
        self.cls_token = nn.Parameter(torch.randn(1, 1, dim))
        self.dropout = nn.Dropout(emb_dropout)

        self.transformer = Transformer(dim, depth, heads, dim_head, mlp_dim, dropout)
        self.pool = pool
        self.to_latent = nn.Identity()

        self.mlp_head = nn.Sequential(
            nn.LayerNorm(dim),
            nn.Linear(dim, num_classes)
        )

    def forward(self, img, mask=None, return_features=False):
        p = self.patch_size
        x = rearrange(img, 'b c (x p1) (y p2) (z p3) -> b (x y z) (p1 p2 p3 c)', p1=p, p2=p, p3=p)
        x = self.patch_to_embedding(x)
        b, n, _ = x.shape

        cls_tokens = repeat(self.cls_token, '() n d -> b n d', b=b)
        x = torch.cat((cls_tokens, x), dim=1)
        x += self.pos_embedding[:, :(n + 1)]
        x = self.dropout(x)

        x = self.transformer(x, mask)
        features = x.mean(dim=1) if self.pool == 'mean' else x[:, 0]
        
        if return_features:
            return features
        
        x = self.to_latent(features)
        return self.mlp_head(x)

# ==================== 3D ResNet 特征提取器 ====================
class FeatureExtractorResNet3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorResNet3D, self).__init__()
        self.model = r3d_18(pretrained=pretrained)
        # 去掉最后一层全连接层，保留特征提取部分
        self.model = nn.Sequential(*list(self.model.children())[:-1])
        
        # 冻结前面的层，只训练后面几层
        for param in list(self.model.parameters())[:-10]:
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

# ==================== 3D ViT 特征提取器 ====================
class FeatureExtractorViT3D(nn.Module):
    def __init__(self, image_size=(96, 96, 64), patch_size=16, dim=512, depth=6, heads=8, mlp_dim=1024):
        super(FeatureExtractorViT3D, self).__init__()
        self.model = ViT3D(
            image_size=image_size,
            patch_size=patch_size,
            num_classes=3,  # 临时设置，实际不使用
            dim=dim,
            depth=depth,
            heads=heads,
            mlp_dim=mlp_dim,
            dropout=0.1,
            emb_dropout=0.1
        )
        
    def forward(self, x):
        # 返回特征而不是分类结果
        features = self.model(x, return_features=True)
        return features

# ==================== 融合模型 ====================
class ResNetViTFusionModel(nn.Module):
    def __init__(self, num_classes=3, image_size=(96, 96, 64), fusion_type='concat'):
        super(ResNetViTFusionModel, self).__init__()
        
        # 特征提取器
        self.resnet_extractor = FeatureExtractorResNet3D(pretrained=True)
        self.vit_extractor = FeatureExtractorViT3D(image_size=image_size)
        
        self.fusion_type = fusion_type
        
        # 特征维度适配
        self.resnet_adapter = nn.Sequential(
            nn.AdaptiveAvgPool3d(1),
            nn.Flatten(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.vit_adapter = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 融合层
        if fusion_type == 'concat':
            fusion_dim = 256 + 256  # ResNet + ViT
        elif fusion_type == 'add':
            fusion_dim = 256
        elif fusion_type == 'attention':
            fusion_dim = 256
            self.attention_weights = nn.Sequential(
                nn.Linear(256 * 2, 128),
                nn.ReLU(),
                nn.Linear(128, 2),
                nn.Softmax(dim=1)
            )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(fusion_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_classes)
        )
        
    def forward(self, x):
        # 确保输入是3通道（对于ResNet）
        if x.size(1) == 1:
            x_resnet = x.expand(-1, 3, -1, -1, -1)
        else:
            x_resnet = x
            
        # 对于ViT，使用单通道
        x_vit = x if x.size(1) == 1 else x[:, :1, :, :, :]
        
        # 特征提取
        resnet_features = self.resnet_extractor(x_resnet)
        vit_features = self.vit_extractor(x_vit)
        
        # 特征适配
        resnet_features = self.resnet_adapter(resnet_features)
        vit_features = self.vit_adapter(vit_features)
        
        # 特征融合
        if self.fusion_type == 'concat':
            fused_features = torch.cat([resnet_features, vit_features], dim=1)
        elif self.fusion_type == 'add':
            fused_features = resnet_features + vit_features
        elif self.fusion_type == 'attention':
            concat_features = torch.cat([resnet_features, vit_features], dim=1)
            attention_weights = self.attention_weights(concat_features)
            fused_features = (attention_weights[:, 0:1] * resnet_features + 
                            attention_weights[:, 1:2] * vit_features)
        
        # 分类
        output = self.classifier(fused_features)
        return output

# ==================== 数据处理 ====================
def create_data_transforms(resized_shape=(96, 96, 64)):
    """创建数据预处理管道"""
    train_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=resized_shape),
        ScaleIntensityRanged(
            keys=["image"], 
            a_min=-200, 
            a_max=300, 
            b_min=0.0, 
            b_max=1.0, 
            clip=True
        ),
        RandRotate90d(keys=["image"], prob=0.3, spatial_axes=[0, 1]),    
        RandFlipd(keys=["image"], prob=0.5, spatial_axis=0),
        RandZoomd(keys=["image"], prob=0.3, min_zoom=0.9, max_zoom=1.1),
        RandShiftIntensityd(keys=["image"], offsets=0.1, prob=0.5),
        EnsureTyped(keys=["image"])
    ])

    test_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=resized_shape),
        ScaleIntensityRanged(
            keys=["image"], 
            a_min=-200, 
            a_max=300, 
            b_min=0.0, 
            b_max=1.0, 
            clip=True
        ),
        EnsureTyped(keys=["image"])
    ])
    
    return train_transforms, test_transforms

def create_dataset(data_dir, label_excel, label_name='label'):
    """创建数据集"""
    # 读取Excel文件
    df = pd.read_excel(label_excel)
    
    # 获取图像文件列表
    image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
    
    # 创建包含图像路径和标签的字典列表
    data = []
    for img_path in image_files:
        base_name = os.path.basename(img_path)
        file_name = os.path.splitext(base_name)[0]  
        file_names = file_name.split('-')[0]     
        
        if file_names in df['name'].values:
            label = df.loc[df['name'] == file_names, label_name].values[0]
            data.append({"image": img_path, "label": label, "file_name": file_names})    
    return data

# ==================== 损失函数 ====================
class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡"""
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class LabelSmoothingLoss(nn.Module):
    """标签平滑损失"""
    def __init__(self, num_classes, smoothing=0.1):
        super(LabelSmoothingLoss, self).__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        
    def forward(self, inputs, targets):
        log_prob = F.log_softmax(inputs, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.num_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        return (-true_dist * log_prob).sum(dim=-1).mean()

# ==================== 学习率调度器 ====================
class PolyLRScheduler(_LRScheduler):
    def __init__(self, optimizer, max_epochs, power=0.9, min_lr=1e-6):
        self.max_epochs = max_epochs
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer)

    def get_lr(self):
        return [max(self.min_lr, base_lr * (1 - self.last_epoch / self.max_epochs) ** self.power)
                for base_lr in self.base_lrs]

# ==================== 早停机制 ====================
class EarlyStopping:
    def __init__(self, patience=15, min_delta=1e-4):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = None
        self.early_stop = False
        self.best_model = None

    def __call__(self, val_loss, model):
        if self.best_loss is None:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
        elif val_loss > self.best_loss - self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_loss = val_loss
            self.best_model = copy.deepcopy(model.state_dict())
            self.counter = 0

# ==================== 训练函数 ====================
def train_model(model, train_loader, val_loader, num_epochs=50, device='cuda', 
                learning_rate=1e-4, weight_decay=1e-4, save_path='best_model.pth'):
    """训练模型"""
    
    # 损失函数 - 组合Focal Loss和Label Smoothing
    focal_loss = FocalLoss(alpha=1.0, gamma=2.0)
    label_smooth_loss = LabelSmoothingLoss(num_classes=3, smoothing=0.1)
    
    def combined_loss(outputs, targets):
        return 0.7 * focal_loss(outputs, targets) + 0.3 * label_smooth_loss(outputs, targets)
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = PolyLRScheduler(optimizer, max_epochs=num_epochs, power=0.9)
    
    # 早停
    early_stopping = EarlyStopping(patience=20, min_delta=1e-4)
    
    # 记录训练历史
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': [],
        'lr': []
    }
    
    best_accuracy = 0.0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        current_lr = optimizer.param_groups[0]['lr']
        history['lr'].append(current_lr)
        print(f'Epoch [{epoch+1}/{num_epochs}], LR: {current_lr:.2e}')
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(device)
            labels = batch['label'].long().to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = combined_loss(outputs, labels)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        # 更新学习率
        scheduler.step()
        
        # 计算训练指标
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100 * correct / total
        history['train_loss'].append(epoch_loss)
        history['train_acc'].append(epoch_acc)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device)
                labels = batch['label'].long().to(device)
                
                outputs = model(images)
                loss = combined_loss(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        # 计算验证指标
        val_loss = val_loss / len(val_loader)
        val_acc = 100 * correct / total
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        print(f'Epoch [{epoch+1}/{num_epochs}]:')
        print(f'Train Loss: {epoch_loss:.4f}, Train Acc: {epoch_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        # 保存最佳模型
        if val_acc > best_accuracy:
            best_accuracy = val_acc
            torch.save(model.state_dict(), save_path)
            print(f'Best model saved with accuracy: {best_accuracy:.2f}%')
        
        # 早停检查
        early_stopping(val_loss, model)
        if early_stopping.early_stop:
            print("Early stopping triggered")
            model.load_state_dict(early_stopping.best_model)
            break
    
    return history, best_accuracy

# ==================== 可视化函数 ====================
def plot_training_history(history):
    """绘制训练历史"""
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 损失曲线
    axes[0].plot(history['train_loss'], label='Train Loss')
    axes[0].plot(history['val_loss'], label='Val Loss')
    axes[0].set_title('Loss History')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    
    # 准确率曲线
    axes[1].plot(history['train_acc'], label='Train Acc')
    axes[1].plot(history['val_acc'], label='Val Acc')
    axes[1].set_title('Accuracy History')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('Accuracy (%)')
    axes[1].legend()
    
    # 学习率曲线
    axes[2].plot(history['lr'])
    axes[2].set_title('Learning Rate History')
    axes[2].set_xlabel('Epoch')
    axes[2].set_ylabel('Learning Rate')
    
    plt.tight_layout()
    plt.show()

# ==================== 主函数 ====================
def main():
    """主函数"""
    # 配置参数
    config = {
        'data_dir': '/path/to/your/data',  # 修改为您的数据路径
        'label_excel': '/path/to/your/labels.xlsx',  # 修改为您的标签文件路径
        'label_name': 'label',  # 标签列名
        'resized_shape': (96, 96, 64),  # 图像尺寸
        'batch_size': 4,  # 批次大小
        'num_epochs': 100,  # 训练轮数
        'learning_rate': 1e-4,  # 学习率
        'weight_decay': 1e-4,  # 权重衰减
        'fusion_type': 'attention',  # 融合方式: 'concat', 'add', 'attention'
        'save_path': 'resnet_vit_fusion_best_model.pth'  # 模型保存路径
    }
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # 数据预处理
    train_transforms, test_transforms = create_data_transforms(config['resized_shape'])
    
    # 创建数据集
    all_data = create_dataset(config['data_dir'], config['label_excel'], config['label_name'])
    
    # 划分训练集和验证集
    train_data, val_data = train_test_split(
        all_data, 
        test_size=0.2, 
        random_state=42,
        stratify=[d["label"] for d in all_data]
    )
    
    # 创建数据加载器
    train_dataset = Dataset(data=train_data, transform=train_transforms)
    val_dataset = Dataset(data=val_data, transform=test_transforms)
    
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
    
    print(f"训练集样本数: {len(train_dataset)}")
    print(f"验证集样本数: {len(val_dataset)}")
    
    # 检查类别分布
    train_labels = [d["label"] for d in train_data]
    val_labels = [d["label"] for d in val_data]
    print(f"训练集类别分布: {Counter(train_labels)}")
    print(f"验证集类别分布: {Counter(val_labels)}")
    
    # 创建模型
    model = ResNetViTFusionModel(
        num_classes=3, 
        image_size=config['resized_shape'],
        fusion_type=config['fusion_type']
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 训练模型
    history, best_accuracy = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['num_epochs'],
        device=device,
        learning_rate=config['learning_rate'],
        weight_decay=config['weight_decay'],
        save_path=config['save_path']
    )
    
    print(f"训练完成！最佳验证准确率: {best_accuracy:.2f}%")
    
    # 绘制训练历史
    plot_training_history(history)
    
    # 保存训练历史
    history_df = pd.DataFrame(history)
    history_df.to_excel('training_history.xlsx', index=False)
    print("训练历史已保存到 training_history.xlsx")

if __name__ == "__main__":
    main()