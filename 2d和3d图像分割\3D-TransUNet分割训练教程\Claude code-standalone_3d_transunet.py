#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D-TransUNet 单独运行脚本
整合nnUNet数据预处理和3D-TransUNet模型训练的完整解决方案

功能特性:
- nnUNet风格的数据预处理
- 3D-TransUNet模型训练
- 完整的训练和验证循环
- 保存最佳和最后的模型权重
- 保存训练指标(dice, loss, lr)
- 支持断点续训
- 内存优化和错误处理
- 可视化训练曲线

作者: AI Assistant
日期: 2024
"""

import os
import sys
import json
import pickle
import logging
import warnings
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.amp import autocast, GradScaler
import SimpleITK as sitk
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.ndimage import zoom
import time
from sklearn.model_selection import train_test_split

# 设置警告过滤
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

def setup_environment():
    """设置环境变量和路径"""
    base_dir = "/root/autodl-tmp"
    
    # 设置nnUNet环境变量
    env_vars = {
        'nnUNet_N_proc_DA': '8',
        'nnUNet_codebase': os.path.join(base_dir, "nnUNet"),
        'nnUNet_raw_data_base': os.path.join(base_dir, "nnUNet_raw_data_base"),
        'nnUNet_preprocessed': os.path.join(base_dir, "nnUNet_preprocessed"),
        'RESULTS_FOLDER': os.path.join(base_dir, "nnUNet_results")
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        if key != 'nnUNet_N_proc_DA':
            os.makedirs(value, exist_ok=True)
    
    # 添加3D-TransUNet路径
    transunet_path = os.path.join(base_dir, "3D-TransUNet")
    if os.path.exists(transunet_path) and transunet_path not in sys.path:
        sys.path.insert(0, transunet_path)
    
    return env_vars

def setup_logging(log_file: str = "standalone_transunet_training.log") -> logging.Logger:
    """设置日志系统"""
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    logger = logging.getLogger('Standalone_TransUNet')
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        # 文件handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

class Config:
    """训练配置类"""
    def __init__(self):
        # 数据路径
        self.image_dir = "/root/autodl-tmp/120HCC/image/ap"
        self.mask_dir = "/root/autodl-tmp/120HCC/mask/ap"
        self.preprocessed_dir = "/root/autodl-tmp/Standalone_TransUNet_preprocessed"
        self.output_dir = "/root/autodl-tmp/Standalone_TransUNet_results"

        # 模型配置
        self.num_classes = 2
        self.input_channels = 1
        self.base_num_features = 32

        # 训练配置 - 优化版
        self.batch_size = 4  # 增加批次大小以提高训练效率
        self.num_epochs = 200  # 增加训练轮数以获得更好的收敛
        self.initial_lr = 1e-3  # 提高初始学习率以加快收敛
        self.min_lr = 1e-7
        self.weight_decay = 1e-4  # 增加正则化
        self.warmup_epochs = 20  # 增加预热期

        # 数据配置 - 优化版
        self.patch_size = [96, 160, 160]  # 增大patch size提高性能
        self.target_spacing = [1.0, 1.0, 1.0]
        self.normalization_scheme = "zscore"  # 使用z-score归一化
        self.crop_to_nonzero = True

        # TransUNet特定配置
        self.vit_depth = 12
        self.vit_hidden_size = 768
        self.max_hidden_dim = 192
        self.num_queries = 20
        self.is_max_hungarian = False
        self.is_max_cls = True
        self.is_vit_pretrain = False
        self.is_max_bottleneck_transformer = True
        self.is_masked_attn = True
        self.max_dec_layers = 3
        self.is_max_ms = True
        self.max_ms_idxs = [-4, -3, -2]
        self.mw = 0.0
        self.is_max_ds = True
        self.is_masking = True
        self.is_mhsa_float32 = True
        self.vit_layer_scale = True
        
        # 其他配置 - 优化版
        self.mixed_precision = True
        self.deep_supervision = True
        self.save_interval = 10
        self.validation_interval = 1
        self.early_stopping_patience = 50  # 增加耐心值
        self.gradient_clip_norm = 1.0
        # 数据增强配置
        self.use_data_augmentation = True
        self.rotation_angle = 15
        self.flip_probability = 0.5
        
        # 创建输出目录
        os.makedirs(self.preprocessed_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
    
    def validate(self) -> bool:
        """验证配置"""
        if not os.path.exists(self.image_dir):
            print(f"错误: 图像目录不存在: {self.image_dir}")
            return False
        if not os.path.exists(self.mask_dir):
            print(f"错误: 标签目录不存在: {self.mask_dir}")
            return False
        return True
    
    def print_summary(self, logger):
        """打印配置摘要"""
        logger.info("\n" + "="*60)
        logger.info("3D-TransUNet 单独训练配置")
        logger.info("="*60)
        logger.info(f"数据路径:")
        logger.info(f"  图像目录: {self.image_dir}")
        logger.info(f"  标签目录: {self.mask_dir}")
        logger.info(f"  预处理目录: {self.preprocessed_dir}")
        logger.info(f"  输出目录: {self.output_dir}")
        logger.info(f"\n模型配置:")
        logger.info(f"  类别数: {self.num_classes}")
        logger.info(f"  输入通道: {self.input_channels}")
        logger.info(f"  补丁大小: {self.patch_size}")
        logger.info(f"\n训练配置:")
        logger.info(f"  批次大小: {self.batch_size}")
        logger.info(f"  训练轮数: {self.num_epochs}")
        logger.info(f"  初始学习率: {self.initial_lr}")
        logger.info(f"  权重衰减: {self.weight_decay}")
        logger.info(f"  预热轮数: {self.warmup_epochs}")
        logger.info("="*60)

class nnUNetStylePreprocessor:
    """nnUNet风格的数据预处理器"""

    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger

    def load_image(self, image_path: str) -> Tuple[np.ndarray, Dict]:
        """加载医学图像"""
        try:
            sitk_image = sitk.ReadImage(image_path)
            image_array = sitk.GetArrayFromImage(sitk_image)

            properties = {
                'original_spacing': sitk_image.GetSpacing()[::-1],  # ITK uses xyz, we use zyx
                'original_size': sitk_image.GetSize()[::-1],
                'original_origin': sitk_image.GetOrigin()[::-1],
                'original_direction': sitk_image.GetDirection()
            }

            return image_array.astype(np.float32), properties

        except Exception as e:
            self.logger.error(f"加载图像失败 {image_path}: {e}")
            raise

    def crop_to_nonzero(self, data: np.ndarray, seg: np.ndarray = None) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """裁剪到非零区域"""
        nonzero_mask = data != 0
        nonzero_coords = np.where(nonzero_mask)
        
        if len(nonzero_coords[0]) == 0:
            self.logger.warning("图像中没有非零像素")
            return data, seg, {}

        bbox = []
        for i in range(len(nonzero_coords)):
            bbox.append([np.min(nonzero_coords[i]), np.max(nonzero_coords[i]) + 1])

        cropped_data = data[bbox[0][0]:bbox[0][1],
                           bbox[1][0]:bbox[1][1],
                           bbox[2][0]:bbox[2][1]]

        cropped_seg = None
        if seg is not None:
            cropped_seg = seg[bbox[0][0]:bbox[0][1],
                             bbox[1][0]:bbox[1][1],
                             bbox[2][0]:bbox[2][1]]

        crop_properties = {
            'crop_bbox': bbox,
            'original_shape': data.shape,
            'cropped_shape': cropped_data.shape
        }

        return cropped_data, cropped_seg, crop_properties

    def resample_image(self, data: np.ndarray, original_spacing: List[float],
                      target_spacing: List[float], is_mask: bool = False) -> np.ndarray:
        """重采样图像到目标spacing"""
        zoom_factors = [orig / target for orig, target in zip(original_spacing, target_spacing)]

        if all(abs(factor - 1.0) < 0.01 for factor in zoom_factors):
            return data.astype(np.float32)

        order = 0 if is_mask else 1
        resampled_data = zoom(data, zoom_factors, order=order, mode='nearest', prefilter=False)

        return resampled_data.astype(np.float32)

    def normalize_image(self, data: np.ndarray, scheme: str = "zscore") -> np.ndarray:
        """图像归一化"""
        if scheme == "noNorm":
            return data
        elif scheme == "zscore":
            # 只在前景区域计算统计量
            foreground_mask = data > 0
            if np.sum(foreground_mask) > 0:
                mean = data[foreground_mask].mean()
                std = data[foreground_mask].std()
                if std > 0:
                    data[foreground_mask] = (data[foreground_mask] - mean) / std
            return data
        elif scheme == "minmax":
            data_min = data.min()
            data_max = data.max()
            if data_max > data_min:
                return (data - data_min) / (data_max - data_min)
            else:
                return data
        else:
            return data

    def preprocess_case(self, image_path: str, mask_path: str) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """预处理单个病例"""
        # 加载图像和标签
        image, image_props = self.load_image(image_path)
        mask, mask_props = self.load_image(mask_path)

        if image.shape != mask.shape:
            raise ValueError(f"图像和标签形状不匹配: {image.shape} vs {mask.shape}")

        # 裁剪到非零区域
        if self.config.crop_to_nonzero:
            image, mask, crop_props = self.crop_to_nonzero(image, mask)
        else:
            crop_props = {}

        # 重采样
        original_spacing = image_props['original_spacing']
        target_spacing = self.config.target_spacing

        spacing_diff = [abs(orig - target) for orig, target in zip(original_spacing, target_spacing)]
        max_diff = max(spacing_diff)

        if max_diff > 0.1:
            image = self.resample_image(image, original_spacing, target_spacing, is_mask=False)
            mask = self.resample_image(mask, original_spacing, target_spacing, is_mask=True)

        # 归一化
        image = self.normalize_image(image, self.config.normalization_scheme)

        # 添加通道维度
        if len(image.shape) == 3:
            image = image[np.newaxis, ...]  # (1, D, H, W)
        if len(mask.shape) == 3:
            mask = mask[np.newaxis, ...]

        # 合并属性
        properties = {
            **image_props,
            **crop_props,
            'target_spacing': self.config.target_spacing,
            'normalization_scheme': self.config.normalization_scheme
        }

        return image.astype(np.float32), mask.astype(np.uint8), properties

    def preprocess_dataset(self) -> bool:
        """预处理整个数据集并保存"""
        self.logger.info("开始nnUNet风格数据预处理...")

        # 获取所有图像文件
        image_files = sorted(list(Path(self.config.image_dir).glob("*.nii.gz")))
        
        # 查找对应的标签文件
        valid_pairs = []
        for img_file in image_files:
            img_name = img_file.stem.replace('.nii', '').replace('-ap', '')
            # 尝试多种标签文件命名模式
            possible_mask_names = [
                f"{img_name}-ap-mask.nii.gz",
                f"{img_name}-mask.nii.gz",
                f"{img_name.replace('-ap', '')}-ap-mask.nii.gz"
            ]
            
            mask_file = None
            for mask_name in possible_mask_names:
                potential_mask = Path(self.config.mask_dir) / mask_name
                if potential_mask.exists():
                    mask_file = potential_mask
                    break
            
            if mask_file:
                valid_pairs.append((img_file, mask_file))
            else:
                self.logger.warning(f"跳过文件（未找到对应标签）: {img_file.name}")

        self.logger.info(f"找到 {len(valid_pairs)} 个有效的图像-标签对")

        if len(valid_pairs) == 0:
            self.logger.error("未找到任何有效的图像-标签对")
            return False

        # 创建数据集信息
        dataset_info = {
            'preprocessed_files': [],
            'preprocessing_config': {
                'target_spacing': self.config.target_spacing,
                'normalization_scheme': self.config.normalization_scheme,
                'crop_to_nonzero': self.config.crop_to_nonzero,
                'patch_size': self.config.patch_size
            }
        }

        processed_count = 0
        progress_bar = tqdm(valid_pairs, desc="预处理数据")

        for img_file, mask_file in progress_bar:
            try:
                # 构建输出文件名
                img_name = img_file.stem.replace('.nii', '').replace('-ap', '')
                
                image_output = os.path.join(self.config.preprocessed_dir, f"{img_name}_image.npz")
                mask_output = os.path.join(self.config.preprocessed_dir, f"{img_name}_mask.npz")
                props_output = os.path.join(self.config.preprocessed_dir, f"{img_name}_props.pkl")

                # 检查是否已经预处理过
                if all(os.path.exists(f) for f in [image_output, mask_output, props_output]):
                    try:
                        # 验证文件完整性
                        test_image = np.load(image_output)['data']
                        test_mask = np.load(mask_output)['data']
                        with open(props_output, 'rb') as f:
                            test_props = pickle.load(f)

                        dataset_info['preprocessed_files'].append({
                            'case_name': img_name,
                            'original_image': str(img_file),
                            'original_mask': str(mask_file),
                            'preprocessed_image': image_output,
                            'preprocessed_mask': mask_output,
                            'properties': props_output,
                            'shape': test_image.shape
                        })

                        processed_count += 1
                        progress_bar.set_postfix({'已处理': processed_count, '状态': '跳过(已存在)'})
                        continue
                    except Exception:
                        pass

                # 预处理
                image, mask, properties = self.preprocess_case(str(img_file), str(mask_file))

                # 保存为压缩格式
                np.savez_compressed(image_output, data=image)
                np.savez_compressed(mask_output, data=mask)

                with open(props_output, 'wb') as f:
                    pickle.dump(properties, f)

                dataset_info['preprocessed_files'].append({
                    'case_name': img_name,
                    'original_image': str(img_file),
                    'original_mask': str(mask_file),
                    'preprocessed_image': image_output,
                    'preprocessed_mask': mask_output,
                    'properties': props_output,
                    'shape': image.shape
                })

                processed_count += 1
                progress_bar.set_postfix({'已处理': processed_count, '状态': '新处理'})

            except Exception as e:
                self.logger.error(f"预处理失败 {img_file.name}: {e}")
                continue

        # 保存数据集信息
        dataset_info_path = os.path.join(self.config.preprocessed_dir, 'dataset_info.json')
        with open(dataset_info_path, 'w') as f:
            json.dump(dataset_info, f, indent=2)

        self.logger.info(f"数据预处理完成！共处理 {processed_count} 个样本")
        return processed_count > 0

class TransUNetDataset(Dataset):
    """3D-TransUNet数据集类"""

    def __init__(self, preprocessed_dir: str, patch_size: List[int], 
                 data_split: str = 'train', logger: logging.Logger = None,
                 use_augmentation: bool = True):
        self.preprocessed_dir = Path(preprocessed_dir)
        self.patch_size = patch_size
        self.data_split = data_split
        self.logger = logger
        self.use_augmentation = use_augmentation and (data_split == 'train')


        self._load_preprocessed_data()

    def _load_preprocessed_data(self):
        """加载预处理后的数据信息"""
        dataset_info_path = self.preprocessed_dir / 'dataset_info.json'

        if not dataset_info_path.exists():
            raise FileNotFoundError(f"未找到数据集信息文件: {dataset_info_path}")

        with open(dataset_info_path, 'r') as f:
            self.dataset_info = json.load(f)

        all_files = self.dataset_info['preprocessed_files']
        
        # 划分训练集和验证集
        if len(all_files) > 1:
            train_files, val_files = train_test_split(
                all_files, test_size=0.2, random_state=42
            )
        else:
            # 如果只有一个文件，同时用于训练和验证
            train_files = val_files = all_files
        
        if self.data_split == 'train':
            self.preprocessed_files = train_files
        else:
            self.preprocessed_files = val_files

        if self.logger:
            self.logger.info(f"加载{self.data_split}数据集，包含 {len(self.preprocessed_files)} 个样本")

    def __len__(self):
        return len(self.preprocessed_files)

    def extract_patch(self, image: np.ndarray, mask: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """提取随机补丁并应用数据增强"""
        _, D, H, W = image.shape
        pd, ph, pw = self.patch_size

        # 如果图像小于补丁大小，进行填充
        if D < pd or H < ph or W < pw:
            pad_d = max(0, pd - D)
            pad_h = max(0, ph - H)
            pad_w = max(0, pw - W)

            image = np.pad(image, ((0, 0), (0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            mask = np.pad(mask, ((0, 0), (0, pad_d), (0, pad_h), (0, pad_w)), mode='constant')
            D, H, W = image.shape[1:]

        # 随机选择起始位置
        start_d = np.random.randint(0, max(1, D - pd + 1))
        start_h = np.random.randint(0, max(1, H - ph + 1))
        start_w = np.random.randint(0, max(1, W - pw + 1))

        # 提取补丁
        image_patch = image[:, start_d:start_d+pd, start_h:start_h+ph, start_w:start_w+pw]
        mask_patch = mask[:, start_d:start_d+pd, start_h:start_h+ph, start_w:start_w+pw]

        # 数据增强（仅在训练时应用）
        if self.use_augmentation:
            image_patch, mask_patch = self.apply_augmentation(image_patch, mask_patch)

        return image_patch, mask_patch

    def apply_augmentation(self, image: np.ndarray, mask: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用数据增强"""
        # 随机翻转
        if np.random.rand() < 0.5:
            # 左右翻转
            image = np.flip(image, axis=-1).copy()
            mask = np.flip(mask, axis=-1).copy()
        
        if np.random.rand() < 0.5:
            # 前后翻转
            image = np.flip(image, axis=-2).copy()
            mask = np.flip(mask, axis=-2).copy()

        # 随机旋转（在axial平面）
        if np.random.rand() < 0.5:
            from scipy.ndimage import rotate
            angle = np.random.uniform(-15, 15)
            image = rotate(image, angle, axes=(-2, -1), reshape=False, order=1, mode='nearest')
            mask = rotate(mask, angle, axes=(-2, -1), reshape=False, order=0, mode='nearest')

        # 随机强度变化
        if np.random.rand() < 0.3:
            # 添加轻微的噪声
            noise_std = 0.01 * np.std(image[image > 0])
            noise = np.random.normal(0, noise_std, image.shape)
            image = image + noise

        return image, mask

    def __getitem__(self, idx):
        try:
            # 加载预处理后的数据
            file_info = self.preprocessed_files[idx]

            image_data = np.load(file_info['preprocessed_image'])['data']
            mask_data = np.load(file_info['preprocessed_mask'])['data']

            # 提取补丁
            image_patch, mask_patch = self.extract_patch(image_data, mask_data)

            # 转换为tensor
            image_tensor = torch.from_numpy(image_patch).float()
            mask_tensor = torch.from_numpy(mask_patch).long()

            return image_tensor, mask_tensor

        except Exception as e:
            if self.logger:
                self.logger.error(f"加载数据时出错 (idx={idx}): {e}")
            # 返回零张量作为fallback
            image_tensor = torch.zeros((1, *self.patch_size), dtype=torch.float32)
            mask_tensor = torch.zeros((1, *self.patch_size), dtype=torch.long)
            return image_tensor, mask_tensor

def try_import_transunet():
    """尝试导入TransUNet模块"""
    try:
        from nn_transunet.networks.transunet3d_model import Generic_TransUNet_max_ppbp, InitWeights_He
        from nn_transunet.trainer.loss_functions import DC_and_CE_loss
        return Generic_TransUNet_max_ppbp, DC_and_CE_loss, True
    except ImportError as e:
        print(f"无法导入TransUNet模块: {e}")
        return None, None, False

class SimpleTransUNet(nn.Module):
    """简化的TransUNet模型（如果无法导入原始模块时使用）"""

    def __init__(self, input_channels=1, num_classes=2, base_features=32):
        super().__init__()

        # 编码器
        self.encoder1 = self._make_layer(input_channels, base_features)
        self.encoder2 = self._make_layer(base_features, base_features * 2)
        self.encoder3 = self._make_layer(base_features * 2, base_features * 4)
        self.encoder4 = self._make_layer(base_features * 4, base_features * 8)

        # 瓶颈层
        self.bottleneck = self._make_layer(base_features * 8, base_features * 16)

        # 解码器
        self.decoder4 = self._make_layer(base_features * 16 + base_features * 8, base_features * 8)
        self.decoder3 = self._make_layer(base_features * 8 + base_features * 4, base_features * 4)
        self.decoder2 = self._make_layer(base_features * 4 + base_features * 2, base_features * 2)
        self.decoder1 = self._make_layer(base_features * 2 + base_features, base_features)

        # 输出层
        self.final_conv = nn.Conv3d(base_features, num_classes, kernel_size=1)

        # 池化和上采样
        self.pool = nn.MaxPool3d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=False)

    def _make_layer(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv3d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.InstanceNorm3d(out_channels),
            nn.LeakyReLU(0.01, inplace=True),
            nn.Conv3d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.InstanceNorm3d(out_channels),
            nn.LeakyReLU(0.01, inplace=True)
        )

    def forward(self, x):
        # 编码路径
        enc1 = self.encoder1(x)
        enc2 = self.encoder2(self.pool(enc1))
        enc3 = self.encoder3(self.pool(enc2))
        enc4 = self.encoder4(self.pool(enc3))

        # 瓶颈
        bottleneck = self.bottleneck(self.pool(enc4))

        # 解码路径
        dec4 = self.decoder4(torch.cat([self.upsample(bottleneck), enc4], dim=1))
        dec3 = self.decoder3(torch.cat([self.upsample(dec4), enc3], dim=1))
        dec2 = self.decoder2(torch.cat([self.upsample(dec3), enc2], dim=1))
        dec1 = self.decoder1(torch.cat([self.upsample(dec2), enc1], dim=1))

        # 输出
        output = self.final_conv(dec1)
        return output

class DiceLoss(nn.Module):
    """Dice损失函数"""

    def __init__(self, smooth=1e-5):
        super().__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = torch.softmax(pred, dim=1)

        if target.dim() == 5:
            target = target.squeeze(1)

        target_one_hot = torch.zeros_like(pred)
        target_one_hot.scatter_(1, target.unsqueeze(1), 1)

        intersection = (pred * target_one_hot).sum(dim=(2, 3, 4))
        union = pred.sum(dim=(2, 3, 4)) + target_one_hot.sum(dim=(2, 3, 4))

        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        return 1 - dice.mean()

class FocalLoss(nn.Module):
    """Focal Loss for imbalanced datasets"""
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class CombinedLoss(nn.Module):
    """优化的组合损失函数（Dice + CrossEntropy + Focal）"""

    def __init__(self, dice_weight=0.4, ce_weight=0.4, focal_weight=0.2):
        super().__init__()
        self.dice_weight = dice_weight
        self.ce_weight = ce_weight
        self.focal_weight = focal_weight
        self.dice_loss = DiceLoss()
        self.ce_loss = nn.CrossEntropyLoss()
        self.focal_loss = FocalLoss(alpha=1, gamma=2)

    def forward(self, pred, target):
        dice = self.dice_loss(pred, target)

        if target.dim() == 5:
            target_ce = target.squeeze(1)
        else:
            target_ce = target

        ce = self.ce_loss(pred, target_ce)
        focal = self.focal_loss(pred, target_ce)
        
        return self.dice_weight * dice + self.ce_weight * ce + self.focal_weight * focal

def calculate_dice_score(pred, target, num_classes=2):
    """计算Dice分数"""
    pred = torch.softmax(pred, dim=1)
    pred = torch.argmax(pred, dim=1)

    if target.dim() == 5:
        target = target.squeeze(1)

    dice_scores = []
    for class_idx in range(num_classes):
        pred_class = (pred == class_idx).float()
        target_class = (target == class_idx).float()

        intersection = (pred_class * target_class).sum()
        union = pred_class.sum() + target_class.sum()

        if union == 0:
            dice = 1.0 if intersection == 0 else 0.0
        else:
            dice = (2 * intersection) / union

        dice_scores.append(dice.item() if hasattr(dice, 'item') else dice)

    return dice_scores

class Trainer:
    """训练器类"""

    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 尝试导入TransUNet模型
        TransUNet, DCCELoss, import_success = try_import_transunet()

        if import_success:
            self.logger.info("成功导入TransUNet模块，使用原始模型")
            try:
                # 创建简化的原始TransUNet模型参数
                self.model = TransUNet(
                    input_channels=config.input_channels,
                    base_num_features=config.base_num_features,
                    num_classes=config.num_classes,
                    num_pool=3,
                    num_conv_per_stage=2,
                    feat_map_mul_on_downscale=2,
                    conv_op=nn.Conv3d,
                    norm_op=nn.InstanceNorm3d,
                    norm_op_kwargs={'eps': 1e-5, 'affine': True},
                    dropout_op=nn.Dropout3d,
                    dropout_op_kwargs={'p': 0.1, 'inplace': True},
                    nonlin=nn.LeakyReLU,
                    nonlin_kwargs={'negative_slope': 0.01, 'inplace': True},
                    deep_supervision=config.deep_supervision,
                    dropout_in_localization=False,
                    final_nonlin=lambda x: x,
                    weightInitializer=InitWeights_He(1e-2) if import_success else None,
                    pool_op_kernel_sizes=[[2, 2, 2]] * 4,
                    conv_kernel_sizes=[[3, 3, 3]] * 5,
                    upscale_logits=False,
                    convolutional_pooling=False,
                    convolutional_upsampling=True,
                    max_num_features=320,
                    patch_size=config.patch_size,
                    vit_depth=config.vit_depth,
                    max_hidden_dim=config.max_hidden_dim,
                    is_max_bottleneck_transformer=config.is_max_bottleneck_transformer,
                    is_masked_attn=config.is_masked_attn,
                    max_dec_layers=config.max_dec_layers,
                    is_max_ms=config.is_max_ms,
                    max_ms_idxs=config.max_ms_idxs,
                    mw=config.mw,
                    is_max_ds=config.is_max_ds,
                    is_masking=config.is_masking,
                    num_queries=config.num_queries,
                    is_max_cls=config.is_max_cls,
                    is_mhsa_float32=config.is_mhsa_float32,
                    vit_layer_scale=config.vit_layer_scale
                )
                self.criterion = DCCELoss({'batch_dice': True, 'smooth': 1e-5, 'do_bg': False}, {})
                self.logger.info("原始TransUNet模型初始化成功")
            except Exception as e:
                self.logger.warning(f"原始TransUNet模型初始化失败: {e}")
                self.logger.info("回退到简化模型")
                self.model = SimpleTransUNet(
                    input_channels=config.input_channels,
                    num_classes=config.num_classes,
                    base_features=config.base_num_features
                )
                self.criterion = CombinedLoss()
        else:
            self.logger.info("使用简化的TransUNet模型")
            self.model = SimpleTransUNet(
                input_channels=config.input_channels,
                num_classes=config.num_classes,
                base_features=config.base_num_features
            )
            self.criterion = CombinedLoss()

        self.model.to(self.device)

        # 优化器和调度器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.initial_lr,
            weight_decay=config.weight_decay
        )

        # 更好的学习率调度器
        total_steps = config.num_epochs
        warmup_steps = config.warmup_epochs
        
        # 使用多步衰减调度器
        self.scheduler = optim.lr_scheduler.MultiStepLR(
            self.optimizer,
            milestones=[total_steps//3, 2*total_steps//3, 4*total_steps//5],
            gamma=0.1
        )
        
        # 添加预热调度器
        from torch.optim.lr_scheduler import LambdaLR
        warmup_scheduler = LambdaLR(
            self.optimizer,
            lr_lambda=lambda epoch: min(1.0, (epoch + 1) / warmup_steps) if epoch < warmup_steps else 1.0
        )
        self.warmup_scheduler = warmup_scheduler

        # 混合精度训练
        self.scaler = GradScaler() if config.mixed_precision else None

        # 训练状态
        self.current_epoch = 0
        self.best_dice = 0.0
        self.train_losses = []
        self.val_losses = []
        self.train_dice_scores = []
        self.val_dice_scores = []
        self.learning_rates = []

        # 早停
        self.patience_counter = 0

    def save_checkpoint(self, is_best=False, is_final=False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_dice': self.best_dice,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_dice_scores': self.train_dice_scores,
            'val_dice_scores': self.val_dice_scores,
            'learning_rates': self.learning_rates,
            'config': self.config.__dict__
        }

        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()

        # 保存最新检查点
        latest_path = os.path.join(self.config.output_dir, 'model_latest.pth')
        torch.save(checkpoint, latest_path)

        # 保存最佳检查点
        if is_best:
            best_path = os.path.join(self.config.output_dir, 'model_best.pth')
            torch.save(checkpoint, best_path)
            self.logger.info(f"保存最佳模型，Dice分数: {self.best_dice:.4f}")

        # 保存最终检查点
        if is_final:
            final_path = os.path.join(self.config.output_dir, 'model_final.pth')
            torch.save(checkpoint, final_path)
            self.logger.info("保存最终模型")

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        if os.path.exists(checkpoint_path):
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            self.current_epoch = checkpoint['epoch']
            self.best_dice = checkpoint['best_dice']
            self.train_losses = checkpoint.get('train_losses', [])
            self.val_losses = checkpoint.get('val_losses', [])
            self.train_dice_scores = checkpoint.get('train_dice_scores', [])
            self.val_dice_scores = checkpoint.get('val_dice_scores', [])
            self.learning_rates = checkpoint.get('learning_rates', [])

            if self.scaler and 'scaler_state_dict' in checkpoint:
                self.scaler.load_state_dict(checkpoint['scaler_state_dict'])

            self.logger.info(f"从epoch {self.current_epoch}恢复训练，最佳Dice: {self.best_dice:.4f}")
            return True
        return False

    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_dice = [0.0] * self.config.num_classes
        num_batches = len(train_loader)

        progress_bar = tqdm(train_loader, desc=f"训练 Epoch {self.current_epoch}")

        for batch_idx, (images, masks) in enumerate(progress_bar):
            images = images.to(self.device)
            masks = masks.to(self.device)

            self.optimizer.zero_grad()

            if self.scaler:
                with autocast('cuda'):
                    outputs = self.model(images)
                    loss = self.criterion(outputs, masks)

                self.scaler.scale(loss).backward()

                if self.config.gradient_clip_norm > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip_norm)

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(images)
                loss = self.criterion(outputs, masks)
                loss.backward()

                if self.config.gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip_norm)

                self.optimizer.step()

            # 计算指标
            total_loss += loss.item()
            dice_scores = calculate_dice_score(outputs, masks, self.config.num_classes)
            for i, score in enumerate(dice_scores):
                total_dice[i] += score

            # 更新进度条
            avg_loss = total_loss / (batch_idx + 1)
            avg_dice = total_dice[1] / (batch_idx + 1) if len(total_dice) > 1 else 0
            progress_bar.set_postfix({
                'Loss': f'{avg_loss:.4f}',
                'Dice': f'{avg_dice:.4f}',
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
            })

        # 计算平均指标
        avg_loss = total_loss / num_batches
        avg_dice_scores = [score / num_batches for score in total_dice]

        return avg_loss, avg_dice_scores

    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        total_dice = [0.0] * self.config.num_classes
        num_batches = len(val_loader)

        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc=f"验证 Epoch {self.current_epoch}")

            for batch_idx, (images, masks) in enumerate(progress_bar):
                images = images.to(self.device)
                masks = masks.to(self.device)

                if self.scaler:
                    with autocast('cuda'):
                        outputs = self.model(images)
                        loss = self.criterion(outputs, masks)
                else:
                    outputs = self.model(images)
                    loss = self.criterion(outputs, masks)

                # 计算指标
                total_loss += loss.item()
                dice_scores = calculate_dice_score(outputs, masks, self.config.num_classes)
                for i, score in enumerate(dice_scores):
                    total_dice[i] += score

                # 更新进度条
                avg_loss = total_loss / (batch_idx + 1)
                avg_dice = total_dice[1] / (batch_idx + 1) if len(total_dice) > 1 else 0
                progress_bar.set_postfix({
                    'Loss': f'{avg_loss:.4f}',
                    'Dice': f'{avg_dice:.4f}'
                })

        # 计算平均指标
        avg_loss = total_loss / num_batches
        avg_dice_scores = [score / num_batches for score in total_dice]

        return avg_loss, avg_dice_scores

    def save_metrics(self):
        """保存训练指标"""
        metrics = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_dice_scores': self.train_dice_scores,
            'val_dice_scores': self.val_dice_scores,
            'learning_rates': self.learning_rates
        }

        metrics_path = os.path.join(self.config.output_dir, 'training_metrics.json')
        with open(metrics_path, 'w') as f:
            json.dump(metrics, f, indent=2)

        # 绘制训练曲线
        self.plot_training_curves()

    def plot_training_curves(self):
        """绘制训练曲线"""
        if len(self.train_losses) == 0:
            return

        epochs = range(1, len(self.train_losses) + 1)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 损失曲线
        ax1.plot(epochs, self.train_losses, 'b-', label='训练损失')
        if self.val_losses:
            ax1.plot(epochs, self.val_losses, 'r-', label='验证损失')
        ax1.set_title('损失曲线')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)

        # Dice分数曲线
        if self.train_dice_scores:
            train_dice_fg = [scores[1] if len(scores) > 1 else 0 for scores in self.train_dice_scores]
            ax2.plot(epochs, train_dice_fg, 'b-', label='训练Dice')
        if self.val_dice_scores:
            val_dice_fg = [scores[1] if len(scores) > 1 else 0 for scores in self.val_dice_scores]
            ax2.plot(epochs, val_dice_fg, 'r-', label='验证Dice')
        ax2.set_title('Dice分数曲线')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Dice Score')
        ax2.legend()
        ax2.grid(True)

        # 学习率曲线
        if self.learning_rates:
            ax3.plot(epochs, self.learning_rates, 'g-', label='学习率')
            ax3.set_title('学习率曲线')
            ax3.set_xlabel('Epoch')
            ax3.set_ylabel('Learning Rate')
            ax3.legend()
            ax3.grid(True)

        # 训练总结
        ax4.axis('off')
        final_val_loss = f"{self.val_losses[-1]:.4f}" if self.val_losses else "N/A"
        final_lr = f"{self.learning_rates[-1]:.6f}" if self.learning_rates else "N/A"

        summary_text = f"""训练总结:

总轮数: {len(self.train_losses)}
最佳验证Dice: {self.best_dice:.4f}
最终训练损失: {self.train_losses[-1]:.4f}
最终验证损失: {final_val_loss}
最终学习率: {final_lr}
        """
        ax4.text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center')

        plt.tight_layout()
        plt.savefig(os.path.join(self.config.output_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()

def create_data_loaders(config: Config, logger: logging.Logger):
    """创建数据加载器"""
    # 创建训练和验证数据集
    train_dataset = TransUNetDataset(
        config.preprocessed_dir,
        config.patch_size,
        data_split='train',
        logger=logger,
        use_augmentation=getattr(config, 'use_data_augmentation', True)
    )

    val_dataset = TransUNetDataset(
        config.preprocessed_dir,
        config.patch_size,
        data_split='val',
        logger=logger,
        use_augmentation=False  # 验证时不使用数据增强
    )

    logger.info(f"数据集大小: 训练集 {len(train_dataset)}, 验证集 {len(val_dataset)}")

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )

    return train_loader, val_loader

def run_training(config: Config, logger: logging.Logger):
    """运行完整的训练流程"""
    logger.info("开始3D-TransUNet完整训练流程")

    # 1. 数据预处理
    preprocessor = nnUNetStylePreprocessor(config, logger)
    dataset_info_path = os.path.join(config.preprocessed_dir, 'dataset_info.json')
    
    if not os.path.exists(dataset_info_path):
        logger.info("未找到预处理数据，开始数据预处理...")
        success = preprocessor.preprocess_dataset()
        if not success:
            logger.error("数据预处理失败")
            return
    else:
        logger.info("发现预处理数据，跳过预处理步骤")

    # 2. 创建数据加载器
    train_loader, val_loader = create_data_loaders(config, logger)

    # 3. 创建训练器
    trainer = Trainer(config, logger)

    # 4. 尝试加载检查点
    checkpoint_path = os.path.join(config.output_dir, 'model_latest.pth')
    if os.path.exists(checkpoint_path):
        logger.info("发现检查点，尝试恢复训练...")
        trainer.load_checkpoint(checkpoint_path)

    start_epoch = max(1, trainer.current_epoch + 1)
    logger.info(f"开始训练，从epoch {start_epoch}到{config.num_epochs}")

    try:
        for epoch in range(start_epoch, config.num_epochs + 1):
            trainer.current_epoch = epoch

            # 训练
            train_loss, train_dice = trainer.train_epoch(train_loader)
            trainer.train_losses.append(train_loss)
            trainer.train_dice_scores.append(train_dice)

            # 验证
            if epoch % config.validation_interval == 0:
                val_loss, val_dice = trainer.validate_epoch(val_loader)
                trainer.val_losses.append(val_loss)
                trainer.val_dice_scores.append(val_dice)

                # 记录学习率
                current_lr = trainer.optimizer.param_groups[0]['lr']
                trainer.learning_rates.append(current_lr)

                # 检查是否为最佳模型
                val_dice_fg = val_dice[1] if len(val_dice) > 1 else val_dice[0]
                is_best = val_dice_fg > trainer.best_dice
                if is_best:
                    trainer.best_dice = val_dice_fg
                    trainer.patience_counter = 0
                else:
                    trainer.patience_counter += 1

                # 记录日志
                logger.info(
                    f"Epoch {epoch}/{config.num_epochs} - "
                    f"训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}, "
                    f"训练Dice: {train_dice[1] if len(train_dice) > 1 else train_dice[0]:.4f}, "
                    f"验证Dice: {val_dice_fg:.4f}, "
                    f"学习率: {current_lr:.6f}"
                )

                # 保存检查点
                trainer.save_checkpoint(is_best=is_best)

                # 早停检查
                if trainer.patience_counter >= config.early_stopping_patience:
                    logger.info(f"验证Dice在{config.early_stopping_patience}个epoch内没有改善，提前停止训练")
                    break

            # 更新学习率
            if epoch <= config.warmup_epochs:
                trainer.warmup_scheduler.step()
            else:
                trainer.scheduler.step()

            # 定期保存指标
            if epoch % config.save_interval == 0:
                trainer.save_metrics()

        # 保存最终模型和指标
        trainer.save_checkpoint(is_final=True)
        trainer.save_metrics()

        logger.info("训练完成！")
        logger.info(f"最佳验证Dice分数: {trainer.best_dice:.4f}")

    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        trainer.save_checkpoint()
        trainer.save_metrics()
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        trainer.save_checkpoint()
        trainer.save_metrics()
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='3D-TransUNet 单独运行脚本')
    parser.add_argument('--image_dir', type=str, default='/root/autodl-tmp/120HCC/image/ap',
                       help='图像目录路径')
    parser.add_argument('--mask_dir', type=str, default='/root/autodl-tmp/120HCC/mask/ap',
                       help='标签目录路径')
    parser.add_argument('--output_dir', type=str, default='/root/autodl-tmp/Standalone_TransUNet_results',
                       help='输出目录路径')
    parser.add_argument('--batch_size', type=int, default=2,
                       help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=125,
                       help='训练轮数')
    parser.add_argument('--initial_lr', type=float, default=3e-4,
                       help='初始学习率')
    parser.add_argument('--patch_size', nargs=3, type=int, default=[96, 160, 160],
                       help='补丁大小 [D, H, W]')
    parser.add_argument('--use_augmentation', action='store_true', default=True,
                       help='是否使用数据增强')
    parser.add_argument('--early_stopping_patience', type=int, default=50,
                       help='早停耐心值')

    args = parser.parse_args()

    # 设置环境
    setup_environment()
    logger = setup_logging()

    # 创建配置
    config = Config()

    # 更新配置参数
    config.image_dir = args.image_dir
    config.mask_dir = args.mask_dir
    config.output_dir = args.output_dir
    config.batch_size = args.batch_size
    config.num_epochs = args.num_epochs
    config.initial_lr = args.initial_lr
    config.patch_size = args.patch_size
    config.use_data_augmentation = args.use_augmentation
    config.early_stopping_patience = args.early_stopping_patience

    # 创建输出目录
    os.makedirs(config.output_dir, exist_ok=True)
    os.makedirs(config.preprocessed_dir, exist_ok=True)

    if not config.validate():
        logger.error("配置验证失败")
        return

    config.print_summary(logger)

    # 检查GPU
    if torch.cuda.is_available():
        logger.info(f"使用GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        logger.warning("未检测到GPU，将使用CPU训练（速度较慢）")

    # 运行训练
    run_training(config, logger)

if __name__ == "__main__":
    main()