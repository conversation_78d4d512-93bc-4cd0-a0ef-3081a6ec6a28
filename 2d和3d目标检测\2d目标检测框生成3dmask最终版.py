import os
import numpy as np
import pandas as pd
import nibabel as nib
import math
import glob

def read_detection_coordinates(excel_file):
    """
    Read detection coordinates from an Excel file.
    The Excel format follows the format shown in the user's query.
    
    Returns a dictionary with image names as keys and list of detections as values
    Each detection is a dictionary with box coordinates and other metadata
    """
    # Read Excel file
    # If Excel file is in the format given in the query, use these columns
    try:
        df = pd.read_excel(excel_file)
    except Exception as e:
        # If there's an error reading the Excel file, try reading directly from the table string
        print(f"Error reading Excel file: {e}")
        print("Attempting to parse from table string...")
        
        # Parse the table string format (this is a fallback if Excel file isn't available)
        table_data = """
        | image | box_x1 | box_y1 | box_x2 | box_y2 | width | height | area | label | score |
        |----------|---------|---------|---------|---------|--------|--------|-------|-------|--------|
        |guofengzi| 51.00689| 97.25278|122.0933|184.1124|71.0264|86.8596|6221.441|hcc|0.901745|
        """
        
        # Convert string to dataframe
        import io
        df = pd.read_csv(io.StringIO(table_data), sep='|', skipinitialspace=True)
        df = df.iloc[1:-1]  # Remove header and empty rows
        df.columns = [col.strip() for col in df.columns]
        
        # Clean up the data
        for col in df.columns:
            if col.strip():  # Only process non-empty column names
                df[col] = df[col].astype(str).str.strip()
        
        # Convert numeric columns to float
        numeric_cols = ['box_x1', 'box_y1', 'box_x2', 'box_y2', 'width', 'height', 'area', 'score']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
    
    detections = {}
    
    for _, row in df.iterrows():
        # Use image name as key - get the part before the first hyphen if it exists
        img_name = row['image'].strip() if isinstance(row['image'], str) else str(row['image'])
        
        # Extract the part before the first hyphen (if there is one)
        if '-' in img_name:
            base_name = img_name.split('-')[0]
        else:
            base_name = img_name
            
        if base_name not in detections:
            detections[base_name] = []
        
        # Extract coordinates from the row
        detection = {
            'box_x1': float(row['box_x1']),
            'box_y1': float(row['box_y1']),
            'box_x2': float(row['box_x2']),
            'box_y2': float(row['box_y2']),
            'width': float(row['width']),
            'height': float(row['height']),
            'label': row['label'],
            'score': float(row['score']),
            'original_name': img_name  # Keep the original name for reference
        }
        detections[base_name].append(detection)
    
    return detections

def create_3d_cube_mask(image_shape, detection, slice_idx, pixel_spacing=None, slice_thickness=None, z_range=None):
    """
    Create a 3D cubic mask based on 2D detection coordinates
    
    Parameters:
    - image_shape: Shape of the 3D image
    - detection: Dictionary with detection coordinates
    - slice_idx: The slice index where the detection was found
    - pixel_spacing: (x,y) spacing in mm for each pixel in xy plane
    - slice_thickness: z-spacing in mm for each slice
    - z_range: Optional tuple (z_min, z_max) to specify exact z range for mask
    
    Returns a 3D binary mask
    """
    # Create empty mask with the same shape as the original image
    mask = np.zeros(image_shape, dtype=np.uint8)
    
    # Calculate center of bounding box
    center_x = int((detection['box_x1'] + detection['box_x2']) / 2)
    center_y = int((detection['box_y1'] + detection['box_y2']) / 2)
    
    # 计算边界框的实际宽度和高度 (像素单位)
    box_width = detection['box_x2'] - detection['box_x1']
    box_height = detection['box_y2'] - detection['box_y1']
    
    # 使用实际边界框的宽度和高度的最大值作为立方体的边长 (像素单位)
    max_diameter_pixels = max(box_width, box_height)
    
    # 如果提供了体素尺寸信息，则考虑物理尺寸
    if pixel_spacing is not None and slice_thickness is not None:
        # 将像素单位转换为物理尺寸 (mm)
        pixel_size_x, pixel_size_y = pixel_spacing
        max_diameter_mm = max(box_width * pixel_size_x, box_height * pixel_size_y)
        
        # 计算z方向上需要多少层才能达到相同的物理尺寸
        z_slices_needed = max_diameter_mm / slice_thickness
        z_radius_slices = int(z_slices_needed / 2)
        
        print(f"物理尺寸：最大边长 = {max_diameter_mm:.2f}mm，层厚 = {slice_thickness}mm")
        print(f"需要的层数：{z_slices_needed:.2f}层，半径：{z_radius_slices}层")
    else:
        # 如果没有体素尺寸信息，直接使用像素单位
        z_radius_slices = int(max_diameter_pixels / 2)
        max_diameter_mm = None
    
    # 计算半径 (像素单位)
    radius = math.ceil(max_diameter_pixels / 2)
    
    # Ensure radius is at least 5 pixels for visibility
    radius = max(radius, 5)
    z_radius_slices = max(z_radius_slices, 5)
    
    print(f"Creating 3D cube mask - Center: ({center_x}, {center_y}, {slice_idx})")
    print(f"像素尺寸：边长 = {max_diameter_pixels}px，半径 = {radius}px")
    if max_diameter_mm:
        print(f"物理尺寸：边长 = {max_diameter_mm:.2f}mm，Z轴层数 = {z_slices_needed:.2f}")
    print(f"Detection box_width: {box_width}, box_height: {box_height}, using max: {max_diameter_pixels}")
    
    # In NIfTI files, the typical dimension order is (x, y, z)
    # Adjust for the specific coordinate system of your data
    
    # Fix potential coordinate issues by ensuring the minimum is less than the maximum
    x_min = max(0, min(center_x - radius, image_shape[0] - 1))
    x_max = min(image_shape[0], max(center_x + radius, x_min + 1))
    y_min = max(0, min(center_y - radius, image_shape[1] - 1))
    y_max = min(image_shape[1], max(center_y + radius, y_min + 1))
    
    # 使用z_range参数或计算Z轴范围
    if z_range is not None:
        # 直接使用提供的z_range
        z_min, z_max = z_range
        print(f"使用指定的Z轴范围: {z_min}-{z_max}")
    else:
        # 使用计算的层数来设置Z轴范围
        # 确保Z轴以slice_idx为精确中心，向上和向下扩展相同的层数
        target_z_size = int(max_diameter_pixels) if max_diameter_mm is None else int(z_slices_needed)
        
        # 无论图像边界如何，先尝试以目标检测层面为中心点
        half_z_size = target_z_size // 2
        
        # 首先，假设我们可以在slice_idx周围对称扩展
        # 考虑奇偶性，确保总高度正确
        if target_z_size % 2 == 0:  # 偶数高度
            z_min = slice_idx - half_z_size
            z_max = slice_idx + half_z_size
        else:  # 奇数高度
            z_min = slice_idx - half_z_size
            z_max = slice_idx + half_z_size + 1
        
        # 检查边界并尝试调整，但优先保持目标检测层面居中
        if z_min < 0 or z_max > image_shape[2]:
            # 优先情况1: 切割两端以保持中心位置
            if z_min < 0 and z_max <= image_shape[2]:
                # 下边界超出，上移整个立方体，但尽量保持中心
                offset = -z_min  # 需要上移的层数
                z_min = 0
                
                # 减少反向偏移量，尽量保持目标层面居中
                if slice_idx + half_z_size + offset <= image_shape[2]:
                    # 还有足够空间向上延伸
                    z_max += min(offset, (image_shape[2] - z_max))
                    print(f"下边界调整: 上移了{offset}层，上边界也上移了{min(offset, (image_shape[2] - z_max))}层")
                else:
                    # 没有足够空间，只能上移一部分
                    z_max = image_shape[2]
                    print(f"下边界调整: 上移了{offset}层，但上边界已达图像边界")
            
            # 优先情况2: 同样切割两端以保持中心位置
            elif z_min >= 0 and z_max > image_shape[2]:
                # 上边界超出，下移整个立方体，但尽量保持中心
                offset = z_max - image_shape[2]  # 需要下移的层数
                z_max = image_shape[2]
                
                # 减少反向偏移量，尽量保持目标层面居中
                if z_min - offset >= 0:
                    # 还有足够空间向下延伸
                    z_min -= min(offset, z_min)
                    print(f"上边界调整: 下移了{offset}层，下边界也下移了{min(offset, z_min)}层")
                else:
                    # 没有足够空间，只能下移一部分
                    z_min = 0
                    print(f"上边界调整: 下移了{offset}层，但下边界已达图像边界")
            
            # 情况3: 总高度大于图像深度，使用全部层数但调整图像显示中心
            else:  # z_min < 0 and z_max > image_shape[2]
                # 如果立方体高度大于图像总高度，我们使用全部图像深度
                # 但尝试通过调整NIFTI文件元数据来表明目标层面的位置
                
                # 先使用全部图像深度
                z_min = 0
                z_max = image_shape[2]
                
                # 记录目标层面索引，以便在文件元数据中标注
                print(f"立方体高度({target_z_size})大于图像深度({image_shape[2]})，使用全部可用层数")
                print(f"目标检测层面索引={slice_idx}，偏离中心点{slice_idx - (z_max + z_min) / 2:.1f}层")
        else:
            # 理想情况 - 立方体完全包含在图像边界内
            print(f"完美情况: 立方体完全包含在图像边界内，以目标检测层面为中心点")
    
    # 确保所有索引都是整数
    z_min = int(max(0, z_min))
    z_max = int(min(image_shape[2], z_max))
    
    # 打印Z轴对齐情况
    z_center = (z_min + z_max) / 2
    z_offset = z_center - slice_idx
    print(f"Z轴对齐情况: 目标层面={slice_idx}, 立方体范围={z_min}-{z_max}, 立方体中心={z_center:.1f}, 偏移量={z_offset:.1f}")
    
    # 如果目标检测层面不在mask范围内（这不应该发生），发出警告
    if slice_idx < z_min or slice_idx >= z_max:
        print(f"警告：目标检测层面{slice_idx}不在mask范围{z_min}-{z_max}内！")
        
    # 确保所有索引都是整数
    x_min, x_max = int(x_min), int(x_max)
    y_min, y_max = int(y_min), int(y_max)
    
    # Calculate the actual dimensions of our cube
    x_size = x_max - x_min
    y_size = y_max - y_min
    z_size = z_max - z_min
    
    # 显示Z轴方向上的居中情况
    z_center = (z_min + z_max) / 2
    z_offset = z_center - slice_idx
    print(f"Z轴信息: 目标层面={slice_idx}, 立方体范围={z_min}-{z_max}, 立方体中心={z_center:.1f}, 偏移量={z_offset:.1f}")
    
    # Calculate the target size (should be the same in all dimensions for a cube)
    target_size = max(x_size, y_size)
    
    # 计算目标Z轴大小（不论是否使用z_range）
    target_z_size = int(max_diameter_pixels) if max_diameter_mm is None else int(z_slices_needed)
    
    # If z-dimension is constrained by image boundaries, note this in output
    if z_range is None and z_size < target_z_size:
        print(f"Note: Z-dimension is constrained by image boundaries ({z_size} vs {target_z_size})")
    
    # Ensure the bounds are valid
    if x_max <= x_min or y_max <= y_min or z_max <= z_min:
        print(f"Warning: Invalid bounds x: {x_min}-{x_max}, y: {y_min}-{y_max}, z: {z_min}-{z_max}")
        # Handle the invalid bounds by using default values
        x_min, x_max = 0, min(int(radius * 2), image_shape[0])
        y_min, y_max = 0, min(int(radius * 2), image_shape[1])
        z_min, z_max = 0, min(int(target_z_size), image_shape[2])
    
    # Create the cubic mask
    print(f"Final cube mask bounds: x: {x_min}-{x_max} ({x_max-x_min}), y: {y_min}-{y_max} ({y_max-y_min}), z: {z_min}-{z_max} ({z_max-z_min})")
    
    # 创建并填充mask的核心部分
    try:
        print(f"填充mask: [{x_min}:{x_max}, {y_min}:{y_max}, {z_min}:{z_max}]")
        
        # 最简单的情况 - 标准的(x,y,z)索引顺序
        mask[x_min:x_max, y_min:y_max, z_min:z_max] = 1
        
        # 如果mask为空，尝试其他常见的医学图像维度排序
        if np.sum(mask) == 0:
            print("警告: Mask为空，尝试替代维度排序方式...")
            
            # 尝试常见的医学图像维度排序
            # 方式1: (z,y,x)
            mask1 = np.zeros(image_shape, dtype=np.uint8)
            mask1[z_min:z_max, y_min:y_max, x_min:x_max] = 1
            
            # 方式2: (y,x,z) 
            mask2 = np.zeros(image_shape, dtype=np.uint8)
            mask2[y_min:y_max, x_min:x_max, z_min:z_max] = 1
            
            # 方式3: (x,z,y)
            mask3 = np.zeros(image_shape, dtype=np.uint8)
            mask3[x_min:x_max, z_min:z_max, y_min:y_max] = 1
            
            # 检查哪种方式产生非空mask
            masks = [mask1, mask2, mask3]
            mask_sums = [np.sum(m) for m in masks]
            
            if any(mask_sums):
                max_idx = np.argmax(mask_sums)
                mask = masks[max_idx]
                dim_orders = ["(z,y,x)", "(y,x,z)", "(x,z,y)"]
                print(f"使用维度排序: {dim_orders[max_idx]}，非零体素数: {mask_sums[max_idx]}")
            else:
                print("所有替代维度排序都生成了空mask，回退到默认排序。")
    except IndexError as e:
        print(f"警告: 创建mask时索引错误: {e}")
        # 创建一个简单的mask作为备选
        center_x = image_shape[0] // 2
        center_y = image_shape[1] // 2
        r = min(20, min(image_shape[0], image_shape[1]) // 4)
        z_size = min(int(target_z_size), image_shape[2]) if z_range is None else z_max - z_min
        
        # 确保目标检测层面在mask内部
        z_min = max(0, slice_idx - z_size // 2) if z_range is None else z_min
        z_max = min(image_shape[2], z_min + z_size) if z_range is None else z_max
        
        # 再次检查并调整确保slice_idx在范围内
        if slice_idx < z_min:
            z_min = max(0, slice_idx - 1)
        if slice_idx >= z_max:
            z_max = min(image_shape[2], slice_idx + 1)
            
        try:
            mask[center_x-r:center_x+r, center_y-r:center_y+r, z_min:z_max] = 1
            print(f"创建了备选mask: 中心=({center_x},{center_y}), 半径={r}, z范围=[{z_min}:{z_max}]")
        except IndexError:
            print(f"创建备选mask也失败，创建一个极小的mask作为最后的选择")
            mask[center_x, center_y, slice_idx] = 1
    
    print(f"Cubic mask created with {np.sum(mask)} voxels set to 1")
    return mask

def detect_lesion_slice(img_data, detection):
    """
    通过分析图像特征自动检测最可能的病灶层面
    
    参数:
    - img_data: 3D图像数据
    - detection: 检测框信息 (字典或列表)
    
    返回:
    - 最可能的病灶层面索引
    """
    # 提取检测框坐标
    if isinstance(detection, list) or isinstance(detection, tuple):
        x1, y1, x2, y2 = int(detection[0]), int(detection[1]), int(detection[2]), int(detection[3])
    else:  # 假设是字典
        x1, y1, x2, y2 = int(detection['box_x1']), int(detection['box_y1']), int(detection['box_x2']), int(detection['box_y2'])
    
    # 确保边界有效
    x1 = max(0, x1)
    y1 = max(0, y1)
    x2 = min(img_data.shape[0], x2)
    y2 = min(img_data.shape[1], y2)
    
    # 如果边界框无效，返回中间层面
    if x1 >= x2 or y1 >= y2:
        print("警告: 检测框无效，使用中间层面")
        return img_data.shape[2] // 2
    
    # 计算每个Z层面的特征
    slice_features = []
    for z in range(img_data.shape[2]):
        # 提取边界框区域
        region = img_data[x1:x2, y1:y2, z]
        
        # 计算区域特征
        if np.all(region == 0):
            # 如果区域全为0，分配最低分数
            score = 0
        else:
            # 特征1: 区域对比度 (最大值 - 最小值)
            contrast = np.max(region) - np.min(region)
            
            # 特征2: 区域方差 (像素值分布的宽度)
            variance = np.var(region)
            
            # 特征3: 梯度强度 (边缘强度，高值表示清晰边界)
            gx = np.gradient(region, axis=0)
            gy = np.gradient(region, axis=1)
            gradient_magnitude = np.sqrt(gx**2 + gy**2)
            mean_gradient = np.mean(gradient_magnitude)
            
            # 特征4: 区域与周围区域的对比
            # 如果可能，获取略大的区域进行对比
            try:
                # 稍微扩大区域
                x1_ext = max(0, x1 - 5)
                y1_ext = max(0, y1 - 5)
                x2_ext = min(img_data.shape[0], x2 + 5)
                y2_ext = min(img_data.shape[1], y2 + 5)
                
                # 周围区域 (扩展区域减去原始区域)
                surrounding = img_data[x1_ext:x2_ext, y1_ext:y2_ext, z].copy()
                # 创建掩码，将原始区域设为0
                mask = np.ones_like(surrounding)
                # 计算原始区域在扩展区域中的偏移
                off_x = x1 - x1_ext
                off_y = y1 - y1_ext
                # 确保偏移是非负的
                if off_x >= 0 and off_y >= 0:
                    mask[off_x:off_x+(x2-x1), off_y:off_y+(y2-y1)] = 0
                    # 计算周围区域的平均值
                    if np.sum(mask) > 0:  # 确保有非零元素
                        surrounding_avg = np.mean(surrounding[mask == 1])
                        region_avg = np.mean(region)
                        # 区域与周围的对比
                        region_contrast = abs(region_avg - surrounding_avg)
                    else:
                        region_contrast = 0
                else:
                    region_contrast = 0
            except Exception as e:
                print(f"计算区域对比度时出错: {e}")
                region_contrast = 0
            
            # 组合所有特征得到最终分数
            # 可以根据实际情况调整权重
            score = contrast * 0.3 + variance * 0.3 + mean_gradient * 0.2 + region_contrast * 0.2
        
        slice_features.append((z, score))
    
    # 按分数排序，找出分数最高的层面
    slice_features.sort(key=lambda x: x[1], reverse=True)
    
    # 打印前三高分的层面供参考
    print("可能的病灶层面 (层面索引, 分数):")
    for i in range(min(3, len(slice_features))):
        print(f"  #{i+1}: {slice_features[i]}")
    
    # 返回分数最高的层面
    best_slice = slice_features[0][0]
    print(f"选择层面 {best_slice} 作为病灶中心，总层数: {img_data.shape[2]}")
    return best_slice

def process_images_and_create_masks(nii_folder, detections, output_folder, use_cubic_mask=True, max_slice_indices=None, 
                                 auto_detect=False, full_volume=False, z_margin=10, adaptive=False, z_scale=1.5, full_volume_fixed=False):
    """
    处理NII图像文件并根据检测结果创建掩码
    
    参数:
    - nii_folder: NII图像文件夹路径
    - detections: 包含检测坐标的字典
    - output_folder: 输出掩码的文件夹路径
    - use_cubic_mask: 是否使用立方体掩码
    - max_slice_indices: 最大层面索引字典（图像名称到最大层面索引的映射）
    - auto_detect: 是否使用自动检测层面的方法
    - full_volume: 是否使用完整体积 (中心层面上下扩展)
    - z_margin: 当使用z轴边缘时，在当前层面上下各扩展的层数
    - adaptive: 是否使用自适应方法确定z轴范围
    - z_scale: 自适应方法中使用的缩放因子
    - full_volume_fixed: 是否使用固定的完整体积 (从0到最大层数)
    
    返回:
    - masks_created: 创建的掩码数量
    """
    
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取所有NII文件
    nii_files = glob.glob(os.path.join(nii_folder, "*.nii.gz"))
    
    # 对文件进行排序
    nii_files.sort()
    
    masks_created = 0
    
    print(f"使用参数: full_volume={full_volume}, full_volume_fixed={full_volume_fixed}, z_margin={z_margin}, auto_detect={auto_detect}, adaptive={adaptive}, z_scale={z_scale}")
    
    # 处理每个NII文件
    for nii_file in nii_files:
        print(f"Processing {os.path.basename(nii_file)}...")
        
        # 获取基础名称
        base_name = os.path.basename(nii_file).split('.')[0]
        nii_base = base_name.split('-')[0]  # 假设命名格式为 "xxx-ap.nii.gz"，我们只需要 "xxx" 部分
        
        # 检查是否有对应的检测结果
        if nii_base in detections and detections[nii_base]:
            # 加载NII图像
            img = nib.load(nii_file)
            img_data = img.get_fdata()
            
            # 创建掩码数据，与原始图像形状相同，但类型为uint8
            mask_data = np.zeros_like(img_data, dtype=np.uint8)
            
            # 打印检测结果的格式
            print(f"检测结果格式: {type(detections[nii_base])}")
            print(f"第一个检测框: {detections[nii_base][0]}")
            
            # 如果启用了自动检测，则尝试检测最可能的病灶层面
            center_slice = None
            if max_slice_indices and nii_base in max_slice_indices:
                center_slice = max_slice_indices[nii_base]
                print(f"使用预定义的中心层面: {center_slice}")
            elif auto_detect:
                print("使用自动检测方法找到最佳层面")
                # 使用第一个检测框来检测最佳层面
                first_detection = detections[nii_base][0]
                center_slice = detect_lesion_slice(img_data, first_detection)
                print(f"自动检测到的最佳层面: {center_slice}")
            else:
                # 默认使用中间层面
                center_slice = img_data.shape[2] // 2
                print(f"使用默认中间层面: {center_slice}")
            
            # 处理每个检测框
            for det in detections[nii_base]:
                # 检查检测框是列表还是字典
                if isinstance(det, list) or isinstance(det, tuple):
                    x1, y1, x2, y2 = int(det[0]), int(det[1]), int(det[2]), int(det[3])
                else:  # 假设是字典
                    x1, y1, x2, y2 = int(det['box_x1']), int(det['box_y1']), int(det['box_x2']), int(det['box_y2'])
                
                if full_volume_fixed:
                    # 使用完整的体积范围，从0到最大层数
                    print(f"使用固定的完整体积方法，不考虑中心层面")
                    z_min = 0
                    z_max = img_data.shape[2]
                    print(f"完整Z轴范围: {z_min}-{z_max} (共{z_max-z_min}层)")
                elif full_volume:
                    # 使用检测到的层面进行上下扩展
                    print(f"使用中心扩展方法，根据中心层面上下各扩展{z_margin}层")
                    z_min = max(0, center_slice - z_margin)
                    z_max = min(img_data.shape[2], center_slice + z_margin)
                    print(f"扩展后的Z轴范围: {z_min}-{z_max} (共{z_max-z_min}层)")
                elif adaptive:
                    # 自适应方法：根据检测框的大小确定z轴范围
                    print(f"使用自适应方法，z_scale={z_scale}")
                    box_size = max(x2 - x1, y2 - y1)
                    # 获取像素间距和层厚
                    pixel_spacing = get_pixel_spacing(img)
                    slice_thickness = get_slice_thickness(img)
                    
                    if pixel_spacing and slice_thickness:
                        # 如果能获取到像素间距和层厚，使用物理尺寸计算
                        physical_box_size = box_size * pixel_spacing
                        physical_z_range = physical_box_size * z_scale
                        z_range_in_slices = int(physical_z_range / slice_thickness)
                        print(f"物理盒子大小: {physical_box_size}mm, 物理Z范围: {physical_z_range}mm, Z范围(层数): {z_range_in_slices}")
                    else:
                        # 否则直接使用层数
                        z_range_in_slices = int(box_size * z_scale / 2)
                        print(f"未找到物理信息，直接使用层数: 盒子大小(像素): {box_size}, Z范围(层数): {z_range_in_slices}")
                    
                    z_min = max(0, center_slice - z_range_in_slices)
                    z_max = min(img_data.shape[2], center_slice + z_range_in_slices)
                    print(f"自适应Z轴范围: {z_min}-{z_max} (共{z_max-z_min}层)")
                else:
                    # 使用指定层面作为中心点，上下各扩展z_margin层
                    print(f"使用固定扩展方法，中心层面{center_slice}，上下各扩展{z_margin}层")
                    z_min = max(0, center_slice - z_margin)
                    z_max = min(img_data.shape[2], center_slice + z_margin)
                    print(f"固定扩展Z轴范围: {z_min}-{z_max} (共{z_max-z_min}层)")
                
                # 计算新的边界以确保立方体形状（如果启用）
                if use_cubic_mask:
                    box_width = x2 - x1
                    box_height = y2 - y1
                    box_size = max(box_width, box_height)
                    
                    # 重新计算边界以获得正方形
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    x1_new = center_x - box_size // 2
                    y1_new = center_y - box_size // 2
                    x2_new = x1_new + box_size
                    y2_new = y1_new + box_size
                    
                    # 确保边界在图像范围内
                    x1 = max(0, x1_new)
                    y1 = max(0, y1_new)
                    x2 = min(img_data.shape[0], x2_new)
                    y2 = min(img_data.shape[1], y2_new)
                    
                    print(f"立方体mask边界: x: {x1}-{x2} ({x2-x1}), y: {y1}-{y2} ({y2-y1}), z: {z_min}-{z_max} ({z_max-z_min})")
                
                # 填充掩码
                print(f"填充mask: [{x1}:{x2}, {y1}:{y2}, {z_min}:{z_max}]")
                mask_data[x1:x2, y1:y2, z_min:z_max] = 1
            
            # 创建NIFTI图像
            mask_img = nib.Nifti1Image(mask_data, img.affine, img.header)
            
            # 调整仿射变换，使目标层面在显示时位于体积的中心
            if center_slice is not None:
                slice_center = img_data.shape[2] / 2
                slice_offset = center_slice - slice_center
                print(f"已调整仿射变换，使目标层面 {center_slice} 在显示时位于体积中心")
                print(f"Z方向偏移: {slice_offset:.2f} 层，物理空间偏移: {slice_offset * get_slice_thickness(img):.2f} mm")
                
                # 打印体素尺寸信息
                pixdim = img.header.get_zooms()  # 获取体素尺寸 (x, y, z)
                print(f"原始体素尺寸: {pixdim}")
                mask_pixdim = mask_img.header.get_zooms()
                print(f"掩码体素尺寸: {mask_pixdim}")
                
                # 获取Z轴中心点信息
                z_center = (z_min + z_max) / 2
                print(f"Z轴实际范围: {z_min}-{z_max-1}, 中心: {z_center}, 偏离目标层面: {z_center-center_slice}层")
                
                # 添加目标层面信息到NIFTI头部
                mask_img.header['descrip'] = f'Target slice: {center_slice}'
                print(f"已将目标层面信息添加到NIFTI头部")
            
            # 保存完整的3D掩码
            output_path = os.path.join(output_folder, f"{base_name}_mask.nii.gz")
            nib.save(mask_img, output_path)
            print(f"Saved mask to {output_path} with {np.sum(mask_data > 0)} non-zero voxels")
            
            masks_created += 1
        else:
            print(f"No detections found for base name '{nii_base}' - skipping mask creation")
    
    print(f"\nProcessing complete! Processed {len(nii_files)} files and created {masks_created} masks.")
    return masks_created

def main(use_cubic_mask=True, max_slice_indices=None, auto_detect=False, full_volume=False, z_margin=10, adaptive=False, z_scale=1.5):
    """
    主函数，处理所有必要的步骤来生成掩码
    
    参数:
    - use_cubic_mask: 是否使用立方体掩码
    - max_slice_indices: 最大层面索引字典（图像名称到最大层面索引的映射）
    - auto_detect: 是否使用自动检测层面的方法
    - full_volume: 是否使用完整体积 (整个Z轴方向)
    - z_margin: 当使用z轴边缘时，在当前层面上下各扩展的层数
    - adaptive: 是否使用自适应方法确定z轴范围
    - z_scale: 自适应方法中使用的缩放因子
    """
    print("*" * 80)
    print("开始处理2D检测框生成3D掩码")
    print("*" * 80)
    
    # 参数说明
    if full_volume:
        print(f"生成完整体积掩码，将边界框扩展到整个Z轴方向，上下各额外扩展{z_margin}层")
    elif adaptive:
        print(f"使用自适应方法确定Z轴范围，缩放因子为{z_scale}")
    elif auto_detect:
        print("使用自动检测方法找到最佳层面")
    else:
        print(f"使用标准方法，当前层面上下各扩展{z_margin}层")
    
    # 检测数据文件
    excel_file = r"F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\2.pytorch深度学习和R代码总结版\深度学习代码总结最新版\2d目标检测\prediction_results.xlsx"
    
    # 确认Excel文件存在
    if not os.path.exists(excel_file):
        print(f"错误: 无法找到Excel文件: {excel_file}")
        return
    
    # 读取检测坐标
    detections = read_detection_coordinates(excel_file)
    print(f"从Excel文件读取了{len(detections)}个基础名称的检测结果")
    
    # 图像和输出路径
    nii_folder = r"H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\598HCC\image\ap"
    output_folder = r"H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\598HCC\mask_output"
    
    # 处理图像并创建掩码
    masks_created = process_images_and_create_masks(
        nii_folder, 
        detections, 
        output_folder, 
        use_cubic_mask=use_cubic_mask,
        max_slice_indices=max_slice_indices,
        auto_detect=auto_detect,
        full_volume=full_volume,
        z_margin=z_margin,
        adaptive=adaptive,
        z_scale=z_scale
    )
    
    print("处理完成!")
    return masks_created

def get_pixel_spacing(img):
    """
    获取像素间距 (mm)
    """
    try:
        pixdim = img.header.get_zooms()
        return (pixdim[0] + pixdim[1]) / 2  # 使用xy平面的平均像素尺寸
    except Exception as e:
        print(f"无法获取像素间距: {e}")
        return 1.0  # 默认值

def get_slice_thickness(img):
    """
    获取层厚 (mm)
    """
    try:
        pixdim = img.header.get_zooms()
        return pixdim[2]  # z方向的层厚
    except Exception as e:
        print(f"无法获取层厚: {e}")
        return 1.0  # 默认值

if __name__ == "__main__":    
    main(use_cubic_mask=True, full_volume=True, z_margin=30) 