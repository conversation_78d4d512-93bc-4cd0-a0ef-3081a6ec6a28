# ==================== R语言火山图绘制代码 ====================
# 火山图用于展示差异表达分析结果，显示fold change和统计显著性的关系
# 作者：AI助手
# 日期：2024

# 清理环境
rm(list = ls())

# 加载必要的包
if (!require("ggplot2")) install.packages("ggplot2")
if (!require("dplyr")) install.packages("dplyr")
if (!require("ggrepel")) install.packages("ggrepel")
if (!require("RColorBrewer")) install.packages("RColorBrewer")

library(ggplot2)
library(dplyr)
library(ggrepel)
library(RColorBrewer)

# ==================== 生成示例数据 ====================
set.seed(123)  # 设置随机种子以确保结果可重现

# 生成基因名称
n_genes <- 1000
gene_names <- paste0("Gene_", sprintf("%04d", 1:n_genes))

# 生成log2 fold change数据
log2FC <- rnorm(n_genes, mean = 0, sd = 1.5)

# 生成p值数据
p_values <- runif(n_genes, min = 0.001, max = 0.5)

# 为一些基因设置更显著的差异
significant_up <- sample(1:n_genes, 50)
significant_down <- sample(1:n_genes, 50)

log2FC[significant_up] <- log2FC[significant_up] + runif(50, 2, 4)
log2FC[significant_down] <- log2FC[significant_down] - runif(50, 2, 4)
p_values[c(significant_up, significant_down)] <- runif(100, 0.0001, 0.01)

# 计算-log10(p值)
neg_log10_p <- -log10(p_values)

# 创建数据框
volcano_data <- data.frame(
  Gene = gene_names,
  log2FC = log2FC,
  p_value = p_values,
  neg_log10_p = neg_log10_p,
  stringsAsFactors = FALSE
)

# ==================== 设置阈值和分类 ====================
# 设置显著性阈值
fc_threshold <- 1.0      # log2 fold change阈值
p_threshold <- 0.05      # p值阈值
neg_log10_p_threshold <- -log10(p_threshold)  # -log10(p值)阈值

# 根据阈值对基因进行分类
volcano_data$significance <- "Not Significant"
volcano_data$significance[volcano_data$log2FC > fc_threshold & volcano_data$p_value < p_threshold] <- "Up-regulated"
volcano_data$significance[volcano_data$log2FC < -fc_threshold & volcano_data$p_value < p_threshold] <- "Down-regulated"

# 转换为因子并设置顺序
volcano_data$significance <- factor(volcano_data$significance, 
                                   levels = c("Up-regulated", "Down-regulated", "Not Significant"))

# ==================== 基础火山图 ====================
basic_volcano <- ggplot(volcano_data, aes(x = log2FC, y = neg_log10_p)) +
  geom_point(aes(color = significance), alpha = 0.6, size = 1.5) +
  scale_color_manual(values = c("Up-regulated" = "#FF6B6B", 
                               "Down-regulated" = "#4ECDC4", 
                               "Not Significant" = "#95A5A6")) +
  geom_vline(xintercept = c(-fc_threshold, fc_threshold), 
             linetype = "dashed", color = "gray50", alpha = 0.8) +
  geom_hline(yintercept = neg_log10_p_threshold, 
             linetype = "dashed", color = "gray50", alpha = 0.8) +
  labs(
    title = "基础火山图",
    subtitle = paste0("阈值: |log2FC| > ", fc_threshold, ", p < ", p_threshold),
    x = "log2(Fold Change)",
    y = "-log10(p-value)",
    color = "显著性"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    legend.position = "bottom"
  )

print(basic_volcano)

# ==================== 增强版火山图 ====================
# 选择要标注的基因（最显著的上调和下调基因）
top_up <- volcano_data %>% 
  filter(significance == "Up-regulated") %>% 
  arrange(desc(neg_log10_p)) %>% 
  head(5)

top_down <- volcano_data %>% 
  filter(significance == "Down-regulated") %>% 
  arrange(desc(neg_log10_p)) %>% 
  head(5)

genes_to_label <- rbind(top_up, top_down)

enhanced_volcano <- ggplot(volcano_data, aes(x = log2FC, y = neg_log10_p)) +
  geom_point(aes(color = significance, size = significance), alpha = 0.7) +
  scale_color_manual(values = c("Up-regulated" = "#E74C3C", 
                               "Down-regulated" = "#3498DB", 
                               "Not Significant" = "#BDC3C7")) +
  scale_size_manual(values = c("Up-regulated" = 2, 
                              "Down-regulated" = 2, 
                              "Not Significant" = 1)) +
  geom_vline(xintercept = c(-fc_threshold, fc_threshold), 
             linetype = "dashed", color = "gray30", alpha = 0.8, size = 0.8) +
  geom_hline(yintercept = neg_log10_p_threshold, 
             linetype = "dashed", color = "gray30", alpha = 0.8, size = 0.8) +
  geom_text_repel(data = genes_to_label, 
                  aes(label = Gene), 
                  size = 3, 
                  box.padding = 0.5,
                  point.padding = 0.3,
                  segment.color = "gray50",
                  max.overlaps = 20) +
  labs(
    title = "增强版火山图",
    subtitle = paste0("显著差异基因: 上调 ", sum(volcano_data$significance == "Up-regulated"), 
                     " 个, 下调 ", sum(volcano_data$significance == "Down-regulated"), " 个"),
    x = "log2(Fold Change)",
    y = "-log10(p-value)",
    color = "显著性",
    size = "显著性"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    legend.position = "bottom",
    legend.title = element_text(size = 11, face = "bold"),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", fill = NA, size = 1)
  ) +
  guides(size = "none")  # 隐藏size图例

print(enhanced_volcano)

# ==================== 自定义主题火山图 ====================
custom_volcano <- ggplot(volcano_data, aes(x = log2FC, y = neg_log10_p)) +
  geom_point(aes(color = significance), alpha = 0.8, size = 1.8) +
  scale_color_manual(
    values = c("Up-regulated" = "#FF4757", 
               "Down-regulated" = "#2ED573", 
               "Not Significant" = "#747D8C"),
    labels = c("上调基因", "下调基因", "无显著差异")
  ) +
  geom_vline(xintercept = c(-fc_threshold, fc_threshold), 
             linetype = "solid", color = "#2C3E50", alpha = 0.6, size = 0.8) +
  geom_hline(yintercept = neg_log10_p_threshold, 
             linetype = "solid", color = "#2C3E50", alpha = 0.6, size = 0.8) +
  annotate("text", x = fc_threshold + 0.2, y = max(volcano_data$neg_log10_p) * 0.95, 
           label = paste("FC >", 2^fc_threshold), size = 3.5, color = "#2C3E50") +
  annotate("text", x = -fc_threshold - 0.2, y = max(volcano_data$neg_log10_p) * 0.95, 
           label = paste("FC <", round(1/2^fc_threshold, 2)), size = 3.5, color = "#2C3E50") +
  annotate("text", x = max(volcano_data$log2FC) * 0.8, y = neg_log10_p_threshold + 0.2, 
           label = paste("p <", p_threshold), size = 3.5, color = "#2C3E50") +
  labs(
    title = "差异表达基因火山图",
    subtitle = "基于RNA-seq数据的差异分析结果",
    x = "log₂(Fold Change)",
    y = "-log₁₀(p-value)",
    color = "基因类型",
    caption = "数据来源: 模拟RNA-seq差异表达分析"
  ) +
  theme_classic() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 18, face = "bold", color = "#2C3E50"),
    plot.subtitle = element_text(hjust = 0.5, size = 14, color = "#34495E"),
    plot.caption = element_text(hjust = 1, size = 10, color = "#7F8C8D"),
    axis.title = element_text(size = 14, face = "bold", color = "#2C3E50"),
    axis.text = element_text(size = 12, color = "#2C3E50"),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    panel.grid.major = element_line(color = "#ECF0F1", size = 0.5),
    panel.grid.minor = element_blank()
  )

print(custom_volcano)

# ==================== 统计信息 ====================
cat("\n==================== 火山图统计信息 ====================\n")
cat("总基因数:", nrow(volcano_data), "\n")
cat("上调基因数:", sum(volcano_data$significance == "Up-regulated"), "\n")
cat("下调基因数:", sum(volcano_data$significance == "Down-regulated"), "\n")
cat("无显著差异基因数:", sum(volcano_data$significance == "Not Significant"), "\n")
cat("显著差异基因比例:", round((sum(volcano_data$significance != "Not Significant") / nrow(volcano_data)) * 100, 2), "%\n")

# 显示最显著的基因
cat("\n最显著的上调基因:\n")
print(top_up[, c("Gene", "log2FC", "p_value")])

cat("\n最显著的下调基因:\n")
print(top_down[, c("Gene", "log2FC", "p_value")])

# ==================== 保存图片 ====================
# 创建输出目录
if (!dir.exists("volcano_plots")) {
  dir.create("volcano_plots")
}

# 保存图片
ggsave("volcano_plots/basic_volcano_plot.png", basic_volcano, 
       width = 10, height = 8, dpi = 300)
ggsave("volcano_plots/enhanced_volcano_plot.png", enhanced_volcano, 
       width = 12, height = 10, dpi = 300)
ggsave("volcano_plots/custom_volcano_plot.png", custom_volcano, 
       width = 12, height = 10, dpi = 300)

# 保存数据
write.csv(volcano_data, "volcano_plots/volcano_data.csv", row.names = FALSE)

cat("\n图片和数据已保存到 volcano_plots/ 目录\n")
cat("程序运行完成！\n")
