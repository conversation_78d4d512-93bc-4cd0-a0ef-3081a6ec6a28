##安装gpu版本的torch：根据pytorch官网  https://pytorch.org/  的提示进行安装，安装前需要卸载之前的cpu版本的torch
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# #!/usr/bin/env python
# coding: utf-8
##
import pandas as pd
from torch.utils import data
from torchvision.datasets import ImageFolder
import torch
print(torch.__version__)
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib   #pycharm中要加这行代码，否则不能出图
matplotlib.use('TkAgg') #pycharm中要加这行代码，否则不能出图
import matplotlib.pyplot as plt
#get_ipython().run_line_magic('matplotlib', 'inline')
from PIL import Image
import torchvision
from torchvision import transforms
import os
from tqdm import tqdm
import shutil
import pandas as pd
from sklearn.metrics import precision_recall_curve, average_precision_score
from sklearn.metrics import roc_curve, auc
from sklearn.metrics import confusion_matrix
from scipy import interp
from itertools import cycle
import itertools
import random
##
def setup_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
# 设置随机数种子
setup_seed(20)

##方法1 根据文件夹中的分类输入，文件夹中要有分类文件夹
# base_dir = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\tumor\jpg\hbp'
# # base_dir = r'G:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\2.卷积部分(第8-11章)参考代码和数据集\datasets\4weather-train-test'
# # base_dir =r'E:\3D Slicer\datasets\VETC-suzhou\APDP'
# # base_dir = r'E:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\2.卷积部分(第8-11章)参考代码和数据集\datasets\EGFR'
#
# train_dir = os.path.join(base_dir , 'train')
# test_dir = os.path.join(base_dir , 'validation')

# train_dir =r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\tumor\jpg\ap'
# test_dir = r'H:\1.HCC-VETC\datasets\HCC-nantong\all-HCC\tumor\jpg\ap'
#%%
##方法2  根据路径和标签输入,glob方法获取路径的list，列表推导式获得标签的list
import glob

train_dir = '/root/autodl-tmp/pp/train/*/*/*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\train\*\*\*.jpg'
test_dir = '/root/autodl-tmp/pp/validation/*/*/*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\validation\*\*\*.jpg'
train_paths = glob.glob(train_dir)
test_paths = glob.glob(test_dir)

train_paths[:5]
test_paths[-5:]
len(train_paths)
len(test_paths)

img_p = train_paths[100]
img_p
int(img_p.split('/')[-3]) #把切出来的字符型转为int

# train_labels = [int(img_p.split('\\')[-3]) for img_p in train_paths]
train_labels = [int(img_p.split('/')[-3]) for img_p in train_paths]
np.unique(train_labels)
train_labels[:5]

# test_labels = [int(img_p.split('\\')[-3]) for img_p in test_paths]
test_labels = [int(img_p.split('/')[-3]) for img_p in test_paths]
np.unique(test_labels)
test_labels[-5:]

# label_to_index = dict((v, k) for k, v in enumerate(np.unique(train_labels)))
# label_to_index
# index_to_label = dict((v, k) for k, v in label_to_index.items())
# index_to_label
# all_labels = [label_to_index.get(la) for la in label_names]
# all_labels[:5]

#%% 方法3



def get_labels(excel_file, folder):
    """
    读取Excel表格中的VETC值，并将VETC值与文件夹名相匹配，然后将VETC值保存到标签中。

    Args:
        excel_file (str): Excel表格的路径。
        folder (str): 文件夹的路径。

    Returns:
        dict: 包含图片路径和对应VETC标签的字典。
    """

    # 读取Excel表格
    data = pd.read_excel(excel_file)

    # 创建字典来存储图片的VETC标签
    labels = {}

    # 遍历文件夹下的文件
    for subfolder in os.listdir(folder):
        subfolder_path = os.path.join(folder, subfolder)
        # 遍历子文件夹下的图片
        for filename in os.listdir(subfolder_path):
            if filename.endswith('.jpg'):
                file_path = os.path.join(subfolder_path, filename)
                file_name_without_extension = subfolder.split('-')[0]
                # 查找对应的name和VETC标签
                matching_row = data[data['name'] == file_name_without_extension]
                if not matching_row.empty:
                    name = matching_row.iloc[0]['name']
                    vetc_label = matching_row.iloc[0]['VETC']
                    # 将图片路径与对应的VETC标签存储在字典中
                    labels[file_path] = vetc_label

    return labels



# ## 读取训练集Excel表格   这种方法要求表格中name和患者文件夹名前面的名字要一致
# train_data = pd.read_excel(r'K:\2020-2023HCC\579hcc\clinical data\data\train.xlsx')
#
# # 创建字典来存储图片的VETC标签
# train_labels = {}
#
# # 遍历ap文件夹下的文件
# train_folder = r'H:\1.HCC-VETC\datasets\paper-data\jpg\ap\train'
#
# for subfolder in os.listdir(train_folder):
#     subfolder_path = os.path.join(train_folder, subfolder)
#     # 遍历子文件夹下的图片
#     for filename in os.listdir(subfolder_path):
#         if filename.endswith('.jpg'):
#             file_path = os.path.join(subfolder_path, filename)
#             file_name_without_extension = subfolder.split('-')[0] # 患者文件夹名格式如 caimeifang-ap
#             # 查找对应的name和VETC标签
#             matching_row = train_data[train_data['name'] == file_name_without_extension]
#             if not matching_row.empty:
#                 name = matching_row.iloc[0]['name']
#                 vetc_label = matching_row.iloc[0]['VETC']
#                 # 将图片路径与对应的VETC标签存储在字典中
#                 train_labels[file_path] = vetc_label
#
# # 打印图片路径与对应的VETC标签
# for train_path, train_label in train_labels.items():
#     print(f"图片路径：{train_path}，VETC：{train_label}")
#
# train_paths = list(train_labels.keys())
# train_paths[:5]
# train_labels = list(train_labels.values())
# train_labels[:5]
# len(train_paths)
# len(train_labels)
#
# ## 读取测试集Excel表格
# test_data = pd.read_excel(r'H:\1.HCC-VETC\datasets\clinical data\data\validation_set.xlsx')
#
# # 创建字典来存储图片的VETC标签
# test_labels = {}
#
# # 遍历ap文件夹下的文件
# test_folder = r'H:\1.HCC-VETC\datasets\paper-data\jpg\ap\validation'  # 将"train"替换为"test"
#
# for subfolder in os.listdir(test_folder):  # 将"train_folder"替换为"test_folder"
#     subfolder_path = os.path.join(test_folder, subfolder)  # 将"train_folder"替换为"test_folder"
#     # 遍历子文件夹下的图片
#     for filename in os.listdir(subfolder_path):
#         if filename.endswith('.jpg'):
#             file_path = os.path.join(subfolder_path, filename)
#             file_name_without_extension = subfolder.split('-')[0]
#             # 查找对应的name和VETC标签
#             matching_row = test_data[test_data['name'] == file_name_without_extension]
#             if not matching_row.empty:
#                 name = matching_row.iloc[0]['name']
#                 vetc_label = matching_row.iloc[0]['VETC']
#                 # 将图片路径与对应的VETC标签存储在字典中
#                 test_labels[file_path] = vetc_label
#
# # 打印图片路径与对应的VETC标签
# for test_path, test_label in test_labels.items():
#     print(f"图片路径：{test_path}，VETC：{test_label}")
#
# test_paths = list(test_labels.keys())
# test_paths[:5]
# test_labels = list(test_labels.values())
# test_labels[:5]
# len(test_paths)
# len(test_labels)
## In[5]:

transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
])


## In[6]:# In[17]:

class Mydataset(data.Dataset):
    def __init__(self, img_paths, labels):
        self.imgs = img_paths
        self.labels = labels

    def __getitem__(self, index):
        img = self.imgs[index]
        label = self.labels[index]

        pil_img = Image.open(img)
        pil_img = pil_img.convert("RGB")  # 转为RGB模式，H,W,C=3
        img_tensor = transform(pil_img)

        return img_tensor, label

    def __len__(self):
        return len(self.imgs)


BATCH_SIZE = 32

# %%
train_ds = Mydataset(train_paths, train_labels)

# In[38]:


test_ds = Mydataset(test_paths, test_labels)

# In[39]:


train_dl = data.DataLoader(train_ds,
                           batch_size=32,
                           shuffle=True)


test_dl = data.DataLoader(test_ds,
                          batch_size=32)


imgs_batch, labels_batch = next(iter(train_dl))

imgs_batch.shape

labels_batch.shape

print(train_ds.imgs[:5])
# print(dataset[0][1])# 第一维是第几张图，第二维为1返回label
print(train_ds[0][0].size()) # 深度学习中图片数据一般保存成CxHxW，即通道数x图片高x图片宽
len(train_ds)
len(test_ds)
##不做数据增强

# # #做数据增强
# train_transform = transforms.Compose([
#     transforms.Resize(224),
#     transforms.RandomResizedCrop(192, scale=(0.6,1.0), ratio=(0.8,1.0)),
#     transforms.RandomHorizontalFlip(),
#     transforms.RandomRotation(0.2),
#     torchvision.transforms.ColorJitter(brightness=0.5, contrast=0, saturation=0, hue=0),
#     torchvision.transforms.ColorJitter(brightness=0, contrast=0.5, saturation=0, hue=0),
#     transforms.ToTensor(),
#     transforms.Normalize(mean=[.5, .5, .5], std=[.5, .5, .5])
# ])
#
#
# test_transform = transforms.Compose([
#     transforms.Resize((192, 192)),
#     transforms.ToTensor(),
#     transforms.Normalize(mean=[.5, .5, .5], std=[.5, .5, .5])
# ])




## In[10]:
# #预训练模型选择torchvision.models.vgg16,19 （mobilenet_v2，resnet18，AlexNet,DenseNet,densenet121,161,169,201;
# GoogLeNet;Inception3,inception_v3,resnet18,34,50,152;）； squeezenet1_0；mnasnet1_0;shufflenet_v2_x1_0;
import torchvision.models as models

model_names = sorted(name for name in models.__dict__
                     if name.islower() and not name.startswith("__")
                     and callable(models.__dict__[name]))

print(model_names)

model = torchvision.models.swin_v2_b(pretrained=True)
# model = torchvision.models.vit_b_32(pretrained=True)#vit模型还有vit_b_32，vit_l_16，vit_l_32，vit_h_14
# model = torchvision.models.vision_transformer.vit_b_16(pretrained=True)
# model = torchvision.models.vgg16(pretrained=True)
# model = torchvision.models.vgg19 (pretrained=True)
# model = torchvision.models.resnet50(pretrained=True)
# model = torchvision.models.resnet101(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
# model = torchvision.models.densenet161(pretrained=True)
# model = torchvision.models.googlenet(pretrained=True)
# model = torchvision.models.alexnet(pretrained=True)
# model = torchvision.models.mobilenet_v2(pretrained=True)
# model = torchvision.models.mnasnet1_0(pretrained=True)
# model = torchvision.models.mobilenet_v3_large(pretrained=True)
# model = torchvision.models.shufflenet_v2_x1_0(pretrained=True)
# model = torchvision.models.squeezenet1_0(pretrained=True)
# model = torchvision.models.efficientnet_b2(pretrained=True)
model.parameters

## In[11]:

for param in model.parameters():
    param.requires_grad = False

#数据集中分类的类别数
num_classes = 2  # num_classes表示输出特征

##resnet，googlenet,shufflenet_v2最后一层为名字为fc;把最后一层替换为新的分类，注意要修改最后一层的名字
in_f = model.fc.in_features      #in_f表示输入特征
model.fc = nn.Linear(in_features=in_f,out_features= num_classes) ##resnet,shufflenet_v2
model  #修改后的模型

# densenet121最后一层为名字为classifier
# in_f = model.classifier.in_features
# model.classifier = nn.Linear(in_features=in_f, out_features=num_classes)
# model  # 修改后的模型

# # alexnet,vgg最后一层为名字为classifier[6]
# in_f = model.classifier[6].in_features
# model.classifier[6] = nn.Linear(in_features=in_f,out_features=num_classes)  #alexnet,vgg
# model  #修改后的模型
#
# #mobilenet_v2
# in_f = model.classifier[1].in_features #mobilenet_v2
# model.classifier[1] = nn.Linear(in_features=in_f, out_features= num_classes)   #mobilenet_v2
# model  #修改后的模型
#
# # 对于squeezenet1_0,可以直接更改最后的classifier层,重新初始化Conv2d层
# model.classifier[1] = nn.Conv2d(512, num_classes, kernel_size=(1,1), stride=(1,1)) #Squeezenet
# model  #修改后的模型

## In[15]:
print(torch.cuda.is_available()) #看一下cuda是否可用

if torch.cuda.is_available():
    model.to('cuda')

loss_fn = nn.CrossEntropyLoss()

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler

optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.001)  #alexnet,vgg注意修改模型最后一层名称
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.001) #densenet模型 Squeezenet
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

##定义训练函数
def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    model.train()
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)  #计算损失
        optimizer.zero_grad()  # 梯度清零，否则会累加
        loss.backward()  # 计算梯度
        optimizer.step()  # 权重更新
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
    exp_lr_scheduler.step()  #注意这个代码有学习率，因此这里需要加一行
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / total
        
        
    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    
    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / test_total
    
        
    print('epoch: ', epoch, 
          'train_loss： ', round(epoch_loss, 3),
          'train_accuracy:', round(epoch_acc, 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
             )
        
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc


## 开始训练

epochs = 30

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)

#查看训练的设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Training on: ", device)

##绘制loss曲线
plt.plot(range(1, len(train_loss)+1), train_loss, label='train_loss')
plt.plot(range(1, len(train_loss)+1), test_loss, label='test_loss')
plt.legend()
plt.title("SqueezeNet_v1.0") #ResNet50,DenseNet121,Inception_v3,SqueezeNet_v1.0
plt.rcParams['font.size'] = 18
plt.show()
# plt.savefig(r"E:\data\6loss.tiff")

##绘制acc曲线
plt.plot(range(1, len(train_loss)+1), train_acc, label='train_acc')
plt.plot(range(1, len(train_loss)+1), test_acc, label='test_acc')
plt.legend()
plt.title("SqueezeNet_v1.0") #ResNet50,DenseNet121,Inception_v3,SqueezeNet_v1.0
plt.rcParams['font.size'] = 18
plt.show()

# plt.savefig(r"E:\data\acc6.tiff")

## 保存预测概率、最大概率、预测label、模型预测值（即深度学习特征）

import torch
import pandas as pd

def get_predictions(model, dataloader):
    model.eval()
    predict_label = []
    probability = []
    max_probability = []
    true_labels = []
    with torch.no_grad():
        for x, y in dataloader:
            if torch.cuda.is_available():
                x = x.to('cuda')
                y = y.to('cuda')
            y_pred = model(x)
            y_pred_prob = torch.softmax(y_pred, dim=1)
            max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)
            predict_label.extend(y_pred_class.tolist())
            probability.extend(y_pred_prob.tolist())
            max_probability.extend(max_prob.tolist())
            true_labels.extend(y.tolist())
    return  true_labels,predict_label,probability, max_probability

# 获取训练集和测试集的预测结果、概率和最大概率
train_true_label,train_predict_label, train_probability, train_max_probability = get_predictions(model, train_dl)
test_true_label,test_predict_label, test_probability, test_max_probability = get_predictions(model, test_dl)

# 创建DataFrame保存预测结果、概率和最大概率
train_results = pd.DataFrame({'True Labels': train_true_label,'Predict_label': train_predict_label,
                              'Probability': train_probability, 'Max Probability': train_max_probability})
test_results = pd.DataFrame({'True Labels': test_true_label,'Predict_label': test_predict_label,
                             'Probability': test_probability, 'Max Probability': test_max_probability})

# 将结果保存到Excel文件
train_results.to_excel(r'D:\dataset\train_results.xlsx', index=False)
test_results.to_excel(r'D:\dataset\test_results.xlsx', index=False)

#### 先训练自己的模型，再微调预训练模型

for param in model.parameters():  #重新训练所有可训练参数
    param.requires_grad = True

len(list(model.parameters())) 
list(model.parameters())[-4:] #表示最后4层
# list(model.parameters())[310:314] #表示最后310到313层

# for param in list(model.parameters())[310:314]:#310-314层可训练
#     param.requires_grad = True  
    
for param in list(model.parameters())[-4:]:  #最后4层重新训练
    print(param)
    param.requires_grad = True 

extend_epochs = 20

from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)                #model.parameters()为整个可训练模型参数
optimizer = torch.optim.Adam(list(model.parameters())[-4:], lr=0.0001)   # 优化器要传入解冻层的参数

exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

for epoch in range(extend_epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)

## In[25]:
plt.plot(range(1, len(train_loss)+1), train_loss, label='train_loss')
plt.plot(range(1, len(train_loss)+1), test_loss, label='test_loss')
plt.legend()

# In[26]:
plt.plot(range(1, len(train_loss)+1), train_acc, label='train_acc')
plt.plot(range(1, len(train_loss)+1), test_acc, label='test_acc')
plt.legend()

## 保存模型

# state_dict就是一个简单的Python字典，它将模型中的可训练参数（比如weights和biases，batchnorm的running_mean、
# torch.optim参数等）通过将模型每层与层的参数张量之间一一映射，实现保存、更新、变化和再存储。

PATH = r'E:\data\my_squeezenet_model.pth'
torch.save(model.state_dict(), PATH)
import torch
new_model = torch.load(PATH)
new_model
# new_model = Net() #新的空模型
# new_model.load_state_dict(torch.load(PATH))
# new_model.to('cuda')

# 可将新训练好的模型应用于测试集

test2_correct = 0
test2_total = 0
new_model.eval()
with torch.no_grad():
    for x, y in test_dl:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = new_model(x)
        y_pred = torch.argmax(y_pred, dim=1)
        test2_correct += (y_pred == y).sum().item()
        test2_total += y.size(0)

epoch_test_acc = test2_correct / test2_total
print(epoch_test_acc)

##%%导出深度学习特征
model
new_model = torch.nn.Sequential(*(list(model.children())[:-1]))  #删除最后一层后的模型，为卷积基部分
print(new_model)

#删除掉classifier的最后一层：
model.classifier=torch.nn.Sequential(*list(model.classifier.children())[:-1])
new_model=model
print(new_model)


#CPU版本提取训练集特征
# train_labels = []
# train_features = []
#
# for image,label in train_dl:
#     o = new_model(image)
#     # o = my_resnet18(im)
#     o = o.view(o.size(0),-1)
#     train_labels.extend(label)
#     train_features.extend(o.cpu().data)

#CPU版本提取测试集特征
# test_labels = []
# test_features = []
#
# for image,label in test_dl:
#     o = new_model(image)
#      # o = my_resnet18(im)
#     o = o.view(o.size(0),-1)
#     test_labels.extend(label)
#     test_features.extend(o.cpu().data)

## GPU版本提取训练集特征
train_labels = []
train_features = []
for image,label in train_dl:
    o = new_model(image.cuda()) # 放到cuda中
    o = o.view(o.size(0),-1)
    train_labels.extend(label)
    train_features.extend(o.cpu().data) # 将数据转移到CPU上

#GPU版本提取测试集特征
test_labels = []
test_features = []

for image, label in test_dl:
    o = new_model(image.cuda())
    # o = my_resnet18(im)
    o = o.view(o.size(0), -1)
    test_labels.extend(label)
    test_features.extend(o.cpu().data)

  ##%%  导出深度学习特征
train_features #tensor格式的list
# train_features = np.array(train_features) #单维度tensor转numpy
train_features= torch.tensor([item.cpu().detach().numpy() for item in train_features]) #多维度tensor转numpy
train_features  #numpy格式
train_features= train_features.tolist() #numpy格式转list
train_features #list格式
train_features = pd.DataFrame(train_features)    
train_features #数据框格式
train_features.to_csv('E:/Rcode/pythondata/trainfeature1.csv')


train_labels #tensor格式的list
train_labels = np.array(train_labels) #单维度tensor转numpy
train_labels  #numpy格式
train_labels= train_labels.tolist() #numpy格式转list
train_labels  #list格式
train_labels = pd.DataFrame(train_labels)    
train_labels #数据框格式
train_labels.to_csv('E:/Rcode/pythondata/trainlabel1.csv')


test_features #tensor格式的list
test_features= torch.tensor([item.cpu().detach().numpy() for item in test_features]) #多维度tensor转numpy
test_features  #numpy格式
test_features= test_features.tolist() #numpy格式转list
test_features #list格式
test_features = pd.DataFrame(test_features)    
test_features #数据框格式
test_features.to_csv('E:/Rcode/pythondata/testfeature1.csv')

test_labels #tensor格式的list
test_labels = np.array(test_labels) #单维度tensor转numpy
test_labels  #numpy格式
test_labels= test_labels.tolist() #numpy格式转list
test_labels  #list格式
test_labels = pd.DataFrame(test_labels)    
test_labels #数据框格式
test_labels.to_csv('E:/Rcode/pythondata/testlabel1.csv')

##总结的混淆矩阵代码,自己加的代码

#获取测试集的标签
test_loader = torch.utils.data.DataLoader(test_ds, batch_size=10000, shuffle=False)#设置好batchsize一次读完所有label
images,test_labels=next(iter(test_loader))
print(test_labels)
print(test_labels.shape)

#%%
#定义一个预测函数
def get_all_preds(model,train_dl):# model为之前已经训练好的模型，loader为对总数据的迭代器
                               
    import numpy as np
    model.eval()

    all_preds=None
    
    for batch in tqdm(train_dl):#以遍历的方式，访问所有的loader
                              #这里的batch和读取数据的batch是不一样的
        images,labels = batch #把batch拆分成两个变量
        preds = model(images)#预测一批batch里的图片

        if all_preds is None :
            all_preds=preds.cpu().detach().numpy()
        else:
            all_preds=np.concatenate((all_preds,preds.cpu().detach().numpy()))
    return all_preds

#重新设置一个loader
# test_dl = torch.utils.data.DataLoader(
#                             test_ds,
#                             batch_size=BTACH_SIZE,
# )
#得到所有数据的预测值
test_preds = get_all_preds(model, test_dl)#开始对图片做预测
test_preds #numpy格式的
print(test_preds.shape)

##得到测试集每个图片预测输出值，test_preds保存为csv格式
test_pred_value= test_preds.tolist() #numpy格式转list
test_pred_value#list格式
test_pred_value= pd.DataFrame(test_pred_value)    
test_pred_value #数据框格式
# test_pred_value.to_csv('E:/data/test_pred_value.csv')

# In[*] 得到预测的label
test_preds_tensor=torch.Tensor(test_preds)#再次将numpy转为tensor
test_preds_labels = test_preds_tensor.argmax(dim=1)#测试结果，用于和test_labels对比
test_preds_labels
len(test_preds_labels)

#保存预测的label
test_preds_labels=np.round(test_preds_labels.cpu().detach().numpy(),3)
test_preds_labels= pd.DataFrame(test_preds_labels)    
test_preds_labels#数据框格式
# test_preds_labels.to_csv('E:/data/test_preds_labels.csv')

#得到每个图片预测概率值
test_probability = torch.nn.functional.softmax(test_preds_tensor,dim=1) #预测概率
print(test_probability.shape)
test_probability=np.round(test_probability.cpu().detach().numpy(),3)
test_probability_value= pd.DataFrame(test_probability)    
test_probability_value#数据框格式
# test_probability_value.to_csv('E:/data/test_probability_value.csv')

# 计算混淆矩阵和评价指标
from sklearn.metrics import classification_report, confusion_matrix
cm = confusion_matrix(test_labels,test_preds_labels)
cm  #横向相加为真实标签值，竖向相加为预测值
print(classification_report(test_labels,test_preds_labels,digits=3))

##训练集混淆矩阵的数据结果
train_loader = torch.utils.data.DataLoader(train_ds, batch_size=10000, shuffle=False)#设置好batchsize一次读完所有label
train_images,train_labels=next(iter(train_loader))
print(train_labels)
print(train_labels.shape)

# In[*] 得到预测的label
train_preds = model(train_images) #test_preds为预测值
print(train_preds.shape)

# In[*] 得到预测的label
train_preds_tensor=torch.Tensor(train_preds)#再次将numpy转为tensor
train_preds_labels = train_preds_tensor.argmax(dim=1)#测试结果，用于和test_labels对比
train_preds_labels
len(train_preds_labels)

confusion_matrix(train_labels,train_preds_labels)
print(classification_report(train_labels,train_preds_labels,digits=3))

## In[*]绘制混淆矩阵表和矩阵图

classes=3  #定义分类类别

def plot_confusion_matrix(cm, classes, normalize= False, title='Confusion matrix', cmap=plt.cm.Blues):
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        print("Normalized confusion matrix")
    else:
        print('Confusion matrix, without normalization')

    print(cm)
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    # plt.colorbar()
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)

    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() / 2.
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        plt.text(j, i, format(cm[i, j], fmt), horizontalalignment="center", color="white" if cm[i, j] > thresh else "black")

    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label')
    
#直接画出矩阵表和矩阵图

#from resources.plotcm import plot_confusion_matrix
names = (
    'VETC+',
    'VETC++',
    'VETC-',
    # 'label0',
    # 'label1',
    # 'label2',
    # 'label3',
    )
plt.figure(figsize=(6.5,6.5))
plt.subplots_adjust(left=0.2,right=0.8,bottom=0.2,top=0.8)
plt.rcParams['font.size'] = 16
plot_confusion_matrix(cm, names)
# plt.savefig(r"F:\1.课题和论文\郁义星-课题汇总2017-2023\2017-2023国自然申报\2023国自然申报\国自然正式填写\图片\cm.tiff")


##PR 曲线(Precision-Recall Curve)：skplt.metrics.plot_precision_recall()
# # PR曲线
import scikitplot as skplt
skplt.metrics.plot_precision_recall(test_labels,test_probability_value,
                                    title='PR Curve for SqueezeNet_v1.0', #,ResNet50 Inception_v3,SqueezeNet_v1.0
                                    # classes_to_plot = ([0,'VETC++'],[1,'VETC+'],[2,'VETC-']),
                                    # classes_to_plot= ['VETC++','VETC+','VETC-'],
                                    cmap='Set1')
plt.show()
# plt.savefig(r"F:\1.课题和论文\郁义星-课题汇总2017-2023\2017-2023国自然申报\2023国自然申报\国自然正式填写\图片\pr_plot.jpg',dpi=300)

## ROC曲线：skplt.metrics.plot_roc()

skplt.metrics.plot_roc(test_labels,test_probability_value,
                       title='ROC Plot for SqueezeNet_v1.0', #Densenet121
                       # classes_to_plot=['class1', 'class2','class3'],
                       cmap='Set1') # cmap 设置调色板
plt.show()
## 校准曲线：binary classification，仅仅用于二分类变量
# 概率列表，若多个，则可设置为probas_list = [y_rfc_proba, y_xgb_proba]

# probas_list = [test_probability_value]
# # 分类器名称列表，若多个，则可设置clf_names = ['RF', 'XGB']
# clf_names = ['Densenet121']
# skplt.metrics.plot_calibration_curve(test_labels,
#                                      probas_list=probas_list, # 概率列表
#                                      clf_names=clf_names, # 分类器名称列表
#                                      cmap='Set1')
# plt.savefig('calibration.jpg', dpi=300)

#%%
##类激活热力图绘制最终版
#pip install grad-cam

from pytorch_grad_cam import GradCAM, ScoreCAM, GradCAMPlusPlus, AblationCAM, XGradCAM, EigenCAM
from pytorch_grad_cam.utils.image import show_cam_on_image, deprocess_image, preprocess_image
import cv2
import numpy as np
import torchvision
import torch
from PIL import Image
import matplotlib.pyplot as plt

# import os
# # MacOS系统应该加这行
# os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"
# 1.加载模型
# model = torchvision.models.vgg16(pretrained=True)    
model = torchvision.models.resnet50(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
model.parameters
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# 2.选择目标层
# target_layer = [model.features[-1]]  #vgg16 and densenet161
target_layer = [model.layer4[-1]]   #resnet50

'''
Resnet18 and 50: model.layer4[-1]
VGG and densenet161: model.features[-1]
mnasnet1_0: model.layers[-1]
ViT: model.blocks[-1].norm1
'''
# 3.输入图像
img_path = r'K:\xiaochanghuaisi\nii\pp\1\17.jpg'
img = Image.open(img_path).convert('RGB')
#img = img.resize((224,224))
# 一转，'x' is a float32 Numpy array of shape
img = np.array(img)
img_float_np = np.float32(img)/255  #归一化
# define the torchvision image transforms
transform = torchvision.transforms.Compose([
    torchvision.transforms.ToTensor(),
])

input_tensor = transform(img)
input_tensor = input_tensor.to(device)

# 二扩，add a dimension to transform the array into a "batch"
input_tensor = input_tensor.unsqueeze(0)

#x.shape (1,3,447,670)

# 4.初始化GradCAM，包括模型，目标层以及是否使用cuda
cam = GradCAM(model=model, target_layers=target_layer, use_cuda=False)

# 5.选定目标类别，如果不设置，则默认为分数最高的那一类
targets = None 
#targets = [ClassifierOutputTarget(280)] #第281类
# 6. 计算cam
grayscale_cam = cam(input_tensor=input_tensor, targets=targets)  
# 加上 aug_smooth=True(应用水平翻转组合，并通过[1.0，1.1，0.9]对图像进行多路复用，使CAM围绕对象居中)
# eigen_smooth=True(去除大量噪声)
# 7.展示热力图并保存, grayscale_cam是一个batch的结果，只能选择一张进行展示
grayscale_cam = grayscale_cam[0,:]
cam_image = show_cam_on_image(img_float_np, grayscale_cam, use_rgb=True) #热图和原始图重叠
# cam_image = show_cam_on_image(0, grayscale_cam, use_rgb=True) #只有热图
cv2.imwrite(f'K:/xiaochanghuaisi/nii/pp/1/17heapmap.jpg', cam_image)

#%%
##类激活热力图绘制1
# pip install grad-cam

'''
1)导入相关的包并加载模型
'''

from pytorch_grad_cam import GradCAM, ScoreCAM, GradCAMPlusPlus, AblationCAM, XGradCAM, EigenCAM
from pytorch_grad_cam.utils.image import show_cam_on_image, deprocess_image, preprocess_image
from torchvision.models import resnet50
import cv2
import numpy as np
import os

os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"

# 1.加载模型
# model = resnet50(pretrained=True) #预先训练
model = torchvision.models.densenet161(pretrained=True)
model.parameters

# 2.选择目标层
# target_layer = [model.layer4[-1]]
# target_layer = [model.layer4]
target_layer =  [model.features[-1]]
target_layer
# target_layer
'''
Resnet18 and 50: model.layer4[-1]
VGG and densenet161: model.features[-1]
mnasnet1_0: model.layers[-1]
ViT: model.blocks[-1].norm1
'''
#------------------------------
'''
2)构建输入图像的Tensor形式，使其能传送到model里面去计算
'''


image_path = 'E:/3D Slicer/datasets/VETC-suzhou/5/PP/labelimage/34.jpg'  #路径不用中文

rgb_img = cv2.imread(image_path, 1)[:, :, ::-1]   # 1是读取rgb
rgb_img                                           #imread返回从指定路径加载的图像
rgb_img = cv2.imread(image_path, 1) #imread()读取的是BGR格式
 
rgb_img = np.float32(rgb_img) / 255
rgb_img   


# preprocess_image作用：归一化图像，并转成tensor
input_tensor = preprocess_image(rgb_img, mean=[0.485, 0.456, 0.406],
                                             std=[0.229, 0.224, 0.225])   # torch.Size([1, 3, 224, 224])
# Create an input tensor image for your model..
# Note: input_tensor can be a batch tensor with several images!


#----------------------------------------
'''
3)初始化CAM对象，包括模型，目标层以及是否使用cuda等
'''
# Construct the CAM object once, and then re-use it on many images:
cam = GradCAM(model=model, target_layers=target_layer, use_cuda=False)
'''
4)选定目标类别，如果不设置，则默认为分数最高的那一类
'''
# If target_category is None, the highest scoring category
# will be used for every image in the batch.
# target_category can also be an integer, or a list of different integers
# for every image in the batch.
# target_category = None  #定义图片所属分类
#指定类：target_category = 281

'''
5)计算cam
'''
# You can also pass aug_smooth=True and eigen_smooth=True, to apply smoothing.
# grayscale_cam = cam(input_tensor=input_tensor, target_category = target_category)  # [batch, 224,224]

grayscale_cam = cam(input_tensor=input_tensor) #自己修改的代码
grayscale_cam 
#----------------------------------
'''
6)展示热力图并保存
'''
# In this example grayscale_cam has only one image in the batch:
# 7.展示热力图并保存, grayscale_cam是一个batch的结果，只能选择一张进行展示
grayscale_cam = grayscale_cam[0]

visualization = show_cam_on_image(rgb_img, grayscale_cam)  # (224, 224, 3) rgb_img  热图和原始图重叠
# visualization = show_cam_on_image(0, grayscale_cam) #只有热图，无原始图片
cv2.imwrite(f'E:/3D Slicer/nii/heatmap05.jpg', visualization)
