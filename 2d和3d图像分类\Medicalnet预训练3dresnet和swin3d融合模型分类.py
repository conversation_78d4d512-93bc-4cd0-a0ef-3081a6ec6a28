#%% MedicalNet的resnet预训练模型与Swin3D融合模型，成功了
#
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from types import SimpleNamespace
import pandas as pd
import numpy as np
import SimpleITK as sitk
from torchvision.models.video import *
import multiprocessing
import matplotlib.pyplot as plt
from datetime import datetime
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns

# 导入MONAI相关库
import monai
from monai.transforms import (
    LoadImaged, EnsureChannelFirstd, Resized, ScaleIntensityRanged, 
    RandRotate90d, Compose, EnsureTyped
)
from monai.data import DataLoader, Dataset

# 添加MedicalNet路径
# sys.path.append(r"E:\1.githubcode\MedicalNet-master")
sys.path.append(r"/root/autodl-tmp/hcc/MedicalNet")


from models import resnet
from model import generate_model

def modify_resnet_for_classification(model):
    """修改ResNet模型，移除分割层，添加分类所需的层"""
    # 1. 移除分割层
    if hasattr(model.module, 'conv_seg'):
        delattr(model.module, 'conv_seg')
    
    # 2. 修改前向传播函数
    def new_forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        
        # 全局平均池化
        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x
    
    # 3. 添加全局平均池化层
    model.module.avgpool = nn.AdaptiveAvgPool3d(1)
    
    # 4. 替换前向传播函数
    import types
    model.module.forward = types.MethodType(new_forward, model.module)
    
    return model

class MedicalNet3DClassifier(nn.Module):
    """MedicalNet的3D ResNet50分类器"""
    def __init__(self, num_classes=2, pretrained=True):
        super().__init__()
        
        # 1. 输入预处理层
        self.preprocessing = nn.Conv3d(3, 1, kernel_size=1, stride=1, padding=0, bias=False)
        
        # 2. 创建配置对象
        self.opt = SimpleNamespace(
            model='resnet',
            model_depth=50,
            input_W=224,
            input_H=224,
            input_D=16,
            resnet_shortcut='B',
            no_cuda=False,
            gpu_id=[0],
            phase='train',
            pretrain_path=None,
            n_seg_classes=1,  # 设置为1以避免创建分割层
            new_layer_names=['fc']
        )
        
        # 3. 创建基础模型
        self.backbone, _ = generate_model(self.opt)
        
        # 4. 修改模型结构
        self.backbone = modify_resnet_for_classification(self.backbone)
        
        # 5. 修改最后的全连接层用于二分类
        self.backbone.module.fc = nn.Sequential(
            nn.Linear(2048, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, num_classes)
        )
        
        # 6. 加载预训练权重
        if pretrained:
            # pretrain_path = r"E:\1.githubcode\MedicalNet-master\pretrain\resnet_50.pth"
            pretrain_path = r"/root/autodl-tmp/hcc/resnet_50.pth"
            if os.path.exists(pretrain_path):
                print(f"Loading pretrained weights from {pretrain_path}")
                weights = torch.load(pretrain_path, map_location='cpu')
                
                # 处理权重键名
                state_dict = weights['state_dict']
                new_state_dict = {}
                for k, v in state_dict.items():
                    if k.startswith('module.'):
                        k = k[7:]  # 移除 'module.' 前缀
                    if not k.startswith('conv_seg') and 'fc' not in k:  # 跳过分割层和原始fc层
                        new_state_dict[k] = v
                
                # 加载除最后一层外的权重
                model_dict = self.backbone.state_dict()
                model_dict.update({k: v for k, v in new_state_dict.items() 
                                 if k in model_dict and 'fc' not in k})
                self.backbone.load_state_dict(model_dict, strict=False)
                print("Successfully loaded pretrained weights")
            else:
                print(f"Pretrained weights not found at {pretrain_path}")
    
    def forward(self, x):
        """
        前向传播过程
        Args:
            x: 输入张量，shape (batch_size, 3, D, H, W)
        Returns:
            x: 输出张量，shape (batch_size, num_classes)
        """
        # 1. 输入预处理：3通道 -> 1通道
        x = self.preprocessing(x)
        
        # 2. 主干网络处理：特征提取 + 分类
        x = self.backbone(x)
        
        return x

# 提供一个特征提取的版本，用于融合模型
class MedicalNet3DFeatureExtractor(nn.Module):
    """MedicalNet的3D ResNet50特征提取器"""
    def __init__(self, pretrained=True):
        super().__init__()
        
        # 1. 输入预处理层
        self.preprocessing = nn.Conv3d(3, 1, kernel_size=1, stride=1, padding=0, bias=False)
        
        # 2. 创建配置对象
        self.opt = SimpleNamespace(
            model='resnet',
            model_depth=50,
            input_W=224,
            input_H=224,
            input_D=70,  # 匹配融合模型使用的深度
            resnet_shortcut='B',
            no_cuda=False,
            gpu_id=[0],
            phase='train',
            pretrain_path=None,
            n_seg_classes=1,
            new_layer_names=['fc']
        )
        
        # 3. 创建基础模型
        self.backbone, _ = generate_model(self.opt)
        
        # 4. 修改模型结构
        self.backbone = modify_resnet_for_classification(self.backbone)
        
        # 修正：检查是否存在fc层，只有存在时才删除
        if hasattr(self.backbone.module, 'fc'):
            delattr(self.backbone.module, 'fc')
            self.backbone.module.fc = nn.Identity()
        
        # 5. 加载预训练权重
        if pretrained:
            # pretrain_path = r"E:\1.githubcode\MedicalNet-master\pretrain\resnet_50.pth"
            pretrain_path = r"/root/autodl-tmp/hcc/resnet_50.pth"
            if os.path.exists(pretrain_path):
                print(f"Loading pretrained weights from {pretrain_path}")
                weights = torch.load(pretrain_path, map_location='cpu')
                
                # 处理权重键名
                state_dict = weights['state_dict']
                new_state_dict = {}
                for k, v in state_dict.items():
                    if k.startswith('module.'):
                        k = k[7:]  # 移除 'module.' 前缀
                    if not k.startswith('conv_seg') and 'fc' not in k:  # 跳过分割层和原始fc层
                        new_state_dict[k] = v
                
                # 加载除最后一层外的权重
                model_dict = self.backbone.state_dict()
                model_dict.update({k: v for k, v in new_state_dict.items() 
                                 if k in model_dict and 'fc' not in k})
                self.backbone.load_state_dict(model_dict, strict=False)
                print("Successfully loaded pretrained weights")
            else:
                print(f"Pretrained weights not found at {pretrain_path}")
                
        # 冻结特征提取器的参数
        for param in self.parameters():
            param.requires_grad = False
    
    def forward(self, x):
        """
        前向传播过程，只输出特征
        Args:
            x: 输入张量，shape (batch_size, 3, D, H, W)
        Returns:
            x: 特征张量，shape (batch_size, 2048)
        """
        # 1. 输入预处理：3通道 -> 1通道
        x = self.preprocessing(x)
        
        # 2. 提取特征但不进行分类
        x = self.backbone.module.conv1(x)
        x = self.backbone.module.bn1(x)
        x = self.backbone.module.relu(x)
        x = self.backbone.module.maxpool(x)
        
        x = self.backbone.module.layer1(x)
        x = self.backbone.module.layer2(x)
        x = self.backbone.module.layer3(x)
        x = self.backbone.module.layer4(x)
        
        # 全局平均池化
        x = self.backbone.module.avgpool(x)
        x = x.view(x.size(0), -1)  # 输出维度是2048
        
        return x

# 添加缺失的 FeatureExtractorSwin3D 类
class FeatureExtractorSwin3D(nn.Module):
    def __init__(self, pretrained=True):
        super(FeatureExtractorSwin3D, self).__init__()
        self.model = swin3d_s(pretrained=pretrained)
        # 去掉最后一层分类层
        self.model.head = nn.Identity()
        # 冻结参数
        for param in self.model.parameters():
            param.requires_grad = False
        
    def forward(self, x):
        x = self.model(x)
        return x

# 添加缺失的 FusionModel 类
class FusionModel(nn.Module):
    def __init__(self, num_classes):
        super(FusionModel, self).__init__()
        self.feature_extractor_medical3d = MedicalNet3DFeatureExtractor(pretrained=True)
        self.feature_extractor_swin3d = FeatureExtractorSwin3D(pretrained=True)
        # 定义一个全连接层来结合两个特征向量
        self.fc = nn.Linear(2048 + 768, num_classes)  # 2048是MedicalNet ResNet50的特征维度，768是Swin3D的特征维度
        
    def forward(self, x):
        features_medical3d = self.feature_extractor_medical3d(x)
        features_medical3d = features_medical3d.view(features_medical3d.size(0), -1)
        
        features_swin3d = self.feature_extractor_swin3d(x)
        features_swin3d = features_swin3d.view(features_swin3d.size(0), -1)
        
        combined_features = torch.cat((features_medical3d, features_swin3d), dim=1)        
        out = self.fc(combined_features)        
        return out

def main():
    # 创建模型实例
    model = MedicalNet3DClassifier(num_classes=2)
    
    # 打印模型结构
    print("\nModel Architecture:")
    print(model)
    
    # 打印参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\nTotal Parameters: {total_params:,}")
    print(f"Trainable Parameters: {trainable_params:,}")
    
    # 测试前向传播
    batch_size, channels, depth, height, width = 2, 3, 16, 224, 224
    dummy_input = torch.randn(batch_size, channels, depth, height, width)
    
    if torch.cuda.is_available():
        model = model.cuda()
        dummy_input = dummy_input.cuda()
    
    with torch.no_grad():
        output = model(dummy_input)
    print(f"\nInput Shape: {dummy_input.shape}")
    print(f"Output Shape: {output.shape}")  # 应该是 [2, 2]

def save_metrics_to_txt(metrics, filepath):
    """保存训练和验证指标到txt文件"""
    with open(filepath, 'w') as f:
        for epoch, metric in enumerate(metrics):
            f.write(f"Epoch {epoch+1}:\n")
            f.write(f"Training Loss: {metric['train_loss']:.4f}\n")
            f.write(f"Testing Loss: {metric['test_loss']:.4f}\n")
            f.write(f"Training Accuracy: {metric['train_acc']:.2f}%\n")
            f.write(f"Testing Accuracy: {metric['test_acc']:.2f}%\n")
            f.write("-" * 50 + "\n")

def plot_learning_curves(metrics, save_dir):
    """绘制并保存学习曲线"""
    epochs = range(1, len(metrics) + 1)
    
    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(epochs, [m['train_loss'] for m in metrics], 'b-', label='Training Loss')
    plt.title('Training Loss Curve')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'training_curves_loss.png'))
    plt.close()
    
    # 绘制准确率曲线
    plt.figure(figsize=(10, 5))
    plt.plot(epochs, [m['train_acc'] for m in metrics], 'b-', label='Training Accuracy')
    plt.plot(epochs, [m['test_acc'] for m in metrics], 'r-', label='Testing Accuracy')
    plt.title('Accuracy Curves')
    plt.xlabel('Epochs')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.savefig(os.path.join(save_dir, 'training_curves_accuracy.png'))
    plt.close()

def save_predictions_to_excel(predictions, save_path):
    """保存预测结果到Excel文件"""
    df = pd.DataFrame(predictions)
    df.to_excel(save_path, index=False)

def train_and_evaluate(train_loader, test_loader, num_epochs=10):
    """训练和评估函数，包装在独立函数中便于主程序调用"""
    # 设置 PyTorch 内存分配器配置
    if torch.cuda.is_available():
        torch.cuda.empty_cache()  # 清空GPU缓存
        # 设置内存分配器配置
        torch.cuda.set_per_process_memory_fraction(0.8)  # 限制GPU内存使用比例
        
    # 创建保存结果的目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_dir = f'/root/autodl-tmp/hcc/results_{timestamp}'
    os.makedirs(results_dir, exist_ok=True)
    
    # 分类的类别数
    num_classes = 2
    model = FusionModel(num_classes=num_classes)
    print(model)

    # 将模型移动到GPU
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    print('device =', device)
    model.to(device)

    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)
    exp_lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

    # 保存最优模型的变量
    best_accuracy = 0.0
    best_model_path = os.path.join(results_dir, 'best_model.pth')
    
    # 用于存储训练过程中的指标
    metrics_history = []
    all_predictions = []

    try:
        for epoch in range(num_epochs):
            model.train()
            running_loss = 0.0
            correct = 0
            total = 0
            
            # 训练循环
            for batch_idx, batch in enumerate(train_loader):
                try:
                    images = batch['image'].to(device)
                    images = images.expand(-1, 3, -1, -1, -1)
                    labels = batch['label'].long().to(device)

                    optimizer.zero_grad()
                    y_pred = model(images)
                    loss = criterion(y_pred, labels)
                    loss.backward()
                    optimizer.step()

                    with torch.no_grad():
                        y_pred = torch.argmax(y_pred, dim=1)
                        correct += (y_pred == labels).sum().item()
                        total += labels.size(0)
                        running_loss += loss.item() * images.size(0)

                    # 清理不需要的缓存
                    del images, labels, y_pred, loss
                    torch.cuda.empty_cache()

                except RuntimeError as e:
                    if "out of memory" in str(e):
                        print('| WARNING: ran out of memory, skipping batch')
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                        continue
                    else:
                        raise e

            exp_lr_scheduler.step()
            torch.cuda.empty_cache()

            epoch_loss = running_loss / len(train_loader.dataset)
            epoch_acc = 100 * correct / total
          
            # 测试循环
            model.eval()
            test_correct = 0
            test_total = 0
            epoch_predictions = []
            
            with torch.no_grad():
                for batch in test_loader:
                    try:
                        images = batch['image'].to(device)
                        images = images.expand(-1, 3, -1, -1, -1)
                        labels = batch['label'].long().to(device)
                        file_names = batch['file_name']

                        outputs = model(images)
                        probabilities = torch.softmax(outputs, dim=1)
                        _, predicted = torch.max(outputs, 1)
                        
                        test_total += labels.size(0)
                        test_correct += (predicted == labels).sum().item()
                        
                        # 收集预测结果
                        for fname, true_label, pred_label, probs in zip(file_names, 
                                                                      labels.cpu().numpy(),
                                                                      predicted.cpu().numpy(),
                                                                      probabilities.cpu().numpy()):
                            # 只包含概率值，用逗号分隔
                            prob_str = f"{probs[0]:.4f}, {probs[1]:.4f}"
                            
                            result_dict = {
                                'file_name': fname,
                                'true_label': int(true_label),
                                'predicted_label': int(pred_label),
                                'probabilities': prob_str
                            }
                            epoch_predictions.append(result_dict)
                        
                        # 清理不需要的缓存
                        del images, labels, outputs, probabilities, predicted
                        torch.cuda.empty_cache()

                    except RuntimeError as e:
                        if "out of memory" in str(e):
                            print('| WARNING: ran out of memory during testing, skipping batch')
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                            continue
                        else:
                            raise e

                test_accuracy = 100 * test_correct / test_total
                
                # 保存当前epoch的指标
                metrics_history.append({
                    'train_loss': epoch_loss,
                    'train_acc': epoch_acc,
                    'test_acc': test_accuracy
                })
                
                print(f'Epoch [{epoch+1}/{num_epochs}]:')
                print(f'Training Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.2f}%')
                print(f'Testing Accuracy: {test_accuracy:.2f}%')

                # 检查是否是最佳模型
                if test_accuracy > best_accuracy:
                    best_accuracy = test_accuracy
                    torch.save(model.state_dict(), best_model_path)
                    print(f'Best model saved with accuracy: {best_accuracy:.2f}%')
                    # 保存最佳模型的预测结果
                    save_predictions_to_excel(epoch_predictions, 
                                           os.path.join(results_dir, 'best_model_predictions.xlsx'))

    except Exception as e:
        print(f"Training interrupted due to error: {str(e)}")
        # 保存当前模型作为最终模型
        final_model_path = os.path.join(results_dir, 'interrupted_model.pth')
        torch.save(model.state_dict(), final_model_path)
        print(f"Saved interrupted model to {final_model_path}")
        raise e

    # 保存最终模型
    final_model_path = os.path.join(results_dir, 'final_model.pth')
    torch.save(model.state_dict(), final_model_path)
    
    # 保存训练指标
    save_metrics_to_txt(metrics_history, os.path.join(results_dir, 'training_log.txt'))
    
    # 绘制并保存学习曲线
    plot_learning_curves(metrics_history, results_dir)
    
    print(f"\nTraining completed. Results saved in {results_dir}")
    print(f"Best accuracy: {best_accuracy:.2f}%")
    
    return model, best_accuracy

if __name__ == "__main__":
    # Windows需要这一行才能正确使用多进程
    multiprocessing.freeze_support()
    
    # 初始化测试
    main()
    
    # 加载数据
    print("Loading datasets...")
    train_dir = '/root/autodl-tmp/hcc/train/ap'
    test_dir = '/root/autodl-tmp/hcc/val/ap'
    train_file_path = '/root/autodl-tmp/hcc/train.xlsx'
    test_file_path = '/root/autodl-tmp/hcc/val.xlsx'

    # 定义转换操作
    resized_shape = (224,224, 70) #r3d_18和swin3d融合用这个

    train_transforms = Compose([
        LoadImaged(keys=["image"]),  # 加载图像
        EnsureChannelFirstd(keys=["image"]),  # 确保通道维度在最前
        Resized(keys=["image"], spatial_size= resized_shape),  # 调整图像尺寸
        ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),  # 强度缩放
        RandRotate90d(keys=["image"], prob=0.5, spatial_axes=[0, 1]),  # 随机90度旋转
        EnsureTyped(keys=["image"])  # 确保数据类型为 PyTorch 张量
    ])

    test_transforms = Compose([
        LoadImaged(keys=["image"]),
        EnsureChannelFirstd(keys=["image"]),
        Resized(keys=["image"], spatial_size=resized_shape),
        ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=800, b_min=0.0, b_max=1.0, clip=True),
        EnsureTyped(keys=["image"])
    ])

    # 定义标签
    label_name = 'PHCC'

    def TumorDataset(data_dir, label_excel):
        # 读取Excel文件
        df = pd.read_excel(label_excel)
        
        # 获取图像文件列表
        image_files = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith('.nii.gz')]
        
        # 创建包含图像路径和标签的字典列表
        data = []
        for img_path in image_files:
            # 从图像文件路径中提取文件名（不包含扩展名）
            base_name = os.path.basename(img_path)
            file_name = os.path.splitext(base_name)[0]  
            file_names = file_name.split('-')[0]     
            # 从DataFrame中查找对应的标签
            if file_names in df['name'].values:
                label = df.loc[df['name'] == file_names, label_name].values[0]
                data.append({"image": img_path, "label": label,"file_name": file_names})    
        return data

    # 加载数据
    train_files = TumorDataset(train_dir, train_file_path)
    test_files = TumorDataset(test_dir, test_file_path)

    # 创建数据集
    train_dataset = Dataset(data=train_files, transform=train_transforms)
    test_dataset = Dataset(data=test_files, transform=test_transforms)

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=0)  # 减小batch_size
    test_loader = DataLoader(test_dataset, batch_size=4, num_workers=0)  # 减小batch_size
    
    # 训练和评估模型
    print("Starting training...")
    model, best_accuracy = train_and_evaluate(train_loader, test_loader, num_epochs=20)
    
    print(f"Training completed. Best accuracy: {best_accuracy:.3f}%")
# %%
