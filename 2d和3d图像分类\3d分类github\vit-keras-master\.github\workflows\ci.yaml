name: CI/CD

on:
  push:
    branches:
      - "**"

jobs:
  test:
    name: Automated checks
    strategy:
      matrix:
        python-version: [3.8]
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Make Host Environment
        run: |
          make init
      - name: Test Source
        run: |
          make format-check type-check lint-check test
