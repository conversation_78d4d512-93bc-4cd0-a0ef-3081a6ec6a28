{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 14. World Coordinates Explanation\n", "This notebook shows to importance of using the world coordinates of images when registering them.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "from itkwidgets import compare, checkerboard, view\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import Images with world coordinates\n", "fixed_image = itk.imread('data/CT_3D_lung_fixed.mha', itk.F)\n", "moving_image = itk.imread('data/CT_3D_lung_moving.mha', itk.F)\n", "\n", "# Recast Image to numpy, then to itk to replace original world coordinates with itk default once.\n", "fixed_image_np = np.asarray(fixed_image).astype(np.float32)\n", "fixed_image_np = itk.image_view_from_array(fixed_image_np)\n", "moving_image_np = np.asarray(moving_image).astype(np.float32)\n", "moving_image_np = itk.image_view_from_array(moving_image_np)\n", "\n", "# Registration with original itk image \n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image)\n", "\n", "# Registration with recasted numpy image with default world coordinates.\n", "result_image_np, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image_np, moving_image_np)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8887d853091a45f4a23ae5e7b368af73", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=False, description='cmap'), Checkbox(v…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare result images with itk widgets, images do not occupy same fysical space.\n", "compare(result_image, result_image_np)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Set origin and spacing equal for pixel-wise image comparison\n", "result_image_no_wc = np.asarray(result_image).astype(np.float32)\n", "result_image_np_no_wc = np.asarray(result_image_np).astype(np.float32)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e5cadc1e92f7402db80c3b971affbc3d", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=False, description='cmap'), Checkbox(v…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare result images, images now occupy same fysical space, but still have (smaller) differences.\n", "compare(result_image_no_wc, result_image_np_no_wc)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}