#!/usr/bin/env python
# coding: utf-8

# ## Setup environment

# In[ ]:


get_ipython().system('pip3 install monai')
get_ipython().system('python3 -c "import monai" || pip3 install "monai[nibabel, tqdm, skimage, einops]"')
get_ipython().system('python3 -c "import matplotlib" || pip3 install -q matplotlib')
get_ipython().system('python3 -c "import pytorch_lightning" || pip3 install pytorch_lightning')
get_ipython().run_line_magic('matplotlib', 'inline')


# In[ ]:


get_ipython().system("pip3 install 'monai[einops]'")


# # Pipeline SwinUNETR

# ## Setup imports

# In[ ]:


import pytorch_lightning
import monai
import numpy as np
import pandas as pd

from typing import Union
from PIL import Image
from monai.utils import set_determinism
from monai.utils.enums import MetricReduction
from monai.transforms import (
    AsDiscrete,
    ScaleIntensityRangePercentilesd,
    EnsureChannelFirstd,
    Compose,
    CropForegroundd,
    LoadImaged,
    Orientationd,
    RandCropByPosNegLabeld,
    ScaleIntensityRanged,
    Spacingd,
    EnsureType,
    MapTransform,
    Activations,
    Activationsd,
    Invertd,
    NormalizeIntensityd,
    RandFlipd,
    RandScaleIntensityd,
    RandShiftIntensityd,
    RandSpatialCropd,
    EnsureTyped,
    SpatialPadd,
    SaveImage,
    RandRotate90d,
    ConcatItemsd,
    DeleteItemsd,
)
from monai.networks.utils import one_hot
from monai.networks.nets import SwinUNETR,UNETR,SegResNet
from monai.networks.layers import Norm
from monai.metrics import DiceMetric,HausdorffDistanceMetric,ConfusionMatrixMetric, compute_hausdorff_distance, CumulativeIterationMetric
from monai.losses import DiceCELoss,DiceLoss, TverskyLoss
from monai.inferers import sliding_window_inference

from monai.data import PersistentDataset, list_data_collate, decollate_batch, DataLoader, load_decathlon_datalist, CacheDataset # NiftiSaver

from monai.config import print_config
from monai.apps import download_and_extract,DecathlonDataset
from monai.handlers.utils import from_engine

import torch
import matplotlib.pyplot as plt
import tempfile
import shutil
import os
import glob
import nibabel as nib

from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from pytorch_lightning.callbacks.early_stopping import EarlyStopping
from pytorch_lightning.callbacks.timer import Timer

os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.enabled = True
print_config()


# ## Setup data directory

# In[ ]:


directory = os.environ.get("MONAI_DATA_DIRECTORY")
# root_dir = tempfile.mkdtemp() if directory is None else directory
root_dir=os.path.join(os.getcwd(), 'Best_modell/')
print(root_dir)

roi_size = (96, 96, 96)
sw_batch_size = 1


# ## Creating a json file

# In[ ]:


file_paths1 = glob.glob('/kaggle/input/brats2023-part-1' + "/*")
file_paths1.sort()

file_names1 = os.listdir('/kaggle/input/brats2023-part-1')
file_names1.sort()

t1c, t1n, t2f, t2w, label = [], [], [], [], []
for i in range(330):
    t1c.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t1c.nii'))
    t1n.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t1n.nii'))
    t2f.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t2f.nii'))
    t2w.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-t2w.nii'))
    label.append(os.path.join(file_paths1[i] + '/' + file_names1[i] + '-seg.nii'))

data = {
    't1c': t1c,
    't1n': t1n,
    't2f': t2f,
    't2w': t2w,
    'label': label
}


# In[ ]:


file= []
for i in range(330):
    file.append({"t1c": data['t1c'][i], "t1n": data['t1n'][i], "t2f": data['t2f'][i], "t2w": data['t2w'][i], "label": data['label'][i]})
    
file_json = {
    "training": file
}

import json
file_path = '/kaggle/working/dataset.json'
with open(file_path, 'w') as json_file:
    json.dump(file_json, json_file)


# ## Check data shape and visualize

# In[ ]:


num = 15
flair = nib.load(data['t2f'][num]).get_fdata()
t1 = nib.load(data['t1n'][num]).get_fdata()
t1ce = nib.load(data['t1c'][num]).get_fdata()
t2 = nib.load(data['t2w'][num]).get_fdata()
mask = nib.load(data['label'][num]).get_fdata()

slice_number = 95
print('slice =', slice_number)
print('shape =', flair.shape)

fig, (ax1, ax2, ax3, ax4, ax5) = plt.subplots(1,5, figsize = (20, 10))
ax1.imshow(flair[:,:,slice_number], cmap='gray')
ax1.set_title('Image flair')
ax2.imshow(t1[:,:,slice_number], cmap='gray')
ax2.set_title('Image t1')
ax3.imshow(t1ce[:,:,slice_number], cmap='gray')
ax3.set_title('Image t1ce')
ax4.imshow(t2[:,:,slice_number], cmap='gray')
ax4.set_title('Image t2')
ax5.imshow(mask[:,:,slice_number])
ax5.set_title('Mask')


# # Define the LightningModule

# In[ ]:


torch.multiprocessing.set_sharing_strategy("file_system")


# In[ ]:


class Net(pytorch_lightning.LightningModule):
    def __init__(self):
        super().__init__()
        
        self._model = SwinUNETR(
            in_channels=4,
            out_channels=4,
            img_size=(96, 96, 96),
            feature_size=48,
            drop_rate=0.0,
            attn_drop_rate=0.0,
            dropout_path_rate=0.0,
            use_checkpoint=True,
        ).to(device)
        

        self.loss_function = DiceCELoss(to_onehot_y=True, softmax=True)
        self.post_pred = AsDiscrete(argmax=True, to_onehot=4)
        self.post_label = AsDiscrete(to_onehot=4)
        
        self.dice_metric = DiceMetric(include_background=False, reduction="mean")
        self.dice_metric_batch = DiceMetric(include_background=False, reduction="mean_batch")
        self.haursdoff = HausdorffDistanceMetric(include_background=False, distance_metric='euclidean', 
                                                 percentile=None, directed=False, reduction="mean_batch", get_not_nans=False)
        self.check_val = 10
        self.best_val_dice = 0
        self.best_val_epoch = 0
        self.epoch_loss_values = []
        self.metric_values = []
        self.metric_values_tc = []
        self.metric_values_wt = []
        self.metric_values_et = []
        self.metric_values_back = []

        self.haursdoff_values_tc = []
        self.haursdoff_values_wt = []
        self.haursdoff_values_et = []
        self.haursdoff_values_back = []
        
        self.validation_step_outputs = []
        self.training_step_outputs = []
    

    def forward(self, x):
        return self._model(x)

    def prepare_data(self):
        # setting up the correct data path
        datasets = "/kaggle/working/dataset.json"
        datalist = load_decathlon_datalist(datasets, True, "training")  
        train_files, val_files = datalist[:300], datalist[300:]

        # setting deterministic training for reproducibility
        set_determinism(seed=0)

        # defining the data transforms
        train_transform = Compose(
            [
                LoadImaged(keys=["t2f", "t1n", "t1c", "t2w", "label"]),
                EnsureChannelFirstd(keys=["t2f", "t1n", "t1c", "t2w", "label"]),
                Orientationd(keys=["t2f", "t1n", "t1c", "t2w", "label"], axcodes="RAS"),
                
                ConcatItemsd(keys=["t2f", "t1n", "t1c", "t2w"], name="image", dim=0),
                DeleteItemsd(keys=["t2f", "t1n", "t1c", "t2w"]),
                
                Spacingd(
                    keys=["image", "label"],
                    pixdim=(1.0, 1.0, 1.0),
                    mode=("bilinear", "nearest"),
                ),
                NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
                RandSpatialCropd(keys=["image", "label"], roi_size=[96, 96, 96], random_size=False),
                RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=0),
                RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=1),
                RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=2),
                RandScaleIntensityd(keys="image", factors=0.1, prob=1.0),
                RandShiftIntensityd(keys="image", offsets=0.1, prob=1.0),
            ]
        )
        
        val_transform = Compose(
            [
                LoadImaged(keys=["t2f", "t1n", "t1c", "t2w", "label"]),
                EnsureChannelFirstd(keys=["t2f", "t1n", "t1c", "t2w", "label"]),
                Orientationd(keys=["t2f", "t1n", "t1c", "t2w", "label"], axcodes="RAS"),
                
                ConcatItemsd(keys=["t2f", "t1n", "t1c", "t2w"], name="image", dim=0),
                DeleteItemsd(keys=["t2f", "t1n", "t1c", "t2w"]),
                
                Spacingd(
                    keys=["image", "label"],
                    pixdim=(1.0, 1.0, 1.0),
                    mode=("bilinear", "nearest"),
                ),
                NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
            ]
        )
        
        persistent_cache = os.path.join(tempfile.mkdtemp(), "persistent_cache")
        
        self.train_ds = monai.data.PersistentDataset(
            data=train_files,
            transform= train_transform,
            cache_dir=persistent_cache,
        )
        self.val_ds = monai.data.PersistentDataset(
            data=val_files,
            transform= val_transform,
            cache_dir=persistent_cache,
        )

    def train_dataloader(self):
        train_loader = DataLoader(
            self.train_ds,
            batch_size=2,
            shuffle=True,
            num_workers=3,
            pin_memory=True,
            collate_fn=list_data_collate,
        )
        return train_loader

    def val_dataloader(self):
        val_loader = DataLoader(
            self.val_ds, 
            batch_size=1, 
            pin_memory=True,
            shuffle=False,
            num_workers=3
        )
        return val_loader

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self._model.parameters(), lr=1e-4, weight_decay=1e-5
        )
        return optimizer

    def training_step(self, batch, batch_idx):
        images, labels = (batch["image"].cuda(), batch["label"].cuda())
        output = self.forward(images)
        loss = self.loss_function(output, labels)
        tensorboard_logs = {"train_loss": loss.item()}
        self.log("train_loss", loss.item())
        self.training_step_outputs.append({"loss": loss})
        return {"loss": loss, "log": tensorboard_logs}
    
    def on_train_epoch_end(self):
        avg_loss = torch.stack([x["loss"] for x in self.training_step_outputs]).mean()
        self.epoch_loss_values.append(avg_loss.detach().cpu().numpy())
        self.training_step_outputs.clear()

    def validation_step(self, batch, batch_idx):
        images, labels = batch["image"], batch["label"]

        outputs = sliding_window_inference(images, roi_size, sw_batch_size, self.forward)

        loss = self.loss_function(outputs, labels)
        outputs = [self.post_pred(i) for i in decollate_batch(outputs)]
        labels = [self.post_label(i) for i in decollate_batch(labels)]
        
        self.dice_metric(y_pred=outputs, y=labels)
        self.dice_metric_batch(y_pred = outputs,y = labels)
        self.haursdoff(y_pred = outputs,y = labels)
        
        d = {"val_loss": loss, "val_number": len(outputs)}
        self.validation_step_outputs.append(d)
    
        return {"val_loss": loss, "val_number": len(outputs)}


    def on_validation_epoch_end(self):
        val_loss, num_items = 0, 0
        for output in self.validation_step_outputs:
            val_loss += output["val_loss"].sum().item()
            num_items += output["val_number"]
        
        mean_val_dice = self.dice_metric.aggregate().item()
        self.metric_values.append(np.array(mean_val_dice))
        self.dice_metric.reset()
        
        metric_batch = self.dice_metric_batch.aggregate()
        #metric_back = metric_batch[0].item()
        #self.metric_values_back.append(metric_back)
        metric_wt = metric_batch[0].item()
        self.metric_values_wt.append(metric_wt)
        metric_et = metric_batch[1].item()
        self.metric_values_et.append(metric_et)
        metric_tc = metric_batch[2].item()
        self.metric_values_tc.append(metric_tc)
        self.dice_metric_batch.reset()
        
        haursdoff = self.haursdoff.aggregate()
        #hd_back = haursdoff[0].item()
        #self.haursdoff_values_back.append(hd_back)
        hd_wt = haursdoff[0].item()
        self.haursdoff_values_wt.append(hd_wt)
        hd_et = haursdoff[1].item()
        self.haursdoff_values_et.append(hd_et)
        hd_tc = haursdoff[2].item()
        self.haursdoff_values_tc.append(hd_tc)
        self.haursdoff.reset()
        
        mean_val_loss = torch.tensor(val_loss / num_items)

        tensorboard_logs = {
            "val_dice": mean_val_dice,
            "val_loss": mean_val_loss,
        }
        
        if mean_val_dice > self.best_val_dice:
            self.best_val_dice = mean_val_dice
            self.best_val_epoch = self.current_epoch
            
            torch.save(
                self._model, f"Model_SwinUNETR.pt")
        
        print(
            f"current epoch: {self.current_epoch}"
            f"current mean dice: {mean_val_dice:.4f}"
            f"\nbest mean dice: {self.best_val_dice:.4f} "
            f"at epoch: {self.best_val_epoch}"
            f" tc: {metric_tc:.4f} wt: {metric_wt:.4f} et: {metric_et:.4f}"
            
            f" hd_tc: {hd_tc:.4f} hd_wt:{hd_wt:.4f} hd_et:{hd_et}"
        )
        self.log("val_loss", mean_val_loss.item()) 
        self.validation_step_outputs.clear() 
        return {"log": tensorboard_logs}


# # Run the training

# In[ ]:


get_ipython().run_cell_magic('time', '', '# initialising the LightningModule\nnet = Net()\n\n# set up checkpoints\ncheckpoint_callback = ModelCheckpoint(dirpath=root_dir, filename="best_metric_model", save_last=True)\nearly_stop_callback = EarlyStopping(\n   monitor="val_loss",\n   min_delta=0.00,\n   patience=3,\n   verbose=False,\n   mode=\'min\'\n)\n\n# stop training after 10 hours\ntimer_callback = Timer(duration="00:10:00:00")\n\n# initialising Lightning\'s trainer\ntrainer = pytorch_lightning.Trainer(\n    precision = 16-mixed,\n    accelerator=\'gpu\',\n    devices="auto",\n    max_epochs=150,\n    check_val_every_n_epoch=net.check_val,\n    callbacks=[checkpoint_callback, early_stop_callback, timer_callback],\n    default_root_dir=root_dir,\n    limit_val_batches = 20,\n)\n\n# training\ntrainer.fit(net)\n')


# In[ ]:


print(f"train completed, best_metric: {net.best_val_dice:.4f} " f"at epoch {net.best_val_epoch}")


# ## Plot the loss and metric

# In[ ]:


plt.figure("train", (12, 6))
plt.subplot(1, 2, 1)
plt.title("Epoch Average Loss")
x = [i + 1 for i in range(len(net.epoch_loss_values))]
y = net.epoch_loss_values
plt.xlabel("epoch")
plt.plot(x, y, color="red")
plt.subplot(1, 2, 2)
plt.title("Val Mean Dice")
x = [i*10 for i in range(len(net.metric_values))]
y = net.metric_values
plt.xlabel("epoch")
plt.plot(x, y, color="green")
plt.savefig("EpochAverageLoss_ValMeanDice1.png")
plt.show()


plt.figure("train", (12, 6))
plt.subplot(1, 2, 1)
plt.title("Val Mean Dice")
x = [i*10 for i in range(len(net.metric_values_wt))]
#y_back = net.metric_values_back
y_tc = net.metric_values_tc
y_wt = net.metric_values_wt
y_et = net.metric_values_et
plt.xlabel("epoch")
#plt.plot(x, y_back, color="m")
plt.plot(x, y_tc, color="blue")
plt.plot(x, y_wt, color="brown")
plt.plot(x, y_et, color="purple")
plt.legend(['Val Mean Dice Back', 'Val Mean Dice TC', 'Val Mean Dice WT', 'Val Mean Dice ET'])
#plt.savefig("ValMeanDiceTC_WT_ET.png")

plt.subplot(1, 2, 2)
plt.title("Haursdoff Values")
x = [i*10 for i in range(len(net.haursdoff_values_tc))]
#y_back = net.haursdoff_values_back
y_tc = net.haursdoff_values_tc
y_wt = net.haursdoff_values_wt
y_et = net.haursdoff_values_et
plt.xlabel("epoch")
#plt.plot(x, y_back, color='purple')
plt.plot(x, y_tc, color='m')
plt.plot(x, y_wt, color='c')
plt.plot(x, y_et, color='k')
plt.legend(['Haursdoff Values back', 'Haursdoff Values TC', 'Haursdoff Values WT', 'Haursdoff Values ET'])
plt.savefig("HaursdoffValuesTC_WT_ET1.png")
plt.show()


# ## Save to CSV

# In[ ]:


import csv

filename = 'MetricsSwinUNETR.csv'
with open(filename, 'w', newline='') as file:
    writer = csv.writer(file)

    writer.writerow(['Index', 'Mean Dice', 'Dice tc', 'Dice wt', 'Dice et',
                     'Haursdoff tc', 'Haursdoff wt', 'Haursdoff et',])

    for i in range(len(net.metric_values)):
        writer.writerow([[i*10 for i in range(len(net.metric_values))][i], net.metric_values[i], 
                         net.metric_values_tc[i], net.metric_values_wt[i], net.metric_values_et[i],
                         net.haursdoff_values_tc[i], net.haursdoff_values_wt[i], net.haursdoff_values_et[i]])

        
filename = "AverageLossSeg.csv" 
with open(filename, 'w', newline='') as file:
    writer = csv.writer(file)
   
    writer.writerow(['Index', 'AverageLoss'])

    for i in range(len(net.epoch_loss_values)):
        writer.writerow([[i + 1 for i in range(len(net.epoch_loss_values))][i], net.epoch_loss_values[i]])


# ## Check best model output with the input image and label

# In[ ]:


model = Net.load_from_checkpoint(os.path.join('/kaggle/working/Best_modell/best_metric_model.ckpt'))
model.eval()
model.to(device)

case_num = 0 # Select image number from val_ds [0,9)
size = 90 # Select the slice number

with torch.no_grad():
    img_name = os.path.split(net.val_ds[case_num]["image"].meta["filename_or_obj"])[1]
    print(img_name)
    image, label = net.val_ds[case_num]["image"], net.val_ds[case_num]["label"]         #torch.Size([4/1, 240, 240, 155])
    val_inputs = torch.unsqueeze(image, 0).cuda()                                       #torch.Size([1, 4, 240, 240, 155])
    pred = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)         #torch.Size([1, 4, 240, 240, 155])
    pred_discret = torch.argmax(pred, dim=1)                                            #torch.Size([1, 240, 240, 155])

    plt.figure("check", (18, 6))
    plt.subplot(1, 3, 1)
    plt.title(f"image")
    plt.imshow(image[0, :, :, size], cmap="gray")
    plt.subplot(1, 3, 2)
    plt.title(f"label")
    plt.imshow(label[0, :, :, size])
    plt.subplot(1, 3, 3)
    plt.title(f"SegResNet")
    plt.imshow(pred_discret.cpu()[0, :, :, size] )
    plt.show()


# ## Metrics

# In[ ]:


import warnings
from typing import Callable, Optional, Union

import torch

from monai.networks import one_hot
from monai.utils import MetricReduction

def compute_meandice(
    y_pred: torch.Tensor,
    y: torch.Tensor,
    include_background: bool = True,
    to_onehot_y: bool = False,
    mutually_exclusive: bool = False,
    sigmoid: bool = False,
    other_act: Optional[Callable] = None,
    logit_thresh: float = 0.5,
) -> torch.Tensor:

    n_classes = y_pred.shape[1]
    n_len = len(y_pred.shape)
    if sigmoid and other_act is not None:
        raise ValueError("Incompatible values: sigmoid=True and other_act is not None.")
    if sigmoid:
        y_pred = y_pred.float().sigmoid()

    if other_act is not None:
        if not callable(other_act):
            raise TypeError(f"other_act must be None or callable but is {type(other_act).__name__}.")
        y_pred = other_act(y_pred)

    if n_classes == 1:
        if mutually_exclusive:
            warnings.warn("y_pred has only one class, mutually_exclusive=True ignored.")
        if to_onehot_y:
            warnings.warn("y_pred has only one channel, to_onehot_y=True ignored.")
        if not include_background:
            warnings.warn("y_pred has only one channel, include_background=False ignored.")
        # make both y and y_pred binary
        y_pred = (y_pred >= logit_thresh).float()
        y = (y > 0).float()
    else:  # multi-channel y_pred
        # make both y and y_pred binary
        if mutually_exclusive:
            if sigmoid:
                raise ValueError("Incompatible values: sigmoid=True and mutually_exclusive=True.")
            y_pred = torch.argmax(y_pred, dim=1, keepdim=True)
            y_pred = one_hot(y_pred, num_classes=n_classes)
        else:
            y_pred = (y_pred >= logit_thresh).float()
        if to_onehot_y:
            y = one_hot(y, num_classes=n_classes)

    if not include_background:
        y = y[:, 1:] if y.shape[1] > 1 else y
        y_pred = y_pred[:, 1:] if y_pred.shape[1] > 1 else y_pred

    assert y.shape == y_pred.shape, "Ground truth one-hot has differing shape (%r) from source (%r)" % (
        y.shape,
        y_pred.shape,
    )
    y = y.float()
    y_pred = y_pred.float()

    # reducing only spatial dimensions (not batch nor channels)
    reduce_axis = list(range(2, n_len))
    intersection = torch.sum(y * y_pred, dim=reduce_axis)

    y_o = torch.sum(y, reduce_axis)
    y_pred_o = torch.sum(y_pred, dim=reduce_axis)
    denominator = y_o + y_pred_o

    f = torch.where(y_o > 0, (2.0 * intersection) / denominator, torch.tensor(float("nan"), device=y_o.device))
    return f  # returns array of Dice shape: [batch, n_classes]


# In[ ]:


val_labels = torch.unsqueeze(label, 0).cuda()

def dice_metric_(y_pred: Union[np.ndarray, torch.Tensor],
                y: Union[np.ndarray, torch.Tensor],
                n_classes: int):
    if isinstance(y_pred, np.ndarray):
        y_pred = torch.from_numpy(y_pred)
    if isinstance(y, np.ndarray):
        y = torch.from_numpy(y)
    
    y = one_hot(y, n_classes, dim=1)
    y_pred = one_hot(y_pred, 4, dim=0).expand(1, 4, 240, 240, 155)
    
    dice = compute_meandice(y_pred=y_pred, y=y, include_background=True)
    return np.around(dice[0].numpy(), decimals=3)


# In[ ]:


for dice_organ in dice_metric_(y=val_labels.cpu(), y_pred=pred_discret.cpu(), n_classes=4):
    print(dice_organ, end=', ')


# In[ ]:


def hausdorff_metric(y_pred: Union[np.ndarray, torch.Tensor],
                y: Union[np.ndarray, torch.Tensor],
                n_classes: int):
    if isinstance(y_pred, np.ndarray):
        y_pred = torch.from_numpy(y_pred)
    if isinstance(y, np.ndarray):
        y = torch.from_numpy(y)
    
    y = one_hot(y, n_classes, dim=1)
    y_pred = one_hot(y_pred, 4, dim=0).expand(1, 4, 240, 240, 155)
    
    hausdorff = compute_hausdorff_distance(y_pred=y_pred, y=y, include_background=True)
    return np.around(hausdorff[0].numpy(), decimals=3)


# In[ ]:


for hausdorff_organ in hausdorff_metric(y=val_labels.cpu(), y_pred=pred_discret.cpu(), n_classes=4):
    print(hausdorff_organ, end=', ')


# ## Saving predictions based on the model

# In[ ]:


# predictions softmax
softmax = torch.nn.Softmax(dim = 1)
out = softmax(pred)
saver = SaveImage(output_dir="Out", output_ext=".nii.gz", output_postfix="seg")
saver(out[0])

label = ['background', 'WT', 'ET', 'TC']

plt.figure("output", (18, 6))
for l, name in enumerate(label):
    plt.subplot(1, 4, l + 1)
    plt.title(f"{name}")
    plt.imshow(out[0, l, :, :, 90].detach().cpu())
plt.show()


# In[ ]:


model = Net.load_from_checkpoint(os.path.join(root_dir, "best_metric_model.ckpt"))
model.eval()
model.to(device)

val_loader = DataLoader(net.val_ds, batch_size=1, shuffle=True, num_workers=4, pin_memory=True)
saver = SaveImage(output_dir="Output", output_ext=".nii.gz", resample=True)

with torch.no_grad():
    for val_data in val_loader:
        images = val_data["image"].to(device)  #torch.Size([1, 4, 240, 240, 155])
        pred = sliding_window_inference(images, roi_size,  sw_batch_size, model)
        
        #Saving prediction without discretization
        output = softmax(pred)   #torch.Size([1, 4, 240, 240, 155])
        saver(output[0])

