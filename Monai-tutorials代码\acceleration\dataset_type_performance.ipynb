{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# PersistentDataset, CacheDataset, LMDBDataset, and simple Dataset Tutorial and Speed Test\n", "\n", "This tutorial shows how to accelerate PyTorch medical DL program based on\n", "how data is loaded and preprocessed using different MONAI `Dataset` managers.\n", "\n", "`Dataset` provides the simplest model of data loading.  Each time a dataset is needed, it is reloaded from the original datasources, and processed through the all non-random and random transforms to generate analyzable tensors. This mechanism has the smallest memory footprint, and the smallest temporary disk footprint.\n", "\n", "`CacheDataset` provides a mechanism to pre-load all original data and apply non-random transforms into analyzable tensors loaded in memory prior to starting analysis.  The `CacheDataset` requires all tensor representations of data requested to be loaded into memory at once. The subset of random transforms is applied to the cached components before use. This is the highest performance dataset if all data fit in core memory.\n", "\n", "`PersistentDataset` processes original data sources through the non-random transforms on first use, and stores these intermediate tensor values to an on-disk persistence representation.  The intermediate processed tensors are loaded from disk on each use for processing by the random-transforms for each analysis request.  The `PersistentDataset` has a similar memory footprint to the simple `Dataset`, with performance characteristics close to the `CacheDataset` at the expense of disk storage.  Additionally, the cost of first time processing of data is distributed across each first use.\n", "\n", "`LMDBDataset` is a variant of `PersistentDataset`. It uses an LMDB database as the persistent backend.\n", "\n", "It's modified from the [Spleen 3D segmentation tutorial notebook](../3d_segmentation/spleen_segmentation_3d.ipynb).\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/acceleration/dataset_type_performance.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": []}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, tqdm, lmdb]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import glob\n", "import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import torch\n", "from monai.apps import download_and_extract\n", "from monai.config import print_config\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Dataset,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    LMDBDataset,\n", "    PersistentDataset,\n", "    decollate_batch,\n", ")\n", "from monai.inferers import sliding_window_inference\n", "from monai.losses import DiceCELoss\n", "from monai.metrics import DiceMetric\n", "from monai.networks.layers import Norm\n", "from monai.networks.nets import UNet\n", "from monai.transforms import (\n", "    EnsureChannelFirstd,\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    RandCropByPosNegLabeld,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", ")\n", "from monai.utils import set_determinism\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define a typical PyTorch training process"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def train_process(train_ds, val_ds):\n", "    # use batch_size=2 to load images and use RandCropByPosNegLabeld\n", "    # to generate 2 x 4 images for network training\n", "    train_loader = DataLoader(\n", "        train_ds,\n", "        batch_size=2,\n", "        shuffle=True,\n", "        num_workers=8,\n", "    )\n", "    val_loader = DataLoader(val_ds, batch_size=1, num_workers=4)\n", "    device = torch.device(\"cuda:0\")\n", "    model = UNet(\n", "        spatial_dims=3,\n", "        in_channels=1,\n", "        out_channels=2,\n", "        channels=(16, 32, 64, 128, 256),\n", "        strides=(2, 2, 2, 2),\n", "        num_res_units=2,\n", "        norm=Norm.BATCH,\n", "    ).to(device)\n", "    loss_function = DiceCELoss(\n", "        include_background=False,\n", "        to_onehot_y=True,\n", "        softmax=True,\n", "        squared_pred=True,\n", "        batch=True,\n", "        smooth_nr=0.00001,\n", "        smooth_dr=0.00001,\n", "        lambda_dice=0.5,\n", "        lambda_ce=0.5,\n", "    )\n", "    optimizer = torch.optim.SGD(\n", "        model.parameters(),\n", "        lr=0.1,\n", "        momentum=0.9,\n", "        weight_decay=0.00004,\n", "    )\n", "\n", "    post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])\n", "    post_label = Compose([AsDiscrete(to_onehot=2)])\n", "\n", "    dice_metric = DiceMetric(include_background=True, reduction=\"mean\", get_not_nans=False)\n", "\n", "    # The initial choice is 300~600 epoches, in order to save time, we use 30 to demonstrate\n", "    # the comparison of dataset classes\n", "    max_epochs = 30\n", "\n", "    val_interval = 1  # do validation for every epoch\n", "    best_metric = -1\n", "    best_metric_epoch = -1\n", "    epoch_loss_values = []\n", "    metric_values = []\n", "    epoch_times = []\n", "    total_start = time.time()\n", "    for epoch in range(max_epochs):\n", "        epoch_start = time.time()\n", "        print(\"-\" * 10)\n", "        print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "        model.train()\n", "        epoch_loss = 0\n", "        step = 0\n", "        for batch_data in train_loader:\n", "            step_start = time.time()\n", "            step += 1\n", "            inputs, labels = (\n", "                batch_data[\"image\"].to(device),\n", "                batch_data[\"label\"].to(device),\n", "            )\n", "            optimizer.zero_grad()\n", "            outputs = model(inputs)\n", "            loss = loss_function(outputs, labels)\n", "            loss.backward()\n", "            optimizer.step()\n", "            epoch_loss += loss.item()\n", "            print(\n", "                f\"{step}/{len(train_ds) // train_loader.batch_size},\"\n", "                f\" train_loss: {loss.item():.4f}\"\n", "                f\" step time: {(time.time() - step_start):.4f}\"\n", "            )\n", "        epoch_loss /= step\n", "        epoch_loss_values.append(epoch_loss)\n", "        print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "        if (epoch + 1) % val_interval == 0:\n", "            model.eval()\n", "            with torch.no_grad():\n", "                for val_data in val_loader:\n", "                    val_inputs, val_labels = (\n", "                        val_data[\"image\"].to(device),\n", "                        val_data[\"label\"].to(device),\n", "                    )\n", "                    roi_size = (160, 160, 160)\n", "                    sw_batch_size = 4\n", "                    val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)\n", "                    val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]\n", "                    val_labels = [post_label(i) for i in decollate_batch(val_labels)]\n", "                    dice_metric(y_pred=val_outputs, y=val_labels)\n", "\n", "                metric = dice_metric.aggregate().item()\n", "                dice_metric.reset()\n", "                metric_values.append(metric)\n", "                if metric > best_metric:\n", "                    best_metric = metric\n", "                    best_metric_epoch = epoch + 1\n", "                    torch.save(\n", "                        model.state_dict(),\n", "                        os.path.join(root_dir, \"best_metric_model.pth\"),\n", "                    )\n", "                    print(\"saved new best metric model\")\n", "                print(\n", "                    f\"current epoch: {epoch + 1} current\"\n", "                    f\" mean dice: {metric:.4f}\"\n", "                    f\" best mean dice: {best_metric:.4f}\"\n", "                    f\" at epoch: {best_metric_epoch}\"\n", "                )\n", "        print(f\"time of epoch {epoch + 1} is: {(time.time() - epoch_start):.4f}\")\n", "        epoch_times.append(time.time() - epoch_start)\n", "\n", "    print(\n", "        f\"train completed, best_metric: {best_metric:.4f}\"\n", "        f\" at epoch: {best_metric_epoch}\"\n", "        f\" total time: {(time.time() - total_start):.4f}\"\n", "    )\n", "    return (\n", "        max_epochs,\n", "        time.time() - total_start,\n", "        epoch_loss_values,\n", "        metric_values,\n", "        epoch_times,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Start of speed testing\n", "\n", "The `PersistenceDataset`, `CacheDataset`, and `Dataset` are compared for speed for running 30 epochs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download dataset\n", "\n", "Downloads and extracts the dataset.\n", "The dataset comes from http://medicaldecathlon.com/."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"tags": []}, "outputs": [], "source": ["resource = \"https://msd-for-monai.s3-us-west-2.amazonaws.com/Task09_Spleen.tar\"\n", "md5 = \"410d4a301da4e5b2f6f86ec3ddba524e\"\n", "\n", "compressed_file = os.path.join(root_dir, \"Task09_Spleen.tar\")\n", "data_dir = os.path.join(root_dir, \"Task09_Spleen\")\n", "if not os.path.exists(data_dir):\n", "    download_and_extract(resource, compressed_file, root_dir, md5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set MSD Spleen dataset path"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["train_images = sorted(glob.glob(os.path.join(data_dir, \"imagesTr\", \"*.nii.gz\")))\n", "train_labels = sorted(glob.glob(os.path.join(data_dir, \"labelsTr\", \"*.nii.gz\")))\n", "data_dicts = [{\"image\": image_name, \"label\": label_name} for image_name, label_name in zip(train_images, train_labels)]\n", "train_files, val_files = data_dicts[:-9], data_dicts[-9:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms for training and validation\n", "\n", "Deterministic transforms during training:\n", "* LoadImaged\n", "* EnsureChannelFirstd\n", "* Spacingd\n", "* Orientationd\n", "* ScaleIntensityRanged\n", "\n", "Non-deterministic transforms:\n", "* RandCropByPosNegLabeld\n", "\n", "All the validation transforms are deterministic.\n", "The results of all the deterministic transforms will be cached to accelerate training."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def transformations():\n", "    train_transforms = Compose(\n", "        [\n", "            # LoadImaged with image_only=True is to return the MetaTensors\n", "            # the additional metadata dictionary is not returned.\n", "            LoadImaged(keys=[\"image\", \"label\"], image_only=True),\n", "            EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "            Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "            Spacingd(\n", "                keys=[\"image\", \"label\"],\n", "                pixdim=(1.5, 1.5, 2.0),\n", "                mode=(\"bilinear\", \"nearest\"),\n", "            ),\n", "            ScaleIntensityRanged(\n", "                keys=[\"image\"],\n", "                a_min=-57,\n", "                a_max=164,\n", "                b_min=0.0,\n", "                b_max=1.0,\n", "                clip=True,\n", "            ),\n", "            CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "            # randomly crop out patch samples from big\n", "            # image based on pos / neg ratio\n", "            # the image centers of negative samples\n", "            # must be in valid image area\n", "            RandCropByPosNegLabeld(\n", "                keys=[\"image\", \"label\"],\n", "                label_key=\"label\",\n", "                spatial_size=(96, 96, 96),\n", "                pos=1,\n", "                neg=1,\n", "                num_samples=4,\n", "                image_key=\"image\",\n", "                image_threshold=0,\n", "            ),\n", "        ]\n", "    )\n", "\n", "    # NOTE: No random cropping in the validation data,\n", "    # we will evaluate the entire image using a sliding window.\n", "    val_transforms = Compose(\n", "        [\n", "            # LoadImaged with image_only=True is to return the MetaTensors\n", "            # the additional metadata dictionary is not returned.\n", "            LoadImaged(keys=[\"image\", \"label\"], image_only=True),\n", "            EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "            Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "            Spacingd(\n", "                keys=[\"image\", \"label\"],\n", "                pixdim=(1.5, 1.5, 2.0),\n", "                mode=(\"bilinear\", \"nearest\"),\n", "            ),\n", "            ScaleIntensityRanged(\n", "                keys=[\"image\"],\n", "                a_min=-57,\n", "                a_max=164,\n", "                b_min=0.0,\n", "                b_max=1.0,\n", "                clip=True,\n", "            ),\n", "            CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "        ]\n", "    )\n", "    return train_transforms, val_transforms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable deterministic training and regular `Dataset`\n", "\n", "Load each original dataset and transform each time it is needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["set_determinism(seed=0)\n", "\n", "train_trans, val_trans = transformations()\n", "train_ds = Dataset(data=train_files, transform=train_trans)\n", "val_ds = Dataset(data=val_files, transform=val_trans)\n", "\n", "(\n", "    max_epochs,\n", "    total_time,\n", "    epoch_loss_values,\n", "    metric_values,\n", "    epoch_times,\n", ") = train_process(train_ds, val_ds)\n", "print(f\"total training time of {max_epochs} epochs\" f\" with regular Dataset: {total_time:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable deterministic training and `PersistentDataset`\n", "\n", "Use persistent storage of non-random transformed training and validation data computed once and stored in persistently across runs"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["persistent_cache = os.path.join(tempfile.mkdtemp(), \"persistent_cache\")\n", "\n", "set_determinism(seed=0)\n", "train_trans, val_trans = transformations()\n", "train_persitence_ds = PersistentDataset(data=train_files, transform=train_trans, cache_dir=persistent_cache)\n", "val_persitence_ds = PersistentDataset(data=val_files, transform=val_trans, cache_dir=persistent_cache)\n", "\n", "(\n", "    persistence_epoch_num,\n", "    persistence_total_time,\n", "    persistence_epoch_loss_values,\n", "    persistence_metric_values,\n", "    persistence_epoch_times,\n", ") = train_process(train_persitence_ds, val_persitence_ds)\n", "print(\n", "    f\"total training time of {persistence_epoch_num}\"\n", "    f\" epochs with persistent storage Dataset: {persistence_total_time:.4f}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable deterministic training and `LMDBDataset`\n", "\n", "Use persistent storage of non-random transformed training and validation data computed once and stored in persistently using LMDB across runs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LMDB_cache = os.path.join(tempfile.mkdtemp(), \"lmdb_cache\")\n", "\n", "set_determinism(seed=0)\n", "train_trans, val_trans = transformations()\n", "lmdb_init_start = time.time()\n", "train_lmdb_ds = LMDBDataset(\n", "    data=train_files, transform=train_trans, cache_dir=LMDB_cache, lmdb_kwargs={\"map_async\": True}\n", ")\n", "val_lmdb_ds = LMDBDataset(data=val_files, transform=val_trans, cache_dir=LMDB_cache, lmdb_kwargs={\"map_async\": True})\n", "lmdb_init_time = time.time() - lmdb_init_start\n", "\n", "(\n", "    lmdb_epoch_num,\n", "    lmdb_total_time,\n", "    lmdb_epoch_loss_values,\n", "    lmdb_metric_values,\n", "    lmdb_epoch_times,\n", ") = train_process(train_lmdb_ds, val_lmdb_ds)\n", "print(f\"total training time of {lmdb_epoch_num}\" f\" epochs with LMDB storage Dataset: {lmdb_total_time:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable deterministic training and `CacheDataset`\n", "\n", "Precompute all non-random transforms of original data and store in memory.\n", "\n", "When `runtime_cache=\"processes\"` the cache initialization time `cache_init_time` is negligible.\n", "Set `runtime_cache=False` to enable precomputing cache."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set_determinism(seed=0)\n", "train_trans, val_trans = transformations()\n", "cache_init_start = time.time()\n", "cache_train_ds = CacheDataset(\n", "    data=train_files, transform=train_trans, cache_rate=1.0, runtime_cache=\"processes\", copy_cache=False\n", ")\n", "cache_val_ds = CacheDataset(\n", "    data=val_files, transform=val_trans, cache_rate=1.0, runtime_cache=\"processes\", copy_cache=False\n", ")\n", "cache_init_time = time.time() - cache_init_start\n", "\n", "(\n", "    cache_epoch_num,\n", "    cache_total_time,\n", "    cache_epoch_loss_values,\n", "    cache_metric_values,\n", "    cache_epoch_times,\n", ") = train_process(cache_train_ds, cache_val_ds)\n", "print(f\"total training time of {cache_epoch_num}\" f\" epochs with CacheDataset: {cache_total_time:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot training loss and validation metrics"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************************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************************************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***************************************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", "text/plain": ["<Figure size 1200x1800 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 18))\n", "\n", "plt.subplot(4, 2, 1)\n", "plt.title(\"Regular Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"red\")\n", "\n", "plt.subplot(4, 2, 2)\n", "plt.title(\"Regular <PERSON> Mean <PERSON>ce\")\n", "x = [i + 1 for i in range(len(metric_values))]\n", "y = cache_metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"red\")\n", "\n", "plt.subplot(4, 2, 3)\n", "plt.title(\"PersistentDataset Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(persistence_epoch_loss_values))]\n", "y = persistence_epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"blue\")\n", "\n", "plt.subplot(4, 2, 4)\n", "plt.title(\"PersistentDataset Val Mean Dice\")\n", "x = [i + 1 for i in range(len(persistence_metric_values))]\n", "y = persistence_metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"blue\")\n", "\n", "plt.subplot(4, 2, 5)\n", "plt.title(\"LMDBDataset Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(lmdb_epoch_loss_values))]\n", "y = lmdb_epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"yellow\")\n", "\n", "plt.subplot(4, 2, 6)\n", "plt.title(\"LMDBDataset Val Mean Dice\")\n", "x = [i + 1 for i in range(len(lmdb_metric_values))]\n", "y = lmdb_metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"yellow\")\n", "\n", "plt.subplot(4, 2, 7)\n", "plt.title(\"Cache Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(cache_epoch_loss_values))]\n", "y = cache_epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"green\")\n", "\n", "plt.subplot(4, 2, 8)\n", "plt.title(\"<PERSON>ache <PERSON>\")\n", "x = [i + 1 for i in range(len(cache_metric_values))]\n", "y = cache_metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.plot(x, y, color=\"green\")\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot total time and every epoch time"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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***************************************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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.title(f\"Total Train Time ({max_epochs} epochs)\")\n", "plt.bar(\"regular\", total_time, 1, label=\"Regular Dataset\", color=\"red\")\n", "plt.bar(\n", "    \"lmdb\",\n", "    lmdb_init_time + lmdb_total_time,\n", "    1,\n", "    label=\"LMDB cache init\",\n", "    color=\"yellow\",\n", ")\n", "plt.bar(\"lmdb\", lmdb_total_time, 1, label=\"LMDB Dataset\", color=\"orange\")\n", "plt.bar(\n", "    \"persistent\",\n", "    persistence_total_time,\n", "    1,\n", "    label=\"Persistent Dataset\",\n", "    color=\"blue\",\n", ")\n", "plt.bar(\n", "    \"cache\",\n", "    cache_init_time + cache_total_time,\n", "    1,\n", "    label=\"Cache Dataset\",\n", "    color=\"green\",\n", ")\n", "if cache_init_time > 1:\n", "    plt.bar(\"cache\", cache_init_time, 1, label=\"Cache Init\", color=\"orange\")\n", "plt.ylabel(\"secs\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Epoch Time\")\n", "x = [i + 1 for i in range(len(epoch_times))]\n", "plt.xlabel(\"epoch\")\n", "plt.ylabel(\"secs\")\n", "plt.plot(x, epoch_times, label=\"Regular Dataset\", color=\"red\")\n", "plt.plot(x, persistence_epoch_times, label=\"Persistent Dataset\", color=\"blue\")\n", "plt.plot(x, lmdb_epoch_times, label=\"LMDB Dataset\", color=\"yellow\")\n", "plt.plot(x, cache_epoch_times, label=\"Cache Dataset\", color=\"green\")\n", "plt.grid(alpha=0.4, linestyle=\":\")\n", "plt.legend(loc=\"best\")\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}