#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
病理组学特征提取工具测试脚本

这个脚本用于测试病理组学特征提取工具的功能
包括创建测试图像和验证特征提取结果
"""

import os
import numpy as np
import cv2
from PIL import Image, ImageDraw
import matplotlib.pyplot as plt

def create_test_images(output_folder="./测试病理图像", num_images=5):
    """
    创建测试用的模拟病理图像
    
    参数:
    output_folder: 输出文件夹路径
    num_images: 创建的图像数量
    """
    os.makedirs(output_folder, exist_ok=True)
    
    print(f"创建 {num_images} 个测试病理图像...")
    
    for i in range(num_images):
        # 创建基础图像 (512x512)
        img = Image.new('RGB', (512, 512), color=(240, 240, 240))
        draw = ImageDraw.Draw(img)
        
        # 添加随机的"细胞核"
        num_nuclei = np.random.randint(20, 50)
        
        for j in range(num_nuclei):
            # 随机位置和大小
            x = np.random.randint(20, 492)
            y = np.random.randint(20, 492)
            radius = np.random.randint(8, 25)
            
            # 随机颜色 (模拟不同染色强度)
            intensity = np.random.randint(50, 150)
            color = (intensity, intensity + 20, intensity + 40)
            
            # 绘制椭圆形"细胞核"
            draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                        fill=color, outline=(intensity-20, intensity-20, intensity-20))
        
        # 添加一些纹理噪声
        img_array = np.array(img)
        noise = np.random.normal(0, 10, img_array.shape).astype(np.int16)
        img_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # 保存图像
        final_img = Image.fromarray(img_array)
        filename = f"test_pathology_{i+1:03d}.png"
        filepath = os.path.join(output_folder, filename)
        final_img.save(filepath)
        
        print(f"创建测试图像: {filename}")
    
    print(f"测试图像已保存到: {output_folder}")
    return output_folder

def run_feature_extraction_test():
    """运行特征提取测试"""
    print("=" * 60)
    print("病理组学特征提取工具 - 测试模式")
    print("=" * 60)
    
    # 1. 创建测试图像
    test_folder = create_test_images()
    
    # 2. 导入特征提取器
    try:
        from 病理组学特征提取_CellProfiler_一键运行 import PathomicsFeatureExtractor
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保 '病理组学特征提取_CellProfiler_一键运行.py' 文件在当前目录下")
        return
    
    # 3. 创建特征提取器
    print("\n创建特征提取器...")
    extractor = PathomicsFeatureExtractor(
        input_folder=test_folder,
        output_folder="./测试结果"
    )
    
    # 4. 执行特征提取
    print("\n开始特征提取...")
    extractor.batch_process()
    
    # 5. 验证结果
    print("\n验证提取结果...")
    verify_results(extractor)
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("请查看以下文件夹中的结果:")
    print(f"- 测试图像: {test_folder}")
    print(f"- 提取结果: {extractor.output_folder}")
    print("=" * 60)

def verify_results(extractor):
    """验证提取结果"""
    if extractor.features_df.empty:
        print("❌ 特征提取失败 - 没有提取到任何特征")
        return
    
    print("✅ 特征提取成功!")
    print(f"   - 处理图像数量: {len(extractor.features_df)}")
    print(f"   - 提取特征数量: {len(extractor.features_df.columns) - 2}")  # 减去Image_Name和Image_Path
    
    # 检查特征类型
    feature_types = {
        'Morphological': 0,
        'Texture': 0,
        'Intensity': 0
    }
    
    for col in extractor.features_df.columns:
        if any(keyword in col for keyword in ['Area', 'Perimeter', 'Eccentricity', 'Solidity', 'FormFactor', 'Nuclei']):
            feature_types['Morphological'] += 1
        elif any(keyword in col for keyword in ['Contrast', 'Dissimilarity', 'Homogeneity', 'Energy', 'Correlation', 'LBP']):
            feature_types['Texture'] += 1
        elif 'Intensity' in col:
            feature_types['Intensity'] += 1
    
    print("   特征类型分布:")
    for ftype, count in feature_types.items():
        print(f"     - {ftype}: {count} 个特征")
    
    # 显示部分特征统计
    print("\n   部分特征统计:")
    numeric_cols = extractor.features_df.select_dtypes(include=[np.number]).columns[:5]
    for col in numeric_cols:
        mean_val = extractor.features_df[col].mean()
        std_val = extractor.features_df[col].std()
        print(f"     - {col}: 均值={mean_val:.3f}, 标准差={std_val:.3f}")

def create_demo_with_real_pathology_simulation():
    """创建更真实的病理图像模拟"""
    output_folder = "./高级测试病理图像"
    os.makedirs(output_folder, exist_ok=True)
    
    print("创建高级模拟病理图像...")
    
    # 创建不同类型的病理图像
    image_types = [
        ("正常组织", (200, 180, 160), 30, 15),
        ("轻度异型", (180, 160, 140), 45, 20),
        ("中度异型", (160, 140, 120), 60, 25),
        ("重度异型", (140, 120, 100), 80, 30),
        ("癌组织", (120, 100, 80), 100, 35)
    ]
    
    for i, (tissue_type, base_color, num_nuclei, max_radius) in enumerate(image_types):
        # 创建图像
        img = Image.new('RGB', (512, 512), color=(250, 245, 240))
        draw = ImageDraw.Draw(img)
        
        # 添加背景纹理
        for _ in range(200):
            x = np.random.randint(0, 512)
            y = np.random.randint(0, 512)
            size = np.random.randint(1, 3)
            color = tuple(np.random.randint(230, 250, 3))
            draw.ellipse([x-size, y-size, x+size, y+size], fill=color)
        
        # 添加细胞核
        for j in range(num_nuclei):
            x = np.random.randint(max_radius, 512-max_radius)
            y = np.random.randint(max_radius, 512-max_radius)
            
            # 根据组织类型调整核的大小和形状
            radius_x = np.random.randint(8, max_radius)
            radius_y = np.random.randint(8, max_radius)
            
            # 颜色变化
            color_var = np.random.randint(-30, 30, 3)
            color = tuple(np.clip(np.array(base_color) + color_var, 0, 255))
            
            # 绘制椭圆核
            draw.ellipse([x-radius_x, y-radius_y, x+radius_x, y+radius_y], 
                        fill=color, outline=tuple(np.array(color) - 20))
        
        # 保存图像
        filename = f"{tissue_type}_{i+1:02d}.png"
        filepath = os.path.join(output_folder, filename)
        img.save(filepath)
        print(f"创建 {tissue_type} 图像: {filename}")
    
    return output_folder

def main():
    """主函数"""
    print("病理组学特征提取工具 - 测试程序")
    print("1. 基础测试")
    print("2. 高级测试 (更真实的病理图像)")
    print("3. 退出")
    
    choice = input("请选择测试模式 (1-3): ").strip()
    
    if choice == "1":
        run_feature_extraction_test()
    elif choice == "2":
        # 创建高级测试图像
        test_folder = create_demo_with_real_pathology_simulation()
        
        # 运行特征提取
        try:
            from 病理组学特征提取_CellProfiler_一键运行 import PathomicsFeatureExtractor
            
            extractor = PathomicsFeatureExtractor(
                input_folder=test_folder,
                output_folder="./高级测试结果"
            )
            
            extractor.batch_process()
            verify_results(extractor)
            
            print(f"\n高级测试完成！结果保存在: {extractor.output_folder}")
            
        except ImportError as e:
            print(f"导入错误: {e}")
    elif choice == "3":
        print("退出测试程序")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
