# ==================== 使用真实数据绘制火山图 ====================
# 本代码展示如何使用真实的差异表达分析数据绘制火山图
# 适用于RNA-seq、蛋白质组学等差异分析结果

# 清理环境
rm(list = ls())

# 加载必要的包
required_packages <- c("ggplot2", "dplyr", "ggrepel", "RColorBrewer", 
                      "scales", "gridExtra", "viridis")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# ==================== 数据读取和预处理 ====================

# 方法1: 读取CSV文件
# 假设您的数据文件格式如下：
# Gene, log2FC, p_value, adj_p_value, base_mean, ...

# 示例：读取数据文件
# data <- read.csv("your_data_file.csv", stringsAsFactors = FALSE)

# 方法2: 使用示例数据（如果没有真实数据）
# 首先运行示例数据生成脚本
if (file.exists("volcano_data/simple_volcano_data.csv")) {
  data <- read.csv("volcano_data/simple_volcano_data.csv", stringsAsFactors = FALSE)
  cat("已读取示例数据文件\n")
} else {
  cat("未找到数据文件，生成示例数据...\n")
  source("示例数据生成.R")
  data <- read.csv("volcano_data/simple_volcano_data.csv", stringsAsFactors = FALSE)
}

# 检查数据结构
cat("数据维度:", dim(data), "\n")
cat("列名:", colnames(data), "\n")
head(data)

# ==================== 数据质量检查 ====================

# 检查缺失值
cat("缺失值检查:\n")
print(sapply(data, function(x) sum(is.na(x))))

# 移除缺失值
data <- data[complete.cases(data), ]

# 检查数据范围
cat("\nlog2FC 范围:", range(data$log2FC), "\n")
cat("p值范围:", range(data$p_value), "\n")

# 处理极值p值
data$p_value[data$p_value == 0] <- 1e-300  # 避免log(0)
data$p_value[data$p_value > 1] <- 1        # 确保p值不超过1

# 计算-log10(p值)
data$neg_log10_p <- -log10(data$p_value)

# ==================== 设置阈值和参数 ====================

# 可自定义的参数
FC_THRESHOLD <- 1.0      # log2 fold change阈值
P_THRESHOLD <- 0.05      # p值阈值
TOP_GENES <- 10          # 标注的基因数量

# 计算阈值线位置
neg_log10_p_threshold <- -log10(P_THRESHOLD)

# 重新分类基因（如果数据中没有significance列）
if (!"significance" %in% colnames(data)) {
  data$significance <- "Not Significant"
  data$significance[data$log2FC > FC_THRESHOLD & data$p_value < P_THRESHOLD] <- "Up-regulated"
  data$significance[data$log2FC < -FC_THRESHOLD & data$p_value < P_THRESHOLD] <- "Down-regulated"
}

# 转换为因子
data$significance <- factor(data$significance, 
                           levels = c("Up-regulated", "Down-regulated", "Not Significant"))

# ==================== 选择要标注的基因 ====================

# 方法1: 基于p值选择最显著的基因
select_top_genes_by_pvalue <- function(data, n_genes = 5) {
  up_genes <- data %>% 
    filter(significance == "Up-regulated") %>% 
    arrange(p_value) %>% 
    head(n_genes)
  
  down_genes <- data %>% 
    filter(significance == "Down-regulated") %>% 
    arrange(p_value) %>% 
    head(n_genes)
  
  return(rbind(up_genes, down_genes))
}

# 方法2: 基于fold change和p值的组合选择
select_top_genes_combined <- function(data, n_genes = 5) {
  data$score <- abs(data$log2FC) * data$neg_log10_p
  
  up_genes <- data %>% 
    filter(significance == "Up-regulated") %>% 
    arrange(desc(score)) %>% 
    head(n_genes)
  
  down_genes <- data %>% 
    filter(significance == "Down-regulated") %>% 
    arrange(desc(score)) %>% 
    head(n_genes)
  
  return(rbind(up_genes, down_genes))
}

# 选择要标注的基因
genes_to_label <- select_top_genes_combined(data, TOP_GENES/2)

# ==================== 火山图绘制函数 ====================

create_volcano_plot <- function(data, genes_to_label = NULL, 
                               title = "Volcano Plot",
                               fc_threshold = 1.0, 
                               p_threshold = 0.05,
                               colors = c("#E74C3C", "#3498DB", "#95A5A6"),
                               point_size = 1.5,
                               label_size = 3) {
  
  p <- ggplot(data, aes(x = log2FC, y = neg_log10_p)) +
    geom_point(aes(color = significance), 
               alpha = 0.7, 
               size = point_size) +
    scale_color_manual(values = setNames(colors, 
                                        c("Up-regulated", "Down-regulated", "Not Significant")),
                      labels = c("上调", "下调", "无显著差异")) +
    
    # 添加阈值线
    geom_vline(xintercept = c(-fc_threshold, fc_threshold), 
               linetype = "dashed", color = "gray40", alpha = 0.8) +
    geom_hline(yintercept = -log10(p_threshold), 
               linetype = "dashed", color = "gray40", alpha = 0.8) +
    
    # 标注基因
    {if (!is.null(genes_to_label) && nrow(genes_to_label) > 0) {
      geom_text_repel(data = genes_to_label, 
                      aes(label = Gene), 
                      size = label_size,
                      box.padding = 0.5,
                      point.padding = 0.3,
                      segment.color = "gray50",
                      max.overlaps = 20,
                      min.segment.length = 0.1)
    }} +
    
    # 标题和标签
    labs(
      title = title,
      x = "log₂(Fold Change)",
      y = "-log₁₀(p-value)",
      color = "基因类型"
    ) +
    
    # 主题设置
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      axis.title = element_text(size = 12, face = "bold"),
      axis.text = element_text(size = 10),
      legend.position = "bottom",
      legend.title = element_text(size = 11, face = "bold"),
      panel.grid.minor = element_blank(),
      panel.border = element_rect(color = "black", fill = NA, size = 0.5)
    )
  
  return(p)
}

# ==================== 绘制不同风格的火山图 ====================

# 1. 经典火山图
classic_plot <- create_volcano_plot(
  data, 
  genes_to_label,
  title = "经典火山图",
  colors = c("#FF6B6B", "#4ECDC4", "#95A5A6")
)

# 2. 科学期刊风格
journal_plot <- create_volcano_plot(
  data, 
  genes_to_label,
  title = "科学期刊风格火山图",
  colors = c("#D32F2F", "#1976D2", "#757575"),
  point_size = 1.2,
  label_size = 2.8
) +
  theme_classic() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.title = element_text(size = 11, face = "bold"),
    axis.text = element_text(size = 9),
    legend.position = "bottom",
    panel.grid.major = element_line(color = "#F5F5F5", size = 0.3)
  )

# 3. 高对比度火山图
high_contrast_plot <- create_volcano_plot(
  data, 
  genes_to_label,
  title = "高对比度火山图",
  colors = c("#FF4444", "#00AA44", "#888888"),
  point_size = 2
) +
  theme_dark() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold", color = "white"),
    axis.title = element_text(size = 12, face = "bold", color = "white"),
    axis.text = element_text(size = 10, color = "white"),
    legend.text = element_text(color = "white"),
    legend.title = element_text(color = "white"),
    panel.background = element_rect(fill = "#2C3E50"),
    plot.background = element_rect(fill = "#2C3E50")
  )

# ==================== 添加统计信息的火山图 ====================

# 计算统计信息
n_total <- nrow(data)
n_up <- sum(data$significance == "Up-regulated")
n_down <- sum(data$significance == "Down-regulated")
n_ns <- sum(data$significance == "Not Significant")

# 创建带统计信息的火山图
stats_plot <- create_volcano_plot(
  data, 
  genes_to_label,
  title = paste0("差异表达基因火山图\n",
                "上调: ", n_up, " | 下调: ", n_down, " | 总计: ", n_total),
  colors = c("#E74C3C", "#3498DB", "#BDC3C7")
) +
  # 添加统计文本
  annotate("text", 
           x = max(data$log2FC) * 0.7, 
           y = max(data$neg_log10_p) * 0.9,
           label = paste0("FC > ", 2^FC_THRESHOLD, "\np < ", P_THRESHOLD),
           size = 3.5, 
           hjust = 0,
           color = "#2C3E50",
           fontface = "bold") +
  
  # 添加基因数量标注
  annotate("text",
           x = FC_THRESHOLD + 0.5,
           y = max(data$neg_log10_p) * 0.1,
           label = paste0("上调: ", n_up),
           size = 3,
           color = "#E74C3C",
           fontface = "bold") +
  
  annotate("text",
           x = -FC_THRESHOLD - 0.5,
           y = max(data$neg_log10_p) * 0.1,
           label = paste0("下调: ", n_down),
           size = 3,
           color = "#3498DB",
           fontface = "bold")

# ==================== 显示和保存图片 ====================

# 显示图片
print(classic_plot)
print(journal_plot)
print(high_contrast_plot)
print(stats_plot)

# 创建组合图
combined_plot <- grid.arrange(
  classic_plot + theme(legend.position = "none"),
  journal_plot + theme(legend.position = "none"),
  high_contrast_plot + theme(legend.position = "none"),
  stats_plot + theme(legend.position = "none"),
  ncol = 2,
  top = "不同风格的火山图比较"
)

# 保存图片
if (!dir.exists("volcano_results")) {
  dir.create("volcano_results")
}

ggsave("volcano_results/classic_volcano.png", classic_plot, 
       width = 10, height = 8, dpi = 300)
ggsave("volcano_results/journal_volcano.png", journal_plot, 
       width = 10, height = 8, dpi = 300)
ggsave("volcano_results/high_contrast_volcano.png", high_contrast_plot, 
       width = 10, height = 8, dpi = 300)
ggsave("volcano_results/stats_volcano.png", stats_plot, 
       width = 12, height = 10, dpi = 300)
ggsave("volcano_results/combined_volcano.png", combined_plot, 
       width = 16, height = 12, dpi = 300)

# ==================== 输出统计摘要 ====================

cat("\n==================== 火山图分析摘要 ====================\n")
cat("数据文件:", "volcano_data/simple_volcano_data.csv", "\n")
cat("总基因数:", n_total, "\n")
cat("上调基因:", n_up, "(", round(n_up/n_total*100, 1), "%)\n")
cat("下调基因:", n_down, "(", round(n_down/n_total*100, 1), "%)\n")
cat("无显著差异:", n_ns, "(", round(n_ns/n_total*100, 1), "%)\n")
cat("使用阈值: |log2FC| >", FC_THRESHOLD, ", p <", P_THRESHOLD, "\n")
cat("标注基因数:", nrow(genes_to_label), "\n")

cat("\n最显著的差异基因:\n")
print(genes_to_label[order(genes_to_label$p_value), c("Gene", "log2FC", "p_value", "significance")])

cat("\n图片已保存到 volcano_results/ 目录\n")
cat("程序运行完成！\n")
