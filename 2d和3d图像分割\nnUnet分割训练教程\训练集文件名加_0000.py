import os
import traceback

try:
    # 使用示例
    # input_dir = r"J:\nnUNet\nnUNet_raw\Dataset505_hbp\imagesTs" 
    print("文件夹参考格式: J:\nnUNet\nnUNet_raw\Dataset505_hbp\imagesTs")
    input_dir = input("请输入训练集文件夹路径: ")

    ##imagesTs数据集转换,文件名后面加上_0000，表示第一个序列
    target_dir = input_dir

    # 检查目录是否存在
    if not os.path.exists(target_dir):
        raise FileNotFoundError(f"目录不存在: {target_dir}")

    # 遍历目标目录下的所有文件
    for filename in os.listdir(target_dir):
        # 获取文件路径
        file_path = os.path.join(target_dir, filename)   
        if filename.endswith('.nii.gz'):
            name = filename[:-7]  # 去掉.nii.gz
            ext = '.nii.gz'
        else:
            name, ext = os.path.splitext(filename)
        
        # 新文件名
        new_filename = f"{name}_0000{ext}"
        print(new_filename)       
        new_file_path = os.path.join(target_dir, new_filename) 
        os.rename(file_path, new_file_path)
    
    print("\n处理完成！")
except Exception as e:
    print(f"发生错误: {e}")
    print("\n详细错误信息:")
    traceback.print_exc()

# 无论是否出错，都等待用户输入后再退出
input("\n按Enter键退出...")


