# SwinUNETR for nnUNet

This implementation integrates the SwinUNETR model from MONAI into the nnUNet framework. SwinUNETR is a powerful Transformer-based architecture for medical image segmentation that combines the advantages of the Swin Transformer with the U-shaped encoder-decoder design.

## Installation

Before using SwinUNETR with nnUNet, make sure to install the required dependencies:

```bash
pip install -r requirements_swinunetr.txt
```

## Using SwinUNETR in nnUNet

To use SwinUNETR as the network architecture in nnUNet, you need to specify the trainer class when running nnUNet commands:

```bash
# For training
nnUNetv2_train 1 3d_fullres all -tr nnUNetTrainerSwinUNETR

# For inference
nnUNetv2_predict -i INPUT_FOLDER -o OUTPUT_FOLDER -d DATASET_ID -c 3d_fullres -tr nnUNetTrainerSwinUNETR
```

## Features and Limitations

- **Preprocessing**: All preprocessing steps remain the same as in the original nnUNet.
- **Deep Supervision**: SwinUNETR doesn't support deep supervision in the same way as the original nnUNet architecture. The implementation will automatically disable deep supervision.
- **Model Parameters**: The SwinUNETR model uses a default feature size of 48, which can be adjusted in the source code if needed.

## Model Architecture

SwinUNETR integrates Swin Transformer blocks in a UNet-like structure, combining the advantages of both architectures:

- **Swin Transformer**: Utilizes shifted windows for efficient self-attention computation
- **UNet Structure**: Maintains the powerful encoder-decoder structure with skip connections
- **Feature Hierarchy**: Preserves hierarchical feature representation for better segmentation performance

## Citation

If you use SwinUNETR in your research, please cite the following papers:

```
@article{hatamizadeh2022swin,
  title={Swin UNETR: Swin Transformers for Semantic Segmentation of Brain Tumors in MRI Images},
  author={Hatamizadeh, Ali and Nath, Vishwesh and Tang, Yucheng and Yang, Dong and Roth, Holger and Xu, Daguang},
  journal={arXiv preprint arXiv:2201.01266},
  year={2022}
}

@inproceedings{tang2022self,
  title={Self-supervised pre-training of swin transformers for 3d medical image analysis},
  author={Tang, Yucheng and Yang, Dong and Li, Wenqi and Roth, Holger R and Landman, Bennett and Xu, Daguang and Nath, Vishwesh and Hatamizadeh, Ali},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={20730--20740},
  year={2022}
}
``` 