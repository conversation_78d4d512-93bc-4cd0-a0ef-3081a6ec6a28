name: Documentation Improvement
description: Report wrong or missing documentation
title: "[DOC]: "
labels: [documentation]

body:
  - type: checkboxes
    attributes:
      label: pycaret version checks
      options:
        - label: >
            I have checked that the issue still exists on the latest versions of the docs
            [here](https://pycaret.gitbook.io/docs/)
          required: true
  - type: textarea
    id: location
    attributes:
      label: Location of the documentation
      description: >
        Please provide the location of the documentation, e.g. the URL of the documentation, e.g.
        "https://pycaret.gitbook.io/docs/get-started/installation"
      placeholder: https://pycaret.gitbook.io/docs/get-started/installation
    validations:
      required: true
  - type: textarea
    id: problem
    attributes:
      label: Documentation problem
      description: >
        Please provide a description of what documentation you believe needs to be fixed/improved
    validations:
      required: true
  - type: textarea
    id: suggested-fix
    attributes:
      label: Suggested fix for documentation
      description: >
        Please explain the suggested fix and **why** it's better than the existing documentation
    validations:
      required: true
