## cnn与临床和影像组学特征全连接层融合，构建端到端模型，已经成功
from torch.utils import data
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import pandas as pd
import matplotlib.pyplot as plt
# get_ipython().run_line_magic('matplotlib', 'inline')
from torchvision.datasets import ImageFolder
import torchvision
from torchvision import transforms
import os
from sklearn.metrics import precision_recall_curve, average_precision_score
from sklearn.metrics import roc_curve, auc
from sklearn.metrics import confusion_matrix
from scipy import interp
from itertools import cycle
import itertools
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import shutil
from PIL import Image

# base_dir = r'K:\2020-2023HCC\579hcc\579hcc\ap\jpg'
# train_dir = r'K:\2020-2023HCC\579hcc\579hcc\ap\jpg\train'
# test_dir = r'K:\2020-2023HCC\579hcc\579hcc\ap\jpg\validation'
train_dir = '/root/autodl-tmp/ap/train'
test_dir = '/root/autodl-tmp/ap/validation'

transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[.5, .5, .5], std=[.5, .5, .5])
])

# In[4]:

train_ds = torchvision.datasets.ImageFolder(
    train_dir,
    transform=transform
)

test_ds = torchvision.datasets.ImageFolder(
    test_dir,
    transform=transform
)

##
import glob
# train_dir = r'K:\2020-2023HCC\579hcc\579hcc\ap\jpg\train\*\*\*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\train\*\*\*.jpg'
# test_dir = r'K:\2020-2023HCC\579hcc\579hcc\ap\jpg\validation\*\*\*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\validation\*\*\*.jpg'
train_dir = '/root/autodl-tmp/ap/train/*/*/*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\train\*\*\*.jpg'
test_dir = '/root/autodl-tmp/ap/validation/*/*/*.jpg'
train_paths = glob.glob(train_dir)
test_paths = glob.glob(test_dir)
len(train_paths)
len(test_paths)
train_paths[0]
train_paths[0].split('/')[-2].split('-')[0]
train_names = [img_p.split('/')[-2].split('-')[0] for img_p in train_paths]
train_names[0]
test_names = [img_p.split('/')[-2].split('-')[0] for img_p in test_paths]
test_names[0]

import pandas as pd
# clinical_features_path = r'K:\2020-2023HCC\579hcc\clinical data\data\shiyan2.xlsx'
# #对每个患者的临床特征根据其图片数进行扩增，使得其与图片数一致，表格中第一列name，第二列为num，第3列开始为特征
clinical_features_path = '/root/autodl-tmp/ap/shiyanradio.xlsx'
clinical_features = pd.read_excel(clinical_features_path)
# 填补缺失值
clinical_features.fillna(clinical_features.iloc[:, 2:].mean(), inplace=True)
print(clinical_features)

# 根据图像的文件名，将这些特征与train_ds和test_ds
# 进行对齐。假设临床特征csv文件中有一个名为name的列
train_clinical_features = []
for img_name in train_names:
    img_clinical_features = clinical_features.loc[clinical_features['name'] == img_name].iloc[:, 2:].values[0]
    train_clinical_features.append(img_clinical_features)
#对齐测试集的临床特征
test_clinical_features = []
for img_name in test_names:
    img_clinical_features = clinical_features.loc[clinical_features['name'] == img_name].iloc[:, 2:].values[0]
    test_clinical_features.append(img_clinical_features)
len(train_clinical_features)
len(test_clinical_features)

from sklearn.preprocessing import MinMaxScaler
# 对训练集的clinical features进行归一化
scaler = MinMaxScaler()
train_clinical_features = scaler.fit_transform(train_clinical_features)
# 对测试集的clinical features进行归一化
test_clinical_features = scaler.transform(test_clinical_features)

 # 转换为张量
train_clinical_features = np.array(train_clinical_features)
test_clinical_features = np.array(test_clinical_features)
# 转换为张量
train_clinical_features = torch.from_numpy(train_clinical_features).float()
test_clinical_features = torch.from_numpy(test_clinical_features).float()

class CustomDataset(torch.utils.data.Dataset):
    def __init__(self, ds, clinical_features):
        self.ds = ds
        self.clinical_features = clinical_features
    def __len__(self):
        return len(self.ds)
    def __getitem__(self, index):
        image, label = self.ds[index]
        clinical_feature = self.clinical_features[index]
        return image, clinical_feature, label

BTACH_SIZE = 16

train_custom_ds = CustomDataset(train_ds, train_clinical_features)
test_custom_ds = CustomDataset(test_ds, test_clinical_features)
train_dl = torch.utils.data.DataLoader(train_custom_ds, batch_size=BTACH_SIZE, shuffle=True)
test_dl = torch.utils.data.DataLoader(test_custom_ds, batch_size=BTACH_SIZE)

 # 设定临床特征的数量
num_clinical_features = clinical_features.iloc[:, 2:].shape[1]
print("Number of features:", num_clinical_features)

# torchvision.models.resnet50(pretrained=True)

class CustomModel(nn.Module):
    def __init__(self, num_clinical_features):
        super(CustomModel, self).__init__()
        # 加载预训练的VGG16模型
        # self.model = torchvision.models.vgg16(pretrained=True)
        # self.model = torchvision.models.vit_b_16(pretrained=True)
        # self.model = torchvision.models.vision_transformer.vit_b_16(pretrained=True)
        # self.model = torchvision.models.vgg19 (pretrained=True)
        # self.model = torchvision.models.resnet50(pretrained=True)
        # self.model = torchvision.models.resnet101(pretrained=True)
        self.model = torchvision.models.densenet121(pretrained=True)
        #self.model = torchvision.models.densenet161(pretrained=True)

        # 冻结VGG16模型的参数
        for param in self.model.parameters():
            param.requires_grad = False
        # 获取VGG16特征提取部分的输出特征大小
        # model_feature_size = self.model.classifier[6].in_features #vgg19
        # model_feature_size = self.model.fc.in_features #resnt
        model_feature_size = self.model.classifier.in_features #densent
        # model_feature_size = self.model.heads[0].in_features #vit
        # 创建一个新的分类器，将提取的图像特征与临床特征进行融合
        # self.model.classifier[6] = nn.Linear(model_feature_size, 512)  #vgg19
        # self.model.fc = nn.Linear(model_feature_size, 512) #resnet
        self.model.classifier = nn.Linear(model_feature_size, 512)  # densnet
        # self.model.heads[0] = nn.Linear(model_feature_size, 512) #vit
        self.fc_added1 = nn.Linear(512+num_clinical_features, 2)
        # self.fc_added2 = nn.Linear(20, 2)

    def forward(self, x, clinical_features):
        image_features = self.model(x)
        combined_features = torch.cat((image_features, clinical_features), dim=1)
        output = self.fc_added1(combined_features)
        # output = self.fc_added2(output)
        return output

# def forward(self, x, slafeat):
#     x = self.model_pre(x)  # 128x128
#     x = torch.cat((x, slafeat), 1)
#     x = self.fc_added1(x)
#     x = self.fc_added2(x)
#     return x

# 创建自定义模型实例
model = CustomModel(num_clinical_features)

# 输出模型结构
print(model)

if torch.cuda.is_available():
    model.to('cuda')

##调整类别不平衡， 假设有两个类别，类别0和类别1
# class_weights = torch.tensor([1.0, 2.0])  # 根据实际情况设置类别权重
# class_weights = class_weights.to('cuda')
# # 定义损失函数，并指定类别权重
# loss_fn = nn.CrossEntropyLoss(weight=class_weights)

# Decay LR by a factor of 0.1 every 7 epochs
loss_fn = nn.CrossEntropyLoss()
from torch.optim import lr_scheduler

# optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.0001) #resnet   优化器要传入最后一层所有参数
# optimizer = torch.optim.SGD(model.fc.parameters(), lr=0.001) #resnet   优化器要传入最后一层所有参数
optimizer = torch.optim.Adam(model.parameters(), lr=0.0002)  #alexnet,vgg注意修改模型最后一层名称
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.001) #densenet模型
# optimizer = torch.optim.Adam(model.heads.parameters(), lr=0.0001)  # vit
# optimizer = torch.optim.Adam(model.head.parameters(), lr=0.001) #swin vit
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

# exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.7)

# In[18]:
def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    model.train()
    for x, clinical_feature, y in trainloader:
        if torch.cuda.is_available():
            x, clinical_feature, y = x.to('cuda'), clinical_feature.to('cuda'), y.to('cuda')
        y_pred = model.forward(x, clinical_feature)  # 传递图像和临床特征给模型的forward方法
        loss = loss_fn(y_pred, y)  # 计算损失
        optimizer.zero_grad()  # 梯度清零，否则会累加
        loss.backward()  # 计算梯度
        optimizer.step()  # 权重更新
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
    exp_lr_scheduler.step()  # 注意这个代码有学习率，因此这里需要加一行
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / total
    test_correct = 0
    test_total = 0
    test_running_loss = 0
    model.eval()
    with torch.no_grad():
        for x, clinical_feature, y in testloader:
            if torch.cuda.is_available():
                x, clinical_feature, y = x.to('cuda'), clinical_feature.to('cuda'), y.to('cuda')
            y_pred = model.forward(x, clinical_feature)  # 传递图像和临床特征给模型的forward方法
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / test_total
    print('epoch:', epoch,
          'train_loss:', round(epoch_loss, 3),
          'train_accuracy:', round(epoch_acc, 3),
          'test_loss:', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
          )
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc
#%%
epochs = 25

train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,
                                                                 model,
                                                                 train_dl,
                                                                 test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
