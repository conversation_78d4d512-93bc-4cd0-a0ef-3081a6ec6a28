{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "SAd865lNzZpT"}, "source": ["#  <span style=\"color:orange\">Regression Tutorial (REG101) - Level Beginner</span>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "yXN8UznszZpc"}, "source": ["**Created using: PyCaret 2.2** <br />\n", "**Date Updated: November 25, 2020**\n", "\n", "# 1.0 チュートリアルの目的\n", "Regression Tutorial (REG101) - Level Beginner へようこそ。このチュートリアルでは、あなたがPyCaretを初めて使い、`pycaret.regression`モジュールを使って回帰を始めようとしていることを想定しています。\n", "\n", "このチュートリアルでは、以下のことを学びます。\n", "\n", "\n", "* **Getting Data:** PyCaret リポジトリからデータをインポートする方法。\n", "* **Setting up Environment:** PyCaretで実験を行い、回帰モデルの構築を開始する方法\n", "* **Create Model:** モデルを作成し、クロスバリデーションを行い、回帰メトリクスを評価する方法\n", "* **Tune Model:** 回帰モデルのハイパーパラメータを自動的にチューニングする方法\n", "* **Plot Model:** 様々なプロットを使用してモデルのパフォーマンスを分析する方法\n", "* **Finalize Model:** 実験の最後に最適なモデルを最終決定する方法\n", "* **Predict Model:** 新しい/未見のデータに対して予測を行う方法\n", "* **Save / Load Model:** 今後の使用のためにモデルを保存/ロードする方法\n", "\n", "読了時間：約30分\n", "\n", "\n", "# 1.1 PyCaretのインストール\n", "PyCaretを使い始めるための最初のステップは、PyCaretをインストールすることです。インストールは簡単で、数分で完了します。以下の説明に従ってください。\n", "\n", "# ローカルJupyterノートブックへのPyCaretのインストール\n", "`pip install pycaret` <br />。\n", "\n", "# Google Colab や Azure Notebooks への PyCaret のインストール\n", "`!pip install pycaret`\n", "\n", "\n", "# 1.2 前提条件\n", "- Python 3.6以上\n", "- PyCaret 2.0 以上\n", "- pycaretのリポジトリからデータを読み込むためのインターネット接続環境\n", "- 回帰の基礎知識\n", "\n", "# 1.3 Google Colabをお使いの方へ。\n", "このノートブックをGoogle colabで動作させている場合、インタラクティブなビジュアルを表示するために、ノートブックの先頭で以下のコードを実行してください。\n", " \n", "`from pycaret.utils import enable_colab` \n", "`enable_colab()`\n", "\n", "# 1.4 See also:\n", "- __[回帰チュートリアル (REG102) - Level Intermediate](https://github.com/pycaret/pycaret/blob/master/tutorials/Regression%20Tutorial%20Level%20Intermediate%20-%20REG102.ipynb)__\n", "- __[回帰チュートリアル (REG103) - Level Expert](https://github.com/pycaret/pycaret/blob/master/tutorials/Regression%20Tutorial%20Level%20Expert%20-%20REG103.ipynb)__"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HuEUiXXhzZpi"}, "source": ["# 2.0 回帰分析とは？\n", "\n", "回帰分析とは、従属変数（しばしば「結果変数」または「ターゲット」と呼ばれる）と1つまたは複数の独立変数（しばしば「特徴」、「予測因子」または「共変量」と呼ばれる）との間の関係を推定するための一連の統計処理です。機械学習における回帰の目的は、売上金額、数量、温度などの連続値を予測することです。\n", "\n", "_[回帰についてもっと知りたい](https://hbr.org/2015/11/a-refresher-on-regression-analysis)__。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "xnEk7n5ZzZpm"}, "source": ["# 3.0 PyCaretの回帰モジュールの概要\n", "PyCaretの回帰モジュール(`pycaret.regression`)は、様々な技術やアルゴリズムを用いて、連続的な値や結果を予測するために使用される教師付き機械学習モジュールです。回帰は、売上、販売数、温度などの値や結果、または連続的な任意の数値を予測するために使用できます。\n", "\n", "PyCaretの回帰モジュールは、25以上のアルゴリズムと、モデルの性能を分析するための10のプロットを備えています。ハイパーパラメータチューニング、アンサンブル、スタックのような高度なテクニックなど、PyCaretの回帰モジュールはすべてを備えています。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "uN95Uqo6zZpq"}, "source": ["# 4.0 Dataset for the Tutorial"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Guj8GFIJzZpu"}, "source": ["このチュートリアルでは、**\"Sarah Gets a Diamond \"**というケーススタディに基づいたデータセットを使います。このケースは、Darden School of Business (University of Virginia)の1年生の意思決定分析のコースで紹介されました。データのベースとなっているのは、絶望的なロマンチストのMBA学生が、花嫁となるサラにふさわしいダイヤモンドを選ぶというケースです。データにはトレーニング用の6000レコードが含まれています。各カラムの簡単な説明は以下の通りです。\n", "\n", "- ID:** 各観測データ（ダイヤモンド）を一意に識別する。\n", "- Carat Weight:** ダイヤモンドの重量をメートル法で表したもの。1カラットは0.2グラムに相当し、ペーパークリップとほぼ同じ重さである。\n", "- Cut：**ダイヤモンドのカットを示す5つの値のうちの1つで、望ましい順に並べたもの（Signature-Ideal、Ideal、Very Good、Good、Fair）。\n", "- Color：**ダイヤモンドのカラーを示す6つの値のうち、望ましい順に1つを選択（D、E、F-無色、G、H、I-無色に近い）。\n", "- Clarity：**ダイヤモンドのクラリティを示す7つの値のうち、望ましい順に1つ（F - Flawless、IF - Internally Flawless、VVS1またはVVS2 - Very, Very Slightly Included、またはVS1またはVS2 - Very Slightly Included、SI1 - Slightly Included）。\n", "- Polish：**ダイヤモンドの研磨状態を示す4つの値のうちの1つ（ID - Ideal、EX - Excellent、VG - Very Good、G - Good）。\n", "- Symmetry：** ダイヤモンドのシンメトリーを示す4つの値のうちの1つ（ID - Ideal, EX - Excellent, VG - Very Good, G - Good）。\n", "- Report:** ダイヤモンドの品質を報告したグレーディング機関を示す2つの値「AGSL」または「GIA」のうちの1つ。\n", "- **Price:** ダイヤモンドの評価額を米ドルで表したもの `Target Column` です。\n", "\n", "\n", "# Dataset Acknowledgement:\n", "このケースは、<PERSON> (Alumni Research Professor of Business Administration)の監修のもと、<PERSON> (MBA '07)が作成しました。Copyright (c) 2007 by the University of Virginia Darden School Foundation, Charlottesville, VA. すべての権利は留保されています。\n", "\n", "オリジナルのデータセットと説明は、__[ここにあります。](https://github.com/DardenDSC/sarah-gets-a-diamond)__。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "wwUzzm1YzZpz"}, "source": ["# 5.0 Getting the Data"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "PFCSZ_NKzZp3"}, "source": ["オリジナルのソースからデータをダウンロードして、pandas __[(Learn How)](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html)__を使ってロードすることもできますし、PyCaretのデータリポジトリを使って、`get_data()`関数を使ってデータをロードすることもできます(この場合はインターネット接続が必要です)。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:12:42.891205Z", "start_time": "2020-04-23T13:12:42.168861Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 191}, "colab_type": "code", "id": "H6qS5U--zZp7", "outputId": "2a11a81c-7e67-425a-a3ef-091d2c9fbd30"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Carat Weight</th>\n", "      <th>Cut</th>\n", "      <th>Color</th>\n", "      <th>Clarity</th>\n", "      <th>Polish</th>\n", "      <th>Symmetry</th>\n", "      <th>Report</th>\n", "      <th>Price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.10</td>\n", "      <td>Ideal</td>\n", "      <td>H</td>\n", "      <td>SI1</td>\n", "      <td>VG</td>\n", "      <td>EX</td>\n", "      <td>GIA</td>\n", "      <td>5169</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.83</td>\n", "      <td>Ideal</td>\n", "      <td>H</td>\n", "      <td>VS1</td>\n", "      <td>ID</td>\n", "      <td>ID</td>\n", "      <td>AGSL</td>\n", "      <td>3470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.85</td>\n", "      <td>Ideal</td>\n", "      <td>H</td>\n", "      <td>SI1</td>\n", "      <td>EX</td>\n", "      <td>EX</td>\n", "      <td>GIA</td>\n", "      <td>3183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.91</td>\n", "      <td>Ideal</td>\n", "      <td>E</td>\n", "      <td>SI1</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>4370</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.83</td>\n", "      <td>Ideal</td>\n", "      <td>G</td>\n", "      <td>SI1</td>\n", "      <td>EX</td>\n", "      <td>EX</td>\n", "      <td>GIA</td>\n", "      <td>3171</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Carat Weight    Cut Color Clarity Polish Symmetry Report  Price\n", "0          1.10  Ideal     H     SI1     VG       EX    GIA   5169\n", "1          0.83  Ideal     H     VS1     ID       ID   AGSL   3470\n", "2          0.85  Ideal     H     SI1     EX       EX    GIA   3183\n", "3          0.91  Ideal     E     SI1     VG       VG    GIA   4370\n", "4          0.83  Ideal     G     SI1     EX       EX    GIA   3171"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pycaret.datasets import get_data\n", "dataset = get_data('diamond')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:12:45.180048Z", "start_time": "2020-04-23T13:12:45.176712Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 33}, "colab_type": "code", "id": "D5PerU66zZqK", "outputId": "2fdd6ab8-7d68-4cc4-81a7-0cb82ed70799"}, "outputs": [{"data": {"text/plain": ["(6000, 8)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#check the shape of data\n", "dataset.shape"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "7eWmeLvYzZqY"}, "source": ["オリジナルのソースからデータをダウンロードして、pandas __[(Learn How)](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html)__を使ってロードすることもできますし、PyCaretのデータリポジトリを使って、`get_data()`関数を使ってデータをロードすることもできます(この場合はインターネット接続が必要です)。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:13:11.922396Z", "start_time": "2020-04-23T13:13:11.912627Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "R4K9F7BXzZqc", "outputId": "22b1c4e7-a1e1-48d2-8ddc-907e716d5b53"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data for Modeling: (5400, 8)\n", "Unseen Data For Predictions: (600, 8)\n"]}], "source": ["data = dataset.sample(frac=0.9, random_state=786)\n", "data_unseen = dataset.drop(data.index)\n", "\n", "data.reset_index(drop=True, inplace=True)\n", "data_unseen.reset_index(drop=True, inplace=True)\n", "\n", "print('Data for Modeling: ' + str(data.shape))\n", "print('Unseen Data For Predictions: ' + str(data_unseen.shape))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "DxnJV14BzZqq"}, "source": ["# 6.0 Setting up Environment in PyCaret"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "15-blMPOzZqw"}, "source": ["`setup()`関数はpycaretの環境を初期化し、モデリングやデプロイメントのためにデータを準備する変換パイプラインを作成します。`setup()`はpycaretの他の関数を実行する前に呼ばれなければなりません。`setup()`は2つの必須パラメータを取ります: pandasデータフレームとターゲットカラムの名前です。他のすべてのパラメータはオプションで、前処理パイプラインをカスタマイズするために使用されます（後のチュートリアルで見てみましょう）。\n", "\n", "`setup()`が実行されると、PyCaretの推論アルゴリズムは、特定のプロパティに基づいて、すべての特徴のデータ型を自動的に推論します。データ型は正しく推論されるべきですが、必ずしもそうではありません。これを考慮して、PyCaretは`setup()`が実行された後に、フィーチャーとその推論されたデータタイプを含むテーブルを表示します。すべてのデータタイプが正しく認識された場合、`enter`を押して続行するか、`quit`をタイプして実験を終了することができます。PyCaretでは、機械学習の実験に不可欠ないくつかの前処理を自動的に行うため、データ型が正しいことを確認することは非常に重要です。これらのタスクはデータタイプごとに実行される内容が異なるため、正しく設定されていることが非常に重要です。\n", "\n", "後のチュートリアルでは、`setup()`の`numeric_features`と`categorical_features`パラメータを使って、PyCaretの推論したデータ型を上書きする方法を学びます。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:16:02.144917Z", "start_time": "2020-04-23T13:15:57.427822Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 803}, "colab_type": "code", "id": "7V2FN4KQzZrA", "outputId": "43d8d23d-ef08-438a-8cc3-ba78e9773aca", "scrolled": false}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_4caf5_row42_col1 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_4caf5_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Description</th>\n", "      <th class=\"col_heading level0 col1\" >Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_4caf5_row0_col0\" class=\"data row0 col0\" >session_id</td>\n", "      <td id=\"T_4caf5_row0_col1\" class=\"data row0 col1\" >123</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_4caf5_row1_col0\" class=\"data row1 col0\" >Target</td>\n", "      <td id=\"T_4caf5_row1_col1\" class=\"data row1 col1\" >Price</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_4caf5_row2_col0\" class=\"data row2 col0\" >Original Data</td>\n", "      <td id=\"T_4caf5_row2_col1\" class=\"data row2 col1\" >(5400, 8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_4caf5_row3_col0\" class=\"data row3 col0\" >Missing Values</td>\n", "      <td id=\"T_4caf5_row3_col1\" class=\"data row3 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_4caf5_row4_col0\" class=\"data row4 col0\" >Numeric Features</td>\n", "      <td id=\"T_4caf5_row4_col1\" class=\"data row4 col1\" >1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_4caf5_row5_col0\" class=\"data row5 col0\" >Categorical Features</td>\n", "      <td id=\"T_4caf5_row5_col1\" class=\"data row5 col1\" >6</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_4caf5_row6_col0\" class=\"data row6 col0\" >Ordinal Features</td>\n", "      <td id=\"T_4caf5_row6_col1\" class=\"data row6 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_4caf5_row7_col0\" class=\"data row7 col0\" >High Cardinality Features</td>\n", "      <td id=\"T_4caf5_row7_col1\" class=\"data row7 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_4caf5_row8_col0\" class=\"data row8 col0\" >High Cardinality Method</td>\n", "      <td id=\"T_4caf5_row8_col1\" class=\"data row8 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_4caf5_row9_col0\" class=\"data row9 col0\" >Transformed Train Set</td>\n", "      <td id=\"T_4caf5_row9_col1\" class=\"data row9 col1\" >(3779, 28)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_4caf5_row10_col0\" class=\"data row10 col0\" >Transformed Test Set</td>\n", "      <td id=\"T_4caf5_row10_col1\" class=\"data row10 col1\" >(1621, 28)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_4caf5_row11_col0\" class=\"data row11 col0\" >Shuffle Train-Test</td>\n", "      <td id=\"T_4caf5_row11_col1\" class=\"data row11 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_4caf5_row12_col0\" class=\"data row12 col0\" >Stratify Train-Test</td>\n", "      <td id=\"T_4caf5_row12_col1\" class=\"data row12 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_4caf5_row13_col0\" class=\"data row13 col0\" >Fold Generator</td>\n", "      <td id=\"T_4caf5_row13_col1\" class=\"data row13 col1\" >KFold</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_4caf5_row14_col0\" class=\"data row14 col0\" >Fold Number</td>\n", "      <td id=\"T_4caf5_row14_col1\" class=\"data row14 col1\" >10</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_4caf5_row15_col0\" class=\"data row15 col0\" >CPU Jobs</td>\n", "      <td id=\"T_4caf5_row15_col1\" class=\"data row15 col1\" >-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_4caf5_row16_col0\" class=\"data row16 col0\" >Use GPU</td>\n", "      <td id=\"T_4caf5_row16_col1\" class=\"data row16 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_4caf5_row17_col0\" class=\"data row17 col0\" >Log Experiment</td>\n", "      <td id=\"T_4caf5_row17_col1\" class=\"data row17 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_4caf5_row18_col0\" class=\"data row18 col0\" >Experiment Name</td>\n", "      <td id=\"T_4caf5_row18_col1\" class=\"data row18 col1\" >reg-default-name</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_4caf5_row19_col0\" class=\"data row19 col0\" >USI</td>\n", "      <td id=\"T_4caf5_row19_col1\" class=\"data row19 col1\" >ad92</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_4caf5_row20_col0\" class=\"data row20 col0\" >Imputation Type</td>\n", "      <td id=\"T_4caf5_row20_col1\" class=\"data row20 col1\" >simple</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_4caf5_row21_col0\" class=\"data row21 col0\" >Iterative Imputation Iteration</td>\n", "      <td id=\"T_4caf5_row21_col1\" class=\"data row21 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_4caf5_row22_col0\" class=\"data row22 col0\" >Numeric Imputer</td>\n", "      <td id=\"T_4caf5_row22_col1\" class=\"data row22 col1\" >mean</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_4caf5_row23_col0\" class=\"data row23 col0\" >Iterative Imputation Numeric Model</td>\n", "      <td id=\"T_4caf5_row23_col1\" class=\"data row23 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_4caf5_row24_col0\" class=\"data row24 col0\" >Categorical Imputer</td>\n", "      <td id=\"T_4caf5_row24_col1\" class=\"data row24 col1\" >constant</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_4caf5_row25_col0\" class=\"data row25 col0\" >Iterative Imputation Categorical Model</td>\n", "      <td id=\"T_4caf5_row25_col1\" class=\"data row25 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_4caf5_row26_col0\" class=\"data row26 col0\" >Unknown Categoricals Handling</td>\n", "      <td id=\"T_4caf5_row26_col1\" class=\"data row26 col1\" >least_frequent</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_4caf5_row27_col0\" class=\"data row27 col0\" >Normalize</td>\n", "      <td id=\"T_4caf5_row27_col1\" class=\"data row27 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_4caf5_row28_col0\" class=\"data row28 col0\" >Normalize Method</td>\n", "      <td id=\"T_4caf5_row28_col1\" class=\"data row28 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_4caf5_row29_col0\" class=\"data row29 col0\" >Transformation</td>\n", "      <td id=\"T_4caf5_row29_col1\" class=\"data row29 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_4caf5_row30_col0\" class=\"data row30 col0\" >Transformation Method</td>\n", "      <td id=\"T_4caf5_row30_col1\" class=\"data row30 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_4caf5_row31_col0\" class=\"data row31 col0\" >PCA</td>\n", "      <td id=\"T_4caf5_row31_col1\" class=\"data row31 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_4caf5_row32_col0\" class=\"data row32 col0\" >PCA Method</td>\n", "      <td id=\"T_4caf5_row32_col1\" class=\"data row32 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_4caf5_row33_col0\" class=\"data row33 col0\" >PCA Components</td>\n", "      <td id=\"T_4caf5_row33_col1\" class=\"data row33 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_4caf5_row34_col0\" class=\"data row34 col0\" >Ignore Low Variance</td>\n", "      <td id=\"T_4caf5_row34_col1\" class=\"data row34 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_4caf5_row35_col0\" class=\"data row35 col0\" >Combine Rare Levels</td>\n", "      <td id=\"T_4caf5_row35_col1\" class=\"data row35 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_4caf5_row36_col0\" class=\"data row36 col0\" >Rare Level Threshold</td>\n", "      <td id=\"T_4caf5_row36_col1\" class=\"data row36 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_4caf5_row37_col0\" class=\"data row37 col0\" >Numeric Binning</td>\n", "      <td id=\"T_4caf5_row37_col1\" class=\"data row37 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_4caf5_row38_col0\" class=\"data row38 col0\" >Remove Outliers</td>\n", "      <td id=\"T_4caf5_row38_col1\" class=\"data row38 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_4caf5_row39_col0\" class=\"data row39 col0\" >Outliers Threshold</td>\n", "      <td id=\"T_4caf5_row39_col1\" class=\"data row39 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "      <td id=\"T_4caf5_row40_col0\" class=\"data row40 col0\" >Remove Multicollinearity</td>\n", "      <td id=\"T_4caf5_row40_col1\" class=\"data row40 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "      <td id=\"T_4caf5_row41_col0\" class=\"data row41 col0\" >Multicollinearity Threshold</td>\n", "      <td id=\"T_4caf5_row41_col1\" class=\"data row41 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "      <td id=\"T_4caf5_row42_col0\" class=\"data row42 col0\" >Remove Perfect Collinearity</td>\n", "      <td id=\"T_4caf5_row42_col1\" class=\"data row42 col1\" >True</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row43\" class=\"row_heading level0 row43\" >43</th>\n", "      <td id=\"T_4caf5_row43_col0\" class=\"data row43 col0\" >Clustering</td>\n", "      <td id=\"T_4caf5_row43_col1\" class=\"data row43 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row44\" class=\"row_heading level0 row44\" >44</th>\n", "      <td id=\"T_4caf5_row44_col0\" class=\"data row44 col0\" >Clustering Iteration</td>\n", "      <td id=\"T_4caf5_row44_col1\" class=\"data row44 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row45\" class=\"row_heading level0 row45\" >45</th>\n", "      <td id=\"T_4caf5_row45_col0\" class=\"data row45 col0\" >Polynomial Features</td>\n", "      <td id=\"T_4caf5_row45_col1\" class=\"data row45 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row46\" class=\"row_heading level0 row46\" >46</th>\n", "      <td id=\"T_4caf5_row46_col0\" class=\"data row46 col0\" >Polynomial Degree</td>\n", "      <td id=\"T_4caf5_row46_col1\" class=\"data row46 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row47\" class=\"row_heading level0 row47\" >47</th>\n", "      <td id=\"T_4caf5_row47_col0\" class=\"data row47 col0\" >Trignometry Features</td>\n", "      <td id=\"T_4caf5_row47_col1\" class=\"data row47 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row48\" class=\"row_heading level0 row48\" >48</th>\n", "      <td id=\"T_4caf5_row48_col0\" class=\"data row48 col0\" >Polynomial Threshold</td>\n", "      <td id=\"T_4caf5_row48_col1\" class=\"data row48 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row49\" class=\"row_heading level0 row49\" >49</th>\n", "      <td id=\"T_4caf5_row49_col0\" class=\"data row49 col0\" >Group Features</td>\n", "      <td id=\"T_4caf5_row49_col1\" class=\"data row49 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row50\" class=\"row_heading level0 row50\" >50</th>\n", "      <td id=\"T_4caf5_row50_col0\" class=\"data row50 col0\" >Feature Selection</td>\n", "      <td id=\"T_4caf5_row50_col1\" class=\"data row50 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row51\" class=\"row_heading level0 row51\" >51</th>\n", "      <td id=\"T_4caf5_row51_col0\" class=\"data row51 col0\" >Feature Selection Method</td>\n", "      <td id=\"T_4caf5_row51_col1\" class=\"data row51 col1\" >classic</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row52\" class=\"row_heading level0 row52\" >52</th>\n", "      <td id=\"T_4caf5_row52_col0\" class=\"data row52 col0\" >Features Selection Threshold</td>\n", "      <td id=\"T_4caf5_row52_col1\" class=\"data row52 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row53\" class=\"row_heading level0 row53\" >53</th>\n", "      <td id=\"T_4caf5_row53_col0\" class=\"data row53 col0\" >Feature Interaction</td>\n", "      <td id=\"T_4caf5_row53_col1\" class=\"data row53 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row54\" class=\"row_heading level0 row54\" >54</th>\n", "      <td id=\"T_4caf5_row54_col0\" class=\"data row54 col0\" >Feature Ratio</td>\n", "      <td id=\"T_4caf5_row54_col1\" class=\"data row54 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row55\" class=\"row_heading level0 row55\" >55</th>\n", "      <td id=\"T_4caf5_row55_col0\" class=\"data row55 col0\" >Interaction Threshold</td>\n", "      <td id=\"T_4caf5_row55_col1\" class=\"data row55 col1\" >None</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row56\" class=\"row_heading level0 row56\" >56</th>\n", "      <td id=\"T_4caf5_row56_col0\" class=\"data row56 col0\" >Transform Target</td>\n", "      <td id=\"T_4caf5_row56_col1\" class=\"data row56 col1\" >False</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_4caf5_level0_row57\" class=\"row_heading level0 row57\" >57</th>\n", "      <td id=\"T_4caf5_row57_col0\" class=\"data row57 col0\" >Transform Target Method</td>\n", "      <td id=\"T_4caf5_row57_col1\" class=\"data row57 col1\" >box-cox</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1918f2841c8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pycaret.regression import *\n", "exp_reg101 = setup(data = data, target = 'Price', session_id=123) "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nWBypX32zZrP"}, "source": ["セットアップが正常に実行されると，いくつかの重要な情報を含む情報グリッドが表示されます。ほとんどの情報は、`setup()`の実行時に構築される前処理パイプラインに関連しています。これらの機能の大部分は、このチュートリアルの目的からは外れています。しかし、この段階で注意すべきいくつかの重要な点があります。\n", "\n", "- **session_id :** 後の再現性のために、すべての関数でシードとして配布される疑似乱数です。もし`session_id`が渡されない場合は、自動的に乱数が生成され、すべての関数に配布されます。この実験では、後の再現性のために、`session_id`を`123`としています。\n", "- **Original Data :** データセットの元の形を表示します。 **元のデータ :**元のデータの形を表示します。\n", "\n", "- **Missing Values :** 元のデータに欠損値がある場合は、Trueと表示されます。この実験では、データセットに欠損値はありません。 \n", " \n", "- **Numeric Features :** 数値として推定された特徴の数です。このデータセットでは、8つの特徴のうち1つが数値として推論されています。 \n", " \n", "- **Categorical Features :** カテゴライズされた特徴の数。このデータセットでは、8つの特徴のうち6つがカテゴライズされています。\n", " \n", "- **Transformed Train Set :** 変換後のトレーニングセットの形状を表示します。元の形状である(5400, 8)が、変換後の訓練セットでは(3779, 28)に変換されていることに注目してください。カテゴリーエンコーディングにより、特徴量の数が28から8に増えています 。\n", "\n", "- **Transformed Test Set :** 変換されたテストセット/ホールドアウトセットの形状を表示します。test/hold-out setには1621個のサンプルがあります。この分割は、デフォルトの70/30に基づいていますが、セットアップの`train_size`パラメータで変更することができます。\n", "\n", "欠損値のインプテーション（この場合、トレーニングデータには欠損値はありませんが、見たことのないデータのインプテーションが必要です）やカテゴリーエンコーディングなど、モデリングを行う上で必須となるいくつかのタスクが自動的に処理されていることに注目してください。setup()`のパラメータのほとんどはオプションで，前処理パイプラインをカスタマイズするために使われます。このチュートリアルでは、これらのパラメータは対象外ですが、中級者、上級者になるにつれて、より詳細に説明していきます。\n", "\n"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "xBqHzabEzZrT"}, "source": ["# 7.0 Comparing All Models"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "QHiNl6UmzZrW"}, "source": ["すべてのモデルを比較して性能を評価することは、セットアップが完了した後のモデリングの出発点として推奨されています（どのようなモデルが必要かを正確に把握している場合は別ですが、そうでない場合も多いです）。この関数は、モデルライブラリ内のすべてのモデルを学習し、k-foldクロスバリデーションを用いてスコアリングを行い、メトリクス評価を行います。出力は、フォールド（デフォルトでは10）間の平均MAE、MSE、RMSE、R2、RMSLE、MAPEを学習時間とともに示すスコアグリッドを表示します。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:21:22.884291Z", "start_time": "2020-04-23T13:20:11.108031Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 421}, "colab_type": "code", "id": "atJfMGD6zZrb", "outputId": "af936da0-cc93-4429-ef61-20d940e0aa4e", "scrolled": false}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_9aab7_ th {\n", "  text-align: left;\n", "}\n", "#T_9aab7_row0_col0, #T_9aab7_row0_col1, #T_9aab7_row0_col5, #T_9aab7_row0_col6, #T_9aab7_row1_col0, #T_9aab7_row1_col1, #T_9aab7_row1_col2, #T_9aab7_row1_col3, #T_9aab7_row1_col4, #T_9aab7_row1_col5, #T_9aab7_row1_col6, #T_9aab7_row2_col0, #T_9aab7_row2_col2, #T_9aab7_row2_col3, #T_9aab7_row2_col4, #T_9aab7_row3_col0, #T_9aab7_row3_col1, #T_9aab7_row3_col2, #T_9aab7_row3_col3, #T_9aab7_row3_col4, #T_9aab7_row3_col5, #T_9aab7_row3_col6, #T_9aab7_row4_col0, #T_9aab7_row4_col1, #T_9aab7_row4_col2, #T_9aab7_row4_col3, #T_9aab7_row4_col4, #T_9aab7_row4_col5, #T_9aab7_row4_col6, #T_9aab7_row5_col0, #T_9aab7_row5_col1, #T_9aab7_row5_col2, #T_9aab7_row5_col3, #T_9aab7_row5_col4, #T_9aab7_row5_col5, #T_9aab7_row5_col6, #T_9aab7_row6_col0, #T_9aab7_row6_col1, #T_9aab7_row6_col2, #T_9aab7_row6_col3, #T_9aab7_row6_col4, #T_9aab7_row6_col5, #T_9aab7_row6_col6, #T_9aab7_row7_col0, #T_9aab7_row7_col1, #T_9aab7_row7_col2, #T_9aab7_row7_col3, #T_9aab7_row7_col4, #T_9aab7_row7_col5, #T_9aab7_row7_col6, #T_9aab7_row8_col0, #T_9aab7_row8_col1, #T_9aab7_row8_col2, #T_9aab7_row8_col3, #T_9aab7_row8_col4, #T_9aab7_row8_col5, #T_9aab7_row8_col6, #T_9aab7_row9_col0, #T_9aab7_row9_col1, #T_9aab7_row9_col2, #T_9aab7_row9_col3, #T_9aab7_row9_col4, #T_9aab7_row9_col5, #T_9aab7_row9_col6, #T_9aab7_row10_col0, #T_9aab7_row10_col1, #T_9aab7_row10_col2, #T_9aab7_row10_col3, #T_9aab7_row10_col4, #T_9aab7_row10_col5, #T_9aab7_row10_col6, #T_9aab7_row11_col0, #T_9aab7_row11_col1, #T_9aab7_row11_col2, #T_9aab7_row11_col3, #T_9aab7_row11_col4, #T_9aab7_row11_col5, #T_9aab7_row11_col6, #T_9aab7_row12_col0, #T_9aab7_row12_col1, #T_9aab7_row12_col2, #T_9aab7_row12_col3, #T_9aab7_row12_col4, #T_9aab7_row12_col5, #T_9aab7_row12_col6, #T_9aab7_row13_col0, #T_9aab7_row13_col1, #T_9aab7_row13_col2, #T_9aab7_row13_col3, #T_9aab7_row13_col4, #T_9aab7_row13_col5, #T_9aab7_row13_col6, #T_9aab7_row14_col0, #T_9aab7_row14_col1, #T_9aab7_row14_col2, #T_9aab7_row14_col3, #T_9aab7_row14_col4, #T_9aab7_row14_col5, #T_9aab7_row14_col6, #T_9aab7_row15_col0, #T_9aab7_row15_col1, #T_9aab7_row15_col2, #T_9aab7_row15_col3, #T_9aab7_row15_col4, #T_9aab7_row15_col5, #T_9aab7_row15_col6, #T_9aab7_row16_col0, #T_9aab7_row16_col1, #T_9aab7_row16_col2, #T_9aab7_row16_col3, #T_9aab7_row16_col4, #T_9aab7_row16_col5, #T_9aab7_row16_col6, #T_9aab7_row17_col0, #T_9aab7_row17_col1, #T_9aab7_row17_col2, #T_9aab7_row17_col3, #T_9aab7_row17_col4, #T_9aab7_row17_col5, #T_9aab7_row17_col6 {\n", "  text-align: left;\n", "}\n", "#T_9aab7_row0_col2, #T_9aab7_row0_col3, #T_9aab7_row0_col4, #T_9aab7_row2_col1, #T_9aab7_row2_col5, #T_9aab7_row2_col6 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "}\n", "#T_9aab7_row0_col7, #T_9aab7_row1_col7, #T_9aab7_row2_col7, #T_9aab7_row3_col7, #T_9aab7_row4_col7, #T_9aab7_row5_col7, #T_9aab7_row6_col7, #T_9aab7_row7_col7, #T_9aab7_row8_col7, #T_9aab7_row9_col7, #T_9aab7_row10_col7, #T_9aab7_row11_col7, #T_9aab7_row13_col7, #T_9aab7_row14_col7, #T_9aab7_row15_col7, #T_9aab7_row17_col7 {\n", "  text-align: left;\n", "  background-color: lightgrey;\n", "}\n", "#T_9aab7_row12_col7, #T_9aab7_row16_col7 {\n", "  text-align: left;\n", "  background-color: yellow;\n", "  background-color: lightgrey;\n", "}\n", "</style>\n", "<table id=\"T_9aab7_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Model</th>\n", "      <th class=\"col_heading level0 col1\" >MAE</th>\n", "      <th class=\"col_heading level0 col2\" >MSE</th>\n", "      <th class=\"col_heading level0 col3\" >RMSE</th>\n", "      <th class=\"col_heading level0 col4\" >R2</th>\n", "      <th class=\"col_heading level0 col5\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col6\" >MAPE</th>\n", "      <th class=\"col_heading level0 col7\" >TT (Sec)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row0\" class=\"row_heading level0 row0\" >et</th>\n", "      <td id=\"T_9aab7_row0_col0\" class=\"data row0 col0\" >Extra Trees Regressor</td>\n", "      <td id=\"T_9aab7_row0_col1\" class=\"data row0 col1\" >762.0118</td>\n", "      <td id=\"T_9aab7_row0_col2\" class=\"data row0 col2\" >2763999.1585</td>\n", "      <td id=\"T_9aab7_row0_col3\" class=\"data row0 col3\" >1612.2410</td>\n", "      <td id=\"T_9aab7_row0_col4\" class=\"data row0 col4\" >0.9729</td>\n", "      <td id=\"T_9aab7_row0_col5\" class=\"data row0 col5\" >0.0817</td>\n", "      <td id=\"T_9aab7_row0_col6\" class=\"data row0 col6\" >0.0607</td>\n", "      <td id=\"T_9aab7_row0_col7\" class=\"data row0 col7\" >0.6770</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row1\" class=\"row_heading level0 row1\" >rf</th>\n", "      <td id=\"T_9aab7_row1_col0\" class=\"data row1 col0\" >Random Forest Regressor</td>\n", "      <td id=\"T_9aab7_row1_col1\" class=\"data row1 col1\" >760.6304</td>\n", "      <td id=\"T_9aab7_row1_col2\" class=\"data row1 col2\" >2929683.1860</td>\n", "      <td id=\"T_9aab7_row1_col3\" class=\"data row1 col3\" >1663.0148</td>\n", "      <td id=\"T_9aab7_row1_col4\" class=\"data row1 col4\" >0.9714</td>\n", "      <td id=\"T_9aab7_row1_col5\" class=\"data row1 col5\" >0.0818</td>\n", "      <td id=\"T_9aab7_row1_col6\" class=\"data row1 col6\" >0.0597</td>\n", "      <td id=\"T_9aab7_row1_col7\" class=\"data row1 col7\" >0.6030</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row2\" class=\"row_heading level0 row2\" >lightgbm</th>\n", "      <td id=\"T_9aab7_row2_col0\" class=\"data row2 col0\" >Light Gradient Boosting Machine</td>\n", "      <td id=\"T_9aab7_row2_col1\" class=\"data row2 col1\" >752.6446</td>\n", "      <td id=\"T_9aab7_row2_col2\" class=\"data row2 col2\" >3056347.8515</td>\n", "      <td id=\"T_9aab7_row2_col3\" class=\"data row2 col3\" >1687.9907</td>\n", "      <td id=\"T_9aab7_row2_col4\" class=\"data row2 col4\" >0.9711</td>\n", "      <td id=\"T_9aab7_row2_col5\" class=\"data row2 col5\" >0.0773</td>\n", "      <td id=\"T_9aab7_row2_col6\" class=\"data row2 col6\" >0.0567</td>\n", "      <td id=\"T_9aab7_row2_col7\" class=\"data row2 col7\" >0.0690</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row3\" class=\"row_heading level0 row3\" >gbr</th>\n", "      <td id=\"T_9aab7_row3_col0\" class=\"data row3 col0\" >Gradient Boosting Regressor</td>\n", "      <td id=\"T_9aab7_row3_col1\" class=\"data row3 col1\" >920.2913</td>\n", "      <td id=\"T_9aab7_row3_col2\" class=\"data row3 col2\" >3764303.9252</td>\n", "      <td id=\"T_9aab7_row3_col3\" class=\"data row3 col3\" >1901.1793</td>\n", "      <td id=\"T_9aab7_row3_col4\" class=\"data row3 col4\" >0.9633</td>\n", "      <td id=\"T_9aab7_row3_col5\" class=\"data row3 col5\" >0.1024</td>\n", "      <td id=\"T_9aab7_row3_col6\" class=\"data row3 col6\" >0.0770</td>\n", "      <td id=\"T_9aab7_row3_col7\" class=\"data row3 col7\" >0.1790</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row4\" class=\"row_heading level0 row4\" >dt</th>\n", "      <td id=\"T_9aab7_row4_col0\" class=\"data row4 col0\" >Decision Tree Regressor</td>\n", "      <td id=\"T_9aab7_row4_col1\" class=\"data row4 col1\" >1003.1237</td>\n", "      <td id=\"T_9aab7_row4_col2\" class=\"data row4 col2\" >5305620.3379</td>\n", "      <td id=\"T_9aab7_row4_col3\" class=\"data row4 col3\" >2228.7271</td>\n", "      <td id=\"T_9aab7_row4_col4\" class=\"data row4 col4\" >0.9476</td>\n", "      <td id=\"T_9aab7_row4_col5\" class=\"data row4 col5\" >0.1083</td>\n", "      <td id=\"T_9aab7_row4_col6\" class=\"data row4 col6\" >0.0775</td>\n", "      <td id=\"T_9aab7_row4_col7\" class=\"data row4 col7\" >0.0250</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row5\" class=\"row_heading level0 row5\" >ridge</th>\n", "      <td id=\"T_9aab7_row5_col0\" class=\"data row5 col0\" >Ridge Regression</td>\n", "      <td id=\"T_9aab7_row5_col1\" class=\"data row5 col1\" >2413.5699</td>\n", "      <td id=\"T_9aab7_row5_col2\" class=\"data row5 col2\" >14120502.6658</td>\n", "      <td id=\"T_9aab7_row5_col3\" class=\"data row5 col3\" >3726.1654</td>\n", "      <td id=\"T_9aab7_row5_col4\" class=\"data row5 col4\" >0.8621</td>\n", "      <td id=\"T_9aab7_row5_col5\" class=\"data row5 col5\" >0.6689</td>\n", "      <td id=\"T_9aab7_row5_col6\" class=\"data row5 col6\" >0.2875</td>\n", "      <td id=\"T_9aab7_row5_col7\" class=\"data row5 col7\" >0.0240</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row6\" class=\"row_heading level0 row6\" >lasso</th>\n", "      <td id=\"T_9aab7_row6_col0\" class=\"data row6 col0\" >Lasso Regression</td>\n", "      <td id=\"T_9aab7_row6_col1\" class=\"data row6 col1\" >2412.1923</td>\n", "      <td id=\"T_9aab7_row6_col2\" class=\"data row6 col2\" >14246797.4336</td>\n", "      <td id=\"T_9aab7_row6_col3\" class=\"data row6 col3\" >3744.2305</td>\n", "      <td id=\"T_9aab7_row6_col4\" class=\"data row6 col4\" >0.8608</td>\n", "      <td id=\"T_9aab7_row6_col5\" class=\"data row6 col5\" >0.6767</td>\n", "      <td id=\"T_9aab7_row6_col6\" class=\"data row6 col6\" >0.2866</td>\n", "      <td id=\"T_9aab7_row6_col7\" class=\"data row6 col7\" >0.0220</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row7\" class=\"row_heading level0 row7\" >llar</th>\n", "      <td id=\"T_9aab7_row7_col0\" class=\"data row7 col0\" >Lasso Least Angle Regression</td>\n", "      <td id=\"T_9aab7_row7_col1\" class=\"data row7 col1\" >2355.6152</td>\n", "      <td id=\"T_9aab7_row7_col2\" class=\"data row7 col2\" >14272020.0301</td>\n", "      <td id=\"T_9aab7_row7_col3\" class=\"data row7 col3\" >3745.3094</td>\n", "      <td id=\"T_9aab7_row7_col4\" class=\"data row7 col4\" >0.8607</td>\n", "      <td id=\"T_9aab7_row7_col5\" class=\"data row7 col5\" >0.6391</td>\n", "      <td id=\"T_9aab7_row7_col6\" class=\"data row7 col6\" >0.2728</td>\n", "      <td id=\"T_9aab7_row7_col7\" class=\"data row7 col7\" >0.0210</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row8\" class=\"row_heading level0 row8\" >br</th>\n", "      <td id=\"T_9aab7_row8_col0\" class=\"data row8 col0\" >Bayesian Ridge</td>\n", "      <td id=\"T_9aab7_row8_col1\" class=\"data row8 col1\" >2415.8031</td>\n", "      <td id=\"T_9aab7_row8_col2\" class=\"data row8 col2\" >14270771.8397</td>\n", "      <td id=\"T_9aab7_row8_col3\" class=\"data row8 col3\" >3746.9951</td>\n", "      <td id=\"T_9aab7_row8_col4\" class=\"data row8 col4\" >0.8606</td>\n", "      <td id=\"T_9aab7_row8_col5\" class=\"data row8 col5\" >0.6696</td>\n", "      <td id=\"T_9aab7_row8_col6\" class=\"data row8 col6\" >0.2873</td>\n", "      <td id=\"T_9aab7_row8_col7\" class=\"data row8 col7\" >0.0230</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row9\" class=\"row_heading level0 row9\" >lr</th>\n", "      <td id=\"T_9aab7_row9_col0\" class=\"data row9 col0\" >Linear Regression</td>\n", "      <td id=\"T_9aab7_row9_col1\" class=\"data row9 col1\" >2461.5495</td>\n", "      <td id=\"T_9aab7_row9_col2\" class=\"data row9 col2\" >14640851.5481</td>\n", "      <td id=\"T_9aab7_row9_col3\" class=\"data row9 col3\" >3784.8908</td>\n", "      <td id=\"T_9aab7_row9_col4\" class=\"data row9 col4\" >0.8576</td>\n", "      <td id=\"T_9aab7_row9_col5\" class=\"data row9 col5\" >0.6667</td>\n", "      <td id=\"T_9aab7_row9_col6\" class=\"data row9 col6\" >0.2937</td>\n", "      <td id=\"T_9aab7_row9_col7\" class=\"data row9 col7\" >1.3470</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row10\" class=\"row_heading level0 row10\" >huber</th>\n", "      <td id=\"T_9aab7_row10_col0\" class=\"data row10 col0\" >Huber Regressor</td>\n", "      <td id=\"T_9aab7_row10_col1\" class=\"data row10 col1\" >1936.1480</td>\n", "      <td id=\"T_9aab7_row10_col2\" class=\"data row10 col2\" >18599218.3068</td>\n", "      <td id=\"T_9aab7_row10_col3\" class=\"data row10 col3\" >4252.8744</td>\n", "      <td id=\"T_9aab7_row10_col4\" class=\"data row10 col4\" >0.8209</td>\n", "      <td id=\"T_9aab7_row10_col5\" class=\"data row10 col5\" >0.4333</td>\n", "      <td id=\"T_9aab7_row10_col6\" class=\"data row10 col6\" >0.1657</td>\n", "      <td id=\"T_9aab7_row10_col7\" class=\"data row10 col7\" >0.0880</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row11\" class=\"row_heading level0 row11\" >par</th>\n", "      <td id=\"T_9aab7_row11_col0\" class=\"data row11 col0\" >Passive Aggressive Regressor</td>\n", "      <td id=\"T_9aab7_row11_col1\" class=\"data row11 col1\" >1944.1634</td>\n", "      <td id=\"T_9aab7_row11_col2\" class=\"data row11 col2\" >19955672.9330</td>\n", "      <td id=\"T_9aab7_row11_col3\" class=\"data row11 col3\" >4400.2133</td>\n", "      <td id=\"T_9aab7_row11_col4\" class=\"data row11 col4\" >0.8083</td>\n", "      <td id=\"T_9aab7_row11_col5\" class=\"data row11 col5\" >0.4317</td>\n", "      <td id=\"T_9aab7_row11_col6\" class=\"data row11 col6\" >0.1594</td>\n", "      <td id=\"T_9aab7_row11_col7\" class=\"data row11 col7\" >0.0510</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row12\" class=\"row_heading level0 row12\" >omp</th>\n", "      <td id=\"T_9aab7_row12_col0\" class=\"data row12 col0\" >Orthogonal Matching Pursuit</td>\n", "      <td id=\"T_9aab7_row12_col1\" class=\"data row12 col1\" >2792.7313</td>\n", "      <td id=\"T_9aab7_row12_col2\" class=\"data row12 col2\" >23728653.8041</td>\n", "      <td id=\"T_9aab7_row12_col3\" class=\"data row12 col3\" >4829.3170</td>\n", "      <td id=\"T_9aab7_row12_col4\" class=\"data row12 col4\" >0.7678</td>\n", "      <td id=\"T_9aab7_row12_col5\" class=\"data row12 col5\" >0.5819</td>\n", "      <td id=\"T_9aab7_row12_col6\" class=\"data row12 col6\" >0.2654</td>\n", "      <td id=\"T_9aab7_row12_col7\" class=\"data row12 col7\" >0.0170</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row13\" class=\"row_heading level0 row13\" >ada</th>\n", "      <td id=\"T_9aab7_row13_col0\" class=\"data row13 col0\" >AdaBoost Regressor</td>\n", "      <td id=\"T_9aab7_row13_col1\" class=\"data row13 col1\" >4232.2217</td>\n", "      <td id=\"T_9aab7_row13_col2\" class=\"data row13 col2\" >25201423.0703</td>\n", "      <td id=\"T_9aab7_row13_col3\" class=\"data row13 col3\" >5012.4175</td>\n", "      <td id=\"T_9aab7_row13_col4\" class=\"data row13 col4\" >0.7467</td>\n", "      <td id=\"T_9aab7_row13_col5\" class=\"data row13 col5\" >0.5102</td>\n", "      <td id=\"T_9aab7_row13_col6\" class=\"data row13 col6\" >0.5970</td>\n", "      <td id=\"T_9aab7_row13_col7\" class=\"data row13 col7\" >0.1970</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row14\" class=\"row_heading level0 row14\" >knn</th>\n", "      <td id=\"T_9aab7_row14_col0\" class=\"data row14 col0\" >K Neighbors Regressor</td>\n", "      <td id=\"T_9aab7_row14_col1\" class=\"data row14 col1\" >2968.0750</td>\n", "      <td id=\"T_9aab7_row14_col2\" class=\"data row14 col2\" >29627913.0479</td>\n", "      <td id=\"T_9aab7_row14_col3\" class=\"data row14 col3\" >5421.7241</td>\n", "      <td id=\"T_9aab7_row14_col4\" class=\"data row14 col4\" >0.7051</td>\n", "      <td id=\"T_9aab7_row14_col5\" class=\"data row14 col5\" >0.3664</td>\n", "      <td id=\"T_9aab7_row14_col6\" class=\"data row14 col6\" >0.2730</td>\n", "      <td id=\"T_9aab7_row14_col7\" class=\"data row14 col7\" >0.0730</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row15\" class=\"row_heading level0 row15\" >en</th>\n", "      <td id=\"T_9aab7_row15_col0\" class=\"data row15 col0\" >Elastic Net</td>\n", "      <td id=\"T_9aab7_row15_col1\" class=\"data row15 col1\" >5029.5913</td>\n", "      <td id=\"T_9aab7_row15_col2\" class=\"data row15 col2\" >56399795.4163</td>\n", "      <td id=\"T_9aab7_row15_col3\" class=\"data row15 col3\" >7467.6597</td>\n", "      <td id=\"T_9aab7_row15_col4\" class=\"data row15 col4\" >0.4472</td>\n", "      <td id=\"T_9aab7_row15_col5\" class=\"data row15 col5\" >0.5369</td>\n", "      <td id=\"T_9aab7_row15_col6\" class=\"data row15 col6\" >0.5845</td>\n", "      <td id=\"T_9aab7_row15_col7\" class=\"data row15 col7\" >0.0220</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row16\" class=\"row_heading level0 row16\" >dummy</th>\n", "      <td id=\"T_9aab7_row16_col0\" class=\"data row16 col0\" >Dummy Regressor</td>\n", "      <td id=\"T_9aab7_row16_col1\" class=\"data row16 col1\" >7280.3308</td>\n", "      <td id=\"T_9aab7_row16_col2\" class=\"data row16 col2\" >101221941.4046</td>\n", "      <td id=\"T_9aab7_row16_col3\" class=\"data row16 col3\" >10032.1624</td>\n", "      <td id=\"T_9aab7_row16_col4\" class=\"data row16 col4\" >-0.0014</td>\n", "      <td id=\"T_9aab7_row16_col5\" class=\"data row16 col5\" >0.7606</td>\n", "      <td id=\"T_9aab7_row16_col6\" class=\"data row16 col6\" >0.8969</td>\n", "      <td id=\"T_9aab7_row16_col7\" class=\"data row16 col7\" >0.0170</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_9aab7_level0_row17\" class=\"row_heading level0 row17\" >lar</th>\n", "      <td id=\"T_9aab7_row17_col0\" class=\"data row17 col0\" >Least Angle Regression</td>\n", "      <td id=\"T_9aab7_row17_col1\" class=\"data row17 col1\" >1156619867.1566</td>\n", "      <td id=\"T_9aab7_row17_col2\" class=\"data row17 col2\" >24458617019283066880.0000</td>\n", "      <td id=\"T_9aab7_row17_col3\" class=\"data row17 col3\" >1563928524.7998</td>\n", "      <td id=\"T_9aab7_row17_col4\" class=\"data row17 col4\" >-305063796584.2870</td>\n", "      <td id=\"T_9aab7_row17_col5\" class=\"data row17 col5\" >1.9696</td>\n", "      <td id=\"T_9aab7_row17_col6\" class=\"data row17 col6\" >172896.7697</td>\n", "      <td id=\"T_9aab7_row17_col7\" class=\"data row17 col7\" >0.0260</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1918f589b08>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["best = compare_models(exclude = ['ransac'])"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "epD0BEVyzZrr"}, "source": ["2つのシンプルなコード ***（1行ではありません）*** は、クロスバリデーションを用いて20以上のモデルをトレーニング、評価しました。上に表示されているスコアグリッドは、比較のために最も高いパフォーマンスのメトリックを強調しています。デフォルトでは、「R2」でソートされていますが、「sort」パラメータを渡すことで変更できます。例えば，`compare_models(sort = 'RMSLE')` は，RMSLE でグリッドをソートします（低い方が良いので，低い方から高い方へ）．foldパラメータをデフォルトの`10`から別の値に変更したい場合は，`fold`パラメータを使用します．例えば，`compare_models(fold = 5)` は，すべてのモデルを5回のクロスバリデーションで比較します．フォールドの数を減らすことで、学習時間を短縮することができます。デフォルトでは、compare_models はデフォルトのソート順に基づいて最もパフォーマンスの高いモデルを返しますが、`n_select` パラメータを使用することで、上位 N 個のモデルのリストを返すことができます。\n", "\n", "特定のモデル（ここでは `RANSAC` ）をブロックするために `exclude` パラメータが使用されていることに注目してください。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZzpBazV1zZrx"}, "source": ["# 8.0 Create a Model"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "IPqPRp5OzZr1"}, "source": ["`create_model`は、PyCaretの中で最も粒度の高い関数であり、PyCaretのほとんどの機能の基礎となっています。名前が示すように、この関数は、foldパラメータで設定できるクロスバリデーションを使用して、モデルのトレーニングと評価を行います。出力は、フォールドごとのMAE、MSE、RMSE、R2、RMSLE、MAPEを示すスコアグリッドを表示します。\n", "\n", "このチュートリアルの残りの部分では、以下のモデルを候補モデルとして使用します。ここでの選択は説明のためのものであり、必ずしもこれらが最高のパフォーマンスであったり、この種のデータにとって理想的であることを意味するものではありません。\n", "\n", "- AdaBoost Regressor ('anda')\n", "- Light Gradient Boosting Machine ('lightgbm') \n", "- 決定木 ('dt')\n", "\n", "PyCaretのモデルライブラリには25個のリグレッサーがあります。全てのレグレッサーのリストを見るには、docstringを確認するか、`models`関数を使ってライブラリを確認してください。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Reference</th>\n", "      <th>Turbo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>lr</th>\n", "      <td>Linear Regression</td>\n", "      <td>sklearn.linear_model._base.LinearRegression</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lasso</th>\n", "      <td>Lasso Regression</td>\n", "      <td>sklearn.linear_model._coordinate_descent.Lasso</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ridge</th>\n", "      <td>Ridge Regression</td>\n", "      <td>sklearn.linear_model._ridge.Ridge</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>en</th>\n", "      <td>Elastic Net</td>\n", "      <td>sklearn.linear_model._coordinate_descent.Elast...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lar</th>\n", "      <td>Least Angle Regression</td>\n", "      <td>sklearn.linear_model._least_angle.Lars</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>llar</th>\n", "      <td><PERSON>so Least Angle Regression</td>\n", "      <td>sklearn.linear_model._least_angle.LassoLars</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>omp</th>\n", "      <td>Orthogonal Matching Pursuit</td>\n", "      <td>sklearn.linear_model._omp.OrthogonalMatchingPu...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>br</th>\n", "      <td>Bayesian Ridge</td>\n", "      <td>sklearn.linear_model._bayes.BayesianRidge</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ard</th>\n", "      <td>Automatic Relevance Determination</td>\n", "      <td>sklearn.linear_model._bayes.ARDRegression</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>par</th>\n", "      <td>Passive Aggressive Regressor</td>\n", "      <td>sklearn.linear_model._passive_aggressive.Passi...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ransac</th>\n", "      <td><PERSON> Consensus</td>\n", "      <td>sklearn.linear_model._ransac.RANSACRegressor</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>tr</th>\n", "      <td>TheilSen Regressor</td>\n", "      <td>sklearn.linear_model._theil_sen.TheilSenRegressor</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>huber</th>\n", "      <td><PERSON><PERSON> Regressor</td>\n", "      <td>sklearn.linear_model._huber.HuberRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>kr</th>\n", "      <td>Kernel Ridge</td>\n", "      <td>sklearn.kernel_ridge.KernelRidge</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>svm</th>\n", "      <td>Support Vector Regression</td>\n", "      <td>sklearn.svm._classes.SVR</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>knn</th>\n", "      <td><PERSON> Regressor</td>\n", "      <td>sklearn.neighbors._regression.KNeighborsRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>dt</th>\n", "      <td>Decision Tree Regressor</td>\n", "      <td>sklearn.tree._classes.DecisionTreeRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rf</th>\n", "      <td>Random Forest Regressor</td>\n", "      <td>sklearn.ensemble._forest.RandomForestRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>et</th>\n", "      <td>Extra Trees Regressor</td>\n", "      <td>sklearn.ensemble._forest.ExtraTreesRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ada</th>\n", "      <td>AdaBoost Regressor</td>\n", "      <td>sklearn.ensemble._weight_boosting.AdaBoostRegr...</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gbr</th>\n", "      <td>Gradient Boosting Regressor</td>\n", "      <td>sklearn.ensemble._gb.GradientBoostingRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mlp</th>\n", "      <td>MLP Regressor</td>\n", "      <td>pycaret.internal.tunable.TunableMLPRegressor</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>xgboost</th>\n", "      <td>Extreme Gradient Boosting</td>\n", "      <td>xgboost.sklearn.XGBRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lightgbm</th>\n", "      <td>Light Gradient Boosting Machine</td>\n", "      <td>lightgbm.sklearn.LGBMRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>catboost</th>\n", "      <td>CatBoost Regressor</td>\n", "      <td>catboost.core.CatBoostRegressor</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       Name  \\\n", "ID                                            \n", "lr                        Linear Regression   \n", "lasso                      <PERSON>   \n", "ridge                      Ridge Regression   \n", "en                              Elastic Net   \n", "lar                  <PERSON>st Angle Regression   \n", "<PERSON><PERSON>           <PERSON><PERSON> Least Angle Regression   \n", "omp             Orthogonal Matching Pursuit   \n", "br                           Bayesian Ridge   \n", "ard       Automatic Relevance Determination   \n", "par            Passive Aggressive Regressor   \n", "ransac              <PERSON> Sam<PERSON> Consensus   \n", "tr                       <PERSON><PERSON><PERSON><PERSON> Regressor   \n", "<PERSON><PERSON>   \n", "kr                             Kernel Ridge   \n", "svm               Support Vector Regression   \n", "knn                   K Neighbors Regressor   \n", "dt                  Decision Tree Regressor   \n", "rf                  Random Forest Regressor   \n", "et                    Extra Trees Regressor   \n", "ada                      <PERSON>t Regressor   \n", "gbr             Gradient Boosting Regressor   \n", "mlp                           MLP Regressor   \n", "xgboost           Extreme Gradient Boosting   \n", "lightgbm    Light Gradient Boosting Machine   \n", "catboost                 CatBoost Regressor   \n", "\n", "                                                  Reference  Turbo  \n", "ID                                                                  \n", "lr              sklearn.linear_model._base.LinearRegression   True  \n", "lasso        sklearn.linear_model._coordinate_descent.Lasso   True  \n", "ridge                     sklearn.linear_model._ridge.Ridge   True  \n", "en        sklearn.linear_model._coordinate_descent.Elast...   True  \n", "lar                  sklearn.linear_model._least_angle.<PERSON>  \n", "llar            sklearn.linear_model._least_angle.LassoLars   True  \n", "omp       sklearn.linear_model._omp.OrthogonalMatchingPu...   True  \n", "br                sklearn.linear_model._bayes.BayesianRidge   True  \n", "ard               sklearn.linear_model._bayes.ARDRegression  False  \n", "par       sklearn.linear_model._passive_aggressive.Passi...   True  \n", "ransac         sklearn.linear_model._ransac.RANSACRegressor  False  \n", "tr        sklearn.linear_model._theil_sen.TheilSenRegressor  False  \n", "huber            sklearn.linear_model._huber.HuberRegressor   True  \n", "kr                         sklearn.kernel_ridge.KernelRidge  False  \n", "svm                                sklearn.svm._classes.SVR  False  \n", "knn       sklearn.neighbors._regression.KNeighborsRegressor   True  \n", "dt              sklearn.tree._classes.DecisionTreeRegressor   True  \n", "rf           sklearn.ensemble._forest.RandomForestRegressor   True  \n", "et             sklearn.ensemble._forest.ExtraTreesRegressor   True  \n", "ada       sklearn.ensemble._weight_boosting.AdaBoostRegr...   True  \n", "gbr          sklearn.ensemble._gb.GradientBoostingRegressor   True  \n", "mlp            pycaret.internal.tunable.TunableMLPRegressor  False  \n", "xgboost                        xgboost.sklearn.XGBRegressor   True  \n", "lightgbm                     lightgbm.sklearn.LGBMRegressor   True  \n", "catboost                    catboost.core.CatBoostRegressor   True  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["models()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "wxKHHQcbzZr5"}, "source": ["# 8.1 AdaBoost Regressor"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:22:30.940588Z", "start_time": "2020-04-23T13:22:28.992149Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "-NVGDCR3zZr8", "outputId": "06f5fc68-d2a5-4b59-fea2-3661ea1cb29d"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_a6e5a_row10_col0, #T_a6e5a_row10_col1, #T_a6e5a_row10_col2, #T_a6e5a_row10_col3, #T_a6e5a_row10_col4, #T_a6e5a_row10_col5 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_a6e5a_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >MAE</th>\n", "      <th class=\"col_heading level0 col1\" >MSE</th>\n", "      <th class=\"col_heading level0 col2\" >RMSE</th>\n", "      <th class=\"col_heading level0 col3\" >R2</th>\n", "      <th class=\"col_heading level0 col4\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col5\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_a6e5a_row0_col0\" class=\"data row0 col0\" >4101.8809</td>\n", "      <td id=\"T_a6e5a_row0_col1\" class=\"data row0 col1\" >23013830.0177</td>\n", "      <td id=\"T_a6e5a_row0_col2\" class=\"data row0 col2\" >4797.2732</td>\n", "      <td id=\"T_a6e5a_row0_col3\" class=\"data row0 col3\" >0.7473</td>\n", "      <td id=\"T_a6e5a_row0_col4\" class=\"data row0 col4\" >0.4758</td>\n", "      <td id=\"T_a6e5a_row0_col5\" class=\"data row0 col5\" >0.5470</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_a6e5a_row1_col0\" class=\"data row1 col0\" >4251.5693</td>\n", "      <td id=\"T_a6e5a_row1_col1\" class=\"data row1 col1\" >29296751.6657</td>\n", "      <td id=\"T_a6e5a_row1_col2\" class=\"data row1 col2\" >5412.6474</td>\n", "      <td id=\"T_a6e5a_row1_col3\" class=\"data row1 col3\" >0.7755</td>\n", "      <td id=\"T_a6e5a_row1_col4\" class=\"data row1 col4\" >0.4940</td>\n", "      <td id=\"T_a6e5a_row1_col5\" class=\"data row1 col5\" >0.5702</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_a6e5a_row2_col0\" class=\"data row2 col0\" >4047.8474</td>\n", "      <td id=\"T_a6e5a_row2_col1\" class=\"data row2 col1\" >22291660.1785</td>\n", "      <td id=\"T_a6e5a_row2_col2\" class=\"data row2 col2\" >4721.4045</td>\n", "      <td id=\"T_a6e5a_row2_col3\" class=\"data row2 col3\" >0.7955</td>\n", "      <td id=\"T_a6e5a_row2_col4\" class=\"data row2 col4\" >0.5068</td>\n", "      <td id=\"T_a6e5a_row2_col5\" class=\"data row2 col5\" >0.5871</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_a6e5a_row3_col0\" class=\"data row3 col0\" >4298.3867</td>\n", "      <td id=\"T_a6e5a_row3_col1\" class=\"data row3 col1\" >23482783.6839</td>\n", "      <td id=\"T_a6e5a_row3_col2\" class=\"data row3 col2\" >4845.9038</td>\n", "      <td id=\"T_a6e5a_row3_col3\" class=\"data row3 col3\" >0.7409</td>\n", "      <td id=\"T_a6e5a_row3_col4\" class=\"data row3 col4\" >0.5089</td>\n", "      <td id=\"T_a6e5a_row3_col5\" class=\"data row3 col5\" >0.5960</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_a6e5a_row4_col0\" class=\"data row4 col0\" >3888.5584</td>\n", "      <td id=\"T_a6e5a_row4_col1\" class=\"data row4 col1\" >24461807.7242</td>\n", "      <td id=\"T_a6e5a_row4_col2\" class=\"data row4 col2\" >4945.8880</td>\n", "      <td id=\"T_a6e5a_row4_col3\" class=\"data row4 col3\" >0.6949</td>\n", "      <td id=\"T_a6e5a_row4_col4\" class=\"data row4 col4\" >0.4764</td>\n", "      <td id=\"T_a6e5a_row4_col5\" class=\"data row4 col5\" >0.5461</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_a6e5a_row5_col0\" class=\"data row5 col0\" >4566.4889</td>\n", "      <td id=\"T_a6e5a_row5_col1\" class=\"data row5 col1\" >29733914.8752</td>\n", "      <td id=\"T_a6e5a_row5_col2\" class=\"data row5 col2\" >5452.8813</td>\n", "      <td id=\"T_a6e5a_row5_col3\" class=\"data row5 col3\" >0.7462</td>\n", "      <td id=\"T_a6e5a_row5_col4\" class=\"data row5 col4\" >0.5462</td>\n", "      <td id=\"T_a6e5a_row5_col5\" class=\"data row5 col5\" >0.6598</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_a6e5a_row6_col0\" class=\"data row6 col0\" >4628.7271</td>\n", "      <td id=\"T_a6e5a_row6_col1\" class=\"data row6 col1\" >27841092.1974</td>\n", "      <td id=\"T_a6e5a_row6_col2\" class=\"data row6 col2\" >5276.4659</td>\n", "      <td id=\"T_a6e5a_row6_col3\" class=\"data row6 col3\" >0.7384</td>\n", "      <td id=\"T_a6e5a_row6_col4\" class=\"data row6 col4\" >0.5549</td>\n", "      <td id=\"T_a6e5a_row6_col5\" class=\"data row6 col5\" >0.6676</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_a6e5a_row7_col0\" class=\"data row7 col0\" >4316.4317</td>\n", "      <td id=\"T_a6e5a_row7_col1\" class=\"data row7 col1\" >25979752.0083</td>\n", "      <td id=\"T_a6e5a_row7_col2\" class=\"data row7 col2\" >5097.0336</td>\n", "      <td id=\"T_a6e5a_row7_col3\" class=\"data row7 col3\" >0.6715</td>\n", "      <td id=\"T_a6e5a_row7_col4\" class=\"data row7 col4\" >0.5034</td>\n", "      <td id=\"T_a6e5a_row7_col5\" class=\"data row7 col5\" >0.5858</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_a6e5a_row8_col0\" class=\"data row8 col0\" >3931.2163</td>\n", "      <td id=\"T_a6e5a_row8_col1\" class=\"data row8 col1\" >21097072.3513</td>\n", "      <td id=\"T_a6e5a_row8_col2\" class=\"data row8 col2\" >4593.1549</td>\n", "      <td id=\"T_a6e5a_row8_col3\" class=\"data row8 col3\" >0.7928</td>\n", "      <td id=\"T_a6e5a_row8_col4\" class=\"data row8 col4\" >0.4858</td>\n", "      <td id=\"T_a6e5a_row8_col5\" class=\"data row8 col5\" >0.5513</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_a6e5a_row9_col0\" class=\"data row9 col0\" >4291.1097</td>\n", "      <td id=\"T_a6e5a_row9_col1\" class=\"data row9 col1\" >24815566.0009</td>\n", "      <td id=\"T_a6e5a_row9_col2\" class=\"data row9 col2\" >4981.5225</td>\n", "      <td id=\"T_a6e5a_row9_col3\" class=\"data row9 col3\" >0.7637</td>\n", "      <td id=\"T_a6e5a_row9_col4\" class=\"data row9 col4\" >0.5495</td>\n", "      <td id=\"T_a6e5a_row9_col5\" class=\"data row9 col5\" >0.6592</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_a6e5a_row10_col0\" class=\"data row10 col0\" >4232.2217</td>\n", "      <td id=\"T_a6e5a_row10_col1\" class=\"data row10 col1\" >25201423.0703</td>\n", "      <td id=\"T_a6e5a_row10_col2\" class=\"data row10 col2\" >5012.4175</td>\n", "      <td id=\"T_a6e5a_row10_col3\" class=\"data row10 col3\" >0.7467</td>\n", "      <td id=\"T_a6e5a_row10_col4\" class=\"data row10 col4\" >0.5102</td>\n", "      <td id=\"T_a6e5a_row10_col5\" class=\"data row10 col5\" >0.5970</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_a6e5a_level0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "      <td id=\"T_a6e5a_row11_col0\" class=\"data row11 col0\" >233.2282</td>\n", "      <td id=\"T_a6e5a_row11_col1\" class=\"data row11 col1\" >2804219.3826</td>\n", "      <td id=\"T_a6e5a_row11_col2\" class=\"data row11 col2\" >277.6577</td>\n", "      <td id=\"T_a6e5a_row11_col3\" class=\"data row11 col3\" >0.0375</td>\n", "      <td id=\"T_a6e5a_row11_col4\" class=\"data row11 col4\" >0.0284</td>\n", "      <td id=\"T_a6e5a_row11_col5\" class=\"data row11 col5\" >0.0457</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1918f4c0448>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ada = create_model('ada')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:22:38.337909Z", "start_time": "2020-04-23T13:22:38.335103Z"}, "colab": {}, "colab_type": "code", "id": "NHL2zciizZsI", "outputId": "d606ad03-ecd5-487b-b205-bfc7a841f3a5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdaBoostRegressor(base_estimator=None, learning_rate=1.0, loss='linear',\n", "                  n_estimators=50, random_state=123)\n"]}], "source": ["print(ada)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "T-dvDHxCzZsU"}, "source": ["# 8.2 Light Gradient Boosting Machine "]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:22:46.292921Z", "start_time": "2020-04-23T13:22:43.713857Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "NC7OVDVrzZsX", "outputId": "a5abc702-d270-4134-892a-e9ecf82bdebb"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_43d5a_row10_col0, #T_43d5a_row10_col1, #T_43d5a_row10_col2, #T_43d5a_row10_col3, #T_43d5a_row10_col4, #T_43d5a_row10_col5 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_43d5a_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >MAE</th>\n", "      <th class=\"col_heading level0 col1\" >MSE</th>\n", "      <th class=\"col_heading level0 col2\" >RMSE</th>\n", "      <th class=\"col_heading level0 col3\" >R2</th>\n", "      <th class=\"col_heading level0 col4\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col5\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_43d5a_row0_col0\" class=\"data row0 col0\" >625.1813</td>\n", "      <td id=\"T_43d5a_row0_col1\" class=\"data row0 col1\" >1051762.9578</td>\n", "      <td id=\"T_43d5a_row0_col2\" class=\"data row0 col2\" >1025.5550</td>\n", "      <td id=\"T_43d5a_row0_col3\" class=\"data row0 col3\" >0.9885</td>\n", "      <td id=\"T_43d5a_row0_col4\" class=\"data row0 col4\" >0.0715</td>\n", "      <td id=\"T_43d5a_row0_col5\" class=\"data row0 col5\" >0.0526</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_43d5a_row1_col0\" class=\"data row1 col0\" >797.6185</td>\n", "      <td id=\"T_43d5a_row1_col1\" class=\"data row1 col1\" >5638866.1771</td>\n", "      <td id=\"T_43d5a_row1_col2\" class=\"data row1 col2\" >2374.6297</td>\n", "      <td id=\"T_43d5a_row1_col3\" class=\"data row1 col3\" >0.9568</td>\n", "      <td id=\"T_43d5a_row1_col4\" class=\"data row1 col4\" >0.0727</td>\n", "      <td id=\"T_43d5a_row1_col5\" class=\"data row1 col5\" >0.0537</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_43d5a_row2_col0\" class=\"data row2 col0\" >829.4586</td>\n", "      <td id=\"T_43d5a_row2_col1\" class=\"data row2 col1\" >3328375.4390</td>\n", "      <td id=\"T_43d5a_row2_col2\" class=\"data row2 col2\" >1824.3836</td>\n", "      <td id=\"T_43d5a_row2_col3\" class=\"data row2 col3\" >0.9695</td>\n", "      <td id=\"T_43d5a_row2_col4\" class=\"data row2 col4\" >0.0860</td>\n", "      <td id=\"T_43d5a_row2_col5\" class=\"data row2 col5\" >0.0619</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_43d5a_row3_col0\" class=\"data row3 col0\" >720.3923</td>\n", "      <td id=\"T_43d5a_row3_col1\" class=\"data row3 col1\" >1697211.3816</td>\n", "      <td id=\"T_43d5a_row3_col2\" class=\"data row3 col2\" >1302.7707</td>\n", "      <td id=\"T_43d5a_row3_col3\" class=\"data row3 col3\" >0.9813</td>\n", "      <td id=\"T_43d5a_row3_col4\" class=\"data row3 col4\" >0.0714</td>\n", "      <td id=\"T_43d5a_row3_col5\" class=\"data row3 col5\" >0.0554</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_43d5a_row4_col0\" class=\"data row4 col0\" >645.6800</td>\n", "      <td id=\"T_43d5a_row4_col1\" class=\"data row4 col1\" >1799949.1196</td>\n", "      <td id=\"T_43d5a_row4_col2\" class=\"data row4 col2\" >1341.6218</td>\n", "      <td id=\"T_43d5a_row4_col3\" class=\"data row4 col3\" >0.9775</td>\n", "      <td id=\"T_43d5a_row4_col4\" class=\"data row4 col4\" >0.0745</td>\n", "      <td id=\"T_43d5a_row4_col5\" class=\"data row4 col5\" >0.0534</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_43d5a_row5_col0\" class=\"data row5 col0\" >830.7176</td>\n", "      <td id=\"T_43d5a_row5_col1\" class=\"data row5 col1\" >6423604.0184</td>\n", "      <td id=\"T_43d5a_row5_col2\" class=\"data row5 col2\" >2534.4830</td>\n", "      <td id=\"T_43d5a_row5_col3\" class=\"data row5 col3\" >0.9452</td>\n", "      <td id=\"T_43d5a_row5_col4\" class=\"data row5 col4\" >0.0810</td>\n", "      <td id=\"T_43d5a_row5_col5\" class=\"data row5 col5\" >0.0567</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_43d5a_row6_col0\" class=\"data row6 col0\" >799.9136</td>\n", "      <td id=\"T_43d5a_row6_col1\" class=\"data row6 col1\" >3353992.2636</td>\n", "      <td id=\"T_43d5a_row6_col2\" class=\"data row6 col2\" >1831.3908</td>\n", "      <td id=\"T_43d5a_row6_col3\" class=\"data row6 col3\" >0.9685</td>\n", "      <td id=\"T_43d5a_row6_col4\" class=\"data row6 col4\" >0.0793</td>\n", "      <td id=\"T_43d5a_row6_col5\" class=\"data row6 col5\" >0.0585</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_43d5a_row7_col0\" class=\"data row7 col0\" >714.3607</td>\n", "      <td id=\"T_43d5a_row7_col1\" class=\"data row7 col1\" >1930222.6458</td>\n", "      <td id=\"T_43d5a_row7_col2\" class=\"data row7 col2\" >1389.3245</td>\n", "      <td id=\"T_43d5a_row7_col3\" class=\"data row7 col3\" >0.9756</td>\n", "      <td id=\"T_43d5a_row7_col4\" class=\"data row7 col4\" >0.0732</td>\n", "      <td id=\"T_43d5a_row7_col5\" class=\"data row7 col5\" >0.0556</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_43d5a_row8_col0\" class=\"data row8 col0\" >784.7648</td>\n", "      <td id=\"T_43d5a_row8_col1\" class=\"data row8 col1\" >2211933.1546</td>\n", "      <td id=\"T_43d5a_row8_col2\" class=\"data row8 col2\" >1487.2569</td>\n", "      <td id=\"T_43d5a_row8_col3\" class=\"data row8 col3\" >0.9783</td>\n", "      <td id=\"T_43d5a_row8_col4\" class=\"data row8 col4\" >0.0766</td>\n", "      <td id=\"T_43d5a_row8_col5\" class=\"data row8 col5\" >0.0582</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_43d5a_row9_col0\" class=\"data row9 col0\" >778.3590</td>\n", "      <td id=\"T_43d5a_row9_col1\" class=\"data row9 col1\" >3127561.3571</td>\n", "      <td id=\"T_43d5a_row9_col2\" class=\"data row9 col2\" >1768.4913</td>\n", "      <td id=\"T_43d5a_row9_col3\" class=\"data row9 col3\" >0.9702</td>\n", "      <td id=\"T_43d5a_row9_col4\" class=\"data row9 col4\" >0.0872</td>\n", "      <td id=\"T_43d5a_row9_col5\" class=\"data row9 col5\" >0.0609</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_43d5a_row10_col0\" class=\"data row10 col0\" >752.6446</td>\n", "      <td id=\"T_43d5a_row10_col1\" class=\"data row10 col1\" >3056347.8515</td>\n", "      <td id=\"T_43d5a_row10_col2\" class=\"data row10 col2\" >1687.9907</td>\n", "      <td id=\"T_43d5a_row10_col3\" class=\"data row10 col3\" >0.9711</td>\n", "      <td id=\"T_43d5a_row10_col4\" class=\"data row10 col4\" >0.0773</td>\n", "      <td id=\"T_43d5a_row10_col5\" class=\"data row10 col5\" >0.0567</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_43d5a_level0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "      <td id=\"T_43d5a_row11_col0\" class=\"data row11 col0\" >69.3829</td>\n", "      <td id=\"T_43d5a_row11_col1\" class=\"data row11 col1\" >1661349.5128</td>\n", "      <td id=\"T_43d5a_row11_col2\" class=\"data row11 col2\" >455.0112</td>\n", "      <td id=\"T_43d5a_row11_col3\" class=\"data row11 col3\" >0.0119</td>\n", "      <td id=\"T_43d5a_row11_col4\" class=\"data row11 col4\" >0.0055</td>\n", "      <td id=\"T_43d5a_row11_col5\" class=\"data row11 col5\" >0.0030</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1918f457c88>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lightgbm = create_model('lightgbm')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "j8DvIuOrzZsm"}, "source": ["# 8.3 Decision Tree"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:22:50.364299Z", "start_time": "2020-04-23T13:22:50.002325Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "1Y_Czm6xzZsr", "outputId": "df27ebb4-257b-440e-cd9b-de05ba0c0f35"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_fab01_row10_col0, #T_fab01_row10_col1, #T_fab01_row10_col2, #T_fab01_row10_col3, #T_fab01_row10_col4, #T_fab01_row10_col5 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_fab01_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >MAE</th>\n", "      <th class=\"col_heading level0 col1\" >MSE</th>\n", "      <th class=\"col_heading level0 col2\" >RMSE</th>\n", "      <th class=\"col_heading level0 col3\" >R2</th>\n", "      <th class=\"col_heading level0 col4\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col5\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_fab01_row0_col0\" class=\"data row0 col0\" >859.1907</td>\n", "      <td id=\"T_fab01_row0_col1\" class=\"data row0 col1\" >2456840.0599</td>\n", "      <td id=\"T_fab01_row0_col2\" class=\"data row0 col2\" >1567.4310</td>\n", "      <td id=\"T_fab01_row0_col3\" class=\"data row0 col3\" >0.9730</td>\n", "      <td id=\"T_fab01_row0_col4\" class=\"data row0 col4\" >0.1016</td>\n", "      <td id=\"T_fab01_row0_col5\" class=\"data row0 col5\" >0.0727</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_fab01_row1_col0\" class=\"data row1 col0\" >1122.9409</td>\n", "      <td id=\"T_fab01_row1_col1\" class=\"data row1 col1\" >9852564.2047</td>\n", "      <td id=\"T_fab01_row1_col2\" class=\"data row1 col2\" >3138.8795</td>\n", "      <td id=\"T_fab01_row1_col3\" class=\"data row1 col3\" >0.9245</td>\n", "      <td id=\"T_fab01_row1_col4\" class=\"data row1 col4\" >0.1102</td>\n", "      <td id=\"T_fab01_row1_col5\" class=\"data row1 col5\" >0.0758</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_fab01_row2_col0\" class=\"data row2 col0\" >911.3452</td>\n", "      <td id=\"T_fab01_row2_col1\" class=\"data row2 col1\" >2803662.6885</td>\n", "      <td id=\"T_fab01_row2_col2\" class=\"data row2 col2\" >1674.4141</td>\n", "      <td id=\"T_fab01_row2_col3\" class=\"data row2 col3\" >0.9743</td>\n", "      <td id=\"T_fab01_row2_col4\" class=\"data row2 col4\" >0.0988</td>\n", "      <td id=\"T_fab01_row2_col5\" class=\"data row2 col5\" >0.0729</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_fab01_row3_col0\" class=\"data row3 col0\" >1002.5575</td>\n", "      <td id=\"T_fab01_row3_col1\" class=\"data row3 col1\" >3926739.3726</td>\n", "      <td id=\"T_fab01_row3_col2\" class=\"data row3 col2\" >1981.6002</td>\n", "      <td id=\"T_fab01_row3_col3\" class=\"data row3 col3\" >0.9567</td>\n", "      <td id=\"T_fab01_row3_col4\" class=\"data row3 col4\" >0.1049</td>\n", "      <td id=\"T_fab01_row3_col5\" class=\"data row3 col5\" >0.0772</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_fab01_row4_col0\" class=\"data row4 col0\" >1167.8154</td>\n", "      <td id=\"T_fab01_row4_col1\" class=\"data row4 col1\" >9751516.1909</td>\n", "      <td id=\"T_fab01_row4_col2\" class=\"data row4 col2\" >3122.7418</td>\n", "      <td id=\"T_fab01_row4_col3\" class=\"data row4 col3\" >0.8784</td>\n", "      <td id=\"T_fab01_row4_col4\" class=\"data row4 col4\" >0.1226</td>\n", "      <td id=\"T_fab01_row4_col5\" class=\"data row4 col5\" >0.0876</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_fab01_row5_col0\" class=\"data row5 col0\" >1047.7778</td>\n", "      <td id=\"T_fab01_row5_col1\" class=\"data row5 col1\" >7833770.7037</td>\n", "      <td id=\"T_fab01_row5_col2\" class=\"data row5 col2\" >2798.8874</td>\n", "      <td id=\"T_fab01_row5_col3\" class=\"data row5 col3\" >0.9331</td>\n", "      <td id=\"T_fab01_row5_col4\" class=\"data row5 col4\" >0.1128</td>\n", "      <td id=\"T_fab01_row5_col5\" class=\"data row5 col5\" >0.0791</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_fab01_row6_col0\" class=\"data row6 col0\" >1010.0816</td>\n", "      <td id=\"T_fab01_row6_col1\" class=\"data row6 col1\" >3989282.4802</td>\n", "      <td id=\"T_fab01_row6_col2\" class=\"data row6 col2\" >1997.3188</td>\n", "      <td id=\"T_fab01_row6_col3\" class=\"data row6 col3\" >0.9625</td>\n", "      <td id=\"T_fab01_row6_col4\" class=\"data row6 col4\" >0.1106</td>\n", "      <td id=\"T_fab01_row6_col5\" class=\"data row6 col5\" >0.0803</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_fab01_row7_col0\" class=\"data row7 col0\" >846.8085</td>\n", "      <td id=\"T_fab01_row7_col1\" class=\"data row7 col1\" >2182534.9007</td>\n", "      <td id=\"T_fab01_row7_col2\" class=\"data row7 col2\" >1477.3405</td>\n", "      <td id=\"T_fab01_row7_col3\" class=\"data row7 col3\" >0.9724</td>\n", "      <td id=\"T_fab01_row7_col4\" class=\"data row7 col4\" >0.0933</td>\n", "      <td id=\"T_fab01_row7_col5\" class=\"data row7 col5\" >0.0709</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_fab01_row8_col0\" class=\"data row8 col0\" >1001.8451</td>\n", "      <td id=\"T_fab01_row8_col1\" class=\"data row8 col1\" >4904945.0821</td>\n", "      <td id=\"T_fab01_row8_col2\" class=\"data row8 col2\" >2214.7111</td>\n", "      <td id=\"T_fab01_row8_col3\" class=\"data row8 col3\" >0.9518</td>\n", "      <td id=\"T_fab01_row8_col4\" class=\"data row8 col4\" >0.1053</td>\n", "      <td id=\"T_fab01_row8_col5\" class=\"data row8 col5\" >0.0734</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_fab01_row9_col0\" class=\"data row9 col0\" >1060.8742</td>\n", "      <td id=\"T_fab01_row9_col1\" class=\"data row9 col1\" >5354347.6956</td>\n", "      <td id=\"T_fab01_row9_col2\" class=\"data row9 col2\" >2313.9463</td>\n", "      <td id=\"T_fab01_row9_col3\" class=\"data row9 col3\" >0.9490</td>\n", "      <td id=\"T_fab01_row9_col4\" class=\"data row9 col4\" >0.1230</td>\n", "      <td id=\"T_fab01_row9_col5\" class=\"data row9 col5\" >0.0847</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_fab01_row10_col0\" class=\"data row10 col0\" >1003.1237</td>\n", "      <td id=\"T_fab01_row10_col1\" class=\"data row10 col1\" >5305620.3379</td>\n", "      <td id=\"T_fab01_row10_col2\" class=\"data row10 col2\" >2228.7271</td>\n", "      <td id=\"T_fab01_row10_col3\" class=\"data row10 col3\" >0.9476</td>\n", "      <td id=\"T_fab01_row10_col4\" class=\"data row10 col4\" >0.1083</td>\n", "      <td id=\"T_fab01_row10_col5\" class=\"data row10 col5\" >0.0775</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fab01_level0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "      <td id=\"T_fab01_row11_col0\" class=\"data row11 col0\" >100.2165</td>\n", "      <td id=\"T_fab01_row11_col1\" class=\"data row11 col1\" >2734194.7557</td>\n", "      <td id=\"T_fab01_row11_col2\" class=\"data row11 col2\" >581.7181</td>\n", "      <td id=\"T_fab01_row11_col3\" class=\"data row11 col3\" >0.0280</td>\n", "      <td id=\"T_fab01_row11_col4\" class=\"data row11 col4\" >0.0091</td>\n", "      <td id=\"T_fab01_row11_col5\" class=\"data row11 col5\" >0.0052</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1918f4c6048>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dt = create_model('dt')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "NsOBIl8szZs1"}, "source": ["すべてのモデルの平均スコアが，`compare_models()`で出力されたスコアと一致していることに注目してください．これは，`compare_models()`のスコアグリッドに表示されるメトリクスが，すべてのCVフォールドの平均スコアであるためです．compare_models()` と同様に，fold パラメータをデフォルトの 10 から別の値に変更したい場合には，`fold` パラメータを使用します．例：`create_model('dt', fold = 5)` とすると，5フォールドのクロスバリデーションを用いて決定木を作成することができます．"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "8RZB8YllzZs7"}, "source": ["# 9.0 Tune a Model"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "AYYWC1X5zZs-"}, "source": ["`create_model`関数を使ってモデルを作成すると，デフォルトのハイパーパラメータを使ってモデルを学習します．ハイパーパラメータを調整するには，`tune_model`関数を使用します．この関数は，あらかじめ定義された探索空間において，`Random Grid Search`を用いてモデルのハイパーパラメータを自動的に調整します．出力では，MAE，MSE，RMSE，R2，RMSLE，MAPEをフォールドごとに示したスコアグリッドを印刷します．カスタム検索グリッドを使用するには，`tune_model`関数で`custom_grid`パラメータを渡します（後述の9.2 LightGBMのチューニングを参照）．"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "5uUSmZLGzZtB"}, "source": ["# 9.1 AdaBoost Regressor"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:25:23.226986Z", "start_time": "2020-04-23T13:25:17.516114Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "XM7qgcGIzZtE", "outputId": "e87c7fac-4dfe-4733-ddc5-d44359b0ea0d"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_e20b7_row10_col0, #T_e20b7_row10_col1, #T_e20b7_row10_col2, #T_e20b7_row10_col3, #T_e20b7_row10_col4, #T_e20b7_row10_col5 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_e20b7_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >MAE</th>\n", "      <th class=\"col_heading level0 col1\" >MSE</th>\n", "      <th class=\"col_heading level0 col2\" >RMSE</th>\n", "      <th class=\"col_heading level0 col3\" >R2</th>\n", "      <th class=\"col_heading level0 col4\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col5\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_e20b7_row0_col0\" class=\"data row0 col0\" >2629.7158</td>\n", "      <td id=\"T_e20b7_row0_col1\" class=\"data row0 col1\" >16222922.0054</td>\n", "      <td id=\"T_e20b7_row0_col2\" class=\"data row0 col2\" >4027.7689</td>\n", "      <td id=\"T_e20b7_row0_col3\" class=\"data row0 col3\" >0.8219</td>\n", "      <td id=\"T_e20b7_row0_col4\" class=\"data row0 col4\" >0.2553</td>\n", "      <td id=\"T_e20b7_row0_col5\" class=\"data row0 col5\" >0.2244</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_e20b7_row1_col0\" class=\"data row1 col0\" >2764.7250</td>\n", "      <td id=\"T_e20b7_row1_col1\" class=\"data row1 col1\" >25273189.9003</td>\n", "      <td id=\"T_e20b7_row1_col2\" class=\"data row1 col2\" >5027.2448</td>\n", "      <td id=\"T_e20b7_row1_col3\" class=\"data row1 col3\" >0.8063</td>\n", "      <td id=\"T_e20b7_row1_col4\" class=\"data row1 col4\" >0.2714</td>\n", "      <td id=\"T_e20b7_row1_col5\" class=\"data row1 col5\" >0.2357</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_e20b7_row2_col0\" class=\"data row2 col0\" >2605.9909</td>\n", "      <td id=\"T_e20b7_row2_col1\" class=\"data row2 col1\" >16883405.3119</td>\n", "      <td id=\"T_e20b7_row2_col2\" class=\"data row2 col2\" >4108.9421</td>\n", "      <td id=\"T_e20b7_row2_col3\" class=\"data row2 col3\" >0.8451</td>\n", "      <td id=\"T_e20b7_row2_col4\" class=\"data row2 col4\" >0.2617</td>\n", "      <td id=\"T_e20b7_row2_col5\" class=\"data row2 col5\" >0.2352</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_e20b7_row3_col0\" class=\"data row3 col0\" >2588.0395</td>\n", "      <td id=\"T_e20b7_row3_col1\" class=\"data row3 col1\" >14475338.1062</td>\n", "      <td id=\"T_e20b7_row3_col2\" class=\"data row3 col2\" >3804.6469</td>\n", "      <td id=\"T_e20b7_row3_col3\" class=\"data row3 col3\" >0.8403</td>\n", "      <td id=\"T_e20b7_row3_col4\" class=\"data row3 col4\" >0.2685</td>\n", "      <td id=\"T_e20b7_row3_col5\" class=\"data row3 col5\" >0.2271</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_e20b7_row4_col0\" class=\"data row4 col0\" >2403.7173</td>\n", "      <td id=\"T_e20b7_row4_col1\" class=\"data row4 col1\" >13602075.2435</td>\n", "      <td id=\"T_e20b7_row4_col2\" class=\"data row4 col2\" >3688.0991</td>\n", "      <td id=\"T_e20b7_row4_col3\" class=\"data row4 col3\" >0.8303</td>\n", "      <td id=\"T_e20b7_row4_col4\" class=\"data row4 col4\" >0.2672</td>\n", "      <td id=\"T_e20b7_row4_col5\" class=\"data row4 col5\" >0.2223</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_e20b7_row5_col0\" class=\"data row5 col0\" >2538.7416</td>\n", "      <td id=\"T_e20b7_row5_col1\" class=\"data row5 col1\" >20724600.2592</td>\n", "      <td id=\"T_e20b7_row5_col2\" class=\"data row5 col2\" >4552.4280</td>\n", "      <td id=\"T_e20b7_row5_col3\" class=\"data row5 col3\" >0.8231</td>\n", "      <td id=\"T_e20b7_row5_col4\" class=\"data row5 col4\" >0.2644</td>\n", "      <td id=\"T_e20b7_row5_col5\" class=\"data row5 col5\" >0.2260</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_e20b7_row6_col0\" class=\"data row6 col0\" >2720.2195</td>\n", "      <td id=\"T_e20b7_row6_col1\" class=\"data row6 col1\" >19796302.1522</td>\n", "      <td id=\"T_e20b7_row6_col2\" class=\"data row6 col2\" >4449.3036</td>\n", "      <td id=\"T_e20b7_row6_col3\" class=\"data row6 col3\" >0.8140</td>\n", "      <td id=\"T_e20b7_row6_col4\" class=\"data row6 col4\" >0.2644</td>\n", "      <td id=\"T_e20b7_row6_col5\" class=\"data row6 col5\" >0.2280</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_e20b7_row7_col0\" class=\"data row7 col0\" >2707.6016</td>\n", "      <td id=\"T_e20b7_row7_col1\" class=\"data row7 col1\" >17084596.1502</td>\n", "      <td id=\"T_e20b7_row7_col2\" class=\"data row7 col2\" >4133.3517</td>\n", "      <td id=\"T_e20b7_row7_col3\" class=\"data row7 col3\" >0.7839</td>\n", "      <td id=\"T_e20b7_row7_col4\" class=\"data row7 col4\" >0.2743</td>\n", "      <td id=\"T_e20b7_row7_col5\" class=\"data row7 col5\" >0.2475</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_e20b7_row8_col0\" class=\"data row8 col0\" >2444.0262</td>\n", "      <td id=\"T_e20b7_row8_col1\" class=\"data row8 col1\" >16340453.5625</td>\n", "      <td id=\"T_e20b7_row8_col2\" class=\"data row8 col2\" >4042.3327</td>\n", "      <td id=\"T_e20b7_row8_col3\" class=\"data row8 col3\" >0.8395</td>\n", "      <td id=\"T_e20b7_row8_col4\" class=\"data row8 col4\" >0.2623</td>\n", "      <td id=\"T_e20b7_row8_col5\" class=\"data row8 col5\" >0.2199</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_e20b7_row9_col0\" class=\"data row9 col0\" >2545.6132</td>\n", "      <td id=\"T_e20b7_row9_col1\" class=\"data row9 col1\" >19267454.7853</td>\n", "      <td id=\"T_e20b7_row9_col2\" class=\"data row9 col2\" >4389.4709</td>\n", "      <td id=\"T_e20b7_row9_col3\" class=\"data row9 col3\" >0.8165</td>\n", "      <td id=\"T_e20b7_row9_col4\" class=\"data row9 col4\" >0.2680</td>\n", "      <td id=\"T_e20b7_row9_col5\" class=\"data row9 col5\" >0.2247</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_e20b7_row10_col0\" class=\"data row10 col0\" >2594.8391</td>\n", "      <td id=\"T_e20b7_row10_col1\" class=\"data row10 col1\" >17967033.7477</td>\n", "      <td id=\"T_e20b7_row10_col2\" class=\"data row10 col2\" >4222.3589</td>\n", "      <td id=\"T_e20b7_row10_col3\" class=\"data row10 col3\" >0.8221</td>\n", "      <td id=\"T_e20b7_row10_col4\" class=\"data row10 col4\" >0.2657</td>\n", "      <td id=\"T_e20b7_row10_col5\" class=\"data row10 col5\" >0.2291</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e20b7_level0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "      <td id=\"T_e20b7_row11_col0\" class=\"data row11 col0\" >111.1423</td>\n", "      <td id=\"T_e20b7_row11_col1\" class=\"data row11 col1\" >3238932.6224</td>\n", "      <td id=\"T_e20b7_row11_col2\" class=\"data row11 col2\" >372.4506</td>\n", "      <td id=\"T_e20b7_row11_col3\" class=\"data row11 col3\" >0.0174</td>\n", "      <td id=\"T_e20b7_row11_col4\" class=\"data row11 col4\" >0.0051</td>\n", "      <td id=\"T_e20b7_row11_col5\" class=\"data row11 col5\" >0.0078</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x191b78d9688>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_ada = tune_model(ada)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:25:31.211932Z", "start_time": "2020-04-23T13:25:31.208661Z"}, "colab": {}, "colab_type": "code", "id": "Ul0HJFoRzZtU", "outputId": "5ac4ab0b-c746-4a9f-a286-3e5cd2bbe536"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdaBoostRegressor(base_estimator=None, learning_rate=0.05, loss='linear',\n", "                  n_estimators=90, random_state=123)\n"]}], "source": ["print(tuned_ada)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "3kvdvfdUzZtj"}, "source": ["# 9.2 Light Gradient Boosting Machine"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "lgbm_params = {'num_leaves': np.arange(10,200,10),\n", "                        'max_depth': [int(x) for x in np.linspace(10, 110, num = 11)],\n", "                        'learning_rate': np.arange(0.1,1,0.1)\n", "                        }"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:25:35.696328Z", "start_time": "2020-04-23T13:25:33.725433Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "s1agvmDFzZtm", "outputId": "7cfab1a7-e7c2-40df-ad1e-a40067bcc4e0"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_7b373_row10_col0, #T_7b373_row10_col1, #T_7b373_row10_col2, #T_7b373_row10_col3, #T_7b373_row10_col4, #T_7b373_row10_col5 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_7b373_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >MAE</th>\n", "      <th class=\"col_heading level0 col1\" >MSE</th>\n", "      <th class=\"col_heading level0 col2\" >RMSE</th>\n", "      <th class=\"col_heading level0 col3\" >R2</th>\n", "      <th class=\"col_heading level0 col4\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col5\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_7b373_row0_col0\" class=\"data row0 col0\" >649.2541</td>\n", "      <td id=\"T_7b373_row0_col1\" class=\"data row0 col1\" >1131046.4835</td>\n", "      <td id=\"T_7b373_row0_col2\" class=\"data row0 col2\" >1063.5067</td>\n", "      <td id=\"T_7b373_row0_col3\" class=\"data row0 col3\" >0.9876</td>\n", "      <td id=\"T_7b373_row0_col4\" class=\"data row0 col4\" >0.0721</td>\n", "      <td id=\"T_7b373_row0_col5\" class=\"data row0 col5\" >0.0544</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_7b373_row1_col0\" class=\"data row1 col0\" >785.8158</td>\n", "      <td id=\"T_7b373_row1_col1\" class=\"data row1 col1\" >5518411.7880</td>\n", "      <td id=\"T_7b373_row1_col2\" class=\"data row1 col2\" >2349.1300</td>\n", "      <td id=\"T_7b373_row1_col3\" class=\"data row1 col3\" >0.9577</td>\n", "      <td id=\"T_7b373_row1_col4\" class=\"data row1 col4\" >0.0730</td>\n", "      <td id=\"T_7b373_row1_col5\" class=\"data row1 col5\" >0.0522</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_7b373_row2_col0\" class=\"data row2 col0\" >808.0977</td>\n", "      <td id=\"T_7b373_row2_col1\" class=\"data row2 col1\" >3024520.4058</td>\n", "      <td id=\"T_7b373_row2_col2\" class=\"data row2 col2\" >1739.1148</td>\n", "      <td id=\"T_7b373_row2_col3\" class=\"data row2 col3\" >0.9723</td>\n", "      <td id=\"T_7b373_row2_col4\" class=\"data row2 col4\" >0.0836</td>\n", "      <td id=\"T_7b373_row2_col5\" class=\"data row2 col5\" >0.0597</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_7b373_row3_col0\" class=\"data row3 col0\" >749.7881</td>\n", "      <td id=\"T_7b373_row3_col1\" class=\"data row3 col1\" >1774260.2775</td>\n", "      <td id=\"T_7b373_row3_col2\" class=\"data row3 col2\" >1332.0136</td>\n", "      <td id=\"T_7b373_row3_col3\" class=\"data row3 col3\" >0.9804</td>\n", "      <td id=\"T_7b373_row3_col4\" class=\"data row3 col4\" >0.0724</td>\n", "      <td id=\"T_7b373_row3_col5\" class=\"data row3 col5\" >0.0556</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_7b373_row4_col0\" class=\"data row4 col0\" >694.0351</td>\n", "      <td id=\"T_7b373_row4_col1\" class=\"data row4 col1\" >1974576.4174</td>\n", "      <td id=\"T_7b373_row4_col2\" class=\"data row4 col2\" >1405.1962</td>\n", "      <td id=\"T_7b373_row4_col3\" class=\"data row4 col3\" >0.9754</td>\n", "      <td id=\"T_7b373_row4_col4\" class=\"data row4 col4\" >0.0838</td>\n", "      <td id=\"T_7b373_row4_col5\" class=\"data row4 col5\" >0.0585</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_7b373_row5_col0\" class=\"data row5 col0\" >841.6462</td>\n", "      <td id=\"T_7b373_row5_col1\" class=\"data row5 col1\" >6725524.0654</td>\n", "      <td id=\"T_7b373_row5_col2\" class=\"data row5 col2\" >2593.3615</td>\n", "      <td id=\"T_7b373_row5_col3\" class=\"data row5 col3\" >0.9426</td>\n", "      <td id=\"T_7b373_row5_col4\" class=\"data row5 col4\" >0.0824</td>\n", "      <td id=\"T_7b373_row5_col5\" class=\"data row5 col5\" >0.0582</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_7b373_row6_col0\" class=\"data row6 col0\" >796.0240</td>\n", "      <td id=\"T_7b373_row6_col1\" class=\"data row6 col1\" >3324498.6208</td>\n", "      <td id=\"T_7b373_row6_col2\" class=\"data row6 col2\" >1823.3208</td>\n", "      <td id=\"T_7b373_row6_col3\" class=\"data row6 col3\" >0.9688</td>\n", "      <td id=\"T_7b373_row6_col4\" class=\"data row6 col4\" >0.0774</td>\n", "      <td id=\"T_7b373_row6_col5\" class=\"data row6 col5\" >0.0564</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_7b373_row7_col0\" class=\"data row7 col0\" >713.1006</td>\n", "      <td id=\"T_7b373_row7_col1\" class=\"data row7 col1\" >1872493.1136</td>\n", "      <td id=\"T_7b373_row7_col2\" class=\"data row7 col2\" >1368.3907</td>\n", "      <td id=\"T_7b373_row7_col3\" class=\"data row7 col3\" >0.9763</td>\n", "      <td id=\"T_7b373_row7_col4\" class=\"data row7 col4\" >0.0715</td>\n", "      <td id=\"T_7b373_row7_col5\" class=\"data row7 col5\" >0.0551</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_7b373_row8_col0\" class=\"data row8 col0\" >775.9760</td>\n", "      <td id=\"T_7b373_row8_col1\" class=\"data row8 col1\" >2274682.3424</td>\n", "      <td id=\"T_7b373_row8_col2\" class=\"data row8 col2\" >1508.2050</td>\n", "      <td id=\"T_7b373_row8_col3\" class=\"data row8 col3\" >0.9777</td>\n", "      <td id=\"T_7b373_row8_col4\" class=\"data row8 col4\" >0.0766</td>\n", "      <td id=\"T_7b373_row8_col5\" class=\"data row8 col5\" >0.0579</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_7b373_row9_col0\" class=\"data row9 col0\" >768.3451</td>\n", "      <td id=\"T_7b373_row9_col1\" class=\"data row9 col1\" >3247098.5445</td>\n", "      <td id=\"T_7b373_row9_col2\" class=\"data row9 col2\" >1801.9707</td>\n", "      <td id=\"T_7b373_row9_col3\" class=\"data row9 col3\" >0.9691</td>\n", "      <td id=\"T_7b373_row9_col4\" class=\"data row9 col4\" >0.0885</td>\n", "      <td id=\"T_7b373_row9_col5\" class=\"data row9 col5\" >0.0594</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_7b373_row10_col0\" class=\"data row10 col0\" >758.2083</td>\n", "      <td id=\"T_7b373_row10_col1\" class=\"data row10 col1\" >3086711.2059</td>\n", "      <td id=\"T_7b373_row10_col2\" class=\"data row10 col2\" >1698.4210</td>\n", "      <td id=\"T_7b373_row10_col3\" class=\"data row10 col3\" >0.9708</td>\n", "      <td id=\"T_7b373_row10_col4\" class=\"data row10 col4\" >0.0781</td>\n", "      <td id=\"T_7b373_row10_col5\" class=\"data row10 col5\" >0.0567</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_7b373_level0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "      <td id=\"T_7b373_row11_col0\" class=\"data row11 col0\" >54.9147</td>\n", "      <td id=\"T_7b373_row11_col1\" class=\"data row11 col1\" >1678033.7774</td>\n", "      <td id=\"T_7b373_row11_col2\" class=\"data row11 col2\" >449.5301</td>\n", "      <td id=\"T_7b373_row11_col3\" class=\"data row11 col3\" >0.0120</td>\n", "      <td id=\"T_7b373_row11_col4\" class=\"data row11 col4\" >0.0058</td>\n", "      <td id=\"T_7b373_row11_col5\" class=\"data row11 col5\" >0.0023</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x191b78ce648>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_lightgbm = tune_model(lightgbm, custom_grid = lgbm_params)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LGBMRegressor(boosting_type='gbdt', class_weight=None, colsample_bytree=1.0,\n", "              importance_type='split', learning_rate=0.1, max_depth=60,\n", "              min_child_samples=20, min_child_weight=0.001, min_split_gain=0.0,\n", "              n_estimators=100, n_jobs=-1, num_leaves=120, objective=None,\n", "              random_state=123, reg_alpha=0.0, reg_lambda=0.0, silent='warn',\n", "              subsample=1.0, subsample_for_bin=200000, subsample_freq=0)\n"]}], "source": ["print(tuned_lightgbm)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Ovz73MkgzZtx"}, "source": ["# 9.3 Decision Tree"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:25:38.913881Z", "start_time": "2020-04-23T13:25:38.267918Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 392}, "colab_type": "code", "id": "kFImcOpXzZt0", "outputId": "36165e01-0b2e-4fa4-efcc-5efa70a3236f"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_669c0_row10_col0, #T_669c0_row10_col1, #T_669c0_row10_col2, #T_669c0_row10_col3, #T_669c0_row10_col4, #T_669c0_row10_col5 {\n", "  background: yellow;\n", "}\n", "</style>\n", "<table id=\"T_669c0_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >MAE</th>\n", "      <th class=\"col_heading level0 col1\" >MSE</th>\n", "      <th class=\"col_heading level0 col2\" >RMSE</th>\n", "      <th class=\"col_heading level0 col3\" >R2</th>\n", "      <th class=\"col_heading level0 col4\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col5\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_669c0_row0_col0\" class=\"data row0 col0\" >1000.7122</td>\n", "      <td id=\"T_669c0_row0_col1\" class=\"data row0 col1\" >2895159.1309</td>\n", "      <td id=\"T_669c0_row0_col2\" class=\"data row0 col2\" >1701.5167</td>\n", "      <td id=\"T_669c0_row0_col3\" class=\"data row0 col3\" >0.9682</td>\n", "      <td id=\"T_669c0_row0_col4\" class=\"data row0 col4\" >0.1076</td>\n", "      <td id=\"T_669c0_row0_col5\" class=\"data row0 col5\" >0.0828</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_669c0_row1_col0\" class=\"data row1 col0\" >1080.2841</td>\n", "      <td id=\"T_669c0_row1_col1\" class=\"data row1 col1\" >6686388.0416</td>\n", "      <td id=\"T_669c0_row1_col2\" class=\"data row1 col2\" >2585.8051</td>\n", "      <td id=\"T_669c0_row1_col3\" class=\"data row1 col3\" >0.9488</td>\n", "      <td id=\"T_669c0_row1_col4\" class=\"data row1 col4\" >0.1053</td>\n", "      <td id=\"T_669c0_row1_col5\" class=\"data row1 col5\" >0.0814</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_669c0_row2_col0\" class=\"data row2 col0\" >1002.3163</td>\n", "      <td id=\"T_669c0_row2_col1\" class=\"data row2 col1\" >3275429.6329</td>\n", "      <td id=\"T_669c0_row2_col2\" class=\"data row2 col2\" >1809.8148</td>\n", "      <td id=\"T_669c0_row2_col3\" class=\"data row2 col3\" >0.9700</td>\n", "      <td id=\"T_669c0_row2_col4\" class=\"data row2 col4\" >0.1051</td>\n", "      <td id=\"T_669c0_row2_col5\" class=\"data row2 col5\" >0.0812</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_669c0_row3_col0\" class=\"data row3 col0\" >1080.7850</td>\n", "      <td id=\"T_669c0_row3_col1\" class=\"data row3 col1\" >4037154.5985</td>\n", "      <td id=\"T_669c0_row3_col2\" class=\"data row3 col2\" >2009.2672</td>\n", "      <td id=\"T_669c0_row3_col3\" class=\"data row3 col3\" >0.9555</td>\n", "      <td id=\"T_669c0_row3_col4\" class=\"data row3 col4\" >0.1172</td>\n", "      <td id=\"T_669c0_row3_col5\" class=\"data row3 col5\" >0.0870</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_669c0_row4_col0\" class=\"data row4 col0\" >1101.6333</td>\n", "      <td id=\"T_669c0_row4_col1\" class=\"data row4 col1\" >7889520.5391</td>\n", "      <td id=\"T_669c0_row4_col2\" class=\"data row4 col2\" >2808.8290</td>\n", "      <td id=\"T_669c0_row4_col3\" class=\"data row4 col3\" >0.9016</td>\n", "      <td id=\"T_669c0_row4_col4\" class=\"data row4 col4\" >0.1189</td>\n", "      <td id=\"T_669c0_row4_col5\" class=\"data row4 col5\" >0.0842</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_669c0_row5_col0\" class=\"data row5 col0\" >1275.5901</td>\n", "      <td id=\"T_669c0_row5_col1\" class=\"data row5 col1\" >11021312.1970</td>\n", "      <td id=\"T_669c0_row5_col2\" class=\"data row5 col2\" >3319.8362</td>\n", "      <td id=\"T_669c0_row5_col3\" class=\"data row5 col3\" >0.9059</td>\n", "      <td id=\"T_669c0_row5_col4\" class=\"data row5 col4\" >0.1250</td>\n", "      <td id=\"T_669c0_row5_col5\" class=\"data row5 col5\" >0.0895</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_669c0_row6_col0\" class=\"data row6 col0\" >1068.6534</td>\n", "      <td id=\"T_669c0_row6_col1\" class=\"data row6 col1\" >4463866.3029</td>\n", "      <td id=\"T_669c0_row6_col2\" class=\"data row6 col2\" >2112.7864</td>\n", "      <td id=\"T_669c0_row6_col3\" class=\"data row6 col3\" >0.9581</td>\n", "      <td id=\"T_669c0_row6_col4\" class=\"data row6 col4\" >0.1076</td>\n", "      <td id=\"T_669c0_row6_col5\" class=\"data row6 col5\" >0.0809</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_669c0_row7_col0\" class=\"data row7 col0\" >975.9364</td>\n", "      <td id=\"T_669c0_row7_col1\" class=\"data row7 col1\" >3271028.5175</td>\n", "      <td id=\"T_669c0_row7_col2\" class=\"data row7 col2\" >1808.5985</td>\n", "      <td id=\"T_669c0_row7_col3\" class=\"data row7 col3\" >0.9586</td>\n", "      <td id=\"T_669c0_row7_col4\" class=\"data row7 col4\" >0.1099</td>\n", "      <td id=\"T_669c0_row7_col5\" class=\"data row7 col5\" >0.0807</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_669c0_row8_col0\" class=\"data row8 col0\" >1101.9207</td>\n", "      <td id=\"T_669c0_row8_col1\" class=\"data row8 col1\" >4441966.3616</td>\n", "      <td id=\"T_669c0_row8_col2\" class=\"data row8 col2\" >2107.5973</td>\n", "      <td id=\"T_669c0_row8_col3\" class=\"data row8 col3\" >0.9564</td>\n", "      <td id=\"T_669c0_row8_col4\" class=\"data row8 col4\" >0.1114</td>\n", "      <td id=\"T_669c0_row8_col5\" class=\"data row8 col5\" >0.0873</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_669c0_row9_col0\" class=\"data row9 col0\" >1065.1662</td>\n", "      <td id=\"T_669c0_row9_col1\" class=\"data row9 col1\" >5192339.2748</td>\n", "      <td id=\"T_669c0_row9_col2\" class=\"data row9 col2\" >2278.6705</td>\n", "      <td id=\"T_669c0_row9_col3\" class=\"data row9 col3\" >0.9506</td>\n", "      <td id=\"T_669c0_row9_col4\" class=\"data row9 col4\" >0.1224</td>\n", "      <td id=\"T_669c0_row9_col5\" class=\"data row9 col5\" >0.0873</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row10\" class=\"row_heading level0 row10\" >Mean</th>\n", "      <td id=\"T_669c0_row10_col0\" class=\"data row10 col0\" >1075.2997</td>\n", "      <td id=\"T_669c0_row10_col1\" class=\"data row10 col1\" >5317416.4597</td>\n", "      <td id=\"T_669c0_row10_col2\" class=\"data row10 col2\" >2254.2722</td>\n", "      <td id=\"T_669c0_row10_col3\" class=\"data row10 col3\" >0.9474</td>\n", "      <td id=\"T_669c0_row10_col4\" class=\"data row10 col4\" >0.1130</td>\n", "      <td id=\"T_669c0_row10_col5\" class=\"data row10 col5\" >0.0842</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_669c0_level0_row11\" class=\"row_heading level0 row11\" >SD</th>\n", "      <td id=\"T_669c0_row11_col0\" class=\"data row11 col0\" >79.0463</td>\n", "      <td id=\"T_669c0_row11_col1\" class=\"data row11 col1\" >2416581.2427</td>\n", "      <td id=\"T_669c0_row11_col2\" class=\"data row11 col2\" >485.4621</td>\n", "      <td id=\"T_669c0_row11_col3\" class=\"data row11 col3\" >0.0227</td>\n", "      <td id=\"T_669c0_row11_col4\" class=\"data row11 col4\" >0.0069</td>\n", "      <td id=\"T_669c0_row11_col5\" class=\"data row11 col5\" >0.0031</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x191faeb14c8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tuned_dt = tune_model(dt)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "TcSjA7SUzZt-"}, "source": ["デフォルトでは，`tune_model` は `R2` を最適化しますが，optimize パラメータを用いてこれを変更することができます．例えば，tune_model(dt, optimize = 'MAE')は，最高の`R2`ではなく，最低の`MAE`となる決定木回帰因子のハイパーパラメータを検索します．この例では、わかりやすくするために、デフォルトの指標である「R2」を使用しています。リグレッサーを評価するために適切なメトリクスを選択する方法は、このチュートリアルの範囲を超えていますが、もっと詳しく知りたい方は、__[click here](https://www.dataquest.io/blog/understanding-regression-error-metrics/)__で回帰誤差メトリクスに関する理解を深めることができます。\n", "\n", "生産に最適なモデルを決定する際に考慮すべき基準は、メトリクスだけではありません。考慮すべき他の要素には、トレーニング時間、k-foldの標準偏差などがあります。チュートリアルシリーズを進めていく中で、これらの要素について、中級者レベル、上級者レベルで詳しく説明していきます。ここでは、`tuned_lightgbm`変数に格納されているTuned Light Gradient Boosting Machineを、このチュートリアルの残りの部分での最適なモデルとして考えてみましょう。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HR-mHgtCzZuE"}, "source": ["# 10.0 Plot a Model"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "N6i7Ggg_zZuH"}, "source": ["モデルを完成させる前に，`plot_model()`関数を使って，残差プロット，予測誤差，特徴の重要度など，さまざまな側面から性能を分析することができます．この関数は、学習されたモデルオブジェクトを受け取り、テスト/ホールドアウトセットに基づいたプロットを返します。\n", "\n", "10種類以上のプロットが用意されていますので、利用可能なプロットのリストは `plot_model()` のドキュメントをご覧ください。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HJCYRQj9zZuU"}, "source": ["# 10.1 Residual Plot 残差プロット"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:29:47.857901Z", "start_time": "2020-04-23T13:29:47.261824Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 376}, "colab_type": "code", "id": "ml-qe8dTzZuX", "outputId": "4c69b0b8-8e82-4003-f0e2-158b19a79f34"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 576x396 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_lightgbm)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "rM9dWgfVzZuh"}, "source": ["# 10.2 Prediction Error Plot 予測誤差のプロット"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:29:53.402735Z", "start_time": "2020-04-23T13:29:53.197809Z"}, "colab": {}, "colab_type": "code", "id": "GPwWRYehzZuk", "outputId": "e3dfe255-fa08-42f7-e556-74c7909f6e6e"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_lightgbm, plot = 'error')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "dWu_EtTGzZuu"}, "source": ["# 10.3 Feature Importance Plot フィーチャー・インポータンス・プロット"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:29:57.854731Z", "start_time": "2020-04-23T13:29:57.758312Z"}, "colab": {}, "colab_type": "code", "id": "7Yh852PPzZux", "outputId": "38295169-5000-4a07-e71d-76de9877ab42"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_model(tuned_lightgbm, plot='feature')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "qI_tk-8RzZu8"}, "source": ["*モデルの性能* を分析するもう一つの方法は，`evaluate_model()`関数を使うことです．この関数は，与えられたモデルについて利用可能なすべてのプロットのためのユーザインタフェースを表示します．この関数は，内部的には `plot_model()` 関数を使用しています．"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:30:17.357283Z", "start_time": "2020-04-23T13:30:17.326916Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 398, "referenced_widgets": ["2f905c9057e849cfb68d8b4a73a9ae2c", "4705ae97c5c341db8a7efcc7e4334c79", "202f5f0dfbea4f6cb6a75e4aebd370c7", "4c0b5f15356140e9bd6ed3a2e0850baa", "63e7b6e04e3d43118698abaca0973960", "b7731479f5f141289a4939cb73adfb28", "5eb7949ac4a140118e9ee6be49921284", "19ea86ac89a349e281011fbd327c29c0", "c9bbc67e75d1477e8cd7a64502b98704", "5334e09f4bed4a0ba45635300e76027e", "14244d0fca4f40a8b3552675869748a3", "baf068abf86f44a9a9502f79dfe17eb5", "3495c4e24f1d4b22af1117ea2403a4fa", "83a10da0205742d9a383671a0be02606", "4ae774bce02d4c1ab1351cd75b25cf17", "ee5416de1bb1462e87b2fa6f9065d646", "36a9c708b65e498586a07d526e6a5d06", "aeb87dce945f4d588807575e9f6e64c3"]}, "colab_type": "code", "id": "J4ryBACHzZu_", "outputId": "246ab553-8043-4cbe-fe99-7ea00bb13aed"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5acd32e6c34248b4835adbb7d71a3786", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(ToggleButtons(description='Plot Type:', icons=('',), options=(('Hyperparameters', 'param…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["evaluate_model(tuned_lightgbm)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "CxKARgKAzZvJ"}, "source": ["# 11.0 Predict on Test / Hold-out Sample"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "r8k_rmplzZvL"}, "source": ["モデルを最終的に決定する前に、テスト/ホールドアウトセットを予測し、評価指標を確認することで、最終的なチェックを行うことが推奨されます。前述のセクション6の情報グリッドを見ると、データの30%（1621サンプル）がテスト/ホールドアウトサンプルとして分離されていることがわかります。これまでの評価指標は、すべてトレーニングセット（70％）のみを対象としたクロスバリデーションの結果です。次に、`tuned_lightgbm`変数に格納されている最終的な学習モデルを用いて、ホールドアウトサンプルを予測し、評価指標がCVの結果と大きく異なっていないかどうかを評価します。"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:31:47.571061Z", "start_time": "2020-04-23T13:31:47.543390Z"}, "colab": {}, "colab_type": "code", "id": "ozTyeSjCzZvY", "outputId": "1cdf25f4-0988-40fa-c8e9-5695fec11f05"}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_d55bc_\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th class=\"col_heading level0 col0\" >Model</th>\n", "      <th class=\"col_heading level0 col1\" >MAE</th>\n", "      <th class=\"col_heading level0 col2\" >MSE</th>\n", "      <th class=\"col_heading level0 col3\" >RMSE</th>\n", "      <th class=\"col_heading level0 col4\" >R2</th>\n", "      <th class=\"col_heading level0 col5\" >RMSLE</th>\n", "      <th class=\"col_heading level0 col6\" >MAPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_d55bc_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_d55bc_row0_col0\" class=\"data row0 col0\" >Light Gradient Boosting Machine</td>\n", "      <td id=\"T_d55bc_row0_col1\" class=\"data row0 col1\" >781.5572</td>\n", "      <td id=\"T_d55bc_row0_col2\" class=\"data row0 col2\" >3816757.2761</td>\n", "      <td id=\"T_d55bc_row0_col3\" class=\"data row0 col3\" >1953.6523</td>\n", "      <td id=\"T_d55bc_row0_col4\" class=\"data row0 col4\" >0.9652</td>\n", "      <td id=\"T_d55bc_row0_col5\" class=\"data row0 col5\" >0.0787</td>\n", "      <td id=\"T_d55bc_row0_col6\" class=\"data row0 col6\" >0.0558</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x19196fc7408>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(tuned_lightgbm);"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "UouOXHaxzZvo"}, "source": ["テスト/ホールドアウトセットの R2 は **`0.9652`** で、これに対して `tuned_lightgbm` の CV 結果では **`0.9708`** でした（上記 9.2 節）。これは有意な差ではありません。テスト／ホールドアウトの結果とCVの結果の間に大きなばらつきがある場合、通常はオーバーフィッティングを示しますが、他のいくつかの要因が原因である可能性もあるため、さらなる調査が必要です。この場合、モデルを最終的に決定し、未経験のデータ（最初に分けた10%のデータで、PyCaretに触れていないデータ）で予測することに進みます。\n", "\n", "(TIP : `create_model` を使用する際に、CV結果の標準偏差を見ることは常に良いことです。)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "J0PmhEQFzZvr"}, "source": ["# 12.0 Finalize Model for Deployment"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Rtaj0uWgzZvx"}, "source": ["モデルの最終決定は、実験の最後のステップです。PyCaretでの通常の機械学習のワークフローは、`setup()`から始まり、`compare_models()`で全てのモデルを比較し、ハイパーパラメータチューニング、アンサンブル、スタッキングなどのモデリング技術を実行するためのいくつかの候補モデルを（対象となる指標に基づいて）選びます。このワークフローにより、新しいデータや未知のデータの予測に使用するための最適なモデルを最終的に導き出すことができます。`finalize_model()`関数は、テスト/ホールドアウトサンプル（ここでは30％）を含む完全なデータセットにモデルをフィットさせます。この関数の目的は，モデルを実運用に投入する前に，完全なデータセットでモデルを訓練することです．"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:33:51.300384Z", "start_time": "2020-04-23T13:33:51.215142Z"}, "colab": {}, "colab_type": "code", "id": "UPk310pezZv0"}, "outputs": [], "source": ["final_lightgbm = finalize_model(tuned_lightgbm)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:33:52.258663Z", "start_time": "2020-04-23T13:33:52.255298Z"}, "colab": {}, "colab_type": "code", "id": "IGFUGDAPzZwF", "outputId": "ddaa33c4-f728-444a-fc33-47da5c37304a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LGBMRegressor(boosting_type='gbdt', class_weight=None, colsample_bytree=1.0,\n", "              importance_type='split', learning_rate=0.1, max_depth=60,\n", "              min_child_samples=20, min_child_weight=0.001, min_split_gain=0.0,\n", "              n_estimators=100, n_jobs=-1, num_leaves=120, objective=None,\n", "              random_state=123, reg_alpha=0.0, reg_lambda=0.0, silent=True,\n", "              subsample=1.0, subsample_for_bin=200000, subsample_freq=0)\n"]}], "source": ["print(final_lightgbm)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "QmJjaIkrzZwQ"}, "source": ["**注意：** 最後に一つだけ注意しておきたいことがあります。`finalize_model()`でモデルを確定すると、テスト/ホールドアウトセットを含む全データセットがトレーニングに使用されます。そのため、`finalize_model()`を使用した後にモデルをホールドアウトセットの予測に使用した場合、モデリングに使用したのと同じデータで予測しようとしているため、表示される情報グリッドは誤解を招くものになります。この点のみを示すために、`predict_model()`の下で`final_lightgbm`を使用して、セクション11の上のものと情報グリッドを比較します。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"ExecuteTime": {"end_time": "2020-04-23T13:34:02.730481Z", "start_time": "2020-04-23T13:34:02.704786Z"}, "colab": {}, "colab_type": "code", "id": "bmYJRTAyzZwU", "outputId": "58e2f57b-abda-4166-c82b-af52ab6f18b8"}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "</style><table id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83da\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Model</th>        <th class=\"col_heading level0 col1\" >MAE</th>        <th class=\"col_heading level0 col2\" >MSE</th>        <th class=\"col_heading level0 col3\" >RMSE</th>        <th class=\"col_heading level0 col4\" >R2</th>        <th class=\"col_heading level0 col5\" >RMSLE</th>        <th class=\"col_heading level0 col6\" >MAPE</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83dalevel0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col0\" class=\"data row0 col0\" >Light Gradient Boosting Machine</td>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col1\" class=\"data row0 col1\" >459.9160</td>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col2\" class=\"data row0 col2\" >1199892.0334</td>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col3\" class=\"data row0 col3\" >1095.3958</td>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col4\" class=\"data row0 col4\" >0.9891</td>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col5\" class=\"data row0 col5\" >0.0498</td>\n", "                        <td id=\"T_d925a2a8_2f66_11eb_ba90_482ae32b83darow0_col6\" class=\"data row0 col6\" >0.0362</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1bb2530a508>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predict_model(final_lightgbm);"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "5NkpL1ZHzZwr"}, "source": ["モデルが同じであるにもかかわらず、`final_lightgbm`のR2が、`0.9652`**から`0.9891`**に増加していることに注目してください。これは、`final_lightgbm`変数が、テスト/ホールドアウトセットを含む完全なデータセットでトレーニングされているためです。"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "CgKSkSsZzZwv"}, "source": ["# 13.0 Predict on Unseen Data"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "6n7QFM94zZwy"}, "source": ["見ていないデータセットに対する予測にも，`predict_model()`関数を使います．上のセクション11との唯一の違いは、今回は`data_unseen`というパラメータを渡すことです。data_unseen`はチュートリアルの最初に作成された変数で、PyCaretに公開されていないオリジナルのデータセットの10%(600サンプル)を含んでいます。(説明はセクション5を参照してください。）"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {}, "colab_type": "code", "id": "YdlpJUx0zZw4", "outputId": "5b45a2b5-c9a1-4d20-80f7-28211f07d586"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Carat Weight</th>\n", "      <th>Cut</th>\n", "      <th>Color</th>\n", "      <th>Clarity</th>\n", "      <th>Polish</th>\n", "      <th>Symmetry</th>\n", "      <th>Report</th>\n", "      <th>Price</th>\n", "      <th>Label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.53</td>\n", "      <td>Ideal</td>\n", "      <td>E</td>\n", "      <td>SI1</td>\n", "      <td>ID</td>\n", "      <td>ID</td>\n", "      <td>AGSL</td>\n", "      <td>12791</td>\n", "      <td>12262.949782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.50</td>\n", "      <td>Fair</td>\n", "      <td>F</td>\n", "      <td>SI1</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>10450</td>\n", "      <td>10122.442382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.01</td>\n", "      <td>Good</td>\n", "      <td>E</td>\n", "      <td>SI1</td>\n", "      <td>G</td>\n", "      <td>G</td>\n", "      <td>GIA</td>\n", "      <td>5161</td>\n", "      <td>5032.520456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2.51</td>\n", "      <td>Very Good</td>\n", "      <td>G</td>\n", "      <td>VS2</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>34361</td>\n", "      <td>34840.379469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.01</td>\n", "      <td>Good</td>\n", "      <td>I</td>\n", "      <td>SI1</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>4238</td>\n", "      <td>4142.695964</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Carat Weight        Cut Color Clarity Polish Symmetry Report  Price  \\\n", "0          1.53      Ideal     E     SI1     ID       ID   AGSL  12791   \n", "1          1.50       Fair     F     SI1     VG       VG    GIA  10450   \n", "2          1.01       Good     E     SI1      G        G    GIA   5161   \n", "3          2.51  Very Good     G     VS2     VG       VG    GIA  34361   \n", "4          1.01       Good     I     SI1     VG       VG    GIA   4238   \n", "\n", "          Label  \n", "0  12262.949782  \n", "1  10122.442382  \n", "2   5032.520456  \n", "3  34840.379469  \n", "4   4142.695964  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["unseen_predictions = predict_model(final_lightgbm, data=data_unseen)\n", "unseen_predictions.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "wZnpuHoDzZxG"}, "source": ["`data_unseen`に`Label`列が追加されています。Labelは，`final_lightgbm`モデルを用いた予測値です．予測値を丸めたい場合には、`predict_model()`の中で`round`パラメータを使用します。また、実際のターゲットカラムである `Price` が利用できるので、これに関するメトリクスをチェックすることもできます。そのためには、pycaret.utilsモジュールを使います。以下の例をご覧ください。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9779"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(unseen_predictions.Price, unseen_predictions.Label, 'R2')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "os2dbiIrzZxJ"}, "source": ["# 14.0 Saving the Model"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "46CV19RlzZxL"}, "source": ["これで、`tuned_lightgbm`のモデルを最終的に決定し、`final_lightgbm`変数に格納して実験を終了しました。また、`final_lightgbm`に格納されたモデルを使って、`data_unseen`を予測しました。これで実験は終わりですが、まだ1つ疑問があります。予測する新しいデータが増えたらどうなるのか？予測すべき新しいデータが増えた場合はどうなるのでしょうか？もう一度実験をやり直す必要があるのでしょうか？答えはノーです。PyCaretの内蔵関数`save_model()`を使えば、モデルと変換パイプライン全体を保存して後で使用することができます。"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {}, "colab_type": "code", "id": "tXl6hkG9zZxN", "outputId": "4af19b19-6c0c-4b1e-a4f7-249e729091bd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Succesfully Saved\n"]}, {"data": {"text/plain": ["(Pipeline(memory=None,\n", "          steps=[('dtypes',\n", "                  DataTypes_Auto_infer(categorical_features=[],\n", "                                       display_types=True, features_todrop=[],\n", "                                       id_columns=[], ml_usecase='regression',\n", "                                       numerical_features=[], target='Price',\n", "                                       time_features=[])),\n", "                 ('imputer',\n", "                  Simple_Imputer(categorical_strategy='not_available',\n", "                                 fill_value_categorical=None,\n", "                                 fill_value_numerical=None,\n", "                                 numeric_strategy='...\n", "                  LGBMRegressor(boosting_type='gbdt', class_weight=None,\n", "                                colsample_bytree=1.0, importance_type='split',\n", "                                learning_rate=0.1, max_depth=60,\n", "                                min_child_samples=20, min_child_weight=0.001,\n", "                                min_split_gain=0.0, n_estimators=100, n_jobs=-1,\n", "                                num_leaves=120, objective=None, random_state=123,\n", "                                reg_alpha=0.0, reg_lambda=0.0, silent=True,\n", "                                subsample=1.0, subsample_for_bin=200000,\n", "                                subsample_freq=0)]],\n", "          verbose=False), 'Final LightGBM Model 25Nov2020.pkl')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["save_model(final_lightgbm,'Final LightGBM Model 25Nov2020')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "q2wgdZ5ozZxX"}, "source": ["(TIP : It's always good to use date in the filename when saving models, it's good for version control.)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "9LsyznpCzZxb"}, "source": ["# 15.0 Loading the Saved Model"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "7ZH-4EMLzZxd"}, "source": ["保存したモデルを後日、同じ環境や別の環境で読み込むには、PyCaretの`load_model()`関数を使用し、保存したモデルを新しい未見のデータに適用して予測することが簡単にできます。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {}, "colab_type": "code", "id": "2hsqdgn3zZxg", "outputId": "9db6b97b-ea93-452b-e4b4-121bea203bd3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformation Pipeline and Model Successfully Loaded\n"]}], "source": ["saved_final_lightgbm = load_model('Final LightGBM Model 25Nov2020')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "NBAXt62nzZx5"}, "source": ["モデルが環境に読み込まれたら、同じ `predict_model()` 関数を使って、新しいデータの予測に使用することができます。以下では、ロードされたモデルを使って、上記のセクション13で使用したのと同じ`data_unseen`を予測しています。"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {}, "colab_type": "code", "id": "y7debJpCzZx8"}, "outputs": [], "source": ["new_prediction = predict_model(saved_final_lightgbm, data=data_unseen)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {}, "colab_type": "code", "id": "8hT1v3N0zZyD", "outputId": "c5f9994b-1859-49d0-9bb0-4d7f6c506b98"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Carat Weight</th>\n", "      <th>Cut</th>\n", "      <th>Color</th>\n", "      <th>Clarity</th>\n", "      <th>Polish</th>\n", "      <th>Symmetry</th>\n", "      <th>Report</th>\n", "      <th>Price</th>\n", "      <th>Label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.53</td>\n", "      <td>Ideal</td>\n", "      <td>E</td>\n", "      <td>SI1</td>\n", "      <td>ID</td>\n", "      <td>ID</td>\n", "      <td>AGSL</td>\n", "      <td>12791</td>\n", "      <td>12262.949782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.50</td>\n", "      <td>Fair</td>\n", "      <td>F</td>\n", "      <td>SI1</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>10450</td>\n", "      <td>10122.442382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.01</td>\n", "      <td>Good</td>\n", "      <td>E</td>\n", "      <td>SI1</td>\n", "      <td>G</td>\n", "      <td>G</td>\n", "      <td>GIA</td>\n", "      <td>5161</td>\n", "      <td>5032.520456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2.51</td>\n", "      <td>Very Good</td>\n", "      <td>G</td>\n", "      <td>VS2</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>34361</td>\n", "      <td>34840.379469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.01</td>\n", "      <td>Good</td>\n", "      <td>I</td>\n", "      <td>SI1</td>\n", "      <td>VG</td>\n", "      <td>VG</td>\n", "      <td>GIA</td>\n", "      <td>4238</td>\n", "      <td>4142.695964</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Carat Weight        Cut Color Clarity Polish Symmetry Report  Price  \\\n", "0          1.53      Ideal     E     SI1     ID       ID   AGSL  12791   \n", "1          1.50       Fair     F     SI1     VG       VG    GIA  10450   \n", "2          1.01       Good     E     SI1      G        G    GIA   5161   \n", "3          2.51  Very Good     G     VS2     VG       VG    GIA  34361   \n", "4          1.01       Good     I     SI1     VG       VG    GIA   4238   \n", "\n", "          Label  \n", "0  12262.949782  \n", "1  10122.442382  \n", "2   5032.520456  \n", "3  34840.379469  \n", "4   4142.695964  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["new_prediction.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "cuVEPftKzZyK"}, "source": ["unseen_predictions \"と \"new_prediction \"の結果が同じであることに注目してください。"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9779"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["from pycaret.utils import check_metric\n", "check_metric(new_prediction.Price, new_prediction.Label, 'R2')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "uE3tuIUHzZyL"}, "source": ["# 16.0 Wrap-up / Next Steps?"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ct8oOjuvzZyS"}, "source": ["このチュートリアルでは、データの取り込み、前処理、モデルのトレーニング、ハイパーパラメータのチューニング、予測、モデルの保存など、機械学習のパイプライン全体をカバーしています。これらのステップは10個以下のコマンドで完了しており、「create_model()」、「tune_model()」、「compare_models()」などの自然な構成で、直感的に覚えられます。この実験全体をPyCaretなしで再作成すると、ほとんどのライブラリで100行以上のコードが必要になります。\n", "\n", "ここでは、`pycaret.regression`の基本的な部分を説明しました。次のチュートリアルでは、高度な前処理、アンサンブル、一般化されたスタッキングなど、機械学習のパイプラインを完全にカスタマイズできる、データサイエンティストにとって必須のテクニックをさらに深く掘り下げていきます。\n", "\n", "それでは、次回のチュートリアルでお会いしましょう。_[回帰チュートリアル(REG102) - Level Intermediate](https://github.com/pycaret/pycaret/blob/master/tutorials/Regression%20Tutorial%20Level%20Intermediate%20-%20REG102.ipynb)__へのリンクをたどってください。"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"colab": {"collapsed_sections": ["wwUzzm1YzZpz", "DxnJV14BzZqq", "xBqHzabEzZrT", "ZzpBazV1zZrx", "wxKHHQcbzZr5", "T-dvDHxCzZsU", "j8DvIuOrzZsm", "8RZB8YllzZs7", "5uUSmZLGzZtB", "3kvdvfdUzZtj", "Ovz73MkgzZtx", "HR-mHgtCzZuE", "HJCYRQj9zZuU", "rM9dWgfVzZuh", "dWu_EtTGzZuu", "CxKARgKAzZvJ", "J0PmhEQFzZvr", "CgKSkSsZzZwv", "os2dbiIrzZxJ", "9LsyznpCzZxb", "uE3tuIUHzZyL"], "name": "Regression Tutorial (REG101) - Level Beginner (ACN_EDITS).ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"14244d0fca4f40a8b3552675869748a3": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "19ea86ac89a349e281011fbd327c29c0": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "202f5f0dfbea4f6cb6a75e4aebd370c7": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsModel", "_options_labels": ["Hyperparameters", "Residuals Plot", "Prediction Error Plot", "Cooks Distance Plot", "Recursive Feature Selection", "Learning Curve", "Validation Curve", "Manifold Learning", "Feature Importance"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonsView", "button_style": "", "description": "Plot Type:", "description_tooltip": null, "disabled": false, "icons": [""], "index": 7, "layout": "IPY_MODEL_b7731479f5f141289a4939cb73adfb28", "style": "IPY_MODEL_63e7b6e04e3d43118698abaca0973960", "tooltips": []}}, "2f905c9057e849cfb68d8b4a73a9ae2c": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "state": {"_dom_classes": ["widget-interact"], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_202f5f0dfbea4f6cb6a75e4aebd370c7", "IPY_MODEL_4c0b5f15356140e9bd6ed3a2e0850baa"], "layout": "IPY_MODEL_4705ae97c5c341db8a7efcc7e4334c79"}}, "3495c4e24f1d4b22af1117ea2403a4fa": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_4ae774bce02d4c1ab1351cd75b25cf17", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_83a10da0205742d9a383671a0be02606", "value": 4}}, "36a9c708b65e498586a07d526e6a5d06": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4705ae97c5c341db8a7efcc7e4334c79": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4ae774bce02d4c1ab1351cd75b25cf17": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c0b5f15356140e9bd6ed3a2e0850baa": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_39f32240322f4053bd57278e81edf9c1", "msg_id": "", "outputs": [{"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfUAAAFdCAYAAAAaB/SSAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\neUUFuSOHMPSMk/ZY5ng/vv0QSnUj536ZY+******************************************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\n0Wg0Gk0v4f8BqXD2ckymA3kAAAAASUVORK5CYII=\n", "metadata": {"tags": []}, "output_type": "display_data", "text/plain": "<Figure size 576x396 with 2 Axes>"}]}}, "5334e09f4bed4a0ba45635300e76027e": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_baf068abf86f44a9a9502f79dfe17eb5", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_14244d0fca4f40a8b3552675869748a3", "value": 4}}, "5eb7949ac4a140118e9ee6be49921284": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_c9bbc67e75d1477e8cd7a64502b98704", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_19ea86ac89a349e281011fbd327c29c0", "value": 1}}, "63e7b6e04e3d43118698abaca0973960": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_width": "", "description_width": "", "font_weight": ""}}, "83a10da0205742d9a383671a0be02606": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "aeb87dce945f4d588807575e9f6e64c3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b7731479f5f141289a4939cb73adfb28": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "baf068abf86f44a9a9502f79dfe17eb5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c9bbc67e75d1477e8cd7a64502b98704": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee5416de1bb1462e87b2fa6f9065d646": {"model_module": "@jupyter-widgets/controls", "model_name": "IntProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "", "description": "Processing: ", "description_tooltip": null, "layout": "IPY_MODEL_aeb87dce945f4d588807575e9f6e64c3", "max": 5, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_36a9c708b65e498586a07d526e6a5d06", "value": 4}}}}}, "nbformat": 4, "nbformat_minor": 1}