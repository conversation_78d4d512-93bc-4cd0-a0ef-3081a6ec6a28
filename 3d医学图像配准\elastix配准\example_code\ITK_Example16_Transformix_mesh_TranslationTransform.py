#!/usr/bin/env python
# coding: utf-8

# # Minimal example on how to transform a mesh of points, using TransformixFilter with TranslationTransform

# This example shows how to transform a mesh, using `itk.TransformixFilter` with `itk.TranslationTransform`.
# 
# Start by importing packages, defining a little print helper function, and creating an TransformixFilter object:

# In[1]:


import itk

def print_mesh(mesh):
    for i in range(mesh.GetNumberOfPoints()):
        print(mesh.GetPoint(i), ' ', end='')
    print('\n')    
    
print('Creating a TransformixFilter (might take a while, please wait)...\n')
ImageType = itk.Image[itk.F, 2]
transformix_filter = itk.TransformixFilter[ImageType].New()


# Create the example input: a mesh of three points, a small translation (0.03125, 0.0625), a "dummy" (1x1) image, and a rather trivial parameter map.

# In[2]:


input_mesh = itk.Mesh[itk.F, 2].New()
input_mesh.SetPoint(0, [0.0, 0.0])
input_mesh.SetPoint(1, [0.0, 1.0])
input_mesh.SetPoint(2, [1.0, 0.0])

print('Input Mesh:')
print_mesh(input_mesh)

translation = [0.03125, 0.0625]
print('Translation:', translation)

transform = itk.TranslationTransform.New()
transform.SetOffset(translation)

# A moving image is required for TransformixFilter, just create a very small one.
moving_image = ImageType.New()
moving_image.SetRegions([1, 1])
moving_image.Allocate(True)

parameter_map = {
                 "Direction": ("1", "0", "0", "1"),
                 "Index": ("0", "0"),
                 "Origin": ("0", "0"),
                 "Size": ("1", "1"),
                 "Spacing": ("1", "1")
                }


# Pass the example input to the TransformixFilter object, apply the transformation, and retrieve the output mesh. Ready!

# In[3]:


parameter_object = itk.ParameterObject.New()
parameter_object.AddParameterMap(parameter_map)

transformix_filter.SetMovingImage(moving_image)
transformix_filter.SetTransformParameterObject(parameter_object)
transformix_filter.SetTransform(transform)
transformix_filter.SetInputMesh(input_mesh)
transformix_filter.Update()

output_mesh = transformix_filter.GetOutputMesh()
print('Output Mesh:')
print_mesh(output_mesh)

