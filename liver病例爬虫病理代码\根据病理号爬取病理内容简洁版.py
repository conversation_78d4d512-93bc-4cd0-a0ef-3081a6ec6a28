# -*- coding = utf-8 -*-
# @Time : 2020/11/23 23:45
# <AUTHOR> none
# @File : binglirun3.py
# @Software : PyCharm
##需要修改的地方： id = i.get("ID2")，ID2需改修改；保存路径修改修改！
##需要修改的地方：for k in range(0,1888):  此处为文件的行数，每次要修改
import requests
from lxml import etree
import csv
import re
from bs4 import BeautifulSoup
import re
import urllib.request, urllib.error
import xlwt
import xlrd
import _sqlite3


headers = {
         "User-Agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36"
    }  
#url = "http://10.1.1.176/pathwebrpt/index_cxm.asp?blh=S2207689" 
#response = requests.get(url, headers=headers)
#response.status_code == requests.codes.ok
#sel = etree.HTML(response.text)
#t_list = sel.xpath('//*[@id="the_note"]/text()')  #*[@class="tt"]                         
#t_list = str(t_list)   
#t_list

def main():
    baseurl = "http://10.1.1.176/pathwebrpt/index_cxm.asp?blh="  # 此处需要添加病理的链接http://10.1.1.176/pathwebrpt/index_cxm.asp?blh=
    # 此处需要获取Excel的病理号内容
    data = xlrd.open_workbook(r'E:\liver\2021.12-2022.3.31.xls')        #此处为读取的文件名！！！！！
    table = data.sheets()[0]    # 创建一个空列表，存储Excel的数据
    tables = import_excel(table)
    # 验证Excel文件存储到列表中的数据
    idzong = []
    for i in tables:
        id = i.get("ID2")     #此处每次需要修改
        idzong.append(id)

    datalist = getdata(baseurl, idzong)
    savepath = "E:/liver/2021.12-2022.3.31ID1b2.xls"       #此处为保存的文件名，每次要修改！！！
    savedata(datalist, savepath)


def import_excel(table):
    tables = []

    for rown in range(table.nrows):
        array = {'name': '', 'ID1': '','ID2': '','ID3': '','ID4': '','ID5': ''}
        array['name'] = table.cell_value(rown, 1)
        array['ID1'] = table.cell_value(rown, 8)
        array['ID2'] = table.cell_value(rown, 9)
        array['ID3'] = table.cell_value(rown, 10)
        array['ID4'] = table.cell_value(rown, 11)
        array['ID5'] = table.cell_value(rown, 12)
        tables.append(array)
    return tables


def getdata(baseurl, idzong):
    datalist = []

    for j in idzong:
        print(type(j))
        url = baseurl + j 
        print(url)
       
        # # print(html)
        # soup = BeautifulSoup(html, "html.parser")      #beautifulsoup读取HTML
        #datalist.append(j)
        data = []
        # item = []
        # zhuyuanhao = soup.i
        # zhuyuanhao = str(zhuyuanhao)
        # data.append(zhuyuanhao)

#        for l in range(1,8):
#            urls = url + str("&keywords_brbh=&keywords_sqxh=")
#            #print(urls)
#            urls = urls + str(l)
#            #print(urls)
##           htmls = askurl(urls)
##           soup = BeautifulSoup(htmls, "html.parser")
        response = requests.get(url, headers=headers)
        response.encoding = 'GBK'    #解码，防止出现爬虫乱码
        response.status_code == requests.codes.ok
        sel = etree.HTML(response.text)
        #t_list = sel.xpath('//*[@id="the_note"]/text()') 一起爬病理描述和结果
        t_list1 = sel.xpath('//html/body/strong/table[2]/tr/td[2]/font/textarea/text()') #只爬病理描述
        t_list2 = sel.xpath('//html/body/strong/table[4]/tr/td[2]/font/textarea/text()') #只爬病理结果
                           
#        t_list = str(t_list)
#        print(t_list)
#        data.append(t_list)
        t_list1 = str(t_list1)
        t_list2 = str(t_list2)
        data.append(t_list1)
        data.append(t_list2)
        datalist.append(data)  # 总data放入datalist内    #此处需要测试datalist处于的位置
       #print(datalist)
    return datalist


# def askurl(url):
#     headers = {
#         "User-Agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36"
#     }
#     request = urllib.request.Request(url, headers=headers)
#     html = ""

#     try:
#         response = urllib.request.urlopen(request)
#         html = response.read().decode("GBK")
#         # print(html)
#     except urllib.error.URLError as e:
#         if hasattr(e, "code"):
#             print(e.code)
#         if hasattr(e, "reason"):
#             print(e.reason)

#     return html


def savedata(datalist, savepath):
    print("save...")
    book = xlwt.Workbook(encoding="utf-8", style_compression=0)  # utf-8创建workbook对象
    sheet = book.add_sheet('sheet1', cell_overwrite_ok=True)  # 创建工作表

    for k in range(1,1888):          #'''此处为文件的行数！！！！'''
        # print("第%d条" %k)
        data = datalist[k]
        for j in range(0,2):
            print(j)
            sheet.write(k, j, data[j])
            #print(data[j])

    book.save(savepath)


if __name__ == "__main__":
    main()
    print("爬虫完毕")