#%%下面是一个使用PyTorch实现的强化学习图像分类代码
# 我们将使用CIFAR-10作为开源数据集
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import gym
from gym import spaces
from tqdm import tqdm
import random
from collections import deque

# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.backends.cudnn.deterministic = True

set_seed()

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 图像分类CNN模型
class CNNModel(nn.Module):
    def __init__(self):
        super(CNNModel, self).__init__()
        self.conv_layers = nn.Sequential(
            # First block
            nn.Conv2d(3, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.Conv2d(32, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Dropout(0.2),
            
            # Second block
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Dropout(0.3),
            
            # Third block
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Dropout(0.4)
        )
        self.fc_layers = nn.Sequential(
            nn.Linear(128 * 4 * 4, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, 10)
        )
    
    def forward(self, x):
        x = self.conv_layers(x)
        x = x.view(x.size(0), -1)  # Flatten
        x = self.fc_layers(x)
        return x  # 返回logits

# 创建图像分类环境
class ImageClassificationEnv(gym.Env):
    def __init__(self, images, labels):
        super(ImageClassificationEnv, self).__init__()
        self.images = images
        self.labels = labels
        self.current_index = 0
        self.action_space = spaces.Discrete(10)  # CIFAR-10有10个类别
        self.observation_space = spaces.Box(low=-1, high=1, shape=(3, 32, 32), dtype=np.float32)
        
        # 打乱数据
        indices = np.arange(len(images))
        np.random.shuffle(indices)
        self.images = self.images[indices]
        self.labels = self.labels[indices]
    
    def reset(self):
        self.current_index = 0
        return self.images[self.current_index]
    
    def step(self, action):
        correct_label = self.labels[self.current_index]
        reward = 1 if action == correct_label else -1
        
        self.current_index += 1
        if self.current_index >= len(self.images):
            done = True
            next_state = None
        else:
            done = False
            next_state = self.images[self.current_index]
        
        info = {"correct": action == correct_label}
        return next_state, reward, done, info

# 经验回放缓冲区
class ReplayBuffer:
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)
    
    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size):
        batch = random.sample(self.buffer, min(len(self.buffer), batch_size))
        states, actions, rewards, next_states, dones = zip(*batch)
        
        # 转换为张量
        states = torch.FloatTensor(np.array(states)).to(device)
        actions = torch.LongTensor(actions).to(device)
        rewards = torch.FloatTensor(rewards).to(device)
        
        # 处理next_states中的None值
        if all(ns is None for ns in next_states):
            next_states = None
        else:
            next_states_array = []
            for ns in next_states:
                if ns is None:
                    next_states_array.append(np.zeros_like(states[0].cpu().numpy()))
                else:
                    next_states_array.append(ns)
            next_states = torch.FloatTensor(np.array(next_states_array)).to(device)
        
        dones = torch.FloatTensor(dones).to(device)
        
        return states, actions, rewards, next_states, dones
    
    def __len__(self):
        return len(self.buffer)

# DQN代理
class DQNAgent:
    def __init__(self, state_dim, action_dim, learning_rate=0.001, gamma=0.99):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma  # 折扣因子
        
        # 探索参数 - 降低初始epsilon并调整衰减率
        self.epsilon = 0.9
        self.epsilon_min = 0.05
        self.epsilon_decay = 0.998
        
        # 创建Q网络和目标网络
        self.q_network = CNNModel().to(device)
        self.target_network = CNNModel().to(device)
        self.update_target_network()
        
        # 优化器和学习率调度器
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='max', factor=0.5, patience=5, verbose=True)
        self.criterion = nn.HuberLoss()  # 使用Huber Loss代替MSE，对异常值更加鲁棒
        
        # 经验回放 - 增加容量
        self.replay_buffer = ReplayBuffer(capacity=50000)
        self.batch_size = 128
    
    def update_target_network(self):
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def select_action(self, state, evaluation=False):
        # 探索-利用权衡
        if not evaluation and np.random.random() < self.epsilon:
            return np.random.randint(self.action_dim)
        
        # 根据Q值选择最佳动作
        state = torch.FloatTensor(state).unsqueeze(0).to(device)
        with torch.no_grad():
            q_values = self.q_network(state)
        return torch.argmax(q_values).item()
    
    def train(self):
        if len(self.replay_buffer) < self.batch_size:
            return None
        
        # 从经验回放中采样
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.batch_size)
        
        # 计算当前Q值
        q_values = self.q_network(states)
        q_values = q_values.gather(1, actions.unsqueeze(1)).squeeze(1)
        
        # 计算目标Q值 - 使用Double DQN方法
        if next_states is not None:
            with torch.no_grad():
                # 使用在线网络选择动作
                next_actions = self.q_network(next_states).max(1)[1].unsqueeze(1)
                # 使用目标网络评估动作价值
                next_q_values = self.target_network(next_states).gather(1, next_actions).squeeze(1)
                targets = rewards + (1 - dones) * self.gamma * next_q_values
        else:
            targets = rewards
        
        # 计算损失并更新网络
        loss = self.criterion(q_values, targets)
        
        self.optimizer.zero_grad()
        loss.backward()
        # 梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), max_norm=1.0)
        self.optimizer.step()
        
        # 更新探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        return loss.item()
        
    # 更新学习率
    def update_scheduler(self, metric):
        self.scheduler.step(metric)

# 加载CIFAR-10数据集
def load_cifar10_data():
    # CIFAR-10 specific mean and std values for proper normalization
    cifar10_mean = (0.4914, 0.4822, 0.4465)
    cifar10_std = (0.2470, 0.2435, 0.2616)
    
    # Training set with data augmentation
    transform_train = transforms.Compose([
        transforms.RandomCrop(32, padding=4),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize(cifar10_mean, cifar10_std)
    ])
    
    # Test set without augmentation
    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(cifar10_mean, cifar10_std)
    ])
    
    train_dataset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform_train)
    test_dataset = torchvision.datasets.CIFAR10(root='./data', train=False, download=True, transform=transform_test)
    
    # 转换为numpy数组
    x_train, y_train = [], []
    for img, label in train_dataset:
        x_train.append(img.numpy())
        y_train.append(label)
    
    x_test, y_test = [], []
    for img, label in test_dataset:
        x_test.append(img.numpy())
        y_test.append(label)
    
    return np.array(x_train), np.array(y_train), np.array(x_test), np.array(y_test)

# 评估函数
def evaluate(agent, x_test, y_test, num_samples=None):
    if num_samples is None:
        num_samples = len(x_test)
    else:
        num_samples = min(num_samples, len(x_test))
    
    correct = 0
    for i in range(num_samples):
        state = x_test[i]
        true_label = y_test[i]
        
        action = agent.select_action(state, evaluation=True)
        
        if action == true_label:
            correct += 1
    
    accuracy = correct / num_samples * 100
    return accuracy

# 训练函数
def train(agent, env, x_test, y_test, num_episodes, update_frequency=2):
    # 记录训练过程
    rewards_history = []
    losses_history = []
    train_acc_history = []
    test_acc_history = []
    
    best_test_acc = 0
    
    for episode in range(num_episodes):
        state = env.reset()
        total_reward = 0
        episode_loss = 0
        steps = 0
        done = False
        
        # 单个episode的训练循环
        pbar = tqdm(total=len(env.images), desc=f"Episode {episode+1}/{num_episodes}")
        while not done:
            # 选择动作
            action = agent.select_action(state)
            
            # 执行动作
            next_state, reward, done, _ = env.step(action)
            
            # 存储经验
            agent.replay_buffer.add(state, action, reward, next_state, done)
            
            # 训练代理
            loss = agent.train()
            
            # 更新状态和奖励
            state = next_state
            total_reward += reward
            if loss is not None:
                episode_loss += loss
                steps += 1
            
            pbar.update(1)
        
        pbar.close()
        
        # 更新目标网络 - 更频繁地更新
        if (episode + 1) % update_frequency == 0:
            agent.update_target_network()
        
        # 计算指标
        avg_loss = episode_loss / max(1, steps)
        train_acc = evaluate(agent, env.images, env.labels, num_samples=2000)
        test_acc = evaluate(agent, x_test, y_test, num_samples=2000)
        
        # 记录历史
        rewards_history.append(total_reward)
        losses_history.append(avg_loss)
        train_acc_history.append(train_acc)
        test_acc_history.append(test_acc)
        
        # 更新学习率调度器
        agent.update_scheduler(test_acc)
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            torch.save(agent.q_network.state_dict(), 'best_rl_cifar10_classifier.pth')
            print(f"New best model saved with test accuracy: {best_test_acc:.2f}%")
        
        # 打印训练进度
        print(f"Episode {episode+1}/{num_episodes}")
        print(f"Total Reward: {total_reward}, Avg Loss: {avg_loss:.4f}")
        print(f"Train Accuracy: {train_acc:.2f}%, Test Accuracy: {test_acc:.2f}%")
        print(f"Exploration Rate (epsilon): {agent.epsilon:.4f}")
        print(f"Best Test Accuracy: {best_test_acc:.2f}%")
        print("-" * 50)
    
    # 绘制训练曲线
    plot_training_curves(rewards_history, losses_history, train_acc_history, test_acc_history)
    
    # 加载最佳模型
    agent.q_network.load_state_dict(torch.load('best_rl_cifar10_classifier.pth'))
    
    return rewards_history, losses_history, train_acc_history, test_acc_history

# 绘制训练曲线
def plot_training_curves(rewards, losses, train_acc, test_acc):
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))
    
    # 绘制奖励曲线
    axs[0, 0].plot(rewards)
    axs[0, 0].set_title('Episode Rewards')
    axs[0, 0].set_xlabel('Episode')
    axs[0, 0].set_ylabel('Total Reward')
    
    # 绘制损失曲线
    axs[0, 1].plot(losses)
    axs[0, 1].set_title('Training Loss')
    axs[0, 1].set_xlabel('Episode')
    axs[0, 1].set_ylabel('Average Loss')
    
    # 绘制训练集准确率
    axs[1, 0].plot(train_acc)
    axs[1, 0].set_title('Training Accuracy')
    axs[1, 0].set_xlabel('Episode')
    axs[1, 0].set_ylabel('Accuracy (%)')
    
    # 绘制测试集准确率
    axs[1, 1].plot(test_acc)
    axs[1, 1].set_title('Test Accuracy')
    axs[1, 1].set_xlabel('Episode')
    axs[1, 1].set_ylabel('Accuracy (%)')
    
    plt.tight_layout()
    plt.savefig('training_curves.png')
    plt.show()

# 主函数
def main():
    # 创建数据目录
    os.makedirs('./data', exist_ok=True)
    
    # 加载CIFAR-10数据集
    print("Loading CIFAR-10 dataset...")
    x_train, y_train, x_test, y_test = load_cifar10_data()
    
    # 使用更多的训练数据
    train_size = 20000  # 使用20000个训练样本
    test_size = 5000    # 使用5000个测试样本
    
    x_train_sample = x_train[:train_size]
    y_train_sample = y_train[:train_size]
    x_test_sample = x_test[:test_size]
    y_test_sample = y_test[:test_size]
    
    print(f"Training data shape: {x_train_sample.shape}")
    print(f"Test data shape: {x_test_sample.shape}")
    
    # 创建环境
    env = ImageClassificationEnv(x_train_sample, y_train_sample)
    
    # 创建DQN代理
    state_dim = (3, 32, 32)
    action_dim = 10
    agent = DQNAgent(state_dim, action_dim)
    
    # 训练代理
    print("Starting training...")
    rewards, losses, train_acc, test_acc = train(
        agent, env, x_test_sample, y_test_sample, 
        num_episodes=100,  # 训练100个episode
        update_frequency=2  # 每2个episode更新一次目标网络
    )
    
    # 最终评估
    print("Final evaluation...")
    final_train_acc = evaluate(agent, x_train_sample, y_train_sample)
    final_test_acc = evaluate(agent, x_test_sample, y_test_sample)
    
    print(f"Final Training Accuracy: {final_train_acc:.2f}%")
    print(f"Final Test Accuracy: {final_test_acc:.2f}%")
    
    # 保存模型
    torch.save(agent.q_network.state_dict(), 'rl_cifar10_classifier.pth')
    print("Model saved as 'rl_cifar10_classifier.pth'")

if __name__ == "__main__":
    main()
# %%
