---
image_key: image
label_key: label
transforms_train:
  _target_: <PERSON><PERSON><PERSON>

  transforms:
  - _target_: Compo<PERSON>
    transforms:
    - _target_: LoadImaged
      keys: "@image_key"
      dtype: "$np.float32"
      image_only: false
    - _target_: LoadImaged
      keys: "@label_key"
      dtype: "$np.uint8"
      image_only: false
  - _target_: EnsureChannelFirstd
    keys: ["@image_key", "@label_key"]
  - PLACEHOLDER_INTENSITY_NORMALIZATION
  - _target_: Orientationd
    keys: ["@image_key", "@label_key"]
    axcodes: RAS
  - _target_: Spacingd
    keys: ["@image_key", "@label_key"]
    pixdim: "@training#transforms#resample_resolution"
    mode: [bilinear, nearest]
    align_corners: [true, true]
  - _target_: CastToTyped
    keys: ["@image_key", "@label_key"]
    dtype: ["$torch.float32", "$torch.uint8"]
  - _target_: EnsureTyped
    keys: ["@image_key", "@label_key"]
    track_meta: true
  - _target_: SpatialPadd
    keys: ["@image_key", "@label_key"]
    spatial_size: "@training#roi_size"
    mode: [constant, constant]

  - _target_: IdentityD  # make the label uptodate (the next transform requires label_key input)
    keys: ["@label_key"]

  # data augmentation
  - _target_: RandCropByLabelClassesd
    keys: ["@image_key", "@label_key"]
    label_key: "@label_key"
    num_classes: "@training#output_classes"
    spatial_size: "@training#roi_size"
    num_samples: "@training#num_crops_per_image"
    warn: false
  - _target_: RandRotated
    keys: ["@image_key", "@label_key"]
    range_x: 0.3
    range_y: 0.3
    range_z: 0.3
    mode: [bilinear, nearest]
    prob: 0.2
  - _target_: RandZoomd
    keys: ["@image_key", "@label_key"]
    min_zoom: 0.8
    max_zoom: 1.2
    mode: [trilinear, nearest]
    prob: 0.16

  - _target_: IdentityD   # make image up-to-date, before this line the cropping hasn't been applied
    keys: ["@image_key", "@label_key"]

  - _target_: RandGaussianSmoothd
    keys: "@image_key"
    sigma_x: [0.5, 1.15]
    sigma_y: [0.5, 1.15]
    sigma_z: [0.5, 1.15]
    prob: 0.15
  - _target_: RandScaleIntensityd
    keys: "@image_key"
    factors: 0.3
    prob: 0.5
  - _target_: RandShiftIntensityd
    keys: "@image_key"
    offsets: 0.1
    prob: 0.5
  - _target_: RandGaussianNoised
    keys: "@image_key"
    std: 0.01
    prob: 0.15
  - _target_: RandFlipd
    keys: ["@image_key", "@label_key"]
    spatial_axis: 0
    prob: 0.5
  - _target_: RandFlipd
    keys: ["@image_key", "@label_key"]
    spatial_axis: 1
    prob: 0.5
  - _target_: RandFlipd
    keys: ["@image_key", "@label_key"]
    spatial_axis: 2
    prob: 0.5
