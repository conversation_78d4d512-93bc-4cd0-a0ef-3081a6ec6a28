# -*- coding: utf-8 -*-
"""
Created on Tue Apr 12 21:08:28 2022

@author: DELL
"""

import requests
from lxml import etree
import csv
import re

from bs4 import BeautifulSoup
import re
import urllib.request, urllib.error
import xlwt
import xlrd
import _sqlite3

# baseurl = 'http://fyy.sdfyy.cn/Article/detail/id/'
# data = xlrd.open_workbook(r'C:\Users\<USER>\Desktop\12345.xls')        #此处为读取的文件名！！！！！
# table = data.sheets()[0]
#     # 创建一个空列表，存储Excel的数据
# tables = import_excel(table)
# idzong = []
# for i in tables:
#     id = i.get("ID")
#     print(id)
#     idzong.append(id)
#     print(idzong)

# for id in idzong:
#     print(type(id))
#     url = baseurl + str(id) 
#     url = url + ".html"
#     print(url)
#     html = askurl(url)

# soup = BeautifulSoup(html, "html.parser")         
# data = []
#         # item = []
# zhuyuanhao = soup.i
# zhuyuanhao = str(zhu<PERSON>hao)
# data.append(zhuyuanhao)
# t_list = soup.find_all("textarea")
# t_list = str(t_list)
# print(t_list)

# data.append(t_list)

#         #soup = BeautifulSoup(html, "html.parser")
#         #data = []
#         #zhuyuanhao = soup.i
#         #zhuyuanhao = str(zhuyuanhao)

#         #data.append(zhuyuanhao)
#         #t_list = soup.find_all("textarea")

#         #t_list = str(t_list)
#         #data.append(t_list)
headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Safari/537.36"
    }   
# response = requests.get(url, headers=headers)
# response.status_code == requests.codes.ok
# sel = etree.HTML(response.text)
# t_list = sel.xpath('//*[@class="tt"]/text()')     
  
# t_list  
    
    

def main():
    baseurl = 'http://fyy.sdfyy.cn/Article/detail/id/'  # 此处需要添加病理的链接
    # 此处需要获取Excel的住院号内容
    data = xlrd.open_workbook(r'C:\Users\<USER>\Desktop\12345.xls')        #此处为读取的文件名！！！！！
    table = data.sheets()[0]
    # 创建一个空列表，存储Excel的数据
    tables = import_excel(table)
    # 验证Excel文件存储到列表中的数据
    idzong = []
    for i in tables:
        id = i.get("ID")
        idzong.append(id)

    datalist = getdata(baseurl,idzong)
    savepath = "C:/Users/<USER>/Desktop/2/binglihao.xls"               #此处为保存的文件名！！！
    savedata(datalist,savepath)

def getdata(baseurl, idzong):
    datalist = []
    
    for id in idzong:
        print(type(id))
        url = baseurl + str(id) 
        urls = url + ".html"
        print(urls)
        # html = askurl(urls)
        #print(html)
        #datalist.append(id) 
        #程序修改处
        response = requests.get(urls, headers=headers)
        response.status_code == requests.codes.ok
        sel = etree.HTML(response.text)
        t_list = sel.xpath('//*[@class="tt"]/text()')   #
        # soup = BeautifulSoup(html, "html.parser")         
        data = []
        # item = []
        # zhuyuanhao = soup.i
        # zhuyuanhao = str(zhuyuanhao)
        # data.append(zhuyuanhao)

        # for l in range(1,8):
        #     urls = url + str("&xh=")
        #     #print(urls)
        #     urls = urls + str(l)
        #     #print(urls)
            # htmls = askurl(url)
            # soup = BeautifulSoup(htmls, "html.parser")
            # print(type(t_list))
 
        # t_list = soup.find_all("textarea")   
        #t_list = soup.find_all(id='text') 
        t_list = str(t_list)
        print(t_list)
        # print(t_list[1].get_text())
        data.append(t_list)

        #soup = BeautifulSoup(html, "html.parser")
        #data = []
        #zhuyuanhao = soup.i
        #zhuyuanhao = str(zhuyuanhao)

        #data.append(zhuyuanhao)
        #t_list = soup.find_all("textarea")

        #t_list = str(t_list)
        #data.append(t_list)
        datalist.append(data)  # 总data放入datalist内            #此处需要测试datalist处于的位置
    #print(datalist)
    return datalist


def import_excel(table):
    tables = []

    for rown in range(table.nrows):
        array = {'name': '', 'ID': ''}
        array['name'] = table.cell_value(rown, 1)
        array['ID'] = table.cell_value(rown, 4)
        tables.append(array)
    return tables


# def askurl(urls):
#     headers = {
#         "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Safari/537.36"
#     }
#     request = urllib.request.Request(urls, headers=headers)
#     html = ""

#     try:
#         response = urllib.request.urlopen(request)
#         html = response.read().decode("utf-8")
#         # print(html)
#     except urllib.error.URLError as e:
#         if hasattr(e, "code"):
#             print(e.code)
#         if hasattr(e, "reason"):
#             print(e.reason)

#     return html


def savedata(datalist, savepath):
    print("save...")
    book = xlwt.Workbook(encoding="utf-8", style_compression=0)  # 创建workbook对象
    sheet = book.add_sheet('sheet1', cell_overwrite_ok=True)  # 创建工作表

    for k in range(1,7):          #'''此处为文件的行数！！！！'''(1,4)左闭右开
        # print("第%d条" %k)
        data = datalist[k]
        # sheet.write(k,data[k])
        for j in range(0,1):
            print(j)
            sheet.write(k, j, data[j])
            # print(data[j])
    book.save(savepath)

if __name__ == "__main__":
    main()
    print("爬去完毕")