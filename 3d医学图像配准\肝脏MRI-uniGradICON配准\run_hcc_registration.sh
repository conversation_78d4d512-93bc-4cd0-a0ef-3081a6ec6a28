#!/bin/bash

# HCC肝脏多序列MRI配准快速启动脚本

echo "🚀 HCC肝脏多序列MRI配准工具"
echo "=================================="

# 设置工作目录
cd "/root/autodl-tmp/3d MRI配准"

# 检查Python环境
echo "🔧 检查Python环境..."
python --version

# 检查数据目录
DATA_DIR="/root/autodl-tmp/120HCC/image"
if [ ! -d "$DATA_DIR" ]; then
    echo "❌ 数据目录不存在: $DATA_DIR"
    exit 1
fi

echo "✅ 数据目录存在: $DATA_DIR"

# 统计患者数量
AP_COUNT=$(ls "$DATA_DIR/ap/"*-ap.nii.gz 2>/dev/null | wc -l)
PP_COUNT=$(ls "$DATA_DIR/pp/"*-pp.nii.gz 2>/dev/null | wc -l)
HBP_COUNT=$(ls "$DATA_DIR/hbp/"*-hbp.nii.gz 2>/dev/null | wc -l)

echo "📊 数据统计:"
echo "   AP序列: $AP_COUNT 个文件"
echo "   PP序列: $PP_COUNT 个文件"
echo "   HBP序列: $HBP_COUNT 个文件"

if [ $AP_COUNT -eq 0 ] || [ $PP_COUNT -eq 0 ] || [ $HBP_COUNT -eq 0 ]; then
    echo "❌ 某些序列数据缺失"
    exit 1
fi

echo ""
echo "请选择运行模式:"
echo "1) 测试安装和单个患者配准"
echo "2) 配准指定患者"
echo "3) 小批量测试 (前5个患者)"
echo "4) 批量配准所有患者"
echo "5) 查看帮助"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "🧪 运行测试..."
        python test_single_patient.py
        ;;
    2)
        read -p "请输入患者名称 (例如: baizhengqiang): " patient_name
        if [ -z "$patient_name" ]; then
            echo "❌ 患者名称不能为空"
            exit 1
        fi
        
        echo "🔄 配准患者: $patient_name"
        python liver_hcc_registration.py --patient_name "$patient_name"
        ;;
    3)
        echo "🔄 小批量测试 (前5个患者)..."
        python liver_hcc_registration.py --max_patients 5
        ;;
    4)
        echo "⚠️  批量配准所有患者将需要较长时间 (4-10小时)"
        read -p "确认继续? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "🔄 开始批量配准..."
            python batch_hcc_registration.py
        else
            echo "❌ 取消批量配准"
        fi
        ;;
    5)
        echo "📖 使用帮助:"
        echo ""
        echo "基本命令:"
        echo "  python test_single_patient.py                    # 测试"
        echo "  python liver_hcc_registration.py --help       # 查看帮助"
        echo ""
        echo "单个患者配准:"
        echo "  python liver_hcc_registration.py --patient_name baizhengqiang"
        echo ""
        echo "批量配准:"
        echo "  python liver_hcc_registration.py --max_patients 5"
        echo "  python batch_hcc_registration.py"
        echo ""
        echo "自定义参数:"
        echo "  --reference_sequence ap|pp|hbp    # 参考序列"
        echo "  --model unigradicon|multigradicon  # 配准模型"
        echo "  --io_iterations 50                # 迭代次数"
        echo "  --similarity lncc2|lncc|mind       # 相似性度量"
        echo ""
        echo "详细文档: README_HCC.md"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 脚本执行完成"
