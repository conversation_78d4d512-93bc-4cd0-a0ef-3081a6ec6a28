{"imports": ["$import glob", "$import os", "$import scripts", "$import numpy as np", "$import copy", "$import json", "$import pathlib"], "bundle_root": "./", "image_key": "image", "output_dir": "$@bundle_root + '/eval'", "output_ext": ".nii.gz", "output_dtype": "$np.float32", "output_postfix": "trans", "separate_folder": true, "input_files": "$sorted(glob.glob('/root/autodl-tmp/bundles/vista3d/image/*.nii.gz'))", "input_dicts": "$[{'image': f, 'label_prompt': [1]} for f in @input_files]", "everything_labels": "$list(set([i+1 for i in range(132)]) - set([2,16,18,20,21,23,24,25,26,27,128,129,130,131,132]))", "metadata_path": "$@bundle_root + '/configs/metadata.json'", "metadata": "$json.loads(pathlib.Path(@metadata_path).read_text())", "labels_dict": "$@metadata['network_data_format']['outputs']['pred']['channel_def']", "subclass": {"2": [14, 5], "20": [28, 29, 30, 31, 32], "21": "$list(range(33, 57)) + list(range(63, 98)) + [114, 120, 122]"}, "input_channels": 1, "resample_spacing": [1.5, 1.5, 1.5], "sw_batch_size": 1, "patch_size": [128, 128, 128], "device": "$torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')", "use_point_window": true, "network_def": "$monai.networks.nets.vista3d132(in_channels=@input_channels)", "network": "$@network_def.to(@device)", "preprocessing_transforms": [{"_target_": "LoadImaged", "keys": "@image_key", "image_only": true}, {"_target_": "EnsureChannelFirstd", "keys": "@image_key"}, {"_target_": "EnsureTyped", "keys": "@image_key", "device": "@device", "track_meta": true}, {"_target_": "Spacingd", "keys": "@image_key", "pixdim": "@resample_spacing", "mode": "bilinear"}, {"_target_": "CropForegroundd", "keys": "@image_key", "allow_smaller": true, "margin": 10, "source_key": "@image_key"}, {"_target_": "monai.apps.vista3d.transforms.VistaPreTransformd", "keys": "@image_key", "subclass": "@subclass", "labels_dict": "@labels_dict"}, {"_target_": "ScaleIntensityRanged", "keys": "@image_key", "a_min": -963.8247715525971, "a_max": 1053.678477684517, "b_min": 0, "b_max": 1, "clip": true}, {"_target_": "Orientationd", "keys": "@image_key", "axcodes": "RAS"}, {"_target_": "CastToTyped", "keys": "@image_key", "dtype": "$torch.float32"}], "preprocessing": {"_target_": "Compose", "transforms": "$@preprocessing_transforms "}, "dataset": {"_target_": "Dataset", "data": "@input_dicts", "transform": "@preprocessing"}, "dataloader": {"_target_": "T<PERSON>ead<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataset": "@dataset", "batch_size": 1, "shuffle": false, "num_workers": 0}, "inferer": {"_target_": "scripts.inferer.Vista3dInferer", "roi_size": "@patch_size", "overlap": 0.3, "sw_batch_size": "@sw_batch_size", "use_point_window": "@use_point_window"}, "postprocessing": {"_target_": "Compose", "transforms": [{"_target_": "ToDeviced", "keys": "pred", "device": "cpu", "_disabled_": true}, {"_target_": "monai.apps.vista3d.transforms.VistaPostTransformd", "keys": "pred"}, {"_target_": "Invertd", "keys": "pred", "transform": "$copy.deepcopy(@preprocessing)", "orig_keys": "@image_key", "nearest_interp": true, "to_tensor": true}, {"_target_": "Lambdad", "func": "$lambda x: torch.nan_to_num(x, nan=255)", "keys": "pred"}, {"_target_": "SaveImaged", "keys": "pred", "resample": false, "output_dir": "@output_dir", "output_ext": "@output_ext", "output_dtype": "@output_dtype", "output_postfix": "@output_postfix", "separate_folder": "@separate_folder"}]}, "handlers": [{"_target_": "StatsHandler", "iteration_log": false}], "checkpointloader": {"_target_": "CheckpointLoader", "load_path": "$@bundle_root + '/models/model.pt'", "load_dict": {"model": "@network"}, "map_location": "@device"}, "evaluator": {"_target_": "scripts.evaluator.Vista3dEvaluator", "device": "@device", "val_data_loader": "@dataloader", "network": "@network", "inferer": "@inferer", "postprocessing": "@postprocessing", "val_handlers": "@handlers", "amp": true, "hyper_kwargs": {"user_prompt": true, "everything_labels": "@everything_labels"}}, "initialize": ["$monai.utils.set_determinism(seed=123)", "$@checkpointloader(@evaluator)"], "run": ["$@evaluator.run()"]}