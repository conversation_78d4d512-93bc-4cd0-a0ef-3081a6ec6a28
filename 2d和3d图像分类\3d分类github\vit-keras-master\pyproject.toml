[build-system]
requires = ["poetry-core>=1.0.0", "poetry-dynamic-versioning"]
build-backend = "poetry.core.masonry.api"

[tool.coverage.run]
omit = []

[tool.poetry]
authors = ["<PERSON><PERSON> <<EMAIL>>"]
classifiers = [
    "Operating System :: POSIX :: Linux",
    "Operating System :: Unix",
    "Operating System :: MacOS",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
]
description = "Keras implementation of ViT (Vision Transformer)"
homepage = "https://github.com/faustomorales/vit-keras"
license = "Apache License 2.0"
name = "vit-keras"
readme = "README.md"
repository = "https://github.com/faustomorales/vit-keras"
# Placeholder for poetry-dynamic-versioning
version = "0.0.0"

# See https://python-poetry.org/docs/versions/ for allowed version specification formats
[tool.poetry.dependencies]
python = ">=3.7, <3.12"
scipy = "*"
validators = "*"

[tool.poetry.dev-dependencies]
black = "*"
mypy = "*"
pytest = "*"
pylint = "*"
opencv-python = "*"
types-requests = "*"
types-pkg-resources = "*"

[tool.poetry.extras]
visualization = [
    "matplotlib"
]

[tool.poetry-dynamic-versioning]
enable = true
vcs = "git"
style = "semver"

[tool.pytest.ini_options]
filterwarnings = [
    "ignore:the imp module is deprecated in favour of importlib;:DeprecationWarning"
]

[tool.pylint.main]
extension-pkg-allow-list = [
    "cv2",
]

[tool.pylint.messages_control]
disable = [
    "line-too-long",
    "missing-module-docstring",
    "invalid-name",
    "attribute-defined-outside-init",
    "too-many-locals",
    "too-many-arguments",
    "too-many-branches",
    "too-many-statements",
    "no-member"
]
