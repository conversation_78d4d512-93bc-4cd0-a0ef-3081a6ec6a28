{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# Medical Image Classification Tutorial with the MedNIST Dataset\n", "\n", "In this tutorial, we introduce an end-to-end training and evaluation example based on the MedNIST dataset.\n", "\n", "We'll go through the following steps:\n", "* Create a dataset for training and testing\n", "* Use MONAI transforms to pre-process data\n", "* Use the DenseNet from MONAI for classification\n", "* Train the model with a PyTorch program\n", "* Evaluate on test dataset\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/2d_classification/mednist_tutorial.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[pillow, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.3.0\n", "Numpy version: 1.22.2\n", "Pytorch version: 2.1.0a0+29c30b1\n", "MONAI flags: HAS_EXT = True, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 865972f7a791bf7b42efbcd87c8402bd865b329e\n", "MONAI __file__: /opt/monai/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.11\n", "ITK version: 5.3.0\n", "Nibabel version: 5.1.0\n", "scikit-image version: 0.22.0\n", "scipy version: 1.11.1\n", "Pillow version: 9.2.0\n", "Tensorboard version: 2.9.0\n", "gdown version: 4.7.1\n", "TorchVision version: 0.16.0a0\n", "tqdm version: 4.65.0\n", "lmdb version: 1.4.1\n", "psutil version: 5.9.4\n", "pandas version: 1.5.2\n", "einops version: 0.6.1\n", "transformers version: 4.21.3\n", "mlflow version: 2.7.1\n", "pynrrd version: 1.0.0\n", "clearml version: 1.13.1\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import matplotlib.pyplot as plt\n", "import PIL\n", "import torch\n", "from torch.utils.tensorboard import SummaryWriter\n", "import numpy as np\n", "from sklearn.metrics import classification_report\n", "\n", "from monai.apps import download_and_extract\n", "from monai.config import print_config\n", "from monai.data import decollate_batch, DataLoader\n", "from monai.metrics import ROCAUCMetric\n", "from monai.networks.nets import DenseNet121\n", "from monai.transforms import (\n", "    Activations,\n", "    EnsureChannelFirst,\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON>,\n", "    LoadImage,\n", "    RandFlip,\n", "    RandRotate,\n", "    RandZoom,\n", "    ScaleIntensity,\n", ")\n", "from monai.utils import set_determinism\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/workspace/Data\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download dataset\n", "\n", "The MedNIST dataset was gathered from several sets from [TCIA](https://wiki.cancerimagingarchive.net/display/Public/Data+Usage+Policies+and+Restrictions),\n", "[the RSNA Bone Age Challenge](http://rsnachallenges.cloudapp.net/competitions/4),\n", "and [the NIH Chest X-ray dataset](https://cloud.google.com/healthcare/docs/resources/public-datasets/nih-chest).\n", "\n", "The dataset is kindly made available by [Dr. <PERSON>, Ph.D.](https://www.mayo.edu/research/labs/radiology-informatics/overview) (Department of Radiology, Mayo Clinic)\n", "under the Creative Commons [CC BY-SA 4.0 license](https://creativecommons.org/licenses/by-sa/4.0/).\n", "\n", "If you use the MedNIST dataset, please acknowledge the source."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"tags": []}, "outputs": [], "source": ["resource = \"https://github.com/Project-MONAI/MONAI-extra-test-data/releases/download/0.8.1/MedNIST.tar.gz\"\n", "md5 = \"0bc7306e7427e00ad1c5526a6677552d\"\n", "\n", "compressed_file = os.path.join(root_dir, \"MedNIST.tar.gz\")\n", "data_dir = os.path.join(root_dir, \"MedNIST\")\n", "if not os.path.exists(data_dir):\n", "    download_and_extract(resource, compressed_file, root_dir, md5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["set_determinism(seed=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Read image filenames from the dataset folders\n", "\n", "First of all, check the dataset files and show some statistics.  \n", "There are 6 folders in the dataset: Hand, AbdomenCT, CXR, ChestCT, BreastMRI, HeadCT,  \n", "which should be used as the labels to train our classification model."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total image count: 58954\n", "Image dimensions: 64 x 64\n", "Label names: ['AbdomenCT', 'BreastMRI', 'CXR', 'ChestCT', 'Hand', 'HeadCT']\n", "Label counts: [10000, 8954, 10000, 10000, 10000, 10000]\n"]}], "source": ["class_names = sorted(x for x in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, x)))\n", "num_class = len(class_names)\n", "image_files = [\n", "    [os.path.join(data_dir, class_names[i], x) for x in os.listdir(os.path.join(data_dir, class_names[i]))]\n", "    for i in range(num_class)\n", "]\n", "num_each = [len(image_files[i]) for i in range(num_class)]\n", "image_files_list = []\n", "image_class = []\n", "for i in range(num_class):\n", "    image_files_list.extend(image_files[i])\n", "    image_class.extend([i] * num_each[i])\n", "num_total = len(image_class)\n", "image_width, image_height = PIL.Image.open(image_files_list[0]).size\n", "\n", "print(f\"Total image count: {num_total}\")\n", "print(f\"Image dimensions: {image_width} x {image_height}\")\n", "print(f\"Label names: {class_names}\")\n", "print(f\"Label counts: {num_each}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Randomly pick images from the dataset to visualize and check"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.subplots(3, 3, figsize=(8, 8))\n", "for i, k in enumerate(np.random.randint(num_total, size=9)):\n", "    im = PIL.Image.open(image_files_list[k])\n", "    arr = np.array(im)\n", "    plt.subplot(3, 3, i + 1)\n", "    plt.xlabel(class_names[image_class[k]])\n", "    plt.imshow(arr, cmap=\"gray\", vmin=0, vmax=255)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare training, validation and test data lists\n", "\n", "Randomly select 10% of the dataset as validation and 10% as test."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training count: 47164, Validation count: 5895, Test count: 5895\n"]}], "source": ["val_frac = 0.1\n", "test_frac = 0.1\n", "length = len(image_files_list)\n", "indices = np.arange(length)\n", "np.random.shuffle(indices)\n", "\n", "test_split = int(test_frac * length)\n", "val_split = int(val_frac * length) + test_split\n", "test_indices = indices[:test_split]\n", "val_indices = indices[test_split:val_split]\n", "train_indices = indices[val_split:]\n", "\n", "train_x = [image_files_list[i] for i in train_indices]\n", "train_y = [image_class[i] for i in train_indices]\n", "val_x = [image_files_list[i] for i in val_indices]\n", "val_y = [image_class[i] for i in val_indices]\n", "test_x = [image_files_list[i] for i in test_indices]\n", "test_y = [image_class[i] for i in test_indices]\n", "\n", "print(f\"Training count: {len(train_x)}, Validation count: \" f\"{len(val_x)}, Test count: {len(test_x)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define MONAI transforms, Dataset and Dataloader to pre-process data"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["train_transforms = Compose(\n", "    [\n", "        LoadImage(image_only=True),\n", "        EnsureChannelFirst(),\n", "        ScaleIntensity(),\n", "        RandRotate(range_x=np.pi / 12, prob=0.5, keep_size=True),\n", "        RandFlip(spatial_axis=0, prob=0.5),\n", "        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),\n", "    ]\n", ")\n", "\n", "val_transforms = Compose([LoadImage(image_only=True), EnsureChannelFirst(), ScaleIntensity()])\n", "\n", "y_pred_trans = Compose([Activations(softmax=True)])\n", "y_trans = Compose([AsDiscrete(to_onehot=num_class)])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["class MedNISTDataset(torch.utils.data.Dataset):\n", "    def __init__(self, image_files, labels, transforms):\n", "        self.image_files = image_files\n", "        self.labels = labels\n", "        self.transforms = transforms\n", "\n", "    def __len__(self):\n", "        return len(self.image_files)\n", "\n", "    def __getitem__(self, index):\n", "        return self.transforms(self.image_files[index]), self.labels[index]\n", "\n", "\n", "train_ds = MedNISTDataset(train_x, train_y, train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=300, shuffle=True, num_workers=10)\n", "\n", "val_ds = MedNISTDataset(val_x, val_y, val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=300, num_workers=10)\n", "\n", "test_ds = MedNISTDataset(test_x, test_y, val_transforms)\n", "test_loader = DataLoader(test_ds, batch_size=300, num_workers=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define network and optimizer\n", "\n", "1. Set learning rate for how much the model is updated per batch.\n", "1. Set total epoch number, as we have shuffle and random transforms, so the training data of every epoch is different.  \n", "And as this is just a get start tutorial, let's just train 4 epochs.  \n", "If train 10 epochs, the model can achieve 100% accuracy on test dataset. \n", "1. Use DenseNet from MONAI and move to GPU device, this DenseNet can support both 2D and 3D classification tasks.\n", "1. Use Adam optimizer."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model = DenseNet121(spatial_dims=2, in_channels=1, out_channels=num_class).to(device)\n", "loss_function = torch.nn.CrossEntropyLoss()\n", "optimizer = torch.optim.Adam(model.parameters(), 1e-5)\n", "max_epochs = 4\n", "val_interval = 1\n", "auc_metric = ROCAUCMetric()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model training\n", "\n", "Execute a typical PyTorch training that run epoch loop and step loop, and do validation after every epoch.  \n", "Will save the model weights to file if got best validation accuracy."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["best_metric = -1\n", "best_metric_epoch = -1\n", "epoch_loss_values = []\n", "metric_values = []\n", "writer = SummaryWriter()\n", "\n", "for epoch in range(max_epochs):\n", "    print(\"-\" * 10)\n", "    print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "    model.train()\n", "    epoch_loss = 0\n", "    step = 0\n", "    for batch_data in train_loader:\n", "        step += 1\n", "        inputs, labels = batch_data[0].to(device), batch_data[1].to(device)\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = loss_function(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "        print(f\"{step}/{len(train_ds) // train_loader.batch_size}, \" f\"train_loss: {loss.item():.4f}\")\n", "        epoch_len = len(train_ds) // train_loader.batch_size\n", "        writer.add_scalar(\"train_loss\", loss.item(), epoch_len * epoch + step)\n", "    epoch_loss /= step\n", "    epoch_loss_values.append(epoch_loss)\n", "    print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        with torch.no_grad():\n", "            y_pred = torch.tensor([], dtype=torch.float32, device=device)\n", "            y = torch.tensor([], dtype=torch.long, device=device)\n", "            for val_data in val_loader:\n", "                val_images, val_labels = (\n", "                    val_data[0].to(device),\n", "                    val_data[1].to(device),\n", "                )\n", "                y_pred = torch.cat([y_pred, model(val_images)], dim=0)\n", "                y = torch.cat([y, val_labels], dim=0)\n", "            y_onehot = [y_trans(i) for i in decollate_batch(y, detach=False)]\n", "            y_pred_act = [y_pred_trans(i) for i in decollate_batch(y_pred)]\n", "            auc_metric(y_pred_act, y_onehot)\n", "            result = auc_metric.aggregate()\n", "            auc_metric.reset()\n", "            del y_pred_act, y_onehot\n", "            metric_values.append(result)\n", "            acc_value = torch.eq(y_pred.argmax(dim=1), y)\n", "            acc_metric = acc_value.sum().item() / len(acc_value)\n", "            if result > best_metric:\n", "                best_metric = result\n", "                best_metric_epoch = epoch + 1\n", "                torch.save(model.state_dict(), os.path.join(root_dir, \"best_metric_model.pth\"))\n", "                print(\"saved new best metric model\")\n", "            print(\n", "                f\"current epoch: {epoch + 1} current AUC: {result:.4f}\"\n", "                f\" current accuracy: {acc_metric:.4f}\"\n", "                f\" best AUC: {best_metric:.4f}\"\n", "                f\" at epoch: {best_metric_epoch}\"\n", "            )\n", "            writer.add_scalar(\"val_accuracy\", acc_metric, epoch + 1)\n", "\n", "print(f\"train completed, best_metric: {best_metric:.4f} \" f\"at epoch: {best_metric_epoch}\")\n", "writer.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot the loss and metric"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(\"train\", (12, 6))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Epoch Average Loss\")\n", "x = [i + 1 for i in range(len(epoch_loss_values))]\n", "y = epoch_loss_values\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y)\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"Val AUC\")\n", "x = [val_interval * (i + 1) for i in range(len(metric_values))]\n", "y = metric_values\n", "plt.xlabel(\"epoch\")\n", "plt.plot(x, y)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate the model on test dataset\n", "\n", "After training and validation, we already got the best model on validation test.  \n", "We need to evaluate the model on test dataset to check whether it's robust and not over-fitting.  \n", "We'll use these predictions to generate a classification report."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["model.load_state_dict(torch.load(os.path.join(root_dir, \"best_metric_model.pth\")))\n", "model.eval()\n", "y_true = []\n", "y_pred = []\n", "with torch.no_grad():\n", "    for test_data in test_loader:\n", "        test_images, test_labels = (\n", "            test_data[0].to(device),\n", "            test_data[1].to(device),\n", "        )\n", "        pred = model(test_images).argmax(dim=1)\n", "        for i in range(len(pred)):\n", "            y_true.append(test_labels[i].item())\n", "            y_pred.append(pred[i].item())"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "   AbdomenCT     0.9930    0.9920    0.9925       995\n", "   BreastMRI     0.9966    0.9932    0.9949       880\n", "         CXR     1.0000    0.9959    0.9980       982\n", "     ChestCT     0.9912    1.0000    0.9956      1014\n", "        Hand     0.9971    0.9914    0.9943      1048\n", "      HeadCT     0.9929    0.9980    0.9954       976\n", "\n", "    accuracy                         0.9951      5895\n", "   macro avg     0.9951    0.9951    0.9951      5895\n", "weighted avg     0.9951    0.9951    0.9951      5895\n", "\n"]}], "source": ["print(classification_report(y_true, y_pred, target_names=class_names, digits=4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}