#!/usr/bin/env python3
"""
3D SwinUNETR Pipeline for HCC Dataset - Fixed Version
Handles dimension mismatches in the dataset

ADDED FEATURES FROM ORIGINAL NOTEBOOK:
1. Detailed metrics calculation and visualization
2. Post-training analysis with comprehensive plots
3. Model inference and prediction visualization
4. Prediction saving in NIfTI format
5. CSV export of training metrics
6. Hausdorff distance calculation
7. Per-class Dice score analysis
8. Comprehensive summary visualizations

This script includes the complete pipeline from training to inference and evaluation.
"""

#%%安装包
import pip
pip.main(['install', 'monai'])
pip.main(['install', 'monai[nibabel, tqdm, skimage, einops]'])
pip.main(['install', 'matplotlib'])
pip.main(['install', 'pytorch_lightning'])
pip.main(['install', 'monai[einops]'])
pip.main(['install', 'pandas'])
pip.main(['install', 'nibabel'])
pip.main(['install', 'tqdm'])
# pip.main(['install', 'einops'])
# pip.main(['install', 'torch'])
# pip.main(['install', 'torchvision'])


# Suppress all warnings first
import warnings
import os
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress specific torch.load warnings
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", message=".*torch.load.*")
warnings.filterwarnings("ignore", message=".*weights_only.*")
warnings.filterwarnings("ignore", message=".*pickle.*")
warnings.filterwarnings("ignore", message=".*malicious.*")
warnings.filterwarnings("ignore", message=".*SECURITY.*")
warnings.filterwarnings("ignore", message=".*unpickling.*")

import pytorch_lightning
import monai
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import tempfile
import glob
import nibabel as nib
import json
import time

from monai.utils import set_determinism
from monai.transforms import (
    AsDiscrete,
    EnsureChannelFirstd,
    Compose,
    CropForegroundd,
    LoadImaged,
    Orientationd,
    Spacingd,
    NormalizeIntensityd,
    RandFlipd,
    RandScaleIntensityd,
    RandShiftIntensityd,
    RandSpatialCropd,
    ConcatItemsd,
    DeleteItemsd,
    Resized,
    SqueezeDimd,
    MapTransform,
)
from monai.networks.nets import SwinUNETR
from monai.metrics import DiceMetric, HausdorffDistanceMetric
from monai.losses import DiceCELoss
from monai.inferers import sliding_window_inference
from monai.data import PersistentDataset, list_data_collate, decollate_batch, DataLoader, load_decathlon_datalist
from monai.config import print_config

from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from pytorch_lightning.callbacks.early_stopping import EarlyStopping
from pytorch_lightning.callbacks.timer import Timer


class FixDimensionsd(MapTransform):
    """Custom transform to fix dimension issues in HCC dataset"""

    def __init__(self, keys):
        super().__init__(keys)

    def __call__(self, data):
        d = dict(data)
        for key in self.keys:
            img = d[key]

            # Handle different dimension cases while preserving metadata
            if hasattr(img, 'shape'):
                original_shape = img.shape

                # Handle 5D data: (H, W, D, extra1, extra2)
                if len(original_shape) == 5:
                    img = img[..., 0, 0]  # Take first of extra dimensions
                # Handle 4D data with singleton dimensions
                elif len(original_shape) == 4:
                    if original_shape[-1] == 1:  # (H, W, D, 1)
                        img = img[..., 0]
                    elif original_shape[-2] == 1:  # (H, W, 1, D)
                        img = img[..., 0, :]
                    elif original_shape[0] == 1:  # (1, H, W, D)
                        img = img[0, ...]

                # Ensure we have 3D data
                if len(img.shape) != 3:
                    print(f"Warning: Could not fix shape {original_shape} -> {img.shape} for key {key}")
                    continue

                d[key] = img
        return d

# Setup environment
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.enabled = True
torch.multiprocessing.set_sharing_strategy("file_system")

print("Starting 3D SwinUNETR HCC Segmentation Pipeline - Fixed Version")
print("=" * 60)
print_config()

# Setup data directory
root_dir = os.path.join(os.getcwd(), 'Best_model/')
print(f"Root directory: {root_dir}")

# HCC dataset paths
data_root = '/root/autodl-tmp/320HCC'
image_dir = os.path.join(data_root, 'image')
mask_dir = os.path.join(data_root, 'mask')

roi_size = (96, 96, 96)
sw_batch_size = 1

def create_dataset_json():
    """Create dataset JSON file for HCC dataset with dimension checking"""
    # Get all patient names from ap folder
    ap_files = glob.glob(os.path.join(image_dir, 'ap', '*.nii.gz'))
    patient_names = []
    for file_path in ap_files:
        filename = os.path.basename(file_path)
        patient_name = filename.replace('-ap.nii.gz', '')
        patient_names.append(patient_name)

    patient_names.sort()
    print(f"Found {len(patient_names)} patients: {patient_names}")

    # Create file paths for each modality
    valid_patients = []
    
    for patient_name in patient_names:
        # Image files
        ap_file = os.path.join(image_dir, 'ap', f'{patient_name}-ap.nii.gz')
        pp_file = os.path.join(image_dir, 'pp', f'{patient_name}-pp.nii.gz')
        hbp_file = os.path.join(image_dir, 'hbp', f'{patient_name}-hbp.nii.gz')
        
        # Label files (check different naming patterns)
        possible_label_patterns = [
            f'{patient_name}-ap-mask.nii.gz',
            f'{patient_name}-AP-mask.nii.gz',
            f'{patient_name}-pp-mask.nii.gz',
            f'{patient_name}-PP-mask.nii.gz',
            f'{patient_name}-hbp-mask.nii.gz',
            f'{patient_name}-HBP-mask.nii.gz'
        ]
        
        label_file = None
        for pattern in possible_label_patterns:
            for phase in ['ap', 'pp', 'hbp']:
                potential_path = os.path.join(mask_dir, phase, pattern)
                if os.path.exists(potential_path):
                    label_file = potential_path
                    break
            if label_file:
                break
        
        if label_file is None:
            print(f"Warning: No label file found for patient {patient_name}")
            continue
        
        # Verify all files exist
        if all(os.path.exists(f) for f in [ap_file, pp_file, hbp_file, label_file]):
            # Check if we can load the images (basic validation)
            try:
                ap_img = nib.load(ap_file)
                pp_img = nib.load(pp_file)
                hbp_img = nib.load(hbp_file)
                mask_img = nib.load(label_file)
                
                # Only include patients with reasonable dimensions
                if (len(ap_img.shape) >= 3 and len(pp_img.shape) >= 3 and 
                    len(hbp_img.shape) >= 3 and len(mask_img.shape) >= 3):
                    valid_patients.append({
                        "ap": ap_file,
                        "pp": pp_file,
                        "hbp": hbp_file,
                        "label": label_file
                    })
                    print(f"✓ Added patient {patient_name}")
                else:
                    print(f"✗ Skipped patient {patient_name} - invalid dimensions")
            except Exception as e:
                print(f"✗ Skipped patient {patient_name} - loading error: {e}")
        else:
            print(f"Warning: Missing files for patient {patient_name}")

    print(f"Successfully loaded {len(valid_patients)} valid patient datasets")
    
    # Create dataset JSON file
    file_json = {"training": valid_patients}
    
    file_path = os.path.join(os.getcwd(), 'hcc_dataset_fixed.json')
    with open(file_path, 'w') as json_file:
        json.dump(file_json, json_file, indent=2)
        
    print(f"Dataset JSON saved to: {file_path}")
    return file_path, valid_patients

# Create dataset
dataset_json_path, valid_patients = create_dataset_json()

if len(valid_patients) == 0:
    print("No valid patients found! Exiting...")
    exit(1)

# Visualize sample data
print("\nVisualizing sample data...")
sample_patient = valid_patients[0]
ap_img = nib.load(sample_patient['ap']).get_fdata()
pp_img = nib.load(sample_patient['pp']).get_fdata()
hbp_img = nib.load(sample_patient['hbp']).get_fdata()
mask = nib.load(sample_patient['label']).get_fdata()

# Handle different dimensions by taking the first 3D volume
if len(ap_img.shape) > 3:
    ap_img = ap_img[..., 0] if ap_img.shape[-1] < ap_img.shape[-2] else ap_img[..., 0, 0]
if len(pp_img.shape) > 3:
    pp_img = pp_img[..., 0] if pp_img.shape[-1] < pp_img.shape[-2] else pp_img[..., 0, 0]
if len(hbp_img.shape) > 3:
    hbp_img = hbp_img[..., 0] if hbp_img.shape[-1] < hbp_img.shape[-2] else hbp_img[..., 0, 0]
if len(mask.shape) > 3:
    mask = mask[..., 0] if mask.shape[-1] < mask.shape[-2] else mask[..., 0, 0]

slice_number = min(ap_img.shape[2] // 2, pp_img.shape[2] // 2, hbp_img.shape[2] // 2)
print('slice =', slice_number)
print('ap_img shape =', ap_img.shape)
print('pp_img shape =', pp_img.shape)
print('hbp_img shape =', hbp_img.shape)
print('mask shape =', mask.shape)
print('mask unique values =', np.unique(mask))

fig, (ax1, ax2, ax3, ax4) = plt.subplots(1, 4, figsize=(16, 8))
ax1.imshow(ap_img[:,:,slice_number], cmap='gray')
ax1.set_title('AP Phase')
ax2.imshow(pp_img[:,:,slice_number], cmap='gray')
ax2.set_title('PP Phase')
ax3.imshow(hbp_img[:,:,slice_number], cmap='gray')
ax3.set_title('HBP Phase')
ax4.imshow(mask[:,:,slice_number])
ax4.set_title('Mask')
plt.tight_layout()
plt.savefig('hcc_data_visualization_fixed.png')
plt.show()

class Net(pytorch_lightning.LightningModule):
    def __init__(self, num_classes=2):
        super().__init__()

        # Modified for 3 input channels (AP, PP, HBP) and adjustable output classes
        self._model = SwinUNETR(
            in_channels=3,  # Changed from 4 to 3 for AP, PP, HBP
            out_channels=num_classes,  # Adjustable based on your segmentation task
            img_size=(96, 96, 96),
            feature_size=48,
            drop_rate=0.0,
            attn_drop_rate=0.0,
            dropout_path_rate=0.0,
            use_checkpoint=True,
        ).to(device)

        self.num_classes = num_classes
        self.loss_function = DiceCELoss(to_onehot_y=True, softmax=True)
        self.post_pred = AsDiscrete(argmax=True, to_onehot=num_classes)
        self.post_label = AsDiscrete(to_onehot=num_classes)

        self.dice_metric = DiceMetric(include_background=False, reduction="mean")
        self.dice_metric_batch = DiceMetric(include_background=False, reduction="mean_batch")
        self.haursdoff = HausdorffDistanceMetric(include_background=False, distance_metric='euclidean',
                                                 percentile=None, directed=False, reduction="mean_batch", get_not_nans=False)
        self.check_val = 10
        self.best_val_dice = 0
        self.best_val_epoch = 0
        self.epoch_loss_values = []
        self.metric_values = []
        self.metric_values_class1 = []
        self.haursdoff_values_class1 = []

        self.validation_step_outputs = []
        self.training_step_outputs = []

    def forward(self, x):
        return self._model(x)

    def prepare_data(self):
        # setting up the correct data path for HCC dataset
        datalist = load_decathlon_datalist(dataset_json_path, True, "training")

        # Split data into train and validation
        total_samples = len(datalist)
        train_size = int(0.7 * total_samples)  # 80% for training
        train_files, val_files = datalist[:train_size], datalist[train_size:]

        print(f"Total samples: {total_samples}")
        print(f"Training samples: {len(train_files)}")
        print(f"Validation samples: {len(val_files)}")

        # setting deterministic training for reproducibility
        set_determinism(seed=0)

        # defining the data transforms for HCC dataset with dimension fixing
        train_transform = Compose(
            [
                LoadImaged(keys=["ap", "pp", "hbp", "label"]),

                # Fix dimension issues first
                FixDimensionsd(keys=["ap", "pp", "hbp", "label"]),

                EnsureChannelFirstd(keys=["ap", "pp", "hbp", "label"], channel_dim="no_channel"),
                Orientationd(keys=["ap", "pp", "hbp", "label"], axcodes="RAS"),

                # Resample all images to the same spacing first
                Spacingd(
                    keys=["ap", "pp", "hbp", "label"],
                    pixdim=(1.5, 1.5, 3.0),  # Common spacing
                    mode=("bilinear", "bilinear", "bilinear", "nearest"),
                ),

                # Resize to common spatial dimensions
                Resized(
                    keys=["ap", "pp", "hbp", "label"],
                    spatial_size=(128, 128, 64),  # Common size
                    mode=("trilinear", "trilinear", "trilinear", "nearest"),
                ),

                # Concatenate the three phases into a single image
                ConcatItemsd(keys=["ap", "pp", "hbp"], name="image", dim=0),
                DeleteItemsd(keys=["ap", "pp", "hbp"]),

                NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
                RandSpatialCropd(keys=["image", "label"], roi_size=[96, 96, 96], random_size=False),
                RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=0),
                RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=1),
                RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=2),
                RandScaleIntensityd(keys="image", factors=0.1, prob=1.0),
                RandShiftIntensityd(keys="image", offsets=0.1, prob=1.0),
            ]
        )

        val_transform = Compose(
            [
                LoadImaged(keys=["ap", "pp", "hbp", "label"]),

                # Fix dimension issues first
                FixDimensionsd(keys=["ap", "pp", "hbp", "label"]),

                EnsureChannelFirstd(keys=["ap", "pp", "hbp", "label"], channel_dim="no_channel"),
                Orientationd(keys=["ap", "pp", "hbp", "label"], axcodes="RAS"),

                # Resample all images to the same spacing first
                Spacingd(
                    keys=["ap", "pp", "hbp", "label"],
                    pixdim=(1.5, 1.5, 3.0),  # Common spacing
                    mode=("bilinear", "bilinear", "bilinear", "nearest"),
                ),

                # Resize to common spatial dimensions
                Resized(
                    keys=["ap", "pp", "hbp", "label"],
                    spatial_size=(128, 128, 64),  # Common size
                    mode=("trilinear", "trilinear", "trilinear", "nearest"),
                ),

                # Concatenate the three phases into a single image
                ConcatItemsd(keys=["ap", "pp", "hbp"], name="image", dim=0),
                DeleteItemsd(keys=["ap", "pp", "hbp"]),

                NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
            ]
        )

        persistent_cache = os.path.join(tempfile.mkdtemp(), "persistent_cache")

        # Create datasets with warning suppression
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            self.train_ds = PersistentDataset(
                data=train_files,
                transform=train_transform,
                cache_dir=persistent_cache,
            )
            self.val_ds = PersistentDataset(
                data=val_files,
                transform=val_transform,
                cache_dir=persistent_cache,
            )

    def train_dataloader(self):
        train_loader = DataLoader(
            self.train_ds,
            batch_size=1,  # Small batch size due to memory constraints
            shuffle=True,
            num_workers=0,  # Set to 0 to avoid worker issues
            pin_memory=False,  # Disable pin_memory to reduce memory usage
            collate_fn=list_data_collate,
            persistent_workers=False,
        )
        return train_loader

    def val_dataloader(self):
        val_loader = DataLoader(
            self.val_ds,
            batch_size=1,
            pin_memory=False,  # Disable pin_memory to reduce memory usage
            shuffle=False,
            num_workers=0,  # Set to 0 to avoid worker issues
            persistent_workers=False,
        )
        return val_loader

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self._model.parameters(), lr=1e-4, weight_decay=1e-5
        )
        return optimizer

    def training_step(self, batch, batch_idx):
        images, labels = (batch["image"].cuda(), batch["label"].cuda())
        output = self.forward(images)
        loss = self.loss_function(output, labels)

        self.log("train_loss", loss.item(), on_step=True, on_epoch=True, prog_bar=True)
        self.training_step_outputs.append({"loss": loss})
        return {"loss": loss}

    def on_train_epoch_end(self):
        avg_loss = torch.stack([x["loss"] for x in self.training_step_outputs]).mean()
        self.epoch_loss_values.append(avg_loss.detach().cpu().numpy())
        self.training_step_outputs.clear()

    def validation_step(self, batch, batch_idx):
        images, labels = batch["image"], batch["label"]

        outputs = sliding_window_inference(images, roi_size, sw_batch_size, self.forward)

        loss = self.loss_function(outputs, labels)
        outputs = [self.post_pred(i) for i in decollate_batch(outputs)]
        labels = [self.post_label(i) for i in decollate_batch(labels)]

        self.dice_metric(y_pred=outputs, y=labels)
        self.dice_metric_batch(y_pred=outputs, y=labels)
        self.haursdoff(y_pred=outputs, y=labels)

        d = {"val_loss": loss, "val_number": len(outputs)}
        self.validation_step_outputs.append(d)

        return {"val_loss": loss, "val_number": len(outputs)}

    def on_validation_epoch_end(self):
        val_loss, num_items = 0, 0
        for output in self.validation_step_outputs:
            val_loss += output["val_loss"].sum().item()
            num_items += output["val_number"]

        mean_val_dice = self.dice_metric.aggregate().item()
        self.metric_values.append(np.array(mean_val_dice))
        self.dice_metric.reset()

        metric_batch = self.dice_metric_batch.aggregate()
        metric_class1 = 0.0
        if len(metric_batch) > 0:
            metric_class1 = metric_batch[0].item()
            self.metric_values_class1.append(metric_class1)
        self.dice_metric_batch.reset()

        haursdoff = self.haursdoff.aggregate()
        hd_class1 = 0.0
        if len(haursdoff) > 0:
            hd_class1 = haursdoff[0].item()
            self.haursdoff_values_class1.append(hd_class1)
        self.haursdoff.reset()

        mean_val_loss = torch.tensor(val_loss / num_items)

        if mean_val_dice > self.best_val_dice:
            self.best_val_dice = mean_val_dice
            self.best_val_epoch = self.current_epoch

            torch.save(self._model, f"Model_SwinUNETR_HCC_Fixed.pt")

        print(
            f"current epoch: {self.current_epoch} "
            f"current mean dice: {mean_val_dice:.4f} "
            f"\nbest mean dice: {self.best_val_dice:.4f} "
            f"at epoch: {self.best_val_epoch} "
            f"class1: {metric_class1:.4f} "
            f"hd_class1: {hd_class1:.4f}"
        )
        self.log("val_loss", mean_val_loss.item())
        self.log("val_dice", mean_val_dice, on_epoch=True, prog_bar=True)
        self.validation_step_outputs.clear()
        return {"val_dice": mean_val_dice, "val_loss": mean_val_loss}


def save_detailed_metrics_to_csv(net):
    """Save detailed training metrics to CSV files"""
    import csv

    # Save validation metrics
    if len(net.metric_values) > 0:
        filename = 'DetailedValidationMetrics_HCC_Fixed.csv'
        with open(filename, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(['Epoch', 'Mean_Dice', 'Dice_Class1', 'Hausdorff_Class1'])

            for i in range(len(net.metric_values)):
                epoch = i * 5  # Every 5 epochs
                mean_dice = net.metric_values[i]
                dice_class1 = net.metric_values_class1[i] if i < len(net.metric_values_class1) else 0
                hd_class1 = net.haursdoff_values_class1[i] if i < len(net.haursdoff_values_class1) else 0
                writer.writerow([epoch, mean_dice, dice_class1, hd_class1])
        print(f"Detailed validation metrics saved to {filename}")

    # Save training loss values
    if len(net.epoch_loss_values) > 0:
        filename = "DetailedTrainingLoss_HCC_Fixed.csv"
        with open(filename, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(['Epoch', 'Training_Loss'])

            for i in range(len(net.epoch_loss_values)):
                writer.writerow([i + 1, net.epoch_loss_values[i]])
        print(f"Detailed training loss saved to {filename}")

    # Save summary statistics
    filename = "TrainingSummary_HCC_Fixed.csv"
    with open(filename, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Metric', 'Value'])
        writer.writerow(['Best_Validation_Dice', net.best_val_dice])
        writer.writerow(['Best_Epoch', net.best_val_epoch])
        writer.writerow(['Total_Epochs', len(net.epoch_loss_values)])
        writer.writerow(['Final_Training_Loss', net.epoch_loss_values[-1] if net.epoch_loss_values else 'N/A'])
        writer.writerow(['Final_Validation_Dice', net.metric_values[-1] if net.metric_values else 'N/A'])
    print(f"Training summary saved to {filename}")


def plot_detailed_metrics(net):
    """Plot detailed training metrics"""
    if len(net.metric_values_class1) > 0:
        plt.figure("detailed_metrics", (15, 10))

        # Plot 1: Training Loss
        plt.subplot(2, 3, 1)
        plt.title("Epoch Average Loss")
        x = [i + 1 for i in range(len(net.epoch_loss_values))]
        y = net.epoch_loss_values
        plt.xlabel("epoch")
        plt.ylabel("Loss")
        plt.plot(x, y, color="red")
        plt.grid(True)

        # Plot 2: Validation Dice
        plt.subplot(2, 3, 2)
        plt.title("Val Mean Dice")
        x = [i*5 for i in range(len(net.metric_values))]  # Every 5 epochs
        y = net.metric_values
        plt.xlabel("epoch")
        plt.ylabel("Dice Score")
        plt.plot(x, y, color="green")
        plt.grid(True)

        # Plot 3: Class 1 Dice
        plt.subplot(2, 3, 3)
        plt.title("Val Mean Dice Class 1")
        x = [i*5 for i in range(len(net.metric_values_class1))]
        y_class1 = net.metric_values_class1
        plt.xlabel("epoch")
        plt.ylabel("Dice Score")
        plt.plot(x, y_class1, color="blue")
        plt.grid(True)

        # Plot 4: Hausdorff Distance
        plt.subplot(2, 3, 4)
        plt.title("Hausdorff Distance Class 1")
        x = [i*5 for i in range(len(net.haursdoff_values_class1))]
        y_hd_class1 = net.haursdoff_values_class1
        plt.xlabel("epoch")
        plt.ylabel("Hausdorff Distance")
        plt.plot(x, y_hd_class1, color='purple')
        plt.grid(True)

        # Plot 5: Combined Dice Scores
        plt.subplot(2, 3, 5)
        plt.title("Dice Scores Comparison")
        x = [i*5 for i in range(len(net.metric_values))]
        plt.plot(x, net.metric_values, color="green", label="Mean Dice")
        if len(net.metric_values_class1) > 0:
            plt.plot(x[:len(net.metric_values_class1)], net.metric_values_class1, color="blue", label="Class 1 Dice")
        plt.xlabel("epoch")
        plt.ylabel("Dice Score")
        plt.legend()
        plt.grid(True)

        # Plot 6: Training Progress Summary
        plt.subplot(2, 3, 6)
        plt.title("Training Summary")
        plt.text(0.1, 0.8, f"Best Dice: {net.best_val_dice:.4f}", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.7, f"Best Epoch: {net.best_val_epoch}", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.6, f"Total Epochs: {len(net.epoch_loss_values)}", fontsize=12, transform=plt.gca().transAxes)
        plt.text(0.1, 0.5, f"Final Loss: {net.epoch_loss_values[-1]:.4f}", fontsize=12, transform=plt.gca().transAxes)
        plt.axis('off')

        plt.tight_layout()
        plt.savefig('DetailedMetrics_HCC_Fixed.png', dpi=300, bbox_inches='tight')
        plt.show()


def perform_inference_and_visualization(model, net, roi_size, sw_batch_size, device):
    """Perform inference and visualize results with size information"""
    case_num = 0  # Select image number from val_ds
    size = 30  # Select the slice number (adjust based on your data)

    with torch.no_grad():
        if len(net.val_ds) > case_num:
            try:
                img_name = os.path.split(net.val_ds[case_num]["image"].meta["filename_or_obj"])[1]
                print(f"Processing: {img_name}")
            except:
                print(f"Processing validation sample {case_num}")

            image, label = net.val_ds[case_num]["image"], net.val_ds[case_num]["label"]
            val_inputs = torch.unsqueeze(image, 0).to(device)
            pred = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)
            pred_discret = torch.argmax(pred, dim=1)

            # Print size information
            print(f"Input image shape: {image.shape}")
            print(f"Label shape: {label.shape}")
            print(f"Prediction shape: {pred_discret.shape}")

            # Adjust slice number if needed
            max_slice = min(image.shape[-1], label.shape[-1], pred_discret.shape[-1]) - 1
            size = min(size, max_slice)
            print(f"Using slice {size} for visualization")

            plt.figure("inference_check", (18, 6))
            plt.subplot(1, 4, 1)
            plt.title(f"AP Phase (slice {size})\nShape: {image.shape}")
            plt.imshow(image[0, :, :, size], cmap="gray")
            plt.axis('off')

            plt.subplot(1, 4, 2)
            plt.title(f"PP Phase (slice {size})")
            plt.imshow(image[1, :, :, size], cmap="gray")
            plt.axis('off')

            plt.subplot(1, 4, 3)
            plt.title(f"Ground Truth\nShape: {label.shape}")
            plt.imshow(label[0, :, :, size])
            plt.axis('off')

            plt.subplot(1, 4, 4)
            plt.title(f"SwinUNETR Prediction\nShape: {pred_discret.shape}")
            plt.imshow(pred_discret.cpu()[0, :, :, size])
            plt.axis('off')

            plt.tight_layout()
            plt.savefig('Inference_Visualization_HCC_Fixed.png', dpi=300, bbox_inches='tight')
            plt.show()

            return pred_discret, label, val_inputs
        else:
            print("No validation data available for visualization")
            return None, None, None


def compute_meandice(y_pred, y, include_background=True, to_onehot_y=False,
                    mutually_exclusive=False, sigmoid=False, other_act=None, logit_thresh=0.5):
    """Compute mean dice coefficient"""
    from monai.networks import one_hot
    import warnings

    n_classes = y_pred.shape[1]
    n_len = len(y_pred.shape)

    if sigmoid and other_act is not None:
        raise ValueError("Incompatible values: sigmoid=True and other_act is not None.")
    if sigmoid:
        y_pred = y_pred.float().sigmoid()

    if other_act is not None:
        if not callable(other_act):
            raise TypeError(f"other_act must be None or callable but is {type(other_act).__name__}.")
        y_pred = other_act(y_pred)

    if n_classes == 1:
        if mutually_exclusive:
            warnings.warn("y_pred has only one class, mutually_exclusive=True ignored.")
        if to_onehot_y:
            warnings.warn("y_pred has only one channel, to_onehot_y=True ignored.")
        if not include_background:
            warnings.warn("y_pred has only one channel, include_background=False ignored.")
        # make both y and y_pred binary
        y_pred = (y_pred >= logit_thresh).float()
        y = (y > 0).float()
    else:  # multi-channel y_pred
        # make both y and y_pred binary
        if mutually_exclusive:
            if sigmoid:
                raise ValueError("Incompatible values: sigmoid=True and mutually_exclusive=True.")
            y_pred = torch.argmax(y_pred, dim=1, keepdim=True)
            y_pred = one_hot(y_pred, num_classes=n_classes)
        else:
            y_pred = (y_pred >= logit_thresh).float()
        if to_onehot_y:
            y = one_hot(y, num_classes=n_classes)

    if not include_background:
        y = y[:, 1:] if y.shape[1] > 1 else y
        y_pred = y_pred[:, 1:] if y_pred.shape[1] > 1 else y_pred

    assert y.shape == y_pred.shape, f"Ground truth one-hot has differing shape ({y.shape}) from source ({y_pred.shape})"
    y = y.float()
    y_pred = y_pred.float()

    # reducing only spatial dimensions (not batch nor channels)
    reduce_axis = list(range(2, n_len))
    intersection = torch.sum(y * y_pred, dim=reduce_axis)

    y_o = torch.sum(y, reduce_axis)
    y_pred_o = torch.sum(y_pred, dim=reduce_axis)
    denominator = y_o + y_pred_o

    f = torch.where(y_o > 0, (2.0 * intersection) / denominator, torch.tensor(float("nan"), device=y_o.device))
    return f  # returns array of Dice shape: [batch, n_classes]


def calculate_detailed_metrics(model, net, roi_size, sw_batch_size, device):
    """Calculate detailed metrics for the prediction"""
    from monai.networks import one_hot
    from typing import Union

    # Get a sample for metrics calculation
    pred_discret, label, val_inputs = perform_inference_and_visualization(model, net, roi_size, sw_batch_size, device)

    if pred_discret is not None and label is not None:
        val_labels = torch.unsqueeze(label, 0).to(device)

        def dice_metric_(y_pred: Union[np.ndarray, torch.Tensor],
                        y: Union[np.ndarray, torch.Tensor],
                        n_classes: int):
            if isinstance(y_pred, np.ndarray):
                y_pred = torch.from_numpy(y_pred)
            if isinstance(y, np.ndarray):
                y = torch.from_numpy(y)

            y = one_hot(y, n_classes, dim=1)
            y_pred = one_hot(y_pred, n_classes, dim=0).expand(1, n_classes, *y_pred.shape[1:])

            dice = compute_meandice(y_pred=y_pred, y=y, include_background=True)
            return np.around(dice[0].cpu().numpy(), decimals=3)

        print("Dice scores per class:")
        dice_scores = dice_metric_(y=val_labels.cpu(), y_pred=pred_discret.cpu(), n_classes=net.num_classes)
        for i, dice_score in enumerate(dice_scores):
            class_name = "Background" if i == 0 else f"HCC Class {i}"
            print(f"{class_name}: {dice_score}")

        # Calculate additional metrics
        print(f"\nOverall Metrics:")
        print(f"Mean Dice (excluding background): {np.mean(dice_scores[1:]):.3f}")
        print(f"Best validation Dice during training: {net.best_val_dice:.4f}")
        print(f"Best epoch: {net.best_val_epoch}")

        return dice_scores
    else:
        print("No validation data available for metrics calculation")
        return None


def save_predictions(model, net, roi_size, sw_batch_size, device):
    """Save predictions for all validation samples with original image size restoration"""
    from monai.transforms import SaveImage, Compose, LoadImaged, EnsureChannelFirstd, Orientationd, Spacingd, ConcatItemsd, DeleteItemsd, NormalizeIntensityd
    import traceback

    if len(net.val_ds) == 0:
        print("No validation data available for saving predictions")
        return

    softmax = torch.nn.Softmax(dim=1)

    # Create output directory
    output_dir = "HCC_Predictions_Fixed"
    os.makedirs(output_dir, exist_ok=True)

    # Create transform to load original images (without resizing)
    original_transform = Compose([
        LoadImaged(keys=["ap", "pp", "hbp", "label"]),
        FixDimensionsd(keys=["ap", "pp", "hbp", "label"]),
        EnsureChannelFirstd(keys=["ap", "pp", "hbp", "label"], channel_dim="no_channel"),
        Orientationd(keys=["ap", "pp", "hbp", "label"], axcodes="RAS"),
        Spacingd(
            keys=["ap", "pp", "hbp", "label"],
            pixdim=(1.5, 1.5, 3.0),
            mode=("bilinear", "bilinear", "bilinear", "nearest"),
        ),
        ConcatItemsd(keys=["ap", "pp", "hbp"], name="image", dim=0),
        DeleteItemsd(keys=["ap", "pp", "hbp"]),
        NormalizeIntensityd(keys="image", nonzero=True, channel_wise=True),
    ])

    # Get original data files for size restoration
    datalist = load_decathlon_datalist(dataset_json_path, True, "training")
    total_samples = len(datalist)
    train_size = int(0.7 * total_samples)
    val_files = datalist[train_size:]

    with torch.no_grad():
        for i, val_file in enumerate(val_files):
            try:
                # Load original size data
                original_data = original_transform(val_file)
                original_image = original_data["image"]
                original_label = original_data["label"]
                original_shape = original_image.shape[1:]  # Get spatial dimensions

                # Load preprocessed data for prediction
                val_data = net.val_ds[i]
                images = torch.unsqueeze(val_data["image"], 0).to(device)

                # Make prediction
                pred = sliding_window_inference(images, roi_size, sw_batch_size, model)

                # Resize prediction back to original image size
                pred_resized = torch.nn.functional.interpolate(
                    pred,
                    size=original_shape,
                    mode='trilinear',
                    align_corners=False
                )

                # Get filename for better identification
                try:
                    patient_name = os.path.basename(val_file["ap"]).replace("-ap.nii.gz", "")
                    output_filename = f"{i:03d}_{patient_name}"
                except:
                    output_filename = f"prediction_{i:03d}"

                # Save softmax probabilities (original size)
                output = softmax(pred_resized)
                prob_saver = SaveImage(
                    output_dir=output_dir,
                    output_ext=".nii.gz",
                    output_postfix="prob",
                    resample=False
                )
                # Use original image metadata for proper spacing and orientation
                prob_saver(output[0], meta_data=original_data["image"].meta)

                # Save discrete prediction (original size)
                pred_discrete = torch.argmax(pred_resized, dim=1, keepdim=True).float()
                discrete_saver = SaveImage(
                    output_dir=output_dir,
                    output_ext=".nii.gz",
                    output_postfix="discrete",
                    resample=False
                )
                # Use original image metadata for proper spacing and orientation
                discrete_saver(pred_discrete[0], meta_data=original_data["image"].meta)

                print(f"Successfully saved prediction {i+1}/{len(val_files)}: {output_filename}")
                print(f"  Original image shape: {original_shape}")
                print(f"  Prediction shape: {pred_discrete.shape[2:]}")

            except Exception as e:
                print(f"Error saving prediction {i+1}: {str(e)}")
                traceback.print_exc()
                continue

            # Limit to first few samples for demonstration
            if i >= 2:  # Save only first 3 predictions
                break

    print(f"Predictions saved to {output_dir}")

    # Create a summary visualization
    create_prediction_summary(output_dir, net)


def create_prediction_summary(output_dir, net):
    """Create a summary visualization of predictions"""
    print("Creating prediction summary...")

    # Create a summary plot
    plt.figure("prediction_summary", (15, 8))

    # Plot training metrics summary
    plt.subplot(2, 3, 1)
    plt.title("Training Loss")
    if len(net.epoch_loss_values) > 0:
        plt.plot(net.epoch_loss_values, 'r-')
        plt.xlabel("Epoch")
        plt.ylabel("Loss")
        plt.grid(True)

    plt.subplot(2, 3, 2)
    plt.title("Validation Dice")
    if len(net.metric_values) > 0:
        epochs = [i*5 for i in range(len(net.metric_values))]
        plt.plot(epochs, net.metric_values, 'g-')
        plt.xlabel("Epoch")
        plt.ylabel("Dice Score")
        plt.grid(True)

    plt.subplot(2, 3, 3)
    plt.title("Model Performance")
    plt.text(0.1, 0.8, f"Best Dice: {net.best_val_dice:.4f}", fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.6, f"Best Epoch: {net.best_val_epoch}", fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.4, f"Total Epochs: {len(net.epoch_loss_values)}", fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.2, f"Predictions saved: {output_dir}", fontsize=10, transform=plt.gca().transAxes)
    plt.axis('off')

    # Add dataset information
    plt.subplot(2, 3, 4)
    plt.title("Dataset Information")
    plt.text(0.1, 0.8, f"Training samples: {len(net.train_ds)}", fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.6, f"Validation samples: {len(net.val_ds)}", fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.4, f"Input channels: 3 (AP, PP, HBP)", fontsize=12, transform=plt.gca().transAxes)
    plt.text(0.1, 0.2, f"Output classes: {net.num_classes}", fontsize=12, transform=plt.gca().transAxes)
    plt.axis('off')

    plt.tight_layout()
    plt.savefig('Prediction_Summary_HCC_Fixed.png', dpi=300, bbox_inches='tight')
    plt.show()


# Main execution
if __name__ == "__main__":
    print("\nInitializing SwinUNETR model...")
    start_time = time.time()

    # initialising the LightningModule
    net = Net(num_classes=2)  # Adjust num_classes based on your segmentation task

    # set up checkpoints
    checkpoint_callback = ModelCheckpoint(
        dirpath=root_dir,
        filename="best_metric_model_hcc_fixed",
        monitor="val_dice",
        mode="max",
        save_top_k=1,
        save_last=True
    )

    early_stop_callback = EarlyStopping(
       monitor="val_dice",
       min_delta=0.001,
       patience=10,
       verbose=True,
       mode='max'
    )

    # stop training after 6 hours
    timer_callback = Timer(duration="00:16:00:00")

    # initialising Lightning's trainer
    trainer = pytorch_lightning.Trainer(
        precision='16-mixed',  # Updated precision format
        accelerator='gpu',
        devices=1,
        max_epochs=10,  # Reduced epochs for testing
        check_val_every_n_epoch=2,  # Validate every 2 epochs
        callbacks=[checkpoint_callback, early_stop_callback, timer_callback],
        default_root_dir=root_dir,
        enable_progress_bar=True,
        log_every_n_steps=1,
    )

    # training
    print("\nStarting training...")
    trainer.fit(net)

    print(f"train completed, best_metric: {net.best_val_dice:.4f} at epoch {net.best_val_epoch}")

    # Print timing information
    end_time = time.time()
    total_time = end_time - start_time
    print(f"Total training time: {total_time:.2f} seconds ({total_time/60:.2f} minutes)")

    # Save metrics
    print("\nSaving training metrics...")

    # Save training loss (recorded every epoch)
    if len(net.epoch_loss_values) > 0:
        loss_data = {
            'epoch': list(range(1, len(net.epoch_loss_values) + 1)),
            'train_loss': net.epoch_loss_values
        }
        loss_df = pd.DataFrame(loss_data)
        loss_df.to_csv('TrainingLoss_HCC_Fixed.csv', index=False)
        print(f"Training loss saved to TrainingLoss_HCC_Fixed.csv")

    # Save validation metrics (recorded every 5 epochs)
    if len(net.metric_values) > 0:
        val_epochs = [i*5 for i in range(len(net.metric_values))]
        val_data = {
            'epoch': val_epochs,
            'val_dice': net.metric_values,
        }

        if net.metric_values_class1 and len(net.metric_values_class1) > 0:
            # Ensure same length
            min_len = min(len(net.metric_values), len(net.metric_values_class1))
            val_data['val_dice_class1'] = net.metric_values_class1[:min_len]
            val_data['epoch'] = val_epochs[:min_len]
            val_data['val_dice'] = net.metric_values[:min_len]

        if net.haursdoff_values_class1 and len(net.haursdoff_values_class1) > 0:
            # Ensure same length
            min_len = min(len(val_data['val_dice']), len(net.haursdoff_values_class1))
            val_data['hausdorff_class1'] = net.haursdoff_values_class1[:min_len]
            # Trim other arrays to match
            for key in val_data:
                if isinstance(val_data[key], list):
                    val_data[key] = val_data[key][:min_len]

        val_df = pd.DataFrame(val_data)
        val_df.to_csv('ValidationMetrics_HCC_Fixed.csv', index=False)
        print(f"Validation metrics saved to ValidationMetrics_HCC_Fixed.csv")

    # Plot training curves
    if len(net.epoch_loss_values) > 0 and len(net.metric_values) > 0:
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 2, 1)
        plt.plot(net.epoch_loss_values)
        plt.title('Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')

        plt.subplot(1, 2, 2)
        plt.plot(net.metric_values)
        plt.title('Validation Dice Score')
        plt.xlabel('Epoch')
        plt.ylabel('Dice Score')

        plt.tight_layout()
        plt.savefig('EpochAverageLoss_ValMeanDice_HCC_Fixed.png')
        plt.show()

    print("Training pipeline completed successfully!")

    # ==========================================
    # POST-TRAINING ANALYSIS AND VISUALIZATION
    # ==========================================

    print("\n" + "="*60)
    print("POST-TRAINING ANALYSIS AND VISUALIZATION")
    print("="*60)

    # Save detailed metrics to CSV
    print("\nSaving detailed metrics to CSV files...")
    save_detailed_metrics_to_csv(net)

    # Plot additional detailed metrics
    print("\nGenerating detailed metric plots...")
    plot_detailed_metrics(net)

    # Load best model for inference
    print("\nLoading best model for inference...")
    try:
        # Try to load from checkpoint first
        best_model_path = os.path.join(root_dir, "best_metric_model_hcc_fixed.ckpt")
        if os.path.exists(best_model_path):
            model = Net.load_from_checkpoint(best_model_path)
            print(f"Loaded model from checkpoint: {best_model_path}")
        else:
            # Use the trained model
            model = net
            print("Using the trained model for inference")

        model.eval()
        model.to(device)

        # Perform inference and visualization
        print("\nPerforming inference on validation data...")
        perform_inference_and_visualization(model, net, roi_size, sw_batch_size, device)

        # Calculate detailed metrics
        print("\nCalculating detailed metrics...")
        calculate_detailed_metrics(model, net, roi_size, sw_batch_size, device)

        # Save predictions
        print("\nSaving predictions...")
        save_predictions(model, net, roi_size, sw_batch_size, device)

    except Exception as e:
        print(f"Error during inference: {e}")
        print("Skipping inference and visualization steps")

    print("\n" + "="*60)
    print("PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*60)
