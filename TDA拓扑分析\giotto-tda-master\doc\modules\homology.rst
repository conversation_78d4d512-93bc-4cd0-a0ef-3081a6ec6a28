:mod:`gtda.homology`: Persistent homology
=========================================

.. automodule:: gtda.homology
   :no-members:
   :no-inherited-members:

Undirected simplicial homology
------------------------------
.. currentmodule:: gtda

.. autosummary::
   :toctree: generated/homology/
   :template: class.rst

   homology.VietorisRipsPersistence
   homology.WeightedRipsPersistence
   homology.SparseRipsPersistence
   homology.WeakAlphaPersistence
   homology.EuclideanCechPersistence

Directed simplicial homology
----------------------------
.. currentmodule:: gtda

.. autosummary::
   :toctree: generated/homology/
   :template: class.rst

   homology.FlagserPersistence

Cubical homology
----------------
.. currentmodule:: gtda

.. autosummary::
   :toctree: generated/homology/
   :template: class.rst

   homology.CubicalPersistence
