# Custom attribute to mark sources as using our C++/C code style.
[attr]our-c-style  whitespace=tab-in-indent,no-lf-at-eof  hooks.style=KWStyle,clangformat

*.c              our-c-style
*.h              our-c-style
*.cxx            our-c-style
*.hxx            our-c-style
*.txx            our-c-style
*.txt            whitespace=tab-in-indent,no-lf-at-eof
*.cmake          whitespace=tab-in-indent,no-lf-at-eof

# ExternalData content links must have LF newlines
*.md5            crlf=input
*.sha512         crlf=input
