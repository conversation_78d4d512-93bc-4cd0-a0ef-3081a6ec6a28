{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# Fast training with MONAI features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This tutorial shows a regular PyTorch training program and a MONAI optimized training program, and compared the performance.  \n", "Mainly includes:\n", "1. AMP (Auto mixed precision).\n", "2. Cache<PERSON>ataset for deterministic transforms.\n", "3. Move data to GPU and cache, then execute random transforms on GPU.\n", "4. Disable meta tracking in the random transforms to avoid unnecessary computation.\n", "5. multi-threads `ThreadDataLoader` is faster than PyTorch DataLoader in light-weight task.\n", "6. Use MONAI `DiceCE` loss instead of regular `Dice` loss.\n", "7. Analyzed training curve and tuned algorithm: Use `SGD` optimizer, different network parameters, etc.\n", "\n", "With a A100 GPU and the target validation `mean dice = 0.94` of the `forground` channel only,  it's more than `150x` speedup compared with the Pytorch regular implementation when achieving the same metric. And every epoch is more than `50x` faster than regular training.\n", "\n", "It's modified from the Spleen 3D segmentation tutorial notebook, the Spleen dataset can be downloaded from http://medicaldecathlon.com/.\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/Project-MONAI/tutorials/blob/main/acceleration/fast_training_tutorial.ipynb)(* please note that the free GPU resource in Colab may be not as powerful as the A100 test results in this notebook: it may not support AMP and the GPU computation of transforms may be not faster than the CPU computation.)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import math\n", "import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import torch\n", "from torch.optim import Adam, SGD\n", "from monai.apps import download_and_extract\n", "from monai.config import print_config\n", "from monai.data import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Dataset,\n", "    decollate_batch,\n", "    set_track_meta,\n", ")\n", "from monai.inferers import sliding_window_inference\n", "from monai.losses import DiceLoss, DiceCELoss\n", "from monai.metrics import DiceMetric\n", "from monai.networks.layers import Act, Norm\n", "from monai.networks.nets import UNet\n", "from monai.transforms import (\n", "    EnsureChannelFirstd,\n", "    As<PERSON>iscrete,\n", "    <PERSON><PERSON><PERSON>,\n", "    CropForegroundd,\n", "    EnsureTyped,\n", "    FgBgToIndicesd,\n", "    LoadImaged,\n", "    Orientationd,\n", "    RandCropByPosNegLabeld,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", ")\n", "from monai.utils import set_determinism\n", "\n", "# for profiling\n", "import nvtx\n", "from monai.utils.nvtx import Range\n", "import contextlib  # to improve code readability (combining training/validation loop with and without profiling)\n", "\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data & output directories\n", "\n", "You can specify a directory with the `MONAI_DATA_DIRECTORY` environment variable.  \n", "This allows you to save results and reuse downloads.  \n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "if directory is not None:\n", "    os.makedirs(directory, exist_ok=True)\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(f\"root dir is: {root_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By default, outputs will go to `outputs/`. \n", "\n", "You can run this tutorial twice, once with profiling and once without, and the outputs will not conflict with each other. \n", "- When profiling, the output is `outputs/output_base.nsys-rep`, which you can then visualize using the GUI of Nsight systems (a brief guide is provided in the \"Profiling visualization\" section below).\n", "- When not profiling, the outputs are `outputs/loss_dice_comparison.png`, `outputs/metric_time_epochs.png`, and `outputs/total_epoch_time_comparison.png`.\n", "\n", "We set up the tutorial such that figures are only generated when not profiling, but that does not have to be the case. In general, the figures make more sense when training is run for a higher number of epochs (e.g., hundreds), which is usually not the case when profiling."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outputs\n", "\n", "out_dir = \"outputs/\"\n", "\n", "if not os.path.exists(out_dir):\n", "    os.makedirs(out_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Profiling"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This section sets up profiling for this tutorial.\n", "\n", "The number of epochs is automatically set based on whether profiling is being performed, but you can modify as needed.\n", "\n", "- If you are not interested in profiling, please set `profiling = False` and move on.\n", "\n", "- If you are profiling:\n", "\n", "  - Because of the currently supported functionality of Nsight systems (`nsys`), profiling can only be performed from the terminal, and not from within this tutorial. For more information, including installation, refer to the [NVIDIA Nsight Systems page](https://developer.nvidia.com/nsight-systems).\n", "  - Perform the following steps:\n", "  \n", "    1) Make sure `nsys` is installed;\n", "    \n", "    2) Set `profiling = True`;\n", "    \n", "    3) Make sure all lines in \"Setup environment\" (first code cell in this tutorial, above) are commented out;\n", "    \n", "    4) Save this notebook;\n", "    \n", "    5) Open the terminal and ensure that you are in the directory of this notebook, then run this command:\n", "    `jupyter nbconvert fast_training_tutorial.ipynb --to python && nsys profile --output ./outputs/output_base --force-overwrite true --trace-fork-before-exec true python3 fast_training_tutorial.py ; rm fast_training_tutorial.py`\n", "    \n", "    This command converts the notebook to a Python script locally and runs `nsys`. The output file is `outputs/output_base.nsys-rep`, but you can modify `--output` to specify the desired location."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["profiling = False\n", "\n", "# to see the trend in training curve and dice results, set max_epochs to be larger (300)\n", "# note that before optimization, training can be quite a bit slower\n", "if profiling:\n", "    max_epochs = 6\n", "else:\n", "    max_epochs = 300\n", "\n", "# to improve readability\n", "\n", "\n", "def range_func(x, y):\n", "    return Range(x)(y) if profiling else y\n", "\n", "\n", "no_profiling = contextlib.nullcontext()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download dataset\n", "\n", "Downloads and extracts the Decathlon Spleen dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["resource = \"https://msd-for-monai.s3-us-west-2.amazonaws.com/Task09_Spleen.tar\"\n", "md5 = \"410d4a301da4e5b2f6f86ec3ddba524e\"\n", "\n", "compressed_file = os.path.join(root_dir, \"Task09_Spleen.tar\")\n", "data_root = os.path.join(root_dir, \"Task09_Spleen\")\n", "if not os.path.exists(data_root):\n", "    download_and_extract(resource, compressed_file, root_dir, md5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set MSD Spleen dataset path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_images = sorted(glob.glob(os.path.join(data_root, \"imagesTr\", \"*.nii.gz\")))\n", "train_labels = sorted(glob.glob(os.path.join(data_root, \"labelsTr\", \"*.nii.gz\")))\n", "data_dicts = [{\"image\": image_name, \"label\": label_name} for image_name, label_name in zip(train_images, train_labels)]\n", "train_files, val_files = data_dicts[:-9], data_dicts[-9:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup transforms for training and validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transformations(fast=False, device=\"cuda:0\"):\n", "    train_transforms = [\n", "        range_func(\"LoadImage\", LoadImaged(keys=[\"image\", \"label\"])),\n", "        range_func(\"EnsureChannelFirst\", EnsureChannelFirstd(keys=[\"image\", \"label\"])),\n", "        range_func(\"Orientation\", Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\")),\n", "        range_func(\n", "            \"Spacing\",\n", "            Spacingd(\n", "                keys=[\"image\", \"label\"],\n", "                pixdim=(1.5, 1.5, 2.0),\n", "                mode=(\"bilinear\", \"nearest\"),\n", "            ),\n", "        ),\n", "        range_func(\n", "            \"ScaleIntensityRange\",\n", "            ScaleIntensityRanged(\n", "                keys=[\"image\"],\n", "                a_min=-57,\n", "                a_max=164,\n", "                b_min=0.0,\n", "                b_max=1.0,\n", "                clip=True,\n", "            ),\n", "        ),\n", "        range_func(\"CropForeground\", CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\")),\n", "        # pre-compute foreground and background indexes\n", "        # and cache them to accelerate training\n", "        range_func(\n", "            \"Indexing\",\n", "            FgBgToIndicesd(\n", "                keys=\"label\",\n", "                fg_postfix=\"_fg\",\n", "                bg_postfix=\"_bg\",\n", "                image_key=\"image\",\n", "            ),\n", "        ),\n", "    ]\n", "\n", "    if fast:\n", "        # convert the data to Tensor without meta, move to GPU and cache to avoid CPU -> GPU sync in every epoch\n", "        train_transforms.append(\n", "            range_func(\"EnsureType\", EnsureTyped(keys=[\"image\", \"label\"], device=device, track_meta=False))\n", "        )\n", "\n", "    train_transforms.append(\n", "        # randomly crop out patch samples from big\n", "        # image based on pos / neg ratio\n", "        # the image centers of negative samples\n", "        # must be in valid image area\n", "        range_func(\n", "            \"RandCrop\",\n", "            RandCropByPosNegLabeld(\n", "                keys=[\"image\", \"label\"],\n", "                label_key=\"label\",\n", "                spatial_size=(96, 96, 96),\n", "                pos=1,\n", "                neg=1,\n", "                num_samples=4,\n", "                fg_indices_key=\"label_fg\",\n", "                bg_indices_key=\"label_bg\",\n", "            ),\n", "        ),\n", "    )\n", "\n", "    val_transforms = [\n", "        LoadImaged(keys=[\"image\", \"label\"]),\n", "        EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        Spacingd(\n", "            keys=[\"image\", \"label\"],\n", "            pixdim=(1.5, 1.5, 2.0),\n", "            mode=(\"bilinear\", \"nearest\"),\n", "        ),\n", "        ScaleIntensityRanged(\n", "            keys=[\"image\"],\n", "            a_min=-57,\n", "            a_max=164,\n", "            b_min=0.0,\n", "            b_max=1.0,\n", "            clip=True,\n", "        ),\n", "        CropForegroundd(keys=[\"image\", \"label\"], source_key=\"image\"),\n", "    ]\n", "    if fast:\n", "        # convert the data to Tensor without meta, move to GPU and cache to avoid CPU -> GPU sync in every epoch\n", "        val_transforms.append(EnsureTyped(keys=[\"image\", \"label\"], device=device, track_meta=False))\n", "\n", "    return Compose(train_transforms), Compose(val_transforms)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the training progress\n", "For a typical PyTorch regular training procedure, use regular `Dataset`, `DataLoader`, `Adam` optimizer and `Di<PERSON>` loss to train the model.\n", "\n", "For MONAI fast training progress, we mainly introduce the following features:\n", "1. `AMP` (auto mixed precision): AMP is an important feature released in PyTorch v1.6, NVIDIA CUDA 11 added strong support for AMP and significantly improved training speed.\n", "2. `CacheDataset`: Dataset with the cache mechanism that can load data and cache deterministic transforms' result during training.\n", "3. `EnsureTyped` transform: to move data to GPU and cache with `CacheDataset`, then execute random transforms on GPU directly, avoid CPU -> GPU sync in every epoch. Please note that not all the MONAI transforms support GPU operation so far, still working in progress.\n", "4. `set_track_meta(False)`: to disable meta tracking in the random transforms to avoid unnecessary computation.\n", "5. `ThreadDataLoader`: uses multi-threads instead of multi-processing, faster than `DataLoader` in light-weight task as we already cached the results of most computation.\n", "6. `DiceCE` loss function: computes Dice loss and Cross Entropy Loss, returns the weighted sum of these two losses.\n", "7. Analyzed the training curve and tuned algorithm: Use `SGD` optimizer, different network parameters, etc.\n", "\n", "(A note on code: to improve readability and support the profiling flag, we used the `with nvtx(...) if profiling else no_profiling` context pattern, where `no_profiling` is a null context from <PERSON>'s native `contextlib` with no effect on the code. An acknowledgement is provided here[<sup id=\"fn1-back\">1</sup>](#fn1).)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_process(fast=False):\n", "    learning_rate = 2e-4\n", "    val_interval = 5  # do validation for every epoch\n", "    set_track_meta(True)\n", "\n", "    if torch.cuda.is_available():\n", "        device = torch.device(\"cuda:0\")\n", "    else:\n", "        raise RuntimeError(\"this tutorial is intended for GPU, but no CUDA device is available\")\n", "\n", "    train_trans, val_trans = transformations(fast=fast, device=device)\n", "    # set <PERSON><PERSON><PERSON><PERSON><PERSON>, ThreadDataLoader and DiceCE loss for MONAI fast training\n", "    if fast:\n", "        # as `RandCropByPosNegLabeld` crops from the cached content and `deepcopy`\n", "        # the crop area instead of modifying the cached value, we can set `copy_cache=False`\n", "        # to avoid unnecessary deepcopy of cached content in `CacheDataset`\n", "        train_ds = CacheDataset(\n", "            data=train_files,\n", "            transform=train_trans,\n", "            cache_rate=1.0,\n", "            num_workers=8,\n", "            copy_cache=False,\n", "        )\n", "        val_ds = CacheDataset(data=val_files, transform=val_trans, cache_rate=1.0, num_workers=5, copy_cache=False)\n", "        # disable multi-workers because `ThreadDataLoader` works with multi-threads\n", "        train_loader = ThreadDataLoader(train_ds, num_workers=0, batch_size=4, shuffle=True)\n", "        val_loader = ThreadDataLoader(val_ds, num_workers=0, batch_size=1)\n", "\n", "        loss_function = DiceCELoss(\n", "            include_background=False,\n", "            to_onehot_y=True,\n", "            softmax=True,\n", "            squared_pred=True,\n", "            batch=True,\n", "            smooth_nr=0.00001,\n", "            smooth_dr=0.00001,\n", "            lambda_dice=0.5,\n", "            lambda_ce=0.5,\n", "        )\n", "        model = UNet(\n", "            spatial_dims=3,\n", "            in_channels=1,\n", "            out_channels=2,\n", "            channels=(32, 64, 128, 256, 512),\n", "            strides=(2, 2, 2, 2),\n", "            num_res_units=2,\n", "            norm=Norm.BATCH,\n", "            kernel_size=3,\n", "            up_kernel_size=3,\n", "            act=Act.PRELU,\n", "            dropout=0.2,\n", "            bias=True,\n", "        ).to(device)\n", "        # avoid the computation of meta information in random transforms\n", "        set_track_meta(False)\n", "    else:\n", "        train_ds = Dataset(data=train_files, transform=train_trans)\n", "        val_ds = Dataset(data=val_files, transform=val_trans)\n", "        train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=8)\n", "        val_loader = DataLoader(val_ds, batch_size=1, num_workers=4)\n", "        loss_function = DiceLoss(to_onehot_y=True, softmax=True)\n", "        model = UNet(\n", "            spatial_dims=3,\n", "            in_channels=1,\n", "            out_channels=2,\n", "            channels=(16, 32, 64, 128, 256),\n", "            strides=(2, 2, 2, 2),\n", "            num_res_units=2,\n", "            norm=Norm.BATCH,\n", "        ).to(device)\n", "\n", "    post_pred = Compose([AsDiscrete(argmax=True, to_onehot=2)])\n", "    post_label = Compose([AsDiscrete(to_onehot=2)])\n", "\n", "    dice_metric = DiceMetric(include_background=False, reduction=\"mean\", get_not_nans=False)\n", "\n", "    if fast:\n", "        # SGD prefer to much bigger learning rate\n", "        optimizer = SGD(\n", "            model.parameters(),\n", "            lr=learning_rate * 1000,\n", "            momentum=0.9,\n", "            weight_decay=0.00004,\n", "        )\n", "        scaler = torch.cuda.amp.GradScaler()\n", "    else:\n", "        optimizer = Adam(model.parameters(), learning_rate)\n", "\n", "    best_metric = -1\n", "    best_metric_epoch = -1\n", "    best_metrics_epochs_and_time = [[], [], []]\n", "    epoch_loss_values = []\n", "    metric_values = []\n", "    epoch_times = []\n", "    total_start = time.time()\n", "\n", "    for epoch in range(max_epochs):\n", "        epoch_start = time.time()\n", "        print(\"-\" * 10)\n", "        print(f\"epoch {epoch + 1}/{max_epochs}\")\n", "\n", "        # profiling: full epoch\n", "        with nvtx.annotate(\"epoch\", color=\"red\") if profiling else no_profiling:\n", "            model.train()\n", "            epoch_loss = 0\n", "            train_loader_iterator = iter(train_loader)\n", "\n", "            # using step instead of iterate through train_loader directly to track data loading time\n", "            # steps are 1-indexed for printing and calculation purposes\n", "            for step in range(1, len(train_loader) + 1):\n", "                step_start = time.time()\n", "\n", "                # profiling: train dataload\n", "                with nvtx.annotate(\"dataload\", color=\"red\") if profiling else no_profiling:\n", "                    # rng_train_dataload = nvtx.start_range(message=\"dataload\", color=\"red\")\n", "                    batch_data = next(train_loader_iterator)\n", "                    inputs, labels = (\n", "                        batch_data[\"image\"].to(device),\n", "                        batch_data[\"label\"].to(device),\n", "                    )\n", "\n", "                optimizer.zero_grad()\n", "                # set AMP for MONAI training\n", "                if fast:\n", "                    # profiling: forward\n", "                    with nvtx.annotate(\"forward\", color=\"green\") if profiling else no_profiling:\n", "                        with torch.cuda.amp.autocast():\n", "                            outputs = model(inputs)\n", "                            loss = loss_function(outputs, labels)\n", "\n", "                    # profiling: backward\n", "                    with nvtx.annotate(\"backward\", color=\"blue\") if profiling else no_profiling:\n", "                        scaler.scale(loss).backward()\n", "\n", "                    # profiling: update\n", "                    with nvtx.annotate(\"update\", color=\"yellow\") if profiling else no_profiling:\n", "                        scaler.step(optimizer)\n", "                        scaler.update()\n", "                else:\n", "                    # profiling: forward\n", "                    with nvtx.annotate(\"forward\", color=\"green\") if profiling else no_profiling:\n", "                        outputs = model(inputs)\n", "                        loss = loss_function(outputs, labels)\n", "\n", "                    # profiling: backward\n", "                    with nvtx.annotate(\"backward\", color=\"blue\") if profiling else no_profiling:\n", "                        loss.backward()\n", "\n", "                    # profiling: update\n", "                    with nvtx.annotate(\"update\", color=\"yellow\") if profiling else no_profiling:\n", "                        optimizer.step()\n", "\n", "                epoch_loss += loss.item()\n", "                epoch_len = math.ceil(len(train_ds) / train_loader.batch_size)\n", "                print(\n", "                    f\"{step}/{epoch_len}, train_loss: {loss.item():.4f}\" f\" step time: {(time.time() - step_start):.4f}\"\n", "                )\n", "            epoch_loss /= step\n", "            epoch_loss_values.append(epoch_loss)\n", "            print(f\"epoch {epoch + 1} average loss: {epoch_loss:.4f}\")\n", "\n", "            if (epoch + 1) % val_interval == 0:\n", "                model.eval()\n", "                with torch.no_grad():\n", "                    val_loader_iterator = iter(val_loader)\n", "\n", "                    for _ in range(len(val_loader)):\n", "                        # profiling: val dataload\n", "                        with nvtx.annotate(\"dataload\", color=\"red\") if profiling else no_profiling:\n", "                            val_data = next(val_loader_iterator)\n", "                            val_inputs, val_labels = (\n", "                                val_data[\"image\"].to(device),\n", "                                val_data[\"label\"].to(device),\n", "                            )\n", "\n", "                        roi_size = (160, 160, 160)\n", "                        sw_batch_size = 4\n", "\n", "                        # profiling: sliding window\n", "                        with nvtx.annotate(\"sliding window\", color=\"green\") if profiling else no_profiling:\n", "                            # set AMP for MONAI validation\n", "                            if fast:\n", "                                with torch.cuda.amp.autocast():\n", "                                    val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)\n", "                            else:\n", "                                val_outputs = sliding_window_inference(val_inputs, roi_size, sw_batch_size, model)\n", "\n", "                        # profiling: decollate batch\n", "                        with nvtx.annotate(\"decollate batch\", color=\"blue\") if profiling else no_profiling:\n", "                            val_outputs = [post_pred(i) for i in decollate_batch(val_outputs)]\n", "                            val_labels = [post_label(i) for i in decollate_batch(val_labels)]\n", "\n", "                        # profiling: compute metric\n", "                        with nvtx.annotate(\"compute metric\", color=\"yellow\") if profiling else no_profiling:\n", "                            dice_metric(y_pred=val_outputs, y=val_labels)\n", "\n", "                    metric = dice_metric.aggregate().item()\n", "                    dice_metric.reset()\n", "                    metric_values.append(metric)\n", "                    if metric > best_metric:\n", "                        best_metric = metric\n", "                        best_metric_epoch = epoch + 1\n", "                        best_metrics_epochs_and_time[0].append(best_metric)\n", "                        best_metrics_epochs_and_time[1].append(best_metric_epoch)\n", "                        best_metrics_epochs_and_time[2].append(time.time() - total_start)\n", "                        torch.save(model.state_dict(), os.path.join(root_dir, \"best_metric_model.pt\"))\n", "                        print(\"saved new best metric model\")\n", "                    print(\n", "                        f\"current epoch: {epoch + 1} current\"\n", "                        f\" mean dice: {metric:.4f}\"\n", "                        f\" best mean dice: {best_metric:.4f}\"\n", "                        f\" at epoch: {best_metric_epoch}\"\n", "                    )\n", "        print(f\"time consuming of epoch {epoch + 1} is:\" f\" {(time.time() - epoch_start):.4f}\")\n", "        epoch_times.append(time.time() - epoch_start)\n", "\n", "    total_time = time.time() - total_start\n", "    print(\n", "        f\"train completed, best_metric: {best_metric:.4f}\"\n", "        f\" at epoch: {best_metric_epoch}\"\n", "        f\" total time: {total_time:.4f}\"\n", "    )\n", "    return (\n", "        max_epochs,\n", "        epoch_loss_values,\n", "        metric_values,\n", "        epoch_times,\n", "        best_metrics_epochs_and_time,\n", "        total_time,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable determinism and execute regular PyTorch training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["set_determinism(seed=0)\n", "regular_start = time.time()\n", "(\n", "    epoch_num,\n", "    epoch_loss_values,\n", "    metric_values,\n", "    epoch_times,\n", "    best,\n", "    train_time,\n", ") = train_process(fast=False)\n", "total_time = time.time() - regular_start\n", "print(f\"total time of {epoch_num} epochs with regular PyTorch training: {total_time:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enable determinism and execute MONAI optimized training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set_determinism(seed=0)\n", "monai_start = time.time()\n", "(\n", "    epoch_num,\n", "    m_epoch_loss_values,\n", "    m_metric_values,\n", "    m_epoch_times,\n", "    m_best,\n", "    m_train_time,\n", ") = train_process(fast=True)\n", "m_total_time = time.time() - monai_start\n", "print(\n", "    f\"total time of {epoch_num} epochs with MONAI fast training: {m_train_time:.4f},\"\n", "    f\" time of preparing cache: {(m_total_time - m_train_time):.4f}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Profiling visualization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we give a brief overview of key observations from the `nsys-rep` output file when opened in Nsight systems (2022.2.1).\n", "\n", "In the GUI, select File -> Open -> output_base.nsys-rep. Here is a sample of the display:\n", "\n", "![png](figures/nsys-all-annotated.png)\n", "- To get a better view of details, you can left-click and select a horizontal section, then right-click and \"Zoom into Selection.\" To return, right-click and select \"Reset Zoom.\"\n", "- Sections B and C show training before and after acceleration (when fast=False and fast=True, accordingly). Clearly, MONAI optimized training is much faster than regular PyTorch training. B and C both contain two rows; the upper row shows per-epoch time, and the lower row shows per-action time (user-defined, like dataloading, forward, backward, etc.).\n", "- Section A shows GPU utilization, where the height of the blue bars represents utilization rate. Regular PyTorch training shows sporadic and varying levels of GPU utilization, while MONAI optimized training shows consistent and high levels of GPU utilization.\n", "\n", "Expanding one more thread in the lower left corner and several more threads below \\[4648\\], we see the following:\n", "\n", "![png](figures/nsys-transforms-annotated.png)\n", "\n", "Sections D and E both include information on the transform chain.\n", "- Section E: In MONAI optimized training, results of all transforms in the chain until the first randomized transform is stored to prevent repeated operations. This explains why E is chronologically before any of the training epochs in the figure.\n", "- Section D: In regular PyTorch training, CacheDataset is not in use, and the transform chain is performed every epoch on all data used.\n", "\n", "Here is the display of the transform chain when zoomed in:\n", "![png](figures/nsys-fast-transform.png)\n", "\n", "And a display of one training epoch of MONAI optimized training when zoomed in:\n", "![png](figures/nsys-epoch-short.png)\n", "\n", "Notice that the per-epoch time is >20 times faster than the regular PyTorch training."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot training loss and validation metrics"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if not profiling:\n", "    plt.figure(\"train\", (12, 12))\n", "    plt.subplot(2, 2, 1)\n", "    plt.title(\"Regular Epoch Average Loss\")\n", "    x = [i + 1 for i in range(len(epoch_loss_values))]\n", "    y = epoch_loss_values\n", "    plt.xlabel(\"epoch\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.plot(x, y, color=\"red\")\n", "\n", "    plt.subplot(2, 2, 2)\n", "    plt.title(\"Regular <PERSON> Mean <PERSON>ce\")\n", "    x = [(i + 1) * 5 for i in range(len(metric_values))]\n", "    y = metric_values\n", "    plt.xlabel(\"epoch\")\n", "    plt.ylim(0, 1)\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.plot(x, y, color=\"red\")\n", "\n", "    plt.subplot(2, 2, 3)\n", "    plt.title(\"Fast Epoch Average Loss\")\n", "    x = [i + 1 for i in range(len(m_epoch_loss_values))]\n", "    y = m_epoch_loss_values\n", "    plt.xlabel(\"epoch\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.plot(x, y, color=\"green\")\n", "\n", "    plt.subplot(2, 2, 4)\n", "    plt.title(\"Fast Val Mean Dice\")\n", "    x = [(i + 1) * 5 for i in range(len(m_metric_values))]\n", "    y = m_metric_values\n", "    plt.xlabel(\"epoch\")\n", "    plt.ylim(0, 1)\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.plot(x, y, color=\"green\")\n", "    plt.savefig(\"outputs/loss_dice_comparison.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot total time and every epoch time"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if not profiling:\n", "    plt.figure(\"train\", (12, 6))\n", "    plt.subplot(1, 2, 1)\n", "    plt.title(\"Total Train Time(300 epochs)\")\n", "    plt.bar(\"regular PyTorch\", total_time, 1, label=\"Regular training\", color=\"red\")\n", "    plt.bar(\"Fast\", m_total_time, 1, label=\"Fast training\", color=\"green\")\n", "    plt.ylabel(\"secs\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.legend(loc=\"best\")\n", "\n", "    plt.subplot(1, 2, 2)\n", "    plt.title(\"Epoch Time\")\n", "    x = [i + 1 for i in range(len(epoch_times))]\n", "    plt.xlabel(\"epoch\")\n", "    plt.ylabel(\"secs\")\n", "    plt.plot(x, epoch_times, label=\"Regular training\", color=\"red\")\n", "    plt.plot(x, m_epoch_times, label=\"Fast training\", color=\"green\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.legend(loc=\"best\")\n", "    plt.savefig(\"outputs/total_epoch_time_comparison.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot total time to achieve metrics"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def get_best_metric_time(threshold, best_values):\n", "    for i, v in enumerate(best_values[0]):\n", "        if round(v, 4) >= threshold:\n", "            return best_values[2][i]\n", "    return -1\n", "\n", "\n", "def get_best_metric_epochs(threshold, best_values):\n", "    for i, v in enumerate(best_values[0]):\n", "        if round(v, 4) >= threshold:\n", "            return best_values[1][i]\n", "    return -1\n", "\n", "\n", "def get_label(index):\n", "    if index == 0:\n", "        return \"Regular training\"\n", "    elif index == 1:\n", "        return \"Fast training\"\n", "    else:\n", "        return None\n", "\n", "\n", "if not profiling:\n", "    plt.set_loglevel(\"WARNING\")\n", "\n", "    plt.figure(\"train\", (18, 6))\n", "    plt.subplot(1, 3, 1)\n", "    plt.title(\"Metrics Time\")\n", "    plt.xlabel(\"secs\")\n", "    plt.ylabel(\"best mean_dice\")\n", "    plt.plot(best[2], best[0], label=\"Regular training\", color=\"red\")\n", "    plt.plot(m_best[2], m_best[0], label=\"Fast training\", color=\"green\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.legend(loc=\"best\")\n", "\n", "    plt.subplot(1, 3, 2)\n", "    plt.title(\"Typical Metrics Time\")\n", "    plt.xlabel(\"best mean_dice\")\n", "    plt.ylabel(\"secs\")\n", "    labels = [\"0.80\", \"0.80 \", \"0.90\", \"0.90 \", \"0.92\", \"0.92 \", \"0.94\", \"0.94 \"]\n", "    x_values = [0.8, 0.8, 0.9, 0.9, 0.92, 0.92, 0.94, 0.94]\n", "    for i, (l, x) in enumerate(zip(labels, x_values)):\n", "        value = int(get_best_metric_time(x, best if i % 2 == 0 else m_best))\n", "        color = \"red\" if i % 2 == 0 else \"green\"\n", "        plt.bar(l, value, 0.5, label=get_label(i), color=color)\n", "        plt.text(l, value, \"%s\" % value, ha=\"center\", va=\"bottom\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.legend(loc=\"best\")\n", "\n", "    plt.subplot(1, 3, 3)\n", "    plt.title(\"Typical Metrics Epochs\")\n", "    plt.xlabel(\"best mean_dice\")\n", "    plt.ylabel(\"epochs\")\n", "    for i, (l, x) in enumerate(zip(labels, x_values)):\n", "        value = int(get_best_metric_epochs(x, best if i % 2 == 0 else m_best))\n", "        color = \"red\" if i % 2 == 0 else \"green\"\n", "        plt.bar(l, value, 0.5, label=get_label(i), color=color)\n", "        plt.text(l, value, \"%s\" % value, ha=\"center\", va=\"bottom\")\n", "    plt.grid(alpha=0.4, linestyle=\":\")\n", "    plt.legend(loc=\"best\")\n", "    plt.savefig(\"outputs/metric_time_epochs.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[<sup id=\"fn1\">1</sup>](#fn1-back) Acknowledgement: This usage is inspired by [Conditional with statement in Python](https://stackoverflow.com/a/68682614) by [<PERSON>](https://stackoverflow.com/users/10712525/l<PERSON><PERSON>-vaz<PERSON>), used with adaptations under [CC BY-SA 4.0](https://creativecommons.org/licenses/by-sa/4.0/), accessed June 14, 2022."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}