_meta_: {}
bundle_root: ./ref_api_work_dir\segresnet_3
ckpt_path: $@bundle_root + '/model'
mlflow_tracking_uri: $@ckpt_path + '/mlruns/'
mlflow_experiment_name: Auto3DSeg
data_file_base_dir: C:\Users\<USER>\AppData\Local\Temp\tmp9lauk690\Task04_Hippocampus
data_list_file_path: "f:\\2. python\u6DF1\u5EA6\u5B66\u4E60\u8BFE\u7A0B\\1.\u65E5\u6708\
  \u5149\u534E-pyorch\u6DF1\u5EA6\u5B66\u4E60\u8BFE\u7A0B\\0.\u6DF1\u5EA6\u5B66\u4E60\
  \u548CR\u4EE3\u7801\u603B\u7ED3\\\u6DF1\u5EA6\u5B66\u4E60\u4EE3\u7801\u603B\u7ED3\
  \u6700\u65B0\u7248\\Monai-tutorials\\auto3dseg\\tasks\\msd\\Task04_Hippocampus\\\
  msd_task04_hippocampus_folds.json"
modality: mri
fold: 3
input_channels: 1
output_classes: 3
class_names: null
class_index: null
debug: false
ckpt_save: true
cache_rate: null
roi_size: [32, 48, 32]
auto_scale_allowed: true
auto_scale_batch: true
auto_scale_roi: false
auto_scale_filters: false
quick: false
channels_last: true
validate_final_original_res: true
calc_val_loss: false
amp: true
log_output_file: null
cache_class_indices: null
early_stopping_fraction: 0.001
determ: false
orientation_ras: true
crop_foreground: true
learning_rate: 0.0002
batch_size: 1
num_images_per_batch: 1
num_epochs: 1250
num_warmup_epochs: 3
sigmoid: false
resample: false
resample_resolution: [1.0, 1.0, 1.0]
crop_mode: rand
normalize_mode: meanstd
intensity_bounds: [12400.749216079712, 55249.28563639323]
num_epochs_per_validation: null
num_epochs_per_saving: 1
num_workers: 4
num_steps_per_image: null
num_crops_per_image: 1
loss: {_target_: DiceCELoss, include_background: true, squared_pred: true, smooth_nr: 0,
  smooth_dr: 1.0e-05, softmax: $not @sigmoid, sigmoid: $@sigmoid, to_onehot_y: $not
    @sigmoid}
optimizer: {_target_: torch.optim.AdamW, lr: '@learning_rate', weight_decay: 1.0e-05}
network:
  _target_: SegResNetDS
  init_filters: 32
  blocks_down: [1, 2, 2, 4, 4]
  norm: INSTANCE_NVFUSER
  in_channels: '@input_channels'
  out_channels: '@output_classes'
  dsdepth: 4
finetune: {enabled: false, ckpt_name: $@bundle_root + '/model/model.pt'}
validate: {enabled: false, ckpt_name: $@bundle_root + '/model/model.pt', output_path: $@bundle_root
    + '/prediction_validation', save_mask: false, invert: true}
infer: {enabled: false, ckpt_name: $@bundle_root + '/model/model.pt', output_path: $@bundle_root
    + '/prediction_' + @infer#data_list_key, data_list_key: testing}
anisotropic_scales: false
spacing_median: [1.0, 1.0, 1.0]
spacing_lower: [1.0, 1.0, 1.0]
spacing_upper: [1.0, 1.0, 1.0]
image_size_mm_median: [35.0, 49.5, 37.0]
image_size_mm_90: [38.0, 54.1, 40.0]
image_size: [38, 54, 40]
name: Task04_Hippocampus
task: segmentation
