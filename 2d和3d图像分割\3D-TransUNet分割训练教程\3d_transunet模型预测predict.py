#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D-TransUNet 模型预测脚本
支持单个或批量3D MRI图像分割预测
"""

import os
import sys
import subprocess
import importlib

def install_dependencies():
    """自动安装必要的依赖包"""
    required_packages = [
        'ml_collections',
        'batchgenerators',
        'medpy',
        'einops',
        'fvcore',
        'tensorboardX',
        'termcolor',
        'tabulate',
        'iopath',
        'portalocker'
    ]

    print("检查并安装必要的依赖包...")

    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败，请手动安装")

    print("依赖包检查完成！")

# 安装依赖
install_dependencies()

import argparse
import pickle
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast
import SimpleITK as sitk
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
from typing import Tuple, List

# 添加项目路径
sys.path.append('/root/autodl-tmp/3D-TransUNet')

from nn_transunet.networks.transunet3d_model import Generic_TransUNet_max_ppbp, InitWeights_He


class Config:
    """预测配置类"""
    def __init__(self):
        # 路径配置
        self.input_path = '/root/autodl-tmp/120HCC/image2'
        self.output_path = '/root/autodl-tmp/output/predictions'
        self.checkpoint_path = '/root/autodl-tmp/output/checkpoints/latest.pth'

        # 模型配置
        self.model_name = "Generic_TransUNet_max_ppbp"
        self.num_classes = 2  # 包括背景类
        self.input_channels = 1
        self.base_num_features = 32

        # 预测配置
        self.patch_size = [64, 160, 160]  # [D, H, W]
        self.step_size = 0.5  # 滑动窗口步长
        self.mixed_precision = True
        self.use_gaussian_weights = True

        # TransUNet特定配置
        self.vit_depth = 12
        self.vit_hidden_size = 768
        self.max_hidden_dim = 192
        self.num_queries = 20
        self.is_max_hungarian = True
        self.is_max_cls = True
        self.deep_supervision = True


def load_nifti_image(filepath):
    """加载NIfTI图像"""
    sitk_image = sitk.ReadImage(filepath)
    image_array = sitk.GetArrayFromImage(sitk_image)
    spacing = sitk_image.GetSpacing()
    origin = sitk_image.GetOrigin()
    direction = sitk_image.GetDirection()
    
    return {
        'data': image_array,
        'spacing': spacing,
        'origin': origin,
        'direction': direction,
        'sitk_image': sitk_image
    }


def save_nifti_image(image_array, reference_image, output_path):
    """保存NIfTI图像"""
    output_image = sitk.GetImageFromArray(image_array.astype(np.uint8))
    output_image.CopyInformation(reference_image)
    sitk.WriteImage(output_image, output_path)


def preprocess_image(image_data, target_spacing=[1.0, 1.0, 1.0]):
    """
    预处理单个图像
    包括重采样、归一化等步骤
    """
    data = image_data['data']
    spacing = image_data['spacing']
    
    # 数据类型转换
    if data.dtype != np.float32:
        data = data.astype(np.float32)
    
    # 重采样到目标spacing
    if spacing != target_spacing:
        # 使用SimpleITK进行重采样
        original_image = image_data['sitk_image']
        
        # 计算新的尺寸
        original_size = original_image.GetSize()
        original_spacing = original_image.GetSpacing()
        
        new_size = [
            int(round(original_size[i] * original_spacing[i] / target_spacing[i]))
            for i in range(3)
        ]
        
        # 重采样
        resampler = sitk.ResampleImageFilter()
        resampler.SetOutputSpacing(target_spacing)
        resampler.SetSize(new_size)
        resampler.SetOutputDirection(original_image.GetDirection())
        resampler.SetOutputOrigin(original_image.GetOrigin())
        resampler.SetTransform(sitk.Transform())
        resampler.SetDefaultPixelValue(0)
        resampler.SetInterpolator(sitk.sitkLinear)
        
        resampled_image = resampler.Execute(original_image)
        data = sitk.GetArrayFromImage(resampled_image)
        
        # 更新图像信息
        image_data['sitk_image'] = resampled_image
        image_data['data'] = data
    
    # 强度归一化
    # 裁剪异常值
    percentile_99_5 = np.percentile(data, 99.5)
    percentile_00_5 = np.percentile(data, 0.5)
    data = np.clip(data, percentile_00_5, percentile_99_5)
    
    # Z-score归一化
    mean = data.mean()
    std = data.std()
    if std > 0:
        data = (data - mean) / std
    
    return data


def create_model(config, checkpoint_path):
    """创建并加载模型"""
    print("创建模型...")
    
    # 模型参数
    model_params = {
        'is_masked_attn': True,
        'max_dec_layers': 3,
        'is_max_bottleneck_transformer': True,
        'vit_depth': config.vit_depth,
        'max_msda': '',
        'is_max_ms': True,
        'max_ms_idxs': [-4, -3, -2],
        'max_hidden_dim': config.max_hidden_dim,
        'mw': 1.0,
        'is_max_ds': config.deep_supervision,
        'is_masking': True,
        'is_max_hungarian': config.is_max_hungarian,
        'num_queries': config.num_queries,
        'is_max_cls': config.is_max_cls,
        'is_mhsa_float32': True,
        'is_vit_pretrain': False,
        'vit_layer_scale': True,
        'decoder_layer_scale': True
    }
    
    # 网络参数
    norm_op_kwargs = {'eps': 1e-5, 'affine': True}
    dropout_op_kwargs = {'p': 0, 'inplace': True}
    net_nonlin = nn.LeakyReLU
    net_nonlin_kwargs = {'negative_slope': 1e-2, 'inplace': True}
    
    # 池化和卷积核大小
    num_pool = 5
    pool_op_kernel_sizes = [[2, 2, 2]] * num_pool
    conv_kernel_sizes = [[3, 3, 3]] * (num_pool + 1)
    
    # 创建模型
    model = Generic_TransUNet_max_ppbp(
        input_channels=config.input_channels,
        base_num_features=config.base_num_features,
        num_classes=config.num_classes,
        num_pool=num_pool,
        num_conv_per_stage=2,
        feat_map_mul_on_downscale=2,
        conv_op=nn.Conv3d,
        norm_op=nn.InstanceNorm3d,
        norm_op_kwargs=norm_op_kwargs,
        dropout_op=nn.Dropout3d,
        dropout_op_kwargs=dropout_op_kwargs,
        nonlin=net_nonlin,
        nonlin_kwargs=net_nonlin_kwargs,
        deep_supervision=config.deep_supervision,
        dropout_in_localization=False,
        final_nonlin=lambda x: x,
        weightInitializer=InitWeights_He(1e-2),
        pool_op_kernel_sizes=pool_op_kernel_sizes,
        conv_kernel_sizes=conv_kernel_sizes,
        upscale_logits=False,
        convolutional_pooling=False,
        convolutional_upsampling=True,
        max_num_features=320,
        patch_size=config.patch_size,
        **model_params
    )
    
    # 加载预训练权重
    print(f"加载模型权重: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 处理状态字典
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    # 移除可能的module.前缀
    new_state_dict = {}
    for k, v in state_dict.items():
        if k.startswith('module.'):
            new_state_dict[k[7:]] = v
        else:
            new_state_dict[k] = v
    
    model.load_state_dict(new_state_dict, strict=False)
    model.eval()
    
    print("模型加载完成")
    return model


def get_gaussian_weights(patch_size, sigma_scale=1./8):
    """生成高斯权重"""
    tmp = np.zeros(patch_size)
    center_coords = [i // 2 for i in patch_size]
    sigmas = [i * sigma_scale for i in patch_size]
    tmp[tuple(center_coords)] = 1
    gaussian_importance_map = gaussian_filter(tmp, sigmas, 0, mode='constant', cval=0)
    gaussian_importance_map = gaussian_importance_map / np.max(gaussian_importance_map) * 1
    gaussian_importance_map = gaussian_importance_map.astype(np.float32)
    
    # 确保权重不为0
    gaussian_importance_map[gaussian_importance_map == 0] = np.min(
        gaussian_importance_map[gaussian_importance_map != 0])
    
    return gaussian_importance_map


def compute_steps_for_sliding_window(patch_size: Tuple[int, ...], 
                                   image_size: Tuple[int, ...], 
                                   step_size: float) -> List[List[int]]:
    """计算滑动窗口的步长"""
    assert [i >= j for i, j in zip(image_size, patch_size)], "图像尺寸必须大于等于补丁尺寸"
    assert 0 < step_size <= 1, '步长必须在0和1之间'
    
    target_step_sizes_in_voxels = [i * step_size for i in patch_size]
    num_steps = [int(np.ceil((i - k) / j)) + 1 for i, j, k in zip(image_size, target_step_sizes_in_voxels, patch_size)]
    
    steps = []
    for dim in range(len(patch_size)):
        max_step_value = image_size[dim] - patch_size[dim]
        if num_steps[dim] > 1:
            actual_step_size = max_step_value / (num_steps[dim] - 1)
        else:
            actual_step_size = 99999999999
        
        steps_here = [int(np.round(actual_step_size * i)) for i in range(num_steps[dim])]
        steps.append(steps_here)
    
    return steps


def predict_3d_sliding_window(model, image, patch_size, step_size, device, config):
    """使用滑动窗口进行3D预测"""
    print(f"图像形状: {image.shape}")
    print(f"补丁大小: {patch_size}")

    # 添加batch和channel维度
    if len(image.shape) == 3:
        image = image[np.newaxis, np.newaxis, ...]  # [1, 1, D, H, W]
    elif len(image.shape) == 4:
        image = image[np.newaxis, ...]  # [1, C, D, H, W]

    # 计算滑动窗口步长
    steps = compute_steps_for_sliding_window(patch_size, image.shape[-3:], step_size)
    num_tiles = len(steps[0]) * len(steps[1]) * len(steps[2])

    print(f"滑动窗口步长: {steps}")
    print(f"总补丁数量: {num_tiles}")

    # 初始化预测结果
    prob_map = torch.zeros((1, config.num_classes,) + image.shape[-3:], dtype=torch.float32, device=device)
    cnt_map = torch.zeros_like(prob_map)

    # 生成高斯权重
    if config.use_gaussian_weights:
        gaussian_weights = torch.from_numpy(get_gaussian_weights(patch_size)).to(device).float()
        gaussian_weights = gaussian_weights[None, None, ...]  # [1, 1, D, H, W]
    else:
        gaussian_weights = torch.ones((1, 1) + tuple(patch_size), device=device)

    # 滑动窗口预测
    with torch.no_grad():
        for x in tqdm(steps[0], desc="预测进度"):
            lb_x = x
            ub_x = x + patch_size[0]
            for y in steps[1]:
                lb_y = y
                ub_y = y + patch_size[1]
                for z in steps[2]:
                    lb_z = z
                    ub_z = z + patch_size[2]

                    # 提取补丁
                    patch = image[:, :, lb_x:ub_x, lb_y:ub_y, lb_z:ub_z]
                    patch_tensor = torch.from_numpy(patch).to(device).float()

                    # 预测
                    if config.mixed_precision:
                        with autocast():
                            output = model(patch_tensor)
                    else:
                        output = model(patch_tensor)

                    # 处理3D-TransUNet的输出格式（参考训练代码）
                    if isinstance(output, (list, tuple)) and len(output) > 0 and isinstance(output[0], dict):
                        # 3D-TransUNet输出：[dict, tensor1, tensor2, ...]
                        main_dict = output[0]
                        main_output = main_dict['pred_masks']  # [B, num_queries, D, H, W]

                        # 对于预测，我们将pred_masks转换为类别预测
                        # 取前num_classes个查询作为类别预测
                        if main_output.shape[1] >= config.num_classes:
                            # 取前num_classes个查询作为类别预测
                            output = main_output[:, :config.num_classes, :, :, :]  # [B, num_classes, D, H, W]
                        else:
                            # 如果查询数少于类别数，进行填充
                            padding = torch.zeros(
                                main_output.shape[0], config.num_classes - main_output.shape[1],
                                *main_output.shape[2:], device=main_output.device
                            )
                            output = torch.cat([main_output, padding], dim=1)

                    elif isinstance(output, dict):
                        # 纯字典输出
                        main_output = output['pred_masks']
                        if main_output.shape[1] >= config.num_classes:
                            output = main_output[:, :config.num_classes, :, :, :]
                        else:
                            padding = torch.zeros(
                                main_output.shape[0], config.num_classes - main_output.shape[1],
                                *main_output.shape[2:], device=main_output.device
                            )
                            output = torch.cat([main_output, padding], dim=1)

                    elif isinstance(output, (list, tuple)):
                        # 纯张量列表输出
                        output = output[0]
                    # else: 单个张量输出，直接使用

                    # 应用softmax
                    output = torch.softmax(output, dim=1)

                    # 应用高斯权重
                    weighted_output = output * gaussian_weights

                    # 累加到结果中
                    prob_map[:, :, lb_x:ub_x, lb_y:ub_y, lb_z:ub_z] += weighted_output
                    cnt_map[:, :, lb_x:ub_x, lb_y:ub_y, lb_z:ub_z] += gaussian_weights

    # 归一化
    prob_map = prob_map / cnt_map

    return prob_map.cpu().numpy()


def postprocess_prediction(prob_map, original_image_info, target_spacing=[1.0, 1.0, 1.0]):
    """后处理预测结果"""
    # 获取最大概率类别
    segmentation = np.argmax(prob_map.squeeze(0), axis=0).astype(np.uint8)

    # 如果需要，重采样回原始spacing
    original_spacing = original_image_info['spacing']
    if original_spacing != target_spacing:
        # 创建分割结果的sitk图像
        seg_image = sitk.GetImageFromArray(segmentation)
        seg_image.SetSpacing(target_spacing)
        seg_image.SetOrigin(original_image_info['sitk_image'].GetOrigin())
        seg_image.SetDirection(original_image_info['sitk_image'].GetDirection())

        # 重采样回原始spacing
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(original_image_info['sitk_image'])
        resampler.SetInterpolator(sitk.sitkNearestNeighbor)
        resampler.SetDefaultPixelValue(0)

        resampled_seg = resampler.Execute(seg_image)
        segmentation = sitk.GetArrayFromImage(resampled_seg)

    return segmentation


def predict_single_image(model, image_path, output_path, config, device):
    """预测单个图像"""
    print(f"正在预测: {image_path}")

    # 加载图像
    image_data = load_nifti_image(image_path)
    original_image_info = image_data.copy()

    # 预处理
    processed_image = preprocess_image(image_data)

    # 如果图像小于patch_size，进行padding
    pad_needed = [max(0, config.patch_size[i] - processed_image.shape[i]) for i in range(3)]
    if any(pad_needed):
        pad_before = [p // 2 for p in pad_needed]
        pad_after = [p - pb for p, pb in zip(pad_needed, pad_before)]
        processed_image = np.pad(
            processed_image,
            [(pb, pa) for pb, pa in zip(pad_before, pad_after)],
            mode='constant',
            constant_values=0
        )
        print(f"图像已padding到: {processed_image.shape}")

    # 预测
    prob_map = predict_3d_sliding_window(
        model, processed_image, config.patch_size, config.step_size, device, config
    )

    # 如果进行了padding，需要裁剪回原始大小
    if any(pad_needed):
        original_shape = image_data['data'].shape
        prob_map = prob_map[
            :, :,
            pad_before[0]:pad_before[0] + original_shape[0],
            pad_before[1]:pad_before[1] + original_shape[1],
            pad_before[2]:pad_before[2] + original_shape[2]
        ]

    # 后处理
    segmentation = postprocess_prediction(prob_map, original_image_info)

    # 保存结果
    save_nifti_image(segmentation, original_image_info['sitk_image'], output_path)

    print(f"预测结果已保存到: {output_path}")

    return segmentation


def predict_batch_images(model, input_dir, output_dir, config, device):
    """批量预测图像"""
    print(f"批量预测目录: {input_dir}")
    print(f"输出目录: {output_dir}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有nii.gz文件
    image_files = []
    for file in os.listdir(input_dir):
        if file.endswith('.nii.gz'):
            image_files.append(file)

    image_files.sort()
    print(f"找到 {len(image_files)} 个图像文件")

    # 逐个预测
    for image_file in tqdm(image_files, desc="批量预测"):
        input_path = os.path.join(input_dir, image_file)
        output_file = image_file.replace('.nii.gz', '_pred.nii.gz')
        output_path = os.path.join(output_dir, output_file)

        try:
            predict_single_image(model, input_path, output_path, config, device)
        except Exception as e:
            print(f"预测 {image_file} 时发生错误: {str(e)}")
            continue

    print("批量预测完成!")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='3D-TransUNet 预测脚本')
    parser.add_argument('--input', type=str, default='/root/autodl-tmp/120HCC/image2',
                        help='输入图像路径或目录')
    parser.add_argument('--output', type=str, default='/root/autodl-tmp/output/predictions',
                        help='输出路径或目录')
    parser.add_argument('--checkpoint', type=str, default='/root/autodl-tmp/output/checkpoints/latest.pth',
                        help='模型检查点路径')
    parser.add_argument('--num_classes', type=int, default=2,
                        help='类别数量（包括背景）')
    parser.add_argument('--patch_size', nargs=3, type=int, default=[64, 160, 160],
                        help='补丁大小 [D, H, W]')
    parser.add_argument('--step_size', type=float, default=0.5,
                        help='滑动窗口步长')
    parser.add_argument('--batch_mode', action='store_true',
                        help='批量预测模式')
    parser.add_argument('--no_gaussian', action='store_true',
                        help='不使用高斯权重')

    args = parser.parse_args()

    # 创建配置
    config = Config()

    # 从命令行参数更新配置
    config.input_path = args.input
    config.output_path = args.output
    config.checkpoint_path = args.checkpoint
    config.num_classes = args.num_classes
    config.patch_size = args.patch_size
    config.step_size = args.step_size
    config.use_gaussian_weights = not args.no_gaussian

    print("3D-TransUNet 预测脚本")
    print("=" * 50)
    print(f"输入: {config.input_path}")
    print(f"输出: {config.output_path}")
    print(f"模型: {config.checkpoint_path}")
    print(f"类别数量: {config.num_classes}")
    print(f"补丁大小: {config.patch_size}")
    print(f"步长: {config.step_size}")
    print(f"高斯权重: {config.use_gaussian_weights}")
    print("=" * 50)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 检查输入
    if not os.path.exists(config.input_path):
        print(f"错误: 输入路径不存在: {config.input_path}")
        return

    if not os.path.exists(config.checkpoint_path):
        print(f"错误: 模型检查点不存在: {config.checkpoint_path}")
        return

    # 创建模型
    try:
        model = create_model(config, config.checkpoint_path)
        model = model.to(device)
        print("模型加载成功")
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return

    # 开始预测
    try:
        if args.batch_mode or os.path.isdir(config.input_path):
            predict_batch_images(model, config.input_path, config.output_path, config, device)
        else:
            predict_single_image(model, config.input_path, config.output_path, config, device)

        print("预测完成!")

    except Exception as e:
        print(f"预测过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
