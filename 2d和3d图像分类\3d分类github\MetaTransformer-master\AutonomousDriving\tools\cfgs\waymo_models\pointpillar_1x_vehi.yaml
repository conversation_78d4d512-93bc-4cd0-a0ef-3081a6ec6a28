CLASS_NAMES: ['Vehicle']

DATA_CONFIG:
    _BASE_CONFIG_: cfgs/dataset_configs/waymo/DA/da_waymo_dataset.yaml

    POINT_CLOUD_RANGE: [-74.88, -74.88, -2, 74.88, 74.88, 4.0]
    DATA_PROCESSOR:
        -   NAME: mask_points_and_boxes_outside_range
            REMOVE_OUTSIDE_BOXES: True

        -   NAME: shuffle_points
            SHUFFLE_ENABLED: {
                'train': True,
                'test': True
            }

        -   NAME: transform_points_to_voxels
            VOXEL_SIZE: [ 0.32, 0.32, 6.0 ]
            MAX_POINTS_PER_VOXEL: 20
            MAX_NUMBER_OF_VOXELS: {
                'train': 150000,
                'test': 150000
            }


MODEL:
    NAME: PointPillar

    VFE:
        NAME: PillarVFE
        WITH_DISTANCE: False
        USE_ABSLOTE_XYZ: True
        USE_NORM: True
        NUM_FILTERS: [ 64, 64 ]

    MAP_TO_BEV:
        NAME: PointPillarScatter
        NUM_BEV_FEATURES: 64

    BACKBONE_2D:
        NAME: BaseBEVBackbone
        LAYER_NUMS: [ 3, 5, 5 ]
        LAYER_STRIDES: [ 1, 2, 2 ]
        NUM_FILTERS: [ 64, 128, 256 ]
        UPSAMPLE_STRIDES: [ 1, 2, 4 ]
        NUM_UPSAMPLE_FILTERS: [ 128, 128, 128 ]

    DENSE_HEAD:
        NAME: AnchorHeadSingle
        CLASS_AGNOSTIC: False

        USE_DIRECTION_CLASSIFIER: True
        DIR_OFFSET: 0.78539
        DIR_LIMIT_OFFSET: 0.0
        NUM_DIR_BINS: 2

        ANCHOR_GENERATOR_CONFIG: [
            {
                'class_name': 'Vehicle',
                'anchor_sizes': [ [ 4.7, 2.1, 1.7 ] ],
                'anchor_rotations': [ 0, 1.57 ],
                'anchor_bottom_heights': [ 0 ],
                'align_center': False,
                'feature_map_stride': 1,
                'matched_threshold': 0.55,
                'unmatched_threshold': 0.4
            }
        ]

        TARGET_ASSIGNER_CONFIG:
            NAME: AxisAlignedTargetAssigner
            POS_FRACTION: -1.0
            SAMPLE_SIZE: 512
            NORM_BY_NUM_EXAMPLES: False
            MATCH_HEIGHT: False
            BOX_CODER: ResidualCoder

        LOSS_CONFIG:
            LOSS_WEIGHTS: {
                'cls_weight': 1.0,
                'loc_weight': 2.0,
                'dir_weight': 0.2,
                'code_weights': [ 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0 ]
            }

    POST_PROCESSING:
        RECALL_THRESH_LIST: [ 0.3, 0.5, 0.7 ]
        SCORE_THRESH: 0.1
        OUTPUT_RAW_SCORE: False

        EVAL_METRIC: waymo

        NMS_CONFIG:
            MULTI_CLASSES_NMS: False
            NMS_TYPE: nms_gpu
            NMS_THRESH: 0.7
            NMS_PRE_MAXSIZE: 4096
            NMS_POST_MAXSIZE: 500


OPTIMIZATION:
    BATCH_SIZE_PER_GPU: 2
    NUM_EPOCHS: 30

    OPTIMIZER: adam_onecycle
    LR: 0.003
    WEIGHT_DECAY: 0.01
    MOMENTUM: 0.9

    MOMS: [0.95, 0.85]
    PCT_START: 0.4
    DIV_FACTOR: 10
    DECAY_STEP_LIST: [35, 45]
    LR_DECAY: 0.1
    LR_CLIP: 0.0000001

    LR_WARMUP: False
    WARMUP_EPOCH: 1

    GRAD_NORM_CLIP: 10