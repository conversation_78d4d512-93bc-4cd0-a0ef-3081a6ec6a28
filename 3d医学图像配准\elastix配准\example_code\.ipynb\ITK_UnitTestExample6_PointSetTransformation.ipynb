{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Elastix\n", "\n", "This notebooks show very basic image registration examples with on-the-fly generated binary images."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import itk\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image generators"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def image_generator(x1, x2, y1, y2):\n", "    image = np.zeros([100, 100], np.float32)\n", "    image[y1:y2, x1:x2] = 1\n", "    image = itk.image_view_from_array(image)\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point set transformation test\n", "See example 10 for more explanation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def point_set_from_txt(file_path):\n", "    image = np.zeros([100, 100], np.float32)\n", "    with open(file_path, \"rt\") as myfile: \n", "        for myline in myfile:            \n", "            string = myline.partition('OutputIndexMoving =')[2]\n", "            string=string.strip()\n", "            string = string.strip('[]')\n", "            string=string.strip()\n", "            y,x = string.split()\n", "            image[int(x),int(y)] = 1\n", "    return image\n", "\n", "def point_set_from_input(x,y):\n", "    image = np.zeros([100, 100], np.float32)\n", "    for i in range(len(x)):\n", "        image[x[i],y[i]]=1\n", "    return image"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Create rigid transformed test images with artefact\n", "fixed_image = image_generator(25, 76, 25, 76)\n", "moving_image = image_generator(1, 52, 10, 61)\n", "\n", "# Create fixed point set\n", "fixed_point_set = open(\"data/fixed_point_set_test.txt\", \"w+\")\n", "fixed_point_set.write(\"point\\n5\\n\")\n", "fixed_point_set.write(\"25 25\\n\")\n", "fixed_point_set.write(\"25 75\\n\")\n", "fixed_point_set.write(\"75 75\\n\")\n", "fixed_point_set.write(\"75 25\\n\")\n", "fixed_point_set.write(\"50 50\")\n", "fixed_point_set.close()\n", "\n", "\n", "# Import Default Parameter Map\n", "parameter_object = itk.ParameterObject.New()\n", "default_rigid_parameter_map = parameter_object.GetDefaultParameterMap('rigid')\n", "parameter_object.AddParameterMap(default_rigid_parameter_map)\n", "\n", "# # Call registration function\n", "result_image, result_transform_parameters = itk.elastix_registration_method(\n", "    fixed_image, moving_image,\n", "    parameter_object=parameter_object)\n", "\n", "# Call transformix\n", "empty_image = itk.transformix_filter(\n", "    moving_image,\n", "    fixed_point_set_file_name='data/fixed_point_set_test.txt',\n", "    transform_parameter_object=result_transform_parameters,\n", "    output_directory='exampleoutput_unittests')\n", "\n", "result_point_set = point_set_from_txt('exampleoutput_unittests/outputpoints.txt')\n", "fixed_point_set = point_set_from_input([25,25,75,75,50],[25,75,75,25,50])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Point set transformation test Visualization"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 3000x3000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "# Plot images\n", "fig, axs = plt.subplots(2,2, sharey=True, figsize=[30,30])\n", "plt.figsize=[100,100]\n", "axs[0,0].imshow(fixed_image)\n", "axs[0,0].set_title('Fixed', fontsize=30)\n", "axs[0,1].imshow(fixed_point_set+fixed_image)\n", "axs[0,1].set_title('Fixed Point Set', fontsize=30)\n", "axs[1,0].imshow(moving_image)\n", "axs[1,0].set_title('Moving', fontsize=30)\n", "axs[1,1].imshow(result_point_set+moving_image)\n", "axs[1,1].set_title('Result Point Set', fontsize=30)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}