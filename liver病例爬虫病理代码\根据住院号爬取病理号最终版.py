# -*- coding: utf-8 -*-
"""
Created on Tue Apr 12 21:08:28 2022

@author: DELL
"""
###需要修改的地方：125行代码for k in range(1,2819): 2819为文件的行数需要修改
### 读取和保存的路径需要修改


import requests
from lxml import etree
import csv
import re
from bs4 import BeautifulSoup
import re
import urllib.request, urllib.error
import xlwt
import xlrd
import _sqlite3

headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36"
    }   
#response = requests.get(baseurl, headers=headers)
#response.status_code == requests.codes.ok
#sel = etree.HTML(response.text)
#t_list = sel.xpath('//html/body/table[2]/tr/td[2]/font/a/@href')   
#t_list = sel.xpath('//html/body/table[2]/tr/td[2]/font/a/text()')  #tbody需要删除
#t_list = sel.xpath('//*[@id="xxx4"]/text()')    
#
#t_list
#print(t_list)  
       
def main():
    baseurl = 'http://10.1.1.176/pathwebrpt/index_main_gl.asp?keywords=&keywords_zyh='  # 此处需要添加病理的链接
    # 此处需要获取Excel的住院号内容
    data = xlrd.open_workbook(r'E:\liver\HCC2021.1-11-30.xls')        #此处为读取的文件名！！！！！
    table = data.sheets()[0]
    # 创建一个空列表，存储Excel的数据
    tables = import_excel(table)
    # 验证Excel文件存储到列表中的数据
    idzong = []
    for i in tables:
        id = i.get("ID")
        idzong.append(id)

    datalist = getdata(baseurl,idzong)
    savepath = "E:/liver/HCCbinglihao2021.xls"               #此处为保存的文件名！！！
    savedata(datalist,savepath)


def import_excel(table):
    tables = []

    for rown in range(table.nrows):
        array = {'name': '', 'ID': ''}
        array['name'] = table.cell_value(rown, 1)
        array['ID'] = table.cell_value(rown, 4)
        tables.append(array)
    return tables


def getdata(baseurl, idzong):
    datalist = []
    
    for id in idzong:
        print(type(id))
        url = baseurl + str(id) 
        urls = url + "&keywords_brbh=&keywords_sqxh="
        print(urls)
        # html = askurl(urls)
        #print(html)
        #datalist.append(id) 
        #程序修改处
        response = requests.get(urls, headers=headers)
        response.status_code == requests.codes.ok
        sel = etree.HTML(response.text)
        t_list1 = sel.xpath('//*[@id="xxx1"]/text()')   #*[@class="tt"]
        t_list2 = sel.xpath('//*[@id="xxx2"]/text()')
        t_list3 = sel.xpath('//*[@id="xxx3"]/text()')
        t_list4 = sel.xpath('//*[@id="xxx4"]/text()')
        t_list5 = sel.xpath('//*[@id="xxx5"]/text()')
        t_list6 = sel.xpath('//*[@id="xxx6"]/text()')
        # soup = BeautifulSoup(html, "html.parser")         
        data = []
        # item = []
        # zhuyuanhao = soup.i
        # zhuyuanhao = str(zhuyuanhao)
        # data.append(zhuyuanhao)
        t_list1 = str(t_list1)
        t_list2 = str(t_list2)
        t_list3 = str(t_list3)
        t_list4 = str(t_list4)
        t_list5 = str(t_list5)
        t_list6 = str(t_list6)
        print(t_list1)
        # print(t_list[1].get_text())
        data.append(t_list1)
        data.append(t_list2)
        data.append(t_list3)
        data.append(t_list4)
        data.append(t_list5)
        data.append(t_list6)
        #soup = BeautifulSoup(html, "html.parser")
        #data = []
        #zhuyuanhao = soup.i
        #zhuyuanhao = str(zhuyuanhao)
        #data.append(zhuyuanhao)
        #t_list = soup.find_all("textarea")
        #t_list = str(t_list)
        #data.append(t_list)
        datalist.append(data)  # 总data放入datalist内   #此处需要测试datalist处于的位置
    #print(datalist)
    return datalist


def savedata(datalist, savepath):
    print("save...")
    book = xlwt.Workbook(encoding="utf-8", style_compression=0)  # 创建workbook对象
    sheet = book.add_sheet('sheet1', cell_overwrite_ok=True)  # 创建工作表

    for k in range(1,2819):          #'''此处为文件的行数！！！！'''(1,4)左闭右开
        # print("第%d条" %k)
        data = datalist[k]
        # sheet.write(k,data[k])
        for j in range(0,6):            #j表示列数
            print(j)
            sheet.write(k, j, data[j])
            # print(data[j])
    book.save(savepath)

if __name__ == "__main__":
    main()
    print("爬去完毕")