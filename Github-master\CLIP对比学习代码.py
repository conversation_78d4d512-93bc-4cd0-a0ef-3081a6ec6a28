#%%CLIP对比学习 https://github.com/openai/CLIP  
# $ conda install --yes -c pytorch pytorch=1.7.1 torchvision cudatoolkit=11.0
# $ pip install ftfy regex tqdm
#Jupyter Notebook 中反复遇到内核崩溃的问题
# pip install --upgrade jupyterlab notebook numpy scipy pandas
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import torch
import clip
from PIL import Image

device = "cuda" if torch.cuda.is_available() else "cpu"
model, preprocess = clip.load("ViT-B/32", device=device)

image_path = r"E:\1.githubcode\dog.jpg"
image = preprocess(Image.open(image_path)).unsqueeze(0).to(device)
text = clip.tokenize(["a diagram", "a dog", "a cat"]).to(device)

with torch.no_grad():
    image_features = model.encode_image(image)
    text_features = model.encode_text(text)
    
    logits_per_image, logits_per_text = model(image, text)
    probs = logits_per_image.softmax(dim=-1).cpu().numpy()

print("Label probs:", probs)  # prints: [[0.9927937  0.00421068 0.00299572]]
# %% nnUNET环境下安装 pip install transformers
import os
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
from transformers import AutoModel, AutoTokenizer
model = AutoModel.from_pretrained("programExec/deepseekv2-16b-tracewise", trust_remote_code=True)

from transformers import AutoModel, AutoTokenizer
import torch
import numpy as np

def extract_features_deepseek(texts, batch_size=8):
    """
    使用DeepSeek-16B模型提取文本特征
    
    参数:
    texts: 文本列表或单个文本
    batch_size: 批处理大小
    
    返回:
    numpy数组格式的特征向量
    """
    # 加载模型和分词器
    model_name = "programExec/deepseekv2-16b-tracewise"
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    model = AutoModel.from_pretrained(model_name, trust_remote_code=True)
    
    # 将模型设置为评估模式
    model.eval()
    
    # 如果输入是单个文本，转换为列表
    if isinstance(texts, str):
        texts = [texts]
    
    # 存储特征
    all_features = []
    
    # 使用GPU如果可用
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # 批处理提取特征
    with torch.no_grad():
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            # 编码文本
            inputs = tokenizer(batch_texts, 
                             padding=True, 
                             truncation=True, 
                             max_length=512, 
                             return_tensors="pt")
            
            # 将输入移到GPU（如果可用）
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # 获取模型输出
            outputs = model(**inputs)
            
            # 获取最后一层的[CLS]标记输出作为特征
            features = outputs.last_hidden_state[:, 0, :]
            
            # 将特征移到CPU并转换为numpy数组
            features = features.cpu().numpy()
            all_features.append(features)
    
    # 合并所有批次的特征
    all_features = np.vstack(all_features)
    
    return all_features

# 使用示例
if __name__ == "__main__":
    # 示例文本
    texts = [
        "这是第一个示例文本",
        "这是第二个示例文本",
        "这是第三个示例文本"
    ]
    
    # 提取特征
    features = extract_features_deepseek(texts)
    
    # 打印特征维度
    print(f"特征维度: {features.shape}")
    
    # 示例：计算文本相似度
    def compute_similarity(text1, text2):
        from scipy.spatial.distance import cosine
        
        # 提取特征
        emb1 = extract_features_deepseek(text1)
        emb2 = extract_features_deepseek(text2)
        
        # 计算余弦相似度
        similarity = 1 - cosine(emb1[0], emb2[0])
        return similarity
    
    # 计算两个文本的相似度
    text1 = "深度学习模型在自然语言处理中的应用"
    text2 = "机器学习技术在文本处理中的使用"
    similarity = compute_similarity(text1, text2)
    print(f"文本相似度: {similarity:.4f}")
    
    # 内存清理
    import torch
    torch.cuda.empty_cache()
# %%
# Use a pipeline as a high-level helper
# Use a pipeline as a high-level helper
from transformers import pipeline

pipe = pipeline("feature-extraction", model="hotal/honeypot-llama3-8B")
# %%
