{"data": {"ap_dir": "/root/autodl-tmp/120HCC/image/ap", "hbp_dir": "/root/autodl-tmp/120HCC/image/hbp", "image_size": [256, 256], "train_ratio": 0.8, "batch_size": 4, "num_workers": 0}, "model": {"in_channels": 1, "out_channels": 1, "time_emb_dim": 128, "base_channels": 64, "channel_multipliers": [1, 2, 4, 8]}, "diffusion": {"timesteps": 1000, "beta_start": 0.0001, "beta_end": 0.02}, "training": {"num_epochs": 800, "learning_rate": 0.0002, "weight_decay": 0.0001, "grad_clip": 1.0}, "loss": {"use_perceptual_loss": true, "perceptual_weight": 0.1}, "validation": {"sample_interval": 20}, "paths": {"save_dir": "outputs/medical_diffusion_experiment"}, "system": {"device": "cuda"}, "checkpoint_path": "/root/autodl-tmp/outputs/medical_diffusion_experiment/best_checkpoint.pth"}