*.7z filter=lfs diff=lfs merge=lfs -text
*.arrow filter=lfs diff=lfs merge=lfs -text
*.bin filter=lfs diff=lfs merge=lfs -text
*.bz2 filter=lfs diff=lfs merge=lfs -text
*.ckpt filter=lfs diff=lfs merge=lfs -text
*.ftz filter=lfs diff=lfs merge=lfs -text
*.gz filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.joblib filter=lfs diff=lfs merge=lfs -text
*.lfs.* filter=lfs diff=lfs merge=lfs -text
*.mlmodel filter=lfs diff=lfs merge=lfs -text
*.model filter=lfs diff=lfs merge=lfs -text
*.msgpack filter=lfs diff=lfs merge=lfs -text
*.npy filter=lfs diff=lfs merge=lfs -text
*.npz filter=lfs diff=lfs merge=lfs -text
*.onnx filter=lfs diff=lfs merge=lfs -text
*.ot filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
*.pb filter=lfs diff=lfs merge=lfs -text
*.pickle filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.pt filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.safetensors filter=lfs diff=lfs merge=lfs -text
saved_model/**/* filter=lfs diff=lfs merge=lfs -text
*.tar.* filter=lfs diff=lfs merge=lfs -text
*.tar filter=lfs diff=lfs merge=lfs -text
*.tflite filter=lfs diff=lfs merge=lfs -text
*.tgz filter=lfs diff=lfs merge=lfs -text
*.wasm filter=lfs diff=lfs merge=lfs -text
*.xz filter=lfs diff=lfs merge=lfs -text
*.zip filter=lfs diff=lfs merge=lfs -text
*.zst filter=lfs diff=lfs merge=lfs -text
*tfevents* filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/autoencoder_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/autoencoder_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/autoencoder_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/autoencoder_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/med3d_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/med3d_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/med3d_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/med3d_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/modelsgen_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/modelsgen_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/modelsgen_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/nnclr_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/nnclr_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/nnclr_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/nnclr_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/simclr_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/simclr_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/simclr_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/simclr_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/swav_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/swav_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/swav_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/swav_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/modelsgen_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/modelsgen_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/modelsgen_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task3/LUNG1/foundation_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task3/LUNG1/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task3/RADIO/foundation_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task3/RADIO/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task2/test_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task2/train_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task2/val_features.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task3/lung1.csv filter=lfs diff=lfs merge=lfs -text
output/features/foundation_features/task3/radio.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task2/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task2/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task2/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task3/lung1.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/foundation_features/task3/radio.csv filter=lfs diff=lfs merge=lfs -text
outputs/stability/foundation_features/lung1_feats.csv filter=lfs diff=lfs merge=lfs -text
outputs/stability/supervised/lung1_feats.csv filter=lfs diff=lfs merge=lfs -text
models/pretrained/fmcib/model_weights.torch filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/med3d/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/med3d/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/med3d/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task2/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task2/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task2/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task3/lung1_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task3/radio_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/models_genesis/task3/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/baselines/supervised/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task2/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task2/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task2/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task3/lung1.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/fmcib/task3/radio.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/autoencoder/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/autoencoder/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/autoencoder/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/nnclr/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/nnclr/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/nnclr/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/simclr/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/simclr/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/simclr/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/swav/task1/test_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/swav/task1/train_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/features/pretraining_comparison/swav/task1/val_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/med3d_features/med3d_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/med3d_features/med3d_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/med3d_features/med3d_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/med3d_features/med3d_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/models_genesis_features/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/models_genesis_features/modelsgen_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/models_genesis_features/modelsgen_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/baselines/models_genesis_features/modelsgen_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/autoencoder/autoencoder_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/autoencoder/autoencoder_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/autoencoder/autoencoder_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/autoencoder/autoencoder_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/nnclr/nnclr_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/nnclr/nnclr_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/nnclr/nnclr_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/nnclr/nnclr_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/simclr/simclr_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/simclr/simclr_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/simclr/simclr_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/simclr/simclr_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/swav/swav_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/swav/swav_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/swav/swav_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task1/pretraining_comparison/swav/swav_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/baselines/models_genesis_features/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/baselines/models_genesis_features/modelsgen_features_10_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/baselines/models_genesis_features/modelsgen_features_20_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task2/baselines/models_genesis_features/modelsgen_features_50_percent.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task3/LUNG1/baselines/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
outputs/predictions/task3/RADIO/baselines/modelsgen_features.csv filter=lfs diff=lfs merge=lfs -text
models/baselines/task1/fmcib_finetuned.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task1/supervised.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task2/fmcib_finetuned.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task2/supervised.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task2/supervised_finetuned.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task3/fmcib_finetuned.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task3/supervised.torch filter=lfs diff=lfs merge=lfs -text
models/baselines/task3/supervised_finetuned.torch filter=lfs diff=lfs merge=lfs -text
