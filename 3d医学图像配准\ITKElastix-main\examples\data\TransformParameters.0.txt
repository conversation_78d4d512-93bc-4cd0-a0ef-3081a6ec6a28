(Transform "BSplineTransform")
(NumberOfParameters 722)
(TransformParameters -2.950718 -3.545671 -3.421660 -3.067611 -2.533958 -1.963698 -1.490026 -1.017845 -0.467196 -0.127669 -0.284559 -0.750926 -1.350883 -1.934219 -2.302518 -2.562390 -2.745011 -2.809988 -2.338047 -3.535318 -4.477050 -4.453247 -3.930407 -2.904125 -1.926020 -1.380477 -0.973446 -0.564215 -0.301026 -0.030497 -0.113791 -1.073645 -2.359913 -2.927137 -3.358793 -3.596887 -3.693836 -3.050551 -3.471090 -4.520869 -4.569575 -4.201315 -2.879661 -1.846299 -0.955210 -1.937543 -1.647879 -2.629003 -1.501293 0.021938 -1.086421 -2.024768 -2.607090 -3.371340 -3.668035 -3.809821 -3.191664 -3.175632 -4.489109 -4.969550 -5.082535 -3.918386 -2.253144 3.452244 0.113750 -3.188402 -0.694271 -1.719018 -1.086656 -0.237362 -1.373361 -2.334240 -2.902834 -3.594471 -3.882843 -3.289022 -2.602565 -4.507842 -6.088234 -7.358732 -6.953177 -5.880913 -6.019906 0.440816 -1.867096 -7.551949 -4.316244 2.891977 2.610392 -0.320198 -1.070985 -1.924836 -3.013360 -3.858333 -3.345997 -1.805178 -4.024396 -6.465502 -8.726976 -8.933109 -5.583799 0.091540 -0.024567 0.925589 -4.991747 2.844553 3.091712 -1.270545 -1.000693 0.597086 -0.881997 -2.234892 -3.537831 -3.271744 -0.816418 -2.711869 -4.996824 -6.909563 -5.632169 -7.775219 2.503547 5.294935 0.049031 2.396336 6.844827 -7.384319 -7.156146 0.524001 2.764341 0.451604 -1.564236 -2.916343 -2.970296 0.239428 -0.998153 -2.568147 -4.315394 -3.686492 -3.890022 1.571245 1.438768 -3.713753 1.218249 3.508548 -2.209704 -1.346028 3.220241 -0.713297 -0.704016 -0.883561 -2.329544 -2.662398 1.246375 0.699841 -0.029704 -3.047451 -1.789318 2.377914 -0.982185 -0.586550 -2.323224 -7.118955 -3.474026 -0.174249 2.966360 4.003034 1.518441 0.759829 -0.137825 -1.896304 -2.512500 2.093483 1.876318 1.486476 -0.152796 3.142898 -4.959401 -3.536323 -3.378004 -4.597378 -5.319944 -1.565663 2.014706 2.664927 -0.028569 7.065064 3.097905 -0.143798 -1.978037 -2.626107 2.682578 2.081456 0.991441 -4.350954 -2.124016 1.115104 -1.082830 -2.002623 3.525789 5.594926 -1.970243 1.225076 -3.363426 3.499379 -1.028139 -6.119944 -2.015085 -2.977107 -3.073518 3.074134 2.097333 0.305712 -3.134747 1.750625 3.681716 -2.426122 4.115977 4.697396 -3.839847 -2.326709 3.821881 -2.307961 0.232608 -1.022546 -1.231606 -4.077139 -4.434363 -3.709457 3.297179 2.600809 1.305259 -0.759394 -1.735710 1.014818 -4.011047 -0.058187 -1.516278 -3.548262 -1.404181 0.123054 -4.853162 -2.863918 -5.225942 -2.401992 -5.902290 -5.736890 -4.360609 3.449239 3.291886 2.825872 2.393526 1.989182 0.984374 -1.159398 -1.628413 0.775143 2.376463 -6.164786 -3.175821 -0.479033 1.741571 -4.056351 -7.154881 -7.060081 -6.604109 -4.884270 3.621269 3.698937 3.534852 2.994511 0.975915 -1.725062 0.378414 2.784489 0.653002 1.992562 -3.935224 -2.179044 -3.446423 3.621631 -1.010501 -5.381007 -6.576228 -6.673034 -5.110716 3.802399 3.959006 3.728888 3.236894 1.931098 0.332054 -1.290413 -1.276090 -2.047794 0.253674 -2.971189 -5.505140 -7.420843 -3.041029 -2.260612 -4.431181 -5.667340 -6.179873 -5.059715 3.934126 4.283061 3.921243 3.487120 2.402603 0.957312 -0.172232 -0.893209 -1.104928 -1.301772 -3.863633 -6.078312 -5.239831 -3.282957 -3.403106 -4.840721 -5.681999 -5.740503 -4.769740 3.932487 4.364415 4.050922 3.605550 2.831012 1.684500 0.460381 -0.362148 -0.981087 -1.917537 -3.632130 -5.326188 -5.496948 -4.957878 -4.897133 -5.234459 -5.593944 -5.461945 -4.434839 3.091770 3.352536 3.173515 2.818030 2.280722 1.527698 0.533188 -0.533164 -1.520758 -2.419589 -3.202526 -3.903812 -4.498533 -4.835783 -4.823902 -4.684597 -4.590400 -4.448422 -3.530176 0.017502 0.472887 0.768498 1.159662 1.614199 2.118520 2.669749 3.347630 4.228766 4.521326 3.453386 2.194773 1.902278 2.054808 2.101060 2.102616 2.119035 2.105837 1.637032 -1.138234 -1.088841 -0.816175 -0.352077 0.464265 1.227704 1.680768 2.499836 4.434919 4.835050 3.282030 0.775988 0.525019 1.099838 1.563137 1.666159 1.745853 1.918615 1.615170 -1.763085 -2.115868 -1.972794 -1.453266 -0.463791 0.417623 0.524836 -2.011902 3.733903 0.532572 2.618851 -1.958356 0.595223 0.271494 1.216313 1.674942 1.613862 1.740037 1.432370 -2.489378 -3.303559 -3.461488 -3.141744 -1.872489 -0.686564 4.999934 -5.567260 -0.971059 2.561605 0.277566 -0.535613 0.793457 -1.035431 0.369973 0.831476 1.261663 1.493148 1.122250 -3.124599 -4.418116 -5.048179 -5.218143 -3.956337 -4.332858 -2.922302 3.404095 -1.996040 -6.120593 -4.941179 4.700600 2.913811 -1.313489 -0.976827 -0.483749 0.221511 0.699025 0.647342 -3.604053 -5.212591 -6.242472 -6.796554 -5.996097 -4.961526 -0.481440 1.378205 1.993529 -3.416481 4.366935 5.415111 -0.287257 0.168235 -2.046275 -1.424597 -0.714098 -0.145326 0.036176 -3.793000 -5.241565 -6.060360 -6.381545 -4.687693 -4.715212 4.918187 4.025694 3.563063 4.926989 4.237610 -4.971030 -6.421081 1.839691 -1.831956 -1.962772 -1.479001 -0.904217 -0.679416 -3.789744 -4.713986 -5.022369 -4.644311 -2.733801 -2.346955 3.708330 3.098170 -2.527944 3.987828 2.294347 -1.339718 -1.399894 3.965990 1.439806 -1.336342 -1.641447 -1.440873 -1.472332 -3.687338 -3.780423 -3.247455 -2.665053 -0.154584 1.353723 0.139620 1.667888 -3.495391 -7.126396 -4.217099 0.792908 4.897962 4.338624 2.058216 -0.718966 -1.959548 -2.241613 -2.336910 -3.542152 -2.986986 -1.826317 -1.037622 -0.500347 -1.807609 -1.551044 0.466787 -0.550468 -2.036606 -1.331715 2.366366 1.882699 3.269147 1.324888 -1.228666 -2.611180 -3.214167 -3.159909 -3.446474 -2.671463 -1.287762 -0.629516 -0.548991 2.286339 -0.218797 -0.222155 5.825560 5.194249 0.459563 2.603374 -3.474162 2.682803 1.223086 -2.501711 -3.198905 -3.907394 -3.786079 -3.370413 -2.626664 -1.366228 -0.054817 1.323925 4.758287 -0.112120 2.242132 4.979688 -2.394993 -2.364711 4.317736 -0.826066 2.149110 0.967044 -1.375219 -3.883836 -4.582099 -4.272365 -3.297386 -2.599784 -1.409699 0.103357 1.870171 1.121399 4.662939 4.665585 -0.611765 -2.685300 -1.263368 1.513574 -6.060273 -1.558978 -3.843640 -2.583750 -4.682767 -5.146677 -4.625543 -3.203946 -2.648119 -1.599686 -0.541140 0.591115 -1.211765 -1.251957 4.044441 3.466232 1.236238 -3.355556 -1.328634 0.543750 2.611128 -1.273861 -4.486999 -5.403461 -5.706416 -4.869518 -3.049133 -2.761989 -2.028699 -0.836940 1.271130 3.754506 1.384290 0.935221 3.807554 1.264093 -3.883362 -1.630805 -2.346472 0.317736 -1.564022 -4.518886 -5.522394 -5.739810 -4.955184 -2.865979 -2.804851 -2.406278 -1.493607 -0.042104 1.787786 1.857881 2.985647 -5.090124 5.579903 2.399437 -0.547086 -4.818778 -8.117451 -6.434460 -5.554724 -5.394038 -5.678843 -4.951638 -2.655105 -2.769116 -2.595082 -1.974362 -1.022395 -0.087630 -1.000750 -0.845660 -1.883596 2.551985 3.440780 0.873513 -3.708553 -7.395707 -7.488810 -6.226085 -5.622846 -5.652340 -4.832016 -2.453623 -2.735638 -2.666260 -2.278418 -1.665054 -1.275221 -1.898102 -2.287830 -0.866828 0.761907 0.650374 -1.079298 -4.368339 -7.118498 -7.355869 -6.617546 -5.986428 -5.626546 -4.633565 -1.745204 -2.086721 -2.123301 -2.082918 -1.960926 -1.889941 -1.986929 -1.982906 -1.583971 -1.220706 -1.349603 -2.083851 -3.520979 -4.800621 -5.101142 -4.920022 -4.677571 -4.435352 -3.583135)
(InitialTransformParametersFileName "data/TransformParameters.1.txt")
(UseBinaryFormatForTransformationParameters "false")
(HowToCombineTransforms "Compose")

// Image specific
(FixedImageDimension 2)
(MovingImageDimension 2)
(FixedInternalImagePixelType "float")
(MovingInternalImagePixelType "float")
(Size 256 256)
(Index 0 0)
(Spacing 1.0000000000 1.0000000000)
(Origin 0.0000000000 0.0000000000)
(Direction 1.0000000000 0.0000000000 0.0000000000 1.0000000000)
(UseDirectionCosines "true")

// BSplineTransform specific
(GridSize 19 19)
(GridIndex 0 0)
(GridSpacing 16.0000000000 16.0000000000)
(GridOrigin -16.5000000000 -16.5000000000)
(GridDirection 1.0000000000 0.0000000000 0.0000000000 1.0000000000)
(BSplineTransformSplineOrder 3)
(UseCyclicTransform "false")

// ResampleInterpolator specific
(ResampleInterpolator "FinalBSplineInterpolator")
(FinalBSplineInterpolationOrder 3)

// Resampler specific
(Resampler "DefaultResampler")
(DefaultPixelValue 0.000000)
(ResultImageFormat "mhd")
(ResultImagePixelType "float")
(CompressResultImage "false")
