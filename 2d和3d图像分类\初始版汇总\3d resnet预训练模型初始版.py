## 3d resnet 18提取3d深度学习特征，已成功-cpu版本
import numpy as np
import torch
import pandas as pd
import SimpleITK as sitk
from torch.utils.data import DataLoader
from torchvision.transforms import Compose
from torchvision.models.video import r3d_18
import os
import glob
 # 定义数据和掩码存储路径
data_dir = r"D:\image\nii\image"
mask_dir = r"D:\image\nii\mask"
excel_path = r"D:\image\nii\features.xlsx"
 # 加载预训练的 3D ResNet 模型
model = r3d_18(pretrained=True)
 # 找到图像和掩码文件并存储它们的路径
img_files_list = []
mask_files_list = []
extensions = ['*.nii'] #extensions = ['*.jpg', '*.png']
for ext in extensions:
    img_files_list.extend(glob.glob(os.path.join(data_dir, ext)))
    mask_files_list.extend(glob.glob(os.path.join(mask_dir, ext)))
 # 定义一个函数，使用预训练模型从图像和掩码中提取特征
def extractor(img_path, model, use_gpu=False):
    image = sitk.ReadImage(img_path)
    np_image = sitk.GetArrayFromImage(image)
    np_image = np_image.astype(np.float32)
    if np_image.ndim == 3:  # 将灰度图像添加两个通道，使其类似于 RGB 图像
       np_image = np.stack([np_image.squeeze()]*3, axis=0)
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    if use_gpu:
        tensor_image = tensor_image.cuda()
    with torch.no_grad():
        features = model(tensor_image)
    return features.squeeze().cpu().numpy()
features = []
for img_path in img_files_list:
    file_name = os.path.splitext(os.path.basename(img_path))[0]
    np_image = sitk.GetArrayFromImage(sitk.ReadImage(img_path)).astype(np.float32)
    if np_image.ndim == 3:  # 将灰度图像添加两个通道，使其类似于 RGB 图像
        np_image = np.stack([np_image.squeeze()]*3, axis=0)
    feature = extractor(img_path, model, use_gpu=False)
    features.append([file_name, img_path] + list(feature))
 # 创建数据帧的列名称
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]
 # 将特征转换为 numpy 数组并将其类型转换为 float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)
 # 创建数据帧
df = pd.DataFrame(features, columns=columns)
 # 使用 astype 将文件名和图像路径转换为字符串并将其设置为数据帧的索引列
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)
 # 将数据帧保存到 Excel 文件中
df.to_excel(excel_path)

##3d resnet预训练模型进行迁移学习的代码  未调试
import torch
import torch.optim as optim
import torch.nn as nn
import torchvision.models as models
import nibabel as nb
import numpy as np
 # Load pre-trained 3D ResNet model
resnet_3d = models.video.r3d_18(pretrained=True)
resnet_3d

 # Freeze all layers except the last fc layer
for param in resnet_3d.parameters():
    param.requires_grad = False
 # Modify the last fc layer to suit your problem
num_classes = 10  # for example
resnet_3d.fc = nn.Linear(resnet_3d.fc.in_features, num_classes)
 # Define loss function and optimizer
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(resnet_3d.fc.parameters(), lr=0.001)
 # Load and preprocess your training and testing data
train_files = ['train_1.nii', 'train_2.nii', ...]
test_files = ['test_1.nii', 'test_2.nii', ...]
def load_nii_file(file):
    nii = nb.load(file)
    data = nii.get_fdata()
    # Add preprocessing steps here
    return data
train_data = [load_nii_file(file) for file in train_files]
test_data = [load_nii_file(file) for file in test_files]
 # Convert data to PyTorch tensors and create data loaders
train_data = torch.from_numpy(np.array(train_data)).float()
test_data = torch.from_numpy(np.array(test_data)).float()
train_dataset = torch.utils.data.TensorDataset(train_data, train_labels)
train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=64, shuffle=True)
test_dataset = torch.utils.data.TensorDataset(test_data, test_labels)
test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=64, shuffle=False)
 # Train the modified model
num_epochs = 10
for epoch in range(num_epochs):
    running_loss = 0.0
    for i, data in enumerate(train_loader, 0):
        # Get inputs and labels
        inputs, labels = data
         # Zero the parameter gradients
        optimizer.zero_grad()
         # Forward + backward + optimize
        outputs = resnet_3d(inputs)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
         # Print statistics
        running_loss += loss.item()
        if (i+1) % 10 == 0:
            print('[%d, %5d] loss: %.3f' %
                  (epoch+1, i+1, running_loss / 10))
            running_loss = 0.0
print('Finished Training')


##添加自注意力机制的3d resnet代码-cpu版本  已成功
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import SimpleITK as sitk
from torchvision.models.video import r3d_18
import os
import glob


# 添加以下代码以设置随机种子：
import random
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
 set_seed(42)

# 定义数据和掩码存储路径
data_dir = r"D:\image\nii\image"
mask_dir = r"D:\image\nii\mask"
excel_path = r"D:\image\nii\features_attention2.xlsx"

class SEModule(nn.Module):
    def __init__(self, channels, reduction=16):
        super(SEModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.fcl = nn.Linear(channels, channels // reduction, bias=False)
        self.relu = nn.ReLU(inplace=True)
        self.fcl2 = nn.Linear(channels // reduction, channels, bias=False)
        self.sigmoid = nn.Sigmoid()
    def forward(self, x):
        scale = self.avg_pool(x)
        scale = torch.flatten(scale, 1)
        scale = self.fcl(scale)
        scale = self.relu(scale)
        scale = self.fcl2(scale)
        scale = self.sigmoid(scale)
        scale = scale.view(scale.size(0), scale.size(1), 1, 1, 1)
        x = x * scale
        return x
def apply_se_to_blocks(model, blocks):
    for block_name in blocks:
        block = getattr(model, block_name)
        for i, layer in enumerate(block.children()):
            # check type of layer
            if isinstance(layer, nn.Conv3d):
                out_channels = layer.out_channels
            elif isinstance(layer, nn.Sequential):
                for l in layer.children():
                    if isinstance(l, nn.Conv3d):
                        out_channels = l.out_channels
                        break
            else:
                out_channels = None
             if out_channels is not None:
                se_module = SEModule(out_channels)
                setattr(block, f"se_module_{i}", se_module)
    return model
 # Load pretrained 3D ResNet model and replace the last layers
model = r3d_18(pretrained=True)
model.fc = nn.Sequential(nn.AdaptiveAvgPool3d(1), nn.Flatten())
 # Apply SE modules to each of the residual blocks
model_se = r3d_18(pretrained=False)
model_se = apply_se_to_blocks(model_se, ["layer1", "layer2", "layer3", "layer4"])
 # Apply an SE module to the stem of the network
model_se.stem.add_module("se_module", SEModule(64))
 # Set the custom 3D ResNet model to evaluation mode
model_se.eval()
 # Transfer the base model's state dict to the custom model, skipping the keys (fc weight and bias) that do not match
model_dict = model.state_dict()
model_se_dict = model_se.state_dict()
for k in model_dict.keys():
    if k in model_se_dict and model_se_dict[k].shape == model_dict[k].shape:
        model_se_dict[k] = model_dict[k]
model_se.load_state_dict(model_se_dict)
 # Keep the rest of the code as it is
# ...
 # Keep the rest of the code as it is
 # Find image and mask files and store their paths
img_files_list = []
mask_files_list = []
extensions = ['*.nii']
for ext in extensions:
    img_files_list.extend(glob.glob(os.path.join(data_dir, ext)))
    mask_files_list.extend(glob.glob(os.path.join(mask_dir, ext)))
def extractor(img_path, model, use_gpu=False):
    image = sitk.ReadImage(img_path)
    np_image = sitk.GetArrayFromImage(image).astype(np.float32)
    if np_image.ndim == 3:
        np_image = np.stack([np_image.squeeze()] * 3, axis=0)
    tensor_image = torch.from_numpy(np_image).unsqueeze(0).float()
    if use_gpu:
        tensor_image = tensor_image.cuda()
    with torch.no_grad():
        features = model(tensor_image)
    return features.squeeze().cpu().numpy()
features = []
for img_path in img_files_list:
    file_name = os.path.splitext(os.path.basename(img_path))[0]
    feature = extractor(img_path, model_se, use_gpu=False)
    features.append([file_name, img_path] + list(feature))
 # Create DataFrame column names
columns = ['file_name', 'img_path'] + [f'feature_{i+1}' for i in range(len(features[0]) - 2)]
 # Convert features to numpy array and cast their type to float32
features = np.array(features)
features[:, 2:] = features[:, 2:].astype(np.float32)
 # Create DataFrame
df = pd.DataFrame(features, columns=columns)
 # Cast file_name and img_path to string and set them as DataFrame index column
df['file_name'] = df['file_name'].astype(str)
df['img_path'] = df['img_path'].astype(str)
df.set_index('file_name', inplace=True)
 # Save DataFrame to Excel file
df.to_excel(excel_path)