##安装gpu版本的torch：根据pytorch官网  https://pytorch.org/  的提示进行安装，安装前需要卸载之前的cpu版本的torch
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# #!/usr/bin/env python
# coding: utf-8
# ImportError: Cannot load backend 'TkAgg' which requires the 'tk' interactive framework
# 保存时可运行下面代码
# sudo apt-get update
# sudo apt-get install python3-tk
## cuda报错时用，或者安装cuda驱动

#%%
from torch.utils import data
from torchvision.datasets import ImageFolder
import torch
print(torch.__version__)
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib   #pycharm中要加这行代码，否则不能出图
# matplotlib.use('TkAgg') #pycharm中要加这行代码，否则不能出图
import matplotlib.pyplot as plt
# get_ipython().run_line_magic('matplotlib', 'inline')

import torchvision
from torchvision import transforms
import os
from tqdm import tqdm
import shutil
import pandas as pd
from sklearn.metrics import precision_recall_curve, average_precision_score
from sklearn.metrics import roc_curve, auc
from sklearn.metrics import confusion_matrix
# from scipy import interp
from itertools import cycle
import itertools
import random

import ssl #更新ssl证书
ssl._create_default_https_context = ssl._create_unverified_context

##
def setup_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
# 设置随机数种子
setup_seed(30)

#%%
# base_dir = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\tumor\jpg\hbp'
# base_dir = r'F:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\2.卷积部分(第8-11章)参考代码和数据集\datasets\4weather-train-test'
# # base_dir =r'E:\3D Slicer\datasets\VETC-suzhou\APDP'
# # base_dir = r'E:\2. python深度学习课程\1.日月光华-pyorch深度学习课程\2.卷积部分(第8-11章)参考代码和数据集\datasets\EGFR'
#
# train_dir = os.path.join(base_dir , 'train')
# test_dir = os.path.join(base_dir , 'test')

# train_dir =r'H:\1.HCC-VETC\datasets\HCC-suzhou\paper-data\jpg\ap\train'
# test_dir = r'H:\1.HCC-VETC\datasets\HCC-suzhou\paper-data\jpg\ap\test'
# 4weather-train-test

# train_dir = '/root/autodl-tmp/ap/train'   #  # '/hy-tmp/ap/train'  #'/root/autodl-tmp/ap/train' # 矩池云文件路径  '/mnt/hcc/train'
# test_dir =  '/root/autodl-tmp/ap/validation'  #     #'/root/autodl-tmp/ap/validation'

train_dir = r'k:\2023大创项目\中期data\train2'   
test_dir =  r'k:\2023大创项目\中期data\validation2'

##不做数据增强  224 224  (192, 192)  (160, 160)  (128, 128)(96, 96)
train_transform = transforms.Compose([
        transforms.Resize((224,224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                             std=[0.229, 0.224, 0.225])
   ])


test_transform = transforms.Compose([
    transforms.Resize((224,224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

## 做数据增强
# train_transform = transforms.Compose([
#     transforms.Resize(224),
#     transforms.RandomResizedCrop(224, scale=(0.6,1.0), ratio=(0.8,1.0)),
#     transforms.RandomHorizontalFlip(),
#     transforms.RandomRotation(0.2),
#     torchvision.transforms.ColorJitter(brightness=0.5, contrast=0, saturation=0, hue=0),
#     torchvision.transforms.ColorJitter(brightness=0, contrast=0.5, saturation=0, hue=0),
#     transforms.ToTensor(),
#     transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
# ])


# test_transform = transforms.Compose([
#     transforms.Resize((224, 224)),
#     transforms.ToTensor(),
#     transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
# ])

##
train_ds =  torchvision.datasets.ImageFolder(
        train_dir,
        transform=train_transform
    )

test_ds =  torchvision.datasets.ImageFolder(
        test_dir,
        transform=test_transform
    )

#%%

print(train_ds.class_to_idx)   #ImageFolder方式导入的文件夹名和对应label
print(test_ds.class_to_idx)
print(train_ds.imgs[:5])
# print(dataset[0][1])# 第一维是第几张图，第二维为1返回label
print(train_ds[0][0].size()) # 深度学习中图片数据一般保存成CxHxW，即通道数x图片高x图片宽
len(train_ds)
# test_ds.imgs

## In[7]:

BATCH_SIZE = 32

# In[8]:

train_dl = torch.utils.data.DataLoader(
                            train_ds,
                            batch_size=BATCH_SIZE,
                            shuffle=True
)


test_dl = torch.utils.data.DataLoader(
                            test_ds,
                            batch_size=BATCH_SIZE,
)

#%% smote 对训练集数据和label进行过采样，只能用于训练集
# from imblearn.over_sampling import SMOTE
# import torch
# import torchvision.transforms.functional as F

# train_features = []
# train_labels = []

# for data in train_ds:
#     features, label = data
#     train_features.append(features)  # 将特征转换为张量
#     train_labels.append(label)

# train_features = torch.stack(train_features)
# train_features = train_features.flatten(start_dim=1)  # 展平特征为一维向量
# train_labels = torch.tensor(train_labels)

# smote = SMOTE()
# train_features_resampled, train_labels_resampled = smote.fit_resample(train_features, train_labels)
# train_features_resampled = torch.from_numpy(train_features_resampled)
# train_labels_resampled = torch.from_numpy(train_labels_resampled)

# train_features_original_shape = []
# train_labels_original_shape = []

# for i in range(len(train_features_resampled)):
#     features_original_shape = torch.reshape(train_features_resampled[i], (3, 224, 224))  # 假设原始形状为 (3, 224, 224)
#     features_original_shape = F.to_pil_image(features_original_shape)  # 转换为 PIL 图像
#     train_features_original_shape.append(F.to_tensor(features_original_shape))  # 将特征转换为张量
#     label = train_labels_resampled[i]
#     train_labels_original_shape.append(label)

# train_features_original_shape = torch.stack(train_features_original_shape)
# train_labels_original_shape = torch.tensor(train_labels_original_shape)

# train_ds_original_shape = torch.utils.data.TensorDataset(train_features_original_shape, train_labels_original_shape)
# train_dl = torch.utils.data.DataLoader(train_ds_original_shape, batch_size=BATCH_SIZE, shuffle=True)

# test_dl = torch.utils.data.DataLoader(
#     test_ds,
#     batch_size=BATCH_SIZE,
# )
#
#学术资源加速
# import subprocess
# import os
#
# result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
# output = result.stdout
# for line in output.splitlines():
#     if '=' in line:
#         var, value = line.split('=', 1)
#         os.environ[var] = value

## In[10]:
# #预训练模型选择torchvision.models.vgg16,19 （mobilenet_v2，resnet18，AlexNet,DenseNet,densenet121,161,169,201;
# GoogLeNet;Inception3,inception_v3,resnet18,34,50,152;）； squeezenet1_0；mnasnet1_0;shufflenet_v2_x1_0;
# Xception不在torchvision中；efficientnet-b0到b7

import torchvision.models as models
print(models.list_models())

#看官网https://pytorch.org/vision/stable/models.html

model = torchvision.models.swin_v2_b(pretrained=True) 
# model = torchvision.models.resnet50(weights="IMAGENET1K_V2") #或者resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)

# model = torchvision.models.vit_b_32(pretrained=True)#vit模型还有vit_b_32，vit_l_16，vit_l_32，vit_h_14
# model = torchvision.models.vision_transformer.vit_b_16(pretrained=True)
# model = torchvision.models.vgg16(pretrained=True)
# model = torchvision.models.vgg19 (pretrained=True)
# model = torchvision.models.resnet50(pretrained=True)
# model = torchvision.models.resnet101(pretrained=True)
# model = torchvision.models.resnet152(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
# model = torchvision.models.densenet161(pretrained=True)
# model = torchvision.models.googlenet(pretrained=True)
# model = torchvision.models.alexnet(pretrained=True)
# model = torchvision.models.mobilenet_v2(pretrained=True)
# model = torchvision.models.mnasnet1_0(pretrained=True)
# model = torchvision.models.mobilenet_v3_large(pretrained=True)
# model = torchvision.models.shufflenet_v2_x1_0(pretrained=True)
# model = torchvision.models.squeezenet1_0(pretrained=True)
# model = torchvision.models.efficientnet_b0(pretrained=True)
model.parameters

# from torchvision.models import resnet50, ResNet50_Weights
# model=resnet50(weights="IMAGENET1K_V2")
## resnet50(weights=ResNet50_Weights.IMAGENET1K_V1)
# model=resnet50(weights=None)

#%%
## In[11]:

for param in model.parameters():
    param.requires_grad = False

#数据集中分类的类别数
num_classes = 2  # num_classes表示输出特征

#resnet，googlenet,shufflenet_v2最后一层为名字为fc;把最后一层替换为新的分类，注意要修改最后一层的名字
in_f = model.fc.in_features      #in_f表示输入特征
model.fc = nn.Linear(in_features=in_f,out_features= num_classes) ##resnet,shufflenet_v2
model  #修改后的模型

# densenet121最后一层为名字为classifier
# in_f = model.classifier.in_features
# model.classifier = nn.Linear(in_features=in_f, out_features=num_classes)
# model  # 修改后的模型
# #
# # alexnet,vgg最后一层为名字为classifier[6]
# in_f = model.classifier[6].in_features
# model.classifier[6] = nn.Linear(in_features=in_f,out_features=num_classes)  #alexnet,vgg
# model  #修改后的模型
#
# # #mobilenet_v2    efficientnet_b2
# in_f = model.classifier[1].in_features #mobilenet_v2
# model.classifier[1] = nn.Linear(in_features=in_f, out_features= num_classes)   #mobilenet_v2
# model  #修改后的模型
#
# # 对于squeezenet1_0,可以直接更改最后的classifier层,重新初始化Conv2d层
# model.classifier[1] = nn.Conv2d(512, num_classes, kernel_size=(1,1), stride=(1,1)) #Squeezenet

# in_f = model.heads[0].in_features   # vit模型
# model.heads[0] = nn.Linear(in_features=in_f, out_features= num_classes) #vit模型
# model  #修改后的模型

in_f = model.head.in_features   # swin_v2_b
model.head = nn.Linear(in_f, 2) #swin_v2_b模型
model

#%% In[15]:
print(torch.cuda.is_available()) #看一下cuda是否可用

if torch.cuda.is_available():    
    num_gpus = torch.cuda.device_count()# 获取GPU数量
    print("可用的GPU数量：", num_gpus)
    model.to('cuda')

# 使用多GPU进行训练
# if torch.cuda.device_count() > 1:
#     model = DataParallel(model)
    
# loss_fn = nn.CrossEntropyLoss()

#调整类别不平衡， 假设有两个类别，类别0和类别1
class_weights = torch.tensor([1.0, 2.0])  # 根据实际情况设置类别权重
class_weights = class_weights.to('cuda')
# 定义损失函数，并指定类别权重
loss_fn = nn.CrossEntropyLoss(weight=class_weights)

# Decay LR by a factor of 0.1 every 7 epochs
from torch.optim import lr_scheduler

# optimizer = torch.optim.Adam(model.fc.parameters(), lr=0.0001) #resnet  shufflenet_v2  优化器要传入最后一层所有参数  超参数搜索提示0.0001
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.0005)  #alexnet,vgg注意修改模型最后一层名称
# optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.0002) #densenet模型 Squeezenet，efficientnet
# optimizer = torch.optim.RMSprop(model.classifier.parameters(), lr=0.0002)#densenet模型 Squeezenet，efficientnet
# optimizer = torch.optim.Adam(model.heads.parameters(), lr=0.0001) #vit模型
# lr=0.0005 auc 0.69  lr=0.000126767 auc 0.70
optimizer = torch.optim.Adam(model.head.parameters(), lr=0.005)#swin-vit模型  0.0006

# 多GPU并行运算
# optimizer = torch.optim.Adam(model.module.fc.parameters(), lr=0.0001) #resnet  shufflenet_v2  优化器要传入最后一层所有参数  超参数搜索提示0.0001
# optimizer = torch.optim.Adam(model.module.classifier.parameters(), lr=0.0005)  #alexnet,vgg注意修改模型最后一层名称
# optimizer = torch.optim.Adam(model.module.classifier.parameters(), lr=0.0002) #densenet模型 Squeezenet，efficientnet
# optimizer = torch.optim.RMSprop(model.module.classifier.parameters(), lr=0.0002)#densenet模型 Squeezenet，efficientnet
# optimizer = torch.optim.Adam(model.module.heads.parameters(), lr=0.0001) #vit模型
# optimizer = torch.optim.Adam(model.module.head.parameters(), lr=0.0005)#swin-vit模型 多GPU并行运算

exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

#%%
##定义训练函数 简单版
# def fit(epoch, model, trainloader, testloader):
#     correct = 0
#     total = 0
#     running_loss = 0
#     model.train()
#     for x, y in trainloader:
#         if torch.cuda.is_available():
#             x, y = x.to('cuda'), y.to('cuda')
#         y_pred = model(x)
#         loss = loss_fn(y_pred, y)  #计算损失
#         optimizer.zero_grad()  # 梯度清零，否则会累加
#         loss.backward()  # 计算梯度
#         optimizer.step()  # 权重更新
#         with torch.no_grad():
#             y_pred = torch.argmax(y_pred, dim=1)
#             correct += (y_pred == y).sum().item()
#             total += y.size(0)
#             running_loss += loss.item()
#     exp_lr_scheduler.step()  #注意这个代码有学习率，因此这里需要加一行
#     epoch_loss = running_loss / len(trainloader.dataset)
#     epoch_acc = correct / total

#     test_correct = 0
#     test_total = 0
#     test_running_loss = 0

#     model.eval()
#     with torch.no_grad():
#         for x, y in testloader:
#             if torch.cuda.is_available():
#                 x, y = x.to('cuda'), y.to('cuda')
#             y_pred = model(x)
#             loss = loss_fn(y_pred, y)
#             y_pred = torch.argmax(y_pred, dim=1)
#             test_correct += (y_pred == y).sum().item()
#             test_total += y.size(0)
#             test_running_loss += loss.item()

#     epoch_test_loss = test_running_loss / len(testloader.dataset)
#     epoch_test_acc = test_correct / test_total

#     print('epoch: ', epoch,
#           'train_loss： ', round(epoch_loss, 3),
#           'train_accuracy:', round(epoch_acc, 3),
#           'test_loss： ', round(epoch_test_loss, 3),
#           'test_accuracy:', round(epoch_test_acc, 3)
#              )

#     return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc

# epochs = 30

# train_loss = []
# train_acc = []
# test_loss = []
# test_acc = []

# for epoch in range(epochs):
#     epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,model,train_dl,test_dl)
#     train_loss.append(epoch_loss)
#     train_acc.append(epoch_acc)
#     test_loss.append(epoch_test_loss)
#     test_acc.append(epoch_test_acc)
#     torch.cuda.empty_cache()  # 清空CUDA缓存，解决报错CUDA断言报错

#%% 复杂版fit函数
def fit(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    model.train()
    train_predictions = []  # 存储训练集预测的标签
    train_labels = []  # 存储训练集真实的标签
    train_probs = []  # 存储训练集预测的概率    
    train_paths = []  # 存储训练集图片路径
    for x, y in trainloader:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred_label = torch.argmax(y_pred, dim=1)
            correct += (y_pred_label == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            train_predictions.extend(y_pred_label.cpu().numpy())  # 将预测的标签添加到列表中
            train_labels.extend(y.cpu().numpy())  # 将真实的标签添加到列表中
            train_probs.extend(torch.softmax(y_pred, dim=1).cpu().numpy())  # 将预测的概率添加到列表中
            # train_probs.extend((torch.softmax(y_pred, dim=1).cpu().numpy()).tolist())                 
            train_paths.clear() # 清空image_paths列表
            train_paths.extend(trainloader.dataset.imgs)# 将图片路径添加到列表中

    exp_lr_scheduler.step()
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / total

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    test_predictions = []  # 存储测试集预测的标签
    test_labels = []  # 存储测试集真实的标签
    test_probs = []  # 存储测试集预测的概率    
    test_paths = []  # 存储测试集图片路径
    
    model.eval()
    with torch.no_grad():
        for x, y in testloader:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred_label = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred_label == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            test_predictions.extend(y_pred_label.cpu().numpy())  # 将预测的标签添加到列表中
            test_labels.extend(y.cpu().numpy())  # 将真实的标签添加到列表中
            test_probs.extend(torch.softmax(y_pred, dim=1).cpu().numpy())     # 将预测的概率添加到列表中           
            test_paths.clear()   # 清空image_paths列表
            test_paths.extend(testloader.dataset.imgs)# 将图片路径添加到列表中

    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / test_total

    print('epoch: ', epoch,
          'train_loss： ', round(epoch_loss, 3),
          'train_accuracy:', round(epoch_acc, 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
          )
    
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc, train_predictions, train_labels, train_probs, train_paths, test_predictions, test_labels, test_probs, test_paths


epochs = 30

train_loss = [];train_acc = [];test_loss = [];test_acc = []

for epoch in range(epochs):
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc, train_predictions, train_labels, train_probs, train_paths, test_predictions, test_labels, test_probs,test_paths = fit(epoch, model, train_dl, test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)
    torch.cuda.empty_cache()  # 清空CUDA缓存，解决报错CUDA断言报错

# 创建字典来保存预测结果、概率和最大概率
train_data = {'Image Path': train_paths, 'True Labels': train_labels, 'Predict_label': train_predictions, 'Probability': train_probs}
test_data = {'Image Path': test_paths, 'True Labels': test_labels, 'Predict_label': test_predictions, 'Probability': test_probs}
train_loss_acc = {'Train Loss': train_loss, 'Train Accuracy': train_acc}
test_loss_acc = {'Test Loss': test_loss, 'Test Accuracy': test_acc}

# 创建DataFrame保存数据
train_results = pd.DataFrame(train_data);test_results = pd.DataFrame(test_data)
train_loss_acc = pd.DataFrame(train_loss_acc);test_loss_acc = pd.DataFrame(test_loss_acc)

# 将结果保存到Excel文件
# train_results.to_excel('/root/autodl-tmp/patch5/train_vit_patch.xlsx', index=False)
# test_results.to_excel('/root/autodl-tmp/patch5/val_vit_patch.xlsx.xlsx', index=False)
# train_loss_acc.to_excel('/root/autodl-tmp/patch5/train_loss_acc.xlsx.xlsx', index=False)
# test_loss_acc.to_excel('/root/autodl-tmp/patch5/test_loss_acc.xlsx.xlsx', index=False)

from sklearn.metrics import classification_report
#训练集混淆矩阵
cm=confusion_matrix(train_labels,train_predictions)
print(classification_report(train_labels,train_predictions,digits=3))
#测试集混淆矩阵
cm=confusion_matrix(test_labels,test_predictions)
print(classification_report(test_labels,test_predictions,digits=3))

#%%绘制loss曲线
plt.plot(range(1, len(train_loss)+1), train_loss, label='train_loss')
plt.plot(range(1, len(train_loss)+1), test_loss, label='test_loss')
plt.legend()
plt.title("ResNet50") #ResNet50,DenseNet121,Inception_v3,SqueezeNet_v1.0
plt.rcParams['font.size'] = 18
plt.show()
# plt.savefig(r"E:\data\6loss.tiff")

##绘制acc曲线
plt.plot(range(1, len(train_loss)+1), train_acc, label='train_acc')
plt.plot(range(1, len(train_loss)+1), test_acc, label='test_acc')
plt.legend()
plt.title("ResNet50") #ResNet50,DenseNet121,Inception_v3,SqueezeNet_v1.0
plt.rcParams['font.size'] = 18
plt.show()
# plt.savefig(r"E:\data\acc6.tiff")

#%%
#### 先训练自己的模型，再微调预训练模型
for param in model.parameters():  #重新训练所有可训练参数
    param.requires_grad = True

len(list(model.parameters())) 
list(model.parameters())[-3:] #表示最后4层
# list(model.parameters())[310:314] #表示最后310到313层

# for param in list(model.parameters())[310:314]:#310-314层可训练
#     param.requires_grad = True
for param in list(model.parameters())[-3:]:  #最后4层重新训练
    print(param)
    param.requires_grad = True 

extend_epochs = 30
from torch.optim import lr_scheduler
# optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)                #model.parameters()为整个可训练模型参数
optimizer = torch.optim.Adam(list(model.parameters())[-3:], lr=0.0001)   # 优化器要传入解冻层的参数
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)

# for epoch in range(extend_epochs):#简单版
#     epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,model,train_dl,test_dl)
#     train_loss.append(epoch_loss)
#     train_acc.append(epoch_acc)
#     test_loss.append(epoch_test_loss)
#     test_acc.append(epoch_test_acc)

for epoch in range(extend_epochs):#复杂版
    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc, train_predictions, train_labels, train_probs, train_paths, test_predictions, test_labels, test_probs, test_paths = fit(epoch, model, train_dl, test_dl)
    train_loss.append(epoch_loss)
    train_acc.append(epoch_acc)
    test_loss.append(epoch_test_loss)
    test_acc.append(epoch_test_acc)    

# 创建字典来保存预测结果、预测概率
train_data = {'Image Path': train_paths, 'True Labels': train_labels, 'Predict_label': train_predictions, 'Probability': train_probs}
test_data = {'Image Path': test_paths, 'True Labels': test_labels, 'Predict_label': test_predictions, 'Probability': test_probs}
train_loss_acc = {'Train Loss': train_loss, 'Train Accuracy': train_acc}
test_loss_acc = {'Test Loss': test_loss, 'Test Accuracy': test_acc}

# 创建DataFrame保存数据
train_results = pd.DataFrame(train_data);test_results = pd.DataFrame(test_data)
train_loss_acc = pd.DataFrame(train_loss_acc);test_loss_acc = pd.DataFrame(test_loss_acc)

# 将结果保存到Excel文件
# train_results.to_excel('/root/autodl-tmp/patch5/train_vit_patch.xlsx', index=False)
# test_results.to_excel('/root/autodl-tmp/patch5/val_vit_patch.xlsx.xlsx', index=False)
# train_loss_acc.to_excel('/root/autodl-tmp/patch5/train_loss_acc.xlsx.xlsx', index=False)
# test_loss_acc.to_excel('/root/autodl-tmp/patch5/test_loss_acc.xlsx.xlsx', index=False)

#训练集混淆矩阵
cm=confusion_matrix(train_labels,train_predictions)
print(classification_report(train_labels,train_predictions,digits=3))
#测试集混淆矩阵
cm=confusion_matrix(test_labels,test_predictions)
print(classification_report(test_labels,test_predictions,digits=3))

#%% In[25]:loss和acc曲线
plt.plot(range(1, len(train_loss)+1), train_loss, label='train_loss')
plt.plot(range(1, len(train_loss)+1), test_loss, label='test_loss')
plt.legend()
plt.show()
plt.savefig('/root/autodl-tmp/appphbp/densenet_loss.jpg')

plt.plot(range(1, len(train_loss)+1), train_acc, label='train_acc')
plt.plot(range(1, len(train_loss)+1), test_acc, label='test_acc')
plt.legend()
plt.show()
plt.savefig('/root/autodl-tmp/appphbp/densenet_acc.jpg')

##ROC曲线和AUC值
import scikitplot as skplt
import matplotlib.pyplot as plt
# 创建一个大一些的图像
plt.figure(figsize=(10, 8))

# 绘制ROC曲线代码
skplt.metrics.plot_roc(train_labels,train_probs, #注意要用多类别预测概率列，而不是最大预测概率
                       title='ROC Plot for SqueezeNet_v1.0', #Densenet121
                       # classes_to_plot=['class1', 'class2','class3'],
                       cmap='Set1') # cmap 设置调色板
# 调整图例的位置和大小
plt.legend(loc='lower right', fontsize='xx-small', markerscale=0.1)
plt.show()

skplt.metrics.plot_roc(test_labels,test_probs, #注意要用多类别预测概率列，而不是最大预测概率
                       title='ROC Plot for SqueezeNet_v1.0', #Densenet121
                       # classes_to_plot=['class1', 'class2','class3'],
                       cmap='Set1') # cmap 设置调色板
# 调整图例的位置和大小
plt.legend(loc='lower right', fontsize='xx-small', markerscale=0.1)
plt.show()
#%%
##保存最优参数的模型
# import copy
# best_model_wts = copy.deepcopy(model.state_dict())

# best_acc = 0.0
# train_loss = []
# train_acc = []
# test_loss = []
# test_acc = []

# for epoch in range(epochs):
#     epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,model,train_dl,test_dl)
#     train_loss.append(epoch_loss)
#     train_acc.append(epoch_acc)
#     test_loss.append(epoch_test_loss)
#     test_acc.append(epoch_test_acc)
#     if epoch_test_acc > best_acc:
#         best_acc = epoch_test_acc
#         best_model_wts = copy.deepcopy(model.state_dict())
#         print(best_acc)
# model.load_state_dict(best_model_wts)
# model.eval()

# #完整模型的保存和加载
# PATH = './my_whole_model.pth'
# torch.save(model, PATH)

#%%Hyperopt超参数搜索迭代
import hyperopt
from hyperopt import fmin, tpe, hp, Trials

def objective(params):   
    lr = params['lr'] # 定义超参数
    batch_size = params['batch_size']

    correct = 0 # 训练步骤    
    total = 0
    running_loss = 0
    model.train()
    for x, y in train_dl:
        if torch.cuda.is_available():
            x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)  # 计算损失
        optimizer.zero_grad()  # 梯度清零，否则会累加
        loss.backward()  # 计算梯度
        optimizer.step()  # 权重更新
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
    exp_lr_scheduler.step()  # 注意这个代码有学习率，因此这里需要加一行
    epoch_loss = running_loss / len(train_dl)
    epoch_acc = correct / total

    test_correct = 0
    test_total = 0
    test_running_loss = 0
    model.eval()
    with torch.no_grad():
        for x, y in test_dl:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
    epoch_test_loss = test_running_loss / len(test_dl)
    epoch_test_acc = test_correct / test_total

    print('train_loss： ', round(epoch_loss, 3),
          'train_accuracy:', round(epoch_acc, 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3)
          )
    return {'loss': epoch_loss, 'acc': epoch_acc, 'test_loss': epoch_test_loss,
            'test_acc': epoch_test_acc,'status': hyperopt.STATUS_OK}

# 定义超参数搜索空间
space = {
    'lr': hp.loguniform('lr', np.log(0.0001), np.log(0.001)),
    'batch_size': hp.choice('batch_size', [16, 32, 64])
}

# 创建Trials对象用于记录每次试验的结果
trials = Trials()

# 运行超参数搜索
best = fmin(fn=objective,space=space,algo=tpe.suggest,max_evals=10,trials=trials)

# 输出最佳超参数组合和对应的性能
best_params = hyperopt.space_eval(space, best)
best_accuracy = -trials.best_trial['result']['loss']
print("最佳超参数组合：", best_params)
print("最佳性能：", best_accuracy)

#%% 保存图片路径、预测概率、预测label
# train_dl不能做shuffle，否则路径对不上
import torch
import pandas as pd
# 计算混淆矩阵和评价指标
from sklearn.metrics import classification_report, confusion_matrix

new_train_dl = torch.utils.data.DataLoader(train_ds,batch_size=BATCH_SIZE) #不做shuffle

def get_predictions(model, dataloader):
    model.eval()
    image_paths = []
    predict_label = []
    probability = []
    max_probability = []
    true_labels = []
    with torch.no_grad():
        for x, y in dataloader:
            if torch.cuda.is_available():
                x = x.to('cuda')
                y = y.to('cuda')
            y_pred = model(x)
            y_pred_prob = torch.softmax(y_pred, dim=1)
            max_prob, y_pred_class = torch.max(y_pred_prob, dim=1)
            predict_label.extend(y_pred_class.tolist())
            probability.extend(y_pred_prob.tolist())
            max_probability.extend(max_prob.tolist())
            true_labels.extend(y.tolist())
            # 清空image_paths列表
            image_paths.clear()
            image_paths.extend(dataloader.dataset.imgs)
    return  image_paths,true_labels,predict_label,probability, max_probability

# 获取训练集和测试集的预测结果、概率和最大概率
train_image_paths,train_true_label,train_predict_label, train_probability, train_max_probability = get_predictions(model, new_train_dl)
test_image_paths,test_true_label,test_predict_label, test_probability, test_max_probability = get_predictions(model, test_dl)

#训练集混淆矩阵
cm=confusion_matrix(train_true_label,train_predict_label)
print(classification_report(train_true_label,train_predict_label,digits=3))
#测试集混淆矩阵
cm=confusion_matrix(test_true_label,test_predict_label)
print(classification_report(test_true_label,test_predict_label,digits=3))

# 创建字典来保存预测结果、概率和最大概率
train_data = {'Image Path': train_image_paths,'True Labels': train_true_label,
               'Predict_label': train_predict_label,'Probability': train_probability}
test_data =  {'Image Path': test_image_paths,'True Labels': test_true_label,
               'Predict_label': test_predict_label,'Probability': test_probability}       

# 创建DataFrame保存数据
train_results = pd.DataFrame(train_data)
test_results = pd.DataFrame(test_data)

# 将结果保存到Excel文件
train_results.to_excel('/root/autodl-tmp/train_swin.xlsx', index=False)
test_results.to_excel('/root/autodl-tmp/val_swin.xlsx', index=False)

#%% 保存模型权重并预测新的数据
import pandas as pd
import torch
import torchvision.models as models
import copy
# 计算混淆矩阵和评价指标
from sklearn.metrics import classification_report, confusion_matrix

# state_dict就是一个简单的Python字典，它将模型中的可训练参数（比如weights和biases，batchnorm的running_mean、
# torch.optim参数等）通过将模型每层与层的参数张量之间一一映射，实现保存、更新、变化和再存储。
# PATH = '/root/autodl-tmp/my_resnet50_model.pth'
PATH = '/hy-tmp/ap_resnet50_model.pth'
# PATH = '/root/autodl-tmp/appphbp_swin_model.pth'   
   
# torch.save(model.state_dict(), PATH)

# 方法1 创建一个与训练模型结构相同的新模型
new_model = models.resnet50(num_classes=2)
# new_model = models.vit_b_32(num_classes=2)
# new_model = models.swin_v2_b(num_classes=2)
# new_model = models.densenet121(num_classes=2)
# new_model = models.vgg19(num_classes=2)
# new_model = models.googlenet(num_classes=2)
new_model

# 方法2 加载非预训练的ResNet-50模型
# pretrained_model = models.googlenet(pretrained=False)
# # 创建一个新的模型
# new_model = copy.deepcopy(pretrained_model)
# new_model.fc = torch.nn.Linear(pretrained_model.fc.in_features, 2)
# new_model

# 加载新模型的权重
new_model.load_state_dict(torch.load(PATH))

# 将新模型移动到GPU上（如果可用）
if torch.cuda.is_available():
    new_model = new_model.to('cuda')

test_data =  '/hy-tmp/train'  # r'D:\HCC\ap\test'    #'/root/autodl-tmp/ap/validation'

test_transform = transforms.Compose([
    transforms.Resize((224,224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

test_dataset =  torchvision.datasets.ImageFolder(test_data,transform=test_transform)
test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=32)

# train_loader = torch.utils.data.DataLoader(train_ds,batch_size=BATCH_SIZE)

# 可将新训练好的模型应用于测试集
def get_predictions(model, data_loader):
    model.eval()
    predictions = []
    probabilities = []
    labels = []
    image_paths = []
    correct = 0
    total = 0    
    with torch.no_grad():
        for x, y in data_loader:
            if torch.cuda.is_available():
                x, y = x.to('cuda'), y.to('cuda')                         
            y_pred = model(x)
            y_pred_label = torch.argmax(y_pred, dim=1)            
            predictions.extend(y_pred_label.cpu().numpy())            
            probabilities.extend(torch.softmax(y_pred, dim=1).cpu().numpy())
            labels.extend(y.cpu().numpy())           
            image_paths.clear() # 清空image_paths列表
            image_paths.extend(data_loader.dataset.imgs)            
            correct += (y_pred_label == y).sum().item()
            total += y.size(0)
    
    acc = correct / total    
    return predictions, probabilities, labels, image_paths, acc

# 使用示例
predictions, probabilities, labels, image_paths,acc = get_predictions(new_model, test_loader)

print("Accuracy:", acc);print("True Labels:", labels);print("Predictions:", predictions)
print("Probabilities:", probabilities);print("Image Paths:", image_paths)

#测试集混淆矩阵
cm=confusion_matrix(labels,predictions)
print(classification_report(labels,predictions,digits=3))

# 创建一个 DataFrame
df = pd.DataFrame({'image_paths': image_paths,'True Labels': labels, 'predictions': predictions, 
                   'Probability': probabilities,'acc': acc })

# 保存到 Excel 文件
# df.to_excel('/root/autodl-tmp/train/train_swin_cp2.xlsx')
df.to_excel('/hy-tmp/resnet_aptrain_features2.xlsx')
 

# skplt.metrics.plot_roc(labels,probabilities, #注意要用多类别预测概率列，而不是最大预测概率
#                        title='ROC Plot for swin', #Densenet121
#                        # classes_to_plot=['class1', 'class2','class3'],
#                        cmap='Set1') # cmap 设置调色板
# # 调整图例的位置和大小
# plt.legend(loc='lower right', fontsize='xx-small', markerscale=0.1)
# plt.show()

#%%提取自己训练的2d模型的深度学习特征  方法1

# # PATH = '/root/autodl-tmp/my_resnet50_model.pth'
# PATH =  '/hy-tmp/ap_resnet50_model.pth'
# # 方法1 创建一个与训练模型结构相同的新模型
# model = torchvision.models.resnet50(num_classes=2)
# # model = torchvision.models.vgg19(num_classes=2)
# # model = models.vit_b_32(num_classes=2)
# # model = models.densenet121(num_classes=2)
# model.load_state_dict(torch.load(PATH))
# model

# 提取基于预训练权重的特征
# model = torchvision.models.resnet50(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
model = torchvision.models.vgg19(pretrained=True)
model = model.cuda()

#删除掉模型最后一层或者全连接层的最后一层：
# model = torch.nn.Sequential(*(list(model.children())[:-1])) #resnet
model.classifier=torch.nn.Sequential(*list(model.classifier.children())[:-1]) #densenet,vgg19
# model.heads=torch.nn.Sequential(*list(model.heads.children())[:-1]) #vit
# model.head=torch.nn.Sequential(*list(model.head.children())[:-1]) #swin
new_model=model;new_model = new_model.cuda()
print(new_model)

train_data =  '/root/autodl-tmp/ap/train'  
BATCH_SIZE = 32
train_dataset =  torchvision.datasets.ImageFolder(train_data,transform=test_transform)
train_dl2 = torch.utils.data.DataLoader(train_dataset,batch_size=BATCH_SIZE) #不做shuffle

test_data =  '/root/autodl-tmp/ap/test'  
# test_data =  '/root/autodl-tmp/ap/validation'  
test_dataset =  torchvision.datasets.ImageFolder(test_data,transform=test_transform)
test_dl2 = torch.utils.data.DataLoader(test_dataset, batch_size=BATCH_SIZE)

#GPU版本提取训练集特征
train_labels = []
train_features = []
train_paths = []
for image,label in train_dl2:
    print(label)
    o = new_model(image.cuda()) # 放到cuda中
    o = o.view(o.size(0),-1)
    train_labels.extend(label)
    train_features.extend(o.cpu().data) # 将数据转移到CPU上
    # 清空image_paths列表
    train_paths.clear()
    train_paths.extend(train_dl2.dataset.imgs)

#GPU版本提取测试集特征
test_labels = []
test_features = []
test_paths = []
for image, label in test_dl2:
    o = new_model(image.cuda())
    # o = my_resnet18(im)
    o = o.view(o.size(0), -1)
    test_labels.extend(label)
    test_features.extend(o.cpu().data)
    # 清空image_paths列表
    test_paths.clear()
    test_paths.extend(test_dl2.dataset.imgs)

num_columns = len(test_features[0])# 获取列表的列数
num_rows = len(test_features)# 获取列表的行数
print("列数：", num_columns);print("行数：", num_rows)

#%%提取预训练和非预训练模型的深度学习特征  方法2 封装函数
import torch
import torchvision

def load_model(model_name): #加载预训练权重
    if model_name == 'vgg19':
        model = torchvision.models.vgg19(pretrained=True)
        model.classifier = torch.nn.Sequential(*list(model.classifier.children())[:-1])
    elif model_name == 'resnet50':
        model = torchvision.models.resnet50(pretrained=True)
        model = torch.nn.Sequential(*(list(model.children())[:-1]))
    elif model_name == 'densenet121':
        model = torchvision.models.densenet121(pretrained=True)
        model.classifier = torch.nn.Sequential(*list(model.classifier.children())[:-1])
    elif model_name == 'vit':
        model = torchvision.models.vit_b_32(pretrained=True)        
        model.heads=torch.nn.Sequential(*list(model.heads.children())[:-1])
    elif model_name == 'swin':
        model = torchvision.models.swin_v2_b(pretrained=True)
        model.head = torch.nn.Sequential(*list(model.head.children())[:-1])
    else:
        raise ValueError("Invalid model name")
    return model

# def load_model(model_name, weight_path):#加载自己训练的模型权重
#     if model_name == 'vgg19':
#         model = torchvision.models.vgg19(pretrained=False)
#         model.classifier = torch.nn.Sequential(*list(model.classifier.children())[:-1])
#     elif model_name == 'resnet50':
#         model = torchvision.models.resnet50(pretrained=False)
#         model = torch.nn.Sequential(*(list(model.children())[:-1]))
#     elif model_name == 'densenet121':
#         model = torchvision.models.densenet121(pretrained=False)
#         model.classifier = torch.nn.Sequential(*list(model.classifier.children())[:-1])
#     elif model_name == 'vit':
#         model = torchvision.models.vit_b_32(pretrained=False)
#         model.heads = torch.nn.Sequential(*list(model.heads.children())[:-1])
#     elif model_name == 'swin':
#         model = torchvision.models.swin_v2_b(pretrained=False)
#         model.head = torch.nn.Sequential(*list(model.head.children())[:-1])
#     else:
#         raise ValueError("Invalid model name")
    
#     model.load_state_dict(torch.load(weight_path))
#     return model

def extract_features(model, dataloader):
    features = []
    labels_list = []
    paths = []
    for images, labels in dataloader:
        outputs = model(images.cuda())
        outputs = outputs.view(outputs.size(0), -1)
        features.extend(outputs.cpu().data)
        labels_list.extend(labels)
        paths.extend(dataloader.dataset.imgs)
    return features, labels_list, paths

# 定义数据集和数据加载器
# train_data = '/root/autodl-tmp/hbp/train'
# test_data = '/root/autodl-tmp/hbp/test'
test_data = '/root/autodl-tmp/pp/val'

BATCH_SIZE = 32

test_transform = transforms.Compose([transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

train_dataset = torchvision.datasets.ImageFolder(train_data, transform=test_transform)
train_dl = torch.utils.data.DataLoader(train_dataset, batch_size=BATCH_SIZE)

test_dataset = torchvision.datasets.ImageFolder(test_data, transform=test_transform)
test_dl = torch.utils.data.DataLoader(test_dataset, batch_size=BATCH_SIZE)

# 加载模型
model = load_model('densenet121') #   vit vgg19  resnet50   densenet121
model = model.cuda()

# 提取训练集特征
train_features, train_labels, train_paths = extract_features(model, train_dl)

# 提取测试集特征
test_features, test_labels, test_paths = extract_features(model, test_dl)

num_columns = len(test_features[0])  # 获取特征的列数
num_rows = len(test_features)  # 获取特征的行数
print("列数：", num_columns)
print("行数：", num_rows)

#%%导出上面提取的深度学习特征  方法1
import numpy as np
import pandas as pd

def save_deep_features(deep_features,labels,paths):  
    deep_features= torch.tensor([item.cpu().detach().numpy() for item in deep_features])  
    deep_features= deep_features.tolist()
    deep_features = pd.DataFrame(deep_features)  # 转换为数据框格式    
    labels = np.array(labels)#转换为numpy数组
    labels = labels.tolist()  # 转换为列表格式
    labels = pd.DataFrame(labels)  # 转换为数据框格式
    paths = pd.DataFrame(paths)
    return deep_features,labels,paths

train_features,train_labels,train_paths = save_deep_features(train_features,train_labels,train_paths)
test_features,test_labels,test_paths = save_deep_features(test_features,test_labels,test_paths)
# 合并数据为一个数据框
train_features = pd.concat([train_paths,train_labels,train_features], axis=1)
test_features = pd.concat([test_paths,test_labels,test_features], axis=1)

# 保存数据到Excel文件
# train_features.to_excel('/root/autodl-tmp/ap30/trainfeature2.xlsx', index=False)
# test_features.to_excel('/root/autodl-tmp/ap30/testfeature2.xlsx', index=False)
# train_features.to_csv('/root/autodl-tmp/hbp/densenet121_hbp_train_features.csv', index=False) #/hy-tmp/
# test_features.to_csv('/root/autodl-tmp/hbp/densenet121_hbp_test_features.csv', index=False)
test_features.to_csv('/root/autodl-tmp/hbp/densenet121_hbp_val_features.csv', index=False)

#%% 导出提取的深度学习特征  方法2
train_features #tensor格式的list
# train_features = np.array(train_features) #单维度tensor转numpy
train_features= torch.tensor([item.cpu().detach().numpy() for item in train_features]) #多维度tensor转numpy
train_features= train_features.tolist() #numpy格式转list
train_features = pd.DataFrame(train_features)  #数据框格式
train_features

train_labels #tensor格式的list
train_labels = np.array(train_labels) #单维度tensor转numpy
train_labels= train_labels.tolist() #numpy格式转list
train_labels = pd.DataFrame(train_labels)
train_labels #数据框格式

test_features #tensor格式的list
test_features= torch.tensor([item.cpu().detach().numpy() for item in test_features]) #多维度tensor转numpy
test_features= test_features.tolist() #numpy格式转list
test_features = pd.DataFrame(test_features)

test_labels #tensor格式的list
test_labels = np.array(test_labels) #单维度tensor转numpy
test_labels= test_labels.tolist() #numpy格式转list
test_labels = pd.DataFrame(test_labels)

train_data = pd.concat([train_features, train_labels], axis=1)
test_data = pd.concat([test_features, test_labels], axis=1)
# 保存数据到Excel文件
train_data.to_excel('/root/autodl-tmp/traindata.xlsx', index=False)
test_data.to_excel('/root/autodl-tmp/testdata.xlsx', index=False)

#%%总结的混淆矩阵代码,自己加的代码
#获取测试集的标签
test_loader = torch.utils.data.DataLoader(test_ds, batch_size=10000, shuffle=False)#设置好batchsize一次读完所有label
images,test_labels=next(iter(test_loader))
print(test_labels.shape)

#定义一个预测函数
def get_all_preds(model,train_dl):# model为之前已经训练好的模型，loader为对总数据的迭代器                               
    import numpy as np
    model.eval()
    all_preds=None    
    for batch in tqdm(train_dl):#以遍历的方式，访问所有的loader
                              #这里的batch和读取数据的batch是不一样的
        images,labels = batch #把batch拆分成两个变量
        preds = model(images.to('cuda'))#预测一批batch里的图片
        if all_preds is None :
            all_preds=preds.cpu().detach().numpy()
        else:
            all_preds=np.concatenate((all_preds,preds.cpu().detach().numpy()))
    return all_preds

#得到所有数据的预测值
test_preds = get_all_preds(model, test_dl)#开始对图片做预测
print(test_preds.shape)

##得到测试集每个图片预测输出值，test_preds保存为csv格式
test_pred_value= test_preds.tolist() #numpy格式转list
test_pred_value= pd.DataFrame(test_pred_value)    
test_pred_value #数据框格式
# test_pred_value.to_csv('E:/data/test_pred_value.csv')

# 得到预测的label
test_preds_tensor=torch.Tensor(test_preds)#再次将numpy转为tensor
test_preds_labels = test_preds_tensor.argmax(dim=1)#测试结果，用于和test_labels对比
len(test_preds_labels)

#保存预测的label
test_preds_labels=np.round(test_preds_labels.cpu().detach().numpy(),3)
test_preds_labels= pd.DataFrame(test_preds_labels) #数据框格式   
# test_preds_labels.to_csv('E:/data/test_preds_labels.csv')

#得到每个图片预测概率值
test_probability = torch.nn.functional.softmax(test_preds_tensor,dim=1) #预测概率
test_probability=np.round(test_probability.cpu().detach().numpy(),3)
test_probability_value= pd.DataFrame(test_probability)   #数据框格式 
# test_probability_value.to_csv('E:/data/test_probability_value.csv')

# 计算混淆矩阵和评价指标
from sklearn.metrics import classification_report, confusion_matrix
cm = confusion_matrix(test_labels,test_preds_labels)
cm  #横向相加为真实标签值，竖向相加为预测值
print(classification_report(test_labels,test_preds_labels,digits=3))

#%%混淆矩阵代码
classes=2  #定义分类类别

def plot_confusion_matrix(cm, classes, normalize= False, title='Confusion matrix', cmap=plt.cm.Blues):
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        print("Normalized confusion matrix")
    else:
        print('Confusion matrix, without normalization')
    print(cm)
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    # plt.colorbar(shrink=0.75)  # 调整进度条长度为矩形框的0.75倍
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45,fontsize=14)# 调整"VETC-"和"VETC+"标签的字体大小
    plt.yticks(tick_marks, classes,rotation=90,fontsize=14)
    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() * 0.6  # 将阈值调整为比例的0.5倍
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        plt.text(j, i, format(cm[i, j], fmt), horizontalalignment="center", color="white" if cm[i, j] > thresh else "black")
        # plt.text(j, i, format(cm[i, j], fmt), horizontalalignment="center", color="black" if cm[i, j] > thresh else "black")
    plt.tight_layout()
    plt.ylabel('True label', labelpad=8,fontsize=14)  # 调整标签与轴之间的间距
    plt.xlabel('Predicted label',labelpad=2,fontsize=14)
    
#直接画出矩阵表和矩阵图
names = ('ischemia','necrosis',
         # 'VETC-','label0','label1','label2','label3',
    )
plt.figure(figsize=(6.5,6.5))
plt.subplots_adjust(left=0.2,right=0.8,bottom=0.2,top=0.8)
plt.rcParams['font.size'] = 16
plot_confusion_matrix(cm, names,normalize= True) #True显示的是比例，False显示的是样本数
plt.show()
# plt.savefig("/root/autodl-tmp/cm.tiff")

#%%ROC、PR曲线直接绘图
import scikitplot as skplt
import matplotlib.pyplot as plt

# 创建一个大一些的图像
plt.figure(figsize=(10, 8))

# 绘制ROC曲线代码
skplt.metrics.plot_roc(test_labels,test_probability_value,
                       title='ROC Plot for Resnet50',
                       #Densenet121
                       # classes_to_plot=['class1', 'class2','class3'],
                       cmap='Set1') # cmap 设置调色板

# 在绘制ROC曲线之前添加以下代码
# plt.rcParams["legend.fontsize"] = 3  # 调整图例的字体大小

# 调整图例的位置和大小
plt.legend(loc='lower right', fontsize='xx-small', markerscale=0.1)
plt.show()

# 绘制PR曲线代码
skplt.metrics.plot_precision_recall(test_labels,test_probability_value,
                                    #,ResNet50 Inception_v3,SqueezeNet_v1.0
                                    # classes_to_plot = ([0,'VETC++'],[1,'VETC+'],[2,'VETC-']),
                                    # classes_to_plot= ['VETC++','VETC+','VETC-'],
                                   cmap='Set1')
# 设置图表标题
plt.legend(loc='lower right', fontsize='x-small', markerscale=0.1)
plt.title('PR Curve')
plt.show()

# 绘制校准曲线代码
probas_list = [test_probability_value]
clf_names = ['resnet50']

skplt.metrics.plot_calibration_curve(test_labels,
                                     probas_list,
                                     clf_names)
plt.show()


#%%类激活热力图绘制最终版
#pip install grad-cam
import torchvision.models as models
from pytorch_grad_cam import GradCAM, ScoreCAM, GradCAMPlusPlus, AblationCAM, XGradCAM, EigenCAM
from pytorch_grad_cam.utils.image import show_cam_on_image, deprocess_image, preprocess_image
import cv2
import numpy as np
import torchvision
import torch;import copy
from PIL import Image
import matplotlib.pyplot as plt
import torchvision.transforms as transforms
# import os
# # MacOS系统应该加这行
# os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"
# 1.加载预训练模型
# model = torchvision.models.vgg19(pretrained=True)    
# model = torchvision.models.resnet50(pretrained=True)
# model = torchvision.models.densenet121(pretrained=True)
# model = torchvision.models. vit_b_32(pretrained=True)
model = torchvision.models.efficientnet_b1(pretrained=True)

# 2.加载非训练模型
# PATH = r'd:\data\patch_densenet_model.pth'   
# model = models.resnet50(num_classes=2)
# model = models.densenet121(num_classes=2)
# # model = models.vgg19(num_classes=2)
# # model = models.googlenet(num_classes=2)
# model = models.efficientnet_b5(num_classes=2)
# model.load_state_dict(torch.load(PATH, map_location="cpu"))# 加载新模型的权重
model

#选择cpu或gpu
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# 2.选择目标层
target_layer = [model.features[-1]]  #vgg16 and densenet161
# target_layer = [model.layer4[-1]]   #resnet50
# target_layer = [model.layers[-1]]  #mnasnet1_0
# target_layer = [model.features]  #efficientnet_b1

# 3.输入图像
img_path = r"J:\data\2.jpg"
# img_path =   '/root/autodl-tmp/123.jpg'
img = Image.open(img_path).convert('RGB')
img2 = np.array(img, dtype=np.uint8)
img_float_np = np.float32(img2)/255  #归一化
# img = img.resize((224,224))

# define the torchvision image transforms
transform = torchvision.transforms.Compose([
    torchvision.transforms.ToTensor(),
])

input_tensor = transform(img).to(device)

# 二扩，add a dimension to transform the array into a "batch"
input_tensor = input_tensor.unsqueeze(0)

# 4.初始化GradCAM，包括模型，目标层以及是否使用cuda
# cam = GradCAM(model=model, target_layers=target_layer,use_cuda=True)#False, use_cuda=True
cam = GradCAM(model=model, target_layers=target_layer)

# 5.选定目标类别，如果不设置，则默认为分数最高的那一类
targets = None # targets = [1] #第1类

# 6. 计算cam
grayscale_cam = cam(input_tensor=input_tensor, targets=targets)  

# 加上 aug_smooth=True(应用水平翻转组合，并通过[1.0，1.1，0.9]对图像进行多路复用，使CAM围绕对象居中)
# eigen_smooth=True(去除大量噪声)
# 7.展示热力图并保存, grayscale_cam是一个batch的结果，只能选择一张进行展示
grayscale_cam = grayscale_cam[0,:]

cam_image = show_cam_on_image(img_float_np, grayscale_cam, use_rgb=True)
plt.imshow(cam_image)
# cv2.imwrite(f'k:/2020-2023HCC/579hcc/578hcc/data/1.jpg')
# cv2.imwrite(f'/root/autodl-tmp/123ap-vit.jpg',  cv2.cvtColor(cam_image, cv2.COLOR_BGR2RGB))

##加颜色条的图
import matplotlib.pyplot as plt
fig, ax = plt.subplots()# 创建一个Figure对象和一个Axes对象
heatmap = ax.imshow(cam_image, cmap='jet')# 显示热力图
cbar = fig.colorbar(heatmap)# 添加颜色条
# cbar.ax.set_yticklabels([])  # 将颜色条上的刻度标签设置为空列表
fig.savefig(r'J:\data\vggheatmap.jpg')# 保存图像
# 关闭Figure对象
# plt.close(fig)

# %% ##类激活热力图绘制最终版2  常见的CNN模型
import os
import numpy as np
import torch
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import models
from torchvision import transforms
# from utils import GradCAM, show_cam_on_image, center_crop_img
from pytorch_grad_cam import GradCAM, ScoreCAM, GradCAMPlusPlus, AblationCAM, XGradCAM, EigenCAM
from pytorch_grad_cam.utils.image import show_cam_on_image, deprocess_image, preprocess_image

def main():
    # model = models.mobilenet_v3_large(pretrained=True)
    # target_layers = [model.features[-1]]

    # model = models.vgg16(pretrained=True)
    # target_layers = [model.features]

    # model = models.resnet34(pretrained=True)
    # target_layers = [model.layer4]

    model = models.regnet_y_800mf(pretrained=True)
    target_layers = [model.trunk_output]

    # model = models.efficientnet_b1(pretrained=True)
    # target_layers = [model.features]

    data_transform = transforms.Compose([transforms.ToTensor(),
                                        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
    
    # load image
    img_path = r"J:\data\2.jpg"
    assert os.path.exists(img_path), "file: '{}' dose not exist.".format(img_path)
    img = Image.open(img_path).convert('RGB')
    img = np.array(img, dtype=np.uint8)
    # img = center_crop_img(img, 224)

    # [C, H, W]
    img_tensor = data_transform(img)
    # expand batch dimension
    # [C, H, W] -> [N, C, H, W]
    input_tensor = torch.unsqueeze(img_tensor, dim=0)

    cam = GradCAM(model=model, target_layers=target_layers) #use_cuda=False
    targets = None   # tabby, tabby cat
    # target_category = 254  # pug, pug-dog

    grayscale_cam = cam(input_tensor=input_tensor, targets=targets)

    grayscale_cam = grayscale_cam[0, :]
    visualization = show_cam_on_image(img.astype(dtype=np.float32) / 255.,
                                      grayscale_cam,
                                      use_rgb=True)
     # 显示图像和颜色条
    plt.imshow(visualization)
    # plt.axis('off')  # 关闭坐标轴
    plt.colorbar()   # 添加颜色条
    plt.show()                                 
   
if __name__ == '__main__':
    main()

# %% VIT模型可视化类激活热力图  已成功
import os
import numpy as np
import torch;import torchvision
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
# from utils import GradCAM, show_cam_on_image, center_crop_img
# from vit_model import vit_base_patch16_224
from torchvision import models
from torchvision import transforms
from pytorch_grad_cam import GradCAM, ScoreCAM, GradCAMPlusPlus, AblationCAM, XGradCAM, EigenCAM
from pytorch_grad_cam.utils.image import show_cam_on_image, deprocess_image, preprocess_image
import cv2

class ReshapeTransform:
    def __init__(self, model):
        input_size = model.image_size
        patch_size = model.patch_size
        self.h = input_size // patch_size
        self.w = input_size // patch_size
    def __call__(self, x):
        print("Input shape:", x.shape)
        # remove cls token and reshape
        # [batch_size, num_tokens, token_dim]
        result = x[:, 1:, :].reshape(x.size(0),self.h,self.w,x.size(2))
        # [batch_size, H, W, C] -> [batch, C, H, W]
        result = result.permute(0, 3, 1, 2)
        return result

def main():
    model = models.vit_b_32(num_classes=2)
    weights_path = r'd:\data\patch_vit_model.pth' 
    model.load_state_dict(torch.load(weights_path, map_location="cpu"))
    # model = torchvision.models.vit_b_32(pretrained=True)    
    
    # Since the final classification is done on the class token computed in the last attention block,
    # the output will not be affected by the 14x14 channels in the last layer.
    # The gradient of the output with respect to them, will be 0!
    # 我们应该选择final attention block前面的层，可替换不同的层  
    target_layers =   [model.encoder.layers.encoder_layer_11.ln_1] #[model.encoder.layers.encoder_layer_11.ln_1]
    data_transform = transforms.Compose([transforms.ToTensor(),                                         
                                         transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
  
    # load image                         
    img_path = r"J:\data\2.jpg"
    # img_path = r'k:\2020-2023HCC\579hcc\578hcc\data\文章图\123.jpg'
    assert os.path.exists(img_path), "file: '{}' dose not exist.".format(img_path)
    img = Image.open(img_path).convert('RGB')
    img= np.array(img, dtype=np.uint8)       
    # img_float_np = np.float32(img2)/255  #归一化 
    # img = center_crop_img(img, 224)
    img = cv2.resize(img, dsize=(224, 224))
   
    img_tensor = data_transform(img)# print(img_tensor.shape)   # [C, H, W]
    input_tensor = torch.unsqueeze(img_tensor, dim=0)   # expand batch dimension  # [C, H, W] -> [N, C, H, W]    
    cam = GradCAM(model=model,target_layers=target_layers,use_cuda=False,reshape_transform=ReshapeTransform(model))    
    targets = None   #  # target_category = 254  # dog cat       
    grayscale_cam = cam(input_tensor=input_tensor, targets=targets)    

    grayscale_cam = grayscale_cam[0, :]       
    visualization = show_cam_on_image(img/255., grayscale_cam, use_rgb=True)#
    plt.imshow(visualization)
    plt.show()  
    fig, ax = plt.subplots()# 创建一个Figure对象和一个Axes对象
    heatmap = ax.imshow(visualization, cmap='jet')# 显示热力图
    cbar = fig.colorbar(heatmap)# 添加颜色条
    cv2.imwrite(f'k:/2020-2023HCC/579hcc/578hcc/data/heatmap24.jpg',  cv2.cvtColor(visualization, cv2.COLOR_BGR2RGB))

if __name__ == '__main__':
    main()

#%% swinVIT可视化  已成功
import torch.nn.functional as F
import os
from torchvision import models
import numpy as np
import torch;import torchvision
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
# from utils import GradCAM, show_cam_on_image, center_crop_img
# from swin_model import swin_base_patch4_window7_224
from pytorch_grad_cam import GradCAM, ScoreCAM, GradCAMPlusPlus, AblationCAM, XGradCAM, EigenCAM
from pytorch_grad_cam.utils.image import show_cam_on_image, deprocess_image, preprocess_image
import cv2
from einops import rearrange

def reshape_transform(tensor, height=7, width=7):
    '''
    不同参数的Swin网络的height和width是不同的,具体需要查看所对应的配置文件yaml
    height = width = config.DATA.IMG_SIZE / config.MODEL.NUM_HEADS[-1]
    比如该例子中IMG_SIZE: 384  NUM_HEADS: [ 4, 8, 16, 32 ]   
    height = width = 224 / 32 = 7
    '''
    result = F.interpolate(tensor, size=(height, width), mode='bilinear', align_corners=False)
    return result

def main():
    # 注意输入的图片必须是32的整数倍
    # 否则由于padding的原因会出现注意力飘逸的问题
    img_size = 224
    assert img_size % 32 == 0
    model = models.swin_v2_b(num_classes=2)    
    weights_path =  r"d:\data\patch_swin_model2.pth"
    model.load_state_dict(torch.load(weights_path, map_location="cpu"))    
    # model = torchvision.models.swin_v2_b(pretrained=True) 
    print(model)
    target_layers = [model.norm]  #可替换不同的层
    # target_layers =  [model.features[7][1].norm2]#可替换不同的层

    data_transform = transforms.Compose([transforms.ToTensor(),
                                         transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])
    # load image
    img_path = r"d:\data\图片1.tif"
    assert os.path.exists(img_path), "file: '{}' dose not exist.".format(img_path)
    img = Image.open(img_path).convert('RGB')
    img = np.array(img, dtype=np.uint8)
    # img = center_crop_img(img, img_size)
    img = cv2.resize(img, dsize=(224, 224))   
    img_tensor = data_transform(img) # [C, H, W]      
    input_tensor = torch.unsqueeze(img_tensor, dim=0)# [C, H, W] -> [N, C, H, W]

    cam = GradCAM(model=model, target_layers=target_layers, reshape_transform=reshape_transform, use_cuda=False)
    targets = None  # tabby, tabby cat  
    grayscale_cam = cam(input_tensor=input_tensor, targets = targets )
    grayscale_cam = grayscale_cam[0, :]
    visualization = show_cam_on_image(img / 255., grayscale_cam, use_rgb=True)
    plt.imshow(visualization)
    plt.show()

    fig, ax = plt.subplots()# 创建一个Figure对象和一个Axes对象
    heatmap = ax.imshow(visualization, cmap='jet')# 显示热力图
    cbar = fig.colorbar(heatmap)# 添加颜色条

if __name__ == '__main__':
    main()

#%%单个模型评价指标计算
#计算模型的AUC、95% CI、准确率、敏感性、特异性、阳性预测值、阴性预测值、精确率、召回率和F1分数
# 数据格式如下：
# Image Path	True Labels	Predict_label	Probability
# baizhengqiang  	0	      0	       [0.6307259202003479, 0.3692740797996521]
# caimingyao	    1	      1	       [0.20197848975658417, 0.7980214953422546]
# caopeide	        0	      0	       [0.7363142967224121, 0.2636857330799103]

import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.utils import resample
from openpyxl import Workbook

# 假设您的数据保存在名为 data.csv 的文件中
data = pd.read_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\数据结果\test_swin3期融合.xlsx')

# 提取真实标签和预测标签以及概率
y_true = data['True_label']
y_pred = data['Predict_label']
probabilities = data['Probability'].apply(eval).apply(lambda x: x[1])

# 计算AUC和95% CI 方法1
roc_auc = roc_auc_score(y_true, probabilities)
ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
ci_lower = roc_auc - ci
ci_upper = roc_auc + ci

# 计算AUC及其95%置信区间（基于标准误差和t分布） 方法2
# fpr, tpr, thresholds = roc_curve(y_true, probabilities)
# roc_auc = auc(fpr, tpr)
# n = len(probabilities)
# std_err = sem(probabilities)
# h = std_err * t.ppf((1 + 0.95) / 2, n - 1)
# ci_lower = max(0, roc_auc - h)
# ci_upper = min(1, roc_auc + h)

# 计算混淆矩阵以获得其他指标
tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

# 计算准确率，敏感性，特异性，阳性预测值，阴性预测值，精确率，召回率，f1分数
accuracy = accuracy_score(y_true, y_pred)
sensitivity = recall_score(y_true, y_pred)
specificity = tn / (tn + fp)
ppv = precision_score(y_true, y_pred)
npv = tn / (tn + fn)
precision = ppv
recall = sensitivity
f1 = f1_score(y_true, y_pred)

# 将结果保存到Excel文件中
results = {
    'Metric': ['AUC', '95% CI Lower', '95% CI Upper', 'Accuracy', 'Sensitivity', 'Specificity', 'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'],
    'Value': [roc_auc, ci_lower, ci_upper, accuracy, sensitivity, specificity, ppv, npv, precision, recall, f1]
}

df_results = pd.DataFrame(results).T
df_results.columns = df_results.iloc[0]
df_results = df_results[1:]

# 保存到Excel文件
df_results.to_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\数据结果\test_result.xlsx', index=False)

#%% 2分类-多个模型评价指标批量计算
import pandas as pd
import numpy as np
from sklearn.metrics import roc_curve, auc, roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os
from scipy.stats import sem, t

# 目标文件夹路径
folder_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\3d深度学习模型结果\3d模型最终结果\最终结果'
output_path = os.path.join(folder_path, 'result_summary.xlsx')


# 初始化结果列表
results_list = []

# 遍历文件夹中的所有Excel文件
for filename in os.listdir(folder_path):
    if filename.endswith('.xlsx') and filename != 'result_summary最终版含95%CI和比例.xlsx':
        file_path = os.path.join(folder_path, filename)
        
        # 读取数据
        data = pd.read_excel(file_path)

        # 提取真实标签和预测标签以及概率
        y_true = data['True_label']
        y_pred = data['Predict_label']
        probabilities = data['Probability'].apply(eval).apply(lambda x: x[1])

        # 计算AUC和95% CI 方法1
        roc_auc = roc_auc_score(y_true, probabilities)
        ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
        ci_lower = roc_auc - ci
        ci_upper = roc_auc + ci

        # 计算AUC及其95%置信区间（基于标准误差和t分布） 方法2        
        # roc_auc = roc_auc_score(y_true, probabilities)
        # n = len(probabilities)
        # std_err = sem(probabilities)
        # h = std_err * t.ppf((1 + 0.95) / 2, n - 1)
        # ci_lower = max(0, roc_auc - h)
        # ci_upper = min(1, roc_auc + h)

        # 计算混淆矩阵以获得其他指标
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

        # 计算准确率，敏感性，特异性，阳性预测值，阴性预测值，精确率，召回率，f1分数
        accuracy = accuracy_score(y_true, y_pred)
        sensitivity = recall_score(y_true, y_pred)
        specificity = tn / (tn + fp)        
        ppv = precision_score(y_true, y_pred)
        npv = tn / (tn + fn)
        precision = ppv
        recall = sensitivity
        f1 = f1_score(y_true, y_pred)

        # 计算比例
        accuracy_ratio = f"{accuracy:.4f} "
        sensitivity_ratio = f"{sensitivity:.4f} "
        specificity_ratio = f"{specificity:.4f} "
        ppv_ratio = f"{ppv:.4f}"
        npv_ratio = f"{npv:.4f}"
        precision_ratio = ppv_ratio
        recall_ratio = sensitivity_ratio
        f1_ratio = f"{f1:.4f}"

        # 添加结果到列表
        results_list.append([
            filename, 
            # f"{roc_auc:.4f}", f"{ci_lower:.4f}", f"{ci_upper:.4f}", 
            f"{roc_auc:.3f} ({ci_lower:.3f}-{ci_upper:.3f})",
            accuracy_ratio, sensitivity_ratio, specificity_ratio, 
            ppv_ratio, npv_ratio, precision_ratio, recall_ratio, f1_ratio
        ])

# 创建结果DataFrame
results_df = pd.DataFrame(results_list, columns=[
    'Filename', 'AUC(95%CI)', 'Accuracy', 'Sensitivity', 'Specificity', 
    'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'
])

# 保存结果到Excel文件
results_df.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%% 2分类-多个模型评价指标批量计算,每个指标对应的比例（分子/分母）一起保存到结果文件中
import pandas as pd
import numpy as np
from sklearn.metrics import roc_curve,roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os

# 目标文件夹路径
folder_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\3d深度学习模型结果\3d模型最终结果\最终结果'
output_path = os.path.join(folder_path, 'result_summary.xlsx')

# 初始化结果列表
results_list = []

# 遍历文件夹中的所有Excel文件
for filename in os.listdir(folder_path):
    if filename.endswith('.xlsx') and filename != 'result_summary.xlsx':
        file_path = os.path.join(folder_path, filename)
        
        # 读取数据
        data = pd.read_excel(file_path)

        # 提取真实标签和预测标签以及概率
        y_true = data['True_label']
        y_pred = data['Predict_label']
        probabilities = data['Probability'].apply(eval).apply(lambda x: x[1])

        # 计算AUC和95% CI 方法1
        roc_auc = roc_auc_score(y_true, probabilities)
        ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
        ci_lower = roc_auc - ci
        ci_upper = roc_auc + ci

        # 计算AUC及其95%置信区间（基于标准误差和t分布） 方法2        
        # roc_auc = roc_auc_score(y_true, probabilities)
        # n = len(probabilities)
        # std_err = sem(probabilities)
        # h = std_err * t.ppf((1 + 0.95) / 2, n - 1)
        # ci_lower = max(0, roc_auc - h)
        # ci_upper = min(1, roc_auc + h)

        # 计算混淆矩阵以获得其他指标
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

        # 计算准确率，敏感性，特异性，阳性预测值，阴性预测值，精确率，召回率，f1分数
        accuracy = accuracy_score(y_true, y_pred)
        sensitivity = recall_score(y_true, y_pred)
        specificity = tn / (tn + fp)        
        ppv = precision_score(y_true, y_pred)
        npv = tn / (tn + fn)
        precision = ppv
        recall = sensitivity
        f1 = f1_score(y_true, y_pred)

        # 计算比例
        accuracy_ratio = f"{accuracy:.4f} ({sum(y_true == y_pred)}/{len(y_true)})"
        sensitivity_ratio = f"{sensitivity:.4f} ({tp}/{tp + fn})"
        specificity_ratio = f"{specificity:.4f} ({tn}/{tn + fp})"
        ppv_ratio = f"{ppv:.4f} ({tp}/{tp + fp})"
        npv_ratio = f"{npv:.4f} ({tn}/{tn + fn})"
        precision_ratio = ppv_ratio
        recall_ratio = sensitivity_ratio
        f1_ratio = f"{f1:.4f} ({2 * tp}/{2 * tp + fp + fn})"

        # 添加结果到列表
        results_list.append([
            filename, 
            # f"{roc_auc:.4f}", f"{ci_lower:.4f}", f"{ci_upper:.4f}", 
            f"{roc_auc:.3f} ({ci_lower:.3f}-{ci_upper:.3f})",
            accuracy_ratio, sensitivity_ratio, specificity_ratio, 
            ppv_ratio, npv_ratio, precision_ratio, recall_ratio, f1_ratio
        ])

# 创建结果DataFrame
results_df = pd.DataFrame(results_list, columns=[
    'Filename', 'AUC(95%CI)', 'Accuracy', 'Sensitivity', 'Specificity', 
    'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'
])

# 保存结果到Excel文件
results_df.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%% 2分类-多个模型评价指标批量计算,每个指标95%CI和比例（分子/分母）一起保存如敏感性（95CI）[分子/分母]
# 使用Wilson score interval方法计算二项比例的置信区间
import pandas as pd
import numpy as np
from sklearn.metrics import roc_curve,roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os
from scipy.stats import norm,sem

# 目标文件夹路径
folder_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\3d深度学习模型结果\3d模型最终结果\最终结果'
output_path = os.path.join(folder_path, 'result_summary2.xlsx')

# 初始化结果列表
results_list = []

# 定义计算置信区间的函数
def wilson_score_interval(count, nobs, alpha=0.05):
    """ Calculate the Wilson score interval for a binomial proportion """
    z = norm.ppf(1 - alpha / 2)
    phat = count / nobs
    denominator = 1 + z**2 / nobs
    center = (phat + z**2 / (2 * nobs)) / denominator
    margin = z * np.sqrt((phat * (1 - phat) + z**2 / (4 * nobs)) / nobs) / denominator
    return center - margin, center + margin

# 遍历文件夹中的所有Excel文件
for filename in os.listdir(folder_path):
    if filename.endswith('.xlsx') and filename != 'result_summary.xlsx':
        file_path = os.path.join(folder_path, filename)
        
        # 读取数据
        data = pd.read_excel(file_path)

        # 提取真实标签和预测标签以及概率
        y_true = data['True_label']
        y_pred = data['Predict_label']
        probabilities = data['Probability'].apply(eval).apply(lambda x: x[1])

        # 计算AUC
        roc_auc = roc_auc_score(y_true, probabilities)

        # # 计算AUC的95% CI CI 方法1
        # ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
        # roc_ci_lower = roc_auc - ci
        # roc_ci_upper = roc_auc + ci   

        # 计算AUC的95% CI 方法2
        std_err = sem(probabilities)
        auc_ci = norm.interval(0.95, loc=roc_auc, scale=std_err)
        roc_ci_lower = auc_ci[0]
        roc_ci_upper = auc_ci[1]

        # 计算AUC及其95%置信区间（基于标准误差和t分布） 方法3=方法2        
        # roc_auc = roc_auc_score(y_true, probabilities)
        # n = len(probabilities)
        # std_err = sem(probabilities)
        # h = std_err * t.ppf((1 + 0.95) / 2, n - 1)
        # roc_ci_lower = max(0, roc_auc - h)
        # roc_ci_lower = min(1, roc_auc + h)             

        # 计算混淆矩阵以获得其他指标
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()

        # 计算并存储所有指标和其95% CI
        metrics = {
            'Accuracy': (accuracy_score(y_true, y_pred), sum(y_true == y_pred), len(y_true)),
            'Sensitivity': (recall_score(y_true, y_pred), tp, tp + fn),
            'Specificity': (tn / (tn + fp), tn, tn + fp),
            'Positive Predictive Value': (precision_score(y_true, y_pred), tp, tp + fp),
            'Negative Predictive Value': (tn / (tn + fn), tn, tn + fn),
        }

        results = {}
        for name, (score, count, nobs) in metrics.items():
            ci_lower, ci_upper = wilson_score_interval(count, nobs)
            ratio = f"{score:.4f} ({ci_lower:.4f}, {ci_upper:.4f}) [{count}/{nobs}]"
            results[name] = ratio

        # 计算精确率，召回率，f1分数
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        f1 = f1_score(y_true, y_pred)
        precision_ci_lower, precision_ci_upper = wilson_score_interval(tp, tp + fp)
        recall_ci_lower, recall_ci_upper = wilson_score_interval(tp, tp + fn)
        f1_ci_lower, f1_ci_upper = wilson_score_interval(2 * tp, 2 * tp + fp + fn)

        precision_ratio = f"{precision:.4f} ({precision_ci_lower:.4f}, {precision_ci_upper:.4f}) [{tp}/{tp + fp}]"
        recall_ratio = f"{recall:.4f} ({recall_ci_lower:.4f}, {recall_ci_upper:.4f}) [{tp}/{tp + fn}]"
        f1_ratio = f"{f1:.4f} ({f1_ci_lower:.4f}, {f1_ci_upper:.4f}) [{2 * tp}/{2 * tp + fp + fn}]"

        # 添加结果到列表
        results_list.append([
            filename, 
            f"{roc_auc:.3f} ({roc_ci_lower:.3f}-{roc_ci_upper:.3f})",
            results['Accuracy'], results['Sensitivity'], results['Specificity'], 
            results['Positive Predictive Value'], results['Negative Predictive Value'], 
            precision_ratio, recall_ratio, f1_ratio
        ])

# 创建结果DataFrame
results_df = pd.DataFrame(results_list, columns=[
    'Filename', 'AUC(95%CI)', 'Accuracy', 'Sensitivity', 'Specificity', 
    'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'
])

# 保存结果到Excel文件
results_df.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%% 多分类-多个模型评价指标批量计算
import pandas as pd
import numpy as np
from sklearn.metrics import roc_curve,roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os

# 目标文件夹路径
folder_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\3d深度学习模型结果\12'
output_path = os.path.join(folder_path, 'result_summary.xlsx')

# 初始化结果列表
results_list = []

# 遍历文件夹中的所有Excel文件
for filename in os.listdir(folder_path):
    if filename.endswith('.xlsx') and filename != 'result_summary.xlsx':
        file_path = os.path.join(folder_path, filename)
        
        # 读取数据
        data = pd.read_excel(file_path)

        # 提取真实标签和预测标签以及概率
        y_true = data['True_label']
        y_pred = data['Predict_label']
        probabilities = np.array(data['Probability'].apply(eval).tolist())

        # 打印 y_true 的分布，确认数据完整性
        print(f"Processing file: {filename}")
        print("Class distribution in y_true:")
        print(y_true.value_counts())

        # 确认 classes 和 y_true 的正确性
        classes = np.unique(y_true)
        y_true_bin = np.zeros((y_true.shape[0], len(classes)))
        for i, label in enumerate(y_true):
            y_true_bin[i, classes.tolist().index(label)] = 1

        print("y_true_bin shape:", y_true_bin.shape)

        # 确保 probabilities 数组的维度是否正确
        print("Shape of probabilities array:", probabilities.shape)

        # 计算每个类别的ROC AUC
        roc_auc = dict()
        for i in range(len(classes)):
            print(f"Calculating AUC for class {classes[i]}")
            try:
                roc_auc[classes[i]] = roc_auc_score(y_true_bin[:, i], probabilities[:, i])
            except ValueError as e:
                print(f"Error calculating AUC for class {classes[i]}: {e}")

        print("ROC AUC:", roc_auc)

        # 计算宏平均和微平均ROC AUC
        roc_auc["macro"] = roc_auc_score(y_true_bin, probabilities, average="macro")
        roc_auc["micro"] = roc_auc_score(y_true_bin, probabilities, average="micro")

        # 计算95% CI
        ci = 1.96 * (roc_auc["macro"] * (1 - roc_auc["macro"]) / len(y_true)) ** 0.5
        ci_lower = roc_auc["macro"] - ci
        ci_upper = roc_auc["macro"] + ci      
       
        # 计算混淆矩阵以获得其他指标
        cm = confusion_matrix(y_true, y_pred)        

        # 计算准确率，精确率，召回率，f1分数
        accuracy = accuracy_score(y_true, y_pred)
        precision_macro = precision_score(y_true, y_pred, average='macro')
        recall_macro = recall_score(y_true, y_pred, average='macro')
        f1_macro = f1_score(y_true, y_pred, average='macro')
        precision_micro = precision_score(y_true, y_pred, average='micro')
        recall_micro = recall_score(y_true, y_pred, average='micro')
        f1_micro = f1_score(y_true, y_pred, average='micro')       

        # 计算每个类别的精确率，召回率，f1分数
        precision_per_class = precision_score(y_true, y_pred, average=None, labels=classes)
        recall_per_class = recall_score(y_true, y_pred, average=None, labels=classes)
        f1_per_class = f1_score(y_true, y_pred, average=None, labels=classes)

        # 构建结果列表
        row = [
            filename, roc_auc["macro"], roc_auc["micro"], ci_lower, ci_upper, 
            accuracy, precision_macro, recall_macro, f1_macro, precision_micro, recall_micro, f1_micro
        ]
        for i in range(len(classes)):
            row.extend([precision_per_class[i], recall_per_class[i], f1_per_class[i], roc_auc[classes[i]]])
        
        # 添加到结果列表
        results_list.append(row)

# 构建列名
columns = [
    'Filename', 'ROC AUC (macro)', 'ROC AUC (micro)', '95% CI Lower', '95% CI Upper', 
    'Accuracy', 'Precision (macro)', 'Recall (macro)', 'F1 Score (macro)',
    'Precision (micro)', 'Recall (micro)', 'F1 Score (micro)'
]
for cls in classes:
    columns.extend([f'Precision (Class {cls})', f'Recall (Class {cls})', f'F1 Score (Class {cls})', f'ROC AUC (Class {cls})'])

# 将结果转换为数据框
df_results = pd.DataFrame(results_list, columns=columns)

# 保存结果到Excel文件
df_results.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%% 多分类-多个模型评价指标批量计算,每个指标对应的比例（分子/分母）一起保存到结果文件中
import pandas as pd
import numpy as np
from sklearn.metrics import roc_curve,roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os

# 目标文件夹路径
folder_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\3d深度学习模型结果\12'
output_path = os.path.join(folder_path, 'result_summary.xlsx')

# 初始化结果列表
results_list = []

# 遍历文件夹中的所有Excel文件
for filename in os.listdir(folder_path):
    if filename.endswith('.xlsx') and filename != 'result_summary.xlsx':
        file_path = os.path.join(folder_path, filename)
        
        # 读取数据
        data = pd.read_excel(file_path)

        # 提取真实标签和预测标签以及概率
        y_true = data['True_label']
        y_pred = data['Predict_label']
        probabilities = np.array(data['Probability'].apply(eval).tolist())

        # 打印 y_true 的分布，确认数据完整性
        print(f"Processing file: {filename}")
        print("Class distribution in y_true:")
        print(y_true.value_counts())

        # 确认 classes 和 y_true 的正确性
        classes = np.unique(y_true)
        y_true_bin = np.zeros((y_true.shape[0], len(classes)))
        for i, label in enumerate(y_true):
            y_true_bin[i, classes.tolist().index(label)] = 1

        print("y_true_bin shape:", y_true_bin.shape)

        # 确保 probabilities 数组的维度是否正确
        print("Shape of probabilities array:", probabilities.shape)

        # 计算每个类别的ROC AUC
        roc_auc = dict()
        for i in range(len(classes)):
            print(f"Calculating AUC for class {classes[i]}")
            try:
                roc_auc[classes[i]] = roc_auc_score(y_true_bin[:, i], probabilities[:, i])
            except ValueError as e:
                print(f"Error calculating AUC for class {classes[i]}: {e}")

        print("ROC AUC:", roc_auc)

        # 计算宏平均和微平均ROC AUC
        roc_auc["macro"] = roc_auc_score(y_true_bin, probabilities, average="macro")
        roc_auc["micro"] = roc_auc_score(y_true_bin, probabilities, average="micro")

        # 计算95% CI
        ci = 1.96 * (roc_auc["macro"] * (1 - roc_auc["macro"]) / len(y_true)) ** 0.5
        ci_lower = roc_auc["macro"] - ci
        ci_upper = roc_auc["macro"] + ci

        # 计算混淆矩阵以获得其他指标
        cm = confusion_matrix(y_true, y_pred)

        # 计算准确率，精确率，召回率，f1分数
        accuracy = accuracy_score(y_true, y_pred)
        accuracy_ratio = f"{accuracy:.3f} ({sum(y_true == y_pred)}/{len(y_true)})"

        precision_macro = precision_score(y_true, y_pred, average='macro')
        recall_macro = recall_score(y_true, y_pred, average='macro')
        f1_macro = f1_score(y_true, y_pred, average='macro')

        precision_micro = precision_score(y_true, y_pred, average='micro')
        recall_micro = recall_score(y_true, y_pred, average='micro')
        f1_micro = f1_score(y_true, y_pred, average='micro')

        # 构建结果列表
        row = [
            filename, roc_auc["macro"], roc_auc["micro"], ci_lower, ci_upper, 
            accuracy_ratio, f"{precision_macro:.3f}", f"{recall_macro:.3f}", f"{f1_macro:.3f}", 
            f"{precision_micro:.3f}", f"{recall_micro:.3f}", f"{f1_micro:.3f}"
        ]
        
        for cls in classes:
            precision = precision_score(y_true, y_pred, labels=[cls], average=None)
            recall = recall_score(y_true, y_pred, labels=[cls], average=None)
            f1 = f1_score(y_true, y_pred, labels=[cls], average=None)

            precision_ratio = f"{precision[0]:.3f} ({sum((y_true == y_pred) & (y_pred == cls))}/{sum(y_pred == cls)})"
            recall_ratio = f"{recall[0]:.3f} ({sum((y_true == y_pred) & (y_true == cls))}/{sum(y_true == cls)})"
            f1_ratio = f"{f1[0]:.3f} ({sum((y_true == y_pred) & (y_pred == cls))}/{sum(y_pred == cls)})"
            
            row.extend([precision_ratio, recall_ratio, f1_ratio, roc_auc[cls]])
        
        # 添加到结果列表
        results_list.append(row)

# 构建列名
columns = [
    'Filename', 'ROC AUC (macro)', 'ROC AUC (micro)', '95% CI Lower', '95% CI Upper', 
    'Accuracy', 'Precision (macro)', 'Recall (macro)', 'F1 Score (macro)',
    'Precision (micro)', 'Recall (micro)', 'F1 Score (micro)'
]
for cls in classes:
    columns.extend([f'Precision (Class {cls})', f'Recall (Class {cls})', f'F1 Score (Class {cls})', f'ROC AUC (Class {cls})'])

# 将结果转换为数据框
df_results = pd.DataFrame(results_list, columns=columns)

# 保存结果到Excel文件
df_results.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%% 单个excel文件中多个模型2分类评价指标计算如AUC、敏感性、特异性等结果
# 使用Wilson score interval方法计算二项比例的置信区间
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from scipy.stats import norm, sem
import os

# 单个Excel文件路径
file_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\模型比较\val模型预测概率汇总.xlsx'
output_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\模型比较\roc_result.xlsx'

# 读取数据
data = pd.read_excel(file_path)
print(data.columns)

# 提取真实标签
y_true = data['truelabel']
# 提取模型预测概率列，假设它们在第10列之后
model_predictions = data.columns[5:10]
print(model_predictions)

# 初始化结果列表
results_list = []

# 定义计算置信区间的函数
def wilson_score_interval(count, nobs, alpha=0.05):
    """ Calculate the Wilson score interval for a binomial proportion """
    z = norm.ppf(1 - alpha / 2)
    phat = count / nobs
    denominator = 1 + z**2 / nobs
    center = (phat + z**2 / (2 * nobs)) / denominator
    margin = z * np.sqrt((phat * (1 - phat) + z**2 / (4 * nobs)) / nobs) / denominator
    return center - margin, center + margin

# 遍历所有模型列
for col in model_predictions:
    y_pred_prob = data[col]
    y_pred = (y_pred_prob >= 0.5).astype(int)
    
    # 计算AUC
    roc_auc = roc_auc_score(y_true, y_pred_prob)
    
    # 计算AUC的95% CI
    std_err = sem(y_pred_prob)
    auc_ci = norm.interval(0.95, loc=roc_auc, scale=std_err)
    roc_ci_lower = auc_ci[0]
    roc_ci_upper = auc_ci[1]

    #计算AUC的95% CI CI 方法1
    # ci = 1.96 * (roc_auc * (1 - roc_auc) / len(y_true)) ** 0.5
    # roc_ci_lower = roc_auc - ci
    # roc_ci_upper = roc_auc + ci   
    
    # 计算混淆矩阵以获得其他指标
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    
    # 计算并存储所有指标和其95% CI
    metrics = {
        'Accuracy': (accuracy_score(y_true, y_pred), sum(y_true == y_pred), len(y_true)),
        'Sensitivity': (recall_score(y_true, y_pred), tp, tp + fn),
        'Specificity': (tn / (tn + fp), tn, tn + fp),
        'Positive Predictive Value': (precision_score(y_true, y_pred), tp, tp + fp),
        'Negative Predictive Value': (tn / (tn + fn), tn, tn + fn),
    }
    
    results = {}
    for name, (score, count, nobs) in metrics.items():
        ci_lower, ci_upper = wilson_score_interval(count, nobs)
        ratio = f"{score:.4f} ({ci_lower:.4f}, {ci_upper:.4f}) [{count}/{nobs}]"
        results[name] = ratio
    
    # 计算精确率，召回率，f1分数
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    precision_ci_lower, precision_ci_upper = wilson_score_interval(tp, tp + fp)
    recall_ci_lower, recall_ci_upper = wilson_score_interval(tp, tp + fn)
    f1_ci_lower, f1_ci_upper = wilson_score_interval(2 * tp, 2 * tp + fp + fn)
    
    precision_ratio = f"{precision:.4f} ({precision_ci_lower:.4f}, {precision_ci_upper:.4f}) [{tp}/{tp + fp}]"
    recall_ratio = f"{recall:.4f} ({recall_ci_lower:.4f}, {recall_ci_upper:.4f}) [{tp}/{tp + fn}]"
    f1_ratio = f"{f1:.4f} ({f1_ci_lower:.4f}, {f1_ci_upper:.4f}) [{2 * tp}/{2 * tp + fp + fn}]"
    
    # 添加结果到列表
    results_list.append([
        col, 
        f"{roc_auc:.3f} ({roc_ci_lower:.3f}-{roc_ci_upper:.3f})",
        results['Accuracy'], results['Sensitivity'], results['Specificity'], 
        results['Positive Predictive Value'], results['Negative Predictive Value'], 
        precision_ratio, recall_ratio, f1_ratio
    ])

# 创建结果DataFrame
results_df = pd.DataFrame(results_list, columns=[
    'Model', 'AUC(95%CI)', 'Accuracy', 'Sensitivity', 'Specificity', 
    'Positive Predictive Value', 'Negative Predictive Value', 'Precision', 'Recall', 'F1 Score'
])

# 保存结果到Excel文件
results_df.to_excel(output_path, index=False)

print(f'Results saved to {output_path}')

#%%单个模型评价指标计算,多分类
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score
from sklearn.metrics import f1_score, confusion_matrix
from sklearn.preprocessing import label_binarize
import ast

# 读取数据
data_path = r"K:\2023大创项目\大创结题\test_swinvit.xlsx"
data = pd.read_excel(data_path)
print(data.columns)

# 使用 os.path.dirname() 获取文件所在的目录路径
dir_path = os.path.dirname(data_path)
print("Directory path:", dir_path)

# 提取真实标签和预测标签
y_true = data['True Label2']
y_pred = data['Predict Label2']

# 处理预测概率 - 转换字符串格式的概率为数组
def parse_probabilities(prob_str):
    return ast.literal_eval(prob_str)
probabilities = np.array([parse_probabilities(p) for p in data['Predict Prob2']])

# 计算多分类AUC（macro和micro）
# 将标签转换为二值化格式
n_classes = 3
y_true_bin = label_binarize(y_true, classes=range(n_classes))

# 计算macro和micro AUC
macro_auc = roc_auc_score(y_true_bin, probabilities, average='macro', multi_class='ovr')
micro_auc = roc_auc_score(y_true_bin, probabilities, average='micro', multi_class='ovr')

# 计算95% CI
n_bootstraps = 1000
auc_bootstraps = []
rng = np.random.RandomState(42)

for i in range(n_bootstraps):
    indices = rng.randint(0, len(y_true), len(y_true))
    if len(np.unique(y_true[indices])) < n_classes:
        continue
    macro_auc_boot = roc_auc_score(y_true_bin[indices], probabilities[indices], 
                                  average='macro', multi_class='ovr')
    auc_bootstraps.append(macro_auc_boot)

ci_lower = np.percentile(auc_bootstraps, 2.5)
ci_upper = np.percentile(auc_bootstraps, 97.5)

# 计算每个类别的指标
# 使用macro平均（对每个类别单独计算后取平均）
accuracy = accuracy_score(y_true, y_pred)
precision_macro = precision_score(y_true, y_pred, average='macro')
recall_macro = recall_score(y_true, y_pred, average='macro')
f1_macro = f1_score(y_true, y_pred, average='macro')

# 计算混淆矩阵
cm = confusion_matrix(y_true, y_pred)

# 计算每个类别的特异性
specificities = []
for i in range(n_classes):
    # 计算真阴性（其他类别被正确分类为非当前类别的数量）
    tn = np.sum(cm) - np.sum(cm[i,:]) - np.sum(cm[:,i]) + cm[i,i]
    # 计算假阳性（其他类别被错误分类为当前类别的数量）
    fp = np.sum(cm[:,i]) - cm[i,i]
    # 计算特异性
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    specificities.append(specificity)

# 计算每个类别的PPV和NPV
ppv = []
npv = []
for i in range(n_classes):
    # PPV (Precision)
    tp = cm[i,i]
    fp = np.sum(cm[:,i]) - cm[i,i]
    ppv_i = tp / (tp + fp) if (tp + fp) > 0 else 0
    ppv.append(ppv_i)
    
    # NPV
    tn = np.sum(cm) - np.sum(cm[i,:]) - np.sum(cm[:,i]) + cm[i,i]
    fn = np.sum(cm[i,:]) - cm[i,i]
    npv_i = tn / (tn + fn) if (tn + fn) > 0 else 0
    npv.append(npv_i)

# 创建结果字典
results = {
    'Metric': [
        'Macro AUC', 'Micro AUC', '95% CI Lower', '95% CI Upper',
        'Accuracy',
        'Macro Precision', 'Macro Recall', 'Macro F1',
        'Class 0 Specificity', 'Class 1 Specificity', 'Class 2 Specificity',
        'Class 0 PPV', 'Class 1 PPV', 'Class 2 PPV',
        'Class 0 NPV', 'Class 1 NPV', 'Class 2 NPV'
    ],
    'Value': [
        macro_auc, micro_auc, ci_lower, ci_upper,
        accuracy,
        precision_macro, recall_macro, f1_macro,
        specificities[0], specificities[1], specificities[2],
        ppv[0], ppv[1], ppv[2],
        npv[0], npv[1], npv[2]
    ]
}

# 转换为DataFrame并保存
df_results = pd.DataFrame(results)
df_results.to_excel(os.path.join(dir_path, "test_swinvit结果.xlsx"), index=False)

# 打印主要结果
print(f"Macro AUC: {macro_auc:.3f} (95% CI: {ci_lower:.3f}-{ci_upper:.3f})")
print(f"Micro AUC: {micro_auc:.3f}")
print(f"Accuracy: {accuracy:.3f}")
print(f"Macro Precision: {precision_macro:.3f}")
print(f"Macro Recall: {recall_macro:.3f}")
print(f"Macro F1: {f1_macro:.3f}")
print("\nConfusion Matrix:")
print(cm)
# %%
