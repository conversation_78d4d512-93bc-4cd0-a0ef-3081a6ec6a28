#!/usr/bin/env python
# coding: utf-8

import os
import nibabel as nib
import pandas as pd

# 指定图像路径
image_dir = '/root/autodl-tmp/HCC503ap/ap'
mask_dir = '/root/autodl-tmp/HCC503ap/apmask'

# 存储维度信息的列表
dimensions_data = []

print("开始检查图像和掩码文件维度:")
print("-" * 100)
print(f"{'图像文件名':<30} {'掩码文件名':<30} {'图像维度':<20} {'掩码维度':<20} {'匹配?':<5}")
print("-" * 100)

# 获取所有图像文件
image_files = sorted([f for f in os.listdir(image_dir) if f.endswith('.nii.gz')])
mask_files = sorted([f for f in os.listdir(mask_dir) if f.endswith('.nii.gz')])

# 映射文件名
# 通常从"name-ap.nii.gz"到"name-ap-mask.nii.gz"
name_to_mask = {}
for mask_file in mask_files:
    # 提取基本名称（去掉"-mask"部分）
    if "-mask.nii.gz" in mask_file:
        base_name = mask_file.replace("-mask.nii.gz", ".nii.gz")
        name_to_mask[base_name] = mask_file

# 遍历所有图像文件
count = 0
processed_pairs = 0

for img_filename in image_files:
    # 尝试找到匹配的掩码文件
    mask_filename = None
    
    # 直接尝试通过替换"-ap.nii.gz"为"-ap-mask.nii.gz"
    if img_filename.endswith("-ap.nii.gz"):
        candidate_mask = img_filename.replace("-ap.nii.gz", "-ap-mask.nii.gz")
        if candidate_mask in mask_files:
            mask_filename = candidate_mask
    
    # 如果没找到，再尝试通过基本名映射查找
    if not mask_filename and img_filename in name_to_mask:
        mask_filename = name_to_mask[img_filename]
        
    # 如果找到了匹配的掩码文件
    if mask_filename:
        try:
            # 加载图像
            img_path = os.path.join(image_dir, img_filename)
            img = nib.load(img_path)
            
            # 获取原始图像数据和维度
            img_data = img.get_fdata()
            img_shape = img_data.shape
            
            # 加载掩码
            mask_path = os.path.join(mask_dir, mask_filename)
            mask = nib.load(mask_path)
            
            # 获取原始掩码数据和维度
            mask_data = mask.get_fdata()
            mask_shape = mask_data.shape
            
            # 检查维度是否匹配
            shapes_match = img_shape == mask_shape
            
            # 打印处理前后的形状对比
            print(f"{img_filename:<30} {mask_filename:<30} {str(img_shape):<20} {str(mask_shape):<20} {'是' if shapes_match else '否'}")
            
            # 存储数据用于后续分析
            dimensions_data.append({
                'image_filename': img_filename,
                'mask_filename': mask_filename,
                'img_shape': str(img_shape),
                'mask_shape': str(mask_shape),
                'shapes_match': shapes_match,
                'img_elements': img_data.size,
                'mask_elements': mask_data.size
            })
            
            processed_pairs += 1
            
            count += 1
            if count >= 500:  # 只显示前10个作为示例
                break
                
        except Exception as e:
            print(f"处理文件对 {img_filename} / {mask_filename} 时出错: {str(e)}")
    else:
        print(f"无法找到 {img_filename} 对应的掩码文件")

print("-" * 100)
print(f"处理了 {processed_pairs} 个文件对")

# 创建DataFrame并分析
if dimensions_data:
    df = pd.DataFrame(dimensions_data)
    
    # 查找不匹配的文件
    mismatched_files = df[df['shapes_match'] == False]
    
    print(f"\n总文件对数: {len(df)}")
    print(f"维度不匹配的文件对数: {len(mismatched_files)}")
    
    if len(mismatched_files) > 0:
        print("\n维度不匹配的文件:")
        for idx, row in mismatched_files.iterrows():
            print(f"{row['image_filename']} - {row['mask_filename']}")
            print(f"  图像形状: {row['img_shape']}, 掩码形状: {row['mask_shape']}")
            print(f"  元素数差异: 图像 {row['img_elements']} vs 掩码 {row['mask_elements']}, 差异: {abs(row['img_elements'] - row['mask_elements'])}")
    
    # 保存分析结果到CSV
    csv_path = os.path.join('/root/autodl-tmp/HCC503ap', 'dimension_analysis.csv')
    df.to_csv(csv_path, index=False)
    print(f"\n分析结果已保存到 {csv_path}")
else:
    print("无法处理任何文件对，请检查路径和文件是否存在")

print("\n图像处理完成") 

#%%检查图像和掩码文件是否损坏
import os
import glob
import nibabel as nib
from tqdm import tqdm

# 路径设置
root_dir = '/root/autodl-tmp/HCC503ap'
image_dir = '/root/autodl-tmp/HCC503ap/ap'
mask_dir = '/root/autodl-tmp/HCC503ap/apmask'

print("检查图像文件完整性...")
corrupt_images = []
for i, file_path in enumerate(tqdm(sorted(glob.glob(os.path.join(image_dir, "*.nii.gz"))))):
    try:
        img = nib.load(file_path)
        # 尝试访问数据以确保文件可读
        _ = img.get_fdata()[0, 0, 0]
    except Exception as e:
        corrupt_images.append((file_path, str(e)))
        print(f"\n错误: {file_path} - {e}")

print("\n检查掩码文件完整性...")
corrupt_masks = []
for i, file_path in enumerate(tqdm(sorted(glob.glob(os.path.join(mask_dir, "*.nii.gz"))))):
    try:
        img = nib.load(file_path)
        # 尝试访问数据以确保文件可读
        _ = img.get_fdata()[0, 0, 0]
    except Exception as e:
        corrupt_masks.append((file_path, str(e)))
        print(f"\n错误: {file_path} - {e}")

print("\n结果摘要:")
print(f"总共检查的图像文件: {len(glob.glob(os.path.join(image_dir, '*.nii.gz')))}")
print(f"总共检查的掩码文件: {len(glob.glob(os.path.join(mask_dir, '*.nii.gz')))}")
print(f"损坏的图像文件数量: {len(corrupt_images)}")
print(f"损坏的掩码文件数量: {len(corrupt_masks)}")

if corrupt_images:
    print("\n损坏的图像文件:")
    for path, error in corrupt_images:
        print(f"- {os.path.basename(path)}: {error}")

if corrupt_masks:
    print("\n损坏的掩码文件:")
    for path, error in corrupt_masks:
        print(f"- {os.path.basename(path)}: {error}")

if not corrupt_images and not corrupt_masks:
    print("\n没有发现损坏的文件。问题可能是临时的或与特定的数据加载方式有关。") 