# -*- coding: utf-8 -*-
"""
Created on Thu Jun 30 15:47:20 2022

@author: DELL
"""


import matplotlib.pyplot as plt

import SimpleITK as sitk
from PIL import Image 
import torch 
import torchvision
import os
import numpy as np
import glob 
import pandas as pd
from PIL import Image 
import random 
import time 
import cv2
import torch 
import torch.nn as nn
import torch.nn.functional as F 
from torch.utils.data import Dataset,DataLoader
from torchvision import transforms 
import PIL 

#%%
ct=sitk.ReadImage('E:/3D Slicer/nii/1.AP.nii')
ct_array=sitk.GetArrayFromImage(ct)
ct_array.shape
img=ct_array[150,:,:] #选择第200张图片
img_pic=Image.fromarray(img) #转换为array
plt.imshow(img_pic,cmap='gray')
#%%

seg=sitk.ReadImage('E:/3D Slicer/nii/1.AP.segment.nii')
seg_array=sitk.GetArrayFromImage(seg)
seg_array.shape
np.unique(seg_array)
len(seg_array)
seg_img=seg_array[150,:,:] #选择第200张图片
seg_pic=Image.fromarray(seg_img) #转换为array
plt.imshow(seg_pic,cmap='gray')
np.unique(seg_img)


#%%
#nii格式转为jpg格式

def getImgFromNii(nii_file):
    img=sitk.ReadImage(nii_file)
    img_array=sitk.GetArrayFromImage(img)
    for i in range(len(seg_array)):
        img_select=img_array[i,:,:]
        img_pic=Image.fromarray(img_select)
        img_savename='E:/3D Slicer/nii/1.AP/1.AP-{}.jpg'.format(i)
        plt.imsave(img_savename,img_pic,cmap='gray')

#%%
image_dataset = 'E:/3D Slicer/nii/1.AP.nii'
getImgFromNii(image_dataset)



#%%
img_list=glob.glob('E:/3D Slicer/nii/1.AP/*.jpg')
img_list[0:5]
#%%数组堆叠
def stackImg(img_slice):
    img_read_list=[]
    for i in img_slice:
        img_read=cv2.imread(i)
        img_array=img_read[:,:,0] #取图片任意通道数
        img_read_list.append(img_array)
    img_stack=np.stack(img_read_list,axis=0)    
    return img_stack
#%%
img_for3dnet=stackImg(img_list)
img_for3dnet.shape

#%%输入数据集

images=os.listdir('E:/3D Slicer/nii/image')
labels=os.listdir('E:/3D Slicer/nii/label')

image_list=[]
label_list=[]

for i in images:
    file_path='E:/3D Slicer/nii/image/{}/{}'.format(i,i)
    print(file_path)
    image_list.append(file_path)

for i in labels:
    file_path='E:/3D Slicer/nii/label/{}/{}'.format(i,i)
    print(file_path)
    label_list.append(file_path)

def GetarrayFromslice(file_path):
    image=sitk.ReadImage(file_path)
    img_array=sitk.GetArrayFromImage(image)
    shape=img_array.shape
    print(shape)
   
for i in image_list:
    GetarrayFromslice(i)    
#%%
class D3UnetData(Dataset):
    def _init_(self,image_list,label_list,transformer):
        self.image_list=image_list
        self.label_list=label_list
        self.transformer=transformer
     
    def _getitem_(self,index):
        image=self.image_list[index]
        label=self.label_list[index]

        image_ct=sitk.ReadImage(image,sitk,sitkInt16)
        image_ct=sitk.read(label,sitk.sitkInt8)
        ct_array=sitk.GetArrayFromImage(image_ct)[250:300,:,:] #250-300张图片
        label_array=sitk.GetArrayFromImage(label_ct)[250:300,:,:]
        
        label_array[label_array>0]=1
        ct_array=ct_array.astype(np.float32)
        ct_array=torch.FloatTensor(ct_array).unsqueeze(0)
        label_array=torch.LongTensor(label_array)
        
        ct_array=self.transformer(ct_array)
        label_array=self.transformer(label_array)
        
        return ct_array,label_array
    
    def _len_(self):
        return len(self.image_list)
    
    
    
class D3UnetData_test(Dataset):
    def __init__(self,image_list,label_list,transformer):
        self.image_list=image_list
        self.label_list=label_list
        self.transformer=transformer
        
    def __getitem__(self,index):
        image=self.image_list[index]
        label=self.label_list[index]
        
        image_ct=sitk.ReadImage(image,sitk.sitkInt16)
        label_ct=sitk.ReadImage(label,sitk.sitkInt8)
        
        ct_array=sitk.GetArrayFromImage(image_ct)[200:250]
        label_array=sitk.GetArrayFromImage(label_ct)[200:250]
        
        label_array[label_array>0]=1
        
        ct_array=ct_array.astype(np.float32)
        
        ct_array=torch.FloatTensor(ct_array).unsqueeze(0)  ###[1,50,512,512]
        label_array=torch.LongTensor(label_array) ###[50,512,512]
        
        ct_array=self.transformer(ct_array)
        label_array=self.transformer(label_array)
        
        return ct_array,label_array
        
    def __len__(self):
        return len(self.image_list)   
 #%%

transformer=transforms.Compose([
    transforms.Resize((96,96)),
])
# In[7]:


train_ds=D3UnetData(image_list,label_list,transformer)
test_ds=D3UnetData_test(image_list,label_list,transformer)
len(train_ds)


# In[8]:


train_dl=DataLoader(train_ds,batch_size=2,shuffle=True)
test_dl=DataLoader(test_ds,batch_size=2,shuffle=True)


# In[9]:


img,label=next(iter(train_dl))
print(img.shape,label.shape)


# In[10]:


img_show=img[0,0,25,:,:].numpy()
plt.imshow(img_show,cmap='gray')


# In[11]:


label_show=label[0,25,:,:].numpy()
plt.imshow(label_show,cmap='gray')


# In[12]:


img.shape


# In[13]:


class DoubleConv(nn.Module):
    def __init__(self,in_channels,out_channels,num_groups=8):
        super(DoubleConv,self).__init__()
        self.double_conv=nn.Sequential(
            nn.Conv3d(in_channels,out_channels,kernel_size=3,stride=1,padding=1),
            #nn.BatchNorm3d(out_channels),
            nn.GroupNorm(num_groups=num_groups,num_channels=out_channels),
            nn.ReLU(inplace=True),
            
            nn.Conv3d(out_channels,out_channels,kernel_size=3,stride=1,padding=1),
            nn.GroupNorm(num_groups=num_groups,num_channels=out_channels),
            nn.ReLU(inplace=True),
        )
        
        
    def forward(self,x):
        return self.double_conv(x)
    
    


# In[14]:


img.shape
net=DoubleConv(1,64,num_groups=8)
out=net(img)
print(out.shape)


# In[15]:


class Down(nn.Module):

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.MaxPool3d(2, 2),
            DoubleConv(in_channels, out_channels)
        )
    def forward(self, x):
        return self.encoder(x)

    
class Up(nn.Module):

    def __init__(self, in_channels, out_channels, trilinear=True):
        super().__init__()
        
        if trilinear:
            self.up = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose3d(in_channels // 2, in_channels // 2, kernel_size=2, stride=2)
            
        self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)

        diffZ = x2.size()[2] - x1.size()[2]
        diffY = x2.size()[3] - x1.size()[3]
        diffX = x2.size()[4] - x1.size()[4]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2, diffZ // 2, diffZ - diffZ // 2])

        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

    
class Out(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size = 1)

    def forward(self, x):
        return self.conv(x)


class UNet3d(nn.Module):
    def __init__(self, in_channels, n_classes, n_channels):
        super().__init__()
        self.in_channels = in_channels
        self.n_classes = n_classes
        self.n_channels = n_channels

        self.conv = DoubleConv(in_channels, n_channels)
        self.enc1 = Down(n_channels, 2 * n_channels)
        self.enc2 = Down(2 * n_channels, 4 * n_channels)
        self.enc3 = Down(4 * n_channels, 8 * n_channels)
        self.enc4 = Down(8 * n_channels, 8 * n_channels)

        self.dec1 = Up(16 * n_channels, 4 * n_channels)
        self.dec2 = Up(8 * n_channels, 2 * n_channels)
        self.dec3 = Up(4 * n_channels, n_channels)
        self.dec4 = Up(2 * n_channels, n_channels)
        self.out = Out(n_channels, n_classes)

    def forward(self, x):
        x1 = self.conv(x)
        x2 = self.enc1(x1)
        x3 = self.enc2(x2)
        x4 = self.enc3(x3)
        x5 = self.enc4(x4)

        mask = self.dec1(x5, x4)
        mask = self.dec2(mask, x3)
        mask = self.dec3(mask, x2)
        mask = self.dec4(mask, x1)
        mask = self.out(mask)
        return mask


# In[16]:


model=UNet3d(1,2,24).cuda()
img,label=next(iter(train_dl))
print(img.shape,label.shape)
img=img.cuda()
pred=model(img)
pred.shape
        
            


# In[17]:


loss_fn=nn.CrossEntropyLoss()
optimizer=torch.optim.Adam(model.parameters(),lr=0.00001)


# In[18]:


from tqdm import tqdm
def train(epoch, model, trainloader, testloader):
    correct = 0
    total = 0
    running_loss = 0
    epoch_iou = []
    
    model.train()
    for x, y in tqdm(trainloader):
 
        x, y = x.to('cuda'), y.to('cuda')
        y_pred = model(x)
        loss = loss_fn(y_pred, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        with torch.no_grad():
            y_pred = torch.argmax(y_pred, dim=1)
            correct += (y_pred == y).sum().item()
            total += y.size(0)
            running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_iou.append(batch_iou.item())
            
    epoch_loss = running_loss / len(trainloader.dataset)
    epoch_acc = correct / (total*96*96*50)
        
        
    test_correct = 0
    test_total = 0
    test_running_loss = 0 
    epoch_test_iou = []
    
    model.eval()
    with torch.no_grad():
        for x, y in tqdm(testloader):

            x, y = x.to('cuda'), y.to('cuda')
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            y_pred = torch.argmax(y_pred, dim=1)
            test_correct += (y_pred == y).sum().item()
            test_total += y.size(0)
            test_running_loss += loss.item()
            
            intersection = torch.logical_and(y, y_pred)
            union = torch.logical_or(y, y_pred)
            batch_iou = torch.sum(intersection) / torch.sum(union)
            epoch_test_iou.append(batch_iou.item())
            
    
    epoch_test_loss = test_running_loss / len(testloader.dataset)
    epoch_test_acc = test_correct / (test_total*96*96*50)
    
    if np.mean(epoch_test_iou)>0.9:
            static_dict=model.state_dict()
            torch.save(static_dict,'./checkpoint/{}_trainIOU_{}_testIOU_{}.pth'.format(epoch,round(np.mean(epoch_iou), 3),round(np.mean(epoch_test_iou),3)))
    print('epoch: ', epoch, 
          'loss： ', round(epoch_loss, 3),
          'accuracy:', round(epoch_acc, 3),
          'IOU:', round(np.mean(epoch_iou), 3),
          'test_loss： ', round(epoch_test_loss, 3),
          'test_accuracy:', round(epoch_test_acc, 3),
           'test_iou:', round(np.mean(epoch_test_iou), 3)
             )
        
    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc


# In[19]:


epochs = 100


# In[20]:


train_loss = []
train_acc = []
test_loss = []
test_acc = []

for epoch in range(epochs):
    train(epoch,
        model,
        train_dl,
        test_dl)


# In[ ]:


img,label=next(iter(train_dl))
print(img.shape,label.shape)


# In[ ]:


img=img.to('cuda')
pred=model(img)
pred.shape


# In[ ]:


label_show=label[0,20,:,:]
plt.imshow(label_show,cmap='gray')


# In[ ]:



pred=pred[0,:,20,:,:]
pred.shape


# In[ ]:


preds=pred.cpu()

plt.imshow(torch.argmax(preds.permute(1,2,0), axis=-1).detach().numpy(),cmap='gray')


# In[ ]:


plt.imshow(label,cmap='gray')


# In[ ]:


label.shape


# In[ ]:


pred.shape






