{"data": {"ap_dir": "/root/autodl-tmp/HCC2024-2025/ap", "hbp_dir": "/root/autodl-tmp/HCC2024-2025/hbp", "image_size": [256, 256], "train_ratio": 0.8, "batch_size": 2, "num_workers": 0}, "model": {"in_channels": 1, "out_channels": 1, "time_emb_dim": 256, "base_channels": 128, "channel_multipliers": [1, 2, 4, 8, 16]}, "diffusion": {"timesteps": 1000, "beta_start": 0.0001, "beta_end": 0.02}, "training": {"num_epochs": 800, "learning_rate": 0.0001, "weight_decay": 0.0001, "grad_clip": 1.0, "warmup_epochs": 50}, "loss": {"use_perceptual_loss": true, "perceptual_weight": 0.1, "ssim_weight": 0.1, "gradient_weight": 0.05}, "validation": {"sample_interval": 20, "num_inference_steps": 100}, "paths": {"save_dir": "outputs/improved_medical_diffusion"}, "system": {"device": "cuda"}}