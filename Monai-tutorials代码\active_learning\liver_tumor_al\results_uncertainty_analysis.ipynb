{"cells": [{"cell_type": "markdown", "id": "ff8040b5", "metadata": {}, "source": ["Copyright (c) MONAI Consortium  \n", "Licensed under the Apache License, Version 2.0 (the \"License\");  \n", "you may not use this file except in compliance with the License.  \n", "You may obtain a copy of the License at  \n", "&nbsp;&nbsp;&nbsp;&nbsp;http://www.apache.org/licenses/LICENSE-2.0  \n", "Unless required by applicable law or agreed to in writing, software  \n", "distributed under the License is distributed on an \"AS IS\" BASIS,  \n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.  \n", "See the License for the specific language governing permissions and  \n", "limitations under the License.\n", "\n", "# MONAI Active Learning Uncertainty Exploration & Results Analysis\n", "\n", "There are two sections in this notebook:\n", "    \n", "1.) How to visualize uncertainty from given unlabeled samples\n", "\n", "2.) How to analyze results once the active learning scripts have been run to completion"]}, {"cell_type": "markdown", "id": "36031dd0", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "id": "9c089995", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[pillow, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "63d921d3", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 3, "id": "945ce03a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.0.1\n", "Numpy version: 1.23.4\n", "Pytorch version: 1.13.0+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 8271a193229fe4437026185e218d5b06f7c8ce69\n", "MONAI __file__: /home/<USER>/anaconda3/envs/mtk_py38/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.3.0\n", "Tensorboard version: 2.11.0\n", "gdown version: 4.5.3\n", "TorchVision version: 0.14.0+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.3.0\n", "psutil version: 5.9.0\n", "pandas version: 1.5.1\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.0.1\n", "pynrrd version: 0.4.3\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import json\n", "import torch\n", "import pickle\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from monai.config import print_config\n", "from monai.networks.nets import UNet\n", "from monai.networks.layers import Norm\n", "from monai.inferers import sliding_window_inference\n", "from monai.data import DataLoader, Dataset\n", "from monai.transforms import (\n", "    CropForegroundd,\n", "    <PERSON><PERSON><PERSON>,\n", "    EnsureTyped,\n", "    EnsureChannelFirstd,\n", "    LoadImaged,\n", "    ScaleIntensityRanged,\n", "    Spacingd,\n", ")\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "5036172f", "metadata": {}, "source": ["## Part 1: Uncertainty Visualization\n", "\n", "Visualization of the spatial uncertainty maps provides an understanding of what particular regions of the unlabeled image the model is not confident about. The below example will showcase uncertainty on a few examples from the training set."]}, {"cell_type": "markdown", "id": "b5848c8d", "metadata": {}, "source": ["### Dataset, Model and Transform Definitions"]}, {"cell_type": "code", "execution_count": 5, "id": "bf7e8624", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["monai.networks.nets.unet UNet.__init__:dimensions: Argument `dimensions` has been deprecated since version 0.6. Please use `spatial_dims` instead.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["We are set to visualize some cool uncertainty ...\n"]}], "source": ["# Paths, to be defined by user\n", "data_root = os.path.normpath(\"/to/be/defined\")\n", "json_path = os.path.normpath(\"/to/be/defined\")\n", "model_ckpt = os.path.normpath(\"/to/be/defined\")\n", "\n", "# <PERSON><PERSON> file\n", "with open(json_path, \"rb\") as f:\n", "    json_data = json.load(f)\n", "f.close()\n", "\n", "unl_transforms = Compose(\n", "    [\n", "        LoadImaged(keys=(\"image\")),\n", "        EnsureChannelFirstd(keys=(\"image\")),\n", "        Spacingd(\n", "            keys=(\"image\"),\n", "            pixdim=(1.5, 1.5, 2.0),\n", "            mode=(\"bilinear\"),\n", "        ),\n", "        ScaleIntensityRanged(keys=\"image\", a_min=-21, a_max=189, b_min=0.0, b_max=1.0, clip=True),\n", "        CropForegroundd(keys=(\"image\"), source_key=\"image\"),\n", "        EnsureTyped(keys=[\"image\"]),\n", "    ]\n", ")\n", "\n", "# Append data paths to data root\n", "unl_d = json_data[\"training\"]\n", "for idx, _each_sample in enumerate(unl_d):\n", "    unl_d[idx][\"image\"] = os.path.join(data_root, unl_d[idx][\"image\"])\n", "    unl_d[idx][\"label\"] = os.path.join(data_root, unl_d[idx][\"label\"])\n", "\n", "# Create Dataloader\n", "unl_ds = Dataset(data=unl_d, transform=unl_transforms)\n", "unl_loader = DataLoader(unl_ds, batch_size=1, shuffle=False)\n", "\n", "# Model Definition\n", "device = torch.device(\"cuda\")\n", "\n", "model = UNet(\n", "    dimensions=3,\n", "    in_channels=1,\n", "    out_channels=3,\n", "    channels=(16, 32, 64, 128, 256),\n", "    strides=(2, 2, 2, 2),\n", "    num_res_units=2,\n", "    norm=Norm.BATCH,\n", "    dropout=0.2,\n", ").cuda()\n", "\n", "# Load Model Weights\n", "model.load_state_dict(torch.load(model_ckpt))\n", "\n", "# Please note that the model is being put to 'train' mode explicitly for Monte-Carlo simulations\n", "model.train()\n", "print(\"We are set to visualize some cool uncertainty ...\")"]}, {"cell_type": "code", "execution_count": 28, "id": "b909419c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(299, 247, 340)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["`np.int` is a deprecated alias for the builtin `int`. To silence this warning, use `int` by itself. Doing this will not modify any behavior and is safe. When replacing `np.int`, you may wish to use e.g. `np.int64` or `np.int32` to specify the precision. If you wish to review your current use, check the release note link for additional information.\n", "Deprecated in NumPy 1.20; for more details and guidance: https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations\n", "`np.int` is a deprecated alias for the builtin `int`. To silence this warning, use `int` by itself. Doing this will not modify any behavior and is safe. When replacing `np.int`, you may wish to use e.g. `np.int64` or `np.int32` to specify the precision. If you wish to review your current use, check the release note link for additional information.\n", "Deprecated in NumPy 1.20; for more details and guidance: https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["with torch.no_grad():\n", "    counter = 1\n", "    for unl_data in unl_loader:\n", "        counter = counter + 1\n", "        if counter == 4:\n", "            unl_inputs = unl_data[\"image\"].to(device)\n", "\n", "            roi_size = (160, 160, 160)\n", "            sw_batch_size = 4\n", "\n", "            accum_unl_outputs = []\n", "\n", "            for _mc in range(3):\n", "                unl_outputs = sliding_window_inference(unl_inputs, roi_size, sw_batch_size, model)\n", "\n", "                # Activate the output with Softmax\n", "                unl_act_outputs = torch.softmax(unl_outputs, dim=1)\n", "\n", "                # Accumulate\n", "                accum_unl_outputs.append(unl_act_outputs)\n", "\n", "            # Stack it up, Squeeze and send to CPU\n", "            accum_tensor = torch.stack(accum_unl_outputs)\n", "            accum_tensor = torch.squeeze(accum_tensor)\n", "            accum_numpy = accum_tensor.to(\"cpu\").numpy()\n", "            accum_numpy = accum_numpy[:, 1:, :, :, :]\n", "\n", "            # The input is assumed with repetitions, channels and then volumetric data\n", "            vol_input = accum_numpy.astype(dtype=\"float32\")\n", "            dims = vol_input.shape\n", "\n", "            # Threshold values less than or equal to zero\n", "            threshold = 0.0005\n", "            vol_input[vol_input <= 0] = threshold\n", "\n", "            vari = np.nanvar(vol_input, axis=0)\n", "            variance = np.sum(vari, axis=0)\n", "            variance_dims = np.shape(variance)\n", "\n", "            show_img = np.squeeze(unl_inputs.to(\"cpu\").numpy())\n", "            print(show_img.shape)\n", "            # Plot with matplotlib\n", "            plt.figure(1, figsize=(12, 4))\n", "            plt.subplot(1, 2, 1)\n", "            plt.imshow(np.rot90(np.squeeze(show_img[:, np.int(variance_dims[1] / 2), :]), 1), cmap=\"gray\")\n", "            plt.xticks([])\n", "            plt.yticks([])\n", "            plt.title(\"Input Image\")\n", "            plt.subplot(1, 2, 2)\n", "            plt.imshow(np.rot90(np.squeeze(variance[:, np.int(variance_dims[1] / 2), :]), 1))\n", "            plt.xticks([])\n", "            plt.yticks([])\n", "            plt.title(\"Dropout Uncertainty\")\n", "            plt.show()\n", "            break"]}, {"cell_type": "markdown", "id": "43768ebc", "metadata": {}, "source": ["## Part 2: Results Analysis"]}, {"cell_type": "code", "execution_count": 29, "id": "f56fb486", "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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***************************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\n", "text/plain": ["<Figure size 600x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["base_path = os.path.normpath(\"/to/be/defined/results/path/from/runner.sh/goes/here\")\n", "\n", "method_name_list = {\n", "    \"random_i5_q5_iter2k\": \"Random\",\n", "    \"variance_i5_q5_iter2k\": \"Variance\",\n", "}\n", "\n", "active_iters = 6\n", "x = range(0, active_iters)\n", "legend_list = []\n", "\n", "plt.figure(1, figsize=(6, 6))\n", "for each_method, legend_name in method_name_list.items():\n", "    method_path = os.path.join(base_path, each_method)\n", "    legend_list.append(legend_name)\n", "    per_active_dice_score = []\n", "\n", "    trial_path = os.path.join(base_path, each_method)\n", "    print(\"Working on {}\".format(trial_path))\n", "    pickle_path = os.path.join(trial_path, \"all_metrics.pickle\")\n", "    with open(pickle_path, \"rb\") as handle:\n", "        data = pickle.load(handle)\n", "    handle.close()\n", "    for each_iter in range(active_iters):\n", "        iter_key = \"model_\" + str(each_iter)\n", "        max_val = np.max(data[iter_key][\"test_dice\"])\n", "\n", "        per_active_dice_score.append(max_val)\n", "\n", "    plt.plot(x, per_active_dice_score, \"-o\")\n", "\n", "# Get Full dataset result\n", "all_data_result_path = os.path.join(base_path, \"all_data_iter2k\", \"all_metrics.pickle\")\n", "with open(all_data_result_path, \"rb\") as handle:\n", "    all_data_result = pickle.load(handle)\n", "handle.close()\n", "all_data_score = all_data_result[\"model_0\"][\"test_dice\"]\n", "all_data_list = [all_data_score] * active_iters\n", "plt.errorbar(list(range(0, active_iters)), all_data_list, fmt=\"--\", ecolor=\"black\")\n", "legend_list.append(\"All Data Supervised\")\n", "\n", "plt.grid()\n", "plt.xlabel(\"Active Iterations\")\n", "plt.ylabel(\"Test Dice\")\n", "plt.xlim([-0.1, active_iters])\n", "plt.xticks(list(range(0, active_iters)))\n", "plt.legend(legend_list)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "mtk_py38", "language": "python", "name": "mtk_py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.15"}}, "nbformat": 4, "nbformat_minor": 5}