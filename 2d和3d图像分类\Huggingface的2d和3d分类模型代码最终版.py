# https://huggingface.co/models  模型地址

#%%利用huggingface中的transformers库构建模型  https://huggingface.co/models
#安装transformers库地址 https://github.com/huggingface/transformers
#安装代码 conda install conda-forge::transformers
# conda install conda-forge::transformers 更新一下transformers库

#%% transformers库方法1：使用国内镜像源
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  #huggingface国内镜像源
# Load model directly
from transformers import AutoTokenizer, AutoModelForVideoClassification
# tokenizer = AutoTokenizer.from_pretrained("google/vivit-b-16x2")
model = AutoModelForVideoClassification.from_pretrained("google/vivit-b-16x2")
model

#%%transformers库方法2：本地加载
# 把huggingface的files and versions文件均下载到本地，然后直接加载路径，模型名称要一样
#https://huggingface.co/google/vivit-b-16x2/tree/main

from transformers import AutoTokenizer, AutoModelForVideoClassification

# 本地模型路径
model_path = r"C:\Users\<USER>\.cache\huggingface\hub\vivit-b-16x2"

# 从本地路径加载模型
model = AutoModelForVideoClassification.from_pretrained(model_path)

# 打印模型以确认加载成功
print(model)

#%% timm库加载模型
# 方法1：网上加载
import timm
model = timm.create_model("hf_hub:timm/mvitv2_base.fb_in1k", pretrained=True)
model

# 方法2：本地加载
# import timm
# path = r'C:\Users\<USER>\.cache\huggingface\hub\mvitv2_base.fb_in1k\pytorch_model.bin'

# model = timm.create_model('mvitv2_base.fb_in1k', pretrained=True,
#                           pretrained_cfg_overlay=dict(file=path))
# model

#%% MambaVision模型 linux环境成功  https://github.com/NVlabs/MambaVision
#方法1根据dockerfile构建镜像，docker build -t mambavision "E:\docker images\mambavision"
# docker run --rm -it  mambavision:latest   #运行容器成功
#docker成功了
import torch
print(torch.__version__)
print(torch.version.cuda)
import mamba_ssm
print(mamba_ssm.__version__)

import os
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  #huggingface国内镜像源

from transformers import AutoModelForImageClassification
model = AutoModelForImageClassification.from_pretrained("nvidia/MambaVision-T-1K", trust_remote_code=True)
model

# %%vit模型
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  #huggingface国内镜像源
# Load model directly

from transformers import AutoImageProcessor, AutoModelForImageClassification

processor = AutoImageProcessor.from_pretrained("nateraw/vit-base-patch16-224-cifar10")
model = AutoModelForImageClassification.from_pretrained("nateraw/vit-base-patch16-224-cifar10")
model

# %%
from transformers import ViTFeatureExtractor, ViTForImageClassification
from PIL import Image
import requests

url = 'https://www.cs.toronto.edu/~kriz/cifar-10-sample/dog10.png'
image = Image.open(requests.get(url, stream=True).raw)
feature_extractor = ViTFeatureExtractor.from_pretrained('nateraw/vit-base-patch16-224-cifar10')
model = ViTForImageClassification.from_pretrained('nateraw/vit-base-patch16-224-cifar10')
inputs = feature_extractor(images=image, return_tensors="pt")
outputs = model(**inputs)
preds = outputs.logits.argmax(dim=1)

classes = [
    'airplane', 'automobile', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck'
]
classes[preds[0]]

#%% swin3d模型
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  #huggingface国内镜像源
# Load model directly
from transformers import AutoModelForVideoClassification
model = AutoModelForVideoClassification.from_pretrained("vsltranslation/swin3d_t_v1.0", trust_remote_code=True)
model
#%% Vision Transformer 迁移学习代码实战
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
import copy
import torch
import torchvision
import torchvision.transforms as transforms
from torchvision import models
from torch.utils.data import DataLoader
from torch import optim, nn
from torch.optim import lr_scheduler
import os
import matplotlib   #pycharm中要加这行代码，否则不能出图
# matplotlib.use('TkAgg') #pycharm中要加这行代码，否则不能出图
import matplotlib.pyplot as plt
import warnings
import numpy as np
import torch
from torchvision import datasets, transforms
import os
import random
from torch.utils import data
from PIL import Image

import ssl #更新ssl证书
ssl._create_default_https_context = ssl._create_unverified_context

# 在这里执行您的其他代码

# 在这里执行您的其他代码

# 在这里执行您的其他代码

#
def setup_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
# 设置随机数种子
setup_seed(25)

warnings.filterwarnings("ignore")
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置GPU
if torch.cuda.is_available():
    print("CUDA is available.")
else:
    print("CUDA is not available.")

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

## 数据集方法1  根据路径和标签输入,glob方法获取路径的list，列表推导式获得标签的list
import glob

train_dir = '/root/autodl-tmp/ap/train/*/*/*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\train\*\*\*.jpg'
test_dir = '/root/autodl-tmp/ap/validation/*/*/*.jpg'  #r'K:\2020-2023HCC\579hcc\579hcc\pp\jpg\validation\*\*\*.jpg'
train_paths = glob.glob(train_dir)
test_paths = glob.glob(test_dir)

train_paths[:5]
test_paths[-5:]
len(train_paths)
len(test_paths)

img_p = train_paths[100]
img_p
int(img_p.split('/')[-3]) #把切出来的字符型转为int

# train_labels = [int(img_p.split('\\')[-3]) for img_p in train_paths]
train_labels = [int(img_p.split('/')[-3]) for img_p in train_paths]
np.unique(train_labels)
train_labels[:5]

# test_labels = [int(img_p.split('\\')[-3]) for img_p in test_paths]
test_labels = [int(img_p.split('/')[-3]) for img_p in test_paths]
np.unique(test_labels)
test_labels[-5:]


# 图像的大小224   256
img_height = 224
img_width = 224

# 数据预处理
# data_transforms = {
#     'train': transforms.Compose([
#         transforms.RandomResizedCrop(img_height),
#         transforms.RandomHorizontalFlip(),
#         transforms.RandomVerticalFlip(),
#         transforms.RandomRotation(0.2),
#         transforms.ToTensor(),
#         transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
#     ]),
#     'val': transforms.Compose([
#         transforms.Resize((img_height, img_width)),
#         transforms.ToTensor(),
#         transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
#     ]),
# }

transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
])

class Mydataset(data.Dataset):
    def __init__(self, img_paths, labels):
        self.imgs = img_paths
        self.labels = labels

    def __getitem__(self, index):
        img = self.imgs[index]
        label = self.labels[index]

        pil_img = Image.open(img)
        pil_img = pil_img.convert("RGB")  # 转为RGB模式，H,W,C=3
        img_tensor = transform(pil_img)

        return img_tensor, label

    def __len__(self):
        return len(self.imgs)


BATCH_SIZE = 32

# %%
train_ds = Mydataset(train_paths, train_labels)

# In[38]:

test_ds = Mydataset(test_paths, test_labels)

# In[39]:

train_dl = data.DataLoader(train_ds,batch_size=32,shuffle=True)
test_dl = data.DataLoader(test_ds,batch_size=32,shuffle=False)

imgs_batch, labels_batch = next(iter(train_dl))

imgs_batch.shape

labels_batch.shape

print(train_ds.imgs[:5])
# print(dataset[0][1])# 第一维是第几张图，第二维为1返回label
print(train_ds[0][0].size()) # 深度学习中图片数据一般保存成CxHxW，即通道数x图片高x图片宽
len(train_ds)
len(test_ds)

# # 创建数据加载器
# batch_size = 32
# train_dataloader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
# val_dataloader = torch.utils.data.DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

dataloaders = {'train': train_dl, 'val': test_dl}
dataset_sizes = {'train': len(train_ds), 'val': len(test_ds)}

##学术资源加速
import subprocess
import os

result = subprocess.run('bash -c "source /etc/network_turbo && env | grep proxy"', shell=True, capture_output=True, text=True)
output = result.stdout
for line in output.splitlines():
    if '=' in line:
        var, value = line.split('=', 1)
        os.environ[var] = value

import ssl
ssl._create_default_https_context = ssl._create_unverified_context

## 定义Vision Transformer模型
# 出现错误HTTPSConnectionPool(host=‘huggingface.co‘, port=443)需要把 requests 降到2.26.0或2.27.1，urllib3 降到1.25.1

import timm

from pprint import pprint
model_names = timm.list_models(pretrained=True)
pprint(model_names)

# model = timm.create_model('vit_small_patch16_224',pretrained=True) #从网上下载预训练模型 https://github.com/google-research/vision_transformer

# 加载当前模型Vision Transformer1
# 加载手动下载的预训练模型权重1    https://paperswithcode.com/lib/timm 模型权重下载 放到C:\Users\<USER>\.cache\torch\hub\checkpoints\路径下

model = timm.create_model('vit_base_patch16_224', pretrained=True) # 你可以选择适合你需求的Vision Transformer版本，这里以vit_base_patch16_224为例
model = timm.create_model('vit_base_patch16_224', pretrained=False)
pretrained_weights = torch.load('/root/autodl-tmp/ap/vit_base_patch16_224.pth')
model.load_state_dict(pretrained_weights)

# 加载当前模型和预训练模型权重2
# model = timm.create_model('vit_large_patch16_224', pretrained=False)
# pretrained_weights = torch.load(r'D:\HCC\vision_transformer\vit_large_patch16_224.pth')
#
# # 加载当前模型 Swin-Transformer
# model = timm.create_model('swinv2_small_window8_256.ms_in1k', pretrained=False)
# # pretrained_weights = torch.load(r'C:\Users\<USER>\.cache\torch\hub\checkpoints\swinv2_small_patch4_window8_256.pth')
#
# # Data-efficient Image Transformers的预训练版本
# # 定义Data-efficient Image Transformers模型
# model = timm.create_model('deit_base_patch16_224', pretrained=True)  # 你可以选择适合你需求的DeiT版本，这里以deit_base_patch16_224为例
# # pretrained_weights = torch.load(r'C:\Users\<USER>\.cache\torch\hub\checkpoints\？??.pth')
#
# # 定义Convolutional Vision Transformer模型
# model = timm.create_model('convit_base.fb_in1k', pretrained=True) # 你可以选择适合你需求的ConViT版本，这里以'convit_base'为例False   convit_base
# model = timm.create_model('convit_base', pretrained=False)
# pretrained_weights = torch.load(r'G:\convit_base.pth')
# model.load_state_dict(pretrained_weights)

#convit预训练权重下载地址
# https://dl.fbaipublicfiles.com/convit/convit_small.pth
# https://dl.fbaipublicfiles.com/convit/convit_base.pth
# https://dl.fbaipublicfiles.com/convit/convit_tiny.pth

model = timm.create_model('convit_base', pretrained=False)
checkpoint = torch.hub.load_state_dict_from_url(
    url="https://dl.fbaipublicfiles.com/convit/convit_base.pth",
    map_location="cpu", check_hash=True
)
model.load_state_dict(checkpoint)
print(model)

# # 定义Transformer in Transformer模型
# model = timm.create_model('tnt_s_patch16_224', pretrained=True)  # 你可以选择适合你需求的TNT版本，这里以tnt_s_patch16_224为例
# # pretrained_weights = torch.load(r'C:\Users\<USER>\.cache\torch\hub\checkpoints\？??.pth')
#
# # 调整权重参数的形状  调用预训练权重报错时用
# # for name, param in pretrained_weights.items():
# #     if name in model.state_dict():
# #         if param.shape != model.state_dict()[name].shape:
# #             param = param.reshape(model.state_dict()[name].shape)
# #         model.state_dict()[name].copy_(param)

# 模型加载完成，现在可以使用该模型进行推断或训练
print(model)
for param in model.parameters():
    param.requires_grad = False

#数据集中分类的类别数
num_classes = 2  # num_classes表示输出特征

num_ftrs = model.head.in_features

# 根据分类任务修改最后一层
model.head = nn.Linear(num_ftrs, num_classes)

model = model.to(device)

# 打印模型摘要
print(model)

## 创建 Vision Transformer 模型  方法2  仍报错
#使用pip install vision_transformer_pytorch安装，并使用以下命令加载经过预训练的VisionTransformer：
import os
# import torch
# import torch.nn as nn
# from torch.optim import Adam
# from torch.utils.data import DataLoader
# import torchvision.transforms as transforms
# from vision_transformer_pytorch import VisionTransformer
# model = VisionTransformer.from_pretrained('ViT-B_16')
#
# # -  ViT-B_32 : 另一个版本的Vision Transformer，具有较大的模型规模。
# # -  ViT-L_16 ,  ViT-L_32 : 更大规模的Vision Transformer模型。
# # -  ViT-H_14 : 最大规模的Vision Transformer模型。
#
# print(model)


# num_features = model.classifier.in_features
# print(num_features)
# # 根据分类任务修改最后一层
# model.classifier = nn.Linear(num_features, len(class_names))
#
# model = model.to(device)
#
# # 打印模型摘要
# print(model)

## 定义Bottleneck Transformer模型
# model = timm.create_model('botnet26t_256', pretrained=True)  # 你可以选择适合你需求的BotNet版本
# # pretrained_weights = torch.load(r'C:\Users\<USER>\.cache\torch\hub\checkpoints\？??.pth')
# # # 调整权重参数的形状
# # for name, param in pretrained_weights.items():
# #     if name in model.state_dict():
# #         if param.shape != model.state_dict()[name].shape:
# #             param = param.reshape(model.state_dict()[name].shape)
# #         model.state_dict()[name].copy_(param)
# # num_ftrs = model.feature_info[-1]['num_chs']
# # # 根据分类任务修改最后一层
# # model.head.fc = nn.Linear(num_ftrs, len(class_names))
# # # 将模型移至指定设备
# # model = model.to(device)
# # # 打印模型摘要
# # print(model)

##编译模型
# 定义损失函数
criterion = nn.CrossEntropyLoss()

# 定义优化器
# optimizer = optim.Adam(model.parameters())
optimizer = torch.optim.Adam(model.head.parameters(), lr=0.0001)
# 定义学习率调度器
exp_lr_scheduler = lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)

# 开始训练模型
num_epochs = 30
best_model_wts = copy.deepcopy(model.state_dict())
best_acc = 0.0

# 初始化记录器
train_loss_history = []
train_acc_history = []
val_loss_history = []
val_acc_history = []

for epoch in range(num_epochs):
    print('Epoch {}/{}'.format(epoch, num_epochs - 1))
    print('-' * 10)

    # 每个epoch都有一个训练和验证阶段
    for phase in ['train', 'val']:
        if phase == 'train':
            model.train()  # Set model to training mode
        else:
            model.eval()  # Set model to evaluate mode

        running_loss = 0.0
        running_corrects = 0

        # 遍历数据
        for inputs, labels in dataloaders[phase]:
            inputs = inputs.to(device)
            labels = labels.to(device)

            # 零参数梯度
            optimizer.zero_grad()

            # 前向
            with torch.set_grad_enabled(phase == 'train'):
                outputs = model(inputs)
                _, preds = torch.max(outputs, 1)
                loss = criterion(outputs, labels)

                # 只在训练模式下进行反向和优化
                if phase == 'train':
                    loss.backward()
                    optimizer.step()

            # 统计
            running_loss += loss.item() * inputs.size(0)
            running_corrects += torch.sum(preds == labels.data)
        exp_lr_scheduler.step()
        epoch_loss = running_loss / dataset_sizes[phase]
        epoch_acc = (running_corrects.double() / dataset_sizes[phase]).item()

        # 记录每个epoch的loss和accuracy
        if phase == 'train':
            train_loss_history.append(epoch_loss)
            train_acc_history.append(epoch_acc)
        else:
            val_loss_history.append(epoch_loss)
            val_acc_history.append(epoch_acc)

        print('{} Loss: {:.4f} Acc: {:.4f}'.format(phase, epoch_loss, epoch_acc))

        # 深拷贝模型
        if phase == 'val' and epoch_acc > best_acc:
            best_acc = epoch_acc
            best_model_wts = copy.deepcopy(model.state_dict())

    print()

print('Best val Acc: {:4f}'.format(best_acc))

# 打印模型所在的设备
print("Model is running on:", device)

##Accuracy和Loss可视化
epoch = range(1, len(train_loss_history) + 1)

fig, ax = plt.subplots(1, 2, figsize=(10, 4))
ax[0].plot(epoch, train_loss_history, label='Train loss')
ax[0].plot(epoch, val_loss_history, label='Validation loss')
ax[0].set_xlabel('Epochs')
ax[0].set_ylabel('Loss')
ax[0].legend()
ax[1].plot(epoch, train_acc_history, label='Train acc')
ax[1].plot(epoch, val_acc_history, label='Validation acc')
ax[1].set_xlabel('Epochs')
ax[1].set_ylabel('Accuracy')
ax[1].legend()

# plt.savefig("loss-acc.pdf", dpi=300,format="pdf")

##混淆矩阵绘制
from sklearn.metrics import classification_report, confusion_matrix
import math
import pandas as pd
import numpy as np
import seaborn as sns
from matplotlib.pyplot import imshow


# 定义一个绘制混淆矩阵图的函数
def plot_cm(labels, predictions):
    # 生成混淆矩阵
    conf_numpy = confusion_matrix(labels, predictions)
    # 将矩阵转化为 DataFrame
    conf_df = pd.DataFrame(conf_numpy, index=class_names, columns=class_names)

    plt.figure(figsize=(8, 7))

    sns.heatmap(conf_df, annot=True, fmt="d", cmap="BuPu")

    plt.title('Confusion matrix', fontsize=15)
    plt.ylabel('Actual value', fontsize=14)
    plt.xlabel('Predictive value', fontsize=14)


def evaluate_model(model, dataloader, device):
    model.eval()  # 设置模型为评估模式
    true_labels = []
    pred_labels = []
    # 遍历数据
    for inputs, labels in dataloader:
        inputs = inputs.to(device)
        labels = labels.to(device)

        # 前向
        with torch.no_grad():
            outputs = model(inputs)
            _, preds = torch.max(outputs, 1)

        true_labels.extend(labels.cpu().numpy())
        pred_labels.extend(preds.cpu().numpy())

    return true_labels, pred_labels


# 获取预测和真实标签
true_labels, pred_labels = evaluate_model(model, dataloaders['val'], device)

# 计算混淆矩阵
cm_val = confusion_matrix(true_labels, pred_labels)
a_val = cm_val[0, 0]
b_val = cm_val[0, 1]
c_val = cm_val[1, 0]
d_val = cm_val[1, 1]

# 计算各种性能指标
acc_val = (a_val + d_val) / (a_val + b_val + c_val + d_val)  # 准确率
error_rate_val = 1 - acc_val  # 错误率
sen_val = d_val / (d_val + c_val)  # 灵敏度
sep_val = a_val / (a_val + b_val)  # 特异度
precision_val = d_val / (b_val + d_val)  # 精确度
F1_val = (2 * precision_val * sen_val) / (precision_val + sen_val)  # F1值
MCC_val = (d_val * a_val - b_val * c_val) / (
    np.sqrt((d_val + b_val) * (d_val + c_val) * (a_val + b_val) * (a_val + c_val)))  # 马修斯相关系数

# 打印出性能指标
print("验证集的灵敏度为：", sen_val,
      "验证集的特异度为：", sep_val,
      "验证集的准确率为：", acc_val,
      "验证集的错误率为：", error_rate_val,
      "验证集的精确度为：", precision_val,
      "验证集的F1为：", F1_val,
      "验证集的MCC为：", MCC_val)

#绘制混淆矩阵
plot_cm(true_labels, pred_labels)

##  获取预测和真实标签
train_true_labels, train_pred_labels = evaluate_model(model, dataloaders['train'], device)
# 计算混淆矩阵
cm_train = confusion_matrix(train_true_labels, train_pred_labels)
a_train = cm_train[0, 0]
b_train = cm_train[0, 1]
c_train = cm_train[1, 0]
d_train = cm_train[1, 1]
acc_train = (a_train + d_train) / (a_train + b_train + c_train + d_train)
error_rate_train = 1 - acc_train
sen_train = d_train / (d_train + c_train)
sep_train = a_train / (a_train + b_train)
precision_train = d_train / (b_train + d_train)
F1_train = (2 * precision_train * sen_train) / (precision_train + sen_train)
MCC_train = (d_train * a_train - b_train * c_train) / (
    math.sqrt((d_train + b_train) * (d_train + c_train) * (a_train + b_train) * (a_train + c_train)))
print("训练集的灵敏度为：", sen_train,
      "训练集的特异度为：", sep_train,
      "训练集的准确率为：", acc_train,
      "训练集的错误率为：", error_rate_train,
      "训练集的精确度为：", precision_train,
      "训练集的F1为：", F1_train,
      "训练集的MCC为：", MCC_train)

# 绘制混淆矩阵
plot_cm(train_true_labels, train_pred_labels)

##AUC曲线绘制
from sklearn import metrics
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.pyplot import imshow
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
import pandas as pd
import math


def plot_roc(name, labels, predictions, **kwargs):
    fp, tp, _ = metrics.roc_curve(labels, predictions)

    plt.plot(fp, tp, label=name, linewidth=2, **kwargs)
    plt.plot([0, 1], [0, 1], color='orange', linestyle='--')
    plt.xlabel('False positives rate')
    plt.ylabel('True positives rate')
    ax = plt.gca()
    ax.set_aspect('equal')


# 确保模型处于评估模式
model.eval()

train_ds = dataloaders['train']
val_ds = dataloaders['val']

val_pre_auc = []
val_label_auc = []

for images, labels in val_ds:
    for image, label in zip(images, labels):
        img_array = image.unsqueeze(0).to(device)  # 在第0维增加一个维度并将图像转移到适当的设备上
        prediction_auc = model(img_array)  # 使用模型进行预测
        val_pre_auc.append(prediction_auc.detach().cpu().numpy()[:, 1])
        val_label_auc.append(label.item())  # 使用Tensor.item()获取Tensor的值
auc_score_val = metrics.roc_auc_score(val_label_auc, val_pre_auc)

train_pre_auc = []
train_label_auc = []

for images, labels in train_ds:
    for image, label in zip(images, labels):
        img_array_train = image.unsqueeze(0).to(device)
        prediction_auc = model(img_array_train)
        train_pre_auc.append(prediction_auc.detach().cpu().numpy()[:, 1])  # 输出概率而不是标签！
        train_label_auc.append(label.item())
auc_score_train = metrics.roc_auc_score(train_label_auc, train_pre_auc)

plot_roc('validation AUC: {0:.4f}'.format(auc_score_val), val_label_auc, val_pre_auc, color="red", linestyle='-')
plot_roc('training AUC: {0:.4f}'.format(auc_score_train), train_label_auc, train_pre_auc, color="blue", linestyle='-')
plt.legend(loc='lower right')
# plt.savefig("roc.pdf", dpi=300,format="pdf")

print("训练集的AUC值为：", auc_score_train, "验证集的AUC值为：", auc_score_val)
