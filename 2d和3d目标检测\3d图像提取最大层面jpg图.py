import os
import numpy as np
import nibabel as nib
from PIL import Image
import matplotlib.pyplot as plt

def extract_largest_tumor_slice(image_path, mask_path, output_path=None):
    """
    从NII.GZ图像中提取肿瘤最大层面并保存为JPG
    
    参数:
    image_path: 原始图像路径(.nii.gz格式)
    mask_path: 肿瘤掩膜路径(.nii.gz格式)
    output_path: 输出JPG图像的路径，默认为当前目录下的tumor_max_slice.jpg
    
    返回:
    保存的JPG图像路径
    """
    # 默认输出路径
    if output_path is None:
        output_path = 'tumor_max_slice.jpg'
    
    # 加载掩膜图像
    mask_nii = nib.load(mask_path)
    mask_data = mask_nii.get_fdata()
    
    # 计算每个切片上的掩膜面积
    slice_areas = []
    for i in range(mask_data.shape[2]):  # 假设切片在第三维度
        slice_area = np.sum(mask_data[:, :, i] > 0)
        slice_areas.append(slice_area)
    
    # 找到最大面积的切片索引
    max_slice_idx = np.argmax(slice_areas)
    print(f"最大肿瘤切片在索引: {max_slice_idx}，面积: {slice_areas[max_slice_idx]}")
    
    # 加载原始图像
    img_nii = nib.load(image_path)
    img_data = img_nii.get_fdata()
    
    # 提取对应切片
    tumor_slice = img_data[:, :, max_slice_idx]
    
    # 改进的对比度调整 - 使用百分位数进行窗位窗宽调整
    p_low, p_high = np.percentile(tumor_slice, [2, 98])  # 使用2%和98%百分位数作为窗宽窗位
    tumor_slice = np.clip(tumor_slice, p_low, p_high)
    
    # 归一化到0-255
    tumor_slice = ((tumor_slice - p_low) / (p_high - p_low)) * 255
    tumor_slice = tumor_slice.astype(np.uint8)
    
    # 图像方向校正 - 根据参考图像调整
    
    # 首先旋转90度
    tumor_slice = np.rot90(tumor_slice, k=1)
    
    # 不需要左右翻转
    # tumor_slice = np.fliplr(tumor_slice)
    
    # 需要上下翻转
    tumor_slice = np.flipud(tumor_slice)
    
    # 保存为JPG
    img = Image.fromarray(tumor_slice)
    img.save(output_path)
    print(f"已将最大肿瘤切片保存到: {output_path}")
    
    return output_path

# 使用示例
if __name__ == "__main__":
    # 使用文件夹路径处理多个图像
    images_folder = r"H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\598HCC\image\ap"  # 包含所有nii.gz图像的文件夹
    masks_folder = r"H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\598HCC\mask\ap"    # 包含所有nii.gz掩膜的文件夹
    output_folder = r"H:\1.HCC-dataset\850HCC\all-HCC\598HCC-suzhou\598HCC\max_tumor_jpg"  # 输出JPG图像的文件夹
    
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取图像文件夹中的所有nii.gz文件
    image_files = [f for f in os.listdir(images_folder) if f.endswith('.nii.gz')]
    
    for image_file in image_files:
        # 假设掩膜文件与图像文件同名或者有规律的命名方式
        # 这里假设掩膜文件名是在原图像文件名基础上加上"_mask"
        image_base_name = os.path.splitext(os.path.splitext(image_file)[0])[0]  # 去掉.nii.gz后缀
        mask_file = f"{image_base_name}-mask.nii.gz"
        
        # 检查对应的掩膜文件是否存在
        if not os.path.exists(os.path.join(masks_folder, mask_file)):
            print(f"警告：未找到图像 {image_file} 对应的掩膜文件 {mask_file}，跳过")
            continue
        
        # 构建完整路径
        image_path = os.path.join(images_folder, image_file)
        mask_path = os.path.join(masks_folder, mask_file)
        output_path = os.path.join(output_folder, f"{image_base_name}_max_tumor.jpg")
        
        print(f"处理图像: {image_file}")
        # 调用函数处理当前图像和掩膜对
        extract_largest_tumor_slice(image_path, mask_path, output_path) 