import os
import shutil

# 定义路径
base_path = r"M:\​新建文件夹\HCC数据集\HCC新增待整理\84HCC"
image_path = os.path.join(base_path, "image")
mask_path = os.path.join(base_path, "mask")
output_path = os.path.join(base_path, "no_mask_images")

# 确保输出目录存在
if not os.path.exists(output_path):
    os.makedirs(output_path)

# 获取所有序列文件夹
sequence_folders = []
for folder in os.listdir(image_path):
    if os.path.isdir(os.path.join(image_path, folder)):
        sequence_folders.append(folder)

print(f"找到序列文件夹: {sequence_folders}")

# 处理每个序列文件夹
for sequence in sequence_folders:
    image_sequence_path = os.path.join(image_path, sequence)
    mask_sequence_path = os.path.join(mask_path, sequence)
    output_sequence_path = os.path.join(output_path, sequence)
    
    # 确保输出序列目录存在
    if not os.path.exists(output_sequence_path):
        os.makedirs(output_sequence_path)
    
    # 检查mask序列文件夹是否存在
    if not os.path.exists(mask_sequence_path):
        print(f"序列 {sequence} 没有对应的mask文件夹，复制所有图像")
        # 复制所有图像
        for img_file in os.listdir(image_sequence_path):
            shutil.copy2(
                os.path.join(image_sequence_path, img_file),
                os.path.join(output_sequence_path, img_file)
            )
        continue
    
    # 获取图像和mask文件列表
    image_files = [f for f in os.listdir(image_sequence_path) if f.endswith(('.nii.gz', '.nii', '.jpg', '.png'))]
    mask_files = [f for f in os.listdir(mask_sequence_path) if f.endswith(('.nii.gz', '.nii', '.jpg', '.png'))]
    
    # 提取mask文件名的基本部分（不含-mask后缀）
    mask_base_names = []
    for mask_file in mask_files:
        base_name = mask_file
        if "-mask" in mask_file:
            base_name = mask_file.replace("-mask", "")
        mask_base_names.append(base_name)
    
    # 找出没有对应mask的图像
    no_mask_images = []
    for img_file in image_files:
        if img_file not in mask_base_names:
            no_mask_images.append(img_file)
    
    # 复制没有mask的图像
    for img_file in no_mask_images:
        shutil.copy2(
            os.path.join(image_sequence_path, img_file),
            os.path.join(output_sequence_path, img_file)
        )
    
    print(f"序列 {sequence}: 找到 {len(image_files)} 个图像，{len(mask_files)} 个mask，复制了 {len(no_mask_images)} 个没有mask的图像")

print("完成！没有对应mask的图像已复制到:", output_path)